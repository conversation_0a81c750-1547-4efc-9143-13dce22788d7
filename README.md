# Baum Cosmology WordPress Plugin

A comprehensive astronomy and astrology plugin for WordPress that provides detailed information about seasons, sun/moon data, planet visibility, meteor showers, eclipses, and more.

## Features

### 🌍 Seasons
- Current season information
- All four seasons data
- Next season countdown
- Support for both Northern and Southern hemispheres

### ☀️ Sun Information
- Sunrise and sunset times
- Solar noon
- Day length calculations
- Twilight times (civil, nautical, astronomical)
- Location-based calculations

### 🌙 Moon Information
- Current moon phase
- Illumination percentage
- Next major moon phases
- Lunar cycle information

### 🪐 Planet Visibility
- Currently visible planets
- Planet magnitudes
- Best viewing times
- Constellation locations

### ☄️ Meteor Showers
- Upcoming meteor showers
- Peak dates and activity periods
- Zenith Hourly Rate (ZHR)
- Radiant constellations

### 🌑 Eclipses
- Upcoming solar and lunar eclipses
- Eclipse types and durations
- Visibility regions
- Magnitude information

### 📅 Comprehensive Astronomy Calendar
- Combined view of all astronomical events
- Customizable display options
- Location-specific data

### 🔮 Astrology Features (Swiss Ephemeris)
- Precise planetary positions using Swiss Ephemeris
- Daily transits and aspects
- Lunar phase calculations
- Zodiac sign ingresses
- Moon void-of-course periods
- Client-side Moshier ephemeris for interactive charts
- Natal chart generation
- Real-time astronomical calculations

## Shortcodes

### Season Shortcodes
```
[current_season hemisphere="northern" show_dates="yes" show_description="yes"]
[season_info season="spring" hemisphere="northern" show_dates="yes" show_description="yes"]
[next_season hemisphere="northern" show_dates="yes" show_description="yes" show_countdown="yes"]
[all_seasons hemisphere="northern" show_dates="yes" show_description="yes"]
```

### Astronomy Shortcodes
```
[sun_times date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_twilight="yes" table_style="yes"]

[moon_info date="2025-01-15" show_next_phases="yes" show_illumination="yes" table_style="yes"]

[planet_visibility date="2025-01-15" show_magnitude="yes" show_constellation="yes" show_best_time="yes" table_style="yes"]

[meteor_showers date="2025-01-15" days_ahead="90" show_zhr="yes" show_radiant="yes" show_best_time="yes" table_style="yes"]

[eclipse_info date="2025-01-15" years_ahead="2" show_magnitude="yes" show_duration="yes" show_visibility="yes" table_style="yes"]

[astro_calendar date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_sun="yes" show_moon="yes" show_planets="yes" show_meteors="yes" show_eclipses="yes" compact="no"]
```

### Astrology Shortcodes
```
[planetary_positions date="2025-01-15" show_motion="yes" show_degree_in_sign="yes" table_style="yes"]

[daily_transits date="2025-01-15" show_orb="yes" show_strength="yes" max_orb="10" table_style="yes"]

[lunar_phases date="2025-01-15" days_ahead="30" show_illumination="yes" table_style="yes"]

[zodiac_ingresses date="2025-01-15" days_ahead="90" table_style="yes"]

[moon_void_course date="2025-01-15" show_details="yes"]

[natal_chart date="1990-01-15" time="12:00" latitude="40.7128" longitude="-74.0060" location_name="New York"]

[astrology_calendar date="2025-01-15" show_positions="yes" show_transits="yes" show_lunar_phases="yes" show_ingresses="yes" show_void_course="yes" compact="no"]
```

### Advanced Astrology Shortcodes
```
[asteroid_positions date="2025-01-15" show_motion="yes" show_speed="no" table_style="yes"]

[planetary_influence date="2025-01-15" show_planets="yes" show_percentages="yes"]

[horoscope_generator date="2025-01-15" sign="Leo" show_transits="yes" show_moon_phase="yes"]

[galactic_center year="2025" show_precession="yes"]

[historical_comparison date="2025-01-15" show_descriptions="yes" limit="5"]

[astrology_timeline start_date="2025-01-15" days_ahead="30" show_transits="yes" show_ingresses="yes" show_phases="yes"]

[user_horoscope user_id="1" date="2025-01-15" show_birth_chart="no"]
```

## Shortcode Parameters

### Common Parameters
- `date` - Date in YYYY-MM-DD format (default: today)
- `table_style` - Display as table ("yes") or list ("no") (default: "yes")

### Location Parameters
- `latitude` - Latitude in decimal degrees (default: 40.7128 - New York)
- `longitude` - Longitude in decimal degrees (default: -74.0060 - New York)
- `location_name` - Display name for the location (default: "New York")

### Season Parameters
- `hemisphere` - "northern" or "southern" (default: "northern")
- `season` - "spring", "summer", "autumn", or "winter"
- `show_dates` - Show season dates ("yes" or "no")
- `show_description` - Show season descriptions ("yes" or "no")
- `show_countdown` - Show countdown to next season ("yes" or "no")

### Sun Parameters
- `show_twilight` - Show twilight times ("yes" or "no")

### Moon Parameters
- `show_next_phases` - Show upcoming moon phases ("yes" or "no")
- `show_illumination` - Show illumination percentage ("yes" or "no")

### Planet Parameters
- `show_magnitude` - Show planet magnitudes ("yes" or "no")
- `show_constellation` - Show constellation locations ("yes" or "no")
- `show_best_time` - Show best viewing times ("yes" or "no")

### Meteor Shower Parameters
- `days_ahead` - Number of days to look ahead (default: 90)
- `show_zhr` - Show Zenith Hourly Rate ("yes" or "no")
- `show_radiant` - Show radiant constellation ("yes" or "no")
- `show_best_time` - Show best viewing times ("yes" or "no")

### Eclipse Parameters
- `years_ahead` - Number of years to look ahead (default: 2)
- `show_magnitude` - Show eclipse magnitude ("yes" or "no")
- `show_duration` - Show eclipse duration ("yes" or "no")
- `show_visibility` - Show visibility regions ("yes" or "no")

### Astro Calendar Parameters
- `show_sun` - Include sun information ("yes" or "no")
- `show_moon` - Include moon information ("yes" or "no")
- `show_planets` - Include planet visibility ("yes" or "no")
- `show_meteors` - Include meteor showers ("yes" or "no")
- `show_eclipses` - Include eclipse information ("yes" or "no")
- `compact` - Use compact display format ("yes" or "no")

### Astrology Parameters
- `show_motion` - Show planetary motion (direct/retrograde) ("yes" or "no")
- `show_degree_in_sign` - Show degree within zodiac sign ("yes" or "no")
- `show_speed` - Show planetary speed ("yes" or "no")
- `show_orb` - Show aspect orb ("yes" or "no")
- `show_strength` - Show aspect strength ("yes" or "no")
- `max_orb` - Maximum orb for aspects (default: "10")
- `show_details` - Show detailed void-of-course information ("yes" or "no")
- `show_positions` - Include planetary positions ("yes" or "no")
- `show_transits` - Include daily transits ("yes" or "no")
- `show_lunar_phases` - Include lunar phases ("yes" or "no")
- `show_ingresses` - Include zodiac ingresses ("yes" or "no")
- `show_void_course` - Include void-of-course information ("yes" or "no")

### Advanced Astrology Parameters
- `show_motion` - Show asteroid motion ("yes" or "no")
- `show_speed` - Show asteroid speed ("yes" or "no")
- `show_planets` - Show planets in each element/modality ("yes" or "no")
- `show_percentages` - Show percentage distribution ("yes" or "no")
- `sign` - Zodiac sign for horoscope generation
- `show_birth_chart` - Include birth chart information ("yes" or "no")
- `year` - Year for Galactic Center position
- `show_precession` - Show precession adjustment ("yes" or "no")
- `show_descriptions` - Show event descriptions ("yes" or "no")
- `limit` - Number of historical events to show
- `start_date` - Starting date for timeline
- `days_ahead` - Number of days for timeline
- `show_phases` - Include lunar phases in timeline ("yes" or "no")
- `user_id` - WordPress user ID for personalized horoscope

## Installation

1. Upload the plugin files to `/wp-content/plugins/baum-cosmology/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Use the shortcodes in your posts, pages, or widgets

## Styling

The plugin includes comprehensive CSS styling with:
- Responsive design for mobile devices
- Dark mode support
- Beautiful table layouts
- Status indicators for upcoming events
- Gradient backgrounds for the astronomy calendar

## Technical Notes

### Astronomical Calculations
- All astronomical calculations are performed using standard astronomical formulas
- Sun and moon calculations are approximations suitable for general use
- Planet visibility is simplified and may not reflect exact current positions
- Eclipse and meteor shower data includes major events for 2025-2026

### Astrological Calculations
- **Swiss Ephemeris Integration**: For precise planetary positions, install the Swiss Ephemeris PHP extension
- **Fallback Calculations**: If Swiss Ephemeris is not available, simplified calculations are used
- **Client-Side Ephemeris**: Moshier ephemeris provides client-side calculations for interactive features
- **Caching**: Planetary positions are cached for 1 hour to improve performance
- **Precision**: Swiss Ephemeris provides accuracy suitable for professional astrological work

### Swiss Ephemeris Installation
To enable precise astrological calculations, install the Swiss Ephemeris:

1. **Linux/Ubuntu**: `sudo apt-get install libswe-dev`
2. **PHP Extension**: Install the PHP Swiss Ephemeris extension
3. **Data Files**: Download Swiss Ephemeris data files to `/usr/share/swisseph/`

Without Swiss Ephemeris, the plugin uses simplified calculations that are adequate for general use but not for precise astrological work.

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher

## License

GPL-2.0+

## Support

For support and feature requests, please contact the plugin author.
