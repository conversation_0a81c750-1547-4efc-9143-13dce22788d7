<?php
/**
 * Test file for Baum Cosmology Plugin
 * This file can be used to test the plugin functionality outside of WordPress
 */

// Mock WordPress functions for testing
if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('_n')) {
    function _n($single, $plural, $number, $domain = 'default') {
        return $number == 1 ? $single : $plural;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim(strip_tags($str));
    }
}

if (!function_exists('shortcode_atts')) {
    function shortcode_atts($pairs, $atts, $shortcode = '') {
        return array_merge($pairs, (array) $atts);
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'assets/';
    }
}

if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src = '', $deps = array(), $ver = false, $media = 'all') {
        // Mock function
    }
}

if (!function_exists('add_action')) {
    function add_action($tag, $function_to_add, $priority = 10, $accepted_args = 1) {
        // Mock function
    }
}

if (!function_exists('add_shortcode')) {
    function add_shortcode($tag, $func) {
        // Mock function
    }
}

// Include the main plugin file
require_once 'four-seasons.php';

// Create an instance of the plugin
$baum_cosmology = new Baum_Cosmology();

echo "Baum Cosmology Plugin Test\n";
echo "==========================\n\n";

// Test sun times
echo "Sun Times Test:\n";
$sun_data = $baum_cosmology->get_sun_times('2025-01-15', 40.7128, -74.0060);
echo "Sunrise: " . $sun_data['sunrise'] . "\n";
echo "Sunset: " . $sun_data['sunset'] . "\n";
echo "Day Length: " . $sun_data['day_length'] . "\n\n";

// Test moon phase
echo "Moon Phase Test:\n";
$moon_data = $baum_cosmology->get_moon_phase('2025-01-15');
echo "Phase: " . $moon_data['phase_name'] . "\n";
echo "Illumination: " . $moon_data['illumination'] . "%\n\n";

// Test visible planets
echo "Visible Planets Test:\n";
$planets = $baum_cosmology->get_visible_planets('2025-01-15');
foreach ($planets as $planet) {
    echo "- " . $planet['name'] . " (Magnitude: " . $planet['magnitude'] . ")\n";
}
echo "\n";

// Test meteor showers
echo "Meteor Showers Test:\n";
$meteors = $baum_cosmology->get_meteor_showers('2025-01-15', 90);
foreach ($meteors as $shower) {
    echo "- " . $shower['name'] . " peaks on " . date('M j', strtotime($shower['peak_date'])) . "\n";
}
echo "\n";

// Test eclipses
echo "Eclipses Test:\n";
$eclipses = $baum_cosmology->get_eclipses('2025-01-15', 2);
foreach ($eclipses as $eclipse) {
    echo "- " . $eclipse['name'] . " on " . date('M j, Y', strtotime($eclipse['date'])) . "\n";
}
echo "\n";

// Test seasons
echo "Current Season Test:\n";
$current_season = $baum_cosmology->get_current_season('northern');
echo "Current Season: " . $current_season['name'] . "\n";
echo "Description: " . $current_season['description'] . "\n\n";

// Test astrology functions
echo "Astrology Functions Test:\n";
echo "========================\n\n";

// Test planetary positions
echo "Planetary Positions Test:\n";
$planetary_positions = $baum_cosmology->get_planetary_positions('2025-01-15');
foreach (array_slice($planetary_positions, 0, 3) as $position) {
    echo "- " . $position['body'] . " in " . $position['sign'] . " at " . $position['degree_in_sign'] . "\n";
}
echo "\n";

// Test daily transits
echo "Daily Transits Test:\n";
$transits = $baum_cosmology->get_daily_transits('2025-01-15');
foreach (array_slice($transits, 0, 3) as $transit) {
    echo "- " . $transit['planet1'] . " " . $transit['aspect'] . " " . $transit['planet2'] . " (Orb: " . $transit['orb'] . ")\n";
}
echo "\n";

// Test lunar phases
echo "Upcoming Lunar Phases Test:\n";
$lunar_phases = $baum_cosmology->get_upcoming_lunar_phases('2025-01-15', 30);
foreach ($lunar_phases as $phase) {
    echo "- " . $phase['phase'] . " on " . $phase['date'] . " (" . $phase['illumination'] . ")\n";
}
echo "\n";

// Test zodiac ingresses
echo "Zodiac Ingresses Test:\n";
$ingresses = $baum_cosmology->get_zodiac_ingresses('2025-01-15', 90);
foreach (array_slice($ingresses, 0, 3) as $ingress) {
    echo "- " . $ingress['planet'] . " enters " . $ingress['to_sign'] . " on " . $ingress['date'] . "\n";
}
echo "\n";

// Test void-of-course
echo "Moon Void-of-Course Test:\n";
$void_info = $baum_cosmology->get_moon_void_of_course('2025-01-15');
if ($void_info) {
    echo "Moon in " . $void_info['current_sign'] . " at " . $void_info['degree_in_sign'] . "\n";
    echo "Next sign: " . $void_info['next_sign'] . "\n";
    echo "Void likely: " . ($void_info['is_void_likely'] ? 'Yes' : 'No') . "\n";
} else {
    echo "Unable to calculate void-of-course information\n";
}
echo "\n";

// Test shortcodes
echo "Shortcode Tests:\n";
echo "================\n\n";

echo "Planetary Positions Shortcode:\n";
$shortcode_output = $baum_cosmology->planetary_positions_shortcode(array(
    'date' => '2025-01-15',
    'show_motion' => 'yes',
    'table_style' => 'no'
));
echo "Generated " . strlen($shortcode_output) . " characters of HTML output\n\n";

echo "Daily Transits Shortcode:\n";
$shortcode_output = $baum_cosmology->daily_transits_shortcode(array(
    'date' => '2025-01-15',
    'max_orb' => '5',
    'table_style' => 'no'
));
echo "Generated " . strlen($shortcode_output) . " characters of HTML output\n\n";

echo "Swiss Ephemeris Status:\n";
if (function_exists('swe_julday')) {
    echo "✓ Swiss Ephemeris is available - precise calculations enabled\n";
} else {
    echo "⚠ Swiss Ephemeris not available - using fallback calculations\n";
}
echo "\n";

echo "Plugin Test Complete!\n";
echo "All astronomy and astrology functions executed successfully.\n";
echo "The plugin is ready for use in WordPress with comprehensive astronomical and astrological features.\n";
?>
