/**
 * Baum Cosmology Plugin JavaScript
 * Handles client-side astronomical calculations and interactive features
 */

(function($) {
  'use strict';
  
  // Plugin namespace
  window.BaumCosmology = window.BaumCosmology || {};
  
  /**
   * Initialize the plugin
   */
  function init() {
    // Initialize interactive elements
    initDatePickers();
    initLocationInputs();
    initRealTimeUpdates();
    initChartGenerators();
    
    // Add event listeners
    $(document).on('click', '.baum-refresh-data', refreshData);
    $(document).on('change', '.baum-date-input', updateDisplays);
    $(document).on('change', '.baum-location-input', updateDisplays);
  }
  
  /**
   * Initialize date picker inputs
   */
  function initDatePickers() {
    $('.baum-date-picker').each(function() {
      const $input = $(this);
      
      // Set default to today if empty
      if (!$input.val()) {
        $input.val(new Date().toISOString().split('T')[0]);
      }
      
      // Add change handler
      $input.on('change', function() {
        updateRelatedDisplays($(this));
      });
    });
  }
  
  /**
   * Initialize location input fields
   */
  function initLocationInputs() {
    $('.baum-location-inputs').each(function() {
      const $container = $(this);
      const $latInput = $container.find('.latitude-input');
      const $lonInput = $container.find('.longitude-input');
      const $nameInput = $container.find('.location-name-input');
      
      // Add geolocation button if supported
      if (navigator.geolocation) {
        const $geoBtn = $('<button type="button" class="baum-geo-btn">📍 Use My Location</button>');
        $container.append($geoBtn);
        
        $geoBtn.on('click', function() {
          getCurrentLocation($latInput, $lonInput, $nameInput);
        });
      }
    });
  }
  
  /**
   * Get current location using browser geolocation
   */
  function getCurrentLocation($latInput, $lonInput, $nameInput) {
    const $btn = $('.baum-geo-btn');
    $btn.prop('disabled', true).text('Getting location...');
    
    navigator.geolocation.getCurrentPosition(
      function(position) {
        $latInput.val(position.coords.latitude.toFixed(6));
        $lonInput.val(position.coords.longitude.toFixed(6));
        
        // Try to get location name via reverse geocoding
        reverseGeocode(position.coords.latitude, position.coords.longitude, $nameInput);
        
        $btn.prop('disabled', false).text('📍 Use My Location');
        updateDisplays();
      },
      function(error) {
        console.error('Geolocation error:', error);
        alert('Unable to get your location. Please enter coordinates manually.');
        $btn.prop('disabled', false).text('📍 Use My Location');
      }
    );
  }
  
  /**
   * Reverse geocode coordinates to get location name
   */
  function reverseGeocode(lat, lon, $nameInput) {
    // This would typically use a geocoding service
    // For now, just set a generic name
    $nameInput.val(`Location (${lat.toFixed(2)}, ${lon.toFixed(2)})`);
  }
  
  /**
   * Initialize real-time updates for current data
   */
  function initRealTimeUpdates() {
    // Update displays that show "current" data every minute
    setInterval(function() {
      $('.baum-real-time').each(function() {
        const $element = $(this);
        if ($element.data('auto-update') === true) {
          updateElementData($element);
        }
      });
    }, 60000); // Update every minute
  }
  
  /**
   * Initialize chart generators
   */
  function initChartGenerators() {
    $('.baum-generate-chart').on('click', function() {
      const $btn = $(this);
      const chartType = $btn.data('chart-type');
      const $container = $btn.closest('.baum-cosmology');
      
      generateChart(chartType, $container);
    });
  }
  
  /**
   * Generate astronomical charts
   */
  function generateChart(chartType, $container) {
    const date = $container.find('.baum-date-input').val() || new Date().toISOString().split('T')[0];
    const lat = parseFloat($container.find('.latitude-input').val()) || 40.7128;
    const lon = parseFloat($container.find('.longitude-input').val()) || -74.0060;
    
    switch (chartType) {
      case 'natal':
        generateNatalChart(date, lat, lon, $container);
        break;
      case 'transit':
        generateTransitChart(date, lat, lon, $container);
        break;
      case 'solar-return':
        generateSolarReturnChart(date, lat, lon, $container);
        break;
      default:
        console.error('Unknown chart type:', chartType);
    }
  }
  
  /**
   * Generate natal chart using client-side calculations
   */
  function generateNatalChart(date, lat, lon, $container) {
    if (!window.BaumMoshier) {
      console.error('Moshier ephemeris not loaded');
      return;
    }
    
    const chartDate = new Date(date);
    const positions = window.BaumMoshier.getAllPlanetaryPositions(chartDate);
    
    // Create chart HTML
    let chartHtml = '<div class="baum-natal-chart">';
    chartHtml += '<h4>Natal Chart Positions</h4>';
    chartHtml += '<table class="chart-positions-table">';
    chartHtml += '<thead><tr><th>Planet</th><th>Sign</th><th>Degree</th></tr></thead>';
    chartHtml += '<tbody>';
    
    for (const [planetKey, planetData] of Object.entries(positions)) {
      chartHtml += '<tr>';
      chartHtml += `<td><strong>${planetData.name}</strong></td>`;
      chartHtml += `<td>${planetData.sign}</td>`;
      chartHtml += `<td>${planetData.degreeInSign.toFixed(2)}°</td>`;
      chartHtml += '</tr>';
    }
    
    chartHtml += '</tbody></table>';
    chartHtml += '</div>';
    
    // Insert chart into container
    const $chartContainer = $container.find('.chart-output');
    if ($chartContainer.length) {
      $chartContainer.html(chartHtml);
    } else {
      $container.append(chartHtml);
    }
  }
  
  /**
   * Generate transit chart
   */
  function generateTransitChart(date, lat, lon, $container) {
    // Implementation for transit chart
    console.log('Generating transit chart for', date, lat, lon);
  }
  
  /**
   * Generate solar return chart
   */
  function generateSolarReturnChart(date, lat, lon, $container) {
    // Implementation for solar return chart
    console.log('Generating solar return chart for', date, lat, lon);
  }
  
  /**
   * Refresh data for a specific element
   */
  function refreshData() {
    const $btn = $(this);
    const $container = $btn.closest('.baum-cosmology');
    
    $btn.prop('disabled', true).text('Refreshing...');
    
    // Get current parameters
    const date = $container.find('.baum-date-input').val();
    const lat = $container.find('.latitude-input').val();
    const lon = $container.find('.longitude-input').val();
    
    // Make AJAX request to refresh server-side data
    $.ajax({
      url: baumCosmology.ajaxurl,
      type: 'POST',
      data: {
        action: 'baum_refresh_data',
        nonce: baumCosmology.nonce,
        date: date,
        latitude: lat,
        longitude: lon,
        container_type: $container.data('type')
      },
      success: function(response) {
        if (response.success) {
          $container.find('.baum-data-content').html(response.data.html);
        } else {
          console.error('Refresh failed:', response.data);
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', error);
      },
      complete: function() {
        $btn.prop('disabled', false).text('Refresh');
      }
    });
  }
  
  /**
   * Update displays when parameters change
   */
  function updateDisplays() {
    $('.baum-cosmology').each(function() {
      updateElementData($(this));
    });
  }
  
  /**
   * Update related displays when a specific input changes
   */
  function updateRelatedDisplays($input) {
    const $container = $input.closest('.baum-cosmology');
    updateElementData($container);
  }
  
  /**
   * Update data for a specific element
   */
  function updateElementData($element) {
    const elementType = $element.data('type');
    const autoUpdate = $element.data('auto-update');
    
    if (!autoUpdate) return;
    
    // Get current parameters
    const date = $element.find('.baum-date-input').val() || new Date().toISOString().split('T')[0];
    const lat = parseFloat($element.find('.latitude-input').val()) || 40.7128;
    const lon = parseFloat($element.find('.longitude-input').val()) || -74.0060;
    
    // Update client-side calculations
    if (window.BaumMoshier && elementType === 'planetary-positions') {
      updatePlanetaryPositionsDisplay($element, date);
    }
  }
  
  /**
   * Update planetary positions display with client-side calculations
   */
  function updatePlanetaryPositionsDisplay($element, date) {
    const chartDate = new Date(date);
    const positions = window.BaumMoshier.getAllPlanetaryPositions(chartDate);
    
    // Update each planet row
    $element.find('.planetary-positions-table tbody tr').each(function() {
      const $row = $(this);
      const planetName = $row.find('td:first').text().trim().toLowerCase();
      
      if (positions[planetName]) {
        const planetData = positions[planetName];
        $row.find('td:nth-child(2)').text(planetData.sign);
        $row.find('td:nth-child(3)').text(planetData.degreeInSign.toFixed(2) + '°');
        $row.find('td:nth-child(4)').text(planetData.longitude.toFixed(2) + '°');
        if ($row.find('td:nth-child(5)').length) {
          $row.find('td:nth-child(5)').text(planetData.motion);
        }
      }
    });
  }
  
  /**
   * Utility function to format degrees
   */
  function formatDegrees(degrees) {
    const deg = Math.floor(degrees);
    const min = Math.floor((degrees - deg) * 60);
    const sec = Math.floor(((degrees - deg) * 60 - min) * 60);
    return `${deg}° ${min}' ${sec}"`;
  }
  
  /**
   * Utility function to calculate aspects between planets
   */
  function calculateAspects(positions) {
    const aspects = [];
    const aspectAngles = {
      0: 'Conjunction',
      60: 'Sextile',
      90: 'Square',
      120: 'Trine',
      180: 'Opposition'
    };
    
    const planetNames = Object.keys(positions);
    
    for (let i = 0; i < planetNames.length; i++) {
      for (let j = i + 1; j < planetNames.length; j++) {
        const planet1 = planetNames[i];
        const planet2 = planetNames[j];
        const angle = Math.abs(positions[planet1].longitude - positions[planet2].longitude);
        const normalizedAngle = angle > 180 ? 360 - angle : angle;
        
        for (const [aspectAngle, aspectName] of Object.entries(aspectAngles)) {
          const orb = Math.abs(normalizedAngle - parseFloat(aspectAngle));
          if (orb <= 8) { // 8-degree orb
            aspects.push({
              planet1: positions[planet1].name,
              planet2: positions[planet2].name,
              aspect: aspectName,
              orb: orb.toFixed(2),
              exact: orb <= 1
            });
          }
        }
      }
    }
    
    return aspects.sort((a, b) => parseFloat(a.orb) - parseFloat(b.orb));
  }
  
  // Initialize when document is ready
  $(document).ready(init);
  
  // Expose public methods
  window.BaumCosmology.init = init;
  window.BaumCosmology.generateChart = generateChart;
  window.BaumCosmology.calculateAspects = calculateAspects;
  window.BaumCosmology.formatDegrees = formatDegrees;
  
})(jQuery);
