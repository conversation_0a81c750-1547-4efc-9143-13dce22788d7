/**
 * Simplified Moshier Ephemeris for Client-Side Calculations
 * Based on the Moshier Ephemeris algorithms
 * 
 * This is a simplified implementation for basic planetary position calculations
 * For precise calculations, use the full Swiss Ephemeris on the server side
 */

window.BaumMoshier = (function() {
  'use strict';
  
  // Constants
  const DEG_TO_RAD = Math.PI / 180.0;
  const RAD_TO_DEG = 180.0 / Math.PI;
  const J2000 = 2451545.0;
  
  // Planet data structure
  const PLANETS = {
    SUN: 0,
    MOON: 1,
    MERCURY: 2,
    VENUS: 3,
    MARS: 4,
    JUPITER: 5,
    SATURN: 6,
    URANUS: 7,
    NEPTUNE: 8,
    PLUTO: 9
  };
  
  // Simplified orbital elements (mean values for J2000.0)
  const ORBITAL_ELEMENTS = {
    [PLANETS.SUN]: {
      L: 280.46646,    // Mean longitude
      a: 1.00000261,   // Semi-major axis (AU)
      e: 0.01671123,   // Eccentricity
      i: 0.00005,      // Inclination
      omega: -11.26064, // Longitude of ascending node
      w: 102.93735,    // Argument of perihelion
      n: 0.98560028    // Mean motion (degrees/day)
    },
    [PLANETS.MOON]: {
      L: 218.3164477,
      a: 60.2666,      // Earth radii
      e: 0.0549,
      i: 5.1454,
      omega: 125.1228,
      w: 318.0634,
      n: 13.0649929509
    },
    [PLANETS.MERCURY]: {
      L: 252.25032350,
      a: 0.38709927,
      e: 0.20563593,
      i: 7.00497902,
      omega: 48.33076593,
      w: 77.45779628,
      n: 4.0923344368
    },
    [PLANETS.VENUS]: {
      L: 181.97909950,
      a: 0.72333566,
      e: 0.00677672,
      i: 3.39467605,
      omega: 76.67984255,
      w: 131.60246718,
      n: 1.6021302244
    },
    [PLANETS.MARS]: {
      L: 355.43299958,
      a: 1.52371034,
      e: 0.09339410,
      i: 1.84969142,
      omega: 49.55953891,
      w: 286.50199975,
      n: 0.5240207766
    },
    [PLANETS.JUPITER]: {
      L: 34.39644051,
      a: 5.20288700,
      e: 0.04838624,
      i: 1.30439695,
      omega: 100.47390909,
      w: 273.86740071,
      n: 0.0830853001
    },
    [PLANETS.SATURN]: {
      L: 50.07744430,
      a: 9.53667594,
      e: 0.05386179,
      i: 2.48599187,
      omega: 113.66242448,
      w: 339.39164343,
      n: 0.0334442282
    },
    [PLANETS.URANUS]: {
      L: 314.05500511,
      a: 19.18916464,
      e: 0.04725744,
      i: 0.77263783,
      omega: 74.01692503,
      w: 96.99839691,
      n: 0.0116574406
    },
    [PLANETS.NEPTUNE]: {
      L: 304.34866548,
      a: 30.06992276,
      e: 0.00859048,
      i: 1.77004347,
      omega: 131.78422574,
      w: 272.84973426,
      n: 0.0059309239
    },
    [PLANETS.PLUTO]: {
      L: 238.92903833,
      a: 39.48211675,
      e: 0.24882730,
      i: 17.14001206,
      omega: 110.30393684,
      w: 224.06891629,
      n: 0.0039757686
    }
  };
  
  /**
   * Calculate Julian Day from date
   * @param {Date} date JavaScript Date object
   * @returns {number} Julian Day
   */
  function getJulianDay(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();
    
    let y = year;
    let m = month;
    
    if (m <= 2) {
      y -= 1;
      m += 12;
    }
    
    const a = Math.floor(y / 100);
    const b = 2 - a + Math.floor(a / 4);
    
    const jd = Math.floor(365.25 * (y + 4716)) + 
               Math.floor(30.6001 * (m + 1)) + 
               day + b - 1524.5;
    
    const dayFraction = (hour + minute/60 + second/3600) / 24;
    
    return jd + dayFraction;
  }
  
  /**
   * Normalize angle to 0-360 degrees
   * @param {number} angle Angle in degrees
   * @returns {number} Normalized angle
   */
  function normalizeAngle(angle) {
    while (angle < 0) angle += 360;
    while (angle >= 360) angle -= 360;
    return angle;
  }
  
  /**
   * Calculate planet position
   * @param {number} planet Planet ID
   * @param {number} jd Julian Day
   * @returns {Object} Planet position data
   */
  function calculatePlanetPosition(planet, jd) {
    const elements = ORBITAL_ELEMENTS[planet];
    if (!elements) {
      throw new Error('Unknown planet: ' + planet);
    }
    
    // Days since J2000.0
    const T = (jd - J2000) / 36525.0;
    const days = jd - J2000;
    
    // Calculate mean longitude
    const L = normalizeAngle(elements.L + elements.n * days);
    
    // Calculate mean anomaly
    const M = normalizeAngle(L - elements.w);
    const M_rad = M * DEG_TO_RAD;
    
    // Solve Kepler's equation (simplified)
    let E = M_rad;
    for (let i = 0; i < 10; i++) {
      E = M_rad + elements.e * Math.sin(E);
    }
    
    // True anomaly
    const nu = 2 * Math.atan2(
      Math.sqrt(1 + elements.e) * Math.sin(E/2),
      Math.sqrt(1 - elements.e) * Math.cos(E/2)
    );
    
    // Heliocentric longitude
    const longitude = normalizeAngle((nu * RAD_TO_DEG) + elements.w);
    
    // Distance from Sun
    const r = elements.a * (1 - elements.e * Math.cos(E));
    
    // For geocentric coordinates, we need to account for Earth's position
    // This is a simplified calculation
    let geocentricLongitude = longitude;
    
    if (planet !== PLANETS.SUN) {
      // Get Earth's position
      const earthPos = calculatePlanetPosition(PLANETS.SUN, jd);
      geocentricLongitude = normalizeAngle(longitude - earthPos.longitude + 180);
    }
    
    return {
      longitude: geocentricLongitude,
      latitude: 0, // Simplified - ignoring orbital inclination
      distance: r,
      speed: elements.n // Simplified daily motion
    };
  }
  
  /**
   * Get zodiac sign from longitude
   * @param {number} longitude Longitude in degrees
   * @returns {Object} Sign information
   */
  function getZodiacSign(longitude) {
    const signs = [
      'Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
      'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
    ];
    
    const signIndex = Math.floor(longitude / 30);
    const degreeInSign = longitude - (signIndex * 30);
    
    return {
      sign: signs[signIndex],
      signIndex: signIndex,
      degreeInSign: degreeInSign
    };
  }
  
  /**
   * Calculate all planetary positions for a given date
   * @param {Date} date JavaScript Date object
   * @returns {Object} All planetary positions
   */
  function getAllPlanetaryPositions(date) {
    const jd = getJulianDay(date);
    const positions = {};
    
    const planetNames = {
      [PLANETS.SUN]: 'Sun',
      [PLANETS.MOON]: 'Moon',
      [PLANETS.MERCURY]: 'Mercury',
      [PLANETS.VENUS]: 'Venus',
      [PLANETS.MARS]: 'Mars',
      [PLANETS.JUPITER]: 'Jupiter',
      [PLANETS.SATURN]: 'Saturn',
      [PLANETS.URANUS]: 'Uranus',
      [PLANETS.NEPTUNE]: 'Neptune',
      [PLANETS.PLUTO]: 'Pluto'
    };
    
    for (const [planetId, planetName] of Object.entries(planetNames)) {
      const pos = calculatePlanetPosition(parseInt(planetId), jd);
      const sign = getZodiacSign(pos.longitude);
      
      positions[planetName.toLowerCase()] = {
        name: planetName,
        longitude: pos.longitude,
        sign: sign.sign,
        degreeInSign: sign.degreeInSign,
        speed: pos.speed,
        motion: pos.speed > 0 ? 'Direct' : 'Retrograde'
      };
    }
    
    return positions;
  }
  
  // Public API
  return {
    PLANETS: PLANETS,
    getJulianDay: getJulianDay,
    calculatePlanetPosition: calculatePlanetPosition,
    getZodiacSign: getZodiacSign,
    getAllPlanetaryPositions: getAllPlanetaryPositions,
    normalizeAngle: normalizeAngle
  };
})();
