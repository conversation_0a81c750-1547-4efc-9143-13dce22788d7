<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baum Cosmology Plugin Examples</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        .shortcode-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .description {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #495057;
            margin-top: 0;
        }
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 Baum Cosmology Plugin Examples</h1>
        
        <p>This page demonstrates the various shortcodes available in the Baum Cosmology WordPress plugin. Copy and paste these shortcodes into your WordPress posts or pages.</p>

        <div class="feature-grid">
            <div class="feature-card">
                <h3><span class="emoji">🌍</span>Seasons</h3>
                <p>Display information about the four seasons, current season, and seasonal transitions.</p>
            </div>
            <div class="feature-card">
                <h3><span class="emoji">☀️</span>Sun Times</h3>
                <p>Show sunrise, sunset, solar noon, and twilight times for any location.</p>
            </div>
            <div class="feature-card">
                <h3><span class="emoji">🌙</span>Moon Phases</h3>
                <p>Display current moon phase, illumination, and upcoming lunar events.</p>
            </div>
            <div class="feature-card">
                <h3><span class="emoji">🪐</span>Planet Visibility</h3>
                <p>Show which planets are visible tonight and when to observe them.</p>
            </div>
            <div class="feature-card">
                <h3><span class="emoji">☄️</span>Meteor Showers</h3>
                <p>List upcoming meteor showers with peak dates and viewing information.</p>
            </div>
            <div class="feature-card">
                <h3><span class="emoji">🌑</span>Eclipses</h3>
                <p>Display information about upcoming solar and lunar eclipses.</p>
            </div>
        </div>

        <h2>🌍 Season Shortcodes</h2>
        
        <div class="description">Display the current season with dates and description:</div>
        <div class="shortcode-example">
[current_season hemisphere="northern" show_dates="yes" show_description="yes"]
        </div>

        <div class="description">Show information about a specific season:</div>
        <div class="shortcode-example">
[season_info season="spring" hemisphere="northern" show_dates="yes" show_description="yes"]
        </div>

        <div class="description">Display the next season with countdown:</div>
        <div class="shortcode-example">
[next_season hemisphere="northern" show_dates="yes" show_description="yes" show_countdown="yes"]
        </div>

        <div class="description">Show all four seasons in a table:</div>
        <div class="shortcode-example">
[all_seasons hemisphere="northern" show_dates="yes" show_description="yes"]
        </div>

        <h2>☀️ Sun Information</h2>
        
        <div class="description">Display sunrise, sunset, and twilight times for New York:</div>
        <div class="shortcode-example">
[sun_times date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_twilight="yes" table_style="yes"]
        </div>

        <div class="description">Simple sun times without twilight information:</div>
        <div class="shortcode-example">
[sun_times latitude="51.5074" longitude="-0.1278" location_name="London" show_twilight="no" table_style="yes"]
        </div>

        <h2>🌙 Moon Information</h2>
        
        <div class="description">Show current moon phase with upcoming phases:</div>
        <div class="shortcode-example">
[moon_info date="2025-01-15" show_next_phases="yes" show_illumination="yes" table_style="yes"]
        </div>

        <div class="description">Simple moon phase display:</div>
        <div class="shortcode-example">
[moon_info show_next_phases="no" show_illumination="yes" table_style="no"]
        </div>

        <h2>🪐 Planet Visibility</h2>
        
        <div class="description">Show visible planets with detailed information:</div>
        <div class="shortcode-example">
[planet_visibility date="2025-01-15" show_magnitude="yes" show_constellation="yes" show_best_time="yes" table_style="yes"]
        </div>

        <div class="description">Simple planet visibility list:</div>
        <div class="shortcode-example">
[planet_visibility show_magnitude="no" show_constellation="yes" show_best_time="yes" table_style="no"]
        </div>

        <h2>☄️ Meteor Showers</h2>
        
        <div class="description">Show meteor showers for the next 90 days:</div>
        <div class="shortcode-example">
[meteor_showers date="2025-01-15" days_ahead="90" show_zhr="yes" show_radiant="yes" show_best_time="yes" table_style="yes"]
        </div>

        <div class="description">Show meteor showers for the next 30 days in list format:</div>
        <div class="shortcode-example">
[meteor_showers days_ahead="30" show_zhr="yes" show_radiant="no" show_best_time="yes" table_style="no"]
        </div>

        <h2>🌑 Eclipse Information</h2>
        
        <div class="description">Show eclipses for the next 2 years:</div>
        <div class="shortcode-example">
[eclipse_info date="2025-01-15" years_ahead="2" show_magnitude="yes" show_duration="yes" show_visibility="yes" table_style="yes"]
        </div>

        <div class="description">Simple eclipse list for the next year:</div>
        <div class="shortcode-example">
[eclipse_info years_ahead="1" show_magnitude="no" show_duration="yes" show_visibility="yes" table_style="no"]
        </div>

        <h2>📅 Comprehensive Astronomy Calendar</h2>
        
        <div class="description">Complete astronomy calendar for New York:</div>
        <div class="shortcode-example">
[astro_calendar date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_sun="yes" show_moon="yes" show_planets="yes" show_meteors="yes" show_eclipses="yes" compact="no"]
        </div>

        <div class="description">Compact astronomy calendar:</div>
        <div class="shortcode-example">
[astro_calendar latitude="51.5074" longitude="-0.1278" location_name="London" show_sun="yes" show_moon="yes" show_planets="yes" show_meteors="yes" show_eclipses="yes" compact="yes"]
        </div>

        <div class="description">Sun and moon only calendar:</div>
        <div class="shortcode-example">
[astro_calendar latitude="35.6762" longitude="139.6503" location_name="Tokyo" show_sun="yes" show_moon="yes" show_planets="no" show_meteors="no" show_eclipses="no" compact="no"]
        </div>

        <h2>🎯 Usage Tips</h2>
        
        <ul>
            <li><strong>Coordinates:</strong> Use decimal degrees for latitude and longitude. Positive values for North/East, negative for South/West.</li>
            <li><strong>Dates:</strong> Use YYYY-MM-DD format. If omitted, today's date is used.</li>
            <li><strong>Table vs List:</strong> Set <code>table_style="yes"</code> for tabular data, <code>table_style="no"</code> for list format.</li>
            <li><strong>Hemispheres:</strong> Use <code>hemisphere="southern"</code> for locations south of the equator.</li>
            <li><strong>Responsive:</strong> All displays are mobile-friendly and responsive.</li>
            <li><strong>Styling:</strong> The plugin includes comprehensive CSS that adapts to your theme.</li>
        </ul>

        <h2>🌐 Popular Locations</h2>
        
        <p>Here are coordinates for some popular cities to use in your shortcodes:</p>
        
        <ul>
            <li><strong>New York:</strong> latitude="40.7128" longitude="-74.0060"</li>
            <li><strong>London:</strong> latitude="51.5074" longitude="-0.1278"</li>
            <li><strong>Tokyo:</strong> latitude="35.6762" longitude="139.6503"</li>
            <li><strong>Sydney:</strong> latitude="-33.8688" longitude="151.2093" hemisphere="southern"</li>
            <li><strong>Paris:</strong> latitude="48.8566" longitude="2.3522"</li>
            <li><strong>Los Angeles:</strong> latitude="34.0522" longitude="-118.2437"</li>
            <li><strong>Cairo:</strong> latitude="30.0444" longitude="31.2357"</li>
            <li><strong>Mumbai:</strong> latitude="19.0760" longitude="72.8777"</li>
        </ul>
    </div>
</body>
</html>
