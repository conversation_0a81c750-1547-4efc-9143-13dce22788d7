<?php
/**
 * Test file for Baum Cosmology Plugin
 * This file can be used to test the plugin functionality outside of WordPress
 */

// Mock WordPress functions for testing
if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('_n')) {
    function _n($single, $plural, $number, $domain = 'default') {
        return $number == 1 ? $single : $plural;
    }
}

if (!function_exists('esc_html')) {
    function esc_html($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('sanitize_text_field')) {
    function sanitize_text_field($str) {
        return trim(strip_tags($str));
    }
}

if (!function_exists('shortcode_atts')) {
    function shortcode_atts($pairs, $atts, $shortcode = '') {
        return array_merge($pairs, (array) $atts);
    }
}

if (!function_exists('plugin_dir_url')) {
    function plugin_dir_url($file) {
        return 'assets/';
    }
}

if (!function_exists('wp_enqueue_style')) {
    function wp_enqueue_style($handle, $src = '', $deps = array(), $ver = false, $media = 'all') {
        // Mock function
    }
}

if (!function_exists('add_action')) {
    function add_action($tag, $function_to_add, $priority = 10, $accepted_args = 1) {
        // Mock function
    }
}

if (!function_exists('add_shortcode')) {
    function add_shortcode($tag, $func) {
        // Mock function
    }
}

// Include the main plugin file
require_once 'four-seasons.php';

// Create an instance of the plugin
$baum_cosmology = new Baum_Cosmology();

echo "Baum Cosmology Plugin Test\n";
echo "==========================\n\n";

// Test sun times
echo "Sun Times Test:\n";
$sun_data = $baum_cosmology->get_sun_times('2025-01-15', 40.7128, -74.0060);
echo "Sunrise: " . $sun_data['sunrise'] . "\n";
echo "Sunset: " . $sun_data['sunset'] . "\n";
echo "Day Length: " . $sun_data['day_length'] . "\n\n";

// Test moon phase
echo "Moon Phase Test:\n";
$moon_data = $baum_cosmology->get_moon_phase('2025-01-15');
echo "Phase: " . $moon_data['phase_name'] . "\n";
echo "Illumination: " . $moon_data['illumination'] . "%\n\n";

// Test visible planets
echo "Visible Planets Test:\n";
$planets = $baum_cosmology->get_visible_planets('2025-01-15');
foreach ($planets as $planet) {
    echo "- " . $planet['name'] . " (Magnitude: " . $planet['magnitude'] . ")\n";
}
echo "\n";

// Test meteor showers
echo "Meteor Showers Test:\n";
$meteors = $baum_cosmology->get_meteor_showers('2025-01-15', 90);
foreach ($meteors as $shower) {
    echo "- " . $shower['name'] . " peaks on " . date('M j', strtotime($shower['peak_date'])) . "\n";
}
echo "\n";

// Test eclipses
echo "Eclipses Test:\n";
$eclipses = $baum_cosmology->get_eclipses('2025-01-15', 2);
foreach ($eclipses as $eclipse) {
    echo "- " . $eclipse['name'] . " on " . date('M j, Y', strtotime($eclipse['date'])) . "\n";
}
echo "\n";

// Test seasons
echo "Current Season Test:\n";
$current_season = $baum_cosmology->get_current_season('northern');
echo "Current Season: " . $current_season['name'] . "\n";
echo "Description: " . $current_season['description'] . "\n\n";

echo "Plugin Test Complete!\n";
echo "All functions executed successfully. The plugin is ready for use in WordPress.\n";
?>
