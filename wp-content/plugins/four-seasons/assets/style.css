/**
 * Baum Cosmology Plugin Styles
 * Comprehensive styling for astronomy and astrology tables
 */

/* Base container styles */
.baum-cosmology {
  margin: 20px 0;
  padding: 20px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.baum-cosmology h2,
.baum-cosmology h3,
.baum-cosmology h4 {
  color: #2c3e50;
  margin-top: 0;
}

.baum-cosmology h2 {
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.baum-cosmology h3 {
  color: #34495e;
  margin-bottom: 15px;
}

.baum-cosmology .date,
.baum-cosmology .date-range {
  color: #7f8c8d;
  font-style: italic;
  margin-bottom: 15px;
}

/* Table styles */
.baum-cosmology table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.baum-cosmology table th,
.baum-cosmology table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #ecf0f1;
}

.baum-cosmology table th {
  background: #34495e;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9em;
  letter-spacing: 0.5px;
}

.baum-cosmology table tr:hover {
  background: #f8f9fa;
}

.baum-cosmology table tr:nth-child(even) {
  background: #fdfdfd;
}

/* Section-specific table styles */
.sun-times-table th {
  background: #f39c12;
}

.moon-info-table th {
  background: #9b59b6;
}

.planet-visibility-table th {
  background: #e74c3c;
}

.meteor-showers-table th {
  background: #1abc9c;
}

.eclipse-info-table th {
  background: #2c3e50;
}

/* Special row styles */
.twilight-header td,
.next-phases-header td {
  background: #ecf0f1 !important;
  font-weight: bold;
  color: #2c3e50;
  text-align: center;
}

/* List styles for non-table format */
.baum-cosmology .sun-times-list,
.baum-cosmology .moon-info-list,
.baum-cosmology .planet-visibility-list,
.baum-cosmology .meteor-showers-list,
.baum-cosmology .eclipse-info-list {
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.baum-cosmology .planet-item,
.baum-cosmology .shower-item,
.baum-cosmology .eclipse-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-left: 4px solid #3498db;
  border-radius: 0 5px 5px 0;
}

.baum-cosmology .planet-item:last-child,
.baum-cosmology .shower-item:last-child,
.baum-cosmology .eclipse-item:last-child {
  margin-bottom: 0;
}

.baum-cosmology .planet-item h4,
.baum-cosmology .shower-item h4,
.baum-cosmology .eclipse-item h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

/* Status indicators */
.peak-today,
.eclipse-today {
  background: #e74c3c;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
}

.peak-soon,
.eclipse-soon {
  background: #f39c12;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
}

.peak-countdown {
  background: #95a5a6;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
}

.current-label {
  background: #27ae60;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: normal;
}

/* Astro Calendar specific styles */
.astro-calendar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.astro-calendar h2,
.astro-calendar h3,
.astro-calendar h4 {
  color: white;
}

.astro-calendar h2 {
  border-bottom: 2px solid rgba(255,255,255,0.3);
}

.astro-section {
  margin: 25px 0;
  padding: 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.astro-section:last-child {
  margin-bottom: 0;
}

.astro-section .baum-cosmology {
  background: rgba(255,255,255,0.95);
  color: #2c3e50;
  margin: 15px 0 0 0;
}

/* Season styles (existing) */
.four-seasons.current-season,
.four-seasons.season-info,
.four-seasons.next-season {
  border-left: 4px solid #27ae60;
}

.four-seasons.all-seasons .season-item {
  margin-bottom: 15px;
  padding: 15px;
  background: white;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.four-seasons.all-seasons .season-item.current-season {
  border-left: 4px solid #27ae60;
  background: #f0fff4;
}

/* Responsive design */
@media (max-width: 768px) {
  .baum-cosmology {
    padding: 15px;
    margin: 15px 0;
  }
  
  .baum-cosmology table {
    font-size: 0.9em;
  }
  
  .baum-cosmology table th,
  .baum-cosmology table td {
    padding: 8px 10px;
  }
  
  .astro-section {
    padding: 15px;
  }
  
  /* Stack table cells on very small screens */
  @media (max-width: 480px) {
    .baum-cosmology table,
    .baum-cosmology table thead,
    .baum-cosmology table tbody,
    .baum-cosmology table th,
    .baum-cosmology table td,
    .baum-cosmology table tr {
      display: block;
    }
    
    .baum-cosmology table thead tr {
      position: absolute;
      top: -9999px;
      left: -9999px;
    }
    
    .baum-cosmology table tr {
      border: 1px solid #ccc;
      margin-bottom: 10px;
      padding: 10px;
      border-radius: 5px;
      background: white;
    }
    
    .baum-cosmology table td {
      border: none;
      position: relative;
      padding-left: 50% !important;
      padding-top: 8px;
      padding-bottom: 8px;
    }
    
    .baum-cosmology table td:before {
      content: attr(data-label) ": ";
      position: absolute;
      left: 6px;
      width: 45%;
      padding-right: 10px;
      white-space: nowrap;
      font-weight: bold;
      color: #2c3e50;
    }
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .baum-cosmology {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
  }
  
  .baum-cosmology h2,
  .baum-cosmology h3,
  .baum-cosmology h4 {
    color: #ecf0f1;
  }
  
  .baum-cosmology table {
    background: #34495e;
  }
  
  .baum-cosmology table tr:hover {
    background: #3c4f66;
  }
  
  .baum-cosmology table tr:nth-child(even) {
    background: #2f3d4e;
  }
  
  .baum-cosmology .planet-item,
  .baum-cosmology .shower-item,
  .baum-cosmology .eclipse-item {
    background: #34495e;
    color: #ecf0f1;
  }
}
