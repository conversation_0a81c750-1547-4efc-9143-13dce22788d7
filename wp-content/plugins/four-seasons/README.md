# Baum Cosmology WordPress Plugin

A comprehensive astronomy and astrology plugin for WordPress that provides detailed information about seasons, sun/moon data, planet visibility, meteor showers, eclipses, and more.

## Features

### 🌍 Seasons
- Current season information
- All four seasons data
- Next season countdown
- Support for both Northern and Southern hemispheres

### ☀️ Sun Information
- Sunrise and sunset times
- Solar noon
- Day length calculations
- Twilight times (civil, nautical, astronomical)
- Location-based calculations

### 🌙 Moon Information
- Current moon phase
- Illumination percentage
- Next major moon phases
- Lunar cycle information

### 🪐 Planet Visibility
- Currently visible planets
- Planet magnitudes
- Best viewing times
- Constellation locations

### ☄️ Meteor Showers
- Upcoming meteor showers
- Peak dates and activity periods
- Zenith Hourly Rate (ZHR)
- Radiant constellations

### 🌑 Eclipses
- Upcoming solar and lunar eclipses
- Eclipse types and durations
- Visibility regions
- Magnitude information

### 📅 Comprehensive Astronomy Calendar
- Combined view of all astronomical events
- Customizable display options
- Location-specific data

## Shortcodes

### Season Shortcodes
```
[current_season hemisphere="northern" show_dates="yes" show_description="yes"]
[season_info season="spring" hemisphere="northern" show_dates="yes" show_description="yes"]
[next_season hemisphere="northern" show_dates="yes" show_description="yes" show_countdown="yes"]
[all_seasons hemisphere="northern" show_dates="yes" show_description="yes"]
```

### Astronomy Shortcodes
```
[sun_times date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_twilight="yes" table_style="yes"]

[moon_info date="2025-01-15" show_next_phases="yes" show_illumination="yes" table_style="yes"]

[planet_visibility date="2025-01-15" show_magnitude="yes" show_constellation="yes" show_best_time="yes" table_style="yes"]

[meteor_showers date="2025-01-15" days_ahead="90" show_zhr="yes" show_radiant="yes" show_best_time="yes" table_style="yes"]

[eclipse_info date="2025-01-15" years_ahead="2" show_magnitude="yes" show_duration="yes" show_visibility="yes" table_style="yes"]

[astro_calendar date="2025-01-15" latitude="40.7128" longitude="-74.0060" location_name="New York" show_sun="yes" show_moon="yes" show_planets="yes" show_meteors="yes" show_eclipses="yes" compact="no"]
```

## Shortcode Parameters

### Common Parameters
- `date` - Date in YYYY-MM-DD format (default: today)
- `table_style` - Display as table ("yes") or list ("no") (default: "yes")

### Location Parameters
- `latitude` - Latitude in decimal degrees (default: 40.7128 - New York)
- `longitude` - Longitude in decimal degrees (default: -74.0060 - New York)
- `location_name` - Display name for the location (default: "New York")

### Season Parameters
- `hemisphere` - "northern" or "southern" (default: "northern")
- `season` - "spring", "summer", "autumn", or "winter"
- `show_dates` - Show season dates ("yes" or "no")
- `show_description` - Show season descriptions ("yes" or "no")
- `show_countdown` - Show countdown to next season ("yes" or "no")

### Sun Parameters
- `show_twilight` - Show twilight times ("yes" or "no")

### Moon Parameters
- `show_next_phases` - Show upcoming moon phases ("yes" or "no")
- `show_illumination` - Show illumination percentage ("yes" or "no")

### Planet Parameters
- `show_magnitude` - Show planet magnitudes ("yes" or "no")
- `show_constellation` - Show constellation locations ("yes" or "no")
- `show_best_time` - Show best viewing times ("yes" or "no")

### Meteor Shower Parameters
- `days_ahead` - Number of days to look ahead (default: 90)
- `show_zhr` - Show Zenith Hourly Rate ("yes" or "no")
- `show_radiant` - Show radiant constellation ("yes" or "no")
- `show_best_time` - Show best viewing times ("yes" or "no")

### Eclipse Parameters
- `years_ahead` - Number of years to look ahead (default: 2)
- `show_magnitude` - Show eclipse magnitude ("yes" or "no")
- `show_duration` - Show eclipse duration ("yes" or "no")
- `show_visibility` - Show visibility regions ("yes" or "no")

### Astro Calendar Parameters
- `show_sun` - Include sun information ("yes" or "no")
- `show_moon` - Include moon information ("yes" or "no")
- `show_planets` - Include planet visibility ("yes" or "no")
- `show_meteors` - Include meteor showers ("yes" or "no")
- `show_eclipses` - Include eclipse information ("yes" or "no")
- `compact` - Use compact display format ("yes" or "no")

## Installation

1. Upload the plugin files to `/wp-content/plugins/baum-cosmology/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Use the shortcodes in your posts, pages, or widgets

## Styling

The plugin includes comprehensive CSS styling with:
- Responsive design for mobile devices
- Dark mode support
- Beautiful table layouts
- Status indicators for upcoming events
- Gradient backgrounds for the astronomy calendar

## Technical Notes

- All astronomical calculations are performed using standard astronomical formulas
- Sun and moon calculations are approximations suitable for general use
- For precise scientific applications, consider using more specialized astronomical libraries
- Planet visibility is simplified and may not reflect exact current positions
- Eclipse and meteor shower data includes major events for 2025-2026

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher

## License

GPL-2.0+

## Support

For support and feature requests, please contact the plugin author.
