<?php
/**
 * Plugin Name: Four Seasons
 * Plugin URI: https://example.com/four-seasons
 * Description: Provides functions and shortcodes for displaying information about the four seasons.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL-2.0+
 * Text Domain: four-seasons
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
  die;
}

/**
 * Class to handle all Four Seasons functionality
 */
class Four_Seasons {
  /**
   * Initialize the plugin
   */
  public function __construct() {
    // Register shortcodes
    add_shortcode('current_season', array($this, 'current_season_shortcode'));
    add_shortcode('season_info', array($this, 'season_info_shortcode'));
    add_shortcode('next_season', array($this, 'next_season_shortcode'));
    add_shortcode('all_seasons', array($this, 'all_seasons_shortcode'));
    
    // Enqueue styles
    add_action('wp_enqueue_scripts', array($this, 'enqueue_styles'));
  }
  
  /**
   * Enqueue plugin styles
   */
  public function enqueue_styles() {
    wp_enqueue_style(
      'four-seasons-styles',
      plugin_dir_url(__FILE__) . 'assets/css/four-seasons.css',
      array(),
      '1.0.0'
    );
    
    // Enqueue dashicons if not already loaded
    wp_enqueue_style('dashicons');
  }
  
  /**
   * Shortcode for displaying current season
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function current_season_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'current_season');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $season = $this->get_current_season($hemisphere);
    
    $output = '<div class="four-seasons current-season">';
    $output .= '<h3>' . sprintf(__('Current Season: %s', 'four-seasons'), esc_html($season['name'])) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'four-seasons'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying specific season information
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function season_info_shortcode($atts) {
    $atts = shortcode_atts(array(
      'season' => 'spring',
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'season_info');
    
    $season_name = sanitize_text_field($atts['season']);
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $seasons = $this->get_seasons_data($hemisphere);
    
    if (!isset($seasons[$season_name])) {
      return '<p>' . __('Invalid season specified.', 'four-seasons') . '</p>';
    }
    
    $season = $seasons[$season_name];
    
    $output = '<div class="four-seasons season-info">';
    $output .= '<h3>' . esc_html($season['name']) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'four-seasons'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying next season
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function next_season_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes',
      'show_countdown' => 'yes'
    ), $atts, 'next_season');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    $show_countdown = sanitize_text_field($atts['show_countdown']) === 'yes';
    
    $season = $this->get_next_season($hemisphere);
    
    $output = '<div class="four-seasons next-season">';
    $output .= '<h3>' . sprintf(__('Next Season: %s', 'four-seasons'), esc_html($season['name'])) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'four-seasons'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    if ($show_countdown) {
      $now = new DateTime();
      $start = new DateTime($season['start_date']);
      $interval = $now->diff($start);
      
      if ($interval->invert == 0) { // If start date is in the future
        $days = $interval->days;
        $output .= '<p>' . sprintf(
          _n(
            'Starting in %d day',
            'Starting in %d days',
            $days,
            'four-seasons'
          ),
          $days
        ) . '</p>';
      }
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying all seasons
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function all_seasons_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'all_seasons');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $seasons = $this->get_seasons_data($hemisphere);
    $current_season = $this->get_current_season($hemisphere);
    
    $output = '<div class="four-seasons all-seasons">';
    $output .= '<h3>' . __('The Four Seasons', 'four-seasons') . '</h3>';
    
    foreach ($seasons as $season_key => $season) {
      $is_current = ($season['name'] === $current_season['name']);
      
      $output .= '<div class="season-item' . ($is_current ? ' current-season' : '') . '">';
      $output .= '<h4>' . esc_html($season['name']) . 
                 ($is_current ? ' <span class="current-label">(' . __('Current', 'four-seasons') . ')</span>' : '') . 
                 '</h4>';
      
      if ($show_description) {
        $output .= '<p>' . esc_html($season['description']) . '</p>';
      }
      
      if ($show_dates) {
        $start = new DateTime($season['start_date']);
        $end = new DateTime($season['end_date']);
        $output .= '<p>' . sprintf(
          __('From %s to %s', 'four-seasons'),
          esc_html($start->format('F j')),
          esc_html($end->format('F j'))
        ) . '</p>';
      }
      
      $output .= '</div>';
    }
    
    $output .= '</div>';
    
    return $output;
  }
}
