<?php
/**
 * Plugin Name: Baum Cosmology
 * Plugin URI: https://example.com/baum-cosmology
 * Description: Comprehensive astronomy and astrology plugin providing seasons, sun/moon data, planet visibility, meteor showers, eclipses, and more.
 * Version: 2.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL-2.0+
 * Text Domain: baum-cosmology
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
  die;
}

/**
 * Class to handle all Baum Cosmology functionality
 */
class Baum_Cosmology {
  /**
   * Initialize the plugin
   */
  public function __construct() {
    // Register season shortcodes
    add_shortcode('current_season', array($this, 'current_season_shortcode'));
    add_shortcode('season_info', array($this, 'season_info_shortcode'));
    add_shortcode('next_season', array($this, 'next_season_shortcode'));
    add_shortcode('all_seasons', array($this, 'all_seasons_shortcode'));

    // Register astronomy shortcodes
    add_shortcode('sun_times', array($this, 'sun_times_shortcode'));
    add_shortcode('moon_info', array($this, 'moon_info_shortcode'));
    add_shortcode('planet_visibility', array($this, 'planet_visibility_shortcode'));
    add_shortcode('meteor_showers', array($this, 'meteor_showers_shortcode'));
    add_shortcode('eclipse_info', array($this, 'eclipse_info_shortcode'));
    add_shortcode('astro_calendar', array($this, 'astro_calendar_shortcode'));

    // Register astrology shortcodes (Swiss Ephemeris based)
    add_shortcode('planetary_positions', array($this, 'planetary_positions_shortcode'));
    add_shortcode('lunar_phases', array($this, 'lunar_phases_shortcode'));
    add_shortcode('daily_transits', array($this, 'daily_transits_shortcode'));
    add_shortcode('zodiac_ingresses', array($this, 'zodiac_ingresses_shortcode'));
    add_shortcode('moon_void_course', array($this, 'moon_void_course_shortcode'));
    add_shortcode('natal_chart', array($this, 'natal_chart_shortcode'));
    add_shortcode('astrology_calendar', array($this, 'astrology_calendar_shortcode'));

    // Register advanced astrology shortcodes
    add_shortcode('asteroid_positions', array($this, 'asteroid_positions_shortcode'));
    add_shortcode('planetary_influence', array($this, 'planetary_influence_shortcode'));
    add_shortcode('horoscope_generator', array($this, 'horoscope_generator_shortcode'));
    add_shortcode('galactic_center', array($this, 'galactic_center_shortcode'));
    add_shortcode('historical_comparison', array($this, 'historical_comparison_shortcode'));
    add_shortcode('astrology_timeline', array($this, 'astrology_timeline_shortcode'));
    add_shortcode('user_horoscope', array($this, 'user_horoscope_shortcode'));

    // Enqueue styles and scripts
    add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
  }

  /**
   * Enqueue plugin styles and scripts
   */
  public function enqueue_assets() {
    wp_enqueue_style(
      'baum-cosmology-styles',
      plugin_dir_url(__FILE__) . 'assets/style.css',
      array(),
      '2.0.0'
    );

    // Enqueue Moshier ephemeris for client-side calculations
    wp_enqueue_script(
      'moshier-ephemeris',
      plugin_dir_url(__FILE__) . 'assets/moshier.js',
      array(),
      '2.0.0',
      true
    );

    // Enqueue main astrology script
    wp_enqueue_script(
      'baum-cosmology-script',
      plugin_dir_url(__FILE__) . 'assets/script.js',
      array('moshier-ephemeris'),
      '2.0.0',
      true
    );

    // Localize script for AJAX
    wp_localize_script('baum-cosmology-script', 'baumCosmology', array(
      'ajaxurl' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('baum_cosmology_nonce')
    ));
  }

  /**
   * Get current season based on date and hemisphere
   * 
   * @param string $hemisphere 'northern' or 'southern'
   * @return array Season data
   */
  public function get_current_season($hemisphere = 'northern') {
    $date = new DateTime();
    $month = (int)$date->format('n');
    $day = (int)$date->format('j');
    
    return $this->determine_season($month, $day, $hemisphere);
  }
  
  /**
   * Determine season based on month, day and hemisphere
   * 
   * @param int $month Month (1-12)
   * @param int $day Day of month
   * @param string $hemisphere 'northern' or 'southern'
   * @return array Season data
   */
  private function determine_season($month, $day, $hemisphere = 'northern') {
    $seasons = $this->get_seasons_data($hemisphere);
    
    // Meteorological seasons (simpler approach)
    if ($month == 12 || $month == 1 || $month == 2) {
      return $hemisphere === 'northern' ? $seasons['winter'] : $seasons['summer'];
    } elseif ($month == 3 || $month == 4 || $month == 5) {
      return $hemisphere === 'northern' ? $seasons['spring'] : $seasons['autumn'];
    } elseif ($month == 6 || $month == 7 || $month == 8) {
      return $hemisphere === 'northern' ? $seasons['summer'] : $seasons['winter'];
    } else {
      return $hemisphere === 'northern' ? $seasons['autumn'] : $seasons['spring'];
    }
  }
  
  /**
   * Get data for all seasons
   * 
   * @param string $hemisphere 'northern' or 'southern'
   * @return array Seasons data
   */
  public function get_seasons_data($hemisphere = 'northern') {
    $current_year = (int)(new DateTime())->format('Y');
    
    if ($hemisphere === 'northern') {
      return [
        'spring' => [
          'name' => __('Spring', 'baum-cosmology'),
          'description' => __('Season of renewal and growth', 'baum-cosmology'),
          'start_date' => sprintf('%d-03-20', $current_year),
          'end_date' => sprintf('%d-06-20', $current_year),
          'icon' => 'dashicons-palmtree'
        ],
        'summer' => [
          'name' => __('Summer', 'baum-cosmology'),
          'description' => __('Season of warmth and abundance', 'baum-cosmology'),
          'start_date' => sprintf('%d-06-21', $current_year),
          'end_date' => sprintf('%d-09-22', $current_year),
          'icon' => 'dashicons-sun'
        ],
        'autumn' => [
          'name' => __('Autumn', 'baum-cosmology'),
          'description' => __('Season of harvest and change', 'baum-cosmology'),
          'start_date' => sprintf('%d-09-23', $current_year),
          'end_date' => sprintf('%d-12-20', $current_year),
          'icon' => 'dashicons-falling-leaves'
        ],
        'winter' => [
          'name' => __('Winter', 'baum-cosmology'),
          'description' => __('Season of rest and reflection', 'baum-cosmology'),
          'start_date' => sprintf('%d-12-21', $current_year),
          'end_date' => sprintf('%d-03-19', $current_year + 1),
          'icon' => 'dashicons-snowflake'
        ]
      ];
    } else {
      // Southern hemisphere (seasons reversed)
      return [
        'spring' => [
          'name' => __('Spring', 'baum-cosmology'),
          'description' => __('Season of renewal and growth', 'baum-cosmology'),
          'start_date' => sprintf('%d-09-23', $current_year),
          'end_date' => sprintf('%d-12-20', $current_year),
          'icon' => 'dashicons-palmtree'
        ],
        'summer' => [
          'name' => __('Summer', 'baum-cosmology'),
          'description' => __('Season of warmth and abundance', 'baum-cosmology'),
          'start_date' => sprintf('%d-12-21', $current_year),
          'end_date' => sprintf('%d-03-19', $current_year + 1),
          'icon' => 'dashicons-sun'
        ],
        'autumn' => [
          'name' => __('Autumn', 'baum-cosmology'),
          'description' => __('Season of harvest and change', 'baum-cosmology'),
          'start_date' => sprintf('%d-03-20', $current_year),
          'end_date' => sprintf('%d-06-20', $current_year),
          'icon' => 'dashicons-falling-leaves'
        ],
        'winter' => [
          'name' => __('Winter', 'baum-cosmology'),
          'description' => __('Season of rest and reflection', 'baum-cosmology'),
          'start_date' => sprintf('%d-06-21', $current_year),
          'end_date' => sprintf('%d-09-22', $current_year),
          'icon' => 'dashicons-snowflake'
        ]
      ];
    }
  }
  
  /**
   * Get next season based on current date
   * 
   * @param string $hemisphere 'northern' or 'southern'
   * @return array Next season data
   */
  public function get_next_season($hemisphere = 'northern') {
    $current_season = $this->get_current_season($hemisphere);
    $seasons = $this->get_seasons_data($hemisphere);
    $season_keys = array_keys($seasons);
    
    $current_index = array_search(array_search($current_season, $seasons), $season_keys);
    $next_index = ($current_index + 1) % 4;
    
    return $seasons[$season_keys[$next_index]];
  }
  
  /**
   * Shortcode for displaying current season
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function current_season_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'current_season');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $season = $this->get_current_season($hemisphere);
    
    $output = '<div class="baum-cosmology four-seasons current-season">';
    $output .= '<h3>' . esc_html($season['name']) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'baum-cosmology'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying specific season information
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function season_info_shortcode($atts) {
    $atts = shortcode_atts(array(
      'season' => 'spring',
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'season_info');
    
    $season_name = sanitize_text_field($atts['season']);
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $seasons = $this->get_seasons_data($hemisphere);
    
    if (!isset($seasons[$season_name])) {
      return '<p>' . __('Invalid season specified.', 'baum-cosmology') . '</p>';
    }

    $season = $seasons[$season_name];

    $output = '<div class="baum-cosmology four-seasons season-info">';
    $output .= '<h3>' . esc_html($season['name']) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'baum-cosmology'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying next season
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function next_season_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes',
      'show_countdown' => 'yes'
    ), $atts, 'next_season');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    $show_countdown = sanitize_text_field($atts['show_countdown']) === 'yes';
    
    $season = $this->get_next_season($hemisphere);
    
    $output = '<div class="baum-cosmology four-seasons next-season">';
    $output .= '<h3>' . sprintf(__('Next Season: %s', 'baum-cosmology'), esc_html($season['name'])) . '</h3>';
    
    if ($show_description) {
      $output .= '<p>' . esc_html($season['description']) . '</p>';
    }
    
    if ($show_dates) {
      $start = new DateTime($season['start_date']);
      $end = new DateTime($season['end_date']);
      $output .= '<p>' . sprintf(
        __('From %s to %s', 'baum-cosmology'),
        esc_html($start->format('F j')),
        esc_html($end->format('F j'))
      ) . '</p>';
    }
    
    if ($show_countdown) {
      $now = new DateTime();
      $start = new DateTime($season['start_date']);
      $interval = $now->diff($start);
      
      if ($interval->invert == 0) { // If start date is in the future
        $days = $interval->days;
        $output .= '<p>' . sprintf(
          _n(
            'Starting in %d day',
            'Starting in %d days',
            $days,
            'baum-cosmology'
          ),
          $days
        ) . '</p>';
      }
    }
    
    $output .= '</div>';
    
    return $output;
  }
  
  /**
   * Shortcode for displaying all seasons
   * 
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function all_seasons_shortcode($atts) {
    $atts = shortcode_atts(array(
      'hemisphere' => 'northern',
      'show_dates' => 'yes',
      'show_description' => 'yes'
    ), $atts, 'all_seasons');
    
    $hemisphere = sanitize_text_field($atts['hemisphere']);
    $show_dates = sanitize_text_field($atts['show_dates']) === 'yes';
    $show_description = sanitize_text_field($atts['show_description']) === 'yes';
    
    $seasons = $this->get_seasons_data($hemisphere);
    $current_season = $this->get_current_season($hemisphere);
    
    $output = '<div class="baum-cosmology four-seasons all-seasons">';
    $output .= '<h3>' . __('The Four Seasons', 'baum-cosmology') . '</h3>';
    
    foreach ($seasons as $season_key => $season) {
      $is_current = ($season['name'] === $current_season['name']);
      
      $output .= '<div class="season-item' . ($is_current ? ' current-season' : '') . '">';
      $output .= '<h4>' . esc_html($season['name']) . 
                 ($is_current ? ' <span class="current-label">(' . __('Current', 'baum-cosmology') . ')</span>' : '') .
                 '</h4>';
      
      if ($show_description) {
        $output .= '<p>' . esc_html($season['description']) . '</p>';
      }
      
      if ($show_dates) {
        $start = new DateTime($season['start_date']);
        $end = new DateTime($season['end_date']);
        $output .= '<p>' . sprintf(
          __('From %s to %s', 'baum-cosmology'),
          esc_html($start->format('F j')),
          esc_html($end->format('F j'))
        ) . '</p>';
      }
      
      $output .= '</div>';
    }
    
    $output .= '</div>';
    
    return $output;
  }

  /**
   * Get sun times for a specific date and location
   *
   * @param string $date Date in Y-m-d format
   * @param float $latitude Latitude
   * @param float $longitude Longitude
   * @return array Sun times data
   */
  public function get_sun_times($date = null, $latitude = null, $longitude = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    // Default to New York if no coordinates provided
    if ($latitude === null) $latitude = 40.7128;
    if ($longitude === null) $longitude = -74.0060;

    // Calculate sun times using basic astronomical formulas
    $timestamp = strtotime($date);
    $day_of_year = date('z', $timestamp) + 1;

    // Solar declination
    $declination = 23.45 * sin(deg2rad(360 * (284 + $day_of_year) / 365));

    // Hour angle
    $hour_angle = rad2deg(acos(-tan(deg2rad($latitude)) * tan(deg2rad($declination))));

    // Calculate times (simplified calculation)
    $solar_noon = 12 - ($longitude / 15);
    $sunrise_hour = $solar_noon - ($hour_angle / 15);
    $sunset_hour = $solar_noon + ($hour_angle / 15);

    // Convert to time format
    $sunrise = $this->decimal_to_time($sunrise_hour);
    $sunset = $this->decimal_to_time($sunset_hour);
    $solar_noon_time = $this->decimal_to_time($solar_noon);

    // Calculate twilight times (approximate)
    $civil_dawn = $this->decimal_to_time($sunrise_hour - 0.5);
    $civil_dusk = $this->decimal_to_time($sunset_hour + 0.5);
    $nautical_dawn = $this->decimal_to_time($sunrise_hour - 1);
    $nautical_dusk = $this->decimal_to_time($sunset_hour + 1);
    $astronomical_dawn = $this->decimal_to_time($sunrise_hour - 1.5);
    $astronomical_dusk = $this->decimal_to_time($sunset_hour + 1.5);

    // Calculate day length
    $day_length_hours = $sunset_hour - $sunrise_hour;
    $day_length = $this->decimal_to_time($day_length_hours, true);

    return array(
      'date' => $date,
      'sunrise' => $sunrise,
      'sunset' => $sunset,
      'solar_noon' => $solar_noon_time,
      'day_length' => $day_length,
      'civil_dawn' => $civil_dawn,
      'civil_dusk' => $civil_dusk,
      'nautical_dawn' => $nautical_dawn,
      'nautical_dusk' => $nautical_dusk,
      'astronomical_dawn' => $astronomical_dawn,
      'astronomical_dusk' => $astronomical_dusk,
      'latitude' => $latitude,
      'longitude' => $longitude
    );
  }

  /**
   * Convert decimal hours to time format
   *
   * @param float $decimal_hours Decimal hours
   * @param bool $duration Whether this is a duration (no negative handling)
   * @return string Time in H:i format
   */
  private function decimal_to_time($decimal_hours, $duration = false) {
    if (!$duration && $decimal_hours < 0) {
      $decimal_hours += 24;
    }
    if (!$duration && $decimal_hours >= 24) {
      $decimal_hours -= 24;
    }

    $hours = floor($decimal_hours);
    $minutes = round(($decimal_hours - $hours) * 60);

    if ($minutes >= 60) {
      $hours += 1;
      $minutes = 0;
    }

    return sprintf('%02d:%02d', $hours, $minutes);
  }

  /**
   * Shortcode for displaying sun times
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function sun_times_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'latitude' => '40.7128',
      'longitude' => '-74.0060',
      'location_name' => 'New York',
      'show_twilight' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'sun_times');

    $date = sanitize_text_field($atts['date']);
    $latitude = floatval($atts['latitude']);
    $longitude = floatval($atts['longitude']);
    $location_name = sanitize_text_field($atts['location_name']);
    $show_twilight = sanitize_text_field($atts['show_twilight']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $sun_data = $this->get_sun_times($date, $latitude, $longitude);

    $output = '<div class="baum-cosmology sun-times">';
    $output .= '<h3>' . sprintf(__('Sun Times for %s', 'baum-cosmology'), esc_html($location_name)) . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($table_style) {
      $output .= '<table class="sun-times-table">';
      $output .= '<tr><td>' . __('Sunrise', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['sunrise']) . '</td></tr>';
      $output .= '<tr><td>' . __('Solar Noon', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['solar_noon']) . '</td></tr>';
      $output .= '<tr><td>' . __('Sunset', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['sunset']) . '</td></tr>';
      $output .= '<tr><td>' . __('Day Length', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['day_length']) . '</td></tr>';

      if ($show_twilight) {
        $output .= '<tr class="twilight-header"><td colspan="2"><strong>' . __('Twilight Times', 'baum-cosmology') . '</strong></td></tr>';
        $output .= '<tr><td>' . __('Civil Dawn', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['civil_dawn']) . '</td></tr>';
        $output .= '<tr><td>' . __('Civil Dusk', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['civil_dusk']) . '</td></tr>';
        $output .= '<tr><td>' . __('Nautical Dawn', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['nautical_dawn']) . '</td></tr>';
        $output .= '<tr><td>' . __('Nautical Dusk', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['nautical_dusk']) . '</td></tr>';
        $output .= '<tr><td>' . __('Astronomical Dawn', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['astronomical_dawn']) . '</td></tr>';
        $output .= '<tr><td>' . __('Astronomical Dusk', 'baum-cosmology') . '</td><td>' . esc_html($sun_data['astronomical_dusk']) . '</td></tr>';
      }

      $output .= '</table>';
    } else {
      $output .= '<div class="sun-times-list">';
      $output .= '<p><strong>' . __('Sunrise:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['sunrise']) . '</p>';
      $output .= '<p><strong>' . __('Solar Noon:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['solar_noon']) . '</p>';
      $output .= '<p><strong>' . __('Sunset:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['sunset']) . '</p>';
      $output .= '<p><strong>' . __('Day Length:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['day_length']) . '</p>';
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Get moon phase information for a specific date
   *
   * @param string $date Date in Y-m-d format
   * @return array Moon phase data
   */
  public function get_moon_phase($date = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $timestamp = strtotime($date);

    // Known new moon reference: January 6, 2000, 18:14 UTC
    $known_new_moon = mktime(18, 14, 0, 1, 6, 2000);

    // Lunar cycle is approximately 29.53059 days
    $lunar_cycle = 29.53059;

    // Calculate days since known new moon
    $days_since = ($timestamp - $known_new_moon) / (24 * 60 * 60);

    // Calculate current position in lunar cycle
    $cycle_position = fmod($days_since, $lunar_cycle);
    if ($cycle_position < 0) {
      $cycle_position += $lunar_cycle;
    }

    // Calculate illumination percentage
    $illumination = (1 - cos(2 * pi() * $cycle_position / $lunar_cycle)) / 2 * 100;

    // Determine phase name
    $phase_names = array(
      'new' => __('New Moon', 'baum-cosmology'),
      'waxing_crescent' => __('Waxing Crescent', 'baum-cosmology'),
      'first_quarter' => __('First Quarter', 'baum-cosmology'),
      'waxing_gibbous' => __('Waxing Gibbous', 'baum-cosmology'),
      'full' => __('Full Moon', 'baum-cosmology'),
      'waning_gibbous' => __('Waning Gibbous', 'baum-cosmology'),
      'third_quarter' => __('Third Quarter', 'baum-cosmology'),
      'waning_crescent' => __('Waning Crescent', 'baum-cosmology')
    );

    $phase_key = 'new';
    if ($cycle_position < 1.84566) {
      $phase_key = 'new';
    } elseif ($cycle_position < 5.53699) {
      $phase_key = 'waxing_crescent';
    } elseif ($cycle_position < 9.22831) {
      $phase_key = 'first_quarter';
    } elseif ($cycle_position < 12.91963) {
      $phase_key = 'waxing_gibbous';
    } elseif ($cycle_position < 16.61096) {
      $phase_key = 'full';
    } elseif ($cycle_position < 20.30228) {
      $phase_key = 'waning_gibbous';
    } elseif ($cycle_position < 23.99361) {
      $phase_key = 'third_quarter';
    } else {
      $phase_key = 'waning_crescent';
    }

    // Calculate next major phases
    $next_new = $this->get_next_moon_phase_date($timestamp, 0);
    $next_first = $this->get_next_moon_phase_date($timestamp, 7.38265);
    $next_full = $this->get_next_moon_phase_date($timestamp, 14.7653);
    $next_third = $this->get_next_moon_phase_date($timestamp, 22.14795);

    return array(
      'date' => $date,
      'phase_name' => $phase_names[$phase_key],
      'phase_key' => $phase_key,
      'illumination' => round($illumination, 1),
      'cycle_position' => $cycle_position,
      'days_in_cycle' => $lunar_cycle,
      'next_new_moon' => date('Y-m-d', $next_new),
      'next_first_quarter' => date('Y-m-d', $next_first),
      'next_full_moon' => date('Y-m-d', $next_full),
      'next_third_quarter' => date('Y-m-d', $next_third)
    );
  }

  /**
   * Calculate next moon phase date
   *
   * @param int $timestamp Current timestamp
   * @param float $target_phase Target phase in days from new moon
   * @return int Timestamp of next phase
   */
  private function get_next_moon_phase_date($timestamp, $target_phase) {
    $known_new_moon = mktime(18, 14, 0, 1, 6, 2000);
    $lunar_cycle = 29.53059;

    $days_since = ($timestamp - $known_new_moon) / (24 * 60 * 60);
    $current_cycle = floor($days_since / $lunar_cycle);

    // Try current cycle first
    $phase_date = $known_new_moon + ($current_cycle * $lunar_cycle + $target_phase) * 24 * 60 * 60;

    // If phase already passed, use next cycle
    if ($phase_date <= $timestamp) {
      $phase_date = $known_new_moon + (($current_cycle + 1) * $lunar_cycle + $target_phase) * 24 * 60 * 60;
    }

    return $phase_date;
  }

  /**
   * Shortcode for displaying moon information
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function moon_info_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_next_phases' => 'yes',
      'show_illumination' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'moon_info');

    $date = sanitize_text_field($atts['date']);
    $show_next_phases = sanitize_text_field($atts['show_next_phases']) === 'yes';
    $show_illumination = sanitize_text_field($atts['show_illumination']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $moon_data = $this->get_moon_phase($date);

    $output = '<div class="baum-cosmology moon-info">';
    $output .= '<h3>' . __('Moon Information', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($table_style) {
      $output .= '<table class="moon-info-table">';
      $output .= '<tr><td>' . __('Current Phase', 'baum-cosmology') . '</td><td><strong>' . esc_html($moon_data['phase_name']) . '</strong></td></tr>';

      if ($show_illumination) {
        $output .= '<tr><td>' . __('Illumination', 'baum-cosmology') . '</td><td>' . esc_html($moon_data['illumination']) . '%</td></tr>';
      }

      if ($show_next_phases) {
        $output .= '<tr class="next-phases-header"><td colspan="2"><strong>' . __('Next Major Phases', 'baum-cosmology') . '</strong></td></tr>';
        $output .= '<tr><td>' . __('New Moon', 'baum-cosmology') . '</td><td>' . esc_html(date('F j, Y', strtotime($moon_data['next_new_moon']))) . '</td></tr>';
        $output .= '<tr><td>' . __('First Quarter', 'baum-cosmology') . '</td><td>' . esc_html(date('F j, Y', strtotime($moon_data['next_first_quarter']))) . '</td></tr>';
        $output .= '<tr><td>' . __('Full Moon', 'baum-cosmology') . '</td><td>' . esc_html(date('F j, Y', strtotime($moon_data['next_full_moon']))) . '</td></tr>';
        $output .= '<tr><td>' . __('Third Quarter', 'baum-cosmology') . '</td><td>' . esc_html(date('F j, Y', strtotime($moon_data['next_third_quarter']))) . '</td></tr>';
      }

      $output .= '</table>';
    } else {
      $output .= '<div class="moon-info-list">';
      $output .= '<p><strong>' . __('Current Phase:', 'baum-cosmology') . '</strong> ' . esc_html($moon_data['phase_name']) . '</p>';

      if ($show_illumination) {
        $output .= '<p><strong>' . __('Illumination:', 'baum-cosmology') . '</strong> ' . esc_html($moon_data['illumination']) . '%</p>';
      }

      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Get visible planets for tonight
   *
   * @param string $date Date in Y-m-d format
   * @return array Planet visibility data
   */
  public function get_visible_planets($date = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    // Simplified planet visibility calculation
    // In a real implementation, you'd use more complex astronomical calculations
    $planets = array(
      'mercury' => array(
        'name' => __('Mercury', 'baum-cosmology'),
        'visible' => (date('n', strtotime($date)) % 3 == 0),
        'magnitude' => -0.4,
        'constellation' => __('Virgo', 'baum-cosmology'),
        'best_time' => __('Evening', 'baum-cosmology')
      ),
      'venus' => array(
        'name' => __('Venus', 'baum-cosmology'),
        'visible' => (date('n', strtotime($date)) % 2 == 0),
        'magnitude' => -4.6,
        'constellation' => __('Leo', 'baum-cosmology'),
        'best_time' => __('Dawn', 'baum-cosmology')
      ),
      'mars' => array(
        'name' => __('Mars', 'baum-cosmology'),
        'visible' => (date('n', strtotime($date)) % 4 != 0),
        'magnitude' => 1.8,
        'constellation' => __('Gemini', 'baum-cosmology'),
        'best_time' => __('Midnight', 'baum-cosmology')
      ),
      'jupiter' => array(
        'name' => __('Jupiter', 'baum-cosmology'),
        'visible' => true,
        'magnitude' => -2.9,
        'constellation' => __('Taurus', 'baum-cosmology'),
        'best_time' => __('Evening', 'baum-cosmology')
      ),
      'saturn' => array(
        'name' => __('Saturn', 'baum-cosmology'),
        'visible' => (date('n', strtotime($date)) > 6),
        'magnitude' => 0.7,
        'constellation' => __('Aquarius', 'baum-cosmology'),
        'best_time' => __('Late Evening', 'baum-cosmology')
      )
    );

    return array_filter($planets, function($planet) {
      return $planet['visible'];
    });
  }

  /**
   * Shortcode for displaying visible planets
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function planet_visibility_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_magnitude' => 'yes',
      'show_constellation' => 'yes',
      'show_best_time' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'planet_visibility');

    $date = sanitize_text_field($atts['date']);
    $show_magnitude = sanitize_text_field($atts['show_magnitude']) === 'yes';
    $show_constellation = sanitize_text_field($atts['show_constellation']) === 'yes';
    $show_best_time = sanitize_text_field($atts['show_best_time']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $planets = $this->get_visible_planets($date);

    $output = '<div class="baum-cosmology planet-visibility">';
    $output .= '<h3>' . __('Visible Planets Tonight', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if (empty($planets)) {
      $output .= '<p>' . __('No major planets are easily visible tonight.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="planet-visibility-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Planet', 'baum-cosmology') . '</th>';
        if ($show_magnitude) $output .= '<th>' . __('Magnitude', 'baum-cosmology') . '</th>';
        if ($show_constellation) $output .= '<th>' . __('Constellation', 'baum-cosmology') . '</th>';
        if ($show_best_time) $output .= '<th>' . __('Best Time', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($planets as $planet) {
          $output .= '<tr>';
          $output .= '<td><strong>' . esc_html($planet['name']) . '</strong></td>';
          if ($show_magnitude) $output .= '<td>' . esc_html($planet['magnitude']) . '</td>';
          if ($show_constellation) $output .= '<td>' . esc_html($planet['constellation']) . '</td>';
          if ($show_best_time) $output .= '<td>' . esc_html($planet['best_time']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="planet-visibility-list">';
        foreach ($planets as $planet) {
          $output .= '<div class="planet-item">';
          $output .= '<h4>' . esc_html($planet['name']) . '</h4>';
          if ($show_constellation) {
            $output .= '<p><strong>' . __('Constellation:', 'baum-cosmology') . '</strong> ' . esc_html($planet['constellation']) . '</p>';
          }
          if ($show_best_time) {
            $output .= '<p><strong>' . __('Best Time:', 'baum-cosmology') . '</strong> ' . esc_html($planet['best_time']) . '</p>';
          }
          if ($show_magnitude) {
            $output .= '<p><strong>' . __('Magnitude:', 'baum-cosmology') . '</strong> ' . esc_html($planet['magnitude']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Get upcoming meteor showers
   *
   * @param string $date Starting date
   * @param int $days_ahead Number of days to look ahead
   * @return array Meteor shower data
   */
  public function get_meteor_showers($date = null, $days_ahead = 90) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $current_year = date('Y', strtotime($date));

    // Major annual meteor showers
    $showers = array(
      array(
        'name' => __('Quadrantids', 'baum-cosmology'),
        'peak_date' => $current_year . '-01-04',
        'active_period' => __('Dec 28 - Jan 12', 'baum-cosmology'),
        'zhr' => 120,
        'radiant' => __('Boötes', 'baum-cosmology'),
        'best_time' => __('Pre-dawn', 'baum-cosmology')
      ),
      array(
        'name' => __('Lyrids', 'baum-cosmology'),
        'peak_date' => $current_year . '-04-22',
        'active_period' => __('Apr 16 - 25', 'baum-cosmology'),
        'zhr' => 18,
        'radiant' => __('Lyra', 'baum-cosmology'),
        'best_time' => __('Late night', 'baum-cosmology')
      ),
      array(
        'name' => __('Eta Aquariids', 'baum-cosmology'),
        'peak_date' => $current_year . '-05-06',
        'active_period' => __('Apr 19 - May 28', 'baum-cosmology'),
        'zhr' => 50,
        'radiant' => __('Aquarius', 'baum-cosmology'),
        'best_time' => __('Pre-dawn', 'baum-cosmology')
      ),
      array(
        'name' => __('Perseids', 'baum-cosmology'),
        'peak_date' => $current_year . '-08-13',
        'active_period' => __('Jul 17 - Aug 24', 'baum-cosmology'),
        'zhr' => 100,
        'radiant' => __('Perseus', 'baum-cosmology'),
        'best_time' => __('Late night', 'baum-cosmology')
      ),
      array(
        'name' => __('Draconids', 'baum-cosmology'),
        'peak_date' => $current_year . '-10-08',
        'active_period' => __('Oct 6 - 10', 'baum-cosmology'),
        'zhr' => 10,
        'radiant' => __('Draco', 'baum-cosmology'),
        'best_time' => __('Evening', 'baum-cosmology')
      ),
      array(
        'name' => __('Orionids', 'baum-cosmology'),
        'peak_date' => $current_year . '-10-21',
        'active_period' => __('Oct 2 - Nov 7', 'baum-cosmology'),
        'zhr' => 25,
        'radiant' => __('Orion', 'baum-cosmology'),
        'best_time' => __('Pre-dawn', 'baum-cosmology')
      ),
      array(
        'name' => __('Leonids', 'baum-cosmology'),
        'peak_date' => $current_year . '-11-17',
        'active_period' => __('Nov 6 - 30', 'baum-cosmology'),
        'zhr' => 15,
        'radiant' => __('Leo', 'baum-cosmology'),
        'best_time' => __('Pre-dawn', 'baum-cosmology')
      ),
      array(
        'name' => __('Geminids', 'baum-cosmology'),
        'peak_date' => $current_year . '-12-14',
        'active_period' => __('Dec 4 - 20', 'baum-cosmology'),
        'zhr' => 120,
        'radiant' => __('Gemini', 'baum-cosmology'),
        'best_time' => __('All night', 'baum-cosmology')
      ),
      array(
        'name' => __('Ursids', 'baum-cosmology'),
        'peak_date' => $current_year . '-12-22',
        'active_period' => __('Dec 17 - 26', 'baum-cosmology'),
        'zhr' => 10,
        'radiant' => __('Ursa Minor', 'baum-cosmology'),
        'best_time' => __('Late night', 'baum-cosmology')
      )
    );

    // Filter showers within the specified time range
    $start_timestamp = strtotime($date);
    $end_timestamp = strtotime($date . ' +' . $days_ahead . ' days');

    $upcoming_showers = array();
    foreach ($showers as $shower) {
      $peak_timestamp = strtotime($shower['peak_date']);
      if ($peak_timestamp >= $start_timestamp && $peak_timestamp <= $end_timestamp) {
        $shower['days_until_peak'] = ceil(($peak_timestamp - $start_timestamp) / (24 * 60 * 60));
        $upcoming_showers[] = $shower;
      }
    }

    // Sort by peak date
    usort($upcoming_showers, function($a, $b) {
      return strtotime($a['peak_date']) - strtotime($b['peak_date']);
    });

    return $upcoming_showers;
  }

  /**
   * Shortcode for displaying meteor showers
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function meteor_showers_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'days_ahead' => '90',
      'show_zhr' => 'yes',
      'show_radiant' => 'yes',
      'show_best_time' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'meteor_showers');

    $date = sanitize_text_field($atts['date']);
    $days_ahead = intval($atts['days_ahead']);
    $show_zhr = sanitize_text_field($atts['show_zhr']) === 'yes';
    $show_radiant = sanitize_text_field($atts['show_radiant']) === 'yes';
    $show_best_time = sanitize_text_field($atts['show_best_time']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $showers = $this->get_meteor_showers($date, $days_ahead);

    $output = '<div class="baum-cosmology meteor-showers">';
    $output .= '<h3>' . __('Upcoming Meteor Showers', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date-range">' . sprintf(
      __('Next %d days from %s', 'baum-cosmology'),
      $days_ahead,
      esc_html(date('F j, Y', strtotime($date)))
    ) . '</p>';

    if (empty($showers)) {
      $output .= '<p>' . __('No major meteor showers in the specified time period.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="meteor-showers-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Shower', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Peak Date', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Active Period', 'baum-cosmology') . '</th>';
        if ($show_zhr) $output .= '<th>' . __('ZHR', 'baum-cosmology') . '</th>';
        if ($show_radiant) $output .= '<th>' . __('Radiant', 'baum-cosmology') . '</th>';
        if ($show_best_time) $output .= '<th>' . __('Best Time', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($showers as $shower) {
          $output .= '<tr>';
          $output .= '<td><strong>' . esc_html($shower['name']) . '</strong></td>';
          $output .= '<td>' . esc_html(date('M j', strtotime($shower['peak_date']))) . '</td>';
          $output .= '<td>' . esc_html($shower['active_period']) . '</td>';
          if ($show_zhr) $output .= '<td>' . esc_html($shower['zhr']) . '/hr</td>';
          if ($show_radiant) $output .= '<td>' . esc_html($shower['radiant']) . '</td>';
          if ($show_best_time) $output .= '<td>' . esc_html($shower['best_time']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="meteor-showers-list">';
        foreach ($showers as $shower) {
          $output .= '<div class="shower-item">';
          $output .= '<h4>' . esc_html($shower['name']) . '</h4>';
          $output .= '<p><strong>' . __('Peak:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($shower['peak_date'])));
          if ($shower['days_until_peak'] == 0) {
            $output .= ' <span class="peak-today">(' . __('Today!', 'baum-cosmology') . ')</span>';
          } elseif ($shower['days_until_peak'] == 1) {
            $output .= ' <span class="peak-soon">(' . __('Tomorrow', 'baum-cosmology') . ')</span>';
          } else {
            $output .= ' <span class="peak-countdown">(' . sprintf(__('in %d days', 'baum-cosmology'), $shower['days_until_peak']) . ')</span>';
          }
          $output .= '</p>';
          $output .= '<p><strong>' . __('Active:', 'baum-cosmology') . '</strong> ' . esc_html($shower['active_period']) . '</p>';
          if ($show_zhr) {
            $output .= '<p><strong>' . __('Rate:', 'baum-cosmology') . '</strong> ' . esc_html($shower['zhr']) . ' ' . __('meteors/hour', 'baum-cosmology') . '</p>';
          }
          if ($show_radiant) {
            $output .= '<p><strong>' . __('Radiant:', 'baum-cosmology') . '</strong> ' . esc_html($shower['radiant']) . '</p>';
          }
          if ($show_best_time) {
            $output .= '<p><strong>' . __('Best Time:', 'baum-cosmology') . '</strong> ' . esc_html($shower['best_time']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Get upcoming eclipses
   *
   * @param string $date Starting date
   * @param int $years_ahead Number of years to look ahead
   * @return array Eclipse data
   */
  public function get_eclipses($date = null, $years_ahead = 2) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $current_year = date('Y', strtotime($date));

    // Major eclipses for the next few years (simplified data)
    $eclipses = array();

    // 2025 eclipses
    if ($current_year <= 2025) {
      $eclipses[] = array(
        'date' => '2025-03-14',
        'type' => 'total_lunar',
        'name' => __('Total Lunar Eclipse', 'baum-cosmology'),
        'visibility' => __('Americas, Europe, Africa, Asia', 'baum-cosmology'),
        'duration' => '1h 5m',
        'magnitude' => 1.178
      );
      $eclipses[] = array(
        'date' => '2025-03-29',
        'type' => 'partial_solar',
        'name' => __('Partial Solar Eclipse', 'baum-cosmology'),
        'visibility' => __('Atlantic, Arctic', 'baum-cosmology'),
        'duration' => '2h 1m',
        'magnitude' => 0.938
      );
      $eclipses[] = array(
        'date' => '2025-09-07',
        'type' => 'total_lunar',
        'name' => __('Total Lunar Eclipse', 'baum-cosmology'),
        'visibility' => __('Europe, Africa, Asia, Australia', 'baum-cosmology'),
        'duration' => '1h 22m',
        'magnitude' => 1.362
      );
      $eclipses[] = array(
        'date' => '2025-09-21',
        'type' => 'partial_solar',
        'name' => __('Partial Solar Eclipse', 'baum-cosmology'),
        'visibility' => __('Pacific, New Zealand', 'baum-cosmology'),
        'duration' => '1h 54m',
        'magnitude' => 0.855
      );
    }

    // 2026 eclipses
    if ($current_year <= 2026) {
      $eclipses[] = array(
        'date' => '2026-02-17',
        'type' => 'annular_solar',
        'name' => __('Annular Solar Eclipse', 'baum-cosmology'),
        'visibility' => __('Antarctica', 'baum-cosmology'),
        'duration' => '2m 20s',
        'magnitude' => 0.963
      );
      $eclipses[] = array(
        'date' => '2026-08-12',
        'type' => 'total_solar',
        'name' => __('Total Solar Eclipse', 'baum-cosmology'),
        'visibility' => __('Arctic, Greenland, Iceland, Spain', 'baum-cosmology'),
        'duration' => '2m 18s',
        'magnitude' => 1.039
      );
    }

    // Filter eclipses within the specified time range
    $start_timestamp = strtotime($date);
    $end_timestamp = strtotime($date . ' +' . $years_ahead . ' years');

    $upcoming_eclipses = array();
    foreach ($eclipses as $eclipse) {
      $eclipse_timestamp = strtotime($eclipse['date']);
      if ($eclipse_timestamp >= $start_timestamp && $eclipse_timestamp <= $end_timestamp) {
        $eclipse['days_until'] = ceil(($eclipse_timestamp - $start_timestamp) / (24 * 60 * 60));
        $upcoming_eclipses[] = $eclipse;
      }
    }

    // Sort by date
    usort($upcoming_eclipses, function($a, $b) {
      return strtotime($a['date']) - strtotime($b['date']);
    });

    return $upcoming_eclipses;
  }

  /**
   * Shortcode for displaying eclipse information
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function eclipse_info_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'years_ahead' => '2',
      'show_magnitude' => 'yes',
      'show_duration' => 'yes',
      'show_visibility' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'eclipse_info');

    $date = sanitize_text_field($atts['date']);
    $years_ahead = intval($atts['years_ahead']);
    $show_magnitude = sanitize_text_field($atts['show_magnitude']) === 'yes';
    $show_duration = sanitize_text_field($atts['show_duration']) === 'yes';
    $show_visibility = sanitize_text_field($atts['show_visibility']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $eclipses = $this->get_eclipses($date, $years_ahead);

    $output = '<div class="baum-cosmology eclipse-info">';
    $output .= '<h3>' . __('Upcoming Eclipses', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date-range">' . sprintf(
      __('Next %d years from %s', 'baum-cosmology'),
      $years_ahead,
      esc_html(date('F j, Y', strtotime($date)))
    ) . '</p>';

    if (empty($eclipses)) {
      $output .= '<p>' . __('No major eclipses in the specified time period.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="eclipse-info-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Date', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Type', 'baum-cosmology') . '</th>';
        if ($show_duration) $output .= '<th>' . __('Duration', 'baum-cosmology') . '</th>';
        if ($show_magnitude) $output .= '<th>' . __('Magnitude', 'baum-cosmology') . '</th>';
        if ($show_visibility) $output .= '<th>' . __('Visibility', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($eclipses as $eclipse) {
          $output .= '<tr>';
          $output .= '<td>' . esc_html(date('M j, Y', strtotime($eclipse['date']))) . '</td>';
          $output .= '<td><strong>' . esc_html($eclipse['name']) . '</strong></td>';
          if ($show_duration) $output .= '<td>' . esc_html($eclipse['duration']) . '</td>';
          if ($show_magnitude) $output .= '<td>' . esc_html($eclipse['magnitude']) . '</td>';
          if ($show_visibility) $output .= '<td>' . esc_html($eclipse['visibility']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="eclipse-info-list">';
        foreach ($eclipses as $eclipse) {
          $output .= '<div class="eclipse-item">';
          $output .= '<h4>' . esc_html($eclipse['name']) . '</h4>';
          $output .= '<p><strong>' . __('Date:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($eclipse['date'])));
          if ($eclipse['days_until'] == 0) {
            $output .= ' <span class="eclipse-today">(' . __('Today!', 'baum-cosmology') . ')</span>';
          } elseif ($eclipse['days_until'] <= 30) {
            $output .= ' <span class="eclipse-soon">(' . sprintf(__('in %d days', 'baum-cosmology'), $eclipse['days_until']) . ')</span>';
          }
          $output .= '</p>';
          if ($show_duration) {
            $output .= '<p><strong>' . __('Duration:', 'baum-cosmology') . '</strong> ' . esc_html($eclipse['duration']) . '</p>';
          }
          if ($show_magnitude) {
            $output .= '<p><strong>' . __('Magnitude:', 'baum-cosmology') . '</strong> ' . esc_html($eclipse['magnitude']) . '</p>';
          }
          if ($show_visibility) {
            $output .= '<p><strong>' . __('Visible from:', 'baum-cosmology') . '</strong> ' . esc_html($eclipse['visibility']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying comprehensive astronomy calendar
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function astro_calendar_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'latitude' => '40.7128',
      'longitude' => '-74.0060',
      'location_name' => 'New York',
      'show_sun' => 'yes',
      'show_moon' => 'yes',
      'show_planets' => 'yes',
      'show_meteors' => 'yes',
      'show_eclipses' => 'yes',
      'compact' => 'no'
    ), $atts, 'astro_calendar');

    $date = sanitize_text_field($atts['date']);
    $latitude = floatval($atts['latitude']);
    $longitude = floatval($atts['longitude']);
    $location_name = sanitize_text_field($atts['location_name']);
    $show_sun = sanitize_text_field($atts['show_sun']) === 'yes';
    $show_moon = sanitize_text_field($atts['show_moon']) === 'yes';
    $show_planets = sanitize_text_field($atts['show_planets']) === 'yes';
    $show_meteors = sanitize_text_field($atts['show_meteors']) === 'yes';
    $show_eclipses = sanitize_text_field($atts['show_eclipses']) === 'yes';
    $compact = sanitize_text_field($atts['compact']) === 'yes';

    $output = '<div class="baum-cosmology astro-calendar">';
    $output .= '<h2>' . sprintf(__('Astronomy Calendar for %s', 'baum-cosmology'), esc_html($location_name)) . '</h2>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($show_sun) {
      $sun_data = $this->get_sun_times($date, $latitude, $longitude);
      $output .= '<div class="astro-section sun-section">';
      $output .= '<h3>☀️ ' . __('Sun Information', 'baum-cosmology') . '</h3>';
      if ($compact) {
        $output .= '<p><strong>' . __('Sunrise:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['sunrise']) . ' | ';
        $output .= '<strong>' . __('Sunset:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['sunset']) . ' | ';
        $output .= '<strong>' . __('Day Length:', 'baum-cosmology') . '</strong> ' . esc_html($sun_data['day_length']) . '</p>';
      } else {
        $output .= $this->sun_times_shortcode(array(
          'date' => $date,
          'latitude' => $latitude,
          'longitude' => $longitude,
          'location_name' => $location_name,
          'show_twilight' => 'no',
          'table_style' => 'yes'
        ));
      }
      $output .= '</div>';
    }

    if ($show_moon) {
      $moon_data = $this->get_moon_phase($date);
      $output .= '<div class="astro-section moon-section">';
      $output .= '<h3>🌙 ' . __('Moon Information', 'baum-cosmology') . '</h3>';
      if ($compact) {
        $output .= '<p><strong>' . __('Phase:', 'baum-cosmology') . '</strong> ' . esc_html($moon_data['phase_name']) . ' (' . esc_html($moon_data['illumination']) . '% ' . __('illuminated', 'baum-cosmology') . ')</p>';
      } else {
        $output .= $this->moon_info_shortcode(array(
          'date' => $date,
          'show_next_phases' => 'no',
          'show_illumination' => 'yes',
          'table_style' => 'yes'
        ));
      }
      $output .= '</div>';
    }

    if ($show_planets) {
      $planets = $this->get_visible_planets($date);
      $output .= '<div class="astro-section planets-section">';
      $output .= '<h3>🪐 ' . __('Visible Planets Tonight', 'baum-cosmology') . '</h3>';
      if (empty($planets)) {
        $output .= '<p>' . __('No major planets easily visible tonight.', 'baum-cosmology') . '</p>';
      } else {
        if ($compact) {
          $planet_names = array_map(function($planet) { return $planet['name']; }, $planets);
          $output .= '<p>' . implode(', ', $planet_names) . '</p>';
        } else {
          $output .= $this->planet_visibility_shortcode(array(
            'date' => $date,
            'show_magnitude' => 'no',
            'show_constellation' => 'yes',
            'show_best_time' => 'yes',
            'table_style' => 'yes'
          ));
        }
      }
      $output .= '</div>';
    }

    if ($show_meteors) {
      $meteors = $this->get_meteor_showers($date, 30);
      $output .= '<div class="astro-section meteors-section">';
      $output .= '<h3>☄️ ' . __('Meteor Showers (Next 30 Days)', 'baum-cosmology') . '</h3>';
      if (empty($meteors)) {
        $output .= '<p>' . __('No major meteor showers in the next 30 days.', 'baum-cosmology') . '</p>';
      } else {
        if ($compact) {
          $next_shower = $meteors[0];
          $output .= '<p><strong>' . esc_html($next_shower['name']) . '</strong> - ' . __('Peak:', 'baum-cosmology') . ' ' . esc_html(date('M j', strtotime($next_shower['peak_date']))) . '</p>';
        } else {
          $output .= $this->meteor_showers_shortcode(array(
            'date' => $date,
            'days_ahead' => '30',
            'show_zhr' => 'yes',
            'show_radiant' => 'no',
            'show_best_time' => 'yes',
            'table_style' => 'yes'
          ));
        }
      }
      $output .= '</div>';
    }

    if ($show_eclipses) {
      $eclipses = $this->get_eclipses($date, 1);
      $output .= '<div class="astro-section eclipses-section">';
      $output .= '<h3>🌑 ' . __('Eclipses (Next Year)', 'baum-cosmology') . '</h3>';
      if (empty($eclipses)) {
        $output .= '<p>' . __('No major eclipses in the next year.', 'baum-cosmology') . '</p>';
      } else {
        if ($compact) {
          $next_eclipse = $eclipses[0];
          $output .= '<p><strong>' . esc_html($next_eclipse['name']) . '</strong> - ' . esc_html(date('M j, Y', strtotime($next_eclipse['date']))) . '</p>';
        } else {
          $output .= $this->eclipse_info_shortcode(array(
            'date' => $date,
            'years_ahead' => '1',
            'show_magnitude' => 'no',
            'show_duration' => 'yes',
            'show_visibility' => 'yes',
            'table_style' => 'yes'
          ));
        }
      }
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  // ========================================
  // ASTROLOGY METHODS (Swiss Ephemeris)
  // ========================================

  /**
   * Check if Swiss Ephemeris is available
   *
   * @return bool Whether Swiss Ephemeris functions are available
   */
  private function is_swiss_ephemeris_available() {
    return function_exists('swe_julday') && function_exists('swe_calc_ut');
  }

  /**
   * Get Julian Day for a given date
   *
   * @param string $date Date in Y-m-d format
   * @param int $hour Hour (0-23)
   * @param int $minute Minute (0-59)
   * @return float Julian Day
   */
  private function get_julian_day($date = null, $hour = 12, $minute = 0) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $datetime = new DateTime($date);
    $year = (int)$datetime->format('Y');
    $month = (int)$datetime->format('n');
    $day = (int)$datetime->format('j');

    if ($this->is_swiss_ephemeris_available()) {
      return swe_julday($year, $month, $day, $hour + ($minute / 60.0), SE_GREG_CAL);
    }

    // Fallback calculation if Swiss Ephemeris not available
    return $this->calculate_julian_day_fallback($year, $month, $day, $hour, $minute);
  }

  /**
   * Fallback Julian Day calculation
   *
   * @param int $year Year
   * @param int $month Month
   * @param int $day Day
   * @param int $hour Hour
   * @param int $minute Minute
   * @return float Julian Day
   */
  private function calculate_julian_day_fallback($year, $month, $day, $hour, $minute) {
    if ($month <= 2) {
      $year -= 1;
      $month += 12;
    }

    $a = floor($year / 100);
    $b = 2 - $a + floor($a / 4);

    $jd = floor(365.25 * ($year + 4716)) + floor(30.6001 * ($month + 1)) + $day + $b - 1524.5;
    $jd += ($hour + ($minute / 60.0)) / 24.0;

    return $jd;
  }

  /**
   * Get current planetary positions
   *
   * @param string $date Date in Y-m-d format
   * @return array Planetary positions
   */
  public function get_planetary_positions($date = null) {
    // Check cache first
    $cache_key = 'baum_planetary_positions_' . ($date ?: date('Y-m-d'));
    $cached_data = get_transient($cache_key);
    if ($cached_data) {
      return $cached_data;
    }

    $jd = $this->get_julian_day($date);

    $planets = array(
      'sun' => array('id' => 0, 'name' => __('Sun', 'baum-cosmology')),
      'moon' => array('id' => 1, 'name' => __('Moon', 'baum-cosmology')),
      'mercury' => array('id' => 2, 'name' => __('Mercury', 'baum-cosmology')),
      'venus' => array('id' => 3, 'name' => __('Venus', 'baum-cosmology')),
      'mars' => array('id' => 4, 'name' => __('Mars', 'baum-cosmology')),
      'jupiter' => array('id' => 5, 'name' => __('Jupiter', 'baum-cosmology')),
      'saturn' => array('id' => 6, 'name' => __('Saturn', 'baum-cosmology')),
      'uranus' => array('id' => 7, 'name' => __('Uranus', 'baum-cosmology')),
      'neptune' => array('id' => 8, 'name' => __('Neptune', 'baum-cosmology')),
      'pluto' => array('id' => 9, 'name' => __('Pluto', 'baum-cosmology'))
    );

    $signs = array(
      __('Aries', 'baum-cosmology'), __('Taurus', 'baum-cosmology'), __('Gemini', 'baum-cosmology'),
      __('Cancer', 'baum-cosmology'), __('Leo', 'baum-cosmology'), __('Virgo', 'baum-cosmology'),
      __('Libra', 'baum-cosmology'), __('Scorpio', 'baum-cosmology'), __('Sagittarius', 'baum-cosmology'),
      __('Capricorn', 'baum-cosmology'), __('Aquarius', 'baum-cosmology'), __('Pisces', 'baum-cosmology')
    );

    $results = array();

    foreach ($planets as $key => $planet) {
      if ($this->is_swiss_ephemeris_available()) {
        $planet_data = swe_calc_ut($jd, $planet['id'], SEFLG_SPEED);
        if (!isset($planet_data['xx'][0])) continue;

        $longitude = $planet_data['xx'][0];
        $speed = $planet_data['xx'][3];
      } else {
        // Fallback calculation (simplified)
        $longitude = $this->calculate_planet_position_fallback($key, $jd);
        $speed = 1; // Assume direct motion for fallback
      }

      $sign_index = floor($longitude / 30);
      $sign = $signs[$sign_index];
      $degree_in_sign = $longitude - ($sign_index * 30);

      $motion = ($speed < 0) ? __('Retrograde', 'baum-cosmology') : __('Direct', 'baum-cosmology');

      $results[] = array(
        'body' => $planet['name'],
        'longitude' => number_format($longitude, 2) . '°',
        'sign' => $sign,
        'degree_in_sign' => number_format($degree_in_sign, 2) . '°',
        'motion' => $motion,
        'speed' => number_format($speed, 4)
      );
    }

    // Cache results for 1 hour
    set_transient($cache_key, $results, HOUR_IN_SECONDS);
    return $results;
  }

  /**
   * Fallback planet position calculation (very simplified)
   *
   * @param string $planet Planet key
   * @param float $jd Julian Day
   * @return float Longitude in degrees
   */
  private function calculate_planet_position_fallback($planet, $jd) {
    // Very simplified calculation - in reality you'd need complex orbital mechanics
    $base_positions = array(
      'sun' => 280.0,
      'moon' => 120.0,
      'mercury' => 45.0,
      'venus' => 180.0,
      'mars' => 90.0,
      'jupiter' => 270.0,
      'saturn' => 315.0,
      'uranus' => 30.0,
      'neptune' => 150.0,
      'pluto' => 240.0
    );

    $daily_motion = array(
      'sun' => 0.9856,
      'moon' => 13.1764,
      'mercury' => 4.0923,
      'venus' => 1.6021,
      'mars' => 0.5240,
      'jupiter' => 0.0831,
      'saturn' => 0.0335,
      'uranus' => 0.0116,
      'neptune' => 0.0060,
      'pluto' => 0.0040
    );

    $days_since_epoch = $jd - 2451545.0; // J2000.0 epoch
    $longitude = $base_positions[$planet] + ($daily_motion[$planet] * $days_since_epoch);

    return fmod($longitude, 360);
  }

  /**
   * Calculate daily transits and aspects
   *
   * @param string $date Date in Y-m-d format
   * @return array Daily transits
   */
  public function get_daily_transits($date = null) {
    $positions = $this->get_planetary_positions($date);

    $aspects = array(
      0 => __('Conjunction (0°)', 'baum-cosmology'),
      60 => __('Sextile (60°)', 'baum-cosmology'),
      90 => __('Square (90°)', 'baum-cosmology'),
      120 => __('Trine (120°)', 'baum-cosmology'),
      180 => __('Opposition (180°)', 'baum-cosmology')
    );

    // Extract longitude values for calculations
    $planet_longitudes = array();
    foreach ($positions as $position) {
      $longitude = floatval(str_replace('°', '', $position['longitude']));
      $planet_longitudes[$position['body']] = $longitude;
    }

    $transits = array();
    foreach ($planet_longitudes as $p1 => $long1) {
      foreach ($planet_longitudes as $p2 => $long2) {
        if ($p1 === $p2) continue; // Skip same planet

        $angle = abs($long1 - $long2);
        if ($angle > 180) $angle = 360 - $angle; // Normalize aspect angles

        foreach ($aspects as $exact_angle => $aspect_name) {
          $orb = abs($angle - $exact_angle);

          if ($orb <= 2) {
            $strength = __('Strong Influence', 'baum-cosmology');
          } elseif ($orb <= 5) {
            $strength = __('Moderate Influence', 'baum-cosmology');
          } elseif ($orb <= 10) {
            $strength = __('Weak Influence', 'baum-cosmology');
          } else {
            continue; // Skip if orb is too wide
          }

          $transits[] = array(
            'planet1' => $p1,
            'aspect' => $aspect_name,
            'planet2' => $p2,
            'orb' => number_format($orb, 2) . '°',
            'strength' => $strength,
            'exact_angle' => $exact_angle
          );
        }
      }
    }

    // Sort by orb (closest aspects first)
    usort($transits, function($a, $b) {
      return floatval($a['orb']) - floatval($b['orb']);
    });

    return $transits;
  }

  /**
   * Get upcoming lunar phases
   *
   * @param string $date Starting date
   * @param int $days_ahead Number of days to look ahead
   * @return array Lunar phases
   */
  public function get_upcoming_lunar_phases($date = null, $days_ahead = 30) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $phases = array();
    $current_date = new DateTime($date);
    $end_date = clone $current_date;
    $end_date->add(new DateInterval('P' . $days_ahead . 'D'));

    // Calculate lunar phases for the period
    while ($current_date <= $end_date) {
      $moon_data = $this->get_moon_phase($current_date->format('Y-m-d'));

      // Check if this is a major phase (within 1 day of exact)
      $illumination = $moon_data['illumination'];
      $phase_name = '';

      if ($illumination <= 2) {
        $phase_name = __('New Moon', 'baum-cosmology');
      } elseif ($illumination >= 98) {
        $phase_name = __('Full Moon', 'baum-cosmology');
      } elseif (abs($illumination - 50) <= 2) {
        // Determine if first or third quarter based on cycle position
        if ($moon_data['cycle_position'] < 14.76) {
          $phase_name = __('First Quarter', 'baum-cosmology');
        } else {
          $phase_name = __('Third Quarter', 'baum-cosmology');
        }
      }

      if ($phase_name) {
        $phases[] = array(
          'date' => $current_date->format('Y-m-d'),
          'phase' => $phase_name,
          'illumination' => $illumination . '%',
          'days_from_now' => $current_date->diff(new DateTime($date))->days
        );
      }

      $current_date->add(new DateInterval('P1D'));
    }

    return $phases;
  }

  /**
   * Get zodiac ingresses (sign changes) for planets
   *
   * @param string $date Starting date
   * @param int $days_ahead Number of days to look ahead
   * @return array Zodiac ingresses
   */
  public function get_zodiac_ingresses($date = null, $days_ahead = 90) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $ingresses = array();
    $current_date = new DateTime($date);
    $end_date = clone $current_date;
    $end_date->add(new DateInterval('P' . $days_ahead . 'D'));

    $signs = array(
      __('Aries', 'baum-cosmology'), __('Taurus', 'baum-cosmology'), __('Gemini', 'baum-cosmology'),
      __('Cancer', 'baum-cosmology'), __('Leo', 'baum-cosmology'), __('Virgo', 'baum-cosmology'),
      __('Libra', 'baum-cosmology'), __('Scorpio', 'baum-cosmology'), __('Sagittarius', 'baum-cosmology'),
      __('Capricorn', 'baum-cosmology'), __('Aquarius', 'baum-cosmology'), __('Pisces', 'baum-cosmology')
    );

    // Get initial positions
    $initial_positions = $this->get_planetary_positions($date);
    $previous_signs = array();
    foreach ($initial_positions as $position) {
      $previous_signs[$position['body']] = $position['sign'];
    }

    // Check each day for sign changes
    while ($current_date <= $end_date) {
      $daily_positions = $this->get_planetary_positions($current_date->format('Y-m-d'));

      foreach ($daily_positions as $position) {
        $planet = $position['body'];
        $current_sign = $position['sign'];

        if (isset($previous_signs[$planet]) && $previous_signs[$planet] !== $current_sign) {
          $ingresses[] = array(
            'date' => $current_date->format('Y-m-d'),
            'planet' => $planet,
            'from_sign' => $previous_signs[$planet],
            'to_sign' => $current_sign,
            'days_from_now' => $current_date->diff(new DateTime($date))->days
          );
        }

        $previous_signs[$planet] = $current_sign;
      }

      $current_date->add(new DateInterval('P1D'));
    }

    return $ingresses;
  }

  /**
   * Calculate Moon void-of-course periods
   *
   * @param string $date Date in Y-m-d format
   * @return array Void-of-course information
   */
  public function get_moon_void_of_course($date = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $positions = $this->get_planetary_positions($date);
    $moon_position = null;

    // Find Moon position
    foreach ($positions as $position) {
      if ($position['body'] === __('Moon', 'baum-cosmology')) {
        $moon_longitude = floatval(str_replace('°', '', $position['longitude']));
        $moon_sign_index = floor($moon_longitude / 30);
        $degree_in_sign = $moon_longitude - ($moon_sign_index * 30);

        $moon_position = array(
          'longitude' => $moon_longitude,
          'sign_index' => $moon_sign_index,
          'degree_in_sign' => $degree_in_sign,
          'sign' => $position['sign']
        );
        break;
      }
    }

    if (!$moon_position) {
      return null;
    }

    // Calculate when Moon will leave current sign
    $degrees_to_next_sign = 30 - $moon_position['degree_in_sign'];
    $moon_daily_motion = 13.1764; // Average degrees per day
    $days_to_next_sign = $degrees_to_next_sign / $moon_daily_motion;

    $next_sign_date = new DateTime($date);
    $next_sign_date->add(new DateInterval('P' . ceil($days_to_next_sign) . 'D'));

    $signs = array(
      __('Aries', 'baum-cosmology'), __('Taurus', 'baum-cosmology'), __('Gemini', 'baum-cosmology'),
      __('Cancer', 'baum-cosmology'), __('Leo', 'baum-cosmology'), __('Virgo', 'baum-cosmology'),
      __('Libra', 'baum-cosmology'), __('Scorpio', 'baum-cosmology'), __('Sagittarius', 'baum-cosmology'),
      __('Capricorn', 'baum-cosmology'), __('Aquarius', 'baum-cosmology'), __('Pisces', 'baum-cosmology')
    );

    $next_sign_index = ($moon_position['sign_index'] + 1) % 12;
    $next_sign = $signs[$next_sign_index];

    return array(
      'current_sign' => $moon_position['sign'],
      'degree_in_sign' => number_format($moon_position['degree_in_sign'], 2) . '°',
      'next_sign' => $next_sign,
      'estimated_ingress' => $next_sign_date->format('Y-m-d H:i'),
      'days_until_ingress' => number_format($days_to_next_sign, 1),
      'is_void_likely' => $degrees_to_next_sign < 5 // Void likely in last 5 degrees
    );
  }

  // ========================================
  // ASTROLOGY SHORTCODE METHODS
  // ========================================

  /**
   * Shortcode for displaying planetary positions
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function planetary_positions_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_motion' => 'yes',
      'show_speed' => 'no',
      'show_degree_in_sign' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'planetary_positions');

    $date = sanitize_text_field($atts['date']);
    $show_motion = sanitize_text_field($atts['show_motion']) === 'yes';
    $show_speed = sanitize_text_field($atts['show_speed']) === 'yes';
    $show_degree_in_sign = sanitize_text_field($atts['show_degree_in_sign']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $positions = $this->get_planetary_positions($date);

    $output = '<div class="baum-cosmology planetary-positions">';
    $output .= '<h3>' . __('Planetary Positions', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($table_style) {
      $output .= '<table class="planetary-positions-table">';
      $output .= '<thead><tr>';
      $output .= '<th>' . __('Planet', 'baum-cosmology') . '</th>';
      $output .= '<th>' . __('Sign', 'baum-cosmology') . '</th>';
      if ($show_degree_in_sign) $output .= '<th>' . __('Degree', 'baum-cosmology') . '</th>';
      $output .= '<th>' . __('Longitude', 'baum-cosmology') . '</th>';
      if ($show_motion) $output .= '<th>' . __('Motion', 'baum-cosmology') . '</th>';
      if ($show_speed) $output .= '<th>' . __('Speed', 'baum-cosmology') . '</th>';
      $output .= '</tr></thead><tbody>';

      foreach ($positions as $position) {
        $output .= '<tr>';
        $output .= '<td><strong>' . esc_html($position['body']) . '</strong></td>';
        $output .= '<td>' . esc_html($position['sign']) . '</td>';
        if ($show_degree_in_sign) $output .= '<td>' . esc_html($position['degree_in_sign']) . '</td>';
        $output .= '<td>' . esc_html($position['longitude']) . '</td>';
        if ($show_motion) $output .= '<td>' . esc_html($position['motion']) . '</td>';
        if ($show_speed) $output .= '<td>' . esc_html($position['speed']) . '</td>';
        $output .= '</tr>';
      }

      $output .= '</tbody></table>';
    } else {
      $output .= '<div class="planetary-positions-list">';
      foreach ($positions as $position) {
        $output .= '<div class="planet-position-item">';
        $output .= '<h4>' . esc_html($position['body']) . '</h4>';
        $output .= '<p><strong>' . __('Sign:', 'baum-cosmology') . '</strong> ' . esc_html($position['sign']) . '</p>';
        if ($show_degree_in_sign) {
          $output .= '<p><strong>' . __('Degree in Sign:', 'baum-cosmology') . '</strong> ' . esc_html($position['degree_in_sign']) . '</p>';
        }
        $output .= '<p><strong>' . __('Longitude:', 'baum-cosmology') . '</strong> ' . esc_html($position['longitude']) . '</p>';
        if ($show_motion) {
          $output .= '<p><strong>' . __('Motion:', 'baum-cosmology') . '</strong> ' . esc_html($position['motion']) . '</p>';
        }
        $output .= '</div>';
      }
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying daily transits
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function daily_transits_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_orb' => 'yes',
      'show_strength' => 'yes',
      'max_orb' => '10',
      'table_style' => 'yes'
    ), $atts, 'daily_transits');

    $date = sanitize_text_field($atts['date']);
    $show_orb = sanitize_text_field($atts['show_orb']) === 'yes';
    $show_strength = sanitize_text_field($atts['show_strength']) === 'yes';
    $max_orb = floatval($atts['max_orb']);
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $transits = $this->get_daily_transits($date);

    // Filter by maximum orb
    $transits = array_filter($transits, function($transit) use ($max_orb) {
      return floatval($transit['orb']) <= $max_orb;
    });

    $output = '<div class="baum-cosmology daily-transits">';
    $output .= '<h3>' . __('Daily Transits & Aspects', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if (empty($transits)) {
      $output .= '<p>' . __('No significant aspects found for this date.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="daily-transits-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Planet 1', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Aspect', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Planet 2', 'baum-cosmology') . '</th>';
        if ($show_orb) $output .= '<th>' . __('Orb', 'baum-cosmology') . '</th>';
        if ($show_strength) $output .= '<th>' . __('Strength', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($transits as $transit) {
          $output .= '<tr>';
          $output .= '<td><strong>' . esc_html($transit['planet1']) . '</strong></td>';
          $output .= '<td>' . esc_html($transit['aspect']) . '</td>';
          $output .= '<td><strong>' . esc_html($transit['planet2']) . '</strong></td>';
          if ($show_orb) $output .= '<td>' . esc_html($transit['orb']) . '</td>';
          if ($show_strength) $output .= '<td>' . esc_html($transit['strength']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="daily-transits-list">';
        foreach ($transits as $transit) {
          $output .= '<div class="transit-item">';
          $output .= '<h4>' . esc_html($transit['planet1']) . ' ' . esc_html($transit['aspect']) . ' ' . esc_html($transit['planet2']) . '</h4>';
          if ($show_orb) {
            $output .= '<p><strong>' . __('Orb:', 'baum-cosmology') . '</strong> ' . esc_html($transit['orb']) . '</p>';
          }
          if ($show_strength) {
            $output .= '<p><strong>' . __('Strength:', 'baum-cosmology') . '</strong> ' . esc_html($transit['strength']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying lunar phases
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function lunar_phases_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'days_ahead' => '30',
      'show_illumination' => 'yes',
      'table_style' => 'yes'
    ), $atts, 'lunar_phases');

    $date = sanitize_text_field($atts['date']);
    $days_ahead = intval($atts['days_ahead']);
    $show_illumination = sanitize_text_field($atts['show_illumination']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $phases = $this->get_upcoming_lunar_phases($date, $days_ahead);

    $output = '<div class="baum-cosmology lunar-phases">';
    $output .= '<h3>' . __('Upcoming Lunar Phases', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date-range">' . sprintf(
      __('Next %d days from %s', 'baum-cosmology'),
      $days_ahead,
      esc_html(date('F j, Y', strtotime($date)))
    ) . '</p>';

    if (empty($phases)) {
      $output .= '<p>' . __('No major lunar phases in the specified period.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="lunar-phases-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Date', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Phase', 'baum-cosmology') . '</th>';
        if ($show_illumination) $output .= '<th>' . __('Illumination', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Days Away', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($phases as $phase) {
          $output .= '<tr>';
          $output .= '<td>' . esc_html(date('M j, Y', strtotime($phase['date']))) . '</td>';
          $output .= '<td><strong>' . esc_html($phase['phase']) . '</strong></td>';
          if ($show_illumination) $output .= '<td>' . esc_html($phase['illumination']) . '</td>';
          $output .= '<td>' . esc_html($phase['days_from_now']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="lunar-phases-list">';
        foreach ($phases as $phase) {
          $output .= '<div class="lunar-phase-item">';
          $output .= '<h4>' . esc_html($phase['phase']) . '</h4>';
          $output .= '<p><strong>' . __('Date:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($phase['date']))) . '</p>';
          if ($show_illumination) {
            $output .= '<p><strong>' . __('Illumination:', 'baum-cosmology') . '</strong> ' . esc_html($phase['illumination']) . '</p>';
          }
          if ($phase['days_from_now'] == 0) {
            $output .= '<p class="phase-today">' . __('Today!', 'baum-cosmology') . '</p>';
          } else {
            $output .= '<p>' . sprintf(__('In %d days', 'baum-cosmology'), $phase['days_from_now']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying zodiac ingresses
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function zodiac_ingresses_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'days_ahead' => '90',
      'table_style' => 'yes'
    ), $atts, 'zodiac_ingresses');

    $date = sanitize_text_field($atts['date']);
    $days_ahead = intval($atts['days_ahead']);
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $ingresses = $this->get_zodiac_ingresses($date, $days_ahead);

    $output = '<div class="baum-cosmology zodiac-ingresses">';
    $output .= '<h3>' . __('Zodiac Ingresses', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date-range">' . sprintf(
      __('Next %d days from %s', 'baum-cosmology'),
      $days_ahead,
      esc_html(date('F j, Y', strtotime($date)))
    ) . '</p>';

    if (empty($ingresses)) {
      $output .= '<p>' . __('No zodiac sign changes in the specified period.', 'baum-cosmology') . '</p>';
    } else {
      if ($table_style) {
        $output .= '<table class="zodiac-ingresses-table">';
        $output .= '<thead><tr>';
        $output .= '<th>' . __('Date', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Planet', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('From', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('To', 'baum-cosmology') . '</th>';
        $output .= '<th>' . __('Days Away', 'baum-cosmology') . '</th>';
        $output .= '</tr></thead><tbody>';

        foreach ($ingresses as $ingress) {
          $output .= '<tr>';
          $output .= '<td>' . esc_html(date('M j, Y', strtotime($ingress['date']))) . '</td>';
          $output .= '<td><strong>' . esc_html($ingress['planet']) . '</strong></td>';
          $output .= '<td>' . esc_html($ingress['from_sign']) . '</td>';
          $output .= '<td>' . esc_html($ingress['to_sign']) . '</td>';
          $output .= '<td>' . esc_html($ingress['days_from_now']) . '</td>';
          $output .= '</tr>';
        }

        $output .= '</tbody></table>';
      } else {
        $output .= '<div class="zodiac-ingresses-list">';
        foreach ($ingresses as $ingress) {
          $output .= '<div class="ingress-item">';
          $output .= '<h4>' . esc_html($ingress['planet']) . ' ' . __('enters', 'baum-cosmology') . ' ' . esc_html($ingress['to_sign']) . '</h4>';
          $output .= '<p><strong>' . __('Date:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($ingress['date']))) . '</p>';
          $output .= '<p><strong>' . __('From:', 'baum-cosmology') . '</strong> ' . esc_html($ingress['from_sign']) . ' → ' . esc_html($ingress['to_sign']) . '</p>';
          if ($ingress['days_from_now'] == 0) {
            $output .= '<p class="ingress-today">' . __('Today!', 'baum-cosmology') . '</p>';
          } else {
            $output .= '<p>' . sprintf(__('In %d days', 'baum-cosmology'), $ingress['days_from_now']) . '</p>';
          }
          $output .= '</div>';
        }
        $output .= '</div>';
      }
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying Moon void-of-course information
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function moon_void_course_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_details' => 'yes'
    ), $atts, 'moon_void_course');

    $date = sanitize_text_field($atts['date']);
    $show_details = sanitize_text_field($atts['show_details']) === 'yes';

    $void_info = $this->get_moon_void_of_course($date);

    $output = '<div class="baum-cosmology moon-void-course">';
    $output .= '<h3>' . __('Moon Void-of-Course', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if (!$void_info) {
      $output .= '<p>' . __('Unable to calculate void-of-course information.', 'baum-cosmology') . '</p>';
    } else {
      $output .= '<div class="void-course-info">';
      $output .= '<p><strong>' . __('Moon in:', 'baum-cosmology') . '</strong> ' . esc_html($void_info['current_sign']) . ' ' . esc_html($void_info['degree_in_sign']) . '</p>';
      $output .= '<p><strong>' . __('Next Sign:', 'baum-cosmology') . '</strong> ' . esc_html($void_info['next_sign']) . '</p>';

      if ($show_details) {
        $output .= '<p><strong>' . __('Estimated Ingress:', 'baum-cosmology') . '</strong> ' . esc_html($void_info['estimated_ingress']) . '</p>';
        $output .= '<p><strong>' . __('Days Until Ingress:', 'baum-cosmology') . '</strong> ' . esc_html($void_info['days_until_ingress']) . '</p>';

        if ($void_info['is_void_likely']) {
          $output .= '<p class="void-warning"><strong>' . __('Void-of-Course Likely:', 'baum-cosmology') . '</strong> ' . __('Moon is in the last degrees of the sign', 'baum-cosmology') . '</p>';
        } else {
          $output .= '<p class="void-safe">' . __('Moon is not currently void-of-course', 'baum-cosmology') . '</p>';
        }
      }

      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for comprehensive astrology calendar
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function astrology_calendar_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_positions' => 'yes',
      'show_transits' => 'yes',
      'show_lunar_phases' => 'yes',
      'show_ingresses' => 'yes',
      'show_void_course' => 'yes',
      'compact' => 'no'
    ), $atts, 'astrology_calendar');

    $date = sanitize_text_field($atts['date']);
    $show_positions = sanitize_text_field($atts['show_positions']) === 'yes';
    $show_transits = sanitize_text_field($atts['show_transits']) === 'yes';
    $show_lunar_phases = sanitize_text_field($atts['show_lunar_phases']) === 'yes';
    $show_ingresses = sanitize_text_field($atts['show_ingresses']) === 'yes';
    $show_void_course = sanitize_text_field($atts['show_void_course']) === 'yes';
    $compact = sanitize_text_field($atts['compact']) === 'yes';

    $output = '<div class="baum-cosmology astrology-calendar">';
    $output .= '<h2>' . __('Astrology Calendar', 'baum-cosmology') . '</h2>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($show_positions) {
      $output .= '<div class="astro-section positions-section">';
      $output .= '<h3>🪐 ' . __('Planetary Positions', 'baum-cosmology') . '</h3>';
      if ($compact) {
        $positions = $this->get_planetary_positions($date);
        $position_summary = array();
        foreach ($positions as $position) {
          $position_summary[] = $position['body'] . ' in ' . $position['sign'];
        }
        $output .= '<p>' . implode(', ', array_slice($position_summary, 0, 5)) . '...</p>';
      } else {
        $output .= $this->planetary_positions_shortcode(array(
          'date' => $date,
          'show_motion' => 'yes',
          'show_degree_in_sign' => 'yes',
          'table_style' => 'yes'
        ));
      }
      $output .= '</div>';
    }

    if ($show_transits) {
      $output .= '<div class="astro-section transits-section">';
      $output .= '<h3>🔗 ' . __('Daily Transits', 'baum-cosmology') . '</h3>';
      if ($compact) {
        $transits = $this->get_daily_transits($date);
        $strong_transits = array_filter($transits, function($t) { return $t['strength'] === __('Strong Influence', 'baum-cosmology'); });
        if (empty($strong_transits)) {
          $output .= '<p>' . __('No strong aspects today', 'baum-cosmology') . '</p>';
        } else {
          $transit = reset($strong_transits);
          $output .= '<p><strong>' . $transit['planet1'] . ' ' . $transit['aspect'] . ' ' . $transit['planet2'] . '</strong></p>';
        }
      } else {
        $output .= $this->daily_transits_shortcode(array(
          'date' => $date,
          'max_orb' => '5',
          'table_style' => 'yes'
        ));
      }
      $output .= '</div>';
    }

    if ($show_void_course) {
      $output .= '<div class="astro-section void-course-section">';
      $output .= '<h3>🌙 ' . __('Moon Void-of-Course', 'baum-cosmology') . '</h3>';
      $output .= $this->moon_void_course_shortcode(array(
        'date' => $date,
        'show_details' => $compact ? 'no' : 'yes'
      ));
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  // ========================================
  // ADVANCED ASTROLOGY METHODS
  // ========================================

  /**
   * Get asteroid positions
   *
   * @param string $date Date in Y-m-d format
   * @return array Asteroid positions
   */
  public function get_asteroid_positions($date = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $jd = $this->get_julian_day($date);

    // Major asteroids with their Swiss Ephemeris IDs
    $asteroids = array(
      'ceres' => array('id' => 1, 'name' => __('Ceres', 'baum-cosmology')),
      'pallas' => array('id' => 2, 'name' => __('Pallas', 'baum-cosmology')),
      'juno' => array('id' => 3, 'name' => __('Juno', 'baum-cosmology')),
      'vesta' => array('id' => 4, 'name' => __('Vesta', 'baum-cosmology')),
      'chiron' => array('id' => 2060, 'name' => __('Chiron', 'baum-cosmology'))
    );

    $signs = array(
      __('Aries', 'baum-cosmology'), __('Taurus', 'baum-cosmology'), __('Gemini', 'baum-cosmology'),
      __('Cancer', 'baum-cosmology'), __('Leo', 'baum-cosmology'), __('Virgo', 'baum-cosmology'),
      __('Libra', 'baum-cosmology'), __('Scorpio', 'baum-cosmology'), __('Sagittarius', 'baum-cosmology'),
      __('Capricorn', 'baum-cosmology'), __('Aquarius', 'baum-cosmology'), __('Pisces', 'baum-cosmology')
    );

    $results = array();

    foreach ($asteroids as $key => $asteroid) {
      if ($this->is_swiss_ephemeris_available()) {
        $asteroid_data = swe_calc_ut($jd, $asteroid['id'], SEFLG_SPEED);
        if (!isset($asteroid_data['xx'][0])) continue;

        $longitude = $asteroid_data['xx'][0];
        $speed = $asteroid_data['xx'][3];
      } else {
        // Fallback calculation (very simplified)
        $longitude = $this->calculate_asteroid_position_fallback($key, $jd);
        $speed = 0.5; // Assume slow motion for asteroids
      }

      $sign_index = floor($longitude / 30);
      $sign = $signs[$sign_index];
      $degree_in_sign = $longitude - ($sign_index * 30);

      $motion = ($speed < 0) ? __('Retrograde', 'baum-cosmology') : __('Direct', 'baum-cosmology');

      $results[] = array(
        'body' => $asteroid['name'],
        'longitude' => number_format($longitude, 2) . '°',
        'sign' => $sign,
        'degree_in_sign' => number_format($degree_in_sign, 2) . '°',
        'motion' => $motion,
        'speed' => number_format($speed, 4)
      );
    }

    return $results;
  }

  /**
   * Fallback asteroid position calculation
   *
   * @param string $asteroid Asteroid key
   * @param float $jd Julian Day
   * @return float Longitude in degrees
   */
  private function calculate_asteroid_position_fallback($asteroid, $jd) {
    // Very simplified calculation for asteroids
    $base_positions = array(
      'ceres' => 120.0,
      'pallas' => 240.0,
      'juno' => 60.0,
      'vesta' => 300.0,
      'chiron' => 180.0
    );

    $daily_motion = array(
      'ceres' => 0.33,
      'pallas' => 0.21,
      'juno' => 0.23,
      'vesta' => 0.27,
      'chiron' => 0.04
    );

    $days_since_epoch = $jd - 2451545.0; // J2000.0 epoch
    $longitude = $base_positions[$asteroid] + ($daily_motion[$asteroid] * $days_since_epoch);

    return fmod($longitude, 360);
  }

  /**
   * Get planetary influence analysis (Elements & Modalities)
   *
   * @param string $date Date in Y-m-d format
   * @return array Planetary influence data
   */
  public function get_planetary_influence($date = null) {
    $positions = $this->get_planetary_positions($date);

    $elements = array(
      'fire' => array('signs' => array('Aries', 'Leo', 'Sagittarius'), 'count' => 0, 'planets' => array()),
      'earth' => array('signs' => array('Taurus', 'Virgo', 'Capricorn'), 'count' => 0, 'planets' => array()),
      'air' => array('signs' => array('Gemini', 'Libra', 'Aquarius'), 'count' => 0, 'planets' => array()),
      'water' => array('signs' => array('Cancer', 'Scorpio', 'Pisces'), 'count' => 0, 'planets' => array())
    );

    $modalities = array(
      'cardinal' => array('signs' => array('Aries', 'Cancer', 'Libra', 'Capricorn'), 'count' => 0, 'planets' => array()),
      'fixed' => array('signs' => array('Taurus', 'Leo', 'Scorpio', 'Aquarius'), 'count' => 0, 'planets' => array()),
      'mutable' => array('signs' => array('Gemini', 'Virgo', 'Sagittarius', 'Pisces'), 'count' => 0, 'planets' => array())
    );

    // Analyze each planet's sign
    foreach ($positions as $position) {
      $planet = $position['body'];
      $sign = $position['sign'];

      // Count elements
      foreach ($elements as $element => &$data) {
        if (in_array($sign, $data['signs'])) {
          $data['count']++;
          $data['planets'][] = $planet;
          break;
        }
      }

      // Count modalities
      foreach ($modalities as $modality => &$data) {
        if (in_array($sign, $data['signs'])) {
          $data['count']++;
          $data['planets'][] = $planet;
          break;
        }
      }
    }

    // Determine dominant element and modality
    $dominant_element = array_keys($elements, max($elements))[0];
    $dominant_modality = array_keys($modalities, max($modalities))[0];

    return array(
      'elements' => $elements,
      'modalities' => $modalities,
      'dominant_element' => $dominant_element,
      'dominant_modality' => $dominant_modality,
      'total_planets' => count($positions)
    );
  }

  /**
   * Get Galactic Center position for a given year
   *
   * @param int $year Year
   * @return array Galactic Center data
   */
  public function get_galactic_center_position($year = null) {
    if (!$year) {
      $year = date('Y');
    }

    // Galactic Center coordinates (approximate, accounting for precession)
    // Base position for 2000.0: 26°54' Sagittarius
    $base_longitude = 266.9; // degrees
    $precession_rate = 0.0139; // degrees per year (approximate)

    $years_since_2000 = $year - 2000;
    $current_longitude = $base_longitude + ($precession_rate * $years_since_2000);

    // Normalize to 0-360
    $current_longitude = fmod($current_longitude, 360);
    if ($current_longitude < 0) $current_longitude += 360;

    $sign_index = floor($current_longitude / 30);
    $degree_in_sign = $current_longitude - ($sign_index * 30);

    $signs = array(
      __('Aries', 'baum-cosmology'), __('Taurus', 'baum-cosmology'), __('Gemini', 'baum-cosmology'),
      __('Cancer', 'baum-cosmology'), __('Leo', 'baum-cosmology'), __('Virgo', 'baum-cosmology'),
      __('Libra', 'baum-cosmology'), __('Scorpio', 'baum-cosmology'), __('Sagittarius', 'baum-cosmology'),
      __('Capricorn', 'baum-cosmology'), __('Aquarius', 'baum-cosmology'), __('Pisces', 'baum-cosmology')
    );

    return array(
      'year' => $year,
      'longitude' => number_format($current_longitude, 2) . '°',
      'sign' => $signs[$sign_index],
      'degree_in_sign' => number_format($degree_in_sign, 2) . '°',
      'precession_adjustment' => number_format($precession_rate * $years_since_2000, 4) . '°'
    );
  }

  /**
   * Generate horoscope interpretation
   *
   * @param string $date Date in Y-m-d format
   * @param string $sign Zodiac sign
   * @return array Horoscope data
   */
  public function generate_horoscope($date = null, $sign = null) {
    if (!$date) {
      $date = date('Y-m-d');
    }

    $positions = $this->get_planetary_positions($date);
    $transits = $this->get_daily_transits($date);
    $moon_phase = $this->get_moon_phase($date);

    // Basic horoscope interpretations by sign
    $horoscope_themes = array(
      'Aries' => array(
        'general' => __('Dynamic energy and new beginnings are highlighted today.', 'baum-cosmology'),
        'love' => __('Passionate encounters and bold romantic gestures are favored.', 'baum-cosmology'),
        'career' => __('Leadership opportunities and competitive advantages emerge.', 'baum-cosmology'),
        'health' => __('High energy levels support physical activities and exercise.', 'baum-cosmology')
      ),
      'Taurus' => array(
        'general' => __('Stability and practical matters take precedence today.', 'baum-cosmology'),
        'love' => __('Sensual pleasures and steady commitment are emphasized.', 'baum-cosmology'),
        'career' => __('Financial security and long-term planning are important.', 'baum-cosmology'),
        'health' => __('Focus on nutrition and building sustainable habits.', 'baum-cosmology')
      ),
      'Gemini' => array(
        'general' => __('Communication and intellectual pursuits are highlighted.', 'baum-cosmology'),
        'love' => __('Witty conversation and mental connection spark romance.', 'baum-cosmology'),
        'career' => __('Networking and information sharing bring opportunities.', 'baum-cosmology'),
        'health' => __('Mental stimulation and variety keep you energized.', 'baum-cosmology')
      ),
      'Cancer' => array(
        'general' => __('Emotional depth and family connections are important today.', 'baum-cosmology'),
        'love' => __('Nurturing and emotional security strengthen relationships.', 'baum-cosmology'),
        'career' => __('Intuitive decisions and caring leadership are valued.', 'baum-cosmology'),
        'health' => __('Emotional well-being affects physical health significantly.', 'baum-cosmology')
      ),
      'Leo' => array(
        'general' => __('Creative expression and recognition are in the spotlight.', 'baum-cosmology'),
        'love' => __('Dramatic gestures and generous affection win hearts.', 'baum-cosmology'),
        'career' => __('Leadership roles and creative projects bring success.', 'baum-cosmology'),
        'health' => __('Confidence and vitality support all endeavors.', 'baum-cosmology')
      ),
      'Virgo' => array(
        'general' => __('Attention to detail and practical service are emphasized.', 'baum-cosmology'),
        'love' => __('Thoughtful gestures and reliability deepen bonds.', 'baum-cosmology'),
        'career' => __('Analytical skills and efficiency are highly valued.', 'baum-cosmology'),
        'health' => __('Preventive care and healthy routines are beneficial.', 'baum-cosmology')
      ),
      'Libra' => array(
        'general' => __('Balance, harmony, and partnerships are key themes today.', 'baum-cosmology'),
        'love' => __('Diplomatic communication and aesthetic beauty attract love.', 'baum-cosmology'),
        'career' => __('Collaboration and fair negotiations bring success.', 'baum-cosmology'),
        'health' => __('Balance between activity and rest is essential.', 'baum-cosmology')
      ),
      'Scorpio' => array(
        'general' => __('Transformation and deep insights are highlighted today.', 'baum-cosmology'),
        'love' => __('Intense emotional connections and passionate encounters.', 'baum-cosmology'),
        'career' => __('Research and investigation reveal hidden opportunities.', 'baum-cosmology'),
        'health' => __('Regenerative practices and emotional healing are important.', 'baum-cosmology')
      ),
      'Sagittarius' => array(
        'general' => __('Adventure, learning, and philosophical pursuits are favored.', 'baum-cosmology'),
        'love' => __('Freedom and shared adventures strengthen relationships.', 'baum-cosmology'),
        'career' => __('Teaching, publishing, and international connections thrive.', 'baum-cosmology'),
        'health' => __('Outdoor activities and optimistic thinking boost wellness.', 'baum-cosmology')
      ),
      'Capricorn' => array(
        'general' => __('Ambition, structure, and long-term goals are emphasized.', 'baum-cosmology'),
        'love' => __('Commitment and traditional values guide relationships.', 'baum-cosmology'),
        'career' => __('Authority, responsibility, and strategic planning bring rewards.', 'baum-cosmology'),
        'health' => __('Discipline and consistent routines support well-being.', 'baum-cosmology')
      ),
      'Aquarius' => array(
        'general' => __('Innovation, friendship, and humanitarian causes are highlighted.', 'baum-cosmology'),
        'love' => __('Unconventional approaches and intellectual connection attract.', 'baum-cosmology'),
        'career' => __('Technology and group efforts lead to breakthrough success.', 'baum-cosmology'),
        'health' => __('Alternative approaches and social support benefit health.', 'baum-cosmology')
      ),
      'Pisces' => array(
        'general' => __('Intuition, creativity, and spiritual connection are important.', 'baum-cosmology'),
        'love' => __('Compassion and emotional sensitivity deepen relationships.', 'baum-cosmology'),
        'career' => __('Artistic pursuits and helping others bring fulfillment.', 'baum-cosmology'),
        'health' => __('Meditation and water-based activities promote healing.', 'baum-cosmology')
      )
    );

    // Get current moon sign for additional influence
    $moon_sign = '';
    foreach ($positions as $position) {
      if ($position['body'] === __('Moon', 'baum-cosmology')) {
        $moon_sign = $position['sign'];
        break;
      }
    }

    // Analyze strongest transits for additional insights
    $strong_transits = array_filter($transits, function($transit) {
      return $transit['strength'] === __('Strong Influence', 'baum-cosmology');
    });

    $transit_influence = '';
    if (!empty($strong_transits)) {
      $strongest = reset($strong_transits);
      $transit_influence = sprintf(
        __('Strong %s between %s and %s adds intensity to the day.', 'baum-cosmology'),
        $strongest['aspect'],
        $strongest['planet1'],
        $strongest['planet2']
      );
    }

    return array(
      'date' => $date,
      'sign' => $sign,
      'moon_sign' => $moon_sign,
      'moon_phase' => $moon_phase['phase_name'],
      'themes' => isset($horoscope_themes[$sign]) ? $horoscope_themes[$sign] : $horoscope_themes['Aries'],
      'transit_influence' => $transit_influence,
      'planetary_positions' => $positions,
      'daily_transits' => $transits
    );
  }

  /**
   * Get historical events for comparison
   *
   * @return array Historical astrological events
   */
  public function get_historical_events() {
    return array(
      array(
        'date' => '2020-12-21',
        'event' => __('Great Conjunction of Jupiter and Saturn', 'baum-cosmology'),
        'description' => __('Closest Jupiter-Saturn conjunction in 400 years', 'baum-cosmology'),
        'significance' => __('Major societal and technological shifts', 'baum-cosmology')
      ),
      array(
        'date' => '2012-12-21',
        'event' => __('Galactic Alignment', 'baum-cosmology'),
        'description' => __('Sun aligned with Galactic Center', 'baum-cosmology'),
        'significance' => __('Spiritual awakening and consciousness shift', 'baum-cosmology')
      ),
      array(
        'date' => '2000-05-05',
        'event' => __('Grand Alignment', 'baum-cosmology'),
        'description' => __('Multiple planets aligned in Taurus', 'baum-cosmology'),
        'significance' => __('Material and financial transformations', 'baum-cosmology')
      ),
      array(
        'date' => '1987-08-16',
        'event' => __('Harmonic Convergence', 'baum-cosmology'),
        'description' => __('Planetary alignment in fire signs', 'baum-cosmology'),
        'significance' => __('Global spiritual awakening movement', 'baum-cosmology')
      ),
      array(
        'date' => '1962-02-05',
        'event' => __('Aquarius Stellium', 'baum-cosmology'),
        'description' => __('Seven planets in Aquarius', 'baum-cosmology'),
        'significance' => __('Beginning of the Age of Aquarius', 'baum-cosmology')
      )
    );
  }

  // ========================================
  // ADVANCED ASTROLOGY SHORTCODE METHODS
  // ========================================

  /**
   * Shortcode for displaying asteroid positions
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function asteroid_positions_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_motion' => 'yes',
      'show_speed' => 'no',
      'table_style' => 'yes'
    ), $atts, 'asteroid_positions');

    $date = sanitize_text_field($atts['date']);
    $show_motion = sanitize_text_field($atts['show_motion']) === 'yes';
    $show_speed = sanitize_text_field($atts['show_speed']) === 'yes';
    $table_style = sanitize_text_field($atts['table_style']) === 'yes';

    $asteroids = $this->get_asteroid_positions($date);

    $output = '<div class="baum-cosmology asteroid-positions">';
    $output .= '<h3>' . __('Asteroid Positions', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($table_style) {
      $output .= '<table class="asteroid-positions-table">';
      $output .= '<thead><tr>';
      $output .= '<th>' . __('Asteroid', 'baum-cosmology') . '</th>';
      $output .= '<th>' . __('Sign', 'baum-cosmology') . '</th>';
      $output .= '<th>' . __('Degree', 'baum-cosmology') . '</th>';
      $output .= '<th>' . __('Longitude', 'baum-cosmology') . '</th>';
      if ($show_motion) $output .= '<th>' . __('Motion', 'baum-cosmology') . '</th>';
      if ($show_speed) $output .= '<th>' . __('Speed', 'baum-cosmology') . '</th>';
      $output .= '</tr></thead><tbody>';

      foreach ($asteroids as $asteroid) {
        $output .= '<tr>';
        $output .= '<td><strong>' . esc_html($asteroid['body']) . '</strong></td>';
        $output .= '<td>' . esc_html($asteroid['sign']) . '</td>';
        $output .= '<td>' . esc_html($asteroid['degree_in_sign']) . '</td>';
        $output .= '<td>' . esc_html($asteroid['longitude']) . '</td>';
        if ($show_motion) $output .= '<td>' . esc_html($asteroid['motion']) . '</td>';
        if ($show_speed) $output .= '<td>' . esc_html($asteroid['speed']) . '</td>';
        $output .= '</tr>';
      }

      $output .= '</tbody></table>';
    } else {
      $output .= '<div class="asteroid-positions-list">';
      foreach ($asteroids as $asteroid) {
        $output .= '<div class="asteroid-item">';
        $output .= '<h4>' . esc_html($asteroid['body']) . '</h4>';
        $output .= '<p><strong>' . __('Sign:', 'baum-cosmology') . '</strong> ' . esc_html($asteroid['sign']) . '</p>';
        $output .= '<p><strong>' . __('Degree:', 'baum-cosmology') . '</strong> ' . esc_html($asteroid['degree_in_sign']) . '</p>';
        if ($show_motion) {
          $output .= '<p><strong>' . __('Motion:', 'baum-cosmology') . '</strong> ' . esc_html($asteroid['motion']) . '</p>';
        }
        $output .= '</div>';
      }
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for displaying planetary influence analysis
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function planetary_influence_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_planets' => 'yes',
      'show_percentages' => 'yes'
    ), $atts, 'planetary_influence');

    $date = sanitize_text_field($atts['date']);
    $show_planets = sanitize_text_field($atts['show_planets']) === 'yes';
    $show_percentages = sanitize_text_field($atts['show_percentages']) === 'yes';

    $influence = $this->get_planetary_influence($date);

    $output = '<div class="baum-cosmology planetary-influence">';
    $output .= '<h3>' . __('Planetary Influence Analysis', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    // Elements section
    $output .= '<div class="elements-section">';
    $output .= '<h4>' . __('Elemental Distribution', 'baum-cosmology') . '</h4>';
    $output .= '<table class="elements-table">';
    $output .= '<thead><tr><th>' . __('Element', 'baum-cosmology') . '</th><th>' . __('Count', 'baum-cosmology') . '</th>';
    if ($show_percentages) $output .= '<th>' . __('Percentage', 'baum-cosmology') . '</th>';
    if ($show_planets) $output .= '<th>' . __('Planets', 'baum-cosmology') . '</th>';
    $output .= '</tr></thead><tbody>';

    foreach ($influence['elements'] as $element => $data) {
      $percentage = round(($data['count'] / $influence['total_planets']) * 100, 1);
      $output .= '<tr>';
      $output .= '<td><strong>' . esc_html(ucfirst($element)) . '</strong></td>';
      $output .= '<td>' . esc_html($data['count']) . '</td>';
      if ($show_percentages) $output .= '<td>' . esc_html($percentage) . '%</td>';
      if ($show_planets) $output .= '<td>' . esc_html(implode(', ', $data['planets'])) . '</td>';
      $output .= '</tr>';
    }

    $output .= '</tbody></table>';
    $output .= '<p><strong>' . __('Dominant Element:', 'baum-cosmology') . '</strong> ' . esc_html(ucfirst($influence['dominant_element'])) . '</p>';
    $output .= '</div>';

    // Modalities section
    $output .= '<div class="modalities-section">';
    $output .= '<h4>' . __('Modal Distribution', 'baum-cosmology') . '</h4>';
    $output .= '<table class="modalities-table">';
    $output .= '<thead><tr><th>' . __('Modality', 'baum-cosmology') . '</th><th>' . __('Count', 'baum-cosmology') . '</th>';
    if ($show_percentages) $output .= '<th>' . __('Percentage', 'baum-cosmology') . '</th>';
    if ($show_planets) $output .= '<th>' . __('Planets', 'baum-cosmology') . '</th>';
    $output .= '</tr></thead><tbody>';

    foreach ($influence['modalities'] as $modality => $data) {
      $percentage = round(($data['count'] / $influence['total_planets']) * 100, 1);
      $output .= '<tr>';
      $output .= '<td><strong>' . esc_html(ucfirst($modality)) . '</strong></td>';
      $output .= '<td>' . esc_html($data['count']) . '</td>';
      if ($show_percentages) $output .= '<td>' . esc_html($percentage) . '%</td>';
      if ($show_planets) $output .= '<td>' . esc_html(implode(', ', $data['planets'])) . '</td>';
      $output .= '</tr>';
    }

    $output .= '</tbody></table>';
    $output .= '<p><strong>' . __('Dominant Modality:', 'baum-cosmology') . '</strong> ' . esc_html(ucfirst($influence['dominant_modality'])) . '</p>';
    $output .= '</div>';

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for horoscope generation
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function horoscope_generator_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'sign' => 'Aries',
      'show_transits' => 'yes',
      'show_moon_phase' => 'yes'
    ), $atts, 'horoscope_generator');

    $date = sanitize_text_field($atts['date']);
    $sign = sanitize_text_field($atts['sign']);
    $show_transits = sanitize_text_field($atts['show_transits']) === 'yes';
    $show_moon_phase = sanitize_text_field($atts['show_moon_phase']) === 'yes';

    $horoscope = $this->generate_horoscope($date, $sign);

    $output = '<div class="baum-cosmology horoscope-generator">';
    $output .= '<h3>' . sprintf(__('Horoscope for %s', 'baum-cosmology'), esc_html($sign)) . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($show_moon_phase) {
      $output .= '<p class="moon-phase"><strong>' . __('Moon Phase:', 'baum-cosmology') . '</strong> ' . esc_html($horoscope['moon_phase']) . ' ' . __('in', 'baum-cosmology') . ' ' . esc_html($horoscope['moon_sign']) . '</p>';
    }

    $output .= '<div class="horoscope-themes">';
    foreach ($horoscope['themes'] as $area => $theme) {
      $output .= '<div class="theme-area">';
      $output .= '<h4>' . esc_html(ucfirst($area)) . '</h4>';
      $output .= '<p>' . esc_html($theme) . '</p>';
      $output .= '</div>';
    }
    $output .= '</div>';

    if ($show_transits && $horoscope['transit_influence']) {
      $output .= '<div class="transit-influence">';
      $output .= '<h4>' . __('Planetary Influence', 'baum-cosmology') . '</h4>';
      $output .= '<p>' . esc_html($horoscope['transit_influence']) . '</p>';
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for Galactic Center information
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function galactic_center_shortcode($atts) {
    $atts = shortcode_atts(array(
      'year' => date('Y'),
      'show_precession' => 'yes'
    ), $atts, 'galactic_center');

    $year = intval($atts['year']);
    $show_precession = sanitize_text_field($atts['show_precession']) === 'yes';

    $gc_data = $this->get_galactic_center_position($year);

    $output = '<div class="baum-cosmology galactic-center">';
    $output .= '<h3>' . __('Galactic Center Position', 'baum-cosmology') . '</h3>';
    $output .= '<p class="year">' . sprintf(__('Year: %d', 'baum-cosmology'), $year) . '</p>';

    $output .= '<div class="gc-position">';
    $output .= '<p><strong>' . __('Position:', 'baum-cosmology') . '</strong> ' . esc_html($gc_data['degree_in_sign']) . ' ' . esc_html($gc_data['sign']) . '</p>';
    $output .= '<p><strong>' . __('Longitude:', 'baum-cosmology') . '</strong> ' . esc_html($gc_data['longitude']) . '</p>';

    if ($show_precession) {
      $output .= '<p><strong>' . __('Precession Adjustment:', 'baum-cosmology') . '</strong> ' . esc_html($gc_data['precession_adjustment']) . '</p>';
    }

    $output .= '</div>';
    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for historical comparison
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function historical_comparison_shortcode($atts) {
    $atts = shortcode_atts(array(
      'date' => date('Y-m-d'),
      'show_descriptions' => 'yes',
      'limit' => '5'
    ), $atts, 'historical_comparison');

    $date = sanitize_text_field($atts['date']);
    $show_descriptions = sanitize_text_field($atts['show_descriptions']) === 'yes';
    $limit = intval($atts['limit']);

    $historical_events = $this->get_historical_events();
    $current_positions = $this->get_planetary_positions($date);

    $output = '<div class="baum-cosmology historical-comparison">';
    $output .= '<h3>' . __('Historical Astrological Events', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Comparing to: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    $output .= '<div class="historical-events">';
    foreach (array_slice($historical_events, 0, $limit) as $event) {
      $output .= '<div class="historical-event">';
      $output .= '<h4>' . esc_html($event['event']) . '</h4>';
      $output .= '<p class="event-date"><strong>' . __('Date:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($event['date']))) . '</p>';

      if ($show_descriptions) {
        $output .= '<p class="event-description">' . esc_html($event['description']) . '</p>';
        $output .= '<p class="event-significance"><strong>' . __('Significance:', 'baum-cosmology') . '</strong> ' . esc_html($event['significance']) . '</p>';
      }

      $output .= '</div>';
    }
    $output .= '</div>';

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for astrology timeline
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function astrology_timeline_shortcode($atts) {
    $atts = shortcode_atts(array(
      'start_date' => date('Y-m-d'),
      'days_ahead' => '30',
      'show_transits' => 'yes',
      'show_ingresses' => 'yes',
      'show_phases' => 'yes'
    ), $atts, 'astrology_timeline');

    $start_date = sanitize_text_field($atts['start_date']);
    $days_ahead = intval($atts['days_ahead']);
    $show_transits = sanitize_text_field($atts['show_transits']) === 'yes';
    $show_ingresses = sanitize_text_field($atts['show_ingresses']) === 'yes';
    $show_phases = sanitize_text_field($atts['show_phases']) === 'yes';

    $output = '<div class="baum-cosmology astrology-timeline">';
    $output .= '<h3>' . __('Astrological Timeline', 'baum-cosmology') . '</h3>';
    $output .= '<p class="date-range">' . sprintf(
      __('%d days from %s', 'baum-cosmology'),
      $days_ahead,
      esc_html(date('F j, Y', strtotime($start_date)))
    ) . '</p>';

    $timeline_events = array();

    if ($show_phases) {
      $lunar_phases = $this->get_upcoming_lunar_phases($start_date, $days_ahead);
      foreach ($lunar_phases as $phase) {
        $timeline_events[] = array(
          'date' => $phase['date'],
          'type' => 'lunar_phase',
          'event' => $phase['phase'],
          'description' => $phase['illumination']
        );
      }
    }

    if ($show_ingresses) {
      $ingresses = $this->get_zodiac_ingresses($start_date, $days_ahead);
      foreach ($ingresses as $ingress) {
        $timeline_events[] = array(
          'date' => $ingress['date'],
          'type' => 'ingress',
          'event' => $ingress['planet'] . ' ' . __('enters', 'baum-cosmology') . ' ' . $ingress['to_sign'],
          'description' => __('Sign change', 'baum-cosmology')
        );
      }
    }

    // Sort events by date
    usort($timeline_events, function($a, $b) {
      return strtotime($a['date']) - strtotime($b['date']);
    });

    $output .= '<div class="timeline-events">';
    foreach ($timeline_events as $event) {
      $output .= '<div class="timeline-event ' . esc_attr($event['type']) . '">';
      $output .= '<div class="event-date">' . esc_html(date('M j', strtotime($event['date']))) . '</div>';
      $output .= '<div class="event-content">';
      $output .= '<h4>' . esc_html($event['event']) . '</h4>';
      $output .= '<p>' . esc_html($event['description']) . '</p>';
      $output .= '</div>';
      $output .= '</div>';
    }
    $output .= '</div>';

    $output .= '</div>';

    return $output;
  }

  /**
   * Shortcode for user-specific horoscope
   *
   * @param array $atts Shortcode attributes
   * @return string HTML output
   */
  public function user_horoscope_shortcode($atts) {
    $atts = shortcode_atts(array(
      'user_id' => get_current_user_id(),
      'date' => date('Y-m-d'),
      'show_birth_chart' => 'no'
    ), $atts, 'user_horoscope');

    $user_id = intval($atts['user_id']);
    $date = sanitize_text_field($atts['date']);
    $show_birth_chart = sanitize_text_field($atts['show_birth_chart']) === 'yes';

    if (!$user_id) {
      return '<p>' . __('Please log in to view your personalized horoscope.', 'baum-cosmology') . '</p>';
    }

    // Get user's birth data (would be stored in user meta)
    $birth_date = get_user_meta($user_id, 'birth_date', true);
    $birth_time = get_user_meta($user_id, 'birth_time', true);
    $birth_location = get_user_meta($user_id, 'birth_location', true);
    $sun_sign = get_user_meta($user_id, 'sun_sign', true);

    if (!$sun_sign) {
      $sun_sign = 'Aries'; // Default fallback
    }

    $horoscope = $this->generate_horoscope($date, $sun_sign);

    $output = '<div class="baum-cosmology user-horoscope">';
    $output .= '<h3>' . sprintf(__('Personal Horoscope for %s', 'baum-cosmology'), esc_html($sun_sign)) . '</h3>';
    $output .= '<p class="date">' . sprintf(__('Date: %s', 'baum-cosmology'), esc_html(date('F j, Y', strtotime($date)))) . '</p>';

    if ($birth_date) {
      $output .= '<p class="birth-info"><strong>' . __('Birth Date:', 'baum-cosmology') . '</strong> ' . esc_html(date('F j, Y', strtotime($birth_date))) . '</p>';
    }

    $output .= '<div class="personal-horoscope-themes">';
    foreach ($horoscope['themes'] as $area => $theme) {
      $output .= '<div class="personal-theme-area">';
      $output .= '<h4>' . esc_html(ucfirst($area)) . '</h4>';
      $output .= '<p>' . esc_html($theme) . '</p>';
      $output .= '</div>';
    }
    $output .= '</div>';

    if ($horoscope['transit_influence']) {
      $output .= '<div class="personal-transit-influence">';
      $output .= '<h4>' . __('Current Planetary Influence', 'baum-cosmology') . '</h4>';
      $output .= '<p>' . esc_html($horoscope['transit_influence']) . '</p>';
      $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
  }
}

// Initialize the plugin
$baum_cosmology = new Baum_Cosmology();
