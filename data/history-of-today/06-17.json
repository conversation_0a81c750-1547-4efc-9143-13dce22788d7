{"date": "June 17", "url": "https://wikipedia.org/wiki/June_17", "data": {"Events": [{"year": "653", "text": "<PERSON> is arrested and taken to Constantinople, due to his opposition to monothelitism.", "html": "653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> I</a> is arrested and taken to Constantinople, due to his opposition to <a href=\"https://wikipedia.org/wiki/Monothelitism\" title=\"Monothelitism\">monothelitism</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> I</a> is arrested and taken to Constantinople, due to his opposition to <a href=\"https://wikipedia.org/wiki/Monothelitism\" title=\"Monothelitism\">monothelitism</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Monothelitism", "link": "https://wikipedia.org/wiki/Monothelitism"}]}, {"year": "1242", "text": "Following the Disputation of Paris, twenty-four carriage loads of Jewish religious manuscripts were burnt in Paris.", "html": "1242 - Following the <a href=\"https://wikipedia.org/wiki/Disputation_of_Paris\" title=\"Disputation of Paris\">Disputation of Paris</a>, twenty-four carriage loads of Jewish religious manuscripts were burnt in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/Disputation_of_Paris\" title=\"Disputation of Paris\">Disputation of Paris</a>, twenty-four carriage loads of Jewish religious manuscripts were burnt in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a>.", "links": [{"title": "Disputation of Paris", "link": "https://wikipedia.org/wiki/Disputation_of_Paris"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}]}, {"year": "1300", "text": "Turku Cathedral is consecrated by Bishop <PERSON> in the city of Turku (Swedish: Åbo).", "html": "1300 - <a href=\"https://wikipedia.org/wiki/Turku_Cathedral\" title=\"Turku Cathedral\">Turku Cathedral</a> is consecrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\">Bishop <PERSON> I</a> in the city of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON></i>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Turku_Cathedral\" title=\"Turku Cathedral\">Turku Cathedral</a> is consecrated by <a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\">Bishop <PERSON> I</a> in the city of <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a> (<a href=\"https://wikipedia.org/wiki/Swedish_language\" title=\"Swedish language\">Swedish</a>: <i lang=\"sv\"><PERSON><PERSON></i>).", "links": [{"title": "Turku Cathedral", "link": "https://wikipedia.org/wiki/Turku_Cathedral"}, {"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_(bishop)"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}, {"title": "Swedish language", "link": "https://wikipedia.org/wiki/Swedish_language"}]}, {"year": "1397", "text": "The Kalmar Union is formed under the rule of <PERSON> of Denmark.", "html": "1397 - The <a href=\"https://wikipedia.org/wiki/Kalmar_Union\" title=\"Kalmar Union\">Kalmar Union</a> is formed under the rule of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kalmar_Union\" title=\"Kalmar Union\">Kalmar Union</a> is formed under the rule of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>.", "links": [{"title": "Kalmar Union", "link": "https://wikipedia.org/wiki/Kalmar_Union"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1462", "text": "<PERSON> the I<PERSON><PERSON>r attempts to assassinate <PERSON><PERSON><PERSON> (The Night Attack at Târgovişte), forcing him to retreat from Wallachia.", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Impaler\" title=\"<PERSON> the Impaler\"><PERSON> the Impaler</a> attempts to <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinate</a> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\">Mehmed II</a> (<a href=\"https://wikipedia.org/wiki/Night_Attack_at_T%C3%A2rgovi%C5%9Fte\" class=\"mw-redirect\" title=\"Night Attack at Târgovişte\">The Night Attack at Târgovişte</a>), forcing him to retreat from <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Impaler\" title=\"<PERSON> the Impaler\"><PERSON> the Impaler</a> attempts to <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinate</a> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\">Mehmed II</a> (<a href=\"https://wikipedia.org/wiki/Night_Attack_at_T%C3%A2rgovi%C5%9Fte\" class=\"mw-redirect\" title=\"Night Attack at Târgovişte\">The Night Attack at Târgovişte</a>), forcing him to retreat from <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a>.", "links": [{"title": "<PERSON> the Impaler", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>r"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}, {"title": "Night Attack at Târgovişte", "link": "https://wikipedia.org/wiki/Night_Attack_at_T%C3%A2rgovi%C5%9Fte"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}]}, {"year": "1497", "text": "Battle of Deptford Bridge: Forces under King <PERSON> defeat troops led by <PERSON>.", "html": "1497 - <a href=\"https://wikipedia.org/wiki/Cornish_Rebellion_of_1497#Battle_of_Deptford_Bridge\" class=\"mw-redirect\" title=\"Cornish Rebellion of 1497\">Battle of Deptford Bridge</a>: Forces under King <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_England\" title=\"Henry VII of England\"><PERSON> VII</a> defeat troops led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornish_Rebellion_of_1497#Battle_of_Deptford_Bridge\" class=\"mw-redirect\" title=\"Cornish Rebellion of 1497\">Battle of Deptford Bridge</a>: Forces under King <a href=\"https://wikipedia.org/wiki/Henry_VII_of_England\" title=\"Henry VII of England\"><PERSON></a> defeat troops led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cornish Rebellion of 1497", "link": "https://wikipedia.org/wiki/Cornish_Rebellion_of_1497#Battle_of_Deptford_Bridge"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON> assassinates the 13th <PERSON><PERSON><PERSON> shō<PERSON>, <PERSON><PERSON><PERSON>.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assassinates the 13th <a href=\"https://wikipedia.org/wiki/Ashikaga_shogunate\" title=\"Ashikaga shogunate\">Ashikaga shōgun</a>, <a href=\"https://wikipedia.org/wiki/Ashikaga_Yoshiteru\" title=\"Ashikaga Yoshiteru\">Ash<PERSON><PERSON> Yoshiteru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> assassinates the 13th <a href=\"https://wikipedia.org/wiki/Ashikaga_shogunate\" title=\"Ashikaga shogunate\">Ashikaga shōgun</a>, <a href=\"https://wikipedia.org/wiki/Ashikaga_Yoshiteru\" title=\"Ashikaga Yoshiteru\">Ashikaga Yoshiteru</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ashikaga shogunate", "link": "https://wikipedia.org/wiki/Ashikaga_shogunate"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashikaga_Yoshiteru"}]}, {"year": "1579", "text": "Sir <PERSON> claims a land he calls Nova Albion (modern California) for England.", "html": "1579 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a> claims a land he calls <i><a href=\"https://wikipedia.org/wiki/New_Albion\" title=\"New Albion\">Nova Albion</a></i> (modern <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>) for <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a> claims a land he calls <i><a href=\"https://wikipedia.org/wiki/New_Albion\" title=\"New Albion\">Nova Albion</a></i> (modern <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>) for <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Albion", "link": "https://wikipedia.org/wiki/New_Albion"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}, {"year": "1596", "text": "The Dutch explorer <PERSON> discovers the Arctic archipelago of Spitsbergen.", "html": "1596 - The Dutch explorer <a href=\"https://wikipedia.org/wiki/Willem_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the Arctic archipelago of <a href=\"https://wikipedia.org/wiki/Svalbard\" title=\"Svalbard\">Spitsbergen</a>.", "no_year_html": "The Dutch explorer <a href=\"https://wikipedia.org/wiki/Willem_<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers the Arctic archipelago of <a href=\"https://wikipedia.org/wiki/Svalbard\" title=\"Svalbard\">Spitsbergen</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Svalbard", "link": "https://wikipedia.org/wiki/Svalbard"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON> dies during childbirth. Her husband, Mughal emperor <PERSON>, will spend the next 17 years building her mausoleum, the Taj Mahal.", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dies during childbirth. Her husband, <a href=\"https://wikipedia.org/wiki/Mughal_emperor\" class=\"mw-redirect\" title=\"Mughal emperor\">Mughal emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> I, will spend the next 17 years building her <a href=\"https://wikipedia.org/wiki/Mausoleum\" title=\"Mausoleum\">mausoleum</a>, the <a href=\"https://wikipedia.org/wiki/Taj_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> dies during childbirth. Her husband, <a href=\"https://wikipedia.org/wiki/Mughal_emperor\" class=\"mw-redirect\" title=\"Mughal emperor\">Mughal emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> I, will spend the next 17 years building her <a href=\"https://wikipedia.org/wiki/Mausoleum\" title=\"Mausoleum\">mausoleum</a>, the <a href=\"https://wikipedia.org/wiki/Taj_<PERSON>\" title=\"Taj <PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mughal emperor", "link": "https://wikipedia.org/wiki/Mughal_emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mausoleum", "link": "https://wikipedia.org/wiki/Mausoleum"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1665", "text": "Battle of Montes Claros: Portugal definitively secured independence from Spain in the last battle of the Portuguese Restoration War.", "html": "1665 - <a href=\"https://wikipedia.org/wiki/Battle_of_Montes_Claros\" title=\"Battle of Montes Claros\">Battle of Montes Claros</a>: Portugal definitively secured independence from Spain in the last battle of the <a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Montes_Claros\" title=\"Battle of Montes Claros\">Battle of Montes Claros</a>: Portugal definitively secured independence from Spain in the last battle of the <a href=\"https://wikipedia.org/wiki/Portuguese_Restoration_War\" title=\"Portuguese Restoration War\">Portuguese Restoration War</a>.", "links": [{"title": "Battle of Montes Claros", "link": "https://wikipedia.org/wiki/Battle_of_Montes_Claros"}, {"title": "Portuguese Restoration War", "link": "https://wikipedia.org/wiki/Portuguese_Restoration_War"}]}, {"year": "1673", "text": "French explorers <PERSON> and <PERSON> reach the Mississippi River and become the first Europeans to make a detailed account of its course.", "html": "1673 - <a href=\"https://wikipedia.org/wiki/French_people\" title=\"French people\">French</a> explorers <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> and become the first Europeans to make a detailed account of its course.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_people\" title=\"French people\">French</a> explorers <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> and become the first Europeans to make a detailed account of its course.", "links": [{"title": "French people", "link": "https://wikipedia.org/wiki/French_people"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}]}, {"year": "1767", "text": "<PERSON>, a British sea captain, sights Tahiti and is considered the first European to reach the island.", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a British sea captain, sights <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a> and is considered the first European to reach the island.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a British sea captain, sights <a href=\"https://wikipedia.org/wiki/Tahiti\" title=\"Tahiti\">Tahiti</a> and is considered the first European to reach the island.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tahiti", "link": "https://wikipedia.org/wiki/Tahiti"}]}, {"year": "1773", "text": "Cúcuta, Colombia, is founded by <PERSON><PERSON>llar.", "html": "1773 - <a href=\"https://wikipedia.org/wiki/C%C3%BAcuta\" title=\"Cúcuta\">Cúcuta</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, is founded by <a href=\"https://wikipedia.org/wiki/Juana_Rangel_de_Cu%C3%A9llar\" title=\"Juana Rangel de Cuéllar\">Juana Rangel de Cuéllar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%BAcuta\" title=\"Cúcuta\">Cúcuta</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, is founded by <a href=\"https://wikipedia.org/wiki/Juana_Rangel_de_Cu%C3%A9llar\" title=\"Juana Rangel de Cuéllar\">Juana Rangel de Cuéllar</a>.", "links": [{"title": "Cúcuta", "link": "https://wikipedia.org/wiki/C%C3%BAcuta"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Juana Rangel de Cuéllar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Rangel_de_Cu%C3%A9llar"}]}, {"year": "1775", "text": "American Revolutionary War: Colonists inflict heavy casualties on British forces while losing the Battle of Bunker Hill.", "html": "1775 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Colonists inflict heavy casualties on British forces while losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Bunker_Hill\" title=\"Battle of Bunker Hill\">Battle of Bunker Hill</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Colonists inflict heavy casualties on British forces while losing the <a href=\"https://wikipedia.org/wiki/Battle_of_Bunker_Hill\" title=\"Battle of Bunker Hill\">Battle of Bunker Hill</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Bunker Hill", "link": "https://wikipedia.org/wiki/Battle_of_Bunker_Hill"}]}, {"year": "1789", "text": "In France, the Third Estate declares itself the National Assembly.", "html": "1789 - In <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, the <a href=\"https://wikipedia.org/wiki/Estates_of_the_realm\" title=\"Estates of the realm\">Third Estate</a> declares itself the <a href=\"https://wikipedia.org/wiki/National_Assembly_(French_Revolution)\" title=\"National Assembly (French Revolution)\">National Assembly</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>, the <a href=\"https://wikipedia.org/wiki/Estates_of_the_realm\" title=\"Estates of the realm\">Third Estate</a> declares itself the <a href=\"https://wikipedia.org/wiki/National_Assembly_(French_Revolution)\" title=\"National Assembly (French Revolution)\">National Assembly</a>.", "links": [{"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "Estates of the realm", "link": "https://wikipedia.org/wiki/Estates_of_the_realm"}, {"title": "National Assembly (French Revolution)", "link": "https://wikipedia.org/wiki/National_Assembly_(French_Revolution)"}]}, {"year": "1794", "text": "Foundation of Anglo-Corsican Kingdom.", "html": "1794 - Foundation of <a href=\"https://wikipedia.org/wiki/Anglo-Corsican_Kingdom\" title=\"Anglo-Corsican Kingdom\">Anglo-Corsican Kingdom</a>.", "no_year_html": "Foundation of <a href=\"https://wikipedia.org/wiki/Anglo-Corsican_Kingdom\" title=\"Anglo-Corsican Kingdom\">Anglo-Corsican Kingdom</a>.", "links": [{"title": "Anglo-Corsican Kingdom", "link": "https://wikipedia.org/wiki/Anglo-Corsican_Kingdom"}]}, {"year": "1795", "text": "The burghers of Swellendam expel the Dutch East India Company magistrate and declare a republic.", "html": "1795 - The burghers of <a href=\"https://wikipedia.org/wiki/Swellendam\" title=\"Swellendam\">Swellendam</a> expel the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> magistrate and declare a republic.", "no_year_html": "The burghers of <a href=\"https://wikipedia.org/wiki/Swellendam\" title=\"Swellendam\">Swellendam</a> expel the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> magistrate and declare a republic.", "links": [{"title": "Swellendam", "link": "https://wikipedia.org/wiki/Swellendam"}, {"title": "Dutch East India Company", "link": "https://wikipedia.org/wiki/Dutch_East_India_Company"}]}, {"year": "1839", "text": "In the Kingdom of Hawaii, <PERSON><PERSON><PERSON><PERSON> <PERSON> issues the edict of toleration which gives Roman Catholics the freedom to worship in the Hawaiian Islands. The Hawaii Catholic Church and the Cathedral of Our Lady of Peace are established as a result.", "html": "1839 - In the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>, <a href=\"https://wikipedia.org/wiki/Kamehameha_III\" title=\"Kamehameha III\">Kamehameha III</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_toleration\" title=\"Edict of toleration\">edict of toleration</a> which gives <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholics</a> the freedom to worship in the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>. The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu\" title=\"Roman Catholic Diocese of Honolulu\">Hawaii Catholic Church</a> and the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace\" class=\"mw-redirect\" title=\"Cathedral of Our Lady of Peace\">Cathedral of Our Lady of Peace</a> are established as a result.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hawaii\" class=\"mw-redirect\" title=\"Kingdom of Hawaii\">Kingdom of Hawaii</a>, <a href=\"https://wikipedia.org/wiki/Kamehameha_III\" title=\"Kamehameha III\">Kamehameha III</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_toleration\" title=\"Edict of toleration\">edict of toleration</a> which gives <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Church\" class=\"mw-redirect\" title=\"Roman Catholic Church\">Roman Catholics</a> the freedom to worship in the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>. The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu\" title=\"Roman Catholic Diocese of Honolulu\">Hawaii Catholic Church</a> and the <a href=\"https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace\" class=\"mw-redirect\" title=\"Cathedral of Our Lady of Peace\">Cathedral of Our Lady of Peace</a> are established as a result.", "links": [{"title": "Kingdom of Hawaii", "link": "https://wikipedia.org/wiki/Kingdom_of_Hawaii"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kamehameha_III"}, {"title": "Edict of toleration", "link": "https://wikipedia.org/wiki/Edict_of_toleration"}, {"title": "Roman Catholic Church", "link": "https://wikipedia.org/wiki/Roman_Catholic_Church"}, {"title": "Hawaiian Islands", "link": "https://wikipedia.org/wiki/Hawaiian_Islands"}, {"title": "Roman Catholic Diocese of Honolulu", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu"}, {"title": "Cathedral of Our Lady of Peace", "link": "https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace"}]}, {"year": "1843", "text": "The Wairau Affray, the first serious clash of arms between Māori and British settlers in the New Zealand Wars, takes place.", "html": "1843 - The <a href=\"https://wikipedia.org/wiki/Wairau_Affray\" title=\"Wairau Affray\">Wairau Affray</a>, the first serious clash of arms between <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> and British settlers in the <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a>, takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wairau_Affray\" title=\"Wairau Affray\">Wairau Affray</a>, the first serious clash of arms between <a href=\"https://wikipedia.org/wiki/M%C4%81ori_people\" title=\"Māori people\">Māori</a> and British settlers in the <a href=\"https://wikipedia.org/wiki/New_Zealand_Wars\" title=\"New Zealand Wars\">New Zealand Wars</a>, takes place.", "links": [{"title": "Wairau Affray", "link": "https://wikipedia.org/wiki/Wairau_<PERSON>ray"}, {"title": "Māori people", "link": "https://wikipedia.org/wiki/M%C4%81ori_people"}, {"title": "New Zealand Wars", "link": "https://wikipedia.org/wiki/New_Zealand_Wars"}]}, {"year": "1861", "text": "American Civil War: Battle of Vienna, Virginia.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna,_Virginia\" title=\"Battle of Vienna, Virginia\">Battle of Vienna, Virginia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Vienna,_Virginia\" title=\"Battle of Vienna, Virginia\">Battle of Vienna, Virginia</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Vienna, Virginia", "link": "https://wikipedia.org/wiki/Battle_of_Vienna,_Virginia"}]}, {"year": "1863", "text": "American Civil War: Battle of Aldie in the Gettysburg Campaign.", "html": "1863 - American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Aldie\" title=\"Battle of Aldie\">Battle of Aldie</a> in the <a href=\"https://wikipedia.org/wiki/Gettysburg_Campaign\" class=\"mw-redirect\" title=\"Gettysburg Campaign\">Gettysburg Campaign</a>.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Battle_of_Aldie\" title=\"Battle of Aldie\">Battle of Aldie</a> in the <a href=\"https://wikipedia.org/wiki/Gettysburg_Campaign\" class=\"mw-redirect\" title=\"Gettysburg Campaign\">Gettysburg Campaign</a>.", "links": [{"title": "Battle of Aldie", "link": "https://wikipedia.org/wiki/Battle_of_Aldie"}, {"title": "Gettysburg Campaign", "link": "https://wikipedia.org/wiki/Gettysburg_Campaign"}]}, {"year": "1876", "text": "American Indian Wars: Battle of the Rosebud: One thousand five hundred Sioux and Cheyenne led by <PERSON> Horse beat back General <PERSON>'s forces at Rosebud Creek in Montana Territory.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Rosebud\" title=\"Battle of the Rosebud\">Battle of the Rosebud</a>: One thousand five hundred <a href=\"https://wikipedia.org/wiki/Sioux\" title=\"Sioux\">Sioux</a> and <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> led by <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> beat back <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces at Rosebud Creek in <a href=\"https://wikipedia.org/wiki/Montana_Territory\" title=\"Montana Territory\">Montana Territory</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Indian_Wars\" title=\"American Indian Wars\">American Indian Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Rosebud\" title=\"Battle of the Rosebud\">Battle of the Rosebud</a>: One thousand five hundred <a href=\"https://wikipedia.org/wiki/Sioux\" title=\"Sioux\">Sioux</a> and <a href=\"https://wikipedia.org/wiki/Cheyenne\" title=\"Cheyenne\">Cheyenne</a> led by <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> beat back <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">General</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces at Rosebud Creek in <a href=\"https://wikipedia.org/wiki/Montana_Territory\" title=\"Montana Territory\">Montana Territory</a>.", "links": [{"title": "American Indian Wars", "link": "https://wikipedia.org/wiki/American_Indian_Wars"}, {"title": "Battle of the Rosebud", "link": "https://wikipedia.org/wiki/Battle_of_the_Rosebud"}, {"title": "Sioux", "link": "https://wikipedia.org/wiki/Sioux"}, {"title": "Cheyenne", "link": "https://wikipedia.org/wiki/Cheyenne"}, {"title": "Crazy Horse", "link": "https://wikipedia.org/wiki/Crazy_Horse"}, {"title": "General officer", "link": "https://wikipedia.org/wiki/General_officer"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Montana Territory", "link": "https://wikipedia.org/wiki/Montana_Territory"}]}, {"year": "1877", "text": "American Indian Wars: Battle of White Bird Canyon: The Nez Perce defeat the U.S. Cavalry at White Bird Canyon in the Idaho Territory.", "html": "1877 - American Indian Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_White_Bird_Canyon\" title=\"Battle of White Bird Canyon\">Battle of White Bird Canyon</a>: The <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">N<PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/United_States_Cavalry\" title=\"United States Cavalry\">U.S. Cavalry</a> at White Bird Canyon in the <a href=\"https://wikipedia.org/wiki/Idaho_Territory\" title=\"Idaho Territory\">Idaho Territory</a>.", "no_year_html": "American Indian Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_White_Bird_Canyon\" title=\"Battle of White Bird Canyon\">Battle of White Bird Canyon</a>: The <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">N<PERSON></a> defeat the <a href=\"https://wikipedia.org/wiki/United_States_Cavalry\" title=\"United States Cavalry\">U.S. Cavalry</a> at White Bird Canyon in the <a href=\"https://wikipedia.org/wiki/Idaho_Territory\" title=\"Idaho Territory\">Idaho Territory</a>.", "links": [{"title": "Battle of White Bird Canyon", "link": "https://wikipedia.org/wiki/Battle_of_White_Bird_Canyon"}, {"title": "Nez Perce people", "link": "https://wikipedia.org/wiki/Nez_Perce_people"}, {"title": "United States Cavalry", "link": "https://wikipedia.org/wiki/United_States_Cavalry"}, {"title": "Idaho Territory", "link": "https://wikipedia.org/wiki/Idaho_Territory"}]}, {"year": "1885", "text": "The Statue of Liberty arrives in New York Harbor.", "html": "1885 - The <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a> arrives in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Statue_of_Liberty\" title=\"Statue of Liberty\">Statue of Liberty</a> arrives in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a>.", "links": [{"title": "Statue of Liberty", "link": "https://wikipedia.org/wiki/Statue_of_Liberty"}, {"title": "New York Harbor", "link": "https://wikipedia.org/wiki/New_York_Harbor"}]}, {"year": "1898", "text": "The United States Navy Hospital Corps is established.", "html": "1898 - The <a href=\"https://wikipedia.org/wiki/Hospital_Corpsman\" class=\"mw-redirect\" title=\"Hospital Corpsman\">United States Navy Hospital Corps</a> is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hospital_Corpsman\" class=\"mw-redirect\" title=\"Hospital Corpsman\">United States Navy Hospital Corps</a> is established.", "links": [{"title": "Hospital Corpsman", "link": "https://wikipedia.org/wiki/Hospital_Corpsman"}]}, {"year": "1900", "text": "Boxer Rebellion: Western Allied and Japanese forces capture the Taku Forts in Tianjin, China.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Eight-Nation_Alliance\" title=\"Eight-Nation Alliance\">Western Allied and Japanese</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Taku_Forts_(1900)\" class=\"mw-redirect\" title=\"Battle of Taku Forts (1900)\">capture</a> the <a href=\"https://wikipedia.org/wiki/Taku_Forts\" title=\"Taku Forts\">Taku Forts</a> in <a href=\"https://wikipedia.org/wiki/Tianjin\" title=\"Tianjin\">Tianjin</a>, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boxer_Rebellion\" title=\"Boxer Rebellion\">Boxer Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Eight-Nation_Alliance\" title=\"Eight-Nation Alliance\">Western Allied and Japanese</a> forces <a href=\"https://wikipedia.org/wiki/Battle_of_Taku_Forts_(1900)\" class=\"mw-redirect\" title=\"Battle of Taku Forts (1900)\">capture</a> the <a href=\"https://wikipedia.org/wiki/Taku_Forts\" title=\"Taku Forts\">Taku Forts</a> in <a href=\"https://wikipedia.org/wiki/Tianjin\" title=\"Tianjin\">Tianjin</a>, China.", "links": [{"title": "Boxer Rebellion", "link": "https://wikipedia.org/wiki/Boxer_Rebellion"}, {"title": "Eight-Nation Alliance", "link": "https://wikipedia.org/wiki/Eight-Nation_Alliance"}, {"title": "Battle of Taku Forts (1900)", "link": "https://wikipedia.org/wiki/Battle_of_Taku_Forts_(1900)"}, {"title": "Taku Forts", "link": "https://wikipedia.org/wiki/Taku_Forts"}, {"title": "Tianjin", "link": "https://wikipedia.org/wiki/Tianjin"}]}, {"year": "1901", "text": "The College Board introduces its first standardized test, the forerunner to the SAT.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/College_Board\" title=\"College Board\">College Board</a> introduces its first standardized test, the forerunner to the <a href=\"https://wikipedia.org/wiki/SAT\" title=\"SAT\">SAT</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/College_Board\" title=\"College Board\">College Board</a> introduces its first standardized test, the forerunner to the <a href=\"https://wikipedia.org/wiki/SAT\" title=\"SAT\">SAT</a>.", "links": [{"title": "College Board", "link": "https://wikipedia.org/wiki/College_Board"}, {"title": "SAT", "link": "https://wikipedia.org/wiki/SAT"}]}, {"year": "1910", "text": "<PERSON><PERSON> pilots an <PERSON><PERSON> nr. 1 on its first flight.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> pilots an <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>_nr._1\" class=\"mw-redirect\" title=\"<PERSON><PERSON> nr. 1\"><PERSON><PERSON> nr. 1</a> on its first flight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Au<PERSON>\"><PERSON><PERSON></a> pilots an <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_nr._1\" class=\"mw-redirect\" title=\"<PERSON><PERSON> nr. 1\"><PERSON><PERSON> nr. 1</a> on its first flight.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> nr. 1", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_nr._1"}]}, {"year": "1922", "text": "Portuguese naval aviators <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> complete the first aerial crossing of the South Atlantic.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Portuguese_Naval_Aviation\" title=\"Portuguese Naval Aviation\">Portuguese naval aviators</a> <a href=\"https://wikipedia.org/wiki/Gago_Couti<PERSON>o\" title=\"Gago Coutinho\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sacadura_Cabral\" title=\"Sacadura Cabral\">Sacadura Cabral</a> complete the <a href=\"https://wikipedia.org/wiki/First_aerial_crossing_of_the_South_Atlantic\" title=\"First aerial crossing of the South Atlantic\">first aerial crossing of the South Atlantic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portuguese_Naval_Aviation\" title=\"Portuguese Naval Aviation\">Portuguese naval aviators</a> <a href=\"https://wikipedia.org/wiki/Gago_Coutinho\" title=\"Gago Coutinho\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Sacadura_Cabral\" title=\"Sacadura Cabral\">Sacadura Cabral</a> complete the <a href=\"https://wikipedia.org/wiki/First_aerial_crossing_of_the_South_Atlantic\" title=\"First aerial crossing of the South Atlantic\">first aerial crossing of the South Atlantic</a>.", "links": [{"title": "Portuguese Naval Aviation", "link": "https://wikipedia.org/wiki/Portuguese_Naval_Aviation"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gago_Couti<PERSON>o"}, {"title": "Sacadura Cabral", "link": "https://wikipedia.org/wiki/Sacadura_Cabral"}, {"title": "First aerial crossing of the South Atlantic", "link": "https://wikipedia.org/wiki/First_aerial_crossing_of_the_South_Atlantic"}]}, {"year": "1929", "text": "The town of Murchison, New Zealand is rocked by a 7.8 magnitude earthquake killing 17. At the time it was New Zealand's worst natural disaster.", "html": "1929 - The town of <a href=\"https://wikipedia.org/wiki/Murchison,_New_Zealand\" title=\"Murchison, New Zealand\">Murchison, New Zealand</a> is rocked by a <a href=\"https://wikipedia.org/wiki/1929_Murchison_earthquake\" title=\"1929 Murchison earthquake\">7.8 magnitude earthquake</a> killing 17. At the time it was New Zealand's worst natural disaster.", "no_year_html": "The town of <a href=\"https://wikipedia.org/wiki/Murchison,_New_Zealand\" title=\"Murchison, New Zealand\">Murchison, New Zealand</a> is rocked by a <a href=\"https://wikipedia.org/wiki/1929_Murchison_earthquake\" title=\"1929 Murchison earthquake\">7.8 magnitude earthquake</a> killing 17. At the time it was New Zealand's worst natural disaster.", "links": [{"title": "Murchison, New Zealand", "link": "https://wikipedia.org/wiki/Murchison,_New_Zealand"}, {"title": "1929 Murchison earthquake", "link": "https://wikipedia.org/wiki/1929_Murchison_earthquake"}]}, {"year": "1930", "text": "U.S. President <PERSON> signs the Smoot-Hawley Tariff Act into law.", "html": "1930 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Smoot%E2%80%93Hawley_Tariff_Act\" title=\"Smoot-Hawley Tariff Act\">Smoot-Hawley Tariff Act</a> into law.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Smoot%E2%80%93Hawley_Tariff_Act\" title=\"Smoot-Hawley Tariff Act\">Smoot-Hawley Tariff Act</a> into law.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Smoot-Hawley Tariff Act", "link": "https://wikipedia.org/wiki/Smoot%E2%80%93Hawley_Tariff_Act"}]}, {"year": "1932", "text": "Bonus Army: Around a thousand World War I veterans amass at the United States Capitol as the U.S. Senate considers a bill that would give them certain benefits.", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>: Around a thousand World War I veterans amass at the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> as the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> considers a bill that would give them certain benefits.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonus_Army\" title=\"Bonus Army\">Bonus Army</a>: Around a thousand World War I veterans amass at the <a href=\"https://wikipedia.org/wiki/United_States_Capitol\" title=\"United States Capitol\">United States Capitol</a> as the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senate</a> considers a bill that would give them certain benefits.", "links": [{"title": "Bonus Army", "link": "https://wikipedia.org/wiki/Bonus_Army"}, {"title": "United States Capitol", "link": "https://wikipedia.org/wiki/United_States_Capitol"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}]}, {"year": "1933", "text": "Union Station massacre: In Kansas City, Missouri, four FBI agents and captured fugitive <PERSON> are gunned down by gangsters attempting to free <PERSON>.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Kansas_City_massacre\" title=\"Kansas City massacre\">Union Station massacre</a>: In <a href=\"https://wikipedia.org/wiki/Kansas_City,_Missouri\" title=\"Kansas City, Missouri\">Kansas City, Missouri</a>, four <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agents and captured fugitive <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are gunned down by gangsters attempting to free <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kansas_City_massacre\" title=\"Kansas City massacre\">Union Station massacre</a>: In <a href=\"https://wikipedia.org/wiki/Kansas_City,_Missouri\" title=\"Kansas City, Missouri\">Kansas City, Missouri</a>, four <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a> agents and captured fugitive <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> are gunned down by gangsters attempting to free <PERSON>.", "links": [{"title": "Kansas City massacre", "link": "https://wikipedia.org/wiki/Kansas_City_massacre"}, {"title": "Kansas City, Missouri", "link": "https://wikipedia.org/wiki/Kansas_City,_Missouri"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "Last public guillotining in France: <PERSON><PERSON>, a convicted murderer, is executed in Versailles outside the Saint-Pierre prison.", "html": "1939 - Last public guillotining in France: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a convicted murderer, is executed in <a href=\"https://wikipedia.org/wiki/Versailles_(city)\" class=\"mw-redirect\" title=\"Versailles (city)\">Versailles</a> outside the Saint-Pierre prison.", "no_year_html": "Last public guillotining in France: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a convicted murderer, is executed in <a href=\"https://wikipedia.org/wiki/Versailles_(city)\" class=\"mw-redirect\" title=\"Versailles (city)\">Versailles</a> outside the Saint-Pierre prison.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Versailles (city)", "link": "https://wikipedia.org/wiki/Versailles_(city)"}]}, {"year": "1940", "text": "World War II: RMS Lancastria is attacked and sunk by the Luftwaffe near Saint-Nazaire, France. At least 3,000 are killed in Britain's worst maritime disaster.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/RMS_Lancastria\" title=\"RMS Lancastria\">RMS <i>Lancastria</i></a> is attacked and sunk by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> near <a href=\"https://wikipedia.org/wiki/Saint-Nazaire\" title=\"Saint-Nazaire\">Saint-Nazaire</a>, France. At least 3,000 are killed in Britain's worst maritime disaster.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/RMS_Lancastria\" title=\"RMS Lancastria\">RMS <i>Lancastria</i></a> is attacked and sunk by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> near <a href=\"https://wikipedia.org/wiki/Saint-Nazaire\" title=\"Saint-Nazaire\">Saint-Nazaire</a>, France. At least 3,000 are killed in Britain's worst maritime disaster.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "RMS Lancastria", "link": "https://wikipedia.org/wiki/RMS_Lancastria"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "Saint-Na<PERSON>re", "link": "https://wikipedia.org/wiki/Saint-Nazaire"}]}, {"year": "1940", "text": "World War II: The British Army's 11th Hussars assault and take Fort Capuzzo in Libya, Africa from Italian forces.", "html": "1940 - World War II: The British Army's <a href=\"https://wikipedia.org/wiki/11th_Hussars\" title=\"11th Hussars\">11th Hussars</a> assault and take <a href=\"https://wikipedia.org/wiki/Fort_Capuzzo\" title=\"Fort Capuzzo\">Fort Capuzzo</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, Africa from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> forces.", "no_year_html": "World War II: The British Army's <a href=\"https://wikipedia.org/wiki/11th_Hussars\" title=\"11th Hussars\">11th Hussars</a> assault and take <a href=\"https://wikipedia.org/wiki/Fort_Capuzzo\" title=\"Fort Capuzzo\">Fort Capuzzo</a> in <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>, Africa from <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> forces.", "links": [{"title": "11th Hussars", "link": "https://wikipedia.org/wiki/11th_Hussars"}, {"title": "Fort Capuzzo", "link": "https://wikipedia.org/wiki/Fort_Capuzzo"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}, {"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}]}, {"year": "1940", "text": "The three Baltic states of Estonia, Latvia and Lithuania fall under the occupation of the Soviet Union.", "html": "1940 - The three <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> fall under the <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_the_Baltic_states_(1940)\" title=\"Soviet occupation of the Baltic states (1940)\">occupation</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "The three <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a> fall under the <a href=\"https://wikipedia.org/wiki/Soviet_occupation_of_the_Baltic_states_(1940)\" title=\"Soviet occupation of the Baltic states (1940)\">occupation</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Baltic states", "link": "https://wikipedia.org/wiki/Baltic_states"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Soviet occupation of the Baltic states (1940)", "link": "https://wikipedia.org/wiki/Soviet_occupation_of_the_Baltic_states_(1940)"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1944", "text": "Iceland declares independence from Denmark and becomes a republic.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> declares <a href=\"https://wikipedia.org/wiki/Icelandic_National_Day\" title=\"Icelandic National Day\">independence</a> from Denmark and becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> declares <a href=\"https://wikipedia.org/wiki/Icelandic_National_Day\" title=\"Icelandic National Day\">independence</a> from Denmark and becomes a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a>.", "links": [{"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "Icelandic National Day", "link": "https://wikipedia.org/wiki/Icelandic_National_Day"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}]}, {"year": "1948", "text": "United Airlines Flight 624, a Douglas DC-6, crashes near Mount Carmel, Pennsylvania, killing all 43 people on board.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_624\" class=\"mw-redirect\" title=\"United Airlines Flight 624\">United Airlines Flight 624</a>, a <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a>, crashes near <a href=\"https://wikipedia.org/wiki/Mount_Carmel,_Pennsylvania\" title=\"Mount Carmel, Pennsylvania\">Mount Carmel, Pennsylvania</a>, killing all 43 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Airlines_Flight_624\" class=\"mw-redirect\" title=\"United Airlines Flight 624\">United Airlines Flight 624</a>, a <a href=\"https://wikipedia.org/wiki/Douglas_DC-6\" title=\"Douglas DC-6\">Douglas DC-6</a>, crashes near <a href=\"https://wikipedia.org/wiki/Mount_Carmel,_Pennsylvania\" title=\"Mount Carmel, Pennsylvania\">Mount Carmel, Pennsylvania</a>, killing all 43 people on board.", "links": [{"title": "United Airlines Flight 624", "link": "https://wikipedia.org/wiki/United_Airlines_Flight_624"}, {"title": "Douglas DC-6", "link": "https://wikipedia.org/wiki/Douglas_DC-6"}, {"title": "Mount Carmel, Pennsylvania", "link": "https://wikipedia.org/wiki/Mount_Carmel,_Pennsylvania"}]}, {"year": "1952", "text": "Guatemala passes Decree 900, ordering the redistribution of uncultivated land.", "html": "1952 - Guatemala passes <a href=\"https://wikipedia.org/wiki/Decree_900\" title=\"Decree 900\">Decree 900</a>, ordering the redistribution of uncultivated land.", "no_year_html": "Guatemala passes <a href=\"https://wikipedia.org/wiki/Decree_900\" title=\"Decree 900\">Decree 900</a>, ordering the redistribution of uncultivated land.", "links": [{"title": "Decree 900", "link": "https://wikipedia.org/wiki/Decree_900"}]}, {"year": "1953", "text": "Cold War: East Germany Workers Uprising: In East Germany, the Soviet Union orders a division of troops into East Berlin to quell a rebellion.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Uprising_of_1953_in_East_Germany\" class=\"mw-redirect\" title=\"Uprising of 1953 in East Germany\">East Germany Workers Uprising</a>: In <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, the Soviet Union orders a division of troops into <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a> to quell a rebellion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/Uprising_of_1953_in_East_Germany\" class=\"mw-redirect\" title=\"Uprising of 1953 in East Germany\">East Germany Workers Uprising</a>: In <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>, the Soviet Union orders a division of troops into <a href=\"https://wikipedia.org/wiki/East_Berlin\" title=\"East Berlin\">East Berlin</a> to quell a rebellion.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Uprising of 1953 in East Germany", "link": "https://wikipedia.org/wiki/Uprising_of_1953_in_East_Germany"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "East Berlin", "link": "https://wikipedia.org/wiki/East_Berlin"}]}, {"year": "1958", "text": "The Ironworkers Memorial Second Narrows Crossing, in the process of being built to connect Vancouver and North Vancouver (Canada), collapses into the Burrard Inlet killing 18 ironworkers and injuring others.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Ironworkers_Memorial_Second_Narrows_Crossing\" title=\"Ironworkers Memorial Second Narrows Crossing\">Ironworkers Memorial Second Narrows Crossing</a>, in the process of being built to connect Vancouver and <a href=\"https://wikipedia.org/wiki/North_Vancouver_(district_municipality)\" title=\"North Vancouver (district municipality)\">North Vancouver</a> (Canada), collapses into the <a href=\"https://wikipedia.org/wiki/Burrard_Inlet\" title=\"Burrard Inlet\">Burrard Inlet</a> killing 18 ironworkers and injuring others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ironworkers_Memorial_Second_Narrows_Crossing\" title=\"Ironworkers Memorial Second Narrows Crossing\">Ironworkers Memorial Second Narrows Crossing</a>, in the process of being built to connect Vancouver and <a href=\"https://wikipedia.org/wiki/North_Vancouver_(district_municipality)\" title=\"North Vancouver (district municipality)\">North Vancouver</a> (Canada), collapses into the <a href=\"https://wikipedia.org/wiki/Burrard_Inlet\" title=\"Burrard Inlet\">Burrard Inlet</a> killing 18 ironworkers and injuring others.", "links": [{"title": "Ironworkers Memorial Second Narrows Crossing", "link": "https://wikipedia.org/wiki/Ironworkers_Memorial_Second_Narrows_Crossing"}, {"title": "North Vancouver (district municipality)", "link": "https://wikipedia.org/wiki/North_Vancouver_(district_municipality)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ard_Inlet"}]}, {"year": "1960", "text": "The Nez Perce tribe is awarded $4 million for 7 million acres (28,000 km2) of land undervalued at four cents/acre in the 1863 treaty.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">Nez <PERSON>ce</a> tribe is awarded $4 million for 7 million acres (28,000 km) of land undervalued at four cents/acre in the 1863 treaty.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nez_Perce_people\" class=\"mw-redirect\" title=\"Nez Perce people\">Nez <PERSON>ce</a> tribe is awarded $4 million for 7 million acres (28,000 km) of land undervalued at four cents/acre in the 1863 treaty.", "links": [{"title": "Nez Perce people", "link": "https://wikipedia.org/wiki/Nez_Perce_people"}]}, {"year": "1963", "text": "The United States Supreme Court rules 8-1 in Abington School District v. <PERSON> against requiring the reciting of Bible verses and the Lord's Prayer in public schools.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules 8-1 in <i><a href=\"https://wikipedia.org/wiki/Abington_School_District_v._Schempp\" title=\"Abington School District v. <PERSON>\">Abington School District v. Sc<PERSON>pp</a></i> against requiring the reciting of <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a> verses and the <a href=\"https://wikipedia.org/wiki/Lord%27s_Prayer\" title=\"Lord's Prayer\">Lord's Prayer</a> in <a href=\"https://wikipedia.org/wiki/Public_school_(government_funded)\" class=\"mw-redirect\" title=\"Public school (government funded)\">public schools</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a> rules 8-1 in <i><a href=\"https://wikipedia.org/wiki/Abington_School_District_v._Schempp\" title=\"Abington School District v. <PERSON>\">Abington School District v. Schempp</a></i> against requiring the reciting of <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a> verses and the <a href=\"https://wikipedia.org/wiki/Lord%27s_Prayer\" title=\"Lord's Prayer\">Lord's Prayer</a> in <a href=\"https://wikipedia.org/wiki/Public_school_(government_funded)\" class=\"mw-redirect\" title=\"Public school (government funded)\">public schools</a>.", "links": [{"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "Abington School District v. <PERSON><PERSON>pp", "link": "https://wikipedia.org/wiki/Abington_School_District_v._<PERSON><PERSON><PERSON>"}, {"title": "Bible", "link": "https://wikipedia.org/wiki/Bible"}, {"title": "Lord's Prayer", "link": "https://wikipedia.org/wiki/Lord%27s_Prayer"}, {"title": "Public school (government funded)", "link": "https://wikipedia.org/wiki/Public_school_(government_funded)"}]}, {"year": "1963", "text": "A day after South Vietnamese President <PERSON><PERSON> announced the Joint Communiqué to end the Buddhist crisis, a riot involving around 2,000 people breaks out. One person is killed.", "html": "1963 - A day after <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">South Vietnamese President</a> <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"Ngô <PERSON>\">Ng<PERSON></a> announced the <a href=\"https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_(Vietnam)\" title=\"Joint Communiqué (Vietnam)\">Joint Communiqué</a> to end the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>, a riot involving around 2,000 people breaks out. One person is killed.", "no_year_html": "A day after <a href=\"https://wikipedia.org/wiki/Leaders_of_South_Vietnam\" class=\"mw-redirect\" title=\"Leaders of South Vietnam\">South Vietnamese President</a> <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"Ngô <PERSON>\">Ng<PERSON></a> announced the <a href=\"https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_(Vietnam)\" title=\"Joint Communiqué (Vietnam)\">Joint Communiqué</a> to end the <a href=\"https://wikipedia.org/wiki/Buddhist_crisis\" title=\"Buddhist crisis\">Buddhist crisis</a>, a riot involving around 2,000 people breaks out. One person is killed.", "links": [{"title": "Leaders of South Vietnam", "link": "https://wikipedia.org/wiki/Leaders_of_South_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m"}, {"title": "Joint Communiqué (Vietnam)", "link": "https://wikipedia.org/wiki/Joint_Communiqu%C3%A9_(Vietnam)"}, {"title": "Buddhist crisis", "link": "https://wikipedia.org/wiki/Buddhist_crisis"}]}, {"year": "1967", "text": "Nuclear weapons testing: China announces a successful test of its first thermonuclear weapon.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> announces a <a href=\"https://wikipedia.org/wiki/Test_No._6\" title=\"Test No. 6\">successful test</a> of its first <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">thermonuclear weapon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> announces a <a href=\"https://wikipedia.org/wiki/Test_No._6\" title=\"Test No. 6\">successful test</a> of its first <a href=\"https://wikipedia.org/wiki/Thermonuclear_weapon\" title=\"Thermonuclear weapon\">thermonuclear weapon</a>.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Test No. 6", "link": "https://wikipedia.org/wiki/Test_No._6"}, {"title": "Thermonuclear weapon", "link": "https://wikipedia.org/wiki/Thermonuclear_weapon"}]}, {"year": "1971", "text": "U.S. President <PERSON> in a televised press conference called drug abuse \"America's public enemy number one\", starting the War on drugs.", "html": "1971 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a televised press conference called <a href=\"https://wikipedia.org/wiki/Substance_abuse\" title=\"Substance abuse\">drug abuse</a> \"America's public enemy number one\", starting the <a href=\"https://wikipedia.org/wiki/War_on_drugs\" title=\"War on drugs\">War on drugs</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a televised press conference called <a href=\"https://wikipedia.org/wiki/Substance_abuse\" title=\"Substance abuse\">drug abuse</a> \"America's public enemy number one\", starting the <a href=\"https://wikipedia.org/wiki/War_on_drugs\" title=\"War on drugs\">War on drugs</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Substance abuse", "link": "https://wikipedia.org/wiki/Substance_abuse"}, {"title": "War on drugs", "link": "https://wikipedia.org/wiki/War_on_drugs"}]}, {"year": "1972", "text": "Watergate scandal: Five White House operatives are arrested for burgling the offices of the Democratic National Committee during an attempt by members of the administration of President <PERSON> to illegally wiretap the political opposition as part of a broader campaign to subvert the democratic process.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Five <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> operatives are arrested for burgling the offices of the <a href=\"https://wikipedia.org/wiki/Democratic_National_Committee\" title=\"Democratic National Committee\">Democratic National Committee</a> during an attempt by members of the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">administration of President <PERSON></a> to illegally <a href=\"https://wikipedia.org/wiki/Telephone_tapping\" class=\"mw-redirect\" title=\"Telephone tapping\">wiretap</a> the political opposition as part of a <a href=\"https://wikipedia.org/wiki/Operation_Sandwedge\" title=\"Operation Sandwedge\">broader campaign to subvert the democratic process</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: Five <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> operatives are arrested for burgling the offices of the <a href=\"https://wikipedia.org/wiki/Democratic_National_Committee\" title=\"Democratic National Committee\">Democratic National Committee</a> during an attempt by members of the <a href=\"https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>\" title=\"Presidency of <PERSON>\">administration of President <PERSON></a> to illegally <a href=\"https://wikipedia.org/wiki/Telephone_tapping\" class=\"mw-redirect\" title=\"Telephone tapping\">wiretap</a> the political opposition as part of a <a href=\"https://wikipedia.org/wiki/Operation_Sandwedge\" title=\"Operation Sandwedge\">broader campaign to subvert the democratic process</a>.", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "Democratic National Committee", "link": "https://wikipedia.org/wiki/Democratic_National_Committee"}, {"title": "Presidency of <PERSON>", "link": "https://wikipedia.org/wiki/Presidency_of_<PERSON>_<PERSON>"}, {"title": "Telephone tapping", "link": "https://wikipedia.org/wiki/Telephone_tapping"}, {"title": "Operation Sandwedge", "link": "https://wikipedia.org/wiki/Operation_Sandwedge"}]}, {"year": "1985", "text": "Space Shuttle program: STS-51-G mission: Space Shuttle Discovery launches carrying <PERSON> bin <PERSON> bin <PERSON><PERSON>, the first Arab and first Muslim in space, as a payload specialist.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-51-G\" title=\"STS-51-G\">STS-51-G</a> mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches carrying <a href=\"https://wikipedia.org/wiki/Sultan_bin_Salman_bin_Abdulaziz_Al_Saud\" class=\"mw-redirect\" title=\"Sultan bin Salman bin <PERSON> Saud\"><PERSON> bin Salman bin <PERSON></a>, the first Arab and first Muslim in space, as a <a href=\"https://wikipedia.org/wiki/Payload_specialist\" title=\"Payload specialist\">payload specialist</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/STS-51-G\" title=\"STS-51-G\">STS-51-G</a> mission: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches carrying <a href=\"https://wikipedia.org/wiki/Sultan_bin_Salman_bin_Abdulaziz_Al_Saud\" class=\"mw-redirect\" title=\"Sultan bin Salman bin Abdulaziz Al Saud\"><PERSON> bin Salman bin <PERSON>ud</a>, the first Arab and first Muslim in space, as a <a href=\"https://wikipedia.org/wiki/Payload_specialist\" title=\"Payload specialist\">payload specialist</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "STS-51-G", "link": "https://wikipedia.org/wiki/STS-51-G"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "<PERSON> bin <PERSON><PERSON> bin <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_bin_<PERSON><PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_Saud"}, {"title": "Payload specialist", "link": "https://wikipedia.org/wiki/Payload_specialist"}]}, {"year": "1987", "text": "With the death of the last individual of the species, the dusky seaside sparrow becomes extinct.", "html": "1987 - With the death of the last individual of the species, the <a href=\"https://wikipedia.org/wiki/Dusky_seaside_sparrow\" title=\"Dusky seaside sparrow\">dusky seaside sparrow</a> becomes extinct.", "no_year_html": "With the death of the last individual of the species, the <a href=\"https://wikipedia.org/wiki/Dusky_seaside_sparrow\" title=\"Dusky seaside sparrow\">dusky seaside sparrow</a> becomes extinct.", "links": [{"title": "Dusky seaside sparrow", "link": "https://wikipedia.org/wiki/Dusky_seaside_sparrow"}]}, {"year": "1989", "text": "Interflug Flight 102 crashes during a rejected takeoff from Berlin Schönefeld Airport, killing 21 people.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Interflug_Flight_102\" title=\"Interflug Flight 102\">Interflug Flight 102</a> crashes during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from <a href=\"https://wikipedia.org/wiki/Berlin_Sch%C3%B6nefeld_Airport\" title=\"Berlin Schönefeld Airport\">Berlin Schönefeld Airport</a>, killing 21 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Interflug_Flight_102\" title=\"Interflug Flight 102\">Interflug Flight 102</a> crashes during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from <a href=\"https://wikipedia.org/wiki/Berlin_Sch%C3%B6nefeld_Airport\" title=\"Berlin Schönefeld Airport\">Berlin Schönefeld Airport</a>, killing 21 people.", "links": [{"title": "Interflug Flight 102", "link": "https://wikipedia.org/wiki/Interflug_Flight_102"}, {"title": "Rejected takeoff", "link": "https://wikipedia.org/wiki/Rejected_takeoff"}, {"title": "Berlin Schönefeld Airport", "link": "https://wikipedia.org/wiki/Berlin_Sch%C3%B6nefeld_Airport"}]}, {"year": "1991", "text": "Apartheid: The South African Parliament repeals the Population Registration Act which required racial classification of all South Africans at birth.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_South_Africa\" title=\"Parliament of South Africa\">South African Parliament</a> repeals the <a href=\"https://wikipedia.org/wiki/Population_Registration_Act\" class=\"mw-redirect\" title=\"Population Registration Act\">Population Registration Act</a> which required racial classification of all South Africans at birth.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_South_Africa\" title=\"Parliament of South Africa\">South African Parliament</a> repeals the <a href=\"https://wikipedia.org/wiki/Population_Registration_Act\" class=\"mw-redirect\" title=\"Population Registration Act\">Population Registration Act</a> which required racial classification of all South Africans at birth.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "Parliament of South Africa", "link": "https://wikipedia.org/wiki/Parliament_of_South_Africa"}, {"title": "Population Registration Act", "link": "https://wikipedia.org/wiki/Population_Registration_Act"}]}, {"year": "1992", "text": "A \"joint understanding\" agreement on arms reduction is signed by U.S. President <PERSON> and Russian President <PERSON> (this would be later codified in START II).", "html": "1992 - A \"joint understanding\" agreement on arms reduction is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">Russian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (this would be later codified in <a href=\"https://wikipedia.org/wiki/START_II\" title=\"START II\">START II</a>).", "no_year_html": "A \"joint understanding\" agreement on arms reduction is signed by U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">Russian President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (this would be later codified in <a href=\"https://wikipedia.org/wiki/START_II\" title=\"START II\">START II</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "START II", "link": "https://wikipedia.org/wiki/START_II"}]}, {"year": "1994", "text": "Following a televised low-speed highway chase, <PERSON><PERSON> <PERSON><PERSON> is arrested for the murders of his ex-wife, <PERSON>, and her friend <PERSON>.", "html": "1994 - Following a televised low-speed highway chase, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is arrested for the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_murder_case\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> murder case\">murders</a> of his ex-wife, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and her friend <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Following a televised low-speed highway chase, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> is arrested for the <a href=\"https://wikipedia.org/wiki/O<PERSON>_<PERSON><PERSON>_<PERSON>_murder_case\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> murder case\">murders</a> of his ex-wife, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, and her friend <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON> murder case", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_murder_case"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "Nine people are killed in a mass shooting at Emanuel African Methodist Episcopal Church in Charleston, South Carolina.", "html": "2015 - Nine people are killed in <a href=\"https://wikipedia.org/wiki/Charleston_church_shooting\" title=\"Charleston church shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Emanuel_African_Methodist_Episcopal_Church\" title=\"Emanuel African Methodist Episcopal Church\">Emanuel African Methodist Episcopal Church</a> in <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>.", "no_year_html": "Nine people are killed in <a href=\"https://wikipedia.org/wiki/Charleston_church_shooting\" title=\"Charleston church shooting\">a mass shooting</a> at <a href=\"https://wikipedia.org/wiki/Emanuel_African_Methodist_Episcopal_Church\" title=\"Emanuel African Methodist Episcopal Church\">Emanuel African Methodist Episcopal Church</a> in <a href=\"https://wikipedia.org/wiki/Charleston,_South_Carolina\" title=\"Charleston, South Carolina\">Charleston, South Carolina</a>.", "links": [{"title": "Charleston church shooting", "link": "https://wikipedia.org/wiki/Charleston_church_shooting"}, {"title": "Emanuel African Methodist Episcopal Church", "link": "https://wikipedia.org/wiki/Emanuel_African_Methodist_Episcopal_Church"}, {"title": "Charleston, South Carolina", "link": "https://wikipedia.org/wiki/Charleston,_South_Carolina"}]}, {"year": "2017", "text": "A series of wildfires in central Portugal kill at least 64 people and injure 204 others.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/June_2017_Portugal_wildfires\" title=\"June 2017 Portugal wildfires\">series of wildfires</a> in central <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> kill at least 64 people and injure 204 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/June_2017_Portugal_wildfires\" title=\"June 2017 Portugal wildfires\">series of wildfires</a> in central <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a> kill at least 64 people and injure 204 others.", "links": [{"title": "June 2017 Portugal wildfires", "link": "https://wikipedia.org/wiki/June_2017_Portugal_wildfires"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "2021", "text": "Juneteenth National Independence Day, was signed into law by President <PERSON>, to become the first federal holiday established since Martin <PERSON> Jr. Day in 1983.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Juneteenth\" title=\"Juneteenth\">Juneteenth</a> National Independence Day, was signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, to become the first federal holiday established since <PERSON> Jr. Day in 1983.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juneteenth\" title=\"Juneteenth\">Juneteenth</a> National Independence Day, was signed into law by President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, to become the first federal holiday established since <PERSON> Jr. Day in 1983.", "links": [{"title": "Juneteenth", "link": "https://wikipedia.org/wiki/Juneteenth"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Births": [{"year": "801", "text": "<PERSON><PERSON> of Metz, Frankish bishop (d. 855)", "html": "801 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, <PERSON><PERSON> bishop (d. 855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz\" title=\"<PERSON><PERSON> of Metz\"><PERSON><PERSON> of Metz</a>, Frankish bishop (d. 855)", "links": [{"title": "<PERSON><PERSON> of Metz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Metz"}]}, {"year": "1239", "text": "<PERSON>, English king (d. 1307)", "html": "1239 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, English king (d. 1307)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON></a>, English king (d. 1307)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}]}, {"year": "1530", "text": "<PERSON>, French nobleman (d. 1579)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Montmorency\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French nobleman (d. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_de_Montmorency\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French nobleman (d. 1579)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_<PERSON>morency"}]}, {"year": "1571", "text": "<PERSON>, English writer on economics (d. 1641)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer on economics (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer on economics (d. 1641)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON> of Cupertino, Italian mystic and saint (d. 1663)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cupertino\" title=\"<PERSON> of Cupertino\"><PERSON> of Cupertino</a>, Italian mystic and saint (d. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cupert<PERSON>\" title=\"<PERSON> of Cupertino\"><PERSON> of Cupertino</a>, Italian mystic and saint (d. 1663)", "links": [{"title": "<PERSON> of Cupertino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cupert<PERSON>"}]}, {"year": "1604", "text": "<PERSON>, Dutch nobleman (d. 1679)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Siegen\" title=\"<PERSON>, Prince of Nassau-Siegen\"><PERSON></a>, Dutch nobleman (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Siegen\" title=\"<PERSON>, Prince of Nassau-Siegen\"><PERSON></a>, Dutch nobleman (d. 1679)", "links": [{"title": "<PERSON>, Prince of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Nassau-Siegen"}]}, {"year": "1610", "text": "<PERSON><PERSON><PERSON><PERSON>, Danish scholar, writer and translator (b. 1662)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Birgitte_Thott\" title=\"<PERSON><PERSON><PERSON><PERSON> Thott\"><PERSON><PERSON><PERSON><PERSON></a>, Danish scholar, writer and translator (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birgitte_Thott\" title=\"<PERSON><PERSON><PERSON><PERSON> Thott\"><PERSON><PERSON><PERSON><PERSON></a>, Danish scholar, writer and translator (b. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgit<PERSON>_<PERSON>hott"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON><PERSON>, Mughal princess (d. 1706)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal princess (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mughal princess (d. 1706)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>gum"}]}, {"year": "1682", "text": "<PERSON>, Swedish king (d. 1718)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON></a>, Swedish king (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON></a>, Swedish king (d. 1718)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1691", "text": "<PERSON>, Italian painter and architect (d. 1765)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and architect (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1693", "text": "<PERSON>, German theologian and author (d. 1775)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and author (d. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, English engineer, invented the Flying shuttle (d. 1780)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(flying_shuttle)\" title=\"<PERSON> (flying shuttle)\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Flying_shuttle\" title=\"Flying shuttle\">Flying shuttle</a> (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(flying_shuttle)\" title=\"<PERSON> (flying shuttle)\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Flying_shuttle\" title=\"Flying shuttle\">Flying shuttle</a> (d. 1780)", "links": [{"title": "<PERSON> (flying shuttle)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(flying_shuttle)"}, {"title": "Flying shuttle", "link": "https://wikipedia.org/wiki/Flying_shuttle"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON>, French astronomer and cartographer (d. 1784)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar-Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>_Thury\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and cartographer (d. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar-Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and cartographer (d. 1784)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar-Fran%C3%A<PERSON><PERSON>_<PERSON><PERSON>_de_Thury"}]}, {"year": "1718", "text": "<PERSON>, English field marshal and politician, Governor of Minorca (d. 1796)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Minorca\" class=\"mw-redirect\" title=\"Governor of Minorca\">Governor of Minorca</a> (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Minorca\" class=\"mw-redirect\" title=\"Governor of Minorca\">Governor of Minorca</a> (d. 1796)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Governor of Minorca", "link": "https://wikipedia.org/wiki/Governor_of_Minorca"}]}, {"year": "1778", "text": "<PERSON>, English-Australian explorer  (d. 1853)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian explorer (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian explorer (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, 3rd Earl of Rosse, English-Irish astronomer and politician (d. 1867)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Rosse\" title=\"<PERSON>, 3rd Earl of Rosse\"><PERSON>, 3rd Earl of Rosse</a>, English-Irish astronomer and politician (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Rosse\" title=\"<PERSON>, 3rd Earl of Rosse\"><PERSON>, 3rd Earl of Rosse</a>, English-Irish astronomer and politician (d. 1867)", "links": [{"title": "<PERSON>, 3rd Earl of Rosse", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Ross<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, Norwegian poet, playwright, and linguist (d. 1845)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian poet, playwright, and linguist (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian poet, playwright, and linguist (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, German poet and translator (d. 1876)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and translator (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON><PERSON>, Icelandic scholar and politician (d. 1879)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic scholar and politician (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic scholar and politician (d. 1879)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3n_Sigur%C3%B0sson"}]}, {"year": "1818", "text": "<PERSON>, French composer and academic (d. 1893)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and academic (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and academic (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1818", "text": "<PERSON> of Württemberg, queen of the Netherlands (d. 1877)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a>, queen of the Netherlands (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrttemberg\" title=\"<PERSON> of Württemberg\"><PERSON> of Württemberg</a>, queen of the Netherlands (d. 1877)", "links": [{"title": "<PERSON> of Württemberg", "link": "https://wikipedia.org/wiki/<PERSON>_of_W%C3%BCrttemberg"}]}, {"year": "1821", "text": "<PERSON><PERSON> <PERSON><PERSON>, American archaeologist and journalist (d. 1888)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American archaeologist and journalist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American archaeologist and journalist (d. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English chemist and physicist (d. 1919)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and physicist (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, Mexican general and president (d. 1893)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Flores"}]}, {"year": "1858", "text": "<PERSON><PERSON>, American businessman and politician, 44th Governor of Massachusetts (d. 1914)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1861", "text": "<PERSON>, American baseball player (d. 1905)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American general (d. 1940)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, duke of Mecklenburg (d. 1934)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON></a>, duke of Mecklenburg (d. 1934)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1865", "text": "<PERSON>, Native American physician (d. 1915)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Picott<PERSON>\"><PERSON></a>, Native American physician (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Picott<PERSON>\"><PERSON></a>, Native American physician (d. 1915)", "links": [{"title": "<PERSON> F<PERSON> Pi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1867", "text": "<PERSON>, English-American actress (d. 1940)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Irish-born American educator, publisher, and humanitarian (d. 1948)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born American educator, publisher, and humanitarian (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born American educator, publisher, and humanitarian (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Australian poet and author (d. 1922)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and author (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and author (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American author, journalist, and activist (d. 1938)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and activist (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, journalist, and activist (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American rower (d. 1942)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, American rower (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, American rower (d. 1942)", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>(rower)"}]}, {"year": "1876", "text": "<PERSON>, American anatomist and author (d. 1922)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anatomist and author (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anatomist and author (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American author and photographer (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, Canadian boxer and promoter (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_boxer)\" title=\"<PERSON> (Canadian boxer)\"><PERSON></a>, Canadian boxer and promoter (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_boxer)\" title=\"<PERSON> (Canadian boxer)\"><PERSON></a>, Canadian boxer and promoter (d. 1955)", "links": [{"title": "<PERSON> (Canadian boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_boxer)"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz (d. 1918)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz\"><PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz</a> (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Grand_Duke_of_Mecklenburg-Strelitz"}]}, {"year": "1882", "text": "<PERSON>, Russian pianist, composer, and conductor (d. 1971)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist, composer, and conductor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German general (d. 1954)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Brazilian girl, popular saint (d. 1911)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ld<PERSON>\" title=\"<PERSON><PERSON> I<PERSON>ld<PERSON>\"><PERSON></a>, Brazilian girl, <a href=\"https://wikipedia.org/wiki/Folk_saint\" title=\"Folk saint\">popular saint</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Brazilian girl, <a href=\"https://wikipedia.org/wiki/Folk_saint\" title=\"Folk saint\">popular saint</a> (d. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Folk saint", "link": "https://wikipedia.org/wiki/Folk_saint"}]}, {"year": "1898", "text": "<PERSON><PERSON> <PERSON><PERSON>, Dutch illustrator (d. 1972)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch illustrator (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch illustrator (d. 1972)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German physicist and academic (d. 1961)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Executed Irish republican (d. 1922)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republican", "link": "https://wikipedia.org/wiki/Irish_republican"}]}, {"year": "1898", "text": "<PERSON>, English soldier and firefighter (d. 2009)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and firefighter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and firefighter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German politician (d. 1945)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Scottish journalist and war correspondent (d. 2000) ", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and war correspondent (d. 2000) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and war correspondent (d. 2000) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American pianist and composer (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ain"}]}, {"year": "1902", "text": "<PERSON>, Australian cricketer (d. 1982)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American chef, created the chocolate chip cookie (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef, created the <a href=\"https://wikipedia.org/wiki/Chocolate_chip_cookie\" title=\"Chocolate chip cookie\">chocolate chip cookie</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef, created the <a href=\"https://wikipedia.org/wiki/Chocolate_chip_cookie\" title=\"Chocolate chip cookie\">chocolate chip cookie</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chocolate chip cookie", "link": "https://wikipedia.org/wiki/Chocolate_chip_cookie"}]}, {"year": "1904", "text": "<PERSON>, American actor (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ralph <PERSON>\"><PERSON></a>, American actor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON>, American pastor and theologian (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pastor and theologian (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pastor and theologian (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Canadian farmer and politician (d. 1989)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Canadian farmer and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Canadian farmer and politician (d. 1989)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(politician)"}]}, {"year": "1907", "text": "<PERSON>, French director, producer, and screenwriter (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director, producer, and screenwriter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American businessman and politician, 30th Governor of Minnesota (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a> (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "1909", "text": "<PERSON>, Canadian-American film editor (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American film editor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American film editor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1968)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Foley"}]}, {"year": "1910", "text": "<PERSON>, Canadian football player and politician (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player and politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American journalist and author (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, American singer and banjo player (d. 1973)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Stringbean%22_A<PERSON>man\" title='<PERSON> \"Stringbean\" Akeman'><PERSON> \"<PERSON>\" <PERSON></a>, American singer and banjo player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Stringbean%22_A<PERSON>man\" title='<PERSON> \"Stringbean\" Akeman'><PERSON> \"<PERSON>\" <PERSON></a>, American singer and banjo player (d. 1973)", "links": [{"title": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>man", "link": "https://wikipedia.org/wiki/<PERSON>_%22Stringbean%22_<PERSON><PERSON><PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Canadian civil servant and diplomat, Canadian Ambassador to the United States (d. 1981)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United States\">Canadian Ambassador to the United States</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian civil servant and diplomat, <a href=\"https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States\" class=\"mw-redirect\" title=\"Canadian Ambassador to the United States\">Canadian Ambassador to the United States</a> (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canadian Ambassador to the United States", "link": "https://wikipedia.org/wiki/Canadian_Ambassador_to_the_United_States"}]}, {"year": "1916", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Canadian politician, 14th Premier of Manitoba (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 14th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Manitoba", "link": "https://wikipedia.org/wiki/Premier_of_Manitoba"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Thai monk and educator (d. 1992)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai monk and educator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai monk and educator (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}]}, {"year": "1919", "text": "<PERSON>, American psychologist and academic (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Scottish lieutenant and pilot (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Scottish lieutenant and pilot (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Scottish lieutenant and pilot (d. 2016)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1919", "text": "<PERSON><PERSON>, English actress (d. 1996)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American lawyer and politician (d. 1981)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Japanese actress (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, French biologist and geneticist, Nobel Prize laureate (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French biologist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1920", "text": "<PERSON>, English air marshal and politician, Lieutenant Governor of Guernsey (d. 2018)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>ina<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ina<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English air marshal and politician, <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey\" title=\"Lieutenant Governor of Guernsey\">Lieutenant Governor of Guernsey</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>eminant"}, {"title": "Lieutenant Governor of Guernsey", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Guernsey"}]}, {"year": "1922", "text": "<PERSON>, English journalist and critic (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and critic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American football player (d. 2004)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American physician and academic (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Canadian historian and academic (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian historian and academic (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American pharmacologist and chemist (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and chemist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacologist and chemist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, German composer and conductor (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ttcher\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ttcher\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_B%C3%B6ttcher"}]}, {"year": "1927", "text": "<PERSON>, American author, illustrator, and publisher (d. 1981)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, and publisher (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, illustrator, and publisher (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, President of Uruguay (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, President of Uruguay (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_Mar%C3%ADa_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, President of Uruguay (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mar%C3%AD<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American journalist and sportscaster (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Armenian chess player (d. 1984)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tig<PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tig<PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian chess player (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tig<PERSON>_Pet<PERSON>ian"}]}, {"year": "1930", "text": "<PERSON>, American guitarist (d. 1988)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>up"}]}, {"year": "1930", "text": "<PERSON>, English cricketer (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American painter and illustrator (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English runner (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American colonel and politician (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American soldier and politician (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French violinist (d. 1982)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French violinist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American basketball player (d. 1970)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Canadian tribal leader and activist (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Harper\"><PERSON><PERSON></a>, Canadian tribal leader and activist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Harper\"><PERSON><PERSON></a>, Canadian tribal leader and activist (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Irish footballer and manager (d. 2013)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer and manager (d. 2013)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1937", "text": "<PERSON>, American sociologist and philosopher", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and philosopher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian fashion designer, television presenter and politician (d. 2009)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian fashion designer, television presenter and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian fashion designer, television presenter and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clodovil_<PERSON>nan<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Economics\" class=\"mw-redirect\" title=\"Nobel Prize in Economics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Economics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Economics"}]}, {"year": "1940", "text": "<PERSON>, American football player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American bassist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English chemist and academic (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>y"}]}, {"year": "1942", "text": "<PERSON>, Egyptian politician, Vice President of Egypt, Nobel Prize laureate", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Egypt\" class=\"mw-redirect\" title=\"Vice President of Egypt\">Vice President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_Egypt\" class=\"mw-redirect\" title=\"Vice President of Egypt\">Vice President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_ElBaradei"}, {"title": "Vice President of Egypt", "link": "https://wikipedia.org/wiki/Vice_President_of_Egypt"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Do%C4%9Fu_Perin%C3%A7ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Do%C4%9Fu_Perin%C3%A7ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Do%C4%9Fu_Perin%C3%A7ek"}]}, {"year": "1942", "text": "<PERSON>, American actor and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American historian and politician, 58th Speaker of the United States House of Representatives", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and politician, 58th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American historian and politician, 58th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Belgian theorist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian theorist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian theorist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American engineer and pilot", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and pilot", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American football player (d. 2009)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player (d. 2009)", "links": [{"title": "<PERSON> (quarterback)", "link": "https://wikipedia.org/wiki/<PERSON>_(quarterback)"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American general", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English politician, 1st Mayor of London", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of London", "link": "https://wikipedia.org/wiki/Mayor_of_London"}]}, {"year": "1945", "text": "<PERSON>, Belgian cyclist and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American broadcaster and author (d. 2018)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Art_Bell\" title=\"Art Bell\"><PERSON></a>, American broadcaster and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Bell\" title=\"Art Bell\"><PERSON></a>, American broadcaster and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Bell"}]}, {"year": "1946", "text": "<PERSON>, Austrian author, poet, and playwright", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor (d. 2008)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American gospel singer, pastor (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gospel singer, pastor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gospel singer, pastor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American journalist and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American composer and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American rock singer-songwriter and keyboard player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1947", "text": "<PERSON>, English singer-songwriter (d. 2000)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer,_born_1947)\" title=\"<PERSON> (singer, born 1947)\"><PERSON></a>, English singer-songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer,_born_1947)\" title=\"<PERSON> (singer, born 1947)\"><PERSON></a>, English singer-songwriter (d. 2000)", "links": [{"title": "<PERSON> (singer, born 1947)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer,_born_1947)"}]}, {"year": "1948", "text": "<PERSON>, Venezuelan baseball player and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>cepci%C3%B3n"}]}, {"year": "1948", "text": "<PERSON>, American historian and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Mexican baseball player and politician (d. 1992)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Aurelio_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurelio_L%C3%B3pez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican baseball player and politician (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_L%C3%B3pez"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English physician and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist (d. 1987)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Snakefinger\" title=\"Snakefinger\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Snakefinger\" title=\"Snakefinger\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>finger"}]}, {"year": "1949", "text": "<PERSON>, English economist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and academic", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>(economist)"}]}, {"year": "1949", "text": "<PERSON>, American country singer-songwriter and guitarist (d. 2019)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country singer-songwriter and guitarist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country singer-songwriter and guitarist (d. 2019)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1950", "text": "<PERSON>, New Zealand film director", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American author and activist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Starhawk\" title=\"Starhawk\">Star<PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Starhawk\" title=\"Starhawk\"><PERSON><PERSON></a>, American author and activist", "links": [{"title": "Starhawk", "link": "https://wikipedia.org/wiki/Starhawk"}]}, {"year": "1951", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1951", "text": "<PERSON>, American actor, comedian, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American ice hockey player, coach, and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley, English educator and politician, Secretary of State for Education", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Yardley\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley\"><PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley</a>, English educator and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baroness_<PERSON>_of_Yardley\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley\"><PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley</a>, English educator and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Education\" title=\"Secretary of State for Education\">Secretary of State for Education</a>", "links": [{"title": "<PERSON><PERSON><PERSON>, Baroness <PERSON> of Yardley", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Yardley"}, {"title": "Secretary of State for Education", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Education"}]}, {"year": "1953", "text": "<PERSON>, English educator and politician, Shadow Secretary of State for Defence", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence\" title=\"Shadow Secretary of State for Defence\">Shadow Secretary of State for Defence</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aker"}, {"title": "Shadow Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Defence"}]}, {"year": "1953", "text": "<PERSON>, Spanish sculptor and storyteller (d. 2001)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, Spanish sculptor and storyteller (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, Spanish sculptor and storyteller (d. 2001)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz_(sculptor)"}]}, {"year": "1954", "text": "<PERSON>-<PERSON>, American actor and director", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Estonian historian, author, and academic", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian historian, author, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1955", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>v%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bob_Sauv%C3%A9"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Turkish fashion designer and businessman", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish fashion designer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish fashion designer and businessman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ko"}]}, {"year": "1956", "text": "<PERSON>, Scottish rugby player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Irish singer-songwriter and guitarist (d. 2013)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American tenor and educator (d. 2005)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American tenor and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American tenor and educator (d. 2005)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1957", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON>, Latvian composer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/U%C4%A3is_Prauli%C5%86%C5%A1\" title=\"Uģis Prauliņ<PERSON>\">Uģis <PERSON><PERSON><PERSON><PERSON></a>, Latvian composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U%C4%A3is_Prauli%C5%86%C5%A1\" title=\"Uģis Prauliņ<PERSON>\">Uģis <PERSON><PERSON><PERSON><PERSON></a>, Latvian composer", "links": [{"title": "Uģis <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U%C4%A3is_Prauli%C5%86%C5%A1"}]}, {"year": "1958", "text": "<PERSON>, French rugby player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON>fra\"><PERSON><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Biafra"}]}, {"year": "1958", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Syrian-Canadian academic and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian-Canadian academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian-Canadian academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American author and historian", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and historian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, South African-English economist and academic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Greek basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Spanish race car driver (d. 2021)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Adri%C3%A1n_Campos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish race car driver (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adri%C3%A1n_Campos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish race car driver (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adri%C3%A1n_Campos"}]}, {"year": "1960", "text": "<PERSON>, American actor", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON> Church\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON> Church\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Japanese actor and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>made<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>made<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dichi_Ya<PERSON>ra"}]}, {"year": "1962", "text": "<PERSON>, Finnish singer-songwriter and saxophonist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter and saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter and saxophonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor, television presenter, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television presenter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television presenter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Italian race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, German swimmer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, German swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, German swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1964", "text": "<PERSON>, English cricketer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, American football player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American speed skater and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American speed skater and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Irish runner and poker player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Dar<PERSON>_<PERSON>%27Kearney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish runner and poker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dar<PERSON>_<PERSON>%27Kearney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish runner and poker player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dara_O%27Kearney"}]}, {"year": "1966", "text": "<PERSON>, Iraqi journalist and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American fashion designer and philanthropist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>urch\" title=\"<PERSON> Burch\"><PERSON></a>, American fashion designer and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>urch\" title=\"<PERSON> Burch\"><PERSON></a>, American fashion designer and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ch"}]}, {"year": "1966", "text": "<PERSON>, American football player (d. 2013)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)"}]}, {"year": "1966", "text": "<PERSON>, English runner", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German soprano and actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dorothea_R%C3%B<PERSON><PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American keyboard player and composer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby league player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Japanese wrestler and mixed martial artist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Suzuki\"><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Suzuki\"><PERSON><PERSON></a>, Japanese wrestler and mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Kenyan runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Ukrainian-Russian footballer and manager (d. 2013)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>mbalar\" title=\"<PERSON><PERSON> Tsymbalar\"><PERSON><PERSON></a>, Ukrainian-Russian footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> T<PERSON>mbalar\"><PERSON><PERSON></a>, Ukrainian-Russian footballer and manager (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ilya_Tsymbalar"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Fiset\" title=\"<PERSON><PERSON><PERSON><PERSON> Fiset\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9phane_Fiset\" title=\"<PERSON><PERSON><PERSON><PERSON> Fiset\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_Fiset"}]}, {"year": "1970", "text": "<PERSON>, American actor, comedian, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English football manager and former professional player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football manager and former professional player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football manager and former professional player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Mexican pop singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rubio\"><PERSON><PERSON></a>, Mexican pop singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Paulina_Rubio"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Irish politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Indian tennis player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Leander_Pa<PERSON>\" title=\"Leander Paes\"><PERSON><PERSON></a>, Indian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leander_Paes\" title=\"Leander Paes\"><PERSON><PERSON></a>, Indian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leander_Paes"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Greek archer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Evangelia_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Evan<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek archer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evangelia_Psarra"}]}, {"year": "1975", "text": "<PERSON>, American actor, director, and screenwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Spanish footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B3n"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Thai actress and model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Phiyada_Akkraseranee\" class=\"mw-redirect\" title=\"<PERSON>yada Akkraseranee\"><PERSON><PERSON><PERSON></a>, Thai actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ya<PERSON>_Akkraser<PERSON>e\" class=\"mw-redirect\" title=\"<PERSON>yada Akkraseranee\"><PERSON><PERSON><PERSON></a>, Thai actress and model", "links": [{"title": "Phiyada Akkraseranee", "link": "https://wikipedia.org/wiki/Phiyada_Akkraseranee"}]}, {"year": "1976", "text": "<PERSON>, English actor and martial artist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Belgian cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Polish philosopher and jurist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Bart<PERSON>z_Bro%C5%BCek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish philosopher and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bart<PERSON>z_Bro%C5%BCek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish philosopher and jurist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartosz_Bro%C5%BCek"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Slovenian tennis player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Tja%C5%A1a_Jezernik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tja%C5%A1a_Jezernik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tja%C5%A1a_Jezernik"}]}, {"year": "1977", "text": "<PERSON>, American football player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French ice dancer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American soccer player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American television personality", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Apostol\"><PERSON></a>, American television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Apostol\"><PERSON></a>, American television personality", "links": [{"title": "Tyson Apostol", "link": "https://wikipedia.org/wiki/Tyson_Apostol"}]}, {"year": "1979", "text": "<PERSON>, American rapper, producer, and voice actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Maylay\"><PERSON></a>, American rapper, producer, and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Maylay\"><PERSON></a>, American rapper, producer, and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Italian race walker", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rigaudo\"><PERSON><PERSON></a>, Italian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Rigaudo\"><PERSON><PERSON></a>, Italian race walker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>udo"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American author and illustrator", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Williams\" title=\"<PERSON> Williams\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Williams\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Williams"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Slovak ice hockey player (d. 2016)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Svato%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Svato%C5%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak ice hockey player (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marek_Svato%C5%A1"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Slovak tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>islav<PERSON>_Hrozensk%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>sk%C3%A1\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stanislava_Hrozensk%C3%A1"}]}, {"year": "1982", "text": "<PERSON>, English racing driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, English actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hittaker\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English singer/actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer/actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer/actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Bahamian sprinter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bahamian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Chinese race walker", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_T<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Chinese race walker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Turkish sprinter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/%C3%96zge_Ak%C4%B1n\" title=\"Özge Akın\"><PERSON>z<PERSON> Akın</a>, Turkish sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96zge_Ak%C4%B1n\" title=\"Özge Akın\"><PERSON>z<PERSON> Akın</a>, Turkish sprinter", "links": [{"title": "Özge Akın", "link": "https://wikipedia.org/wiki/%C3%96zge_Ak%C4%B1n"}]}, {"year": "1985", "text": "<PERSON>, Cypriot tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B3bis\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%B3bis\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_S%C3%B3bis"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Armenian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Armenian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Apo<PERSON>_Edel"}]}, {"year": "1986", "text": "<PERSON>, English rower", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" class=\"mw-redirect\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rower)\" class=\"mw-redirect\" title=\"<PERSON> (rower)\"><PERSON></a>, English rower", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rower)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Japanese singer and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji\" title=\"<PERSON><PERSON><PERSON> Tsuji\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji\" title=\"<PERSON><PERSON><PERSON>suji\"><PERSON><PERSON><PERSON></a>, Japanese singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ji"}]}, {"year": "1988", "text": "<PERSON>, Australian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Welsh footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1988)\" title=\"<PERSON> (footballer, born 1988)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1988)"}]}, {"year": "1988", "text": "<PERSON>, Australian swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Cypriot footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cypriot footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_To<PERSON>s"}]}, {"year": "1989", "text": "<PERSON>, American singer and actress (d. 2014)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Simone Battle\"><PERSON></a>, American singer and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Battle\" title=\"Simone Battle\"><PERSON></a>, American singer and actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Henderson"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian-Tongan rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Tongan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Lenglet\" title=\"C<PERSON><PERSON> Lengle<PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cl%C3%A9ment_Lenglet\" title=\"C<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cl%C3%A9ment_Lenglet"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Japanese actress and model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, New Zealand actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>\" title=\"KJ <PERSON>pa\"><PERSON><PERSON></a>, New Zealand actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"KJ <PERSON>pa\"><PERSON><PERSON></a>, New Zealand actor", "links": [{"title": "KJ Apa", "link": "https://wikipedia.org/wiki/KJ_Apa"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Romanian-Cypriot tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C8%98erban\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Cypriot tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C8%98erban\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Cypriot tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raluca_%C8%98erban"}]}, {"year": "1999", "text": "<PERSON>, Finnish ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Kazakhstani tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakhstani tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "656", "text": "<PERSON><PERSON><PERSON>, caliph of the Rashidun Caliphate (b. 579)", "html": "656 - <a href=\"https://wikipedia.org/wiki/<PERSON>th<PERSON>\" title=\"Uth<PERSON>\"><PERSON><PERSON><PERSON></a>, caliph of the Rashidun Caliphate (b. 579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>th<PERSON>\" title=\"<PERSON>th<PERSON>\"><PERSON><PERSON><PERSON></a>, caliph of the Rashidun Caliphate (b. 579)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>man"}]}, {"year": "676", "text": "<PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "676 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON><PERSON>tus_II\" title=\"Pope <PERSON>eo<PERSON>tus II\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>tus_II\" title=\"Pope Adeo<PERSON>tus II\"><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_II"}]}, {"year": "811", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Japanese shōgun (b. 758)", "html": "811 - <a href=\"https://wikipedia.org/wiki/Sakan<PERSON><PERSON>_no_<PERSON>mar<PERSON>\" title=\"<PERSON>kan<PERSON><PERSON> no Tam<PERSON>mar<PERSON>\"><PERSON><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese shōgun (b. 758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa<PERSON><PERSON><PERSON>_no_<PERSON>mar<PERSON>\" title=\"<PERSON>kan<PERSON>e no Tam<PERSON>maro\"><PERSON><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese shōgun (b. 758)", "links": [{"title": "Sakan<PERSON>e no <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>o"}]}, {"year": "850", "text": "<PERSON><PERSON><PERSON> <PERSON>, Japanese empress (b. 786)", "html": "850 - <a href=\"https://wikipedia.org/wiki/Tachibana_no_Ka<PERSON><PERSON>\" title=\"Tachibana no Kachiko\">Tachi<PERSON> no <PERSON></a>, Japanese empress (b. 786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tachibana_no_Ka<PERSON><PERSON>\" title=\"Tachibana no Kachiko\">Tachi<PERSON> no <PERSON></a>, Japanese empress (b. 786)", "links": [{"title": "Tachibana no Kachiko", "link": "https://wikipedia.org/wiki/Tachibana_no_Kachiko"}]}, {"year": "900", "text": "<PERSON><PERSON>, French archbishop and chancellor", "html": "900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Reims)\" title=\"<PERSON><PERSON> (archbishop of Reims)\"><PERSON><PERSON></a>, French archbishop and chancellor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Reims)\" title=\"<PERSON><PERSON> (archbishop of Reims)\"><PERSON><PERSON></a>, French archbishop and chancellor", "links": [{"title": "<PERSON><PERSON> (archbishop of Reims)", "link": "https://wikipedia.org/wiki/Fulk_(archbishop_of_Reims)"}]}, {"year": "1025", "text": "<PERSON><PERSON><PERSON> the Brave, Polish king (b. 967)", "html": "1025 - <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_I_the_Brave\" title=\"<PERSON><PERSON><PERSON> I the Brave\"><PERSON><PERSON><PERSON> I the Brave</a>, Polish king (b. 967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_I_the_Brave\" title=\"<PERSON><PERSON><PERSON> I the Brave\"><PERSON><PERSON><PERSON> the Brave</a>, Polish king (b. 967)", "links": [{"title": "<PERSON><PERSON><PERSON> the Brave", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_I_the_Brave"}]}, {"year": "1091", "text": "<PERSON>, count of Holland (b. 1052)", "html": "1091 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON></a>, count of Holland (b. 1052)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON></a>, count of Holland (b. 1052)", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}]}, {"year": "1207", "text": "<PERSON><PERSON><PERSON>, Chinese buddhist monk (b. 1130)", "html": "1207 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese buddhist monk (b. 1130)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese buddhist monk (b. 1130)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1219", "text": "<PERSON> of Scotland, 8th Earl of Huntingdon", "html": "1219 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Huntingdon\" title=\"<PERSON>, Earl of Huntingdon\"><PERSON> of Scotland, 8th Earl of Huntingdon</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Earl_of_Huntingdon\" title=\"<PERSON>, Earl of Huntingdon\"><PERSON> of Scotland, 8th Earl of Huntingdon</a>", "links": [{"title": "<PERSON>, Earl of Huntingdon", "link": "https://wikipedia.org/wiki/<PERSON>,_Earl_of_Huntingdon"}]}, {"year": "1361", "text": "<PERSON><PERSON><PERSON> of Norway, princess consort and regent of Sweden (b. 1301)", "html": "1361 - <a href=\"https://wikipedia.org/wiki/Ingeborg_of_Norway\" title=\"Ingeborg of Norway\">Ingeborg of Norway</a>, princess consort and regent of Sweden (b. 1301)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ingeborg_of_Norway\" title=\"Ingeborg of Norway\">Ingeborg of Norway</a>, princess consort and regent of Sweden (b. 1301)", "links": [{"title": "Ingeborg of Norway", "link": "https://wikipedia.org/wiki/Ingeborg_of_Norway"}]}, {"year": "1400", "text": "<PERSON>, archbishop of Prague (b. 1348)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/Jan_of_<PERSON>%C5%<PERSON><PERSON><PERSON>\" title=\"<PERSON> Jenštejn\"><PERSON>jn</a>, archbishop of Prague (b. 1348)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_<PERSON>_<PERSON>%C5%<PERSON><PERSON><PERSON>\" title=\"<PERSON> Jenštejn\"><PERSON></a>, archbishop of Prague (b. 1348)", "links": [{"title": "<PERSON> Jenštejn", "link": "https://wikipedia.org/wiki/Jan_<PERSON>_<PERSON>%C5%A1tejn"}]}, {"year": "1463", "text": "<PERSON> of Portugal, Portuguese princess (b. 1436)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal_(nun)\" title=\"<PERSON> of Portugal (nun)\"><PERSON> of Portugal</a>, Portuguese princess (b. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal_(nun)\" title=\"<PERSON> of Portugal (nun)\"><PERSON> of Portugal</a>, Portuguese princess (b. 1436)", "links": [{"title": "<PERSON> of Portugal (nun)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal_(nun)"}]}, {"year": "1501", "text": "<PERSON>, Polish king (b. 1459)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (b. 1459)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (b. 1459)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1565", "text": "<PERSON><PERSON><PERSON>, Japanese shō<PERSON> (b. 1536)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Ashikaga_Yoshiteru\" title=\"Ashikaga Yoshiteru\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ashika<PERSON>_Yoshiteru\" title=\"Ashikaga Yoshiteru\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1536)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ashikaga_Yoshiteru"}]}, {"year": "1631", "text": "<PERSON><PERSON><PERSON>, Mughal princess (b. 1593)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal princess (b. 1593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mughal princess (b. 1593)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON><PERSON> of Joseon, Korean king (b. 1595)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/Injo_of_Joseon\" title=\"Injo of Joseon\"><PERSON><PERSON> of Joseon</a>, Korean king (b. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Injo_of_Joseon\" title=\"Injo of Joseon\"><PERSON><PERSON> of Joseon</a>, Korean king (b. 1595)", "links": [{"title": "<PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/Injo_of_Joseon"}]}, {"year": "1674", "text": "<PERSON><PERSON><PERSON><PERSON>, Dowager Queen, mother of <PERSON><PERSON> (b. 1598)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/Jija<PERSON>i\" title=\"Jija<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dowager Queen, mother of <PERSON><PERSON> (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jijabai\" title=\"Jija<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dowager Queen, mother of <PERSON><PERSON> (b. 1598)", "links": [{"title": "Jijabai", "link": "https://wikipedia.org/wiki/Jijabai"}]}, {"year": "1694", "text": "<PERSON>, English cardinal (b. 1629)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, English cardinal (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, English cardinal (b. 1629)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)"}]}, {"year": "1696", "text": "<PERSON>, Polish king (b. 1629)", "html": "1696 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish king (b. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, English essayist, poet, playwright, and politician  (b. 1672)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, poet, playwright, and politician (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English essayist, poet, playwright, and politician (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, French general and politician, French Secretary of State for War (b. 1653)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Secretary of State for War</a> (b. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1740", "text": "Sir <PERSON>, 3rd Baronet, English politician, Chancellor of the Exchequer (b. 1687)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (b. 1687)", "links": [{"title": "Sir <PERSON>, 3rd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1762", "text": "Prosper <PERSON><PERSON>, French poet and playwright (b. 1674)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Prosper_<PERSON><PERSON><PERSON>_de_Cr%C3%A9billon\" title=\"Prosper <PERSON>rébillon\">Pro<PERSON></a>, French poet and playwright (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prosper_<PERSON><PERSON>_de_Cr%C3%A9billon\" title=\"Prosper <PERSON><PERSON> Crébillon\"><PERSON><PERSON></a>, French poet and playwright (b. 1674)", "links": [{"title": "Prosper <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prosper_<PERSON><PERSON><PERSON>_de_Cr%C3%A9billon"}]}, {"year": "1771", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek rebel leader (b. 1722)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>ogian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek rebel leader (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>kalogian<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek rebel leader (b. 1722)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Daskalogiannis"}]}, {"year": "1775", "text": "<PERSON>, Scottish-English soldier (b. 1722)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English soldier (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English soldier (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Persian tribal chief (b. 1742)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Persian tribal chief (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Persian tribal chief (b. 1742)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, 1st Baron <PERSON>, Scottish-English admiral and politician (b. 1726)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Barham\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English admiral and politician (b. 1726)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron Barham\"><PERSON>, 1st Baron <PERSON></a>, Scottish-English admiral and politician (b. 1726)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, Argentinian general and politician (b. 1785)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>_<PERSON>_<PERSON>%C3%BCemes\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>_<PERSON>_<PERSON>%C3%BCemes\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>_de_<PERSON>%C3%BCemes"}]}, {"year": "1839", "text": "Lord <PERSON>, English general and politician, 14th Governor-General of India (b. 1774)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English general and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English general and politician, 14th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1774)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1866", "text": "<PERSON>, French poet and author (b. 1798)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French poet and author (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ry"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Chiracaua Apache warrior woman (b. ~1840)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Lozen\" title=\"Lozen\"><PERSON><PERSON></a>, Chiracaua Apache warrior woman (b. ~1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lozen\" title=\"Lozen\"><PERSON><PERSON></a>, Chiracaua Apache warrior woman (b. ~1840)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>zen"}]}, {"year": "1898", "text": "<PERSON>, English soldier and painter (b. 1833)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Russian soldier and politician, Governor-General of Finland (b. 1839)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_Finland\" title=\"Governor-General of Finland\">Governor-General of Finland</a> (b. 1839)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of Finland", "link": "https://wikipedia.org/wiki/Governor-General_of_Finland"}]}, {"year": "1914", "text": "<PERSON>, French military officer and aviator (b. 1869) ", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, French military officer and aviator (b. 1869) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9lix\" title=\"<PERSON>\"><PERSON></a>, French military officer and aviator (b. 1869) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julien_F%C3%A9lix"}]}, {"year": "1936", "text": "<PERSON>, Estonian journalist, politician, and diplomat, Estonian Minister of Foreign Affairs (b. 1883)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1939", "text": "<PERSON>, American baseball player, coach, and manager (b. 1893)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, German criminal (b. 1908)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German criminal (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German criminal (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (b. 1865)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1941", "text": "<PERSON>, Dutch organist and composer (b. 1862)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch organist and composer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch organist and composer (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, protopresbyter of the Serbian Orthodox Church, victim of Genocide of Serbs (b. 1911)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C4%90or%C4%91e_Bogi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, protopresbyter of the <a href=\"https://wikipedia.org/wiki/Serbian_Orthodox_Church\" title=\"Serbian Orthodox Church\">Serbian Orthodox Church</a>, victim of <a href=\"https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia\" title=\"Genocide of Serbs in the Independent State of Croatia\">Genocide of Serbs</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%90or%C4%91e_Bogi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, protopresbyter of the <a href=\"https://wikipedia.org/wiki/Serbian_Orthodox_Church\" title=\"Serbian Orthodox Church\">Serbian Orthodox Church</a>, victim of <a href=\"https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia\" title=\"Genocide of Serbs in the Independent State of Croatia\">Genocide of Serbs</a> (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%90or%C4%91e_Bogi%C4%87"}, {"title": "Serbian Orthodox Church", "link": "https://wikipedia.org/wiki/Serbian_Orthodox_Church"}, {"title": "Genocide of Serbs in the Independent State of Croatia", "link": "https://wikipedia.org/wiki/Genocide_of_Serbs_in_the_Independent_State_of_Croatia"}]}, {"year": "1942", "text": "<PERSON>, Canadian lawyer and politician, 5th Chief Justice of Canada (b. 1853)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1952", "text": "<PERSON>, American chemist and engineer (b. 1914)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)\" class=\"mw-redirect\" title=\"<PERSON> (rocket engineer)\"><PERSON></a>, American chemist and engineer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)\" class=\"mw-redirect\" title=\"<PERSON> (rocket engineer)\"><PERSON></a>, American chemist and engineer (b. 1914)", "links": [{"title": "<PERSON> (rocket engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rocket_engineer)"}]}, {"year": "1954", "text": "<PERSON>, American guitarist and bandleader (b. 1920)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and bandleader (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and bandleader (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>, English businessman (b. 1878)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, 1st Baron <PERSON>\"><PERSON><PERSON><PERSON>, 1st Baron <PERSON></a>, English businessman (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_1st_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German surgeon and academic (b. 1892)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German surgeon and academic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American race car driver (b. 1926)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English journalist and author (b. 1873)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian-American cartoonist (b. 1888)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American cartoonist (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American cartoonist (b. 1888)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor (b. 1918)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian politician (b. 1882)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla\" title=\"Aleksan<PERSON>sküla\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla\" title=\"<PERSON>eksan<PERSON>üla\"><PERSON><PERSON><PERSON><PERSON></a>, Estonian politician (b. 1882)", "links": [{"title": "Aleksander <PERSON>", "link": "https://wikipedia.org/wiki/Aleksander_Kesk%C3%BCla"}]}, {"year": "1968", "text": "<PERSON>, Uruguayan footballer and manager (b. 1901)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and politician, 8th Speaker of the Grand National Assembly of Turkey (b. 1889)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Grand_National_Assembly_of_Turkey\" class=\"mw-redirect\" title=\"Speaker of the Grand National Assembly of Turkey\">Speaker of the Grand National Assembly of Turkey</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Grand_National_Assembly_of_Turkey\" class=\"mw-redirect\" title=\"Speaker of the Grand National Assembly of Turkey\">Speaker of the Grand National Assembly of Turkey</a> (b. 1889)", "links": [{"title": "Re<PERSON>k <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>fik_<PERSON>tan"}, {"title": "Speaker of the Grand National Assembly of Turkey", "link": "https://wikipedia.org/wiki/Speaker_of_the_Grand_National_Assembly_of_Turkey"}]}, {"year": "1975", "text": "<PERSON>, American historian and academic (b. 1893)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American historian and academic (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American historian and academic (b. 1893)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English cricketer and politician (b. 1898)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and politician (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player and manager (b. 1888)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Indian-English general (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Indian-English general (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Indian-English general (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_O%27Connor"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American author and educator (b. 1889)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Z<PERSON><PERSON> Sharp\"><PERSON><PERSON><PERSON></a>, American author and educator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Sharp\"><PERSON><PERSON><PERSON></a>, American author and educator (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sharp"}]}, {"year": "1982", "text": "<PERSON>, Italian banker (b. 1920)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian banker (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American composer and educator (b. 1923)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and educator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English director, producer, and screenwriter (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer (b. 1907)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player, coach, and manager (b. 1936)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American historian and philosopher (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and philosopher (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American illustrator (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American illustrator (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English cardinal (b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, South African lawyer and jurist, 17th Chief Justice of South Africa (b. 1931)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and jurist, 17th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and jurist, 17th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ismail_<PERSON>"}, {"title": "Chief Justice of South Africa", "link": "https://wikipedia.org/wiki/Chief_Justice_of_South_Africa"}]}, {"year": "2001", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2001", "text": "<PERSON>, Scottish cardinal (b. 1925)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Winning\"><PERSON></a>, Scottish cardinal (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Winning\"><PERSON></a>, Scottish cardinal (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American sprinter and hurdler (b. 1943)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, German footballer (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Canadian ice hockey player (b. 1926)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Brazilian comedian (b. 1962)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Bussunda\" title=\"Bussunda\"><PERSON><PERSON><PERSON></a>, Brazilian comedian (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bussunda\" title=\"Bussunda\"><PERSON><PERSON><PERSON></a>, Brazilian comedian (b. 1962)", "links": [{"title": "Bussunda", "link": "https://wikipedia.org/wiki/Bussunda"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian fashion designer (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ferr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian fashion designer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rr%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian fashion designer (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gianfranco_Ferr%C3%A9"}]}, {"year": "2007", "text": "<PERSON>, American dancer and choreographer (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American actress and dancer (b. 1922)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and dancer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, German-English sociologist and politician (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English sociologist and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English sociologist and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American sergeant (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Australian rugby player and sportscaster (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and sportscaster (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, French mountaineer (b. 1971)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mountaineer (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French mountaineer (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (b. 1931)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2012", "text": "<PERSON>, Canadian mathematician and chess player (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician and chess player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian mathematician and chess player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American victim of police brutality (b. 1965)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American victim of <a href=\"https://wikipedia.org/wiki/Police_brutality\" title=\"Police brutality\">police brutality</a> (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American victim of <a href=\"https://wikipedia.org/wiki/Police_brutality\" title=\"Police brutality\">police brutality</a> (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Police brutality", "link": "https://wikipedia.org/wiki/Police_brutality"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Pakistani actress and politician (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Fauzia_Wahab\" title=\"Fauzia Wahab\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Pakistani actress and politician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fauzia_Wahab\" title=\"Fauzia Wahab\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani actress and politician (b. 1956)", "links": [{"title": "<PERSON>au<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>au<PERSON>_Wahab"}]}, {"year": "2013", "text": "<PERSON>, New Zealand-English theorist and author (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English theorist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English theorist and author (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Bangladeshi playwright and producer (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Atiq<PERSON>_<PERSON>_<PERSON>\" title=\"Atiqul <PERSON> Chow<PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi playwright and producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atiq<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Atiqul <PERSON> Chowdh<PERSON>\">At<PERSON><PERSON></a>, Bangladeshi playwright and producer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atiq<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian lawyer and civil servant (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and civil servant (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and civil servant (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_F._C%C3%B4t%C3%A9"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American basketball player (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>hler<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American politician, 68th Governor of North Carolina (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_North_Carolina\" title=\"Governor of North Carolina\">Governor of North Carolina</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of North Carolina", "link": "https://wikipedia.org/wiki/Governor_of_North_Carolina"}]}, {"year": "2014", "text": "<PERSON><PERSON>, English actress (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Canadian epidemiologist and academic (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/%C3%89ric_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian epidemiologist and academic (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian epidemiologist and academic (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON> 3, American businessman and philanthropist (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Stanley_Marsh_3\" title=\"Stanley Marsh 3\"><PERSON> 3</a>, American businessman and philanthropist (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanley_Marsh_3\" title=\"Stanley Marsh 3\"><PERSON> 3</a>, American businessman and philanthropist (b. 1938)", "links": [{"title": "Stanley Marsh 3", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_3"}]}, {"year": "2014", "text": "<PERSON>, American physician and academic (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American ice hockey player and sportscaster (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and sportscaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player and sportscaster (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian runner and politician, Mayor of the Gold Coast (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast\" title=\"Mayor of the Gold Coast\">Mayor of the Gold Coast</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast\" title=\"Mayor of the Gold Coast\">Mayor of the Gold Coast</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of the Gold Coast", "link": "https://wikipedia.org/wiki/Mayor_of_the_Gold_Coast"}]}, {"year": "2015", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish engineer and politician, 9th President of Turkey (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_Demirel\" title=\"Süleyman Demirel\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON>leyman_Demirel\" title=\"Süleyman Demirel\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish engineer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "2015", "text": "<PERSON>, Argentinian general and politician, 36th President of Argentina (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_Argentina\" title=\"President of Argentina\">President of Argentina</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Argentina", "link": "https://wikipedia.org/wiki/President_of_Argentina"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American minister and politician (b. 1973)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Clementa_C._<PERSON>ney\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American minister and politician (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clementa_C._<PERSON>ney\" title=\"Clement<PERSON>\"><PERSON><PERSON></a>, American minister and politician (b. 1973)", "links": [{"title": "Clementa C<PERSON>", "link": "https://wikipedia.org/wiki/Clementa_<PERSON><PERSON>_<PERSON>nc<PERSON>ney"}]}, {"year": "2017", "text": "<PERSON>, president of Vanuatu (b. 1948)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, president of Vanuatu (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baldwin <PERSON>\"><PERSON></a>, president of Vanuatu (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Baldwin_Lonsdale"}]}, {"year": "2019", "text": "<PERSON>, American artist, author actress, fashion designer, heiress and socialite (b. 1924)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, author actress, fashion designer, heiress and socialite (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist, author actress, fashion designer, heiress and socialite (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Egyptian professor and politician, first elected president of Egypt after Egyptian revolution (b. 1951)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian professor and politician, first elected <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Egypt\" title=\"List of presidents of Egypt\">president of Egypt</a> after <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">Egyptian revolution</a> (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian professor and politician, first elected <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_Egypt\" title=\"List of presidents of Egypt\">president of Egypt</a> after <a href=\"https://wikipedia.org/wiki/2011_Egyptian_revolution\" title=\"2011 Egyptian revolution\">Egyptian revolution</a> (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of presidents of Egypt", "link": "https://wikipedia.org/wiki/List_of_presidents_of_Egypt"}, {"title": "2011 Egyptian revolution", "link": "https://wikipedia.org/wiki/2011_Egyptian_revolution"}]}, {"year": "2020", "text": "<PERSON>, American activist, humanitarian, author and diplomat (United States Ambassador to Ireland, 1993-1998) (b. 1928)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, humanitarian, author and diplomat (<a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Ireland\" class=\"mw-redirect\" title=\"United States Ambassador to Ireland\">United States Ambassador to Ireland</a>, 1993-1998) (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, humanitarian, author and diplomat (<a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Ireland\" class=\"mw-redirect\" title=\"United States Ambassador to Ireland\">United States Ambassador to Ireland</a>, 1993-1998) (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Ireland", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Ireland"}]}, {"year": "2021", "text": "<PERSON>, Zambian educator and politician, first president of Zambia (b. 1924)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian educator and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">president of Zambia</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian educator and politician, first <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">president of Zambia</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}]}}