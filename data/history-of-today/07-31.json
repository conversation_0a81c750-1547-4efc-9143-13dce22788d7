{"date": "July 31", "url": "https://wikipedia.org/wiki/July_31", "data": {"Events": [{"year": "30 BC", "text": "Battle of Alexandria: <PERSON> achieves a minor victory over <PERSON><PERSON><PERSON>'s forces, but most of his army subsequently deserts, leading to his suicide.", "html": "30 BC - 30 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Alexandria_(30_BC)\" title=\"Battle of Alexandria (30 BC)\">Battle of Alexandria</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> achieves a minor victory over <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>'s forces, but most of his army subsequently deserts, leading to his suicide.", "no_year_html": "30 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_Alexandria_(30_BC)\" title=\"Battle of Alexandria (30 BC)\">Battle of Alexandria</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> achieves a minor victory over <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>'s forces, but most of his army subsequently deserts, leading to his suicide.", "links": [{"title": "Battle of Alexandria (30 BC)", "link": "https://wikipedia.org/wiki/Battle_of_Alexandria_(30_BC)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "781", "text": "The oldest recorded eruption of Mount Fuji (Traditional Japanese date: Sixth day of the seventh month of the first year of the Ten'o (天応) era).", "html": "781 - The oldest recorded eruption of <a href=\"https://wikipedia.org/wiki/Mount_Fuji\" title=\"Mount Fuji\">Mount Fuji</a> (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: Sixth day of the seventh month of the first year of the Ten'o (天応) era).", "no_year_html": "The oldest recorded eruption of <a href=\"https://wikipedia.org/wiki/Mount_Fuji\" title=\"Mount Fuji\">Mount Fuji</a> (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: Sixth day of the seventh month of the first year of the Ten'o (天応) era).", "links": [{"title": "Mount Fuji", "link": "https://wikipedia.org/wiki/Mount_Fuji"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "1009", "text": "Pope <PERSON><PERSON><PERSON> becomes the 142nd pope, succeeding Pope <PERSON>.", "html": "1009 - <a href=\"https://wikipedia.org/wiki/Pope_Sergius_IV\" title=\"Pope Sergius IV\">Pope <PERSON><PERSON><PERSON> IV</a> becomes the 142nd pope, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON>_John_XVIII\" title=\"Pope John XVIII\">Pope John <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Sergius_IV\" title=\"Pope Sergius IV\">Pope <PERSON><PERSON><PERSON> IV</a> becomes the 142nd pope, succeeding <a href=\"https://wikipedia.org/wiki/<PERSON>_John_XVI<PERSON>\" title=\"Pope John XVIII\">Pope John <PERSON></a>.", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_IV"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1201", "text": "Attempted usurpation by <PERSON> the Fat for the throne of <PERSON><PERSON>.", "html": "1201 - Attempted usurpation by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Fat\" title=\"<PERSON> the Fat\"><PERSON> the Fat</a> for the throne of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Angelos\" title=\"<PERSON><PERSON> III Angelos\"><PERSON><PERSON></a>.", "no_year_html": "Attempted usurpation by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Fat\" title=\"<PERSON> the Fat\"><PERSON> the Fat</a> for the throne of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Angelo<PERSON>\" title=\"<PERSON><PERSON> III <PERSON>s\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON> the Fat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Fat"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexios_III_Angelos"}]}, {"year": "1423", "text": "Hundred Years' War: Battle of Cravant: A Franco-Scottish army is defeated by the Anglo-Burgundians at Cravant on the banks of the river Yonne.", "html": "1423 - <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cravant\" title=\"Battle of Cravant\">Battle of Cravant</a>: A Franco-Scottish army is defeated by the Anglo-Burgundians at Cravant on the banks of the river <a href=\"https://wikipedia.org/wiki/Yonne\" title=\"Yonne\">Yonne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Cravant\" title=\"Battle of Cravant\">Battle of Cravant</a>: A Franco-Scottish army is defeated by the Anglo-Burgundians at Cravant on the banks of the river <a href=\"https://wikipedia.org/wiki/Yonne\" title=\"Yonne\">Yonne</a>.", "links": [{"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}, {"title": "Battle of Cravant", "link": "https://wikipedia.org/wiki/Battle_of_Cravant"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yonne"}]}, {"year": "1451", "text": "<PERSON> is arrested by order of <PERSON> of France.", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a> is arrested by order of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%93ur\" title=\"<PERSON>\"><PERSON></a> is arrested by order of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VII of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_C%C5%93ur"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}]}, {"year": "1492", "text": "All remaining Jews are expelled from Spain when the Alhambra Decree takes effect.", "html": "1492 - All remaining Jews are expelled from Spain when the <a href=\"https://wikipedia.org/wiki/Alhambra_Decree\" title=\"Alhambra Decree\">Alhambra Decree</a> takes effect.", "no_year_html": "All remaining Jews are expelled from Spain when the <a href=\"https://wikipedia.org/wiki/Alhambra_Decree\" title=\"Alhambra Decree\">Alhambra Decree</a> takes effect.", "links": [{"title": "Alhambra Decree", "link": "https://wikipedia.org/wiki/Alhambra_Decree"}]}, {"year": "1498", "text": "On his third voyage to the Western Hemisphere, <PERSON> becomes the first European to discover the island of Trinidad.", "html": "1498 - On his third voyage to the Western Hemisphere, <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to discover the island of <a href=\"https://wikipedia.org/wiki/Trinidad\" title=\"Trinidad\">Trinidad</a>.", "no_year_html": "On his third voyage to the Western Hemisphere, <a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to discover the island of <a href=\"https://wikipedia.org/wiki/Trinidad\" title=\"Trinidad\">Trinidad</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Trinidad", "link": "https://wikipedia.org/wiki/Trinidad"}]}, {"year": "1618", "text": "<PERSON>, Prince of Orange disbands the waardgelders militia in Utrecht, a pivotal event in the Remonstrant/Counter-Remonstrant tensions.", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> disbands the waardgelders militia in <a href=\"https://wikipedia.org/wiki/Utrecht\" title=\"Utrecht\">Utrecht</a>, a pivotal event in the <a href=\"https://wikipedia.org/wiki/Remonstrant\" class=\"mw-redirect\" title=\"Remonstrant\">Remonstrant</a>/<a href=\"https://wikipedia.org/wiki/Counter-Remonstrant\" class=\"mw-redirect\" title=\"Counter-Remonstrant\">Counter-Remonstrant</a> tensions.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Orange\" title=\"<PERSON>, Prince of Orange\"><PERSON>, Prince of Orange</a> disbands the waardgelders militia in <a href=\"https://wikipedia.org/wiki/Utrecht\" title=\"Utrecht\">Utrecht</a>, a pivotal event in the <a href=\"https://wikipedia.org/wiki/Remonstrant\" class=\"mw-redirect\" title=\"Remonstrant\">Remonstrant</a>/<a href=\"https://wikipedia.org/wiki/Counter-Remonstrant\" class=\"mw-redirect\" title=\"Counter-Remonstrant\">Counter-Remonstrant</a> tensions.", "links": [{"title": "<PERSON>, Prince of Orange", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Orange"}, {"title": "Utrecht", "link": "https://wikipedia.org/wiki/Utrecht"}, {"title": "Remonstrant", "link": "https://wikipedia.org/wiki/Remonstrant"}, {"title": "Counter-Remonstrant", "link": "https://wikipedia.org/wiki/Counter-Remonstrant"}]}, {"year": "1655", "text": "Russo-Polish War (1654-67): The Russian army enters the capital of the Grand Duchy of Lithuania, Vilnius, which it holds for six years.", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%9367)\" class=\"mw-redirect\" title=\"Russo-Polish War (1654-67)\">Russo-Polish War (1654-67)</a>: The Russian army enters the capital of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>, which it holds for six years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%9367)\" class=\"mw-redirect\" title=\"Russo-Polish War (1654-67)\">Russo-Polish War (1654-67)</a>: The Russian army enters the capital of the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a>, <a href=\"https://wikipedia.org/wiki/Vilnius\" title=\"Vilnius\">Vilnius</a>, which it holds for six years.", "links": [{"title": "Russo-Polish War (1654-67)", "link": "https://wikipedia.org/wiki/Russo-Polish_War_(1654%E2%80%9367)"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Vilnius", "link": "https://wikipedia.org/wiki/Vilnius"}]}, {"year": "1658", "text": "<PERSON><PERSON><PERSON><PERSON> is proclaimed Mughal emperor of India.", "html": "1658 - <a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangzeb\">Aurangzeb</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Mughal_Empire\" title=\"Mughal Empire\">Mughal emperor</a> of India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurangzeb\" title=\"Aurangzeb\">Aurangzeb</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Mughal_Empire\" title=\"Mughal Empire\">Mughal emperor</a> of India.", "links": [{"title": "Aurangzeb", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>zeb"}, {"title": "Mughal Empire", "link": "https://wikipedia.org/wiki/Mughal_Empire"}]}, {"year": "1703", "text": "<PERSON> is placed in a pillory for the crime of seditious libel after publishing a politically satirical pamphlet, but is pelted with flowers.", "html": "1703 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is placed in a <a href=\"https://wikipedia.org/wiki/Pillory\" title=\"Pillory\">pillory</a> for the crime of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a> after publishing a politically satirical <a href=\"https://wikipedia.org/wiki/Pamphlet\" title=\"Pamphlet\">pamphlet</a>, but is pelted with flowers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is placed in a <a href=\"https://wikipedia.org/wiki/Pillory\" title=\"Pillory\">pillory</a> for the crime of <a href=\"https://wikipedia.org/wiki/Seditious_libel\" title=\"Seditious libel\">seditious libel</a> after publishing a politically satirical <a href=\"https://wikipedia.org/wiki/Pamphlet\" title=\"Pamphlet\">pamphlet</a>, but is pelted with flowers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pillory"}, {"title": "Seditious libel", "link": "https://wikipedia.org/wiki/Seditious_libel"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pamphlet"}]}, {"year": "1715", "text": "Seven days after a Spanish treasure fleet of 12 ships left Havana, Cuba for Spain, 11 of them sink in a storm off the coast of Florida. A few centuries later, treasure is salvaged from these wrecks.", "html": "1715 - Seven days after a <a href=\"https://wikipedia.org/wiki/1715_Treasure_Fleet\" title=\"1715 Treasure Fleet\">Spanish treasure fleet</a> of 12 ships left <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> for Spain, 11 of them sink in a storm off the coast of <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>. A few centuries later, treasure is salvaged from these wrecks.", "no_year_html": "Seven days after a <a href=\"https://wikipedia.org/wiki/1715_Treasure_Fleet\" title=\"1715 Treasure Fleet\">Spanish treasure fleet</a> of 12 ships left <a href=\"https://wikipedia.org/wiki/Havana\" title=\"Havana\">Havana</a>, <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a> for Spain, 11 of them sink in a storm off the coast of <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>. A few centuries later, treasure is salvaged from these wrecks.", "links": [{"title": "1715 Treasure Fleet", "link": "https://wikipedia.org/wiki/1715_Treasure_Fleet"}, {"title": "Havana", "link": "https://wikipedia.org/wiki/Havana"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "1741", "text": "<PERSON> of Bavaria invades Upper Austria and Bohemia.", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON> of Bavaria</a> invades <a href=\"https://wikipedia.org/wiki/Upper_Austria\" title=\"Upper Austria\">Upper Austria</a> and <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VII, Holy Roman Emperor\"><PERSON> of Bavaria</a> invades <a href=\"https://wikipedia.org/wiki/Upper_Austria\" title=\"Upper Austria\">Upper Austria</a> and <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Upper Austria", "link": "https://wikipedia.org/wiki/Upper_Austria"}, {"title": "Bohemia", "link": "https://wikipedia.org/wiki/Bohemia"}]}, {"year": "1763", "text": "Odawa Chief <PERSON>'s forces defeat British troops at the Battle of Bloody Run during Pontiac's War.", "html": "1763 - <a href=\"https://wikipedia.org/wiki/Odawa\" title=\"Odawa\">Odawa</a> Chief <a href=\"https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)\" class=\"mw-redirect\" title=\"<PERSON> (Ottawa leader)\">Pontiac</a>'s forces defeat British troops at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bloody_Run\" title=\"Battle of Bloody Run\">Battle of Bloody Run</a> during <a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odawa\" title=\"Odawa\">Odawa</a> Chief <a href=\"https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)\" class=\"mw-redirect\" title=\"<PERSON> (Ottawa leader)\">Pontiac</a>'s forces defeat British troops at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bloody_Run\" title=\"Battle of Bloody Run\">Battle of Bloody Run</a> during <a href=\"https://wikipedia.org/wiki/Pontiac%27s_War\" title=\"Pontiac's War\">Pontiac's War</a>.", "links": [{"title": "Odawa", "link": "https://wikipedia.org/wiki/Odawa"}, {"title": "<PERSON> (Ottawa leader)", "link": "https://wikipedia.org/wiki/Pontiac_(Ottawa_leader)"}, {"title": "Battle of Bloody Run", "link": "https://wikipedia.org/wiki/Battle_of_Bloody_Run"}, {"title": "Pontiac's War", "link": "https://wikipedia.org/wiki/Pontiac%27s_War"}]}, {"year": "1777", "text": "The U.S. Second Continental Congress passes a resolution that the services of <PERSON>, <PERSON> \"be accepted, and that, in consideration of his zeal, illustrious family and connexions, he have the rank and commission of major-general of the United States.\"", "html": "1777 - The U.S. <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes a resolution that the services of <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Marquis <PERSON>\"><PERSON>, Marquis <PERSON></a> \"be accepted, and that, in consideration of his zeal, illustrious family and connexions, he have the rank and commission of major-general of the United States.\"", "no_year_html": "The U.S. <a href=\"https://wikipedia.org/wiki/Second_Continental_Congress\" title=\"Second Continental Congress\">Second Continental Congress</a> passes a resolution that the services of <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, Marquis <PERSON>\"><PERSON>, Marquis <PERSON></a> \"be accepted, and that, in consideration of his zeal, illustrious family and connexions, he have the rank and commission of major-general of the United States.\"", "links": [{"title": "Second Continental Congress", "link": "https://wikipedia.org/wiki/Second_Continental_Congress"}, {"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "The first U.S. patent is issued, to inventor <PERSON> for a potash process.", "html": "1790 - The first U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> is issued, to inventor <a href=\"https://wikipedia.org/wiki/<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> for a <a href=\"https://wikipedia.org/wiki/Potash\" title=\"Potash\">potash</a> process.", "no_year_html": "The first U.S. <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> is issued, to inventor <a href=\"https://wikipedia.org/wiki/<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a> for a <a href=\"https://wikipedia.org/wiki/Potash\" title=\"Potash\">potash</a> process.", "links": [{"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)"}, {"title": "Potash", "link": "https://wikipedia.org/wiki/Potash"}]}, {"year": "1856", "text": "Christchurch, New Zealand, is chartered as a city.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Christchurch\" title=\"Christchurch\">Christchurch</a>, New Zealand, is chartered as a city.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christchurch\" title=\"Christchurch\">Christchurch</a>, New Zealand, is chartered as a city.", "links": [{"title": "Christchurch", "link": "https://wikipedia.org/wiki/Christchurch"}]}, {"year": "1865", "text": "The first narrow-gauge mainline railway in the world opens at Grandchester, Queensland, Australia.", "html": "1865 - The first <a href=\"https://wikipedia.org/wiki/Narrow-gauge_railway\" title=\"Narrow-gauge railway\">narrow-gauge</a> mainline railway in the world opens at <a href=\"https://wikipedia.org/wiki/Grandchester,_Queensland\" title=\"Grandchester, Queensland\">Grandchester, Queensland</a>, Australia.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Narrow-gauge_railway\" title=\"Narrow-gauge railway\">narrow-gauge</a> mainline railway in the world opens at <a href=\"https://wikipedia.org/wiki/Grandchester,_Queensland\" title=\"Grandchester, Queensland\">Grandchester, Queensland</a>, Australia.", "links": [{"title": "Narrow-gauge railway", "link": "https://wikipedia.org/wiki/Narrow-gauge_railway"}, {"title": "Grandchester, Queensland", "link": "https://wikipedia.org/wiki/Grandchester,_Queensland"}]}, {"year": "1874", "text": "<PERSON> became the first African-American inaugurated as president of a predominantly white university, Georgetown University.", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> became the first <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> inaugurated as president of a predominantly white university, <a href=\"https://wikipedia.org/wiki/Georgetown_University\" title=\"Georgetown University\">Georgetown University</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> became the first <a href=\"https://wikipedia.org/wiki/African-American\" class=\"mw-redirect\" title=\"African-American\">African-American</a> inaugurated as president of a predominantly white university, <a href=\"https://wikipedia.org/wiki/Georgetown_University\" title=\"Georgetown University\">Georgetown University</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "African-American", "link": "https://wikipedia.org/wiki/African-American"}, {"title": "Georgetown University", "link": "https://wikipedia.org/wiki/Georgetown_University"}]}, {"year": "1904", "text": "Russo-Japanese War: Battle of Hsimucheng: Units of the Imperial Japanese Army defeat units of the Imperial Russian Army in a strategic confrontation.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hsimucheng\" title=\"Battle of Hsimucheng\">Battle of Hsimucheng</a>: Units of the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> defeat units of the <a href=\"https://wikipedia.org/wiki/Imperial_Russian_Army\" title=\"Imperial Russian Army\">Imperial Russian Army</a> in a strategic confrontation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hsimucheng\" title=\"Battle of Hsimucheng\">Battle of Hsimucheng</a>: Units of the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> defeat units of the <a href=\"https://wikipedia.org/wiki/Imperial_Russian_Army\" title=\"Imperial Russian Army\">Imperial Russian Army</a> in a strategic confrontation.", "links": [{"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}, {"title": "Battle of Hsimucheng", "link": "https://wikipedia.org/wiki/Battle_of_Hsimucheng"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}, {"title": "Imperial Russian Army", "link": "https://wikipedia.org/wiki/Imperial_Russian_Army"}]}, {"year": "1917", "text": "World War I: The Battle of Passchendaele begins near Ypres in West Flanders, Belgium.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/The_Battle_of_Passchendaele\" class=\"mw-redirect\" title=\"The Battle of Passchendaele\">The Battle of Passchendaele</a> begins near <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a> in <a href=\"https://wikipedia.org/wiki/West_Flanders\" title=\"West Flanders\">West Flanders</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/The_Battle_of_Passchendaele\" class=\"mw-redirect\" title=\"The Battle of Passchendaele\">The Battle of Passchendaele</a> begins near <a href=\"https://wikipedia.org/wiki/Ypres\" title=\"Ypres\">Ypres</a> in <a href=\"https://wikipedia.org/wiki/West_Flanders\" title=\"West Flanders\">West Flanders</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "The Battle of Passchendaele", "link": "https://wikipedia.org/wiki/The_Battle_of_Passchendaele"}, {"title": "Ypres", "link": "https://wikipedia.org/wiki/Ypres"}, {"title": "West Flanders", "link": "https://wikipedia.org/wiki/West_Flanders"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}]}, {"year": "1932", "text": "The NSDAP (Nazi Party) wins more than 38% of the vote in German elections.", "html": "1932 - The NSDAP (<a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a>) wins more than 38% of the vote in <a href=\"https://wikipedia.org/wiki/German_federal_election,_July_1932\" class=\"mw-redirect\" title=\"German federal election, July 1932\">German elections</a>.", "no_year_html": "The NSDAP (<a href=\"https://wikipedia.org/wiki/Nazi_Party\" title=\"Nazi Party\">Nazi Party</a>) wins more than 38% of the vote in <a href=\"https://wikipedia.org/wiki/German_federal_election,_July_1932\" class=\"mw-redirect\" title=\"German federal election, July 1932\">German elections</a>.", "links": [{"title": "Nazi Party", "link": "https://wikipedia.org/wiki/Nazi_Party"}, {"title": "German federal election, July 1932", "link": "https://wikipedia.org/wiki/German_federal_election,_July_1932"}]}, {"year": "1938", "text": "Bulgaria signs a non-aggression pact with Greece and other states of Balkan Antanti (Turkey, Romania, Yugoslavia).", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> signs a non-aggression pact with Greece and other states of Balkan Antanti (<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Yugoslavia\" title=\"Yugoslavia\">Yugoslavia</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a> signs a non-aggression pact with Greece and other states of Balkan Antanti (<a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Yugoslavia\" title=\"Yugoslavia\">Yugoslavia</a>).", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Yugoslavia", "link": "https://wikipedia.org/wiki/Yugoslavia"}]}, {"year": "1938", "text": "Archaeologists discover engraved gold and silver plates from King <PERSON> the <PERSON> in Persepolis.", "html": "1938 - Archaeologists discover engraved <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> and <a href=\"https://wikipedia.org/wiki/Silver\" title=\"Silver\">silver</a> plates from <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_I\" class=\"mw-redirect\" title=\"<PERSON> I\"><PERSON> the Great</a> in <a href=\"https://wikipedia.org/wiki/Persepolis\" title=\"Persepolis\">Persepolis</a>.", "no_year_html": "Archaeologists discover engraved <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> and <a href=\"https://wikipedia.org/wiki/Silver\" title=\"Silver\">silver</a> plates from <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_I\" class=\"mw-redirect\" title=\"<PERSON> I\"><PERSON> the Great</a> in <a href=\"https://wikipedia.org/wiki/Persepolis\" title=\"Persepolis\">Persepolis</a>.", "links": [{"title": "Gold", "link": "https://wikipedia.org/wiki/Gold"}, {"title": "Silver", "link": "https://wikipedia.org/wiki/Silver"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Persepolis", "link": "https://wikipedia.org/wiki/Persepolis"}]}, {"year": "1941", "text": "The Holocaust: Under instructions from <PERSON>, Nazi official <PERSON> orders SS General <PERSON><PERSON><PERSON> to \"submit to me as soon as possible a general plan of the administrative material and financial measures necessary for carrying out the desired Final Solution of the Jewish question.\"", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: Under instructions from <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> official <a href=\"https://wikipedia.org/wiki/Hermann_G%C3%B6ring\" title=\"<PERSON>\"><PERSON></a> orders <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"<PERSON>hutzstaffel\">SS</a> General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to \"submit to me as soon as possible a general plan of the administrative material and financial measures necessary for carrying out the desired <a href=\"https://wikipedia.org/wiki/Final_Solution\" title=\"Final Solution\">Final Solution</a> of the <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish</a> question.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: Under instructions from <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi</a> official <a href=\"https://wikipedia.org/wiki/Hermann_G%C3%B6ring\" title=\"<PERSON>\"><PERSON></a> orders <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"<PERSON>hutzstaffel\">SS</a> General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to \"submit to me as soon as possible a general plan of the administrative material and financial measures necessary for carrying out the desired <a href=\"https://wikipedia.org/wiki/Final_Solution\" title=\"Final Solution\">Final Solution</a> of the <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jewish</a> question.\"", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hermann_G%C3%B6ring"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Final Solution", "link": "https://wikipedia.org/wiki/Final_Solution"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}]}, {"year": "1941", "text": "World War II: The Battle of Smolensk concludes with Germany capturing about 300,000 Soviet Red Army prisoners.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)\" title=\"Battle of Smolensk (1941)\">Battle of Smolensk</a> concludes with Germany capturing about 300,000 Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> prisoners.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)\" title=\"Battle of Smolensk (1941)\">Battle of Smolensk</a> concludes with Germany capturing about 300,000 Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> prisoners.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Smolensk (1941)", "link": "https://wikipedia.org/wiki/Battle_of_Smolensk_(1941)"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1945", "text": "<PERSON>, the fugitive former leader of Vichy France, surrenders to Allied soldiers in Austria.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the fugitive former leader of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy <PERSON></a>, surrenders to <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> soldiers in Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the fugitive former leader of <a href=\"https://wikipedia.org/wiki/Vichy_France\" title=\"Vichy France\">Vichy <PERSON></a>, surrenders to <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> soldiers in Austria.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vichy_France"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1948", "text": "At Idlewild Field in New York, New York International Airport (later renamed John F. Kennedy International Airport) is dedicated.", "html": "1948 - At Idlewild Field in New York, New York International Airport (later renamed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>) is dedicated.", "no_year_html": "At Idlewild Field in New York, New York International Airport (later renamed <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>) is dedicated.", "links": [{"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}]}, {"year": "1948", "text": "USS Nevada is sunk by an aerial torpedo after surviving hits from two atomic bombs (as part of post-war tests) and being used for target practice by three other ships.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/USS_Nevada_(BB-36)\" title=\"USS Nevada (BB-36)\">USS <i>Nevada</i></a> is sunk by an aerial torpedo after surviving hits from two atomic bombs (as part of post-war tests) and being used for target practice by three other ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/USS_Nevada_(BB-36)\" title=\"USS Nevada (BB-36)\">USS <i>Nevada</i></a> is sunk by an aerial torpedo after surviving hits from two atomic bombs (as part of post-war tests) and being used for target practice by three other ships.", "links": [{"title": "USS Nevada (BB-36)", "link": "https://wikipedia.org/wiki/USS_Nevada_(BB-36)"}]}, {"year": "1964", "text": "Ranger program: Ranger 7 sends back the first close-up photographs of the moon, with images 1,000 times clearer than anything ever seen from earth-bound telescopes.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Ranger program</a>: <a href=\"https://wikipedia.org/wiki/Ranger_7\" title=\"Ranger 7\">Ranger 7</a> sends back the first close-up photographs of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">moon</a>, with images 1,000 times clearer than anything ever seen from earth-bound <a href=\"https://wikipedia.org/wiki/Telescope\" title=\"Telescope\">telescopes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranger_program\" title=\"Ranger program\">Ranger program</a>: <a href=\"https://wikipedia.org/wiki/Ranger_7\" title=\"Ranger 7\">Ranger 7</a> sends back the first close-up photographs of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">moon</a>, with images 1,000 times clearer than anything ever seen from earth-bound <a href=\"https://wikipedia.org/wiki/Telescope\" title=\"Telescope\">telescopes</a>.", "links": [{"title": "Ranger program", "link": "https://wikipedia.org/wiki/Ranger_program"}, {"title": "Ranger 7", "link": "https://wikipedia.org/wiki/Ranger_7"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Telescope", "link": "https://wikipedia.org/wiki/Telescope"}]}, {"year": "1966", "text": "The pleasure cruiser MV Darlwyne disappeared off the Cornwall coast with the loss of all 31 aboard.", "html": "1966 - The pleasure cruiser MV <i>Darlwyne</i> <a href=\"https://wikipedia.org/wiki/Loss_of_MV_Darlwyne\" title=\"Loss of MV Darlwyne\">disappeared</a> off the <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> coast with the loss of all 31 aboard.", "no_year_html": "The pleasure cruiser MV <i>Darlwyne</i> <a href=\"https://wikipedia.org/wiki/Loss_of_MV_Darlwyne\" title=\"Loss of MV Darlwyne\">disappeared</a> off the <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> coast with the loss of all 31 aboard.", "links": [{"title": "Loss of MV Darlwyne", "link": "https://wikipedia.org/wiki/Loss_of_MV_Darlwyne"}, {"title": "Cornwall", "link": "https://wikipedia.org/wiki/Cornwall"}]}, {"year": "1970", "text": "Black Tot Day: The last day of the officially sanctioned rum ration in the Royal Navy.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Black_Tot_Day\" title=\"Black Tot Day\">Black Tot Day</a>: The last day of the officially sanctioned <a href=\"https://wikipedia.org/wiki/Rum#Naval_rum\" title=\"Rum\">rum ration</a> in the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Tot_Day\" title=\"Black Tot Day\">Black Tot Day</a>: The last day of the officially sanctioned <a href=\"https://wikipedia.org/wiki/Rum#Naval_rum\" title=\"Rum\">rum ration</a> in the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a>.", "links": [{"title": "Black Tot Day", "link": "https://wikipedia.org/wiki/Black_Tot_Day"}, {"title": "Rum", "link": "https://wikipedia.org/wiki/Rum#Naval_rum"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}]}, {"year": "1971", "text": "Apollo program: the Apollo 15 astronauts become the first to ride in a lunar rover.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: the <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a> astronauts become the first to ride in a <a href=\"https://wikipedia.org/wiki/Lunar_rover\" title=\"Lunar rover\">lunar rover</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: the <a href=\"https://wikipedia.org/wiki/Apollo_15\" title=\"Apollo 15\">Apollo 15</a> astronauts become the first to ride in a <a href=\"https://wikipedia.org/wiki/Lunar_rover\" title=\"Lunar rover\">lunar rover</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 15", "link": "https://wikipedia.org/wiki/Apollo_15"}, {"title": "Lunar rover", "link": "https://wikipedia.org/wiki/Lunar_rover"}]}, {"year": "1972", "text": "The Troubles: In Operation Motorman, the British Army re-takes the urban no-go areas of Northern Ireland. It is the biggest British military operation since the Suez Crisis of 1956, and the biggest in Ireland since the Irish War of Independence. Later that day, nine civilians are killed by car bombs in the village of Claudy.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In <a href=\"https://wikipedia.org/wiki/Operation_Motorman\" title=\"Operation Motorman\">Operation Motorman</a>, the British Army re-takes the urban <a href=\"https://wikipedia.org/wiki/No-go_area\" title=\"No-go area\">no-go areas</a> of <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>. It is the biggest British military operation since the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a> of 1956, and the biggest in Ireland since the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>. Later that day, nine civilians are <a href=\"https://wikipedia.org/wiki/Claudy_bombing\" title=\"Claudy bombing\">killed by car bombs</a> in the village of <a href=\"https://wikipedia.org/wiki/Claudy\" title=\"Claudy\">Claudy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: In <a href=\"https://wikipedia.org/wiki/Operation_Motorman\" title=\"Operation Motorman\">Operation Motorman</a>, the British Army re-takes the urban <a href=\"https://wikipedia.org/wiki/No-go_area\" title=\"No-go area\">no-go areas</a> of <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>. It is the biggest British military operation since the <a href=\"https://wikipedia.org/wiki/Suez_Crisis\" title=\"Suez Crisis\">Suez Crisis</a> of 1956, and the biggest in Ireland since the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a>. Later that day, nine civilians are <a href=\"https://wikipedia.org/wiki/Claudy_bombing\" title=\"Claudy bombing\">killed by car bombs</a> in the village of <a href=\"https://wikipedia.org/wiki/Claudy\" title=\"Claudy\">Clau<PERSON></a>.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Operation Motorman", "link": "https://wikipedia.org/wiki/Operation_Motorman"}, {"title": "No-go area", "link": "https://wikipedia.org/wiki/No-go_area"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Suez Crisis", "link": "https://wikipedia.org/wiki/Suez_Crisis"}, {"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}, {"title": "C<PERSON>dy bombing", "link": "https://wikipedia.org/wiki/<PERSON>lau<PERSON>_bombing"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>dy"}]}, {"year": "1973", "text": "A Delta Air Lines jetliner, flight DL 723 crashes while landing in fog at Logan International Airport, Boston, Massachusetts killing 89.", "html": "1973 - A <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> jetliner, flight <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines_Flight_723\" title=\"Delta Air Lines Flight 723\">DL 723</a> crashes while landing in fog at <a href=\"https://wikipedia.org/wiki/Logan_International_Airport\" title=\"Logan International Airport\">Logan International Airport</a>, Boston, Massachusetts killing 89.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines\" title=\"Delta Air Lines\">Delta Air Lines</a> jetliner, flight <a href=\"https://wikipedia.org/wiki/Delta_Air_Lines_Flight_723\" title=\"Delta Air Lines Flight 723\">DL 723</a> crashes while landing in fog at <a href=\"https://wikipedia.org/wiki/Logan_International_Airport\" title=\"Logan International Airport\">Logan International Airport</a>, Boston, Massachusetts killing 89.", "links": [{"title": "Delta Air Lines", "link": "https://wikipedia.org/wiki/Delta_Air_Lines"}, {"title": "Delta Air Lines Flight 723", "link": "https://wikipedia.org/wiki/Delta_Air_Lines_Flight_723"}, {"title": "Logan International Airport", "link": "https://wikipedia.org/wiki/Logan_International_Airport"}]}, {"year": "1975", "text": "The Troubles: Three members of a popular cabaret band and two gunmen are killed during a botched paramilitary attack in Northern Ireland.", "html": "1975 - The Troubles: Three members of a popular cabaret band and two gunmen <a href=\"https://wikipedia.org/wiki/Miami_Showband_killings\" title=\"Miami Showband killings\">are killed during a botched paramilitary attack</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "The Troubles: Three members of a popular cabaret band and two gunmen <a href=\"https://wikipedia.org/wiki/Miami_Showband_killings\" title=\"Miami Showband killings\">are killed during a botched paramilitary attack</a> in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "Miami Showband killings", "link": "https://wikipedia.org/wiki/Miami_Showband_killings"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1987", "text": "A tornado occurs in Edmonton, Alberta, killing 27 people.", "html": "1987 - A <a href=\"https://wikipedia.org/wiki/Edmonton_tornado\" title=\"Edmonton tornado\">tornado</a> occurs in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a>, <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>, killing 27 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Edmonton_tornado\" title=\"Edmonton tornado\">tornado</a> occurs in <a href=\"https://wikipedia.org/wiki/Edmonton\" title=\"Edmonton\">Edmonton</a>, <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>, killing 27 people.", "links": [{"title": "Edmonton tornado", "link": "https://wikipedia.org/wiki/Edmonton_tornado"}, {"title": "Edmonton", "link": "https://wikipedia.org/wiki/Edmonton"}, {"title": "Alberta", "link": "https://wikipedia.org/wiki/Alberta"}]}, {"year": "1988", "text": "Thirty-two people are killed and 1,674 injured when a bridge at the Sultan <PERSON> ferry terminal collapses in Butterworth, Penang, Malaysia.", "html": "1988 - Thirty-two people are killed and 1,674 injured when a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ferry_terminal_bridge_collapse\" title=\"<PERSON> ferry terminal bridge collapse\">bridge at the <PERSON> ferry terminal collapses</a> in <a href=\"https://wikipedia.org/wiki/Butterworth,_Penang\" class=\"mw-redirect\" title=\"Butterworth, Penang\">Butterworth, Penang</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "no_year_html": "Thirty-two people are killed and 1,674 injured when a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ferry_terminal_bridge_collapse\" title=\"<PERSON> ferry terminal bridge collapse\">bridge at the <PERSON> ferry terminal collapses</a> in <a href=\"https://wikipedia.org/wiki/Butterworth,_Penang\" class=\"mw-redirect\" title=\"Butterworth, Penang\">Butterworth, Penang</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>.", "links": [{"title": "<PERSON> ferry terminal bridge collapse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_ferry_terminal_bridge_collapse"}, {"title": "Butterworth, Penang", "link": "https://wikipedia.org/wiki/Butterworth,_Penang"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}]}, {"year": "1991", "text": "The United States and Soviet Union both sign the START I Strategic Arms Reduction Treaty, the first to reduce (with verification) both countries' stockpiles.", "html": "1991 - The United States and <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> both sign the <a href=\"https://wikipedia.org/wiki/START_I\" title=\"START I\">START I</a> Strategic Arms Reduction Treaty, the first to reduce (with verification) both countries' stockpiles.", "no_year_html": "The United States and <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> both sign the <a href=\"https://wikipedia.org/wiki/START_I\" title=\"START I\">START I</a> Strategic Arms Reduction Treaty, the first to reduce (with verification) both countries' stockpiles.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "START I", "link": "https://wikipedia.org/wiki/START_I"}]}, {"year": "1992", "text": "The nation of Georgia joins the United Nations.", "html": "1992 - The nation of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "The nation of <a href=\"https://wikipedia.org/wiki/Georgia_(country)\" title=\"Georgia (country)\">Georgia</a> joins the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Georgia (country)", "link": "https://wikipedia.org/wiki/Georgia_(country)"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1992", "text": "Thai Airways International Flight 311 crashes into a mountain north of Kathmandu, Nepal killing all 113 people on board.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Thai_Airways_International_Flight_311\" title=\"Thai Airways International Flight 311\">Thai Airways International Flight 311</a> crashes into a mountain north of <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a>, <a href=\"https://wikipedia.org/wiki/Nepal\" title=\"Nepal\">Nepal</a> killing all 113 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thai_Airways_International_Flight_311\" title=\"Thai Airways International Flight 311\">Thai Airways International Flight 311</a> crashes into a mountain north of <a href=\"https://wikipedia.org/wiki/Kathmandu\" title=\"Kathmandu\">Kathmandu</a>, <a href=\"https://wikipedia.org/wiki/Nepal\" title=\"Nepal\">Nepal</a> killing all 113 people on board.", "links": [{"title": "Thai Airways International Flight 311", "link": "https://wikipedia.org/wiki/Thai_Airways_International_Flight_311"}, {"title": "Kathman<PERSON>", "link": "https://wikipedia.org/wiki/Kathmandu"}, {"title": "Nepal", "link": "https://wikipedia.org/wiki/Nepal"}]}, {"year": "1992", "text": "China General Aviation Flight 7552 crashes during takeoff from Nanjing Dajiaochang Airport, killing 108.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/China_General_Aviation_Flight_7552\" title=\"China General Aviation Flight 7552\">China General Aviation Flight 7552</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Nanjing_Dajiaochang_Airport\" title=\"Nanjing Dajiaochang Airport\">Nanjing Dajiaochang Airport</a>, killing 108.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_General_Aviation_Flight_7552\" title=\"China General Aviation Flight 7552\">China General Aviation Flight 7552</a> crashes during takeoff from <a href=\"https://wikipedia.org/wiki/Nanjing_Dajiaochang_Airport\" title=\"Nanjing Dajiaochang Airport\">Nanjing Dajiaochang Airport</a>, killing 108.", "links": [{"title": "China General Aviation Flight 7552", "link": "https://wikipedia.org/wiki/China_General_Aviation_Flight_7552"}, {"title": "Nanjing Dajiaochang Airport", "link": "https://wikipedia.org/wiki/Nanjing_Dajiaochang_Airport"}]}, {"year": "1992", "text": "Space Shuttle program: Atlantis is launched on STS-46 to deploy the European Retrievable Carrier and the Tethered Satellite System.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\"><i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-46\" title=\"STS-46\">STS-46</a> to deploy the <a href=\"https://wikipedia.org/wiki/European_Retrievable_Carrier\" title=\"European Retrievable Carrier\">European Retrievable Carrier</a> and the <a href=\"https://wikipedia.org/wiki/Space_tether\" title=\"Space tether\">Tethered Satellite System</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\"><i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-46\" title=\"STS-46\">STS-46</a> to deploy the <a href=\"https://wikipedia.org/wiki/European_Retrievable_Carrier\" title=\"European Retrievable Carrier\">European Retrievable Carrier</a> and the <a href=\"https://wikipedia.org/wiki/Space_tether\" title=\"Space tether\">Tethered Satellite System</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-46", "link": "https://wikipedia.org/wiki/STS-46"}, {"title": "European Retrievable Carrier", "link": "https://wikipedia.org/wiki/European_Retrievable_Carrier"}, {"title": "Space tether", "link": "https://wikipedia.org/wiki/Space_tether"}]}, {"year": "1997", "text": "FedEx Express Flight 14 crashes at Newark International Airport, injuring five.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/FedEx_Express_Flight_14\" title=\"FedEx Express Flight 14\">FedEx Express Flight 14</a> crashes at <a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark International Airport</a>, injuring five.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FedEx_Express_Flight_14\" title=\"FedEx Express Flight 14\">FedEx Express Flight 14</a> crashes at <a href=\"https://wikipedia.org/wiki/Newark_Liberty_International_Airport\" title=\"Newark Liberty International Airport\">Newark International Airport</a>, injuring five.", "links": [{"title": "FedEx Express Flight 14", "link": "https://wikipedia.org/wiki/FedEx_Express_Flight_14"}, {"title": "Newark Liberty International Airport", "link": "https://wikipedia.org/wiki/Newark_Liberty_International_Airport"}]}, {"year": "1999", "text": "Discovery Program: Lunar Prospector: NASA intentionally crashes the spacecraft into the Moon, thus ending its mission to detect frozen water on the Moon's surface.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Discovery_Program\" title=\"Discovery Program\">Discovery Program</a>: <i><a href=\"https://wikipedia.org/wiki/Lunar_Prospector\" title=\"Lunar Prospector\">Lunar Prospector</a></i>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> intentionally crashes the spacecraft into the Moon, thus ending its mission to detect frozen water on the Moon's surface.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Discovery_Program\" title=\"Discovery Program\">Discovery Program</a>: <i><a href=\"https://wikipedia.org/wiki/Lunar_Prospector\" title=\"Lunar Prospector\">Lunar Prospector</a></i>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> intentionally crashes the spacecraft into the Moon, thus ending its mission to detect frozen water on the Moon's surface.", "links": [{"title": "Discovery Program", "link": "https://wikipedia.org/wiki/Discovery_Program"}, {"title": "Lunar Prospector", "link": "https://wikipedia.org/wiki/Lunar_Prospector"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}]}, {"year": "2006", "text": "<PERSON><PERSON> hands over power to his brother, <PERSON><PERSON>.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> hands over power to his brother, <a href=\"https://wikipedia.org/wiki/Ra%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> hands over power to his brother, <a href=\"https://wikipedia.org/wiki/Ra%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Castro"}]}, {"year": "2007", "text": "Operation Banner, the presence of the British Army in Northern Ireland, and the longest-running British Army operation ever, comes to an end.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>, the presence of the British Army in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, and the longest-running British Army operation ever, comes to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>, the presence of the British Army in <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, and the longest-running British Army operation ever, comes to an end.", "links": [{"title": "Operation Banner", "link": "https://wikipedia.org/wiki/Operation_Banner"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "2008", "text": "East Coast Jets Flight 81 crashes near Owatonna Degner Regional Airport in Owatonna, Minnesota, killing all eight people on board.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/East_Coast_Jets_Flight_81\" title=\"East Coast Jets Flight 81\">East Coast Jets Flight 81</a> crashes near <a href=\"https://wikipedia.org/wiki/Owatonna_Degner_Regional_Airport\" title=\"Owatonna Degner Regional Airport\">Owatonna Degner Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Owatonna,_Minnesota\" title=\"Owatonna, Minnesota\">Owatonna, Minnesota</a>, killing all eight people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Coast_Jets_Flight_81\" title=\"East Coast Jets Flight 81\">East Coast Jets Flight 81</a> crashes near <a href=\"https://wikipedia.org/wiki/Owatonna_Degner_Regional_Airport\" title=\"Owatonna Degner Regional Airport\">Owatonna Degner Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Owatonna,_Minnesota\" title=\"Owatonna, Minnesota\">Owatonna, Minnesota</a>, killing all eight people on board.", "links": [{"title": "East Coast Jets Flight 81", "link": "https://wikipedia.org/wiki/East_Coast_Jets_Flight_81"}, {"title": "Owatonna Degner Regional Airport", "link": "https://wikipedia.org/wiki/Owatonna_Degner_Regional_Airport"}, {"title": "Owatonna, Minnesota", "link": "https://wikipedia.org/wiki/Owatonna,_Minnesota"}]}, {"year": "2012", "text": "<PERSON> breaks the record set in 1964 by <PERSON><PERSON><PERSON> for the most medals won at the Olympics.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the record set in 1964 by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> for the <a href=\"https://wikipedia.org/wiki/List_of_multiple_Olympic_medalists\" title=\"List of multiple Olympic medalists\">most medals won at the Olympics</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> breaks the record set in 1964 by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> for the <a href=\"https://wikipedia.org/wiki/List_of_multiple_Olympic_medalists\" title=\"List of multiple Olympic medalists\">most medals won at the Olympics</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of multiple Olympic medalists", "link": "https://wikipedia.org/wiki/List_of_multiple_Olympic_medalists"}]}, {"year": "2014", "text": "Gas explosions in the southern Taiwanese city of Kaohsiung kill at least 20 people and injure more than 270.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/2014_Kaohsiung_gas_explosions\" title=\"2014 Kaohsiung gas explosions\">Gas explosions</a> in the southern Taiwanese city of <a href=\"https://wikipedia.org/wiki/Kaohsiung\" title=\"Kaohsiung\">Ka<PERSON><PERSON><PERSON></a> kill at least 20 people and injure more than 270.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2014_Kaohsiung_gas_explosions\" title=\"2014 Kaohsiung gas explosions\">Gas explosions</a> in the southern Taiwanese city of <a href=\"https://wikipedia.org/wiki/Kaohsiung\" title=\"Kaohsiung\"><PERSON><PERSON><PERSON><PERSON></a> kill at least 20 people and injure more than 270.", "links": [{"title": "2014 Kaohsiung gas explosions", "link": "https://wikipedia.org/wiki/2014_Kaohsiung_gas_explosions"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}], "Births": [{"year": "1143", "text": "Emperor <PERSON><PERSON> of Japan (d. 1165)", "html": "1143 - <a href=\"https://wikipedia.org/wiki/Emperor_Nij%C5%8D\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (d. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Nij%C5%8D\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (d. 1165)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Nij%C5%8D"}]}, {"year": "1396", "text": "<PERSON>, Duke of Burgundy (d. 1467)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (d. 1467)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (d. 1467)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1526", "text": "<PERSON>, Elector of Saxony (d. 1586)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1586)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1527", "text": "<PERSON>, Holy Roman Emperor (d. 1576)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1576)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1595", "text": "<PERSON>, Count of Hanau-Lichtenberg (d. 1641)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Hanau-Lichtenberg\" title=\"<PERSON>, Count of Hanau-Lichtenberg\"><PERSON>, Count of Hanau-Lichtenberg</a> (d. 1641)", "links": [{"title": "<PERSON>, Count of Hanau-Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1598", "text": "<PERSON>, Italian sculptor (d. 1654)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor (d. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON> of France, Duke of Berry (d. 1714)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Berry_(1686%E2%80%931714)\" title=\"<PERSON>, Duke of Berry (1686-1714)\"><PERSON> of France, Duke of Berry</a> (d. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Berry_(1686%E2%80%931714)\" title=\"<PERSON>, Duke of Berry (1686-1714)\"><PERSON> of France, Duke of Berry</a> (d. 1714)", "links": [{"title": "<PERSON>, Duke of Berry (1686-1714)", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_<PERSON>_(1686%E2%80%931714)"}]}, {"year": "1702", "text": "<PERSON>, French missionary and painter (d. 1768)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and painter (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and painter (d. 1768)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1704", "text": "<PERSON>, Swiss mathematician and physicist (d. 1752)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and physicist (d. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and physicist (d. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON>, English physicist and academic (d. 1772)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/John_Canton\" title=\"John Canton\"><PERSON></a>, English physicist and academic (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Canton\" title=\"John Canton\"><PERSON></a>, English physicist and academic (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_Canton"}]}, {"year": "1724", "text": "<PERSON><PERSON>, French lexicographer and author (d. 1801)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Fran%C3%A<PERSON><PERSON>_de_<PERSON>ailly\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and author (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Fran%C3%A<PERSON><PERSON>_de_<PERSON>ail<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lexicographer and author (d. 1801)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Fran%C3%A<PERSON><PERSON>_de_W<PERSON>ly"}]}, {"year": "1759", "text": "<PERSON><PERSON><PERSON>, Austrian nobleman and government official (d. 1796)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Austrian nobleman and government official (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Austrian nobleman and government official (d. 1796)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Argentine priest and politician (d. 1849)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine priest and politician (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine priest and politician (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech-French actor and mime (d. 1846)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-French actor and mime (d. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech-French actor and mime (d. 1846)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, German chemist and academic (d. 1882)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hler\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Friedrich_W%C3%B6hler"}]}, {"year": "1803", "text": "<PERSON>, Swedish-American engineer, co-designed the USS Princeton and the Novelty Locomotive (d. 1889)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer, co-designed the <a href=\"https://wikipedia.org/wiki/USS_Princeton_(1843)\" title=\"USS Princeton (1843)\">USS Princeton</a> and the <a href=\"https://wikipedia.org/wiki/Novelty_(locomotive)\" title=\"Novelty (locomotive)\">Novelty Locomotive</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-American engineer, co-designed the <a href=\"https://wikipedia.org/wiki/USS_Princeton_(1843)\" title=\"USS Princeton (1843)\">USS Princeton</a> and the <a href=\"https://wikipedia.org/wiki/Novelty_(locomotive)\" title=\"<PERSON><PERSON> (locomotive)\">Novelty Locomotive</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "USS Princeton (1843)", "link": "https://wikipedia.org/wiki/USS_Princeton_(1843)"}, {"title": "Novelty (locomotive)", "link": "https://wikipedia.org/wiki/Novelty_(locomotive)"}]}, {"year": "1816", "text": "<PERSON>, American general (d. 1870)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON>, Finnish serial killer (d. 1854)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>poika\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Finnish serial killer (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Finnish serial killer (d. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ika"}]}, {"year": "1826", "text": "<PERSON>, American colonel and politician (d. 1886)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, French lawyer and politician, 50th Prime Minister of France (d. 1912)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 50th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1835", "text": "<PERSON>, French-American anthropologist and explorer (d. 1903)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American anthropologist and explorer (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American anthropologist and explorer (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON>, Russian author and activist (d. 1878)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and activist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author and activist (d. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, American captain (d. 1865)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, Venezuelan general and politician, 25th President of Venezuela (d. 1925)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan general and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (d. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1843", "text": "<PERSON>, Austrian poet and author (d. 1918)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, Cuban pianist and composer (d. 1905)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and composer (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban pianist and composer (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Spanish academic and politician, Prime Minister of Spain (d. 1912)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Canalejas_y_M%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Spanish academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Canalejas_y_M%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, Spanish academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Canalejas_y_M%C3%A9ndez"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1854", "text": "<PERSON>, 15th president of Liberia (d. 1938)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 15th president of Liberia (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 15th president of Liberia (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, English seismologist and geologist (d. 1936)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English seismologist and geologist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English seismologist and geologist (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, influential American educator (d. 1948)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, influential American educator (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, influential American educator (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American painter and illustrator (d. 1940)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businessman, founded Kmart (d. 1966)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/S._S._Kresge\" title=\"S. S. Kresge\">S. S. K<PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Kmart_(United_States)\" class=\"mw-redirect\" title=\"Kmart (United States)\">Kmart</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._S._Kresge\" title=\"S. S. Kresge\">S. S. K<PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Kmart_(United_States)\" class=\"mw-redirect\" title=\"Kmart (United States)\">K<PERSON></a> (d. 1966)", "links": [{"title": "S. S. Kresge", "link": "https://wikipedia.org/wiki/S._S._Kresge"}, {"title": "Kmart (United States)", "link": "https://wikipedia.org/wiki/Kmart_(United_States)"}]}, {"year": "1875", "text": "<PERSON>, French painter (d. 1963)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, South African botanist and taxonomist (d. 1970)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African botanist and taxonomist (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African botanist and taxonomist (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and playwright (d. 1936)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Premchand\" title=\"Premchand\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and playwright (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Premchand\" title=\"Premchand\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author and playwright (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Premchand"}]}, {"year": "1883", "text": "<PERSON>, Cuban fencer (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Fonst\" title=\"<PERSON>\"><PERSON></a>, Cuban fencer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Fonst\" title=\"<PERSON>\"><PERSON></a>, Cuban fencer (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Fonst"}]}, {"year": "1884", "text": "<PERSON>, Polish-German economist and politician (d. 1945)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German economist and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-German economist and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Italian-American mob boss (d. 1931)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mob boss (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American animation producer (d. 1965)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animation producer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animation producer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German sociologist and philosopher (d. 1969)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American evangelist and publisher, founded Worldwide Church of God (d. 1986)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Worldwide_Church_of_God\" class=\"mw-redirect\" title=\"Worldwide Church of God\">Worldwide Church of God</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and publisher, founded <a href=\"https://wikipedia.org/wiki/Worldwide_Church_of_God\" class=\"mw-redirect\" title=\"Worldwide Church of God\">Worldwide Church of God</a> (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Worldwide Church of God", "link": "https://wikipedia.org/wiki/Worldwide_Church_of_God"}]}, {"year": "1892", "text": "<PERSON>, Canadian archbishop (d. 1959)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Welsh footballer (d. 1972)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, French painter and sculptor (d. 1985)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Australian-English cricketer and soldier (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-English cricketer and soldier (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Australian-English cricketer and soldier (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American engineer, surveyor, and author (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, surveyor, and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, surveyor, and author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Austrian theorist and author (d. 1999)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian theorist and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Austrian theorist and author (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American violinist (d. 1983)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Australian cricketer (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer (d. 2008)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1912", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (d. 2006)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, American football player and journalist (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\">Ir<PERSON></a>, American football player and journalist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irv_<PERSON>\" title=\"Irv <PERSON>\">Ir<PERSON></a>, American football player and journalist (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Irv_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Canadian ice hockey player (d. 1984)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1914", "text": "<PERSON>, American conductor and composer (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and composer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Italian director and screenwriter (d. 1980)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French actor and screenwriter (d. 1983)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fun%C3%A8s"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Pakistani journalist, scholar, and activist (d. 1986)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani journalist, scholar, and activist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani journalist, scholar, and activist (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American baseball player, coach, and manager (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American screenwriter and producer (d. 1979)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2018)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1918", "text": "<PERSON>, American pianist, composer, and bandleader (d. 2010)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and bandleader (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, New Zealand businessman and financier (d. 1998)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and financier (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman and financier (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Indian cricketer (d. 2003)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American sportscaster and actor (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wdy\" title=\"<PERSON><PERSON> Gowdy\"><PERSON><PERSON></a>, American sportscaster and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wdy\" title=\"<PERSON><PERSON> Gowdy\"><PERSON><PERSON></a>, American sportscaster and actor (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wdy"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Italian chemist and author (d. 1987)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Primo Levi\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian chemist and author (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Primo Levi\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian chemist and author (d. 1987)", "links": [{"title": "P<PERSON><PERSON> Levi", "link": "https://wikipedia.org/wiki/Primo_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American religious leader, lawyer, and politician (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, lawyer, and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, lawyer, and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English lawyer and activist, founded Amnesty International (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and activist, founded <a href=\"https://wikipedia.org/wiki/Amnesty_International\" title=\"Amnesty International\">Amnesty International</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and activist, founded <a href=\"https://wikipedia.org/wiki/Amnesty_International\" title=\"Amnesty International\">Amnesty International</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Amnesty International", "link": "https://wikipedia.org/wiki/Amnesty_International"}]}, {"year": "1921", "text": "<PERSON>, American sergeant and author (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American activist (d. 1971)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, American activist (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American baseball player and manager (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Turkish-American songwriter and producer, founded Atlantic Records (d. 2006)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gun\" title=\"<PERSON>met Ertegun\"><PERSON><PERSON></a>, Turkish-American songwriter and producer, founded <a href=\"https://wikipedia.org/wiki/Atlantic_Records\" title=\"Atlantic Records\">Atlantic Records</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_E<PERSON>gun\" title=\"<PERSON><PERSON> Ertegun\"><PERSON><PERSON></a>, Turkish-American songwriter and producer, founded <a href=\"https://wikipedia.org/wiki/Atlantic_Records\" title=\"Atlantic Records\">Atlantic Records</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Atlantic Records", "link": "https://wikipedia.org/wiki/Atlantic_Records"}]}, {"year": "1923", "text": "<PERSON>, American chemist and engineer, invented <PERSON><PERSON><PERSON> (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Kevlar\" title=\"Kevlar\">Ke<PERSON><PERSON></a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and engineer, invented <a href=\"https://wikipedia.org/wiki/Kevlar\" title=\"Kevlar\"><PERSON><PERSON><PERSON></a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kevlar"}]}, {"year": "1924", "text": "<PERSON>, American tennis player and coach (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Irish singer, actress and writer (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, actress and writer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Quinn\"><PERSON></a>, Irish singer, actress and writer (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Quinn"}]}, {"year": "1925", "text": "<PERSON>, Canadian-American jurist and politician, 42nd Governor of Michigan (d. 1994)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jurist and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American jurist and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1926", "text": "<PERSON>, American physician and activist (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and activist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American mathematician, computer scientist, and philosopher (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and philosopher (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, computer scientist, and philosopher (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English author and playwright (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English author and playwright (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English author and playwright (d. 2019)", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_(playwright)"}]}, {"year": "1928", "text": "<PERSON>, American lieutenant and politician (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, English author (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian director and screenwriter (d. 2009)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American actor (d. 2024)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1929", "text": "<PERSON>, Uruguayan footballer and manager", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Santamar%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Santamar%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Santamar%C3%ADa"}]}, {"year": "1931", "text": "<PERSON>, American tennis player and coach (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor and screenwriter (d. 1979)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American philosopher and academic", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Dutch journalist, author, and poet", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, author, and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch journalist, author, and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Canadian comedian, actor, and producer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian comedian, actor, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor and screenwriter (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, English pianist and conductor (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Steuart_Bedford\" title=\"Steuart Bedford\"><PERSON><PERSON><PERSON></a>, English pianist and conductor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Steuart_Bedford\" title=\"Steuart Bedford\"><PERSON><PERSON><PERSON></a>, English pianist and conductor (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>art_Bedford"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Vietnamese-French actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/France_Nuyen\" title=\"France Nuyen\"><PERSON> Nuyen</a>, Vietnamese-French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Nuyen\" title=\"France Nuyen\">France Nuyen</a>, Vietnamese-French actress", "links": [{"title": "France Nuyen", "link": "https://wikipedia.org/wiki/France_Nuyen"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Indian politician, 8th Chief Minister of Gujarat (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hary"}, {"title": "Chief Minister of Gujarat", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Gujarat"}]}, {"year": "1943", "text": "<PERSON>, American journalist and politician, 3rd United States Secretary of Education", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Education\" title=\"United States Secretary of Education\">United States Secretary of Education</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of Education", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Education"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American actress and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American film producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sherry <PERSON>\"><PERSON><PERSON></a>, American film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"She<PERSON>\"><PERSON><PERSON></a>, American film producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American economist and academic, Nobel Prize laureate", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1944", "text": "<PERSON>, Irish scholar and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish scholar and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Irish scholar and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}]}, {"year": "1945", "text": "<PERSON>, American lawyer and politician, 68th Governor of Massachusetts", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1946", "text": "<PERSON>, American pop-rock musician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pop-rock musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American pop-rock musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1947", "text": "<PERSON>, English bass player and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English actor (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Indian_actress)\" title=\"<PERSON><PERSON><PERSON> (Indian actress)\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Indian_actress)\" title=\"<PERSON><PERSON><PERSON> (Indian actress)\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON> (Indian actress)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Indian_actress)"}]}, {"year": "1947", "text": "<PERSON>, French politician, French Minister of Foreign Affairs", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9drine\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_Foreign_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Foreign Ministers of France\">French Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9drine\" title=\"<PERSON>\"><PERSON></a>, French politician, <a href=\"https://wikipedia.org/wiki/List_of_Foreign_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Foreign Ministers of France\">French Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hubert_V%C3%A9drine"}, {"title": "List of Foreign Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Foreign_Ministers_of_France"}]}, {"year": "1947", "text": "<PERSON>, English children's illustrator and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's illustrator and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English children's illustrator and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Morris\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1949", "text": "<PERSON>, English journalist and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Australian tennis player", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1952", "text": "<PERSON>, American football player, actor, and politician, 23rd Mayor of Fresno, California", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Fresno,_California\" class=\"mw-redirect\" title=\"Mayor of Fresno, California\">Mayor of Fresno, California</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and politician, 23rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Fresno,_California\" class=\"mw-redirect\" title=\"Mayor of Fresno, California\">Mayor of Fresno, California</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Fresno, California", "link": "https://wikipedia.org/wiki/Mayor_of_Fresno,_California"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Latvian ice hockey player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Portuguese author and critic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Barreiros\" title=\"<PERSON>\"><PERSON></a>, Portuguese author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Barreiros\" title=\"<PERSON>\"><PERSON></a>, Portuguese author and critic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Barreiros"}]}, {"year": "1952", "text": "<PERSON>, American author", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian architect and politician, 46th Premier of Victoria", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and politician, 46th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian architect and politician, 46th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1953", "text": "<PERSON>, South African cricketer and coach", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English cellist (d. 2018)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian ice hockey player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1954)\" title=\"<PERSON> (ice hockey, born 1954)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1954)\" title=\"<PERSON> (ice hockey, born 1954)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1954)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1954)"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American football player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and radio host", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American lawyer and politician, 71st Governor of Massachusetts", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 71st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 71st <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American author and illustrator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, American online celebrity chef", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Lynja\" title=\"Lynja\"><PERSON><PERSON><PERSON></a>, American online celebrity chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynja\" title=\"Lynja\"><PERSON><PERSON><PERSON></a>, American online celebrity chef", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lynja"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English business executive", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(media_executive)\" title=\"<PERSON> (media executive)\"><PERSON></a>, English business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(media_executive)\" title=\"<PERSON> (media executive)\"><PERSON></a>, English business executive", "links": [{"title": "<PERSON> (media executive)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(media_executive)"}]}, {"year": "1958", "text": "<PERSON>, American drummer and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American businessman and television personality", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Mark_Cuban\" title=\"Mark Cuban\"><PERSON></a>, American businessman and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mark_Cuban\" title=\"Mark Cuban\"><PERSON></a>, American businessman and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, French music editor and composer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music editor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French music editor and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American guitarist, pianist, and songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, pianist, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Stanley Jordan\"><PERSON></a>, American guitarist, pianist, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stanley_Jordan"}]}, {"year": "1959", "text": "<PERSON>, Scottish journalist and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English journalist and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Scottish guitarist and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1961", "text": "<PERSON>, English captain and journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English captain and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English captain and journalist", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(journalist)"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Nigerian banker, royal", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lamido_Sanusi\" title=\"<PERSON><PERSON><PERSON> Lamido Sanusi\"><PERSON><PERSON><PERSON></a>, Nigerian banker, royal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lamido_Sanusi\" title=\"San<PERSON>i Lamido Sanusi\"><PERSON><PERSON><PERSON></a>, Nigerian banker, royal", "links": [{"title": "<PERSON><PERSON>i Lam<PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Sanusi"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician, 31st California State Controller", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(California_politician)\" title=\"<PERSON> (California politician)\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/California_State_Controller\" title=\"California State Controller\">California State Controller</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(California_politician)\" title=\"<PERSON> (California politician)\"><PERSON></a>, American lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/California_State_Controller\" title=\"California State Controller\">California State Controller</a>", "links": [{"title": "<PERSON> (California politician)", "link": "https://wikipedia.org/wiki/<PERSON>(California_politician)"}, {"title": "California State Controller", "link": "https://wikipedia.org/wiki/California_State_Controller"}]}, {"year": "1962", "text": "<PERSON>, American football player and coach (d. 2020)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON> (Fatboy Slim), English DJ and musician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON> (Fatboy Slim)</a>, English DJ and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON> (Fatboy Slim)</a>, English DJ and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English chef and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Irish singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Estonian footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American wrestler and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English-Australian rugby league player and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English-Australian rugby league player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, English-Australian rugby league player and actor", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1965", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author and film producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ling\" title=\"<PERSON><PERSON> <PERSON><PERSON>ling\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ling\" title=\"<PERSON><PERSON> <PERSON><PERSON> Rowling\"><PERSON><PERSON> <PERSON><PERSON></a>, English author and film producer", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ling"}]}, {"year": "1966", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American basketball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Welsh composer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_musician)\" title=\"<PERSON> (Welsh musician)\"><PERSON></a>, Welsh composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_musician)\" title=\"<PERSON> (Welsh musician)\"><PERSON></a>, Welsh composer", "links": [{"title": "<PERSON> (Welsh musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Welsh_musician)"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Emirati cricketer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Emirati cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Welsh director and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Welsh director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Welsh director and producer", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1969", "text": "<PERSON>, Italian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Iranian author and poet", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Polish footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>la%C5%84ski"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Greek basketball player, coach, and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player, coach, and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gior<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American football player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league_born_1973)\" class=\"mw-redirect\" title=\"<PERSON> (rugby league born 1973)\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_league_born_1973)\" class=\"mw-redirect\" title=\"<PERSON> (rugby league born 1973)\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON> (rugby league born 1973)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league_born_1973)"}]}, {"year": "1974", "text": "<PERSON>, English actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fox\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Emilia Fox\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emilia_Fox"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American-English singer-songwriter and guitarist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, South African cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andrew Hall\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andrew Hall\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andrew_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American baseball player and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American guitarist and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Costa Rican footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paulo_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American country singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Band\" title=\"Zac <PERSON> Band\"><PERSON><PERSON></a>, American country singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Band\" title=\"Zac <PERSON> Band\"><PERSON><PERSON></a>, American country singer-songwriter and guitarist", "links": [{"title": "Zac <PERSON> Band", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Band"}]}, {"year": "1978", "text": "<PERSON>, English drummer (<PERSON><PERSON>)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Will_Champion\" title=\"Will Champion\"><PERSON></a>, English drummer (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Will_Champion\" title=\"Will Champion\"><PERSON></a>, English drummer (<a href=\"https://wikipedia.org/wiki/Coldplay\" title=\"Coldplay\">Coldplay</a>)", "links": [{"title": "Will <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Champion"}, {"title": "Coldplay", "link": "https://wikipedia.org/wiki/Coldplay"}]}, {"year": "1978", "text": "<PERSON>, American football player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English race car driver (d. 2015)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (d. 2015)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, South African-Italian rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-Italian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Danish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Per_Kr%C3%B8ld<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_Kr%C3%B8ld<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Per_Kr%C3%B8ldrup"}]}, {"year": "1979", "text": "<PERSON>, Spanish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor, director, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Finnish race car driver", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, New Zealand rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Muliaina\" title=\"<PERSON><PERSON> Muliaina\"><PERSON><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Muliaina\" title=\"<PERSON><PERSON> Muliaina\"><PERSON><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ls_Muliaina"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Spanish tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/DeMarcus_Ware\" title=\"DeMarcus Ware\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DeMarcus_Ware\" title=\"DeMarcus Ware\"><PERSON><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "DeMarcus Ware", "link": "https://wikipedia.org/wiki/DeMarcus_Ware"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French cyclist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/R%C3%A9my_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9my_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9my_<PERSON>_Gregorio"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>v<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American soccer player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1988", "text": "<PERSON>, New Zealand rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Belorussian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Victoria_Azarenka\" title=\"Victoria Azarenka\"><PERSON></a>, Belorussian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Azarenka\" title=\"Victoria Azarenka\"><PERSON></a>, Belorussian tennis player", "links": [{"title": "Victoria Azarenka", "link": "https://wikipedia.org/wiki/Victoria_Azarenka"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Hungarian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/R%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1992", "text": "<PERSON>, Cuban-American baseball player (d. 2016)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1<PERSON><PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, Cuban-American baseball player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1<PERSON><PERSON>_(right-handed_pitcher)\" title=\"<PERSON> (right-handed pitcher)\"><PERSON></a>, Cuban-American baseball player (d. 2016)", "links": [{"title": "<PERSON> (right-handed pitcher)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Fern%C3%A1nde<PERSON>_(right-handed_pitcher)"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American race car driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Swedish professional hockey player ", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish professional hockey player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish professional hockey player ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American hip hop artist", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Uzi V<PERSON>\"><PERSON></a>, American hip hop artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> Uzi V<PERSON>\"><PERSON><PERSON></a>, American hip hop artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>ert"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American podcaster and influencer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American podcaster and influencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American podcaster and influencer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2000", "text": "<PERSON>, South Korean actress (d. 2025)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress (d. 2025)", "links": [{"title": "<PERSON>on", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>on"}]}, {"year": "2002", "text": "<PERSON>, Brazilian singer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Brazilian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON>_<PERSON><PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Brazilian singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON>_(singer)"}]}, {"year": "2002", "text": "<PERSON>, Australian-Tongan rugby league player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Tongan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Scottish footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "54 BC", "text": "<PERSON><PERSON><PERSON>, Roman mother of <PERSON> (b. 120 BC)", "html": "54 BC - 54 BC - <a href=\"https://wikipedia.org/wiki/Aurelia_Cotta\" class=\"mw-redirect\" title=\"Aurelia Cotta\"><PERSON><PERSON><PERSON></a>, Roman mother of <a href=\"https://wikipedia.org/wiki/Gaius_Julius_Caesar\" class=\"mw-redirect\" title=\"Gaius Julius Caesar\">Gaius <PERSON></a> (b. 120 BC)", "no_year_html": "54 BC - <a href=\"https://wikipedia.org/wiki/Aurelia_Cotta\" class=\"mw-redirect\" title=\"Aurelia Cotta\"><PERSON><PERSON><PERSON></a>, Roman mother of <a href=\"https://wikipedia.org/wiki/Gaius_Julius_Caesar\" class=\"mw-redirect\" title=\"Gaius Julius Caesar\">Gaius Julius <PERSON></a> (b. 120 BC)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelia_Cotta"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Julius_<PERSON>"}]}, {"year": "450", "text": "<PERSON>, Italian bishop and saint (b. 380)", "html": "450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop and saint (b. 380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian bishop and saint (b. 380)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "910", "text": "<PERSON>, Chinese warlord", "html": "910 - <a href=\"https://wikipedia.org/wiki/Feng_Xi<PERSON>\" title=\"Feng Xingxi\"><PERSON></a>, Chinese warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Feng_<PERSON>\" title=\"Feng Xingxi\"><PERSON></a>, Chinese warlord", "links": [{"title": "Feng <PERSON>", "link": "https://wikipedia.org/wiki/Feng_Xingxi"}]}, {"year": "975", "text": "<PERSON>, Chinese general  (b. 898)", "html": "975 - <a href=\"https://wikipedia.org/wiki/Fu_Yanqing\" title=\"Fu Yanqing\"><PERSON></a>, Chinese general (b. 898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fu_Yanqing\" title=\"Fu Yanqing\"><PERSON></a>, Chinese general (b. 898)", "links": [{"title": "Fu Yanqing", "link": "https://wikipedia.org/wiki/Fu_Yanqing"}]}, {"year": "1098", "text": "<PERSON> Montgomery, 2nd Earl of Shrewsbury", "html": "1098 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Shrewsbury\" title=\"<PERSON>, 2nd Earl of Shrewsbury\"><PERSON>, 2nd Earl of Shrewsbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Shrewsbury\" title=\"<PERSON>, 2nd Earl of Shrewsbury\"><PERSON> Montgomery, 2nd Earl of Shrewsbury</a>", "links": [{"title": "<PERSON> Montgomery, 2nd Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_2nd_Earl_of_Shrewsbury"}]}, {"year": "1358", "text": "<PERSON>, French rebel leader (b. 1302)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rebel leader (b. 1302)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rebel leader (b. 1302)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>"}]}, {"year": "1396", "text": "<PERSON>, English archbishop and politician, Lord Chancellor of the United Kingdom (b. 1342)", "html": "1396 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1342)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (b. 1342)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1508", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian emperor", "html": "1508 - <a href=\"https://wikipedia.org/wiki/Na%27od\" title=\"Na'od\">Na'od</a>, Ethiopian emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na%27od\" title=\"Na'od\">Na'od</a>, Ethiopian emperor", "links": [{"title": "Na'od", "link": "https://wikipedia.org/wiki/Na%27od"}]}, {"year": "1556", "text": "<PERSON><PERSON><PERSON> <PERSON> Loyola, Spanish priest and theologian, founded the Society of Jesus (b. 1491)", "html": "1556 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a>, Spanish priest and theologian, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> (b. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a>, Spanish priest and theologian, founded the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> (b. 1491)", "links": [{"title": "<PERSON><PERSON><PERSON> of Loyola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola"}, {"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}]}, {"year": "1616", "text": "<PERSON>, Solicitor-General for Ireland (b. 1553)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solicitor-General for Ireland (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Solicitor-General for Ireland (b. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON><PERSON><PERSON>, German poet (b. 1621)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German poet (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German poet (b. 1621)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON>, English soldier and politician, 3rd Governor of Massachusetts Bay Colony (b. 1576)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of Massachusetts Bay Colony\">Governor of Massachusetts Bay Colony</a> (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts_Bay_Colony\" class=\"mw-redirect\" title=\"Governor of Massachusetts Bay Colony\">Governor of Massachusetts Bay Colony</a> (b. 1576)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts_Bay_Colony"}]}, {"year": "1693", "text": "<PERSON>, Dutch still life painter (b. 1619)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch still life painter (b. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch still life painter (b. 1619)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1726", "text": "<PERSON><PERSON>, Swiss mathematician and theorist (b. 1695)", "html": "1726 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and theorist (b. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss mathematician and theorist (b. 1695)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, king of Portugal (b. 1689)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"John V of Portugal\"><PERSON></a>, king of Portugal (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_V_of_Portugal\" title=\"<PERSON> V of Portugal\"><PERSON></a>, king of Portugal (b. 1689)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/John_V_of_Portugal"}]}, {"year": "1762", "text": "<PERSON>, Spanish sailor and commander (b. 1711)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>sco_e_Isla\" class=\"mw-redirect\" title=\"<PERSON> e Isla\"><PERSON></a>, Spanish sailor and commander (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>elasco_e_Isla\" class=\"mw-redirect\" title=\"<PERSON>elasco e Isla\"><PERSON></a>, Spanish sailor and commander (b. 1711)", "links": [{"title": "<PERSON> Velasco e Isla", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, 3rd Earl of Darnley, British parliamentarian (b. 1719)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Darnley\" title=\"<PERSON>, 3rd Earl of Darnley\"><PERSON>, 3rd Earl of Darnley</a>, British parliamentarian (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Darnley\" title=\"<PERSON>, 3rd Earl of Darnley\"><PERSON>, 3rd Earl of Darnley</a>, British parliamentarian (b. 1719)", "links": [{"title": "<PERSON>, 3rd Earl of Darnley", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Darnley"}]}, {"year": "1784", "text": "<PERSON>, French philosopher and critic (b. 1713)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and critic (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and critic (b. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON>, Indian soldier (b. 1756)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian soldier (b. 1756)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1864", "text": "<PERSON>, French publisher (b. 1800)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Hachette\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French publisher (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A7ois_Hachette\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French publisher (b. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A<PERSON>ois_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American general and politician, 17th President of the United States (b. 1808)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Vietnamese emperor (b. 1869)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/Ki%E1%BA%BFn_Ph%C3%BAc\" title=\"<PERSON>ế<PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ki%E1%BA%BFn_Ph%C3%BAc\" title=\"Kiến <PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ki%E1%BA%BFn_Ph%C3%BAc"}]}, {"year": "1886", "text": "<PERSON>, Hungarian pianist, composer, and conductor (b. 1811)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist, composer, and conductor (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pianist, composer, and conductor (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Liszt"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, Belgian stained glass painter (b. 1814)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian stained glass painter (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian stained glass painter (b. 1814)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1913", "text": "<PERSON>, British geologist and mining engineer. (b. 1850)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist and mining engineer. (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British geologist and mining engineer. (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French journalist and politician (b. 1859)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s"}]}, {"year": "1917", "text": "<PERSON>, Irish soldier and poet (b. 1881)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and poet (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soldier and poet (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Welsh language poet (b. 1887)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yn\" title=\"<PERSON><PERSON> Wyn\"><PERSON><PERSON></a>, Welsh language poet (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>yn\" title=\"<PERSON><PERSON> <PERSON>yn\"><PERSON><PERSON></a>, Welsh language poet (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hedd_Wyn"}]}, {"year": "1920", "text": "<PERSON>, Greek philosopher and diplomat (b. 1878)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek philosopher and diplomat (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek philosopher and diplomat (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Indian activist (b. 1899)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, British Army Officer, explorer and spiritual writer (b. 1863)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> Officer, <a href=\"https://wikipedia.org/wiki/Explorer\" class=\"mw-redirect\" title=\"Explorer\">explorer</a> and spiritual writer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> Officer, <a href=\"https://wikipedia.org/wiki/Explorer\" class=\"mw-redirect\" title=\"Explorer\">explorer</a> and spiritual writer (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>band"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Explorer", "link": "https://wikipedia.org/wiki/Explorer"}]}, {"year": "1943", "text": "<PERSON><PERSON>, English cricketer and soldier (b. 1905)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and soldier (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and soldier (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, French pilot and poet (b. 1900)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>up%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French pilot and poet (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>%C3%A9ry\" title=\"<PERSON>\"><PERSON></a>, French pilot and poet (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-Exup%C3%A9ry"}]}, {"year": "1951", "text": "<PERSON>, North Korean poet (b. 1913)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chon\" title=\"<PERSON>-chon\"><PERSON>on</a>, North Korean poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chon\" title=\"<PERSON>-chon\"><PERSON>on</a>, North Korean poet (b. 1913)", "links": [{"title": "<PERSON>on", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-chon"}]}, {"year": "1953", "text": "<PERSON>, American soldier and politician (b. 1889)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Argentine race car driver (b. 1923)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Onofre_Marim%C3%B3n\" title=\"Onofre <PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine race car driver (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Onofre_Marim%C3%B3n\" title=\"Onofre <PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine race car driver (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Onofre_Marim%C3%B3n"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Finnish philosopher and psychologist, attendant of the Vienna circle (b. 1890)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish philosopher and psychologist, attendant of the Vienna circle (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish philosopher and psychologist, attendant of the Vienna circle (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter (b. 1923)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American pianist (b. 1924)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian politician, 29th Premier of Queensland (b. 1911)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1971", "text": "<PERSON>, American soldier and activist (b. 1923)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and activist (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Belgian politician, 40th Prime Minister of Belgium, 1st President of the United Nations General Assembly (b. 1899)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly\" title=\"President of the United Nations General Assembly\">President of the United Nations General Assembly</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}, {"title": "President of the United Nations General Assembly", "link": "https://wikipedia.org/wiki/President_of_the_United_Nations_General_Assembly"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 40th <PERSON><PERSON><PERSON><PERSON> (b. 1921)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>ji_Kin%27ichi\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 40th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>ji_Kin%27ichi\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 40th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Azumafuji_Kin%27ichi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1979", "text": "<PERSON><PERSON>, English actress and director (b. 1903)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and director (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and director (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, German physicist, author, and academic (b. 1902)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Pas<PERSON>al_<PERSON>\" title=\"Pas<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German physicist, author, and academic (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pas<PERSON>al_<PERSON>\" title=\"Pa<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, German physicist, author, and academic (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pas<PERSON>al_Jordan"}]}, {"year": "1980", "text": "<PERSON>, Indian playback singer (b. 1924)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Playback_singer\" title=\"Playback singer\">playback singer</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Playback_singer\" title=\"Playback singer\">playback singer</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Playback singer", "link": "https://wikipedia.org/wiki/Playback_singer"}]}, {"year": "1981", "text": "<PERSON>, Panamanian general and politician, Military Leader of Panama (b. 1929)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military Leader of Panama</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama\" title=\"List of heads of state of Panama\">Military Leader of Panama</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of Panama", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Panama"}]}, {"year": "1985", "text": "<PERSON>, American religious leader (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese diplomat (b. 1900)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chiune_Sugihara\" title=\"Chiune Sugihara\"><PERSON><PERSON></a>, Japanese diplomat (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiune_Sugihara\" title=\"<PERSON><PERSON> Sugihara\"><PERSON><PERSON></a>, Japanese diplomat (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chiune_Sugihara"}]}, {"year": "1987", "text": "<PERSON>, American film producer (b, 1905)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b, 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b, 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player (b. 1902)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English captain and pilot (b. 1917)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Leonard_<PERSON>\" title=\"Leonard <PERSON>\"><PERSON></a>, English captain and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leonard_<PERSON>\" title=\"Leonard <PERSON>\"><PERSON></a>, English captain and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, King of Belgium (b. 1930)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON></a>, King of Belgium (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Belgium\" title=\"<PERSON><PERSON><PERSON> of Belgium\"><PERSON><PERSON><PERSON></a>, King of Belgium (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Baudouin_of_Belgium"}]}, {"year": "2000", "text": "<PERSON>, American editor, novelist, short story writer, and essayist (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"William <PERSON>s Maxwell Jr.\"><PERSON> Jr.</a>, American editor, novelist, short story writer, and essayist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"William Keepers Maxwell Jr.\"><PERSON><PERSON>.</a>, American editor, novelist, short story writer, and essayist (b. 1908)", "links": [{"title": "William Keepers Maxwell Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2001", "text": "<PERSON>, Portuguese general and politician, 15th President of Portugal (b. 1914)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese general and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese general and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "2001", "text": "<PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin (b. 1910)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Hereditary_Grand_Duke_of_Mecklenburg-Schwerin\" title=\"<PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin\"><PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Hereditary_Grand_Duke_of_Mecklenburg-Schwerin\" title=\"<PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin\"><PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin</a> (b. 1910)", "links": [{"title": "<PERSON>, Hereditary Grand Duke of Mecklenburg-Schwerin", "link": "https://wikipedia.org/wiki/<PERSON>,_Hereditary_Grand_Duke_of_Mecklenburg-Schwerin"}]}, {"year": "2003", "text": "<PERSON>, Italian author and illustrator (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_<PERSON>x"}]}, {"year": "2004", "text": "<PERSON>, American actress (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Virginia_Grey\" title=\"Virginia Grey\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Grey\" title=\"Virginia Grey\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "Virginia Grey", "link": "https://wikipedia.org/wiki/Virginia_Grey"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Dutch economist and politician, 1st President of the European Central Bank (b. 1935)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_European_Central_Bank\" title=\"President of the European Central Bank\">President of the European Central Bank</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_European_Central_Bank\" title=\"President of the European Central Bank\">President of the European Central Bank</a> (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>"}, {"title": "President of the European Central Bank", "link": "https://wikipedia.org/wiki/President_of_the_European_Central_Bank"}]}, {"year": "2009", "text": "<PERSON>, English footballer and manager (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English-Canadian screenwriter and producer (b. 1920)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian screenwriter and producer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian screenwriter and producer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Scottish author and playwright (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mollie Hunter\"><PERSON><PERSON></a>, Scottish author and playwright (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Mollie Hunter\"><PERSON><PERSON></a>, Scottish author and playwright (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Brazilian footballer and coach (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Brazilian_footballer)\" title=\"<PERSON> (Brazilian footballer)\"><PERSON></a>, Brazilian footballer and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Brazilian_footballer)\" title=\"<PERSON> (Brazilian footballer)\"><PERSON></a>, Brazilian footballer and coach (b. 1924)", "links": [{"title": "<PERSON> (Brazilian footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Brazilian_footballer)"}]}, {"year": "2012", "text": "<PERSON>, American novelist, screenwriter, and critic (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vidal"}]}, {"year": "2012", "text": "<PERSON>, American musician, singer-songwriter (b. 1970)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer-songwriter (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, singer-songwriter (b. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Syrian-American actor (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian-American actor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian-American actor (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English-Belgian general and pilot (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian general and pilot (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian general and pilot (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American captain and author (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American captain and author (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American captain and author (b. 1920)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "2013", "text": "<PERSON>, English businessman, founded Pukka Pies (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Pukka_Pies\" title=\"Pukka Pies\"><PERSON><PERSON><PERSON></a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Pukka_Pies\" title=\"Pukka Pies\"><PERSON>uk<PERSON></a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pukka Pies", "link": "https://wikipedia.org/wiki/Pukka_Pies"}]}, {"year": "2014", "text": "<PERSON>, American scholar, author, and academic (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, author, and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, author, and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian journalist and author (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian journalist and author (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English footballer (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American lawyer and judge (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and judge (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American writer and critic (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and critic (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and critic (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American surgeon and academic (b. 1910)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American baseball player and sportscaster (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Canadian wrestler and actor (b. 1954)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler and actor (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian wrestler and actor (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier and politician, 14th United States Secretary of Health and Human Services (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 58th <PERSON><PERSON><PERSON><PERSON> (b. 1955)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>of<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 58th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 58th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yonof<PERSON><PERSON>_<PERSON>tsugu"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "2016", "text": "<PERSON>, South African mathematician (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mathematician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African mathematician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "2017", "text": "<PERSON>, French actress (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, British sailor & businessman (b. 1939)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sailor &amp; businessman (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British sailor &amp; businessman (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Broadway producer and director, who received more Tony awards than anyone else in history (b. 1928)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prince\"><PERSON></a>, Broadway producer and director, who received more <a href=\"https://wikipedia.org/wiki/Tony_Award\" class=\"mw-redirect\" title=\"Tony Award\">Tony awards</a> than anyone else in history (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Prince\"><PERSON></a>, Broadway producer and director, who received more <a href=\"https://wikipedia.org/wiki/Tony_Award\" class=\"mw-redirect\" title=\"Tony Award\">Tony awards</a> than anyone else in history (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tony Award", "link": "https://wikipedia.org/wiki/Tony_Award"}]}, {"year": "2020", "text": "<PERSON>, English filmmaker (b. 1944)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English filmmaker (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English filmmaker (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, 12th President of the Philippines (b. 1928)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, 12th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fi<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "2022", "text": "<PERSON>, NBA Hall of Fame Player and Coach (b. 1934)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/NBA\" class=\"mw-redirect\" title=\"NBA\">NBA</a> Hall of Fame Player and Coach (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/NBA\" class=\"mw-redirect\" title=\"NBA\">NBA</a> Hall of Fame Player and Coach (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NBA", "link": "https://wikipedia.org/wiki/NBA"}]}, {"year": "2023", "text": "<PERSON>, American actor (b. 1998)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cloud\" title=\"Angus Cloud\"><PERSON></a>, American actor (b. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Angus_Cloud\" title=\"Angus Cloud\"><PERSON></a>, American actor (b. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_Cloud"}]}, {"year": "2024", "text": "<PERSON>, United States Army Medal of Honor recipient (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Army <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, United States Army <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2024", "text": "<PERSON>, Palestinian politician, political leader of Hamas (b. 1962/1963)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, political leader of <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (b. 1962/1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, political leader of <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> (b. 1962/1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ismail_<PERSON>"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}]}]}}