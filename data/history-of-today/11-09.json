{"date": "November 9", "url": "https://wikipedia.org/wiki/November_9", "data": {"Events": [{"year": "694", "text": "At the Seventeenth Council of Toledo, <PERSON><PERSON><PERSON>, a king of the Visigoths of Hispania, accuses Jews of aiding Muslims, sentencing all Jews to slavery.", "html": "694 - At the <a href=\"https://wikipedia.org/wiki/Seventeenth_Council_of_Toledo\" title=\"Seventeenth Council of Toledo\">Seventeenth Council of Toledo</a>, <a href=\"https://wikipedia.org/wiki/Egica\" title=\"Egic<PERSON>\"><PERSON><PERSON><PERSON></a>, a king of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> of <a href=\"https://wikipedia.org/wiki/Hispania\" title=\"Hispania\">Hispania</a>, accuses Jews of aiding Muslims, sentencing all Jews to <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Seventeenth_Council_of_Toledo\" title=\"Seventeenth Council of Toledo\">Seventeenth Council of Toledo</a>, <a href=\"https://wikipedia.org/wiki/Egica\" title=\"Egica\"><PERSON><PERSON><PERSON></a>, a king of the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> of <a href=\"https://wikipedia.org/wiki/Hispania\" title=\"Hispania\">Hispania</a>, accuses Jews of aiding Muslims, sentencing all Jews to <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a>.", "links": [{"title": "Seventeenth Council of Toledo", "link": "https://wikipedia.org/wiki/Seventeenth_Council_of_Toledo"}, {"title": "Egica", "link": "https://wikipedia.org/wiki/Egica"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}, {"title": "Hispania", "link": "https://wikipedia.org/wiki/Hispania"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1180", "text": "The Battle of Fujigawa: Minamoto forces (30,000 men) under <PERSON><PERSON> defeat <PERSON><PERSON> during a night attack near the Fuji River but he escapes safely with the routed army.", "html": "1180 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Fujigawa\" title=\"Battle of Fujigawa\">Battle of Fujigawa</a>: Minamoto forces (30,000 men) under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoritomo\" title=\"Minamoto no Yoritomo\"><PERSON><PERSON> no Yoritomo</a> defeat <a href=\"https://wikipedia.org/wiki/Taira_no_Koremori\" title=\"Taira no Koremori\"><PERSON><PERSON> no <PERSON>ori</a> during a night attack near the Fuji River but he escapes safely with the routed army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Fujigawa\" title=\"Battle of Fujigawa\">Battle of Fujigawa</a>: Minamoto forces (30,000 men) under <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoritomo\" title=\"Minamoto no Yoritomo\"><PERSON><PERSON> no Yoritomo</a> defeat <a href=\"https://wikipedia.org/wiki/Taira_no_Koremori\" title=\"Taira no Koremori\"><PERSON><PERSON> <PERSON></a> during a night attack near the Fuji River but he escapes safely with the routed army.", "links": [{"title": "Battle of Fujigawa", "link": "https://wikipedia.org/wiki/Battle_of_Fujigawa"}, {"title": "<PERSON><PERSON> no Yoritomo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>ra no Koremori", "link": "https://wikipedia.org/wiki/Tai<PERSON>_no_Koremori"}]}, {"year": "1277", "text": "The Treaty of Aberconwy, a humiliating settlement forced on <PERSON><PERSON><PERSON><PERSON> a<PERSON> by King <PERSON> of England, brings a temporary end to the Welsh Wars.", "html": "1277 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Aberconwy\" title=\"Treaty of Aberconwy\">Treaty of Aberconwy</a>, a humiliating settlement forced on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_<PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap Gruffudd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON>udd</a> by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>, brings a temporary end to the Welsh Wars.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Aberconwy\" title=\"Treaty of Aberconwy\">Treaty of Aberconwy</a>, a humiliating settlement forced on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ap_<PERSON><PERSON>udd\" title=\"<PERSON><PERSON><PERSON><PERSON> ap G<PERSON>udd\"><PERSON><PERSON><PERSON><PERSON> ap <PERSON></a> by King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>, brings a temporary end to the Welsh Wars.", "links": [{"title": "Treaty of Aberconwy", "link": "https://wikipedia.org/wiki/Treaty_of_Aberconwy"}, {"title": "<PERSON><PERSON><PERSON><PERSON> ap <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_a<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}]}, {"year": "1307", "text": "Knights Templar officer <PERSON><PERSON> is forced to confess during the Trials of the Knights Templar. He was persecuted on the charges of false idolism and sodomy.", "html": "1307 - <a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Knights Templar</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is forced to confess during the <a href=\"https://wikipedia.org/wiki/Trials_of_the_Knights_Templar\" title=\"Trials of the Knights Templar\">Trials of the Knights Templar</a>. He was persecuted on the charges of false idolism and sodomy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Knights_Templar\" title=\"Knights Templar\">Knights Templar</a> officer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is forced to confess during the <a href=\"https://wikipedia.org/wiki/Trials_of_the_Knights_Templar\" title=\"Trials of the Knights Templar\">Trials of the Knights Templar</a>. He was persecuted on the charges of false idolism and sodomy.", "links": [{"title": "Knights Templar", "link": "https://wikipedia.org/wiki/Knights_Templar"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Trials of the Knights Templar", "link": "https://wikipedia.org/wiki/Trials_of_the_Knights_Templar"}]}, {"year": "1313", "text": "<PERSON> the Bavarian defeats his cousin <PERSON> of Austria at the Battle of Gammelsdorf.", "html": "1313 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bavarian\" class=\"mw-redirect\" title=\"<PERSON> the Bavarian\"><PERSON> the <PERSON></a> defeats his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria_(Habsburg)\" class=\"mw-redirect\" title=\"<PERSON> I of Austria (Habsburg)\"><PERSON> of Austria</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gammelsdorf\" title=\"Battle of Gammelsdorf\">Battle of Gammelsdorf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bavarian\" class=\"mw-redirect\" title=\"<PERSON> the Bavarian\"><PERSON> the <PERSON></a> defeats his cousin <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria_(Habsburg)\" class=\"mw-redirect\" title=\"<PERSON> of Austria (Habsburg)\"><PERSON> of Austria</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Gammelsdorf\" title=\"Battle of Gammelsdorf\">Battle of Gammelsdorf</a>.", "links": [{"title": "<PERSON> the Bavarian", "link": "https://wikipedia.org/wiki/<PERSON>_the_<PERSON>"}, {"title": "<PERSON> of Austria (Habsburg)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria_(Habsburg)"}, {"title": "Battle of Gammelsdorf", "link": "https://wikipedia.org/wiki/Battle_of_Gammelsdorf"}]}, {"year": "1323", "text": "Siege of Warangal: <PERSON><PERSON><PERSON><PERSON><PERSON> surrenders to <PERSON> bin <PERSON>, officially marking the end of the Kakatiya dynasty.", "html": "1323 - <a href=\"https://wikipedia.org/wiki/Siege_of_Warangal_(1323)\" title=\"Siege of Warangal (1323)\">Siege of Warangal</a>: <a href=\"https://wikipedia.org/wiki/Prataparudra\" title=\"Pratapar<PERSON>ra\">P<PERSON><PERSON><PERSON><PERSON></a> surrenders to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>laq\" class=\"mw-redirect\" title=\"<PERSON>q\"><PERSON> bin <PERSON></a>, officially marking the end of the <a href=\"https://wikipedia.org/wiki/Kakatiya_dynasty\" title=\"Kakatiya dynasty\">Kakatiya dynasty</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Siege_of_Warangal_(1323)\" title=\"Siege of Warangal (1323)\">Siege of Warangal</a>: <a href=\"https://wikipedia.org/wiki/Prataparudra\" title=\"Pratapar<PERSON>ra\">P<PERSON><PERSON><PERSON>ra</a> surrenders to <a href=\"https://wikipedia.org/wiki/<PERSON>_bin_<PERSON>la<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON> bin <PERSON></a>, officially marking the end of the <a href=\"https://wikipedia.org/wiki/Kakatiya_dynasty\" title=\"Kakatiya dynasty\">Kakatiya dynasty</a>.", "links": [{"title": "Siege of Warangal (1323)", "link": "https://wikipedia.org/wiki/Siege_of_Warangal_(1323)"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prataparudra"}, {"title": "<PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kakatiya dynasty", "link": "https://wikipedia.org/wiki/Kakatiya_dynasty"}]}, {"year": "1330", "text": "At the Battle of Posada, <PERSON><PERSON><PERSON> of Wallachia defeats the Hungarian army of <PERSON>.", "html": "1330 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_Posada\" title=\"Battle of Posada\">Battle of Posada</a>, <a href=\"https://wikipedia.org/wiki/Ba<PERSON>ab_I_of_Wallachia\" title=\"<PERSON><PERSON><PERSON> I of Wallachia\"><PERSON><PERSON><PERSON> I of Wallachia</a> defeats the Hungarian army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"Charles I of Hungary\"><PERSON></a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_Posada\" title=\"Battle of Posada\">Battle of Posada</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Wallachia\" title=\"<PERSON>sarab I of Wallachia\"><PERSON><PERSON><PERSON> I of Wallachia</a> defeats the Hungarian army of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"Charles I of Hungary\"><PERSON></a>.", "links": [{"title": "Battle of Posada", "link": "https://wikipedia.org/wiki/Battle_of_Posada"}, {"title": "Basarab I of Wallachia", "link": "https://wikipedia.org/wiki/Basarab_I_of_Wallachia"}, {"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1372", "text": "<PERSON><PERSON><PERSON><PERSON> succeeds his brother <PERSON><PERSON><PERSON><PERSON> as King of Vietnam.", "html": "1372 - <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Du%E1%BB%87_T%C3%B4ng\" title=\"Trầ<PERSON> <PERSON> Tông\">Tr<PERSON><PERSON></a> succeeds his brother <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Ngh%E1%BB%87_T%C3%B4ng\" title=\"Trần Nghệ Tông\">Tr<PERSON><PERSON>ông</a> as King of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Du%E1%BB%87_T%C3%B4ng\" title=\"Trần <PERSON> Tông\">Tr<PERSON><PERSON></a> succeeds his brother <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Ngh%E1%BB%87_T%C3%B4ng\" title=\"Trần Nghệ Tông\">T<PERSON><PERSON><PERSON> Tông</a> as King of <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Du%E1%BB%87_T%C3%B4ng"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Ngh%E1%BB%87_T%C3%B4ng"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1431", "text": "The Battle of Ilava: The Hungarians defeat the Hussite army.", "html": "1431 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Ilava\" title=\"Battle of Ilava\">Battle of Ilava</a>: The Hungarians defeat the <a href=\"https://wikipedia.org/wiki/Hussite\" class=\"mw-redirect\" title=\"<PERSON>ssi<PERSON>\">Hussite</a> army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Ilava\" title=\"Battle of Ilava\">Battle of Ilava</a>: The Hungarians defeat the <a href=\"https://wikipedia.org/wiki/Hussite\" class=\"mw-redirect\" title=\"Hussi<PERSON>\">Hussite</a> army.", "links": [{"title": "Battle of Ilava", "link": "https://wikipedia.org/wiki/Battle_of_Ilava"}, {"title": "Hussite", "link": "https://wikipedia.org/wiki/<PERSON>ssite"}]}, {"year": "1456", "text": "<PERSON>, Count of Celje, last ruler of the County of Cilli, is assassinated in Belgrade.", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Celje\" title=\"<PERSON> II, Count of Celje\"><PERSON>, Count of Celje</a>, last ruler of the <a href=\"https://wikipedia.org/wiki/County_of_Cilli\" title=\"County of Cilli\">County of Cilli</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Celje\" title=\"<PERSON> II, Count of Celje\"><PERSON>, Count of Celje</a>, last ruler of the <a href=\"https://wikipedia.org/wiki/County_of_Cilli\" title=\"County of Cilli\">County of Cilli</a>, is assassinated in <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>.", "links": [{"title": "<PERSON>, Count of Celje", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "County of Cilli", "link": "https://wikipedia.org/wiki/County_of_Cilli"}, {"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}]}, {"year": "1520", "text": "More than 50 people are sentenced and executed in the Stockholm Bloodbath.", "html": "1520 - More than 50 people are sentenced and executed in the <a href=\"https://wikipedia.org/wiki/Stockholm_Bloodbath\" title=\"Stockholm Bloodbath\">Stockholm Bloodbath</a>.", "no_year_html": "More than 50 people are sentenced and executed in the <a href=\"https://wikipedia.org/wiki/Stockholm_Bloodbath\" title=\"Stockholm Bloodbath\">Stockholm Bloodbath</a>.", "links": [{"title": "Stockholm Bloodbath", "link": "https://wikipedia.org/wiki/Stockholm_Bloodbath"}]}, {"year": "1580", "text": "Second Desmond Rebellion: The Siege of Smerwick ends with the Catholic garrison surrendering to the English forces under <PERSON>. The majority of the garrison is massacred the next day.", "html": "1580 - <a href=\"https://wikipedia.org/wiki/Second_Desmond_Rebellion\" title=\"Second Desmond Rebellion\">Second Desmond Rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Smerwick\" title=\"Siege of Smerwick\">Siege of Smerwick</a> ends with the Catholic garrison surrendering to the English forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Baron_<PERSON>_<PERSON>_Wilton\" title=\"<PERSON>, 14th Baron <PERSON>\"><PERSON></a>. The majority of the garrison is massacred the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Desmond_Rebellion\" title=\"Second Desmond Rebellion\">Second Desmond Rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Smerwick\" title=\"Siege of Smerwick\">Siege of Smerwick</a> ends with the Catholic garrison surrendering to the English forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_14th_Baron_<PERSON>_<PERSON>_Wilton\" title=\"<PERSON>, 14th Baron <PERSON>\"><PERSON></a>. The majority of the garrison is massacred the next day.", "links": [{"title": "Second Desmond Rebellion", "link": "https://wikipedia.org/wiki/Second_Desmond_<PERSON>"}, {"title": "Siege of Smerwick", "link": "https://wikipedia.org/wiki/Siege_of_Smerwick"}, {"title": "<PERSON>, 14th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_14th_Baron_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1620", "text": "The Bohemian King <PERSON> flees Prague to Wroclaw one day after the defeat of his troops in the Battle of White Mountain.", "html": "1620 - The Bohemian King Frederick I flees Prague to Wroclaw one day after the defeat of his troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a>.", "no_year_html": "The Bohemian King <PERSON> flees Prague to Wroclaw one day after the defeat of his troops in the <a href=\"https://wikipedia.org/wiki/Battle_of_White_Mountain\" title=\"Battle of White Mountain\">Battle of White Mountain</a>.", "links": [{"title": "Battle of White Mountain", "link": "https://wikipedia.org/wiki/Battle_of_White_Mountain"}]}, {"year": "1688", "text": "Glorious Revolution: William of Orange captures Exeter.", "html": "1688 - <a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> Orange</a> captures <a href=\"https://wikipedia.org/wiki/Exeter\" title=\"Exeter\">Exeter</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glorious_Revolution\" title=\"Glorious Revolution\">Glorious Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"William III of England\"><PERSON> Orange</a> captures <a href=\"https://wikipedia.org/wiki/Exeter\" title=\"Exeter\">Exeter</a>.", "links": [{"title": "Glorious Revolution", "link": "https://wikipedia.org/wiki/Glorious_Revolution"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Exeter", "link": "https://wikipedia.org/wiki/Exeter"}]}, {"year": "1719", "text": "In a treaty between Sweden and Hanover at the close of the Great Northern War, Sweden cedes the  Duchies of Bremen and Verden (in northern Germany) to Hanover.", "html": "1719 - In <a href=\"https://wikipedia.org/wiki/Treaties_of_Stockholm_(Great_Northern_War)#Treaty_with_Hanover\" title=\"Treaties of Stockholm (Great Northern War)\">a treaty between Sweden and Hanover</a> at the close of the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>, Sweden cedes the <a href=\"https://wikipedia.org/wiki/Bremen-Verden\" title=\"Bremen-Verden\">Duchies of Bremen and Verden</a> (in northern Germany) to Hanover.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Treaties_of_Stockholm_(Great_Northern_War)#Treaty_with_Hanover\" title=\"Treaties of Stockholm (Great Northern War)\">a treaty between Sweden and Hanover</a> at the close of the <a href=\"https://wikipedia.org/wiki/Great_Northern_War\" title=\"Great Northern War\">Great Northern War</a>, Sweden cedes the <a href=\"https://wikipedia.org/wiki/Bremen-Verden\" title=\"Bremen-Verden\">Duchies of Bremen and Verden</a> (in northern Germany) to Hanover.", "links": [{"title": "Treaties of Stockholm (Great Northern War)", "link": "https://wikipedia.org/wiki/Treaties_of_Stockholm_(Great_Northern_War)#Treaty_with_Hanover"}, {"title": "Great Northern War", "link": "https://wikipedia.org/wiki/Great_Northern_War"}, {"title": "Bremen-Verden", "link": "https://wikipedia.org/wiki/Bremen-Verden"}]}, {"year": "1720", "text": "The synagogue of Judah HeHasid is burned down by Arab creditors, leading to the expulsion of the Ashkenazim from Jerusalem.", "html": "1720 - The synagogue of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jerusalem)\" title=\"<PERSON> (Jerusalem)\"><PERSON></a> is burned down by Arab creditors, leading to the expulsion of the <a href=\"https://wikipedia.org/wiki/Ashkenazi_Jews\" title=\"Ashkenazi Jews\">Ashkenazim</a> from <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "The synagogue of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jerusalem)\" title=\"<PERSON> (Jerusalem)\"><PERSON></a> is burned down by Arab creditors, leading to the expulsion of the <a href=\"https://wikipedia.org/wiki/Ashkenazi_Jews\" title=\"Ashkenazi Jews\">Ashkenazim</a> from <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "<PERSON> (Jerusalem)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Jerusalem)"}, {"title": "Ashkenazi Jews", "link": "https://wikipedia.org/wiki/Ashkenazi_Jews"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1729", "text": "Spain, France and Great Britain sign the Treaty of Seville.", "html": "1729 - Spain, France and Great Britain sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Seville_(1729)\" class=\"mw-redirect\" title=\"Treaty of Seville (1729)\">Treaty of Seville</a>.", "no_year_html": "Spain, France and Great Britain sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Seville_(1729)\" class=\"mw-redirect\" title=\"Treaty of Seville (1729)\">Treaty of Seville</a>.", "links": [{"title": "Treaty of Seville (1729)", "link": "https://wikipedia.org/wiki/Treaty_of_Seville_(1729)"}]}, {"year": "1780", "text": "American Revolutionary War: In the Battle of Fishdam Ford a force of British and Loyalist troops fail in a surprise attack against the South Carolina Patriot militia under Brigadier General <PERSON>.", "html": "1780 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Fishdam_Ford\" title=\"Battle of Fishdam Ford\">Battle of Fishdam Ford</a> a force of British and Loyalist troops fail in a surprise attack against the <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> Patriot militia under Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Fishdam_Ford\" title=\"Battle of Fishdam Ford\">Battle of Fishdam Ford</a> a force of British and Loyalist troops fail in a surprise attack against the <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> Patriot militia under Brigadier General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Fishdam Ford", "link": "https://wikipedia.org/wiki/Battle_of_Fishdam_Ford"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "The Dublin Society of United Irishmen is founded.", "html": "1791 - The Dublin <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">Society of United Irishmen</a> is founded.", "no_year_html": "The Dublin <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">Society of United Irishmen</a> is founded.", "links": [{"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}]}, {"year": "1799", "text": "<PERSON> leads the Coup of 18 Brumaire ending the Directory government, and becoming First Consul of the successor Consulate Government.", "html": "1799 - <PERSON> leads the <a href=\"https://wikipedia.org/wiki/Coup_of_18_Brumaire\" title=\"Coup of 18 Brumaire\">Coup of 18 Brumaire</a> ending the Directory government, and becoming First Consul of the successor Consulate Government.", "no_year_html": "<PERSON> leads the <a href=\"https://wikipedia.org/wiki/Coup_of_18_Brumaire\" title=\"Coup of 18 Brumaire\">Coup of 18 Brumaire</a> ending the Directory government, and becoming First Consul of the successor Consulate Government.", "links": [{"title": "Coup of 18 Brumaire", "link": "https://wikipedia.org/wiki/Coup_of_18_Brumaire"}]}, {"year": "1851", "text": "Kentucky marshals abduct abolitionist minister <PERSON> from Jeffersonville, Indiana, and take him to Kentucky to stand trial for helping a slave escape.", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> marshals abduct abolitionist minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Jeffersonville,_Indiana\" title=\"Jeffersonville, Indiana\">Jeffersonville, Indiana</a>, and take him to Kentucky to stand trial for helping a slave escape.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> marshals abduct abolitionist minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Jeffersonville,_Indiana\" title=\"Jeffersonville, Indiana\">Jeffersonville, Indiana</a>, and take him to Kentucky to stand trial for helping a slave escape.", "links": [{"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Calvin_<PERSON>bank"}, {"title": "Jeffersonville, Indiana", "link": "https://wikipedia.org/wiki/Jeffersonville,_Indiana"}]}, {"year": "1862", "text": "American Civil War: Union General <PERSON> assumes command of the Army of the Potomac, after <PERSON> is removed.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>, after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is removed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">Union</a> General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> assumes command of the <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a>, after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is removed.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "The Tokugawa shogunate hands back power to the Emperor of Japan, starting the Meiji Restoration.", "html": "1867 - The <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> hands back power to the <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">Emperor of Japan</a>, starting the <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> hands back power to the <a href=\"https://wikipedia.org/wiki/Emperor_of_Japan\" title=\"Emperor of Japan\">Emperor of Japan</a>, starting the <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a>.", "links": [{"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Emperor of Japan", "link": "https://wikipedia.org/wiki/Emperor_of_Japan"}, {"title": "Meiji Restoration", "link": "https://wikipedia.org/wiki/Meiji_Restoration"}]}, {"year": "1870", "text": "The Battle of Coulmiers ends in a Pyrrhic victory for the French army during the Franco-German War of 1870.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Coulmiers\" title=\"Battle of Coulmiers\">Battle of Coulmiers</a> ends in a Pyrrhic victory for the French army during the Franco-German War of 1870.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Coulmiers\" title=\"Battle of Coulmiers\">Battle of Coulmiers</a> ends in a Pyrrhic victory for the French army during the Franco-German War of 1870.", "links": [{"title": "Battle of Coulmiers", "link": "https://wikipedia.org/wiki/Battle_of_Coulmiers"}]}, {"year": "1872", "text": "The Great Boston Fire of 1872.", "html": "1872 - The <a href=\"https://wikipedia.org/wiki/Great_Boston_Fire_of_1872\" title=\"Great Boston Fire of 1872\">Great Boston Fire of 1872</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Boston_Fire_of_1872\" title=\"Great Boston Fire of 1872\">Great Boston Fire of 1872</a>.", "links": [{"title": "Great Boston Fire of 1872", "link": "https://wikipedia.org/wiki/Great_Boston_Fire_of_1872"}]}, {"year": "1880", "text": "A major earthquake strikes Zagreb and destroys many buildings, including Zagreb Cathedral.", "html": "1880 - A <a href=\"https://wikipedia.org/wiki/1880_Zagreb_earthquake\" title=\"1880 Zagreb earthquake\">major earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a> and destroys many buildings, including <a href=\"https://wikipedia.org/wiki/Zagreb_Cathedral\" title=\"Zagreb Cathedral\">Zagreb Cathedral</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1880_Zagreb_earthquake\" title=\"1880 Zagreb earthquake\">major earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a> and destroys many buildings, including <a href=\"https://wikipedia.org/wiki/Zagreb_Cathedral\" title=\"Zagreb Cathedral\">Zagreb Cathedral</a>.", "links": [{"title": "1880 Zagreb earthquake", "link": "https://wikipedia.org/wiki/1880_Zagreb_earthquake"}, {"title": "Zagreb", "link": "https://wikipedia.org/wiki/Zagreb"}, {"title": "Zagreb Cathedral", "link": "https://wikipedia.org/wiki/Zagreb_Cathedral"}]}, {"year": "1881", "text": "Mapuche rebels attack the fortified Chilean settlement of Temuco.", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche rebels</a> attack the fortified Chilean settlement of <a href=\"https://wikipedia.org/wiki/Temuco\" title=\"Temuco\">Temuco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1881\" title=\"Mapuche uprising of 1881\">Mapuche rebels</a> attack the fortified Chilean settlement of <a href=\"https://wikipedia.org/wiki/Temuco\" title=\"Temuco\">Temuco</a>.", "links": [{"title": "Mapuche uprising of 1881", "link": "https://wikipedia.org/wiki/Mapuche_uprising_of_1881"}, {"title": "Temuco", "link": "https://wikipedia.org/wiki/Temuco"}]}, {"year": "1887", "text": "The United States receives rights to Pearl Harbor, Hawaii.", "html": "1887 - The United States receives rights to <a href=\"https://wikipedia.org/wiki/Pearl_Harbor\" title=\"Pearl Harbor\">Pearl Harbor</a>, Hawaii.", "no_year_html": "The United States receives rights to <a href=\"https://wikipedia.org/wiki/Pearl_Harbor\" title=\"Pearl Harbor\">Pearl Harbor</a>, Hawaii.", "links": [{"title": "Pearl Harbor", "link": "https://wikipedia.org/wiki/Pearl_Harbor"}]}, {"year": "1888", "text": "<PERSON> the <PERSON><PERSON><PERSON> murders <PERSON>, his final victim in the Whitechapel murders.", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ripper\" title=\"<PERSON> the Ripper\"><PERSON> the Ripper</a> murders <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, his final victim in the <a href=\"https://wikipedia.org/wiki/Whitechapel_murders\" title=\"Whitechapel murders\">Whitechapel murders</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ripper\" title=\"<PERSON> the Ripper\"><PERSON> the Ripper</a> murders <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, his final victim in the <a href=\"https://wikipedia.org/wiki/Whitechapel_murders\" title=\"Whitechapel murders\">Whitechapel murders</a>.", "links": [{"title": "<PERSON> the Rip<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Whitechapel murders", "link": "https://wikipedia.org/wiki/Whitechapel_murders"}]}, {"year": "1900", "text": "Russian invasion of Manchuria: Russia completes its occupation of Manchuria with 100,000 troops.", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Manchuria\" title=\"Russian invasion of Manchuria\">Russian invasion of Manchuria</a>: <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> completes its occupation of <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a> with 100,000 troops.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_invasion_of_Manchuria\" title=\"Russian invasion of Manchuria\">Russian invasion of Manchuria</a>: <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a> completes its occupation of <a href=\"https://wikipedia.org/wiki/Manchuria\" title=\"Manchuria\">Manchuria</a> with 100,000 troops.", "links": [{"title": "Russian invasion of Manchuria", "link": "https://wikipedia.org/wiki/Russian_invasion_of_Manchuria"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Manchuria", "link": "https://wikipedia.org/wiki/Manchuria"}]}, {"year": "1901", "text": "<PERSON> <PERSON>, Duke of Cornwall (later <PERSON> of the United Kingdom), becomes Prince of Wales and Earl of Chester.", "html": "1901 - Prince <PERSON>, <a href=\"https://wikipedia.org/wiki/Duke_of_Cornwall\" title=\"Duke of Cornwall\">Duke of Cornwall</a> (later <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON> V</a> of the United Kingdom), becomes <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a> and <a href=\"https://wikipedia.org/wiki/Earl_<PERSON>_Chester\" title=\"Earl of Chester\">Earl of Chester</a>.", "no_year_html": "<PERSON>, <a href=\"https://wikipedia.org/wiki/Duke_of_Cornwall\" title=\"Duke of Cornwall\">Duke of Cornwall</a> (later <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the United Kingdom), becomes <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chester\" title=\"Earl of Chester\">Earl of Chester</a>.", "links": [{"title": "Duke of Cornwall", "link": "https://wikipedia.org/wiki/Duke_of_Cornwall"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}, {"title": "<PERSON> of Chester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Chester"}]}, {"year": "1905", "text": "The Province of Alberta, Canada, holds its first general election.", "html": "1905 - The Province of <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>, Canada, holds its <a href=\"https://wikipedia.org/wiki/1905_Alberta_general_election\" title=\"1905 Alberta general election\">first general election</a>.", "no_year_html": "The Province of <a href=\"https://wikipedia.org/wiki/Alberta\" title=\"Alberta\">Alberta</a>, Canada, holds its <a href=\"https://wikipedia.org/wiki/1905_Alberta_general_election\" title=\"1905 Alberta general election\">first general election</a>.", "links": [{"title": "Alberta", "link": "https://wikipedia.org/wiki/Alberta"}, {"title": "1905 Alberta general election", "link": "https://wikipedia.org/wiki/1905_Alberta_general_election"}]}, {"year": "1906", "text": "<PERSON> is the first sitting President of the United States to make an official trip outside the country, doing so to inspect progress on the Panama Canal.", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first sitting President of the United States to make an <a href=\"https://wikipedia.org/wiki/List_of_international_trips_made_by_the_President_of_the_United_States#<PERSON>\" class=\"mw-redirect\" title=\"List of international trips made by the President of the United States\">official trip outside the country</a>, doing so to inspect progress on the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is the first sitting President of the United States to make an <a href=\"https://wikipedia.org/wiki/List_of_international_trips_made_by_the_President_of_the_United_States#<PERSON>\" class=\"mw-redirect\" title=\"List of international trips made by the President of the United States\">official trip outside the country</a>, doing so to inspect progress on the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of international trips made by the President of the United States", "link": "https://wikipedia.org/wiki/List_of_international_trips_made_by_the_President_of_the_United_States#<PERSON><PERSON>"}, {"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}]}, {"year": "1907", "text": "The Cullinan Diamond is presented to King <PERSON> on his birthday.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/Cullinan_Diamond\" title=\"Cullinan Diamond\">Cullinan Diamond</a> is presented to <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_VII\" title=\"Edward VII\"><PERSON> VII</a> on his birthday.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cullinan_Diamond\" title=\"Cullinan Diamond\">Cullinan Diamond</a> is presented to <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward VII\"><PERSON> VII</a> on his birthday.", "links": [{"title": "Cullinan Diamond", "link": "https://wikipedia.org/wiki/Cullinan_Diamond"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "The Great Lakes Storm of 1913, the most destructive natural disaster ever to hit the lakes, reaches its greatest intensity after beginning two days earlier. The storm destroys 19 ships and kills more than 250 people.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913\" title=\"Great Lakes Storm of 1913\">Great Lakes Storm of 1913</a>, the most destructive natural disaster ever to hit the lakes, reaches its greatest intensity after beginning two days earlier. The storm destroys 19 ships and kills more than 250 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913\" title=\"Great Lakes Storm of 1913\">Great Lakes Storm of 1913</a>, the most destructive natural disaster ever to hit the lakes, reaches its greatest intensity after beginning two days earlier. The storm destroys 19 ships and kills more than 250 people.", "links": [{"title": "Great Lakes Storm of 1913", "link": "https://wikipedia.org/wiki/Great_Lakes_Storm_of_1913"}]}, {"year": "1914", "text": "SMS <PERSON>den is sunk by HMAS Sydney in the Battle of Cocos.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/SMS_Emden\" title=\"SMS Emden\">SMS <i>Emden</i></a> is sunk by <a href=\"https://wikipedia.org/wiki/HMAS_Sydney_(1912)\" title=\"HMAS Sydney (1912)\">HMAS <i>Sydney</i></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cocos\" title=\"Battle of Cocos\">Battle of Cocos</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SMS_Emden\" title=\"SMS Emden\">SMS <i>Emden</i></a> is sunk by <a href=\"https://wikipedia.org/wiki/HMAS_Sydney_(1912)\" title=\"HMAS Sydney (1912)\">HMAS <i>Sydney</i></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Cocos\" title=\"Battle of Cocos\">Battle of Cocos</a>.", "links": [{"title": "SMS Emden", "link": "https://wikipedia.org/wiki/SMS_Emden"}, {"title": "HMAS Sydney (1912)", "link": "https://wikipedia.org/wiki/HMAS_Sydney_(1912)"}, {"title": "Battle of Cocos", "link": "https://wikipedia.org/wiki/Battle_of_Cocos"}]}, {"year": "1917", "text": "The Balfour Declaration is published in The Times newspaper.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/Balfour_Declaration\" title=\"Balfour Declaration\">Balfour Declaration</a> is published in <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> newspaper.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Balfour_Declaration\" title=\"Balfour Declaration\">Balfour Declaration</a> is published in <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> newspaper.", "links": [{"title": "Balfour Declaration", "link": "https://wikipedia.org/wiki/Balfour_Declaration"}, {"title": "The Times", "link": "https://wikipedia.org/wiki/The_Times"}]}, {"year": "1918", "text": "Kaiser <PERSON> of Germany abdicates after the German Revolution, and Germany is proclaimed a Republic.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Kaiser\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wilhelm_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Wilhelm <PERSON> of Germany</a> abdicates after the <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution</a>, and <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_republic_in_Germany\" title=\"Proclamation of the republic in Germany\">Germany is proclaimed</a> a <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Kaiser\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Wilhelm <PERSON> of Germany</a> abdicates after the <a href=\"https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319\" class=\"mw-redirect\" title=\"German Revolution of 1918-19\">German Revolution</a>, and <a href=\"https://wikipedia.org/wiki/Proclamation_of_the_republic_in_Germany\" title=\"Proclamation of the republic in Germany\">Germany is proclaimed</a> a <a href=\"https://wikipedia.org/wiki/Weimar_Republic\" title=\"Weimar Republic\">Republic</a>.", "links": [{"title": "Kaiser", "link": "https://wikipedia.org/wiki/Kaiser"}, {"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "German Revolution of 1918-19", "link": "https://wikipedia.org/wiki/German_Revolution_of_1918%E2%80%9319"}, {"title": "Proclamation of the republic in Germany", "link": "https://wikipedia.org/wiki/Proclamation_of_the_republic_in_Germany"}, {"title": "Weimar Republic", "link": "https://wikipedia.org/wiki/Weimar_Republic"}]}, {"year": "1921", "text": "The National Fascist Party (Partito Nazionale Fascista or PNF) is founded in Italy.", "html": "1921 - The <a href=\"https://wikipedia.org/wiki/National_Fascist_Party\" title=\"National Fascist Party\">National Fascist Party</a> (Partito Nazionale Fascista or PNF) is founded in <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Fascist_Party\" title=\"National Fascist Party\">National Fascist Party</a> (Partito Nazionale Fascista or PNF) is founded in <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a>.", "links": [{"title": "National Fascist Party", "link": "https://wikipedia.org/wiki/National_Fascist_Party"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}]}, {"year": "1923", "text": "In Munich, police and government troops crush the Nazi Beer Hall Putsch.", "html": "1923 - In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, police and government troops crush the Nazi <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Munich\" title=\"Munich\">Munich</a>, police and government troops crush the Nazi <a href=\"https://wikipedia.org/wiki/Beer_Hall_Putsch\" title=\"Beer Hall Putsch\">Beer Hall Putsch</a>.", "links": [{"title": "Munich", "link": "https://wikipedia.org/wiki/Munich"}, {"title": "Beer Hall Putsch", "link": "https://wikipedia.org/wiki/Beer_Hall_Putsch"}]}, {"year": "1935", "text": "The Committee for Industrial Organization, the precursor to the Congress of Industrial Organizations, is founded in Atlantic City, New Jersey, by eight trade unions belonging to the American Federation of Labor.", "html": "1935 - The Committee for Industrial Organization, the precursor to the <a href=\"https://wikipedia.org/wiki/Congress_of_Industrial_Organizations\" title=\"Congress of Industrial Organizations\">Congress of Industrial Organizations</a>, is founded in <a href=\"https://wikipedia.org/wiki/Atlantic_City,_New_Jersey\" title=\"Atlantic City, New Jersey\">Atlantic City, New Jersey</a>, by eight <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade unions</a> belonging to the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a>.", "no_year_html": "The Committee for Industrial Organization, the precursor to the <a href=\"https://wikipedia.org/wiki/Congress_of_Industrial_Organizations\" title=\"Congress of Industrial Organizations\">Congress of Industrial Organizations</a>, is founded in <a href=\"https://wikipedia.org/wiki/Atlantic_City,_New_Jersey\" title=\"Atlantic City, New Jersey\">Atlantic City, New Jersey</a>, by eight <a href=\"https://wikipedia.org/wiki/Trade_union\" title=\"Trade union\">trade unions</a> belonging to the <a href=\"https://wikipedia.org/wiki/American_Federation_of_Labor\" title=\"American Federation of Labor\">American Federation of Labor</a>.", "links": [{"title": "Congress of Industrial Organizations", "link": "https://wikipedia.org/wiki/Congress_of_Industrial_Organizations"}, {"title": "Atlantic City, New Jersey", "link": "https://wikipedia.org/wiki/Atlantic_City,_New_Jersey"}, {"title": "Trade union", "link": "https://wikipedia.org/wiki/Trade_union"}, {"title": "American Federation of Labor", "link": "https://wikipedia.org/wiki/American_Federation_of_Labor"}]}, {"year": "1936", "text": "American fashion designer <PERSON> encounters and captures a nine-week-old panda cub in Sichuan; it becomes the first live giant panda to enter the United States.", "html": "1936 - American fashion designer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> encounters and captures a nine-week-old panda cub in <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>; it becomes the first live <a href=\"https://wikipedia.org/wiki/Giant_panda\" title=\"Giant panda\">giant panda</a> to enter the United States.", "no_year_html": "American fashion designer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> encounters and captures a nine-week-old panda cub in <a href=\"https://wikipedia.org/wiki/Sichuan\" title=\"Sichuan\">Sichuan</a>; it becomes the first live <a href=\"https://wikipedia.org/wiki/Giant_panda\" title=\"Giant panda\">giant panda</a> to enter the United States.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sichuan", "link": "https://wikipedia.org/wiki/Sichuan"}, {"title": "Giant panda", "link": "https://wikipedia.org/wiki/Giant_panda"}]}, {"year": "1937", "text": "Second Sino-Japanese War: The Chinese Army withdraws from the Battle of Shanghai.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The Chinese Army withdraws from the <a href=\"https://wikipedia.org/wiki/Battle_of_Shanghai\" title=\"Battle of Shanghai\">Battle of Shanghai</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: The Chinese Army withdraws from the <a href=\"https://wikipedia.org/wiki/Battle_of_Shanghai\" title=\"Battle of Shanghai\">Battle of Shanghai</a>.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "Battle of Shanghai", "link": "https://wikipedia.org/wiki/Battle_of_Shanghai"}]}, {"year": "1938", "text": "Kris<PERSON>lnacht occurs, instigated by the Nazis using the killing of German diplomat <PERSON> by <PERSON><PERSON><PERSON> as justification.", "html": "1938 - <i><a href=\"https://wikipedia.org/wiki/Kristallnacht\" title=\"Kristallnacht\">Kristallnacht</a></i> occurs, instigated by the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> using the killing of German diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON> Rat<PERSON>\"><PERSON> v<PERSON></a> by <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_Grynszpan\" title=\"<PERSON><PERSON><PERSON> Grynszpan\"><PERSON><PERSON><PERSON></a> as justification.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Kristallnacht\" title=\"Kristallnacht\">Kris<PERSON>lnacht</a></i> occurs, instigated by the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a> using the killing of German diplomat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>om_<PERSON>\" title=\"<PERSON> v<PERSON> Rat<PERSON>\"><PERSON> v<PERSON></a> by <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_G<PERSON>szpan\" title=\"<PERSON><PERSON><PERSON> Grynszpan\"><PERSON><PERSON><PERSON></a> as justification.", "links": [{"title": "Kristallnacht", "link": "https://wikipedia.org/wiki/Kristallnacht"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "<PERSON> v<PERSON> Rath", "link": "https://wikipedia.org/wiki/<PERSON>_vom_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "Warsaw is awarded the Virtuti Militari by the Polish government-in-exile.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a> is awarded the <a href=\"https://wikipedia.org/wiki/Virtuti_Militari\" title=\"Virtuti Militari\">Virtuti Militari</a> by the <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Polish government-in-exile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a> is awarded the <a href=\"https://wikipedia.org/wiki/Virtuti_Militari\" title=\"Virtuti Militari\">Virtuti Militari</a> by the <a href=\"https://wikipedia.org/wiki/Polish_government-in-exile\" title=\"Polish government-in-exile\">Polish government-in-exile</a>.", "links": [{"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}, {"title": "Virtuti Militari", "link": "https://wikipedia.org/wiki/Virtuti_Militari"}, {"title": "Polish government-in-exile", "link": "https://wikipedia.org/wiki/Polish_government-in-exile"}]}, {"year": "1942", "text": "Battle of Stalingrad: German forces of the 6th Army under general <PERSON> reach finally the river bank of the Volga, capturing 90% of the ruined city of Stalingrad and splitting the remaining Soviet forces into two narrow pockets.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: German forces of the 6th Army under general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach finally the river bank of the Volga, capturing 90% of the ruined city of <a href=\"https://wikipedia.org/wiki/Stalingrad\" class=\"mw-redirect\" title=\"Stalingrad\">Stalingrad</a> and splitting the remaining Soviet forces into two narrow pockets.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: German forces of the 6th Army under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reach finally the river bank of the Volga, capturing 90% of the ruined city of <a href=\"https://wikipedia.org/wiki/Stalingrad\" class=\"mw-redirect\" title=\"Stalingrad\">Stalingrad</a> and splitting the remaining Soviet forces into two narrow pockets.", "links": [{"title": "Battle of Stalingrad", "link": "https://wikipedia.org/wiki/Battle_of_Stalingrad"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Stalingrad", "link": "https://wikipedia.org/wiki/Stalingrad"}]}, {"year": "1943", "text": "An agreement for the foundation of the United Nations Relief and Rehabilitation Administration is signed by 44 countries in the White House, Washington, D.C.", "html": "1943 - An agreement for the foundation of the United Nations Relief and Rehabilitation Administration is signed by 44 countries in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>, Washington, D.C.", "no_year_html": "An agreement for the foundation of the United Nations Relief and Rehabilitation Administration is signed by 44 countries in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>, Washington, D.C.", "links": [{"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1945", "text": "Soo Bahk Do and Moo <PERSON> martial arts are founded in Korea.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Soo Bahk Do\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> martial arts are founded in <a href=\"https://wikipedia.org/wiki/Korea\" title=\"Korea\">Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Soo Bahk Do\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> martial arts are founded in <a href=\"https://wikipedia.org/wiki/Korea\" title=\"Korea\">Korea</a>.", "links": [{"title": "Soo Bahk Do", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Korea", "link": "https://wikipedia.org/wiki/Korea"}]}, {"year": "1953", "text": "Cambodia gains independence from France.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a> gains independence from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodia</a> gains independence from France.", "links": [{"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}]}, {"year": "1960", "text": "<PERSON> is named president of the Ford Motor Company, becoming the first non-Ford family member to serve in that post. He resigns a month later to join the newly elected <PERSON> administration.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named president of the <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a>, becoming the first non-Ford family member to serve in that post. He resigns a month later to join the newly elected <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> administration.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is named president of the <a href=\"https://wikipedia.org/wiki/Ford_Motor_Company\" title=\"Ford Motor Company\">Ford Motor Company</a>, becoming the first non-Ford family member to serve in that post. He resigns a month later to join the newly elected <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> administration.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ford Motor Company", "link": "https://wikipedia.org/wiki/Ford_Motor_Company"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "At a coal mine in Miike, Japan, an explosion kills 458 and hospitalises 839 with carbon monoxide poisoning.", "html": "1963 - At a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_coal_mine\" title=\"Miike coal mine\">coal mine</a> in <a href=\"https://wikipedia.org/wiki/Miike_District,_Fukuoka\" class=\"mw-redirect\" title=\"Miike District, Fukuoka\">Miike</a>, Japan, <a href=\"https://wikipedia.org/wiki/Mi<PERSON>i_Miike_Coal_Mine_disaster\" class=\"mw-redirect\" title=\"Mitsui Miike Coal Mine disaster\">an explosion</a> kills 458 and hospitalises 839 with <a href=\"https://wikipedia.org/wiki/Carbon_monoxide_poisoning\" title=\"Carbon monoxide poisoning\">carbon monoxide poisoning</a>.", "no_year_html": "At a <a href=\"https://wikipedia.org/wiki/Mi<PERSON>_coal_mine\" title=\"Miike coal mine\">coal mine</a> in <a href=\"https://wikipedia.org/wiki/Miike_District,_Fukuoka\" class=\"mw-redirect\" title=\"Miike District, Fukuoka\">Miike</a>, Japan, <a href=\"https://wikipedia.org/wiki/Mi<PERSON>i_Miike_Coal_Mine_disaster\" class=\"mw-redirect\" title=\"Mi<PERSON>i Miike Coal Mine disaster\">an explosion</a> kills 458 and hospitalises 839 with <a href=\"https://wikipedia.org/wiki/Carbon_monoxide_poisoning\" title=\"Carbon monoxide poisoning\">carbon monoxide poisoning</a>.", "links": [{"title": "Miike coal mine", "link": "https://wikipedia.org/wiki/Miike_coal_mine"}, {"title": "Miike District, Fukuoka", "link": "https://wikipedia.org/wiki/Miike_District,_Fukuoka"}, {"title": "<PERSON><PERSON>i Miike Coal Mine disaster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Coal_Mine_disaster"}, {"title": "Carbon monoxide poisoning", "link": "https://wikipedia.org/wiki/Carbon_monoxide_poisoning"}]}, {"year": "1965", "text": "Several U.S. states and parts of Canada are hit by a series of blackouts lasting up to 13 hours in the Northeast blackout of 1965.", "html": "1965 - Several U.S. states and parts of Canada are hit by a series of <a href=\"https://wikipedia.org/wiki/Power_outage\" title=\"Power outage\">blackouts</a> lasting up to 13 hours in the <a href=\"https://wikipedia.org/wiki/Northeast_blackout_of_1965\" title=\"Northeast blackout of 1965\">Northeast blackout of 1965</a>.", "no_year_html": "Several U.S. states and parts of Canada are hit by a series of <a href=\"https://wikipedia.org/wiki/Power_outage\" title=\"Power outage\">blackouts</a> lasting up to 13 hours in the <a href=\"https://wikipedia.org/wiki/Northeast_blackout_of_1965\" title=\"Northeast blackout of 1965\">Northeast blackout of 1965</a>.", "links": [{"title": "Power outage", "link": "https://wikipedia.org/wiki/Power_outage"}, {"title": "Northeast blackout of 1965", "link": "https://wikipedia.org/wiki/Northeast_blackout_of_1965"}]}, {"year": "1965", "text": "A Catholic Worker Movement member, <PERSON>, protesting against the Vietnam War, sets himself on fire in front of the United Nations building.", "html": "1965 - A <a href=\"https://wikipedia.org/wiki/Catholic_Worker_Movement\" title=\"Catholic Worker Movement\">Catholic Worker Movement</a> member, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, protesting against the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">sets himself on fire</a> in front of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> building.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Catholic_Worker_Movement\" title=\"Catholic Worker Movement\">Catholic Worker Movement</a> member, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, protesting against the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/Self-immolation\" title=\"Self-immolation\">sets himself on fire</a> in front of the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> building.", "links": [{"title": "Catholic Worker Movement", "link": "https://wikipedia.org/wiki/Catholic_Worker_Movement"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Self-immolation", "link": "https://wikipedia.org/wiki/Self-immolation"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1967", "text": "Apollo program: NASA launches the unmanned Apollo 4 test spacecraft, atop the first Saturn V rocket, from Florida's Cape Kennedy.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the unmanned <a href=\"https://wikipedia.org/wiki/Apollo_4\" title=\"Apollo 4\">Apollo 4</a> test spacecraft, atop the first <a href=\"https://wikipedia.org/wiki/Saturn_V\" title=\"Saturn V\">Saturn V</a> rocket, from Florida's <a href=\"https://wikipedia.org/wiki/Cape_Canaveral#Cape_Kennedy\" title=\"Cape Canaveral\"><PERSON> Kennedy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> launches the unmanned <a href=\"https://wikipedia.org/wiki/Apollo_4\" title=\"Apollo 4\">Apollo 4</a> test spacecraft, atop the first <a href=\"https://wikipedia.org/wiki/Saturn_V\" title=\"Saturn V\">Saturn V</a> rocket, from Florida's <a href=\"https://wikipedia.org/wiki/Cape_Canaveral#Cape_Kennedy\" title=\"Cape Canaveral\"><PERSON> Kennedy</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Apollo 4", "link": "https://wikipedia.org/wiki/Apollo_4"}, {"title": "Saturn V", "link": "https://wikipedia.org/wiki/Saturn_V"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral#Cape_Kennedy"}]}, {"year": "1970", "text": "Vietnam War: The Supreme Court of the United States votes 6-3 against hearing a case to allow Massachusetts to enforce its law granting residents the right to refuse military service in an undeclared war.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> <a href=\"https://wikipedia.org/wiki/Massachusetts_v._<PERSON>rd\" title=\"Massachusetts v. Laird\">votes 6-3 against hearing a case</a> to allow <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> to enforce its law granting residents the right to refuse military service in an undeclared war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">Supreme Court of the United States</a> <a href=\"https://wikipedia.org/wiki/Massachusetts_v._<PERSON>rd\" title=\"Massachusetts v. Laird\">votes 6-3 against hearing a case</a> to allow <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> to enforce its law granting residents the right to refuse military service in an undeclared war.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Massachusetts v. <PERSON>rd", "link": "https://wikipedia.org/wiki/Massachusetts_v._<PERSON>rd"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}]}, {"year": "1971", "text": "American banker <PERSON> murdered his wife, mother, and three children with a pair of handguns.", "html": "1971 - American banker <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(murderer)\" title=\"<PERSON> (murderer)\"><PERSON></a> murdered his wife, mother, and three children with a pair of handguns.", "no_year_html": "American banker <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(murderer)\" title=\"<PERSON> (murderer)\"><PERSON></a> murdered his wife, mother, and three children with a pair of handguns.", "links": [{"title": "<PERSON> (murderer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(murderer)"}]}, {"year": "1979", "text": "Cold War: Nuclear false alarm: The NORAD computers and the Alternate National Military Command Center in Fort Ritchie, Maryland, detect a purported massive Soviet nuclear strike. After reviewing the raw data from satellites and checking the early-warning radars, the alert is cancelled.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command#False_alarms\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">Nuclear false alarm</a>: The <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">NORAD</a> computers and the Alternate National Military Command Center in <a href=\"https://wikipedia.org/wiki/Fort_Ritchie,_Maryland\" title=\"Fort Ritchie, Maryland\">Fort Ritchie, Maryland</a>, detect a purported massive <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> nuclear strike. After reviewing the raw data from satellites and checking the <a href=\"https://wikipedia.org/wiki/Early-warning_radar\" title=\"Early-warning radar\">early-warning radars</a>, the alert is cancelled.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command#False_alarms\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">Nuclear false alarm</a>: The <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">NORAD</a> computers and the Alternate National Military Command Center in <a href=\"https://wikipedia.org/wiki/Fort_Ritchie,_Maryland\" title=\"Fort Ritchie, Maryland\">Fort Ritchie, Maryland</a>, detect a purported massive <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> nuclear strike. After reviewing the raw data from satellites and checking the <a href=\"https://wikipedia.org/wiki/Early-warning_radar\" title=\"Early-warning radar\">early-warning radars</a>, the alert is cancelled.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "North American Aerospace Defense Command", "link": "https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command#False_alarms"}, {"title": "North American Aerospace Defense Command", "link": "https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command"}, {"title": "Fort Ritchie, Maryland", "link": "https://wikipedia.org/wiki/Fort_Ritchie,_Maryland"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Early-warning radar", "link": "https://wikipedia.org/wiki/Early-warning_radar"}]}, {"year": "1985", "text": "<PERSON>, 22, of the Soviet Union, becomes the youngest World Chess Champion by beating fellow Soviet <PERSON><PERSON><PERSON>.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 22, of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, becomes the youngest <a href=\"https://wikipedia.org/wiki/World_Chess_Champion\" class=\"mw-redirect\" title=\"World Chess Champion\">World Chess Champion</a> by beating fellow Soviet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 22, of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>, becomes the youngest <a href=\"https://wikipedia.org/wiki/World_Chess_Champion\" class=\"mw-redirect\" title=\"World Chess Champion\">World Chess Champion</a> by beating fellow Soviet <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "World Chess Champion", "link": "https://wikipedia.org/wiki/World_Chess_Champion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "Cold War: Fall of the Berlin Wall: East Germany opens checkpoints in the Berlin Wall, allowing its citizens to travel to West Berlin.", "html": "1989 - Cold War: <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">Fall of the Berlin Wall</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> opens <a href=\"https://wikipedia.org/wiki/Border_checkpoint\" title=\"Border checkpoint\">checkpoints</a> in the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>, allowing its citizens to travel to West Berlin.", "no_year_html": "Cold War: <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">Fall of the Berlin Wall</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> opens <a href=\"https://wikipedia.org/wiki/Border_checkpoint\" title=\"Border checkpoint\">checkpoints</a> in the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>, allowing its citizens to travel to West Berlin.", "links": [{"title": "Fall of the Berlin Wall", "link": "https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Border checkpoint", "link": "https://wikipedia.org/wiki/Border_checkpoint"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "1993", "text": "Stari Most, the \"old bridge\" in the Bosnian city of Mostar, built in 1566, collapses after several days of bombing by Croat forces during the Croat-Bosniak War.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Stari_Most\" title=\"Stari Most\">Stari Most</a>, the \"old bridge\" in the <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnian</a> city of <a href=\"https://wikipedia.org/wiki/Mostar\" title=\"Mostar\">Mostar</a>, built in <a href=\"https://wikipedia.org/wiki/1566\" title=\"1566\">1566</a>, collapses after several days of bombing by Croat forces during the <a href=\"https://wikipedia.org/wiki/Croat%E2%80%93Bosniak_War\" title=\"Croat-Bosniak War\">Croat-Bosniak War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stari_Most\" title=\"Stari Most\">Stari Most</a>, the \"old bridge\" in the <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnian</a> city of <a href=\"https://wikipedia.org/wiki/Mostar\" title=\"Mostar\">Mostar</a>, built in <a href=\"https://wikipedia.org/wiki/1566\" title=\"1566\">1566</a>, collapses after several days of bombing by Croat forces during the <a href=\"https://wikipedia.org/wiki/Croat%E2%80%93Bosniak_War\" title=\"Croat-Bosniak War\">Croat-Bosniak War</a>.", "links": [{"title": "Stari Most", "link": "https://wikipedia.org/wiki/Stari_Most"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mostar"}, {"title": "1566", "link": "https://wikipedia.org/wiki/1566"}, {"title": "Croat-Bosniak War", "link": "https://wikipedia.org/wiki/Croat%E2%80%93Bosniak_War"}]}, {"year": "1994", "text": "The chemical element darmstadtium is discovered.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/Chemical_element\" title=\"Chemical element\">chemical element</a> <a href=\"https://wikipedia.org/wiki/Darmstadtium\" title=\"Darmstadtium\">darmstadtium</a> is discovered.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chemical_element\" title=\"Chemical element\">chemical element</a> <a href=\"https://wikipedia.org/wiki/Darmstadtium\" title=\"Darmstadtium\">darmstadtium</a> is discovered.", "links": [{"title": "Chemical element", "link": "https://wikipedia.org/wiki/Chemical_element"}, {"title": "Darmstadtium", "link": "https://wikipedia.org/wiki/Darmstadtium"}]}, {"year": "1998", "text": "A U.S. federal judge, in the largest civil settlement in American history, orders 37 U.S. brokerage houses to pay US$1.03 billion to cheated NASDAQ investors to compensate for price fixing.", "html": "1998 - A U.S. federal judge, in the largest civil settlement in American history, orders 37 U.S. brokerage houses to pay US$1.03 billion to cheated <a href=\"https://wikipedia.org/wiki/NASDAQ\" class=\"mw-redirect\" title=\"NASDAQ\">NASDAQ</a> investors to compensate for <a href=\"https://wikipedia.org/wiki/Price_fixing\" title=\"Price fixing\">price fixing</a>.", "no_year_html": "A U.S. federal judge, in the largest civil settlement in American history, orders 37 U.S. brokerage houses to pay US$1.03 billion to cheated <a href=\"https://wikipedia.org/wiki/NASDAQ\" class=\"mw-redirect\" title=\"NASDAQ\">NASDAQ</a> investors to compensate for <a href=\"https://wikipedia.org/wiki/Price_fixing\" title=\"Price fixing\">price fixing</a>.", "links": [{"title": "NASDAQ", "link": "https://wikipedia.org/wiki/NASDAQ"}, {"title": "Price fixing", "link": "https://wikipedia.org/wiki/Price_fixing"}]}, {"year": "1998", "text": "Capital punishment in the United Kingdom, already abolished for murder, is completely abolished for all remaining capital offences.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom\" title=\"Capital punishment in the United Kingdom\">Capital punishment in the United Kingdom</a>, already abolished for murder, is completely abolished for all remaining capital offences.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom\" title=\"Capital punishment in the United Kingdom\">Capital punishment in the United Kingdom</a>, already abolished for murder, is completely abolished for all remaining capital offences.", "links": [{"title": "Capital punishment in the United Kingdom", "link": "https://wikipedia.org/wiki/Capital_punishment_in_the_United_Kingdom"}]}, {"year": "1999", "text": "TAESA Flight 725 crashes after takeoff from Uruapan International Airport in Uruapan, Michoacán, Mexico, killing all 18 people on board.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/TAESA_Flight_725\" title=\"TAESA Flight 725\">TAESA Flight 725</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Uruapan_International_Airport\" title=\"Uruapan International Airport\">Uruapan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Uruapan\" title=\"Uruapan\">Uruapan</a>, <a href=\"https://wikipedia.org/wiki/Michoac%C3%A1n\" title=\"Michoacán\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>, killing all 18 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAESA_Flight_725\" title=\"TAESA Flight 725\">TAESA Flight 725</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Uruapan_International_Airport\" title=\"Uruapan International Airport\">Uruapan International Airport</a> in <a href=\"https://wikipedia.org/wiki/Uruapan\" title=\"Uruapan\">Uruapan</a>, <a href=\"https://wikipedia.org/wiki/Michoac%C3%A1n\" title=\"Michoacán\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Mexico\" title=\"Mexico\">Mexico</a>, killing all 18 people on board.", "links": [{"title": "TAESA Flight 725", "link": "https://wikipedia.org/wiki/TAESA_Flight_725"}, {"title": "Uruapan International Airport", "link": "https://wikipedia.org/wiki/Uruapan_International_Airport"}, {"title": "Uruapan", "link": "https://wikipedia.org/wiki/Uruapan"}, {"title": "Michoacán", "link": "https://wikipedia.org/wiki/Michoac%C3%A1n"}, {"title": "Mexico", "link": "https://wikipedia.org/wiki/Mexico"}]}, {"year": "2000", "text": "Uttarakhand officially becomes the 27th state of India, formed from thirteen districts of northwestern Uttar Pradesh.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Uttarakhand\" title=\"Uttarakhand\">Uttarakhand</a> officially <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh_Reorganisation_Act,_2000\" title=\"Uttar Pradesh Reorganisation Act, 2000\">becomes</a> the 27th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from thirteen districts of northwestern <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh\" title=\"Uttar Pradesh\">Uttar Pradesh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uttarakhand\" title=\"Uttarakhand\">Uttarakhand</a> officially <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh_Reorganisation_Act,_2000\" title=\"Uttar Pradesh Reorganisation Act, 2000\">becomes</a> the 27th <a href=\"https://wikipedia.org/wiki/States_of_India\" class=\"mw-redirect\" title=\"States of India\">state of India</a>, formed from thirteen districts of northwestern <a href=\"https://wikipedia.org/wiki/Uttar_Pradesh\" title=\"Uttar Pradesh\">Uttar Pradesh</a>.", "links": [{"title": "Uttarakhand", "link": "https://wikipedia.org/wiki/Uttarakhand"}, {"title": "Uttar Pradesh Reorganisation Act, 2000", "link": "https://wikipedia.org/wiki/Uttar_Pradesh_Reorganisation_Act,_2000"}, {"title": "States of India", "link": "https://wikipedia.org/wiki/States_of_India"}, {"title": "Uttar Pradesh", "link": "https://wikipedia.org/wiki/Uttar_Pradesh"}]}, {"year": "2004", "text": "Firefox 1.0 is released.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Firefox\" title=\"Firefox\">Firefox</a> 1.0 is released.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Firefox\" title=\"Firefox\">Firefox</a> 1.0 is released.", "links": [{"title": "Firefox", "link": "https://wikipedia.org/wiki/Firefox"}]}, {"year": "2005", "text": "The Venus Express mission of the European Space Agency is launched from the Baikonur Cosmodrome in Kazakhstan.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Venus_Express\" title=\"Venus Express\">Venus Express</a> mission of the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a> is launched from the <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> in <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Venus_Express\" title=\"Venus Express\">Venus Express</a> mission of the <a href=\"https://wikipedia.org/wiki/European_Space_Agency\" title=\"European Space Agency\">European Space Agency</a> is launched from the <a href=\"https://wikipedia.org/wiki/Baikonur_Cosmodrome\" title=\"Baikonur Cosmodrome\">Baikonur Cosmodrome</a> in <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "links": [{"title": "Venus Express", "link": "https://wikipedia.org/wiki/Venus_Express"}, {"title": "European Space Agency", "link": "https://wikipedia.org/wiki/European_Space_Agency"}, {"title": "Baikonur Cosmodrome", "link": "https://wikipedia.org/wiki/Baikonur_Cosmodrome"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}, {"year": "2005", "text": "Suicide bombers attack three hotels in Amman, Jordan, killing at least 60 people.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/2005_Amman_bombings\" title=\"2005 Amman bombings\">Suicide bombers attack</a> three hotels in <a href=\"https://wikipedia.org/wiki/Amman,_Jordan\" class=\"mw-redirect\" title=\"Amman, Jordan\">Amman, Jordan</a>, killing at least 60 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2005_Amman_bombings\" title=\"2005 Amman bombings\">Suicide bombers attack</a> three hotels in <a href=\"https://wikipedia.org/wiki/Amman,_Jordan\" class=\"mw-redirect\" title=\"Amman, Jordan\">Amman, Jordan</a>, killing at least 60 people.", "links": [{"title": "2005 Amman bombings", "link": "https://wikipedia.org/wiki/2005_Amman_bombings"}, {"title": "Amman, Jordan", "link": "https://wikipedia.org/wiki/Amman,_Jordan"}]}, {"year": "2011", "text": "The first national test of the Emergency Alert System is activated in the United States at 2:00 p.m. EST.", "html": "2011 - The first national test of the <a href=\"https://wikipedia.org/wiki/Emergency_Alert_System\" title=\"Emergency Alert System\">Emergency Alert System</a> is activated in the United States at 2:00 p.m. EST.", "no_year_html": "The first national test of the <a href=\"https://wikipedia.org/wiki/Emergency_Alert_System\" title=\"Emergency Alert System\">Emergency Alert System</a> is activated in the United States at 2:00 p.m. EST.", "links": [{"title": "Emergency Alert System", "link": "https://wikipedia.org/wiki/Emergency_Alert_System"}]}, {"year": "2012", "text": "A train carrying liquid fuel crashes and bursts into flames in northern Myanmar, killing 27 people and injuring 80 others.", "html": "2012 - A train carrying liquid fuel <a href=\"https://wikipedia.org/wiki/2012_Myanmar_train_crash\" title=\"2012 Myanmar train crash\">crashes and bursts into flames</a> in northern <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing 27 people and injuring 80 others.", "no_year_html": "A train carrying liquid fuel <a href=\"https://wikipedia.org/wiki/2012_Myanmar_train_crash\" title=\"2012 Myanmar train crash\">crashes and bursts into flames</a> in northern <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing 27 people and injuring 80 others.", "links": [{"title": "2012 Myanmar train crash", "link": "https://wikipedia.org/wiki/2012_Myanmar_train_crash"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "2012", "text": "At least 27 people are killed and dozens are wounded in conflicts between inmates and guards at Welikada prison in Colombo.", "html": "2012 - At least 27 people are killed and dozens are wounded in <a href=\"https://wikipedia.org/wiki/2012_Welikada_prison_riot\" title=\"2012 Welikada prison riot\">conflicts</a> between inmates and guards at Welikada prison in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>.", "no_year_html": "At least 27 people are killed and dozens are wounded in <a href=\"https://wikipedia.org/wiki/2012_Welikada_prison_riot\" title=\"2012 Welikada prison riot\">conflicts</a> between inmates and guards at Welikada prison in <a href=\"https://wikipedia.org/wiki/Colombo\" title=\"Colombo\">Colombo</a>.", "links": [{"title": "2012 Welikada prison riot", "link": "https://wikipedia.org/wiki/2012_Welikada_prison_riot"}, {"title": "Colombo", "link": "https://wikipedia.org/wiki/Colombo"}]}, {"year": "2014", "text": "A non-binding self-determination consultation is held in Catalonia, asking Catalan citizens their opinion on whether Catalonia should become a state and, if so, whether it should be an independent state.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/Catalan_self-determination_referendum\" class=\"mw-redirect\" title=\"Catalan self-determination referendum\">non-binding self-determination consultation</a> is held in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, asking Catalan citizens their opinion on whether Catalonia should become a state and, if so, whether it should be an independent state.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Catalan_self-determination_referendum\" class=\"mw-redirect\" title=\"Catalan self-determination referendum\">non-binding self-determination consultation</a> is held in <a href=\"https://wikipedia.org/wiki/Catalonia\" title=\"Catalonia\">Catalonia</a>, asking Catalan citizens their opinion on whether Catalonia should become a state and, if so, whether it should be an independent state.", "links": [{"title": "Catalan self-determination referendum", "link": "https://wikipedia.org/wiki/Catalan_self-determination_referendum"}, {"title": "Catalonia", "link": "https://wikipedia.org/wiki/Catalonia"}]}, {"year": "2020", "text": "Second Nagorno-Karabakh War: An armistice agreement is signed by Armenia, Azerbaijan and Russia.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: An armistice <a href=\"https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement\" title=\"2020 Nagorno-Karabakh ceasefire agreement\">agreement</a> is signed by <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> and <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War\" title=\"Second Nagorno-Karabakh War\">Second Nagorno-Karabakh War</a>: An armistice <a href=\"https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement\" title=\"2020 Nagorno-Karabakh ceasefire agreement\">agreement</a> is signed by <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> and <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "links": [{"title": "Second Nagorno-Karabakh War", "link": "https://wikipedia.org/wiki/Second_Nagorno-Karabakh_War"}, {"title": "2020 Nagorno-Karabakh ceasefire agreement", "link": "https://wikipedia.org/wiki/2020_Nagorno-Karabakh_ceasefire_agreement"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "2023", "text": "U.S. surgeons at NYU Langone Health announce the world's first whole eye transplant.", "html": "2023 - U.S. surgeons at <a href=\"https://wikipedia.org/wiki/NYU_Langone_Health\" title=\"NYU Langone Health\">NYU Langone Health</a> announce the <a href=\"https://wikipedia.org/wiki/Eye_transplantation#2023_NYU_Langone_Health_attempt\" title=\"Eye transplantation\">world's first whole eye transplant</a>.", "no_year_html": "U.S. surgeons at <a href=\"https://wikipedia.org/wiki/NYU_Langone_Health\" title=\"NYU Langone Health\">NYU Langone Health</a> announce the <a href=\"https://wikipedia.org/wiki/Eye_transplantation#2023_NYU_Langone_Health_attempt\" title=\"Eye transplantation\">world's first whole eye transplant</a>.", "links": [{"title": "NYU Langone Health", "link": "https://wikipedia.org/wiki/NYU_Langone_Health"}, {"title": "Eye transplantation", "link": "https://wikipedia.org/wiki/Eye_transplantation#2023_NYU_Langone_Health_attempt"}]}], "Births": [{"year": "955", "text": "<PERSON><PERSON><PERSON><PERSON>, Korean king (d. 981)", "html": "955 - <a href=\"https://wikipedia.org/wiki/Gyeongjong_of_Goryeo\" title=\"<PERSON><PERSON><PERSON>jong of Goryeo\"><PERSON><PERSON><PERSON><PERSON></a>, Korean king (d. 981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gyeongjong_of_Goryeo\" title=\"<PERSON>yeongjong of Goryeo\"><PERSON><PERSON><PERSON><PERSON></a>, Korean king (d. 981)", "links": [{"title": "<PERSON><PERSON>ongjong of Goryeo", "link": "https://wikipedia.org/wiki/Gyeongjong_of_Goryeo"}]}, {"year": "1383", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Marquis of Ferrara (d. 1441)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_III_d%27<PERSON><PERSON>,_Marquis_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Ferrara\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Ferrara</a> (d. 1441)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Niccol%C3%B2_III_d%27<PERSON><PERSON>,_Marquis_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Marquis of Ferrara\"><PERSON><PERSON><PERSON><PERSON>, Marquis of Ferrara</a> (d. 1441)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Marquis of Ferrara", "link": "https://wikipedia.org/wiki/Niccol%C3%B2_III_d%27Este,_<PERSON>_<PERSON>_Ferra<PERSON>"}]}, {"year": "1389", "text": "<PERSON> of Valois, French princess and queen of England (d. 1409)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"Isabella of Valois\">Isabella of Valois</a>, French princess and queen of England (d. 1409)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"Isabella of Valois\">Isabella of Valois</a>, French princess and queen of England (d. 1409)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois"}]}, {"year": "1414", "text": "<PERSON><PERSON>, Elector of Brandenburg (d. 1486)", "html": "1414 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Achilles,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> III Achilles, Elector of Brandenburg\"><PERSON><PERSON>, Elector of Brandenburg</a> (d. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_Achilles,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON><PERSON> III Achilles, Elector of Brandenburg\"><PERSON><PERSON>, Elector of Brandenburg</a> (d. 1486)", "links": [{"title": "<PERSON><PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1455", "text": "<PERSON>, Count of Nassau-Siegen, German count (d. 1516)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (d. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (d. 1516)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1467", "text": "<PERSON> <PERSON>, Duke of Guelders, count of Zutphen from 1492 (d. 1538)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON>, Duke of Guelders</a>, count of Zutphen from 1492 (d. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON>, Duke of Guelders</a>, count of Zutphen from 1492 (d. 1538)", "links": [{"title": "<PERSON>, Duke of Guelders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders"}]}, {"year": "1467", "text": "<PERSON><PERSON> of Guelders, twin sister of <PERSON>, Dutch duchess consort (d. 1547)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Guelders\" title=\"<PERSON><PERSON> of Guelders\"><PERSON><PERSON> of Guelders</a>, twin sister of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON></a>, Dutch duchess consort (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Guelders\" title=\"<PERSON><PERSON> of Guelders\"><PERSON><PERSON> of Guelders</a>, twin sister of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders\" title=\"<PERSON>, Duke of Guelders\"><PERSON> II</a>, Dutch duchess consort (d. 1547)", "links": [{"title": "<PERSON><PERSON> of Guelders", "link": "https://wikipedia.org/wiki/Philippa_of_Guelders"}, {"title": "<PERSON>, Duke of Guelders", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Guelders"}]}, {"year": "1522", "text": "<PERSON>, German astrologer and theologian (d. 1586)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astrologer and theologian (d. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astrologer and theologian (d. 1586)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1535", "text": "<PERSON><PERSON>, king of Burma (d. 1600)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/Nanda_Bayin\" title=\"Nanda Bayin\"><PERSON><PERSON></a>, king of Burma (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nanda_Bayin\" title=\"Nanda Bayin\"><PERSON><PERSON></a>, king of Burma (d. 1600)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1580", "text": "<PERSON>, Dutch physician and poet (d. 1637)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician and poet (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician and poet (d. 1637)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, German philosopher and educator (d. 1681)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and educator (d. 1681)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and educator (d. 1681)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, German organist and composer (d. 1719)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, English librarian and author (d. 1695)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English librarian and author (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, English librarian and author (d. 1695)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1666", "text": "<PERSON>, Swedish officer, general and f<PERSON><PERSON>re (d. 1736)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish officer, general and <a href=\"https://wikipedia.org/wiki/Friherre\" class=\"mw-redirect\" title=\"Friherre\">fri<PERSON>re</a> (d. 1736)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish officer, general and <a href=\"https://wikipedia.org/wiki/Friherre\" class=\"mw-redirect\" title=\"Friherre\">fri<PERSON>re</a> (d. 1736)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Frih<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Friherre"}]}, {"year": "1683", "text": "<PERSON> of Great Britain (d. 1760)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> II of Great Britain\"><PERSON> of Great Britain</a> (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> of Great Britain</a> (d. 1760)", "links": [{"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}]}, {"year": "1697", "text": "<PERSON>, Italian singer and composer (d. 1760)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer and composer (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer and composer (d. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, Italian priest, theoretician, and academic (d. 1796)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, theoretician, and academic (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest, theoretician, and academic (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON>, English physician and poet (d. 1770)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and poet (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and poet (d. 1770)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, Abbess of Quedlinburg (d. 1787)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (d. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg\" title=\"<PERSON>, Abbess of Quedlinburg\"><PERSON>, Abbess of Quedlinburg</a> (d. 1787)", "links": [{"title": "<PERSON>, Abbess of Quedlinburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Abbess_of_Quedlinburg"}]}, {"year": "1731", "text": "<PERSON>, American farmer, surveyor, and author (d. 1806)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, surveyor, and author (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer, surveyor, and author (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1732", "text": "<PERSON>, French businesswoman and author (d. 1776)", "html": "1732 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89l%C3%A9<PERSON><PERSON>_<PERSON>_Lespinasse\" title=\"<PERSON>\"><PERSON></a>, French businesswoman and author (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89l%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>pina<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businesswoman and author (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%89l%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Danish author (d. 1856)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-Ehrensv%C3%A4rd\" title=\"<PERSON><PERSON>-<PERSON>svärd\"><PERSON><PERSON>-<PERSON>värd</a>, Danish author (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-Ehrensv%C3%A4rd\" title=\"<PERSON><PERSON>-<PERSON>hrensvärd\"><PERSON><PERSON>-<PERSON>värd</a>, Danish author (d. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>-Ehrensv%C3%A4rd"}]}, {"year": "1780", "text": "<PERSON><PERSON>, Norwegian priest, writer and politician (d. 1848)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian priest, writer and politician (d. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian priest, writer and politician (d. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Prince of Vasa (d. 1877)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Vasa\" title=\"<PERSON>, Prince of Vasa\"><PERSON>, Prince of Vasa</a> (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Vasa\" title=\"<PERSON>, Prince of Vasa\"><PERSON>, Prince of Vasa</a> (d. 1877)", "links": [{"title": "<PERSON>, Prince of Vasa", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Vasa"}]}, {"year": "1801", "text": "<PERSON>, American surveyor and publisher, invented condensed milk (d. 1874)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and publisher, invented <a href=\"https://wikipedia.org/wiki/Condensed_milk\" title=\"Condensed milk\">condensed milk</a> (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and publisher, invented <a href=\"https://wikipedia.org/wiki/Condensed_milk\" title=\"Condensed milk\">condensed milk</a> (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Condensed milk", "link": "https://wikipedia.org/wiki/Condensed_milk"}]}, {"year": "1802", "text": "<PERSON>, American minister, journalist, and activist (d. 1837)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Elijah_Parish_Lovejoy\" title=\"Elijah Parish Lovejoy\"><PERSON> Lovejoy</a>, American minister, journalist, and activist (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elijah_Parish_Lovejoy\" title=\"Elijah Parish Lovejoy\">Elijah Parish Lovejoy</a>, American minister, journalist, and activist (d. 1837)", "links": [{"title": "<PERSON> Lovejoy", "link": "https://wikipedia.org/wiki/Elijah_Parish_Lovejoy"}]}, {"year": "1810", "text": "<PERSON>, German general, surgeon, and academic (d. 1887)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general, surgeon, and academic (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general, surgeon, and academic (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Russian author and playwright (d. 1883)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and playwright (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON> <PERSON><PERSON>, American general (d. 1865)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/A._<PERSON>._<PERSON>\" title=\"A. P. Hill\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A._<PERSON>._<PERSON>\" title=\"A. P. Hill\"><PERSON><PERSON> <PERSON><PERSON></a>, American general (d. 1865)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A._P._Hill"}]}, {"year": "1829", "text": "<PERSON>, English general (d. 1918)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, French author and journalist (d. 1873)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Gaboriau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and journalist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Gaboriau\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French author and journalist (d. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Gaboriau"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, 5th Premier of Quebec (d. 1898)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1841", "text": "<PERSON> of the United Kingdom (d. 1910)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward VII\"><PERSON></a> of the United Kingdom (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII\" title=\"Edward VII\"><PERSON></a> of the United Kingdom (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, German pharmacologist and academic (d. 1929)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pharmacologist and academic (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pharmacologist and academic (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON>, American architect and partner, co-founded McKim, Mead & White (d. 1906)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"<PERSON> White\"><PERSON></a>, American architect and partner, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON>_%26_White\" title=\"<PERSON><PERSON><PERSON><PERSON>, Mead &amp; <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Mead &amp; White</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stanford_White\" title=\"<PERSON> White\"><PERSON></a>, American architect and partner, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON>_%26_White\" title=\"<PERSON><PERSON><PERSON><PERSON>, Mead &amp; <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Mead &amp; White</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stanford_White"}, {"title": "McKim, Mead & White", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_<PERSON>_%26_White"}]}, {"year": "1854", "text": "<PERSON>, American activist and author (d. 1948)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and author (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON><PERSON>, Georgian painter and educator (d. 1936)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian painter and educator (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian painter and educator (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Canadian-American actress and singer (d. 1934)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress and singer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American medical scientist (d. 1953)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Florence_R._<PERSON>\" title=\"Florence R. Sa<PERSON>\"><PERSON> <PERSON><PERSON></a>, American medical scientist (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_R._<PERSON>\" title=\"Florence R. Sabin\"><PERSON> <PERSON></a>, American medical scientist (d. 1953)", "links": [{"title": "Florence R<PERSON>", "link": "https://wikipedia.org/wiki/Florence_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Ukrainian author and poet (d. 1941)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian author and poet (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian author and poet (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON>, German neurologist and surgeon (d. 1941)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and surgeon (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and surgeon (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otf<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American botanist and academic (d. 1954)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and academic (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and academic (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Italian journalist, lawyer, and politician, 1st President of the Italian Republic (d. 1959)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, lawyer, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Italian_Republic\" class=\"mw-redirect\" title=\"President of the Italian Republic\">President of the Italian Republic</a> (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Italian Republic", "link": "https://wikipedia.org/wiki/President_of_the_Italian_Republic"}]}, {"year": "1877", "text": "<PERSON>, Pakistani philosopher, poet, and politician (d. 1938)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani philosopher, poet, and politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani philosopher, poet, and politician (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Korean activist and politician (d. 1938)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Ah<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Korean activist and politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ah<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Ah<PERSON>\"><PERSON><PERSON></a>, Korean activist and politician (d. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahn_<PERSON><PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON>, Hungarian architect and sculptor (d. 1959)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Jen%C5%91_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian architect and sculptor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen%C5%91_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian architect and sculptor (d. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jen%C5%91_Bory"}]}, {"year": "1879", "text": "<PERSON>, Croatian historian and politician (d. 1931)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Milan_%C5%A0ufflay\" title=\"<PERSON> Š<PERSON>\"><PERSON></a>, Croatian historian and politician (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_%C5%A0ufflay\" title=\"<PERSON> Š<PERSON>\"><PERSON></a>, Croatian historian and politician (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_%C5%A0ufflay"}]}, {"year": "1880", "text": "<PERSON>, English architect, designed the red telephone box (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Red_telephone_box\" title=\"Red telephone box\">red telephone box</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Red_telephone_box\" title=\"Red telephone box\">red telephone box</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Red telephone box", "link": "https://wikipedia.org/wiki/Red_telephone_box"}]}, {"year": "1883", "text": "<PERSON>, American actress (d. 1942)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German mathematician and physicist (d. 1954)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Russian poet and playwright (d. 1922)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and playwright (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and playwright (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Italian tenor and educator (d. 1952)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tenor and educator (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian tenor and educator (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German mathematician, physicist, and philosopher (d. 1955)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, physicist, and philosopher (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, physicist, and philosopher (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American actor (d. 1966)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wynn"}]}, {"year": "1888", "text": "<PERSON>, French economist and diplomat (d. 1979)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and diplomat (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and diplomat (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, American botanist and parapsychologist (d. 1983)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and parapsychologist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and parapsychologist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actress (d. 1968)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Marsh\"><PERSON></a>, American actress (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, General of the German Army during World War II (d. 1966)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, General of the <a href=\"https://wikipedia.org/wiki/German_Army_(Wehrmacht)\" class=\"mw-redirect\" title=\"German Army (Wehrmacht)\">German Army</a> during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, General of the <a href=\"https://wikipedia.org/wiki/German_Army_(Wehrmacht)\" class=\"mw-redirect\" title=\"German Army (Wehrmacht)\">German Army</a> during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "German Army (Wehrmacht)", "link": "https://wikipedia.org/wiki/German_Army_(Wehrmacht)"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1897", "text": "<PERSON>, American baseball player (d. 1941)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 1978)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Estonian author and academic (d. 1961)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and academic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author and academic (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English director and screenwriter (d. 1968)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and screenwriter (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, German SS officer (d. 1948)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Estonian poet (d. 1947)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Talvik\" title=\"<PERSON><PERSON> Talvik\"><PERSON><PERSON></a>, Estonian poet (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Talvik\" title=\"<PERSON><PERSON> Talvik\"><PERSON><PERSON></a>, Estonian poet (d. 1947)", "links": [{"title": "Heiti Talvik", "link": "https://wikipedia.org/wiki/Heiti_Talvik"}]}, {"year": "1905", "text": "<PERSON><PERSON>, German-Swiss actress and author (d. 1969)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Swiss actress and author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Swiss actress and author (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German scientist and engineer (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist and engineer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist and engineer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, American actress (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American priest, historian, and theologian (d. 2009)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, historian, and theologian (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, historian, and theologian (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Austrian-American actress and inventor (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress and inventor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American actress and inventor (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Romanian-French illustrator, painter, and sculptor (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fran%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Romanian-French illustrator, painter, and sculptor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fran%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Romanian-French illustrator, painter, and sculptor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>an%C3%A7ois"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American lieutenant, lawyer, and politician, 21st United States Ambassador to France (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>rgent_Shriver\" title=\"Sargent Shriver\"><PERSON><PERSON> Shriver</a>, American lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Shriver\" title=\"<PERSON>rgent Shriver\"><PERSON><PERSON> Shriver</a>, American lieutenant, lawyer, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_France\" class=\"mw-redirect\" title=\"United States Ambassador to France\">United States Ambassador to France</a> (d. 2011)", "links": [{"title": "<PERSON>rgent <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ver"}, {"title": "United States Ambassador to France", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_France"}]}, {"year": "1916", "text": "<PERSON>, American lieutenant, historian, and educator (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Putney\"><PERSON></a>, American lieutenant, historian, and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Putney\"><PERSON></a>, American lieutenant, historian, and educator (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, American soldier, lawyer, and politician, 39th Vice President of the United States (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S<PERSON><PERSON>_<PERSON>gnew"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1918", "text": "<PERSON>, American swimmer (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Florence_Chadwick\" title=\"Florence Chadwick\"><PERSON></a>, American swimmer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Chadwick\" title=\"Florence Chadwick\"><PERSON></a>, American swimmer (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Chadwick"}]}, {"year": "1918", "text": "<PERSON>, American colonel (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, South Korean general and martial artist, co-founded taekwondo (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hong Hi\"><PERSON></a>, South Korean general and martial artist, co-founded <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">taekwondo</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hong Hi\"><PERSON></a>, South Korean general and martial artist, co-founded <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">taekwondo</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Taekwondo", "link": "https://wikipedia.org/wiki/Taekwondo"}]}, {"year": "1919", "text": "<PERSON>, Brazilian actress (d. 2017)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dor"}]}, {"year": "1920", "text": "<PERSON>, American assassin of <PERSON><PERSON><PERSON> (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/Med<PERSON>_<PERSON>\" title=\"Medgar Evers\">Me<PERSON><PERSON> <PERSON></a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assassin of <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON>_<PERSON>\" title=\"Medgar Evers\">Me<PERSON><PERSON></a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Medgar_<PERSON>s"}]}, {"year": "1920", "text": "<PERSON>, American engineer and academic (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and academic (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, Canadian soprano and actress (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and actress (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Ukrainian gymnast and coach (d. 1984)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast and coach (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian gymnast and coach (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American actress, singer, and dancer (d. 1965)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Belgian-French comedian and clown (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French comedian and clown (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French comedian and clown (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Hungarian mathematician, philosopher, and academic (d. 1974)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mathematician, philosopher, and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian mathematician, philosopher, and academic (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Im<PERSON>_<PERSON>kat<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American high jumper (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Coachman\" title=\"Alice Coachman\"><PERSON></a>, American high jumper (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Coachman\" title=\"Alice Coachman\"><PERSON></a>, American high jumper (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Coachman"}]}, {"year": "1923", "text": "<PERSON>, American-Nepali journalist and historian (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Nepali journalist and historian (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Nepali journalist and historian (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American poet and author (d. 1991)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Swiss-American photographer and director (d. 2019)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American photographer and director (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American photographer and director (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English-American journalist, historian, and author (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist, historian, and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist, historian, and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Spanish director, producer, and screenwriter (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director, producer, and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Spanish bullfighter (d. 1996)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish bullfighter (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Dom<PERSON>u%C3%ADn"}]}, {"year": "1928", "text": "<PERSON>, American poet and academic (d. 1974)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian actor and poet (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and poet (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and poet (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Hungarian author, Nobel Prize laureate (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Imre_Kert%C3%A9sz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imre_Kert%C3%A9sz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Imre_Kert%C3%A9sz"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zog\" title=\"<PERSON><PERSON>zo<PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zog\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Whitey_Herzog"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Russian surgeon and transplantologist (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian surgeon and transplantologist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian surgeon and transplantologist (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and coach (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2013)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1932", "text": "<PERSON>, American basketball player and coach (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American professional bodybuilder (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional bodybuilder (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional bodybuilder (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American game show host (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_personality)\" title=\"<PERSON> (television personality)\"><PERSON></a>, American game show host (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_personality)\" title=\"<PERSON> (television personality)\"><PERSON></a>, American game show host (d. 2015)", "links": [{"title": "<PERSON> (television personality)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_personality)"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Swedish economist and politician, 29th Prime Minister of Sweden", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish economist and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish economist and politician, 29th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1934", "text": "<PERSON>, South African author, playwright, and screenwriter (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author, playwright, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African author, playwright, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American astronomer, astrophysicist, and cosmologist (d. 1996)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, astrophysicist, and cosmologist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, astrophysicist, and cosmologist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and coach (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Baron <PERSON> of Sunningdale, English businessman and politician (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Sunningdale\" title=\"<PERSON>, Baron <PERSON> of Sunningdale\"><PERSON>, Baron <PERSON> of Sunningdale</a>, English businessman and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Sunningdale\" title=\"<PERSON>, Baron <PERSON> of Sunningdale\"><PERSON>, Baron <PERSON> of Sunningdale</a>, English businessman and politician (d. 2021)", "links": [{"title": "<PERSON>, Baron <PERSON> of Sunningdale", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Sunningdale"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 38th Governor of Florida (d. 2024)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "1936", "text": "<PERSON>, Latvian-Russian chess player and author (d. 1992)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Russian chess player and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian-Russian chess player and author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter (d. 2009)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, American singer-songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, American singer-songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English author, poet, and playwright", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English journalist and academic (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian lawyer and politician, 5th Premier of Newfoundland", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland\" class=\"mw-redirect\" title=\"Premier of Newfoundland\">Premier of Newfoundland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland\" class=\"mw-redirect\" title=\"Premier of Newfoundland\">Premier of Newfoundland</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Premier of Newfoundland", "link": "https://wikipedia.org/wiki/Premier_of_Newfoundland"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, American author and critic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Ti-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American author and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American author and critic", "links": [{"title": "Ti-<PERSON>", "link": "https://wikipedia.org/wiki/Ti-<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American psychologist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, <PERSON> of Oldham, English academic and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Oldham\" title=\"<PERSON>, <PERSON> of Oldham\"><PERSON>, Baron <PERSON> of Oldham</a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Oldham\" title=\"<PERSON>, <PERSON> of Oldham\"><PERSON>, Baron <PERSON> of Oldham</a>, English academic and politician", "links": [{"title": "<PERSON>, <PERSON> of Oldham", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Oldham"}]}, {"year": "1941", "text": "<PERSON>, English cricketer and umpire", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1990)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian businessman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_entrepreneur)\" title=\"<PERSON> (Australian entrepreneur)\"><PERSON></a>, Australian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_entrepreneur)\" title=\"<PERSON> (Australian entrepreneur)\"><PERSON></a>, Australian businessman", "links": [{"title": "<PERSON> (Australian entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_entrepreneur)"}]}, {"year": "1942", "text": "<PERSON>, English businessman and philanthropist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American golfer and sportscaster (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Indian dancer and choreographer (d. 2015)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dancer and choreographer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian dancer and choreographer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May_(singer)\" title=\"<PERSON> (singer)\"><PERSON> May</a>, English singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May_(singer)\" title=\"<PERSON> (singer)\"><PERSON> May</a>, English singer-songwriter (d. 2020)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, South African economist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mbeki\"><PERSON><PERSON><PERSON></a>, South African economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>beki\"><PERSON><PERSON><PERSON></a>, South African economist and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1945", "text": "<PERSON>, American actor (d. 2021)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter (d. 2020)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English author and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Warner\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marina_Warner\" title=\"<PERSON> Warner\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Warner"}]}, {"year": "1947", "text": "<PERSON>, American actor, singer, and pianist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Danish director, cinematographer, and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_August\" title=\"<PERSON><PERSON> August\"><PERSON><PERSON> August</a>, Danish director, cinematographer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_August\" title=\"Bille August\"><PERSON><PERSON> August</a>, Danish director, cinematographer, and screenwriter", "links": [{"title": "<PERSON><PERSON> August", "link": "https://wikipedia.org/wiki/Bille_August"}]}, {"year": "1948", "text": "<PERSON>, American bass player and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English economist, historian, and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, historian, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist, historian, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ies"}]}, {"year": "1948", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Brazilian footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, New Zealand politician, 40th Minister of Māori Affairs (d. 2013)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Parekura_Horomia\" title=\"Parekura Horomia\"><PERSON><PERSON><PERSON></a>, New Zealand politician, 40th <a href=\"https://wikipedia.org/wiki/Minister_of_M%C4%81ori_Affairs\" class=\"mw-redirect\" title=\"Minister of Māori Affairs\">Minister of Māori Affairs</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Parekura_Horomia\" title=\"Parekura Horomia\"><PERSON><PERSON><PERSON></a>, New Zealand politician, 40th <a href=\"https://wikipedia.org/wiki/Minister_of_M%C4%81ori_Affairs\" class=\"mw-redirect\" title=\"Minister of Māori Affairs\">Minister of Māori Affairs</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Parekura_Horomia"}, {"title": "Minister of Māori Affairs", "link": "https://wikipedia.org/wiki/Minister_of_M%C4%81ori_Affairs"}]}, {"year": "1951", "text": "<PERSON>, American bodybuilder and actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bodybuilder and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, American academic and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Venezuelan politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian boxer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%A9tan_Hart"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Thai singer-songwriter and guitarist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Aed_Carabao\" title=\"Aed Carabao\"><PERSON><PERSON></a>, Thai singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aed_Carabao\" title=\"Aed Carabao\"><PERSON><PERSON></a>, Thai singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aed_Carabao"}]}, {"year": "1955", "text": "<PERSON>, Brazilian director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Canadian lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German opera singer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, British actor, comedian and television personality (d. 2025)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, comedian and television personality (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor, comedian and television personality (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German footballer and manager (d. 2024)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American-English anthropologist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English anthropologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English anthropologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, American drummer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Plakas\"><PERSON><PERSON><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Demet<PERSON> Plakas\"><PERSON><PERSON><PERSON></a>, American drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetra_P<PERSON>as"}]}, {"year": "1961", "text": "<PERSON>, English journalist (d. 1999)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actor, director, and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English-Irish model and actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American television writer, producer, and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American television writer, producer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American television writer, producer, and director", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1965", "text": "<PERSON>, Welsh opera singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian pianist and educator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and educator", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nazza<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1968", "text": "<PERSON>, English political scientist, author, and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_scientist)\" title=\"<PERSON> (political scientist)\"><PERSON></a>, English political scientist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(political_scientist)\" title=\"<PERSON> (political scientist)\"><PERSON></a>, English political scientist, author, and academic", "links": [{"title": "<PERSON> (political scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(political_scientist)"}]}, {"year": "1969", "text": "<PERSON>, Jamaican-American rapper and actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American rapper and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American rapper and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American swimmer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American DJ and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(producer)\" title=\"<PERSON><PERSON> (producer)\"><PERSON><PERSON></a>, American DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(producer)\" title=\"<PERSON><PERSON> (producer)\"><PERSON><PERSON></a>, American DJ and producer", "links": [{"title": "<PERSON><PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(producer)"}]}, {"year": "1970", "text": "<PERSON>, Dutch volleyball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rtzen\" title=\"<PERSON>\"><PERSON></a>, Dutch volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6rtzen\" title=\"<PERSON>\"><PERSON></a>, Dutch volleyball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_G%C3%B6rtzen"}]}, {"year": "1970", "text": "<PERSON>, American ice hockey player, coach, and executive", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American-Canadian wrestler", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>ar<PERSON>_(rapper)\" title=\"<PERSON>ar<PERSON> (rapper)\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ar<PERSON>_(rapper)\" title=\"<PERSON>ar<PERSON> (rapper)\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>ar<PERSON>_(rapper)"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American golfer and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, French footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Japanese voice actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%8D\" title=\"<PERSON>\"><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%8D\" title=\"<PERSON>\"><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Naomi_Shind%C5%8D"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Canadian actress and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Alyson_Court\" title=\"Alyson Court\"><PERSON><PERSON><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alyson_Court\" title=\"Alyson Court\"><PERSON><PERSON><PERSON></a>, Canadian actress and producer", "links": [{"title": "Alyson Court", "link": "https://wikipedia.org/wiki/Alyson_Court"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian actress and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Greek footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Italian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Italian actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giovanna_Mezzogiorno"}]}, {"year": "1975", "text": "<PERSON>, English singer and conductor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, New Zealand cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Daisuke\" title=\"Tochia<PERSON><PERSON> Daisuke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>suke\" title=\"Tochia<PERSON><PERSON> Daisuke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer)"}]}, {"year": "1977", "text": "<PERSON>, Mexican footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Norwegian bass player and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Even Ormestad\"><PERSON></a>, Norwegian bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Even Ormestad\"><PERSON></a>, Norwegian bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ormestad"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter, producer, and actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Sisq%C3%B3\" title=\"<PERSON>sq<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sisq%C3%B3\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sisq%C3%B3"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English television presenter, radio presenter, and model (d. 2020)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter, radio presenter, and model (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television presenter, radio presenter, and model (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ck"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1979)"}]}, {"year": "1980", "text": "<PERSON>, Filipino-American television host and actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian snowboarder", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American rapper and producer (d. 2010)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Eyedea\"><PERSON><PERSON></a>, American rapper and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>dea\"><PERSON><PERSON></a>, American rapper and producer (d. 2010)", "links": [{"title": "Eyedea", "link": "https://wikipedia.org/wiki/Eyedea"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Jamaican footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nu<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nu<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian racquetball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racquetball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian racquetball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American-Welsh footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>hill\"><PERSON><PERSON></a>, American-Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Welsh footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hill"}]}, {"year": "1982", "text": "<PERSON>, Australian hurdler", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, German rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American golfer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American golfer", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1983)\" title=\"<PERSON> (footballer, born 1983)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1983)"}]}, {"year": "1984", "text": "<PERSON>, Australian singer-songwriter, pianist, and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Delta_Goodrem\" title=\"Delta Goodrem\"><PERSON>rem</a>, Australian singer-songwriter, pianist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delta_Goodrem\" title=\"Delta Goodrem\"><PERSON> Goodrem</a>, Australian singer-songwriter, pianist, and actress", "links": [{"title": "Delta Goodrem", "link": "https://wikipedia.org/wiki/Delta_Goodrem"}]}, {"year": "1984", "text": "<PERSON>, Moroccan-American rapper", "html": "1984 - <a href=\"https://wikipedia.org/wiki/French_Montana\" title=\"French Montana\">French Montana</a>, Moroccan-American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Montana\" title=\"French Montana\">French Montana</a>, Moroccan-American rapper", "links": [{"title": "French Montana", "link": "https://wikipedia.org/wiki/French_Montana"}]}, {"year": "1984", "text": "<PERSON>, South Korean singer, dancer, and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Korean_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Korean singer)\">Seven</a>, South Korean singer, dancer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Korean_singer)\" class=\"mw-redirect\" title=\"<PERSON> (Korean singer)\">Seven</a>, South Korean singer, dancer, and actor", "links": [{"title": "<PERSON> (Korean singer)", "link": "https://wikipedia.org/wiki/Seven_(Korean_singer)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Malian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Bakary_Soumar%C3%A9\" title=\"<PERSON><PERSON><PERSON> Souma<PERSON>\"><PERSON><PERSON><PERSON></a>, Malian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bakary_Soumar%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bakary_Soumar%C3%A9"}]}, {"year": "1986", "text": "<PERSON>, Swedish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress, singer, and dancer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actor and model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ton"}]}, {"year": "1989", "text": "<PERSON>, French model and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French model and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Nigerian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Nosa_I<PERSON>\" title=\"Nosa Igiebor\"><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nosa_<PERSON>\" title=\"Nosa Igiebor\"><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nosa_I<PERSON>bor"}]}, {"year": "1993", "text": "<PERSON>, English wrestler", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/L<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lyric<PERSON>_<PERSON>\" title=\"Lyric<PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actor", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cole\" title=\"Finn Cole\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Cole\" title=\"Finn Cole\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Japanese dancer and singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese dancer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese dancer and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rai"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>rith<PERSON>_<PERSON>\" title=\"<PERSON>rith<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rith<PERSON>_<PERSON>\" title=\"<PERSON>rith<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prithvi_<PERSON>"}]}], "Deaths": [{"year": "959", "text": "<PERSON>, Byzantine emperor (b. 905)", "html": "959 - <a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"Constantine VII\"><PERSON> VII</a>, Byzantine emperor (b. 905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantine_VII\" title=\"Constantine VII\"><PERSON> VII</a>, Byzantine emperor (b. 905)", "links": [{"title": "Constantine VII", "link": "https://wikipedia.org/wiki/Constantine_VII"}]}, {"year": "1034", "text": "<PERSON><PERSON><PERSON>, Duke of Bohemia (b. c. 975)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 975</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 975</span>)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/Old%C5%99ich,_Duke_of_Bohemia"}]}, {"year": "1187", "text": "Emperor <PERSON><PERSON> of Song (b. 1107)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (b. 1107)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (b. 1107)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1208", "text": "<PERSON><PERSON> of Castile, Queen of Aragon (b. 1154)", "html": "1208 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Castile,_Queen_of_Aragon\" title=\"<PERSON><PERSON> of Castile, Queen of Aragon\"><PERSON><PERSON> of Castile, Queen of Aragon</a> (b. 1154)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San<PERSON>_of_Castile,_Queen_of_Aragon\" title=\"<PERSON><PERSON> of Castile, Queen of Aragon\"><PERSON><PERSON> of Castile, Queen of Aragon</a> (b. 1154)", "links": [{"title": "<PERSON><PERSON> of Castile, Queen of Aragon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Castile,_Queen_of_Aragon"}]}, {"year": "1261", "text": "<PERSON><PERSON> of Provence, queen consort of Germany", "html": "1261 - <a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">San<PERSON> of Provence</a>, queen consort of Germany", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sanchia_of_Provence\" title=\"Sanchia of Provence\">Sanchia of Provence</a>, queen consort of Germany", "links": [{"title": "Sanchia of Provence", "link": "https://wikipedia.org/wiki/Sanchia_of_Provence"}]}, {"year": "1284", "text": "<PERSON><PERSON> of Brabant, Dutch philosopher (b. 1240)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Brabant\" title=\"<PERSON><PERSON> of Brabant\"><PERSON><PERSON> of Brabant</a>, Dutch philosopher (b. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Brabant\" title=\"<PERSON><PERSON> of Brabant\"><PERSON><PERSON> of Brabant</a>, Dutch philosopher (b. 1240)", "links": [{"title": "<PERSON><PERSON> of Brabant", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_<PERSON>rabant"}]}, {"year": "1286", "text": "<PERSON>, English statesman (b. 1230)", "html": "1286 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English statesman (b. 1230)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1312", "text": "<PERSON>, Duke of Bavaria (b. 1261)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1261)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1321", "text": "<PERSON>, bishop of Lichfield and treasurer of England (b. 1243)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Lichfield and treasurer of England (b. 1243)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, bishop of Lichfield and treasurer of England (b. 1243)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1456", "text": "<PERSON>, Count of Celje (b. 1406)", "html": "1456 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Celje\" title=\"<PERSON>, Count of Celje\"><PERSON>, Count of Celje</a> (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Celje\" title=\"<PERSON>, Count of Celje\"><PERSON>, Count of Celje</a> (b. 1406)", "links": [{"title": "<PERSON>, Count of Celje", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1492", "text": "<PERSON><PERSON>, Persian poet (b. 1414)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian poet (b. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian poet (b. 1414)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jami"}]}, {"year": "1596", "text": "<PERSON>, English translator, poet, and dramatist (b. 1556)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English translator, poet, and dramatist (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English translator, poet, and dramatist (b. 1556)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON>, English historian and topographer (b. 1551)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/William_Camden\" title=\"William Camden\"><PERSON></a>, English historian and topographer (b. 1551)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Camden\" title=\"William Camden\"><PERSON></a>, English historian and topographer (b. 1551)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Camden"}]}, {"year": "1641", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Austria (b. 1610)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_of_Austria\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Austria\">Cardinal<PERSON><PERSON><PERSON><PERSON> of Austria</a> (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cardinal-<PERSON><PERSON><PERSON>_<PERSON>_of_Austria\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Austria\">Cardinal<PERSON><PERSON><PERSON><PERSON> of Austria</a> (b. 1610)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>_of_Austria"}]}, {"year": "1677", "text": "<PERSON><PERSON>, Dutch painter (b. 1603)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (b. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter (b. 1603)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON><PERSON>, imperial general (b. 1651)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON> (general)\"><PERSON><PERSON></a>, imperial general (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(general)\" title=\"<PERSON><PERSON> (general)\"><PERSON><PERSON></a>, imperial general (b. 1651)", "links": [{"title": "<PERSON><PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(general)"}]}, {"year": "1706", "text": " <PERSON>, English Royalist theologian and bishop (b. 1619)", "html": "1706 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Royalist theologian and bishop (b. <a href=\"https://wikipedia.org/wiki/1619\" title=\"1619\">1619</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Royalist theologian and bishop (b. <a href=\"https://wikipedia.org/wiki/1619\" title=\"1619\">1619</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1619", "link": "https://wikipedia.org/wiki/1619"}]}, {"year": "1719", "text": "<PERSON><PERSON>, British Member of Parliament (b. 1684)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British Member of Parliament (b. <a href=\"https://wikipedia.org/wiki/1684\" title=\"1684\">1684</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British Member of Parliament (b. <a href=\"https://wikipedia.org/wiki/1684\" title=\"1684\">1684</a>)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "1684", "link": "https://wikipedia.org/wiki/1684"}]}, {"year": "1766", "text": "<PERSON><PERSON>, Dutch composer and diplomat (b. 1692)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Unico_<PERSON>_<PERSON>\" title=\"Unico Wilhelm <PERSON>\">Unico <PERSON></a>, Dutch composer and diplomat (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unico_Wilhelm_<PERSON>\" title=\"Unico Wilhelm <PERSON>\">Unico <PERSON></a>, Dutch composer and diplomat (b. 1692)", "links": [{"title": "Unico Wilhelm <PERSON>", "link": "https://wikipedia.org/wiki/Un<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, 4th Duke of Argyll, Scottish general and politician (b. 1693)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Argyll\" title=\"<PERSON>, 4th Duke of Argyll\"><PERSON>, 4th Duke of Argyll</a>, Scottish general and politician (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Argyll\" title=\"<PERSON>, 4th Duke of Argyll\"><PERSON>, 4th Duke of Argyll</a>, Scottish general and politician (b. 1693)", "links": [{"title": "<PERSON>, 4th Duke of Argyll", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Argyll"}]}, {"year": "1778", "text": "<PERSON>, Italian sculptor and illustrator (b. 1720)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and illustrator (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and illustrator (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1801", "text": "<PERSON>, German-Czech violinist and composer (b. 1745)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Czech violinist and composer (b. 1745)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Czech violinist and composer (b. 1745)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, German poet and politician (b. 1810)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and politician (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and politician (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, wife/widow of <PERSON> and co-founder of the first private orphanage in New York (b. 1757)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife/widow of <PERSON> and co-founder of the first private orphanage in New York (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, wife/widow of <PERSON> and co-founder of the first private orphanage in New York (b. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American businessman (b. 1819)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English suffragist, educational reformer and author (b. 1831)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English suffragist, educational reformer and author (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English suffragist, educational reformer and author (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American author and illustrator (b. 1853)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Australian cricketer (b. 1866)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Italian-French author, poet, and playwright (b. 1880)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>pollinaire\" title=\"Guillaume Apollinaire\"><PERSON></a>, Italian-French author, poet, and playwright (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>linaire\" title=\"Guillaume Apollinaire\"><PERSON></a>, Italian-French author, poet, and playwright (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guillaume_Apollinaire"}]}, {"year": "1918", "text": "<PERSON>, English general (b. 1829)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Swiss lawyer and politician, 26th President of the Swiss Confederation (b. 1848)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)\" title=\"<PERSON> (Swiss politician)\"><PERSON></a>, Swiss lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)\" title=\"<PERSON> (Swiss politician)\"><PERSON></a>, Swiss lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1848)", "links": [{"title": "<PERSON> (Swiss politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(Swiss_politician)"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1924", "text": "<PERSON>, American historian and politician (b. 1850)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and politician (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, second wife of <PERSON> (b. 1901)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, second wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Scottish journalist and politician, Prime Minister of the United Kingdom (b. 1866)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Russian marshal (b. 1889)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian marshal (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian marshal (b. 1889)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Portuguese-American bishop (b. 1876)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-American bishop (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese-American bishop (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English businessman and politician, Prime Minister of the United Kingdom (b. 1869)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1942", "text": "<PERSON>, American painter (b. 1861)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actress (b. 1883)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American chess player and theoretician (b. 1877)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and theoretician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and theoretician (b. 1877)", "links": [{"title": "<PERSON> (chess player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(chess_player)"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Hungarian-American pianist and composer (b. 1887)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>\" title=\"<PERSON>g<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-American pianist and composer (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>g"}]}, {"year": "1952", "text": "<PERSON>, Scottish-American labor leader (b. 1886)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American labor leader (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American labor leader (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Belarusian-Israeli chemist, academic, and politician, 1st President of Israel (b. 1874)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli chemist, academic, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli chemist, academic, and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1874)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1953", "text": "<PERSON>, American philanthropist and activist (b. 1859)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and activist (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and activist (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Saudi Arabian king (b. 1880)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian king (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saudi Arabian king (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Welsh poet and author (b. 1914)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh poet and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh poet and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Finnish-Estonian author (b. 1878)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Estonian author (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-Estonian author (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Irish long jumper (b. 1872)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish long jumper (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Irish long jumper (b. 1872)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(athlete)"}]}, {"year": "1958", "text": "<PERSON>, American educational reformer, social activist and author (b. 1879)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educational reformer, social activist and author (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educational reformer, social activist and author (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Indian activist and academic (b. 1858)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>sha<PERSON> Karve\"><PERSON><PERSON><PERSON></a>, Indian activist and academic (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>hon<PERSON>sha<PERSON> Karve\"><PERSON><PERSON><PERSON></a>, Indian activist and academic (b. 1858)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>rve"}]}, {"year": "1968", "text": "<PERSON>, Swedish pianist (b. 1931)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, Swedish pianist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)\" title=\"<PERSON> (jazz musician)\"><PERSON></a>, Swedish pianist (b. 1931)", "links": [{"title": "<PERSON> (jazz musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_musician)"}]}, {"year": "1970", "text": "<PERSON>, French general and politician, 18th President of France (b. 1890)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_France\" class=\"mw-redirect\" title=\"List of Presidents of France\">President of France</a> (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, 18th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_France\" class=\"mw-redirect\" title=\"List of Presidents of France\">President of France</a> (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of France", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_France"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American actress and screenwriter (b. 1883)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter (b. 1883)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>; American director, producer, screenwriter, and actor (b. 1890)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; American director, producer, screenwriter, and actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>; American director, producer, screenwriter, and actor (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Finnish discus thrower and shot putter (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Armas_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish discus thrower and shot putter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Armas_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish discus thrower and shot putter (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Armas_Taipale"}]}, {"year": "1977", "text": "<PERSON>, American baseball player, coach, and manager (b. 1898)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French actress (b. 1946)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player, coach, and priest (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and priest (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and priest (b. 1924)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1988", "text": "<PERSON>, American lieutenant, lawyer, and politician, 67th United States Attorney General (b. 1913)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 67th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician, 67th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1988", "text": "<PERSON>, English author and screenwriter (b. 1920)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian politician, 34th Premier of Tasmania (b. 1925)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1991", "text": "<PERSON>, Italian-French actor (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English missionary and author (b. 1904)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Danish-American scout leader and author (b. 1900)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American scout leader and author (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American scout leader and author (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>court"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Sri Lankan politician (b. 1926)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/T._<PERSON>aram\" title=\"T. <PERSON>m\"><PERSON><PERSON></a>, Sri Lankan politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON>aram\" title=\"T. <PERSON>aram\"><PERSON><PERSON></a>, Sri Lankan politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON>vasithamparam"}]}, {"year": "1993", "text": "<PERSON>, American illustrator (b. 1925)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian lawyer, judge, and politician, 27th Premier of Prince Edward Island (b. 1945)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 27th <a href=\"https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island\" title=\"Premier of Prince Edward Island\">Premier of Prince Edward Island</a> (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joe_<PERSON>z"}, {"title": "Premier of Prince Edward Island", "link": "https://wikipedia.org/wiki/Premier_of_Prince_Edward_Island"}]}, {"year": "1997", "text": "<PERSON>, German philosopher from the Vienna and the Berlin Circle (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna and the Berlin Circle (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher from the Vienna and the Berlin Circle (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Argentinian-Italian footballer and manager (b. 1910)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian-Italian footballer and manager (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian-Italian footballer and manager (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actress and singer (b. 1932)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American actress and singer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American actress and singer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American race car driver (b. 1927)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English television host, founded <PERSON> World (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host, founded <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host, founded <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Miss World", "link": "https://wikipedia.org/wiki/Miss_World"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Canadian historian and curator (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian and curator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian historian and curator (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Italian lawyer and politician, 6th President of Italy (b. 1908)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Giovanni_<PERSON>\" title=\"Giovanni Leone\"><PERSON></a>, Italian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giovanni_<PERSON>\" title=\"Giovanni Leone\"><PERSON></a>, Italian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> (b. 1908)", "links": [{"title": "Giovanni Leone", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}]}, {"year": "2002", "text": "<PERSON>, American psychologist and academic (b. 1925)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American actor and comedian (b. 1918)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Art_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Carney\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Art_Carney"}]}, {"year": "2003", "text": "<PERSON>, English-American painter (b. 1912)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American painter (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Gordon <PERSON> Ford\"><PERSON></a>, English-American painter (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American historian, journalist, and author (b. 1968)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, journalist, and author (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chang\"><PERSON></a>, American historian, journalist, and author (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Chang"}]}, {"year": "2004", "text": "<PERSON><PERSON>, English footballer and manager (b. 1947)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager (b. 1947)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Swedish journalist and author (b. 1954)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian journalist and politician, 10th President of India (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian journalist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1921)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "2006", "text": "<PERSON>, American journalist (b. 1941)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American journalist and activist (b. 1941)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, German intelligence officer (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German intelligence officer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German intelligence officer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>,  Australian bioinorganic chemist and protein crystallographer (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bioinorganic chemist and protein crystallographer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian bioinorganic chemist and protein crystallographer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, South African singer and activist (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African singer and activist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African singer and activist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Slovak lawyer and politician, 5th Prime Minister of the Slovak Socialist Republic (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Milan_%C4%8Ci%C4%8D\" title=\"Milan Čič\"><PERSON></a>, Slovak lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Slovak_Socialist_Republic\" class=\"mw-redirect\" title=\"List of Prime Ministers of the Slovak Socialist Republic\">Prime Minister of the Slovak Socialist Republic</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_%C4%8Ci%C4%8D\" title=\"Milan Čič\"><PERSON></a>, Slovak lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Slovak_Socialist_Republic\" class=\"mw-redirect\" title=\"List of Prime Ministers of the Slovak Socialist Republic\">Prime Minister of the Slovak Socialist Republic</a> (b. 1932)", "links": [{"title": "Milan Čič", "link": "https://wikipedia.org/wiki/Milan_%C4%8Ci%C4%8D"}, {"title": "List of Prime Ministers of the Slovak Socialist Republic", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_the_Slovak_Socialist_Republic"}]}, {"year": "2012", "text": "<PERSON>, American soldier and politician (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian mathematician and academic (b. 1905)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American colonel, Medal of Honor recipient (b. 1922)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Turkish journalist (b. 1954)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Sava%C5%9F_Ay\" title=\"<PERSON>vaş Ay\"><PERSON><PERSON><PERSON></a>, Turkish journalist (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sava%C5%9F_Ay\" title=\"Savaş Ay\"><PERSON><PERSON><PERSON></a>, Turkish journalist (b. 1954)", "links": [{"title": "Savaş Ay", "link": "https://wikipedia.org/wiki/Sava%C5%9F_Ay"}]}, {"year": "2013", "text": "<PERSON>, Scottish politician (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Norwegian biologist and academic (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian biologist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Norwegian biologist and academic (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, American saxophonist (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American saxophonist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American saxophonist (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English rugby player (b. 1973)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Austrian-American biologist and academic (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American biologist and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-American biologist and academic (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Argentinian golfer (b. 1961)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian golfer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian golfer (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, Qatari prince (b. 1966)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> <PERSON></a>, Qatari prince (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON> <PERSON></a>, Qatari prince (b. 1966)", "links": [{"title": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and publisher (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and publisher (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and publisher (b. 1936)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Bahamian pastor and author (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian pastor and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bahamian pastor and author (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American football player (b. 1972)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"<PERSON> Thomas\"><PERSON></a>, American football player (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Thomas\" title=\"<PERSON> Thomas\"><PERSON></a>, American football player (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "2014", "text": "<PERSON>, Irish politician, Minister for Agriculture, Food and the Marine (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, Irish politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine\" title=\"Minister for Agriculture, Food and the Marine\">Minister for Agriculture, Food and the Marine</a> (b. 1943)", "links": [{"title": "<PERSON> (Irish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_politician)"}, {"title": "Minister for Agriculture, Food and the Marine", "link": "https://wikipedia.org/wiki/Minister_for_Agriculture,_Food_and_the_Marine"}]}, {"year": "2015", "text": "<PERSON>, American actress and dancer (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Austrian painter, sculptor, and illustrator (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Austrian painter, sculptor, and illustrator (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Austrian painter, sculptor, and illustrator (b. 1930)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "2015", "text": "<PERSON>, American baseball player (b. 1986)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American fencer (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fencer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fencer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Scottish drummer (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Scottish drummer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, Scottish drummer (b. 1930)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(drummer)"}]}, {"year": "2016", "text": "<PERSON>, American basketball player and coach (b. 1955)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach (b. 1955)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2017", "text": "<PERSON>, American singer songwriter (b. 1959)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer songwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer songwriter (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Canadian pornographic actress (b. 1982)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/Shyla_Stylez\" title=\"Shyla Stylez\"><PERSON><PERSON><PERSON></a>, Canadian pornographic actress (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shyla_Stylez\" title=\"Shyla Stylez\"><PERSON><PERSON><PERSON></a>, Canadian pornographic actress (b. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shyla_Stylez"}]}, {"year": "2021", "text": "<PERSON>, American politician (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Japanese singer (b. 1950)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American race car driver and businessman (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and businessman (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American saxophonist (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American dancer and choreographer (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American folk singer (b. 1924)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk singer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}