{"date": "September 13", "url": "https://wikipedia.org/wiki/September_13", "data": {"Events": [{"year": "585 BC", "text": "<PERSON>, king of Rome, celebrates a triumph for his victories over the Sabines, and the surrender of Collatia.", "html": "585 BC - 585 BC - <a href=\"https://wikipedia.org/wiki/Lucius_Tarquinius_Priscus\" title=\"Lucius Tarquinius P<PERSON>cus\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for his <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Sabine_wars#War_with_Tarquinius_Priscus\" title=\"Roman-Sabine wars\">victories</a> over the <a href=\"https://wikipedia.org/wiki/Sabines\" title=\"Sabines\">Sabines</a>, and the surrender of <a href=\"https://wikipedia.org/wiki/Collatia\" title=\"Collatia\">Collatia</a>.", "no_year_html": "585 BC - <a href=\"https://wikipedia.org/wiki/Lucius_Tarquinius_Priscus\" title=\"Lucius Tarquinius P<PERSON>cus\"><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for his <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Sabine_wars#War_with_Tarquinius_Priscus\" title=\"Roman-Sabine wars\">victories</a> over the <a href=\"https://wikipedia.org/wiki/Sabines\" title=\"Sabines\">Sabines</a>, and the surrender of <a href=\"https://wikipedia.org/wiki/Collatia\" title=\"Collatia\">Collatia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>cus"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Roman-Sabine wars", "link": "https://wikipedia.org/wiki/Roman%E2%80%93Sabine_wars#War_with_Tarquinius_Priscus"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sabines"}, {"title": "Collatia", "link": "https://wikipedia.org/wiki/Collatia"}]}, {"year": "509 BC", "text": "The Temple of Jupiter Optimus Maximus on Rome's Capitoline Hill is dedicated on the ides of September.", "html": "509 BC - 509 BC - The <a href=\"https://wikipedia.org/wiki/Temple_of_Jupiter_Optimus_Maximus\" title=\"Temple of Jupiter Optimus Maximus\">Temple of Jupiter Optimus Maximus</a> on Rome's <a href=\"https://wikipedia.org/wiki/Capitoline_Hill\" title=\"Capitoline Hill\">Capitoline Hill</a> is dedicated on the <a href=\"https://wikipedia.org/wiki/Roman_calendar\" title=\"Roman calendar\">ides</a> of September.", "no_year_html": "509 BC - The <a href=\"https://wikipedia.org/wiki/Temple_of_Jupiter_Optimus_Maximus\" title=\"Temple of Jupiter Optimus Maximus\">Temple of Jupiter Optimus Maximus</a> on Rome's <a href=\"https://wikipedia.org/wiki/Capitoline_Hill\" title=\"Capitoline Hill\">Capitoline Hill</a> is dedicated on the <a href=\"https://wikipedia.org/wiki/Roman_calendar\" title=\"Roman calendar\">ides</a> of September.", "links": [{"title": "Temple of Jupiter Optimus Maximus", "link": "https://wikipedia.org/wiki/Temple_of_<PERSON>_Optimus_Maximus"}, {"title": "Capitoline Hill", "link": "https://wikipedia.org/wiki/Capitoline_Hill"}, {"title": "Roman calendar", "link": "https://wikipedia.org/wiki/Roman_calendar"}]}, {"year": "379", "text": "<PERSON><PERSON> is crowned as 15th Ajaw of Tikal", "html": "379 - <a href=\"https://wikipedia.org/wiki/Yax_Nuun_Ahiin_I\" title=\"Yax Nuun Ahiin I\">Yax Nuun Ahiin I</a> is crowned as 15th <a href=\"https://wikipedia.org/wiki/Ajaw\" title=\"Ajaw\">Ajaw</a> of <a href=\"https://wikipedia.org/wiki/Tikal\" title=\"Tikal\">Tikal</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yax_Nuun_Ahiin_I\" title=\"Yax Nuun Ahiin I\">Yax Nuun Ahiin I</a> is crowned as 15th <a href=\"https://wikipedia.org/wiki/Ajaw\" title=\"Ajaw\">Ajaw</a> of <a href=\"https://wikipedia.org/wiki/Tikal\" title=\"Tikal\">Tikal</a>", "links": [{"title": "<PERSON><PERSON> I", "link": "https://wikipedia.org/wiki/Yax_<PERSON>u<PERSON>_Ahiin_I"}, {"title": "Ajaw", "link": "https://wikipedia.org/wiki/Ajaw"}, {"title": "Tikal", "link": "https://wikipedia.org/wiki/Tikal"}]}, {"year": "533", "text": "<PERSON><PERSON><PERSON> of the Byzantine Empire defeats <PERSON><PERSON><PERSON> and the Vandals at the Battle of Ad Decimum, near Carthage, North Africa.", "html": "533 - <a href=\"https://wikipedia.org/wiki/Bel<PERSON>rius\" title=\"Bel<PERSON><PERSON>\">Belisa<PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ad_Decimum\" title=\"Battle of Ad Decimum\">Battle of Ad Decimum</a>, near <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>, North Africa.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belisarius\" title=\"<PERSON><PERSON><PERSON>\">Belisa<PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Vandals\" title=\"Vandals\">Vandals</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ad_Decimum\" title=\"Battle of Ad Decimum\">Battle of Ad Decimum</a>, near <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>, North Africa.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belisarius"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Vandals", "link": "https://wikipedia.org/wiki/Vandals"}, {"title": "Battle of Ad Decimum", "link": "https://wikipedia.org/wiki/Battle_of_Ad_Decimum"}, {"title": "Carthage", "link": "https://wikipedia.org/wiki/Carthage"}]}, {"year": "1229", "text": "<PERSON><PERSON><PERSON> Khan is proclaimed Khagan of the Mongol Empire in Kodoe Aral, Khentii: Mongolia.", "html": "1229 - <a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"Ögedei Khan\">Öged<PERSON> Khan</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">Khagan</a> of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a> in Kodoe Aral, Khentii: <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96<PERSON><PERSON>_Khan\" title=\"Ögedei Khan\"><PERSON><PERSON><PERSON> Khan</a> is proclaimed <a href=\"https://wikipedia.org/wiki/Khagan\" title=\"Khagan\">Khagan</a> of the <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongol Empire</a> in Kodoe Aral, Khentii: <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96ged<PERSON>_Khan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>gan"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}]}, {"year": "1437", "text": "Battle of Tangier: a Portuguese expeditionary force initiates a failed attempt to seize the Moroccan citadel of Tangier.", "html": "1437 - <a href=\"https://wikipedia.org/wiki/Battle_of_Tangier_(1437)\" title=\"Battle of Tangier (1437)\">Battle of Tangier</a>: a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> expeditionary force initiates a failed attempt to seize the Moroccan citadel of <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tangier\">Tangier</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Tangier_(1437)\" title=\"Battle of Tangier (1437)\">Battle of Tangier</a>: a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> expeditionary force initiates a failed attempt to seize the Moroccan citadel of <a href=\"https://wikipedia.org/wiki/Tangier\" title=\"Tangier\">Tangier</a>.", "links": [{"title": "Battle of Tangier (1437)", "link": "https://wikipedia.org/wiki/Battle_of_Tangier_(1437)"}, {"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}, {"title": "Tangier", "link": "https://wikipedia.org/wiki/<PERSON>ier"}]}, {"year": "1609", "text": "<PERSON> reaches the river that would later be named after him - the Hudson River.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> reaches the river that would later be named after him - the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> reaches the river that would later be named after him - the <a href=\"https://wikipedia.org/wiki/Hudson_River\" title=\"Hudson River\">Hudson River</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hudson River", "link": "https://wikipedia.org/wiki/Hudson_River"}]}, {"year": "1645", "text": "Wars of the Three Kingdoms: Scottish Royalists are defeated by Covenanters at the Battle of Philiphaugh.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms\" title=\"Wars of the Three Kingdoms\">Wars of the Three Kingdoms</a>: Scottish Royalists are defeated by Covenanters at the <a href=\"https://wikipedia.org/wiki/Battle_of_Philiphaugh\" title=\"Battle of Philiphaugh\">Battle of Philiphaugh</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms\" title=\"Wars of the Three Kingdoms\">Wars of the Three Kingdoms</a>: Scottish Royalists are defeated by Covenanters at the <a href=\"https://wikipedia.org/wiki/Battle_of_Philiphaugh\" title=\"Battle of Philiphaugh\">Battle of Philiphaugh</a>.", "links": [{"title": "Wars of the Three Kingdoms", "link": "https://wikipedia.org/wiki/Wars_of_the_Three_Kingdoms"}, {"title": "Battle of Philiphaugh", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>"}]}, {"year": "1743", "text": "Great Britain, Austria and the Kingdom of Sardinia sign the Treaty of Worms.", "html": "1743 - Great Britain, Austria and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Kingdom of Sardinia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Worms_(1743)\" title=\"Treaty of Worms (1743)\">Treaty of Worms</a>.", "no_year_html": "Great Britain, Austria and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sardinia\" title=\"Kingdom of Sardinia\">Kingdom of Sardinia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Worms_(1743)\" title=\"Treaty of Worms (1743)\">Treaty of Worms</a>.", "links": [{"title": "Kingdom of Sardinia", "link": "https://wikipedia.org/wiki/Kingdom_of_Sardinia"}, {"title": "Treaty of Worms (1743)", "link": "https://wikipedia.org/wiki/Treaty_of_Worms_(1743)"}]}, {"year": "1759", "text": "Battle of the Plains of Abraham: the British defeat the French near Quebec City in the Seven Years' War, known in the United States as the French and Indian War.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Plains_of_Abraham\" title=\"Battle of the Plains of Abraham\">Battle of the Plains of Abraham</a>: the British defeat the French near <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a> in the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>, known in the United States as the <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Plains_of_Abraham\" title=\"Battle of the Plains of Abraham\">Battle of the Plains of Abraham</a>: the British defeat the French near <a href=\"https://wikipedia.org/wiki/Quebec_City\" title=\"Quebec City\">Quebec City</a> in the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>, known in the United States as the <a href=\"https://wikipedia.org/wiki/French_and_Indian_War\" title=\"French and Indian War\">French and Indian War</a>.", "links": [{"title": "Battle of the Plains of Abraham", "link": "https://wikipedia.org/wiki/Battle_of_the_Plains_of_Abraham"}, {"title": "Quebec City", "link": "https://wikipedia.org/wiki/Quebec_City"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "French and Indian War", "link": "https://wikipedia.org/wiki/French_and_Indian_War"}]}, {"year": "1782", "text": "American Revolutionary War: Franco-Spanish troops launch the unsuccessful \"grand assault\" during the Great Siege of Gibraltar.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Franco-Spanish troops launch the unsuccessful \"grand assault\" during the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: Franco-Spanish troops launch the unsuccessful \"grand assault\" during the <a href=\"https://wikipedia.org/wiki/Great_Siege_of_Gibraltar\" title=\"Great Siege of Gibraltar\">Great Siege of Gibraltar</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Great Siege of Gibraltar", "link": "https://wikipedia.org/wiki/Great_Siege_of_Gibraltar"}]}, {"year": "1788", "text": "The Congress of the Confederation sets the date for the first presidential election in the United States, and New York City becomes the country's temporary capital.", "html": "1788 - The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederation\" title=\"Congress of the Confederation\">Congress of the Confederation</a> sets the date for the first presidential election in the United States, and <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> becomes the country's temporary capital.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Congress_of_the_Confederation\" title=\"Congress of the Confederation\">Congress of the Confederation</a> sets the date for the first presidential election in the United States, and <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> becomes the country's temporary capital.", "links": [{"title": "Congress of the Confederation", "link": "https://wikipedia.org/wiki/Congress_of_the_Confederation"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1791", "text": "King <PERSON> of France accepts the new constitution.", "html": "1791 - King <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> XVI of France</a> accepts the <a href=\"https://wikipedia.org/wiki/French_Constitution_of_1791\" title=\"French Constitution of 1791\">new constitution</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> XVI of France</a> accepts the <a href=\"https://wikipedia.org/wiki/French_Constitution_of_1791\" title=\"French Constitution of 1791\">new constitution</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "French Constitution of 1791", "link": "https://wikipedia.org/wiki/French_Constitution_of_1791"}]}, {"year": "1807", "text": "<PERSON>'s Mass in C major, Op. 86, is premiered, commissioned by <PERSON><PERSON>, Prince <PERSON>, and displeasing him.", "html": "1807 - Beethoven's <a href=\"https://wikipedia.org/wiki/Mass_in_C_major_(<PERSON>)\" title=\"Mass in C major (<PERSON>)\">Mass in C major</a>, <a href=\"https://wikipedia.org/wiki/Opus_number\" title=\"Opus number\">Op</a>. 86, is premiered, commissioned by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>h%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, and displeasing him.", "no_year_html": "Beethoven's <a href=\"https://wikipedia.org/wiki/Mass_in_C_major_(<PERSON>)\" title=\"Mass in C major (<PERSON>)\">Mass in C major</a>, <a href=\"https://wikipedia.org/wiki/Opus_number\" title=\"Opus number\">Op</a>. 86, is premiered, commissioned by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>h%C3%<PERSON><PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, and displeasing him.", "links": [{"title": "Mass in C major (<PERSON>)", "link": "https://wikipedia.org/wiki/Mass_in_C_major_(<PERSON>)"}, {"title": "Opus number", "link": "https://wikipedia.org/wiki/Opus_number"}, {"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>%C3%A1zy"}]}, {"year": "1808", "text": "Finnish War: In the Battle of Jutas, Swedish forces under Lieutenant General <PERSON> beat the Russians, making <PERSON> a Swedish war hero.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Jutas\" title=\"Battle of Jutas\">Battle of Jutas</a>, Swedish forces under Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"Georg <PERSON>\"><PERSON></a> beat the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a>, making <PERSON> a Swedish war hero.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Jutas\" title=\"Battle of Jutas\">Battle of Jutas</a>, Swedish forces under Lieutenant General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"Georg <PERSON>\"><PERSON></a> beat the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a>, making <PERSON> a Swedish war hero.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "Battle of Jutas", "link": "https://wikipedia.org/wiki/Battle_of_Jutas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1812", "text": "War of 1812: A supply wagon sent to relieve Fort Harrison is ambushed in the Attack at the Narrows.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: A supply wagon sent to relieve <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Harrison\" class=\"mw-redirect\" title=\"Siege of Fort Harrison\">Fort Harrison</a> is ambushed in the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Harrison#Attacks_at_the_Narrows\" class=\"mw-redirect\" title=\"Siege of Fort Harrison\">Attack at the Narrows</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: A supply wagon sent to relieve <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Harrison\" class=\"mw-redirect\" title=\"Siege of Fort Harrison\">Fort Harrison</a> is ambushed in the <a href=\"https://wikipedia.org/wiki/Siege_of_Fort_Harrison#Attacks_at_the_Narrows\" class=\"mw-redirect\" title=\"Siege of Fort Harrison\">Attack at the Narrows</a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "Siege of Fort Harrison", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Harrison"}, {"title": "Siege of Fort Harrison", "link": "https://wikipedia.org/wiki/Siege_of_Fort_Harrison#Attacks_at_the_Narrows"}]}, {"year": "1814", "text": "In a turning point in the War of 1812, the British fail to capture Baltimore. During the battle, <PERSON> composes his poem \"Defence of Fort McHenry\", which is later set to music and becomes the United States' national anthem.", "html": "1814 - In a turning point in the War of 1812, the British fail to capture <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>. During the battle, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> composes his poem \"Defence of Fort McHenry\", which is later set to music and becomes <a href=\"https://wikipedia.org/wiki/The_Star-Spangled_Banner\" title=\"The Star-Spangled Banner\">the United States' national anthem</a>.", "no_year_html": "In a turning point in the War of 1812, the British fail to capture <a href=\"https://wikipedia.org/wiki/Baltimore\" title=\"Baltimore\">Baltimore</a>. During the battle, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> composes his poem \"Defence of Fort McHenry\", which is later set to music and becomes <a href=\"https://wikipedia.org/wiki/The_Star-Spangled_Banner\" title=\"The Star-Spangled Banner\">the United States' national anthem</a>.", "links": [{"title": "Baltimore", "link": "https://wikipedia.org/wiki/Baltimore"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Star-Spangled Banner", "link": "https://wikipedia.org/wiki/The_Star-Spangled_Banner"}]}, {"year": "1843", "text": "The Greek Army rebels (OS date: September 3) against the autocratic rule of king <PERSON> of Greece, demanding the granting of a constitution.", "html": "1843 - The Greek Army <a href=\"https://wikipedia.org/wiki/3_September_1843_Revolution\" title=\"3 September 1843 Revolution\">rebels</a> (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">OS date</a>: September 3) against the autocratic rule of king <a href=\"https://wikipedia.org/wiki/Otto_of_Greece\" title=\"Otto of Greece\"><PERSON> of Greece</a>, demanding the granting of a constitution.", "no_year_html": "The Greek Army <a href=\"https://wikipedia.org/wiki/3_September_1843_Revolution\" title=\"3 September 1843 Revolution\">rebels</a> (<a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">OS date</a>: September 3) against the autocratic rule of king <a href=\"https://wikipedia.org/wiki/Otto_of_Greece\" title=\"Otto of Greece\"><PERSON> of Greece</a>, demanding the granting of a constitution.", "links": [{"title": "3 September 1843 Revolution", "link": "https://wikipedia.org/wiki/3_September_1843_Revolution"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}, {"title": "Otto of Greece", "link": "https://wikipedia.org/wiki/Otto_of_Greece"}]}, {"year": "1847", "text": "Mexican-American War: Six teenage military cadets known as <PERSON><PERSON><PERSON> die defending Chapultepec Castle in the Battle of Chapultepec. American troops under General <PERSON> capture Mexico City in the Mexican-American War.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: Six teenage military <a href=\"https://wikipedia.org/wiki/Cadet\" title=\"Cadet\">cadets</a> known as <a href=\"https://wikipedia.org/wiki/Ni%C3%B1os_H%C3%A9roes\" title=\"Niños Héroes\">Ni<PERSON><PERSON></a> die defending <a href=\"https://wikipedia.org/wiki/Chapultepec_Castle\" title=\"Chapultepec Castle\">Chapultepec Castle</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chapultepec\" title=\"Battle of Chapultepec\">Battle of Chapultepec</a>. American troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture Mexico City in the Mexican-American War.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: Six teenage military <a href=\"https://wikipedia.org/wiki/Cadet\" title=\"Cadet\">cadets</a> known as <a href=\"https://wikipedia.org/wiki/Ni%C3%B1os_H%C3%A9roes\" title=\"Niños Héroes\">Ni<PERSON><PERSON></a> die defending <a href=\"https://wikipedia.org/wiki/Chapultepec_Castle\" title=\"Chapultepec Castle\">Chapultepec Castle</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Chapultepec\" title=\"Battle of Chapultepec\">Battle of Chapultepec</a>. American troops under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> capture Mexico City in the Mexican-American War.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Cadet", "link": "https://wikipedia.org/wiki/Cadet"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni%C3%B1os_H%C3%A9roes"}, {"title": "Chapultepec Castle", "link": "https://wikipedia.org/wiki/Chapultepec_Castle"}, {"title": "Battle of Chapultepec", "link": "https://wikipedia.org/wiki/Battle_of_Chapultepec"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "Vermont railroad worker <PERSON><PERSON><PERSON> survives an iron rod .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1+1⁄4 inches (3.2 cm) in diameter being driven through his brain; the reported effects on his behavior and personality stimulate discussion of the nature of the brain and its functions.", "html": "1848 - Vermont railroad worker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Gage\"><PERSON><PERSON><PERSON></a> survives an iron rod <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">1<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">4</span></span> inches (3.2 cm) in diameter being driven through his brain; the reported effects on his behavior and personality stimulate discussion of the nature of the brain and its functions.", "no_year_html": "Vermont railroad worker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> survives an iron rod <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">1<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">4</span></span> inches (3.2 cm) in diameter being driven through his brain; the reported effects on his behavior and personality stimulate discussion of the nature of the brain and its functions.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "American Civil War: Union soldiers find a copy of <PERSON>'s battle plans in a field outside Frederick, Maryland. It is the prelude to the Battle of Antietam.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> soldiers find a copy of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Special_Order_191\" title=\"Special Order 191\">battle plans</a> in a field outside <a href=\"https://wikipedia.org/wiki/Frederick,_Maryland\" title=\"Frederick, Maryland\">Frederick, Maryland</a>. It is the prelude to the <a href=\"https://wikipedia.org/wiki/Battle_of_Antietam\" title=\"Battle of Antietam\">Battle of Antietam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> soldiers find a copy of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Special_Order_191\" title=\"Special Order 191\">battle plans</a> in a field outside <a href=\"https://wikipedia.org/wiki/Frederick,_Maryland\" title=\"Frederick, Maryland\">Frederick, Maryland</a>. It is the prelude to the <a href=\"https://wikipedia.org/wiki/Battle_of_Antietam\" title=\"Battle of Antietam\">Battle of Antietam</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Special Order 191", "link": "https://wikipedia.org/wiki/Special_Order_191"}, {"title": "Frederick, Maryland", "link": "https://wikipedia.org/wiki/Frederick,_Maryland"}, {"title": "Battle of Antietam", "link": "https://wikipedia.org/wiki/Battle_of_Antietam"}]}, {"year": "1880", "text": "The Basuto Gun War breaks out after the Basuto launch a rebellion against the Cape Colony.", "html": "1880 - The <a href=\"https://wikipedia.org/wiki/Basuto_Gun_War\" title=\"Basuto Gun War\">Basuto Gun War</a> breaks out after the <a href=\"https://wikipedia.org/wiki/Basuto\" class=\"mw-redirect\" title=\"<PERSON>suto\">Basu<PERSON></a> launch a rebellion against the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Basuto_Gun_War\" title=\"Basuto Gun War\">Basuto Gun War</a> breaks out after the <a href=\"https://wikipedia.org/wiki/Basuto\" class=\"mw-redirect\" title=\"<PERSON>suto\">Basuto</a> launch a rebellion against the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a>.", "links": [{"title": "Basuto Gun War", "link": "https://wikipedia.org/wiki/Basuto_Gun_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>su<PERSON>"}, {"title": "Cape Colony", "link": "https://wikipedia.org/wiki/Cape_Colony"}]}, {"year": "1882", "text": "Anglo-Egyptian War: The Battle of Tel el-Kebir is fought.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_War\" title=\"Anglo-Egyptian War\">Anglo-Egyptian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tel_el-Kebir\" class=\"mw-redirect\" title=\"Battle of Tel el-Kebir\">Battle of Tel el-Kebir</a> is fought.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Egyptian_War\" title=\"Anglo-Egyptian War\">Anglo-Egyptian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tel_el-Kebir\" class=\"mw-redirect\" title=\"Battle of Tel el-Kebir\">Battle of Tel el-Kebir</a> is fought.", "links": [{"title": "Anglo-Egyptian War", "link": "https://wikipedia.org/wiki/Anglo-Egyptian_War"}, {"title": "Battle of Tel el-Kebir", "link": "https://wikipedia.org/wiki/Battle_of_Tel_el-Kebir"}]}, {"year": "1898", "text": "<PERSON> patents celluloid photographic film.", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents <a href=\"https://wikipedia.org/wiki/Celluloid\" title=\"Celluloid\">celluloid</a> <a href=\"https://wikipedia.org/wiki/Photographic_film\" title=\"Photographic film\">photographic film</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents <a href=\"https://wikipedia.org/wiki/Celluloid\" title=\"Celluloid\">celluloid</a> <a href=\"https://wikipedia.org/wiki/Photographic_film\" title=\"Photographic film\">photographic film</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Celluloid", "link": "https://wikipedia.org/wiki/Celluloid"}, {"title": "Photographic film", "link": "https://wikipedia.org/wiki/Photographic_film"}]}, {"year": "1899", "text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> make the first ascent of Bat<PERSON> (5,199 m - 17,058 ft), the highest peak of Mount Kenya.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mackinder\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> make the first ascent of Batian (5,199 m - 17,058 ft), the highest peak of <a href=\"https://wikipedia.org/wiki/Mount_Kenya\" title=\"Mount Kenya\">Mount Kenya</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mackinder\"><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> make the first ascent of <PERSON><PERSON> (5,199 m - 17,058 ft), the highest peak of <a href=\"https://wikipedia.org/wiki/Mount_Kenya\" title=\"Mount Kenya\">Mount Kenya</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Mount Kenya", "link": "https://wikipedia.org/wiki/Mount_Kenya"}]}, {"year": "1900", "text": "Filipino insurgents defeat a small American column in the Battle of Pulang Lupa, during the Philippine-American War.", "html": "1900 - Filipino insurgents defeat a small American column in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pulang_Lupa\" title=\"Battle of Pulang Lupa\">Battle of Pulang Lupa</a>, during the <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>.", "no_year_html": "Filipino insurgents defeat a small American column in the <a href=\"https://wikipedia.org/wiki/Battle_of_Pulang_Lupa\" title=\"Battle of Pulang Lupa\">Battle of Pulang Lupa</a>, during the <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>.", "links": [{"title": "Battle of Pulang Lupa", "link": "https://wikipedia.org/wiki/Battle_of_Pulang_Lupa"}, {"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}]}, {"year": "1906", "text": "The Santos-Dumont 14-bis makes a short hop, the first flight of a fixed-wing aircraft in Europe.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_14-bis\" title=\"<PERSON> 14-bis\"><PERSON> 14-bis</a> makes a short hop, the first flight of a fixed-wing aircraft in Europe.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_14-bis\" title=\"Santos<PERSON> 14-bis\"><PERSON> 14-bis</a> makes a short hop, the first flight of a fixed-wing aircraft in Europe.", "links": [{"title": "Santos-Dumont 14-bis", "link": "https://wikipedia.org/wiki/Santos-<PERSON>_14-bis"}]}, {"year": "1922", "text": "The final act of the Greco-Turkish War, the Great Fire of Smyrna, commences.", "html": "1922 - The final act of the <a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)\" class=\"mw-redirect\" title=\"Greco-Turkish War (1919-22)\">Greco-Turkish War</a>, the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Smyrna\" class=\"mw-redirect\" title=\"Great Fire of Smyrna\">Great Fire of Smyrna</a>, commences.", "no_year_html": "The final act of the <a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)\" class=\"mw-redirect\" title=\"Greco-Turkish War (1919-22)\">Greco-Turkish War</a>, the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_Smyrna\" class=\"mw-redirect\" title=\"Great Fire of Smyrna\">Great Fire of Smyrna</a>, commences.", "links": [{"title": "Greco-Turkish War (1919-22)", "link": "https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%9322)"}, {"title": "Great Fire of Smyrna", "link": "https://wikipedia.org/wiki/Great_Fire_of_Smyrna"}]}, {"year": "1923", "text": "Following a military coup in Spain, <PERSON> takes over, setting up a dictatorship.", "html": "1923 - Following a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>#Establishment_of_dictatorship\" title=\"Miguel <PERSON>\">military coup</a> in Spain, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Miguel <PERSON>\"><PERSON></a> takes over, setting up a dictatorship.", "no_year_html": "Following a <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>#Establishment_of_dictatorship\" title=\"Miguel <PERSON>\">military coup</a> in Spain, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Miguel <PERSON>\"><PERSON></a> takes over, setting up a dictatorship.", "links": [{"title": "Miguel <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>#Establishment_of_dictatorship"}, {"title": "Miguel <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON> becomes the first woman elected to the New Zealand Parliament.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman <a href=\"https://wikipedia.org/wiki/Lyttelton_by-election,_1933\" class=\"mw-redirect\" title=\"Lyttelton by-election, 1933\">elected</a> to the New Zealand Parliament.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman <a href=\"https://wikipedia.org/wiki/Lyttelton_by-election,_1933\" class=\"mw-redirect\" title=\"Lyttelton by-election, 1933\">elected</a> to the New Zealand Parliament.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lyttelton by-election, 1933", "link": "https://wikipedia.org/wiki/L<PERSON><PERSON>ton_by-election,_1933"}]}, {"year": "1942", "text": "World War II: Second day of the Battle of Edson's Ridge in the Guadalcanal Campaign. U.S. Marines successfully defeat attacks by the Japanese with heavy losses for the Japanese forces.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge\" title=\"Battle of Edson's Ridge\">Battle of Edson's Ridge</a> in the Guadalcanal Campaign. U.S. Marines successfully defeat attacks by the Japanese with heavy losses for the Japanese forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Second day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge\" title=\"Battle of Edson's Ridge\">Battle of Edson's Ridge</a> in the Guadalcanal Campaign. U.S. Marines successfully defeat attacks by the Japanese with heavy losses for the Japanese forces.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Edson's Ridge", "link": "https://wikipedia.org/wiki/Battle_of_Edson%27s_Ridge"}]}, {"year": "1944", "text": "World War II: Start of the Battle of Meligalas between the Greek Resistance forces of the Greek People's Liberation Army (ELAS) and the collaborationist security battalions.", "html": "1944 - World War II: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Meligalas\" title=\"Battle of Meligalas\">Battle of Meligalas</a> between the Greek Resistance forces of the Greek People's Liberation Army (ELAS) and the collaborationist security battalions.", "no_year_html": "World War II: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_Meligalas\" title=\"Battle of Meligalas\">Battle of Meligalas</a> between the Greek Resistance forces of the Greek People's Liberation Army (ELAS) and the collaborationist security battalions.", "links": [{"title": "Battle of Meligalas", "link": "https://wikipedia.org/wiki/Battle_of_Meligalas"}]}, {"year": "1948", "text": "Deputy Prime Minister of India <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> orders the Army to move into Hyderabad to integrate it with the Indian Union.", "html": "1948 - Deputy Prime Minister of India <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> orders the Army to move into <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad</a> to <a href=\"https://wikipedia.org/wiki/Indian_annexation_of_Hyderabad\" class=\"mw-redirect\" title=\"Indian annexation of Hyderabad\">integrate it</a> with the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Indian Union</a>.", "no_year_html": "Deputy Prime Minister of India <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> orders the Army to move into <a href=\"https://wikipedia.org/wiki/Hyderabad_State\" title=\"Hyderabad State\">Hyderabad</a> to <a href=\"https://wikipedia.org/wiki/Indian_annexation_of_Hyderabad\" class=\"mw-redirect\" title=\"Indian annexation of Hyderabad\">integrate it</a> with the <a href=\"https://wikipedia.org/wiki/Dominion_of_India\" title=\"Dominion of India\">Indian Union</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Hyderabad State", "link": "https://wikipedia.org/wiki/Hyderabad_State"}, {"title": "Indian annexation of Hyderabad", "link": "https://wikipedia.org/wiki/Indian_annexation_of_Hyderabad"}, {"title": "Dominion of India", "link": "https://wikipedia.org/wiki/Dominion_of_India"}]}, {"year": "1948", "text": "<PERSON> is elected United States senator, and becomes the first woman to serve in both the U.S. House of Representatives and the United States Senate.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/United_States_senator\" class=\"mw-redirect\" title=\"United States senator\">United States senator</a>, and becomes the first woman to serve in both the U.S. <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> and the United States Senate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/United_States_senator\" class=\"mw-redirect\" title=\"United States senator\">United States senator</a>, and becomes the first woman to serve in both the U.S. <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">House of Representatives</a> and the United States Senate.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States senator", "link": "https://wikipedia.org/wiki/United_States_senator"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}]}, {"year": "1953", "text": "<PERSON><PERSON> is appointed General Secretary of the Communist Party of the Soviet Union.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is appointed <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1956", "text": "The IBM 305 RAMAC is introduced, the first commercial computer to use disk storage.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/IBM_305_RAMAC\" title=\"IBM 305 RAMAC\">IBM 305 RAMAC</a> is introduced, the first commercial computer to use <a href=\"https://wikipedia.org/wiki/Disk_storage\" title=\"Disk storage\">disk storage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/IBM_305_RAMAC\" title=\"IBM 305 RAMAC\">IBM 305 RAMAC</a> is introduced, the first commercial computer to use <a href=\"https://wikipedia.org/wiki/Disk_storage\" title=\"Disk storage\">disk storage</a>.", "links": [{"title": "IBM 305 RAMAC", "link": "https://wikipedia.org/wiki/IBM_305_RAMAC"}, {"title": "Disk storage", "link": "https://wikipedia.org/wiki/Disk_storage"}]}, {"year": "1956", "text": "The dike around the Dutch polder East Flevoland is closed.", "html": "1956 - The <a href=\"https://wikipedia.org/wiki/Dike_(construction)\" class=\"mw-redirect\" title=\"Dike (construction)\">dike</a> around the Dutch <a href=\"https://wikipedia.org/wiki/Polder\" title=\"Polder\">polder</a> <a href=\"https://wikipedia.org/wiki/Zuiderzee_Works#The_Flevolands\" title=\"Zuiderzee Works\">East Flevoland</a> is closed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dike_(construction)\" class=\"mw-redirect\" title=\"Dike (construction)\">dike</a> around the Dutch <a href=\"https://wikipedia.org/wiki/Polder\" title=\"Polder\">polder</a> <a href=\"https://wikipedia.org/wiki/Zuiderzee_Works#The_Flevolands\" title=\"Zuiderzee Works\">East Flevoland</a> is closed.", "links": [{"title": "Dike (construction)", "link": "https://wikipedia.org/wiki/Dike_(construction)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>der"}, {"title": "Zuiderzee Works", "link": "https://wikipedia.org/wiki/Zuiderzee_Works#The_Flevolands"}]}, {"year": "1962", "text": "An appeals court orders the University of Mississippi to admit <PERSON>, the first African-American student admitted to the segregated university.", "html": "1962 - An appeals court orders the University of Mississippi to admit <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first African-American student admitted to the segregated university.", "no_year_html": "An appeals court orders the University of Mississippi to admit <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first African-American student admitted to the segregated university.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "South Vietnamese Generals <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> fail in a coup attempt against General <PERSON><PERSON><PERSON><PERSON>.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese</a> Generals <a href=\"https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t\" title=\"Lâm <PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_%C4%90%E1%BB%A9c\" title=\"Dương Văn <PERSON>\">Dương <PERSON></a> fail in a <a href=\"https://wikipedia.org/wiki/September_1964_South_Vietnamese_coup_attempt\" title=\"September 1964 South Vietnamese coup attempt\">coup attempt</a> against General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese</a> Generals <a href=\"https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t\" title=\"Lâm <PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_%C4%90%E1%BB%A9c\" title=\"Dương Văn <PERSON>\">Dương <PERSON></a> fail in a <a href=\"https://wikipedia.org/wiki/September_1964_South_Vietnamese_coup_attempt\" title=\"September 1964 South Vietnamese coup attempt\">coup attempt</a> against General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A2m_V%C4%83n_Ph%C3%A1t"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_%C4%90%E1%BB%A9c"}, {"title": "September 1964 South Vietnamese coup attempt", "link": "https://wikipedia.org/wiki/September_1964_South_Vietnamese_coup_attempt"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}]}, {"year": "1964", "text": "<PERSON> addresses a crowd of 20,000 West Berliners on Sunday, in Waldbühne.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> addresses a crowd of 20,000 West Berliners on Sunday, in <a href=\"https://wikipedia.org/wiki/Waldb%C3%BChne\" title=\"Waldbühne\">W<PERSON><PERSON><PERSON><PERSON>e</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> addresses a crowd of 20,000 West Berliners on Sunday, in <a href=\"https://wikipedia.org/wiki/Waldb%C3%BChne\" title=\"Waldbühne\">W<PERSON><PERSON><PERSON><PERSON>e</a>.", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Waldbühne", "link": "https://wikipedia.org/wiki/Waldb%C3%BChne"}]}, {"year": "1968", "text": "Cold War: Albania leaves the Warsaw Pact.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania\" title=\"People's Socialist Republic of Albania\">Albania</a> leaves the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania\" title=\"People's Socialist Republic of Albania\">Albania</a> leaves the <a href=\"https://wikipedia.org/wiki/Warsaw_Pact\" title=\"Warsaw Pact\">Warsaw Pact</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "People's Socialist Republic of Albania", "link": "https://wikipedia.org/wiki/People%27s_Socialist_Republic_of_Albania"}, {"title": "Warsaw Pact", "link": "https://wikipedia.org/wiki/Warsaw_Pact"}]}, {"year": "1971", "text": "State police and National Guardsmen storm New York's Attica Prison to quell a prison revolt, which claimed 43 lives.", "html": "1971 - State police and <a href=\"https://wikipedia.org/wiki/National_Guard_of_the_United_States\" class=\"mw-redirect\" title=\"National Guard of the United States\">National Guardsmen</a> storm New York's <a href=\"https://wikipedia.org/wiki/Attica_Prison\" class=\"mw-redirect\" title=\"Attica Prison\">Attica Prison</a> to quell a <a href=\"https://wikipedia.org/wiki/Attica_Prison_riot\" title=\"Attica Prison riot\">prison revolt</a>, which claimed 43 lives.", "no_year_html": "State police and <a href=\"https://wikipedia.org/wiki/National_Guard_of_the_United_States\" class=\"mw-redirect\" title=\"National Guard of the United States\">National Guardsmen</a> storm New York's <a href=\"https://wikipedia.org/wiki/Attica_Prison\" class=\"mw-redirect\" title=\"Attica Prison\">Attica Prison</a> to quell a <a href=\"https://wikipedia.org/wiki/Attica_Prison_riot\" title=\"Attica Prison riot\">prison revolt</a>, which claimed 43 lives.", "links": [{"title": "National Guard of the United States", "link": "https://wikipedia.org/wiki/National_Guard_of_the_United_States"}, {"title": "Attica Prison", "link": "https://wikipedia.org/wiki/Attica_Prison"}, {"title": "Attica Prison riot", "link": "https://wikipedia.org/wiki/Attica_Prison_riot"}]}, {"year": "1971", "text": "Chairman <PERSON>'s second in command and successor Marshal <PERSON> flees China after the failure of an alleged coup. His plane crashes in Mongolia, killing all aboard.", "html": "1971 - Chairman <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s second in command and successor Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> flees China after the failure of an alleged coup. His plane crashes in <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>, killing all aboard.", "no_year_html": "Chairman <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s second in command and successor Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Biao\"><PERSON></a> flees China after the failure of an alleged coup. His plane crashes in <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a>, killing all aboard.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lin_<PERSON>o"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}]}, {"year": "1979", "text": "South Africa grants independence to the \"homeland\" of Venda (not recognised outside South Africa).", "html": "1979 - South Africa grants independence to the \"homeland\" of <a href=\"https://wikipedia.org/wiki/Venda\" title=\"Venda\">Venda</a> (not recognised outside South Africa).", "no_year_html": "South Africa grants independence to the \"homeland\" of <a href=\"https://wikipedia.org/wiki/Venda\" title=\"Venda\">Venda</a> (not recognised outside South Africa).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Venda"}]}, {"year": "1982", "text": "Spantax Flight 995 crashes at Málaga Airport during a rejected takeoff, killing 50 of the 394 people on board.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Spantax_Flight_995\" title=\"Spantax Flight 995\">Spantax Flight 995</a> crashes at <a href=\"https://wikipedia.org/wiki/M%C3%A1laga_Airport\" title=\"Málaga Airport\">Málaga Airport</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a>, killing 50 of the 394 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spantax_Flight_995\" title=\"Spantax Flight 995\">Spantax Flight 995</a> crashes at <a href=\"https://wikipedia.org/wiki/M%C3%A1laga_Airport\" title=\"Málaga Airport\">Málaga Airport</a> during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a>, killing 50 of the 394 people on board.", "links": [{"title": "Spantax Flight 995", "link": "https://wikipedia.org/wiki/Spantax_Flight_995"}, {"title": "Málaga Airport", "link": "https://wikipedia.org/wiki/M%C3%A1laga_Airport"}, {"title": "Rejected takeoff", "link": "https://wikipedia.org/wiki/Rejected_takeoff"}]}, {"year": "1985", "text": "Super Mario Bros. is released in Japan for the NES, which starts the Super Mario series of platforming games.", "html": "1985 - <i><a href=\"https://wikipedia.org/wiki/Super_Mario_Bros.\" title=\"Super Mario Bros.\">Super Mario Bros.</a></i> is released in Japan for the <a href=\"https://wikipedia.org/wiki/Nintendo_Entertainment_System\" title=\"Nintendo Entertainment System\">NES</a>, which starts the <i><a href=\"https://wikipedia.org/wiki/Super_Mario\" title=\"Super Mario\">Super Mario</a></i> series of platforming games.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Super_Mario_Bros.\" title=\"Super Mario Bros.\">Super Mario Bros.</a></i> is released in Japan for the <a href=\"https://wikipedia.org/wiki/Nintendo_Entertainment_System\" title=\"Nintendo Entertainment System\">NES</a>, which starts the <i><a href=\"https://wikipedia.org/wiki/Super_Mario\" title=\"Super Mario\">Super Mario</a></i> series of platforming games.", "links": [{"title": "Super Mario Bros.", "link": "https://wikipedia.org/wiki/Super_Mario_<PERSON>."}, {"title": "Nintendo Entertainment System", "link": "https://wikipedia.org/wiki/Nintendo_Entertainment_System"}, {"title": "Super Mario", "link": "https://wikipedia.org/wiki/<PERSON>_Mario"}]}, {"year": "1986", "text": "A magnitude 6.0 earthquake strikes Kalamata, Greece with a maximum Modified Mercalli intensity of X (Extreme), killing at least 20 and causing heavy damage in the city.", "html": "1986 - A <a href=\"https://wikipedia.org/wiki/1986_Kalamata_earthquake\" title=\"1986 Kalamata earthquake\">magnitude 6.0 earthquake</a> strikes Kalamata, Greece with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Modified Mercalli intensity</a> of X (<i>Extreme</i>), killing at least 20 and causing heavy damage in the city.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1986_Kalamata_earthquake\" title=\"1986 Kalamata earthquake\">magnitude 6.0 earthquake</a> strikes Kalamata, Greece with a maximum <a href=\"https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale\" title=\"Modified Mercalli intensity scale\">Modified Mercalli intensity</a> of X (<i>Extreme</i>), killing at least 20 and causing heavy damage in the city.", "links": [{"title": "1986 Kalamata earthquake", "link": "https://wikipedia.org/wiki/1986_Kalamata_earthquake"}, {"title": "Modified Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Modified_Mercalli_intensity_scale"}]}, {"year": "1987", "text": "Goiânia accident: A radioactive object is stolen from an abandoned hospital in Goiânia, Brazil, contaminating many people in the following weeks and causing some to die from radiation poisoning.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Goi%C3%A2nia_accident\" title=\"Goiânia accident\">Goiânia accident</a>: A <a href=\"https://wikipedia.org/wiki/Radioactive\" class=\"mw-redirect\" title=\"Radioactive\">radioactive</a> object is stolen from an abandoned hospital in <a href=\"https://wikipedia.org/wiki/Goi%C3%A2nia\" title=\"Goiânia\">Goiânia</a>, Brazil, contaminating many people in the following weeks and causing some to die from <a href=\"https://wikipedia.org/wiki/Radiation_poisoning\" class=\"mw-redirect\" title=\"Radiation poisoning\">radiation poisoning</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goi%C3%A2nia_accident\" title=\"Goiânia accident\">Goiânia accident</a>: A <a href=\"https://wikipedia.org/wiki/Radioactive\" class=\"mw-redirect\" title=\"Radioactive\">radioactive</a> object is stolen from an abandoned hospital in <a href=\"https://wikipedia.org/wiki/Goi%C3%A2nia\" title=\"Goiânia\">Goiânia</a>, Brazil, contaminating many people in the following weeks and causing some to die from <a href=\"https://wikipedia.org/wiki/Radiation_poisoning\" class=\"mw-redirect\" title=\"Radiation poisoning\">radiation poisoning</a>.", "links": [{"title": "Goiânia accident", "link": "https://wikipedia.org/wiki/Goi%C3%A2nia_accident"}, {"title": "Radioactive", "link": "https://wikipedia.org/wiki/Radioactive"}, {"title": "Goiânia", "link": "https://wikipedia.org/wiki/Goi%C3%A2nia"}, {"title": "Radiation poisoning", "link": "https://wikipedia.org/wiki/Radiation_poisoning"}]}, {"year": "1988", "text": "Hurricane <PERSON> is the strongest recorded hurricane in the Western Hemisphere, later replaced by Hurricane <PERSON><PERSON><PERSON> in 2005 (based on barometric pressure).", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Hurricane_Gilbert\" title=\"Hurricane Gilbert\">Hurricane <PERSON></a> is the strongest recorded hurricane in the Western Hemisphere, later replaced by <a href=\"https://wikipedia.org/wiki/Hurricane_Wilma\" title=\"Hurricane Wilma\">Hurricane Wilma</a> in <a href=\"https://wikipedia.org/wiki/2005\" title=\"2005\">2005</a> (based on <a href=\"https://wikipedia.org/wiki/Barometric_pressure\" class=\"mw-redirect\" title=\"Barometric pressure\">barometric pressure</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Gilbert\" title=\"Hurricane Gilbert\">Hurricane <PERSON></a> is the strongest recorded hurricane in the Western Hemisphere, later replaced by <a href=\"https://wikipedia.org/wiki/Hurricane_Wilma\" title=\"Hurricane Wilma\">Hurricane Wilma</a> in <a href=\"https://wikipedia.org/wiki/2005\" title=\"2005\">2005</a> (based on <a href=\"https://wikipedia.org/wiki/Barometric_pressure\" class=\"mw-redirect\" title=\"Barometric pressure\">barometric pressure</a>).", "links": [{"title": "Hurricane Gilbert", "link": "https://wikipedia.org/wiki/Hurricane_Gilbert"}, {"title": "Hurricane <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hurricane_Wilma"}, {"title": "2005", "link": "https://wikipedia.org/wiki/2005"}, {"title": "Barometric pressure", "link": "https://wikipedia.org/wiki/Barometric_pressure"}]}, {"year": "1989", "text": "Largest anti-Apartheid march in South Africa, led by <PERSON>.", "html": "1989 - Largest anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a> march in South Africa, led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Largest anti-<a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a> march in South Africa, led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "Israeli Prime Minister <PERSON><PERSON><PERSON> shakes hands with Palestine Liberation Organization chairman <PERSON><PERSON> at the White House after signing the Oslo Accords granting limited Palestinian autonomy.", "html": "1993 - Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Yitz<PERSON>bin\"><PERSON><PERSON><PERSON></a> shakes hands with <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> chairman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> after signing the <a href=\"https://wikipedia.org/wiki/Oslo_Accords\" title=\"Oslo Accords\">Oslo Accords</a> granting limited Palestinian autonomy.", "no_year_html": "Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Yi<PERSON><PERSON>bin\"><PERSON><PERSON><PERSON></a> shakes hands with <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a> chairman <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> after signing the <a href=\"https://wikipedia.org/wiki/Oslo_Accords\" title=\"Oslo Accords\">Oslo Accords</a> granting limited Palestinian autonomy.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "Oslo Accords", "link": "https://wikipedia.org/wiki/Oslo_Accords"}]}, {"year": "1997", "text": "A German Air Force Tupolev Tu-154 and a United States Air Force Lockheed C-141 Starlifter collide in mid-air near Namibia, killing 33.", "html": "1997 - A <a href=\"https://wikipedia.org/wiki/German_Air_Force\" title=\"German Air Force\">German Air Force</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> and a <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-141_Starlifter\" title=\"Lockheed C-141 Starlifter\">Lockheed C-141 Starlifter</a> <a href=\"https://wikipedia.org/wiki/1997_Namibia_mid-air_collision\" title=\"1997 Namibia mid-air collision\">collide</a> in mid-air near <a href=\"https://wikipedia.org/wiki/Namibia\" title=\"Namibia\">Namibia</a>, killing 33.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/German_Air_Force\" title=\"German Air Force\">German Air Force</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> and a <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-141_Starlifter\" title=\"Lockheed C-141 Starlifter\">Lockheed C-141 Starlifter</a> <a href=\"https://wikipedia.org/wiki/1997_Namibia_mid-air_collision\" title=\"1997 Namibia mid-air collision\">collide</a> in mid-air near <a href=\"https://wikipedia.org/wiki/Namibia\" title=\"Namibia\">Namibia</a>, killing 33.", "links": [{"title": "German Air Force", "link": "https://wikipedia.org/wiki/German_Air_Force"}, {"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Lockheed C-141 Starlifter", "link": "https://wikipedia.org/wiki/Lockheed_C-141_Starlifter"}, {"title": "1997 Namibia mid-air collision", "link": "https://wikipedia.org/wiki/1997_Namibia_mid-air_collision"}, {"title": "Namibia", "link": "https://wikipedia.org/wiki/Namibia"}]}, {"year": "2001", "text": "Civilian aircraft traffic resumes in the United States after the September 11 attacks.", "html": "2001 - Civilian aircraft traffic resumes in the United States after the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "no_year_html": "Civilian aircraft traffic resumes in the United States after the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a>.", "links": [{"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}]}, {"year": "2007", "text": "The Declaration on the Rights of Indigenous Peoples is adopted by the United Nations General Assembly.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Declaration_on_the_Rights_of_Indigenous_Peoples\" title=\"Declaration on the Rights of Indigenous Peoples\">Declaration on the Rights of Indigenous Peoples</a> is adopted by the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Declaration_on_the_Rights_of_Indigenous_Peoples\" title=\"Declaration on the Rights of Indigenous Peoples\">Declaration on the Rights of Indigenous Peoples</a> is adopted by the <a href=\"https://wikipedia.org/wiki/United_Nations_General_Assembly\" title=\"United Nations General Assembly\">United Nations General Assembly</a>.", "links": [{"title": "Declaration on the Rights of Indigenous Peoples", "link": "https://wikipedia.org/wiki/Declaration_on_the_Rights_of_Indigenous_Peoples"}, {"title": "United Nations General Assembly", "link": "https://wikipedia.org/wiki/United_Nations_General_Assembly"}]}, {"year": "2007", "text": "The McLaren F1 team are found guilty of possessing confidential information from the Ferrari team, fined $100 million, and excluded from the constructors' championship standings.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren F1 team</a> are found guilty of <a href=\"https://wikipedia.org/wiki/2007_Formula_One_espionage_controversy\" title=\"2007 Formula One espionage controversy\">possessing confidential information from the</a> <a href=\"https://wikipedia.org/wiki/Scuderia_Ferrari\" title=\"Scuderia Ferrari\">Ferrari</a> team, fined $100 million, and excluded from the constructors' championship standings.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/McLaren\" title=\"McLaren\">McLaren F1 team</a> are found guilty of <a href=\"https://wikipedia.org/wiki/2007_Formula_One_espionage_controversy\" title=\"2007 Formula One espionage controversy\">possessing confidential information from the</a> <a href=\"https://wikipedia.org/wiki/Scuderia_Ferrari\" title=\"Scuderia Ferrari\">Ferrari</a> team, fined $100 million, and excluded from the constructors' championship standings.", "links": [{"title": "McLaren", "link": "https://wikipedia.org/wiki/McLaren"}, {"title": "2007 Formula One espionage controversy", "link": "https://wikipedia.org/wiki/2007_Formula_One_espionage_controversy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Scuderia_Ferrari"}]}, {"year": "2008", "text": "Delhi, India, is hit by a series of bomb blasts, resulting in 30 deaths and 130 injuries.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, India, is hit by a <a href=\"https://wikipedia.org/wiki/13_September_2008_Delhi_bombings\" title=\"13 September 2008 Delhi bombings\">series of bomb blasts</a>, resulting in 30 deaths and 130 injuries.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Delhi\" title=\"Delhi\">Delhi</a>, India, is hit by a <a href=\"https://wikipedia.org/wiki/13_September_2008_Delhi_bombings\" title=\"13 September 2008 Delhi bombings\">series of bomb blasts</a>, resulting in 30 deaths and 130 injuries.", "links": [{"title": "Delhi", "link": "https://wikipedia.org/wiki/Delhi"}, {"title": "13 September 2008 Delhi bombings", "link": "https://wikipedia.org/wiki/13_September_2008_Delhi_bombings"}]}, {"year": "2013", "text": "Taliban insurgents attack the United States consulate in Herat, Afghanistan, with two members of the Afghan National Police reported dead and about 20 civilians injured.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> insurgents <a href=\"https://wikipedia.org/wiki/2013_attack_on_U.S._consulate_in_Herat\" title=\"2013 attack on U.S. consulate in Herat\">attack the United States consulate in Herat</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, with two members of the <a href=\"https://wikipedia.org/wiki/Afghan_National_Police\" title=\"Afghan National Police\">Afghan National Police</a> reported dead and about 20 civilians injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> insurgents <a href=\"https://wikipedia.org/wiki/2013_attack_on_U.S._consulate_in_Herat\" title=\"2013 attack on U.S. consulate in Herat\">attack the United States consulate in Herat</a>, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, with two members of the <a href=\"https://wikipedia.org/wiki/Afghan_National_Police\" title=\"Afghan National Police\">Afghan National Police</a> reported dead and about 20 civilians injured.", "links": [{"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "2013 attack on U.S. consulate in Herat", "link": "https://wikipedia.org/wiki/2013_attack_on_U.S._consulate_in_Herat"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "Afghan National Police", "link": "https://wikipedia.org/wiki/Afghan_National_Police"}]}], "Births": [{"year": "64", "text": "<PERSON>, Roman daughter of <PERSON> (d. AD 91)", "html": "64 - AD 64 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Flav<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Titus\" title=\"<PERSON>\"><PERSON></a> (d. AD 91)", "no_year_html": "AD 64 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Titus\" title=\"<PERSON>\"><PERSON></a> (d. AD 91)", "links": [{"title": "Julia Flavia", "link": "https://wikipedia.org/wiki/Julia_Flavia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}]}, {"year": "678", "text": "<PERSON><PERSON><PERSON><PERSON>, Mayan ruler (d. 730)", "html": "678 - <a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Ahkal_Mo%CA%BC_Nahb_III\" title=\"Kʼinich Ahkal Moʼ Nahb III\">Kʼ<PERSON>ch Ahkal Moʼ Nahb III</a>, Mayan ruler (d. 730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Ahkal_Mo%CA%BC_Nahb_III\" title=\"Kʼinich Ahkal Moʼ Nahb III\">Kʼinich Ahkal Moʼ Nahb III</a>, Mayan ruler (d. 730)", "links": [{"title": "Kʼinich Ahkal Moʼ Nahb III", "link": "https://wikipedia.org/wiki/K%CA%BCinich_Ahkal_Mo%CA%BC_Nahb_III"}]}, {"year": "1087", "text": "<PERSON> <PERSON>, Byzantine emperor (d. 1143)", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (d. 1143)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (d. 1143)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1373", "text": "<PERSON><PERSON><PERSON>, King of Ava (d. 1431)", "html": "1373 - <a href=\"https://wikipedia.org/wiki/Minkhaung_I\" title=\"Minkhaung I\"><PERSON><PERSON><PERSON> <PERSON></a>, King of Ava (d. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minkhaung_I\" title=\"Minkhaung I\">Minkha<PERSON> <PERSON></a>, King of Ava (d. 1431)", "links": [{"title": "Minkhaung I", "link": "https://wikipedia.org/wiki/Min<PERSON><PERSON>_I"}]}, {"year": "1475", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (d. 1507)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1507)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 1507)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e_<PERSON>rgia"}]}, {"year": "1502", "text": "<PERSON>, English poet and historian (d. 1552)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)\" title=\"<PERSON> (antiquary)\"><PERSON></a>, English poet and historian (d. 1552)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)\" title=\"<PERSON> (antiquary)\"><PERSON></a>, English poet and historian (d. 1552)", "links": [{"title": "<PERSON> (antiquary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antiquary)"}]}, {"year": "1521", "text": "<PERSON>, 1st Baron <PERSON>, English academic and politician, Lord High Treasurer (d. 1598)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_Burg<PERSON>ey\" title=\"<PERSON>, 1st Baron Burg<PERSON>ey\"><PERSON>, 1st Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>ey\" title=\"<PERSON>, 1st Baron B<PERSON>ey\"><PERSON>, 1st Baron <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1598)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1594", "text": "<PERSON>, Italian theorbo player and composer (d. 1667)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Theorbo\" title=\"<PERSON><PERSON><PERSON>\">theor<PERSON></a> player and composer (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Theorbo\" title=\"<PERSON><PERSON><PERSON>\">theorbo</a> player and composer (d. 1667)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theorbo"}]}, {"year": "1604", "text": "Sir <PERSON>, 1st Baronet, English commander and politician (d. 1698)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English commander and politician (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English commander and politician (d. 1698)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}]}, {"year": "1755", "text": "<PERSON>, American inventor, engineer and businessman (d. 1819)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, engineer and businessman (d. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor, engineer and businessman (d. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, German philosopher and author (d. 1880)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arnold Ruge\"><PERSON></a>, German philosopher and author (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arnold Ruge\"><PERSON></a>, German philosopher and author (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American general and educator (d. 1864)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and educator (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and educator (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Former American slave and a founder of Friends' Asylum for Colored Orphans (d. 1900)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former American slave and a founder of <a href=\"https://wikipedia.org/wiki/Friends%27_Asylum_for_Colored_Orphans\" title=\"Friends' Asylum for Colored Orphans\">Friends' Asylum for Colored Orphans</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Former American slave and a founder of <a href=\"https://wikipedia.org/wiki/Friends%27_Asylum_for_Colored_Orphans\" title=\"Friends' Asylum for Colored Orphans\">Friends' Asylum for Colored Orphans</a> (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Friends' Asylum for Colored Orphans", "link": "https://wikipedia.org/wiki/Friends%27_Asylum_for_Colored_Orphans"}]}, {"year": "1819", "text": "<PERSON>, German pianist and composer (d. 1896)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist and composer (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Austrian author (d. 1916)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, American soldier and politician (d. 1920)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>head"}]}, {"year": "1851", "text": "<PERSON>, American physician and biologist (d. 1902)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biologist (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and biologist (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON><PERSON><PERSON>, Polish rebel and activist (d. 1937)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Drzyma%C5%82a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish rebel and activist (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>%C5%82_Drzyma%C5%82a\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish rebel and activist (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_Drzyma%C5%82a"}]}, {"year": "1857", "text": "<PERSON>, American businessman, founded The Hershey Company (d. 1945)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Milton_S._Hershey\" title=\"Milton S. Hershey\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/The_Hershey_Company\" title=\"The Hershey Company\">The Hershey Company</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milton_S._Hershey\" title=\"Milton S. Hershey\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/The_Hershey_Company\" title=\"The Hershey Company\">The Hershey Company</a> (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milton_S._Hershey"}, {"title": "The Hershey Company", "link": "https://wikipedia.org/wiki/The_Hershey_Company"}]}, {"year": "1860", "text": "<PERSON>, American general and lawyer (d. 1948)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and lawyer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and lawyer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Indian-English field marshal (d. 1951)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English field marshal (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English field marshal (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese politician and diplomat, 44th Prime Minister of Japan (d. 1951)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_Shi<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, 44th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_Shi<PERSON>hara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese politician and diplomat, 44th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_<PERSON><PERSON>hara"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1873", "text": "Constantin <PERSON>, German mathematician and author (d. 1950)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Constantin_<PERSON>th%C3%A9odory\" title=\"Constantin <PERSON>\">Con<PERSON><PERSON></a>, German mathematician and author (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_<PERSON>th%C3%A9odory\" title=\"Constantin <PERSON>\">Con<PERSON><PERSON></a>, German mathematician and author (d. 1950)", "links": [{"title": "Constantin <PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Carath%C3%A9odory"}]}, {"year": "1874", "text": "<PERSON>, American lawyer and politician (d. 1962)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Austrian composer and painter (d. 1951)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and painter (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and painter (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON>,  American novelist and short story writer (d. 1941)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, German-Swiss explorer (d. 1957)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss explorer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss explorer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English captain (d. 1962)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American film producer, co-founded Famous Players-<PERSON><PERSON> (d. 1958)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Famous_Players%E2%80%93Lasky\" title=\"Famous Players-Lasky\">Famous Players-<PERSON><PERSON></a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer, co-founded <a href=\"https://wikipedia.org/wiki/Famous_Players%E2%80%93Lasky\" title=\"Famous Players-Lasky\">Famous Players-<PERSON><PERSON></a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Famous Players-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Famous_Players%E2%80%93Lasky"}]}, {"year": "1882", "text": "<PERSON>, Cuban physician and politician, 6th President of Cuba (d. 1969)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Grau\" title=\"<PERSON>\"><PERSON></a>, Cuban physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Grau\" title=\"<PERSON>\"><PERSON></a>, Cuban physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cuba\" title=\"President of Cuba\">President of Cuba</a> (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Grau"}, {"title": "President of Cuba", "link": "https://wikipedia.org/wiki/President_of_Cuba"}]}, {"year": "1883", "text": "<PERSON><PERSON><PERSON>, American pole vaulter (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pole vaulter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pole vaulter (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Greek admiral and politician, 136th Prime Minister of Greece (d. 1957)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral and politician, 136th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek admiral and politician, 136th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ros_Voulgaris"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1885", "text": "<PERSON>, Austrian-German mathematician and academic (d. 1962)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German mathematician and academic (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German mathematician and academic (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, German pilot and sculptor (d. 1925)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pilot and sculptor (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German pilot and sculptor (d. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (d. 1975)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organic_chemist)\" class=\"mw-redirect\" title=\"<PERSON> (organic chemist)\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(organic_chemist)\" class=\"mw-redirect\" title=\"<PERSON> (organic chemist)\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1975)", "links": [{"title": "<PERSON> (organic chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organic_chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1887", "text": "<PERSON>, Croatian-Swiss biochemist and academic, Nobel Prize laureate (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C5%BEi%C4%8Dka\" title=\"<PERSON>\"><PERSON></a>, Croatian-Swiss biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>u%C5%BEi%C4%8Dka\" title=\"<PERSON> R<PERSON>\"><PERSON></a>, Croatian-Swiss biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_Ru%C5%BEi%C4%8Dka"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1890", "text": "<PERSON>, French-Monegasque businessman, founded the Monaco Grand Prix (d. 1978)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French-Monegasque businessman, founded the <a href=\"https://wikipedia.org/wiki/Monaco_Grand_Prix\" title=\"Monaco Grand Prix\">Monaco Grand Prix</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s\" title=\"<PERSON>\"><PERSON></a>, French-Monegasque businessman, founded the <a href=\"https://wikipedia.org/wiki/Monaco_Grand_Prix\" title=\"Monaco Grand Prix\">Monaco Grand Prix</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antony_Nogh%C3%A8s"}, {"title": "Monaco Grand Prix", "link": "https://wikipedia.org/wiki/Monaco_Grand_Prix"}]}, {"year": "1891", "text": "<PERSON>, German captain and pilot (d. 1960)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>russ\"><PERSON></a>, German captain and pilot (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American clarinet player (d. 1953)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist and playwright (d. 1984)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist and playwright (d. 1984)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Polish poet, playwright, and director (d. 1953)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet, playwright, and director (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet, playwright, and director (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American rugby player and sprinter (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rugby player and sprinter (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rugby player and sprinter (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, French conductor and composer (d. 1963)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9sormi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9sormi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French conductor and composer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roger_D%C3%A9sormi%C3%A8re"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Sri Lankan lawyer and politician (d. 1964)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/C._Sittampalam\" title=\"C. Sittampalam\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._Sittampalam\" title=\"C<PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and politician (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Sittampalam"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON><PERSON>, Romanian politician (d. 1938)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Romanian politician (d. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American actress (d. 1996)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American civil rights organizer, mother of <PERSON>, Jr. (d. 1974)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Alberta_<PERSON>_<PERSON>\" title=\"Alberta Williams King\"><PERSON></a>, American civil rights organizer, mother of <PERSON>, Jr. (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alberta_<PERSON>_<PERSON>\" title=\"Alberta Williams King\"><PERSON></a>, American civil rights organizer, mother of <PERSON>, Jr. (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberta_Williams_King"}]}, {"year": "1904", "text": "<PERSON>, American actress (d. 1954)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American saxophonist (d. 1941)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Greek director and playwright (d. 1987)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and playwright (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and playwright (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Dutch farmer and politician, 4th President of the European Commission (d. 1995)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>holt\" title=\"<PERSON><PERSON> Mansholt\"><PERSON><PERSON></a>, Dutch farmer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>holt\" title=\"<PERSON><PERSON> Mansholt\"><PERSON><PERSON></a>, Dutch farmer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1908", "text": "<PERSON>, American actress and vocal artist (d. 1998)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Questel\"><PERSON></a>, American actress and vocal artist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mae Questel\"><PERSON></a>, American actress and vocal artist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mae_Questel"}]}, {"year": "1909", "text": "<PERSON>, English footballer (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON>, Dutch journalist and radio host (d. 2014)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Frits_Thors\" title=\"Frits Thors\"><PERSON><PERSON> <PERSON></a>, Dutch journalist and radio host (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Thors\" title=\"Frits Thors\"><PERSON><PERSON> <PERSON><PERSON></a>, Dutch journalist and radio host (d. 2014)", "links": [{"title": "Frits Thors", "link": "https://wikipedia.org/wiki/Frits_Thors"}]}, {"year": "1911", "text": "<PERSON>, American singer-songwriter and mandolin player (d. 1996)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and mandolin player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and mandolin player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American colonel and politician (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and politician (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American actress (d. 1982)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Finnish physician and professor (d. 2005)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4l%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and professor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Set%C3%A4l%C3%A4\" title=\"<PERSON>\"><PERSON></a>, Finnish physician and professor (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kai_Set%C3%A4l%C3%A4"}]}, {"year": "1914", "text": "<PERSON>, English-American pianist, composer, producer, and journalist (d. 1994)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist, composer, producer, and journalist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American pianist, composer, producer, and journalist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, British novelist, poet, and screenwriter (d. 1990)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British novelist, poet, and screenwriter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British novelist, poet, and screenwriter (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oa<PERSON>_<PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American historian and author (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (d. 2012)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1917", "text": "<PERSON>, American soldier, composer, and educator (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American soldier, composer, and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American soldier, composer, and educator (d. 2013)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1918", "text": "<PERSON>, American singer-songwriter and conductor (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)\" title=\"<PERSON> (musician, born 1918)\"><PERSON></a>, American singer-songwriter and conductor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)\" title=\"<PERSON> (musician, born 1918)\"><PERSON></a>, American singer-songwriter and conductor (d. 2015)", "links": [{"title": "<PERSON> (musician, born 1918)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician,_born_1918)"}]}, {"year": "1918", "text": "<PERSON>, Argentinian actor and singer (d. 1980)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and singer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actor and singer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English philosopher and author (d. 2018)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Baron <PERSON>, Austrian-English journalist, publisher, and philanthropist (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Austrian-English journalist, publisher, and philanthropist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Austrian-English journalist, publisher, and philanthropist (d. 2016)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Danish-American journalist and author (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Mina<PERSON>\" title=\"<PERSON><PERSON> Holmelund Minarik\"><PERSON><PERSON></a>, Danish-American journalist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Holmelund Minarik\"><PERSON><PERSON></a>, Danish-American journalist and author (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/El<PERSON>_<PERSON><PERSON><PERSON><PERSON>_Minarik"}]}, {"year": "1922", "text": "<PERSON>, American singer and pianist (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and pianist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer and pianist (d. 1999)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1922", "text": "<PERSON>, American lawyer and jurist (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, French photographer and journalist (d. 1999)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French photographer and journalist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French photographer and journalist (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian tenor and educator (d. 1976)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tenor and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tenor and educator (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor (d. 1985)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French composer and conductor (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and actor (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Mel_Torm%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mel_Torm%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_Torm%C3%A9"}]}, {"year": "1926", "text": "<PERSON>, American economist and academic (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Canadian ice hockey player, coach, and manager (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON> <PERSON>, American soldier and politician (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON>.</a>, American soldier and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> <PERSON>.\"><PERSON><PERSON>.</a>, American soldier and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON> <PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1927", "text": "<PERSON>, Brazilian actress", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American painter and sculptor (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Robert_Indiana\" title=\"Robert Indiana\"><PERSON></a>, American painter and sculptor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_Indiana\" title=\"Robert Indiana\"><PERSON></a>, American painter and sculptor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_Indiana"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Greek soldier and politician, 175th Prime Minister of Greece (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek soldier and politician, 175th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek soldier and politician, 175th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1930", "text": "<PERSON>, Baron <PERSON>, English publisher and philanthropist (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English publisher and philanthropist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English publisher and philanthropist (d. 2015)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian tennis player and sportscaster", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9dar<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Canadian tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9dar<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, Canadian tennis player and sportscaster", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/Robert_B%C3%A9dard_(tennis)"}]}, {"year": "1931", "text": "<PERSON>-<PERSON>, Australian sprinter and politician, 33rd Governor of South Australia", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter and politician, 33rd <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a>", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Estonian cinematographer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian cinematographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1932", "text": "<PERSON>, Spanish-Colombian journalist and actor (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Pacheco\" title=\"<PERSON>\"><PERSON></a>, Spanish-Colombian journalist and actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Pacheco\" title=\"<PERSON>\"><PERSON></a>, Spanish-Colombian journalist and actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%A1lez_Pacheco"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Czech actor (d. 2012)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ohat%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>at%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech actor (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brzobohat%C3%BD"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Swedish pianist and composer (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish pianist and composer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish pianist and composer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ben<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actress", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Australian businessman and activist (d. 1977)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anti-drugs_campaigner)\" title=\"<PERSON> (anti-drugs campaigner)\"><PERSON></a>, Australian businessman and activist (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(anti-drugs_campaigner)\" title=\"<PERSON> (anti-drugs campaigner)\"><PERSON></a>, Australian businessman and activist (d. 1977)", "links": [{"title": "<PERSON> (anti-drugs campaigner)", "link": "https://wikipedia.org/wiki/<PERSON>_(anti-drugs_campaigner)"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, American bass player (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American bass player (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American bass player (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English tennis player and coach", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Italian activist, founded National Vanguard (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist, founded <a href=\"https://wikipedia.org/wiki/National_Vanguard_(Italy)\" title=\"National Vanguard (Italy)\">National Vanguard</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist, founded <a href=\"https://wikipedia.org/wiki/National_Vanguard_(Italy)\" title=\"National Vanguard (Italy)\">National Vanguard</a> (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "National Vanguard (Italy)", "link": "https://wikipedia.org/wiki/National_Vanguard_(Italy)"}]}, {"year": "1937", "text": "<PERSON>, American animator, director, and producer, co-founded Sullivan Bluth Studios and Fox Animation Studios", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/Sullivan_<PERSON>th_Studios\" class=\"mw-redirect\" title=\"Sullivan Bluth Studios\">Sullivan Bluth Studios</a> and <a href=\"https://wikipedia.org/wiki/Fox_Animation_Studios\" title=\"Fox Animation Studios\">Fox Animation Studios</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Studios\" class=\"mw-redirect\" title=\"Sullivan Bluth Studios\">Sullivan Bluth Studios</a> and <a href=\"https://wikipedia.org/wiki/Fox_Animation_Studios\" title=\"Fox Animation Studios\">Fox Animation Studios</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sullivan <PERSON>th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Studios"}, {"title": "Fox Animation Studios", "link": "https://wikipedia.org/wiki/Fox_Animation_Studios"}]}, {"year": "1938", "text": "<PERSON>, Scottish lawyer and politician, Shadow Chancellor of the Exchequer (d. 1994)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)\" title=\"<PERSON> (Labour Party leader)\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer\" title=\"Shadow Chancellor of the Exchequer\">Shadow Chancellor of the Exchequer</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)\" title=\"<PERSON> (Labour Party leader)\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer\" title=\"Shadow Chancellor of the Exchequer\">Shadow Chancellor of the Exchequer</a> (d. 1994)", "links": [{"title": "<PERSON> (Labour Party leader)", "link": "https://wikipedia.org/wiki/<PERSON>_(Labour_Party_leader)"}, {"title": "Shadow Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Shadow_Chancellor_of_the_Exchequer"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American soprano and educator (d. 1993)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American soprano and educator (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1939", "text": "<PERSON>, American actor and voice artist (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and voice artist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Latvian economist and politician, 5th President of Latvia", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian economist and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian economist and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American photographer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American photographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Costa Rican politician, President of Costa Rica, Nobel Prize laureate", "html": "1940 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Arias\" title=\"Óscar Arias\"><PERSON><PERSON><PERSON></a>, Costa Rican politician, <a href=\"https://wikipedia.org/wiki/President_of_Costa_Rica\" title=\"President of Costa Rica\">President of Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Arias\" title=\"Óscar Arias\"><PERSON><PERSON><PERSON></a>, Costa Rican politician, <a href=\"https://wikipedia.org/wiki/President_of_Costa_Rica\" title=\"President of Costa Rica\">President of Costa Rica</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON> Aria<PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_Arias"}, {"title": "President of Costa Rica", "link": "https://wikipedia.org/wiki/President_of_Costa_Rica"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1940", "text": "<PERSON>, English cricketer (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian businessman", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Japanese architect and academic, designed Piccadilly Gardens", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese architect and academic, designed <a href=\"https://wikipedia.org/wiki/Piccadilly_Gardens\" title=\"Piccadilly Gardens\">Piccadilly Gardens</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese architect and academic, designed <a href=\"https://wikipedia.org/wiki/Piccadilly_Gardens\" title=\"Piccadilly Gardens\">Piccadilly Gardens</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Piccadilly Gardens", "link": "https://wikipedia.org/wiki/Piccadilly_Gardens"}]}, {"year": "1941", "text": "<PERSON>, English-Canadian singer-songwriter and guitarist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Turkish judge and politician, 10th President of the Republic of Turkey", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ah<PERSON>_Necdet_<PERSON>zer\" title=\"<PERSON>met Necdet Sezer\"><PERSON><PERSON></a>, Turkish judge and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Turkey\" class=\"mw-redirect\" title=\"President of the Republic of Turkey\">President of the Republic of Turkey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ah<PERSON>_Necdet_Sezer\" title=\"<PERSON>met Necdet Sezer\"><PERSON><PERSON></a>, Turkish judge and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Turkey\" class=\"mw-redirect\" title=\"President of the Republic of Turkey\">President of the Republic of Turkey</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_Necdet_Sezer"}, {"title": "President of the Republic of Turkey", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Turkey"}]}, {"year": "1942", "text": "<PERSON>, Canadian businessman and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4t%C3%A9_(Canadian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B4t%C3%A9_(Canadian_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Canadian politician)\"><PERSON></a>, Canadian businessman and politician", "links": [{"title": "<PERSON> (Canadian politician)", "link": "https://wikipedia.org/wiki/Michel_C%C3%B4t%C3%A9_(Canadian_politician)"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English journalist (d. 2008)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actress and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter, bass player, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, bass player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Australian surfer (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Midget_<PERSON><PERSON>\" title=\"Midget Farrelly\"><PERSON><PERSON> <PERSON></a>, Australian surfer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Midget_<PERSON><PERSON>\" title=\"Midget Farrelly\"><PERSON><PERSON> <PERSON><PERSON></a>, Australian surfer (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Midget_<PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Belgian actor, director, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/No%C3%AB<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_<PERSON>in"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Swedish journalist and politician (d. 2002)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Andre<PERSON>_<PERSON>%C3%BCng\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andre<PERSON>_<PERSON>%C3%BCng\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist and politician (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andres_K%C3%BCng"}]}, {"year": "1946", "text": "<PERSON>, American director and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)"}]}, {"year": "1946", "text": "<PERSON>, Georgian swimmer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress and singer (d. 2003)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Greek physicist and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Fijian general and politician, 3rd Prime Minister of Fiji", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rabuka\" title=\"<PERSON><PERSON><PERSON> Rabuka\"><PERSON><PERSON><PERSON></a>, Fijian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rabuka\" title=\"<PERSON>iveni Rabuka\"><PERSON><PERSON><PERSON></a>, Fijian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sitiveni_Rabuka"}, {"title": "Prime Minister of Fiji", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Fiji"}]}, {"year": "1949", "text": "<PERSON>, American basketball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish lawyer and politician, 8th Prime Minister of Poland", "html": "1950 - <a href=\"https://wikipedia.org/wiki/W%C5%82od<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Wł<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82od<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English footballer and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Pat_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat_<PERSON>\" title=\"Pat <PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_Holland"}]}, {"year": "1950", "text": "<PERSON>, American mountaineer (d. 2018)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Irish author, playwright, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Irish author, playwright, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Irish author, playwright, and screenwriter", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)"}]}, {"year": "1951", "text": "<PERSON><PERSON>, South Sudanese politician, 1st President of South Sudan", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Sudanese politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of Presidents of South Sudan\">President of South Sudan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Sudanese politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan\" class=\"mw-redirect\" title=\"List of Presidents of South Sudan\">President of South Sudan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>va_Ki<PERSON>_<PERSON>t"}, {"title": "List of Presidents of South Sudan", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_South_Sudan"}]}, {"year": "1951", "text": "<PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Smart\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American pop and disco singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop and disco singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American pop and disco singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1952", "text": "<PERSON>, American bass player and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Was\"><PERSON></a>, American bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Was\"><PERSON></a>, American bass player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English-Australian singer-songwriter and bass player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON><PERSON>.</a>, American actor", "links": [{"title": "<PERSON><PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1955", "text": "<PERSON>, 4th Baron <PERSON>, English rower and politician, Minister for Sport and the Olympics", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English rower and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English rower and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1955", "text": "<PERSON>, American guitarist and composer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American guitarist and composer", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)"}]}, {"year": "1956", "text": "<PERSON>, French-Monégasque chef", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Monégasque chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Monégasque chef", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian-New Zealand photographer and fashion designer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand photographer and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand photographer and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Irish Republican, hunger striker (d. 1981)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a>, hunger striker (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a>, hunger striker (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer and songwriter (d. 2017)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and songwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and songwriter (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ledge"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American rock drummer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Vinny_Appice\" title=\"Vinny Appice\"><PERSON><PERSON></a>, American rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vinny_Appice\" title=\"Vinny Appice\"><PERSON><PERSON></a>, American rock drummer", "links": [{"title": "<PERSON>ny A<PERSON>", "link": "https://wikipedia.org/wiki/Vinny_Appice"}]}, {"year": "1957", "text": "<PERSON>, American ice dancer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice dancer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Irish footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Mal_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mal_Don<PERSON>y\" title=\"<PERSON> Don<PERSON>y\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mal_<PERSON>y"}]}, {"year": "1957", "text": "<PERSON>, English-American philosopher and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English lawyer and judge", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)\" title=\"<PERSON> (judge)\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON> (judge)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(judge)"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American neurosurgeon and academic", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, American neurosurgeon and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, American neurosurgeon and academic", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, 17th President of the Philippines", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 17th President of the Philippines", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, 17th President of the Philippines", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>gbon<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English comedian and actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Polish conductor and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Przytocki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish conductor and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Przytocki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish conductor and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_P<PERSON><PERSON><PERSON>i"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter and actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dji_Tamaki"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Russian journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, South African photojournalist (d. 1994)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African photojournalist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African photojournalist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American artist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Japanese singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ull\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist", "links": [{"title": "KK <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ull"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Estonian author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nu_%C3%95nnepalu\" title=\"Tõnu Õnnepalu\">Tõnu Õnnepalu</a>, Estonian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nu_%C3%95nnepalu\" title=\"Tõnu Õnnepalu\">Tõnu Õnnepalu</a>, Estonian author", "links": [{"title": "Tõnu Õnnepalu", "link": "https://wikipedia.org/wiki/T%C3%B5nu_%C3%95nnepalu"}]}, {"year": "1963", "text": "<PERSON>, Russian boxer (d. 2013)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Russian boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, Russian boxer (d. 2013)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)"}]}, {"year": "1963", "text": "<PERSON>, English pharmacologist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Greek journalist and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, South African-English cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African-English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American talk show host, journalist, and author", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American talk show host, journalist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Smile<PERSON>\"><PERSON><PERSON></a>, American talk show host, journalist, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Smiley"}]}, {"year": "1965", "text": "<PERSON>, American poker player and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American comedian, director, and author", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, director, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, director, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, English drummer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, German physician and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>w%C3%A4ngler\" title=\"<PERSON>ngler\"><PERSON></a>, German physician and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>w%C3%A4ngler\" title=\"<PERSON>ngle<PERSON>\"><PERSON></a>, German physician and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Furtw%C3%A4ngler"}]}, {"year": "1966", "text": "<PERSON>, Australian rugby league player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian ice hockey player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian actor", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American former sprinter and journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, American former sprinter and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(sprinter)\" title=\"<PERSON> (sprinter)\"><PERSON></a>, American former sprinter and journalist", "links": [{"title": "<PERSON> (sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>(sprinter)"}]}, {"year": "1967", "text": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Tim_%22Rip<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON>\" <PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tim_%22Rip<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Tim_%22Ripper%22_Owens"}]}, {"year": "1967", "text": "<PERSON>, American drummer and songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American football player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1968", "text": "<PERSON>, Puerto Rican-American baseball player and guitarist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Uruguayan footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian cricketer, coach, and sportscaster (d. 2022)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, coach, and sportscaster (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American bass player and composer (d. 2016)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and composer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and composer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Argentinian footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Mart%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%AD<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Herrera"}]}, {"year": "1970", "text": "<PERSON>, English actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player (d. 1992)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1992)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Croatian tennis player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1evi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%A1evi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Goran_Ivani%C5%A1evi%C4%87"}]}, {"year": "1971", "text": "<PERSON>, English fashion designer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> McCartney\"><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> McCartney\"><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese pianist and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Manabu_Namiki\" title=\"Manabu Namiki\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manab<PERSON>_Namiki\" title=\"Manabu Namiki\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manabu_Namiki"}]}, {"year": "1973", "text": "<PERSON>, French runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer and photographer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American basketball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON><PERSON> (Canadian football)\"><PERSON><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_(Canadian_football)\" title=\"<PERSON><PERSON> (Canadian football)\"><PERSON><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON><PERSON> (Canadian football)", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_(Canadian_football)"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ivet"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Israeli footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Idan_<PERSON>l\" title=\"Idan <PERSON>l\"><PERSON><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Idan_<PERSON>l\" title=\"Idan <PERSON>l\"><PERSON><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idan_<PERSON>l"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>na"}]}, {"year": "1976", "text": "<PERSON>, New Zealand cricketer, coach, and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Latvian boxer, trainer, and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian boxer, trainer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Latvian boxer, trainer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Th%C3%A9odore\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Th%C3%A9odore\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Th%C3%A9odore"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Swedish pornographic actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Puma_Swede\" title=\"Puma Swede\"><PERSON><PERSON></a>, Swedish pornographic actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Puma_Swede\" title=\"Puma Swede\"><PERSON><PERSON></a>, Swedish pornographic actress", "links": [{"title": "Puma Swede", "link": "https://wikipedia.org/wiki/Puma_Swede"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter, producer, and pianist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fiona Apple\"><PERSON></a>, American singer-songwriter, producer, and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fiona Apple\"><PERSON></a>, American singer-songwriter, producer, and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fiona_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Maltese actor, singer, director, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese actor, singer, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese actor, singer, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese singer-songwriter and drummer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese singer-songwriter and drummer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Swizz_Beatz\" title=\"Swizz Beatz\"><PERSON>wizz <PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swizz_Beatz\" title=\"Swizz Beatz\"><PERSON>wizz <PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "Swizz Beatz", "link": "https://wikipedia.org/wiki/Swizz_<PERSON>z"}]}, {"year": "1978", "text": "<PERSON>, Swedish businessman", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Japanese wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Belgian singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1979)\" title=\"<PERSON> (footballer, born 1979)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1979)"}]}, {"year": "1980", "text": "<PERSON>, German footballer (d. 2014)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>-young, South Korean actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-young\"><PERSON>-<PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>-young", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-young"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>gel<PERSON> Nastos\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Nastos\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Vire<PERSON>_<PERSON>\" title=\"<PERSON>ire<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vire<PERSON>_<PERSON>\" title=\"<PERSON>ire<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viren_Rasquinha"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Spanish cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rn%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Koldo_Fern%C3%A1ndez"}]}, {"year": "1981", "text": "<PERSON>, Canadian-American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Love\" title=\"<PERSON> Love\"><PERSON></a>, Canadian-American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Love\" title=\"<PERSON> Love\"><PERSON></a>, Canadian-American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Nen%C3%AA\" title=\"Nenê\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nen%C3%AA\" title=\"Nenê\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nen%C3%AA"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American guitarist, bassist, and producer/engineer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, bassist, and producer/engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, bassist, and producer/engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Slovenian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American illustrator and journalist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ple\"><PERSON></a>, American illustrator and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Estonian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American director, producer, and screenwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-Harb\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-Harb\"><PERSON><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON><PERSON><PERSON>-<PERSON>rb"}]}, {"year": "1984", "text": "<PERSON>, American wrestler", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baron <PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baron <PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English singer-songwriter", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1985", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Belgian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Edenilson_Bergonsi\" title=\"Edenilson Bergonsi\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edenilson_Bergonsi\" title=\"Edenilson Bergonsi\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>onsi"}]}, {"year": "1987", "text": "<PERSON>, Canadian-Dutch footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Canadian-Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Canadian-Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1n"}]}, {"year": "1987", "text": "<PERSON>, Irish rugby player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Panamanian footballer (d. 2014)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Panamanian footballer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Panamanian footballer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Renter%C3%ADa"}]}, {"year": "1988", "text": "<PERSON>, Irish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Elys%C3%A9e_Iri%C3%A9_Bi_S%C3%A9hi\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ée I<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elys%C3%A9e_Iri%C3%A9_Bi_S%C3%A9hi\" class=\"mw-redirect\" title=\"Elysée I<PERSON>\"><PERSON><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elys%C3%A9e_Iri%C3%A9_Bi_S%C3%A9hi"}]}, {"year": "1989", "text": "<PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1989", "text": "<PERSON>, Australian rugby league player (d. 2013)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_M%C3%BCller"}]}, {"year": "1989", "text": "<PERSON>, Ghanaian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1989)\" title=\"<PERSON> (footballer, born 1989)\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1989)\" title=\"<PERSON> (footballer, born 1989)\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON> (footballer, born 1989)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1989)"}]}, {"year": "1990", "text": "<PERSON>, Canadian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>oi Nakabeppu\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>pp<PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON> Naka<PERSON>pp<PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_Na<PERSON>ppu"}]}, {"year": "1990", "text": "<PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Russian gymnast", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>a"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Irish singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Irish-Canadian singer and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Canadian singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Portuguese singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Slovak tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%ADna_Schmiedlov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%ADna_Schmiedlov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%AD<PERSON>_Schm<PERSON>lov%C3%A1"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Cameron_<PERSON>\" title=\"Cameron Munster\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cameron_<PERSON>\" title=\"Cameron Munster\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cameron_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American professional cyclist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Sepp_<PERSON>\" title=\"Sepp Kuss\"><PERSON><PERSON></a>, American professional cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sepp_<PERSON>\" title=\"Sepp Kuss\"><PERSON><PERSON></a>, American professional cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ss"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1995)\" title=\"<PERSON><PERSON> (footballer, born 1995)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1995)\" title=\"<PERSON><PERSON> (footballer, born 1995)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1995)"}]}, {"year": "1995", "text": "<PERSON>, Swedish handball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Swedish ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}], "Deaths": [{"year": "81", "text": "<PERSON>, Roman emperor (b. AD 39)", "html": "81 - <a href=\"https://wikipedia.org/wiki/Titus\" title=\"Titus\"><PERSON></a>, Roman emperor (b. AD 39)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Titus\"><PERSON></a>, Roman emperor (b. AD 39)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}]}, {"year": "413", "text": "<PERSON><PERSON><PERSON> of Carthage, martyr and saint", "html": "413 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>us_of_Carthage\" title=\"<PERSON><PERSON><PERSON> of Carthage\"><PERSON><PERSON><PERSON> of Carthage</a>, <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a> and <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>us_of_Carthage\" title=\"<PERSON><PERSON><PERSON> of Carthage\"><PERSON><PERSON><PERSON> of Carthage</a>, <a href=\"https://wikipedia.org/wiki/Martyr\" title=\"Martyr\">martyr</a> and <a href=\"https://wikipedia.org/wiki/Saint\" title=\"Saint\">saint</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Carthage", "link": "https://wikipedia.org/wiki/Marcellinus_of_Carthage"}, {"title": "Martyr", "link": "https://wikipedia.org/wiki/Martyr"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint"}]}, {"year": "531", "text": "<PERSON><PERSON><PERSON>, Sasanian King of Kings of Iran (b. 473)", "html": "531 - <a href=\"https://wikipedia.org/wiki/Kavad_I\" title=\"Kavad I\"><PERSON>vad I</a>, <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian</a> <a href=\"https://wikipedia.org/wiki/King_of_Kings\" title=\"King of Kings\">King of Kings</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> (b. 473)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kavad_I\" title=\"Kavad I\"><PERSON>vad I</a>, <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian</a> <a href=\"https://wikipedia.org/wiki/King_of_Kings\" title=\"King of Kings\">King of Kings</a> of <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a> (b. 473)", "links": [{"title": "Kavad I", "link": "https://wikipedia.org/wiki/<PERSON>va<PERSON>_I"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "King of Kings", "link": "https://wikipedia.org/wiki/King_of_Kings"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}]}, {"year": "864", "text": "<PERSON>, doge of Venice", "html": "864 - <a href=\"https://wikipedia.org/wiki/Pietro_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, doge of <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Venice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pietro_<PERSON>rado<PERSON>o"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "908", "text": "<PERSON><PERSON><PERSON> mac <PERSON>, king of Munster (Ireland)", "html": "908 - <a href=\"https://wikipedia.org/wiki/Cormac_mac_Cuilenn%C3%A1in\" title=\"<PERSON><PERSON><PERSON> mac Cuilennáin\"><PERSON><PERSON><PERSON> mac Cuilennáin</a>, king of <a href=\"https://wikipedia.org/wiki/Munster\" title=\"Munster\">Munster</a> (Ireland)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cormac_mac_Cuilenn%C3%A1in\" title=\"<PERSON><PERSON><PERSON> mac Cuilennáin\"><PERSON><PERSON><PERSON> mac Cuilennáin</a>, king of <a href=\"https://wikipedia.org/wiki/Munster\" title=\"Munster\">Munster</a> (Ireland)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Cormac_mac_Cuilenn%C3%A1in"}, {"title": "Munster", "link": "https://wikipedia.org/wiki/Munster"}]}, {"year": "1171", "text": "<PERSON><PERSON><PERSON><PERSON>, last <PERSON><PERSON>id caliph (b. 1151)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/Al-<PERSON>\" title=\"Al-Adid\"><PERSON><PERSON><PERSON><PERSON></a>, last <PERSON><PERSON>id caliph (b. 1151)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Adid\" title=\"Al-Adid\"><PERSON><PERSON><PERSON><PERSON></a>, last Fatimid caliph (b. 1151)", "links": [{"title": "Al-Adid", "link": "https://wikipedia.org/wiki/Al-Adid"}]}, {"year": "1313", "text": "<PERSON><PERSON><PERSON>, Austrian saint (b. 1265)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/Notburga\" title=\"<PERSON>burg<PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian saint (b. 1265)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Notburga\" title=\"<PERSON>burg<PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian saint (b. 1265)", "links": [{"title": "Notburga", "link": "https://wikipedia.org/wiki/Notburga"}]}, {"year": "1409", "text": "<PERSON> of Valois, French princess and queen of England (b. 1389)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"Isabella of Valois\">Isabella of Valois</a>, French princess and queen of England (b. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Valois\" title=\"Isabella of Valois\">Isabella of Valois</a>, French princess and queen of England (b. 1389)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois"}]}, {"year": "1488", "text": "<PERSON>, Duke of Bourbon (b. 1434)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1434)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1434)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1506", "text": "<PERSON>, Italian painter and engraver (b. 1431)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (b. 1431)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and engraver (b. 1431)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1557", "text": "<PERSON>, English scholar and politician, Secretary of State for England (b. 1514)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">Secretary of State for England</a> (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">Secretary of State for England</a> (b. 1514)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1592", "text": "<PERSON>, French philosopher and author (b. 1533)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (b. 1533)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1598", "text": "<PERSON> of Spain (b. 1526)", "html": "1598 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> II of Spain\"><PERSON> of Spain</a> (b. 1526)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1612", "text": "<PERSON>, Queen of Sweden (b. 1550)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5nsdotter\" title=\"<PERSON>\"><PERSON></a>, Queen of Sweden (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5nsdotter\" title=\"<PERSON>\"><PERSON></a>, Queen of Sweden (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%A5nsdotter"}]}, {"year": "1632", "text": "<PERSON>, Archduke of Austria (b. 1586)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (b. 1586)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (b. 1586)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria"}]}, {"year": "1759", "text": "<PERSON>, English general (b. 1727)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "<PERSON>, English scholar and author (b. 1704)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and author (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and author (b. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, French-English general and explorer (b. 1735)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English general and explorer (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English general and explorer (b. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, English soldier and politician, Secretary of State for Foreign and Commonwealth Affairs (b. 1749)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a> (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Italian poet, playwright, and critic (b. 1718)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, playwright, and critic (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, playwright, and critic (b. 1718)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian emperor", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Hezqeyas\" title=\"Hezqeyas\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hezq<PERSON>as\" title=\"Hezqeyas\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian emperor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>as"}]}, {"year": "1847", "text": "<PERSON>, French general (b. 1767)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Turkish journalist, author, and translator (b. 1826)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/%C4%B0brahim_%C5%9Einasi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and translator (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0brahim_%C5%9Einasi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and translator (b. 1826)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0brahim_%C5%9Einasi"}]}, {"year": "1872", "text": "<PERSON>, German anthropologist and philosopher (b. 1804)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and philosopher (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German anthropologist and philosopher (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American general and politician, 30th Governor of Rhode Island (b. 1824)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Governor_of_Rhode_Island\" title=\"Governor of Rhode Island\">Governor of Rhode Island</a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Rhode Island", "link": "https://wikipedia.org/wiki/Governor_of_Rhode_Island"}]}, {"year": "1885", "text": "<PERSON>, German composer and educator (b. 1821)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Friedrich_<PERSON>\" title=\"Friedrich <PERSON>\"><PERSON></a>, German composer and educator (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Friedrich_<PERSON>\" title=\"Friedrich Kiel\"><PERSON></a>, German composer and educator (b. 1821)", "links": [{"title": "Friedrich Kiel", "link": "https://wikipedia.org/wiki/Friedrich_Kiel"}]}, {"year": "1894", "text": "<PERSON>, French pianist and composer (b. 1841)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist and composer (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, French lawyer and politician, 52nd Prime Minister of France (b. 1828)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goblet\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goblet\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Goblet"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi poet and composer (b. 1865)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and composer (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi poet and composer (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Sen"}]}, {"year": "1912", "text": "<PERSON>, Australian author and poet (b. 1843)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and poet (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Japanese general (b. 1849)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general (b. 1849)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Romanian pilot and engineer (b. 1882)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian pilot and engineer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian pilot and engineer (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American general and politician, 44th Governor of Ohio (b. 1835)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1918", "text": "<PERSON>, American artist and author (b. 1845)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist and author (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>inshield"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Italian author and playwright (b. 1861)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Italo_Svevo\" title=\"Italo Svevo\">Italo <PERSON></a>, Italian author and playwright (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italo_Svevo\" title=\"Italo Svevo\">Italo <PERSON></a>, Italian author and playwright (b. 1861)", "links": [{"title": "Italo Svevo", "link": "https://wikipedia.org/wiki/Italo_Svevo"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian activist (b. 1904)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Danish model and painter (b. 1882)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish model and painter (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish model and painter (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Scottish rugby player and golfer (b. 1869)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish rugby player and golfer (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" class=\"mw-redirect\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish rugby player and golfer (b. 1869)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1941", "text": "<PERSON>, Canadian-American farmer and businessman (b. 1859)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American farmer and businessman (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American farmer and businessman (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, English cartoonist (b. 1872)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English cartoonist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English cartoonist (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Austrian captain and Nazi war criminal (b. 1908)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Amon_G%C3%B6th\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian captain and Nazi war criminal (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amon_G%C3%B6th\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian captain and Nazi war criminal (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amon_G%C3%B6th"}]}, {"year": "1946", "text": "<PERSON>, Russian painter, sculptor, and illustrator (b. 1875)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter, sculptor, and illustrator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter, sculptor, and illustrator (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian lawyer and politician, 24th Premier of Victoria (b. 1871)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1871)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>(Australian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1949", "text": "<PERSON>, Danish physiologist and academic, Nobel Prize laureate (b. 1874)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Danish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Danish physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1874)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_K<PERSON>h"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1953", "text": "<PERSON>, American painter (b. 1868)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Hungarian composer and educator (b. 1885)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Le%C3%B3_Weiner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian composer and educator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le%C3%B3_Weiner\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian composer and educator (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le%C3%B3_<PERSON>ner"}]}, {"year": "1967", "text": "<PERSON>, Yemeni-Saudi Arabian businessman, founded Saudi Binladin Group (b. 1903)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_bin_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> bin <PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Yemeni-Saudi Arabian businessman, founded <a href=\"https://wikipedia.org/wiki/Saudi_Binladin_Group\" title=\"Saudi Binladin Group\">Saudi Binladin Group</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> bin <PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Yemeni-Saudi Arabian businessman, founded <a href=\"https://wikipedia.org/wiki/Saudi_Binladin_Group\" title=\"Saudi Binladin Group\">Saudi Binladin Group</a> (b. 1903)", "links": [{"title": "<PERSON> bin <PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_bin_<PERSON>"}, {"title": "Saudi Binladin Group", "link": "https://wikipedia.org/wiki/Saudi_Binladin_Group"}]}, {"year": "1967", "text": "<PERSON>, English air marshal and politician, 24th Governor of South Australia (b. 1896)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal and politician, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Australia\" title=\"Governor of South Australia\">Governor of South Australia</a> (b. 1896)", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)"}, {"title": "Governor of South Australia", "link": "https://wikipedia.org/wiki/Governor_of_South_Australia"}]}, {"year": "1967", "text": "<PERSON>, English businessman (b. 1896)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Chinese general and politician, 2nd Vice Premier of the People's Republic of China (b. 1907)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lin_<PERSON>o"}, {"title": "List of vice premiers of the People's Republic of China", "link": "https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China"}]}, {"year": "1973", "text": "<PERSON>, American actress (b. 1913)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Field\" title=\"Betty Field\"><PERSON></a>, American actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Field\" title=\"Betty Field\"><PERSON></a>, American actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and philosopher (b. 1905)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and philosopher (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and philosopher (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian singer and musicologist (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Mudicondan_Venkatarama_Iyer\" title=\"Mudicondan Venkatarama Iyer\"><PERSON>dicondan Venkatarama <PERSON></a>, Indian singer and musicologist (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mudicondan_Venkatarama_Iyer\" title=\"Mudicondan Venkatarama Iyer\"><PERSON>dicondan Venkata<PERSON></a>, Indian singer and musicologist (b. 1897)", "links": [{"title": "Mudicondan Venkatarama Iyer", "link": "https://wikipedia.org/wiki/Mudicondan_Venkatarama_Iyer"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player (b. 1905)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian priest, historian, and director (b. 1895)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest, historian, and director (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian priest, historian, and director (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English conductor (b. 1882)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American publisher (b. 1905)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American illustrator (b. 1917)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> C<PERSON>all\"><PERSON></a>, American illustrator (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Reed_C<PERSON>all"}]}, {"year": "1985", "text": "<PERSON>, French-American astrologer, composer, and author (b. 1895)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American astrologer, composer, and author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American astrologer, composer, and author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American actor, director, and producer (b. 1900)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor, director, and producer (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English soldier and conductor (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English soldier and conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English soldier and conductor (b. 1913)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>(conductor)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Turkish footballer and manager (b. 1936)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish footballer and manager (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>y"}]}, {"year": "1991", "text": "<PERSON>, Hungarian-American production manager and producer (b. 1901)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American production manager and producer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American production manager and producer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American ice hockey player and referee (b. 1907)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and referee (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and referee (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American rapper, producer, and actor (b. 1971)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur\"><PERSON><PERSON><PERSON></a>, American rapper, producer, and actor (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, producer, and actor (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ur"}]}, {"year": "1997", "text": "<PERSON>, Egyptian-French actor, singer, and dancer (b. 1915)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tary\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French actor, singer, and dancer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9tary\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French actor, singer, and dancer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georges_Gu%C3%A9tary"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Greek footballer (b. 1962)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Mitsibonas"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, Turkish civil servant and politician (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Necdet_Calp\" title=\"Necdet Calp\"><PERSON><PERSON><PERSON></a>, Turkish civil servant and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necdet_Calp\" title=\"Necdet Calp\"><PERSON><PERSON><PERSON></a>, Turkish civil servant and politician (b. 1922)", "links": [{"title": "Necdet <PERSON>", "link": "https://wikipedia.org/wiki/Necdet_Calp"}]}, {"year": "1998", "text": "<PERSON>, Canadian ice hockey player (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (b. 1926)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1998", "text": "<PERSON>, New Zealand businessman (b. 1918)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American sergeant, lawyer, and politician, 45th Governor of Alabama (b. 1919)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, lawyer, and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, lawyer, and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "1999", "text": "<PERSON>, American psychologist and academic (b. 1913)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian nurse and author (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian nurse and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian nurse and author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American sailor and illustrator (b. 1926)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Czech-English ice hockey player and tennis player (b. 1921)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English ice hockey player and tennis player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English ice hockey player and tennis player (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n%C3%BD"}]}, {"year": "2001", "text": "<PERSON>, American actress (b. 1916)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian soldier, historian, and author, designed the Flag of Canada (b. 1907)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, historian, and author, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">Flag of Canada</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, historian, and author, designed the <a href=\"https://wikipedia.org/wiki/Flag_of_Canada\" title=\"Flag of Canada\">Flag of Canada</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Flag of Canada", "link": "https://wikipedia.org/wiki/Flag_of_Canada"}]}, {"year": "2003", "text": "<PERSON>, American publisher, lawyer, and politician, 47th Governor of Indiana (b. 1930)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Bannon\" title=\"<PERSON>\"><PERSON></a>, American publisher, lawyer, and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Bannon\" title=\"<PERSON>\"><PERSON></a>, American publisher, lawyer, and politician, 47th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_O%27Bannon"}, {"title": "Governor of Indiana", "link": "https://wikipedia.org/wiki/Governor_of_Indiana"}]}, {"year": "2004", "text": "<PERSON>, Mexican chemist, co-invented the birth-control pill (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist, co-invented the <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">birth-control pill</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican chemist, co-invented the <a href=\"https://wikipedia.org/wiki/Combined_oral_contraceptive_pill\" title=\"Combined oral contraceptive pill\">birth-control pill</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_<PERSON>._Miramontes"}, {"title": "Combined oral contraceptive pill", "link": "https://wikipedia.org/wiki/Combined_oral_contraceptive_pill"}]}, {"year": "2004", "text": "<PERSON>, American serial killer (b. 1957)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Austrian footballer (b. 1945)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Colombian lawyer and politician, 25th President of Colombia (b. 1916)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9sar_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julio_<PERSON>%C3%A9<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julio_C%C3%A9<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "2006", "text": "<PERSON>, American educator and politician, 45th Governor of Texas (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, New Zealand archbishop (b. 1928)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Whakahuihui_Vercoe\" title=\"Whakahuihui Vercoe\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, New Zealand archbishop (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whakahuihui_Vercoe\" title=\"Whakahuihui Vercoe\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, New Zealand archbishop (b. 1928)", "links": [{"title": "Whakahu<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Whakahuih<PERSON>_Vercoe"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1926)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2011", "text": "<PERSON>, Italian mountaineer and journalist (b. 1930)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer and journalist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American composer and author (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and author (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American composer and author (b. 1943)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "2012", "text": "<PERSON>, Canadian football player, lawyer, and politician, 10th Premier of Alberta (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player, lawyer, and politician, 10th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "2012", "text": "<PERSON>, English-Australian actor and director (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor and director (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor and director (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and jurist, 21st Chief Justice of India (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and jurist, 21st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and jurist, 21st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_India\" title=\"Chief Justice of India\">Chief Justice of India</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chief Justice of India", "link": "https://wikipedia.org/wiki/Chief_Justice_of_India"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian politician, 15th Governor of Ondo State (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gagu\" title=\"<PERSON><PERSON><PERSON><PERSON> Agagu\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Ondo_State\" class=\"mw-redirect\" title=\"Governor of Ondo State\">Governor of Ondo State</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>gagu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Ondo_State\" class=\"mw-redirect\" title=\"Governor of Ondo State\">Governor of Ondo State</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>eg<PERSON>_Agagu"}, {"title": "Governor of Ondo State", "link": "https://wikipedia.org/wiki/Governor_of_Ondo_State"}]}, {"year": "2013", "text": "<PERSON>, American biologist and academic (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Brazilian trade union leader and politician (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian trade union leader and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian trade union leader and politician (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ken"}]}, {"year": "2014", "text": "<PERSON>, Nigerian general (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Serbian footballer (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Milan_Gali%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Gali%C4%87\" title=\"<PERSON> Galić\"><PERSON></a>, Serbian footballer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Gali%C4%87"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and manager (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer (b. 1961)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American baseball player (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2015", "text": "<PERSON>, English cricketer and coach (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and sportscaster (b. 1955)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, British historian (b. 1938)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British historian (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American politician, senator of New Mexico (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, senator of New Mexico (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, senator of New Mexico (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pete_Dome<PERSON>i"}]}, {"year": "2019", "text": "<PERSON>, American musician (b. 1949)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Money\" title=\"Eddie Money\"><PERSON></a>, American musician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eddie_Money\" title=\"Eddie Money\"><PERSON></a>, American musician (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, French-Swiss film director, screenwriter, and film critic (b. 1930)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss film director, screenwriter, and film critic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss film director, screenwriter, and film critic (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German politician (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, South African politician (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African politician (b. 1949)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian actor (b. 1949)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lex_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American fashion designer (b. 1938)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}