{"date": "August 15", "url": "https://wikipedia.org/wiki/August_15", "data": {"Events": [{"year": "636", "text": "Arab-Byzantine wars: The Battle of Yarmouk between the Byzantine Empire and the Rashidun Caliphate begins.", "html": "636 - <a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Yarmouk\" class=\"mw-redirect\" title=\"Battle of Yarmouk\">Battle of Yarmouk</a> between the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> and the <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\"><PERSON><PERSON></a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars\" title=\"Arab-Byzantine wars\">Arab-Byzantine wars</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Yarmouk\" class=\"mw-redirect\" title=\"Battle of Yarmouk\">Battle of Yarmouk</a> between the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a> and the <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\"><PERSON><PERSON> Calip<PERSON>e</a> begins.", "links": [{"title": "Arab-Byzantine wars", "link": "https://wikipedia.org/wiki/Arab%E2%80%93Byzantine_wars"}, {"title": "Battle of Yarmouk", "link": "https://wikipedia.org/wiki/Battle_of_Yarmouk"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rashidun_Caliphate"}]}, {"year": "717", "text": "Arab-Byzantine wars: <PERSON><PERSON><PERSON> ibn <PERSON> begins the Second Arab Siege of Constantinople, which will last for nearly a year.", "html": "717 - Arab-Byzantine wars: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople\" class=\"mw-redirect\" title=\"Second Arab Siege of Constantinople\">Second Arab Siege of Constantinople</a>, which will last for nearly a year.", "no_year_html": "Arab-Byzantine wars: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a> begins the <a href=\"https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople\" class=\"mw-redirect\" title=\"Second Arab Siege of Constantinople\">Second Arab Siege of Constantinople</a>, which will last for nearly a year.", "links": [{"title": "<PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Second Arab Siege of Constantinople", "link": "https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople"}]}, {"year": "718", "text": "Arab-Byzantine wars: Raising of the Second Arab Siege of Constantinople.", "html": "718 - Arab-Byzantine wars: Raising of the <a href=\"https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople\" class=\"mw-redirect\" title=\"Second Arab Siege of Constantinople\">Second Arab Siege of Constantinople</a>.", "no_year_html": "Arab-Byzantine wars: Raising of the <a href=\"https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople\" class=\"mw-redirect\" title=\"Second Arab Siege of Constantinople\">Second Arab Siege of Constantinople</a>.", "links": [{"title": "Second Arab Siege of Constantinople", "link": "https://wikipedia.org/wiki/Second_Arab_Siege_of_Constantinople"}]}, {"year": "747", "text": "<PERSON><PERSON>, mayor of the palace of Austrasia, renounces his position as majordomo and retires to a monastery near Rome. His brother, <PERSON><PERSON><PERSON> the <PERSON>, becomes the sole ruler (de facto) of the Frankish Kingdom.", "html": "747 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(mayor_of_the_palace)\" title=\"<PERSON><PERSON> (mayor of the palace)\"><PERSON><PERSON></a>, mayor of the palace of <a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasia</a>, renounces his position as <i><a href=\"https://wikipedia.org/wiki/Majordomo\" title=\"Majordomo\">majordomo</a></i> and retires to a <a href=\"https://wikipedia.org/wiki/Monastery\" title=\"Monastery\">monastery</a> near Rome. His brother, <a href=\"https://wikipedia.org/wiki/Pepin_the_Short\" title=\"<PERSON><PERSON><PERSON> the Short\"><PERSON><PERSON><PERSON> the Short</a>, becomes the sole ruler (<i><a href=\"https://wikipedia.org/wiki/De_facto\" title=\"De facto\">de facto</a></i>) of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(mayor_of_the_palace)\" title=\"<PERSON><PERSON> (mayor of the palace)\"><PERSON><PERSON></a>, mayor of the palace of <a href=\"https://wikipedia.org/wiki/Austrasia\" title=\"Austrasia\">Austrasia</a>, renounces his position as <i><a href=\"https://wikipedia.org/wiki/Majordomo\" title=\"Majordomo\">majordomo</a></i> and retires to a <a href=\"https://wikipedia.org/wiki/Monastery\" title=\"Monastery\">monastery</a> near Rome. His brother, <a href=\"https://wikipedia.org/wiki/Pepin_the_Short\" title=\"<PERSON><PERSON><PERSON> the Short\"><PERSON><PERSON><PERSON> the Short</a>, becomes the sole ruler (<i><a href=\"https://wikipedia.org/wiki/De_facto\" title=\"De facto\">de facto</a></i>) of the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish Kingdom</a>.", "links": [{"title": "<PERSON><PERSON> (mayor of the palace)", "link": "https://wikipedia.org/wiki/Carloman_(mayor_of_the_palace)"}, {"title": "Austrasia", "link": "https://wikipedia.org/wiki/Austrasia"}, {"title": "Majordomo", "link": "https://wikipedia.org/wiki/Majordomo"}, {"title": "Monastery", "link": "https://wikipedia.org/wiki/Monastery"}, {"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Short"}, {"title": "De facto", "link": "https://wikipedia.org/wiki/De_facto"}, {"title": "Francia", "link": "https://wikipedia.org/wiki/Francia"}]}, {"year": "778", "text": "The Battle of Roncevaux Pass takes place between the army of Charlemagne and a Basque army.", "html": "778 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Roncevaux_Pass\" title=\"Battle of Roncevaux Pass\">Battle of Roncevaux Pass</a> takes place between the army of <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\">Charlemagne</a> and a Basque army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Roncevaux_Pass\" title=\"Battle of Roncevaux Pass\">Battle of Roncevaux Pass</a> takes place between the army of <a href=\"https://wikipedia.org/wiki/Charlemagne\" title=\"Charlemagne\">Charlemagne</a> and a Basque army.", "links": [{"title": "Battle of Roncevaux Pass", "link": "https://wikipedia.org/wiki/Battle_of_Ronce<PERSON>ux_Pass"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}]}, {"year": "805", "text": "Noble <PERSON><PERSON><PERSON> of Dahauua grants the Bavarian town of Dachau to the Diocese of Freising", "html": "805 - <PERSON> of Dahauua grants the <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavarian</a> town of <a href=\"https://wikipedia.org/wiki/Dachau,_Bavaria\" title=\"Dachau, Bavaria\">Dachau</a> to the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising\" title=\"Roman Catholic Archdiocese of Munich and Freising\">Diocese of Freising</a>", "no_year_html": "<PERSON> of Dahauua grants the <a href=\"https://wikipedia.org/wiki/Bavaria\" title=\"Bavaria\">Bavarian</a> town of <a href=\"https://wikipedia.org/wiki/Dachau,_Bavaria\" title=\"Dachau, Bavaria\">Dachau</a> to the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising\" title=\"Roman Catholic Archdiocese of Munich and Freising\">Diocese of Freising</a>", "links": [{"title": "Bavaria", "link": "https://wikipedia.org/wiki/Bavaria"}, {"title": "Dachau, Bavaria", "link": "https://wikipedia.org/wiki/Dachau,_Bavaria"}, {"title": "Roman Catholic Archdiocese of Munich and Freising", "link": "https://wikipedia.org/wiki/Roman_Catholic_Archdiocese_of_Munich_and_Freising"}]}, {"year": "927", "text": "The Saracens conquer and destroy Taranto.", "html": "927 - The <a href=\"https://wikipedia.org/wiki/Saracen\" title=\"Saracen\"><PERSON><PERSON></a> conquer and destroy <a href=\"https://wikipedia.org/wiki/Taranto\" title=\"Taranto\">Taranto</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Saracen\" title=\"Saracen\"><PERSON><PERSON></a> conquer and destroy <a href=\"https://wikipedia.org/wiki/Taranto\" title=\"Taranto\">Taranto</a>.", "links": [{"title": "Saracen", "link": "https://wikipedia.org/wiki/Saracen"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taranto"}]}, {"year": "982", "text": "Holy Roman Emperor <PERSON> is defeated by the Saracens in the Battle of Capo Colonna, in Calabria.", "html": "982 - <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> is defeated by the <a href=\"https://wikipedia.org/wiki/Saracen\" title=\"Saracen\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Capo_Colonna\" class=\"mw-redirect\" title=\"Battle of Capo Colonna\">Battle of Capo Colonna</a>, in <a href=\"https://wikipedia.org/wiki/Calabria\" title=\"Calabria\">Calabria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> is defeated by the <a href=\"https://wikipedia.org/wiki/Saracen\" title=\"Saracen\"><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Capo_Colonna\" class=\"mw-redirect\" title=\"Battle of Capo Colonna\">Battle of Capo Colonna</a>, in <a href=\"https://wikipedia.org/wiki/Calabria\" title=\"Calabria\">Calabria</a>.", "links": [{"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Saracen", "link": "https://wikipedia.org/wiki/Saracen"}, {"title": "Battle of Capo Colonna", "link": "https://wikipedia.org/wiki/Battle_of_Capo_Colonna"}, {"title": "Calabria", "link": "https://wikipedia.org/wiki/Calabria"}]}, {"year": "1018", "text": "Byzantine general <PERSON><PERSON><PERSON><PERSON>es blinds and captures Ibatzes of Bulgaria by a ruse, thereby ending Bulgarian resistance against Emperor <PERSON>'s conquest of Bulgaria.", "html": "1018 - Byzantine general <a href=\"https://wikipedia.org/wiki/Eust<PERSON><PERSON>_Daphnomeles\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> blinds and captures <a href=\"https://wikipedia.org/wiki/Ivats\" title=\"Ivats\"><PERSON><PERSON><PERSON> of Bulgaria</a> by a ruse, thereby ending Bulgarian resistance against Emperor <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\">Basil II</a>'s <a href=\"https://wikipedia.org/wiki/Byzantine_conquest_of_Bulgaria\" title=\"Byzantine conquest of Bulgaria\">conquest of Bulgaria</a>.", "no_year_html": "Byzantine general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Da<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> blinds and captures <a href=\"https://wikipedia.org/wiki/Ivats\" title=\"Ivats\"><PERSON><PERSON><PERSON> of Bulgaria</a> by a ruse, thereby ending Bulgarian resistance against Emperor <a href=\"https://wikipedia.org/wiki/Basil_II\" title=\"Basil II\"><PERSON> II</a>'s <a href=\"https://wikipedia.org/wiki/Byzantine_conquest_of_Bulgaria\" title=\"Byzantine conquest of Bulgaria\">conquest of Bulgaria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eustath<PERSON>_Daphnomeles"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivats"}, {"title": "Basil II", "link": "https://wikipedia.org/wiki/Basil_II"}, {"title": "Byzantine conquest of Bulgaria", "link": "https://wikipedia.org/wiki/Byzantine_conquest_of_Bulgaria"}]}, {"year": "1038", "text": "King <PERSON>, the first king of Hungary, dies; his nephew, <PERSON>, succeeds him.", "html": "1038 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON></a>, the first king of Hungary, dies; his nephew, <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, succeeds him.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON></a>, the first king of Hungary, dies; his nephew, <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, succeeds him.", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hungary"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1057", "text": "King <PERSON><PERSON> is killed at the Battle of Lumphanan by the forces of <PERSON><PERSON><PERSON> mac <PERSON>.", "html": "1057 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON></a> is killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lumphanan\" title=\"Battle of Lumphanan\">Battle of Lumphanan</a> by the forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON></a> is killed at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lumphanan\" title=\"Battle of Lumphanan\">Battle of Lumphanan</a> by the forces of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> of Scotland\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>, King of Scotland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland"}, {"title": "Battle of Lumphanan", "link": "https://wikipedia.org/wiki/Battle_of_Lumphanan"}, {"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1070", "text": "The Pavian-born Benedictine <PERSON><PERSON><PERSON><PERSON> is appointed as the new Archbishop of Canterbury in England.", "html": "1070 - The <a href=\"https://wikipedia.org/wiki/Pavia\" title=\"Pavia\"><PERSON><PERSON></a>-born <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> <a href=\"https://wikipedia.org/wiki/Lanfranc\" title=\"Lanfranc\"><PERSON><PERSON><PERSON><PERSON></a> is appointed as the new <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> in England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pavia\" title=\"Pavia\"><PERSON><PERSON></a>-born <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> <a href=\"https://wikipedia.org/wiki/Lanfranc\" title=\"Lanfranc\"><PERSON><PERSON><PERSON><PERSON></a> is appointed as the new <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop of Canterbury</a> in England.", "links": [{"title": "Pavia", "link": "https://wikipedia.org/wiki/Pavia"}, {"title": "Benedictine", "link": "https://wikipedia.org/wiki/Benedictine"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nfra<PERSON>"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}]}, {"year": "1096", "text": "Starting date of the First Crusade as set by <PERSON>.", "html": "1096 - Starting date of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> as set by <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\">Pope Urban II</a>.", "no_year_html": "Starting date of the <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a> as set by <a href=\"https://wikipedia.org/wiki/Pope_Urban_II\" title=\"Pope Urban II\"><PERSON> Urban II</a>.", "links": [{"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/Pope_Urban_II"}]}, {"year": "1185", "text": "The cave city of Vardzia is consecrated by Queen <PERSON><PERSON> of Georgia.", "html": "1185 - The cave city of <a href=\"https://wikipedia.org/wiki/Vardzia\" title=\"Vardzia\">Vardzia</a> is consecrated by Queen <a href=\"https://wikipedia.org/wiki/Tamar_of_Georgia\" title=\"Tamar of Georgia\">Tamar of Georgia</a>.", "no_year_html": "The cave city of <a href=\"https://wikipedia.org/wiki/Vardzia\" title=\"Vardzia\">Vardzia</a> is consecrated by Queen <a href=\"https://wikipedia.org/wiki/Tamar_of_Georgia\" title=\"Tamar of Georgia\">Tamar of Georgia</a>.", "links": [{"title": "Vardzia", "link": "https://wikipedia.org/wiki/Vardzia"}, {"title": "Tamar of Georgia", "link": "https://wikipedia.org/wiki/Tamar_of_Georgia"}]}, {"year": "1224", "text": "The Livonian Brothers of the Sword, a Catholic military order, occupy Tarbatu (today Tartu) as part of the Livonian Crusade.", "html": "1224 - The <a href=\"https://wikipedia.org/wiki/Livonian_Brothers_of_the_Sword\" title=\"Livonian Brothers of the Sword\">Livonian Brothers of the Sword</a>, a Catholic military order, <a href=\"https://wikipedia.org/wiki/Siege_of_Tartu_(1224)\" title=\"Siege of Tartu (1224)\">occupy Tarbatu</a> (today <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>) as part of the <a href=\"https://wikipedia.org/wiki/Livonian_Crusade\" title=\"Livonian Crusade\">Livonian Crusade</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Livonian_Brothers_of_the_Sword\" title=\"Livonian Brothers of the Sword\">Livonian Brothers of the Sword</a>, a Catholic military order, <a href=\"https://wikipedia.org/wiki/Siege_of_Tartu_(1224)\" title=\"Siege of Tartu (1224)\">occupy Tarbatu</a> (today <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>) as part of the <a href=\"https://wikipedia.org/wiki/Livonian_Crusade\" title=\"Livonian Crusade\">Livonian Crusade</a>.", "links": [{"title": "Livonian Brothers of the Sword", "link": "https://wikipedia.org/wiki/Livonian_Brothers_of_the_Sword"}, {"title": "Siege of Tartu (1224)", "link": "https://wikipedia.org/wiki/Siege_of_Tartu_(1224)"}, {"title": "Tartu", "link": "https://wikipedia.org/wiki/Tartu"}, {"title": "Livonian Crusade", "link": "https://wikipedia.org/wiki/Livonian_Crusade"}]}, {"year": "1237", "text": "Spanish Reconquista: The Battle of the Puig between the Moorish forces of Taifa of Valencia against the Kingdom of Aragon culminates in an Aragonese victory.", "html": "1237 - <a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Puig\" title=\"Battle of the Puig\">Battle of the Puig</a> between the <a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">Moorish</a> forces of <a href=\"https://wikipedia.org/wiki/Taifa_of_Valencia\" title=\"Taifa of Valencia\">Taifa of Valencia</a> against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Aragon\" title=\"Kingdom of Aragon\">Kingdom of Aragon</a> culminates in an Aragonese victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Reconquista\" class=\"mw-redirect\" title=\"Spanish Reconquista\">Spanish Reconquista</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Puig\" title=\"Battle of the Puig\">Battle of the Puig</a> between the <a href=\"https://wikipedia.org/wiki/Al-Andalus\" title=\"Al-Andalus\">Moorish</a> forces of <a href=\"https://wikipedia.org/wiki/Taifa_of_Valencia\" title=\"Taifa of Valencia\">Taifa of Valencia</a> against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Aragon\" title=\"Kingdom of Aragon\">Kingdom of Aragon</a> culminates in an Aragonese victory.", "links": [{"title": "Spanish Reconquista", "link": "https://wikipedia.org/wiki/Spanish_Reconquista"}, {"title": "Battle of the Puig", "link": "https://wikipedia.org/wiki/Battle_of_the_Puig"}, {"title": "Al-Andalus", "link": "https://wikipedia.org/wiki/Al-Andalus"}, {"title": "Tai<PERSON> of Valencia", "link": "https://wikipedia.org/wiki/Taifa_of_Valencia"}, {"title": "Kingdom of Aragon", "link": "https://wikipedia.org/wiki/Kingdom_of_Aragon"}]}, {"year": "1248", "text": "The foundation stone of Cologne Cathedral, built to house the relics of the Three Wise Men, is laid. (Construction is eventually completed in 1880.)", "html": "1248 - The <a href=\"https://wikipedia.org/wiki/Cornerstone\" title=\"Cornerstone\">foundation stone</a> of <a href=\"https://wikipedia.org/wiki/Cologne_Cathedral\" title=\"Cologne Cathedral\">Cologne Cathedral</a>, built to house the <a href=\"https://wikipedia.org/wiki/Shrine_of_the_Three_Kings\" title=\"Shrine of the Three Kings\">relics</a> of the <a href=\"https://wikipedia.org/wiki/Biblical_Magi\" title=\"Biblical Magi\">Three Wise Men</a>, is laid. (Construction is eventually completed in <a href=\"https://wikipedia.org/wiki/1880\" title=\"1880\">1880</a>.)", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cornerstone\" title=\"Cornerstone\">foundation stone</a> of <a href=\"https://wikipedia.org/wiki/Cologne_Cathedral\" title=\"Cologne Cathedral\">Cologne Cathedral</a>, built to house the <a href=\"https://wikipedia.org/wiki/Shrine_of_the_Three_Kings\" title=\"Shrine of the Three Kings\">relics</a> of the <a href=\"https://wikipedia.org/wiki/Biblical_Magi\" title=\"Biblical Magi\">Three Wise Men</a>, is laid. (Construction is eventually completed in <a href=\"https://wikipedia.org/wiki/1880\" title=\"1880\">1880</a>.)", "links": [{"title": "Cornerstone", "link": "https://wikipedia.org/wiki/Cornerstone"}, {"title": "Cologne Cathedral", "link": "https://wikipedia.org/wiki/Cologne_Cathedral"}, {"title": "Shrine of the Three Kings", "link": "https://wikipedia.org/wiki/Shrine_of_the_Three_Kings"}, {"title": "Biblical Magi", "link": "https://wikipedia.org/wiki/Biblical_Magi"}, {"title": "1880", "link": "https://wikipedia.org/wiki/1880"}]}, {"year": "1261", "text": "<PERSON> is crowned as the first Byzantine emperor in fifty-seven years.", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned as the first <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine emperor</a> in fifty-seven years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> VIII Pa<PERSON>ologos\"><PERSON> VIII <PERSON></a> is crowned as the first <a href=\"https://wikipedia.org/wiki/List_of_Byzantine_emperors\" title=\"List of Byzantine emperors\">Byzantine emperor</a> in fifty-seven years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Byzantine emperors", "link": "https://wikipedia.org/wiki/List_of_Byzantine_emperors"}]}, {"year": "1281", "text": "Mongol invasion of Japan: The Mongolian fleet of <PERSON><PERSON><PERSON> Khan is destroyed by a \"divine wind\" for the second time in the Battle of Kōan.", "html": "1281 - <a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Japan\" title=\"Mongol invasions of Japan\">Mongol invasion of Japan</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongolian</a> fleet of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kubla<PERSON> Khan\"><PERSON><PERSON><PERSON></a> is destroyed by a \"<a href=\"https://wikipedia.org/wiki/Kamikaze_(typhoon)\" title=\"Kamikaze (typhoon)\">divine wind</a>\" for the second time in the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C5%8Dan\" title=\"Battle of Kōan\">Battle of Kōan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mongol_invasions_of_Japan\" title=\"Mongol invasions of Japan\">Mongol invasion of Japan</a>: The <a href=\"https://wikipedia.org/wiki/Mongol_Empire\" title=\"Mongol Empire\">Mongolian</a> fleet of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kublai Khan\"><PERSON><PERSON><PERSON></a> is destroyed by a \"<a href=\"https://wikipedia.org/wiki/Kamikaze_(typhoon)\" title=\"Kamikaze (typhoon)\">divine wind</a>\" for the second time in the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C5%8Dan\" title=\"Battle of Kōan\">Battle of Kōan</a>.", "links": [{"title": "Mongol invasions of Japan", "link": "https://wikipedia.org/wiki/Mongol_invasions_of_Japan"}, {"title": "Mongol Empire", "link": "https://wikipedia.org/wiki/Mongol_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (typhoon)", "link": "https://wikipedia.org/wiki/Ka<PERSON>ka<PERSON>_(typhoon)"}, {"title": "Battle of Kōan", "link": "https://wikipedia.org/wiki/Battle_of_K%C5%8Dan"}]}, {"year": "1310", "text": "The city of Rhodes surrenders to the forces of the Knights of St. John, completing their conquest of Rhodes. The knights establish their headquarters on the island and rename themselves the Knights of Rhodes.", "html": "1310 - <a href=\"https://wikipedia.org/wiki/Rhodes_(city)\" title=\"Rhodes (city)\">The city of Rhodes</a> surrenders to the forces of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights of St. John</a>, completing <a href=\"https://wikipedia.org/wiki/Hospitaller_conquest_of_Rhodes\" title=\"Hospitaller conquest of Rhodes\">their conquest of Rhodes</a>. The knights establish their headquarters on the island and rename themselves the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights of Rhodes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhodes_(city)\" title=\"Rhodes (city)\">The city of Rhodes</a> surrenders to the forces of the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights of St. John</a>, completing <a href=\"https://wikipedia.org/wiki/Hospitaller_conquest_of_Rhodes\" title=\"Hospitaller conquest of Rhodes\">their conquest of Rhodes</a>. The knights establish their headquarters on the island and rename themselves the <a href=\"https://wikipedia.org/wiki/Knights_Hospitaller\" title=\"Knights Hospitaller\">Knights of Rhodes</a>.", "links": [{"title": "Rhodes (city)", "link": "https://wikipedia.org/wiki/Rhodes_(city)"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}, {"title": "Hospitaller conquest of Rhodes", "link": "https://wikipedia.org/wiki/Hospitaller_conquest_of_Rhodes"}, {"title": "Knights Hospitaller", "link": "https://wikipedia.org/wiki/Knights_Hospitaller"}]}, {"year": "1430", "text": "<PERSON>, lord of Milan, conquers Lucca.", "html": "1430 - <a href=\"https://wikipedia.org/wiki/<PERSON>_I_<PERSON>rz<PERSON>\" title=\"Francesco I Sforza\"><PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, conquers <a href=\"https://wikipedia.org/wiki/Lucca\" title=\"Lucca\">Lucca</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> I Sforza\"><PERSON></a>, lord of <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>, conquers <a href=\"https://wikipedia.org/wiki/Lucca\" title=\"Lucca\">Lucca</a>.", "links": [{"title": "Francesco I Sforza", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}, {"title": "Lucca", "link": "https://wikipedia.org/wiki/Lucca"}]}, {"year": "1461", "text": "The Empire of Trebizond surrenders to the forces of Sultan <PERSON><PERSON><PERSON>. This is regarded by some historians as the real end of the Byzantine Empire. Emperor <PERSON> is exiled and later murdered.", "html": "1461 - The <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Empire of Trebizond</a> surrenders to the forces of <PERSON> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\">Mehmed II</a>. This is regarded by some historians as the real end of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>. Emperor <a href=\"https://wikipedia.org/wiki/David_of_Trebizond\" title=\"David of Trebizond\">David</a> is exiled and later murdered.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Empire_of_Trebizond\" title=\"Empire of Trebizond\">Empire of Trebizond</a> surrenders to the forces of <PERSON> <a href=\"https://wikipedia.org/wiki/Mehmed_II\" title=\"Mehmed II\">Mehmed II</a>. This is regarded by some historians as the real end of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>. Emperor <a href=\"https://wikipedia.org/wiki/David_of_Trebizond\" title=\"David of Trebizond\"><PERSON></a> is exiled and later murdered.", "links": [{"title": "Empire of Trebizond", "link": "https://wikipedia.org/wiki/Empire_of_Trebizond"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "David of Trebizond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Trebizond"}]}, {"year": "1483", "text": "<PERSON> <PERSON><PERSON> IV consecrates the Sistine Chapel.", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"Pope Sixtus IV\">Pope <PERSON>tus IV</a> consecrates the <a href=\"https://wikipedia.org/wiki/Sistine_Chapel\" title=\"Sistine Chapel\">Sistine Chapel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"Pope Sixtus IV\"><PERSON> Sixtus IV</a> consecrates the <a href=\"https://wikipedia.org/wiki/Sistine_Chapel\" title=\"Sistine Chapel\">Sistine Chapel</a>.", "links": [{"title": "<PERSON> <PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}, {"title": "Sistine Chapel", "link": "https://wikipedia.org/wiki/Sistine_Chapel"}]}, {"year": "1511", "text": "<PERSON><PERSON><PERSON> of Portugal conquers Malacca, the capital of the Malacca Sultanate.", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of Portugal <a href=\"https://wikipedia.org/wiki/Capture_of_Malacca_(1511)\" title=\"Capture of Malacca (1511)\">conquers</a> <a href=\"https://wikipedia.org/wiki/Malacca\" title=\"Malacca\">Malacca</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Malacca_Sultanate\" title=\"Malacca Sultanate\">Malacca Sultanate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of Portugal <a href=\"https://wikipedia.org/wiki/Capture_of_Malacca_(1511)\" title=\"Capture of Malacca (1511)\">conquers</a> <a href=\"https://wikipedia.org/wiki/Malacca\" title=\"Malacca\">Malacca</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Malacca_Sultanate\" title=\"Malacca Sultanate\">Malacca Sultanate</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Capture of Malacca (1511)", "link": "https://wikipedia.org/wiki/Capture_of_Malacca_(1511)"}, {"title": "Malacca", "link": "https://wikipedia.org/wiki/Malacca"}, {"title": "Malacca Sultanate", "link": "https://wikipedia.org/wiki/Malacca_Sultanate"}]}, {"year": "1517", "text": "Seven Portuguese armed vessels led by <PERSON><PERSON><PERSON> meet Chinese officials at the Pearl River estuary.", "html": "1517 - Seven Portuguese armed <a href=\"https://wikipedia.org/wiki/Watercraft\" title=\"Watercraft\">vessels</a> led by <a href=\"https://wikipedia.org/wiki/Fern%C3%A3o_Pires_de_Andrade\" title=\"<PERSON>rn<PERSON> Pires de Andrade\"><PERSON>rn<PERSON> Pires de Andrade</a> meet Chinese officials at the <a href=\"https://wikipedia.org/wiki/Pearl_River_Delta\" title=\"Pearl River Delta\">Pearl River estuary</a>.", "no_year_html": "Seven Portuguese armed <a href=\"https://wikipedia.org/wiki/Watercraft\" title=\"Watercraft\">vessels</a> led by <a href=\"https://wikipedia.org/wiki/Fern%C3%A3o_Pires_de_Andrade\" title=\"Fernão Pires de Andrade\"><PERSON>rn<PERSON> Pires de Andrade</a> meet Chinese officials at the <a href=\"https://wikipedia.org/wiki/Pearl_River_Delta\" title=\"Pearl River Delta\">Pearl River estuary</a>.", "links": [{"title": "Watercraft", "link": "https://wikipedia.org/wiki/Watercraft"}, {"title": "Fernão Pires de Andrade", "link": "https://wikipedia.org/wiki/Fern%C3%A3o_<PERSON><PERSON>_de_Andrade"}, {"title": "Pearl River Delta", "link": "https://wikipedia.org/wiki/Pearl_River_Delta"}]}, {"year": "1519", "text": "Panama City, Panama is founded.", "html": "1519 - <a href=\"https://wikipedia.org/wiki/Panama_City,_Panama\" class=\"mw-redirect\" title=\"Panama City, Panama\">Panama City, Panama</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Panama_City,_Panama\" class=\"mw-redirect\" title=\"Panama City, Panama\">Panama City, Panama</a> is founded.", "links": [{"title": "Panama City, Panama", "link": "https://wikipedia.org/wiki/Panama_City,_Panama"}]}, {"year": "1534", "text": "<PERSON><PERSON><PERSON> <PERSON> Loyola and six classmates take initial vows, leading to the creation of the Society of Jesus in September 1540.", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a> and six classmates take initial vows, leading to the creation of the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> in September 1540.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola\" title=\"<PERSON><PERSON><PERSON> of Loyola\"><PERSON><PERSON><PERSON> of Loyola</a> and six classmates take initial vows, leading to the creation of the <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Society of Jesus</a> in September 1540.", "links": [{"title": "<PERSON><PERSON><PERSON> of Loyola", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Loyola"}, {"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}]}, {"year": "1537", "text": "Asunción, Paraguay is founded.", "html": "1537 - <a href=\"https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay\" class=\"mw-redirect\" title=\"Asunción, Paraguay\">Asunción, Paraguay</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay\" class=\"mw-redirect\" title=\"Asunción, Paraguay\">Asunción, Paraguay</a> is founded.", "links": [{"title": "Asunción, Paraguay", "link": "https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay"}]}, {"year": "1540", "text": "Arequipa, Peru is founded.", "html": "1540 - <a href=\"https://wikipedia.org/wiki/Arequipa,_Peru\" class=\"mw-redirect\" title=\"Arequipa, Peru\">Arequipa, Peru</a> is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arequipa,_Peru\" class=\"mw-redirect\" title=\"Arequipa, Peru\">Arequipa, Peru</a> is founded.", "links": [{"title": "Arequipa, Peru", "link": "https://wikipedia.org/wiki/Arequipa,_Peru"}]}, {"year": "1549", "text": "Jesuit priest <PERSON> comes ashore at Kagoshima (Traditional Japanese date: 22 July 1549).", "html": "1549 - <a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuit</a> priest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> comes ashore at <a href=\"https://wikipedia.org/wiki/Kagoshima\" title=\"Kagoshima\">Kagoshima</a> (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: 22 July 1549).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Society_of_Jesus\" class=\"mw-redirect\" title=\"Society of Jesus\">Jesuit</a> priest <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> comes ashore at <a href=\"https://wikipedia.org/wiki/Kagoshima\" title=\"Kagoshima\">Kagoshima</a> (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: 22 July 1549).", "links": [{"title": "Society of Jesus", "link": "https://wikipedia.org/wiki/Society_of_Jesus"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kagoshima", "link": "https://wikipedia.org/wiki/Kagoshima"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "1592", "text": "Imjin War: At the Battle of Hansan Island, the Korean Navy, led by <PERSON>, <PERSON>, and <PERSON>, decisively defeats the Japanese Navy, led by <PERSON><PERSON><PERSON><PERSON>.", "html": "1592 - <a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-98)\">Imjin War</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Hansan_Island\" title=\"Battle of Hansan Island\">Battle of Hansan Island</a>, the Korean Navy, led by <a href=\"https://wikipedia.org/wiki/Yi_Sun-sin\" title=\"Yi Sun-sin\"><PERSON> Sun-sin</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Eok-gi\" class=\"mw-redirect\" title=\"Yi Eok-gi\"><PERSON>-gi</a>, and <a href=\"https://wikipedia.org/wiki/Won_Gyun\" class=\"mw-redirect\" title=\"Won Gyun\">Won Gyun</a>, decisively defeats the Japanese Navy, led by <a href=\"https://wikipedia.org/wiki/Waki<PERSON>ka_Ya<PERSON>u\" title=\"Waki<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-98)\">Imjin War</a>: At the <a href=\"https://wikipedia.org/wiki/Battle_of_Hansan_Island\" title=\"Battle of Hansan Island\">Battle of Hansan Island</a>, the Korean Navy, led by <a href=\"https://wikipedia.org/wiki/Yi_Sun-sin\" title=\"Yi Sun-sin\">Yi Sun-sin</a>, <a href=\"https://wikipedia.org/wiki/Yi_Eok-gi\" class=\"mw-redirect\" title=\"Yi Eok-gi\"><PERSON>-gi</a>, and <a href=\"https://wikipedia.org/wiki/Won_Gyun\" class=\"mw-redirect\" title=\"Won Gyun\">Won Gyun</a>, decisively defeats the Japanese Navy, led by <a href=\"https://wikipedia.org/wiki/Waki<PERSON><PERSON>_<PERSON>\" title=\"Waki<PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>.", "links": [{"title": "Japanese invasions of Korea (1592-98)", "link": "https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%9398)"}, {"title": "Battle of Hansan Island", "link": "https://wikipedia.org/wiki/Battle_of_Hansan_Island"}, {"title": "<PERSON>-sin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-sin"}, {"title": "<PERSON>gi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-gi"}, {"title": "<PERSON>yun", "link": "https://wikipedia.org/wiki/Won_Gyun"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waki<PERSON><PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1599", "text": "Nine Years' War: Battle of Curlew Pass: Irish forces led by <PERSON> successfully ambush English forces, led by Sir <PERSON><PERSON>, sent to relieve Collooney Castle.", "html": "1599 - <a href=\"https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)\" title=\"Nine Years' War (Ireland)\">Nine Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Curlew_Pass\" title=\"Battle of Curlew Pass\">Battle of Curlew Pass</a>: Irish forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a> successfully ambush English forces, led by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, sent to relieve Collooney Castle.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)\" title=\"Nine Years' War (Ireland)\">Nine Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Curlew_Pass\" title=\"Battle of Curlew Pass\">Battle of Curlew Pass</a>: Irish forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a> successfully ambush English forces, led by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, sent to relieve Collooney Castle.", "links": [{"title": "Nine Years' War (Ireland)", "link": "https://wikipedia.org/wiki/Nine_Years%27_War_(Ireland)"}, {"title": "Battle of Curlew Pass", "link": "https://wikipedia.org/wiki/Battle_of_Curlew_Pass"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Donnell"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1695", "text": "French forces end the bombardment of Brussels.", "html": "1695 - French forces end the <a href=\"https://wikipedia.org/wiki/Bombardment_of_Brussels_(1695)\" class=\"mw-redirect\" title=\"Bombardment of Brussels (1695)\">bombardment of Brussels</a>.", "no_year_html": "French forces end the <a href=\"https://wikipedia.org/wiki/Bombardment_of_Brussels_(1695)\" class=\"mw-redirect\" title=\"Bombardment of Brussels (1695)\">bombardment of Brussels</a>.", "links": [{"title": "Bombardment of Brussels (1695)", "link": "https://wikipedia.org/wiki/Bombardment_of_Brussels_(1695)"}]}, {"year": "1760", "text": "Seven Years' War: Battle of Liegnitz: <PERSON> the Great's victory over the Austrians under <PERSON>.", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Liegnitz_(1760)\" title=\"Battle of Liegnitz (1760)\">Battle of Liegnitz</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>'s victory over the <a href=\"https://wikipedia.org/wiki/Austrians\" title=\"Austrians\">Austrians</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Liegnitz_(1760)\" title=\"Battle of Liegnitz (1760)\">Battle of Liegnitz</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>'s victory over the <a href=\"https://wikipedia.org/wiki/Austrians\" title=\"Austrians\">Austrians</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Battle of Liegnitz (1760)", "link": "https://wikipedia.org/wiki/Battle_of_Liegnitz_(1760)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Austrians", "link": "https://wikipedia.org/wiki/Austrians"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "The <PERSON>, the last surviving French general of the American Revolutionary War, arrives in New York and begins a tour of 24 states.", "html": "1824 - The <a href=\"https://wikipedia.org/wiki/<PERSON>,_Marquis_<PERSON>\" title=\"<PERSON>, <PERSON>\">Marquis <PERSON></a>, the last surviving French general of the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, arrives in New York and begins a <a href=\"https://wikipedia.org/wiki/Visit_of_the_<PERSON>_<PERSON>_<PERSON>_to_the_United_States\" title=\"Visit of the <PERSON> to the United States\">tour of 24 states</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>,_Marquis_<PERSON>\" title=\"<PERSON>, <PERSON>\">Marquis <PERSON></a>, the last surviving French general of the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, arrives in New York and begins a <a href=\"https://wikipedia.org/wiki/Visit_of_the_<PERSON>_<PERSON>_<PERSON>_to_the_United_States\" title=\"Visit of the <PERSON> to the United States\">tour of 24 states</a>.", "links": [{"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Visit of the <PERSON> to the United States", "link": "https://wikipedia.org/wiki/Visit_of_the_Marquis_de_<PERSON>_to_the_United_States"}]}, {"year": "1843", "text": "The Cathedral of Our Lady of Peace in Honolulu, Hawaii is dedicated. Now the cathedral of the Roman Catholic Diocese of Honolulu, it is the oldest Roman Catholic cathedral in continuous use in the United States.", "html": "1843 - The <a href=\"https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace\" class=\"mw-redirect\" title=\"Cathedral of Our Lady of Peace\">Cathedral of Our Lady of Peace</a> in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a> is dedicated. Now the cathedral of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu\" title=\"Roman Catholic Diocese of Honolulu\">Roman Catholic Diocese of Honolulu</a>, it is the oldest <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> <a href=\"https://wikipedia.org/wiki/Cathedral\" title=\"Cathedral\">cathedral</a> in continuous use in the United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace\" class=\"mw-redirect\" title=\"Cathedral of Our Lady of Peace\">Cathedral of Our Lady of Peace</a> in <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a> is dedicated. Now the cathedral of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu\" title=\"Roman Catholic Diocese of Honolulu\">Roman Catholic Diocese of Honolulu</a>, it is the oldest <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Roman Catholic</a> <a href=\"https://wikipedia.org/wiki/Cathedral\" title=\"Cathedral\">cathedral</a> in continuous use in the United States.", "links": [{"title": "Cathedral of Our Lady of Peace", "link": "https://wikipedia.org/wiki/Cathedral_of_Our_Lady_of_Peace"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}, {"title": "Roman Catholic Diocese of Honolulu", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Honolulu"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}, {"title": "Cathedral", "link": "https://wikipedia.org/wiki/Cathedral"}]}, {"year": "1843", "text": "Tivoli Gardens, one of the oldest still intact amusement parks in the world, opens in Copenhagen, Denmark.", "html": "1843 - <a href=\"https://wikipedia.org/wiki/Tivoli_Gardens\" title=\"Tivoli Gardens\">Tivoli Gardens</a>, one of the oldest still intact <a href=\"https://wikipedia.org/wiki/Amusement_park\" title=\"Amusement park\">amusement parks</a> in the world, opens in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tivoli_Gardens\" title=\"Tivoli Gardens\">Tivoli Gardens</a>, one of the oldest still intact <a href=\"https://wikipedia.org/wiki/Amusement_park\" title=\"Amusement park\">amusement parks</a> in the world, opens in <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a>, Denmark.", "links": [{"title": "Tivoli Gardens", "link": "https://wikipedia.org/wiki/Tivoli_Gardens"}, {"title": "Amusement park", "link": "https://wikipedia.org/wiki/Amusement_park"}, {"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}]}, {"year": "1863", "text": "The Anglo-Satsuma War begins between the Satsuma Domain of Japan and the United Kingdom (Traditional Japanese date: July 2, 1863).", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/Bombardment_of_Kagoshima\" title=\"Bombardment of Kagoshima\">Anglo-Satsuma War</a> begins between the <a href=\"https://wikipedia.org/wiki/Satsuma_Domain\" title=\"Satsuma Domain\">Satsuma Domain</a> of Japan and the United Kingdom (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 2, 1863).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bombardment_of_Kagoshima\" title=\"Bombardment of Kagoshima\">Anglo-Satsuma War</a> begins between the <a href=\"https://wikipedia.org/wiki/Satsuma_Domain\" title=\"Satsuma Domain\">Satsuma Domain</a> of Japan and the United Kingdom (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 2, 1863).", "links": [{"title": "Bombardment of Kagoshima", "link": "https://wikipedia.org/wiki/Bombardment_of_Kagoshima"}, {"title": "Satsuma Domain", "link": "https://wikipedia.org/wiki/Satsuma_Domain"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "1893", "text": "Ibadan area becomes a British Protectorate after a treaty signed by <PERSON>jabi, the Baale of Ibadan with the British acting Governor of Lagos, <PERSON>.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Ibadan\" title=\"Ibadan\">Ibadan</a> area becomes a British <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">Protectorate</a> after a treaty signed by Fijabi, the <a href=\"https://wikipedia.org/wiki/Olubadan\" title=\"Olubadan\"><PERSON><PERSON> of Ibadan</a> with the British acting Governor of <a href=\"https://wikipedia.org/wiki/Lagos_Colony\" title=\"Lagos Colony\">Lagos</a>, <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ibadan\" title=\"Ibadan\">Ibadan</a> area becomes a British <a href=\"https://wikipedia.org/wiki/Protectorate\" title=\"Protectorate\">Protectorate</a> after a treaty signed by <PERSON>jabi, the <a href=\"https://wikipedia.org/wiki/Olubadan\" title=\"Olubadan\"><PERSON><PERSON> of Ibadan</a> with the British acting Governor of <a href=\"https://wikipedia.org/wiki/Lagos_Colony\" title=\"Lagos Colony\">Lagos</a>, <PERSON>.", "links": [{"title": "Ibadan", "link": "https://wikipedia.org/wiki/Ibadan"}, {"title": "Protectorate", "link": "https://wikipedia.org/wiki/Protectorate"}, {"title": "Olubadan", "link": "https://wikipedia.org/wiki/Olubadan"}, {"title": "Lagos Colony", "link": "https://wikipedia.org/wiki/Lagos_Colony"}]}, {"year": "1899", "text": "Fratton Park football ground in Portsmouth, England is officially first opened.", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Fratton_Park\" title=\"Fratton Park\">Fratton Park</a> football ground in <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> is officially first opened.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fratton_Park\" title=\"Fratton Park\">Fratton Park</a> football ground in <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a> is officially first opened.", "links": [{"title": "Fratton Park", "link": "https://wikipedia.org/wiki/Fratton_Park"}, {"title": "Portsmouth", "link": "https://wikipedia.org/wiki/Portsmouth"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}]}, {"year": "1907", "text": "Ordination in Constantinople of <PERSON><PERSON> <PERSON>, the first African-American Orthodox priest, \"Priest-Apostolic\" to America and the West Indies.", "html": "1907 - Ordination in Constantinople of Fr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first African-American <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Orthodox</a> priest, \"Priest-Apostolic\" to America and the West Indies.", "no_year_html": "Ordination in Constantinople of Fr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first African-American <a href=\"https://wikipedia.org/wiki/Eastern_Orthodox_Church\" title=\"Eastern Orthodox Church\">Orthodox</a> priest, \"Priest-Apostolic\" to America and the West Indies.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Eastern Orthodox Church", "link": "https://wikipedia.org/wiki/Eastern_Orthodox_Church"}]}, {"year": "1914", "text": "A servant of American architect, <PERSON>, sets fire to the living quarters of <PERSON>'s Wisconsin home, Taliesin, and murders seven people there.", "html": "1914 - A servant of American <a href=\"https://wikipedia.org/wiki/Architect\" title=\"Architect\">architect</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sets fire to the living quarters of <PERSON>'s <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a> home, <a href=\"https://wikipedia.org/wiki/Ta<PERSON><PERSON>_(studio)\" title=\"Taliesin (studio)\">Talies<PERSON></a>, and murders seven people there.", "no_year_html": "A servant of American <a href=\"https://wikipedia.org/wiki/Architect\" title=\"Architect\">architect</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sets fire to the living quarters of <PERSON>'s <a href=\"https://wikipedia.org/wiki/Wisconsin\" title=\"Wisconsin\">Wisconsin</a> home, <a href=\"https://wikipedia.org/wiki/Ta<PERSON><PERSON>_(studio)\" title=\"Taliesin (studio)\">Taliesin</a>, and murders seven people there.", "links": [{"title": "Architect", "link": "https://wikipedia.org/wiki/Architect"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wisconsin", "link": "https://wikipedia.org/wiki/Wisconsin"}, {"title": "Ta<PERSON>in (studio)", "link": "https://wikipedia.org/wiki/Taliesin_(studio)"}]}, {"year": "1914", "text": "The Panama Canal opens to traffic with the transit of the cargo ship SS Ancon.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> opens to traffic with the transit of the cargo ship <a href=\"https://wikipedia.org/wiki/SS_Ancon\" title=\"SS Ancon\">SS <i>Ancon</i></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a> opens to traffic with the transit of the cargo ship <a href=\"https://wikipedia.org/wiki/SS_Ancon\" title=\"SS Ancon\">SS <i>Ancon</i></a>.", "links": [{"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}, {"title": "SS Ancon", "link": "https://wikipedia.org/wiki/SS_Ancon"}]}, {"year": "1914", "text": "World War I: The First Russian Army, led by <PERSON>, enters East Prussia.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/1st_Army_(Russian_Empire)\" title=\"1st Army (Russian Empire)\">First Russian Army</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, enters <a href=\"https://wikipedia.org/wiki/East_Prussia\" title=\"East Prussia\">East Prussia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/1st_Army_(Russian_Empire)\" title=\"1st Army (Russian Empire)\">First Russian Army</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, enters <a href=\"https://wikipedia.org/wiki/East_Prussia\" title=\"East Prussia\">East Prussia</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "1st Army (Russian Empire)", "link": "https://wikipedia.org/wiki/1st_Army_(Russian_Empire)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "East Prussia", "link": "https://wikipedia.org/wiki/East_Prussia"}]}, {"year": "1914", "text": "World War I: Beginning of the Battle of Cer, the first Allied victory of World War I.", "html": "1914 - World War I: Beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_Cer\" title=\"Battle of Cer\">Battle of Cer</a>, the first <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> victory of World War I.", "no_year_html": "World War I: Beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_Cer\" title=\"Battle of Cer\">Battle of Cer</a>, the first <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied</a> victory of World War I.", "links": [{"title": "Battle of Cer", "link": "https://wikipedia.org/wiki/Battle_of_Cer"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1915", "text": "A story in New York World newspaper reveals that the Imperial German government had purchased excess phenol from Thomas Edison that could be used to make explosives for the war effort and diverted it to Bayer for aspirin production.", "html": "1915 - A story in <i>New York World</i> newspaper reveals that the Imperial German government had purchased excess phenol from Thomas Edison that could be used to make explosives for the war effort and <a href=\"https://wikipedia.org/wiki/Great_Phenol_Plot\" title=\"Great Phenol Plot\">diverted it</a> to Bayer for aspirin production.", "no_year_html": "A story in <i>New York World</i> newspaper reveals that the Imperial German government had purchased excess phenol from Thomas Edison that could be used to make explosives for the war effort and <a href=\"https://wikipedia.org/wiki/Great_Phenol_Plot\" title=\"Great Phenol Plot\">diverted it</a> to Bayer for aspirin production.", "links": [{"title": "Great Phenol Plot", "link": "https://wikipedia.org/wiki/Great_Phenol_Plot"}]}, {"year": "1920", "text": "Polish-Soviet War: Battle of Warsaw, so-called Miracle at the Vistula.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a>, so-called Miracle at the Vistula.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)\" title=\"Battle of Warsaw (1920)\">Battle of Warsaw</a>, so-called Miracle at the Vistula.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "Battle of Warsaw (1920)", "link": "https://wikipedia.org/wiki/Battle_of_Warsaw_(1920)"}]}, {"year": "1935", "text": "<PERSON> and Wiley Post are killed after their aircraft develops engine problems during takeoff in Barrow, Alaska.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\"><PERSON></a> are killed after their aircraft develops engine problems during takeoff in <a href=\"https://wikipedia.org/wiki/Barrow,_Alaska\" class=\"mw-redirect\" title=\"Barrow, Alaska\">Barrow, Alaska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\"><PERSON></a> are killed after their aircraft develops engine problems during takeoff in <a href=\"https://wikipedia.org/wiki/Barrow,_Alaska\" class=\"mw-redirect\" title=\"Barrow, Alaska\">Barrow, Alaska</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wiley Post", "link": "https://wikipedia.org/wiki/Wiley_Post"}, {"title": "Barrow, Alaska", "link": "https://wikipedia.org/wiki/Barrow,_Alaska"}]}, {"year": "1939", "text": "Twenty-six Junkers Ju 87 bombers commanded by <PERSON> meet unexpected ground fog during a dive-bombing demonstration for Luftwaffe generals at Neuhammer. Thirteen of them crash and burn.", "html": "1939 - Twenty-six <a href=\"https://wikipedia.org/wiki/Junkers_Ju_87\" title=\"Junkers Ju 87\">Junkers Ju 87</a> bombers commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet unexpected ground fog during a dive-bombing demonstration for Luftwaffe generals at <a href=\"https://wikipedia.org/wiki/%C5%9Awi%C4%99tosz%C3%B3w\" title=\"Świętoszów\">Neuhammer</a>. Thirteen of them crash and burn.", "no_year_html": "Twenty-six <a href=\"https://wikipedia.org/wiki/Junkers_Ju_87\" title=\"Junkers Ju 87\">Junkers Ju 87</a> bombers commanded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> meet unexpected ground fog during a dive-bombing demonstration for Luftwaffe generals at <a href=\"https://wikipedia.org/wiki/%C5%9Awi%C4%99tosz%C3%B3w\" title=\"Świętoszów\">Neuhammer</a>. Thirteen of them crash and burn.", "links": [{"title": "Junkers Ju 87", "link": "https://wikipedia.org/wiki/Junkers_Ju_87"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Świętoszów", "link": "https://wikipedia.org/wiki/%C5%9Awi%C4%99tosz%C3%B3w"}]}, {"year": "1939", "text": "The Wizard of Oz premieres at <PERSON><PERSON><PERSON>'s Chinese Theater in Los Angeles, California.", "html": "1939 - <i><a href=\"https://wikipedia.org/wiki/The_Wizard_of_Oz_(1939_film)\" class=\"mw-redirect\" title=\"The Wizard of Oz (1939 film)\">The Wizard of Oz</a></i> premieres at <PERSON><PERSON><PERSON>'s Chinese Theater in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Wizard_of_Oz_(1939_film)\" class=\"mw-redirect\" title=\"The Wizard of Oz (1939 film)\">The Wizard of Oz</a></i> premieres at <PERSON><PERSON><PERSON>'s Chinese Theater in <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles, California</a>.", "links": [{"title": "The Wizard of Oz (1939 film)", "link": "https://wikipedia.org/wiki/The_Wizard_of_Oz_(1939_film)"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1940", "text": "An Italian submarine torpedoes and sinks the Greek cruiser <PERSON><PERSON> at Tinos harbor during peacetime, marking the most serious Italian provocation prior to the outbreak of the Greco-Italian War in October.", "html": "1940 - An <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> submarine torpedoes and sinks the <a href=\"https://wikipedia.org/wiki/Greek_cruiser_<PERSON><PERSON>_(1912)\" title=\"Greek cruiser <PERSON><PERSON> (1912)\">Greek cruiser <i><PERSON><PERSON></i></a> at <a href=\"https://wikipedia.org/wiki/Tinos\" title=\"Tinos\">Tinos</a> harbor during peacetime, marking the most serious Italian provocation prior to the outbreak of the <a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Greco-Italian War</a> in October.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italian</a> submarine torpedoes and sinks the <a href=\"https://wikipedia.org/wiki/Greek_cruiser_<PERSON><PERSON>_(1912)\" title=\"Greek cruiser <PERSON><PERSON> (1912)\">Greek cruiser <i><PERSON><PERSON></i></a> at <a href=\"https://wikipedia.org/wiki/Tinos\" title=\"Tinos\">Tinos</a> harbor during peacetime, marking the most serious Italian provocation prior to the outbreak of the <a href=\"https://wikipedia.org/wiki/Greco-Italian_War\" title=\"Greco-Italian War\">Greco-Italian War</a> in October.", "links": [{"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}, {"title": "Greek cruiser <PERSON><PERSON> (1912)", "link": "https://wikipedia.org/wiki/Greek_cruiser_<PERSON><PERSON>_(1912)"}, {"title": "Tin<PERSON>", "link": "https://wikipedia.org/wiki/Tinos"}, {"title": "Greco-Italian War", "link": "https://wikipedia.org/wiki/Greco-Italian_War"}]}, {"year": "1941", "text": "Corporal <PERSON> is executed by firing squad at the Tower of London at 07:12, making him the last person to be executed at the Tower for espionage.", "html": "1941 - Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by firing squad at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> at 07:12, making him the last person to be executed at the Tower for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "no_year_html": "Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is executed by firing squad at the <a href=\"https://wikipedia.org/wiki/Tower_of_London\" title=\"Tower of London\">Tower of London</a> at 07:12, making him the last person to be executed at the Tower for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tower of London", "link": "https://wikipedia.org/wiki/Tower_of_London"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1942", "text": "World War II: Operation Pedestal: The oil tanker SS Ohio reaches the island of Malta barely afloat carrying vital fuel supplies for the island's defenses.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Pedestal\" title=\"Operation Pedestal\">Operation Pedestal</a>: The oil tanker <a href=\"https://wikipedia.org/wiki/SS_Ohio_(1940)\" title=\"SS Ohio (1940)\">SS <i>Ohio</i></a> reaches the island of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> barely afloat carrying vital fuel supplies for the island's defenses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Pedestal\" title=\"Operation Pedestal\">Operation Pedestal</a>: The oil tanker <a href=\"https://wikipedia.org/wiki/SS_Ohio_(1940)\" title=\"SS Ohio (1940)\">SS <i>Ohio</i></a> reaches the island of <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a> barely afloat carrying vital fuel supplies for the island's defenses.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Pedestal", "link": "https://wikipedia.org/wiki/Operation_Pedestal"}, {"title": "SS Ohio (1940)", "link": "https://wikipedia.org/wiki/SS_Ohio_(1940)"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1943", "text": "World War II:  Battle of Trahili: Superior German forces surround Cretan partisans, who manage to escape against all odds.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Trahili\" title=\"Battle of Trahili\">Battle of Trahili</a>: Superior German forces surround Cretan partisans, who manage to escape against all odds.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Trahili\" title=\"Battle of Trahili\">Battle of Trahili</a>: Superior German forces surround Cretan partisans, who manage to escape against all odds.", "links": [{"title": "Battle of Trahili", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "World War II: Operation Dragoon: Allied forces land in southern France.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Dragoon\" title=\"Operation Dragoon\">Operation Dragoon</a>: Allied forces land in <a href=\"https://wikipedia.org/wiki/Southern_France\" title=\"Southern France\">southern France</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Dragoon\" title=\"Operation Dragoon\">Operation Dragoon</a>: Allied forces land in <a href=\"https://wikipedia.org/wiki/Southern_France\" title=\"Southern France\">southern France</a>.", "links": [{"title": "Operation Dragoon", "link": "https://wikipedia.org/wiki/Operation_Dragoon"}, {"title": "Southern France", "link": "https://wikipedia.org/wiki/Southern_France"}]}, {"year": "1945", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> broadcasts his declaration of surrender following the effective surrender of Japan in World War II; Korea gains independence from the Empire of Japan.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Hirohito\" title=\"Hirohito\">Emperor <PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Hirohito_surrender_broadcast\" title=\"Hirohito surrender broadcast\">broadcasts his declaration of surrender</a> following the effective <a href=\"https://wikipedia.org/wiki/Surrender_of_Japan\" title=\"Surrender of Japan\">surrender of Japan</a> in <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>; <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">Korea</a> gains <a href=\"https://wikipedia.org/wiki/National_Liberation_Day_of_Korea\" title=\"National Liberation Day of Korea\">independence</a> from the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hirohito\" title=\"Hirohito\">Emperor <PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Hirohito_surrender_broadcast\" title=\"Hirohito surrender broadcast\">broadcasts his declaration of surrender</a> following the effective <a href=\"https://wikipedia.org/wiki/Surrender_of_Japan\" title=\"Surrender of Japan\">surrender of Japan</a> in <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>; <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">Korea</a> gains <a href=\"https://wikipedia.org/wiki/National_Liberation_Day_of_Korea\" title=\"National Liberation Day of Korea\">independence</a> from the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON><PERSON>o"}, {"title": "<PERSON><PERSON><PERSON><PERSON> surrender broadcast", "link": "https://wikipedia.org/wiki/Hirohito_surrender_broadcast"}, {"title": "Surrender of Japan", "link": "https://wikipedia.org/wiki/Surrender_of_Japan"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Korea under Japanese rule", "link": "https://wikipedia.org/wiki/Korea_under_Japanese_rule"}, {"title": "National Liberation Day of Korea", "link": "https://wikipedia.org/wiki/National_Liberation_Day_of_Korea"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}]}, {"year": "1947", "text": "India gains independence from British rule after near 190 years of British company and crown rule and joins the Commonwealth of Nations.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> gains <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">independence</a> from <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British rule</a> after near 190 years of <a href=\"https://wikipedia.org/wiki/Company_rule_in_India\" title=\"Company rule in India\">British company</a> and <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">crown rule</a> and joins the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> gains <a href=\"https://wikipedia.org/wiki/Indian_independence_movement\" title=\"Indian independence movement\">independence</a> from <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">British rule</a> after near 190 years of <a href=\"https://wikipedia.org/wiki/Company_rule_in_India\" title=\"Company rule in India\">British company</a> and <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">crown rule</a> and joins the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "links": [{"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Indian independence movement", "link": "https://wikipedia.org/wiki/Indian_independence_movement"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}, {"title": "Company rule in India", "link": "https://wikipedia.org/wiki/Company_rule_in_India"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1947", "text": "Founder of Pakistan, <PERSON> is sworn in as first Governor-General of Pakistan in Karachi.", "html": "1947 - Founder of Pakistan, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "no_year_html": "Founder of Pakistan, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Pakistan", "link": "https://wikipedia.org/wiki/Governor-General_of_Pakistan"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}]}, {"year": "1948", "text": "The First Republic of Korea (South Korea) is established in the southern half of the peninsula.", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">First Republic of Korea</a> (South Korea) is established in the southern half of the peninsula.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Republic_of_Korea\" title=\"First Republic of Korea\">First Republic of Korea</a> (South Korea) is established in the southern half of the peninsula.", "links": [{"title": "First Republic of Korea", "link": "https://wikipedia.org/wiki/First_Republic_of_Korea"}]}, {"year": "1950", "text": "Measuring .mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}Mw 8.6, the largest earthquake on land occurs in the Assam-Tibet-Myanmar border, killing 4,800.", "html": "1950 - Measuring <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> 8.6, the <a href=\"https://wikipedia.org/wiki/1950_Assam%E2%80%93Tibet_earthquake\" title=\"1950 Assam-Tibet earthquake\">largest earthquake on land</a> occurs in the <a href=\"https://wikipedia.org/wiki/India%E2%80%93Myanmar_border\" title=\"India-Myanmar border\">Assam-Tibet-Myanmar border</a>, killing 4,800.", "no_year_html": "Measuring <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> 8.6, the <a href=\"https://wikipedia.org/wiki/1950_Assam%E2%80%93Tibet_earthquake\" title=\"1950 Assam-Tibet earthquake\">largest earthquake on land</a> occurs in the <a href=\"https://wikipedia.org/wiki/India%E2%80%93Myanmar_border\" title=\"India-Myanmar border\">Assam-Tibet-Myanmar border</a>, killing 4,800.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "1950 Assam-Tibet earthquake", "link": "https://wikipedia.org/wiki/1950_Assam%E2%80%93Tibet_earthquake"}, {"title": "India-Myanmar border", "link": "https://wikipedia.org/wiki/India%E2%80%93Myanmar_border"}]}, {"year": "1952", "text": "A flash flood drenches the town of Lynmouth, England, killing 34 people.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Lynmouth_Flood\" title=\"Lynmouth Flood\">A flash flood</a> drenches the town of <a href=\"https://wikipedia.org/wiki/Lynmouth\" title=\"Lynmouth\">Lynmouth</a>, England, killing 34 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lynmouth_Flood\" title=\"Lynmouth Flood\">A flash flood</a> drenches the town of <a href=\"https://wikipedia.org/wiki/Lynmouth\" title=\"Lynmouth\">Lynmouth</a>, England, killing 34 people.", "links": [{"title": "Lynmouth Flood", "link": "https://wikipedia.org/wiki/Lynmouth_Flood"}, {"title": "Lynmouth", "link": "https://wikipedia.org/wiki/Lynmouth"}]}, {"year": "1954", "text": "<PERSON> begins his dictatorship in Paraguay.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his dictatorship in <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins his dictatorship in <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguay</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}]}, {"year": "1959", "text": "American Airlines Flight 514, a Boeing 707, crashes near the Calverton Executive Airpark in Calverton, New York, killing all five people on board.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_514\" title=\"American Airlines Flight 514\">American Airlines Flight 514</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, crashes near the <a href=\"https://wikipedia.org/wiki/Calverton_Executive_Airpark\" title=\"Calverton Executive Airpark\">Calverton Executive Airpark</a> in <a href=\"https://wikipedia.org/wiki/Calverton,_New_York\" title=\"Calverton, New York\">Calverton, New York</a>, killing all five people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_514\" title=\"American Airlines Flight 514\">American Airlines Flight 514</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a>, crashes near the <a href=\"https://wikipedia.org/wiki/Calverton_Executive_Airpark\" title=\"Calverton Executive Airpark\">Calverton Executive Airpark</a> in <a href=\"https://wikipedia.org/wiki/Calverton,_New_York\" title=\"Calverton, New York\">Calverton, New York</a>, killing all five people on board.", "links": [{"title": "American Airlines Flight 514", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_514"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "Calverton Executive Airpark", "link": "https://wikipedia.org/wiki/Calverton_Executive_Airpark"}, {"title": "Calverton, New York", "link": "https://wikipedia.org/wiki/Calverton,_New_York"}]}, {"year": "1960", "text": "Republic of the Congo (Brazzaville) becomes independent from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a> (<a href=\"https://wikipedia.org/wiki/Brazzaville\" title=\"Brazzaville\">Brazzaville</a>) becomes independent from <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a> (<a href=\"https://wikipedia.org/wiki/Brazzaville\" title=\"Brazzaville\">Brazzaville</a>) becomes independent from <a href=\"https://wikipedia.org/wiki/French_Fourth_Republic\" title=\"French Fourth Republic\">France</a>.", "links": [{"title": "Republic of the Congo", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo"}, {"title": "Brazzaville", "link": "https://wikipedia.org/wiki/Brazzaville"}, {"title": "French Fourth Republic", "link": "https://wikipedia.org/wiki/French_Fourth_Republic"}]}, {"year": "1961", "text": "Border guard <PERSON> flees from East Germany while on duty guarding the construction of the Berlin Wall.", "html": "1961 - Border guard <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> flees from <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> while on duty guarding the construction of the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "no_year_html": "Border guard <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> flees from <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a> while on duty guarding the construction of the <a href=\"https://wikipedia.org/wiki/Berlin_Wall\" title=\"Berlin Wall\">Berlin Wall</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Berlin Wall", "link": "https://wikipedia.org/wiki/Berlin_Wall"}]}, {"year": "1962", "text": "<PERSON> defects to North Korea after running across the Korean Demilitarized Zone. <PERSON><PERSON><PERSON><PERSON> died in 2016.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects to <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> after running across the <a href=\"https://wikipedia.org/wiki/Korean_Demilitarized_Zone\" title=\"Korean Demilitarized Zone\">Korean Demilitarized Zone</a>. <PERSON><PERSON><PERSON><PERSON> died in 2016.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defects to <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> after running across the <a href=\"https://wikipedia.org/wiki/Korean_Demilitarized_Zone\" title=\"Korean Demilitarized Zone\">Korean Demilitarized Zone</a>. <PERSON><PERSON><PERSON><PERSON> died in 2016.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Korean Demilitarized Zone", "link": "https://wikipedia.org/wiki/Korean_Demilitarized_Zone"}]}, {"year": "1963", "text": "Execution of <PERSON>, the last man to be hanged in Scotland.", "html": "1963 - Execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the last man to be hanged in <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a>.", "no_year_html": "Execution of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the last man to be hanged in <a href=\"https://wikipedia.org/wiki/Scotland\" title=\"Scotland\">Scotland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Scotland", "link": "https://wikipedia.org/wiki/Scotland"}]}, {"year": "1963", "text": "President <PERSON><PERSON> is overthrown in the Republic of the Congo, after a three-day uprising in the capital.", "html": "1963 - President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is overthrown in the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a>, after a <a href=\"https://wikipedia.org/wiki/Trois_Glorieuses_(1963)\" title=\"Trois Glorieuses (1963)\">three-day uprising</a> in the capital.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is overthrown in the <a href=\"https://wikipedia.org/wiki/Republic_of_the_Congo\" title=\"Republic of the Congo\">Republic of the Congo</a>, after a <a href=\"https://wikipedia.org/wiki/Trois_Glorieuses_(1963)\" title=\"Trois Glorieuses (1963)\">three-day uprising</a> in the capital.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Republic of the Congo", "link": "https://wikipedia.org/wiki/Republic_of_the_Congo"}, {"title": "<PERSON><PERSON> Glorieuses (1963)", "link": "https://wikipedia.org/wiki/Trois_Glorieuses_(1963)"}]}, {"year": "1965", "text": "The Beatles play to nearly 60,000 fans at Shea Stadium in New York City, an event later regarded as the birth of stadium rock.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> play to nearly <a href=\"https://wikipedia.org/wiki/The_Beatles%27_1965_US_tour\" title=\"The Beatles' 1965 US tour\">60,000 fans at Shea Stadium</a> in New York City, an event later regarded as the birth of <a href=\"https://wikipedia.org/wiki/Arena_rock\" title=\"Arena rock\">stadium rock</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> play to nearly <a href=\"https://wikipedia.org/wiki/The_Beatles%27_1965_US_tour\" title=\"The Beatles' 1965 US tour\">60,000 fans at Shea Stadium</a> in New York City, an event later regarded as the birth of <a href=\"https://wikipedia.org/wiki/Arena_rock\" title=\"Arena rock\">stadium rock</a>.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "The Beatles' 1965 US tour", "link": "https://wikipedia.org/wiki/The_Beatles%27_1965_US_tour"}, {"title": "Arena rock", "link": "https://wikipedia.org/wiki/Arena_rock"}]}, {"year": "1969", "text": "The Woodstock Music & Art Fair opens in Bethel, New York, featuring some of the top rock musicians of the era.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Woodstock\" title=\"Woodstock\">Woodstock Music &amp; Art Fair</a> opens in <a href=\"https://wikipedia.org/wiki/Bethel,_New_York\" title=\"Bethel, New York\">Bethel, New York</a>, featuring some of the top rock musicians of the era.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Woodstock\" title=\"Woodstock\">Woodstock Music &amp; Art Fair</a> opens in <a href=\"https://wikipedia.org/wiki/Bethel,_New_York\" title=\"Bethel, New York\">Bethel, New York</a>, featuring some of the top rock musicians of the era.", "links": [{"title": "Woodstock", "link": "https://wikipedia.org/wiki/Woodstock"}, {"title": "Bethel, New York", "link": "https://wikipedia.org/wiki/Bethel,_New_York"}]}, {"year": "1970", "text": "<PERSON> becomes the first woman to play professionally in an American football game.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to play professionally in an <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> game.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first woman to play professionally in an <a href=\"https://wikipedia.org/wiki/American_football\" title=\"American football\">American football</a> game.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American football", "link": "https://wikipedia.org/wiki/American_football"}]}, {"year": "1971", "text": "President <PERSON> completes the break from the gold standard by ending convertibility of the United States dollar into gold by foreign investors.", "html": "1971 - President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes the break from the <a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">gold standard</a> by <a href=\"https://wikipedia.org/wiki/Nixon_Shock\" class=\"mw-redirect\" title=\"Nixon Shock\">ending</a> convertibility of the <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">United States dollar</a> into gold by foreign investors.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> completes the break from the <a href=\"https://wikipedia.org/wiki/Gold_standard\" title=\"Gold standard\">gold standard</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_Shock\" class=\"mw-redirect\" title=\"Nixon Shock\">ending</a> convertibility of the <a href=\"https://wikipedia.org/wiki/United_States_dollar\" title=\"United States dollar\">United States dollar</a> into gold by foreign investors.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gold standard", "link": "https://wikipedia.org/wiki/Gold_standard"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Shock"}, {"title": "United States dollar", "link": "https://wikipedia.org/wiki/United_States_dollar"}]}, {"year": "1971", "text": "Bahrain gains independence from the United Kingdom.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> gains <a href=\"https://wikipedia.org/wiki/Independence_Day_(Bahrain)\" title=\"Independence Day (Bahrain)\">independence</a> from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a> gains <a href=\"https://wikipedia.org/wiki/Independence_Day_(Bahrain)\" title=\"Independence Day (Bahrain)\">independence</a> from the United Kingdom.", "links": [{"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}, {"title": "Independence Day (Bahrain)", "link": "https://wikipedia.org/wiki/Independence_Day_(Bahrain)"}]}, {"year": "1973", "text": "Vietnam War: The USAF bombing of Cambodia ends.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The USAF <a href=\"https://wikipedia.org/wiki/Operation_Freedom_Deal\" title=\"Operation Freedom Deal\">bombing of Cambodia</a> ends.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The USAF <a href=\"https://wikipedia.org/wiki/Operation_Freedom_Deal\" title=\"Operation Freedom Deal\">bombing of Cambodia</a> ends.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Freedom Deal", "link": "https://wikipedia.org/wiki/Operation_Freedom_Deal"}]}, {"year": "1974", "text": "<PERSON><PERSON>, First Lady of South Korea, is killed during an apparent assassination attempt upon President <PERSON>.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady\" class=\"mw-redirect\" title=\"First Lady\">First Lady</a> of South Korea, is killed during an apparent assassination attempt upon <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/First_Lady\" class=\"mw-redirect\" title=\"First Lady\">First Lady</a> of South Korea, is killed during an apparent assassination attempt upon <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>o", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>o"}, {"title": "First Lady", "link": "https://wikipedia.org/wiki/First_Lady"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Bangladeshi leader Sheikh <PERSON><PERSON><PERSON> is killed along with most members of his family during a military coup.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Bangladeshis\" title=\"Bangladeshis\">Bangladeshi</a> leader <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a> is killed along with most members of his family during a military coup.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangladeshis\" title=\"Bangladeshis\">Bangladeshi</a> leader <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a> is killed along with most members of his family during a military coup.", "links": [{"title": "Bangladeshis", "link": "https://wikipedia.org/wiki/Bangladeshis"}, {"title": "Sheikh <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON> makes the first official pilgrimage to Yasukuni Shrine by an incumbent prime minister on the anniversary of the end of World War II.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> makes the first official pilgrimage to <a href=\"https://wikipedia.org/wiki/Yasukuni_Shrine\" title=\"Yasukuni Shrine\">Yasukuni Shrine</a> by an incumbent <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">prime minister</a> on the anniversary of the end of World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> makes the first official pilgrimage to <a href=\"https://wikipedia.org/wiki/Yasukuni_Shrine\" title=\"Yasukuni Shrine\">Yasukuni Shrine</a> by an incumbent <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">prime minister</a> on the anniversary of the end of World War II.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Yasukuni Shrine", "link": "https://wikipedia.org/wiki/Yasukuni_Shrine"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1976", "text": "SAETA Flight 232 crashes into the Chimborazo volcano in Ecuador, killing all 59 people on board; the wreckage is not discovered until 2002.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/SAETA_Flight_232\" class=\"mw-redirect\" title=\"SAETA Flight 232\">SAETA Flight 232</a> crashes into the <a href=\"https://wikipedia.org/wiki/Chimborazo\" title=\"Chimborazo\">Chimborazo</a> volcano in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, killing all 59 people on board; the wreckage is not discovered until 2002.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SAETA_Flight_232\" class=\"mw-redirect\" title=\"SAETA Flight 232\">SAETA Flight 232</a> crashes into the <a href=\"https://wikipedia.org/wiki/Chimborazo\" title=\"Chimborazo\">Chimborazo</a> volcano in <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>, killing all 59 people on board; the wreckage is not discovered until 2002.", "links": [{"title": "SAETA Flight 232", "link": "https://wikipedia.org/wiki/SAETA_Flight_232"}, {"title": "Chimborazo", "link": "https://wikipedia.org/wiki/Chimborazo"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}]}, {"year": "1977", "text": "The Big Ear, a radio telescope operated by Ohio State University as part of the SETI project, receives a radio signal from deep space; the event is named the \"Wow! signal\" from the notation made by a volunteer on the project.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ohio_State_University_Radio_Observatory\" title=\"Ohio State University Radio Observatory\">The Big Ear</a>, a <a href=\"https://wikipedia.org/wiki/Radio_telescope\" title=\"Radio telescope\">radio telescope</a> operated by <a href=\"https://wikipedia.org/wiki/Ohio_State_University\" title=\"Ohio State University\">Ohio State University</a> as part of the <a href=\"https://wikipedia.org/wiki/Search_for_extraterrestrial_intelligence\" title=\"Search for extraterrestrial intelligence\">SETI</a> project, receives a radio signal from deep space; the event is named the \"<a href=\"https://wikipedia.org/wiki/Wow!_signal\" title=\"Wow! signal\">Wow! signal</a>\" from the notation made by a volunteer on the project.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ohio_State_University_Radio_Observatory\" title=\"Ohio State University Radio Observatory\">The Big Ear</a>, a <a href=\"https://wikipedia.org/wiki/Radio_telescope\" title=\"Radio telescope\">radio telescope</a> operated by <a href=\"https://wikipedia.org/wiki/Ohio_State_University\" title=\"Ohio State University\">Ohio State University</a> as part of the <a href=\"https://wikipedia.org/wiki/Search_for_extraterrestrial_intelligence\" title=\"Search for extraterrestrial intelligence\">SETI</a> project, receives a radio signal from deep space; the event is named the \"<a href=\"https://wikipedia.org/wiki/Wow!_signal\" title=\"Wow! signal\">Wow! signal</a>\" from the notation made by a volunteer on the project.", "links": [{"title": "Ohio State University Radio Observatory", "link": "https://wikipedia.org/wiki/Ohio_State_University_Radio_Observatory"}, {"title": "Radio telescope", "link": "https://wikipedia.org/wiki/Radio_telescope"}, {"title": "Ohio State University", "link": "https://wikipedia.org/wiki/Ohio_State_University"}, {"title": "Search for extraterrestrial intelligence", "link": "https://wikipedia.org/wiki/Search_for_extraterrestrial_intelligence"}, {"title": "Wow! signal", "link": "https://wikipedia.org/wiki/Wow!_signal"}]}, {"year": "1984", "text": "The Kurdistan Workers' Party in Turkey starts a campaign of armed attacks upon the Turkish Armed Forces with an attack on police and gendarmerie bases in Şemdinli and Eruh.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Kurdistan_Workers%27_Party\" title=\"Kurdistan Workers' Party\">Kurdistan Workers' Party</a> in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> starts a campaign of armed attacks upon the <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> with an <a href=\"https://wikipedia.org/wiki/1984_PKK_attacks\" title=\"1984 PKK attacks\">attack on police and gendarmerie bases in Şemdinli and Eruh</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kurdistan_Workers%27_Party\" title=\"Kurdistan Workers' Party\">Kurdistan Workers' Party</a> in <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> starts a campaign of armed attacks upon the <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> with an <a href=\"https://wikipedia.org/wiki/1984_PKK_attacks\" title=\"1984 PKK attacks\">attack on police and gendarmerie bases in Şemdinli and Eruh</a>.", "links": [{"title": "Kurdistan Workers' Party", "link": "https://wikipedia.org/wiki/Kurdistan_Workers%27_Party"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "Turkish Armed Forces", "link": "https://wikipedia.org/wiki/Turkish_Armed_Forces"}, {"title": "1984 PKK attacks", "link": "https://wikipedia.org/wiki/1984_PKK_attacks"}]}, {"year": "1985", "text": "Signing of the Assam Accord, an agreement between representatives of the Government of India and the leaders of the Assam Movement to end the movement.", "html": "1985 - Signing of the <a href=\"https://wikipedia.org/wiki/Assam_Accord\" title=\"Assam Accord\">Assam Accord</a>, an agreement between representatives of the <a href=\"https://wikipedia.org/wiki/Government_of_India\" title=\"Government of India\">Government of India</a> and the leaders of the <a href=\"https://wikipedia.org/wiki/Assam_Movement\" title=\"Assam Movement\">Assam Movement</a> to end the movement.", "no_year_html": "Signing of the <a href=\"https://wikipedia.org/wiki/Assam_Accord\" title=\"Assam Accord\">Assam Accord</a>, an agreement between representatives of the <a href=\"https://wikipedia.org/wiki/Government_of_India\" title=\"Government of India\">Government of India</a> and the leaders of the <a href=\"https://wikipedia.org/wiki/Assam_Movement\" title=\"Assam Movement\">Assam Movement</a> to end the movement.", "links": [{"title": "Assam Accord", "link": "https://wikipedia.org/wiki/Assam_Accord"}, {"title": "Government of India", "link": "https://wikipedia.org/wiki/Government_of_India"}, {"title": "Assam Movement", "link": "https://wikipedia.org/wiki/Assam_Movement"}]}, {"year": "1989", "text": "China Eastern Airlines Flight 5510 crashes after takeoff from Shanghai Hongqiao International Airport, killing 34 of the 40 people on board.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5510\" title=\"China Eastern Airlines Flight 5510\">China Eastern Airlines Flight 5510</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Shanghai_Hongqiao_International_Airport\" title=\"Shanghai Hongqiao International Airport\">Shanghai Hongqiao International Airport</a>, killing 34 of the 40 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5510\" title=\"China Eastern Airlines Flight 5510\">China Eastern Airlines Flight 5510</a> crashes after takeoff from <a href=\"https://wikipedia.org/wiki/Shanghai_Hongqiao_International_Airport\" title=\"Shanghai Hongqiao International Airport\">Shanghai Hongqiao International Airport</a>, killing 34 of the 40 people on board.", "links": [{"title": "China Eastern Airlines Flight 5510", "link": "https://wikipedia.org/wiki/China_Eastern_Airlines_Flight_5510"}, {"title": "Shanghai Hongqiao International Airport", "link": "https://wikipedia.org/wiki/Shanghai_Hongqiao_International_Airport"}]}, {"year": "1995", "text": "In South Carolina, <PERSON> becomes the first female cadet matriculated at The Citadel (she drops out less than a week later).", "html": "1995 - In <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Cadet\" title=\"Cadet\">cadet</a> matriculated at <a href=\"https://wikipedia.org/wiki/The_Citadel,_The_Military_College_of_South_Carolina\" class=\"mw-redirect\" title=\"The Citadel, The Military College of South Carolina\">The Citadel</a> (she drops out less than a week later).", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Cadet\" title=\"Cadet\">cadet</a> matriculated at <a href=\"https://wikipedia.org/wiki/The_Citadel,_The_Military_College_of_South_Carolina\" class=\"mw-redirect\" title=\"The Citadel, The Military College of South Carolina\">The Citadel</a> (she drops out less than a week later).", "links": [{"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cadet", "link": "https://wikipedia.org/wiki/Cadet"}, {"title": "The Citadel, The Military College of South Carolina", "link": "https://wikipedia.org/wiki/The_Citadel,_The_Military_College_of_South_Carolina"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Prime Minister of Japan, releases the Murayama Statement, which formally expresses remorse for Japanese war crimes committed during World War II.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mu<PERSON>ama\" title=\"<PERSON><PERSON><PERSON> Muray<PERSON>\"><PERSON><PERSON><PERSON></a>, Prime Minister of Japan, releases the <a href=\"https://wikipedia.org/wiki/Murayama_Statement\" title=\"Murayama Statement\">Murayama Statement</a>, which formally expresses remorse for <a href=\"https://wikipedia.org/wiki/Japanese_war_crimes\" title=\"Japanese war crimes\">Japanese war crimes</a> committed during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Prime Minister of Japan, releases the <a href=\"https://wikipedia.org/wiki/Murayama_Statement\" title=\"Murayama Statement\">Murayama Statement</a>, which formally expresses remorse for <a href=\"https://wikipedia.org/wiki/Japanese_war_crimes\" title=\"Japanese war crimes\">Japanese war crimes</a> committed during <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ama"}, {"title": "Murayama Statement", "link": "https://wikipedia.org/wiki/Murayama_Statement"}, {"title": "Japanese war crimes", "link": "https://wikipedia.org/wiki/Japanese_war_crimes"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1998", "text": "Northern Ireland: Omagh bombing takes place; 29 people (including a woman pregnant with twins) killed and some 220 others injured.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>: <a href=\"https://wikipedia.org/wiki/Omagh_bombing\" title=\"Omagh bombing\">Omagh bombing</a> takes place; 29 people (including a woman pregnant with twins) killed and some 220 others injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>: <a href=\"https://wikipedia.org/wiki/Omagh_bombing\" title=\"Omagh bombing\">Omagh bombing</a> takes place; 29 people (including a woman pregnant with twins) killed and some 220 others injured.", "links": [{"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Omagh bombing", "link": "https://wikipedia.org/wiki/<PERSON>ma<PERSON>_bombing"}]}, {"year": "1998", "text": "Apple introduces the iMac computer.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple</a> introduces the <a href=\"https://wikipedia.org/wiki/IMac\" title=\"IMac\">iMac</a> computer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple</a> introduces the <a href=\"https://wikipedia.org/wiki/IMac\" title=\"IMac\">iMac</a> computer.", "links": [{"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}, {"title": "IMac", "link": "https://wikipedia.org/wiki/IMac"}]}, {"year": "1999", "text": "<PERSON><PERSON> massacre in Algeria: Some 29 people are killed at a false roadblock near the Moroccan border, leading to temporary tensions with Morocco.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/B%C3%A9ni_Ounif\" title=\"Béni Ouni<PERSON>\"><PERSON>i <PERSON> massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>: Some 29 people are killed at a false roadblock near the Moroccan border, leading to temporary tensions with <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9ni_Ounif\" title=\"Béni <PERSON>\">Beni <PERSON> massacre</a> in <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>: Some 29 people are killed at a false roadblock near the Moroccan border, leading to temporary tensions with <a href=\"https://wikipedia.org/wiki/Morocco\" title=\"Morocco\">Morocco</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9ni_Ounif"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}, {"title": "Morocco", "link": "https://wikipedia.org/wiki/Morocco"}]}, {"year": "2005", "text": "Israel's unilateral disengagement plan to evict all Israelis from the Gaza Strip and from four settlements in the northern West Bank begins.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Israel%27s_unilateral_disengagement_plan\" class=\"mw-redirect\" title=\"Israel's unilateral disengagement plan\">Israel's unilateral disengagement plan</a> to evict all <a href=\"https://wikipedia.org/wiki/Israelis\" title=\"Israelis\">Israelis</a> from the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and from four <a href=\"https://wikipedia.org/wiki/Israeli_settlement\" title=\"Israeli settlement\">settlements</a> in the northern <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel%27s_unilateral_disengagement_plan\" class=\"mw-redirect\" title=\"Israel's unilateral disengagement plan\">Israel's unilateral disengagement plan</a> to evict all <a href=\"https://wikipedia.org/wiki/Israelis\" title=\"Israelis\">Israelis</a> from the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and from four <a href=\"https://wikipedia.org/wiki/Israeli_settlement\" title=\"Israeli settlement\">settlements</a> in the northern <a href=\"https://wikipedia.org/wiki/West_Bank\" title=\"West Bank\">West Bank</a> begins.", "links": [{"title": "Israel's unilateral disengagement plan", "link": "https://wikipedia.org/wiki/Israel%27s_unilateral_disengagement_plan"}, {"title": "Israelis", "link": "https://wikipedia.org/wiki/Israelis"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Israeli settlement", "link": "https://wikipedia.org/wiki/Israeli_settlement"}, {"title": "West Bank", "link": "https://wikipedia.org/wiki/West_Bank"}]}, {"year": "2005", "text": "The Helsinki Agreement between the Free Aceh Movement and the Government of Indonesia was signed, ending almost three decades of fighting.", "html": "2005 - The Helsinki Agreement between the <a href=\"https://wikipedia.org/wiki/Free_Aceh_Movement\" title=\"Free Aceh Movement\">Free Aceh Movement</a> and the <a href=\"https://wikipedia.org/wiki/Government_of_Indonesia\" title=\"Government of Indonesia\">Government of Indonesia</a> was signed, ending <a href=\"https://wikipedia.org/wiki/Insurgency_in_Aceh\" title=\"Insurgency in Aceh\">almost three decades of fighting</a>.", "no_year_html": "The Helsinki Agreement between the <a href=\"https://wikipedia.org/wiki/Free_Aceh_Movement\" title=\"Free Aceh Movement\">Free Aceh Movement</a> and the <a href=\"https://wikipedia.org/wiki/Government_of_Indonesia\" title=\"Government of Indonesia\">Government of Indonesia</a> was signed, ending <a href=\"https://wikipedia.org/wiki/Insurgency_in_Aceh\" title=\"Insurgency in Aceh\">almost three decades of fighting</a>.", "links": [{"title": "Free Aceh Movement", "link": "https://wikipedia.org/wiki/Free_Aceh_Movement"}, {"title": "Government of Indonesia", "link": "https://wikipedia.org/wiki/Government_of_Indonesia"}, {"title": "Insurgency in Aceh", "link": "https://wikipedia.org/wiki/Insurgency_in_Aceh"}]}, {"year": "2007", "text": "An 8.0-magnitude earthquake off the Pacific coast devastates Ica and various regions of Peru killing 514 and injuring 1,090.", "html": "2007 - An <a href=\"https://wikipedia.org/wiki/2007_Peru_earthquake\" title=\"2007 Peru earthquake\">8.0-magnitude earthquake</a> off the Pacific coast devastates <a href=\"https://wikipedia.org/wiki/Ica_Region\" class=\"mw-redirect\" title=\"Ica Region\">Ica</a> and various regions of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> killing 514 and injuring 1,090.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2007_Peru_earthquake\" title=\"2007 Peru earthquake\">8.0-magnitude earthquake</a> off the Pacific coast devastates <a href=\"https://wikipedia.org/wiki/Ica_Region\" class=\"mw-redirect\" title=\"Ica Region\">Ica</a> and various regions of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a> killing 514 and injuring 1,090.", "links": [{"title": "2007 Peru earthquake", "link": "https://wikipedia.org/wiki/2007_Peru_earthquake"}, {"title": "Ica Region", "link": "https://wikipedia.org/wiki/Ica_Region"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "2013", "text": "At least 27 people are killed and 226 injured in an explosion in southern Beirut near a complex used by Lebanon's militant group Hezbollah in Lebanon. A previously unknown Syrian Sunni group claims responsibility in an online video.", "html": "2013 - At least 27 people are killed and 226 injured in an <a href=\"https://wikipedia.org/wiki/August_2013_Beirut_bombing\" title=\"August 2013 Beirut bombing\">explosion</a> in southern <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> near a complex used by Lebanon's militant group <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> in Lebanon. A previously unknown <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian</a> <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> group claims responsibility in an online video.", "no_year_html": "At least 27 people are killed and 226 injured in an <a href=\"https://wikipedia.org/wiki/August_2013_Beirut_bombing\" title=\"August 2013 Beirut bombing\">explosion</a> in southern <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> near a complex used by Lebanon's militant group <a href=\"https://wikipedia.org/wiki/Hezbollah\" title=\"Hezbollah\">Hezbollah</a> in Lebanon. A previously unknown <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syrian</a> <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> group claims responsibility in an online video.", "links": [{"title": "August 2013 Beirut bombing", "link": "https://wikipedia.org/wiki/August_2013_Beirut_bombing"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}, {"title": "Hezbollah", "link": "https://wikipedia.org/wiki/Hezbollah"}, {"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "Sunni", "link": "https://wikipedia.org/wiki/Sunni"}]}, {"year": "2013", "text": "The Smithsonian announces the discovery of the olinguito, the first new carnivorous species found in the Americas in 35 years.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Smithsonian\" class=\"mw-redirect\" title=\"Smithsonian\">Smithsonian</a> announces the discovery of the <a href=\"https://wikipedia.org/wiki/Olinguito\" title=\"Olinguito\">olinguito</a>, the first new carnivorous species found in the Americas in 35 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Smithsonian\" class=\"mw-redirect\" title=\"Smithsonian\">Smithsonian</a> announces the discovery of the <a href=\"https://wikipedia.org/wiki/Olinguito\" title=\"Olinguito\">olinguito</a>, the first new carnivorous species found in the Americas in 35 years.", "links": [{"title": "Smithsonian", "link": "https://wikipedia.org/wiki/Smithsonian"}, {"title": "Olinguito", "link": "https://wikipedia.org/wiki/Olinguito"}]}, {"year": "2015", "text": "North Korea moves its clock back half an hour to introduce Pyongyang Time, 8.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1⁄2 hours ahead of UTC.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> moves its clock back half an hour to introduce <a href=\"https://wikipedia.org/wiki/Time_in_North_Korea\" title=\"Time in North Korea\">Pyongyang Time</a>, 8<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours ahead of <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> moves its clock back half an hour to introduce <a href=\"https://wikipedia.org/wiki/Time_in_North_Korea\" title=\"Time in North Korea\">Pyongyang Time</a>, 8<style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\"><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> hours ahead of <a href=\"https://wikipedia.org/wiki/UTC\" class=\"mw-redirect\" title=\"UTC\">UTC</a>.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Time in North Korea", "link": "https://wikipedia.org/wiki/Time_in_North_Korea"}, {"title": "UTC", "link": "https://wikipedia.org/wiki/UTC"}]}, {"year": "2020", "text": "Russia begins production on the Sputnik V COVID-19 vaccine.", "html": "2020 - Russia begins production on the <a href=\"https://wikipedia.org/wiki/Sputnik_V_COVID-19_vaccine\" title=\"Sputnik V COVID-19 vaccine\">Sputnik V COVID-19 vaccine</a>.", "no_year_html": "Russia begins production on the <a href=\"https://wikipedia.org/wiki/Sputnik_V_COVID-19_vaccine\" title=\"Sputnik V COVID-19 vaccine\">Sputnik V COVID-19 vaccine</a>.", "links": [{"title": "Sputnik V COVID-19 vaccine", "link": "https://wikipedia.org/wiki/Sputnik_V_COVID-19_vaccine"}]}, {"year": "2021", "text": "Kabul falls into the hands of the Taliban as <PERSON><PERSON><PERSON> flees Afghanistan along with local residents and foreign nationals, effectively reestablishing the Islamic Emirate of Afghanistan.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a> <a href=\"https://wikipedia.org/wiki/Fall_of_Kabul_(2021)\" title=\"Fall of Kabul (2021)\">falls</a> into the hands of the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> as <a href=\"https://wikipedia.org/wiki/Ashraf_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> flees Afghanistan along with local residents and foreign nationals, effectively reestablishing the <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Islamic Emirate of Afghanistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kabul\" title=\"Kabul\">Kabul</a> <a href=\"https://wikipedia.org/wiki/Fall_of_Kabul_(2021)\" title=\"Fall of Kabul (2021)\">falls</a> into the hands of the <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a> as <a href=\"https://wikipedia.org/wiki/Ashra<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> flees Afghanistan along with local residents and foreign nationals, effectively reestablishing the <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Islamic Emirate of Afghanistan</a>.", "links": [{"title": "Kabul", "link": "https://wikipedia.org/wiki/Kabul"}, {"title": "Fall of Kabul (2021)", "link": "https://wikipedia.org/wiki/Fall_of_Kabul_(2021)"}, {"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}], "Births": [{"year": "1013", "text": "<PERSON><PERSON>, empress of Japan (d. 1094)", "html": "1013 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>\"><PERSON><PERSON></a>, empress of Japan (d. 1094)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>\"><PERSON><PERSON></a>, empress of Japan (d. 1094)", "links": [{"title": "Princess <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>"}]}, {"year": "1171", "text": "<PERSON>, king of León and Galicia (d. 1230)", "html": "1171 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Le%C3%B3n\" title=\"<PERSON> of León\"><PERSON> IX</a>, king of León and Galicia (d. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_IX_of_Le%C3%B3n\" title=\"<PERSON> of León\"><PERSON> IX</a>, king of León and Galicia (d. 1230)", "links": [{"title": "<PERSON> IX of León", "link": "https://wikipedia.org/wiki/Alfonso_IX_of_Le%C3%B3n"}]}, {"year": "1195", "text": "<PERSON> of Padua, Portuguese priest and saint (d. 1231)", "html": "1195 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua\" title=\"<PERSON> of Padua\"><PERSON> of Padua</a>, Portuguese priest and saint (d. 1231)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua\" title=\"<PERSON> of Padua\"><PERSON> of Padua</a>, Portuguese priest and saint (d. 1231)", "links": [{"title": "<PERSON> of Padua", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Padua"}]}, {"year": "1385", "text": "<PERSON>, 11th Earl of Oxford, English commander (d. 1417)", "html": "1385 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford\" title=\"<PERSON>, 11th Earl of Oxford\"><PERSON>, 11th Earl <PERSON> Oxford</a>, English commander (d. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford\" title=\"<PERSON>, 11th Earl of Oxford\"><PERSON>, 11th Earl <PERSON> Oxford</a>, English commander (d. 1417)", "links": [{"title": "<PERSON>, 11th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_11th_Earl_of_Oxford"}]}, {"year": "1432", "text": "<PERSON>, Italian poet (d. 1484)", "html": "1432 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (d. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet (d. 1484)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1455", "text": "<PERSON>, duke of Bavaria (d. 1503)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (d. 1503)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1507", "text": "<PERSON>, Prince of Anhalt-Dessau, German prince (d. 1553)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau\" title=\"<PERSON>, Prince of Anhalt-Dessau\"><PERSON>, Prince of Anhalt-Dessau</a>, German prince (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau\" title=\"<PERSON>, Prince of Anhalt-Dessau\"><PERSON>, Prince of Anhalt-Dessau</a>, German prince (d. 1553)", "links": [{"title": "<PERSON>, Prince of Anhalt-Dessau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau"}]}, {"year": "1575", "text": "<PERSON><PERSON>, Croatian linguist and lexicographer (d. 1650)", "html": "1575 - <a href=\"https://wikipedia.org/wiki/Bart<PERSON>_Ka%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian linguist and lexicographer (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ka%C5%A1i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian linguist and lexicographer (d. 1650)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartol_Ka%C5%A1i%C4%87"}]}, {"year": "1589", "text": "<PERSON>, Prince of Transylvania (d. 1613)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (d. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1thory\" title=\"<PERSON>\"><PERSON></a>, Prince of Transylvania (d. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gabriel_B%C3%A1thory"}]}, {"year": "1607", "text": "<PERSON>, landgrave of Hesse-Rotenburg (d. 1658)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Rotenburg\" title=\"<PERSON>, Landgrave of Hesse-Rotenburg\"><PERSON></a>, landgrave of Hesse-Rotenburg (d. 1658)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Landgrave_of_Hesse-Rotenburg\" title=\"<PERSON>, Landgrave of Hesse-Rotenburg\"><PERSON></a>, landgrave of Hesse-Rotenburg (d. 1658)", "links": [{"title": "<PERSON>, Landgrave of Hesse-Rotenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Hesse-Rotenburg"}]}, {"year": "1608", "text": "<PERSON>, 22nd Earl of Arundel, English politician (d. 1652)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_22nd_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 22nd Earl of Arundel\"><PERSON>, 22nd Earl of Arundel</a>, English politician (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_22nd_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 22nd Earl of Arundel\"><PERSON>, 22nd Earl of Arundel</a>, English politician (d. 1652)", "links": [{"title": "<PERSON>, 22nd Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_22nd_Earl_of_Arundel"}]}, {"year": "1613", "text": "<PERSON>, French lawyer, philologist, and scholar (d. 1692)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nage\" title=\"<PERSON>\"><PERSON></a>, French lawyer, philologist, and scholar (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9nage\" title=\"<PERSON>\"><PERSON></a>, French lawyer, philologist, and scholar (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilles_M%C3%A9nage"}]}, {"year": "1615", "text": "<PERSON>, duchess of Guise (d. 1688)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Guise\" title=\"<PERSON>, Duchess of Guise\"><PERSON></a>, duchess of Guise (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Guise\" title=\"<PERSON>, Duchess of Guise\"><PERSON></a>, duchess of Guise (d. 1688)", "links": [{"title": "<PERSON>, Duchess of Guise", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Guise"}]}, {"year": "1652", "text": "<PERSON>, American politician (d. 1708)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 1708)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, Italian painter and Royal Academician (d. 1788)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and Royal Academician (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and Royal Academician (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, English engineer (d. 1810)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(civil_engineer)\" title=\"<PERSON> (civil engineer)\"><PERSON></a>, English engineer (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(civil_engineer)\" title=\"<PERSON> (civil engineer)\"><PERSON></a>, English engineer (d. 1810)", "links": [{"title": "<PERSON> (civil engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(civil_engineer)"}]}, {"year": "1736", "text": "<PERSON>, German organist and composer (d. 1803)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, German poet and author (d. 1815)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, French general and emperor (d. 1821)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and emperor (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and emperor (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1771", "text": "<PERSON>, Scottish novelist, playwright, and poet (d. 1832)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, playwright, and poet (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish novelist, playwright, and poet (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON>, English journalist and author (d. 1859)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, American writer, editor, abolitionist (d. 1860)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor, abolitionist (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer, editor, abolitionist (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, Indian warrior (d. 1831)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sangolli Ray<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian warrior (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sang<PERSON><PERSON> Ray<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian warrior (d. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1807", "text": "<PERSON>, French lawyer and politician, 4th President of the French Republic (d. 1891)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gr%C3%A9vy\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jules_Gr%C3%A9vy"}, {"title": "President of the French Republic", "link": "https://wikipedia.org/wiki/President_of_the_French_Republic"}]}, {"year": "1810", "text": "<PERSON>, French poet (d. 1876)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, American businessman (d. 1884)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, Czech piano maker (d. 1915)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Petrof\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech piano maker (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton%C3%ADn_Petrof\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech piano maker (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton%C3%ADn_Petrof"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, Canadian journalist, lawyer, and politician (d. 1908)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, lawyer, and politician (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist, lawyer, and politician (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, English artist and book illustrator (d. 1915)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist and book illustrator (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist and book illustrator (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, Scottish politician and trade unionist (d. 1915)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ie\"><PERSON><PERSON></a>, Scottish politician and trade unionist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ie\"><PERSON><PERSON></a>, Scottish politician and trade unionist (d. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ie"}]}, {"year": "1857", "text": "<PERSON>, German businessman (d. 1918)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, English author and poet (d. 1924)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/E._Nesbit\" title=\"E. Nesbit\"><PERSON><PERSON></a>, English author and poet (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._Nesbit\" title=\"E. Nesbit\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, English author and poet (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E._Nesbit"}]}, {"year": "1859", "text": "<PERSON>, American baseball player and manager (d. 1931)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, American publisher, 31st First Lady of the United States (d. 1924)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, 31st <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, 31st <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Harding"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1863", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and engineer (d. 1945)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and engineer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and engineer (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, Japanese spiritual leader, founded <PERSON><PERSON> (d. 1926)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese spiritual leader, founded <a href=\"https://wikipedia.org/wiki/Reiki\" title=\"Reiki\">Reiki</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese spiritual leader, founded <a href=\"https://wikipedia.org/wiki/Reiki\" title=\"Reiki\">Reiki</a> (d. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ui"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reiki"}]}, {"year": "1866", "text": "<PERSON><PERSON>, Italian fencer (d. 1945)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Italo <PERSON>elli\"><PERSON><PERSON></a>, Italian fencer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Italo <PERSON>elli\"><PERSON><PERSON></a>, Italian fencer (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>elli"}]}, {"year": "1872", "text": "<PERSON>, Indian guru, poet, and philosopher (d. 1950)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Sri_Aurobindo\" title=\"Sri Aurobindo\"><PERSON> <PERSON><PERSON><PERSON></a>, Indian guru, poet, and philosopher (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Aurobindo\" title=\"Sri Aurobindo\"><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Indian guru, poet, and philosopher (d. 1950)", "links": [{"title": "<PERSON> Aurobindo", "link": "https://wikipedia.org/wiki/Sri_Aurobindo"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Indian archaeologist and historian (d. 1942)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian archaeologist and historian (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian archaeologist and historian (d. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>-<PERSON>, English pianist, violinist, and composer (d. 1912)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, violinist, and composer (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, violinist, and composer (d. 1912)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek colonel and politician, 111th Prime Minister of Greece (d. 1966)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Stylianos_Gonatas\" title=\"Stylianos Gonatas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek colonel and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stylianos_Gonatas\" title=\"Stylianos Gonatas\"><PERSON><PERSON><PERSON><PERSON></a>, Greek colonel and politician, 111th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1966)", "links": [{"title": "Styl<PERSON>s <PERSON>", "link": "https://wikipedia.org/wiki/Stylianos_Gonatas"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 22nd <PERSON><PERSON><PERSON><PERSON> (d. 1941)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon\" title=\"Ta<PERSON><PERSON> Mineemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 22nd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon\" title=\"<PERSON><PERSON><PERSON> Mineemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 22nd <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mineemon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1879", "text": "<PERSON>, American actress (d. 1959)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German-American activist and politician (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist and politician (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American activist and politician (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_Wagenknecht"}]}, {"year": "1882", "text": "<PERSON>, American composer and critic (d. 1955)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and critic (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON>, English archaeologist and art historian (d. 1972)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archaeologist and art historian (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English archaeologist and art historian (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Croatian sculptor and architect (d. 1962)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1trovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian sculptor and architect (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1trovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian sculptor and architect (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ivan_Me%C5%A1trovi%C4%87"}]}, {"year": "1885", "text": "<PERSON>, American novelist, short story writer, and playwright (d. 1968)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Australian cricketer (d. 1974)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, French composer and educator (d. 1962)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, French physicist and academic, Nobel Prize laureate (d. 1987)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1892", "text": "<PERSON>, New Zealand politician, 35th Mayor of Invercargill (d. 1950)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Invercargill\" title=\"Mayor of Invercargill\">Mayor of Invercargill</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand politician, 35th <a href=\"https://wikipedia.org/wiki/Mayor_of_Invercargill\" title=\"Mayor of Invercargill\">Mayor of Invercargill</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Invercargill", "link": "https://wikipedia.org/wiki/Mayor_of_Invercargill"}]}, {"year": "1893", "text": "<PERSON>, New Zealand astronomer and academic (d. 1950)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and academic (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand astronomer and academic (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Czech-American biochemist and physiologist, Nobel Prize laureate (d. 1957)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-American biochemist and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1896", "text": "<PERSON>, Russian-Canadian activist, founded the Madonna House Apostolate (d. 1985)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian activist, founded the <a href=\"https://wikipedia.org/wiki/Madonna_House_Apostolate\" title=\"Madonna House Apostolate\">Madonna House Apostolate</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian activist, founded the <a href=\"https://wikipedia.org/wiki/Madonna_House_Apostolate\" title=\"Madonna House Apostolate\">Madonna House Apostolate</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Madonna House Apostolate", "link": "https://wikipedia.org/wiki/Madonna_House_Apostolate"}]}, {"year": "1896", "text": "<PERSON>, American photographer and educator (d. 1958)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and educator (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Polish author and poet (d. 1966)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and poet (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish author and poet (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American silent film actress (d. 1995)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American silent film actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American silent film actress (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Polish-American painter and educator (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and educator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American painter and educator (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Panamanian politician, 21st President of Panamá (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Arnulfo_Arias\" title=\"<PERSON>rn<PERSON><PERSON> Arias\"><PERSON><PERSON><PERSON><PERSON></a>, Panamanian politician, 21st President of Panamá (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arn<PERSON>o_Arias\" title=\"Arn<PERSON>o Arias\"><PERSON><PERSON><PERSON><PERSON></a>, Panamanian politician, 21st President of Panamá (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arn<PERSON>o_Arias"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and theorist (d. 1975)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and theorist (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Dutch journalist and critic (d. 1943)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and critic (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch journalist and critic (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Canadian inventor, invented the motorized wheelchair (d. 1992)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, Canadian inventor, invented the <a href=\"https://wikipedia.org/wiki/Motorized_wheelchair\" title=\"Motorized wheelchair\">motorized wheelchair</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, Canadian inventor, invented the <a href=\"https://wikipedia.org/wiki/Motorized_wheelchair\" title=\"Motorized wheelchair\">motorized wheelchair</a> (d. 1992)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>(inventor)"}, {"title": "Motorized wheelchair", "link": "https://wikipedia.org/wiki/Motorized_wheelchair"}]}, {"year": "1909", "text": "<PERSON>, American composer and bandleader (d. 1973)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and bandleader (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and bandleader (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American chef and author (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julia Child\"><PERSON></a>, American chef and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julia Child\"><PERSON></a>, American chef and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English actress (d. 2003)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American graphic designer and art director (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer and art director (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American graphic designer and art director (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Swedish-American actress (d. 2002)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish-American actress (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Albanian journalist and author (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Aleks_%C3%87a%C3%A7i\" title=\"Aleks <PERSON>ç<PERSON>\"><PERSON><PERSON><PERSON></a>, Albanian journalist and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleks_%C3%87a%C3%A7i\" title=\"Aleks <PERSON>çi\"><PERSON><PERSON><PERSON></a>, Albanian journalist and author (d. 1989)", "links": [{"title": "Aleks <PERSON>", "link": "https://wikipedia.org/wiki/Aleks_%C3%87a%C3%A7i"}]}, {"year": "1917", "text": "<PERSON>, Irish footballer and politician, 5th Taoiseach of Ireland (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and politician, 5th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and politician, 5th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Salvadoran archbishop (d. 1980)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran archbishop (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Salvadoran archbishop (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American actor (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Huntz_Hall\" title=\"Huntz Hall\"><PERSON><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Huntz_Hall\" title=\"Huntz Hall\"><PERSON><PERSON></a>, American actor (d. 1999)", "links": [{"title": "Huntz Hall", "link": "https://wikipedia.org/wiki/Huntz_Hall"}]}, {"year": "1919", "text": "<PERSON>, Irish journalist and author (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish journalist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Austrian-Australian painter (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian painter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian painter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Polish actor and director (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Polish actor and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\"><PERSON></a>, Polish actor and director (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American sculptor and illustrator (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Greek trumpet player and composer (d. 2005)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek trumpet player and composer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek trumpet player and composer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Spanish footballer and manager (d. 1988)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ga\" title=\"Sabino Barinaga\"><PERSON><PERSON></a>, Spanish footballer and manager (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bari<PERSON>ga\" title=\"Sabino Barinaga\"><PERSON><PERSON></a>, Spanish footballer and manager (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ga"}]}, {"year": "1923", "text": "<PERSON>, American actress and singer (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English playwright and screenwriter (d. 1995)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, German-American Holocaust survivor and activist (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American Holocaust survivor and activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American Holocaust survivor and activist (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Japanese production designer, art director, and fashion designer (d. 2009)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Yoshir%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese production designer, art director, and fashion designer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yoshir%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese production designer, art director, and fashion designer (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yoshir%C5%8D_<PERSON><PERSON>i"}]}, {"year": "1924", "text": "<PERSON>, American lawyer, writer, and political activist (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, writer, and political activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, writer, and political activist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor and producer (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and fiddle player (d. 1998)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and fiddle player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and fiddle player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian pianist and composer (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Swedish-Estonian painter and author (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Swedish-Estonian painter and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Swedish-Estonian painter and author (d. 2014)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_(painter)"}]}, {"year": "1926", "text": "<PERSON>, American pianist and composer (d. 1969)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 1997)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Little Sky\"><PERSON></a>, American actor (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Iraqi-Israeli author and playwright (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Israeli author and playwright (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi-Israeli author and playwright (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American philosopher and academic (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Greek lawyer and politician, 6th President of Greece (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}]}, {"year": "1927", "text": "<PERSON>, English cricketer (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>beat<PERSON>\"><PERSON></a>, English cricketer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>beater\"><PERSON></a>, English cricketer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>beater"}]}, {"year": "1927", "text": "<PERSON>, English cricketer and judge (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and judge (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and judge (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, German scholar and academic (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scholar and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American businessman (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English director and cinematographer (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and cinematographer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and cinematographer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American captain and pilot (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2015)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1932", "text": "<PERSON>, American actress (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American physicist and engineer (d. 2002)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American game show host and DJ (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and DJ (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and DJ (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Baron <PERSON>, South African-English lawyer and judge  (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, South African-English lawyer and judge (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, South African-English lawyer and judge (d. 2017)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1997)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American social psychologist (d. 1984)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social psychologist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American folk musician and folklorist (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk musician and folklorist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American folk musician and folklorist (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and producer (d. 2007)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian musician, singer and composer (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay\" title=\"<PERSON><PERSON><PERSON><PERSON> Upadhyay\"><PERSON><PERSON><PERSON><PERSON>adhya<PERSON></a>, Indian musician, singer and composer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay\" title=\"<PERSON><PERSON><PERSON><PERSON> Upadhyay\"><PERSON><PERSON><PERSON><PERSON>adhya<PERSON></a>, Indian musician, singer and composer (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Upadhyay"}]}, {"year": "1934", "text": "<PERSON>, Jamaican cricketer and coach (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican cricketer and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American illustrator (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Soviet pilot and cosmonaut instructor (d. 1980)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet pilot and cosmonaut instructor (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet pilot and cosmonaut instructor (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English actor, narrator, singer, director, and composer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, narrator, singer, director, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, narrator, singer, director, and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON>, French author, playwright, and director (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gine_Deforges\" title=\"<PERSON><PERSON><PERSON><PERSON> Deforges\"><PERSON><PERSON><PERSON><PERSON></a>, French author, playwright, and director (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gine_Deforges\" title=\"<PERSON><PERSON><PERSON><PERSON> Deforges\"><PERSON><PERSON><PERSON><PERSON></a>, French author, playwright, and director (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gine_Deforges"}]}, {"year": "1936", "text": "<PERSON>, American actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1936", "text": "<PERSON>, American soprano and educator (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American jazz drummer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hooper\"><PERSON><PERSON></a>, American jazz drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hooper\"><PERSON><PERSON></a>, American jazz drummer", "links": [{"title": "St<PERSON> Hooper", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Indian cartoonist (d. 2014)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cartoonist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cartoonist (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American educator and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Polish engineer and author (d. 1985)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish engineer and author (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, German militant leader, founded Red Army Faction (d. 1977)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German militant leader, founded <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German militant leader, founded <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Red Army Faction", "link": "https://wikipedia.org/wiki/Red_Army_Faction"}]}, {"year": "1941", "text": "<PERSON>, American sculptor (d. 2013)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim Brothers\"><PERSON></a>, American sculptor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jim_Brothers\" title=\"Jim Brothers\"><PERSON></a>, American sculptor (d. 2013)", "links": [{"title": "Jim Brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American country musician (d. 1974)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country musician (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rich\"><PERSON></a>, American country musician (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English rock drummer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Northern Irish civil servant and politician, 2nd Speaker of the Northern Ireland Assembly", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish civil servant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Northern_Ireland_Assembly\" title=\"Speaker of the Northern Ireland Assembly\">Speaker of the Northern Ireland Assembly</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish civil servant and politician, 2nd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Northern_Ireland_Assembly\" title=\"Speaker of the Northern Ireland Assembly\">Speaker of the Northern Ireland Assembly</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the Northern Ireland Assembly", "link": "https://wikipedia.org/wiki/Speaker_of_the_Northern_Ireland_Assembly"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Health (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Greece)\" title=\"Ministry of Health (Greece)\">Greek Minister of Health</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Greece)\" title=\"Ministry of Health (Greece)\">Greek Minister of Health</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Health (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Greece)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician, Prime Minister of Bangladesh", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Khaleda_Zia"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian film actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian film actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Canadian singer-songwriter and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1949", "text": "<PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American drummer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1950", "text": "<PERSON>, Princess <PERSON> of the United Kingdom", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>\" title=\"<PERSON>, Princess <PERSON>\"><PERSON>, Princess <PERSON></a> of the United Kingdom", "links": [{"title": "<PERSON>, Princess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Princess_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American screenwriter and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English cricketer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1952", "text": "<PERSON>, American drummer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English businessman", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Swedish journalist and author (d. 2004)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist and author (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ie<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian pianist and composer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lorraine_Desmarais"}]}, {"year": "1956", "text": "<PERSON>, Ivorian journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Freedom_Neruda\" title=\"Freedom Neruda\">Freedom Neruda</a>, Ivorian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Freedom_Neruda\" title=\"Freedom Neruda\">Freedom Neruda</a>, Ivorian journalist", "links": [{"title": "Freedom Neruda", "link": "https://wikipedia.org/wiki/Freedom_Neruda"}]}, {"year": "1956", "text": "<PERSON>, English businessman and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian-American actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/%C5%B<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%B<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian-American actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-Canadian psychiatrist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian psychiatrist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian psychiatrist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Indian actress and costume designer (d. 2009)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Simple_Kapadia\" title=\"Simple Kapadia\"><PERSON> Kapadia</a>, Indian actress and costume designer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simple_Kapadia\" title=\"Simple Kapadia\"><PERSON> Kapadia</a>, Indian actress and costume designer (d. 2009)", "links": [{"title": "Simple Kapadia", "link": "https://wikipedia.org/wiki/Simple_Kapadia"}]}, {"year": "1958", "text": "<PERSON>, Russian journalist and radio host", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American actor and comedian", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American captain, pilot, and astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American political strategist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political strategist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political strategist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and musician", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1961", "text": "<PERSON>, American football player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Indian actress and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nam"}]}, {"year": "1962", "text": "<PERSON>, American chef and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/R%C4%B1dvan_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C4%B1dvan_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C4%B1dvan_<PERSON>en"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Portuguese writer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/In%C3%AAs_Pedrosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/In%C3%AAs_Pedrosa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese writer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In%C3%AAs_Pedrosa"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, Estonian lawyer and politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Vil<PERSON>_<PERSON>aar-Toomast\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>aar-Toomast\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>Too<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vil<PERSON>_<PERSON>aar-Toomast"}]}, {"year": "1963", "text": "<PERSON>, Mexican director, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_I%C3%B1%C3%A1rritu\" title=\"<PERSON>\"><PERSON></a>, Mexican director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_I%C3%B1%C3%A1rritu\" title=\"<PERSON>\"><PERSON></a>, Mexican director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A1lez_I%C3%B1%C3%A1rritu"}]}, {"year": "1963", "text": "<PERSON>, Welsh soldier and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, England cricketer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1963)\" title=\"<PERSON> (cricketer, born 1963)\"><PERSON></a>, England cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1963)\" title=\"<PERSON> (cricketer, born 1963)\"><PERSON></a>, England cricketer and coach", "links": [{"title": "<PERSON> (cricketer, born 1963)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1963)"}]}, {"year": "1964", "text": "<PERSON>, English lawyer and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American businesswoman and philanthropist, co-founded the Gates Foundation", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Gates\"><PERSON></a>, American businesswoman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Gates_Foundation\" title=\"Gates Foundation\">Gates Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gates\" class=\"mw-redirect\" title=\"<PERSON> Gates\"><PERSON></a>, American businesswoman and philanthropist, co-founded the <a href=\"https://wikipedia.org/wiki/Gates_Foundation\" title=\"Gates Foundation\">Gates Foundation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gates Foundation", "link": "https://wikipedia.org/wiki/Gates_Foundation"}]}, {"year": "1965", "text": "<PERSON>, American author, screenwriter, and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, screenwriter, and producer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Greek basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball_player)\" title=\"<PERSON><PERSON> (basketball player)\"><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball_player)\" title=\"<PERSON><PERSON> (basketball player)\"><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON> (basketball player)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball_player)"}]}, {"year": "1967", "text": "<PERSON>, Scottish ice hockey player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hand\"><PERSON></a>, Scottish ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Argentine footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian, actor, and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, producer, and screenwriter, founded Electus Studios", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/Electus\" title=\"Electus\">Electus Studios</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter, founded <a href=\"https://wikipedia.org/wiki/Electus\" title=\"Electus\">Electus Studios</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Electus"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Indian singer, musician, music composer, pianist and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>nan <PERSON>\"><PERSON><PERSON></a>, Indian singer, musician, music composer, pianist and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Adnan Sami\"><PERSON><PERSON></a>, Indian singer, musician, music composer, pianist and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ballerina (d. 2007)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ballerina (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Irish singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON><PERSON></a>, Irish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON><PERSON></a>, Irish singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1974", "text": "<PERSON>, Canadian model and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Polish footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player and radio host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Indian cricketer and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j\" title=\"<PERSON>dwaj\"><PERSON></a>, Indian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bharadwaj\"><PERSON></a>, Indian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vijay_Bharadwaj"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> W<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>jn <PERSON>den\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>den"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Australian journalist and television host", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Waleed_Aly\" title=\"Waleed Aly\"><PERSON><PERSON><PERSON></a>, Australian journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waleed_Aly\" title=\"Waleed Aly\"><PERSON><PERSON><PERSON></a>, Australian journalist and television host", "links": [{"title": "Waleed Al<PERSON>", "link": "https://wikipedia.org/wiki/Waleed_Aly"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Ukrainian gymnast", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American volleyball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Icelandic explorer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic explorer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic explorer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American swimmer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/%C3%93liver_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93liver_P%C3%A9rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93liver_P%C3%A9rez"}]}, {"year": "1982", "text": "<PERSON>, American weightlifter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weightlifter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Germ%C3%A1n_Caffa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Germ%C3%A1n_Caffa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germ%C3%A1n_Caffa"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, English association football goalkeeper", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English association football goalkeeper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English association football goalkeeper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American businesswoman", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress and singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American rapper (d. 2019)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Hu<PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>le"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Imperio\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Imperio\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ryan_D%27Imperio"}]}, {"year": "1987", "text": "<PERSON>, Dutch cyclist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Moroccan footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Oussama_As<PERSON>di\" title=\"Oussama Assaidi\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uss<PERSON>_<PERSON>\" title=\"Oussama Assaidi\"><PERSON><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uss<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actor and singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aVeg<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Jordan_Rapana\" title=\"<PERSON> Rap<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jordan_Rapana\" title=\"<PERSON> Rap<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jordan_Rapana"}]}, {"year": "1990", "text": "<PERSON>, American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Finnish snowboarder", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish snowboarder", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Indian chess player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian chess player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ban"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Barbadian netball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Holder\" title=\"<PERSON><PERSON><PERSON> Holder\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Holder\" title=\"<PERSON><PERSON><PERSON> Holder\"><PERSON><PERSON><PERSON></a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Holder"}]}, {"year": "1993", "text": "<PERSON>, Cameroonian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Jie\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Jie\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clinton_N%27Jie"}]}, {"year": "1993", "text": "<PERSON>-<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Danish footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>igen_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese swimmer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "Chief <PERSON>, American rapper", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON><PERSON>\">Chief <PERSON><PERSON></a>, American rapper", "links": [{"title": "Chief <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON><PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Indonesian-Australian badminton player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Map<PERSON>\"><PERSON><PERSON></a>, Indonesian-Australian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian-Australian badminton player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Mapasa"}]}, {"year": "1999", "text": "<PERSON><PERSON>, BMX rider", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, BMX rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, BMX rider", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>is"}]}], "Deaths": [{"year": "398", "text": "<PERSON><PERSON>, official of the Xianbei state Later Yan", "html": "398 - <a href=\"https://wikipedia.org/wiki/Lan_Han\" title=\"Lan Han\"><PERSON><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/Xianbei\" title=\"Xianbei\">Xianbei</a> state <a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lan_Han\" title=\"Lan Han\"><PERSON><PERSON></a>, official of the <a href=\"https://wikipedia.org/wiki/Xianbei\" title=\"Xianbei\">Xianbei</a> state <a href=\"https://wikipedia.org/wiki/Later_Yan\" title=\"Later Yan\">Later Yan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON>"}, {"title": "Xianbei", "link": "https://wikipedia.org/wiki/Xianbei"}, {"title": "Later Yan", "link": "https://wikipedia.org/wiki/Later_Yan"}]}, {"year": "423", "text": "<PERSON><PERSON>, Roman emperor (b. 384)", "html": "423 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, Roman emperor (b. 384)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (emperor)\"><PERSON><PERSON></a>, Roman emperor (b. 384)", "links": [{"title": "<PERSON><PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(emperor)"}]}, {"year": "465", "text": "<PERSON><PERSON>, Roman emperor (b. 420)", "html": "465 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>us\" title=\"Libius Severus\"><PERSON><PERSON></a>, Roman emperor (b. 420)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Severus\" title=\"Libius Severus\"><PERSON><PERSON></a>, Roman emperor (b. 420)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Libius_Severus"}]}, {"year": "698", "text": "<PERSON><PERSON><PERSON> of Amida, Syrian Orthodox holy man", "html": "698 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Amida\" title=\"<PERSON><PERSON><PERSON> of Amida\"><PERSON><PERSON><PERSON> of Amida</a>, Syrian Orthodox holy man", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Amida\" title=\"<PERSON><PERSON><PERSON> of Amida\"><PERSON><PERSON><PERSON> of Amida</a>, Syrian Orthodox holy man", "links": [{"title": "<PERSON><PERSON><PERSON> of Amida", "link": "https://wikipedia.org/wiki/Theodotus_of_Amida"}]}, {"year": "767", "text": "<PERSON>, Iraqi scholar and educator (b. 699)", "html": "767 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi scholar and educator (b. 699)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi scholar and educator (b. 699)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "778", "text": "<PERSON>, Frankish military leader", "html": "778 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish military leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Frankish military leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roland"}]}, {"year": "873", "text": "<PERSON>, Chinese emperor (b. 833)", "html": "873 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON></a>, Chinese emperor (b. 833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON>zong of Tang\"><PERSON></a>, Chinese emperor (b. 833)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "874", "text": "<PERSON><PERSON><PERSON>, bishop of Hildesheim", "html": "874 - <a href=\"https://wikipedia.org/wiki/Altfrid\" title=\"Altfrid\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Hildesheim\" title=\"Roman Catholic Diocese of Hildesheim\">Hildesheim</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Altfrid\" title=\"<PERSON><PERSON>rid\"><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Hildesheim\" title=\"Roman Catholic Diocese of Hildesheim\">Hildesheim</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Altfrid"}, {"title": "Roman Catholic Diocese of Hildesheim", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Hildesheim"}]}, {"year": "912", "text": "<PERSON>, Chinese warlord (b. 855)", "html": "912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Zhenguo_warlord)\" title=\"<PERSON> (Zhenguo warlord)\"><PERSON></a>, Chinese warlord (b. 855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Zhenguo_warlord)\" title=\"<PERSON> (Zhenguo warlord)\"><PERSON></a>, Chinese warlord (b. 855)", "links": [{"title": "<PERSON> (Zhenguo warlord)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Zheng<PERSON>_warlord)"}]}, {"year": "932", "text": "<PERSON>, Chinese governor and king (b. 899)", "html": "932 - <a href=\"https://wikipedia.org/wiki/Ma_<PERSON>\" title=\"Ma Xi<PERSON>ng\"><PERSON></a>, Chinese governor and king (b. 899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ma Xi<PERSON>ng\"><PERSON></a>, Chinese governor and king (b. 899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ma_<PERSON>"}]}, {"year": "955", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian tribal chieftain (harka)", "html": "955 - <a href=\"https://wikipedia.org/wiki/Bulcs%C3%BA_(chieftain)\" title=\"<PERSON><PERSON><PERSON><PERSON> (chieftain)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tribal chieftain (<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(title)\" title=\"<PERSON><PERSON> (title)\">harka</a></i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulcs%C3%BA_(chieftain)\" title=\"<PERSON><PERSON><PERSON><PERSON> (chieftain)\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tribal chieftain (<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(title)\" title=\"<PERSON><PERSON> (title)\">harka</a></i>)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (chieftain)", "link": "https://wikipedia.org/wiki/Bulcs%C3%BA_(chieftain)"}, {"title": "<PERSON><PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(title)"}]}, {"year": "955", "text": "<PERSON><PERSON>, Hungarian tribal chieftain", "html": "955 - <a href=\"https://wikipedia.org/wiki/Le<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tribal chieftain", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tribal chieftain", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "955", "text": "<PERSON><PERSON><PERSON>, Hungarian tribal chieftain", "html": "955 - <a href=\"https://wikipedia.org/wiki/S%C3%BAr_(chieftain)\" title=\"<PERSON><PERSON><PERSON> (chieftain)\"><PERSON><PERSON><PERSON></a>, Hungarian tribal chieftain", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%BAr_(chieftain)\" title=\"<PERSON><PERSON><PERSON> (chieftain)\"><PERSON><PERSON><PERSON></a>, Hungarian tribal chieftain", "links": [{"title": "<PERSON><PERSON><PERSON> (chieftain)", "link": "https://wikipedia.org/wiki/S%C3%BAr_(chieftain)"}]}, {"year": "978", "text": "<PERSON>, ruler ('king') of Southern Tang", "html": "978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)\" title=\"<PERSON> (Southern Tang)\"><PERSON></a>, ruler ('king') of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)\" title=\"<PERSON> (Southern Tang)\"><PERSON></a>, ruler ('king') of <a href=\"https://wikipedia.org/wiki/Southern_Tang\" title=\"Southern Tang\">Southern Tang</a>", "links": [{"title": "<PERSON> (Southern Tang)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Southern_Tang)"}, {"title": "Southern Tang", "link": "https://wikipedia.org/wiki/Southern_Tang"}]}, {"year": "986", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish missionary and abbot", "html": "986 - <a href=\"https://wikipedia.org/wiki/Minn<PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Cologne\"><PERSON><PERSON><PERSON><PERSON></a>, Irish missionary and abbot", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>n<PERSON><PERSON>_of_Cologne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Cologne\"><PERSON><PERSON><PERSON><PERSON></a>, Irish missionary and abbot", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Cologne", "link": "https://wikipedia.org/wiki/Minnborinus_of_Cologne"}]}, {"year": "1022", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine rebel", "html": "1022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Barytrachelos\" title=\"<PERSON><PERSON><PERSON><PERSON> Phokas Barytrachelos\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine rebel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Barytrachelos\" title=\"<PERSON><PERSON><PERSON><PERSON>okas Barytrachelos\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine rebel", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_Barytrac<PERSON>"}]}, {"year": "1038", "text": "<PERSON>, Hungarian king (b. 975)", "html": "1038 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON></a>, Hungarian king (b. 975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON></a>, Hungarian king (b. 975)", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hungary"}]}, {"year": "1057", "text": "<PERSON><PERSON>, King of Scotland", "html": "1057 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON>, King of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland\" title=\"<PERSON><PERSON>, King of Scotland\"><PERSON><PERSON>, King of Scotland</a>", "links": [{"title": "<PERSON><PERSON>, King of Scotland", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_King_of_Scotland"}]}, {"year": "1118", "text": "<PERSON><PERSON>, Byzantine emperor (b. 1048)", "html": "1118 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>mnenos\" title=\"<PERSON><PERSON> Komnen<PERSON>\"><PERSON><PERSON></a>, Byzantine emperor (b. 1048)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>mnen<PERSON>\"><PERSON><PERSON></a>, Byzantine emperor (b. 1048)", "links": [{"title": "Alexios I Komnenos", "link": "https://wikipedia.org/wiki/Alexios_I_Komnenos"}]}, {"year": "1196", "text": "<PERSON>, Duke of Swabia (b. 1173)", "html": "1196 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON>, Duke of Swabia</a> (b. 1173)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON>, Duke of Swabia</a> (b. 1173)", "links": [{"title": "<PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia"}]}, {"year": "1224", "text": "<PERSON> France, Duchess of Brabant (b. 1198)", "html": "1224 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Brabant\" title=\"<PERSON> of France, Duchess of Brabant\"><PERSON> of France, Duchess of Brabant</a> (b. 1198)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_of_Brabant\" title=\"<PERSON> of France, Duchess of Brabant\"><PERSON> of France, Duchess of Brabant</a> (b. 1198)", "links": [{"title": "<PERSON> of France, Duchess of Brabant", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Duchess_<PERSON>_<PERSON>"}]}, {"year": "1257", "text": "Saint Hyacinth of Poland", "html": "1257 - <PERSON> <a href=\"https://wikipedia.org/wiki/Hyacinth_of_Poland\" title=\"Hyacinth of Poland\">Hyacinth of Poland</a>", "no_year_html": "Saint <a href=\"https://wikipedia.org/wiki/Hyacinth_of_Poland\" title=\"Hyacinth of Poland\">Hyacinth of Poland</a>", "links": [{"title": "Hyacinth of Poland", "link": "https://wikipedia.org/wiki/Hyacinth_of_Poland"}]}, {"year": "1274", "text": "<PERSON>, French theologian and educator, founded the College of Sorbonne (b. 1201)", "html": "1274 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and educator, founded the <a href=\"https://wikipedia.org/wiki/College_of_Sorbonne\" title=\"College of Sorbonne\">College of Sorbonne</a> (b. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and educator, founded the <a href=\"https://wikipedia.org/wiki/College_of_Sorbonne\" title=\"College of Sorbonne\">College of Sorbonne</a> (b. 1201)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "College of Sorbonne", "link": "https://wikipedia.org/wiki/College_of_Sorbonne"}]}, {"year": "1275", "text": "<PERSON>, <PERSON><PERSON> of Venice", "html": "1275 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> of Venice", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1328", "text": "<PERSON><PERSON><PERSON>, emperor of the Yuan dynasty (b. 1293)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/Yes%C3%BCn_Tem%C3%BCr_(Yuan_dynasty)\" title=\"<PERSON><PERSON><PERSON> (Yuan dynasty)\"><PERSON><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan dynasty</a> (b. 1293)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yes%C3%BCn_Tem%C3%BCr_(Yuan_dynasty)\" title=\"<PERSON><PERSON><PERSON> (Yuan dynasty)\"><PERSON><PERSON><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Yuan_dynasty\" title=\"Yuan dynasty\">Yuan dynasty</a> (b. 1293)", "links": [{"title": "<PERSON><PERSON><PERSON> (Yuan dynasty)", "link": "https://wikipedia.org/wiki/Yes%C3%BCn_Tem%C3%BCr_(Yuan_dynasty)"}, {"title": "Yuan dynasty", "link": "https://wikipedia.org/wiki/Yuan_dynasty"}]}, {"year": "1369", "text": "<PERSON><PERSON> of Hainault, Queen consort of <PERSON> of England (b. 1314)", "html": "1369 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Hainault\" title=\"<PERSON><PERSON> of Hainault\"><PERSON><PERSON> of Hainault</a>, Queen consort of <PERSON> of England (b. 1314)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hai<PERSON>\" title=\"<PERSON><PERSON> of Hainault\"><PERSON><PERSON> of Hainault</a>, Queen consort of <PERSON> of England (b. 1314)", "links": [{"title": "<PERSON><PERSON> of Hainault", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1388", "text": "<PERSON><PERSON><PERSON>, Bohemian theologian and rector of the University of Paris (b. circa 1320)", "html": "1388 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nconis_de_Ericinio\" title=\"Adalbertus Ranconis de Ericinio\"><PERSON><PERSON><PERSON> Ericinio</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemian</a> theologian and rector of the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">University of Paris</a> (b. circa 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Eric<PERSON>\" title=\"<PERSON>lbertus Ranconis de Ericini<PERSON>\"><PERSON><PERSON><PERSON> Eric<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemian</a> theologian and rector of the <a href=\"https://wikipedia.org/wiki/University_of_Paris\" title=\"University of Paris\">University of Paris</a> (b. circa 1320)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Kingdom of Bohemia", "link": "https://wikipedia.org/wiki/Kingdom_of_Bohemia"}, {"title": "University of Paris", "link": "https://wikipedia.org/wiki/University_of_Paris"}]}, {"year": "1399", "text": "<PERSON><PERSON>, Danish noblewoman (b. 1358)", "html": "1399 - <a href=\"https://wikipedia.org/wiki/Ide_<PERSON><PERSON><PERSON>_<PERSON>alk\" title=\"Ide P<PERSON>rsdatter Falk\"><PERSON><PERSON><PERSON></a>, Danish noblewoman (b. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ide_<PERSON><PERSON><PERSON>_<PERSON>alk\" title=\"Ide P<PERSON>tter Falk\"><PERSON><PERSON><PERSON><PERSON></a>, Danish noblewoman (b. 1358)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_Falk"}]}, {"year": "1496", "text": "<PERSON><PERSON><PERSON> of Portugal, Queen of Castile and León (b. 1428)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Castile\" title=\"Isabella of Portugal, Queen of Castile\">In<PERSON><PERSON> of Portugal</a>, Queen of Castile and León (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Castile\" title=\"Isabella of Portugal, Queen of Castile\">In<PERSON><PERSON> of Portugal</a>, Queen of Castile and León (b. 1428)", "links": [{"title": "<PERSON> of Portugal, Queen of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Castile"}]}, {"year": "1506", "text": "<PERSON>, Flemish composer (b. c. 1445)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. c. 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. c. 1445)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1507", "text": "<PERSON>, Duke of Saxe-Lauenburg (b. 1439)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (b. 1439)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (b. 1439)", "links": [{"title": "<PERSON>, Duke of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg"}]}, {"year": "1528", "text": "<PERSON><PERSON> <PERSON> Foix, Viscount of Lautrec, French general (b. 1485)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Foix,_Viscount_of_Lautrec\" title=\"<PERSON><PERSON> of Foix, Viscount of Lautrec\"><PERSON><PERSON> of Foix, Viscount of Lautrec</a>, French general (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Foix,_Viscount_of_Lautrec\" title=\"<PERSON><PERSON> of Foix, Viscount of Lautrec\"><PERSON><PERSON> of Foix, Viscount of Lautrec</a>, French general (b. 1485)", "links": [{"title": "<PERSON><PERSON> of Foix, Viscount of Lautrec", "link": "https://wikipedia.org/wiki/O<PERSON>_of_<PERSON>,_Viscount_of_Lautrec"}]}, {"year": "1552", "text": "<PERSON> Wied, German archbishop (b. 1477)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wied\" title=\"<PERSON> of Wied\"><PERSON> of Wied</a>, German archbishop (b. 1477)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_W<PERSON>\" title=\"<PERSON> of Wied\"><PERSON> of Wied</a>, German archbishop (b. 1477)", "links": [{"title": "<PERSON> Wied", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1594", "text": "<PERSON>, English playwright (b. 1558)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright (b. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English playwright (b. 1558)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1621", "text": "<PERSON>, Scottish poet and author (b. 1582)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and author (b. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and author (b. 1582)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1666", "text": "<PERSON>, German missionary and astronomer (b. 1591)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and astronomer (b. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German missionary and astronomer (b. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1714", "text": "<PERSON><PERSON><PERSON>, Romanian prince (b. 1654)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu\" title=\"Con<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian prince (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian prince (b. 1654)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constantin_Br%C3%A2ncoveanu"}]}, {"year": "1728", "text": "<PERSON>, French viol player and composer (b. 1656)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French viol player and composer (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French viol player and composer (b. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, French mathematician, geophysicist, and astronomer (b. 1698)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, geophysicist, and astronomer (b. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician, geophysicist, and astronomer (b. 1698)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, Italian poet and author (b. 1729)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, governor of Spanish East Florida (b. 1733)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>ppinger\" title=\"<PERSON>\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/East_Florida\" title=\"East Florida\">Spanish East Florida</a> (b. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%AD<PERSON>_<PERSON>ppinger\" title=\"<PERSON>\"><PERSON></a>, governor of <a href=\"https://wikipedia.org/wiki/East_Florida\" title=\"East Florida\">Spanish East Florida</a> (b. 1733)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Mar%C3%ADa_Coppinger"}, {"title": "East Florida", "link": "https://wikipedia.org/wiki/East_Florida"}]}, {"year": "1852", "text": "<PERSON>, Finnish chemist, physicist, and mineralogist (b. 1760)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish chemist, physicist, and mineralogist (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish chemist, physicist, and mineralogist (b. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, American farmer and politician (b. 1777)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Hungarian violinist, composer, and conductor (b. 1831)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian sociologist and journalist (b. 1866)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Euclides_da_Cunha\" title=\"Euclides da Cunha\"><PERSON><PERSON><PERSON><PERSON> <PERSON> Cunha</a>, Brazilian sociologist and journalist (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Euclides_da_Cunha\" title=\"Euclides da Cunha\"><PERSON><PERSON><PERSON><PERSON> da Cunha</a>, Brazilian sociologist and journalist (b. 1866)", "links": [{"title": "Euc<PERSON>s da Cunha", "link": "https://wikipedia.org/wiki/Euclides_da_<PERSON>unha"}]}, {"year": "1917", "text": "<PERSON>, American sergeant, Medal of Honor recipient (b. 1831)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1925", "text": "<PERSON>, Estonian painter and educator (b. 1878)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and educator (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian painter and educator (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Konrad_M%C3%A4gi"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Italian ethnologist and academic, co-founded St Edmund's College, Cambridge (b. 1854)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCgel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian ethnologist and academic, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_College,_Cambridge\" title=\"St Edmund's College, Cambridge\">St Edmund's College, Cambridge</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%BCgel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian ethnologist and academic, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_College,_Cambridge\" title=\"St Edmund's College, Cambridge\">St Edmund's College, Cambridge</a> (b. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_H%C3%BCgel"}, {"title": "St Edmund's College, Cambridge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_College,_Cambridge"}]}, {"year": "1935", "text": "<PERSON>, American pilot (b. 1898)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\">Wiley Post</a>, American pilot (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wiley_Post\" title=\"Wiley Post\"><PERSON></a>, American pilot (b. 1898)", "links": [{"title": "Wiley Post", "link": "https://wikipedia.org/wiki/Wiley_Post"}]}, {"year": "1935", "text": "<PERSON>, American actor, comedian, and screenwriter (b. 1879)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French painter and author (b. 1863)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and author (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and author (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Italian novelist and poet, Nobel Prize laureate (b. 1871)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Grazia_Deledda\" title=\"Grazia Del<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grazia_Deledd<PERSON>\" title=\"Graz<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian novelist and poet, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1871)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Grazia_<PERSON>a"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Indian activist and author (b. 1892)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and author (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese general and politician, 54th Japanese Minister of the Army (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general and politician, 54th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of the Army</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese general and politician, 54th <a href=\"https://wikipedia.org/wiki/Ministry_of_War_of_Japan\" class=\"mw-redirect\" title=\"Ministry of War of Japan\">Japanese Minister of the Army</a> (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ko<PERSON>chi<PERSON>_Anami"}, {"title": "Ministry of War of Japan", "link": "https://wikipedia.org/wiki/Ministry_of_War_of_Japan"}]}, {"year": "1945", "text": "<PERSON>, English lieutenant and pilot (b. 1923)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lieutenant and pilot (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Polish pianist and composer (b. 1882)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish pianist and composer (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, German physicist and engineer (b. 1875)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and engineer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Chinese soldier (b. 1940)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese soldier (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese soldier (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Belgian painter (b. 1898)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Ma<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Magritte"}]}, {"year": "1971", "text": "<PERSON>, Hungarian-American actor (b. 1887)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American actor (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American businessman (b. 1913)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "Sheikh <PERSON><PERSON><PERSON>, Bangladeshi politician, 1st President of Bangladesh (b. 1920)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Sheikh <PERSON><PERSON><PERSON>\">Sheikh <PERSON><PERSON><PERSON></a>, Bangladeshi politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (b. 1920)", "links": [{"title": "Sheikh <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Turkish political activist and author (b. 1942)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish political activist and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish political activist and author (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author (b. 1895)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>k"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Norwegian gynaecologist and academic (b. 1896)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_L%C3%B8vset\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian gynaecologist and academic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_L%C3%B8vset\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian gynaecologist and academic (b. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8rgen_L%C3%B8vset"}]}, {"year": "1982", "text": "<PERSON>, American cartoonist (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Scottish motorcycle sidecar racer (b. 1954)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish motorcycle sidecar racer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish motorcycle sidecar racer (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Swedish biochemist and academic, Nobel Prize laureate (b. 1903)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Japanese general, pilot, and politician (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a>, Japanese general, pilot, and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general, pilot, and politician (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>u_Genda"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Greek general and diplomat (b. 1897)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON><PERSON>_Tsakalotos\" title=\"<PERSON><PERSON>sy<PERSON><PERSON> Tsakalotos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek general and diplomat (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON><PERSON>_Tsakalotos\" title=\"T<PERSON>sy<PERSON><PERSON> Tsakalotos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Greek general and diplomat (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>ulos_T<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Russian musician and actor (b. 1962)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian musician and actor (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian musician and actor (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American physician and academic (b. 1947)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Dutch cyclist (b. 1929)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Wout_Wagtmans\" title=\"Wout Wagtmans\"><PERSON><PERSON></a>, Dutch cyclist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wout_Wagtmans\" title=\"Wout Wagtmans\"><PERSON><PERSON></a>, Dutch cyclist (b. 1929)", "links": [{"title": "Wout <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wout_W<PERSON><PERSON>s"}]}, {"year": "1995", "text": "<PERSON>, American journalist and actor (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Dutch poet and educator (b. 1905)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and educator (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and educator (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English architect and interior designer (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and interior designer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and interior designer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, English barrister and biochemist, co-founder of <PERSON><PERSON> (b. 1915)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ware\"><PERSON><PERSON></a>, English barrister and biochemist, co-founder of <PERSON>sa (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ware\"><PERSON><PERSON></a>, English barrister and biochemist, co-founder of <PERSON><PERSON> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lancelot_Ware"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter (b. 1970)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Yavuz_%C3%87etin\" title=\"Ya<PERSON><PERSON> Ç<PERSON>n\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yavuz_%C3%87etin\" title=\"Ya<PERSON><PERSON> Çetin\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter (b. 1970)", "links": [{"title": "Yavuz Çetin", "link": "https://wikipedia.org/wiki/Yavuz_%C3%87etin"}]}, {"year": "2001", "text": "<PERSON>, Kenyan runner (b. 1972)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Ukrainian computer scientist and academic (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON><PERSON> (scientist)\"><PERSON><PERSON><PERSON></a>, Ukrainian computer scientist and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)\" title=\"<PERSON><PERSON><PERSON> (scientist)\"><PERSON><PERSON><PERSON></a>, Ukrainian computer scientist and academic (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(scientist)"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Swedish biochemist and academic, Nobel Prize laureate (b. 1916)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Sune_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>röm\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>e_Bergstr%C3%B6m\" title=\"<PERSON><PERSON>röm\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sune_Bergstr%C3%B6m"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Indian politician, 8th Chief Minister of Gujarat (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 8th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (b. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hary"}, {"title": "Chief Minister of Gujarat", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Gujarat"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Indian dermatologist and academic (b. 1927)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Bendapudi_Venkata_Satyanarayana\" title=\"Bendapudi Venkata Satyanarayana\"><PERSON>apudi Venkata Satyanarayana</a>, Indian dermatologist and academic (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bendapudi_Venkata_Satyanarayana\" title=\"Bendapudi Venkata Satyanarayana\"><PERSON>apudi Venkata Satyanarayana</a>, Indian dermatologist and academic (b. 1927)", "links": [{"title": "Bendapudi Venkata Satyanarayana", "link": "https://wikipedia.org/wiki/Bendapudi_Venkata_Satyanarayana"}]}, {"year": "2006", "text": "<PERSON>, New Zealand queen (b. 1931)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ang<PERSON>ahu\" title=\"<PERSON> Atairangikaahu\"><PERSON></a>, New Zealand queen (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ang<PERSON>ahu\" title=\"<PERSON> Atairangikaahu\"><PERSON></a>, New Zealand queen (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>airang<PERSON>ahu"}]}, {"year": "2006", "text": "<PERSON>, Australian rugby league player (b. 1955)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch computer scientist and academic (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra<PERSON>_<PERSON>\" title=\"<PERSON>en<PERSON><PERSON> Bron\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch computer scientist and academic (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra<PERSON>_<PERSON><PERSON>\" title=\"Coenra<PERSON> Bron\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch computer scientist and academic (b. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Dutch footballer and manager (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer and manager (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aa<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English conductor and director (b. 1944)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (b. 1944)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)"}]}, {"year": "2007", "text": "<PERSON>, American biologist, chemist, and physicist (b. 1918)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, chemist, and physicist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist, chemist, and physicist (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand physician (b. 1908)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand physician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand physician (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian businessman (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, South African-Australian boxer (b. 1929)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian boxer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Australian boxer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_To<PERSON>el"}]}, {"year": "2008", "text": "<PERSON>, American journalist and producer (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and producer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Canadian ice hockey player (b. 1984)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American bass player and saxophonist (b. 1956)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and saxophonist (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and saxophonist (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Brazilian flute player and composer (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Altami<PERSON>_<PERSON>\" title=\"Alt<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian flute player and composer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alt<PERSON><PERSON>_<PERSON>\" title=\"Alt<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian flute player and composer (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Altamiro_Carrilho"}]}, {"year": "2012", "text": "<PERSON>, American author and illustrator (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and illustrator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author and illustrator (b. 1925)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Spanish businesswoman, co-founded Inditex and <PERSON><PERSON> (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa_Mera\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Inditex\" title=\"Inditex\">Inditex</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>ara_(retailer)\" title=\"<PERSON>ara (retailer)\"><PERSON><PERSON></a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosal%C3%ADa_Mera\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish businesswoman, co-founded <a href=\"https://wikipedia.org/wiki/Inditex\" title=\"Inditex\">Inditex</a> and <a href=\"https://wikipedia.org/wiki/Zara_(retailer)\" title=\"<PERSON>ara (retailer)\"><PERSON><PERSON></a> (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosal%C3%ADa_Mera"}, {"title": "Inditex", "link": "https://wikipedia.org/wiki/Inditex"}, {"title": "Zara (retailer)", "link": "https://wikipedia.org/wiki/Zara_(retailer)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-French author and playwright (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-French author and playwright (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-French author and playwright (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C5%82awomir_Mro%C5%BCek"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Nepali politician, 28th Prime Minister of Nepal (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>est<PERSON>\" title=\"<PERSON><PERSON>est<PERSON>\"><PERSON><PERSON></a>, Nepali politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nepali politician, 28th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Nepal\" title=\"Prime Minister of Nepal\">Prime Minister of Nepal</a> (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Nepal", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Nepal"}]}, {"year": "2013", "text": "<PERSON>, Canadian actor (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Canadian actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Canadian actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Italian-American soprano and actress (b. 1909)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Li<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American soprano and actress (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American soprano and actress (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Licia_Albanese"}]}, {"year": "2015", "text": "<PERSON>, American academic, leader of the civil rights movement, and politician (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic, leader of the civil rights movement, and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic, leader of the civil rights movement, and politician (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Pakistani general (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Latvian-American architect (b. 1925)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-American architect (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Latvian-American architect (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American real-estate developer, business executive (b. 1948)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real-estate developer, business executive (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American real-estate developer, business executive (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, German footballer (b. 1945)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/G<PERSON>_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gerd_M%C3%BCller"}]}, {"year": "2024", "text": "<PERSON>, American game show host, performer, and singer (b. 1926)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American game show host, performer, and singer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)\" title=\"<PERSON> (entertainer)\"><PERSON></a>, American game show host, performer, and singer (b. 1926)", "links": [{"title": "<PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(entertainer)"}]}]}}