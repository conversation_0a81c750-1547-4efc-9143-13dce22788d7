{"date": "September 28", "url": "https://wikipedia.org/wiki/September_28", "data": {"Events": [{"year": "48 BC", "text": "<PERSON><PERSON><PERSON> disembarks at Pelusium upon arriving in Egypt, whereupon he is assassinated by order of King <PERSON>.", "html": "48 BC - 48 BC - <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\"><PERSON><PERSON><PERSON></a> disembarks at Pelusium upon arriving in Egypt, whereupon he is assassinated by order of King <a href=\"https://wikipedia.org/wiki/Ptolemy_XIII_Theos_Phil<PERSON>\" title=\"Ptolemy XIII Theos Philopator\"><PERSON> XIII</a>.", "no_year_html": "48 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ey\" title=\"<PERSON>mp<PERSON>\"><PERSON><PERSON><PERSON></a> disembarks at Pelusium upon arriving in Egypt, whereupon he is assassinated by order of King <a href=\"https://wikipedia.org/wiki/Ptolemy_XIII_Theos_Phil<PERSON>\" title=\"Ptolemy XIII Theos Philopator\"><PERSON> XIII</a>.", "links": [{"title": "Pompey", "link": "https://wikipedia.org/wiki/Pompey"}, {"title": "Ptolemy XIII <PERSON>", "link": "https://wikipedia.org/wiki/Ptolemy_XIII_Theo<PERSON>_Phil<PERSON>ator"}]}, {"year": "235", "text": "<PERSON> <PERSON><PERSON> resigns. He is exiled to the mines of Sardinia, along with <PERSON><PERSON><PERSON><PERSON> of Rome.", "html": "235 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON></a> resigns. He is exiled to the mines of Sardinia, along with <a href=\"https://wikipedia.org/wiki/Hippolytus_of_Rome\" title=\"Hippolytus of Rome\">Hippolytus of Rome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> resigns. He is exiled to the mines of Sardinia, along with <a href=\"https://wikipedia.org/wiki/Hippolytus_of_Rome\" title=\"Hippolytus of Rome\">Hippolytus of Rome</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Pontian"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Rome", "link": "https://wikipedia.org/wiki/Hippolytus_of_Rome"}]}, {"year": "351", "text": "<PERSON><PERSON><PERSON> II defeats the usurper <PERSON><PERSON><PERSON><PERSON>.", "html": "351 - <PERSON><PERSON><PERSON> II <a href=\"https://wikipedia.org/wiki/Battle_of_Mursa_Major\" title=\"Battle of Mursa Major\">defeats</a> the usurper <PERSON><PERSON><PERSON><PERSON>.", "no_year_html": "<PERSON><PERSON><PERSON> II <a href=\"https://wikipedia.org/wiki/Battle_of_Mursa_Major\" title=\"Battle of Mursa Major\">defeats</a> the usurper <PERSON><PERSON><PERSON><PERSON>.", "links": [{"title": "Battle of Mursa Major", "link": "https://wikipedia.org/wiki/Battle_of_Mursa_Major"}]}, {"year": "365", "text": "Roman usurper <PERSON><PERSON><PERSON><PERSON> bribes two legions passing by Constantinople, and proclaims himself emperor.", "html": "365 - Roman usurper <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (usurper)\"><PERSON><PERSON><PERSON><PERSON></a> bribes two legions passing by Constantinople, and proclaims himself emperor.", "no_year_html": "Roman usurper <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(usurper)\" title=\"<PERSON><PERSON><PERSON><PERSON> (usurper)\"><PERSON><PERSON><PERSON><PERSON></a> bribes two legions passing by Constantinople, and proclaims himself emperor.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (usurper)", "link": "https://wikipedia.org/wiki/Procopi<PERSON>_(usurper)"}]}, {"year": "935", "text": "Duke <PERSON><PERSON><PERSON> of Bohemia is murdered by a group of nobles led by his brother <PERSON><PERSON><PERSON>, who succeeds him.", "html": "935 - Duke <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON> I</a> of <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> is murdered by a group of nobles led by his brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON></a>, who succeeds him.", "no_year_html": "Duke <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON> I</a> of <a href=\"https://wikipedia.org/wiki/Bohemia\" title=\"Bohemia\">Bohemia</a> is murdered by a group of nobles led by his brother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON></a>, who succeeds him.", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}, {"title": "Bohemia", "link": "https://wikipedia.org/wiki/Bohemia"}, {"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}]}, {"year": "995", "text": "<PERSON><PERSON><PERSON>, Duke of Bohemia, kills most members of the rival Slavník dynasty.", "html": "995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a>, kills most members of the rival Slavník dynasty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON>, Duke of Bohemia</a>, kills most members of the rival Slavník dynasty.", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}]}, {"year": "1066", "text": "<PERSON> the Conqueror lands in England, beginning the Norman conquest.", "html": "1066 - <PERSON> the Conqueror lands in <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, beginning the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest</a>.", "no_year_html": "<PERSON> the Conqueror lands in <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, beginning the <a href=\"https://wikipedia.org/wiki/Norman_conquest_of_England\" class=\"mw-redirect\" title=\"Norman conquest of England\">Norman conquest</a>.", "links": [{"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "Norman conquest of England", "link": "https://wikipedia.org/wiki/Norman_conquest_of_England"}]}, {"year": "1106", "text": "King <PERSON> of England defeats his brother <PERSON> at the Battle of Tinchebray.", "html": "1106 - <a href=\"https://wikipedia.org/wiki/King_Henry_I_of_England\" class=\"mw-redirect\" title=\"King <PERSON> I of England\">King <PERSON> I of England</a> defeats his brother <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tinchebray\" title=\"Battle of Tinchebray\">Battle of Tinchebray</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/King_Henry_<PERSON>_of_England\" class=\"mw-redirect\" title=\"King Henry I of England\">King <PERSON> I of England</a> defeats his brother <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tinchebray\" title=\"Battle of Tinchebray\">Battle of Tinchebray</a>.", "links": [{"title": "King <PERSON> of England", "link": "https://wikipedia.org/wiki/King_<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Tinchebray", "link": "https://wikipedia.org/wiki/Battle_of_Tinchebray"}]}, {"year": "1213", "text": "Queen consort <PERSON> of Merania is assassinated by a group of Hungarian lords.", "html": "1213 - Queen consort <a href=\"https://wikipedia.org/wiki/Gertrude_of_Merania\" title=\"Gertrude of Merania\"><PERSON> of Merania</a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_of_Merania\" title=\"Assassination of <PERSON> of Merania\">assassinated</a> by a group of Hungarian lords.", "no_year_html": "Queen consort <a href=\"https://wikipedia.org/wiki/Gertrude_of_Merania\" title=\"Gertrude of Merania\"><PERSON> of Merania</a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_of_Merania\" title=\"Assassination of <PERSON> of Merania\">assassinated</a> by a group of Hungarian lords.", "links": [{"title": "Gertrude of Merania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Merania"}, {"title": "Assassination of Gertrude of Merania", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_of_Merania"}]}, {"year": "1238", "text": "King <PERSON> of Aragon conquers Valencia from the Moors. Shortly thereafter, he proclaims himself king of Valencia.", "html": "1238 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> conquers Valencia from the Moors. Shortly thereafter, he proclaims himself <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">king of Valencia</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> conquers Valencia from the Moors. Shortly thereafter, he proclaims himself <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">king of Valencia</a>.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Kingdom of Valencia", "link": "https://wikipedia.org/wiki/Kingdom_of_Valencia"}]}, {"year": "1322", "text": "<PERSON>, Holy Roman Emperor, defeats <PERSON> of Austria in the Battle of Mühldorf.", "html": "1322 - <PERSON>, Holy Roman Emperor, defeats <PERSON> of Austria in the <a href=\"https://wikipedia.org/wiki/Battle_of_M%C3%BChldorf\" title=\"Battle of Mühldorf\">Battle of Mühldorf</a>.", "no_year_html": "<PERSON>, Holy Roman Emperor, defeats <PERSON> of Austria in the <a href=\"https://wikipedia.org/wiki/Battle_of_M%C3%BChldorf\" title=\"Battle of Mühldorf\">Battle of Mühldorf</a>.", "links": [{"title": "Battle of Mühldorf", "link": "https://wikipedia.org/wiki/Battle_of_M%C3%BChldorf"}]}, {"year": "1538", "text": "Ottoman-Venetian War: The Ottoman Navy scores a decisive victory over a Holy League fleet in the Battle of Preveza.", "html": "1538 - Ottoman-Venetian War: The Ottoman Navy scores a decisive victory over a Holy League fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Preveza\" title=\"Battle of Preveza\">Battle of Preveza</a>.", "no_year_html": "Ottoman-Venetian War: The Ottoman Navy scores a decisive victory over a Holy League fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Preveza\" title=\"Battle of Preveza\">Battle of Preveza</a>.", "links": [{"title": "Battle of Preveza", "link": "https://wikipedia.org/wiki/Battle_of_Preveza"}]}, {"year": "1542", "text": "<PERSON> of Portugal arrives at what is now San Diego, California. He is the first European in California.", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Cabrillo\" title=\"<PERSON>\"><PERSON></a> of Portugal arrives at what is now San Diego, California. He is the first European in California.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_<PERSON>abrillo\" title=\"<PERSON>\"><PERSON></a> of Portugal arrives at what is now San Diego, California. He is the first European in California.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_<PERSON>abrillo"}]}, {"year": "1779", "text": "American Revolution: <PERSON> is elected President of the Continental Congress, succeeding <PERSON>.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>(Connecticut_politician)\" title=\"<PERSON> (Connecticut politician)\"><PERSON></a> is elected President of the Continental Congress, succeeding <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_(Connecticut_politician)\" title=\"<PERSON> (Connecticut politician)\"><PERSON></a> is elected President of the Continental Congress, succeeding <PERSON>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "<PERSON> (Connecticut politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Connecticut_politician)"}]}, {"year": "1781", "text": "American Revolution: French and American forces backed by a French fleet begin the siege of Yorktown.", "html": "1781 - American Revolution: French and American forces backed by a French fleet begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">siege of Yorktown</a>.", "no_year_html": "American Revolution: French and American forces backed by a French fleet begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">siege of Yorktown</a>.", "links": [{"title": "Siege of Yorktown", "link": "https://wikipedia.org/wiki/Siege_of_Yorktown"}]}, {"year": "1787", "text": "The Congress of the Confederation votes to send the newly written United States Constitution to the state legislatures for approval.", "html": "1787 - The Congress of the Confederation <a href=\"https://wikipedia.org/wiki/History_of_the_United_States_Constitution\" title=\"History of the United States Constitution\">votes</a> to send the newly written United States Constitution to the state legislatures for approval.", "no_year_html": "The Congress of the Confederation <a href=\"https://wikipedia.org/wiki/History_of_the_United_States_Constitution\" title=\"History of the United States Constitution\">votes</a> to send the newly written United States Constitution to the state legislatures for approval.", "links": [{"title": "History of the United States Constitution", "link": "https://wikipedia.org/wiki/History_of_the_United_States_Constitution"}]}, {"year": "1821", "text": "The Declaration of Independence of the Mexican Empire is drafted. It will be made public on 13 October.", "html": "1821 - The <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_the_Mexican_Empire\" class=\"mw-redirect\" title=\"Declaration of Independence of the Mexican Empire\">Declaration of Independence of the Mexican Empire</a> is drafted. It will be made public on 13 October.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_the_Mexican_Empire\" class=\"mw-redirect\" title=\"Declaration of Independence of the Mexican Empire\">Declaration of Independence of the Mexican Empire</a> is drafted. It will be made public on 13 October.", "links": [{"title": "Declaration of Independence of the Mexican Empire", "link": "https://wikipedia.org/wiki/Declaration_of_Independence_of_the_Mexican_Empire"}]}, {"year": "1844", "text": "<PERSON> of Sweden-Norway is crowned king of Sweden.", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a>-Norway is crowned king of Sweden.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a>-Norway is crowned king of Sweden.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1867", "text": "Toronto becomes the capital of Ontario, having also been the capital of Ontario's predecessors since 1796.", "html": "1867 - Toronto <a href=\"https://wikipedia.org/wiki/History_of_Toronto\" title=\"History of Toronto\">becomes the capital</a> of Ontario, having also been the capital of Ontario's predecessors since 1796.", "no_year_html": "Toronto <a href=\"https://wikipedia.org/wiki/History_of_Toronto\" title=\"History of Toronto\">becomes the capital</a> of Ontario, having also been the capital of Ontario's predecessors since 1796.", "links": [{"title": "History of Toronto", "link": "https://wikipedia.org/wiki/History_of_Toronto"}]}, {"year": "1868", "text": "The Battle of Alcolea causes Queen <PERSON> of Spain to flee to France.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Alcolea_(1868)\" title=\"Battle of Alcolea (1868)\">Battle of Alcolea</a> causes Queen <PERSON> of Spain to flee to France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Alcolea_(1868)\" title=\"Battle of Alcolea (1868)\">Battle of Alcolea</a> causes Queen <PERSON> of Spain to flee to France.", "links": [{"title": "Battle of Alcolea (1868)", "link": "https://wikipedia.org/wiki/Battle_of_Alcolea_(1868)"}]}, {"year": "1871", "text": "The Brazilian Parliament passes a law that frees all children thereafter born to slaves, and all government-owned slaves.", "html": "1871 - The Brazilian Parliament passes <a href=\"https://wikipedia.org/wiki/Rio_Branco_Law\" title=\"Rio Branco Law\">a law</a> that frees all children thereafter born to slaves, and all government-owned slaves.", "no_year_html": "The Brazilian Parliament passes <a href=\"https://wikipedia.org/wiki/Rio_Branco_Law\" title=\"Rio Branco Law\">a law</a> that frees all children thereafter born to slaves, and all government-owned slaves.", "links": [{"title": "Rio Branco Law", "link": "https://wikipedia.org/wiki/Rio_Branco_Law"}]}, {"year": "1889", "text": "The General Conference on Weights and Measures (CGPM) defines the length of a metre.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/General_Conference_on_Weights_and_Measures\" title=\"General Conference on Weights and Measures\">General Conference on Weights and Measures</a> (CGPM) defines the <a href=\"https://wikipedia.org/wiki/International_Prototype_Metre\" class=\"mw-redirect\" title=\"International Prototype Metre\">length of a metre</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Conference_on_Weights_and_Measures\" title=\"General Conference on Weights and Measures\">General Conference on Weights and Measures</a> (CGPM) defines the <a href=\"https://wikipedia.org/wiki/International_Prototype_Metre\" class=\"mw-redirect\" title=\"International Prototype Metre\">length of a metre</a>.", "links": [{"title": "General Conference on Weights and Measures", "link": "https://wikipedia.org/wiki/General_Conference_on_Weights_and_Measures"}, {"title": "International Prototype Metre", "link": "https://wikipedia.org/wiki/International_Prototype_Metre"}]}, {"year": "1892", "text": "The first night game for American football takes place in a contest between Wyoming Seminary and Mansfield State Normal.", "html": "1892 - The first night game for American football takes place <a href=\"https://wikipedia.org/wiki/1892_Wyoming_Seminary_vs._Mansfield_State_Normal_football_game\" class=\"mw-redirect\" title=\"1892 Wyoming Seminary vs. Mansfield State Normal football game\">in a contest</a> between Wyoming Seminary and Mansfield State Normal.", "no_year_html": "The first night game for American football takes place <a href=\"https://wikipedia.org/wiki/1892_Wyoming_Seminary_vs._Mansfield_State_Normal_football_game\" class=\"mw-redirect\" title=\"1892 Wyoming Seminary vs. Mansfield State Normal football game\">in a contest</a> between Wyoming Seminary and Mansfield State Normal.", "links": [{"title": "1892 Wyoming Seminary vs. Mansfield State Normal football game", "link": "https://wikipedia.org/wiki/1892_Wyoming_Seminary_vs._Mansfield_State_Normal_football_game"}]}, {"year": "1901", "text": "Philippine-American War: Filipino guerrillas kill more than forty American soldiers while losing 28 of their own.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>: Filipino guerrillas <a href=\"https://wikipedia.org/wiki/Balangiga_massacre\" title=\"Balangiga massacre\">kill</a> more than forty American soldiers while losing 28 of their own.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philippine%E2%80%93American_War\" title=\"Philippine-American War\">Philippine-American War</a>: Filipino guerrillas <a href=\"https://wikipedia.org/wiki/Balangiga_massacre\" title=\"Balangiga massacre\">kill</a> more than forty American soldiers while losing 28 of their own.", "links": [{"title": "Philippine-American War", "link": "https://wikipedia.org/wiki/Philippine%E2%80%93American_War"}, {"title": "Balangiga massacre", "link": "https://wikipedia.org/wiki/Balangiga_massacre"}]}, {"year": "1912", "text": "The Ulster Covenant is signed by some 500,000 Ulster Unionists in opposition to the Third Irish Home Rule Bill.", "html": "1912 - The <a href=\"https://wikipedia.org/wiki/Ulster_Covenant\" title=\"Ulster Covenant\">Ulster Covenant</a> is signed by some 500,000 Ulster Unionists in opposition to the Third Irish Home Rule Bill.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ulster_Covenant\" title=\"Ulster Covenant\">Ulster Covenant</a> is signed by some 500,000 Ulster Unionists in opposition to the Third Irish Home Rule Bill.", "links": [{"title": "Ulster Covenant", "link": "https://wikipedia.org/wiki/Ulster_Covenant"}]}, {"year": "1912", "text": "Corporal <PERSON> of the United States Army becomes the first enlisted man to die in an airplane crash.", "html": "1912 - Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the United States Army becomes the first enlisted man to die in an airplane crash.", "no_year_html": "Corporal <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the United States Army becomes the first enlisted man to die in an airplane crash.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "World War I: The Fifth Battle of Ypres begins.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Fifth_Battle_of_Ypres\" title=\"Fifth Battle of Ypres\">Fifth Battle of Ypres</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Fifth_Battle_of_Ypres\" title=\"Fifth Battle of Ypres\">Fifth Battle of Ypres</a> begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Fifth Battle of Ypres", "link": "https://wikipedia.org/wiki/Fifth_Battle_of_Ypres"}]}, {"year": "1919", "text": "Race riots begin in Omaha, Nebraska, United States.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Omaha_race_riot_of_1919\" title=\"Omaha race riot of 1919\">Race riots</a> begin in Omaha, Nebraska, United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Omaha_race_riot_of_1919\" title=\"Omaha race riot of 1919\">Race riots</a> begin in Omaha, Nebraska, United States.", "links": [{"title": "Omaha race riot of 1919", "link": "https://wikipedia.org/wiki/Omaha_race_riot_of_1919"}]}, {"year": "1924", "text": "The first aerial circumnavigation is completed by a team from the US Army.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/First_aerial_circumnavigation\" title=\"First aerial circumnavigation\">first aerial circumnavigation</a> is completed by a team from the US Army.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_aerial_circumnavigation\" title=\"First aerial circumnavigation\">first aerial circumnavigation</a> is completed by a team from the US Army.", "links": [{"title": "First aerial circumnavigation", "link": "https://wikipedia.org/wiki/First_aerial_circumnavigation"}]}, {"year": "1928", "text": "<PERSON> notices a bacteria-killing mold growing in his laboratory, discovering what later became known as penicillin.", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> notices a bacteria-killing mold growing in his laboratory, discovering what later became known as <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> notices a bacteria-killing mold growing in his laboratory, discovering what later became known as <a href=\"https://wikipedia.org/wiki/Penicillin\" title=\"Penicillin\">penicillin</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Penicillin", "link": "https://wikipedia.org/wiki/Penicillin"}]}, {"year": "1939", "text": "World War II: Nazi Germany and the Soviet Union agree on a division of Poland.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Nazi Germany and the Soviet Union <a href=\"https://wikipedia.org/wiki/German%E2%80%93Soviet_Treaty_of_Friendship,_Cooperation_and_Demarcation\" class=\"mw-redirect\" title=\"German-Soviet Treaty of Friendship, Cooperation and Demarcation\">agree</a> on a division of Poland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Nazi Germany and the Soviet Union <a href=\"https://wikipedia.org/wiki/German%E2%80%93Soviet_Treaty_of_Friendship,_Cooperation_and_Demarcation\" class=\"mw-redirect\" title=\"German-Soviet Treaty of Friendship, Cooperation and Demarcation\">agree</a> on a division of Poland.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "German-Soviet Treaty of Friendship, Cooperation and Demarcation", "link": "https://wikipedia.org/wiki/German%E2%80%93Soviet_Treaty_of_Friendship,_Cooperation_and_Demarcation"}]}, {"year": "1939", "text": "World War II: The siege of Warsaw comes to an end.", "html": "1939 - World War II: The <a href=\"https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)\" title=\"Siege of Warsaw (1939)\">siege of Warsaw</a> comes to an end.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)\" title=\"Siege of Warsaw (1939)\">siege of Warsaw</a> comes to an end.", "links": [{"title": "Siege of Warsaw (1939)", "link": "https://wikipedia.org/wiki/Siege_of_Warsaw_(1939)"}]}, {"year": "1941", "text": "World War II: The Drama uprising against the Bulgarian occupation in northern Greece begins.", "html": "1941 - World War II: The <a href=\"https://wikipedia.org/wiki/Drama_uprising\" title=\"Drama uprising\">Drama uprising</a> against the Bulgarian occupation in northern Greece begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Drama_uprising\" title=\"Drama uprising\">Drama uprising</a> against the Bulgarian occupation in northern Greece begins.", "links": [{"title": "Drama uprising", "link": "https://wikipedia.org/wiki/Drama_uprising"}]}, {"year": "1941", "text": "<PERSON> achieves a .406 batting average for the season, and becomes the last major league baseball player to bat .400 or better.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> achieves a .406 batting average for the season, and becomes the last major league baseball player to bat .400 or better.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> achieves a .406 batting average for the season, and becomes the last major league baseball player to bat .400 or better.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "World War II: Soviet Army troops liberate Klooga concentration camp in Estonia.", "html": "1944 - World War II: Soviet Army troops liberate <a href=\"https://wikipedia.org/wiki/Klooga_concentration_camp\" title=\"Klooga concentration camp\">Klooga concentration camp</a> in Estonia.", "no_year_html": "World War II: Soviet Army troops liberate <a href=\"https://wikipedia.org/wiki/Klooga_concentration_camp\" title=\"Klooga concentration camp\">Klooga concentration camp</a> in Estonia.", "links": [{"title": "Klooga concentration camp", "link": "https://wikipedia.org/wiki/Klooga_concentration_camp"}]}, {"year": "1951", "text": "CBS makes the first color televisions available for sale to the general public, but the product is discontinued less than a month later.", "html": "1951 - CBS makes the first <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color televisions</a> available for sale to the general public, but the product is discontinued less than a month later.", "no_year_html": "CBS makes the first <a href=\"https://wikipedia.org/wiki/Color_television\" title=\"Color television\">color televisions</a> available for sale to the general public, but the product is discontinued less than a month later.", "links": [{"title": "Color television", "link": "https://wikipedia.org/wiki/Color_television"}]}, {"year": "1958", "text": "<PERSON>, a Mexican tour guide in New Orleans, dies of injuries sustained in an incident of gay bashing.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a>, a Mexican tour guide in New Orleans, dies of injuries sustained in an incident of <a href=\"https://wikipedia.org/wiki/Gay_bashing\" title=\"Gay bashing\">gay bashing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>\" title=\"Killing of <PERSON>\"><PERSON></a>, a Mexican tour guide in New Orleans, dies of injuries sustained in an incident of <a href=\"https://wikipedia.org/wiki/Gay_bashing\" title=\"Gay bashing\">gay bashing</a>.", "links": [{"title": "Killing of <PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_<PERSON>_<PERSON>"}, {"title": "Gay bashing", "link": "https://wikipedia.org/wiki/Gay_bashing"}]}, {"year": "1961", "text": "A military coup in Damascus effectively ends the United Arab Republic, the union between Egypt and Syria.", "html": "1961 - A <a href=\"https://wikipedia.org/wiki/1961_Syrian_coup_d%27%C3%A9tat\" title=\"1961 Syrian coup d'état\">military coup</a> in Damascus effectively ends the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>, the union between Egypt and Syria.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1961_Syrian_coup_d%27%C3%A9tat\" title=\"1961 Syrian coup d'état\">military coup</a> in Damascus effectively ends the <a href=\"https://wikipedia.org/wiki/United_Arab_Republic\" title=\"United Arab Republic\">United Arab Republic</a>, the union between Egypt and Syria.", "links": [{"title": "1961 Syrian coup d'état", "link": "https://wikipedia.org/wiki/1961_Syrian_coup_d%27%C3%A9tat"}, {"title": "United Arab Republic", "link": "https://wikipedia.org/wiki/United_Arab_Republic"}]}, {"year": "1970", "text": "Egyptian President <PERSON><PERSON><PERSON> dies of a heart attack in Cairo.", "html": "1970 - Egyptian President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Gamal Abdel Na<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON><PERSON><PERSON> <PERSON>\">dies</a> of a heart attack in Cairo.", "no_year_html": "Egyptian President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Gamal Abdel Nasser\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON><PERSON><PERSON> <PERSON><PERSON>\">dies</a> of a heart attack in Cairo.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Death and state funeral of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "The ITT Building in New York City is bombed in protest at ITT's alleged involvement in the coup d'état in Chile.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/ITT_Corporation\" class=\"mw-redirect\" title=\"ITT Corporation\">ITT</a> Building in New York City is bombed in protest at ITT's alleged involvement in the <a href=\"https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat\" title=\"1973 Chilean coup d'état\">coup d'état in Chile</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/ITT_Corporation\" class=\"mw-redirect\" title=\"ITT Corporation\">ITT</a> Building in New York City is bombed in protest at ITT's alleged involvement in the <a href=\"https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat\" title=\"1973 Chilean coup d'état\">coup d'état in Chile</a>.", "links": [{"title": "ITT Corporation", "link": "https://wikipedia.org/wiki/ITT_Corporation"}, {"title": "1973 Chilean coup d'état", "link": "https://wikipedia.org/wiki/1973_Chilean_coup_d%27%C3%A9tat"}]}, {"year": "1975", "text": "The Spaghetti House siege, in which nine people are taken hostage, takes place in London.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Spaghetti_House_siege\" title=\"Spaghetti House siege\">Spaghetti House siege</a>, in which nine people are taken hostage, takes place in London.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Spaghetti_House_siege\" title=\"Spaghetti House siege\">Spaghetti House siege</a>, in which nine people are taken hostage, takes place in London.", "links": [{"title": "Spaghetti House siege", "link": "https://wikipedia.org/wiki/Spaghetti_House_siege"}]}, {"year": "1986", "text": "The Democratic Progressive Party becomes the first opposition party in Taiwan.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Democratic_Progressive_Party\" title=\"Democratic Progressive Party\">Democratic Progressive Party</a> becomes the first opposition party in Taiwan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Democratic_Progressive_Party\" title=\"Democratic Progressive Party\">Democratic Progressive Party</a> becomes the first opposition party in Taiwan.", "links": [{"title": "Democratic Progressive Party", "link": "https://wikipedia.org/wiki/Democratic_Progressive_Party"}]}, {"year": "1992", "text": "A Pakistan International Airlines flight crashes into a hill in Nepal, killing all 167 passengers and crew.", "html": "1992 - A Pakistan International Airlines flight <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_268\" title=\"Pakistan International Airlines Flight 268\">crashes</a> into a hill in Nepal, killing all 167 passengers and crew.", "no_year_html": "A Pakistan International Airlines flight <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_268\" title=\"Pakistan International Airlines Flight 268\">crashes</a> into a hill in Nepal, killing all 167 passengers and crew.", "links": [{"title": "Pakistan International Airlines Flight 268", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_268"}]}, {"year": "1994", "text": "The cruise ferry MS Estonia sinks in the Baltic Sea, killing 852 people.", "html": "1994 - The cruise ferry <a href=\"https://wikipedia.org/wiki/MS_Estonia\" title=\"MS Estonia\">MS <i>Estonia</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_the_MS_Estonia\" title=\"Sinking of the MS Estonia\">sinks in the Baltic Sea</a>, killing 852 people.", "no_year_html": "The cruise ferry <a href=\"https://wikipedia.org/wiki/MS_Estonia\" title=\"MS Estonia\">MS <i>Estonia</i></a> <a href=\"https://wikipedia.org/wiki/Sinking_of_the_MS_Estonia\" title=\"Sinking of the MS Estonia\">sinks in the Baltic Sea</a>, killing 852 people.", "links": [{"title": "MS Estonia", "link": "https://wikipedia.org/wiki/MS_Estonia"}, {"title": "Sinking of the MS Estonia", "link": "https://wikipedia.org/wiki/Sinking_of_the_MS_Estonia"}]}, {"year": "1995", "text": "<PERSON> and a group of mercenaries take the islands of the Comoros in a coup.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a group of mercenaries take the islands of the Comoros in a <a href=\"https://wikipedia.org/wiki/Operation_Azalee#Den<PERSON>_coup\" title=\"Operation Azalee\">coup</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and a group of mercenaries take the islands of the Comoros in a <a href=\"https://wikipedia.org/wiki/Operation_Azalee#Den<PERSON>_coup\" title=\"Operation Azalee\">coup</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Operation Azalee", "link": "https://wikipedia.org/wiki/Operation_Azalee#Denard_coup"}]}, {"year": "1995", "text": "Israeli Prime Minister <PERSON><PERSON><PERSON> and PLO Chairman <PERSON><PERSON> sign the Interim Agreement on the West Bank and the Gaza Strip.", "html": "1995 - Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and PLO Chairman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Oslo_II_Accord\" title=\"Oslo II Accord\">Interim Agreement on the West Bank and the Gaza Strip</a>.", "no_year_html": "Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and PLO Chairman <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sign the <a href=\"https://wikipedia.org/wiki/Oslo_II_Accord\" title=\"Oslo II Accord\">Interim Agreement on the West Bank and the Gaza Strip</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Oslo II Accord", "link": "https://wikipedia.org/wiki/Oslo_II_Accord"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> Inti<PERSON>da: <PERSON> visits Al-Aqsa Mosque known to Jews as the Temple Mount in Jerusalem.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Al-Aqsa_Intifada\" class=\"mw-redirect\" title=\"Al-Aqsa Intifada\">Al-Aqsa Intifada</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> visits Al-Aqsa Mosque known to Jews as the <a href=\"https://wikipedia.org/wiki/Temple_Mount\" title=\"Temple Mount\">Temple Mount</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Aqsa_Intifada\" class=\"mw-redirect\" title=\"Al-Aqsa Intifada\">Al-<PERSON>qsa Intifada</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> visits Al-Aqsa Mosque known to Jews as the <a href=\"https://wikipedia.org/wiki/Temple_Mount\" title=\"Temple Mount\">Temple Mount</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Al-Aqsa Intifada", "link": "https://wikipedia.org/wiki/Al-Aqsa_Intifada"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Temple Mount", "link": "https://wikipedia.org/wiki/Temple_Mount"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "2008", "text": "Falcon 1 becomes the first privately developed liquid-fuel ground-launched vehicle to put a payload into orbit by the RatSat mission.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Falcon_1\" title=\"Falcon 1\">Falcon 1</a> becomes the first privately developed liquid-fuel ground-launched vehicle to put a payload into orbit by the <a href=\"https://wikipedia.org/wiki/RatSat\" title=\"RatSat\">RatSat</a> mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falcon_1\" title=\"Falcon 1\">Falcon 1</a> becomes the first privately developed liquid-fuel ground-launched vehicle to put a payload into orbit by the <a href=\"https://wikipedia.org/wiki/RatSat\" title=\"RatSat\">RatSat</a> mission.", "links": [{"title": "Falcon 1", "link": "https://wikipedia.org/wiki/Falcon_1"}, {"title": "RatSat", "link": "https://wikipedia.org/wiki/RatSat"}]}, {"year": "2008", "text": "The Singapore Grand Prix is held as Formula One's inaugural night race, with <PERSON> winning the event. Almost a year later it was revealed that <PERSON>'s team-mate <PERSON> had been ordered to crash his car to help bring out the safety car and give <PERSON> the advantage and win.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/2008_Singapore_Grand_Prix\" title=\"2008 Singapore Grand Prix\">Singapore Grand Prix</a> is held as <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a>'s inaugural night race, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> winning the event. Almost a year later it was revealed that <PERSON>'s team-mate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a> <a href=\"https://wikipedia.org/wiki/Renault_Formula_One_crash_controversy\" title=\"Renault Formula One crash controversy\">had been ordered to crash his car</a> to help bring out the <a href=\"https://wikipedia.org/wiki/Safety_car\" title=\"Safety car\">safety car</a> and give <PERSON> the advantage and win.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2008_Singapore_Grand_Prix\" title=\"2008 Singapore Grand Prix\">Singapore Grand Prix</a> is held as <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a>'s inaugural night race, with <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> winning the event. Almost a year later it was revealed that <PERSON>'s team-mate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a> <a href=\"https://wikipedia.org/wiki/Renault_Formula_One_crash_controversy\" title=\"Renault Formula One crash controversy\">had been ordered to crash his car</a> to help bring out the <a href=\"https://wikipedia.org/wiki/Safety_car\" title=\"Safety car\">safety car</a> and give <PERSON> the advantage and win.", "links": [{"title": "2008 Singapore Grand Prix", "link": "https://wikipedia.org/wiki/2008_Singapore_Grand_Prix"}, {"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Renault Formula One crash controversy", "link": "https://wikipedia.org/wiki/Renault_Formula_One_crash_controversy"}, {"title": "Safety car", "link": "https://wikipedia.org/wiki/Safety_car"}]}, {"year": "2009", "text": "The military junta leading Guinea attacks a protest rally, killing or wounding 1,400 people.", "html": "2009 - The military junta leading Guinea <a href=\"https://wikipedia.org/wiki/2009_Guinean_protests\" title=\"2009 Guinean protests\">attacks a protest rally</a>, killing or wounding 1,400 people.", "no_year_html": "The military junta leading Guinea <a href=\"https://wikipedia.org/wiki/2009_Guinean_protests\" title=\"2009 Guinean protests\">attacks a protest rally</a>, killing or wounding 1,400 people.", "links": [{"title": "2009 Guinean protests", "link": "https://wikipedia.org/wiki/2009_Guinean_protests"}]}, {"year": "2012", "text": "Somali and African Union forces launch a coordinated assault on the Somali port of Kismayo to take back the city from al-Shabaab militants.", "html": "2012 - Somali and African Union forces launch a coordinated <a href=\"https://wikipedia.org/wiki/Battle_of_Kismayo_(2012)\" title=\"Battle of Kismayo (2012)\">assault</a> on the Somali port of Kismayo to take back the city from al-Shabaab militants.", "no_year_html": "Somali and African Union forces launch a coordinated <a href=\"https://wikipedia.org/wiki/Battle_of_Kismayo_(2012)\" title=\"Battle of Kismayo (2012)\">assault</a> on the Somali port of Kismayo to take back the city from al-Shabaab militants.", "links": [{"title": "Battle of Kismayo (2012)", "link": "https://wikipedia.org/wiki/Battle_of_Kismayo_(2012)"}]}, {"year": "2012", "text": "Sita Air Flight 601 crashes in Madhyapur Thimi, Nepal, killing all 19 passengers and crew.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Sita_Air_Flight_601\" title=\"Sita Air Flight 601\">Sita Air Flight 601</a> crashes in <a href=\"https://wikipedia.org/wiki/Madhyapur_Thimi\" title=\"Madhyapur Thimi\">Madhyapur Thimi</a>, Nepal, killing all 19 passengers and crew.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sita_Air_Flight_601\" title=\"Sita Air Flight 601\">Sita Air Flight 601</a> crashes in <a href=\"https://wikipedia.org/wiki/Madhyapur_Thimi\" title=\"Madhyapur Thimi\">Madhyapur Thimi</a>, Nepal, killing all 19 passengers and crew.", "links": [{"title": "Sita Air Flight 601", "link": "https://wikipedia.org/wiki/Sita_Air_Flight_601"}, {"title": "Madhyapur Thimi", "link": "https://wikipedia.org/wiki/Madhyapur_Thimi"}]}, {"year": "2014", "text": "The 2014 Hong Kong protests begin in response to restrictive political reforms imposed by the NPC in Beijing.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/2014_Hong_Kong_protests\" title=\"2014 Hong Kong protests\">2014 Hong Kong protests</a> begin in response to <a href=\"https://wikipedia.org/wiki/2014_NPCSC_Decision_on_Hong_Kong\" title=\"2014 NPCSC Decision on Hong Kong\">restrictive political reforms</a> imposed by the <a href=\"https://wikipedia.org/wiki/National_People%27s_Congress\" title=\"National People's Congress\">NPC</a> in Beijing.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2014_Hong_Kong_protests\" title=\"2014 Hong Kong protests\">2014 Hong Kong protests</a> begin in response to <a href=\"https://wikipedia.org/wiki/2014_NPCSC_Decision_on_Hong_Kong\" title=\"2014 NPCSC Decision on Hong Kong\">restrictive political reforms</a> imposed by the <a href=\"https://wikipedia.org/wiki/National_People%27s_Congress\" title=\"National People's Congress\">NPC</a> in Beijing.", "links": [{"title": "2014 Hong Kong protests", "link": "https://wikipedia.org/wiki/2014_Hong_Kong_protests"}, {"title": "2014 NPCSC Decision on Hong Kong", "link": "https://wikipedia.org/wiki/2014_NPCSC_Decision_on_Hong_Kong"}, {"title": "National People's Congress", "link": "https://wikipedia.org/wiki/National_People%27s_Congress"}]}, {"year": "2016", "text": "The 2016 South Australian blackout occurs, lasting up to three days in some areas.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/2016_South_Australian_blackout\" title=\"2016 South Australian blackout\">2016 South Australian blackout</a> occurs, lasting up to three days in some areas.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2016_South_Australian_blackout\" title=\"2016 South Australian blackout\">2016 South Australian blackout</a> occurs, lasting up to three days in some areas.", "links": [{"title": "2016 South Australian blackout", "link": "https://wikipedia.org/wiki/2016_South_Australian_blackout"}]}, {"year": "2018", "text": "The 7.5 Mw 2018 Sulawesi earthquake, which triggered a large tsunami, leaves 4,340 dead and 10,679 injured.", "html": "2018 - The 7.5 M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/2018_Sulawesi_earthquake_and_tsunami\" title=\"2018 Sulawesi earthquake and tsunami\">2018 Sulawesi earthquake</a>, which triggered a large tsunami, leaves 4,340 dead and 10,679 injured.", "no_year_html": "The 7.5 M<sub>w</sub> <a href=\"https://wikipedia.org/wiki/2018_Sulawesi_earthquake_and_tsunami\" title=\"2018 Sulawesi earthquake and tsunami\">2018 Sulawesi earthquake</a>, which triggered a large tsunami, leaves 4,340 dead and 10,679 injured.", "links": [{"title": "2018 Sulawesi earthquake and tsunami", "link": "https://wikipedia.org/wiki/2018_Sulawesi_earthquake_and_tsunami"}]}, {"year": "2022", "text": "Hurricane <PERSON> makes landfall in Cayo Costa State Park, Florida as a category four hurricane, killing 169 and doing $113 billion in damage, becoming Florida's costliest hurricane and the deadliest in 89 years.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Hurricane_Ian\" title=\"Hurricane Ian\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Cayo_Costa_State_Park\" title=\"Cayo Costa State Park\">Cayo Costa State Park</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">category four hurricane</a>, killing 169 and doing $113 billion in damage, becoming Florida's costliest hurricane and the deadliest in <a href=\"https://wikipedia.org/wiki/1935_Labor_Day_hurricane\" title=\"1935 Labor Day hurricane\">89 years</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Ian\" title=\"Hurricane Ian\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Cayo_Costa_State_Park\" title=\"Cayo Costa State Park\">Cayo Costa State Park</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a> as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">category four hurricane</a>, killing 169 and doing $113 billion in damage, becoming Florida's costliest hurricane and the deadliest in <a href=\"https://wikipedia.org/wiki/1935_Labor_Day_hurricane\" title=\"1935 Labor Day hurricane\">89 years</a>.", "links": [{"title": "Hurricane Ian", "link": "https://wikipedia.org/wiki/Hurricane_Ian"}, {"title": "Cayo Costa State Park", "link": "https://wikipedia.org/wiki/Cayo_Costa_State_Park"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Saffir-Simpson hurricane wind scale", "link": "https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_4"}, {"title": "1935 Labor Day hurricane", "link": "https://wikipedia.org/wiki/1935_Labor_Day_hurricane"}]}, {"year": "2023", "text": "The 2023 Rotterdam shootings occurred, during which two people were killed in a shooting and arson incident at a residence in Delfshaven, Rotterdam. Additionally, one person lost their life in a classroom at the Erasmus University Medical Center.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/2023_Rotterdam_shootings\" title=\"2023 Rotterdam shootings\">2023 Rotterdam shootings</a> occurred, during which two people were killed in a <a href=\"https://wikipedia.org/wiki/Shooting\" title=\"Shooting\">shooting</a> and <a href=\"https://wikipedia.org/wiki/Arson\" title=\"Arson\">arson</a> incident at a residence in <a href=\"https://wikipedia.org/wiki/Delfshaven\" title=\"Delfshaven\">Delfshaven</a>, Rotterdam. Additionally, one person lost their life in a classroom at the <a href=\"https://wikipedia.org/wiki/Erasmus_University_Medical_Center\" class=\"mw-redirect\" title=\"Erasmus University Medical Center\">Erasmus University Medical Center</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2023_Rotterdam_shootings\" title=\"2023 Rotterdam shootings\">2023 Rotterdam shootings</a> occurred, during which two people were killed in a <a href=\"https://wikipedia.org/wiki/Shooting\" title=\"Shooting\">shooting</a> and <a href=\"https://wikipedia.org/wiki/Arson\" title=\"Arson\">arson</a> incident at a residence in <a href=\"https://wikipedia.org/wiki/Delfshaven\" title=\"Delfshaven\">Delfshaven</a>, Rotterdam. Additionally, one person lost their life in a classroom at the <a href=\"https://wikipedia.org/wiki/Erasmus_University_Medical_Center\" class=\"mw-redirect\" title=\"Erasmus University Medical Center\">Erasmus University Medical Center</a>.", "links": [{"title": "2023 Rotterdam shootings", "link": "https://wikipedia.org/wiki/2023_Rotterdam_shootings"}, {"title": "Shooting", "link": "https://wikipedia.org/wiki/Shooting"}, {"title": "Arson", "link": "https://wikipedia.org/wiki/Arson"}, {"title": "Delfshaven", "link": "https://wikipedia.org/wiki/Delfshaven"}, {"title": "Erasmus University Medical Center", "link": "https://wikipedia.org/wiki/Erasmus_University_Medical_Center"}]}], "Births": [{"year": "551 BC", "text": "Confucius, Chinese teacher, editor, politician, and philosopher of the Spring and Autumn period of Chinese history. (d. 479 BC)", "html": "551 BC - 551 BC - <a href=\"https://wikipedia.org/wiki/Confucius\" title=\"Confucius\"><PERSON><PERSON><PERSON></a>, Chinese teacher, editor, politician, and philosopher of the Spring and Autumn period of Chinese history. (d. 479 BC)", "no_year_html": "551 BC - <a href=\"https://wikipedia.org/wiki/Confucius\" title=\"Confucius\"><PERSON><PERSON><PERSON></a>, Chinese teacher, editor, politician, and philosopher of the Spring and Autumn period of Chinese history. (d. 479 BC)", "links": [{"title": "<PERSON><PERSON>cius", "link": "https://wikipedia.org/wiki/Confucius"}]}, {"year": "616", "text": "<PERSON><PERSON><PERSON>, King of Caucasian Albania (d. 680)", "html": "616 - <a href=\"https://wikipedia.org/wiki/Javanshir\" class=\"mw-redirect\" title=\"Javanshir\"><PERSON><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Caucasian_Albania\" title=\"Caucasian Albania\">Caucasian Albania</a> (d. 680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javanshir\" class=\"mw-redirect\" title=\"Javanshir\"><PERSON><PERSON><PERSON></a>, King of <a href=\"https://wikipedia.org/wiki/Caucasian_Albania\" title=\"Caucasian Albania\">Caucasian Albania</a> (d. 680)", "links": [{"title": "Javanshir", "link": "https://wikipedia.org/wiki/Javanshir"}, {"title": "Caucasian Albania", "link": "https://wikipedia.org/wiki/Caucasian_Albania"}]}, {"year": "1494", "text": "<PERSON><PERSON><PERSON>, Italian poet and playwright (d. 1545)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nzuola\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and playwright (d. 1545)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian poet and playwright (d. 1545)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Firenzuola"}]}, {"year": "1555", "text": "<PERSON> d<PERSON>Auvergne, Marshal of France (d. 1623)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Bouillon\" title=\"<PERSON> d'Auvergne, Duke of Bouillon\"><PERSON> d'Auvergne</a>, Marshal of France (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Bouillon\" title=\"<PERSON> Tour d'Auvergne, Duke of Bouillon\"><PERSON> d'Auvergne</a>, Marshal of France (d. 1623)", "links": [{"title": "<PERSON> d'Auvergne, Duke of Bouillon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Tour_d%27A<PERSON><PERSON><PERSON>,_Duke_<PERSON>_Bouillon"}]}, {"year": "1573", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss physician (d. 1654)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss physician (d. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>_<PERSON>\" title=\"Th<PERSON>od<PERSON>\">T<PERSON><PERSON><PERSON><PERSON></a>, Swiss physician (d. 1654)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Th%C3%A9od<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON>, French astronomer and mathematician (d. 1694)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and mathematician (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and mathematician (d. 1694)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%C3%ABl_Bullialdus"}]}, {"year": "1681", "text": "<PERSON>, German composer, lexicographer, and diplomat (d. 1764)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, lexicographer, and diplomat (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer, lexicographer, and diplomat (d. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, 1st Baron <PERSON>, English politician, Secretary of State for the Southern Department (d. 1774)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1774)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1705", "text": "<PERSON>, German organist and composer (d. 1772)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "<PERSON>, 3rd Duke of Grafton, English academic and politician, Prime Minister of Great Britain (d. 1811)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton\" title=\"<PERSON>, 3rd Duke of Grafton\"><PERSON>, 3rd Duke of Grafton</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton\" title=\"<PERSON>, 3rd Duke of Grafton\"><PERSON>, 3rd Duke of Grafton</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1811)", "links": [{"title": "<PERSON>, 3rd Duke of Grafton", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Duke_of_Grafton"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1746", "text": "<PERSON>, English-Welsh philologist and scholar (d. 1794)", "html": "1746 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philologist)\" title=\"<PERSON> (philologist)\"><PERSON></a>, English-Welsh philologist and scholar (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philologist)\" title=\"<PERSON> (philologist)\"><PERSON></a>, English-Welsh philologist and scholar (d. 1794)", "links": [{"title": "<PERSON> (philologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(philologist)"}]}, {"year": "1765", "text": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg (d. 1814)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" title=\"<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg\" title=\"<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg\"><PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg</a> (d. 1814)", "links": [{"title": "<PERSON>, Duke of Schleswig-Holstein-Sonderburg-Augustenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Schleswig-Holstein-Sonderburg-Augustenburg"}]}, {"year": "1803", "text": "<PERSON><PERSON>, French archaeologist, historian, and author (d. 1870)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, French archaeologist, historian, and author (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e\" title=\"Prosper <PERSON>\"><PERSON><PERSON></a>, French archaeologist, historian, and author (d. 1870)", "links": [{"title": "Pro<PERSON>", "link": "https://wikipedia.org/wiki/Prosper_M%C3%A9rim%C3%A9e"}]}, {"year": "1809", "text": "<PERSON><PERSON>, American physician and botanist (d. 1899)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and botanist (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician and botanist (d. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON><PERSON><PERSON>, Spanish engineer and publisher (d. 1885)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish engineer and publisher (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish engineer and publisher (d. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Narc%C3%ADs_Monturiol"}]}, {"year": "1821", "text": "<PERSON>, American minister and politician (d. 1874)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and politician (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, French painter and educator (d. 1889)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, English poet and critic (d. 1897)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and critic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, English plumber, invented the ballcock (d. 1910)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English plumber, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>cock\" title=\"Ballcock\">ballcock</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English plumber, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>cock\" title=\"Ballcock\">ballcock</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French journalist, physician, and politician, 85th Prime Minister of France (d. 1929)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, physician, and politician, 85th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, physician, and politician, 85th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1844", "text": "<PERSON>, Scottish-New Zealand lawyer and politician, 13th Prime Minister of New Zealand (d. 1930)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1852", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (d. 1907)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1852", "text": "<PERSON><PERSON>, British astronomer and meteorologist (d. 1945)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British astronomer and meteorologist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British astronomer and meteorologist (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1852", "text": "<PERSON>, 1st Earl of Ypres, British Army officer (d. 1925)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Ypres\" title=\"<PERSON>, 1st Earl of Ypres\"><PERSON>, 1st Earl of Ypres</a>, British Army officer (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Ypres\" title=\"<PERSON>, 1st Earl of Ypres\"><PERSON>, 1st Earl of Ypres</a>, British Army officer (d. 1925)", "links": [{"title": "<PERSON>, 1st Earl of Ypres", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Ypres"}]}, {"year": "1856", "text": "<PERSON>, American author and educator (d. 1923)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin\" title=\"<PERSON> Wiggin\"><PERSON></a>, American author and educator (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin\" title=\"<PERSON> Wiggin\"><PERSON></a>, American author and educator (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>iggin"}]}, {"year": "1860", "text": "<PERSON>, French chemist and physicist (d. 1934)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON> of Orléans, queen consort of Portugal (d. 1951)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9<PERSON>_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON> of Orléans</a>, queen consort of Portugal (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9<PERSON>_of_Orl%C3%A9ans\" title=\"<PERSON><PERSON><PERSON> of Orléans\"><PERSON><PERSON><PERSON> of Orléans</a>, queen consort of Portugal (d. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON> of Orléans", "link": "https://wikipedia.org/wiki/Am%C3%A9lie_of_Orl%C3%A9ans"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, Japanese lawyer and politician, 35th Prime Minister of Japan (d. 1952)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Hiranuma_Kiichir%C5%8D\" title=\"Hiran<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hiranuma_Kiichir%C5%8D\" title=\"Hiran<PERSON> Kiichirō\"><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hiranuma_Kiichir%C5%8D"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1867", "text": "<PERSON>, American educator, school administrator, newspaper editor, poet, and essayist (d. 1896)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American educator, school administrator, newspaper editor, poet, and essayist (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American educator, school administrator, newspaper editor, poet, and essayist (d. 1896)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON>, French composer and critic (d. 1958)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Florent_<PERSON>\" title=\"Florent <PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_<PERSON>\" title=\"Florent <PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and critic (d. 1958)", "links": [{"title": "Florent <PERSON>", "link": "https://wikipedia.org/wiki/Florent_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American boxer and promoter (d. 1940)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and promoter (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer and promoter (d. 1940)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "1878", "text": "<PERSON>, American swimmer and water polo player (d. 1962)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American actor (d. 1950)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cordoba\"><PERSON></a>, American actor (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cordoba\"><PERSON></a>, American actor (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Estonian organist and composer (d. 1963)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Mart_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian organist and composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart_Saar\" title=\"Mart <PERSON>\"><PERSON></a>, Estonian organist and composer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart_Saar"}]}, {"year": "1883", "text": "<PERSON>, French priest, hermit and ethnologist (d. 1959)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French priest, hermit and ethnologist (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French priest, hermit and ethnologist (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1885", "text": "<PERSON>, Finnish wrestler, coach, and referee (d. 1974)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4re\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler, coach, and referee (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4re\" title=\"<PERSON>\"><PERSON></a>, Finnish wrestler, coach, and referee (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Emil_V%C3%A4re"}]}, {"year": "1887", "text": "<PERSON>, American businessman, 5th President of the International Olympic Committee (d. 1975)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brundage\"><PERSON></a>, American businessman, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brundage\"><PERSON></a>, American businessman, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>run<PERSON>ge"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "1889", "text": "<PERSON>, American baseball player and coach (d. 1973)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Australian electrical engineer (d. 1982)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Florence_<PERSON>_<PERSON>\" title=\"Florence Violet McKenzie\"><PERSON></a>, Australian electrical engineer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_<PERSON>_<PERSON>\" title=\"Florence Violet McKenzie\"><PERSON></a>, Australian electrical engineer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Violet_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American playwright (d. 1967)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Austrian mathematician (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mathematician (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Greek author, poet, and playwright (d. 1984)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author, poet, and playwright (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author, poet, and playwright (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, German Nazi physician (d. 1957)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> physician (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Nazi\" class=\"mw-redirect\" title=\"Nazi\">Nazi</a> physician (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nazi", "link": "https://wikipedia.org/wiki/Nazi"}]}, {"year": "1900", "text": "<PERSON>, American socialite, fought as part of the French Resistance during WWII (d. 1951)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, fought as part of the <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> during WWII (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, fought as part of the <a href=\"https://wikipedia.org/wiki/French_Resistance\" title=\"French Resistance\">French Resistance</a> during WWII (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "French Resistance", "link": "https://wikipedia.org/wiki/French_Resistance"}]}, {"year": "1901", "text": "<PERSON>, American broadcaster, founded CBS (d. 1990)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "CBS", "link": "https://wikipedia.org/wiki/CBS"}]}, {"year": "1901", "text": "<PERSON>, American television host (d. 1974)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American general (d. 1988)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Haywood_S._Hansell\" title=\"Haywood S. Hansell\"><PERSON><PERSON> <PERSON></a>, American general (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haywood_S._Hansell\" title=\"Haywood S. Hansell\"><PERSON><PERSON> <PERSON></a>, American general (d. 1988)", "links": [{"title": "Haywood S. <PERSON>", "link": "https://wikipedia.org/wiki/Haywood_S._Hans<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, German boxer (d. 2005)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German boxer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German boxer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Finnish gymnast and physician (d. 1997)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(gymnast)\" title=\"<PERSON><PERSON><PERSON> (gymnast)\"><PERSON><PERSON><PERSON></a>, Finnish gymnast and physician (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(gymnast)\" title=\"<PERSON><PERSON><PERSON> (gymnast)\"><PERSON><PERSON><PERSON></a>, Finnish gymnast and physician (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON> (gymnast)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(gymnast)"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Indian activist (d. 1931)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American author and illustrator (d. 1979)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1979)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_Capp"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino lawyer and politician, 9th President of the Philippines (d. 1997)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Diosdado_Macapagal\" title=\"Diosdado Macapagal\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diosdado_Macapagal\" title=\"Diosdado Macapagal\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1997)", "links": [{"title": "Diosdado Macapagal", "link": "https://wikipedia.org/wiki/Diosdado_Macapagal"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino lawyer and politician (d. 1942)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Wenceslao_Vinzons\" title=\"Wenceslao Vin<PERSON>s\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wenceslao_<PERSON>s\" title=\"Wenceslao Vin<PERSON>s\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (d. 1942)", "links": [{"title": "Wenceslao <PERSON>s", "link": "https://wikipedia.org/wiki/Wenceslao_Vinzons"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss illustrator (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>vater\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>vate<PERSON>\"><PERSON><PERSON></a>, Swiss illustrator (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>r\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>vate<PERSON>\"><PERSON><PERSON></a>, Swiss illustrator (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American tennis player (d. 1990)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Austrian-American refugee and singer (d. 2014)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American refugee and singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American refugee and singer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American spy (d. 1953)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American spy (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English-Australian actor (d. 1977)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actor (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Ukrainian-Russian ballerina and educator (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Ukrainian-Russian ballerina and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)\" title=\"<PERSON> (dancer)\"><PERSON></a>, Ukrainian-Russian ballerina and educator (d. 2008)", "links": [{"title": "<PERSON> (dancer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(dancer)"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Singaporean judge (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Wee Chong Jin\"><PERSON><PERSON></a>, Singaporean judge (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Wee Chong Jin\"><PERSON><PERSON><PERSON></a>, Singaporean judge (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/We<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Argentinian footballer and manager (d. 1983)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81ngel_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81ngel_<PERSON>runa"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American actress (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Norwegian actress (d. 2014)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Liv_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liv_Dom<PERSON>nes"}]}, {"year": "1922", "text": "<PERSON>, American sportscaster (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Prime Minister of Suriname (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Suriname\" class=\"mw-redirect\" title=\"Prime Minister of Suriname\">Prime Minister of Suriname</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Suriname\" class=\"mw-redirect\" title=\"Prime Minister of Suriname\">Prime Minister of Suriname</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Suriname", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Suriname"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American singer, poet, and writer (d. 2010)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, poet, and writer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, poet, and writer (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, 9th Duke of Buccleuch, Scottish captain and politician, Lord Lieutenant of Selkirkshire (d. 2007)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Duke_of_Buccleuch\" title=\"<PERSON>, 9th Duke of Buccleuch\"><PERSON>, 9th Duke of Buccleuch</a>, Scottish captain and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Selkirkshire\" title=\"Lord Lieutenant of Selkirkshire\">Lord Lieutenant of Selkirkshire</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Duke_of_Buccleuch\" title=\"<PERSON>, 9th Duke of Buccleuch\"><PERSON>, 9th Duke of Buccleuch</a>, Scottish captain and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Selkirkshire\" title=\"Lord Lieutenant of Selkirkshire\">Lord Lieutenant of Selkirkshire</a> (d. 2007)", "links": [{"title": "<PERSON>, 9th Duke of Buccleuch", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Duke_of_Buccleuch"}, {"title": "Lord Lieutenant of Selkirkshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Selkirkshire"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2012)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1924", "text": "<PERSON>, Russian-Swiss viola player and conductor (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss viola player and conductor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Swiss viola player and conductor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Italian-French actor and singer (d. 1996)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor and singer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-French actor and singer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American computer scientist, founded the CRAY Computer Company (d. 1996)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded the <a href=\"https://wikipedia.org/wiki/Cray\" title=\"Cray\">CRAY Computer Company</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, founded the <a href=\"https://wikipedia.org/wiki/Cray\" title=\"<PERSON>ray\">CRAY Computer Company</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cray"}]}, {"year": "1925", "text": "<PERSON>, South African composer (d. 1991)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African composer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African composer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American physicist and mathematician (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American soldier, comedian, and author (d. 1998)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, comedian, and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, comedian, and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American singer (d. 2009)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Indian playback singer and composer (d. 2022)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer and composer (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American country music singer-songwriter (d. 2000)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American country music singer-songwriter (d. 2000)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, American sociologist, author, and academic (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>\" title=\"I<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sociologist, author, and academic (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>\" title=\"I<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sociologist, author, and academic (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Immanu<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Scottish screenwriter and producer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Chilean singer-songwriter, poet, and director (d. 1973)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean singer-songwriter, poet, and director (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean singer-songwriter, poet, and director (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Jara"}]}, {"year": "1933", "text": "<PERSON>, English soldier and politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Spanish sculptor and educator (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and educator (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and educator (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter  (d. 2011)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Country%22_Math<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"Country\" Mathis'><PERSON> \"Country\" <PERSON></a>, American singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Country%22_Math<PERSON>\" class=\"mw-redirect\" title='<PERSON> \"Country\" Mathis'><PERSON> \"Country\" <PERSON></a>, American singer-songwriter (d. 2011)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Country%22_Mathis"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, French actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian golfer", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Baron <PERSON> of Chiswick, English diplomat, British Permanent Representative to the United Nations", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Chiswick\" title=\"<PERSON>, Baron <PERSON> of Chiswick\"><PERSON>, Baron <PERSON> of Chiswick</a>, English diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Permanent Representative to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Chiswick\" title=\"<PERSON>, Baron <PERSON> of Chiswick\"><PERSON>, Baron <PERSON> of Chiswick</a>, English diplomat, <a href=\"https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations\" title=\"Permanent Representative of the United Kingdom to the United Nations\">British Permanent Representative to the United Nations</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Chiswick", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Chiswick"}, {"title": "Permanent Representative of the United Kingdom to the United Nations", "link": "https://wikipedia.org/wiki/Permanent_Representative_of_the_United_Kingdom_to_the_United_Nations"}]}, {"year": "1935", "text": "<PERSON>, English actor (d. 1991)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, American guitarist, invented the Chapman Stick (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_Stick\" title=\"Chapman Stick\">Chapman Stick</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American guitarist, invented the <a href=\"https://wikipedia.org/wiki/Chapman_Stick\" title=\"Chapman Stick\">Chapman Stick</a> (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> Stick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Australian rugby league player (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Dutch television actor (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch television actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch television actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>ers"}]}, {"year": "1937", "text": "<PERSON>, English trade union leader and politician (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trade union leader and politician (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>,  American country music songwriter and record producer (d. 2007)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter and record producer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music songwriter and record producer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and producer (d. 2015)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American biologist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American philosopher and academic (d. 2001)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, American philosopher and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, American philosopher and academic (d. 2001)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1941", "text": "<PERSON>, German lawyer and politician, Minister President of Bavaria", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister President of Bavaria\">Minister President of Bavaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Bavaria\" class=\"mw-redirect\" title=\"Minister President of Bavaria\">Minister President of Bavaria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister President of Bavaria", "link": "https://wikipedia.org/wiki/Minister_President_of_Bavaria"}]}, {"year": "1941", "text": "<PERSON>, American football player (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 1999)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9menti\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9menti\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_Cl%C3%A9menti"}]}, {"year": "1942", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter and guitarist (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Little_<PERSON>%22_Forehand\" title='<PERSON> \"<PERSON> Buster\" Forehand'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Little_<PERSON>%22_Forehand\" title='<PERSON> \"<PERSON> Buster\" Forehand'><PERSON> \"<PERSON>\" <PERSON>ehan<PERSON></a>, American singer-songwriter and guitarist (d. 2006)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>ehand", "link": "https://wikipedia.org/wiki/<PERSON>_%22Little_<PERSON>%22_Forehand"}]}, {"year": "1943", "text": "<PERSON>, American businessman", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American novelist, playwright, and critic (d. 2006)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_Trow\" title=\"<PERSON>. <PERSON>\"><PERSON></a>, American novelist, playwright, and critic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>row\" title=\"<PERSON>. <PERSON>\"><PERSON></a>, American novelist, playwright, and critic (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Trow"}]}, {"year": "1943", "text": "<PERSON>, German-Canadian bass player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Nicholas\"><PERSON></a>, German-Canadian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nick_<PERSON>._Nicholas\" title=\"Nick <PERSON>. Nicholas\"><PERSON></a>, German-Canadian bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nick_St._Nicholas"}]}, {"year": "1944", "text": "<PERSON>, American golfer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American journalist and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, French skier", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Greek singer-songwriter and journalist (d. 2011)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Man<PERSON>_Rasoulis\" title=\"<PERSON><PERSON> Rasoulis\"><PERSON><PERSON></a>, Greek singer-songwriter and journalist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_Rasoulis\" title=\"<PERSON><PERSON> Rasoulis\"><PERSON><PERSON></a>, Greek singer-songwriter and journalist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Man<PERSON>_Ra<PERSON>ulis"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Japanese activist, founded the Japanese Red Army", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese activist, founded the <a href=\"https://wikipedia.org/wiki/Japanese_Red_Army\" title=\"Japanese Red Army\">Japanese Red Army</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese activist, founded the <a href=\"https://wikipedia.org/wiki/Japanese_Red_Army\" title=\"Japanese Red Army\">Japanese Red Army</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Japanese Red Army", "link": "https://wikipedia.org/wiki/Japanese_Red_Army"}]}, {"year": "1946", "text": "<PERSON>, English journalist and author", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Indian-Pakistani cricketer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1946)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (cricketer, born 1946)\"><PERSON><PERSON></a>, Indian-Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1946)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (cricketer, born 1946)\"><PERSON><PERSON></a>, Indian-Pakistani cricketer", "links": [{"title": "<PERSON><PERSON> (cricketer, born 1946)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer,_born_1946)"}]}, {"year": "1947", "text": "<PERSON>, Australian journalist and politician, 37th Australian Minister of Foreign Affairs", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 37th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and politician, 37th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)\" title=\"Minister for Foreign Affairs (Australia)\">Australian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Australia)"}]}, {"year": "1947", "text": "<PERSON> <PERSON><PERSON>, Bangladeshi politician, 10th Prime Minister of Bangladesh", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a>, Bangladeshi politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sheikh_<PERSON><PERSON>\" title=\"Sheikh <PERSON><PERSON>\">Sheikh <PERSON><PERSON></a>, Bangladeshi politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh\" title=\"Prime Minister of Bangladesh\">Prime Minister of Bangladesh</a>", "links": [{"title": "Sheikh <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of Bangladesh", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Bangladesh"}]}, {"year": "1947", "text": "<PERSON>, English journalist and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, English journalist and academic", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American mathematician and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English drummer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1950", "text": "<PERSON>, American author and philosopher", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and philosopher", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American novelist, director, and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish singer-songwriter and musician (d. 2015)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter and musician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Scottish singer-songwriter and musician (d. 2015)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1952", "text": "<PERSON>, American satirical novelist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American satirical novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American satirical novelist", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_(novelist)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek basketball player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Efthim<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON>him<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Efthim<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch model and actress (d. 2012)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch model and actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch model and actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English drummer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Liechtensteiner educator and politician, 11th Prime Minister of Liechtenstein", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liechtensteiner educator and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Liechtensteiner educator and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein\" class=\"mw-redirect\" title=\"Prime Minister of Liechtenstein\">Prime Minister of Liechtenstein</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Liechtenstein", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Liechtenstein"}]}, {"year": "1954", "text": "<PERSON>, American football player and politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American guitarist and songwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1954", "text": "<PERSON>, English rugby player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1954)\" title=\"<PERSON> (rugby union, born 1954)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1954)\" title=\"<PERSON> (rugby union, born 1954)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union, born 1954)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(rugby_union,_born_1954)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Swedish politician and diplomat, 42nd Swedish Minister for Foreign Affairs", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Margot_<PERSON>tr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician and diplomat, 42nd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tr%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish politician and diplomat, 42nd <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)\" title=\"Minister for Foreign Affairs (Sweden)\">Swedish Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Margot_Wallstr%C3%B6m"}, {"title": "Minister for Foreign Affairs (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Sweden)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian sociologist and politician, 15th Canadian Minister of the Environment", "html": "1955 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian sociologist and politician, 15th <a href=\"https://wikipedia.org/wiki/Minister_of_the_Environment_(Canada)\" class=\"mw-redirect\" title=\"Minister of the Environment (Canada)\">Canadian Minister of the Environment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian sociologist and politician, 15th <a href=\"https://wikipedia.org/wiki/Minister_of_the_Environment_(Canada)\" class=\"mw-redirect\" title=\"Minister of the Environment (Canada)\">Canadian Minister of the Environment</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9phane_<PERSON>"}, {"title": "Minister of the Environment (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_the_Environment_(Canada)"}]}, {"year": "1955", "text": "<PERSON>, Xhosa sangoma and HIV activist from South Africa", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Man<PERSON>\" title=\"<PERSON> Manci\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Xhosa_people\" title=\"Xhosa people\">Xhosa</a> <a href=\"https://wikipedia.org/wiki/Sangoma\" class=\"mw-redirect\" title=\"Sangoma\">sangoma</a> and <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a> <a href=\"https://wikipedia.org/wiki/Activism\" title=\"Activism\">activist</a> from <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Manci\" title=\"<PERSON> Manci\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Xhosa_people\" title=\"Xhosa people\">Xhosa</a> <a href=\"https://wikipedia.org/wiki/Sangoma\" class=\"mw-redirect\" title=\"Sangoma\">sangoma</a> and <a href=\"https://wikipedia.org/wiki/HIV\" title=\"HIV\">HIV</a> <a href=\"https://wikipedia.org/wiki/Activism\" title=\"Activism\">activist</a> from <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Xhosa people", "link": "https://wikipedia.org/wiki/Xhosa_people"}, {"title": "Sangoma", "link": "https://wikipedia.org/wiki/Sangoma"}, {"title": "HIV", "link": "https://wikipedia.org/wiki/HIV"}, {"title": "Activism", "link": "https://wikipedia.org/wiki/Activism"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}]}, {"year": "1955", "text": "<PERSON>, American pianist (d. 1998)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American politician and physician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and physician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American artist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian race car driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian footballer and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American golfer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hammerschlag"}]}, {"year": "1960", "text": "<PERSON>, Trinidadian cricketer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Logie\" title=\"Gus Logie\"><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gus_Logie\" title=\"Gus Logie\"><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gus_Logie"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, <PERSON> of Bradford, English politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Bradford\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of Bradford\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Bradford</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_Bradford\" title=\"<PERSON><PERSON><PERSON>, <PERSON> of Bradford\"><PERSON><PERSON><PERSON>, Baron <PERSON> of Bradford</a>, English politician", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON> of Bradford", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Bradford"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Filipino archbishop", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates <PERSON>\"><PERSON><PERSON></a>, Filipino archbishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates <PERSON>\"><PERSON><PERSON></a>, Filipino archbishop", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Socrates_<PERSON>gas"}]}, {"year": "1961", "text": "<PERSON>, English lawyer and politician, Minister for Sport and the Olympics", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1961", "text": "<PERSON>, American actor and singer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American lawyer and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81nanakoa\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81nanak<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%81nanakoa"}]}, {"year": "1961", "text": "<PERSON>, American tennis player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American journalist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer_and_editor)\" class=\"mw-redirect\" title=\"<PERSON> (writer and editor)\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer_and_editor)\" class=\"mw-redirect\" title=\"<PERSON> (writer and editor)\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON> (writer and editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer_and_editor)"}]}, {"year": "1963", "text": "<PERSON>, American wrestler and martial artist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, French race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/%C3%89rik_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89rik_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89rik_Comas"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American voice actor, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Argentinian footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_manager)\" class=\"mw-redirect\" title=\"<PERSON> (football manager)\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_manager)\" class=\"mw-redirect\" title=\"<PERSON> (football manager)\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON> (football manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(football_manager)"}]}, {"year": "1964", "text": "<PERSON>, Scottish race car driver", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American comedian, actress, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian, actress, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Latvian lawyer and politician (d. 2012)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Roze\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Roze\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Latvian lawyer and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81rti%C5%86%C5%A1_Roze"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, the Prince of Rap, American rapper (d. 2023)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/B.G.,_the_Prince_of_Rap\" title=\"<PERSON><PERSON><PERSON><PERSON>, the Prince of Rap\"><PERSON><PERSON><PERSON><PERSON>, the Prince of Rap</a>, American rapper (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B.G.,_the_Prince_of_Rap\" title=\"<PERSON><PERSON><PERSON><PERSON>, the Prince of Rap\"><PERSON><PERSON><PERSON><PERSON>, the Prince of Rap</a>, American rapper (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, the Prince of Rap", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_the_Prince_of_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player (d. 2013)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (d. 2013)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Cuban-American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Maria_Canals-Barrera\" title=\"Maria Canals-Barrera\"><PERSON>s-Barr<PERSON></a>, Cuban-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_Canals-Barrera\" title=\"Maria Canals-Barrera\"><PERSON></a>, Cuban-American actress", "links": [{"title": "Maria Canals-Barrera", "link": "https://wikipedia.org/wiki/Maria_Canals-Barrera"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Indian director, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jagan<PERSON>dh\" title=\"Puri Jagannadh\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Puri Jagannadh\"><PERSON><PERSON></a>, Indian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Puri_Jagan<PERSON>dh"}]}, {"year": "1967", "text": "<PERSON>, American actress", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "Mira <PERSON>", "link": "https://wikipedia.org/wiki/Mira_Sorvino"}]}, {"year": "1967", "text": "<PERSON>, American actress and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Zappa\" title=\"<PERSON> Zappa\"><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Zappa\" title=\"<PERSON> Zappa\"><PERSON></a>, American actress and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Z<PERSON>pa"}]}, {"year": "1968", "text": "<PERSON><PERSON>, South African boxer and mixed martial artist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African boxer and mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African boxer and mixed martial artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Finnish race car driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Mi<PERSON>_H%C3%A4kkinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mi<PERSON>_H%C3%A4kkinen\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mika_H%C3%A4kkinen"}]}, {"year": "1968", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist (d. 2011)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>enan"}]}, {"year": "1968", "text": "<PERSON>, American R&B singer-songwriter and actor (d. 2008)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actor (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter and actor (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American race car driver (d. 1990)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English-Australian actress and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, electronic music producer and DJ", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, electronic music producer and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, electronic music producer and DJ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch decathlete", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American author and memoirist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Piper_Kerman\" title=\"Piper Kerman\"><PERSON></a>, American author and memoirist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piper_Kerman\" title=\"Piper Kerman\"><PERSON></a>, American author and memoirist", "links": [{"title": "Piper Kerman", "link": "https://wikipedia.org/wiki/Piper_Kerman"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Canadian singer-songwriter and keyboard player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Canadian singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Canadian singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, German race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Scottish politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Japanese tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Krumm\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>rumm\"><PERSON><PERSON></a>, Japanese tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Krumm\" class=\"mw-redirect\" title=\"<PERSON><PERSON>-<PERSON>rumm\"><PERSON><PERSON></a>, Japanese tennis player", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Date-Krumm"}]}, {"year": "1970", "text": "<PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Brazilian race car driver", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Salle<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English lawyer and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, South African rugby player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American model and dancer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American ice hockey player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Italian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Russian swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Finnish footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Australian rugby league player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian cricketer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American journalist and academic (d. 2013)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jordan\"><PERSON><PERSON></a>, American journalist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Jordan\"><PERSON><PERSON></a>, American journalist and academic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isamu_Jordan"}]}, {"year": "1975", "text": "<PERSON>, Russian-American swimmer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Russian mixed martial artist and politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mixed martial artist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian mixed martial artist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wells\" title=\"Bon<PERSON> Wells\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonzi_Wells\" title=\"Bon<PERSON> Wells\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wells"}]}, {"year": "1977", "text": "<PERSON>, South Korean golfer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>i\" title=\"<PERSON> Se<PERSON>ri\"><PERSON></a>, South Korean golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>i\" title=\"Pak Se-ri\"><PERSON></a>, South Korean golfer", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/Pak_Se-ri"}]}, {"year": "1977", "text": "<PERSON>, American rapper", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Young_Jeezy\" class=\"mw-redirect\" title=\"Young Jeezy\"><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Young_Jeezy\" class=\"mw-redirect\" title=\"Young Jeezy\"><PERSON></a>, American rapper", "links": [{"title": "<PERSON>ezy", "link": "https://wikipedia.org/wiki/Young_<PERSON>ezy"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Australian cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American skateboarder, actor, and stuntman", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ba<PERSON>\"><PERSON><PERSON></a>, American skateboarder, actor, and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American skateboarder, actor, and stuntman", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American-Greek rapper and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Greek rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Greek rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>san"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Parmer\" title=\"Marlon Parmer\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Parmer\" title=\"Marlon Parmer\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1981", "text": "<PERSON>, American pianist and composer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American pianist and composer", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>(pianist)"}]}, {"year": "1981", "text": "<PERSON>, Argentine footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Calder%C3%B3n_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Calder%C3%B3n_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Calder%C3%B3n_(basketball)"}]}, {"year": "1981", "text": "<PERSON>, Ecuadorian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nton"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Brazilian bass player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Ira<PERSON>ma_Trevisan\" title=\"Ira<PERSON>ma Trevisan\"><PERSON><PERSON><PERSON></a>, Brazilian bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ira<PERSON><PERSON>_Trevisan\" title=\"Iracema Trevisan\"><PERSON><PERSON><PERSON>re<PERSON></a>, Brazilian bass player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ma_Trevisan"}]}, {"year": "1982", "text": "<PERSON>, Russian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian target shooter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ab<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian target shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"A<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian target shooter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ab<PERSON><PERSON>_<PERSON>dra"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player (d. 2018)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Indian actor and director", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, French singer-songwriter and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Emeka_Okafor\" title=\"Emeka Okafor\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emeka_Okafor\" title=\"Emeka Okafor\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "Emeka Okafor", "link": "https://wikipedia.org/wiki/Emeka_Okafor"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Estonian skier", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Brazilian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%A3o\" title=\"<PERSON>\"><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anderson_Varej%C3%A3o"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>(musician)"}]}, {"year": "1983", "text": "<PERSON>, English footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian singer-songwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Kosovan-Swedish boxer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan-Swedish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kosovan-Swedish boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter and dancer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, South Korean singer-songwriter and dancer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Shindong\" title=\"Shindong\"><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shindong\" title=\"Shindong\"><PERSON><PERSON></a>, South Korean singer-songwriter and dancer", "links": [{"title": "Shindong", "link": "https://wikipedia.org/wiki/Shindong"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Russian-English violinist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-English violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Mexican footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Guardado\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Guardado\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Guardado"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner (d. 2013)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Meskerem_Legesse\" title=\"Meskerem Legesse\">Me<PERSON><PERSON></a>, Ethiopian runner (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meskerem_Legesse\" title=\"Meskerem Legesse\"><PERSON><PERSON><PERSON></a>, Ethiopian runner (d. 2013)", "links": [{"title": "Meskerem Legesse", "link": "https://wikipedia.org/wiki/Meskerem_Legesse"}]}, {"year": "1986", "text": "<PERSON>, American musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON>,  American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, English violinist", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Chlo%C3%AB_Hanslip\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chlo%C3%AB_Hanslip\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English violinist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chlo%C3%AB_Hanslip"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Estonian high jumper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Viktoria_Leks\" title=\"Viktoria Le<PERSON>\"><PERSON><PERSON></a>, Estonian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viktoria_Leks\" title=\"Viktoria Le<PERSON>\"><PERSON><PERSON></a>, Estonian high jumper", "links": [{"title": "Viktoria Leks", "link": "https://wikipedia.org/wiki/Viktoria_Leks"}]}, {"year": "1988", "text": "<PERSON>, Croatian tennis player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Cili%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C4%8Cili%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marin_%C4%8Cili%C4%87"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Dutch singer-songwriter", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Esm%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esm%C3%A9e_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Esm%C3%A9e_<PERSON>ters"}]}, {"year": "1988", "text": "<PERSON>, American wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress, model, television host, and beauty pageant titleholder", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, television host, and beauty pageant titleholder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, model, television host, and beauty pageant titleholder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actress, model, and fashion designer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, model, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, model, and fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, French DJ and electronic musician", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Worakls\" title=\"Worakls\"><PERSON><PERSON><PERSON><PERSON></a>, French DJ and electronic musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Worakls\" title=\"Worakls\"><PERSON><PERSON><PERSON><PERSON></a>, French DJ and electronic musician", "links": [{"title": "Worakls", "link": "https://wikipedia.org/wiki/Worakls"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Fla_B%C3%BCy%C3%BCkak%C3%A7ay\" title=\"Çağla Büyükakçay\"><PERSON><PERSON><PERSON><PERSON> Büyükakçay</a>, Turkish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%87a%C4%9Fla_B%C3%BCy%C3%BCkak%C3%A7ay\" title=\"Çağla Büyükakçay\"><PERSON>ağ<PERSON> Büyükakçay</a>, Turkish tennis player", "links": [{"title": "Çağla Büyükakçay", "link": "https://wikipedia.org/wiki/%C3%87a%C4%9Fla_B%C3%BCy%C3%BCkak%C3%A7ay"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Battye\" title=\"<PERSON> Battye\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Battye\" title=\"<PERSON> Battye\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>e", "link": "https://wikipedia.org/wiki/Phoenix_Battye"}]}, {"year": "1991", "text": "<PERSON>, Puerto Rican baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, American sprinter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Canadian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Canadian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Canadian actor and musician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian actor and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Argentine tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English-Northern Irish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English-Northern Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_T<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese gymnast", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON><PERSON><PERSON>i"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English sprinter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nang%C3%B3mez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nang%C3%B3mez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Juancho_Hernang%C3%B3mez"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)"}]}, {"year": "1996", "text": "<PERSON>, British race car driver", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Hungarian tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panna_U<PERSON>vard<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Kayla_Day\" title=\"Kayla Day\"><PERSON><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>la_Day\" title=\"Kayla Day\"><PERSON><PERSON></a>, American tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kayla_Day"}]}, {"year": "2000", "text": "<PERSON>, American actor, singer, and songwriter", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jonas\"><PERSON></a>, American actor, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "48 BC", "text": "<PERSON><PERSON><PERSON>, Roman general and politician (b. 106 BC)", "html": "48 BC - 48 BC - <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pompey\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 106 BC)", "no_year_html": "48 BC - <a href=\"https://wikipedia.org/wiki/Pompey\" title=\"Pomp<PERSON>\"><PERSON><PERSON><PERSON></a>, Roman general and politician (b. 106 BC)", "links": [{"title": "Pompey", "link": "https://wikipedia.org/wiki/Pompey"}]}, {"year": "135", "text": "Rabbi <PERSON><PERSON><PERSON>, Jewish sage, martyr. (b. c. 50)", "html": "135 - <a href=\"https://wikipedia.org/wiki/Rabbi_<PERSON><PERSON><PERSON>\" title=\"Rabbi <PERSON><PERSON><PERSON>\">Rabbi <PERSON><PERSON><PERSON></a>, Jewish sage, <a href=\"https://wikipedia.org/wiki/Ten_Martyrs\" title=\"Ten Martyrs\">martyr</a>. (b. c. 50)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rabbi_<PERSON><PERSON><PERSON>\" title=\"Rabbi <PERSON><PERSON>\">Rabbi <PERSON><PERSON><PERSON></a>, Jewish sage, <a href=\"https://wikipedia.org/wiki/Ten_Martyrs\" title=\"Ten Martyrs\">martyr</a>. (b. c. 50)", "links": [{"title": "Rabbi <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Ten Martyrs", "link": "https://wikipedia.org/wiki/Ten_Martyrs"}]}, {"year": "782", "text": "<PERSON><PERSON>, Anglo-Saxon nun (b. c. 710)", "html": "782 - <a href=\"https://wikipedia.org/wiki/Leoba\" title=\"Leo<PERSON>\"><PERSON><PERSON></a>, Anglo-Saxon nun (b. c. 710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leoba\" title=\"Leo<PERSON>\"><PERSON><PERSON></a>, Anglo-Saxon nun (b. c. 710)", "links": [{"title": "Leoba", "link": "https://wikipedia.org/wiki/Leoba"}]}, {"year": "935", "text": "<PERSON><PERSON><PERSON>, duke of Bohemia (b. c. 907)", "html": "935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON></a>, duke of Bohemia (b. c. 907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Bohemia\"><PERSON><PERSON><PERSON></a>, duke of Bohemia (b. c. 907)", "links": [{"title": "<PERSON><PERSON><PERSON>, Duke of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Bohemia"}]}, {"year": "980", "text": "<PERSON><PERSON>, Japanese nobleman (b. 918)", "html": "980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Hiromasa\" title=\"Minamoto no Hiromasa\"><PERSON><PERSON> no Hi<PERSON></a>, Japanese nobleman (b. 918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Hiromasa\" title=\"Minamoto no Hiromasa\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 918)", "links": [{"title": "<PERSON><PERSON> no Hiromasa", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1197", "text": "<PERSON>, Holy Roman Emperor (b. 1165)", "html": "1197 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON> VI</a>, Holy Roman Emperor (b. 1165)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> VI, Holy Roman Emperor\"><PERSON> VI</a>, Holy Roman Emperor (b. 1165)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1213", "text": "<PERSON> of <PERSON><PERSON>a, queen consort of Hungary (b. 1185)", "html": "1213 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Merania\" title=\"<PERSON> of Merania\"><PERSON> of Merania</a>, queen consort of Hungary (b. 1185)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Merania\" title=\"<PERSON> of Merania\"><PERSON> of Merania</a>, queen consort of Hungary (b. 1185)", "links": [{"title": "Gertrude of Merania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Merania"}]}, {"year": "1330", "text": "<PERSON> of Bohemia, queen consort of Bohemia (b. 1292)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_(1292%E2%80%931330)\" title=\"<PERSON> of Bohemia (1292-1330)\"><PERSON> of Bohemia</a>, queen consort of Bohemia (b. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bohemia_(1292%E2%80%931330)\" title=\"<PERSON> of Bohemia (1292-1330)\"><PERSON> of Bohemia</a>, queen consort of Bohemia (b. 1292)", "links": [{"title": "<PERSON> of Bohemia (1292-1330)", "link": "https://wikipedia.org/wiki/Elizabeth_of_Bohemia_(1292%E2%80%931330)"}]}, {"year": "1429", "text": "<PERSON><PERSON><PERSON><PERSON> of Masovia, duchess consort of Austria (b. 1394)", "html": "1429 - <a href=\"https://wikipedia.org/wiki/Cy<PERSON>urg<PERSON>_of_Masovia\" title=\"Cy<PERSON>urg<PERSON> of Masovia\"><PERSON><PERSON><PERSON><PERSON> of Masovia</a>, duchess consort of Austria (b. 1394)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cy<PERSON>urg<PERSON>_of_Masovia\" title=\"Cy<PERSON>urg<PERSON> of Masovia\"><PERSON><PERSON><PERSON><PERSON> of Masovia</a>, duchess consort of Austria (b. 1394)", "links": [{"title": "Cymburgis of Masovia", "link": "https://wikipedia.org/wiki/Cymburgis_of_Masovia"}]}, {"year": "1582", "text": "<PERSON>, Scottish historian and scholar (b. 1506)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and scholar (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "<PERSON>, countess of Derby (b. 1540)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby\" title=\"<PERSON>, Countess of Derby\"><PERSON></a>, countess of Derby (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby\" title=\"<PERSON>, Countess of Derby\"><PERSON></a>, countess of Derby (b. 1540)", "links": [{"title": "<PERSON>, Countess of Derby", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby"}]}, {"year": "1618", "text": "<PERSON><PERSON><PERSON>, English poet and translator (b. 1563)", "html": "1618 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and translator (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet and translator (b. 1563)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1687", "text": "<PERSON>, Swiss-Italian theologian and academic (b. 1623)", "html": "1687 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian theologian and academic (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-Italian theologian and academic (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, French mathematician and theologian (b. 1618)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theologian (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theologian (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, 2nd Earl of Sunderland, French-English lawyer and politician, Lord President of the Council (b. 1640)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>_Sunderland\" title=\"<PERSON>, 2nd Earl of Sunderland\"><PERSON>, 2nd Earl of Sunderland</a>, French-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_<PERSON>_Sunderland\" title=\"<PERSON>, 2nd Earl of Sunderland\"><PERSON>, 2nd Earl of Sunderland</a>, French-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_President_of_the_Council\" title=\"Lord President of the Council\">Lord President of the Council</a> (b. 1640)", "links": [{"title": "<PERSON>, 2nd Earl of Sunderland", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Sunderland"}, {"title": "Lord President of the Council", "link": "https://wikipedia.org/wiki/Lord_President_of_the_Council"}]}, {"year": "1742", "text": "<PERSON>, French bishop (b. 1663)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop (b. 1663)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French bishop (b. 1663)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Prince-Bishop of Bamberg (b. 1724)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Bamberg (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prince-Bishop of Bamberg (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1829", "text": "<PERSON><PERSON>, Russian general and politician (b. 1771)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician (b. 1771)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON><PERSON><PERSON>, Russian general and politician (b. 1769)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general and politician (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general and politician (b. 1769)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, German geographer and academic (b. 1779)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and academic (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON>, French journalist and author (b. 1832)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Gaboriau\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Gaboriau\" title=\"É<PERSON>\"><PERSON><PERSON></a>, French journalist and author (b. 1832)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Gaboriau"}]}, {"year": "1882", "text": "<PERSON><PERSON>, Norwegian opera singer (b. 1846)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ko<PERSON>\"><PERSON><PERSON></a>, Norwegian opera singer (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kolderup\"><PERSON><PERSON></a>, Norwegian opera singer (b. 1846)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>up"}]}, {"year": "1891", "text": "<PERSON>, American author and poet (b. 1819)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, British painter (b. 1826)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, French chemist and microbiologist (b. 1822)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and microbiologist (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and microbiologist (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Austrian painter (b. 1858)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American politician and attorney (b. 1853)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and attorney (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek-Japanese historian and author (b. 1850)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Lafcadio_Hearn\" title=\"Lafcadio Hearn\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-Japanese historian and author (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lafcadio_Hearn\" title=\"Lafcadio Hearn\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-Japanese historian and author (b. 1850)", "links": [{"title": "Lafcadio Hearn", "link": "https://wikipedia.org/wiki/Lafcadio_Hearn"}]}, {"year": "1914", "text": "<PERSON>, American businessman, co-founded Sears (b. 1863)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sears\" title=\"Sears\"><PERSON></a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sears\" title=\"<PERSON>\">Sears</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sears", "link": "https://wikipedia.org/wiki/Sears"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Japanese samurai (b. 1844)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Sait%C5%8D_Hajime\" title=\"<PERSON><PERSON> Hajime\"><PERSON><PERSON></a>, Japanese samurai (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sait%C5%8D_Hajime\" title=\"<PERSON>tō Hajime\"><PERSON><PERSON></a>, Japanese samurai (b. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sait%C5%8D_<PERSON><PERSON>me"}]}, {"year": "1918", "text": "<PERSON>, German sociologist and philosopher (b. 1858)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1918", "text": "<PERSON>, American soldier, Medal of Honor recipient (b. 1896)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1920", "text": "<PERSON>, Korean Independence Activist (b. 1902)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Korean Independence Activist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Korean Independence Activist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French actor (b. 1888)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, French-Scottish actor, director, and producer, invented the Ki<PERSON><PERSON><PERSON> (b. 1860)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Scottish actor, director, and producer, invented the <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">Kinetoscope</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Scottish actor, director, and producer, invented the <a href=\"https://wikipedia.org/wiki/Kinetoscope\" title=\"Kinetoscope\">Kinetoscope</a> (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kinetoscope", "link": "https://wikipedia.org/wiki/Kinetoscope"}]}, {"year": "1938", "text": "<PERSON>, American engineer and businessman, founded the Duryea Motor Wagon Company  (b. 1861)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company\" title=\"Duryea Motor Wagon Company\">Duryea Motor Wagon Company</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded the <a href=\"https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company\" title=\"Duryea Motor Wagon Company\">Duryea Motor Wagon Company</a> (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Duryea Motor Wagon Company", "link": "https://wikipedia.org/wiki/Duryea_Motor_Wagon_Company"}]}, {"year": "1941", "text": "<PERSON>, American golfer, ranked No. 1 in the United States (b. 1914)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, ranked No. 1 in the United States (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer, ranked No. 1 in the United States (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American chemist and academic (b. 1913)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Italian partisan, Gold Medal of Military Valour (b. 1930)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian partisan, Gold Medal of Military Valour (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian partisan, Gold Medal of Military Valour (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens (b. 1881)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_Athens\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens\">Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Archbishop_<PERSON><PERSON><PERSON><PERSON>_of_Athens\" class=\"mw-redirect\" title=\"Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens\">Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens</a> (b. 1881)", "links": [{"title": "Archbishop <PERSON><PERSON><PERSON><PERSON> of Athens", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_of_Athens"}]}, {"year": "1953", "text": "<PERSON>, American astronomer and scholar (b. 1889)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and scholar (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and scholar (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American businessman, founded the Boeing Company (b. 1881)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing Company</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing Company</a> (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Boeing"}, {"title": "Boeing", "link": "https://wikipedia.org/wiki/Boeing"}]}, {"year": "1957", "text": "<PERSON>, Uruguayan violinist and composer (b. 1888)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan violinist and composer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan violinist and composer (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, German race car driver (b. 1901)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, French soldier and author (b. 1925)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American comedian, actor, and singer (b. 1888)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian, actor, and singer (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian, actor, and singer (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French author and poet (b. 1896)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Breton\" title=\"André Breton\"><PERSON></a>, French author and poet (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Breton\" title=\"André Breton\"><PERSON></a>, French author and poet (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Breton"}]}, {"year": "1970", "text": "<PERSON>, American novelist, poet, essayist, and playwright (b. 1896)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, essayist, and playwright (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, essayist, and playwright (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Egyptian colonel and politician, 2nd President of Egypt (b. 1918)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Egyptian colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Egyptian colonel and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}]}, {"year": "1978", "text": "<PERSON> <PERSON> (b. 1912)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> <PERSON></a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian physicist and engineer (b. 1921)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and engineer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and engineer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan journalist and politician, President of Venezuela (b. 1908)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/R%C3%B3<PERSON><PERSON>_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan journalist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%B3<PERSON><PERSON>_Betancourt\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan journalist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Venezuela\" title=\"President of Venezuela\">President of Venezuela</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%B3mu<PERSON>_Betancourt"}, {"title": "President of Venezuela", "link": "https://wikipedia.org/wiki/President_of_Venezuela"}]}, {"year": "1982", "text": "<PERSON>, American actress (b. 1901)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Turkish journalist, author, and politician (b. 1911)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and politician (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist, author, and politician (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1989", "text": "<PERSON>, Filipino lawyer and politician, 10th President of the Philippines (b. 1917)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1990", "text": "<PERSON>, American businessman and politician, 57th United States Postmaster General (b. 1917)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Brien\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 57th <a href=\"https://wikipedia.org/wiki/United_States_Postmaster_General\" title=\"United States Postmaster General\">United States Postmaster General</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien"}, {"title": "United States Postmaster General", "link": "https://wikipedia.org/wiki/United_States_Postmaster_General"}]}, {"year": "1991", "text": "<PERSON>, American trumpet player, composer, and bandleader (b. 1926)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player, composer, and bandleader (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American editor and novelist  (b. 1910)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American editor and novelist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American editor and novelist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American sergeant (b. 1910)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Estonian singer (b. 1953)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Alender\"><PERSON><PERSON><PERSON></a>, Estonian singer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Alender\"><PERSON><PERSON><PERSON></a>, Estonian singer (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ender"}]}, {"year": "1994", "text": "<PERSON>, Mexican lawyer and politician, 6th Governor of Guerrero (b. 1946)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Guerrero\" class=\"mw-redirect\" title=\"Governor of Guerrero\">Governor of Guerrero</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Guerrero\" class=\"mw-redirect\" title=\"Governor of Guerrero\">Governor of Guerrero</a> (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Guerrero", "link": "https://wikipedia.org/wiki/Governor_of_Guerrero"}]}, {"year": "1994", "text": "<PERSON>, Canadian production manager and producer (b. 1915)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian production manager and producer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian production manager and producer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian film actor and comedian (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/K._<PERSON>._<PERSON>gavelu\" title=\"K. <PERSON><PERSON>vel<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian film actor and comedian (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON>._<PERSON>gavelu\" title=\"K. A. Thangavelu\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian film actor and comedian (b. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON>gavelu"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Canadian academic and diplomat (b. 1905)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and diplomat (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian academic and diplomat (b. 1905)", "links": [{"title": "Escott Reid", "link": "https://wikipedia.org/wiki/<PERSON>scott_Reid"}]}, {"year": "2000", "text": "<PERSON>, Canadian journalist, lawyer, and politician, 15th Prime Minister of Canada (b. 1919)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American lawyer and politician (b. 1927)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Canadian captain and politician (b. 1907)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian captain and politician (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian captain and politician (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hart<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American tennis player and golfer (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and golfer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and golfer (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American director, producer, and screenwriter (b. 1909)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Elia_Kazan\" title=\"Elia Kazan\"><PERSON><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elia_Kazan\" title=\"Elia Kazan\"><PERSON><PERSON></a>, American director, producer, and screenwriter (b. 1909)", "links": [{"title": "Elia Kazan", "link": "https://wikipedia.org/wiki/Elia_Kazan"}]}, {"year": "2003", "text": "<PERSON>, Saint Lucian politician and diplomat (b. 1934)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian politician and diplomat (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Saint Lucian politician and diplomat (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American fashion designer (b. 1924)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American lawyer, judge, and politician (b. 1921)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French mountaineer (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mountaineer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American businessman, founded the National Hot Rod Association (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/National_Hot_Rod_Association\" title=\"National Hot Rod Association\">National Hot Rod Association</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/National_Hot_Rod_Association\" title=\"National Hot Rod Association\">National Hot Rod Association</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Hot Rod Association", "link": "https://wikipedia.org/wiki/National_Hot_Rod_Association"}]}, {"year": "2009", "text": "<PERSON>, Panamanian lawyer and politician, 32nd President of Panama (b. 1936)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a> (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Swedish actor and director (b. 1956)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, German mountaineer and photographer (b. 1954)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer and photographer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mountaineer and photographer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American director and producer (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Penn\"><PERSON></a>, American director and producer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur Penn\"><PERSON></a>, American director and producer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_Penn"}]}, {"year": "2010", "text": "<PERSON>, American soprano and actress (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli general (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Avraham_<PERSON>\" title=\"Avraham <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli general (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avrah<PERSON>_<PERSON>\" title=\"Avraham <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli general (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "2012", "text": "<PERSON>, American journalist and sportscaster (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Indian politician and diplomat, 1st Indian National Security Advisor (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(India)\" title=\"National Security Advisor (India)\">Indian National Security Advisor</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician and diplomat, 1st <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(India)\" title=\"National Security Advisor (India)\">Indian National Security Advisor</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "National Security Advisor (India)", "link": "https://wikipedia.org/wiki/National_Security_Advisor_(India)"}]}, {"year": "2013", "text": "<PERSON>, American-French poet and scholar (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French poet and scholar (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French poet and scholar (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>-<PERSON>, South African cricketer and rugby player (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and rugby player (b. 1932)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer and pianist  (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and pianist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Welsh physician, poet, and author (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh physician, poet, and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh physician, poet, and author (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>e"}]}, {"year": "2014", "text": "<PERSON>, American colonel and historian (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and historian (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and historian (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English dentist and politician (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dentist and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English dentist and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English footballer and manager (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lings"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Czech pianist and composer (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech pianist and composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech pianist and composer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "2015", "text": "<PERSON>, Irish composer and conductor (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish composer and conductor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American rancher and politician, 29th Governor of South Dakota (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rancher and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Dakota\" title=\"Governor of South Dakota\">Governor of South Dakota</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of South Dakota", "link": "https://wikipedia.org/wiki/Governor_of_South_Dakota"}]}, {"year": "2015", "text": "<PERSON>, Spanish footballer (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American television writer and director (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and director (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and director (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American television writer and producer (b. 1966)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television writer and producer (b. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Polish-Israeli statesman and politician, 9th President of Israel (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli statesman and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Israeli statesman and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "2016", "text": "<PERSON>, American novelist (b. 1950)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Israeli television host and newsreader (b. 1943)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27er\" title=\"<PERSON>\"><PERSON></a>, Israeli television host and newsreader (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27er\" title=\"<PERSON>\"><PERSON></a>, Israeli television host and newsreader (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27er"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Serbian actor (b. 1947)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Predrag_<PERSON><PERSON>\" title=\"Predrag <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Predrag_<PERSON>\" title=\"Predrag <PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian actor (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Predrag_<PERSON><PERSON><PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Mexican musician and singer (b. 1948)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Mexican musician and singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Mexican musician and singer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Jos%C3%A9"}]}, {"year": "2022", "text": "<PERSON><PERSON>, American rapper (b. 1963)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper (b. 1963)", "links": [{"title": "Coolio", "link": "https://wikipedia.org/wiki/Coolio"}]}, {"year": "2024", "text": "<PERSON>, American politician, 43rd Governor of Tennessee (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Tennessee\" title=\"Governor of Tennessee\">Governor of Tennessee</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Tennessee", "link": "https://wikipedia.org/wiki/Governor_of_Tennessee"}]}, {"year": "2024", "text": "<PERSON>, American actor (b. 1953)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Drake_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American singer, songwriter, and actor (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}]}}