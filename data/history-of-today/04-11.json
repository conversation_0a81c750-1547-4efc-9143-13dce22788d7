{"date": "April 11", "url": "https://wikipedia.org/wiki/April_11", "data": {"Events": [{"year": "491", "text": "<PERSON><PERSON><PERSON> becomes Byzantine emperor, with the name of <PERSON><PERSON><PERSON>.", "html": "491 - <PERSON><PERSON><PERSON> becomes <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a>, with the name of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_<PERSON>us\" title=\"<PERSON><PERSON><PERSON> I <PERSON>\"><PERSON><PERSON><PERSON> I</a>.", "no_year_html": "<PERSON><PERSON><PERSON> becomes <a href=\"https://wikipedia.org/wiki/Byzantine_emperor\" class=\"mw-redirect\" title=\"Byzantine emperor\">Byzantine emperor</a>, with the name of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_Dicorus\" title=\"<PERSON><PERSON><PERSON> I <PERSON>\"><PERSON><PERSON><PERSON> I</a>.", "links": [{"title": "Byzantine emperor", "link": "https://wikipedia.org/wiki/Byzantine_emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>sius_I_Dicorus"}]}, {"year": "1241", "text": "<PERSON><PERSON> Khan defeats <PERSON><PERSON><PERSON> IV of Hungary at the Battle of Mohi.", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary\" title=\"Béla IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mohi\" title=\"Battle of Mohi\">Battle of Mohi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> defeats <a href=\"https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary\" title=\"Béla IV of Hungary\"><PERSON><PERSON><PERSON> IV of Hungary</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Mohi\" title=\"Battle of Mohi\">Battle of Mohi</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Béla IV of Hungary", "link": "https://wikipedia.org/wiki/B%C3%A9la_IV_of_Hungary"}, {"title": "Battle of Mohi", "link": "https://wikipedia.org/wiki/Battle_of_Mohi"}]}, {"year": "1512", "text": "War of the League of Cambrai: Franco-Ferrarese forces led by <PERSON> and <PERSON> win the Battle of Ravenna against the Papal-Spanish forces.", "html": "1512 - <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">War of the League of Cambrai</a>: Franco-Ferrarese forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_N<PERSON>\" title=\"<PERSON>, Duke of Nemours\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Este\" title=\"<PERSON>\"><PERSON></a> win the <a href=\"https://wikipedia.org/wiki/Battle_of_Ravenna_(1512)\" title=\"Battle of Ravenna (1512)\">Battle of Ravenna</a> against the Papal-Spanish forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">War of the League of Cambrai</a>: Franco-Ferrarese forces led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_N<PERSON>\" title=\"<PERSON>, Duke of Nemours\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Este\" title=\"Alfonso <PERSON>\"><PERSON></a> win the <a href=\"https://wikipedia.org/wiki/Battle_of_Ravenna_(1512)\" title=\"Battle of Ravenna (1512)\">Battle of Ravenna</a> against the Papal-Spanish forces.", "links": [{"title": "War of the League of Cambrai", "link": "https://wikipedia.org/wiki/War_of_the_League_of_Cambrai"}, {"title": "<PERSON> Foix, Duke of Nemours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>em<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_I_d%27Este"}, {"title": "Battle of Ravenna (1512)", "link": "https://wikipedia.org/wiki/Battle_of_Ravenna_(1512)"}]}, {"year": "1544", "text": "Italian War of 1542-46: A French army defeats Habsburg forces at the Battle of Ceresole, but fails to exploit its victory.", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346\" class=\"mw-redirect\" title=\"Italian War of 1542-46\">Italian War of 1542-46</a>: A French army defeats <a href=\"https://wikipedia.org/wiki/House_of_Habsburg\" title=\"House of Habsburg\">Habsburg</a> forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ceresole\" title=\"Battle of Ceresole\">Battle of Ceresole</a>, but fails to exploit its victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346\" class=\"mw-redirect\" title=\"Italian War of 1542-46\">Italian War of 1542-46</a>: A French army defeats <a href=\"https://wikipedia.org/wiki/House_of_<PERSON>\" title=\"House of Habsburg\">Habsburg</a> forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Ceresole\" title=\"Battle of Ceresole\">Battle of Ceresole</a>, but fails to exploit its victory.", "links": [{"title": "Italian War of 1542-46", "link": "https://wikipedia.org/wiki/Italian_War_of_1542%E2%80%9346"}, {"title": "House of Habsburg", "link": "https://wikipedia.org/wiki/House_of_Habsburg"}, {"title": "Battle of Ceresole", "link": "https://wikipedia.org/wiki/Battle_of_Ceresole"}]}, {"year": "1689", "text": "<PERSON> and <PERSON> are crowned as joint sovereigns of Great Britain on the same day that the Scottish Parliament concurs with the English decision of 12 February.", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"William III of England\"><PERSON> III</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> II of England\"><PERSON> II</a> are crowned as joint sovereigns of Great Britain on the same day that the Scottish Parliament concurs with the English decision of 12 February.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> III of England\"><PERSON> III</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" class=\"mw-redirect\" title=\"<PERSON> II of England\"><PERSON> II</a> are crowned as joint sovereigns of Great Britain on the same day that the Scottish Parliament concurs with the English decision of 12 February.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Mary_<PERSON>_of_England"}]}, {"year": "1713", "text": "France and Great Britain sign the Treaty of Utrecht, bringing an end to the War of the Spanish Succession (Queen <PERSON>'s War). Britain accepts <PERSON> as King of Spain, while <PERSON> renounces any claim to the French throne.", "html": "1713 - France and Great Britain sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Utrecht\" class=\"mw-redirect\" title=\"Treaty of Utrecht\">Treaty of Utrecht</a>, bringing an end to the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a> (<a href=\"https://wikipedia.org/wiki/Queen_Anne%27s_War\" title=\"Queen <PERSON>'s War\">Queen <PERSON>'s War</a>). Britain accepts <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"<PERSON> V of Spain\"><PERSON> V</a> as King of Spain, while <PERSON> renounces any claim to the French throne.", "no_year_html": "France and Great Britain sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Utrecht\" class=\"mw-redirect\" title=\"Treaty of Utrecht\">Treaty of Utrecht</a>, bringing an end to the <a href=\"https://wikipedia.org/wiki/War_of_the_Spanish_Succession\" title=\"War of the Spanish Succession\">War of the Spanish Succession</a> (<a href=\"https://wikipedia.org/wiki/Queen_Anne%27s_War\" title=\"Queen <PERSON>'s War\">Queen <PERSON>'s War</a>). Britain accepts <a href=\"https://wikipedia.org/wiki/Philip_V_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> V</a> as King of Spain, while <PERSON> renounces any claim to the French throne.", "links": [{"title": "Treaty of Utrecht", "link": "https://wikipedia.org/wiki/Treaty_of_Utrecht"}, {"title": "War of the Spanish Succession", "link": "https://wikipedia.org/wiki/War_of_the_Spanish_Succession"}, {"title": "Queen Anne's War", "link": "https://wikipedia.org/wiki/Queen_<PERSON>%27s_War"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Philip_V_of_Spain"}]}, {"year": "1727", "text": "Premiere of <PERSON>'s St Matthew Passion BWV 244b at St. Thomas Church in Leipzig, Electorate of Saxony (now Germany).", "html": "1727 - Premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/St_Matthew_Passion\" title=\"St Matthew Passion\">St Matthew Passion</a> BWV 244b at <a href=\"https://wikipedia.org/wiki/St._Thomas_Church,_Leipzig\" title=\"St. Thomas Church, Leipzig\">St. Thomas Church</a> in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Electorate of Saxony</a> (now Germany).", "no_year_html": "Premiere of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/St_Matthew_Passion\" title=\"St Matthew Passion\">St Matthew Passion</a> BWV 244b at <a href=\"https://wikipedia.org/wiki/St._Thomas_Church,_Leipzig\" title=\"St. Thomas Church, Leipzig\">St. Thomas Church</a> in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Electorate of Saxony</a> (now Germany).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "St Matthew Passion", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "St. Thomas Church, Leipzig", "link": "https://wikipedia.org/wiki/St._Thomas_Church,_Leipzig"}, {"title": "Leipzig", "link": "https://wikipedia.org/wiki/Leipzig"}, {"title": "Electorate of Saxony", "link": "https://wikipedia.org/wiki/Electorate_of_Saxony"}]}, {"year": "1809", "text": "Battle of the Basque Roads: Admiral Lord <PERSON> fails to support Captain Lord <PERSON>, leading to an incomplete British victory over the French fleet.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Basque_Roads\" title=\"Battle of the Basque Roads\">Battle of the Basque Roads</a>: Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\">Lord <PERSON></a> fails to support Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_<PERSON>_Du<PERSON>\" title=\"<PERSON>, 10th Earl of Dundonald\">Lord <PERSON></a>, leading to an incomplete British victory over the French fleet.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Basque_Roads\" title=\"Battle of the Basque Roads\">Battle of the Basque Roads</a>: Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\">Lord <PERSON></a> fails to support Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_<PERSON>_Du<PERSON>ald\" title=\"<PERSON>, 10th Earl of Dundonald\">Lord <PERSON></a>, leading to an incomplete British victory over the French fleet.", "links": [{"title": "Battle of the Basque Roads", "link": "https://wikipedia.org/wiki/Battle_of_the_Basque_Roads"}, {"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}, {"title": "<PERSON>, 10th Earl of Dundonald", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald"}]}, {"year": "1814", "text": "The Treaty of Fontainebleau ends the War of the Sixth Coalition against <PERSON>, and forces him to abdicate unconditionally for the first time.", "html": "1814 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fontainebleau_(1814)\" title=\"Treaty of Fontainebleau (1814)\">Treaty of Fontainebleau</a> ends the <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a> against <a href=\"https://wikipedia.org/wiki/Napoleon_Bonaparte\" class=\"mw-redirect\" title=\"<PERSON> Bonaparte\"><PERSON></a>, and forces him to abdicate unconditionally for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Fontainebleau_(1814)\" title=\"Treaty of Fontainebleau (1814)\">Treaty of Fontainebleau</a> ends the <a href=\"https://wikipedia.org/wiki/War_of_the_Sixth_Coalition\" title=\"War of the Sixth Coalition\">War of the Sixth Coalition</a> against <a href=\"https://wikipedia.org/wiki/<PERSON>_Bonaparte\" class=\"mw-redirect\" title=\"<PERSON> Bonaparte\"><PERSON></a>, and forces him to abdicate unconditionally for the first time.", "links": [{"title": "Treaty of Fontainebleau (1814)", "link": "https://wikipedia.org/wiki/Treaty_of_Fontainebleau_(1814)"}, {"title": "War of the Sixth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Sixth_Coalition"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "Second Battle of Rivas: <PERSON> burns down the hostel where <PERSON>'s filibusters are holed up.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Rivas\" title=\"Second Battle of Rivas\">Second Battle of Rivas</a>: <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADa\" title=\"Juan <PERSON>\"><PERSON></a> burns down the hostel where <a href=\"https://wikipedia.org/wiki/<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Filibuster_(military)\" title=\"Filibuster (military)\">filibusters</a> are holed up.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Battle_of_Rivas\" title=\"Second Battle of Rivas\">Second Battle of Rivas</a>: <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADa\" title=\"Juan <PERSON>\"><PERSON></a> burns down the hostel where <a href=\"https://wikipedia.org/wiki/<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Filibuster_(military)\" title=\"Filibuster (military)\">filibusters</a> are holed up.", "links": [{"title": "Second Battle of Rivas", "link": "https://wikipedia.org/wiki/Second_Battle_of_Rivas"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>mar%C3%ADa"}, {"title": "<PERSON> (filibuster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filibuster)"}, {"title": "Filibuster (military)", "link": "https://wikipedia.org/wiki/Filibuster_(military)"}]}, {"year": "1868", "text": "Former shōgun Tokugawa <PERSON> surrenders Edo Castle to Imperial forces, marking the end of the Tokugawa shogunate.", "html": "1868 - Former <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> <a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\">Tokugawa Yoshinobu</a> surrenders <a href=\"https://wikipedia.org/wiki/Edo_Castle\" title=\"Edo Castle\">Edo Castle</a> to Imperial forces, marking the end of the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a>.", "no_year_html": "Former <i><a href=\"https://wikipedia.org/wiki/Sh%C5%8Dgun\" class=\"mw-redirect\" title=\"Shōgun\">shōgun</a></i> <a href=\"https://wikipedia.org/wiki/Tokugawa_Yoshinobu\" title=\"Tokugawa Yoshinobu\">Tokugawa Yoshinobu</a> surrenders <a href=\"https://wikipedia.org/wiki/Edo_Castle\" title=\"Edo Castle\">Edo Castle</a> to Imperial forces, marking the end of the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dgun"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Edo Castle", "link": "https://wikipedia.org/wiki/Edo_Castle"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}]}, {"year": "1876", "text": "The Benevolent and Protective Order of Elks is organized.", "html": "1876 - The <a href=\"https://wikipedia.org/wiki/Benevolent_and_Protective_Order_of_Elks\" title=\"Benevolent and Protective Order of Elks\">Benevolent and Protective Order of Elks</a> is organized.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Benevolent_and_Protective_Order_of_Elks\" title=\"Benevolent and Protective Order of Elks\">Benevolent and Protective Order of Elks</a> is organized.", "links": [{"title": "Benevolent and Protective Order of Elks", "link": "https://wikipedia.org/wiki/Benevolent_and_Protective_Order_of_Elks"}]}, {"year": "1881", "text": "Spelman College is founded in Atlanta, Georgia as the Atlanta Baptist Female Seminary, an institute of higher education for African-American women.", "html": "1881 - <a href=\"https://wikipedia.org/wiki/Spelman_College\" title=\"Spelman College\">Spelman College</a> is founded in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a> as the Atlanta Baptist Female Seminary, an institute of higher education for African-American women.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spelman_College\" title=\"Spelman College\">Spelman College</a> is founded in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta, Georgia</a> as the Atlanta Baptist Female Seminary, an institute of higher education for African-American women.", "links": [{"title": "Spelman College", "link": "https://wikipedia.org/wiki/Spelman_College"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}]}, {"year": "1908", "text": "SMS Blücher, the last armored cruiser to be built by the Imperial German Navy, is launched.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/SMS_Bl%C3%BCcher\" title=\"SMS Blücher\">SMS <i>Blücher</i></a>, the last <a href=\"https://wikipedia.org/wiki/Armored_cruiser\" title=\"Armored cruiser\">armored cruiser</a> to be built by the <a href=\"https://wikipedia.org/wiki/Imperial_German_Navy\" title=\"Imperial German Navy\">Imperial German Navy</a>, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SMS_Bl%C3%BCcher\" title=\"SMS Blücher\">SMS <i>Blücher</i></a>, the last <a href=\"https://wikipedia.org/wiki/Armored_cruiser\" title=\"Armored cruiser\">armored cruiser</a> to be built by the <a href=\"https://wikipedia.org/wiki/Imperial_German_Navy\" title=\"Imperial German Navy\">Imperial German Navy</a>, is launched.", "links": [{"title": "SMS Blücher", "link": "https://wikipedia.org/wiki/SMS_Bl%C3%BCcher"}, {"title": "Armored cruiser", "link": "https://wikipedia.org/wiki/Armored_cruiser"}, {"title": "Imperial German Navy", "link": "https://wikipedia.org/wiki/Imperial_German_Navy"}]}, {"year": "1909", "text": "The city of Tel Aviv is founded.", "html": "1909 - The city of <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a> is founded.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a> is founded.", "links": [{"title": "Tel Aviv", "link": "https://wikipedia.org/wiki/Tel_Aviv"}]}, {"year": "1921", "text": "<PERSON><PERSON> establishes the first centralised government in the newly created British protectorate of Transjordan.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\"><PERSON>ir</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON></a> establishes the first centralised government in the newly created <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorate</a> of <a href=\"https://wikipedia.org/wiki/Emirate_of_Transjordan\" title=\"Emirate of Transjordan\">Transjordan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\">Emir</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Jordan\" title=\"<PERSON> of Jordan\"><PERSON></a> establishes the first centralised government in the newly created <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorate</a> of <a href=\"https://wikipedia.org/wiki/Emirate_of_Transjordan\" title=\"Emirate of Transjordan\">Transjordan</a>.", "links": [{"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}, {"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan"}, {"title": "British protectorate", "link": "https://wikipedia.org/wiki/British_protectorate"}, {"title": "Emirate of Transjordan", "link": "https://wikipedia.org/wiki/Emirate_of_Transjordan"}]}, {"year": "1935", "text": "Stresa Front: opening of the conference between the British Prime Minister <PERSON>, the Italian Prime Minister <PERSON> and the French Minister for Foreign Affairs <PERSON> to condemn the German violations of the Treaty of Versailles.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Stresa_Front\" title=\"Stresa Front\">Stresa Front</a>: opening of the conference between the British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the Italian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the French Minister for Foreign Affairs <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to condemn the German violations of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stresa_Front\" title=\"Stresa Front\">Stresa Front</a>: opening of the conference between the British Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the Italian Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the French Minister for Foreign Affairs <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to condemn the German violations of the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>.", "links": [{"title": "Stresa Front", "link": "https://wikipedia.org/wiki/Stresa_Front"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Treaty of Versailles", "link": "https://wikipedia.org/wiki/Treaty_of_Versailles"}]}, {"year": "1945", "text": "World War II: American forces liberate the Buchenwald concentration camp.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: American forces liberate the <a href=\"https://wikipedia.org/wiki/Buchenwald_concentration_camp\" title=\"Buchenwald concentration camp\">Buchenwald concentration camp</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: American forces liberate the <a href=\"https://wikipedia.org/wiki/Buchenwald_concentration_camp\" title=\"Buchenwald concentration camp\">Buchenwald concentration camp</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Buchenwald concentration camp", "link": "https://wikipedia.org/wiki/Buchenwald_concentration_camp"}]}, {"year": "1951", "text": "Korean War: President <PERSON> relieves <PERSON> of the command of American forces in Korea and Japan.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: President <PERSON> <a href=\"https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>\" title=\"Relief of <PERSON>\">relieves <PERSON></a> of the command of American forces in Korea and Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: President <PERSON> <a href=\"https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>\" title=\"Relief of <PERSON>\">relieves <PERSON></a> of the command of American forces in Korea and Japan.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Relief of <PERSON>", "link": "https://wikipedia.org/wiki/Relief_of_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "The Stone of Scone, the stone upon which Scottish monarchs were traditionally crowned, is found on the site of the altar of Arbroath Abbey. It had been taken by Scottish nationalist students from its place in Westminster Abbey.", "html": "1951 - The <a href=\"https://wikipedia.org/wiki/Stone_of_Scone\" title=\"Stone of Scone\">Stone of Scone</a>, the stone upon which Scottish monarchs were traditionally crowned, is found on the site of the altar of <a href=\"https://wikipedia.org/wiki/Arbroath_Abbey\" title=\"Arbroath Abbey\">Arbroath Abbey</a>. It had been taken by Scottish nationalist students from its place in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Stone_of_Scone\" title=\"Stone of Scone\">Stone of Scone</a>, the stone upon which Scottish monarchs were traditionally crowned, is found on the site of the altar of <a href=\"https://wikipedia.org/wiki/Arbroath_Abbey\" title=\"Arbroath Abbey\">Arbroath Abbey</a>. It had been taken by Scottish nationalist students from its place in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "Stone of Scone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "Arbroath Abbey", "link": "https://wikipedia.org/wiki/Arbroath_Abbey"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1952", "text": "Bolivian National Revolution: Rebels take over <PERSON><PERSON><PERSON>.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Bolivian_National_Revolution\" title=\"Bolivian National Revolution\">Bolivian National Revolution</a>: Rebels take over <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bolivian_National_Revolution\" title=\"Bolivian National Revolution\">Bolivian National Revolution</a>: Rebels take over <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Bolivian National Revolution", "link": "https://wikipedia.org/wiki/Bolivian_National_Revolution"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1952", "text": "Pan Am Flight 526A ditches near San Juan-Isla Grande Airport in San Juan, Puerto Rico, after experiencing an engine failure, killing 52 people.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_526A\" title=\"Pan Am Flight 526A\">Pan Am Flight 526A</a> ditches near <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Dominicci_Airport\" title=\"Fernando Luis Ribas Dominicci Airport\">San Juan-Isla Grande Airport</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, after experiencing an engine failure, killing 52 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pan_Am_Flight_526A\" title=\"Pan Am Flight 526A\">Pan Am Flight 526A</a> ditches near <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Dominicci_Airport\" title=\"Fernando Luis Ribas Dominicci Airport\">San Juan-Isla Grande Airport</a> in <a href=\"https://wikipedia.org/wiki/San_Juan,_Puerto_Rico\" title=\"San Juan, Puerto Rico\">San Juan, Puerto Rico</a>, after experiencing an engine failure, killing 52 people.", "links": [{"title": "Pan Am Flight 526A", "link": "https://wikipedia.org/wiki/Pan_Am_Flight_526A"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "San Juan, Puerto Rico", "link": "https://wikipedia.org/wiki/San_Juan,_Puerto_Rico"}]}, {"year": "1955", "text": "The Air India Kashmir Princess is bombed and crashes in a failed assassination attempt on <PERSON> by the Kuomintang.", "html": "1955 - The <a href=\"https://wikipedia.org/wiki/Air_India\" title=\"Air India\">Air India</a> <a href=\"https://wikipedia.org/wiki/Kashmir_Princess\" title=\"Kashmir Princess\">Kashmir Princess</a> is bombed and crashes in a failed assassination attempt on <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Air_India\" title=\"Air India\">Air India</a> <a href=\"https://wikipedia.org/wiki/Kashmir_Princess\" title=\"Kashmir Princess\">Kashmir Princess</a> is bombed and crashes in a failed assassination attempt on <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a>.", "links": [{"title": "Air India", "link": "https://wikipedia.org/wiki/Air_India"}, {"title": "Kashmir Princess", "link": "https://wikipedia.org/wiki/Kashmir_Princess"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang"}]}, {"year": "1957", "text": "United Kingdom agrees to Singaporean self-rule.", "html": "1957 - United Kingdom agrees to <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singaporean</a> <a href=\"https://wikipedia.org/wiki/Self-governance_of_Singapore\" title=\"Self-governance of Singapore\">self-rule</a>.", "no_year_html": "United Kingdom agrees to <a href=\"https://wikipedia.org/wiki/Singapore\" title=\"Singapore\">Singaporean</a> <a href=\"https://wikipedia.org/wiki/Self-governance_of_Singapore\" title=\"Self-governance of Singapore\">self-rule</a>.", "links": [{"title": "Singapore", "link": "https://wikipedia.org/wiki/Singapore"}, {"title": "Self-governance of Singapore", "link": "https://wikipedia.org/wiki/Self-governance_of_Singapore"}]}, {"year": "1961", "text": "The trial of <PERSON> begins in Jerusalem.", "html": "1961 - The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1963", "text": "<PERSON> <PERSON> issues <PERSON><PERSON> in terris, the first encyclical addressed to all Christians instead of only Catholics, and which described the conditions for world peace in human terms.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>X<PERSON>\" title=\"Pope John X<PERSON>II<PERSON>\">Pope <PERSON> X<PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/Pacem_in_terris\" title=\"Pacem in terris\">Pacem in terris</a></i>, the first encyclical addressed to all Christians instead of only Catholics, and which described the conditions for world peace in human terms.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXII<PERSON>\" title=\"Pope John XXIII\">Pope John X<PERSON></a> issues <i><a href=\"https://wikipedia.org/wiki/Pacem_in_terris\" title=\"Pacem in terris\">Pacem in terris</a></i>, the first encyclical addressed to all Christians instead of only Catholics, and which described the conditions for world peace in human terms.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>III"}, {"title": "Pacem in terris", "link": "https://wikipedia.org/wiki/Pacem_in_terris"}]}, {"year": "1964", "text": "Brazilian Marshal <PERSON><PERSON><PERSON> is elected president by the National Congress.", "html": "1964 - Brazilian Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Branco\" title=\"<PERSON><PERSON><PERSON> <PERSON> Alencar <PERSON>elo Bran<PERSON>\"><PERSON><PERSON><PERSON> <PERSON>elo <PERSON>ran<PERSON></a> is elected president by the <a href=\"https://wikipedia.org/wiki/National_Congress_of_Brazil\" class=\"mw-redirect\" title=\"National Congress of Brazil\">National Congress</a>.", "no_year_html": "Brazilian Marshal <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Branco\" title=\"<PERSON><PERSON><PERSON> de Alencar Castelo Branco\"><PERSON><PERSON><PERSON> <PERSON> Al<PERSON>car <PERSON>elo Branco</a> is elected president by the <a href=\"https://wikipedia.org/wiki/National_Congress_of_Brazil\" class=\"mw-redirect\" title=\"National Congress of Brazil\">National Congress</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>car <PERSON>elo Branco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Branco"}, {"title": "National Congress of Brazil", "link": "https://wikipedia.org/wiki/National_Congress_of_Brazil"}]}, {"year": "1965", "text": "The Palm Sunday tornado outbreak of 1965: Fifty-five tornadoes hit in six Midwestern states of the United States, killing 266 people.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Palm_Sunday_tornado_outbreak_of_1965\" class=\"mw-redirect\" title=\"Palm Sunday tornado outbreak of 1965\">Palm Sunday tornado outbreak of 1965</a>: Fifty-five tornadoes hit in six <a href=\"https://wikipedia.org/wiki/Midwestern\" class=\"mw-redirect\" title=\"Midwestern\">Midwestern</a> states of the United States, killing 266 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Palm_Sunday_tornado_outbreak_of_1965\" class=\"mw-redirect\" title=\"Palm Sunday tornado outbreak of 1965\">Palm Sunday tornado outbreak of 1965</a>: Fifty-five tornadoes hit in six <a href=\"https://wikipedia.org/wiki/Midwestern\" class=\"mw-redirect\" title=\"Midwestern\">Midwestern</a> states of the United States, killing 266 people.", "links": [{"title": "Palm Sunday tornado outbreak of 1965", "link": "https://wikipedia.org/wiki/Palm_Sunday_tornado_outbreak_of_1965"}, {"title": "Midwestern", "link": "https://wikipedia.org/wiki/Midwestern"}]}, {"year": "1968", "text": "US President <PERSON> signs the Civil Rights Act of 1968, prohibiting discrimination in the sale, rental, and financing of housing.", "html": "1968 - US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1968\" title=\"Civil Rights Act of 1968\">Civil Rights Act of 1968</a>, prohibiting discrimination in the sale, rental, and financing of housing.", "no_year_html": "US President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1968\" title=\"Civil Rights Act of 1968\">Civil Rights Act of 1968</a>, prohibiting discrimination in the sale, rental, and financing of housing.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Civil Rights Act of 1968", "link": "https://wikipedia.org/wiki/Civil_Rights_Act_of_1968"}]}, {"year": "1968", "text": "A failed assassination attempt on <PERSON><PERSON>, leader of the German student movement, leaves <PERSON><PERSON><PERSON><PERSON> suffering from brain damage.", "html": "1968 - A failed assassination attempt on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/German_student_movement\" class=\"mw-redirect\" title=\"German student movement\">German student movement</a>, leaves <PERSON><PERSON><PERSON><PERSON> suffering from brain damage.", "no_year_html": "A failed assassination attempt on <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, leader of the <a href=\"https://wikipedia.org/wiki/German_student_movement\" class=\"mw-redirect\" title=\"German student movement\">German student movement</a>, leaves <PERSON><PERSON><PERSON><PERSON> suffering from brain damage.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "German student movement", "link": "https://wikipedia.org/wiki/German_student_movement"}]}, {"year": "1970", "text": "Apollo Program: Apollo 13 is launched.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Apollo_Program\" class=\"mw-redirect\" title=\"Apollo Program\">Apollo Program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_13\" title=\"Apollo 13\">Apollo 13</a> is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_Program\" class=\"mw-redirect\" title=\"Apollo Program\">Apollo Program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_13\" title=\"Apollo 13\">Apollo 13</a> is launched.", "links": [{"title": "Apollo Program", "link": "https://wikipedia.org/wiki/Apollo_Program"}, {"title": "Apollo 13", "link": "https://wikipedia.org/wiki/Apollo_13"}]}, {"year": "1976", "text": "The Apple I is created.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/Apple_I\" title=\"Apple I\">Apple I</a> is created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Apple_I\" title=\"Apple I\">Apple I</a> is created.", "links": [{"title": "Apple I", "link": "https://wikipedia.org/wiki/Apple_I"}]}, {"year": "1977", "text": "London Transport's Silver Jubilee AEC Routemaster buses are launched.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/London_Transport_Executive_(GLC)\" title=\"London Transport Executive (GLC)\">London Transport's</a> <a href=\"https://wikipedia.org/wiki/Silver_Jubilee_of_Elizabeth_II\" title=\"Silver Jubilee of Elizabeth II\">Silver Jubilee</a> <a href=\"https://wikipedia.org/wiki/AEC_Routemaster\" title=\"AEC Routemaster\">AEC Routemaster</a> buses are launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/London_Transport_Executive_(GLC)\" title=\"London Transport Executive (GLC)\">London Transport's</a> <a href=\"https://wikipedia.org/wiki/Silver_Jubilee_of_Elizabeth_II\" title=\"Silver Jubilee of Elizabeth II\">Silver Jubilee</a> <a href=\"https://wikipedia.org/wiki/AEC_Routemaster\" title=\"AEC Routemaster\">AEC Routemaster</a> buses are launched.", "links": [{"title": "London Transport Executive (GLC)", "link": "https://wikipedia.org/wiki/London_Transport_Executive_(GLC)"}, {"title": "Silver Jubilee of Elizabeth II", "link": "https://wikipedia.org/wiki/Silver_Jubilee_of_<PERSON>_II"}, {"title": "AEC Routemaster", "link": "https://wikipedia.org/wiki/AEC_Routemaster"}]}, {"year": "1979", "text": "Ugandan dictator <PERSON><PERSON> is deposed.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Ugandan</a> dictator <a href=\"https://wikipedia.org/wiki/Idi_<PERSON><PERSON>\" title=\"<PERSON>di <PERSON><PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Fall_of_Kampala\" title=\"Fall of Kampala\">deposed</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Ugandan</a> dictator <a href=\"https://wikipedia.org/wiki/Idi_<PERSON><PERSON>\" title=\"<PERSON>di <PERSON>\"><PERSON><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Fall_of_Kampala\" title=\"Fall of Kampala\">deposed</a>.", "links": [{"title": "Uganda", "link": "https://wikipedia.org/wiki/Uganda"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idi_<PERSON>in"}, {"title": "Fall of Kampala", "link": "https://wikipedia.org/wiki/Fall_of_Kampala"}]}, {"year": "1981", "text": "A massive riot in Brixton, south London results in almost 300 police injuries and 65 serious civilian injuries.", "html": "1981 - A massive <a href=\"https://wikipedia.org/wiki/1981_Brixton_riot\" title=\"1981 Brixton riot\">riot in Brixton, south London</a> results in almost 300 police injuries and 65 serious civilian injuries.", "no_year_html": "A massive <a href=\"https://wikipedia.org/wiki/1981_Brixton_riot\" title=\"1981 Brixton riot\">riot in Brixton, south London</a> results in almost 300 police injuries and 65 serious civilian injuries.", "links": [{"title": "1981 Brixton riot", "link": "https://wikipedia.org/wiki/1981_Brixton_riot"}]}, {"year": "1986", "text": "FBI Miami Shootout: A gun battle in broad daylight in Dade County, Florida between two bank/armored car robbers and pursuing FBI agents. During the firefight, FBI agents <PERSON> and <PERSON> were killed, while five other agents were wounded. As a result, the popular .40 S&W cartridge was developed.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/1986_FBI_Miami_shootout\" title=\"1986 FBI Miami shootout\">FBI Miami Shootout</a>: A gun battle in broad daylight in Dade County, Florida between two bank/armored car robbers and pursuing FBI agents. During the firefight, FBI agents <PERSON> and <PERSON> were killed, while five other agents were wounded. As a result, the popular <a href=\"https://wikipedia.org/wiki/40_S%26W\" class=\"mw-redirect\" title=\"40 S&amp;W\">.40 S&amp;W cartridge</a> was developed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1986_FBI_Miami_shootout\" title=\"1986 FBI Miami shootout\">FBI Miami Shootout</a>: A gun battle in broad daylight in Dade County, Florida between two bank/armored car robbers and pursuing FBI agents. During the firefight, FBI agents <PERSON> and <PERSON> were killed, while five other agents were wounded. As a result, the popular <a href=\"https://wikipedia.org/wiki/40_S%26W\" class=\"mw-redirect\" title=\"40 S&amp;W\">.40 S&amp;W cartridge</a> was developed.", "links": [{"title": "1986 FBI Miami shootout", "link": "https://wikipedia.org/wiki/1986_FBI_Miami_shootout"}, {"title": "40 S&W", "link": "https://wikipedia.org/wiki/40_S%26W"}]}, {"year": "1987", "text": "The London Agreement is secretly signed between Israeli Foreign Affairs Minister <PERSON><PERSON> and King <PERSON> of Jordan.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Peres%E2%80%93Hussein_London_Agreement\" title=\"Peres-Hussein London Agreement\">London Agreement</a> is secretly signed between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> Foreign Affairs Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and King <a href=\"https://wikipedia.org/wiki/Hussein_<PERSON>_Jordan\" title=\"<PERSON> Jordan\"><PERSON> Jordan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peres%E2%80%93Hussein_London_Agreement\" title=\"Peres-Hussein London Agreement\">London Agreement</a> is secretly signed between <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israeli</a> Foreign Affairs Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and King <a href=\"https://wikipedia.org/wiki/Hussein_<PERSON>_Jordan\" title=\"<PERSON> Jordan\"><PERSON> Jordan</a>.", "links": [{"title": "<PERSON><PERSON>-Hussein London Agreement", "link": "https://wikipedia.org/wiki/Peres%E2%80%93Hussein_London_Agreement"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>es"}, {"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}]}, {"year": "1990", "text": "Customs officers in Middlesbrough, England, seize what they believe to be the barrel of a massive gun on a ship bound for Iraq.", "html": "1990 - Customs officers in <a href=\"https://wikipedia.org/wiki/Middlesbrough\" title=\"Middlesbrough\">Middlesbrough</a>, England, seize what they believe to be the barrel of a <a href=\"https://wikipedia.org/wiki/Project_Babylon\" title=\"Project Babylon\">massive gun</a> on a ship bound for <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "no_year_html": "Customs officers in <a href=\"https://wikipedia.org/wiki/Middlesbrough\" title=\"Middlesbrough\">Middlesbrough</a>, England, seize what they believe to be the barrel of a <a href=\"https://wikipedia.org/wiki/Project_Babylon\" title=\"Project Babylon\">massive gun</a> on a ship bound for <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "links": [{"title": "Middlesbrough", "link": "https://wikipedia.org/wiki/Middlesbrough"}, {"title": "Project Babylon", "link": "https://wikipedia.org/wiki/Project_Babylon"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "1993", "text": "Four hundred fifty prisoners rioted at the Southern Ohio Correctional Facility in Lucasville, Ohio, and continued to do so for ten days, citing grievances related to prison conditions, as well as the forced vaccination of Nation of Islam prisoners (for tuberculosis) against their religious beliefs.", "html": "1993 - Four hundred fifty prisoners <a href=\"https://wikipedia.org/wiki/Prison_riot\" title=\"Prison riot\">rioted</a> at the <a href=\"https://wikipedia.org/wiki/Southern_Ohio_Correctional_Facility\" title=\"Southern Ohio Correctional Facility\">Southern Ohio Correctional Facility</a> in <a href=\"https://wikipedia.org/wiki/Lucasville,_Ohio\" title=\"Lucasville, Ohio\">Lucasville, Ohio</a>, and continued to do so for ten days, citing grievances related to <a href=\"https://wikipedia.org/wiki/Incarceration_in_the_United_States#Conditions\" title=\"Incarceration in the United States\">prison conditions</a>, as well as the forced <a href=\"https://wikipedia.org/wiki/Vaccination\" title=\"Vaccination\">vaccination</a> of <a href=\"https://wikipedia.org/wiki/Nation_of_Islam\" title=\"Nation of Islam\">Nation of Islam</a> prisoners (for <a href=\"https://wikipedia.org/wiki/Tuberculosis\" title=\"Tuberculosis\">tuberculosis</a>) against their religious beliefs.", "no_year_html": "Four hundred fifty prisoners <a href=\"https://wikipedia.org/wiki/Prison_riot\" title=\"Prison riot\">rioted</a> at the <a href=\"https://wikipedia.org/wiki/Southern_Ohio_Correctional_Facility\" title=\"Southern Ohio Correctional Facility\">Southern Ohio Correctional Facility</a> in <a href=\"https://wikipedia.org/wiki/Lucasville,_Ohio\" title=\"Lucasville, Ohio\">Lucasville, Ohio</a>, and continued to do so for ten days, citing grievances related to <a href=\"https://wikipedia.org/wiki/Incarceration_in_the_United_States#Conditions\" title=\"Incarceration in the United States\">prison conditions</a>, as well as the forced <a href=\"https://wikipedia.org/wiki/Vaccination\" title=\"Vaccination\">vaccination</a> of <a href=\"https://wikipedia.org/wiki/Nation_of_Islam\" title=\"Nation of Islam\">Nation of Islam</a> prisoners (for <a href=\"https://wikipedia.org/wiki/Tuberculosis\" title=\"Tuberculosis\">tuberculosis</a>) against their religious beliefs.", "links": [{"title": "Prison riot", "link": "https://wikipedia.org/wiki/Prison_riot"}, {"title": "Southern Ohio Correctional Facility", "link": "https://wikipedia.org/wiki/Southern_Ohio_Correctional_Facility"}, {"title": "Lucasville, Ohio", "link": "https://wikipedia.org/wiki/Lucasville,_Ohio"}, {"title": "Incarceration in the United States", "link": "https://wikipedia.org/wiki/Incarceration_in_the_United_States#Conditions"}, {"title": "Vaccination", "link": "https://wikipedia.org/wiki/Vaccination"}, {"title": "Nation of Islam", "link": "https://wikipedia.org/wiki/Nation_of_Islam"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tuberculosis"}]}, {"year": "2001", "text": "The detained crew of a United States EP-3E aircraft that landed in Hainan, China after a collision with a J-8 fighter, is released.", "html": "2001 - The detained crew of a United States <a href=\"https://wikipedia.org/wiki/EP-3E\" class=\"mw-redirect\" title=\"EP-3E\">EP-3E</a> aircraft that <a href=\"https://wikipedia.org/wiki/Hainan_Island_incident\" title=\"Hainan Island incident\">landed</a> in <a href=\"https://wikipedia.org/wiki/Hainan\" title=\"Hainan\">Hainan</a>, China after a collision with a <a href=\"https://wikipedia.org/wiki/Shenyang_J-8\" title=\"Shenyang J-8\">J-8</a> fighter, is released.", "no_year_html": "The detained crew of a United States <a href=\"https://wikipedia.org/wiki/EP-3E\" class=\"mw-redirect\" title=\"EP-3E\">EP-3E</a> aircraft that <a href=\"https://wikipedia.org/wiki/Hainan_Island_incident\" title=\"Hainan Island incident\">landed</a> in <a href=\"https://wikipedia.org/wiki/Hainan\" title=\"Hainan\">Hainan</a>, China after a collision with a <a href=\"https://wikipedia.org/wiki/Shenyang_J-8\" title=\"Shenyang J-8\">J-8</a> fighter, is released.", "links": [{"title": "EP-3E", "link": "https://wikipedia.org/wiki/EP-3E"}, {"title": "Hainan Island incident", "link": "https://wikipedia.org/wiki/Hainan_Island_incident"}, {"title": "Hainan", "link": "https://wikipedia.org/wiki/Hainan"}, {"title": "Shenyang J-8", "link": "https://wikipedia.org/wiki/Shenyang_J-8"}]}, {"year": "2002", "text": "The Ghriba synagogue bombing by al-Qaeda kills 21 in Tunisia.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Ghriba_synagogue_bombing\" title=\"Ghriba synagogue bombing\">Ghriba synagogue bombing</a> by <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> kills 21 in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ghriba_synagogue_bombing\" title=\"Ghriba synagogue bombing\">Ghriba synagogue bombing</a> by <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> kills 21 in <a href=\"https://wikipedia.org/wiki/Tunisia\" title=\"Tunisia\">Tunisia</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> synagogue bombing", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_synagogue_bombing"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}, {"title": "Tunisia", "link": "https://wikipedia.org/wiki/Tunisia"}]}, {"year": "2002", "text": "Over two hundred thousand people march in Caracas towards the presidential palace to demand the resignation of President <PERSON>. Nineteen protesters are killed.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/2002_Venezuelan_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2002 Venezuelan coup d'état attempt\">Over two hundred thousand people march in Caracas</a> towards the presidential <a href=\"https://wikipedia.org/wiki/Miraflores_Palace\" title=\"Miraflores Palace\">palace</a> to demand the resignation of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>. Nineteen protesters are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2002_Venezuelan_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2002 Venezuelan coup d'état attempt\">Over two hundred thousand people march in Caracas</a> towards the presidential <a href=\"https://wikipedia.org/wiki/Miraflores_Palace\" title=\"Miraflores Palace\">palace</a> to demand the resignation of President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>. Nineteen protesters are killed.", "links": [{"title": "2002 Venezuelan coup d'état attempt", "link": "https://wikipedia.org/wiki/2002_Venezuelan_coup_d%27%C3%A9tat_attempt"}, {"title": "Miraflores Palace", "link": "https://wikipedia.org/wiki/Miraflores_Palace"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ch%C3%A1vez"}]}, {"year": "2006", "text": "Iranian president <PERSON><PERSON><PERSON> announces Iran's claim to have successfully enriched uranium.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Iranian_president\" class=\"mw-redirect\" title=\"Iranian president\">Iranian president</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>'s claim to have successfully enriched <a href=\"https://wikipedia.org/wiki/Uranium\" title=\"Uranium\">uranium</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iranian_president\" class=\"mw-redirect\" title=\"Iranian president\">Iranian president</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> announces <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iran</a>'s claim to have successfully enriched <a href=\"https://wikipedia.org/wiki/Uranium\" title=\"Uranium\">uranium</a>.", "links": [{"title": "Iranian president", "link": "https://wikipedia.org/wiki/Iranian_president"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "Uranium", "link": "https://wikipedia.org/wiki/Uranium"}]}, {"year": "2007", "text": "Algiers bombings: Two bombings in Algiers kill 33 people and wound a further 222 others.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/11_April_2007_Algiers_bombings\" class=\"mw-redirect\" title=\"11 April 2007 Algiers bombings\">Algiers bombings</a>: Two bombings in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a> kill 33 people and wound a further 222 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/11_April_2007_Algiers_bombings\" class=\"mw-redirect\" title=\"11 April 2007 Algiers bombings\">Algiers bombings</a>: Two bombings in <a href=\"https://wikipedia.org/wiki/Algiers\" title=\"Algiers\">Algiers</a> kill 33 people and wound a further 222 others.", "links": [{"title": "11 April 2007 Algiers bombings", "link": "https://wikipedia.org/wiki/11_April_2007_Algiers_bombings"}, {"title": "Algiers", "link": "https://wikipedia.org/wiki/Algiers"}]}, {"year": "2008", "text": "Kata Air Transport Flight 007 crashes while attempting an emergency landing at Chișinău International Airport, killing eight.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Kata_Air_Transport_Flight_007\" title=\"Kata Air Transport Flight 007\">Kata Air Transport Flight 007</a> crashes while attempting an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport\" title=\"Chișinău International Airport\">Chișinău International Airport</a>, killing eight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kata_Air_Transport_Flight_007\" title=\"Kata Air Transport Flight 007\">Kata Air Transport Flight 007</a> crashes while attempting an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> at <a href=\"https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport\" title=\"Chișinău International Airport\">Chișinău International Airport</a>, killing eight.", "links": [{"title": "Kata Air Transport Flight 007", "link": "https://wikipedia.org/wiki/Kata_Air_Transport_Flight_007"}, {"title": "Emergency landing", "link": "https://wikipedia.org/wiki/Emergency_landing"}, {"title": "Chișinău International Airport", "link": "https://wikipedia.org/wiki/Chi%C8%99in%C4%83u_International_Airport"}]}, {"year": "2011", "text": "An explosion in the Minsk Metro, Belarus kills 15 people and injures 204 others.", "html": "2011 - An <a href=\"https://wikipedia.org/wiki/2011_Minsk_Metro_bombing\" title=\"2011 Minsk Metro bombing\">explosion</a> in the <a href=\"https://wikipedia.org/wiki/Minsk_Metro\" title=\"Minsk Metro\">Minsk Metro</a>, <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> kills 15 people and injures 204 others.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2011_Minsk_Metro_bombing\" title=\"2011 Minsk Metro bombing\">explosion</a> in the <a href=\"https://wikipedia.org/wiki/Minsk_Metro\" title=\"Minsk Metro\">Minsk Metro</a>, <a href=\"https://wikipedia.org/wiki/Belarus\" title=\"Belarus\">Belarus</a> kills 15 people and injures 204 others.", "links": [{"title": "2011 Minsk Metro bombing", "link": "https://wikipedia.org/wiki/2011_Minsk_Metro_bombing"}, {"title": "Minsk Metro", "link": "https://wikipedia.org/wiki/Minsk_Metro"}, {"title": "Belarus", "link": "https://wikipedia.org/wiki/Belarus"}]}, {"year": "2012", "text": "A pair of great earthquakes occur in the Wharton Basin west of Sumatra in Indonesia. The maximum Mercalli intensity of this strike-slip doublet earthquake is VII (Very strong). Ten are killed, twelve are injured, and a non-destructive tsunami is observed on the island of Nias.", "html": "2012 - A pair of great <a href=\"https://wikipedia.org/wiki/2012_Indian_Ocean_earthquakes\" title=\"2012 Indian Ocean earthquakes\">earthquakes</a> occur in the <a href=\"https://wikipedia.org/wiki/Wharton_Basin\" title=\"Wharton Basin\">Wharton Basin</a> west of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>. The maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of this <a href=\"https://wikipedia.org/wiki/Fault_(geology)#Strike-slip_faults\" title=\"Fault (geology)\">strike-slip</a> <a href=\"https://wikipedia.org/wiki/Doublet_earthquake\" title=\"Doublet earthquake\">doublet earthquake</a> is VII (<i>Very strong</i>). Ten are killed, twelve are injured, and a non-destructive <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> is observed on the island of <a href=\"https://wikipedia.org/wiki/Nias\" title=\"Nias\">Nias</a>.", "no_year_html": "A pair of great <a href=\"https://wikipedia.org/wiki/2012_Indian_Ocean_earthquakes\" title=\"2012 Indian Ocean earthquakes\">earthquakes</a> occur in the <a href=\"https://wikipedia.org/wiki/Wharton_Basin\" title=\"Wharton Basin\">Wharton Basin</a> west of <a href=\"https://wikipedia.org/wiki/Sumatra\" title=\"Sumatra\">Sumatra</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>. The maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of this <a href=\"https://wikipedia.org/wiki/Fault_(geology)#Strike-slip_faults\" title=\"Fault (geology)\">strike-slip</a> <a href=\"https://wikipedia.org/wiki/Doublet_earthquake\" title=\"Doublet earthquake\">doublet earthquake</a> is VII (<i>Very strong</i>). Ten are killed, twelve are injured, and a non-destructive <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> is observed on the island of <a href=\"https://wikipedia.org/wiki/Nias\" title=\"Nias\">Nias</a>.", "links": [{"title": "2012 Indian Ocean earthquakes", "link": "https://wikipedia.org/wiki/2012_Indian_Ocean_earthquakes"}, {"title": "Wharton Basin", "link": "https://wikipedia.org/wiki/Wharton_Basin"}, {"title": "Sumatra", "link": "https://wikipedia.org/wiki/Sumatra"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}, {"title": "<PERSON><PERSON> (geology)", "link": "https://wikipedia.org/wiki/Fault_(geology)#Strike-slip_faults"}, {"title": "Doublet earthquake", "link": "https://wikipedia.org/wiki/Doublet_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nias"}]}, {"year": "2018", "text": "An Ilyushin Il-76 which was owned and operated by the Algerian Air Force crashes near Boufarik, Algeria, killing 257.", "html": "2018 - An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> which was owned and operated by the <a href=\"https://wikipedia.org/wiki/Algerian_Air_Force\" title=\"Algerian Air Force\">Algerian Air Force</a> <a href=\"https://wikipedia.org/wiki/2018_Algerian_Air_Force_Il-76_crash\" class=\"mw-redirect\" title=\"2018 Algerian Air Force Il-76 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Boufarik\" title=\"Boufarik\">Boufarik</a>, Algeria, killing 257.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Ilyushin_Il-76\" title=\"Ilyushin Il-76\">Ilyushin Il-76</a> which was owned and operated by the <a href=\"https://wikipedia.org/wiki/Algerian_Air_Force\" title=\"Algerian Air Force\">Algerian Air Force</a> <a href=\"https://wikipedia.org/wiki/2018_Algerian_Air_Force_Il-76_crash\" class=\"mw-redirect\" title=\"2018 Algerian Air Force Il-76 crash\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Boufarik\" title=\"Boufarik\">Boufarik</a>, Algeria, killing 257.", "links": [{"title": "Ilyushin Il-76", "link": "https://wikipedia.org/wiki/Ilyushin_Il-76"}, {"title": "Algerian Air Force", "link": "https://wikipedia.org/wiki/Algerian_Air_Force"}, {"title": "2018 Algerian Air Force Il-76 crash", "link": "https://wikipedia.org/wiki/2018_Algerian_Air_Force_Il-76_crash"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ufarik"}]}, {"year": "2021", "text": "Twenty year old <PERSON><PERSON><PERSON> is shot and killed in Brooklyn Center, Minnesota by officer <PERSON>, sparking protests in the city, when the officer allegedly mistakes her own gun for her taser.", "html": "2021 - Twenty year old <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is shot and killed in <a href=\"https://wikipedia.org/wiki/Brooklyn_Center,_Minnesota\" title=\"Brooklyn Center, Minnesota\">Brooklyn Center, Minnesota</a> by officer <PERSON>, sparking protests in the city, when the officer allegedly mistakes her own gun for her taser.", "no_year_html": "Twenty year old <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is shot and killed in <a href=\"https://wikipedia.org/wiki/Brooklyn_Center,_Minnesota\" title=\"Brooklyn Center, Minnesota\">Brooklyn Center, Minnesota</a> by officer <PERSON>, sparking protests in the city, when the officer allegedly mistakes her own gun for her taser.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Brooklyn Center, Minnesota", "link": "https://wikipedia.org/wiki/Brooklyn_Center,_Minnesota"}]}, {"year": "2023", "text": "During the Pazigyi massacre, an airstrike conducted by the Myanmar Air Force kills at least 100 villagers in Pazigyi, Sagaing Region.", "html": "2023 - During the <a href=\"https://wikipedia.org/wiki/Pazigyi_massacre\" title=\"Pazigyi massacre\">Pazigyi massacre</a>, an airstrike conducted by the <a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> kills at least 100 villagers in <a href=\"https://wikipedia.org/wiki/Pazigyi\" title=\"Pazigyi\">Pazigyi</a>, <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing Region</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Pazigyi_massacre\" title=\"Pazigyi massacre\">Pazigyi massacre</a>, an airstrike conducted by the <a href=\"https://wikipedia.org/wiki/Myanmar_Air_Force\" title=\"Myanmar Air Force\">Myanmar Air Force</a> kills at least 100 villagers in <a href=\"https://wikipedia.org/wiki/Pazigyi\" title=\"Pazigyi\">Pazigyi</a>, <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing Region</a>.", "links": [{"title": "Pazigyi massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Myanmar Air Force", "link": "https://wikipedia.org/wiki/Myanmar_Air_Force"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>igyi"}, {"title": "Sagaing Region", "link": "https://wikipedia.org/wiki/Sagaing_Region"}]}], "Births": [{"year": "145", "text": "<PERSON><PERSON><PERSON>, Roman emperor (probable; d. 211)", "html": "145 - <a href=\"https://wikipedia.org/wiki/Septimius_Se<PERSON>us\" title=\"Septimius Severus\">Sept<PERSON>ius <PERSON></a>, Roman emperor (probable; d. 211)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Septimius_Se<PERSON>us\" title=\"Septimius Severus\">Septimius <PERSON></a>, Roman emperor (probable; d. 211)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Septimius_Severus"}]}, {"year": "1184", "text": "<PERSON> Winchester, Lord of Lüneburg (d. 1213)", "html": "1184 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Winchester,_Lord_of_L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> of Winchester, Lord of Lüneburg\"><PERSON> of Winchester, Lord of Lüneburg</a> (d. 1213)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Winchester,_Lord_of_L%C3%BCneburg\" class=\"mw-redirect\" title=\"<PERSON> of Winchester, Lord of Lüneburg\"><PERSON> Winchester, Lord of Lüneburg</a> (d. 1213)", "links": [{"title": "<PERSON> Winchester, Lord of Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Winchester,_Lord_of_L%C3%BCneburg"}]}, {"year": "1348", "text": "<PERSON><PERSON><PERSON>, Byzantine Emperor (d. 1385)", "html": "1348 - <a href=\"https://wikipedia.org/wiki/Andronikos_IV_Palaiologos\" title=\"Andronikos IV Palaiologos\"><PERSON>ron<PERSON> IV Palaiologos</a>, Byzantine Emperor (d. 1385)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andronikos_IV_Palaiologos\" title=\"Andronikos IV Palaiologos\">Andronikos IV Palaiologos</a>, Byzantine Emperor (d. 1385)", "links": [{"title": "Andronikos IV Palaiologos", "link": "https://wikipedia.org/wiki/Andronikos_IV_Palaiologos"}]}, {"year": "1357", "text": "<PERSON> of Portugal (d. 1433)", "html": "1357 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> I of Portugal\"><PERSON> of Portugal</a> (d. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1433)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1370", "text": "<PERSON>, Elector of Saxony (d. 1428)", "html": "1370 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (d. 1428)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Saxony"}]}, {"year": "1374", "text": "<PERSON>, 4th Earl of <PERSON>, heir to the throne of England (d. 1398)", "html": "1374 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_March\" title=\"<PERSON>, 4th Earl of March\"><PERSON>, 4th Earl of <PERSON></a>, heir to the throne of England (d. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_<PERSON>\" title=\"<PERSON>, 4th Earl of <PERSON>\"><PERSON>, 4th Earl of March</a>, heir to the throne of England (d. 1398)", "links": [{"title": "<PERSON>, 4th Earl of March", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_<PERSON>"}]}, {"year": "1493", "text": "<PERSON>, Duke of Pomerania (d. 1531)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (d. 1531)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> (d. 1531)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1591", "text": "<PERSON><PERSON><PERSON><PERSON>, Silezian painter (d. 1650)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/Bartholome<PERSON>_<PERSON>l\" title=\"Barthol<PERSON><PERSON> Strobel\"><PERSON><PERSON><PERSON><PERSON></a>, Silezian painter (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Barthol<PERSON><PERSON>_<PERSON>l\" title=\"Barthol<PERSON><PERSON> Strobel\"><PERSON><PERSON><PERSON><PERSON></a>, Silezian painter (d. 1650)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bartholomeus_Strobel"}]}, {"year": "1592", "text": "<PERSON>, English lawyer and politician (d. 1632)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English lawyer and politician (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(statesman)\" title=\"<PERSON> (statesman)\"><PERSON></a>, English lawyer and politician (d. 1632)", "links": [{"title": "<PERSON> (statesman)", "link": "https://wikipedia.org/wiki/<PERSON>(statesman)"}]}, {"year": "1644", "text": "<PERSON> of Savoy-Nemours, Duchess of Savoy (d. 1724)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Marie_<PERSON>_<PERSON>_of_Savoy-Nemours\" title=\"<PERSON> of Savoy-Nemours\"><PERSON> of Savoy-Nemours</a>, Duchess of Savoy (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie_<PERSON>_<PERSON>_of_Savoy-Nemours\" title=\"<PERSON> of Savoy-Nemours\"><PERSON> of Savoy-Nemours</a>, Duchess of Savoy (d. 1724)", "links": [{"title": "<PERSON> of Savoy-Nemours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Savoy-Nemours"}]}, {"year": "1658", "text": "<PERSON>, 4th Duke of Hamilton, Scottish peer (d. 1712)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Hamilton\" title=\"<PERSON>, 4th Duke of Hamilton\"><PERSON>, 4th Duke of Hamilton</a>, Scottish peer (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 4th Duke of Hamilton\"><PERSON>, 4th Duke of Hamilton</a>, Scottish peer (d. 1712)", "links": [{"title": "<PERSON>, 4th Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, French composer and conductor (d. 1738)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and conductor (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French composer and conductor (d. 1738)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1715", "text": "<PERSON>, English organist and composer (d. 1806)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and composer (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(organist)\" title=\"<PERSON> (organist)\"><PERSON></a>, English organist and composer (d. 1806)", "links": [{"title": "<PERSON> (organist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(organist)"}]}, {"year": "1721", "text": "<PERSON>, Czech-American clergyman and missionary (d. 1808)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American clergyman and missionary (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American clergyman and missionary (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, English actor, playwright, and poet (d. 1771)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and poet (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, playwright, and poet (d. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French miniaturist and portrait painter (d. 1803)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Ad%C3%A9la%C3%AFde_<PERSON>ille-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French miniaturist and portrait painter (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad%C3%A9la%C3%AFde_Labille-Guiard\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>ille-<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French miniaturist and portrait painter (d. 1803)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ad%C3%A9la%C3%AFde_Labille-<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, English surgeon, geologist, and paleontologist (d. 1824)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon, geologist, and paleontologist (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surgeon, geologist, and paleontologist (d. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, Irish-English lawyer and politician, Prime Minister of the United Kingdom (d. 1827)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1794", "text": "<PERSON>, English-American educator and politician, 15th Governor of Massachusetts (d. 1865)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, Italian physicist and academic (d. 1854)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Macedonio_Melloni\" title=\"Macedon<PERSON> Melloni\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Macedonio_Melloni\" title=\"<PERSON><PERSON><PERSON>i\"><PERSON><PERSON><PERSON></a>, Italian physicist and academic (d. 1854)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Macedonio_Melloni"}]}, {"year": "1819", "text": "<PERSON>, German-English pianist and conductor (d. 1895)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German-English pianist and conductor (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German-English pianist and conductor (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Hall%C3%A9"}]}, {"year": "1825", "text": "<PERSON>, German philosopher and jurist (d. 1864)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and jurist (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and jurist (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian scholar, philosopher, and activist (d. 1890)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>rao_Phule\" title=\"Jyo<PERSON>rao Phule\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian scholar, philosopher, and activist (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>rao_Phule\" title=\"Jyotirao Phule\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian scholar, philosopher, and activist (d. 1890)", "links": [{"title": "Jyotirao <PERSON>", "link": "https://wikipedia.org/wiki/Jyotirao_Phule"}]}, {"year": "1830", "text": "<PERSON>, English architect (d. 1911)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_architect)\" title=\"<PERSON> (English architect)\"><PERSON></a>, English architect (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(English_architect)\" title=\"<PERSON> (English architect)\"><PERSON></a>, English architect (d. 1911)", "links": [{"title": "<PERSON> (English architect)", "link": "https://wikipedia.org/wiki/<PERSON>(English_architect)"}]}, {"year": "1837", "text": "<PERSON>, American army officer and law clerk (d. 1861)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American army officer and law clerk (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American army officer and law clerk (d. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, Australian cricketer (d. 1938)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, English cricketer and rugby player (d. 1903)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur <PERSON>\"><PERSON></a>, English cricketer and rugby player (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Arthur <PERSON>\"><PERSON></a>, English cricketer and rugby player (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Shrewsbury"}]}, {"year": "1859", "text": "<PERSON><PERSON>, Greek historian and author (d. 1939)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek historian and author (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek historian and author (d. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American astronomer and academic (d. 1938)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American lawyer and politician, 44th United States Secretary of State (d. 1948)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 44th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1864", "text": "<PERSON>, German author and activist (d. 1943)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and activist (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Johanna_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Australian journalist, author, and poet (d. 1953)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dowd\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and poet (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dowd\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, author, and poet (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_O%27Dowd"}]}, {"year": "1867", "text": "<PERSON>, American educator (d. 1928)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Norwegian sculptor, designed the Nobel Peace Prize medal (d. 1943)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian sculptor, designed the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize medal</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian sculptor, designed the <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Peace Prize medal</a> (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Hungarian runner (d. 1940)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Albanian poet, rilindas and author of national anthem of Albania (d. 1947)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Aleksand%C3%ABr_Stavre_Drenova\" title=\"Aleksandër Stav<PERSON>\"><PERSON>eksand<PERSON><PERSON></a>, Albanian poet, <a href=\"https://wikipedia.org/wiki/Rilindas\" class=\"mw-redirect\" title=\"Rilindas\">rilindas</a> and author of <a href=\"https://wikipedia.org/wiki/Himni_i_Flamurit\" title=\"Himni i Flamurit\">national anthem</a> of Albania (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksand%C3%ABr_Stav<PERSON>_<PERSON>eno<PERSON>\" title=\"Aleksandër Stav<PERSON>\">Aleksand<PERSON><PERSON></a>, Albanian poet, <a href=\"https://wikipedia.org/wiki/Rilindas\" class=\"mw-redirect\" title=\"Rilindas\">rilindas</a> and author of <a href=\"https://wikipedia.org/wiki/Himni_i_Flamurit\" title=\"Himni i Flamurit\">national anthem</a> of Albania (d. 1947)", "links": [{"title": "Aleksandë<PERSON>", "link": "https://wikipedia.org/wiki/Aleksand%C3%ABr_<PERSON><PERSON><PERSON>_<PERSON>enova"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rilindas"}, {"title": "<PERSON><PERSON> i Flamurit", "link": "https://wikipedia.org/wiki/Himni_i_Flamurit"}]}, {"year": "1873", "text": "<PERSON>, English soldier, Victoria Cross recipient (d. 1955)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC)\" title=\"<PERSON> (VC)\"><PERSON></a>, English soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(VC)\" title=\"<PERSON> (VC)\"><PERSON></a>, English soldier, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (d. 1955)", "links": [{"title": "<PERSON> (VC)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(VC)"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1876", "text": "<PERSON>, Irish painter (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Irish painter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Irish painter (d. 1958)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(painter)"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Georgian historian and academic (d. 1940)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian historian and academic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian historian and academic (d. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, British pioneer of volunteer blood donation (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British pioneer of volunteer blood donation (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British pioneer of volunteer blood donation (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Oliver"}]}, {"year": "1879", "text": "<PERSON>, Estonian-German astronomer and optician (d. 1935)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German astronomer and optician (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German astronomer and optician (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Indian painter (d. 1972)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian painter (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian painter (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American lawyer and politician, 51st United States Secretary of State (d. 1971)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Dean_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1896", "text": "Léo<PERSON><PERSON>, Canadian journalist and author (d. 1967)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and author (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian journalist and author (d. 1967)", "links": [{"title": "Léo-<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o-<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, African-American chemist and academic (d. 1975)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American chemist and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American chemist and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Hungarian journalist and author (d. 1989)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_M%C3%A1rai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_M%C3%A1rai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian journalist and author (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_M%C3%A1rai"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Japanese poet (d. 1930)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese poet (d. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian singer and actor (d. 1947)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian singer and actor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian singer and actor (d. 1947)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Hungarian poet and educator (d. 1937)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Attila_J%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and educator (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attila_J%C3%B3<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian poet and educator (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attila_J%C3%B3zsef"}]}, {"year": "1906", "text": "<PERSON>, American author and illustrator (d. 2005)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American actor (d. 1959)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1959)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1908", "text": "<PERSON>, American lawyer and judge (d. 2007)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, co-founded Sony (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Masaru_I<PERSON>\" title=\"Masaru Ibuka\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>sar<PERSON>_<PERSON>\" title=\"Masaru Ibuka\"><PERSON><PERSON><PERSON></a>, Japanese businessman, co-founded <a href=\"https://wikipedia.org/wiki/Sony\" title=\"Sony\">Sony</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Sony", "link": "https://wikipedia.org/wiki/Sony"}]}, {"year": "1908", "text": "<PERSON>, English tennis player and sportscaster (d. 1992)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player and sportscaster (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Polish-American author and academic (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American author and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_R<PERSON>en"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese general and politician, 14th President of Portugal (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola\" title=\"<PERSON><PERSON><PERSON><PERSON>p<PERSON>ola\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese general and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola\" title=\"<PERSON><PERSON><PERSON><PERSON> Spínola\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese general and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1996)", "links": [{"title": "António de Spínola", "link": "https://wikipedia.org/wiki/Ant%C3%B3nio_de_Sp%C3%ADnola"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1912", "text": "<PERSON>, American bassist and businessman (d. 2012)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist and businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bassist and businessman (d. 2012)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1913", "text": "<PERSON><PERSON>, French-American fashion designer (d. 2006)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American fashion designer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American fashion designer (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oleg_<PERSON>ini"}]}, {"year": "1914", "text": "<PERSON>, Scottish-Canadian animator, director, and producer (d. 1987)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> McLaren\"><PERSON></a>, Scottish-Canadian animator, director, and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman McLaren\"><PERSON></a>, Scottish-Canadian animator, director, and producer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Canadian economist, lawyer, and politician, 17th Premier of Nova Scotia (d. 2003)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist, lawyer, and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist, lawyer, and politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1914", "text": "<PERSON>, American mathematician (d. 1988)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Argentinian pianist and composer (d. 1983)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian pianist and composer (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American director and producer (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American soldier, journalist, and author (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, journalist, and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, English soldier and politician (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English soldier and politician (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English soldier and politician (d. 2003)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1919", "text": "<PERSON>, English historian and academic (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Italian lawyer and politician, 40th Prime Minister of Italy (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 40th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1920", "text": "<PERSON>, American soldier and politician (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1921", "text": "<PERSON>, Australian rugby league player and coach (d. 2008)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Estonian-Canadian soldier and author (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Arved_Viirlaid\" title=\"Arved Viirlaid\"><PERSON><PERSON><PERSON></a>, Estonian-Canadian soldier and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arved_Viirlaid\" title=\"Arved Viirlaid\"><PERSON><PERSON><PERSON></a>, Estonian-Canadian soldier and author (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arved_Viirlaid"}]}, {"year": "1923", "text": "<PERSON>, Sr., American businessman (d. 1980)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American businessman (d. 1980)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}]}, {"year": "1924", "text": "<PERSON>, Pakistani-English activist and politician (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English activist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English activist and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Russian hurdler and commander (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian hurdler and commander (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian hurdler and commander (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American civil rights activist (d. 1965)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Estonian botanist and ecologist (d. 2001)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian botanist and ecologist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian botanist and ecologist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian businessman, founded Quebecor (d. 1997)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Quebecor\" title=\"Quebecor\">Quebecor</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/Quebecor\" title=\"Quebecor\"><PERSON>or</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_P%C3%A9<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quebecor"}]}, {"year": "1926", "text": "<PERSON>, American commander and diplomat, United States Permanent Representative to NATO (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO\" class=\"mw-redirect\" title=\"United States Permanent Representative to NATO\">United States Permanent Representative to NATO</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO\" class=\"mw-redirect\" title=\"United States Permanent Representative to NATO\">United States Permanent Representative to NATO</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Permanent Representative to NATO", "link": "https://wikipedia.org/wiki/United_States_Permanent_Representative_to_NATO"}]}, {"year": "1926", "text": "<PERSON>, Canadian pianist and composer (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Estonian physicist and academic (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian physicist and academic (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Indian historian", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian historian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American philanthropist (d. 2024)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American journalist and author (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Hungarian-Australian pianist, composer, and conductor (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian pianist, composer, and conductor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian pianist, composer, and conductor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American businessman and politician, 68th United States Secretary of the Treasury", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 68th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 68th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1930", "text": "<PERSON>, German javelin thrower (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German javelin thrower (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, German javelin thrower (d. 2018)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_Kr%C3%<PERSON>ger_(athlete)"}]}, {"year": "1930", "text": "<PERSON>, American occultist, founded the Church of Satan (d. 1997)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American occultist, founded the <a href=\"https://wikipedia.org/wiki/Church_of_Satan\" title=\"Church of Satan\">Church of Satan</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American occultist, founded the <a href=\"https://wikipedia.org/wiki/Church_of_Satan\" title=\"Church of Satan\">Church of Satan</a> (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Church of Satan", "link": "https://wikipedia.org/wiki/Church_of_Satan"}]}, {"year": "1931", "text": "<PERSON>, Welsh rugby player and coach (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1931)\" title=\"<PERSON> (rugby, born 1931)\"><PERSON></a>, Welsh rugby player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1931)\" title=\"<PERSON> (rugby, born 1931)\"><PERSON></a>, Welsh rugby player and coach (d. 2024)", "links": [{"title": "<PERSON> (rugby, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby,_born_1931)"}]}, {"year": "1932", "text": "<PERSON>, American actor, singer, and dancer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American journalist and academic", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist and academic", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1934", "text": "<PERSON>, Canadian-born American poet, essayist, and translator (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark <PERSON>\"><PERSON></a>, Canadian-born American poet, essayist, and translator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Mark Strand\"><PERSON></a>, Canadian-born American poet, essayist, and translator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor, director and playwright (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director and playwright (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director and playwright (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter (d. 1997)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter (d. 1997)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1936", "text": "<PERSON>, English bishop (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (d. 2019)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1937", "text": "<PERSON>, English actress and author (d. 2020)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American soccer player and manager (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American politician, Deputy White House Chief of Staff (d. 2007)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Deputy_White_House_Chief_of_Staff\" class=\"mw-redirect\" title=\"Deputy White House Chief of Staff\">Deputy White House Chief of Staff</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/Deputy_White_House_Chief_of_Staff\" class=\"mw-redirect\" title=\"Deputy White House Chief of Staff\">Deputy White House Chief of Staff</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy White House Chief of Staff", "link": "https://wikipedia.org/wiki/Deputy_<PERSON>_House_Chief_of_Staff"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, American chemist and businesswoman", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> King\"><PERSON><PERSON><PERSON></a>, American chemist and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> King\"><PERSON><PERSON><PERSON></a>, American chemist and businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer and guitarist (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Guitar_Junior)\" title=\"<PERSON> (Guitar Junior)\"><PERSON></a>, American singer and guitarist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Guitar_Junior)\" title=\"<PERSON> (Guitar Junior)\"><PERSON></a>, American singer and guitarist (d. 2022)", "links": [{"title": "<PERSON> (Guitar Junior)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Guitar_Junior)"}]}, {"year": "1939", "text": "<PERSON>, American actress", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian politician (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Firmin\" title=\"<PERSON> Firmin\"><PERSON></a>, Australian politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Firmin\"><PERSON></a>, Australian politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Col_Firmin"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish shot putter and actor (d. 1998)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish shot putter and actor (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish shot putter and actor (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_Komar"}]}, {"year": "1941", "text": "<PERSON>, American journalist and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English actress (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Russian colonel, pilot, and astronaut (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, pilot, and astronaut (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian colonel, pilot, and astronaut (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Berezovoy"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American writer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American writer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English pathologist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pathologist)\" title=\"<PERSON> (pathologist)\"><PERSON></a>, English pathologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pathologist)\" title=\"<PERSON> (pathologist)\"><PERSON></a>, English pathologist and academic", "links": [{"title": "<PERSON> (pathologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pathologist)"}]}, {"year": "1943", "text": "<PERSON>, 11th Earl of Sandwich, English businessman and politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Sandwich\" title=\"<PERSON>, 11th Earl of Sandwich\"><PERSON>, 11th Earl of Sandwich</a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_11th_Earl_of_Sandwich\" title=\"<PERSON>, 11th Earl of Sandwich\"><PERSON>, 11th Earl of Sandwich</a>, English businessman and politician", "links": [{"title": "<PERSON>, 11th Earl of Sandwich", "link": "https://wikipedia.org/wiki/<PERSON>,_11th_Earl_of_Sandwich"}]}, {"year": "1943", "text": "<PERSON>, American wrestler and trainer (d. 2019)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Harley_Race\" title=\"Harley Race\">Harley Race</a>, American wrestler and trainer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harley_Race\" title=\"Harley Race\">Harley Race</a>, American wrestler and trainer (d. 2019)", "links": [{"title": "Harley Race", "link": "https://wikipedia.org/wiki/Harley_Race"}]}, {"year": "1944", "text": "<PERSON>, German footballer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9F"}]}, {"year": "1944", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, <PERSON>, English zoologist and academic", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English zoologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English zoologist and academic", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American sculptor, illustrator, and academic (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, illustrator, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor, illustrator, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English journalist and radio host", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_presenter)\" title=\"<PERSON> (radio presenter)\"><PERSON></a>, English journalist and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_presenter)\" title=\"<PERSON> (radio presenter)\"><PERSON></a>, English journalist and radio host", "links": [{"title": "<PERSON> (radio presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(radio_presenter)"}]}, {"year": "1947", "text": "<PERSON>, Ukrainian-Russian physicist and academic (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian physicist and academic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian physicist and academic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, German director and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Edel"}]}, {"year": "1947", "text": "<PERSON>, American pianist and composer (d. 2004)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor, screenwriter and film director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, screenwriter and film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English engineer and academic (d. 2015)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(university_administrator)\" title=\"<PERSON> (university administrator)\"><PERSON></a>, English engineer and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(university_administrator)\" title=\"<PERSON> (university administrator)\"><PERSON></a>, English engineer and academic (d. 2015)", "links": [{"title": "<PERSON> (university administrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(university_administrator)"}]}, {"year": "1949", "text": "<PERSON>, American writer (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, German director and producer (d. 2011)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German director and producer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and clown", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and <a href=\"https://wikipedia.org/wiki/Clown\" title=\"Clown\">clown</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and <a href=\"https://wikipedia.org/wiki/Clown\" title=\"Clown\">clown</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Clown", "link": "https://wikipedia.org/wiki/Clown"}]}, {"year": "1951", "text": "<PERSON>, English singer and guitarist (d. 2007)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer and guitarist (d. 2007)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1952", "text": "<PERSON>, American singer and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Sri Lankan engineer and academic", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Indira_Samarasekera\" title=\"Indira Samarasekera\"><PERSON><PERSON> Samarase<PERSON></a>, Sri Lankan engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indira_Samarasekera\" title=\"Indira Samarasekera\"><PERSON><PERSON> Samarase<PERSON></a>, Sri Lankan engineer and academic", "links": [{"title": "<PERSON><PERSON> Samara<PERSON>ker<PERSON>", "link": "https://wikipedia.org/wiki/Indira_Samarasekera"}]}, {"year": "1952", "text": "<PERSON>, English-Australian journalist and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Belgian politician, 47th Prime Minister of Belgium", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Guy_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 47th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guy_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian politician, 47th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_<PERSON>stadt"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1953", "text": "<PERSON>, English mathematician and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Turkish engineer and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish engineer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Azerbaijani cyclist and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Azerbaijani cyclist and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Azerbaijani cyclist and coach", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cyclist)"}]}, {"year": "1954", "text": "<PERSON>, English guitarist and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish psychologist and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish psychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish psychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English biologist and conservationist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and conservationist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist and conservationist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player (d. 2015)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American singer-songwriter and AIDS activist (d. 1993)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and AIDS activist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and AIDS activist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Scottish singer-songwriter and guitarist (d. 2001)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian sprinter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Australian rugby league player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1959)\" title=\"<PERSON> (ice hockey, born 1959)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1959)\" title=\"<PERSON> (ice hockey, born 1959)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1959)", "link": "https://wikipedia.org/wiki/<PERSON>_(ice_hockey,_born_1959)"}]}, {"year": "1959", "text": "<PERSON>, Cuban-American lawyer and judge", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Polo\" title=\"<PERSON>\"><PERSON></a>, Cuban-American lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Polo\" title=\"<PERSON>\"><PERSON></a>, Cuban-American lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Mar%C3%ADa_Polo"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English journalist and television presenter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, producer, and musician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American guitarist and songwriter (d. 1993)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Japanese martial artist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, French fencer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>an<PERSON>_Du<PERSON>\" title=\"Franck Ducheix\"><PERSON><PERSON><PERSON></a>, French fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>\" title=\"Franck Ducheix\"><PERSON><PERSON><PERSON></a>, French fencer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_Ducheix"}]}, {"year": "1962", "text": "<PERSON>, English journalist and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, New Zealand cricketer and umpire", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Waldemar_Fornalik\" title=\"Waldemar Fornalik\">Waldemar Fornalik</a>, Polish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Waldemar_Fornalik\" title=\"Waldemar Fornalik\">Waldemar Fornalik</a>, Polish footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Waldemar_Fornalik"}]}, {"year": "1963", "text": "<PERSON>, Australian tennis player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Greek singer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>poulou\" title=\"<PERSON><PERSON>po<PERSON>\"><PERSON><PERSON></a>, Greek singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>igopoulou\" title=\"<PERSON><PERSON>poulo<PERSON>\"><PERSON><PERSON></a>, Greek singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eleni_Tsaligo<PERSON>ulou"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English journalist and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American cellist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hagen\" title=\"<PERSON><PERSON>hagen\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hagen\" title=\"<PERSON><PERSON>hagen\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1964", "text": "<PERSON>, Kenyan runner", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, South Korean singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hun\" title=\"<PERSON>-hun\"><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hun\" title=\"<PERSON>-hun\"><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hun"}]}, {"year": "1966", "text": "<PERSON>, English singer-songwriter and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Kazakh-Russian journalist and author", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh-Russian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kazakh-Russian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Welsh singer-songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American wrestler", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swiss skier", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCnigen\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCnigen\" title=\"<PERSON>\"><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCnigen"}]}, {"year": "1970", "text": "<PERSON>, Canadian ice hockey player and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Danish singer and songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Whigfield\" title=\"Whigfield\"><PERSON><PERSON></a>, Danish singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Whigfield\" title=\"Whigfield\"><PERSON><PERSON></a>, Danish singer and songwriter", "links": [{"title": "Whigfield", "link": "https://wikipedia.org/wiki/Whigfield"}]}, {"year": "1971", "text": "<PERSON>, German bass player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American wrestler (d. 2016)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mahoney\"><PERSON><PERSON></a>, American wrestler (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mahoney\"><PERSON><PERSON> <PERSON></a>, American wrestler (d. 2016)", "links": [{"title": "<PERSON>s <PERSON>y", "link": "https://wikipedia.org/wiki/<PERSON>s_<PERSON>y"}]}, {"year": "1972", "text": "<PERSON>, French singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9o\" title=\"<PERSON>\"><PERSON></a>, French singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A9o\" title=\"<PERSON>\"><PERSON></a>, French singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Allan_Th%C3%A9o"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and writer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French rugby player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Spanish tennis player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/%C3%80lex_Corretja\" title=\"Àlex Corretja\"><PERSON><PERSON> Corretja</a>, Spanish tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%80lex_Corretja\" title=\"Àlex Corretja\"><PERSON><PERSON> Corretja</a>, Spanish tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%80lex_Corretja"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Armenian weightlifter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian weightlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish singer-songwriter and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American baseball player and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Trot_<PERSON>\" title=\"Trot <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON>\" title=\"Trot <PERSON>\"><PERSON><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trot_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Norwegian journalist, non-fiction writer, and organizational leader", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian journalist, non-fiction writer, and organizational leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian journalist, non-fiction writer, and organizational leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Kelvi<PERSON>_Escobar\" title=\"Kelvi<PERSON> Escobar\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kelvi<PERSON>_Escobar\" title=\"Kelvi<PERSON> Escobar\"><PERSON><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kelvim_Escobar"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ko<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>iji"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player (d. 2007)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter and guitarist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swiss ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Server\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Server\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josh_Server"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ada"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Brazilian model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dominican basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, Dominican basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1981", "text": "<PERSON>, Australian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>yke\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Estonian skier", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCmmel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BCmmel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peeter_K%C3%BCmmel"}]}, {"year": "1983", "text": "<PERSON>, Canadian skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Palazuelos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Palazuelos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Palazu<PERSON>s"}]}, {"year": "1983", "text": "<PERSON>, Dutch race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French handball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Nikola_Kara<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, French handball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nikola_<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, French handball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Karabati%C4%87"}]}, {"year": "1985", "text": "<PERSON>, Spanish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1ndez_Dom%C3%ADnguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1ndez_Dom%C3%ADnguez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_Hern%C3%A1ndez_Dom%C3%ADnguez"}]}, {"year": "1985", "text": "<PERSON>, Australian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Haitian model and human rights lawyer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian model and human rights lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian model and human rights lawyer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>in"}]}, {"year": "1986", "text": "<PERSON>, German pentathlete", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pentathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pentathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lena_Sch%C3%B6neborn"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Stone\"><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Stone\"><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American sprinter (d. 2014)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sprinter (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Greek footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, South African footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ero"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Thiago_Alc%C3%A2ntara\" title=\"Thiago <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiago_Alc%C3%A2ntara\" title=\"Thia<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thiago_Alc%C3%A2ntara"}]}, {"year": "1991", "text": "<PERSON>, American racing driver", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, English international footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alli\" title=\"Dele Alli\"><PERSON><PERSON></a>, English international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Alli\" title=\"Dele Alli\"><PERSON><PERSON></a>, English international footballer", "links": [{"title": "Dele Alli", "link": "https://wikipedia.org/wiki/Dele_Alli"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Australian actress", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)\" title=\"<PERSON><PERSON> (South Korean singer)\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)\" title=\"<PERSON><PERSON> (South Korean singer)\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON> (South Korean singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(South_Korean_singer)"}]}, {"year": "2001", "text": "<PERSON>, Uruguayan footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Australian cricketer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, South Korean-Australian singer", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-Australian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-Australian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "618", "text": "<PERSON>, Chinese emperor of the Sui Dynasty (b. 569)", "html": "618 - <a href=\"https://wikipedia.org/wiki/Emperor_Yang_of_Sui\" title=\"Emperor Yang of Sui\"><PERSON></a>, Chinese emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui Dynasty</a> (b. 569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Yang_of_Sui\" title=\"Emperor Yang of Sui\"><PERSON></a>, Chinese emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui Dynasty</a> (b. 569)", "links": [{"title": "Emperor <PERSON> of Sui", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Sui"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}]}, {"year": "678", "text": "<PERSON><PERSON>, pope of the Catholic Church (b. 610)", "html": "678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON><PERSON></a>, pope of the Catholic Church (b. 610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON><PERSON></a>, pope of the Catholic Church (b. 610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "924", "text": "<PERSON>, chancellor and archbishop of Cologne", "html": "924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON> (archbishop of Cologne)\"><PERSON></a>, chancellor and archbishop of Cologne", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON> (archbishop of Cologne)\"><PERSON></a>, chancellor and archbishop of Cologne", "links": [{"title": "<PERSON> (archbishop of Cologne)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archbishop_of_Cologne)"}]}, {"year": "1034", "text": "<PERSON><PERSON>, Byzantine emperor (b. 968)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/Romano<PERSON>_III_<PERSON>\" title=\"<PERSON><PERSON> III Argyros\"><PERSON><PERSON></a>, Byzantine emperor (b. 968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_<PERSON>\" title=\"<PERSON><PERSON> Argyros\"><PERSON><PERSON></a>, Byzantine emperor (b. 968)", "links": [{"title": "Romanos III Argyros", "link": "https://wikipedia.org/wiki/Romanos_III_Argyros"}]}, {"year": "1077", "text": "<PERSON><PERSON><PERSON><PERSON>, king of Burma and founder of the Pagan Empire (b. 1014)", "html": "1077 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Burma and founder of the Pagan Empire (b. 1014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, king of Burma and founder of the Pagan Empire (b. 1014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anawrahta"}]}, {"year": "1079", "text": "<PERSON><PERSON><PERSON> of Szczepanów, bishop of Kraków (b. 1030)", "html": "1079 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, bishop of Kraków (b. 1030)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w\" title=\"<PERSON><PERSON><PERSON> of Szczepanów\"><PERSON><PERSON><PERSON> of Szczepanów</a>, bishop of Kraków (b. 1030)", "links": [{"title": "<PERSON><PERSON><PERSON> of Szczepanów", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Szczepan%C3%B3w"}]}, {"year": "1165", "text": "<PERSON>, king of Hungary and Croatia", "html": "1165 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> IV of Hungary\"><PERSON> IV</a>, king of Hungary and Croatia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> IV of Hungary\"><PERSON> IV</a>, king of Hungary and Croatia", "links": [{"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}]}, {"year": "1240", "text": "<PERSON><PERSON><PERSON><PERSON> the <PERSON>, Welsh prince (b. 1172)", "html": "1240 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a>, Welsh prince (b. 1172)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a>, Welsh prince (b. 1172)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great"}]}, {"year": "1349", "text": "<PERSON><PERSON>, first known Muslim from Korea", "html": "1349 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, first known Muslim from Korea", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, first known Muslim from Korea", "links": [{"title": "<PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1447", "text": "<PERSON>, Cardinal, Lord Chancellor of England (b. 1377)", "html": "1447 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON>, Lord Chancellor of England (b. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON>, Lord Chancellor of England (b. 1377)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1512", "text": "<PERSON>, French military commander (b. 1489)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Nemours\" title=\"<PERSON> Foix, Duke of Nemours\"><PERSON></a>, French military commander (b. 1489)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Nemours\" title=\"<PERSON> Foix, Duke of Nemours\"><PERSON></a>, French military commander (b. 1489)", "links": [{"title": "<PERSON> Foix, Duke of Nemours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>em<PERSON>"}]}, {"year": "1554", "text": "<PERSON> the Younger, English rebel leader (b. 1521)", "html": "1554 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the <PERSON>\"><PERSON> the <PERSON></a>, English rebel leader (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the <PERSON></a>, English rebel leader (b. 1521)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger"}]}, {"year": "1587", "text": "<PERSON>, English lord chancellor (b. 1530)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lord chancellor (b. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lord chancellor (b. 1530)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, 1st Baron <PERSON>, English noble (b. 1533)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English noble (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, English noble (b. 1533)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1612", "text": "<PERSON>, Flemish historian and author (b. 1535)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish historian and author (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish historian and author (b. 1535)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON>, English minister and martyr (b. 1566)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and martyr (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and martyr (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, Ragusan mathematician and physicist (b. 1568)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Marino_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\"><PERSON><PERSON><PERSON></a> mathematician and physicist (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marino_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ragusa\" title=\"Republic of Ragusa\"><PERSON><PERSON><PERSON></a> mathematician and physicist (b. 1568)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marino_<PERSON>di"}, {"title": "Republic of Ragusa", "link": "https://wikipedia.org/wiki/Republic_of_Ragusa"}]}, {"year": "1712", "text": "<PERSON>, French priest and critic (b. 1638)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, French priest and critic (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, French priest and critic (b. 1638)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(priest)"}]}, {"year": "1723", "text": "<PERSON>, English bishop and diplomat (b. 1650)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)\" title=\"<PERSON> (bishop of London)\"><PERSON></a>, English bishop and diplomat (b. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)\" title=\"<PERSON> (bishop of London)\"><PERSON></a>, English bishop and diplomat (b. 1650)", "links": [{"title": "<PERSON> (bishop of London)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop_of_London)"}]}, {"year": "1783", "text": "<PERSON><PERSON>, Polish-Russian politician, Russian Minister of Foreign Affairs (b. 1718)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Russian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a> (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-Russian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)\" title=\"Minister of Foreign Affairs (Russia)\">Russian Minister of Foreign Affairs</a> (b. 1718)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Russia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Russia)"}]}, {"year": "1798", "text": "<PERSON>, German poet and academic (b. 1725)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and academic (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and academic (b. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Costa Rican soldier (b. 1831)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>mar%C3%ADa\" title=\"Juan <PERSON>\"><PERSON></a>, Costa Rican soldier (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADa\" title=\"Juan <PERSON>\"><PERSON></a>, Costa Rican soldier (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_<PERSON>mar%C3%ADa"}]}, {"year": "1861", "text": "<PERSON>, Mexican poet and composer (b. 1824)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Bocanegra\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and composer (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez_Bocanegra\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and composer (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Gonz%C3%A1lez_Bocanegra"}]}, {"year": "1873", "text": "<PERSON>, American general (b. 1817)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Dutch Talmudist (b. 1808)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Dutch Talmudist (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Talmudist (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English man with severe deformities (b. 1862)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English man with severe deformities (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English man with severe deformities (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1894", "text": "Con<PERSON><PERSON>, German architect and theorist (b. 1832)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Con<PERSON><PERSON></a>, German architect and theorist (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Con<PERSON><PERSON>_<PERSON>\" title=\"Constantin <PERSON>\"><PERSON><PERSON><PERSON></a>, German architect and theorist (b. 1832)", "links": [{"title": "Constant<PERSON>", "link": "https://wikipedia.org/wiki/Constantin_<PERSON><PERSON><PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German chemist (b. 1830)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Confederate general and politician, 77th Governor of South Carolina (b. 1818)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Confederate general and politician, 77th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Confederate general and politician, 77th <a href=\"https://wikipedia.org/wiki/Governor_of_South_Carolina\" title=\"Governor of South Carolina\">Governor of South Carolina</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of South Carolina", "link": "https://wikipedia.org/wiki/Governor_of_South_Carolina"}]}, {"year": "1903", "text": "<PERSON>, Italian mystic and saint (b. 1878)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mystic and saint (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mystic and saint (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gemma_<PERSON><PERSON>i"}]}, {"year": "1906", "text": "<PERSON>, American businessman, co-founded Ringling Bros. and Barnum & Bailey Circus (b. 1847)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_Bailey_<PERSON>\" title=\"Ringling Bros. and Barnum &amp; Bailey Circus\">Ringling Bros. and Barnum &amp; Bailey Circus</a> (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ringling Bros. and Barnum & Bailey Circus", "link": "https://wikipedia.org/wiki/Ringling_Bros._and_Barnum_%26_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American journalist and publisher, co-founded Armed Forces Journal and The Galaxy Magazine (b. 1839)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Armed_Forces_Journal\" title=\"Armed Forces Journal\">Armed Forces Journal</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Galaxy_(magazine)\" title=\"The Galaxy (magazine)\">The Galaxy Magazine</a></i> (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and publisher, co-founded <i><a href=\"https://wikipedia.org/wiki/Armed_Forces_Journal\" title=\"Armed Forces Journal\">Armed Forces Journal</a></i> and <i><a href=\"https://wikipedia.org/wiki/The_Galaxy_(magazine)\" title=\"The Galaxy (magazine)\">The Galaxy Magazine</a></i> (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Armed Forces Journal", "link": "https://wikipedia.org/wiki/Armed_Forces_Journal"}, {"title": "The Galaxy (magazine)", "link": "https://wikipedia.org/wiki/The_Galaxy_(magazine)"}]}, {"year": "1908", "text": "<PERSON>, English chess player and author (b. 1829)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, English chess player and author (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, English chess player and author (b. 1829)", "links": [{"title": "<PERSON> (chess player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)"}]}, {"year": "1916", "text": "<PERSON>, American journalist and author (b. 1864)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Austrian architect and urban planner (b. 1841)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian architect and urban planner (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian architect and urban planner (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American botanist and academic (b. 1849)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and academic (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist and academic (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luther_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Turkish wrestler (b. 1864)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kurt<PERSON><PERSON> Mehm<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish wrestler (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Kurt<PERSON><PERSON> Mehmet\"><PERSON><PERSON><PERSON></a>, Turkish wrestler (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American baseball player and manager (b. 1869)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American violinist and bandleader (b. 1895)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and bandleader (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and bandleader (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian painter and educator (b. 1875)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and educator (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Swedish actress (b. 1878)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Rosa_Gr%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Gr%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Gr%C3%BCnberg"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese physicist and academic (b. 1900)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American hurdler and educator (b. 1880)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and educator (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and educator (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Norwegian painter (b. 1887)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian painter (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>old"}]}, {"year": "1967", "text": "<PERSON>, American general (b. 1891)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)\" class=\"mw-redirect\" title=\"<PERSON> (general)\"><PERSON></a>, American general (b. 1891)", "links": [{"title": "<PERSON> (general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(general)"}]}, {"year": "1967", "text": "<PERSON>, Jamaican lawyer and politician, 2nd Prime Minister of Jamaica (b. 1911)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Jamaica\" title=\"Prime Minister of Jamaica\">Prime Minister of Jamaica</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Jamaica\" title=\"Prime Minister of Jamaica\">Prime Minister of Jamaica</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Jamaica", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Jamaica"}]}, {"year": "1970", "text": "<PERSON>, American actress (b. 1923)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donnell"}]}, {"year": "1970", "text": "<PERSON>, American novelist and short story writer (b. 1905)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hara\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_<PERSON>%27Hara"}]}, {"year": "1974", "text": "<PERSON>, German actor (b. 1894)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French poet and screenwriter (b. 1900)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vert\" title=\"<PERSON>\"><PERSON></a>, French poet and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9vert\" title=\"<PERSON>\"><PERSON></a>, French poet and screenwriter (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_Pr%C3%A9vert"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>', Indian author and activist (b. 1921)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Ph<PERSON>shwar_Nath_%27Renu%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> 'Renu'\"><PERSON><PERSON><PERSON><PERSON> '<PERSON>'</a>, Indian author and activist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>shwar_Nath_%27Renu%27\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> 'Renu'\"><PERSON><PERSON><PERSON><PERSON> 'Ren<PERSON>'</a>, Indian author and activist (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Phanishwar_Nath_%27Renu%27"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Turkish journalist and producer (b. 1935)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Kaftanc%C4%B1o%C4%9Flu\" title=\"<PERSON>mit Kaftancıoğlu\"><PERSON><PERSON> Kaftancıoğlu</a>, Turkish journalist and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%9Cmit_Kaftanc%C4%B1o%C4%9Flu\" title=\"<PERSON>mit Kaftancıoğlu\"><PERSON><PERSON> Kaftancıoğlu</a>, Turkish journalist and producer (b. 1935)", "links": [{"title": "Ümit Kaftancıoğlu", "link": "https://wikipedia.org/wiki/%C3%9Cmit_Kaftanc%C4%B1o%C4%9Flu"}]}, {"year": "1981", "text": "<PERSON>, American author and critic (b. 1895)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Mexican actress (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_del_R%C3%ADo\" title=\"Dolores del Río\"><PERSON></a>, Mexican actress (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_R%C3%ADo\" title=\"<PERSON> del Río\"><PERSON></a>, Mexican actress (b. 1904)", "links": [{"title": "Dolores del Río", "link": "https://wikipedia.org/wiki/Dolores_del_R%C3%ADo"}]}, {"year": "1984", "text": "<PERSON>, Estonian historian and politician, Estonian Minister of Education (b. 1910)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian historian and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)\" title=\"Minister of Education and Research (Estonia)\">Estonian Minister of Education</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Education and Research (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Education_and_Research_(Estonia)"}]}, {"year": "1985", "text": "<PERSON>, Irish-born English businessman (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English businessman (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-born English businessman (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English artist and illustrator (b. 1898)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English artist and illustrator (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English artist and illustrator (b. 1898)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Albanian educator and politician, 21st Prime Minister of Albania (b. 1908)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Enver_Ho<PERSON>\" title=\"Enver Ho<PERSON>\"><PERSON><PERSON></a>, Albanian educator and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Albania\">Prime Minister of Albania</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Enver_<PERSON>\" title=\"Enver Ho<PERSON>\"><PERSON><PERSON></a>, Albanian educator and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Albania\">Prime Minister of Albania</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Enver_<PERSON>xha"}, {"title": "List of Prime Ministers of Albania", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Albania"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American novelist and short story writer (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and short story writer (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Italian chemist and author (b. 1919)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Primo Levi\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian chemist and author (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Primo Levi\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian chemist and author (b. 1919)", "links": [{"title": "P<PERSON><PERSON> Levi", "link": "https://wikipedia.org/wiki/Primo_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian businessman (b. 1903)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player and manager (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>. German glass harp player (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>. German <a href=\"https://wikipedia.org/wiki/Glass_harp\" title=\"Glass harp\">glass harp</a> player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. German <a href=\"https://wikipedia.org/wiki/Glass_harp\" title=\"Glass harp\">glass harp</a> player (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Glass harp", "link": "https://wikipedia.org/wiki/Glass_harp"}]}, {"year": "1992", "text": "<PERSON>, American actor and singer (b. 1920)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1920)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1992", "text": "<PERSON>, American author and poet (b. 1916)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Colombian painter, sculptor, and engraver (b. 1920)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian painter, sculptor, and engraver (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a>, Colombian painter, sculptor, and engraver (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alejandro_Obreg%C3%B3n"}]}, {"year": "1996", "text": "<PERSON>, American pilot (b. 1988)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Canadian lawyer and politician, Canadian Speaker of the Senate (b. 1899)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)\" class=\"mw-redirect\" title=\"Speaker of the Senate (Canada)\">Canadian Speaker of the Senate</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Senate (Canada)", "link": "https://wikipedia.org/wiki/Speaker_of_the_Senate_(Canada)"}]}, {"year": "1997", "text": "<PERSON>, contemporary Chinese novelist and essayist (b. 1952)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xiaobo\"><PERSON></a>, contemporary Chinese novelist and essayist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Xiaobo\"><PERSON></a>, contemporary Chinese novelist and essayist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American author and educator (b. 1911)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator (b. 1911)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(author)"}]}, {"year": "2000", "text": "<PERSON>, English actress, singer and dancer (b. 1945)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer and dancer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer and dancer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Welsh-English actor (b. 1921)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English actor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English-American geophysicist and businessman, founded Texas Instruments (b. 1900)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geophysicist and businessman, founded <a href=\"https://wikipedia.org/wiki/Texas_Instruments\" title=\"Texas Instruments\">Texas Instruments</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American geophysicist and businessman, founded <a href=\"https://wikipedia.org/wiki/Texas_Instruments\" title=\"Texas Instruments\">Texas Instruments</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Texas Instruments", "link": "https://wikipedia.org/wiki/Texas_Instruments"}]}, {"year": "2005", "text": "<PERSON>, Romanian-French cartoonist, painter, and sculptor (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fran%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Romanian-French cartoonist, painter, and sculptor (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Fran%C3%A7ois\" title=\"<PERSON>\"><PERSON></a>, Romanian-French cartoonist, painter, and sculptor (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>an%C3%A7ois"}]}, {"year": "2005", "text": "<PERSON>, French footballer and coach (b. 1907)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and coach (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer (b. 1953)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/June_Pointer\" title=\"June Pointer\">June <PERSON></a>, American singer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_Pointer\" title=\"June Pointer\"><PERSON> <PERSON></a>, American singer (b. 1953)", "links": [{"title": "<PERSON> <PERSON>er", "link": "https://wikipedia.org/wiki/June_Pointer"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, American rapper and actor (b. 1973)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper and actor (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rapper and actor (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, American actor and director (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, French diver (b. 1970)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Lo%C3%AF<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French diver (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lo%C3%AF<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French diver (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lo%C3%AFc_<PERSON>me"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and author (b. 1954)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Scottish-American colonel (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American colonel (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American colonel (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American novelist, short story writer, and playwright (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American sergeant (b. 1985)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Merlin_German\" title=\"Merlin German\"><PERSON></a>, American sergeant (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Merlin_German\" title=\"Merlin German\"><PERSON></a>, American sergeant (b. 1985)", "links": [{"title": "Merlin German", "link": "https://wikipedia.org/wiki/Merlin_German"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Danish actress and singer (b. 1914)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actress and singer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish actress and singer (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Indian author and playwright (b. 1912)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Spanish author (b. 1927)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Cor%C3%ADn_Tellado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cor%C3%ADn_Tellado\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cor%C3%ADn_Tellado"}]}, {"year": "2010", "text": "<PERSON>, Bulgarian pianist and composer (b. 1948)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian pianist and composer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian pianist and composer (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American wrestler and manager (b. 1981)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and manager (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Algerian soldier and politician, 1st President of Algeria (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "2012", "text": "<PERSON>, Canadian criminal and author (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian criminal and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian criminal and author (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American basketball player and coach (b. 1915)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Tippy_<PERSON>ye\" title=\"Tippy Dye\">T<PERSON><PERSON></a>, American basketball player and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tippy_<PERSON>ye\" title=\"Tippy Dye\"><PERSON><PERSON><PERSON></a>, American basketball player and coach (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tippy_<PERSON>ye"}]}, {"year": "2012", "text": "<PERSON>, American saxophonist, clarinet player, and flute player (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and flute player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, clarinet player, and flute player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American bishop (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Agustin_Roman\" title=\"Agustin Roman\"><PERSON><PERSON><PERSON></a>, American bishop (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agustin_Roman\" title=\"Agustin Roman\"><PERSON><PERSON><PERSON></a>, American bishop (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter, pianist, and producer (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American fiddler (b.1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler (b.1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fiddler (b.1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player, coach, and manager (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English actor and singer (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and singer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Polish-American virologist and immunologist (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American virologist and immunologist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American virologist and immunologist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, French singer-songwriter (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American ballerina (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Tallchief"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-Argentinian architect (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON><PERSON>_<PERSON>a\" title=\"C<PERSON>ind<PERSON> Testa\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Argentinian architect (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Clorind<PERSON> Testa\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-Argentinian architect (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clorindo_Testa"}]}, {"year": "2013", "text": "<PERSON>, American comedian, actor and screenwriter (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor and screenwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor and screenwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Swiss sculptor and illustrator (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss sculptor and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss sculptor and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actress (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1927)\" title=\"<PERSON> (baseball, born 1927)\"><PERSON></a>, American baseball player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1927)\" title=\"<PERSON> (baseball, born 1927)\"><PERSON></a>, American baseball player (b. 1927)", "links": [{"title": "<PERSON> (baseball, born 1927)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(baseball,_born_1927)"}]}, {"year": "2014", "text": "<PERSON>, American basketball player and sportscaster (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and sportscaster (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON> <PERSON>, American rabbi and scholar (b. 1914)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American rabbi and scholar (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American rabbi and scholar (b. 1914)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Russian engineer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Bangladeshi journalist and politician (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi journalist and politician (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi journalist and politician (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, French journalist and author (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Ma<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Ma<PERSON>o"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian general (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian general (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON><PERSON></a>, Indian general (b. 1933)", "links": [{"title": "<PERSON><PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(soldier)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Nigerian historian and academic (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian historian and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian historian and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American singer and guitarist (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Canadian researcher and HIV/AIDS activist (b. 1945)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian researcher and HIV/AIDS activist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian researcher and HIV/AIDS activist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English mathematician (b. 1937)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, South Korean singer (b. 1994)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ram\" title=\"<PERSON> Bo-ram\"><PERSON></a>, South Korean singer (b. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bo-ram\" title=\"<PERSON> Bo-ram\"><PERSON></a>, South Korean singer (b. 1994)", "links": [{"title": "<PERSON>-ram", "link": "https://wikipedia.org/wiki/Park_Bo-ram"}]}]}}