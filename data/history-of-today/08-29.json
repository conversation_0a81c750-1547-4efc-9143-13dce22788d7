{"date": "August 29", "url": "https://wikipedia.org/wiki/August_29", "data": {"Events": [{"year": "708", "text": "Copper coins are minted in Japan for the first time (Traditional Japanese date: August 10, 708).", "html": "708 - <a href=\"https://wikipedia.org/wiki/Wad%C5%8Dkaichin\" title=\"Wadōkaichin\">Copper coins</a> are minted in <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> for the first time (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: August 10, 708).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wad%C5%8Dkaichin\" title=\"Wadōkaichin\">Copper coins</a> are minted in <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> for the first time (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: August 10, 708).", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wad%C5%8Dkaichin"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "870", "text": "The city of Melite surrenders to an Aghlabid army following a siege, putting an end to Byzantine Malta.", "html": "870 - The city of <a href=\"https://wikipedia.org/wiki/Melite_(ancient_city)\" title=\"Melite (ancient city)\">Melite</a> surrenders to an <a href=\"https://wikipedia.org/wiki/Aghlabids\" class=\"mw-redirect\" title=\"Aghlabids\">Aghlabid</a> army <a href=\"https://wikipedia.org/wiki/Siege_of_Melite_(870)\" title=\"Siege of Melite (870)\">following a siege</a>, putting an end to <a href=\"https://wikipedia.org/wiki/Byzantine_Malta\" title=\"Byzantine Malta\">Byzantine Malta</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Melite_(ancient_city)\" title=\"Melite (ancient city)\">Melite</a> surrenders to an <a href=\"https://wikipedia.org/wiki/Aghlabids\" class=\"mw-redirect\" title=\"Aghlabids\">Aghlabid</a> army <a href=\"https://wikipedia.org/wiki/Siege_of_Melite_(870)\" title=\"Siege of Melite (870)\">following a siege</a>, putting an end to <a href=\"https://wikipedia.org/wiki/Byzantine_Malta\" title=\"Byzantine Malta\">Byzantine Malta</a>.", "links": [{"title": "Melite (ancient city)", "link": "https://wikipedia.org/wiki/Melite_(ancient_city)"}, {"title": "Aghlabids", "link": "https://wikipedia.org/wiki/Aghlabids"}, {"title": "Siege of Melite (870)", "link": "https://wikipedia.org/wiki/Siege_of_Melite_(870)"}, {"title": "Byzantine Malta", "link": "https://wikipedia.org/wiki/Byzantine_Malta"}]}, {"year": "1009", "text": "Mainz Cathedral suffers extensive damage from a fire, which destroys the building on the day of its inauguration.", "html": "1009 - <a href=\"https://wikipedia.org/wiki/Mainz_Cathedral\" title=\"Mainz Cathedral\">Mainz Cathedral</a> suffers extensive damage from a fire, which destroys the building on the day of its <a href=\"https://wikipedia.org/wiki/Inauguration\" title=\"Inauguration\">inauguration</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mainz_Cathedral\" title=\"Mainz Cathedral\">Mainz Cathedral</a> suffers extensive damage from a fire, which destroys the building on the day of its <a href=\"https://wikipedia.org/wiki/Inauguration\" title=\"Inauguration\">inauguration</a>.", "links": [{"title": "Mainz Cathedral", "link": "https://wikipedia.org/wiki/Mainz_Cathedral"}, {"title": "Inauguration", "link": "https://wikipedia.org/wiki/Inauguration"}]}, {"year": "1219", "text": "The Battle of Fariskur occurs during the Fifth Crusade.", "html": "1219 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Fariskur_(1219)\" title=\"Battle of Fariskur (1219)\">Battle of Fariskur</a> occurs during the <a href=\"https://wikipedia.org/wiki/Fifth_Crusade\" title=\"Fifth Crusade\">Fifth Crusade</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Fariskur_(1219)\" title=\"Battle of Fariskur (1219)\">Battle of Fariskur</a> occurs during the <a href=\"https://wikipedia.org/wiki/Fifth_Crusade\" title=\"Fifth Crusade\">Fifth Crusade</a>.", "links": [{"title": "Battle of Fariskur (1219)", "link": "https://wikipedia.org/wiki/Battle_of_Fariskur_(1219)"}, {"title": "Fifth Crusade", "link": "https://wikipedia.org/wiki/Fifth_Crusade"}]}, {"year": "1261", "text": "<PERSON> <PERSON> succeeds <PERSON> <PERSON>, becoming the 182nd pope.", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Urban_IV\" title=\"Pope Urban IV\">Pope Urban IV</a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_IV\" title=\"Pope Alexander IV\">Pope Alexander IV</a>, becoming the 182nd pope.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Urban_IV\" title=\"Pope Urban IV\">Pope Urban IV</a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_IV\" title=\"Pope Alexander IV\">Pope Alexander IV</a>, becoming the 182nd pope.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Urban_IV"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1315", "text": "Battle of Montecatini: The army of the Republic of Pisa, commanded by <PERSON><PERSON><PERSON><PERSON> Faggiuola, wins a decisive victory against the joint forces of the Kingdom of Naples and the Republic of Florence despite being outnumbered.", "html": "1315 - <a href=\"https://wikipedia.org/wiki/Battle_of_Montecatini\" title=\"Battle of Montecatini\">Battle of Montecatini</a>: The army of the <a href=\"https://wikipedia.org/wiki/Republic_of_Pisa\" title=\"Republic of Pisa\">Republic of Pisa</a>, commanded by <a href=\"https://wikipedia.org/wiki/Uguccione_della_Faggiuola\" title=\"Uguccione della Faggiuola\">Uguccione della Faggiuola</a>, wins a decisive victory against the joint forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">Republic of Florence</a> despite being outnumbered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Montecatini\" title=\"Battle of Montecatini\">Battle of Montecatini</a>: The army of the <a href=\"https://wikipedia.org/wiki/Republic_of_Pisa\" title=\"Republic of Pisa\">Republic of Pisa</a>, commanded by <a href=\"https://wikipedia.org/wiki/Uguccione_della_Faggiuola\" title=\"Uguccione della Faggiuola\">Uguccione della Faggiuola</a>, wins a decisive victory against the joint forces of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Naples\" title=\"Kingdom of Naples\">Kingdom of Naples</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Florence\" title=\"Republic of Florence\">Republic of Florence</a> despite being outnumbered.", "links": [{"title": "Battle of Montecatini", "link": "https://wikipedia.org/wiki/Battle_of_Montecatini"}, {"title": "Republic of Pisa", "link": "https://wikipedia.org/wiki/Republic_of_Pisa"}, {"title": "Uguccione della Faggiuola", "link": "https://wikipedia.org/wiki/Uguccione_<PERSON>_<PERSON>"}, {"title": "Kingdom of Naples", "link": "https://wikipedia.org/wiki/Kingdom_of_Naples"}, {"title": "Republic of Florence", "link": "https://wikipedia.org/wiki/Republic_of_Florence"}]}, {"year": "1350", "text": "Battle of Winchelsea (or Les Espagnols sur Mer): The English naval fleet under King <PERSON> defeats a Castilian fleet of 40 ships.", "html": "1350 - <a href=\"https://wikipedia.org/wiki/Battle_of_Winchelsea\" title=\"Battle of Winchelsea\">Battle of Winchelsea</a> (or <a href=\"https://wikipedia.org/wiki/Les_Espagnols_sur_Mer\" class=\"mw-redirect\" title=\"<PERSON> Espagnols sur Mer\">Les Espagnols sur Mer</a>): The <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> <a href=\"https://wikipedia.org/wiki/Naval_fleet\" title=\"Naval fleet\">naval fleet</a> under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Edward III of England\"><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> fleet of 40 ships.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Winchelsea\" title=\"Battle of Winchelsea\">Battle of Winchelsea</a> (or <a href=\"https://wikipedia.org/wiki/<PERSON>_E<PERSON>agnols_sur_Mer\" class=\"mw-redirect\" title=\"Les Espagnols sur Mer\">Les Espagnols sur Mer</a>): The <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">English</a> <a href=\"https://wikipedia.org/wiki/Naval_fleet\" title=\"Naval fleet\">naval fleet</a> under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"Edward III of England\"><PERSON></a> defeats a <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castilian</a> fleet of 40 ships.", "links": [{"title": "Battle of Winchelsea", "link": "https://wikipedia.org/wiki/Battle_of_Winchelsea"}, {"title": "Les Espagnols sur Mer", "link": "https://wikipedia.org/wiki/Les_E<PERSON>agno<PERSON>_sur_Mer"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Naval fleet", "link": "https://wikipedia.org/wiki/Naval_fleet"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}]}, {"year": "1475", "text": "The Treaty of Picquigny ends a brief war between  the kingdoms of France and England.", "html": "1475 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Picquigny\" title=\"Treaty of Picquigny\">Treaty of Picquigny</a> ends a brief war between the kingdoms of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> and England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Picquigny\" title=\"Treaty of Picquigny\">Treaty of Picquigny</a> ends a brief war between the kingdoms of <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> and England.", "links": [{"title": "Treaty of Picquigny", "link": "https://wikipedia.org/wiki/Treaty_of_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1484", "text": "<PERSON> <PERSON> succeeds <PERSON> <PERSON><PERSON>.", "html": "1484 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_VIII\" title=\"Pope Innocent VIII\">Pope Innocent VIII</a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"Pope Sixtus IV\"><PERSON> Sixtus IV</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Innocent_VIII\" title=\"Pope Innocent VIII\">Pope Innocent VIII</a> succeeds <a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"Pope Sixtus IV\">Pope Sixtus IV</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_VIII"}, {"title": "<PERSON> <PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}]}, {"year": "1498", "text": "<PERSON><PERSON> da Gama decides to depart Calicut and return to the Kingdom of Portugal.", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> decides to depart <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Calicut</a> and return to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Kingdom of Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a> decides to depart <a href=\"https://wikipedia.org/wiki/Kozhikode\" title=\"Kozhikode\">Calicut</a> and return to the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Kingdom of Portugal</a>.", "links": [{"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}, {"title": "Kozhikode", "link": "https://wikipedia.org/wiki/Kozhikode"}, {"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}]}, {"year": "1521", "text": "The Ottoman Turks capture Nándorfehérvár (Belgrade).", "html": "1521 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> capture <a href=\"https://wikipedia.org/wiki/N%C3%A1ndorfeh%C3%A9rv%C3%A1r\" class=\"mw-redirect\" title=\"Nándorfehérvár\">Nándorfehérvár</a> (Belgrade).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> capture <a href=\"https://wikipedia.org/wiki/N%C3%A1ndorfeh%C3%A9rv%C3%A1r\" class=\"mw-redirect\" title=\"Nándorfehérvár\">Nándorfehérvár</a> (Belgrade).", "links": [{"title": "Ottoman Turks", "link": "https://wikipedia.org/wiki/Ottoman_Turks"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A1ndorfeh%C3%A9rv%C3%A1r"}]}, {"year": "1526", "text": "Battle of Mohács: The Ottoman Turks led by <PERSON><PERSON><PERSON> the Magnificent defeat and kill the last Jagiellonian king of Hungary and Bohemia.", "html": "1526 - <a href=\"https://wikipedia.org/wiki/Battle_of_Moh%C3%A1cs\" title=\"Battle of Mohács\">Battle of Mohács</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> led by <a href=\"https://wikipedia.org/wiki/Suleiman_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a> defeat and kill the last <a href=\"https://wikipedia.org/wiki/Jagiellonian\" class=\"mw-redirect\" title=\"Jagiellonian\">Jagiellonian</a> king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Moh%C3%A1cs\" title=\"Battle of Mohács\">Battle of Mohács</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> led by <a href=\"https://wikipedia.org/wiki/Suleiman_the_Magnificent\" title=\"<PERSON><PERSON><PERSON> the Magnificent\"><PERSON><PERSON><PERSON> the Magnificent</a> defeat and kill the last <a href=\"https://wikipedia.org/wiki/Jagiellonian\" class=\"mw-redirect\" title=\"Jagiellonian\">Jagiellonian</a> king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Hungary</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Bohemia\" title=\"Kingdom of Bohemia\">Bohemia</a>.", "links": [{"title": "Battle of Mohács", "link": "https://wikipedia.org/wiki/Battle_of_Moh%C3%A1cs"}, {"title": "Ottoman Turks", "link": "https://wikipedia.org/wiki/Ottoman_Turks"}, {"title": "Sul<PERSON><PERSON> the Magnificent", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_the_Magnificent"}, {"title": "Jagiellonian", "link": "https://wikipedia.org/wiki/Jagiellonian"}, {"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "Kingdom of Bohemia", "link": "https://wikipedia.org/wiki/Kingdom_of_Bohemia"}]}, {"year": "1541", "text": "The Ottoman Turks capture Buda, the capital of the Hungarian Kingdom.", "html": "1541 - The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> capture <a href=\"https://wikipedia.org/wiki/Buda\" title=\"Buda\">Buda</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Hungarian_Kingdom\" class=\"mw-redirect\" title=\"Hungarian Kingdom\">Hungarian Kingdom</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> capture <a href=\"https://wikipedia.org/wiki/Buda\" title=\"Buda\">Buda</a>, the capital of the <a href=\"https://wikipedia.org/wiki/Hungarian_Kingdom\" class=\"mw-redirect\" title=\"Hungarian Kingdom\">Hungarian Kingdom</a>.", "links": [{"title": "Ottoman Turks", "link": "https://wikipedia.org/wiki/Ottoman_Turks"}, {"title": "Buda", "link": "https://wikipedia.org/wiki/Buda"}, {"title": "Hungarian Kingdom", "link": "https://wikipedia.org/wiki/Hungarian_Kingdom"}]}, {"year": "1588", "text": "<PERSON><PERSON><PERSON> issues a nationwide sword hunting ordinance, disarming the peasantry so as to firmly separate the samurai and commoner classes, prevent peasant uprisings, and further centralise his own power.", "html": "1588 - <a href=\"https://wikipedia.org/wiki/Toyotomi_Hideyoshi\" title=\"Toyotomi Hideyoshi\">Toyotomi Hideyoshi</a> issues a nationwide <a href=\"https://wikipedia.org/wiki/Sword_hunt\" title=\"Sword hunt\">sword hunting ordinance</a>, disarming the peasantry so as to firmly separate the <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> and commoner classes, prevent peasant uprisings, and further centralise his own power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toy<PERSON><PERSON>_Hideyoshi\" title=\"Toyotomi Hideyoshi\">Toyotomi Hideyoshi</a> issues a nationwide <a href=\"https://wikipedia.org/wiki/Sword_hunt\" title=\"Sword hunt\">sword hunting ordinance</a>, disarming the peasantry so as to firmly separate the <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> and commoner classes, prevent peasant uprisings, and further centralise his own power.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>deyoshi"}, {"title": "Sword hunt", "link": "https://wikipedia.org/wiki/Sword_hunt"}, {"title": "Samurai", "link": "https://wikipedia.org/wiki/Samurai"}]}, {"year": "1604", "text": "The Guru <PERSON> is fully compiled and completed by <PERSON><PERSON>.", "html": "1604 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guru <PERSON>\">Guru <PERSON></a> is fully compiled and completed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guru <PERSON>\">Guru <PERSON></a> is fully compiled and completed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Guru <PERSON></a>.", "links": [{"title": "Guru <PERSON> Sahib", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1728", "text": "The city of Nuuk in Greenland is founded as the fort of Godt-Haab by the royal governor <PERSON>.", "html": "1728 - The city of <a href=\"https://wikipedia.org/wiki/Nuuk\" title=\"Nuuk\">Nuuk</a> in <a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a> is founded as the fort of Godt-Haab by the <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Greenland\" title=\"List of governors of Greenland\">royal governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Nuuk\" title=\"Nuuk\">Nuuk</a> in <a href=\"https://wikipedia.org/wiki/Greenland\" title=\"Greenland\">Greenland</a> is founded as the fort of Godt-Haab by the <a href=\"https://wikipedia.org/wiki/List_of_governors_of_Greenland\" title=\"List of governors of Greenland\">royal governor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuuk"}, {"title": "Greenland", "link": "https://wikipedia.org/wiki/Greenland"}, {"title": "List of governors of Greenland", "link": "https://wikipedia.org/wiki/List_of_governors_of_Greenland"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1741", "text": "The eruption of Oshima-Ōshima and the Kampo tsunami: At least 2,000 people along the Japanese coast drown in a tsunami caused by the eruption of Oshima.", "html": "1741 - The <a href=\"https://wikipedia.org/wiki/1741_eruption_of_Oshima%E2%80%93%C5%8Cshima_and_the_Kampo_tsunami\" title=\"1741 eruption of Oshima-Ōshima and the Kampo tsunami\">eruption of Oshima-Ōshima and the Kampo tsunami</a>: At least 2,000 people along the Japanese coast drown in a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> caused by the eruption of <a href=\"https://wikipedia.org/wiki/Oshima_(Hokkaido)\" title=\"Oshima (Hokkaido)\">Oshima</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1741_eruption_of_Oshima%E2%80%93%C5%8Cshima_and_the_Kampo_tsunami\" title=\"1741 eruption of Oshima-Ōshima and the Kampo tsunami\">eruption of Oshima-Ōshima and the Kampo tsunami</a>: At least 2,000 people along the Japanese coast drown in a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> caused by the eruption of <a href=\"https://wikipedia.org/wiki/Oshima_(Hokkaido)\" title=\"Oshima (Hokkaido)\">Oshima</a>.", "links": [{"title": "1741 eruption of Oshima-Ōshima and the Kampo tsunami", "link": "https://wikipedia.org/wiki/1741_eruption_of_Oshima%E2%80%93%C5%8Cshima_and_the_Kampo_tsunami"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}, {"title": "Oshima (Hokkaido)", "link": "https://wikipedia.org/wiki/Oshima_(Hokkaido)"}]}, {"year": "1756", "text": "<PERSON> the Great attacks Saxony, beginning the Seven Years' War in Europe.", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> attacks <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Saxony</a>, beginning the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a> in Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> attacks <a href=\"https://wikipedia.org/wiki/Electorate_of_Saxony\" title=\"Electorate of Saxony\">Saxony</a>, beginning the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a> in Europe.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Electorate of Saxony", "link": "https://wikipedia.org/wiki/Electorate_of_Saxony"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1758", "text": "The Treaty of Easton establishes the first American Indian reservation, at Indian Mills, New Jersey, for the Lenape.", "html": "1758 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Easton\" title=\"Treaty of Easton\">Treaty of Easton</a> establishes the first American <a href=\"https://wikipedia.org/wiki/Indian_reservation\" title=\"Indian reservation\">Indian reservation</a>, at <a href=\"https://wikipedia.org/wiki/Indian_Mills,_New_Jersey\" title=\"Indian Mills, New Jersey\">Indian Mills, New Jersey</a>, for the <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Easton\" title=\"Treaty of Easton\">Treaty of Easton</a> establishes the first American <a href=\"https://wikipedia.org/wiki/Indian_reservation\" title=\"Indian reservation\">Indian reservation</a>, at <a href=\"https://wikipedia.org/wiki/Indian_Mills,_New_Jersey\" title=\"Indian Mills, New Jersey\">Indian Mills, New Jersey</a>, for the <a href=\"https://wikipedia.org/wiki/Lenape\" title=\"Lenape\">Lenape</a>.", "links": [{"title": "Treaty of Easton", "link": "https://wikipedia.org/wiki/Treaty_of_Easton"}, {"title": "Indian reservation", "link": "https://wikipedia.org/wiki/Indian_reservation"}, {"title": "Indian Mills, New Jersey", "link": "https://wikipedia.org/wiki/Indian_Mills,_New_Jersey"}, {"title": "Lenape", "link": "https://wikipedia.org/wiki/Lenape"}]}, {"year": "1778", "text": "American Revolutionary War: British and American forces battle indecisively at the Battle of Rhode Island.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and American forces battle indecisively at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rhode_Island\" title=\"Battle of Rhode Island\">Battle of Rhode Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: British and American forces battle indecisively at the <a href=\"https://wikipedia.org/wiki/Battle_of_Rhode_Island\" title=\"Battle of Rhode Island\">Battle of Rhode Island</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Rhode Island", "link": "https://wikipedia.org/wiki/Battle_of_Rhode_Island"}]}, {"year": "1779", "text": "American Revolutionary War: American forces battle and defeat the British and Iroquois forces at the Battle of Newtown.", "html": "1779 - American Revolutionary War: American forces battle and defeat the British and <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Newtown\" title=\"Battle of Newtown\">Battle of Newtown</a>.", "no_year_html": "American Revolutionary War: American forces battle and defeat the British and <a href=\"https://wikipedia.org/wiki/Iroquois\" title=\"Iroquois\">Iroquois</a> forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Newtown\" title=\"Battle of Newtown\">Battle of Newtown</a>.", "links": [{"title": "Iroquois", "link": "https://wikipedia.org/wiki/Iroquois"}, {"title": "Battle of Newtown", "link": "https://wikipedia.org/wiki/Battle_of_Newtown"}]}, {"year": "1786", "text": "<PERSON><PERSON>' Rebellion, an armed uprising of Massachusetts farmers, begins in response to high debt and tax burdens.", "html": "1786 - <a href=\"https://wikipedia.org/wiki/Shays%27_Rebellion\" class=\"mw-redirect\" title=\"Shays' Rebellion\">Shays' Rebellion</a>, an armed uprising of <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> farmers, begins in response to high debt and tax burdens.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shays%27_Rebellion\" class=\"mw-redirect\" title=\"Shays' Rebellion\">Shays' Rebellion</a>, an armed uprising of <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> farmers, begins in response to high debt and tax burdens.", "links": [{"title": "<PERSON><PERSON>' Rebellion", "link": "https://wikipedia.org/wiki/Shays%27_Rebellion"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}]}, {"year": "1807", "text": "British troops under Sir <PERSON> defeat a Danish militia outside Copenhagen in the Battle of Køge.", "html": "1807 - British troops under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON></a> defeat a Danish militia outside <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B8ge\" title=\"Battle of Køge\">Battle of Køge</a>.", "no_year_html": "British troops under <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Wellington\" title=\"<PERSON>, 1st Duke of Wellington\"><PERSON></a> defeat a Danish militia outside <a href=\"https://wikipedia.org/wiki/Copenhagen\" title=\"Copenhagen\">Copenhagen</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_K%C3%B8ge\" title=\"Battle of Køge\">Battle of Køge</a>.", "links": [{"title": "<PERSON>, 1st Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Wellington"}, {"title": "Copenhagen", "link": "https://wikipedia.org/wiki/Copenhagen"}, {"title": "Battle of Køge", "link": "https://wikipedia.org/wiki/Battle_of_K%C3%B8ge"}]}, {"year": "1825", "text": "Portuguese and Brazilian diplomats sign the Treaty of Rio de Janeiro, which has Portugal recognise Brazilian independence, formally ending the Brazilian war of independence. The treaty will be ratified by the King of Portugal three months later.", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> and <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Brazilian</a> diplomats sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rio_de_Janeiro_(1825)\" title=\"Treaty of Rio de Janeiro (1825)\">Treaty of Rio de Janeiro</a>, which has Portugal recognise Brazilian independence, formally ending the <a href=\"https://wikipedia.org/wiki/War_of_Independence_of_Brazil\" class=\"mw-redirect\" title=\"War of Independence of Brazil\">Brazilian war of independence</a>. The treaty will be ratified by the <a href=\"https://wikipedia.org/wiki/King_of_Portugal\" class=\"mw-redirect\" title=\"King of Portugal\">King of Portugal</a> three months later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Portugal\" title=\"Kingdom of Portugal\">Portuguese</a> and <a href=\"https://wikipedia.org/wiki/Empire_of_Brazil\" title=\"Empire of Brazil\">Brazilian</a> diplomats sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Rio_de_Janeiro_(1825)\" title=\"Treaty of Rio de Janeiro (1825)\">Treaty of Rio de Janeiro</a>, which has Portugal recognise Brazilian independence, formally ending the <a href=\"https://wikipedia.org/wiki/War_of_Independence_of_Brazil\" class=\"mw-redirect\" title=\"War of Independence of Brazil\">Brazilian war of independence</a>. The treaty will be ratified by the <a href=\"https://wikipedia.org/wiki/King_of_Portugal\" class=\"mw-redirect\" title=\"King of Portugal\">King of Portugal</a> three months later.", "links": [{"title": "Kingdom of Portugal", "link": "https://wikipedia.org/wiki/Kingdom_of_Portugal"}, {"title": "Empire of Brazil", "link": "https://wikipedia.org/wiki/Empire_of_Brazil"}, {"title": "Treaty of Rio de Janeiro (1825)", "link": "https://wikipedia.org/wiki/Treaty_of_Rio_de_Janeiro_(1825)"}, {"title": "War of Independence of Brazil", "link": "https://wikipedia.org/wiki/War_of_Independence_of_Brazil"}, {"title": "King of Portugal", "link": "https://wikipedia.org/wiki/King_of_Portugal"}]}, {"year": "1831", "text": "<PERSON> discovers electromagnetic induction.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Electromagnetic_induction\" title=\"Electromagnetic induction\">electromagnetic induction</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Electromagnetic_induction\" title=\"Electromagnetic induction\">electromagnetic induction</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electromagnetic induction", "link": "https://wikipedia.org/wiki/Electromagnetic_induction"}]}, {"year": "1842", "text": "Treaty of Nanking signing ends the First Opium War.", "html": "1842 - <a href=\"https://wikipedia.org/wiki/Treaty_of_Nanking\" title=\"Treaty of Nanking\">Treaty of Nanking</a> signing ends the <a href=\"https://wikipedia.org/wiki/First_Opium_War\" title=\"First Opium War\">First Opium War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Treaty_of_Nanking\" title=\"Treaty of Nanking\">Treaty of Nanking</a> signing ends the <a href=\"https://wikipedia.org/wiki/First_Opium_War\" title=\"First Opium War\">First Opium War</a>.", "links": [{"title": "Treaty of Nanking", "link": "https://wikipedia.org/wiki/Treaty_of_Nanking"}, {"title": "First Opium War", "link": "https://wikipedia.org/wiki/First_Opium_War"}]}, {"year": "1861", "text": "American Civil War: The Battle of Hatteras Inlet Batteries gives Federal forces control of Pamlico Sound.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries\" title=\"Battle of Hatteras Inlet Batteries\">Battle of Hatteras Inlet Batteries</a> gives Federal forces control of <a href=\"https://wikipedia.org/wiki/Pamlico_Sound\" title=\"Pamlico Sound\">Pamlico Sound</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries\" title=\"Battle of Hatteras Inlet Batteries\">Battle of Hatteras Inlet Batteries</a> gives Federal forces control of <a href=\"https://wikipedia.org/wiki/Pamlico_Sound\" title=\"Pamlico Sound\">Pamlico Sound</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Hatteras Inlet Batteries", "link": "https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries"}, {"title": "Pamlico Sound", "link": "https://wikipedia.org/wiki/Pamlico_Sound"}]}, {"year": "1869", "text": "The Mount Washington Cog Railway opens, making it the world's first mountain-climbing rack railway.", "html": "1869 - The <a href=\"https://wikipedia.org/wiki/Mount_Washington_Cog_Railway\" title=\"Mount Washington Cog Railway\">Mount Washington Cog Railway</a> opens, making it the world's first mountain-climbing <a href=\"https://wikipedia.org/wiki/Rack_railway\" title=\"Rack railway\">rack railway</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mount_Washington_Cog_Railway\" title=\"Mount Washington Cog Railway\">Mount Washington Cog Railway</a> opens, making it the world's first mountain-climbing <a href=\"https://wikipedia.org/wiki/Rack_railway\" title=\"Rack railway\">rack railway</a>.", "links": [{"title": "Mount Washington Cog Railway", "link": "https://wikipedia.org/wiki/Mount_Washington_Cog_Railway"}, {"title": "Rack railway", "link": "https://wikipedia.org/wiki/Rack_railway"}]}, {"year": "1871", "text": "Emperor <PERSON> orders the abolition of the han system and the establishment of prefectures as local centers of administration. (Traditional Japanese date: July 14, 1871).", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>\" title=\"Emperor Meiji\">Emperor Meiji</a> orders the <a href=\"https://wikipedia.org/wiki/Abolition_of_the_han_system\" title=\"Abolition of the han system\">abolition of the han system</a> and the establishment of <a href=\"https://wikipedia.org/wiki/Prefectures_of_Japan\" title=\"Prefectures of Japan\">prefectures</a> as local centers of administration. (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 14, 1871).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Meiji\" title=\"Emperor Meiji\">Emperor Meiji</a> orders the <a href=\"https://wikipedia.org/wiki/Abolition_of_the_han_system\" title=\"Abolition of the han system\">abolition of the han system</a> and the establishment of <a href=\"https://wikipedia.org/wiki/Prefectures_of_Japan\" title=\"Prefectures of Japan\">prefectures</a> as local centers of administration. (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 14, 1871).", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>"}, {"title": "Abolition of the han system", "link": "https://wikipedia.org/wiki/Abolition_of_the_han_system"}, {"title": "Prefectures of Japan", "link": "https://wikipedia.org/wiki/Prefectures_of_Japan"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON><PERSON> Daimler patents the world's first motorcycle with an internal combustion engine, the Reitwagen.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Daimler\" title=\"Gottlieb Daimler\"><PERSON><PERSON><PERSON><PERSON></a> patents the world's first <a href=\"https://wikipedia.org/wiki/Motorcycle\" title=\"Motorcycle\">motorcycle</a> with an <a href=\"https://wikipedia.org/wiki/Internal_combustion\" class=\"mw-redirect\" title=\"Internal combustion\">internal combustion</a> engine, the <i><a href=\"https://wikipedia.org/wiki/Daimler_Reitwagen\" title=\"Daimler Reitwagen\">Reitwagen</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>mler\" title=\"Gottl<PERSON><PERSON> Daimler\"><PERSON><PERSON><PERSON><PERSON></a> patents the world's first <a href=\"https://wikipedia.org/wiki/Motorcycle\" title=\"Motorcycle\">motorcycle</a> with an <a href=\"https://wikipedia.org/wiki/Internal_combustion\" class=\"mw-redirect\" title=\"Internal combustion\">internal combustion</a> engine, the <i><a href=\"https://wikipedia.org/wiki/Daimler_Reitwagen\" title=\"Daimler Reitwagen\">Reitwagen</a></i>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Motorcycle", "link": "https://wikipedia.org/wiki/Motorcycle"}, {"title": "Internal combustion", "link": "https://wikipedia.org/wiki/Internal_combustion"}, {"title": "Daimler Reitwagen", "link": "https://wikipedia.org/wiki/Daimler_Reitwagen"}]}, {"year": "1898", "text": "The Goodyear tire company is founded in Akron, Ohio.", "html": "1898 - The <a href=\"https://wikipedia.org/wiki/Goodyear_Tire_and_Rubber_Company\" title=\"Goodyear Tire and Rubber Company\">Goodyear</a> tire company is founded in <a href=\"https://wikipedia.org/wiki/Akron,_Ohio\" title=\"Akron, Ohio\">Akron, Ohio</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Goodyear_Tire_and_Rubber_Company\" title=\"Goodyear Tire and Rubber Company\">Goodyear</a> tire company is founded in <a href=\"https://wikipedia.org/wiki/Akron,_Ohio\" title=\"Akron, Ohio\">Akron, Ohio</a>.", "links": [{"title": "Goodyear Tire and Rubber Company", "link": "https://wikipedia.org/wiki/Goodyear_Tire_and_R<PERSON>ber_Company"}, {"title": "Akron, Ohio", "link": "https://wikipedia.org/wiki/Akron,_Ohio"}]}, {"year": "1903", "text": "The Slava, the last of the five Borodino-class battleships, is launched.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Russian_battleship_Slava\" title=\"Russian battleship Slava\"><i>Slava</i></a>, the last of the five <a href=\"https://wikipedia.org/wiki/Borodino-class_battleship\" title=\"Borodino-class battleship\"><i>Borodino</i>-class battleships</a>, is launched.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_battleship_Slava\" title=\"Russian battleship Slava\"><i>Slava</i></a>, the last of the five <a href=\"https://wikipedia.org/wiki/Borodino-class_battleship\" title=\"Borodino-class battleship\"><i>Borodino</i>-class battleships</a>, is launched.", "links": [{"title": "Russian battleship Slava", "link": "https://wikipedia.org/wiki/Russian_battleship_Slava"}, {"title": "Borodino-class battleship", "link": "https://wikipedia.org/wiki/Borodino-class_battleship"}]}, {"year": "1907", "text": "The Quebec Bridge collapses during construction, killing 75 workers.", "html": "1907 - The <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a> collapses during construction, killing 75 workers.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Quebec_Bridge\" title=\"Quebec Bridge\">Quebec Bridge</a> collapses during construction, killing 75 workers.", "links": [{"title": "Quebec Bridge", "link": "https://wikipedia.org/wiki/Quebec_Bridge"}]}, {"year": "1910", "text": "The Japan-Korea Treaty of 1910, also known as the Japan-Korea Annexation Treaty, becomes effective, officially starting the period of Japanese rule in Korea.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Japan%E2%80%93Korea_Treaty_of_1910\" title=\"Japan-Korea Treaty of 1910\">Japan-Korea Treaty of 1910</a>, also known as the Japan-Korea Annexation Treaty, becomes effective, officially starting the <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">period of Japanese rule</a> in Korea.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Japan%E2%80%93Korea_Treaty_of_1910\" title=\"Japan-Korea Treaty of 1910\">Japan-Korea Treaty of 1910</a>, also known as the Japan-Korea Annexation Treaty, becomes effective, officially starting the <a href=\"https://wikipedia.org/wiki/Korea_under_Japanese_rule\" title=\"Korea under Japanese rule\">period of Japanese rule</a> in Korea.", "links": [{"title": "Japan-Korea Treaty of 1910", "link": "https://wikipedia.org/wiki/Japan%E2%80%93Korea_Treaty_of_1910"}, {"title": "Korea under Japanese rule", "link": "https://wikipedia.org/wiki/Korea_under_Japanese_rule"}]}, {"year": "1911", "text": "<PERSON><PERSON>, considered the last Native American to make contact with European Americans, emerges from the wilderness of northeastern California.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>hi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, considered the last <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> to make contact with European Americans, emerges from the wilderness of northeastern <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, considered the last <a href=\"https://wikipedia.org/wiki/Native_Americans_in_the_United_States\" title=\"Native Americans in the United States\">Native American</a> to make contact with European Americans, emerges from the wilderness of northeastern <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ishi"}, {"title": "Native Americans in the United States", "link": "https://wikipedia.org/wiki/Native_Americans_in_the_United_States"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1911", "text": "The Canadian Naval Service becomes the Royal Canadian Navy.", "html": "1911 - The Canadian Naval Service becomes the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>.", "no_year_html": "The Canadian Naval Service becomes the <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a>.", "links": [{"title": "Royal Canadian Navy", "link": "https://wikipedia.org/wiki/Royal_Canadian_Navy"}]}, {"year": "1912", "text": "A typhoon strikes China, killing at least 50,000 people.", "html": "1912 - A <a href=\"https://wikipedia.org/wiki/1912_China_typhoon\" title=\"1912 China typhoon\">typhoon strikes China</a>, killing at least 50,000 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1912_China_typhoon\" title=\"1912 China typhoon\">typhoon strikes China</a>, killing at least 50,000 people.", "links": [{"title": "1912 China typhoon", "link": "https://wikipedia.org/wiki/1912_China_typhoon"}]}, {"year": "1914", "text": "World War I: Start of the Battle of St. Quentin in which the French Fifth Army counter-attacked the invading Germans at Saint-Quentin, Aisne.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Quentin_(1914)\" title=\"Battle of St. Quentin (1914)\">Battle of St. Quentin</a> in which the French <a href=\"https://wikipedia.org/wiki/Fifth_Army_(France)\" class=\"mw-redirect\" title=\"Fifth Army (France)\">Fifth Army</a> counter-attacked the invading Germans at <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Aisne\" title=\"Saint<PERSON><PERSON>, Aisne\"><PERSON><PERSON><PERSON>, Aisne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: Start of the <a href=\"https://wikipedia.org/wiki/Battle_of_St._Quentin_(1914)\" title=\"Battle of St. Quentin (1914)\">Battle of St. Quentin</a> in which the French <a href=\"https://wikipedia.org/wiki/Fifth_Army_(France)\" class=\"mw-redirect\" title=\"Fifth Army (France)\">Fifth Army</a> counter-attacked the invading Germans at <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>,_Aisne\" title=\"Saint-<PERSON>, Aisne\">Saint-<PERSON>, Aisne</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of St. Quentin (1914)", "link": "https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1914)"}, {"title": "Fifth Army (France)", "link": "https://wikipedia.org/wiki/Fifth_Army_(France)"}, {"title": "Saint<PERSON><PERSON>, Aisne", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>"}]}, {"year": "1915", "text": "US Navy salvage divers raise F-4, the first U.S. submarine sunk in an accident.", "html": "1915 - US Navy salvage divers raise <a href=\"https://wikipedia.org/wiki/USS_F-4\" title=\"USS F-4\"><i>F-4</i></a>, the first U.S. <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> sunk in an accident.", "no_year_html": "US Navy salvage divers raise <a href=\"https://wikipedia.org/wiki/USS_F-4\" title=\"USS F-4\"><i>F-4</i></a>, the first U.S. <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> sunk in an accident.", "links": [{"title": "USS F-4", "link": "https://wikipedia.org/wiki/USS_F-4"}, {"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}]}, {"year": "1916", "text": "The United States passes the Philippine Autonomy Act.", "html": "1916 - The United States passes the <a href=\"https://wikipedia.org/wiki/Jones_Law_(Philippines)\" title=\"Jones Law (Philippines)\">Philippine Autonomy Act</a>.", "no_year_html": "The United States passes the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Philippines)\" title=\"Jones Law (Philippines)\">Philippine Autonomy Act</a>.", "links": [{"title": "<PERSON> (Philippines)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Philippines)"}]}, {"year": "1918", "text": "World War I: Bapaume taken by the New Zealand Division in the Hundred Days Offensive.", "html": "1918 - World War I: <a href=\"https://wikipedia.org/wiki/Bapaume\" title=\"Bapaume\">Bapaume</a> taken by the <a href=\"https://wikipedia.org/wiki/New_Zealand_Division\" title=\"New Zealand Division\">New Zealand Division</a> in the <a href=\"https://wikipedia.org/wiki/Hundred_Days_Offensive\" title=\"Hundred Days Offensive\">Hundred Days Offensive</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Bapaume\" title=\"Bapaume\">Bapaume</a> taken by the <a href=\"https://wikipedia.org/wiki/New_Zealand_Division\" title=\"New Zealand Division\">New Zealand Division</a> in the <a href=\"https://wikipedia.org/wiki/Hundred_Days_Offensive\" title=\"Hundred Days Offensive\">Hundred Days Offensive</a>.", "links": [{"title": "Bapaume", "link": "https://wikipedia.org/wiki/Bapaume"}, {"title": "New Zealand Division", "link": "https://wikipedia.org/wiki/New_Zealand_Division"}, {"title": "Hundred Days Offensive", "link": "https://wikipedia.org/wiki/Hundred_Days_Offensive"}]}, {"year": "1930", "text": "The last 36 remaining inhabitants of St Kilda are voluntarily evacuated to other parts of Scotland.", "html": "1930 - The last 36 remaining inhabitants of <a href=\"https://wikipedia.org/wiki/St_Kilda,_Scotland\" title=\"St Kilda, Scotland\">St Kilda</a> are voluntarily evacuated to other parts of Scotland.", "no_year_html": "The last 36 remaining inhabitants of <a href=\"https://wikipedia.org/wiki/St_Kilda,_Scotland\" title=\"St Kilda, Scotland\">St Kilda</a> are voluntarily evacuated to other parts of Scotland.", "links": [{"title": "St Kilda, Scotland", "link": "https://wikipedia.org/wiki/St_Kilda,_Scotland"}]}, {"year": "1941", "text": "World War II: Tallinn, the capital of Estonia, is occupied by Nazi Germany following an occupation by the Soviet Union.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>, the capital of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, is occupied by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> following an occupation by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>, the capital of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, is occupied by <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> following an occupation by the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Tallinn", "link": "https://wikipedia.org/wiki/Tallinn"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1943", "text": "World War II: German-occupied Denmark scuttles most of its navy; Germany dissolves the Danish government.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Occupation_of_Denmark\" class=\"mw-redirect\" title=\"Occupation of Denmark\">German-occupied Denmark</a> scuttles most of its <a href=\"https://wikipedia.org/wiki/Royal_Danish_Navy\" title=\"Royal Danish Navy\">navy</a>; Germany dissolves the Danish government.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Occupation_of_Denmark\" class=\"mw-redirect\" title=\"Occupation of Denmark\">German-occupied Denmark</a> scuttles most of its <a href=\"https://wikipedia.org/wiki/Royal_Danish_Navy\" title=\"Royal Danish Navy\">navy</a>; Germany dissolves the Danish government.", "links": [{"title": "Occupation of Denmark", "link": "https://wikipedia.org/wiki/Occupation_of_Denmark"}, {"title": "Royal Danish Navy", "link": "https://wikipedia.org/wiki/Royal_Danish_Navy"}]}, {"year": "1944", "text": "World War II:  Slovak National Uprising takes place as 60,000 Slovak troops turn against the Nazis.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Slovak_National_Uprising\" title=\"Slovak National Uprising\">Slovak National Uprising</a> takes place as 60,000 <a href=\"https://wikipedia.org/wiki/Slovaks\" title=\"Slovaks\">Slovak</a> troops turn against the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Slovak_National_Uprising\" title=\"Slovak National Uprising\">Slovak National Uprising</a> takes place as 60,000 <a href=\"https://wikipedia.org/wiki/Slovaks\" title=\"Slovaks\">Slovak</a> troops turn against the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazis</a>.", "links": [{"title": "Slovak National Uprising", "link": "https://wikipedia.org/wiki/Slovak_National_Uprising"}, {"title": "Slovaks", "link": "https://wikipedia.org/wiki/Slovaks"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}]}, {"year": "1948", "text": "Northwest Airlines Flight 421 crashes in Fountain City, Wisconsin, killing all 37 aboard.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Northwest_Airlines_Flight_421\" class=\"mw-redirect\" title=\"Northwest Airlines Flight 421\">Northwest Airlines Flight 421</a> crashes in <a href=\"https://wikipedia.org/wiki/Fountain_City,_Wisconsin\" title=\"Fountain City, Wisconsin\">Fountain City, Wisconsin</a>, killing all 37 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Airlines_Flight_421\" class=\"mw-redirect\" title=\"Northwest Airlines Flight 421\">Northwest Airlines Flight 421</a> crashes in <a href=\"https://wikipedia.org/wiki/Fountain_City,_Wisconsin\" title=\"Fountain City, Wisconsin\">Fountain City, Wisconsin</a>, killing all 37 aboard.", "links": [{"title": "Northwest Airlines Flight 421", "link": "https://wikipedia.org/wiki/Northwest_Airlines_Flight_421"}, {"title": "Fountain City, Wisconsin", "link": "https://wikipedia.org/wiki/Fountain_City,_Wisconsin"}]}, {"year": "1949", "text": "Soviet atomic bomb project: The Soviet Union tests its first atomic bomb, known as First Lightning or Joe 1, at Semipalatinsk, Kazakhstan.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Soviet_atomic_bomb_project\" title=\"Soviet atomic bomb project\">Soviet atomic bomb project</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> tests its first <a href=\"https://wikipedia.org/wiki/Atomic_bomb\" class=\"mw-redirect\" title=\"Atomic bomb\">atomic bomb</a>, known as <i><a href=\"https://wikipedia.org/wiki/RDS-1\" title=\"RDS-1\">First Lightning</a></i> or <i>Joe 1</i>, at <a href=\"https://wikipedia.org/wiki/Semipalatinsk_Test_Site\" title=\"Semipalatinsk Test Site\">Semipalatinsk</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soviet_atomic_bomb_project\" title=\"Soviet atomic bomb project\">Soviet atomic bomb project</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> tests its first <a href=\"https://wikipedia.org/wiki/Atomic_bomb\" class=\"mw-redirect\" title=\"Atomic bomb\">atomic bomb</a>, known as <i><a href=\"https://wikipedia.org/wiki/RDS-1\" title=\"RDS-1\">First Lightning</a></i> or <i>Joe 1</i>, at <a href=\"https://wikipedia.org/wiki/Semipalatinsk_Test_Site\" title=\"Semipalatinsk Test Site\">Semipalatinsk</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>.", "links": [{"title": "Soviet atomic bomb project", "link": "https://wikipedia.org/wiki/Soviet_atomic_bomb_project"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Atomic bomb", "link": "https://wikipedia.org/wiki/Atomic_bomb"}, {"title": "RDS-1", "link": "https://wikipedia.org/wiki/RDS-1"}, {"title": "Semipalatinsk Test Site", "link": "https://wikipedia.org/wiki/Semipalatinsk_Test_Site"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}, {"year": "1950", "text": "Korean War: British Commonwealth Forces Korea arrives to bolster the US presence.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/British_Commonwealth_Forces_Korea\" title=\"British Commonwealth Forces Korea\">British Commonwealth Forces Korea</a> arrives to bolster the US presence.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: <a href=\"https://wikipedia.org/wiki/British_Commonwealth_Forces_Korea\" title=\"British Commonwealth Forces Korea\">British Commonwealth Forces Korea</a> arrives to bolster the US presence.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "British Commonwealth Forces Korea", "link": "https://wikipedia.org/wiki/British_Commonwealth_Forces_Korea"}]}, {"year": "1952", "text": "American experimental composer <PERSON>’s 4’33” premieres at Maverick Concert Hall, played by American pianist <PERSON>.", "html": "1952 - American <a href=\"https://wikipedia.org/wiki/Experimental_music\" title=\"Experimental music\">experimental composer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s <i><a href=\"https://wikipedia.org/wiki/4%E2%80%9933%E2%80%9D\" class=\"mw-redirect\" title=\"4’33”\">4’33”</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Maverick_Concert_Hall\" title=\"Maverick Concert Hall\">Maverick Concert Hall</a>, played by <a href=\"https://wikipedia.org/wiki/American_people\" class=\"mw-redirect\" title=\"American people\">American</a> pianist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American <a href=\"https://wikipedia.org/wiki/Experimental_music\" title=\"Experimental music\">experimental composer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s <i><a href=\"https://wikipedia.org/wiki/4%E2%80%9933%E2%80%9D\" class=\"mw-redirect\" title=\"4’33”\">4’33”</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Maverick_Concert_Hall\" title=\"Maverick Concert Hall\">Maverick Concert Hall</a>, played by <a href=\"https://wikipedia.org/wiki/American_people\" class=\"mw-redirect\" title=\"American people\">American</a> pianist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Experimental music", "link": "https://wikipedia.org/wiki/Experimental_music"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "4’33”", "link": "https://wikipedia.org/wiki/4%E2%80%9933%E2%80%9D"}, {"title": "Maverick Concert Hall", "link": "https://wikipedia.org/wiki/Maverick_Concert_Hall"}, {"title": "American people", "link": "https://wikipedia.org/wiki/American_people"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "United States Air Force Academy opens in Colorado Springs, Colorado.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/United_States_Air_Force_Academy\" title=\"United States Air Force Academy\">United States Air Force Academy</a> opens in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Air_Force_Academy\" title=\"United States Air Force Academy\">United States Air Force Academy</a> opens in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>.", "links": [{"title": "United States Air Force Academy", "link": "https://wikipedia.org/wiki/United_States_Air_Force_Academy"}, {"title": "Colorado Springs, Colorado", "link": "https://wikipedia.org/wiki/Colorado_Springs,_Colorado"}]}, {"year": "1960", "text": "Air France Flight 343 crashes on approach to Yoff Airport in Senegal, killing all 63 aboard.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_343\" title=\"Air France Flight 343\">Air France Flight 343</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor_International_Airport\" title=\"Léopold Sédar Senghor International Airport\">Yoff Airport</a> in Senegal, killing all 63 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_343\" title=\"Air France Flight 343\">Air France Flight 343</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor_International_Airport\" title=\"Léopold Sédar Senghor International Airport\">Yoff Airport</a> in Senegal, killing all 63 aboard.", "links": [{"title": "Air France Flight 343", "link": "https://wikipedia.org/wiki/Air_France_Flight_343"}, {"title": "Léopold Sédar <PERSON> International Airport", "link": "https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor_International_Airport"}]}, {"year": "1965", "text": "The Gemini V spacecraft returns to Earth, landing in the Atlantic Ocean.", "html": "1965 - The <a href=\"https://wikipedia.org/wiki/Gemini_5\" title=\"Gemini 5\">Gemini V</a> spacecraft returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>, landing in the Atlantic Ocean.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gemini_5\" title=\"Gemini 5\">Gemini V</a> spacecraft returns to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a>, landing in the Atlantic Ocean.", "links": [{"title": "Gemini 5", "link": "https://wikipedia.org/wiki/Gemini_5"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}]}, {"year": "1966", "text": "The Beatles perform their last concert before paying fans at Candlestick Park in San Francisco.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> perform their last concert before paying fans at <a href=\"https://wikipedia.org/wiki/Candlestick_Park\" title=\"Candlestick Park\">Candlestick Park</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Beatles\" title=\"The Beatles\">The Beatles</a> perform their last concert before paying fans at <a href=\"https://wikipedia.org/wiki/Candlestick_Park\" title=\"Candlestick Park\">Candlestick Park</a> in <a href=\"https://wikipedia.org/wiki/San_Francisco\" title=\"San Francisco\">San Francisco</a>.", "links": [{"title": "The Beatles", "link": "https://wikipedia.org/wiki/The_Beatles"}, {"title": "Candlestick Park", "link": "https://wikipedia.org/wiki/Candlestick_Park"}, {"title": "San Francisco", "link": "https://wikipedia.org/wiki/San_Francisco"}]}, {"year": "1966", "text": "Leading Egyptian thinker <PERSON><PERSON><PERSON> is executed for plotting the assassination of President <PERSON><PERSON><PERSON>.", "html": "1966 - Leading Egyptian thinker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is executed for plotting the assassination of President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Leading Egyptian thinker <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is executed for plotting the assassination of President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>b"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Chicano Moratorium against the Vietnam War, East Los Angeles, California. Police riot kills three people, including journalist <PERSON><PERSON><PERSON>.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Chicano_Moratorium\" title=\"Chicano Moratorium\">Chicano Moratorium</a> against the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/East_Los_Angeles,_California\" title=\"East Los Angeles, California\">East Los Angeles, California</a>. Police riot kills three people, including journalist <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chicano_Moratorium\" title=\"Chicano Moratorium\">Chicano Moratorium</a> against the <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>, <a href=\"https://wikipedia.org/wiki/East_Los_Angeles,_California\" title=\"East Los Angeles, California\">East Los Angeles, California</a>. Police riot kills three people, including journalist <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Chicano Moratorium", "link": "https://wikipedia.org/wiki/Chicano_Moratorium"}, {"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "East Los Angeles, California", "link": "https://wikipedia.org/wiki/East_Los_Angeles,_California"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_Salazar"}]}, {"year": "1975", "text": "El Tacnazo:  <PERSON>, Peruvian Prime Minister carries out a coup d’état in the city of Tacna, forcing the sitting President of Peru, <PERSON>, to resign and assuming his place as the new President.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>c<PERSON><PERSON>\"><PERSON></a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Peruvian <a href=\"https://wikipedia.org/wiki/President_of_the_Council_of_Ministers_of_Peru\" class=\"mw-redirect\" title=\"President of the Council of Ministers of Peru\">Prime Minister</a> carries out a <a href=\"https://wikipedia.org/wiki/Tacna<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">coup d’état</a> in the city of <a href=\"https://wikipedia.org/wiki/Tacna\" title=\"Tacna\">Tacna</a>, forcing the sitting <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, to resign and assuming his place as the new <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>c<PERSON><PERSON>\"><PERSON></a>: <a href=\"https://wikipedia.org/wiki/<PERSON>%C3%BAdez\" title=\"<PERSON>\"><PERSON></a>, Peruvian <a href=\"https://wikipedia.org/wiki/President_of_the_Council_of_Ministers_of_Peru\" class=\"mw-redirect\" title=\"President of the Council of Ministers of Peru\">Prime Minister</a> carries out a <a href=\"https://wikipedia.org/wiki/Tac<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">coup d’état</a> in the city of <a href=\"https://wikipedia.org/wiki/Tacna\" title=\"Tacna\">Tacna</a>, forcing the sitting <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President of Peru</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, to resign and assuming his place as the new <a href=\"https://wikipedia.org/wiki/President_of_Peru\" title=\"President of Peru\">President</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BAdez"}, {"title": "President of the Council of Ministers of Peru", "link": "https://wikipedia.org/wiki/President_of_the_Council_of_Ministers_of_Peru"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Tacna", "link": "https://wikipedia.org/wiki/Tacna"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Peru", "link": "https://wikipedia.org/wiki/President_of_Peru"}]}, {"year": "1982", "text": " Meitnerium, a synthetic chemical element with the atomic number 109, is first synthesized at the Gesellschaft für Schwerionenforschung in Darmstadt, Germany.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Meitnerium\" title=\"Meitnerium\">Meitnerium</a>, a synthetic <a href=\"https://wikipedia.org/wiki/Chemical_element\" title=\"Chemical element\">chemical element</a> with the <a href=\"https://wikipedia.org/wiki/Atomic_number\" title=\"Atomic number\">atomic number</a> 109, is first synthesized at the <a href=\"https://wikipedia.org/wiki/Gesellschaft_f%C3%BCr_Schwerionenforschung\" class=\"mw-redirect\" title=\"Gesellschaft für Schwerionenforschung\">Gesellschaft für Schwerionenforschung</a> in <a href=\"https://wikipedia.org/wiki/Darmstadt\" title=\"Darmstadt\">Darmstadt</a>, Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meitnerium\" title=\"Meitnerium\">Meitnerium</a>, a synthetic <a href=\"https://wikipedia.org/wiki/Chemical_element\" title=\"Chemical element\">chemical element</a> with the <a href=\"https://wikipedia.org/wiki/Atomic_number\" title=\"Atomic number\">atomic number</a> 109, is first synthesized at the <a href=\"https://wikipedia.org/wiki/Gesellschaft_f%C3%BCr_Schwerionenforschung\" class=\"mw-redirect\" title=\"Gesellschaft für Schwerionenforschung\">Gesellschaft für Schwerionenforschung</a> in <a href=\"https://wikipedia.org/wiki/Darmstadt\" title=\"Darmstadt\">Darmstadt</a>, Germany.", "links": [{"title": "Meitnerium", "link": "https://wikipedia.org/wiki/Meitnerium"}, {"title": "Chemical element", "link": "https://wikipedia.org/wiki/Chemical_element"}, {"title": "Atomic number", "link": "https://wikipedia.org/wiki/Atomic_number"}, {"title": "Gesellschaft für Schwerionenforschung", "link": "https://wikipedia.org/wiki/Gesellschaft_f%C3%BCr_Schwerionenforschung"}, {"title": "Darmstadt", "link": "https://wikipedia.org/wiki/Darmstadt"}]}, {"year": "1987", "text": "Odaeyang mass suicide: Thirty-three individuals linked to a religious cult are found dead in the attic of a cafeteria in Yongin, South Korea. Investigators attribute their deaths to a murder-suicide pact.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea#Odaeyang_mass_suicide\" title=\"Evangelical Baptist Church of Korea\">Odaeyang mass suicide</a>: Thirty-three individuals linked to a <a href=\"https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea\" title=\"Evangelical Baptist Church of Korea\">religious cult</a> are found dead in the attic of a cafeteria in <a href=\"https://wikipedia.org/wiki/Yongin\" title=\"Yongin\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>. Investigators attribute their deaths to a <a href=\"https://wikipedia.org/wiki/Murder-suicide\" class=\"mw-redirect\" title=\"Murder-suicide\">murder-suicide</a> pact.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea#Odaeyang_mass_suicide\" title=\"Evangelical Baptist Church of Korea\">Odaeyang mass suicide</a>: Thirty-three individuals linked to a <a href=\"https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea\" title=\"Evangelical Baptist Church of Korea\">religious cult</a> are found dead in the attic of a cafeteria in <a href=\"https://wikipedia.org/wiki/Yongin\" title=\"Yongin\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a>. Investigators attribute their deaths to a <a href=\"https://wikipedia.org/wiki/Murder-suicide\" class=\"mw-redirect\" title=\"Murder-suicide\">murder-suicide</a> pact.", "links": [{"title": "Evangelical Baptist Church of Korea", "link": "https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea#Odaeyang_mass_suicide"}, {"title": "Evangelical Baptist Church of Korea", "link": "https://wikipedia.org/wiki/Evangelical_Baptist_Church_of_Korea"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>in"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "Murder-suicide", "link": "https://wikipedia.org/wiki/Murder-suicide"}]}, {"year": "1991", "text": "Supreme Soviet of the Soviet Union suspends all activities of the Soviet Communist Party.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Supreme_Soviet_of_the_Soviet_Union\" title=\"Supreme Soviet of the Soviet Union\">Supreme Soviet of the Soviet Union</a> suspends all activities of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union\" title=\"Communist Party of the Soviet Union\">Soviet Communist Party</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Supreme_Soviet_of_the_Soviet_Union\" title=\"Supreme Soviet of the Soviet Union\">Supreme Soviet of the Soviet Union</a> suspends all activities of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union\" title=\"Communist Party of the Soviet Union\">Soviet Communist Party</a>.", "links": [{"title": "Supreme Soviet of the Soviet Union", "link": "https://wikipedia.org/wiki/Supreme_Soviet_of_the_Soviet_Union"}, {"title": "Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Communist_Party_of_the_Soviet_Union"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, an Italian businessman from Palermo, is killed by the Sicilian Mafia after taking a solitary stand against their extortion demands.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Libero_Grassi\" title=\"Libero <PERSON>\"><PERSON><PERSON><PERSON></a>, an Italian businessman from <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, is killed by the <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Sicilian Mafia</a> after taking a solitary stand against their <a href=\"https://wikipedia.org/wiki/Extortion\" title=\"Extortion\">extortion</a> demands.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libero_Grassi\" title=\"Libero <PERSON>\"><PERSON><PERSON><PERSON></a>, an Italian businessman from <a href=\"https://wikipedia.org/wiki/Palermo\" title=\"Palermo\">Palermo</a>, is killed by the <a href=\"https://wikipedia.org/wiki/Sicilian_Mafia\" title=\"Sicilian Mafia\">Sicilian Mafia</a> after taking a solitary stand against their <a href=\"https://wikipedia.org/wiki/Extortion\" title=\"Extortion\">extortion</a> demands.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Libero_Grassi"}, {"title": "Palermo", "link": "https://wikipedia.org/wiki/Palermo"}, {"title": "Sicilian Mafia", "link": "https://wikipedia.org/wiki/Sicilian_Mafia"}, {"title": "Extortion", "link": "https://wikipedia.org/wiki/Extortion"}]}, {"year": "1996", "text": "Vnukovo Airlines Flight 2801, a Tupolev Tu-154, crashes into a mountain on the Arctic island of Spitsbergen, killing all 141 aboard.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Vnukovo_Airlines_Flight_2801\" title=\"Vnukovo Airlines Flight 2801\">Vnukovo Airlines Flight 2801</a>, a <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a>, crashes into a mountain on the <a href=\"https://wikipedia.org/wiki/Arctic\" title=\"Arctic\">Arctic</a> island of <a href=\"https://wikipedia.org/wiki/Spitsbergen\" title=\"Spitsbergen\">Spitsbergen</a>, killing all 141 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vnukovo_Airlines_Flight_2801\" title=\"Vnukovo Airlines Flight 2801\">Vnukovo Airlines Flight 2801</a>, a <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a>, crashes into a mountain on the <a href=\"https://wikipedia.org/wiki/Arctic\" title=\"Arctic\">Arctic</a> island of <a href=\"https://wikipedia.org/wiki/Spitsbergen\" title=\"Spitsbergen\">Spitsbergen</a>, killing all 141 aboard.", "links": [{"title": "Vnukovo Airlines Flight 2801", "link": "https://wikipedia.org/wiki/Vnukovo_Airlines_Flight_2801"}, {"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "Arctic", "link": "https://wikipedia.org/wiki/Arctic"}, {"title": "Spitsbergen", "link": "https://wikipedia.org/wiki/Spitsbergen"}]}, {"year": "1997", "text": "Netflix is launched as an internet DVD rental service.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Netflix\" title=\"Netflix\">Netflix</a> is launched as an internet <a href=\"https://wikipedia.org/wiki/DVD\" title=\"DVD\">DVD</a> rental service.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Netflix\" title=\"Netflix\">Netflix</a> is launched as an internet <a href=\"https://wikipedia.org/wiki/DVD\" title=\"DVD\">DVD</a> rental service.", "links": [{"title": "Netflix", "link": "https://wikipedia.org/wiki/Netflix"}, {"title": "DVD", "link": "https://wikipedia.org/wiki/DVD"}]}, {"year": "1997", "text": "At least 98 villagers are killed by the Armed Islamic Group of Algeria GIA in the Rais massacre, Algeria.", "html": "1997 - At least 98 villagers are killed by the Armed Islamic Group of Algeria <a href=\"https://wikipedia.org/wiki/Armed_Islamic_Group\" class=\"mw-redirect\" title=\"Armed Islamic Group\">GIA</a> in the <a href=\"https://wikipedia.org/wiki/Rais_massacre\" title=\"Rais massacre\">Rais massacre</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "At least 98 villagers are killed by the Armed Islamic Group of Algeria <a href=\"https://wikipedia.org/wiki/Armed_Islamic_Group\" class=\"mw-redirect\" title=\"Armed Islamic Group\">GIA</a> in the <a href=\"https://wikipedia.org/wiki/Rais_massacre\" title=\"Rais massacre\">Rais massacre</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "Armed Islamic Group", "link": "https://wikipedia.org/wiki/Armed_Islamic_Group"}, {"title": "Rais massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_massacre"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "1998", "text": "Eighty people are killed when Cubana de Aviación Flight 389 crashes during a rejected takeoff from the Old Mariscal Sucre International Airport in Quito, Ecuador.", "html": "1998 - Eighty people are killed when <a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_389\" title=\"Cubana de Aviación Flight 389\">Cubana de Aviación Flight 389</a> crashes during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from the <a href=\"https://wikipedia.org/wiki/Old_Mariscal_Sucre_International_Airport\" title=\"Old Mariscal Sucre International Airport\">Old Mariscal Sucre International Airport</a> in <a href=\"https://wikipedia.org/wiki/Quito\" title=\"Quito\">Quito</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "no_year_html": "Eighty people are killed when <a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_389\" title=\"Cubana de Aviación Flight 389\">Cubana de Aviación Flight 389</a> crashes during a <a href=\"https://wikipedia.org/wiki/Rejected_takeoff\" title=\"Rejected takeoff\">rejected takeoff</a> from the <a href=\"https://wikipedia.org/wiki/Old_Mariscal_Sucre_International_Airport\" title=\"Old Mariscal Sucre International Airport\">Old Mariscal Sucre International Airport</a> in <a href=\"https://wikipedia.org/wiki/Quito\" title=\"Quito\">Quito</a>, <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a>.", "links": [{"title": "Cubana de Aviación Flight 389", "link": "https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_389"}, {"title": "Rejected takeoff", "link": "https://wikipedia.org/wiki/Rejected_takeoff"}, {"title": "Old Mariscal Sucre International Airport", "link": "https://wikipedia.org/wiki/Old_Mariscal_Sucre_International_Airport"}, {"title": "Quito", "link": "https://wikipedia.org/wiki/Quito"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}]}, {"year": "2001", "text": "Four people are killed when Binter Mediterráneo Flight 8261 crashes into the N-340 highway near Málaga Airport.", "html": "2001 - Four people are killed when <a href=\"https://wikipedia.org/wiki/Binter_Mediterr%C3%A1neo_Flight_8261\" title=\"Binter Mediterráneo Flight 8261\">Binter Mediterráneo Flight 8261</a> crashes into the <a href=\"https://wikipedia.org/wiki/N-340_road_(Spain)\" title=\"N-340 road (Spain)\">N-340 highway</a> near <a href=\"https://wikipedia.org/wiki/M%C3%A1laga_Airport\" title=\"Málaga Airport\">Málaga Airport</a>.", "no_year_html": "Four people are killed when <a href=\"https://wikipedia.org/wiki/Binter_Mediterr%C3%A1neo_Flight_8261\" title=\"Binter Mediterráneo Flight 8261\">Binter Mediterráneo Flight 8261</a> crashes into the <a href=\"https://wikipedia.org/wiki/N-340_road_(Spain)\" title=\"N-340 road (Spain)\">N-340 highway</a> near <a href=\"https://wikipedia.org/wiki/M%C3%A1laga_Airport\" title=\"Málaga Airport\">Málaga Airport</a>.", "links": [{"title": "Binter Mediterráneo Flight 8261", "link": "https://wikipedia.org/wiki/Binter_Mediterr%C3%A1neo_Flight_8261"}, {"title": "N-340 road (Spain)", "link": "https://wikipedia.org/wiki/N-340_road_(Spain)"}, {"title": "Málaga Airport", "link": "https://wikipedia.org/wiki/M%C3%A1laga_Airport"}]}, {"year": "2003", "text": "<PERSON><PERSON>, the Shia Muslim leader in Iraq, is assassinated in a terrorist bombing, along with nearly 100 worshippers as they leave a mosque in Najaf.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Sayed\" class=\"mw-redirect\" title=\"Sayed\">Sayed</a> <a href=\"https://wikipedia.org/wiki/Ayatollah\" title=\"Ayatollah\"><PERSON><PERSON>to<PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_al-Hakim\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Shia_Muslim\" class=\"mw-redirect\" title=\"Shia Muslim\">Shia Muslim</a> leader in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, is <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a> in a <a href=\"https://wikipedia.org/wiki/Imam_Ali_mosque_bombing\" class=\"mw-redirect\" title=\"Imam Ali mosque bombing\">terrorist bombing</a>, along with nearly 100 worshippers as they leave a <a href=\"https://wikipedia.org/wiki/Mosque\" title=\"Mosque\">mosque</a> in <a href=\"https://wikipedia.org/wiki/Najaf\" title=\"Najaf\">Najaf</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sayed\" class=\"mw-redirect\" title=\"Sayed\">Sayed</a> <a href=\"https://wikipedia.org/wiki/Ayatollah\" title=\"Ayatollah\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Hakim\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the <a href=\"https://wikipedia.org/wiki/Shia_Muslim\" class=\"mw-redirect\" title=\"Shia Muslim\">Shia Muslim</a> leader in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>, is <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassinated</a> in a <a href=\"https://wikipedia.org/wiki/Imam_Ali_mosque_bombing\" class=\"mw-redirect\" title=\"Imam Ali mosque bombing\">terrorist bombing</a>, along with nearly 100 worshippers as they leave a <a href=\"https://wikipedia.org/wiki/Mosque\" title=\"Mosque\">mosque</a> in <a href=\"https://wikipedia.org/wiki/Najaf\" title=\"Najaf\">Najaf</a>.", "links": [{"title": "Sayed", "link": "https://wikipedia.org/wiki/Sayed"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Shia Muslim", "link": "https://wikipedia.org/wiki/Shia_Muslim"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "<PERSON> mosque bombing", "link": "https://wikipedia.org/wiki/Imam_Ali_mosque_bombing"}, {"title": "Mosque", "link": "https://wikipedia.org/wiki/Mosque"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Najaf"}]}, {"year": "2005", "text": "Hurricane Katrina devastates much of the U.S. Gulf Coast from Louisiana to the Florida Panhandle, killing up to 1,836 people and causing $125 billion in damage.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Hurricane Katrina</a> devastates much of the <a href=\"https://wikipedia.org/wiki/U.S._Gulf_Coast\" class=\"mw-redirect\" title=\"U.S. Gulf Coast\">U.S. Gulf Coast</a> from <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a> to the <a href=\"https://wikipedia.org/wiki/Florida_Panhandle\" class=\"mw-redirect\" title=\"Florida Panhandle\">Florida Panhandle</a>, <a href=\"https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_New_Orleans\" title=\"Effects of Hurricane Katrina in New Orleans\">killing up to 1,836 people and causing $125 billion in damage</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Katrina\" title=\"Hurricane Katrina\">Hurricane Katrina</a> devastates much of the <a href=\"https://wikipedia.org/wiki/U.S._Gulf_Coast\" class=\"mw-redirect\" title=\"U.S. Gulf Coast\">U.S. Gulf Coast</a> from <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a> to the <a href=\"https://wikipedia.org/wiki/Florida_Panhandle\" class=\"mw-redirect\" title=\"Florida Panhandle\">Florida Panhandle</a>, <a href=\"https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_New_Orleans\" title=\"Effects of Hurricane Katrina in New Orleans\">killing up to 1,836 people and causing $125 billion in damage</a>.", "links": [{"title": "Hurricane Katrina", "link": "https://wikipedia.org/wiki/Hurricane_Katrina"}, {"title": "U.S. Gulf Coast", "link": "https://wikipedia.org/wiki/U.S._Gulf_Coast"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}, {"title": "Florida Panhandle", "link": "https://wikipedia.org/wiki/Florida_Panhandle"}, {"title": "Effects of Hurricane Katrina in New Orleans", "link": "https://wikipedia.org/wiki/Effects_of_Hurricane_Katrina_in_New_Orleans"}]}, {"year": "2012", "text": "At least 26 Chinese miners are killed and 21 missing after a blast in the Xiaojiawan coal mine, located at Panzhihua, Sichuan Province.", "html": "2012 - At least 26 Chinese miners are killed and 21 missing after a blast in the <a href=\"https://wikipedia.org/wiki/Xiaojiawan_coal_mine_disaster\" title=\"Xiaojiawan coal mine disaster\">Xiaojiawan coal mine</a>, located at <a href=\"https://wikipedia.org/wiki/Panzhihua\" title=\"Panzhihua\">Panzhihua, Sichuan Province</a>.", "no_year_html": "At least 26 Chinese miners are killed and 21 missing after a blast in the <a href=\"https://wikipedia.org/wiki/Xiaojiawan_coal_mine_disaster\" title=\"Xiaojiawan coal mine disaster\">Xiaojiawan coal mine</a>, located at <a href=\"https://wikipedia.org/wiki/Panzhihua\" title=\"Panzhihua\">Panzhihua, Sichuan Province</a>.", "links": [{"title": "Xiaojiawan coal mine disaster", "link": "https://wikipedia.org/wiki/Xiaojiawan_coal_mine_disaster"}, {"title": "Panzhihua", "link": "https://wikipedia.org/wiki/Panzhihua"}]}, {"year": "2012", "text": "The XIV Paralympic Games open in London, England, United Kingdom.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/2012_Summer_Paralympics\" title=\"2012 Summer Paralympics\">XIV Paralympic Games</a> open in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2012_Summer_Paralympics\" title=\"2012 Summer Paralympics\">XIV Paralympic Games</a> open in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, <a href=\"https://wikipedia.org/wiki/England\" title=\"England\">England</a>, <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "links": [{"title": "2012 Summer Paralympics", "link": "https://wikipedia.org/wiki/2012_Summer_Paralympics"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "England", "link": "https://wikipedia.org/wiki/England"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "2020", "text": "2020 Women's FA Community Shield.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/2020_Women%27s_FA_Community_Shield\" title=\"2020 Women's FA Community Shield\">2020 Women's FA Community Shield</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2020_Women%27s_FA_Community_Shield\" title=\"2020 Women's FA Community Shield\">2020 Women's FA Community Shield</a>.", "links": [{"title": "2020 Women's FA Community Shield", "link": "https://wikipedia.org/wiki/2020_Women%27s_FA_Community_Shield"}]}, {"year": "2022", "text": "Russo-Ukrainian War: Ukraine begins its southern counteroffensive in the Kherson Oblast, eventually culminating in the liberation of the city of Kherson.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukraine begins its <a href=\"https://wikipedia.org/wiki/2022_Ukrainian_southern_counteroffensive\" class=\"mw-redirect\" title=\"2022 Ukrainian southern counteroffensive\">southern counteroffensive</a> in the <a href=\"https://wikipedia.org/wiki/Kherson_Oblast\" title=\"Kherson Oblast\">Kherson Oblast</a>, eventually culminating in the <a href=\"https://wikipedia.org/wiki/Liberation_of_Kherson\" title=\"Liberation of Kherson\">liberation</a> of the city of <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Ukrainian_War\" title=\"Russo-Ukrainian War\">Russo-Ukrainian War</a>: Ukraine begins its <a href=\"https://wikipedia.org/wiki/2022_Ukrainian_southern_counteroffensive\" class=\"mw-redirect\" title=\"2022 Ukrainian southern counteroffensive\">southern counteroffensive</a> in the <a href=\"https://wikipedia.org/wiki/Kherson_Oblast\" title=\"Kherson Oblast\">Kherson Oblast</a>, eventually culminating in the <a href=\"https://wikipedia.org/wiki/Liberation_of_Kherson\" title=\"Liberation of Kherson\">liberation</a> of the city of <a href=\"https://wikipedia.org/wiki/Kherson\" title=\"Kherson\">Kherson</a>.", "links": [{"title": "Russo-Ukrainian War", "link": "https://wikipedia.org/wiki/Russo-Ukrainian_War"}, {"title": "2022 Ukrainian southern counteroffensive", "link": "https://wikipedia.org/wiki/2022_Ukrainian_southern_counteroffensive"}, {"title": "Kherson Oblast", "link": "https://wikipedia.org/wiki/Kherson_Oblast"}, {"title": "Liberation of Kherson", "link": "https://wikipedia.org/wiki/Liberation_of_Kherson"}, {"title": "Kherson", "link": "https://wikipedia.org/wiki/Kherson"}]}], "Births": [{"year": "979", "text": "<PERSON> (or <PERSON><PERSON>), French nobleman (d. 1045)", "html": "979 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Vermandois\" title=\"<PERSON>, Count of Vermandois\"><PERSON></a> (or <PERSON><PERSON>), French nobleman (d. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Vermandois\" title=\"<PERSON>, Count of Vermandois\"><PERSON></a> (or <PERSON><PERSON>), French nobleman (d. 1045)", "links": [{"title": "<PERSON>, Count of Vermandois", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1321", "text": "<PERSON> of Artois, French nobleman  (d. 1387)", "html": "1321 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON> of Artois, Count of Eu\"><PERSON> of Artois</a>, French nobleman (d. 1387)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Eu\" title=\"<PERSON> of Artois, Count of Eu\"><PERSON> of Artois</a>, French nobleman (d. 1387)", "links": [{"title": "<PERSON> of Artois, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_<PERSON><PERSON>"}]}, {"year": "1347", "text": "<PERSON>, 2nd Earl of Pembroke, English nobleman and soldier (d. 1375)", "html": "1347 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English nobleman and soldier (d. 1375)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Pembroke\" title=\"<PERSON>, 2nd Earl of Pembroke\"><PERSON>, 2nd Earl of Pembroke</a>, English nobleman and soldier (d. 1375)", "links": [{"title": "<PERSON>, 2nd Earl of Pembroke", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Pembroke"}]}, {"year": "1434", "text": "<PERSON><PERSON>, Hungarian bishop and poet (d. 1472)", "html": "1434 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian bishop and poet (d. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian bishop and poet (d. 1472)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1514", "text": "<PERSON>, 4th Marquis of Villafranca, Spanish noble and admiral (d. 1577)", "html": "1514 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Marquis_of_Villafranca\" class=\"mw-redirect\" title=\"<PERSON>, 4th Marquis of Villafranca\"><PERSON>, 4th Marquis of Villafranca</a>, Spanish noble and admiral (d. 1577)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Marquis_of_Villafranca\" class=\"mw-redirect\" title=\"<PERSON>, 4th Marquis of Villafranca\"><PERSON>, 4th Marquis of Villafranca</a>, Spanish noble and admiral (d. 1577)", "links": [{"title": "<PERSON>, 4th Marquis of Villafranca", "link": "https://wikipedia.org/wiki/Garc%C3%ADa_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_4th_Marquis_of_Villafranca"}]}, {"year": "1534", "text": "<PERSON>, Dutch Franciscan friar and martyr (d. 1572)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Franciscan friar and martyr (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Franciscan friar and martyr (d. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1597", "text": "<PERSON>, Royalist officer in the English Civil War (d. 1645)", "html": "1597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, Royalist officer in the English Civil War (d. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON></a>, Royalist officer in the English Civil War (d. 1645)", "links": [{"title": "<PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)"}]}, {"year": "1619", "text": "<PERSON><PERSON><PERSON>, French economist and politician, Controller-General of Finances (d. 1683)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (d. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French economist and politician, <a href=\"https://wikipedia.org/wiki/Controller-General_of_Finances\" title=\"Controller-General of Finances\">Controller-General of Finances</a> (d. 1683)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Controller-General of Finances", "link": "https://wikipedia.org/wiki/Controller-General_of_Finances"}]}, {"year": "1628", "text": "<PERSON>, 1st Earl of Bath, English soldier and politician, Lord Lieutenant of Ireland (d. 1701)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl of Bath\"><PERSON>, 1st Earl of Bath</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_Bath\" title=\"<PERSON>, 1st Earl <PERSON> Bath\"><PERSON>, 1st Earl of Bath</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1701)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> Bath", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Bath"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1632", "text": "<PERSON>, English physician and philosopher (d. 1704)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philosopher (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and philosopher (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Italian poet and author (d. 1803)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and author (d. 1803)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1725", "text": "<PERSON>, English politician, Chancellor of the Exchequer (d. 1767)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1767)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1767)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1728", "text": "<PERSON> of Saxony, electress of Bavaria (d. 1797)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, electress of Bavaria (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, electress of Bavaria (d. 1797)", "links": [{"title": "<PERSON> of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Saxony"}]}, {"year": "1756", "text": "<PERSON>, Polish mathematician and astronomer (d. 1830)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/Jan_%C5%9A<PERSON>de<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician and astronomer (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_%C5%9A<PERSON>de<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish mathematician and astronomer (d. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_%C5%9<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1756", "text": "<PERSON> <PERSON>, Austrian general and politician (d. 1845)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian general and politician (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>\">Count <PERSON></a>, Austrian general and politician (d. 1845)", "links": [{"title": "Count <PERSON>", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON>, Scottish Quaker (d. 1852)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, Scottish <a href=\"https://wikipedia.org/wiki/Quaker\" class=\"mw-redirect\" title=\"Quaker\">Quaker</a> (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(industrialist)\" title=\"<PERSON> (industrialist)\"><PERSON></a>, Scottish <a href=\"https://wikipedia.org/wiki/Quaker\" class=\"mw-redirect\" title=\"Quaker\">Quaker</a> (d. 1852)", "links": [{"title": "<PERSON> (industrialist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(industrialist)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Quaker"}]}, {"year": "1777", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian religious leader, founded Sinology (d. 1853)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Hyacinth_(Bichurin)\" title=\"Hyacinth (Bichurin)\"><PERSON>yacin<PERSON></a>, Russian religious leader, founded <a href=\"https://wikipedia.org/wiki/Sinology\" title=\"Sinology\">Sinology</a> (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyacinth_(Bichurin)\" title=\"Hyacinth (Bichurin)\"><PERSON>yacin<PERSON></a>, Russian religious leader, founded <a href=\"https://wikipedia.org/wiki/Sinology\" title=\"Sinology\">Sinology</a> (d. 1853)", "links": [{"title": "Hyacinth (Bichurin)", "link": "https://wikipedia.org/wiki/Hyacinth_(Bichurin)"}, {"title": "Sinology", "link": "https://wikipedia.org/wiki/Sinology"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French painter and illustrator (d. 1867)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French painter and illustrator (d. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, American minister and author (d. 1875)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and author (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, English priest, theologian, and author (d. 1872)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English priest, theologian, and author (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English priest, theologian, and author (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, American physician and author (d. 1894)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>.</a>, American physician and author (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American physician and author (d. 1894)", "links": [{"title": "<PERSON> Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1810", "text": "<PERSON>, Argentine theorist and diplomat (d. 1884)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Argentine theorist and diplomat (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Juan <PERSON>\"><PERSON></a>, Argentine theorist and diplomat (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, American activist, founded the ASPCA (d. 1888)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals\" title=\"American Society for the Prevention of Cruelty to Animals\">ASPCA</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist, founded the <a href=\"https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals\" title=\"American Society for the Prevention of Cruelty to Animals\">ASPCA</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Society for the Prevention of Cruelty to Animals", "link": "https://wikipedia.org/wiki/American_Society_for_the_Prevention_of_Cruelty_to_Animals"}]}, {"year": "1842", "text": "<PERSON>, English cricketer, rugby player, and umpire (d. 1907)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, rugby player, and umpire (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer, rugby player, and umpire (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American lawyer and politician, 29th Governor of New York (d. 1910)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1844", "text": "<PERSON>, English anthologist and poet (d. 1929)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthologist and poet (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anthologist and poet (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1854", "text": "<PERSON>, American Seventh-day Adventist Church minister (d. 1937)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a> minister (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a> minister (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Seventh-day Adventist Church", "link": "https://wikipedia.org/wiki/Seventh-day_Adventist_Church"}]}, {"year": "1857", "text": "<PERSON><PERSON>, English cricketer (d. 1937)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer (d. 1937)", "links": [{"title": "<PERSON><PERSON> Schultz", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American singer (d. 1936)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, Scottish-Australian politician and diplomat, 5th Prime Minister of Australia (d. 1928)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Australian politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1862", "text": "<PERSON>, Belgian poet and playwright, Nobel Prize laureate (d. 1949)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1871", "text": "<PERSON>, French engineer and politician, 15th President of France (d. 1950)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French engineer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1875", "text": "<PERSON>, Italian flute player and educator (d. 1962)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian flute player and educator (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian flute player and educator (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American engineer and businessman, founded Delco Electronics (d. 1958)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Delco_Electronics\" title=\"Delco Electronics\">Delco Electronics</a> (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Delco_Electronics\" title=\"Delco Electronics\">Delco Electronics</a> (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Delco Electronics", "link": "https://wikipedia.org/wiki/Delco_Electronics"}]}, {"year": "1876", "text": "<PERSON>, South Korean politician, 6th President of The Provisional Government of the Republic of Korea (d. 1949)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 6th <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea\" title=\"Provisional Government of the Republic of Korea\">President of The Provisional Government of the Republic of Korea</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 6th <a href=\"https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea\" title=\"Provisional Government of the Republic of Korea\">President of The Provisional Government of the Republic of Korea</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional Government of the Republic of Korea", "link": "https://wikipedia.org/wiki/Provisional_Government_of_the_Republic_of_Korea"}]}, {"year": "1879", "text": "<PERSON>, Korean independence activist, reformer, and poet (d. 1944)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean independence activist, reformer, and poet (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean independence activist, reformer, and poet (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Indian physicians and politician, 1st Chief Minister of Gujarat (d. 1978)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicians and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian physicians and politician, 1st <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Gujarat\" class=\"mw-redirect\" title=\"Chief Minister of Gujarat\">Chief Minister of Gujarat</a> (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Minister of Gujarat", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Gujarat"}]}, {"year": "1888", "text": "<PERSON><PERSON>, Estonian-English politician (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-English politician (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-English politician (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Norwegian Communist and anti-Nazi Resistance leader (d. 1975)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian Communist and anti-Nazi Resistance leader (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian Communist and anti-Nazi Resistance leader (d. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>n"}]}, {"year": "1891", "text": "<PERSON>, American journalist and author (d. 1955)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1955)", "links": [{"title": "Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American director and producer (d. 1959)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Preston_Sturges\" title=\"Preston Sturges\"><PERSON></a>, American director and producer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Preston_Sturges\" title=\"Preston Sturges\"><PERSON></a>, American director and producer (d. 1959)", "links": [{"title": "Preston Sturges", "link": "https://wikipedia.org/wiki/Preston_Sturges"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player and referee (d. 1986)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Aur%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and referee (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aur%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and referee (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aur%C3%A8le_<PERSON><PERSON>t"}]}, {"year": "1904", "text": "<PERSON>, German physician and academic, Nobel Prize laureate (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Indian field hockey player (d. 1979)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian field hockey player (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>d"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Finnish activist (d. 1941)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish activist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish activist (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, American surgeon and academic (d. 1985)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American surgeon and academic (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American surgeon and academic (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, British orthopedic surgeon (d. 1982)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British orthopedic surgeon (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British orthopedic surgeon (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, South Korean runner (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-chung\" title=\"<PERSON><PERSON> Kee-chung\"><PERSON><PERSON>ung</a>, South Korean runner (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-chung\" title=\"<PERSON><PERSON> Kee-chung\"><PERSON><PERSON>ung</a>, South Korean runner (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-chung"}]}, {"year": "1912", "text": "<PERSON>, American actor (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1912", "text": "<PERSON>, Austrian-English cinematographer and photographer (d. 2016)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English cinematographer and photographer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-English cinematographer and photographer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, New Zealand cricketer (d. 1999)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American baseball pitcher (d. 1987)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Swedish actress (d. 1982)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American nutritionist and author (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nutritionist and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1916", "text": "<PERSON>, American playwright and screenwriter (d. 2008)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American inventor and engineer (d. 1982)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>kin\"><PERSON></a>, American inventor and engineer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boykin\"><PERSON></a>, American inventor and engineer (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American saxophonist and composer (d. 1955)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American baseball player (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American businesswoman, interior designer, and philanthropist (d. 2024)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businesswoman, interior designer, and philanthropist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Apf<PERSON>\"><PERSON></a>, American businesswoman, interior designer, and philanthropist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iris_Apfel"}]}, {"year": "1922", "text": "<PERSON>, American actor (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1922", "text": "<PERSON>, American actor, fashion designer, and critic (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, fashion designer, and critic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, fashion designer, and critic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American author and educator (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor, director, and producer (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American singer and pianist (d. 1963)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Dinah Washington\"><PERSON><PERSON></a>, American singer and pianist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dinah_<PERSON>\" title=\"Dinah Washington\"><PERSON><PERSON></a>, American singer and pianist (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dinah_Washington"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Greek historian and academic", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek historian and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Haitian writer", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Depestre"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American author and speaker (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and speaker (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and speaker (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actress (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Swiss author and translator (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and translator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and translator (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English-American poet and academic (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian businessman (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Filipino basketball player and coach (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Greek singer and guitarist (d. 2001)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer and guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer and guitarist (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Canadian journalist and politician (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Li<PERSON>_Payette\" title=\"Lise Payette\"><PERSON><PERSON></a>, Canadian journalist and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li<PERSON>_<PERSON>ette\" title=\"Lise Payette\"><PERSON><PERSON></a>, Canadian journalist and politician (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lise_Payette"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Romanian-Canadian sculptor, painter, and illustrator (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/So<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian-Canadian sculptor, painter, and illustrator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"So<PERSON>\"><PERSON><PERSON></a>, Romanian-Canadian sculptor, painter, and illustrator (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_Etrog"}]}, {"year": "1933", "text": "<PERSON>, Swiss politician", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Greek actor and director (d. 2004)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor and director (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor and director (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Dutch linguist and author (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American director, producer, and screenwriter (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian psychologist and scholar (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Garai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> G<PERSON>i\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian psychologist and scholar (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Garai\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> G<PERSON>i\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Hungarian psychologist and scholar (d. 2019)", "links": [{"title": "<PERSON>ás<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A1szl%C3%B3_Garai"}]}, {"year": "1936", "text": "<PERSON>, American captain and politician (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American commander, lawyer, and politician, 49th Governor of New Jersey (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1938", "text": "<PERSON>, American actor and producer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English journalist and author", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German footballer and manager", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Christian_M%C3%BC<PERSON>_(footballer,_born_1938)\" title=\"<PERSON> (footballer, born 1938)\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_M%C3%BC<PERSON>_(footballer,_born_1938)\" title=\"<PERSON> (footballer, born 1938)\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1938)", "link": "https://wikipedia.org/wiki/Christian_M%C3%BCller_(footballer,_born_1938)"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician, 70th United States Secretary of the Treasury", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 70th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Hungarian discus thrower and shot putter (d. 2022)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Jol%C3%A1n_Kleiber-Ko<PERSON>ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian discus thrower and shot putter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jol%C3%A1n_Kle<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian discus thrower and shot putter (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jol%C3%A1n_<PERSON><PERSON><PERSON>-Ko<PERSON>ek"}]}, {"year": "1939", "text": "<PERSON>, American director, producer, and screenwriter (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American politician and activist, 15th White House Press Secretary (d. 2014)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist, 15th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and activist, 15th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1940", "text": "<PERSON>, American race car driver (d. 1984)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English journalist and television host (d. 2018)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television host (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and television host (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American cinematographer (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, German actor (d. 2014)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer and guitarist (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Kenyan photographer and journalist (d. 1996)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan photographer and journalist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan photographer and journalist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American pianist and composer (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian astrophysicist and academic, Nobel Prize laureate", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astrophysicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian astrophysicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, American sprinter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Wyo<PERSON>_<PERSON>\" title=\"Wyo<PERSON>\"><PERSON><PERSON><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wyo<PERSON>_<PERSON>\" title=\"Wyo<PERSON>\"><PERSON><PERSON><PERSON></a>, American sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wyomia_Tyus"}]}, {"year": "1946", "text": "<PERSON>, American long jumper", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American economist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Fran<PERSON>_D._Blau\" title=\"Francine D<PERSON>\">Fran<PERSON></a>, American economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran<PERSON>_D._Blau\" title=\"Francine D<PERSON>\">Fran<PERSON></a>, American economist and academic", "links": [{"title": "Francine <PERSON>", "link": "https://wikipedia.org/wiki/Fran<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Cypriot businessman and politician, 6th President of Cyprus (d. 2019)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot businessman and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Cyprus\" title=\"President of Cyprus\">President of Cyprus</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demetris_Christofias"}, {"title": "President of Cyprus", "link": "https://wikipedia.org/wiki/President_of_Cyprus"}]}, {"year": "1946", "text": "<PERSON>, American basketball player (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Warren_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Italian lawyer and politician, 17th Mayor of Venice", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Mayor_of_Venice\" title=\"Mayor of Venice\">Mayor of Venice</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician, 17th <a href=\"https://wikipedia.org/wiki/Mayor_of_Venice\" title=\"Mayor of Venice\">Mayor of Venice</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Venice", "link": "https://wikipedia.org/wiki/Mayor_of_Venice"}]}, {"year": "1947", "text": "<PERSON>, American ethologist, academic, and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Temple_Grandin\" title=\"Temple Grandin\">Temple Grandin</a>, American ethologist, academic, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Temple_Grandin\" title=\"Temple Grandin\">Temple Grandin</a>, American ethologist, academic, and author", "links": [{"title": "Temple Grandin", "link": "https://wikipedia.org/wiki/Temple_Grandin"}]}, {"year": "1947", "text": "<PERSON>, English race car driver and sportscaster (d. 1993)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and sportscaster (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Hunt\"><PERSON></a>, English race car driver and sportscaster (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American chemical engineer, entrepreneur, and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemical engineer, entrepreneur, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemical engineer, entrepreneur, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American wrestler and actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American basketball player", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Dar<PERSON>_Hillman\" title=\"Darnell Hillman\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dar<PERSON>_Hillman\" title=\"Darnell Hillman\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>man"}]}, {"year": "1950", "text": "<PERSON>, American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American director and screenwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American soldier and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Japanese singer (d. 2023)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American author and poet", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American Hall of Fame country music songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame\" class=\"mw-redirect\" title=\"Country Music Hall of Fame\">Hall of Fame</a> country music songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Country_Music_Hall_of_Fame\" class=\"mw-redirect\" title=\"Country Music Hall of Fame\">Hall of Fame</a> country music songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Country Music Hall of Fame", "link": "https://wikipedia.org/wiki/Country_Music_Hall_of_Fame"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English rugby player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)"}]}, {"year": "1953", "text": "<PERSON>, Nicaraguan-American anthropologist and academic", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan-American anthropologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan-American anthropologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, American journalist, author, and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and pianist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Diamanda_Gal%C3%A1s\" title=\"Diamanda Galás\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Diamanda_Gal%C3%A1s\" title=\"Diamanda Galás\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "Diamanda Galás", "link": "https://wikipedia.org/wiki/Diamanda_Gal%C3%A1s"}]}, {"year": "1955", "text": "<PERSON>, American lawyer and politician, 25th White House Chief of Staff", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 25th <a href=\"https://wikipedia.org/wiki/White_House_Chief_of_Staff\" title=\"White House Chief of Staff\">White House Chief of Staff</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Chief_of_Staff"}]}, {"year": "1956", "text": "<PERSON>, American dancer and choreographer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American dancer and choreographer", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)"}]}, {"year": "1956", "text": "<PERSON>, American football player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Charalambos_Xanthopoulos\" class=\"mw-redirect\" title=\"<PERSON>ral<PERSON><PERSON> Xanthopoulos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charalambos_Xanthopoulos\" class=\"mw-redirect\" title=\"<PERSON>ralambos Xanthopoulos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charalambos_<PERSON>hopoulos"}]}, {"year": "1956", "text": "<PERSON>, American novelist and short story writer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1957", "text": "<PERSON>, American jockey and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish singer-songwriter, film music composer (d. 2001)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish singer-songwriter, film music composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish singer-songwriter, film music composer (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English comedian, actor, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, producer, dancer, and actor (d. 2009)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, dancer, and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, dancer, and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Argentine footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_D%C3%ADaz\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_D%C3%ADaz"}]}, {"year": "1959", "text": "<PERSON>, Canadian football player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian colonel, pilot, and astronaut", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Scottish singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON>_Reader\" title=\"Eddi Reader\">Eddi Reader</a>, Scottish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed<PERSON>_Reader\" title=\"Eddi Reader\">Ed<PERSON> Reader</a>, Scottish singer-songwriter, guitarist, and producer", "links": [{"title": "Eddi Reader", "link": "https://wikipedia.org/wiki/Eddi_Reader"}]}, {"year": "1959", "text": "<PERSON>, American businessman and activist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English-American physicist and mathematician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American physicist and mathematician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Indian film actor, Producer and Businessman", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian film actor, Producer and Businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)\" title=\"<PERSON><PERSON><PERSON> (actor)\"><PERSON><PERSON><PERSON></a>, Indian film actor, Producer and Businessman", "links": [{"title": "<PERSON><PERSON><PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON>, American chef and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Todd_English\" title=\"Todd English\"><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Todd_English\" title=\"Todd English\"><PERSON></a>, American chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Todd_English"}]}, {"year": "1960", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, German field hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1962", "text": "<PERSON>, American football player and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Japanese game designer and composer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game designer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese game designer and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian voice actor, writer, producer and author", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actor, writer, producer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actor, writer, producer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English historian and academic", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American serial killer and poisoner", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and poisoner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and poisoner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Scottish singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, American dance-pop and urban contemporary singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Perri_%22Pebbles%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>'><PERSON><PERSON> \"Pebbles\" <PERSON></a>, American dance-pop and urban contemporary singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Perri_%22Pebbles%22_<PERSON>\" class=\"mw-redirect\" title='<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>'><PERSON><PERSON> \"Peb<PERSON>\" <PERSON></a>, American dance-pop and urban contemporary singer-songwriter", "links": [{"title": "<PERSON><PERSON> \"P<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Perri_%22Pebbles%22_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> T<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> T<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Perdue\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Will Perdue\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian politician and engineer, Norwegian Minister of Fisheries and Seafood", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician and engineer, <a href=\"https://wikipedia.org/wiki/Minister_of_Fisheries_(Norway)\" class=\"mw-redirect\" title=\"Minister of Fisheries (Norway)\">Norwegian Minister of Fisheries and Seafood</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian politician and engineer, <a href=\"https://wikipedia.org/wiki/Minister_of_Fisheries_(Norway)\" class=\"mw-redirect\" title=\"Minister of Fisheries (Norway)\">Norwegian Minister of Fisheries and Seafood</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Fisheries (Norway)", "link": "https://wikipedia.org/wiki/Minister_of_Fisheries_(Norway)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/J%C3%B6rn_Gro%C3%9Fkopf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B6rn_Gro%C3%9Fkopf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B6rn_Gro%C3%9Fkopf"}]}, {"year": "1967", "text": "<PERSON>, American lawyer and jurist, Associate Justice of the Supreme Court of the United States", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice of the Supreme Court of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_<PERSON>combe"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, German-American singer-songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Meshe<PERSON>_Ndegeocello\" title=\"Meshell Ndegeocello\"><PERSON><PERSON><PERSON></a>, German-American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meshe<PERSON>_Ndegeocello\" title=\"Meshell Ndegeocello\">Me<PERSON><PERSON> Ndegeocello</a>, German-American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Meshell_Ndegeocello"}]}, {"year": "1969", "text": "<PERSON>, Northern Irish snooker player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1969", "text": "<PERSON>, American screenwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Mexican singer, songwriter, actress, and television host", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON></a>, Mexican singer, songwriter, actress, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON><PERSON></a>, Mexican singer, songwriter, actress, and television host", "links": [{"title": "<PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)"}]}, {"year": "1971", "text": "<PERSON>, Venezuelan baseball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English bass player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, South Korean actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-j<PERSON>\" title=\"<PERSON><PERSON>j<PERSON>\"><PERSON><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-j<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-joon"}]}, {"year": "1973", "text": "<PERSON>, English singer and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, French motorcycle racer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Japanese keyboard player and composer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese keyboard player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese keyboard player and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Irish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(band_manager)\" title=\"<PERSON> (band manager)\"><PERSON></a>, English manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(band_manager)\" title=\"<PERSON> (band manager)\"><PERSON></a>, English manager", "links": [{"title": "<PERSON> (band manager)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(band_manager)"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Argentine-American soccer player and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-American soccer player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-American soccer player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Danish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek DJ and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Cayetano_(Gior<PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cay<PERSON><PERSON>_(Gior<PERSON>_<PERSON>)\" title=\"<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)\"><PERSON><PERSON><PERSON><PERSON></a>, Greek DJ and producer", "links": [{"title": "Cayetano (Giorgos <PERSON>)", "link": "https://wikipedia.org/wiki/Cayetano_(G<PERSON><PERSON>_<PERSON>)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American soccer player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27B<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien_(soccer)"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian comedian and radio host", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, German-Turkish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Volkan_Arslan\" title=\"Volkan Arslan\"><PERSON><PERSON></a>, German-Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Volkan_Arslan\" title=\"Volkan Arslan\"><PERSON><PERSON></a>, German-Turkish footballer", "links": [{"title": "Volkan <PERSON>lan", "link": "https://wikipedia.org/wiki/Volkan_A<PERSON>lan"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Dev<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Dev<PERSON>er\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>older"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Estonian decathlete", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1981", "text": "<PERSON>, Czech ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Genevi%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Genevi%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Genevi%C3%A8<PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand-Australian actor and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, New Zealand-Australian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, New Zealand-Australian actor and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>, Kenyan journalist and radio host (d. 2013)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>uh<PERSON>_<PERSON>-Sood\" title=\"<PERSON>uh<PERSON>-Sood\"><PERSON><PERSON><PERSON>-<PERSON></a>, Kenyan journalist and radio host (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uh<PERSON>_<PERSON>-Sood\" title=\"<PERSON>uh<PERSON>-Sood\"><PERSON><PERSON><PERSON>-<PERSON></a>, Kenyan journalist and radio host (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uh<PERSON>_Ada<PERSON>-Sood"}]}, {"year": "1982", "text": "<PERSON>, Argentine-Italian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-Italian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine-Italian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, French basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Yakhou<PERSON> Diawara\"><PERSON><PERSON><PERSON><PERSON></a>, French basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Yakh<PERSON><PERSON> Diawara\"><PERSON><PERSON><PERSON><PERSON></a>, French basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ya<PERSON><PERSON><PERSON>_Di<PERSON>ra"}]}, {"year": "1982", "text": "<PERSON>, Nigerian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON><PERSON> (ice hockey)\"><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON><PERSON> (ice hockey)\"><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(ice_hockey)"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese illustrator", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Michele\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Irish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1990", "text": "<PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/N%C3%A9stor_Araujo\" title=\"<PERSON>és<PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N%C3%A9stor_Araujo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/N%C3%A9stor_Araujo"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Brazilian singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mallu_<PERSON>h%C3%A3es\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%A3es\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mallu_Magalh%C3%A3es"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American YouTuber and actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English singer-songwriter from One Direction (d. 2024)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter from <a href=\"https://wikipedia.org/wiki/One_Direction\" title=\"One Direction\">One Direction</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter from <a href=\"https://wikipedia.org/wiki/One_Direction\" title=\"One Direction\">One Direction</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "One Direction", "link": "https://wikipedia.org/wiki/One_Direction"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Belgian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ysaline_Bonaventure\" title=\"Ysaline Bonaventure\"><PERSON><PERSON><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ysaline_Bonaventure\" title=\"Ysaline Bonaventure\"><PERSON><PERSON><PERSON></a>, Belgian tennis player", "links": [{"title": "Y<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ysaline_Bonaventure"}]}, {"year": "1996", "text": "<PERSON><PERSON>, British sprinter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}], "Deaths": [{"year": "886", "text": "<PERSON>, Byzantine emperor (b. 811)", "html": "886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a>, Byzantine emperor (b. 811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> I\"><PERSON></a>, Byzantine emperor (b. 811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "892", "text": "<PERSON><PERSON> of Thessaloniki, Byzantine nun and saint (b. 812)", "html": "892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Thessaloniki\" title=\"<PERSON><PERSON> of Thessaloniki\"><PERSON><PERSON> of Thessaloniki</a>, Byzantine nun and saint (b. 812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Thessaloniki\" title=\"<PERSON><PERSON> of Thessaloniki\"><PERSON><PERSON> of Thessaloniki</a>, Byzantine nun and saint (b. 812)", "links": [{"title": "<PERSON>a of Thessaloniki", "link": "https://wikipedia.org/wiki/Theodora_of_Thessaloniki"}]}, {"year": "939", "text": "<PERSON>, Chinese emperor of Min", "html": "939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor of Min", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor of Min", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "939", "text": "<PERSON>, Chinese empress", "html": "939 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, Chinese empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, Chinese empress", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "956", "text": "<PERSON> the Elder, Chinese empress", "html": "956 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_the_Elder\" title=\"Empress <PERSON> the Elder\"><PERSON> the Elder</a>, Chinese empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_the_Elder\" title=\"Empress <PERSON> the Elder\"><PERSON> the Elder</a>, Chinese empress", "links": [{"title": "Empress <PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "979", "text": "<PERSON>, Hamdanid emir", "html": "979 - <a href=\"https://wikipedia.org/wiki/Abu_Taghlib\" title=\"Abu Taghlib\"><PERSON></a>, <PERSON><PERSON><PERSON> emir", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abu_Taghlib\" title=\"Abu Taghlib\"><PERSON></a>, <PERSON><PERSON><PERSON> emir", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ib"}]}, {"year": "1021", "text": "<PERSON><PERSON>, Japanese nobleman (b. 948)", "html": "1021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yorimitsu\" title=\"Minamoto no Yorimitsu\"><PERSON><PERSON> no Yorimitsu</a>, Japanese nobleman (b. 948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yorimitsu\" title=\"Minamoto no Yorimitsu\"><PERSON><PERSON> no Yorimitsu</a>, Japanese nobleman (b. 948)", "links": [{"title": "<PERSON><PERSON> no Yorimitsu", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1046", "text": "<PERSON> of Csanád Venetian monk and Hungarian bishop (b.980)", "html": "1046 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Csan%C3%A1d\" title=\"<PERSON> of Csanád\"><PERSON> of Csanád</a> Venetian monk and Hungarian bishop (b.980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_C<PERSON>%C3%A1d\" title=\"<PERSON> of Csanád\"><PERSON> of Csanád</a> Venetian monk and Hungarian bishop (b.980)", "links": [{"title": "<PERSON> of Csanád", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>%C3%A1d"}]}, {"year": "1093", "text": "<PERSON>, duke of Burgundy (b. 1057)", "html": "1093 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, duke of Burgundy (b. 1057)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" title=\"<PERSON>, Duke of Burgundy\"><PERSON></a>, duke of Burgundy (b. 1057)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1123", "text": "<PERSON><PERSON><PERSON>, king of Norway (b. 1088)", "html": "1123 - <a href=\"https://wikipedia.org/wiki/Eystein_I_of_Norway\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> I of Norway\"><PERSON><PERSON><PERSON></a>, king of Norway (b. 1088)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eystein_I_of_Norway\" class=\"mw-redirect\" title=\"<PERSON>ys<PERSON> I of Norway\"><PERSON><PERSON><PERSON></a>, king of Norway (b. 1088)", "links": [{"title": "<PERSON>ystein I of Norway", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Norway"}]}, {"year": "1135", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 1092)", "html": "1135 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>hi<PERSON>\" title=\"Al<PERSON>Mustarshid\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 1092)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-Mustarshid\" title=\"Al<PERSON>Mustarshid\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 1092)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>arshid"}]}, {"year": "1159", "text": "<PERSON><PERSON> of Sulzbach, Byzantine empress", "html": "1159 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Sulzbach\" title=\"<PERSON><PERSON> of Sulzbach\"><PERSON><PERSON> of Sulzbach</a>, Byzantine empress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Sulzbach\" title=\"<PERSON><PERSON> of Sulzbach\"><PERSON><PERSON> of Sulzbach</a>, Byzantine empress", "links": [{"title": "<PERSON><PERSON> of Sulzbach", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Sulzbach"}]}, {"year": "1298", "text": "<PERSON> of England, Countess of Bar, English princess (b. 1269)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Countess_of_Bar\" title=\"<PERSON> of England, Countess of Bar\"><PERSON> of England, Countess of Bar</a>, English princess (b. 1269)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_England,_Countess_of_Bar\" title=\"<PERSON> of England, Countess of Bar\"><PERSON> of England, Countess of Bar</a>, English princess (b. 1269)", "links": [{"title": "<PERSON> of England, Countess of Bar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_England,_Countess_<PERSON>_Bar"}]}, {"year": "1315", "text": "<PERSON>, Italian nobleman (b. 1291)", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian nobleman (b. 1291)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian nobleman (b. 1291)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1315", "text": "<PERSON> of Taranto, Italian nobleman (b. 1296)", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Taranto\" title=\"<PERSON> of Taranto\"><PERSON> of Taranto</a>, Italian nobleman (b. 1296)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Taranto\" title=\"<PERSON> of Taranto\"><PERSON> of Taranto</a>, Italian nobleman (b. 1296)", "links": [{"title": "<PERSON> of Taranto", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tara<PERSON>"}]}, {"year": "1395", "text": "<PERSON>, duke of Austria (b. 1349)", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a>, duke of Austria (b. 1349)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a>, duke of Austria (b. 1349)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria"}]}, {"year": "1442", "text": "<PERSON>, duke of Brittany (b. 1389)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1389)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON></a>, duke of Brittany (b. 1389)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1499", "text": "<PERSON><PERSON><PERSON>, Florentine painter (b. 1427)", "html": "1499 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, F<PERSON>ntine painter (b. 1427)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, F<PERSON>ntine painter (b. 1427)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1523", "text": "<PERSON>, Lutheran reformer (b. 1488)", "html": "1523 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran reformer (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lutheran reformer (b. 1488)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1526", "text": "<PERSON>, king of Hungary and Croatia (b. 1506)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> II</a>, king of Hungary and Croatia (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> II</a>, king of Hungary and Croatia (b. 1506)", "links": [{"title": "<PERSON> II of Hungary", "link": "https://wikipedia.org/wiki/Louis_II_of_Hungary"}]}, {"year": "1526", "text": "<PERSON><PERSON><PERSON> archbishop and soldier (b. 1475)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Hungarian archbishop and soldier (b. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> Hungarian archbishop and soldier (b. 1475)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A1<PERSON>_<PERSON>ori"}]}, {"year": "1533", "text": "<PERSON><PERSON><PERSON><PERSON>, Inca emperor (b. 1497)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahu<PERSON><PERSON></a>, Inca emperor (b. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atahualpa\" title=\"Atahualpa\">Atahu<PERSON><PERSON></a>, Inca emperor (b. 1497)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Atahualpa"}]}, {"year": "1542", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Portuguese commander (b. 1516)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/Crist%C3%B3v%C3%A3o_da_Gama\" title=\"Cristóv<PERSON> da Gama\"><PERSON><PERSON><PERSON><PERSON><PERSON> da Gama</a>, Portuguese commander (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crist%C3%B3v%C3%A3o_da_Gama\" title=\"Cristóv<PERSON> da Gama\"><PERSON><PERSON><PERSON><PERSON><PERSON> da Gama</a>, Portuguese commander (b. 1516)", "links": [{"title": "Cristóvão da Gama", "link": "https://wikipedia.org/wiki/Crist%C3%B3v%C3%A3o_da_Gama"}]}, {"year": "1604", "text": "<PERSON><PERSON>, Mughal empress (b. 1527)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Banu_Begum\" title=\"Hamida Banu Begum\"><PERSON><PERSON> Ban<PERSON></a>, Mughal empress (b. 1527)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Banu_Begum\" title=\"Hamida Banu Begum\"><PERSON><PERSON></a>, Mughal empress (b. 1527)", "links": [{"title": "Hamida Banu Begum", "link": "https://wikipedia.org/wiki/Hamida_Banu_Begum"}]}, {"year": "1657", "text": "<PERSON>, English activist (b. 1614)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English activist (b. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1712", "text": "<PERSON>, English genealogist, engraver, and statistician (b. 1648)", "html": "1712 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, English genealogist, engraver, and statistician (b. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, English genealogist, engraver, and statistician (b. 1648)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Hungarian pastor and polymath (b. 1684)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pastor and polymath (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian pastor and polymath (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English author and educator (b. 1672)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (b. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (b. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1780", "text": "<PERSON><PERSON><PERSON>, French architect, co-designed <PERSON> (b. 1713)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect, co-designed <a href=\"https://wikipedia.org/wiki/Panth%C3%A9on,_Paris\" class=\"mw-redirect\" title=\"Panthéon, Paris\">The Panthéon</a> (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French architect, co-designed <a href=\"https://wikipedia.org/wiki/Panth%C3%A<PERSON>on,_Paris\" class=\"mw-redirect\" title=\"Panthéon, Paris\">The Panthéon</a> (b. 1713)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Panthéon, Paris", "link": "https://wikipedia.org/wiki/Panth%C3%A9on,_Paris"}]}, {"year": "1799", "text": "<PERSON>, pope of the Catholic Church (b. 1717)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VI\" title=\"Pope Pius VI\"><PERSON></a>, pope of the Catholic Church (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"Pope Pius VI\"><PERSON></a>, pope of the Catholic Church (b. 1717)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Irish missionary and educator, founded the Christian Brothers and Presentation Brothers (b. 1762)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish missionary and educator, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Christian_Brothers\" title=\"Congregation of Christian Brothers\">Christian Brothers</a> and <a href=\"https://wikipedia.org/wiki/Presentation_Brothers\" title=\"Presentation Brothers\">Presentation Brothers</a> (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish missionary and educator, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_Christian_Brothers\" title=\"Congregation of Christian Brothers\">Christian Brothers</a> and <a href=\"https://wikipedia.org/wiki/Presentation_Brothers\" title=\"Presentation Brothers\">Presentation Brothers</a> (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Congregation of Christian Brothers", "link": "https://wikipedia.org/wiki/Congregation_of_Christian_Brothers"}, {"title": "Presentation Brothers", "link": "https://wikipedia.org/wiki/Presentation_Brothers"}]}, {"year": "1856", "text": "<PERSON>, English author and activist (b. 1778)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and activist (b. 1778)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Japanese shōgun (b. 1846)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Iemochi\" title=\"Tokugawa Iemochi\"><PERSON></a>, Japanese shōgun (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Iemochi\" title=\"Tokugawa Iemochi\">Tokugawa <PERSON></a>, Japanese shō<PERSON> (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>i"}]}, {"year": "1877", "text": "<PERSON>, American religious leader, 2nd President of The Church of Jesus Christ of Latter-day Saints (b. 1801)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 2nd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 2nd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1801)", "links": [{"title": "<PERSON> Young", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1889", "text": "<PERSON>, Bulgarian colonel (b. 1815)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian colonel (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian colonel (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, French businessman, invented the bicycle (b. 1843)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, invented the <a href=\"https://wikipedia.org/wiki/Bicycle\" title=\"Bicycle\">bicycle</a> (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman, invented the <a href=\"https://wikipedia.org/wiki/Bicycle\" title=\"Bicycle\">bicycle</a> (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bicycle", "link": "https://wikipedia.org/wiki/Bicycle"}]}, {"year": "1892", "text": "<PERSON>, Scottish historian and author (b. 1809)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish historian and author (b. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>e"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Ottoman sultan (b. 1840)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Murad_V\" title=\"Murad V\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murad_V\" title=\"Murad V\"><PERSON><PERSON> <PERSON></a>, Ottoman sultan (b. 1840)", "links": [{"title": "<PERSON><PERSON> V", "link": "https://wikipedia.org/wiki/Murad_V"}]}, {"year": "1911", "text": "<PERSON> <PERSON><PERSON><PERSON>, 6th Nizam of Hyderabad (b. 1866)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"<PERSON>zam\"><PERSON><PERSON></a> of Hyderabad (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>, 6th <a href=\"https://wikipedia.org/wiki/Nizam\" class=\"mw-redirect\" title=\"<PERSON>zam\"><PERSON><PERSON></a> of Hyderabad (b. 1866)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nizam"}]}, {"year": "1917", "text": "<PERSON>, American businessman (b. 1833)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Huntington_Hartford\" title=\"George Huntington Hartford\"><PERSON></a>, American businessman (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/George_Huntington_Hartford\" title=\"George Huntington Hartford\"><PERSON></a>, American businessman (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Huntington_Hartford"}]}, {"year": "1930", "text": "<PERSON>, English priest and author (b. 1844)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American businessman, co-founded Abercrombie & Fitch (b. 1867)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Abe<PERSON><PERSON><PERSON>_%26_Fitch\" title=\"Abe<PERSON><PERSON><PERSON> &amp; Fitch\">Abercrombie &amp; Fitch</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Aberc<PERSON><PERSON>_%26_Fitch\" title=\"<PERSON><PERSON><PERSON><PERSON> &amp; Fitch\">Aberc<PERSON>bie &amp; Fitch</a> (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Abercrombie & Fitch", "link": "https://wikipedia.org/wiki/Abercrombie_%26_Fitch"}]}, {"year": "1932", "text": "<PERSON>, Canadian poet and author (b. 1899)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Greek pianist and composer (b. 1885)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Attik\" title=\"Attik\"><PERSON><PERSON><PERSON></a>, Greek pianist and composer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attik\" title=\"Attik\"><PERSON><PERSON><PERSON></a>, Greek pianist and composer (b. 1885)", "links": [{"title": "Attik", "link": "https://wikipedia.org/wiki/Attik"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, American businessman (b. 1891)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American businessman (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American painter and academic (b. 1897)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English economist and civil servant (b. 1871)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and civil servant (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Chapman_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, English economist and civil servant (b. 1871)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1952", "text": "<PERSON>, Austrian lawyer (b. 1894)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Anton_Pi%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton_Pi%C3%ABch\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_Pi%C3%ABch"}]}, {"year": "1958", "text": "<PERSON>, American author and illustrator (b. 1897)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Egyptian theorist, author, and poet (b. 1906)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian theorist, author, and poet (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian theorist, author, and poet (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>b"}]}, {"year": "1968", "text": "<PERSON>, American general (b. 1881)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a>, American general (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, American general (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American murderer (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON>.</a>, American murderer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" title=\"<PERSON> and <PERSON><PERSON>\"><PERSON>.</a>, American murderer (b. 1904)", "links": [{"title": "<PERSON> and Lo<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, German singer-songwriter (b. 1905)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Irish soldier and politician, 3rd President of Ireland (b. 1882)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/%C3%89amon_de_<PERSON>ra\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89amon_de_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish soldier and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (b. 1882)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89amon_<PERSON>_<PERSON>ra"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1977", "text": "<PERSON>, American actress (b. 1923)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian race car driver (b. 1945)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver (b. 1945)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1979", "text": "<PERSON>, American author and educator (b. 1890)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American journalist and author (b. 1892)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Thomas\"><PERSON></a>, American journalist and author (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Thomas"}]}, {"year": "1982", "text": "<PERSON>, Swedish actress (b. 1915)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American composer and conductor (b. 1910)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and conductor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer and conductor (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Engel"}]}, {"year": "1985", "text": "<PERSON>, British-American actress (b. 1918)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American actor and screenwriter (b. 1914)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and screenwriter (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor and screenwriter (b. 1914)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1987", "text": "<PERSON>, American actor (b. 1924)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English explorer and painter (b. 1909)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and painter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer and painter (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Canadian-American mystic and author (b. 1901)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall\" class=\"mw-redirect\" title=\"<PERSON>ly Palmer Hall\"><PERSON><PERSON></a>, Canadian-American mystic and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall\" class=\"mw-redirect\" title=\"<PERSON>ly Palmer Hall\"><PERSON><PERSON></a>, Canadian-American mystic and author (b. 1901)", "links": [{"title": "<PERSON><PERSON> Palmer Hall", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Hall"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Italian businessman (b. 1924)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Libero_Grassi\" title=\"Libero Grassi\"><PERSON><PERSON><PERSON></a>, Italian businessman (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libero_Grassi\" title=\"Libero Grassi\"><PERSON><PERSON><PERSON></a>, Italian businessman (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Libero_Grassi"}]}, {"year": "1992", "text": "<PERSON>, French philosopher and theorist (b. 1930)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_G<PERSON>ttari\" title=\"<PERSON>\"><PERSON></a>, French philosopher and theorist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_G<PERSON>ri\" title=\"<PERSON>\"><PERSON></a>, French philosopher and theorist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Guattari"}]}, {"year": "1995", "text": "<PERSON>, American director, producer, and screenwriter (b. 1930)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1922)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer and manager (b. 1951)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American-Italian painter and academic (b. 1913)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian painter and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Italian painter and academic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Australian singer-songwriter & television personality (b. 1952)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter &amp; television personality (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter &amp; television personality (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Spanish actor, director, and screenwriter (b. 1926)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, English race car driver (b. 1919)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Iraqi politician (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English painter and academic (b. 1936)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Dutch conductor (b. 1942)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, Dutch conductor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, Dutch conductor (b. 1942)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)"}]}, {"year": "2007", "text": "<PERSON>, New Zealand businessman (b. 1914)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, New Zealand businessman (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American police officer (b. 1962)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French civil servant and politician, 154th Prime Minister of France (b. 1916)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant and politician, 154th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French civil servant and politician, 154th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "2007", "text": "<PERSON>, Dutch-American businessman, founded P<PERSON><PERSON>'s Coffee & Tea (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American businessman, founded <a href=\"https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Coffee &amp; Tea\"><PERSON><PERSON><PERSON>'s Coffee &amp; Tea</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American businessman, founded <a href=\"https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Coffee &amp; Tea\"><PERSON><PERSON><PERSON>'s Coffee &amp; Tea</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peet's Coffee & Tea", "link": "https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea"}]}, {"year": "2008", "text": "<PERSON>, English actor, producer, and screenwriter (b. 1953)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American geophysicist and theorist (b. 1939)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist and theorist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geophysicist and theorist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (b. 1915)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Honeyboy%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Honeyboy%22_<PERSON>\" title='<PERSON> \"<PERSON>boy\" <PERSON>'><PERSON><PERSON></a>, American singer-songwriter and guitarist (b. 1915)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Honeyboy%22_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Japanese voice actor (b. 1931)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese voice actor (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian academic and philanthropist, co-founded Pier 21 (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Pier_21\" title=\"Pier 21\">Pier 21</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian academic and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Pier_21\" title=\"Pier 21\">Pier 21</a> (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pier 21", "link": "https://wikipedia.org/wiki/Pier_21"}]}, {"year": "2012", "text": "<PERSON>-<PERSON>, English historian and author (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1953)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese-American mathematician and academic (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American mathematician and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American mathematician and academic (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soprano (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player, coach, and manager (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian volleyball player and coach (b. 1969)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(volleyball)\" title=\"<PERSON> (volleyball)\"><PERSON></a>, Russian volleyball player and coach (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(volleyball)\" title=\"<PERSON> (volleyball)\"><PERSON></a>, Russian volleyball player and coach (b. 1969)", "links": [{"title": "<PERSON> (volleyball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(volleyball)"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Zambian cardinal (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zambian cardinal (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Zambian cardinal (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American geologist and academic, co-founded The Planetary Society (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic, co-founded <a href=\"https://wikipedia.org/wiki/The_Planetary_Society\" title=\"The Planetary Society\">The Planetary Society</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic, co-founded <a href=\"https://wikipedia.org/wiki/The_Planetary_Society\" title=\"The Planetary Society\">The Planetary Society</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "The Planetary Society", "link": "https://wikipedia.org/wiki/The_Planetary_Society"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Argentine pianist and composer (b. 1975)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Octavi<PERSON>_<PERSON>\" title=\"<PERSON>avi<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine pianist and composer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Octavi<PERSON>_<PERSON>\" title=\"<PERSON>avi<PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine pianist and composer (b. 1975)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>avi<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish race car driver (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish race car driver (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd\" title=\"<PERSON><PERSON>ö<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish race car driver (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_Waldeg%C3%A5rd"}]}, {"year": "2016", "text": "<PERSON>, American stage and screen comic actor, screenwriter, film director, and author (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and screen comic actor, screenwriter, film director, and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stage and screen comic actor, screenwriter, film director, and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Scottish economist, Nobel Prize laureate (b. 1936)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> laureate (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish economist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel Prize</a> laureate (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize", "link": "https://wikipedia.org/wiki/Nobel_Prize"}]}, {"year": "2018", "text": "<PERSON>, American choreographer (b. 1930)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American choreographer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(choreographer)\" title=\"<PERSON> (choreographer)\"><PERSON></a>, American choreographer (b. 1930)", "links": [{"title": "<PERSON> (choreographer)", "link": "https://wikipedia.org/wiki/<PERSON>_(choreographer)"}]}, {"year": "2021", "text": "<PERSON>, American actor (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>, Jamaican reggae producer (b. 1936)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Scratch%22_<PERSON>\" title='<PERSON> \"<PERSON>ratch\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, Jamaican reggae producer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Scratch%22_<PERSON>\" title='<PERSON> \"<PERSON>ratch\" <PERSON>'><PERSON> \"<PERSON>ratch\" <PERSON></a>, Jamaican reggae producer (b. 1936)", "links": [{"title": "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Scratch%22_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, French orthopedic surgeon, Olympic sailor and the 8th President of the International Olympic Committee (b. 1942)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orthopedic surgeon, Olympic sailor and the 8th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French orthopedic surgeon, Olympic sailor and the 8th <a href=\"https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee\" title=\"President of the International Olympic Committee\">President of the International Olympic Committee</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the International Olympic Committee", "link": "https://wikipedia.org/wiki/President_of_the_International_Olympic_Committee"}]}, {"year": "2023", "text": "<PERSON>, Filipino broadcaster (b. 1951)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino broadcaster (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino broadcaster (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American ice hockey player (b. 1993)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}