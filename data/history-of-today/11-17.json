{"date": "November 17", "url": "https://wikipedia.org/wiki/November_17", "data": {"Events": [{"year": "887", "text": "Emperor <PERSON> the <PERSON> is deposed by the Frankish magnates in an assembly at Frankfurt, leading his nephew, <PERSON><PERSON><PERSON> of Carinthia, to declare himself king of the East Frankish Kingdom in late November.", "html": "887 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> the <PERSON></a> is deposed by the Frankish <a href=\"https://wikipedia.org/wiki/Magnate\" title=\"Magnate\">magnates</a> in an assembly at <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, leading his nephew, <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_of_Carinthia\" title=\"<PERSON><PERSON><PERSON> of Carinthia\"><PERSON><PERSON><PERSON> of Carinthia</a>, to declare himself king of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a> in late November.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> the <PERSON></a> is deposed by the Frankish <a href=\"https://wikipedia.org/wiki/Magnate\" title=\"Magnate\">magnates</a> in an assembly at <a href=\"https://wikipedia.org/wiki/Frankfurt\" title=\"Frankfurt\">Frankfurt</a>, leading his nephew, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Carinthia\" title=\"<PERSON><PERSON><PERSON> of Carinthia\"><PERSON><PERSON><PERSON> of Carinthia</a>, to declare himself king of the <a href=\"https://wikipedia.org/wiki/East_Francia\" title=\"East Francia\">East Frankish Kingdom</a> in late November.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Fat"}, {"title": "Magnate", "link": "https://wikipedia.org/wiki/Magnate"}, {"title": "Frankfurt", "link": "https://wikipedia.org/wiki/Frankfurt"}, {"title": "<PERSON><PERSON><PERSON> of Carinthia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Carinthia"}, {"title": "East Francia", "link": "https://wikipedia.org/wiki/East_Francia"}]}, {"year": "1183", "text": "Genpei War: The Battle of Mizushima takes place off the Japanese coast, where <PERSON><PERSON>'s invasion force is intercepted and defeated by the Taira clan.", "html": "1183 - Genpei War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mizushima\" title=\"Battle of Mizushima\">Battle of Mizushima</a> takes place off the Japanese coast, where <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoshinaka\" title=\"<PERSON><PERSON> no Yoshinaka\"><PERSON><PERSON> no Yoshinaka</a>'s invasion force is intercepted and defeated by the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a>.", "no_year_html": "Genpei War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mizushima\" title=\"Battle of Mizushima\">Battle of Mizushima</a> takes place off the Japanese coast, where <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yoshinaka\" title=\"<PERSON><PERSON> no Yoshinaka\"><PERSON><PERSON> no Yoshinaka</a>'s invasion force is intercepted and defeated by the <a href=\"https://wikipedia.org/wiki/Taira_clan\" title=\"Taira clan\">Taira clan</a>.", "links": [{"title": "Battle of Mizushima", "link": "https://wikipedia.org/wiki/Battle_of_Mizushima"}, {"title": "<PERSON><PERSON> no <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Taira clan", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_clan"}]}, {"year": "1292", "text": "<PERSON> becomes King of Scotland.", "html": "1292 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes King of Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes King of Scotland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1405", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON> establishes the Sultanate of Sulu.", "html": "1405 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ul-<PERSON>%C4%81shim_of_Sulu\" title=\"<PERSON> of Sulu\"><PERSON></a> establishes the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Sulu\" title=\"Sultanate of Sulu\">Sultanate of Sulu</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ul-<PERSON>%C4%81shim_of_Sulu\" title=\"<PERSON> of Sulu\"><PERSON></a> establishes the <a href=\"https://wikipedia.org/wiki/Sultanate_of_Sulu\" title=\"Sultanate of Sulu\">Sultanate of Sulu</a>.", "links": [{"title": "<PERSON> of Sulu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C4%81shim_of_Sulu"}, {"title": "Sultanate of Sulu", "link": "https://wikipedia.org/wiki/Sultanate_of_Sulu"}]}, {"year": "1494", "text": "French King <PERSON> occupies Florence, Italy.", "html": "1494 - French King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_France\" title=\"Charles VIII of France\"><PERSON> VIII</a> <a href=\"https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398\" class=\"mw-redirect\" title=\"Italian War of 1494-98\">occupies Florence</a>, Italy.", "no_year_html": "French King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"Charles VIII of France\"><PERSON> VIII</a> <a href=\"https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398\" class=\"mw-redirect\" title=\"Italian War of 1494-98\">occupies Florence</a>, Italy.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_<PERSON>_of_France"}, {"title": "Italian War of 1494-98", "link": "https://wikipedia.org/wiki/Italian_War_of_1494%E2%80%9398"}]}, {"year": "1511", "text": "<PERSON> of England concludes the Treaty of Westminster, a pledge of mutual aid against the French, with <PERSON> of <PERSON>.", "html": "1511 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a> concludes the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai#Holy_League\" title=\"War of the League of Cambrai\">Treaty of Westminster</a>, a pledge of mutual aid against the French, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> of <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII of England</a> concludes the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai#Holy_League\" title=\"War of the League of Cambrai\">Treaty of Westminster</a>, a pledge of mutual aid against the French, with <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> of Aragon</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "War of the League of Cambrai", "link": "https://wikipedia.org/wiki/War_of_the_League_of_Cambrai#Holy_League"}, {"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}]}, {"year": "1558", "text": "Elizabethan era begins: Queen <PERSON> of England dies and is succeeded by her half-sister <PERSON> of England.", "html": "1558 - <a href=\"https://wikipedia.org/wiki/Elizabethan_era\" title=\"Elizabethan era\">Elizabethan era</a> begins: Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> dies and is succeeded by her half-sister <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabethan_era\" title=\"Elizabethan era\">Elizabethan era</a> begins: Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> dies and is succeeded by her half-sister <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> I of England\"><PERSON> of England</a>.", "links": [{"title": "Elizabethan era", "link": "https://wikipedia.org/wiki/Elizabethan_era"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1603", "text": "English explorer, writer and courtier Sir <PERSON> goes on trial for treason.", "html": "1603 - English explorer, writer and <a href=\"https://wikipedia.org/wiki/Courtier\" title=\"Courtier\">courtier</a> Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "English explorer, writer and <a href=\"https://wikipedia.org/wiki/Courtier\" title=\"Courtier\">courtier</a> Sir <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "Courtier", "link": "https://wikipedia.org/wiki/Courtier"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Raleigh"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1775", "text": "The city of Kuopio, Finland (belonging to Sweden at this time) is founded by King <PERSON> of Sweden.", "html": "1775 - The city of <a href=\"https://wikipedia.org/wiki/Ku<PERSON><PERSON>\" title=\"Kuop<PERSON>\">Kuopio, Finland</a> (belonging to <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> at this time) is founded by King <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Sweden\" class=\"mw-redirect\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a>.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"Kuop<PERSON>\">Kuopio, Finland</a> (belonging to <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Sweden</a> at this time) is founded by King <a href=\"https://wikipedia.org/wiki/Gustav_III_of_Sweden\" class=\"mw-redirect\" title=\"Gustav III of Sweden\"><PERSON> of Sweden</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>opio"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1777", "text": "Articles of Confederation (United States) are submitted to the states for ratification.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a> (United States) are submitted to the states for ratification.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Articles_of_Confederation\" title=\"Articles of Confederation\">Articles of Confederation</a> (United States) are submitted to the states for ratification.", "links": [{"title": "Articles of Confederation", "link": "https://wikipedia.org/wiki/Articles_of_Confederation"}]}, {"year": "1796", "text": "French Revolutionary Wars: Battle of the Bridge of Arcole: French forces defeat the Austrians in Italy.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bridge_of_Arcole\" class=\"mw-redirect\" title=\"Battle of the Bridge of Arcole\">Battle of the Bridge of Arcole</a>: French forces defeat the Austrians in Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Bridge_of_Arcole\" class=\"mw-redirect\" title=\"Battle of the Bridge of Arcole\">Battle of the Bridge of Arcole</a>: French forces defeat the Austrians in Italy.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of the Bridge of Arcole", "link": "https://wikipedia.org/wiki/Battle_of_the_Bridge_of_Arcole"}]}, {"year": "1800", "text": "The United States Congress holds its first session in Washington, D.C.", "html": "1800 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> holds its first <a href=\"https://wikipedia.org/wiki/6th_United_States_Congress\" title=\"6th United States Congress\">session</a> in Washington, D.C.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> holds its first <a href=\"https://wikipedia.org/wiki/6th_United_States_Congress\" title=\"6th United States Congress\">session</a> in Washington, D.C.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "6th United States Congress", "link": "https://wikipedia.org/wiki/6th_United_States_Congress"}]}, {"year": "1810", "text": "Sweden declares war on its ally the United Kingdom to begin the Anglo-Swedish War, although no fighting ever takes place.", "html": "1810 - Sweden declares war on its ally the United Kingdom to begin the <a href=\"https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Swedish War (1810-12)\">Anglo-Swedish War</a>, although no fighting ever takes place.", "no_year_html": "Sweden declares war on its ally the United Kingdom to begin the <a href=\"https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Swedish War (1810-12)\">Anglo-Swedish War</a>, although no fighting ever takes place.", "links": [{"title": "Anglo-Swedish War (1810-12)", "link": "https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)"}]}, {"year": "1811", "text": "<PERSON>, Chilean founding father, is sworn in as President of the executive Jun<PERSON> of the government of Chile.", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean founding father, is sworn in as President of the executive <PERSON><PERSON> of the government of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean founding father, is sworn in as President of the executive Jun<PERSON> of the government of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "1820", "text": "Captain <PERSON> becomes the first American to see Antarctica. (The Palmer Peninsula is later named after him.)", "html": "1820 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American to see Antarctica. (The <a href=\"https://wikipedia.org/wiki/Antarctic_Peninsula\" title=\"Antarctic Peninsula\">Palmer Peninsula</a> is later named after him.)", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first American to see Antarctica. (The <a href=\"https://wikipedia.org/wiki/Antarctic_Peninsula\" title=\"Antarctic Peninsula\">Palmer Peninsula</a> is later named after him.)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Antarctic Peninsula", "link": "https://wikipedia.org/wiki/Antarctic_Peninsula"}]}, {"year": "1831", "text": "Ecuador and Venezuela are separated from Gran Colombia.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> and <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> are separated from <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> and <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a> are separated from <a href=\"https://wikipedia.org/wiki/Gran_Colombia\" title=\"Gran Colombia\">Gran Colombia</a>.", "links": [{"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}, {"title": "Gran Colombia", "link": "https://wikipedia.org/wiki/Gran_Colombia"}]}, {"year": "1837", "text": "An earthquake in Valdivia, south-central Chile, causes a tsunami that leads to significant destruction along Japan's coast.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/1837_Valdivia_earthquake\" title=\"1837 Valdivia earthquake\">An earthquake in Valdivia</a>, south-central Chile, causes a tsunami that leads to significant destruction along Japan's coast.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1837_Valdivia_earthquake\" title=\"1837 Valdivia earthquake\">An earthquake in Valdivia</a>, south-central Chile, causes a tsunami that leads to significant destruction along Japan's coast.", "links": [{"title": "1837 Valdivia earthquake", "link": "https://wikipedia.org/wiki/1837_Valdivia_earthquake"}]}, {"year": "1856", "text": "American Old West: On the Sonoita River in present-day southern Arizona, the United States Army establishes Fort Buchanan in order to help control new land acquired in the Gadsden Purchase.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/American_frontier\" title=\"American frontier\">American Old West</a>: On the <a href=\"https://wikipedia.org/wiki/Sonoita_Creek\" title=\"Sonoita Creek\">Sonoita River</a> in present-day southern <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a>, the United States Army establishes <a href=\"https://wikipedia.org/wiki/Fort_Buchanan,_Arizona\" title=\"Fort Buchanan, Arizona\">Fort Buchanan</a> in order to help control new land acquired in the <a href=\"https://wikipedia.org/wiki/Gadsden_Purchase\" title=\"Gadsden Purchase\">Gadsden Purchase</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_frontier\" title=\"American frontier\">American Old West</a>: On the <a href=\"https://wikipedia.org/wiki/Sonoita_Creek\" title=\"Sonoita Creek\">Sonoita River</a> in present-day southern <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a>, the United States Army establishes <a href=\"https://wikipedia.org/wiki/Fort_Buchanan,_Arizona\" title=\"Fort Buchanan, Arizona\">Fort Buchanan</a> in order to help control new land acquired in the <a href=\"https://wikipedia.org/wiki/Gadsden_Purchase\" title=\"Gadsden Purchase\">Gadsden Purchase</a>.", "links": [{"title": "American frontier", "link": "https://wikipedia.org/wiki/American_frontier"}, {"title": "Sonoita Creek", "link": "https://wikipedia.org/wiki/Sonoita_Creek"}, {"title": "Arizona", "link": "https://wikipedia.org/wiki/Arizona"}, {"title": "Fort Buchanan, Arizona", "link": "https://wikipedia.org/wiki/Fort_Buchanan,_Arizona"}, {"title": "Gadsden Purchase", "link": "https://wikipedia.org/wiki/Gadsden_Purchase"}]}, {"year": "1858", "text": "Modified Julian Day zero.", "html": "1858 - Modified <a href=\"https://wikipedia.org/wiki/Julian_day\" title=\"Julian day\">Julian Day</a> zero.", "no_year_html": "Modified <a href=\"https://wikipedia.org/wiki/Julian_day\" title=\"Julian day\">Julian Day</a> zero.", "links": [{"title": "Julian day", "link": "https://wikipedia.org/wiki/Julian_day"}]}, {"year": "1858", "text": "The city of Denver, Colorado is founded.", "html": "1858 - The city of <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> is founded.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Denver\" title=\"Denver\">Denver</a>, <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> is founded.", "links": [{"title": "Denver", "link": "https://wikipedia.org/wiki/Denver"}, {"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}]}, {"year": "1863", "text": "American Civil War: Siege of Knoxville begins: During the Knoxville campaign, Confederate forces under General <PERSON> besiege the city of Knoxville, Tennessee and its Union defenders led by General <PERSON>.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Knoxville\" title=\"Siege of Knoxville\">Siege of Knoxville</a> begins: During the <a href=\"https://wikipedia.org/wiki/Knoxville_campaign\" title=\"Knoxville campaign\">Knoxville campaign</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> besiege the city of <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a> and its <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defenders led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Siege_of_Knoxville\" title=\"Siege of Knoxville\">Siege of Knoxville</a> begins: During the <a href=\"https://wikipedia.org/wiki/Knoxville_campaign\" title=\"Knoxville campaign\">Knoxville campaign</a>, <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> besiege the city of <a href=\"https://wikipedia.org/wiki/Knoxville,_Tennessee\" title=\"Knoxville, Tennessee\">Knoxville, Tennessee</a> and its <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> defenders led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Siege of Knoxville", "link": "https://wikipedia.org/wiki/Siege_of_Knoxville"}, {"title": "Knoxville campaign", "link": "https://wikipedia.org/wiki/Knoxville_campaign"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Knoxville, Tennessee", "link": "https://wikipedia.org/wiki/Knoxville,_Tennessee"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "In Egypt, the Suez Canal, linking the Mediterranean Sea with the Red Sea, is inaugurated.", "html": "1869 - In <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, linking the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> with the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, is inaugurated.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a>, linking the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean Sea</a> with the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, is inaugurated.", "links": [{"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}, {"title": "Red Sea", "link": "https://wikipedia.org/wiki/Red_Sea"}]}, {"year": "1878", "text": "First assassination attempt against <PERSON><PERSON> of Italy by anarchist <PERSON>, who was armed with a dagger. The King survived with a slight wound in an arm. Prime Minister <PERSON><PERSON><PERSON> blocked the aggressor, receiving an injury in a leg.", "html": "1878 - First assassination attempt against <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> by anarchist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who was armed with a dagger. The King survived with a slight wound in an arm. <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> blocked the aggressor, receiving an injury in a leg.", "no_year_html": "First assassination attempt against <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> by anarchist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who was armed with a dagger. The King survived with a slight wound in an arm. <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> blocked the aggressor, receiving an injury in a leg.", "links": [{"title": "<PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/Umberto_I_of_Italy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1885", "text": "Serbo-Bulgarian War: The decisive Battle of Slivnitsa begins.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Serbo-Bulgarian_War\" title=\"Serbo-Bulgarian War\">Serbo-Bulgarian War</a>: The decisive <a href=\"https://wikipedia.org/wiki/Battle_of_Slivnitsa\" title=\"Battle of Slivnitsa\">Battle of Slivnitsa</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbo-Bulgarian_War\" title=\"Serbo-Bulgarian War\">Serbo-Bulgarian War</a>: The decisive <a href=\"https://wikipedia.org/wiki/Battle_of_Slivnitsa\" title=\"Battle of Slivnitsa\">Battle of Slivnitsa</a> begins.", "links": [{"title": "Serbo-Bulgarian War", "link": "https://wikipedia.org/wiki/Serbo-Bulgarian_War"}, {"title": "Battle of Slivnitsa", "link": "https://wikipedia.org/wiki/Battle_of_Slivnitsa"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON>, one of the first modern serial killers, is arrested in Boston, Massachusetts.", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, one of the first modern <a href=\"https://wikipedia.org/wiki/Serial_killer\" title=\"Serial killer\">serial killers</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, one of the first modern <a href=\"https://wikipedia.org/wiki/Serial_killer\" title=\"Serial killer\">serial killers</a>, is arrested in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a>, Massachusetts.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Serial killer", "link": "https://wikipedia.org/wiki/Serial_killer"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}]}, {"year": "1896", "text": "The Western Pennsylvania Hockey League, which later became the first ice hockey league to openly trade and hire players, began play at Pittsburgh's Schenley Park Casino.", "html": "1896 - The <a href=\"https://wikipedia.org/wiki/Western_Pennsylvania_Hockey_League\" title=\"Western Pennsylvania Hockey League\">Western Pennsylvania Hockey League</a>, which later became the first <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> league to openly trade and hire players, <a href=\"https://wikipedia.org/wiki/1896%E2%80%9397_WPHL_season\" title=\"1896-97 WPHL season\">began play</a> at <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>'s <a href=\"https://wikipedia.org/wiki/Schenley_Park_Casino\" title=\"Schenley Park Casino\">Schenley Park Casino</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Western_Pennsylvania_Hockey_League\" title=\"Western Pennsylvania Hockey League\">Western Pennsylvania Hockey League</a>, which later became the first <a href=\"https://wikipedia.org/wiki/Ice_hockey\" title=\"Ice hockey\">ice hockey</a> league to openly trade and hire players, <a href=\"https://wikipedia.org/wiki/1896%E2%80%9397_WPHL_season\" title=\"1896-97 WPHL season\">began play</a> at <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh</a>'s <a href=\"https://wikipedia.org/wiki/Schenley_Park_Casino\" title=\"Schenley Park Casino\">Schenley Park Casino</a>.", "links": [{"title": "Western Pennsylvania Hockey League", "link": "https://wikipedia.org/wiki/Western_Pennsylvania_Hockey_League"}, {"title": "Ice hockey", "link": "https://wikipedia.org/wiki/Ice_hockey"}, {"title": "1896-97 WPHL season", "link": "https://wikipedia.org/wiki/1896%E2%80%9397_WPHL_season"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "Schenley Park Casino", "link": "https://wikipedia.org/wiki/Schenley_Park_Casino"}]}, {"year": "1903", "text": "The Russian Social Democratic Labour Party splits into two groups: The Bolsheviks (Russian for \"majority\") and Mensheviks (Russian for \"minority\").", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party\" title=\"Russian Social Democratic Labour Party\">Russian Social Democratic Labour Party</a> splits into two groups: The <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a> (Russian for \"majority\") and <a href=\"https://wikipedia.org/wiki/Mensheviks\" title=\"Mensheviks\">Mensheviks</a> (Russian for \"minority\").", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party\" title=\"Russian Social Democratic Labour Party\">Russian Social Democratic Labour Party</a> splits into two groups: The <a href=\"https://wikipedia.org/wiki/Bolsheviks\" title=\"Bolsheviks\">Bolsheviks</a> (Russian for \"majority\") and <a href=\"https://wikipedia.org/wiki/Mensheviks\" title=\"Mensheviks\">Mensheviks</a> (Russian for \"minority\").", "links": [{"title": "Russian Social Democratic Labour Party", "link": "https://wikipedia.org/wiki/Russian_Social_Democratic_Labour_Party"}, {"title": "Bolsheviks", "link": "https://wikipedia.org/wiki/Bolsheviks"}, {"title": "Mensheviks", "link": "https://wikipedia.org/wiki/Mensheviks"}]}, {"year": "1939", "text": "Nine Czech students are executed as a response to anti-Nazi demonstrations prompted by the death of <PERSON>. All Czech universities are shut down and more than 1,200 students sent to concentration camps. Since this event, International Students' Day is celebrated in many countries, especially in the Czech Republic.", "html": "1939 - Nine <a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> students are executed as a response to anti-<a href=\"https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia\" title=\"Protectorate of Bohemia and Moravia\">Nazi</a> demonstrations prompted by the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jan <PERSON>\"><PERSON></a>. All Czech universities are shut down and more than 1,200 students sent to <a href=\"https://wikipedia.org/wiki/Concentration_camp\" title=\"Concentration camp\">concentration camps</a>. Since this event, <a href=\"https://wikipedia.org/wiki/International_Students%27_Day\" title=\"International Students' Day\">International Students' Day</a> is celebrated in many countries, especially in the Czech Republic.", "no_year_html": "Nine <a href=\"https://wikipedia.org/wiki/Czechs\" title=\"Czechs\">Czech</a> students are executed as a response to anti-<a href=\"https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia\" title=\"Protectorate of Bohemia and Moravia\">Nazi</a> demonstrations prompted by the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jan <PERSON>\"><PERSON></a>. All Czech universities are shut down and more than 1,200 students sent to <a href=\"https://wikipedia.org/wiki/Concentration_camp\" title=\"Concentration camp\">concentration camps</a>. Since this event, <a href=\"https://wikipedia.org/wiki/International_Students%27_Day\" title=\"International Students' Day\">International Students' Day</a> is celebrated in many countries, especially in the Czech Republic.", "links": [{"title": "Czechs", "link": "https://wikipedia.org/wiki/Czechs"}, {"title": "Protectorate of Bohemia and Moravia", "link": "https://wikipedia.org/wiki/Protectorate_of_Bohemia_and_Moravia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Concentration camp", "link": "https://wikipedia.org/wiki/Concentration_camp"}, {"title": "International Students' Day", "link": "https://wikipedia.org/wiki/International_Students%27_Day"}]}, {"year": "1940", "text": "The Tartu Art Museum is established in Tartu, Estonia.", "html": "1940 - The <a href=\"https://wikipedia.org/wiki/Tartu_Art_Museum\" title=\"Tartu Art Museum\">Tartu Art Museum</a> is established in <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tartu_Art_Museum\" title=\"Tartu Art Museum\">Tartu Art Museum</a> is established in <a href=\"https://wikipedia.org/wiki/Tartu\" title=\"Tartu\">Tartu</a>, <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "links": [{"title": "Tartu Art Museum", "link": "https://wikipedia.org/wiki/Tartu_Art_Museum"}, {"title": "Tartu", "link": "https://wikipedia.org/wiki/Tartu"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}]}, {"year": "1947", "text": "The Screen Actors Guild implements an anti-Communist loyalty oath.", "html": "1947 - The <a href=\"https://wikipedia.org/wiki/Screen_Actors_Guild\" title=\"Screen Actors Guild\">Screen Actors Guild</a> implements an anti-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> loyalty oath.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Screen_Actors_Guild\" title=\"Screen Actors Guild\">Screen Actors Guild</a> implements an anti-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">Communist</a> loyalty oath.", "links": [{"title": "Screen Actors Guild", "link": "https://wikipedia.org/wiki/Screen_Actors_Guild"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}]}, {"year": "1947", "text": "American scientists <PERSON> and <PERSON> observe the basic principles of the transistor, a key element for the electronics revolution of the 20th century.", "html": "1947 - American scientists <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> observe the basic principles of the <a href=\"https://wikipedia.org/wiki/Transistor\" title=\"Transistor\">transistor</a>, a key element for the <a href=\"https://wikipedia.org/wiki/Electronics\" title=\"Electronics\">electronics</a> revolution of the <a href=\"https://wikipedia.org/wiki/20th_century\" title=\"20th century\">20th century</a>.", "no_year_html": "American scientists <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> observe the basic principles of the <a href=\"https://wikipedia.org/wiki/Transistor\" title=\"Transistor\">transistor</a>, a key element for the <a href=\"https://wikipedia.org/wiki/Electronics\" title=\"Electronics\">electronics</a> revolution of the <a href=\"https://wikipedia.org/wiki/20th_century\" title=\"20th century\">20th century</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Transistor", "link": "https://wikipedia.org/wiki/Transistor"}, {"title": "Electronics", "link": "https://wikipedia.org/wiki/Electronics"}, {"title": "20th century", "link": "https://wikipedia.org/wiki/20th_century"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON> is officially named the 14th Dal<PERSON> Lama.", "html": "1950 - <PERSON><PERSON><PERSON> is officially named the <a href=\"https://wikipedia.org/wiki/14th_Dalai_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>.", "no_year_html": "<PERSON><PERSON><PERSON> is officially named the <a href=\"https://wikipedia.org/wiki/14th_Dalai_Lama\" title=\"14th Dalai Lama\">14th Dalai Lama</a>.", "links": [{"title": "14th Dalai Lama", "link": "https://wikipedia.org/wiki/14th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "United Nations Security Council Resolution 89 relating to the Palestine Question is adopted.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_89\" title=\"United Nations Security Council Resolution 89\">United Nations Security Council Resolution 89</a> relating to the Palestine Question is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_89\" title=\"United Nations Security Council Resolution 89\">United Nations Security Council Resolution 89</a> relating to the Palestine Question is adopted.", "links": [{"title": "United Nations Security Council Resolution 89", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_89"}]}, {"year": "1953", "text": "The remaining human inhabitants of the Blasket Islands, County Kerry, Ireland, are evacuated to the mainland.", "html": "1953 - The remaining human inhabitants of the <a href=\"https://wikipedia.org/wiki/Blasket_Islands\" title=\"Blasket Islands\">Blasket Islands</a>, <a href=\"https://wikipedia.org/wiki/County_Kerry\" title=\"County Kerry\">County Kerry</a>, Ireland, are evacuated to the mainland.", "no_year_html": "The remaining human inhabitants of the <a href=\"https://wikipedia.org/wiki/Blasket_Islands\" title=\"Blasket Islands\">Blasket Islands</a>, <a href=\"https://wikipedia.org/wiki/County_Kerry\" title=\"County Kerry\">County Kerry</a>, Ireland, are evacuated to the mainland.", "links": [{"title": "Blasket Islands", "link": "https://wikipedia.org/wiki/Blasket_Islands"}, {"title": "County Kerry", "link": "https://wikipedia.org/wiki/County_Kerry"}]}, {"year": "1957", "text": "Vickers Viscount G-AOHP of British European Airways crashes at Ballerup after the failure of three engines on approach to Copenhagen Airport. The cause is a malfunction of the anti-icing system on the aircraft. There are no fatalities.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> G-AOHP of <a href=\"https://wikipedia.org/wiki/British_European_Airways\" title=\"British European Airways\">British European Airways</a> crashes at Ballerup after the failure of three engines on approach to <a href=\"https://wikipedia.org/wiki/Copenhagen_Airport\" title=\"Copenhagen Airport\">Copenhagen Airport</a>. The cause is a malfunction of the anti-icing system on the aircraft. There are no fatalities.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vickers_Viscount\" title=\"Vickers Viscount\">Vickers Viscount</a> G-AOHP of <a href=\"https://wikipedia.org/wiki/British_European_Airways\" title=\"British European Airways\">British European Airways</a> crashes at Ballerup after the failure of three engines on approach to <a href=\"https://wikipedia.org/wiki/Copenhagen_Airport\" title=\"Copenhagen Airport\">Copenhagen Airport</a>. The cause is a malfunction of the anti-icing system on the aircraft. There are no fatalities.", "links": [{"title": "Vickers Viscount", "link": "https://wikipedia.org/wiki/Vickers_Viscount"}, {"title": "British European Airways", "link": "https://wikipedia.org/wiki/British_European_Airways"}, {"title": "Copenhagen Airport", "link": "https://wikipedia.org/wiki/Copenhagen_Airport"}]}, {"year": "1962", "text": "President <PERSON> dedicates Washington Dulles International Airport, serving the Washington, D.C., region.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates <a href=\"https://wikipedia.org/wiki/Washington_Dulles_International_Airport\" class=\"mw-redirect\" title=\"Washington Dulles International Airport\">Washington Dulles International Airport</a>, serving the Washington, D.C., region.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> dedicates <a href=\"https://wikipedia.org/wiki/Washington_Dulles_International_Airport\" class=\"mw-redirect\" title=\"Washington Dulles International Airport\">Washington Dulles International Airport</a>, serving the Washington, D.C., region.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Washington Dulles International Airport", "link": "https://wikipedia.org/wiki/Washington_Dulles_International_Airport"}]}, {"year": "1967", "text": "Vietnam War: Acting on optimistic reports that he had been given on November 13, U.S. President <PERSON> tells the nation that, while much remained to be done, \"We are inflicting greater losses than we're taking...We are making progress.\"", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Acting on optimistic reports that he had been given on November 13, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells the nation that, while much remained to be done, \"We are inflicting greater losses than we're taking...We are making progress.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Acting on optimistic reports that he had been given on November 13, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells the nation that, while much remained to be done, \"We are inflicting greater losses than we're taking...We are making progress.\"", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "British European Airways introduces the BAC One-Eleven into commercial service.", "html": "1968 - British European Airways introduces the <a href=\"https://wikipedia.org/wiki/BAC_One-Eleven\" title=\"BAC One-Eleven\">BAC One-Eleven</a> into commercial service.", "no_year_html": "British European Airways introduces the <a href=\"https://wikipedia.org/wiki/BAC_One-Eleven\" title=\"BAC One-Eleven\">BAC One-Eleven</a> into commercial service.", "links": [{"title": "BAC One-Eleven", "link": "https://wikipedia.org/wiki/BAC_One-Eleven"}]}, {"year": "1968", "text": "Viewers of the Raiders-Jets football game in the eastern United States are denied the opportunity to watch its exciting finish when NBC broadcasts <PERSON> instead, prompting changes to sports broadcasting in the U.S.", "html": "1968 - Viewers of the <a href=\"https://wikipedia.org/wiki/Heidi_Game\" title=\"Heidi Game\">Raiders-Jets football game</a> in the eastern United States are denied the opportunity to watch its exciting finish when <a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a> broadcasts <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(1968_film)\" title=\"<PERSON> (1968 film)\"><PERSON></a></i> instead, prompting changes to sports broadcasting in the U.S.", "no_year_html": "Viewers of the <a href=\"https://wikipedia.org/wiki/Heidi_Game\" title=\"Heidi Game\">Raiders-Jets football game</a> in the eastern United States are denied the opportunity to watch its exciting finish when <a href=\"https://wikipedia.org/wiki/NBC\" title=\"NBC\">NBC</a> broadcasts <i><a href=\"https://wikipedia.org/wiki/<PERSON>_(1968_film)\" title=\"<PERSON> (1968 film)\"><PERSON></a></i> instead, prompting changes to sports broadcasting in the U.S.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "NBC", "link": "https://wikipedia.org/wiki/NBC"}, {"title": "<PERSON> (1968 film)", "link": "https://wikipedia.org/wiki/<PERSON>_(1968_film)"}]}, {"year": "1969", "text": "Cold War: Negotiators from the Soviet Union and the United States meet in Helsinki, Finland to begin SALT I negotiations aimed at limiting the number of strategic weapons on both sides.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Negotiators from the Soviet Union and the United States meet in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland to begin <a href=\"https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks#SALT_I_Treaty\" title=\"Strategic Arms Limitation Talks\">SALT I</a> negotiations aimed at limiting the number of <a href=\"https://wikipedia.org/wiki/Weapon_of_mass_destruction\" title=\"Weapon of mass destruction\">strategic weapons</a> on both sides.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Negotiators from the Soviet Union and the United States meet in <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a>, Finland to begin <a href=\"https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks#SALT_I_Treaty\" title=\"Strategic Arms Limitation Talks\">SALT I</a> negotiations aimed at limiting the number of <a href=\"https://wikipedia.org/wiki/Weapon_of_mass_destruction\" title=\"Weapon of mass destruction\">strategic weapons</a> on both sides.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "Strategic Arms Limitation Talks", "link": "https://wikipedia.org/wiki/Strategic_Arms_Limitation_Talks#SALT_I_Treaty"}, {"title": "Weapon of mass destruction", "link": "https://wikipedia.org/wiki/Weapon_of_mass_destruction"}]}, {"year": "1970", "text": "Vietnam War: Lieutenant <PERSON> goes on trial for the My Lai Massacre.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for the <a href=\"https://wikipedia.org/wiki/My_Lai_Massacre\" class=\"mw-redirect\" title=\"My Lai Massacre\">My Lai Massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Lieutenant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for the <a href=\"https://wikipedia.org/wiki/My_Lai_Massacre\" class=\"mw-redirect\" title=\"My Lai Massacre\">My Lai Massacre</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "My Lai Massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "Luna programme: The Soviet Union lands Lunokhod 1 on Mare Imbrium (Sea of Rains) on the Moon. This is the first roving remote-controlled robot to land on another world and is released by the orbiting Luna 17 spacecraft.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Luna_programme\" title=\"Luna programme\">Luna programme</a>: The Soviet Union lands <i><a href=\"https://wikipedia.org/wiki/Lunokhod_1\" title=\"Lunokhod 1\">Lunokhod 1</a></i> on <a href=\"https://wikipedia.org/wiki/Mare_Imbrium\" title=\"Mare Imbrium\">Mare Imbrium</a> (Sea of Rains) on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>. This is the first roving remote-controlled robot to land on another world and is released by the orbiting <i><a href=\"https://wikipedia.org/wiki/Luna_17\" title=\"Luna 17\">Luna 17</a></i> spacecraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luna_programme\" title=\"Luna programme\">Luna programme</a>: The Soviet Union lands <i><a href=\"https://wikipedia.org/wiki/Lunokhod_1\" title=\"Lunokhod 1\">Lunokhod 1</a></i> on <a href=\"https://wikipedia.org/wiki/Mare_Imbrium\" title=\"Mare Imbrium\">Mare Imbrium</a> (Sea of Rains) on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>. This is the first roving remote-controlled robot to land on another world and is released by the orbiting <i><a href=\"https://wikipedia.org/wiki/Luna_17\" title=\"Luna 17\">Luna 17</a></i> spacecraft.", "links": [{"title": "Luna programme", "link": "https://wikipedia.org/wiki/Luna_programme"}, {"title": "Lunokhod 1", "link": "https://wikipedia.org/wiki/Lunokhod_1"}, {"title": "Mare Imbrium", "link": "https://wikipedia.org/wiki/Mare_Imbrium"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}, {"title": "Luna 17", "link": "https://wikipedia.org/wiki/Luna_17"}]}, {"year": "1973", "text": "Watergate scandal: In Orlando, Florida, U.S. President <PERSON> tells 400 Associated Press managing editors \"I am not a crook.\"", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: In <a href=\"https://wikipedia.org/wiki/Orlando,_Florida\" title=\"Orlando, Florida\">Orlando, Florida</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells 400 <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> managing editors \"I am not a crook.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Watergate_scandal\" title=\"Watergate scandal\">Watergate scandal</a>: In <a href=\"https://wikipedia.org/wiki/Orlando,_Florida\" title=\"Orlando, Florida\">Orlando, Florida</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> tells 400 <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> managing editors \"I am not a crook.\"", "links": [{"title": "Watergate scandal", "link": "https://wikipedia.org/wiki/Watergate_scandal"}, {"title": "Orlando, Florida", "link": "https://wikipedia.org/wiki/Orlando,_Florida"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Associated Press", "link": "https://wikipedia.org/wiki/Associated_Press"}]}, {"year": "1973", "text": "The Athens Polytechnic uprising against the military regime ends in a bloodshed in the Greek capital.", "html": "1973 - The <a href=\"https://wikipedia.org/wiki/Athens_Polytechnic_uprising\" title=\"Athens Polytechnic uprising\">Athens Polytechnic uprising</a> against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">military regime</a> ends in a bloodshed in the Greek capital.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Athens_Polytechnic_uprising\" title=\"Athens Polytechnic uprising\">Athens Polytechnic uprising</a> against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">military regime</a> ends in a bloodshed in the Greek capital.", "links": [{"title": "Athens Polytechnic uprising", "link": "https://wikipedia.org/wiki/Athens_Polytechnic_uprising"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}]}, {"year": "1983", "text": "The Zapatista Army of National Liberation is founded in Mexico.", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation\" title=\"Zapatista Army of National Liberation\">Zapatista Army of National Liberation</a> is founded in Mexico.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation\" title=\"Zapatista Army of National Liberation\">Zapatista Army of National Liberation</a> is founded in Mexico.", "links": [{"title": "Zapatista Army of National Liberation", "link": "https://wikipedia.org/wiki/Zapatista_Army_of_National_Liberation"}]}, {"year": "1986", "text": "The flight crew of Japan Airlines Flight 1628 are involved in a UFO sighting incident while flying over Alaska.", "html": "1986 - The flight crew of <a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_1628_incident\" class=\"mw-redirect\" title=\"Japan Airlines Flight 1628 incident\">Japan Airlines Flight 1628</a> are involved in a <a href=\"https://wikipedia.org/wiki/UFO_sighting\" class=\"mw-redirect\" title=\"UFO sighting\">UFO sighting</a> incident while flying over Alaska.", "no_year_html": "The flight crew of <a href=\"https://wikipedia.org/wiki/Japan_Airlines_Flight_1628_incident\" class=\"mw-redirect\" title=\"Japan Airlines Flight 1628 incident\">Japan Airlines Flight 1628</a> are involved in a <a href=\"https://wikipedia.org/wiki/UFO_sighting\" class=\"mw-redirect\" title=\"UFO sighting\">UFO sighting</a> incident while flying over Alaska.", "links": [{"title": "Japan Airlines Flight 1628 incident", "link": "https://wikipedia.org/wiki/Japan_Airlines_Flight_1628_incident"}, {"title": "UFO sighting", "link": "https://wikipedia.org/wiki/UFO_sighting"}]}, {"year": "1989", "text": "Cold War: Velvet Revolution begins: In Czechoslovakia, a student demonstration in Prague is quelled by riot police. This sparks an uprising aimed at overthrowing the communist government (it succeeds on December 29).", "html": "1989 - Cold War: <a href=\"https://wikipedia.org/wiki/Velvet_Revolution\" title=\"Velvet Revolution\">Velvet Revolution</a> begins: In <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, a student demonstration in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> is quelled by riot police. This sparks an uprising aimed at overthrowing the communist government (it succeeds on December 29).", "no_year_html": "Cold War: <a href=\"https://wikipedia.org/wiki/Velvet_Revolution\" title=\"Velvet Revolution\">Velvet Revolution</a> begins: In <a href=\"https://wikipedia.org/wiki/Czechoslovakia\" title=\"Czechoslovakia\">Czechoslovakia</a>, a student demonstration in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> is quelled by riot police. This sparks an uprising aimed at overthrowing the communist government (it succeeds on December 29).", "links": [{"title": "Velvet Revolution", "link": "https://wikipedia.org/wiki/Velvet_Revolution"}, {"title": "Czechoslovakia", "link": "https://wikipedia.org/wiki/Czechoslovakia"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}]}, {"year": "1990", "text": "Fugendake, part of the Mount Unzen volcanic complex, Nagasaki Prefecture, Japan, becomes active again and erupts.", "html": "1990 - Fugendake, part of the <a href=\"https://wikipedia.org/wiki/Mount_Unzen\" title=\"Mount Unzen\">Mount Unzen</a> volcanic complex, <a href=\"https://wikipedia.org/wiki/Nagasaki_Prefecture\" title=\"Nagasaki Prefecture\">Nagasaki Prefecture</a>, Japan, becomes active again and erupts.", "no_year_html": "Fugendake, part of the <a href=\"https://wikipedia.org/wiki/Mount_Unzen\" title=\"Mount Unzen\">Mount Unzen</a> volcanic complex, <a href=\"https://wikipedia.org/wiki/Nagasaki_Prefecture\" title=\"Nagasaki Prefecture\">Nagasaki Prefecture</a>, Japan, becomes active again and erupts.", "links": [{"title": "Mount Unzen", "link": "https://wikipedia.org/wiki/Mount_Unzen"}, {"title": "Nagasaki Prefecture", "link": "https://wikipedia.org/wiki/Nagasaki_Prefecture"}]}, {"year": "1993", "text": "United States House of Representatives passes a resolution to establish the North American Free Trade Agreement.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> passes a resolution to establish the <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> passes a resolution to establish the <a href=\"https://wikipedia.org/wiki/North_American_Free_Trade_Agreement\" title=\"North American Free Trade Agreement\">North American Free Trade Agreement</a>.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "North American Free Trade Agreement", "link": "https://wikipedia.org/wiki/North_American_Free_Trade_Agreement"}]}, {"year": "1993", "text": "In Nigeria, General <PERSON><PERSON> ousts the government of <PERSON> in a military coup.", "html": "1993 - In Nigeria, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> ousts the government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a military coup.", "no_year_html": "In Nigeria, General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> ousts the government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in a military coup.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "In Luxor, Egypt, 62 people are killed by six Islamic militants outside the Temple of Hatshepsut, known as Luxor massacre.", "html": "1997 - In <a href=\"https://wikipedia.org/wiki/Luxor\" title=\"Luxor\">Luxor</a>, Egypt, 62 people are killed by six <a href=\"https://wikipedia.org/wiki/Islamic_terrorism\" title=\"Islamic terrorism\">Islamic militants</a> outside the <a href=\"https://wikipedia.org/wiki/Deir_el-Bahari\" title=\"Deir el-Bahari\">Temple of Hatshepsut</a>, known as <a href=\"https://wikipedia.org/wiki/Luxor_massacre\" title=\"Luxor massacre\">Luxor massacre</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Luxor\" title=\"Luxor\">Luxor</a>, Egypt, 62 people are killed by six <a href=\"https://wikipedia.org/wiki/Islamic_terrorism\" title=\"Islamic terrorism\">Islamic militants</a> outside the <a href=\"https://wikipedia.org/wiki/Deir_el-Bahari\" title=\"Deir el-Bahari\">Temple of Hatshepsut</a>, known as <a href=\"https://wikipedia.org/wiki/Luxor_massacre\" title=\"Luxor massacre\">Luxor massacre</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Luxor"}, {"title": "Islamic terrorism", "link": "https://wikipedia.org/wiki/Islamic_terrorism"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "Luxor massacre", "link": "https://wikipedia.org/wiki/Luxor_massacre"}]}, {"year": "2000", "text": "A catastrophic landslide in Log pod Mangartom, Slovenia, kills seven, and causes millions of SIT of damage. It is one of the worst catastrophes in Slovenia in the past 100 years.", "html": "2000 - A catastrophic <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> in <a href=\"https://wikipedia.org/wiki/Log_pod_Mangartom\" title=\"Log pod Mangartom\">Log pod Mangartom</a>, <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>, kills seven, and causes millions of <a href=\"https://wikipedia.org/wiki/Slovenian_tolar\" title=\"Slovenian tolar\">SIT</a> of damage. It is one of the worst catastrophes in Slovenia in the past 100 years.", "no_year_html": "A catastrophic <a href=\"https://wikipedia.org/wiki/Landslide\" title=\"Landslide\">landslide</a> in <a href=\"https://wikipedia.org/wiki/Log_pod_Mangartom\" title=\"Log pod Mangartom\">Log pod Mangartom</a>, <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a>, kills seven, and causes millions of <a href=\"https://wikipedia.org/wiki/Slovenian_tolar\" title=\"Slovenian tolar\">SIT</a> of damage. It is one of the worst catastrophes in Slovenia in the past 100 years.", "links": [{"title": "Landslide", "link": "https://wikipedia.org/wiki/Landslide"}, {"title": "Log pod Mangartom", "link": "https://wikipedia.org/wiki/Log_pod_Mangartom"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "Slovenian tolar", "link": "https://wikipedia.org/wiki/Slovenian_tolar"}]}, {"year": "2000", "text": "<PERSON> is removed from office as president of Peru.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is removed from office as president of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is removed from office as president of <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "2003", "text": "Actor <PERSON>’s tenure as the governor of California began.", "html": "2003 - Actor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s tenure as the <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">governor of California</a> began.", "no_year_html": "Actor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s tenure as the <a href=\"https://wikipedia.org/wiki/Governor_of_California\" title=\"Governor of California\">governor of California</a> began.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of California", "link": "https://wikipedia.org/wiki/Governor_of_California"}]}, {"year": "2012", "text": "At least 50 schoolchildren are killed in an accident at a railway crossing near Manfalut, Egypt.", "html": "2012 - At least 50 schoolchildren are killed in <a href=\"https://wikipedia.org/wiki/Manfalut_railway_accident\" class=\"mw-redirect\" title=\"Manfalut railway accident\">an accident</a> at a railway crossing near <a href=\"https://wikipedia.org/wiki/Manfalut\" title=\"Manfalut\">Manfalut</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "At least 50 schoolchildren are killed in <a href=\"https://wikipedia.org/wiki/Manfalut_railway_accident\" class=\"mw-redirect\" title=\"Manfalut railway accident\">an accident</a> at a railway crossing near <a href=\"https://wikipedia.org/wiki/Manfalut\" title=\"Manfalut\">Manfalut</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Manfalut railway accident", "link": "https://wikipedia.org/wiki/Man<PERSON>lut_railway_accident"}, {"title": "Man<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>lut"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "2013", "text": "Fifty people are killed when Tatarstan Airlines Flight 363 crashes at Kazan Airport, Russia.", "html": "2013 - Fifty people are killed when <a href=\"https://wikipedia.org/wiki/Tatarstan_Airlines_Flight_363\" title=\"Tatarstan Airlines Flight 363\">Tatarstan Airlines Flight 363</a> crashes at Kazan Airport, Russia.", "no_year_html": "Fifty people are killed when <a href=\"https://wikipedia.org/wiki/Tatarstan_Airlines_Flight_363\" title=\"Tatarstan Airlines Flight 363\">Tatarstan Airlines Flight 363</a> crashes at Kazan Airport, Russia.", "links": [{"title": "Tatarstan Airlines Flight 363", "link": "https://wikipedia.org/wiki/Tatarstan_Airlines_Flight_363"}]}, {"year": "2013", "text": "A rare late-season tornado outbreak strikes the Midwest. Illinois and Indiana are most affected with tornado reports as far north as lower Michigan. In all around six dozen tornadoes touch down in approximately an 11-hour time period, including seven EF3 and two EF4 tornadoes.", "html": "2013 - A rare late-season <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_November_17,_2013\" title=\"Tornado outbreak of November 17, 2013\">tornado outbreak</a> strikes the <a href=\"https://wikipedia.org/wiki/Midwest\" class=\"mw-redirect\" title=\"Midwest\">Midwest</a>. <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> and <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> are most affected with <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> reports as far north as lower <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>. In all around six dozen tornadoes touch down in approximately an 11-hour time period, including seven EF3 and two EF4 tornadoes.", "no_year_html": "A rare late-season <a href=\"https://wikipedia.org/wiki/Tornado_outbreak_of_November_17,_2013\" title=\"Tornado outbreak of November 17, 2013\">tornado outbreak</a> strikes the <a href=\"https://wikipedia.org/wiki/Midwest\" class=\"mw-redirect\" title=\"Midwest\">Midwest</a>. <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> and <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> are most affected with <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> reports as far north as lower <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>. In all around six dozen tornadoes touch down in approximately an 11-hour time period, including seven EF3 and two EF4 tornadoes.", "links": [{"title": "Tornado outbreak of November 17, 2013", "link": "https://wikipedia.org/wiki/Tornado_outbreak_of_November_17,_2013"}, {"title": "Midwest", "link": "https://wikipedia.org/wiki/Midwest"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}]}, {"year": "2019", "text": "The first known case of COVID-19 is traced to a 55-year-old man who had visited a market in Wuhan, Hubei Province, China.", "html": "2019 - The first known case of <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> is traced to a 55-year-old man who had visited a market in <a href=\"https://wikipedia.org/wiki/Wuhan,_Hubei_Province\" class=\"mw-redirect\" title=\"Wuhan, Hubei Province\">Wuhan, Hubei Province</a>, China.", "no_year_html": "The first known case of <a href=\"https://wikipedia.org/wiki/COVID-19\" title=\"COVID-19\">COVID-19</a> is traced to a 55-year-old man who had visited a market in <a href=\"https://wikipedia.org/wiki/Wuhan,_Hubei_Province\" class=\"mw-redirect\" title=\"Wuhan, Hubei Province\">Wuhan, Hubei Province</a>, China.", "links": [{"title": "COVID-19", "link": "https://wikipedia.org/wiki/COVID-19"}, {"title": "Wuhan, Hubei Province", "link": "https://wikipedia.org/wiki/Wuhan,_Hubei_Province"}]}], "Births": [{"year": "9", "text": "<PERSON><PERSON><PERSON><PERSON>, Roman emperor (d. 79)", "html": "9 - AD 9 - <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 79)", "no_year_html": "AD 9 - <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\"><PERSON><PERSON><PERSON><PERSON></a>, Roman emperor (d. 79)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}]}, {"year": "1019", "text": "<PERSON><PERSON>, Chinese politician (d. 1086)", "html": "1019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (d. 1086)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese politician (d. 1086)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1412", "text": "<PERSON><PERSON><PERSON>, Italian painter (d. 1468)", "html": "1412 - <a href=\"https://wikipedia.org/wiki/Zano<PERSON>_<PERSON>\" title=\"Zano<PERSON> Strozzi\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zano<PERSON>_<PERSON>\" title=\"Zano<PERSON> Strozzi\"><PERSON><PERSON><PERSON></a>, Italian painter (d. 1468)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1453", "text": "<PERSON>, Asturian prince (d. 1468)", "html": "1453 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias_(1453%E2%80%931468)\" title=\"<PERSON>, Prince of Asturias (1453-1468)\"><PERSON></a>, Asturian prince (d. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias_(1453%E2%80%931468)\" title=\"<PERSON>, Prince of Asturias (1453-1468)\"><PERSON></a>, Asturian prince (d. 1468)", "links": [{"title": "<PERSON>, Prince of Asturias (1453-1468)", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias_(1453%E2%80%931468)"}]}, {"year": "1493", "text": "<PERSON>, 3rd Baron <PERSON>, English politician (d. 1543)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician (d. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician (d. 1543)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1503", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (d. 1572)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/Bronzino\" title=\"Bronzin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bronzino\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (d. 1572)", "links": [{"title": "Bronzino", "link": "https://wikipedia.org/wiki/Bronzino"}]}, {"year": "1576", "text": "<PERSON><PERSON><PERSON>, Paraguayan missionary and saint (d. 1628)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1lez_de_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Santa Cruz\"><PERSON><PERSON><PERSON> Cruz</a>, Paraguayan missionary and saint (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nz%C3%A1lez_de_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Cruz\"><PERSON><PERSON><PERSON> Cruz</a>, Paraguayan missionary and saint (d. 1628)", "links": [{"title": "<PERSON><PERSON><PERSON> Cruz", "link": "https://wikipedia.org/wiki/Roque_Gonz%C3%A1lez_de_Santa_Cruz"}]}, {"year": "1587", "text": "<PERSON><PERSON>, Dutch poet and playwright (d. 1679)", "html": "1587 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch poet and playwright (d. 1679)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch poet and playwright (d. 1679)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON> of <PERSON>, French Catholic nun (d. 1634)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>\" title=\"<PERSON> of Jesus\"><PERSON> of <PERSON></a>, French Catholic nun (d. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>\" title=\"<PERSON> of Jesus\"><PERSON> of <PERSON></a>, French Catholic nun (d. 1634)", "links": [{"title": "<PERSON> of Jesus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1612", "text": "<PERSON><PERSON>, Chinese prince and regent (d. 1650)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese prince and regent (d. 1650)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Dorgon\"><PERSON><PERSON></a>, Chinese prince and regent (d. 1650)", "links": [{"title": "Dorgon", "link": "https://wikipedia.org/wiki/Do<PERSON>"}]}, {"year": "1681", "text": "<PERSON>, French theologian and author (d. 1776)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and author (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theologian and author (d. 1776)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1685", "text": "<PERSON>, Canadian commander and explorer (d. 1749)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON> Varennes, sieur de La Vérendrye\"><PERSON> Varennes</a>, Canadian commander and explorer (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Varennes,_sieur_de_La_V%C3%A9rendrye\" title=\"<PERSON> Varennes, sieur de La Vérendrye\"><PERSON> Varennes</a>, Canadian commander and explorer (d. 1749)", "links": [{"title": "<PERSON>, <PERSON><PERSON> de La Vérendrye", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_<PERSON>eur_de_La_V%C3%A9rendrye"}]}, {"year": "1729", "text": "<PERSON>, Sardinian queen consort (d. 1785)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON></a>, Sardinian queen consort (d. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON></a>, Sardinian queen consort (d. 1785)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Maria_Antonia_Ferdinanda_of_Spain"}]}, {"year": "1749", "text": "<PERSON>, French chef, invented canning (d. 1841)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef, invented <a href=\"https://wikipedia.org/wiki/Canning\" title=\"Canning\">canning</a> (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef, invented <a href=\"https://wikipedia.org/wiki/Canning\" title=\"Canning\">canning</a> (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Canning", "link": "https://wikipedia.org/wiki/Canning"}]}, {"year": "1753", "text": "<PERSON><PERSON><PERSON>, American pastor and botanist (d. 1815)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American pastor and botanist (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American pastor and botanist (d. 1815)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, king of France (d. 1824)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XVIII_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVIII of France\"><PERSON></a>, king of France (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XVIII_of_France\" class=\"mw-redirect\" title=\"<PERSON> XVIII of France\"><PERSON></a>, king of France (d. 1824)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XVIII_of_France"}]}, {"year": "1765", "text": "<PERSON>, French general (d. 1840)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, duchess of Mecklenburg-Strelitz (d. 1818)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Mecklenburg-Strelitz\" title=\"Duchess <PERSON> of Mecklenburg-Strelitz\"><PERSON></a>, duchess of Mecklenburg-Strelitz (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Mecklenburg-Strelitz\" title=\"Duchess <PERSON> of Mecklenburg-Strelitz\"><PERSON></a>, duchess of Mecklenburg-Strelitz (d. 1818)", "links": [{"title": "Duchess <PERSON> of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON><PERSON>_of_Mecklenburg-Strelitz"}]}, {"year": "1790", "text": "<PERSON> <PERSON>, German mathematician and astronomer (d. 1868)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_M%C3%B6bius\" title=\"August <PERSON>\">August <PERSON></a>, German mathematician and astronomer (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>%C3%B6bius\" title=\"August <PERSON>\">August <PERSON></a>, German mathematician and astronomer (d. 1868)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_M%C3%B6bius"}]}, {"year": "1793", "text": "<PERSON>, English painter, historian, and academic (d. 1865)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lake\" title=\"<PERSON>lake\"><PERSON></a>, English painter, historian, and academic (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Eastlake\" title=\"Charles <PERSON> Eastlake\"><PERSON></a>, English painter, historian, and academic (d. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Eastlake"}]}, {"year": "1816", "text": "<PERSON>, Austrian composer and historian (d. 1876)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Austrian composer and historian (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Austrian composer and historian (d. 1876)", "links": [{"title": "August <PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON><PERSON>, Bulgarian journalist and poet (d. 1895)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian journalist and poet (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian journalist and poet (d. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, American general and politician, 44th Governor of Ohio (d. 1915)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1854", "text": "<PERSON>, French general and politician, French Minister of War (d. 1934)", "html": "1854 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1857", "text": "<PERSON>, French neurologist and academic (d. 1932)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1866", "text": "<PERSON><PERSON><PERSON>, American author and activist (d. 1912)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Voltai<PERSON>_<PERSON>_<PERSON>\" title=\"Voltai<PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tai<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>tai<PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist (d. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, German neurologist and academic (d. 1918)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Korb<PERSON>_B<PERSON>mann\" title=\"Korbinian Brodmann\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and academic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korb<PERSON>_B<PERSON>mann\" title=\"Korb<PERSON> Brodmann\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and academic (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English-Canadian journalist and businessman (d. 1943)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and businessman (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian journalist and businessman (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American social worker (d. 1939)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American social worker (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American swimmer and water polo player (d. 1963)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer and water polo player (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, English-American philosopher, academic, and civil servant (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher, academic, and civil servant (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American philosopher, academic, and civil servant (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, 1st Viscount <PERSON> of Alamein, English field marshal (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount <PERSON> of Alamein\"><PERSON>, 1st Viscount <PERSON> of Alamein</a>, English field marshal (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein\" class=\"mw-redirect\" title=\"<PERSON>, 1st Viscount <PERSON> of Alamein\"><PERSON>, 1st Viscount <PERSON> of Alamein</a>, English field marshal (d. 1976)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON> of Alamein", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>_of_Alamein"}]}, {"year": "1891", "text": "<PERSON>, American screen, stage, vaudeville, circus actor, and film director (d. 1949)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screen, stage, vaudeville, circus actor, and film director (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screen, stage, vaudeville, circus actor, and film director (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Mexican journalist, author, and poet (d. 1966)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_L%C3%B3<PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, Mexican journalist, author, and poet (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3<PERSON><PERSON>_(writer)\" title=\"<PERSON><PERSON> (writer)\"><PERSON><PERSON></a>, Mexican journalist, author, and poet (d. 1966)", "links": [{"title": "<PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/Gregorio_L%C3%B3<PERSON><PERSON>_(writer)"}]}, {"year": "1896", "text": "<PERSON>, Belarusian-Russian psychologist and philosopher (d. 1934)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian psychologist and philosopher (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian psychologist and philosopher (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vy<PERSON>sky"}]}, {"year": "1897", "text": "<PERSON>, American actor, singer, and screenwriter (d. 1961)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" class=\"mw-redirect\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor, singer, and screenwriter (d. 1961)", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_actor)"}]}, {"year": "1899", "text": "<PERSON>, Canadian-American engineer (d. 1971)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, German academic and politician, first President of the European Commission (d. 1982)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, first <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German academic and politician, first <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>stein"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1901", "text": "<PERSON>, Ukrainian-American actor and director (d. 1982)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American actor and director (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American actor and director (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Hungarian physicist and mathematician, Nobel Prize laureate (d. 1995)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1904", "text": "<PERSON><PERSON>, American sculptor and architect (d. 1988)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sculptor and architect (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sculptor and architect (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON> of Sweden (d. 1935)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/Astrid_of_Sweden\" title=\"<PERSON>tri<PERSON> of Sweden\"><PERSON><PERSON><PERSON> of Sweden</a> (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Sweden\" title=\"<PERSON><PERSON><PERSON> of Sweden\"><PERSON><PERSON><PERSON> of Sweden</a> (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Astrid_of_Sweden"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Russian-American actor (d. 1967)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American actor (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American actor (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Australian cricketer (d. 1987)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON>, Japanese engineer and businessman, co-founded the Honda Motor Company (d. 1991)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Soichiro_Honda\" title=\"Soichiro Honda\"><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda Motor Company</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soichiro_Honda\" title=\"Soichiro Honda\"><PERSON><PERSON><PERSON></a>, Japanese engineer and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Honda\" title=\"Honda\">Honda Motor Company</a> (d. 1991)", "links": [{"title": "<PERSON><PERSON><PERSON> Honda", "link": "https://wikipedia.org/wiki/<PERSON>ichiro_Honda"}, {"title": "Honda", "link": "https://wikipedia.org/wiki/Honda"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American baseball player (d. 2007)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English occultist and author (d. 1985)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Israel_Regardie\" title=\"Israel Regardie\">Israel Regardie</a>, English occultist and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Regardie\" title=\"Israel Regardie\">Israel Regardie</a>, English occultist and author (d. 1985)", "links": [{"title": "Israel Regardie", "link": "https://wikipedia.org/wiki/Israel_Regardie"}]}, {"year": "1911", "text": "<PERSON>, French lawyer and politician, French Minister of the Interior (d. 1974)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France\" class=\"mw-redirect\" title=\"List of Interior Ministers of France\">French Minister of the Interior</a> (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>chet"}, {"title": "List of Interior Ministers of France", "link": "https://wikipedia.org/wiki/List_of_Interior_Ministers_of_France"}]}, {"year": "1916", "text": "<PERSON>, American historian and author (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American mathematician (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Korean painter and educator (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean painter and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean painter and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Luxembourgian singer-songwriter (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n\" title=\"<PERSON><PERSON> Felgen\"><PERSON><PERSON></a>, Luxembourgian singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cam<PERSON>_Felgen\" title=\"<PERSON><PERSON> Felgen\"><PERSON><PERSON></a>, Luxembourgian singer-songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1920", "text": "<PERSON>, Indian actor and director (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ganesan\" title=\"Gemini Ganesan\"><PERSON></a>, Indian actor and director (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ganesan\" title=\"Gemini Ganesan\"><PERSON></a>, Indian actor and director (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Danish painter and illustrator (d. 2019)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish painter and illustrator (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>(biochemist)\" title=\"<PERSON> (biochemist)\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(biochemist)\" title=\"<PERSON> (biochemist)\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON> (biochemist)", "link": "https://wikipedia.org/wiki/<PERSON>(biochemist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1922", "text": "<PERSON>, English footballer (d. 1993)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Swedish bishop (d. 2009)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish bishop (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish bishop (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American baseball player (d. 1986)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1923)\" title=\"<PERSON> (baseball, born 1923)\"><PERSON></a>, American baseball player (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1923)\" title=\"<PERSON> (baseball, born 1923)\"><PERSON></a>, American baseball player (d. 1986)", "links": [{"title": "<PERSON> (baseball, born 1923)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1923)"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Cape Verdean politician, first President of Cape Verde (d. 2011)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cape Verdean politician, first <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cape Verdean politician, first <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>st<PERSON>_Pereira"}, {"title": "President of Cape Verde", "link": "https://wikipedia.org/wiki/President_of_Cape_Verde"}]}, {"year": "1923", "text": "<PERSON>, New Zealand cricketer and coach (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and coach (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American baseball player and bowler (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and bowler (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and bowler (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Rock_Hudson\" title=\"Rock Hudson\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rock_Hudson\" title=\"Rock Hudson\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "Rock Hudson", "link": "https://wikipedia.org/wiki/Rock_Hudson"}]}, {"year": "1925", "text": "<PERSON>, American-Australian oboe player and conductor (d. 2010)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and conductor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and conductor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Oboe", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American clarinet player and composer (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player and composer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, English actress (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fielding"}]}, {"year": "1927", "text": "<PERSON>, Canadian geologist, businessman, and politician (d. 2020) ", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian geologist, businessman, and politician (d. 2020) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Canadian geologist, businessman, and politician (d. 2020) ", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, French-American painter and sculptor (d. 2005)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American painter and sculptor (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American painter and sculptor (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American actor, producer, and screenwriter (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, producer, and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, producer, and screenwriter (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Australian cricketer (d. 2021)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)\" title=\"<PERSON> (Australian cricketer)\"><PERSON></a>, Australian cricketer (d. 2021)", "links": [{"title": "<PERSON> (Australian cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_cricketer)"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Japanese actor and director (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Gor%C5%8D_Naya\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gor%C5%8D_Naya\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actor and director (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gor%C5%8D_Naya"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American baseball player (d. 1999)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American decathlete, actor, and politician (d. 2006)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete, actor, and politician (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete, actor, and politician (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English admiral (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, English admiral (d. 2015)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1933", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Cuban-American baseball player and scout", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Orlando_Pe%C3%B1a\" title=\"Orlando Peña\"><PERSON></a>, Cuban-American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Pe%C3%B1a\" title=\"Orlando Peña\"><PERSON></a>, Cuban-American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Pe%C3%B1a"}]}, {"year": "1934", "text": "<PERSON>, American soldier and politician, senior senator of Oklahoma (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, senior senator of Oklahoma (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, senior senator of Oklahoma (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian-English Psephologist and academic (d. 2017)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" class=\"mw-redirect\" title=\"<PERSON> (professor)\"><PERSON></a>, Canadian-English <a href=\"https://wikipedia.org/wiki/Psephology\" title=\"Psephology\">Psephologist</a> and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)\" class=\"mw-redirect\" title=\"<PERSON> (professor)\"><PERSON></a>, Canadian-English <a href=\"https://wikipedia.org/wiki/Psephology\" title=\"Psephology\">Psephologist</a> and academic (d. 2017)", "links": [{"title": "<PERSON> (professor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(professor)"}, {"title": "Psephology", "link": "https://wikipedia.org/wiki/Psephology"}]}, {"year": "1934", "text": "<PERSON>, American basketball player (d. 2014)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Austrian skier and actor (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Japanese record producer (d. 2021)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese record producer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese record producer (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, English Roman Catholic bishop", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Cris<PERSON>_<PERSON>llis\" title=\"Crispian Hollis\"><PERSON><PERSON><PERSON></a>, English Roman Catholic bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cris<PERSON>_<PERSON>llis\" title=\"Crispian Hollis\"><PERSON><PERSON><PERSON></a>, English Roman Catholic bishop", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English comedian, actor, and screenwriter (d. 1995)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and screenwriter (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Baron <PERSON> of Craigiebank, Scottish general", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Craigiebank\" title=\"<PERSON>, Baron <PERSON> of Craigiebank\"><PERSON>, Baron <PERSON> of Craigiebank</a>, Scottish general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Craigiebank\" title=\"<PERSON>, Baron <PERSON> of Craigiebank\"><PERSON>, Baron <PERSON> of Craigiebank</a>, Scottish general", "links": [{"title": "<PERSON>, <PERSON> of Craigiebank", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Craigiebank"}]}, {"year": "1938", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>foot"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, English journalist and author (d. 2001)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Waugh\" title=\"<PERSON>ber<PERSON> Waugh\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Waugh\" title=\"<PERSON>ber<PERSON> Waugh\"><PERSON><PERSON><PERSON></a>, English journalist and author (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Irish singer, folk musician and actor (d. 1984)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, <a href=\"https://wikipedia.org/wiki/Folk_music\" title=\"Folk music\">folk musician</a> and actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, <a href=\"https://wikipedia.org/wiki/Folk_music\" title=\"Folk music\">folk musician</a> and actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Folk music", "link": "https://wikipedia.org/wiki/Folk_music"}]}, {"year": "1942", "text": "<PERSON>, English-Australian runner", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Bangladeshi economist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi economist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English endocrinologist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English endocrinologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English endocrinologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Hungarian-Japanese microbiologist and physician (d. 1993)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Roszt%C3%B3czy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Japanese microbiologist and physician (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Roszt%C3%B3czy\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Japanese microbiologist and physician (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Roszt%C3%B3czy"}]}, {"year": "1942", "text": "<PERSON>, American director, producer, screenwriter, and actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, screenwriter, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American model and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American basketball player and coach", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English-Scottish journalist, academic, and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish journalist, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Scottish journalist, academic, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and musician (d. 1991)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and musician (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor, director, and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Dutch architect and academic, designed the Seattle Central Library", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Seattle_Central_Library\" title=\"Seattle Central Library\">Seattle Central Library</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Seattle_Central_Library\" title=\"Seattle Central Library\">Seattle Central Library</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Seattle Central Library", "link": "https://wikipedia.org/wiki/Seattle_Central_Library"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Canadian-American screenwriter and producer, created Saturday Night Live", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American screenwriter and producer, created <i><a href=\"https://wikipedia.org/wiki/Saturday_Night_Live\" title=\"Saturday Night Live\">Saturday Night Live</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American screenwriter and producer, created <i><a href=\"https://wikipedia.org/wiki/Saturday_Night_Live\" title=\"Saturday Night Live\">Saturday Night Live</a></i>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Saturday Night Live", "link": "https://wikipedia.org/wiki/Saturday_Night_Live"}]}, {"year": "1944", "text": "<PERSON>, American baseball pitcher (d. 2020)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball pitcher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American civil rights activist (d. 1966)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American civil rights activist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American civil rights activist (d. 1966)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1945", "text": "<PERSON>, English journalist and activist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English accountant and politician, British Minister of State for Foreign Affairs", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Foreign_Affairs_(United_Kingdom)\" class=\"mw-redirect\" title=\"Minister of State for Foreign Affairs (United Kingdom)\">British Minister of State for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English accountant and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Foreign_Affairs_(United_Kingdom)\" class=\"mw-redirect\" title=\"Minister of State for Foreign Affairs (United Kingdom)\">British Minister of State for Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Foreign Affairs (United Kingdom)", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Foreign_Affairs_(United_Kingdom)"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American basketball player and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English-French director, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English-French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English-French director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Algerian politician, 8th President of Algeria", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Abdel<PERSON><PERSON>_Tebb<PERSON>ne\" title=\"Abdel<PERSON><PERSON> Tebb<PERSON>ne\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abdel<PERSON><PERSON>_Tebboune\" title=\"Abdel<PERSON><PERSON> Tebb<PERSON>ne\"><PERSON><PERSON><PERSON><PERSON></a>, Algerian politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abdel<PERSON><PERSON>_<PERSON>"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "1946", "text": "<PERSON>, English guitarist and songwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American soldier, lawyer, and politician, 39th Governor of Iowa", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Iowa\" class=\"mw-redirect\" title=\"Governor of Iowa\">Governor of Iowa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Iowa\" class=\"mw-redirect\" title=\"Governor of Iowa\">Governor of Iowa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Iowa", "link": "https://wikipedia.org/wiki/Governor_of_Iowa"}]}, {"year": "1946", "text": "<PERSON>, Dutch-Canadian figure skater and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Petra_B<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petra_B<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_Burka"}]}, {"year": "1947", "text": "<PERSON>, British singer-songwriter, guitarist, and multi-instrumentalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter, guitarist, and multi-instrumentalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer-songwriter, guitarist, and multi-instrumentalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American physician and politician, 79th Governor of Vermont", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 79th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 79th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Vermont", "link": "https://wikipedia.org/wiki/Governor_of_Vermont"}]}, {"year": "1948", "text": "<PERSON>, American guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/East_Bay_Ray\" title=\"East Bay Ray\">East Bay Ray</a>, American guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/East_Bay_Ray\" title=\"East Bay Ray\">East Bay Ray</a>, American guitarist", "links": [{"title": "East Bay Ray", "link": "https://wikipedia.org/wiki/East_Bay_Ray"}]}, {"year": "1948", "text": "<PERSON>, American journalist and television commentator (d. 2024)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and television commentator (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and television commentator (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American businessman and politician, 53rd Speaker of the United States House of Representatives", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 53rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese soldier and politician, eighth Prime Minister of Vietnam", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_T%E1%BA%A5n_D%C5%A9ng\" title=\"Nguyễn Tấn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese soldier and politician, eighth <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam\" class=\"mw-redirect\" title=\"List of Prime Ministers of Vietnam\">Prime Minister of Vietnam</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_T%E1%BA%A5n_D%C5%A9ng\" title=\"Nguyễn Tấn D<PERSON>ng\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese soldier and politician, eighth <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam\" class=\"mw-redirect\" title=\"List of Prime Ministers of Vietnam\">Prime Minister of Vietnam</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_T%E1%BA%A5n_D%C5%A9ng"}, {"title": "List of Prime Ministers of Vietnam", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Vietnam"}]}, {"year": "1949", "text": "<PERSON>, Australian swimmer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, German swimmer (d. 2019)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German swimmer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, German economist and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer, actor, and pilot (d. 1987)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and pilot (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and pilot (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American actor", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish painter and philanthropist (d. 2025)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and philanthropist (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and philanthropist (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Welsh fashion designer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Welsh fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)\" title=\"<PERSON> (fashion designer)\"><PERSON></a>, Welsh fashion designer", "links": [{"title": "<PERSON> (fashion designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(fashion_designer)"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Dutch field hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Ties_Kruize\" title=\"Ties Kruize\"><PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ties_Kruize\" title=\"Ties Kruize\"><PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON>ruize"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Bangladeshi singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1952", "text": "<PERSON>, South African businessman and politician, fifth President of South Africa", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African businessman and politician, fifth <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African businessman and politician, fifth <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Greek footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Tennes\"><PERSON><PERSON></a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer and manager", "links": [{"title": "Babis <PERSON>", "link": "https://wikipedia.org/wiki/Babis_Tennes"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Australian criminal and author (d. 2013)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Chopper_Read\" class=\"mw-redirect\" title=\"Chopper Read\">Cho<PERSON></a>, Australian criminal and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chopper_Read\" class=\"mw-redirect\" title=\"Chopper Read\">Cho<PERSON></a>, Australian criminal and author (d. 2013)", "links": [{"title": "Chopper <PERSON>", "link": "https://wikipedia.org/wiki/Chopper_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English singer-songwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, American actress and activist (d. 2007)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_King\" title=\"<PERSON><PERSON><PERSON> King\"><PERSON><PERSON><PERSON></a>, American actress and activist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> King\"><PERSON><PERSON><PERSON></a>, American actress and activist (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Ukrainian-Canadian ice hockey player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ukrainian-Canadian\" class=\"mw-redirect\" title=\"Ukrainian-Canadian\">Ukrainian-Canadian</a> ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ukrainian-Canadian\" class=\"mw-redirect\" title=\"Ukrainian-Canadian\">Ukrainian-Canadian</a> ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ukrainian-Canadian", "link": "https://wikipedia.org/wiki/Ukrainian-Canadian"}]}, {"year": "1956", "text": "<PERSON><PERSON>, German glider pilot (d. 2006)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German glider pilot (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German glider pilot (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k"}]}, {"year": "1956", "text": "<PERSON>, Scottish politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}]}, {"year": "1957", "text": "<PERSON>, American guitarist and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English footballer and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actor and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Estonian politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English actor and talk show host", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, American drag queen performer, actor, and singer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Ru<PERSON><PERSON>\" title=\"Ru<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u<PERSON><PERSON>\" title=\"<PERSON>u<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Drag_queen\" title=\"Drag queen\">drag queen</a> performer, actor, and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/RuPaul"}, {"title": "Drag queen", "link": "https://wikipedia.org/wiki/Drag_queen"}]}, {"year": "1960", "text": "<PERSON>, American basketball player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American soldier (d. 1985)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American businessman and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer-songwriter and guitarist (d. 2000)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/D%C3%A9d%C3%A9_Fortin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9d%C3%A9_Fortin\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9d%C3%A9_Fortin"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Adrian_Branch\" title=\"Adrian Branch\"><PERSON></a>, American basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adrian_Branch\" title=\"Adrian Branch\"><PERSON></a>, American basketball player and sportscaster", "links": [{"title": "Adrian <PERSON>", "link": "https://wikipedia.org/wiki/Adrian_Branch"}]}, {"year": "1963", "text": "<PERSON>, American novelist and short story writer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and short story writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American novelist and short story writer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American academic and politician, 24th United States National Security Advisor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 24th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 24th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "National Security Advisor (United States)", "link": "https://wikipedia.org/wiki/National_Security_Advisor_(United_States)"}]}, {"year": "1964", "text": "<PERSON>, American baseball player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian jockey", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jockey", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian jockey", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Australian violinist and composer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian violinist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Australian violinist and composer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1966", "text": "<PERSON>, American bassist and composer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1997)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian singer-songwriter and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American guitarist, songwriter, and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Cuban-American model and actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, French actress, director, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Filipino basketball player and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oni<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ta<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ta<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer, producer, and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese voice actor and singer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8Dtar%C5%8D_Okiayu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8Dtar%C5%8D_Okiayu\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese voice actor and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%8Dtar%C5%8D_Okiayu"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Belgian table tennis player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian table tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian table tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English guitarist and songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Australian actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Norwegian handball player, journalist, newspaper editor, and gambling executive", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ton<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian handball player, journalist, newspaper editor, and gambling executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ton<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian handball player, journalist, newspaper editor, and gambling executive", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/To<PERSON><PERSON>_<PERSON>en"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English badminton player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English badminton player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English model and chef", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and chef", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model and chef", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Swedish singer-songwriter and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Cuban baseball player, coach, and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, German footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1973", "text": "<PERSON>, Russian figure skater and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Sierra Leonean-French heptathlete and long jumper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean-French heptathlete and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sierra Leonean-French heptathlete and long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actress and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Spanish comedian and actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish comedian and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Polish mountaineer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish mountaineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/King<PERSON>_<PERSON>anowska"}]}, {"year": "1975", "text": "<PERSON>, Australian cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Brandon Call\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Brandon Call\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress and director", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, South African swimmer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Neethling\"><PERSON><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Neethling\"><PERSON><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>ling"}]}, {"year": "1978", "text": "<PERSON>, Australian rugby league player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Glen_Air\" title=\"Glen Air\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_Air\" title=\"Glen Air\"><PERSON></a>, Australian rugby league player", "links": [{"title": "Glen Air", "link": "https://wikipedia.org/wiki/Glen_Air"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, New Zealand actress and stuntwoman", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Bell\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand actress and stuntwoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Bell\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand actress and stuntwoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Bell"}]}, {"year": "1978", "text": "<PERSON>, Welsh actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Welsh actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1978", "text": "<PERSON>, Canadian actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matthew_Spring"}]}, {"year": "1980", "text": "<PERSON>, American wrestler", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English singer, dancer, and actress (d. 2021)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, dancer, and actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, dancer, and actress (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor, comedian, film critic, internet personality, and filmmaker", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, comedian, film critic, internet personality, and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American actor, comedian, film critic, internet personality, and filmmaker", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Indian cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, New Zealand singer-songwriter and guitarist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian actress, producer, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Bianca\" title=\"Viva Bianca\"><PERSON></a>, Australian actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bianca\" title=\"Viva Bianca\"><PERSON></a>, Australian actress, producer, and screenwriter", "links": [{"title": "Viva Bianca", "link": "https://wikipedia.org/wiki/<PERSON>_Bianca"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American figure skater", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Australian swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1983", "text": "<PERSON>, American author", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American figure skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON>, South Korean model and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-byul"}]}, {"year": "1985", "text": "<PERSON>, Uruguayan footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Malian footballer (d. 2013)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON><PERSON></a>, Malian footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_(footballer,_born_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)\"><PERSON><PERSON><PERSON><PERSON></a>, Malian footballer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, born 1985)", "link": "https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_Camara_(footballer,_born_1985)"}]}, {"year": "1985", "text": "<PERSON>, Swedish journalist", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Carolina_Neurath\" title=\"Carolina Neurath\"><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carolina_Neurath\" title=\"Carolina Neurath\"><PERSON></a>, Swedish journalist", "links": [{"title": "Carolina Neurath", "link": "https://wikipedia.org/wiki/Carolina_Neurath"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Nicaraguan baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Ever<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cabrera\"><PERSON><PERSON></a>, Nicaraguan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cabrera\"><PERSON><PERSON></a>, Nicaraguan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Italian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>abi<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1986", "text": "<PERSON>, English long jumper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, English actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fforth\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>fforth\" title=\"<PERSON>\"><PERSON></a>, English swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>fforth"}]}, {"year": "1988", "text": "<PERSON>, American actor", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)\" title=\"<PERSON> (quarterback)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (quarterback)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(quarterback)"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Ukrainian footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Roman_Zozulya\" title=\"Roman Zozulya\"><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Zozulya\" title=\"Roman Zozulya\"><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Zozulya"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Venezuelan baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/El%C3%ADas_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C3%ADas_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/El%C3%ADas_D%C3%ADaz"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Brazilian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kat<PERSON><PERSON><PERSON>_Kawa"}]}, {"year": "1992", "text": "<PERSON>, Australian synchronised swimmer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian synchronised swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian synchronised swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American snowboarder", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Taylor_Gold\" title=\"Taylor Gold\"><PERSON></a>, American snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taylor_Gold\" title=\"Taylor Gold\"><PERSON></a>, American snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actress and singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>-<PERSON>, British actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Belgian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Scottish rugby union player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish rugby union player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>-<PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Croatian basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Norwegian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Yugyeom\" title=\"Yugyeom\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yugyeom\" title=\"<PERSON>gyeo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>gyeom"}]}, {"year": "2000", "text": "<PERSON>, Swiss tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCger\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Joanne_Z%C3%BCger"}]}, {"year": "2004", "text": "<PERSON>, Czech tennis player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Linda_Noskov%C3%A1"}]}], "Deaths": [{"year": "375", "text": "<PERSON><PERSON><PERSON>, Roman emperor (b. 321)", "html": "375 - <a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\"><PERSON><PERSON><PERSON> I</a>, Roman emperor (b. 321)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valentinian_I\" title=\"Valentinian I\"><PERSON>ntin<PERSON> I</a>, Roman emperor (b. 321)", "links": [{"title": "Valentinian I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I"}]}, {"year": "594", "text": "<PERSON> of Tours, Roman bishop and saint (b. 538)", "html": "594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tours\" title=\"<PERSON> of Tours\"><PERSON> of Tours</a>, Roman bishop and saint (b. 538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tours\" title=\"<PERSON> of Tours\"><PERSON> of Tours</a>, Roman bishop and saint (b. 538)", "links": [{"title": "Gregory of Tours", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Tours"}]}, {"year": "641", "text": "Emperor <PERSON><PERSON> of Japan (b. 593)", "html": "641 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 593)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 593)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "885", "text": "<PERSON><PERSON><PERSON> of Saxony (b. 845)", "html": "885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_885)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Saxony (died 885)\"><PERSON><PERSON><PERSON> of Saxony</a> (b. 845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_885)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Saxony (died 885)\"><PERSON><PERSON><PERSON> of Saxony</a> (b. 845)", "links": [{"title": "<PERSON><PERSON><PERSON> of Saxony (died 885)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Saxony_(died_885)"}]}, {"year": "935", "text": "<PERSON>, empress of Min (b. 893)", "html": "935 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/<PERSON>_(Ten_Kingdoms)\" title=\"<PERSON> (Ten Kingdoms)\">Min</a> (b. 893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Empress <PERSON>\"><PERSON></a>, empress of <a href=\"https://wikipedia.org/wiki/<PERSON>_(Ten_Kingdoms)\" title=\"<PERSON> (Ten Kingdoms)\">Min</a> (b. 893)", "links": [{"title": "Empress <PERSON>", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_<PERSON>"}, {"title": "Min (Ten Kingdoms)", "link": "https://wikipedia.org/wiki/<PERSON>_(Ten_Kingdoms)"}]}, {"year": "935", "text": "<PERSON>, emperor of Min (Ten Kingdoms)", "html": "935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of Min (<a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, emperor of Min (<a href=\"https://wikipedia.org/wiki/Ten_Kingdoms\" class=\"mw-redirect\" title=\"Ten Kingdoms\">Ten Kingdoms</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ten Kingdoms", "link": "https://wikipedia.org/wiki/Ten_Kingdoms"}]}, {"year": "1104", "text": "<PERSON><PERSON><PERSON><PERSON>, Byzantine general (b. 1045)", "html": "1104 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine general (b. 1045)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Byzantine general (b. 1045)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON><PERSON>_<PERSON>nos"}]}, {"year": "1188", "text": "<PERSON><PERSON> ibn <PERSON>, Arab chronicler (b. 1095)", "html": "1188 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn Mu<PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Arab chronicler (b. 1095)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn Mu<PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Arab chronicler (b. 1095)", "links": [{"title": "<PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1231", "text": "<PERSON> of Hungary (b. 1207)", "html": "1231 - <a href=\"https://wikipedia.org/wiki/Elizabeth_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1207)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elizabeth_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> (b. 1207)", "links": [{"title": "Elizabeth of Hungary", "link": "https://wikipedia.org/wiki/Elizabeth_of_Hungary"}]}, {"year": "1307", "text": "<PERSON><PERSON><PERSON> <PERSON>, King of Armenia (b. 1266)", "html": "1307 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_II,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II, King of Armenia\"><PERSON><PERSON><PERSON> II, King of Armenia</a> (b. 1266)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_II,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON>, King of Armenia\"><PERSON><PERSON><PERSON> II, King of Armenia</a> (b. 1266)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/Heth<PERSON>_<PERSON>,_King_of_Armenia"}]}, {"year": "1307", "text": "<PERSON>, King of Armenia (b. 1289)", "html": "1307 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON>, King of Armenia\"><PERSON>, King of Armenia</a> (b. 1289)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON>, King of Armenia\"><PERSON>, King of Armenia</a> (b. 1289)", "links": [{"title": "<PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia"}]}, {"year": "1326", "text": "<PERSON>, 9th Earl of Arundel, English politician (b. 1285)", "html": "1326 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 9th Earl of Arundel\"><PERSON>, 9th Earl of Arundel</a>, English politician (b. 1285)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel\" class=\"mw-redirect\" title=\"<PERSON>, 9th Earl of Arundel\"><PERSON>, 9th Earl of Arundel</a>, English politician (b. 1285)", "links": [{"title": "<PERSON>, 9th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Earl_of_Arundel"}]}, {"year": "1417", "text": "<PERSON><PERSON>, Ottoman general (b. 1288)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/Gazi_Evrenos\" class=\"mw-redirect\" title=\"Gazi Evrenos\"><PERSON><PERSON></a>, Ottoman general (b. 1288)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gazi_Evrenos\" class=\"mw-redirect\" title=\"Gazi Evrenos\"><PERSON><PERSON></a>, Ottoman general (b. 1288)", "links": [{"title": "Gazi <PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>_<PERSON>"}]}, {"year": "1492", "text": "<PERSON><PERSON>, Persian poet and saint (b. 1414)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian poet and saint (b. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Persian poet and saint (b. 1414)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jami"}]}, {"year": "1494", "text": "<PERSON>, Italian philosopher and author (b. 1463)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Mirandola\" title=\"<PERSON> della Mirandola\"><PERSON>ndo<PERSON></a>, Italian philosopher and author (b. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Mirandola\" title=\"<PERSON> Mirandola\"><PERSON> Mirandola</a>, Italian philosopher and author (b. 1463)", "links": [{"title": "Giovanni <PERSON> Mirando<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>ndola"}]}, {"year": "1525", "text": "<PERSON> Viseu, queen of <PERSON> II of Portugal (b. 1458)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Viseu\" title=\"<PERSON> of Viseu\"><PERSON> of Viseu</a>, queen of <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_II_of_Portugal\" class=\"mw-redirect\" title=\"João II of Portugal\"><PERSON> II of Portugal</a> (b. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Vise<PERSON>\" title=\"<PERSON> of Viseu\"><PERSON> of Viseu</a>, queen of <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_II_of_Portugal\" class=\"mw-redirect\" title=\"João II of Portugal\"><PERSON> of Portugal</a> (b. 1458)", "links": [{"title": "<PERSON> of Viseu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> II of Portugal", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_II_of_Portugal"}]}, {"year": "1558", "text": "<PERSON> of England (b. 1516)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> (b. 1516)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1558", "text": "<PERSON>, English cardinal and academic (b. 1500)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and academic (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal and academic (b. 1500)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1558", "text": "<PERSON>, English composer (b. 1485)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer (b. 1485)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hugh <PERSON>\"><PERSON></a>, English composer (b. 1485)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON> of Navarre (b. 1518)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1518)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1518)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/Antoine_of_Navarre"}]}, {"year": "1592", "text": "<PERSON> of Sweden (b. 1537)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON> of Sweden</a> (b. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1537)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1600", "text": "<PERSON><PERSON>, Japanese commander (b. 1542)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese commander (b. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese commander (b. 1542)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1624", "text": "<PERSON>, German mystic (b. 1575)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6hme\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (b. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6hme\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/German_mystic\" class=\"mw-redirect\" title=\"German mystic\">German mystic</a> (b. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jakob_B%C3%B6hme"}, {"title": "German mystic", "link": "https://wikipedia.org/wiki/German_mystic"}]}, {"year": "1632", "text": "<PERSON><PERSON><PERSON> zu Pappenheim, Bavarian field marshal (b. 1594)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Pappenheim\" title=\"<PERSON><PERSON><PERSON> zu Pappenheim\"><PERSON><PERSON><PERSON> zu Pappenheim</a>, Bavarian field marshal (b. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_Pappenheim\" title=\"<PERSON><PERSON><PERSON> zu Pappenheim\"><PERSON><PERSON><PERSON> zu Pappenheim</a>, Bavarian field marshal (b. 1594)", "links": [{"title": "<PERSON><PERSON><PERSON> Pappenheim", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1643", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, French general (b. 1602)", "html": "1643 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Gu%C3%A9briant\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French general (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_Gu%C3%A9briant\" title=\"<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON></a>, French general (b. 1602)", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>u%C3%A9briant"}]}, {"year": "1648", "text": "<PERSON>, English viol player, composer, and poet (b. 1580)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English viol player, composer, and poet (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English viol player, composer, and poet (b. 1580)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1665", "text": "<PERSON>, English bishop (b. 1601)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop (b. 1601)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1668", "text": "<PERSON>, English pastor and author (b. 1634)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pastor and author (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, duc <PERSON>, French general and politician (b. 1610)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French general and politician (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French general and politician (b. 1610)", "links": [{"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>"}]}, {"year": "1708", "text": "<PERSON><PERSON><PERSON>, German-Dutch painter (b. 1631)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Dutch painter (b. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Dutch painter (b. 1631)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, South African-Indonesian merchant and politician, Governor-General of the Dutch East Indies (b. 1653)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Indonesian merchant and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (b. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-Indonesian merchant and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (b. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of the Dutch East Indies", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies"}]}, {"year": "1747", "text": "<PERSON><PERSON><PERSON>, French author and playwright (b. 1668)", "html": "1747 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (b. 1668)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (b. 1668)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON>age"}]}, {"year": "1768", "text": "<PERSON>, 1st Duke of Newcastle, English lawyer and politician, Prime Minister of Great Britain (b. 1693)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (b. 1693)", "links": [{"title": "<PERSON>, 1st Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Newcastle"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1776", "text": "<PERSON>, Scottish astronomer and instrument maker (b. 1710)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)\" title=\"<PERSON> (Scottish astronomer)\"><PERSON></a>, Scottish astronomer and instrument maker (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)\" title=\"<PERSON> (Scottish astronomer)\"><PERSON></a>, Scottish astronomer and instrument maker (b. 1710)", "links": [{"title": "<PERSON> (Scottish astronomer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_astronomer)"}]}, {"year": "1780", "text": "<PERSON>, Italian painter and illustrator (b. 1720)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, of Russia (b. 1729)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>, of Russia (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a>, of Russia (b. 1729)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, Czech-American pastor and missionary (b. 1721)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pastor and missionary (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-American pastor and missionary (b. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, English Insurance underwriter and founder of The Times newspaper (b. 1738/1739)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English Insurance underwriter and founder of <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> newspaper (b. 1738/1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(publisher)\" title=\"<PERSON> (publisher)\"><PERSON></a>, English Insurance underwriter and founder of <i><a href=\"https://wikipedia.org/wiki/The_Times\" title=\"The Times\">The Times</a></i> newspaper (b. 1738/1739)", "links": [{"title": "<PERSON> (publisher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(publisher)"}, {"title": "The Times", "link": "https://wikipedia.org/wiki/The_Times"}]}, {"year": "1818", "text": "<PERSON> of Mecklenburg-Strelitz (b. 1744)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz\" title=\"<PERSON> of Mecklenburg-Strelitz\"><PERSON> of Mecklenburg-Strelitz</a> (b. 1744)", "links": [{"title": "<PERSON> of Mecklenburg-Strelitz", "link": "https://wikipedia.org/wiki/Charlotte_of_Mecklenburg-Strelitz"}]}, {"year": "1835", "text": "<PERSON><PERSON>, French painter and lithographer (b. 1758)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and lithographer (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and lithographer (b. 1758)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American physician and author (b. 1813)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and author (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American pastor and theologian (b. 1820)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and theologian (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, Welsh theologian and educator (b. 1847)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh theologian and educator (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh theologian and educator (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON>, Grand Duke of Luxembourg, (b. 1817)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Luxembourg\"><PERSON><PERSON><PERSON>, Grand Duke of Luxembourg</a>, (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Grand_Duke_of_Luxembourg\" title=\"<PERSON><PERSON><PERSON>, Grand Duke of Luxembourg\"><PERSON><PERSON><PERSON>, Grand Duke of Luxembourg</a>, (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON>, Grand Duke of Luxembourg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Grand_Duke_of_Luxembourg"}]}, {"year": "1910", "text": "<PERSON>, American pilot (b. 1886)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French sculptor and illustrator (b. 1840)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor and illustrator (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Swiss lawyer and politician, 29th President of the Swiss Confederation (b. 1847)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 29th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1923", "text": "<PERSON>, Estonian author (b. 1862)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6he\" title=\"<PERSON>\"><PERSON></a>, Estonian author (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6he\" title=\"<PERSON>\"><PERSON></a>, Estonian author (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6he"}]}, {"year": "1924", "text": "<PERSON> of Constantinople (b. 1850)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Constantinople\" title=\"Gregory VII of Constantinople\"><PERSON> of Constantinople</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_VII_of_Constantinople\" title=\"Gregory VII of Constantinople\"><PERSON> VII of Constantinople</a> (b. 1850)", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/Gregory_<PERSON>_of_Constantinople"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Indian author and politician (b. 1865)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and politician (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and politician (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_Rai"}]}, {"year": "1929", "text": "<PERSON>, American statistician and businessman (b. 1860)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and businessman (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American statistician and businessman (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American lawyer, author, and activist (b. 1858)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and activist (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and activist (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, German-American singer (b. 1861)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American singer (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American singer (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>ink"}]}, {"year": "1937", "text": "<PERSON>, Australian footballer, cricketer, and coach (b. 1860)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, and coach (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer, cricketer, and coach (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Croatian lawyer and politician, 20th Mayor of Split (b. 1864)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/An<PERSON>_T<PERSON>bi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Mayor_of_Split\" title=\"Mayor of Split\">Mayor of Split</a> (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON>bi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/Mayor_of_Split\" title=\"Mayor of Split\">Mayor of Split</a> (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ante_Trumbi%C4%87"}, {"title": "Mayor of Split", "link": "https://wikipedia.org/wiki/Mayor_of_Split"}]}, {"year": "1940", "text": "<PERSON>, English sculptor and typeface designer (b. 1882)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and typeface designer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor and typeface designer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American biologist and academic (b. 1879)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and academic (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Russian historian and author (b. 1890)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and author (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian and author (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Russian-Israeli poet and journalist (b. 1899)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli poet and journalist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli poet and journalist (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American pianist and composer (b. 1894)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American baseball player (b. 1913)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Cooper\"><PERSON><PERSON></a>, American baseball player (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Brazilian guitarist and composer (b. 1887)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>itor_Villa-Lobos\" title=\"<PERSON>itor Villa-Lobos\"><PERSON><PERSON>Lo<PERSON></a>, Brazilian guitarist and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>itor_Villa-Lobos\" title=\"<PERSON>itor Villa-Lobos\"><PERSON><PERSON></a>, Brazilian guitarist and composer (b. 1887)", "links": [{"title": "<PERSON>itor <PERSON>", "link": "https://wikipedia.org/wiki/Heitor_Villa-Lobos"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, English poet, author, and illustrator (b. 1911)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet, author, and illustrator (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English poet, author, and illustrator (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Bengali politician (b. 1876)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali politician (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengali politician (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1971", "text": "<PERSON>, English actress (b. 1888)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, French-Indian spiritual leader (b. 1878)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Indian spiritual leader (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-Indian spiritual leader (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Bangladeshi scholar and politician (b. 1880)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi scholar and politician (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi scholar and politician (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English singer and bass player  (b. 1951)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Estonian composer and conductor (b. 1905)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer and conductor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian composer and conductor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, French businessman (b. 1927)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French businessman (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American baseball player (b. 1906)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, English-American actress, author, and journalist (b. 1904)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actress, author, and journalist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American actress, author, and journalist (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American criminal (b. 1960)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Costa<PERSON>e_Farace\" title=\"<PERSON><PERSON><PERSON> Farace\"><PERSON><PERSON><PERSON></a>, American criminal (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Costa<PERSON><PERSON>_Farace\" title=\"<PERSON><PERSON><PERSON> Farace\"><PERSON><PERSON><PERSON></a>, American criminal (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Costabile_Farace"}]}, {"year": "1990", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1915)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American poet, essayist, memoirist, and activist (b. 1934)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, essayist, memoirist, and activist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, essayist, memoirist, and activist (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Canadian lawyer and politician, fifth Deputy Premier of Quebec (b. 1926)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_D._Levesque\" title=\"Gérard D. Levesque\"><PERSON><PERSON></a>, Canadian lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_D._Levesque\" title=\"Gérard D. Levesque\"><PERSON><PERSON></a>, Canadian lawyer and politician, fifth <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_D._Levesque"}, {"title": "Deputy Premier of Quebec", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_Quebec"}]}, {"year": "1995", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1945)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Dutch tennis player (b. 1903)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch tennis player (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch tennis player (b. 1903)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, French physicist and academic, Nobel Prize laureate (b. 1904)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Louis_N%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_N%C3%A9el\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_N%C3%A9el"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2001", "text": "<PERSON>, German guitarist and songwriter (b. 1948)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist and songwriter (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German guitarist and songwriter (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American lieutenant, lawyer, and politician (b. 1919)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and politician (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, South African-Israeli soldier and politician, third Israeli Minister of Foreign Affairs (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Eban\"><PERSON><PERSON></a>, South African-Israeli soldier and politician, third <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)\" title=\"Ministry of Foreign Affairs (Israel)\">Israeli Minister of Foreign Affairs</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-Israeli soldier and politician, third <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)\" title=\"Ministry of Foreign Affairs (Israel)\">Israeli Minister of Foreign Affairs</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON>ban"}, {"title": "Ministry of Foreign Affairs (Israel)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Israel)"}]}, {"year": "2002", "text": "<PERSON>, American painter and illustrator (b. 1924)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and illustrator (b. 1924)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(artist)"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Indian singer (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (b. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ia"}]}, {"year": "2003", "text": "<PERSON>, American-Dutch singer-songwriter (b. 1946)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Dutch singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Dutch singer-songwriter (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Swedish wrestler and manager (b. 1970)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish wrestler and manager (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish wrestler and manager (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Russian ice hockey player (b. 1941)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Polish actor and director (b. 1942)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor and director (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor and director (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter and actress (b. 1928)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer and manager (b. 1927)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>renc_<PERSON>%C3%A1s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer and manager (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferenc_Pusk%C3%A1s"}]}, {"year": "2006", "text": "<PERSON>, American football player and coach (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Estonian chess player (b. 1940)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian chess player (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American admiral (b. 1919)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American basketball player and coach (b. 1915)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American basketball player and coach (b. 1961)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian businessman and philanthropist (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ponty_Chadha\" title=\"Ponty Chadha\"><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ponty_Chadha\" title=\"Ponty Chadha\"><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ponty_Chadha"}]}, {"year": "2012", "text": "<PERSON>, Belgian cyclist (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Hungarian-Israeli fashion designer, founded the Gottex Company (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ie<PERSON>\" title=\"<PERSON> Gottlieb\"><PERSON></a>, Hungarian-Israeli fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Gottex\" title=\"Gottex\">Gottex Company</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Gottlieb\"><PERSON></a>, Hungarian-Israeli fashion designer, founded the <a href=\"https://wikipedia.org/wiki/Gottex\" title=\"Gottex\">Gottex Company</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gottex", "link": "https://wikipedia.org/wiki/Gottex"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American singer-songwriter (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian cartoonist and politician (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Bal_Thackeray\" title=\"Bal Thackeray\"><PERSON><PERSON></a>, Indian cartoonist and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal_Thackeray\" title=\"Bal Thackeray\"><PERSON><PERSON></a>, Indian cartoonist and politician (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bal_Thackeray"}]}, {"year": "2012", "text": "<PERSON>, English author (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bella\"><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English colonel and lawyer (b. 1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and lawyer (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and lawyer (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American screenwriter and producer (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Syd_<PERSON>\" title=\"Syd Field\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yd_<PERSON>\" title=\"Syd Field\"><PERSON><PERSON></a>, American screenwriter and producer (b. 1935)", "links": [{"title": "Syd Field", "link": "https://wikipedia.org/wiki/Syd_Field"}]}, {"year": "2013", "text": "<PERSON>, British novelist, poet, playwright, Nobel Prize laureate (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British novelist, poet, playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "2013", "text": "<PERSON>, Portuguese footballer (b. 1993)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American CIA agent and judge (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> agent and judge (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> agent and judge (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "2014", "text": "<PERSON>, American lieutenant and politician (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American psychologist and philosopher (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English lawyer and diplomat, High Commissioner to Australia (b. 1928)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Australia\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to Australia\">High Commissioner to Australia</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, English lawyer and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Australia\" class=\"mw-redirect\" title=\"List of High Commissioners of the United Kingdom to Australia\">High Commissioner to Australia</a> (b. 1928)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}, {"title": "List of High Commissioners of the United Kingdom to Australia", "link": "https://wikipedia.org/wiki/List_of_High_Commissioners_of_the_United_Kingdom_to_Australia"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Iranian poet and songwriter (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and songwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Brazilian race car driver (b. 1982)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> R<PERSON>\"><PERSON><PERSON></a>, Brazilian race car driver (b. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2021", "text": "<PERSON>, American rapper (b. 1985)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Dolph\" title=\"Young Dolph\"><PERSON></a>, American rapper (b. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Dolph\" title=\"Young Dolph\"><PERSON></a>, American rapper (b. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ph"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Japanese manga artist (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1934)", "links": [{"title": "2024", "link": "https://wikipedia.org/wiki/2024"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}