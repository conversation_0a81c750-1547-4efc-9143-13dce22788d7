{"date": "August 1", "url": "https://wikipedia.org/wiki/August_1", "data": {"Events": [{"year": "30 BC", "text": "<PERSON><PERSON><PERSON> (later known as <PERSON>) enters Alexandria, Egypt, bringing it under the control of the Roman Republic.", "html": "30 BC - 30 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> (later known as <PERSON>) enters <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, bringing it under the control of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a>.", "no_year_html": "30 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a> (later known as <PERSON>) enters <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>, <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>, bringing it under the control of the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman Republic</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}]}, {"year": "69", "text": "Batavian rebellion: The Batavians in Germania Inferior (Netherlands) revolt under the leadership of Gaius <PERSON>.", "html": "69 - AD 69 - <a href=\"https://wikipedia.org/wiki/Revolt_of_the_Batavi\" title=\"Revolt of the Batavi\">Batavian rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Batavi_(Germanic_tribe)\" title=\"Batavi (Germanic tribe)\">Batavians</a> in <a href=\"https://wikipedia.org/wiki/Germania_Inferior\" title=\"Germania Inferior\">Germania Inferior</a> (Netherlands) revolt under the leadership of <a href=\"https://wikipedia.org/wiki/Gaius_Julius_<PERSON>is\" title=\"Gaius <PERSON>\"><PERSON></a>.", "no_year_html": "AD 69 - <a href=\"https://wikipedia.org/wiki/Revolt_of_the_Batavi\" title=\"Revolt of the Batavi\">Batavian rebellion</a>: The <a href=\"https://wikipedia.org/wiki/Batavi_(Germanic_tribe)\" title=\"Batavi (Germanic tribe)\">Batavians</a> in <a href=\"https://wikipedia.org/wiki/Germania_Inferior\" title=\"Germania Inferior\">Germania Inferior</a> (Netherlands) revolt under the leadership of <a href=\"https://wikipedia.org/wiki/Gaius_Julius_<PERSON>is\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Revolt of the Batavi", "link": "https://wikipedia.org/wiki/Revolt_of_the_Batavi"}, {"title": "Batavi (Germanic tribe)", "link": "https://wikipedia.org/wiki/Batavi_(Germanic_tribe)"}, {"title": "Germania Inferior", "link": "https://wikipedia.org/wiki/Germania_Inferior"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "527", "text": "<PERSON><PERSON> I becomes the sole ruler of the Byzantine Empire.", "html": "527 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Justinian I\"><PERSON><PERSON> I</a> becomes the sole ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Justinian I\"><PERSON><PERSON> I</a> becomes the sole ruler of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "607", "text": "<PERSON><PERSON> <PERSON> is dispatched as envoy to the Sui court in China (Traditional Japanese date: July 3, 607).", "html": "607 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> no Imoko\"><PERSON><PERSON> <PERSON></a> is dispatched as envoy to the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui</a> court in China (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 3, 607).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> no Imoko\"><PERSON><PERSON> <PERSON></a> is dispatched as envoy to the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui</a> court in China (Traditional <a href=\"https://wikipedia.org/wiki/Japanese_calendar\" title=\"Japanese calendar\">Japanese date</a>: July 3, 607).", "links": [{"title": "<PERSON>o no <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}, {"title": "Japanese calendar", "link": "https://wikipedia.org/wiki/Japanese_calendar"}]}, {"year": "902", "text": "Taormina, the last Byzantine stronghold in Sicily, is captured by the Aghlabid army, concluding the Muslim conquest of Sicily.", "html": "902 - <a href=\"https://wikipedia.org/wiki/Taormina\" title=\"Taormina\">Taormina</a>, the last Byzantine stronghold in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, is <a href=\"https://wikipedia.org/wiki/Siege_of_Taormina_(902)\" title=\"Siege of Taormina (902)\">captured</a> by the <a href=\"https://wikipedia.org/wiki/Aghlabid\" class=\"mw-redirect\" title=\"Aghlabid\">Aghlabid</a> army, concluding the <a href=\"https://wikipedia.org/wiki/Muslim_conquest_of_Sicily\" title=\"Muslim conquest of Sicily\">Muslim conquest of Sicily</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taormina\" title=\"Taormina\">Taormina</a>, the last Byzantine stronghold in <a href=\"https://wikipedia.org/wiki/Sicily\" title=\"Sicily\">Sicily</a>, is <a href=\"https://wikipedia.org/wiki/Siege_of_Taormina_(902)\" title=\"Siege of Taormina (902)\">captured</a> by the <a href=\"https://wikipedia.org/wiki/Aghlabid\" class=\"mw-redirect\" title=\"Aghlabid\">Aghlabid</a> army, concluding the <a href=\"https://wikipedia.org/wiki/Muslim_conquest_of_Sicily\" title=\"Muslim conquest of Sicily\">Muslim conquest of Sicily</a>.", "links": [{"title": "Taormina", "link": "https://wikipedia.org/wiki/Taormina"}, {"title": "Sicily", "link": "https://wikipedia.org/wiki/Sicily"}, {"title": "Siege of Taormina (902)", "link": "https://wikipedia.org/wiki/Siege_of_Taormina_(902)"}, {"title": "A<PERSON>labid", "link": "https://wikipedia.org/wiki/Aghlabid"}, {"title": "Muslim conquest of Sicily", "link": "https://wikipedia.org/wiki/Muslim_conquest_of_Sicily"}]}, {"year": "1203", "text": "<PERSON>, restored Byzantine Emperor, declares his son <PERSON><PERSON> co-emperor after pressure from the forces of the Fourth Crusade.", "html": "1203 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II <PERSON>\"><PERSON></a>, restored <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>, declares his son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV_Angelos\" title=\"<PERSON>ios IV Angelos\"><PERSON><PERSON> IV <PERSON>s</a> co-emperor after pressure from the forces of the <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> II Angelos\"><PERSON></a>, restored <a href=\"https://wikipedia.org/wiki/Byzantine_Emperor\" class=\"mw-redirect\" title=\"Byzantine Emperor\">Byzantine Emperor</a>, declares his son <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_IV_Angelos\" title=\"<PERSON>ios IV Angelos\"><PERSON><PERSON> IV <PERSON>s</a> co-emperor after pressure from the forces of the <a href=\"https://wikipedia.org/wiki/Fourth_Crusade\" title=\"Fourth Crusade\">Fourth Crusade</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Byzantine Emperor", "link": "https://wikipedia.org/wiki/Byzantine_Emperor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alexios_IV_Angelos"}, {"title": "Fourth Crusade", "link": "https://wikipedia.org/wiki/Fourth_Crusade"}]}, {"year": "1291", "text": "The Old Swiss Confederacy is formed with the signature of the Federal Charter.", "html": "1291 - The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> is formed with the signature of the <a href=\"https://wikipedia.org/wiki/Federal_Charter_of_1291\" title=\"Federal Charter of 1291\">Federal Charter</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Old_Swiss_Confederacy\" title=\"Old Swiss Confederacy\">Old Swiss Confederacy</a> is formed with the signature of the <a href=\"https://wikipedia.org/wiki/Federal_Charter_of_1291\" title=\"Federal Charter of 1291\">Federal Charter</a>.", "links": [{"title": "Old Swiss Confederacy", "link": "https://wikipedia.org/wiki/Old_Swiss_Confederacy"}, {"title": "Federal Charter of 1291", "link": "https://wikipedia.org/wiki/Federal_Charter_of_1291"}]}, {"year": "1469", "text": "<PERSON> XI of France founds the chivalric order called the Order of Saint Michael in Amboise.", "html": "1469 - <a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\">Louis XI of France</a> founds the <a href=\"https://wikipedia.org/wiki/Chivalric_order\" class=\"mw-redirect\" title=\"Chivalric order\">chivalric order</a> called the <a href=\"https://wikipedia.org/wiki/Order_of_Saint_Michael\" title=\"Order of Saint Michael\">Order of Saint Michael</a> in <a href=\"https://wikipedia.org/wiki/Amboise\" title=\"Amboise\">Amboise</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XI_of_France\" class=\"mw-redirect\" title=\"Louis XI of France\">Louis XI of France</a> founds the <a href=\"https://wikipedia.org/wiki/Chivalric_order\" class=\"mw-redirect\" title=\"Chivalric order\">chivalric order</a> called the <a href=\"https://wikipedia.org/wiki/Order_of_Saint_Michael\" title=\"Order of Saint Michael\">Order of Saint Michael</a> in <a href=\"https://wikipedia.org/wiki/Amboise\" title=\"Amboise\">Amboise</a>.", "links": [{"title": "Louis XI of France", "link": "https://wikipedia.org/wiki/Louis_XI_of_France"}, {"title": "Chivalric order", "link": "https://wikipedia.org/wiki/Chivalric_order"}, {"title": "Order of Saint Michael", "link": "https://wikipedia.org/wiki/Order_of_Saint_Michael"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amboise"}]}, {"year": "1498", "text": "<PERSON> becomes the first European to visit what is now Venezuela.", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to visit what is now <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> becomes the first European to visit what is now <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "1571", "text": "The Ottoman conquest of Cyprus is concluded, by the surrender of Famagusta.", "html": "1571 - The <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1570%E2%80%9373)#Ottoman_conquest_of_Cyprus\" class=\"mw-redirect\" title=\"Ottoman-Venetian War (1570-73)\">Ottoman conquest of Cyprus</a> is concluded, by the surrender of <a href=\"https://wikipedia.org/wiki/Famagusta\" title=\"Famagusta\">Famagusta</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1570%E2%80%9373)#Ottoman_conquest_of_Cyprus\" class=\"mw-redirect\" title=\"Ottoman-Venetian War (1570-73)\">Ottoman conquest of Cyprus</a> is concluded, by the surrender of <a href=\"https://wikipedia.org/wiki/Famagusta\" title=\"Famagusta\">Famagusta</a>.", "links": [{"title": "Ottoman-Venetian War (1570-73)", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Venetian_War_(1570%E2%80%9373)#Ottoman_conquest_of_Cyprus"}, {"title": "Famagusta", "link": "https://wikipedia.org/wiki/Famagusta"}]}, {"year": "1620", "text": "<PERSON><PERSON> leaves Delfshaven to bring pilgrims to America by way of England.", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Speedwell_(1577_ship)\" title=\"Speedwell (1577 ship)\"><i>Speedwell</i></a> leaves <a href=\"https://wikipedia.org/wiki/Delfshaven\" title=\"Delfshaven\">Delfshaven</a> to bring <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">pilgrims</a> to <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">America</a> by way of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Speedwell_(1577_ship)\" title=\"Speedwell (1577 ship)\"><i>Speedwell</i></a> leaves <a href=\"https://wikipedia.org/wiki/Delfshaven\" title=\"Delfshaven\">Delfshaven</a> to bring <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">pilgrims</a> to <a href=\"https://wikipedia.org/wiki/Plymouth_Colony\" title=\"Plymouth Colony\">America</a> by way of England.", "links": [{"title": "Speedwell (1577 ship)", "link": "https://wikipedia.org/wiki/Speedwell_(1577_ship)"}, {"title": "Delfshaven", "link": "https://wikipedia.org/wiki/Delfshaven"}, {"title": "Pilgrim Fathers", "link": "https://wikipedia.org/wiki/Pilgrim_Fathers"}, {"title": "Plymouth Colony", "link": "https://wikipedia.org/wiki/Plymouth_Colony"}]}, {"year": "1664", "text": "Ottoman forces are defeated in the battle of Saint Gotthard by an Austrian army led by <PERSON><PERSON><PERSON>, resulting in the Peace of Vasvár.", "html": "1664 - <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman forces</a> are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Saint_Gotthard_(1664)\" title=\"Battle of Saint Gotthard (1664)\">battle of Saint Gotthard</a> by an <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> army led by <a href=\"https://wikipedia.org/wiki/Rai<PERSON><PERSON>_Monte<PERSON>li\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, resulting in the <a href=\"https://wikipedia.org/wiki/Peace_of_Vasv%C3%A1r\" title=\"Peace of Vasvár\">Peace of Vasvár</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman forces</a> are defeated in the <a href=\"https://wikipedia.org/wiki/Battle_of_Saint_Gotthard_(1664)\" title=\"Battle of Saint Gotthard (1664)\">battle of Saint Gotthard</a> by an <a href=\"https://wikipedia.org/wiki/Habsburg_monarchy\" title=\"Habsburg monarchy\">Austrian</a> army led by <a href=\"https://wikipedia.org/wiki/Rai<PERSON>o_Montecu<PERSON>li\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, resulting in the <a href=\"https://wikipedia.org/wiki/Peace_of_Vasv%C3%A1r\" title=\"Peace of Vasvár\">Peace of Vasvár</a>.", "links": [{"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Battle of Saint Got<PERSON> (1664)", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON>_<PERSON>_(1664)"}, {"title": "Habsburg monarchy", "link": "https://wikipedia.org/wiki/Habsburg_monarchy"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Peace of Vasvár", "link": "https://wikipedia.org/wiki/Peace_of_Vasv%C3%A1r"}]}, {"year": "1714", "text": "<PERSON>, Elector of Hanover, becomes King <PERSON> of Great Britain, marking the beginning of the Georgian era of British history.", "html": "1714 - <PERSON>, Elector of <a href=\"https://wikipedia.org/wiki/Hanover\" title=\"Hanover\">Hanover</a>, becomes King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> of Great Britain</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Georgian_era\" title=\"Georgian era\">Georgian era</a> of British history.", "no_year_html": "<PERSON>, Elector of <a href=\"https://wikipedia.org/wiki/Hanover\" title=\"Hanover\">Hanover</a>, becomes King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain\" title=\"<PERSON> of Great Britain\"><PERSON> of Great Britain</a>, marking the beginning of the <a href=\"https://wikipedia.org/wiki/Georgian_era\" title=\"Georgian era\">Georgian era</a> of British history.", "links": [{"title": "Hanover", "link": "https://wikipedia.org/wiki/Hanover"}, {"title": "<PERSON> of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Great_Britain"}, {"title": "Georgian era", "link": "https://wikipedia.org/wiki/Georgian_era"}]}, {"year": "1759", "text": "Seven Years' War: The Battle of Minden, an allied Anglo-German army victory over the French. In Britain this was one of a number of events that constituted the Annus Mirabilis of 1759 and is celebrated as Minden Day by certain British Army regiments.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Minden\" title=\"Battle of Minden\">Battle of Minden</a>, an allied Anglo-German army victory over the French. In Britain this was one of a number of events that constituted the <a href=\"https://wikipedia.org/wiki/Annus_Mirabilis_of_1759\" class=\"mw-redirect\" title=\"Annus Mirabilis of 1759\"><PERSON><PERSON>bilis of 1759</a> and is celebrated as <a href=\"https://wikipedia.org/wiki/Minden_Day\" title=\"Minden Day\">Minden Day</a> by certain British Army regiments.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Minden\" title=\"Battle of Minden\">Battle of Minden</a>, an allied Anglo-German army victory over the French. In Britain this was one of a number of events that constituted the <a href=\"https://wikipedia.org/wiki/Annus_Mirabilis_of_1759\" class=\"mw-redirect\" title=\"Annus Mirabilis of 1759\"><PERSON><PERSON> Mirabilis of 1759</a> and is celebrated as <a href=\"https://wikipedia.org/wiki/Minden_Day\" title=\"Minden Day\">Minden Day</a> by certain British Army regiments.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Battle of Minden", "link": "https://wikipedia.org/wiki/Battle_of_Minden"}, {"title": "<PERSON><PERSON> of 1759", "link": "https://wikipedia.org/wiki/Annus_Mirabilis_of_1759"}, {"title": "Minden Day", "link": "https://wikipedia.org/wiki/Minden_Day"}]}, {"year": "1774", "text": "British scientist <PERSON> discovers oxygen gas, corroborating the prior discovery of this element by German-Swedish chemist <PERSON>.", "html": "1774 - British scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Oxygen\" title=\"Oxygen\">oxygen</a> gas, corroborating the prior discovery of this element by German-Swedish chemist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "British scientist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Oxygen\" title=\"Oxygen\">oxygen</a> gas, corroborating the prior discovery of this element by German-Swedish chemist <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Oxygen", "link": "https://wikipedia.org/wiki/Oxygen"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "French Revolutionary Wars: Battle of the Nile (Battle of Aboukir Bay): Battle begins when a British fleet engages the French Revolutionary Navy fleet in an unusual night action.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Nile\" title=\"Battle of the Nile\">Battle of the Nile (Battle of Aboukir Bay)</a>: Battle begins when a British fleet engages the French Revolutionary Navy fleet in an unusual night action.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Nile\" title=\"Battle of the Nile\">Battle of the Nile (Battle of Aboukir Bay)</a>: Battle begins when a British fleet engages the French Revolutionary Navy fleet in an unusual night action.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Battle of the Nile", "link": "https://wikipedia.org/wiki/Battle_of_the_Nile"}]}, {"year": "1800", "text": "The Acts of Union 1800 are passed which merge the Kingdom of Great Britain and the Kingdom of Ireland into the United Kingdom of Great Britain and Ireland.", "html": "1800 - The <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1800\" title=\"Acts of Union 1800\">Acts of Union 1800</a> are passed which merge the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Kingdom of Ireland</a> into the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Acts_of_Union_1800\" title=\"Acts of Union 1800\">Acts of Union 1800</a> are passed which merge the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a> and the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Ireland\" title=\"Kingdom of Ireland\">Kingdom of Ireland</a> into the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "links": [{"title": "Acts of Union 1800", "link": "https://wikipedia.org/wiki/Acts_of_Union_1800"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Kingdom of Ireland", "link": "https://wikipedia.org/wiki/Kingdom_of_Ireland"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1801", "text": "First Barbary War: The American schooner USS Enterprise captures the Tripolitan polacca Tripoli in a single-ship action off the coast of modern-day Libya.", "html": "1801 - <a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: The American <a href=\"https://wikipedia.org/wiki/Schooner\" title=\"Schooner\">schooner</a> <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(1799)\" title=\"USS Enterprise (1799)\">USS <i>Enterprise</i></a> <a href=\"https://wikipedia.org/wiki/Action_of_1_August_1801\" title=\"Action of 1 August 1801\">captures</a> the <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripolitan</a> <a href=\"https://wikipedia.org/wiki/Polacca\" title=\"Polacca\">polacca</a> <i>Tripoli</i> in a <a href=\"https://wikipedia.org/wiki/Single-ship_action\" class=\"mw-redirect\" title=\"Single-ship action\">single-ship action</a> off the coast of modern-day <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Barbary_War\" title=\"First Barbary War\">First Barbary War</a>: The American <a href=\"https://wikipedia.org/wiki/Schooner\" title=\"Schooner\">schooner</a> <a href=\"https://wikipedia.org/wiki/USS_Enterprise_(1799)\" title=\"USS Enterprise (1799)\">USS <i>Enterprise</i></a> <a href=\"https://wikipedia.org/wiki/Action_of_1_August_1801\" title=\"Action of 1 August 1801\">captures</a> the <a href=\"https://wikipedia.org/wiki/Tripoli,_Libya\" title=\"Tripoli, Libya\">Tripolitan</a> <a href=\"https://wikipedia.org/wiki/Polacca\" title=\"Polacca\">polacca</a> <i>Tripoli</i> in a <a href=\"https://wikipedia.org/wiki/Single-ship_action\" class=\"mw-redirect\" title=\"Single-ship action\">single-ship action</a> off the coast of modern-day <a href=\"https://wikipedia.org/wiki/Libya\" title=\"Libya\">Libya</a>.", "links": [{"title": "First Barbary War", "link": "https://wikipedia.org/wiki/First_Barbary_War"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>r"}, {"title": "USS Enterprise (1799)", "link": "https://wikipedia.org/wiki/USS_Enterprise_(1799)"}, {"title": "Action of 1 August 1801", "link": "https://wikipedia.org/wiki/Action_of_1_August_1801"}, {"title": "Tripoli, Libya", "link": "https://wikipedia.org/wiki/Tripoli,_Libya"}, {"title": "Polacca", "link": "https://wikipedia.org/wiki/Polacca"}, {"title": "Single-ship action", "link": "https://wikipedia.org/wiki/Single-ship_action"}, {"title": "Libya", "link": "https://wikipedia.org/wiki/Libya"}]}, {"year": "1834", "text": "Slavery is abolished in the British Empire as the Slavery Abolition Act 1833 comes into force, although it remains legal in the possessions of the East India Company until the passage of the Indian Slavery Act, 1843.", "html": "1834 - <a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_Kingdom\" title=\"Abolitionism in the United Kingdom\">Slavery is abolished in the British Empire</a> as the <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a> comes into force, although it remains legal in the possessions of the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> until the passage of the <a href=\"https://wikipedia.org/wiki/Indian_Slavery_Act,_1843\" title=\"Indian Slavery Act, 1843\">Indian Slavery Act, 1843</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abolitionism_in_the_United_Kingdom\" title=\"Abolitionism in the United Kingdom\">Slavery is abolished in the British Empire</a> as the <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a> comes into force, although it remains legal in the possessions of the <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> until the passage of the <a href=\"https://wikipedia.org/wiki/Indian_Slavery_Act,_1843\" title=\"Indian Slavery Act, 1843\">Indian Slavery Act, 1843</a>.", "links": [{"title": "Abolitionism in the United Kingdom", "link": "https://wikipedia.org/wiki/Abolitionism_in_the_United_Kingdom"}, {"title": "Slavery Abolition Act 1833", "link": "https://wikipedia.org/wiki/Slavery_Abolition_Act_1833"}, {"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "Indian Slavery Act, 1843", "link": "https://wikipedia.org/wiki/Indian_Slavery_Act,_1843"}]}, {"year": "1834", "text": "Construction begins on the Wilberforce Monument in Kingston Upon Hull.", "html": "1834 - Construction begins on the <a href=\"https://wikipedia.org/wiki/Wilberforce_Monument\" title=\"Wilberforce Monument\">Wilberforce Monument</a> in <a href=\"https://wikipedia.org/wiki/Kingston_Upon_Hull\" class=\"mw-redirect\" title=\"Kingston Upon Hull\">Kingston Upon Hull</a>.", "no_year_html": "Construction begins on the <a href=\"https://wikipedia.org/wiki/Wilberforce_Monument\" title=\"Wilberforce Monument\">Wilberforce Monument</a> in <a href=\"https://wikipedia.org/wiki/Kingston_Upon_Hull\" class=\"mw-redirect\" title=\"Kingston Upon Hull\">Kingston Upon Hull</a>.", "links": [{"title": "Wilberforce Monument", "link": "https://wikipedia.org/wiki/Wilberforce_Monument"}, {"title": "Kingston Upon Hull", "link": "https://wikipedia.org/wiki/Kingston_Upon_Hull"}]}, {"year": "1842", "text": "The Lombard Street riot erupts in Philadelphia, Pennsylvania, United States.", "html": "1842 - The <a href=\"https://wikipedia.org/wiki/Lombard_Street_riot\" title=\"Lombard Street riot\">Lombard Street riot</a> erupts in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>, United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lombard_Street_riot\" title=\"Lombard Street riot\">Lombard Street riot</a> erupts in <a href=\"https://wikipedia.org/wiki/Philadelphia\" title=\"Philadelphia\">Philadelphia</a>, <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>, United States.", "links": [{"title": "Lombard Street riot", "link": "https://wikipedia.org/wiki/Lombard_Street_riot"}, {"title": "Philadelphia", "link": "https://wikipedia.org/wiki/Philadelphia"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1849", "text": "<PERSON><PERSON> wrecks at the coast of Araucanía, Chile, leading to allegations that local Mapuche tribes murdered survivors and kidnapped <PERSON><PERSON>.", "html": "1849 - <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> wrecks at the coast of <a href=\"https://wikipedia.org/wiki/Araucan%C3%ADa_(historic_region)\" title=\"Araucanía (historic region)\">Araucanía</a>, Chile, leading to allegations that local <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> tribes murdered survivors and kidnapped <a href=\"https://wikipedia.org/wiki/Elisa_Bravo\" title=\"Elisa Bravo\">Elisa Bravo</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> wrecks at the coast of <a href=\"https://wikipedia.org/wiki/Araucan%C3%ADa_(historic_region)\" title=\"Araucanía (historic region)\">Araucanía</a>, Chile, leading to allegations that local <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> tribes murdered survivors and kidnapped <a href=\"https://wikipedia.org/wiki/Elisa_Bravo\" title=\"Elisa Bravo\">Elisa Bravo</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Araucanía (historic region)", "link": "https://wikipedia.org/wiki/Araucan%C3%ADa_(historic_region)"}, {"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bravo"}]}, {"year": "1855", "text": "The first ascent of Monte Rosa, the second highest summit in the Alps.", "html": "1855 - The first ascent of <a href=\"https://wikipedia.org/wiki/Monte_Rosa\" title=\"Monte Rosa\">Monte Rosa</a>, the second highest summit in the Alps.", "no_year_html": "The first ascent of <a href=\"https://wikipedia.org/wiki/Monte_Rosa\" title=\"Monte Rosa\">Monte Rosa</a>, the second highest summit in the Alps.", "links": [{"title": "Monte Rosa", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "At the suggestion of Senator <PERSON><PERSON> <PERSON><PERSON> and the order of Emperor <PERSON>, full rights are promised to the Finnish language by a language regulation in the Grand Duchy of Finland.", "html": "1863 - At the suggestion of Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and the order of Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" title=\"Alexander II of Russia\"><PERSON> II</a>, full rights are promised to the <a href=\"https://wikipedia.org/wiki/Finnish_language\" title=\"Finnish language\">Finnish language</a> by a language regulation in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "no_year_html": "At the suggestion of Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and the order of Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Russia\" title=\"<PERSON> II of Russia\"><PERSON> II</a>, full rights are promised to the <a href=\"https://wikipedia.org/wiki/Finnish_language\" title=\"Finnish language\">Finnish language</a> by a language regulation in the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Russia"}, {"title": "Finnish language", "link": "https://wikipedia.org/wiki/Finnish_language"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1876", "text": "Colorado is admitted as the 38th U.S. state.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> is admitted as the 38th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colorado\" title=\"Colorado\">Colorado</a> is admitted as the 38th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Colorado", "link": "https://wikipedia.org/wiki/Colorado"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1893", "text": "<PERSON> patents shredded wheat.", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents <a href=\"https://wikipedia.org/wiki/Shredded_wheat\" title=\"Shredded wheat\">shredded wheat</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> patents <a href=\"https://wikipedia.org/wiki/Shredded_wheat\" title=\"Shredded wheat\">shredded wheat</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shredded wheat", "link": "https://wikipedia.org/wiki/Shredded_wheat"}]}, {"year": "1894", "text": "The Empire of Japan and Qing China declare war on each other after a week of fighting over Korea, formally inaugurating the First Sino-Japanese War.", "html": "1894 - The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> and <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> declare war on each other after a week of fighting over Korea, formally inaugurating the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> and <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing China</a> declare war on each other after a week of fighting over Korea, formally inaugurating the <a href=\"https://wikipedia.org/wiki/First_Sino-Japanese_War\" title=\"First Sino-Japanese War\">First Sino-Japanese War</a>.", "links": [{"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "First Sino-Japanese War", "link": "https://wikipedia.org/wiki/First_Sino-Japanese_War"}]}, {"year": "1907", "text": "The start of the first Scout camp on Brownsea Island, the origin of the worldwide Scouting movement.", "html": "1907 - The start of the <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">first Scout camp</a> on <a href=\"https://wikipedia.org/wiki/Brownsea_Island\" title=\"Brownsea Island\">Brownsea Island</a>, the origin of the worldwide <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Scouting</a> movement.", "no_year_html": "The start of the <a href=\"https://wikipedia.org/wiki/Brownsea_Island_Scout_camp\" title=\"Brownsea Island Scout camp\">first Scout camp</a> on <a href=\"https://wikipedia.org/wiki/Brownsea_Island\" title=\"Brownsea Island\">Brownsea Island</a>, the origin of the worldwide <a href=\"https://wikipedia.org/wiki/Scouting\" title=\"Scouting\">Scouting</a> movement.", "links": [{"title": "Brownsea Island Scout camp", "link": "https://wikipedia.org/wiki/Brownsea_Island_Scout_camp"}, {"title": "Brownsea Island", "link": "https://wikipedia.org/wiki/Brownsea_Island"}, {"title": "Scouting", "link": "https://wikipedia.org/wiki/Scouting"}]}, {"year": "1911", "text": "<PERSON> takes her pilot's test and becomes the first U.S. woman to earn an Aero Club of America aviator's certificate.", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes her pilot's test and becomes the first U.S. woman to earn an Aero Club of America aviator's certificate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes her pilot's test and becomes the first U.S. woman to earn an Aero Club of America aviator's certificate.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "World War I: The German Empire declares war on the Russian Empire.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a> declares war on the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a> declares war on the <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russian Empire</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}]}, {"year": "1914", "text": "World War I: The Swiss Army mobilizes because of World War I.", "html": "1914 - World War I: The <a href=\"https://wikipedia.org/wiki/Military_of_Switzerland\" class=\"mw-redirect\" title=\"Military of Switzerland\">Swiss Army</a> mobilizes because of World War I.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Military_of_Switzerland\" class=\"mw-redirect\" title=\"Military of Switzerland\">Swiss Army</a> mobilizes because of World War I.", "links": [{"title": "Military of Switzerland", "link": "https://wikipedia.org/wiki/Military_of_Switzerland"}]}, {"year": "1915", "text": "<PERSON> gives his famous speech \"Ireland unfree shall never be at peace\" at <PERSON><PERSON><PERSON>'s funeral in Dublin.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives his famous speech \"<a href=\"https://wikipedia.org/wiki/Ireland_unfree_shall_never_be_at_peace\" title=\"Ireland unfree shall never be at peace\">Ireland unfree shall never be at peace</a>\" at <a href=\"https://wikipedia.org/wiki/O%27Donovan_Rossa\" class=\"mw-redirect\" title=\"O'<PERSON>\"><PERSON><PERSON><PERSON></a>'s funeral in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives his famous speech \"<a href=\"https://wikipedia.org/wiki/Ireland_unfree_shall_never_be_at_peace\" title=\"Ireland unfree shall never be at peace\">Ireland unfree shall never be at peace</a>\" at <a href=\"https://wikipedia.org/wiki/O%27Donovan_Rossa\" class=\"mw-redirect\" title=\"<PERSON>'<PERSON>\"><PERSON><PERSON><PERSON></a>'s funeral in <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ireland unfree shall never be at peace", "link": "https://wikipedia.org/wiki/Ireland_unfree_shall_never_be_at_peace"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O%27Donovan_<PERSON>a"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "1927", "text": "The Nanchang Uprising marks the first significant battle in the Chinese Civil War between the Kuomintang and Chinese Communist Party. This day is commemorated as the anniversary of the founding of the People's Liberation Army.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Nanchang_Uprising\" class=\"mw-redirect\" title=\"Nanchang Uprising\">Nanchang Uprising</a> marks the first significant battle in the <a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a> between the <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a> and <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a>. This day is commemorated as the anniversary of the founding of the <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nanchang_Uprising\" class=\"mw-redirect\" title=\"Nanchang Uprising\">Nanchang Uprising</a> marks the first significant battle in the <a href=\"https://wikipedia.org/wiki/Chinese_Civil_War\" title=\"Chinese Civil War\">Chinese Civil War</a> between the <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a> and <a href=\"https://wikipedia.org/wiki/Chinese_Communist_Party\" title=\"Chinese Communist Party\">Chinese Communist Party</a>. This day is commemorated as the anniversary of the founding of the <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a>.", "links": [{"title": "Nanchang Uprising", "link": "https://wikipedia.org/wiki/Nanchang_Uprising"}, {"title": "Chinese Civil War", "link": "https://wikipedia.org/wiki/Chinese_Civil_War"}, {"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang"}, {"title": "Chinese Communist Party", "link": "https://wikipedia.org/wiki/Chinese_Communist_Party"}, {"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}]}, {"year": "1933", "text": "Anti-Fascist activists <PERSON>, <PERSON>, <PERSON> and <PERSON> are executed by the Nazi regime in Altona.", "html": "1933 - Anti-Fascist activists <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antifascist)\" title=\"<PERSON> (antifascist)\"><PERSON></a>, <PERSON>, <PERSON> and <PERSON> are executed by the Nazi regime in <a href=\"https://wikipedia.org/wiki/Altona,_Hamburg\" title=\"Altona, Hamburg\">Altona</a>.", "no_year_html": "Anti-Fascist activists <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antifascist)\" title=\"<PERSON> (antifascist)\"><PERSON></a>, <PERSON>, <PERSON> and <PERSON> are executed by the Nazi regime in <a href=\"https://wikipedia.org/wiki/Altona,_Hamburg\" title=\"Altona, Hamburg\">Altona</a>.", "links": [{"title": "<PERSON> (antifascist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(antifascist)"}, {"title": "Altona, Hamburg", "link": "https://wikipedia.org/wiki/Altona,_Hamburg"}]}, {"year": "1936", "text": "The Olympics opened in Berlin with a ceremony presided over by <PERSON>.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">Olympics</a> opened in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> with a ceremony presided over by <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1936_Summer_Olympics\" title=\"1936 Summer Olympics\">Olympics</a> opened in <a href=\"https://wikipedia.org/wiki/Berlin\" title=\"Berlin\">Berlin</a> with a ceremony presided over by <a href=\"https://wikipedia.org/wiki/Adolf_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a>.", "links": [{"title": "1936 Summer Olympics", "link": "https://wikipedia.org/wiki/1936_Summer_Olympics"}, {"title": "Berlin", "link": "https://wikipedia.org/wiki/Berlin"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON> reads the resolution \"Manifesto of constitutional congress of KPH\" to the constitutive congress of KPH (Croatian Communist Party) in woods near Samobor.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> reads the resolution \"Manifesto of constitutional congress of KPH\" to the constitutive congress of KPH (<a href=\"https://wikipedia.org/wiki/League_of_Communists_of_Croatia\" title=\"League of Communists of Croatia\">Croatian Communist Party</a>) in woods near <a href=\"https://wikipedia.org/wiki/Samobor\" title=\"Samobor\">Samobor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> reads the resolution \"Manifesto of constitutional congress of KPH\" to the constitutive congress of KPH (<a href=\"https://wikipedia.org/wiki/League_of_Communists_of_Croatia\" title=\"League of Communists of Croatia\">Croatian Communist Party</a>) in woods near <a href=\"https://wikipedia.org/wiki/Samobor\" title=\"Samobor\">Samobor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "League of Communists of Croatia", "link": "https://wikipedia.org/wiki/League_of_Communists_of_Croatia"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Samobor"}]}, {"year": "1943", "text": "World War II: Operation Tidal Wave also known as \"Black Sunday\", was a failed American attempt to destroy Romanian oil fields.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Tidal_Wave\" title=\"Operation Tidal Wave\">Operation Tidal Wave</a> also known as \"Black Sunday\", was a failed American attempt to destroy Romanian oil fields.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Tidal_Wave\" title=\"Operation Tidal Wave\">Operation Tidal Wave</a> also known as \"Black Sunday\", was a failed American attempt to destroy Romanian oil fields.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Tidal Wave", "link": "https://wikipedia.org/wiki/Operation_Tidal_Wave"}]}, {"year": "1944", "text": "World War II: The Warsaw Uprising against the Nazi German occupation breaks out in Warsaw, Poland.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Warsaw_Uprising\" title=\"Warsaw Uprising\">Warsaw Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi German</a> occupation breaks out in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Warsaw_Uprising\" title=\"Warsaw Uprising\">Warsaw Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Nazism\" title=\"Nazism\">Nazi German</a> occupation breaks out in <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>, Poland.", "links": [{"title": "Warsaw Uprising", "link": "https://wikipedia.org/wiki/Warsaw_Uprising"}, {"title": "Nazism", "link": "https://wikipedia.org/wiki/Nazism"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}]}, {"year": "1946", "text": "Leaders of the Russian Liberation Army, a force of Russian prisoners of war that collaborated with Nazi Germany, are executed in Moscow, Soviet Union for treason.", "html": "1946 - Leaders of the <a href=\"https://wikipedia.org/wiki/Russian_Liberation_Army\" title=\"Russian Liberation Army\">Russian Liberation Army</a>, a force of Russian prisoners of war that <a href=\"https://wikipedia.org/wiki/Collaboration_with_Nazi_Germany_and_Fascist_Italy\" title=\"Collaboration with Nazi Germany and Fascist Italy\">collaborated with Nazi Germany</a>, are executed in Moscow, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> for treason.", "no_year_html": "Leaders of the <a href=\"https://wikipedia.org/wiki/Russian_Liberation_Army\" title=\"Russian Liberation Army\">Russian Liberation Army</a>, a force of Russian prisoners of war that <a href=\"https://wikipedia.org/wiki/Collaboration_with_Nazi_Germany_and_Fascist_Italy\" title=\"Collaboration with Nazi Germany and Fascist Italy\">collaborated with Nazi Germany</a>, are executed in Moscow, <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> for treason.", "links": [{"title": "Russian Liberation Army", "link": "https://wikipedia.org/wiki/Russian_Liberation_Army"}, {"title": "Collaboration with Nazi Germany and Fascist Italy", "link": "https://wikipedia.org/wiki/Collaboration_with_Nazi_Germany_and_Fascist_Italy"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1950", "text": "Guam is organized as an unincorporated territory of the United States as the President <PERSON> signs the Guam Organic Act.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a> is organized as an unincorporated territory of the United States as the President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Guam_Organic_Act_of_1950\" title=\"Guam Organic Act of 1950\">Guam Organic Act</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a> is organized as an unincorporated territory of the United States as the President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Guam_Organic_Act_of_1950\" title=\"Guam Organic Act of 1950\">Guam Organic Act</a>.", "links": [{"title": "Guam", "link": "https://wikipedia.org/wiki/Guam"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Guam Organic Act of 1950", "link": "https://wikipedia.org/wiki/Guam_Organic_Act_of_1950"}]}, {"year": "1957", "text": "The United States and Canada form the North American Aerospace Defense Command (NORAD).", "html": "1957 - The United States and Canada form the <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">North American Aerospace Defense Command</a> (NORAD).", "no_year_html": "The United States and Canada form the <a href=\"https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command\" class=\"mw-redirect\" title=\"North American Aerospace Defense Command\">North American Aerospace Defense Command</a> (NORAD).", "links": [{"title": "North American Aerospace Defense Command", "link": "https://wikipedia.org/wiki/North_American_Aerospace_Defense_Command"}]}, {"year": "1960", "text": "Dahomey (later renamed Benin) declares independence from France.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Dahomey\" title=\"Dahomey\">Dahomey</a> (later renamed <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>) declares independence from France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dahomey\" title=\"Dahomey\">Dahomey</a> (later renamed <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>) declares independence from France.", "links": [{"title": "Dahomey", "link": "https://wikipedia.org/wiki/Dahomey"}, {"title": "Benin", "link": "https://wikipedia.org/wiki/Benin"}]}, {"year": "1960", "text": "Islamabad is declared the federal capital of the Government of Pakistan.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a> is declared the <a href=\"https://wikipedia.org/wiki/Federal_capital\" title=\"Federal capital\">federal capital</a> of the <a href=\"https://wikipedia.org/wiki/Government_of_Pakistan\" title=\"Government of Pakistan\">Government of Pakistan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Islamabad\" title=\"Islamabad\">Islamabad</a> is declared the <a href=\"https://wikipedia.org/wiki/Federal_capital\" title=\"Federal capital\">federal capital</a> of the <a href=\"https://wikipedia.org/wiki/Government_of_Pakistan\" title=\"Government of Pakistan\">Government of Pakistan</a>.", "links": [{"title": "Islamabad", "link": "https://wikipedia.org/wiki/Islamabad"}, {"title": "Federal capital", "link": "https://wikipedia.org/wiki/Federal_capital"}, {"title": "Government of Pakistan", "link": "https://wikipedia.org/wiki/Government_of_Pakistan"}]}, {"year": "1961", "text": "U.S. Defense Secretary <PERSON> orders the creation of the Defense Intelligence Agency (DIA), the nation's first centralized military espionage organization.", "html": "1961 - U.S. Defense Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the creation of the <a href=\"https://wikipedia.org/wiki/Defense_Intelligence_Agency\" title=\"Defense Intelligence Agency\">Defense Intelligence Agency</a> (DIA), the nation's first centralized military espionage organization.", "no_year_html": "U.S. Defense Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> orders the creation of the <a href=\"https://wikipedia.org/wiki/Defense_Intelligence_Agency\" title=\"Defense Intelligence Agency\">Defense Intelligence Agency</a> (DIA), the nation's first centralized military espionage organization.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Defense Intelligence Agency", "link": "https://wikipedia.org/wiki/Defense_Intelligence_Agency"}]}, {"year": "1964", "text": "The former Belgian Congo is renamed the Democratic Republic of the Congo.", "html": "1964 - The former <a href=\"https://wikipedia.org/wiki/Belgian_Congo\" title=\"Belgian Congo\">Belgian Congo</a> is renamed the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "no_year_html": "The former <a href=\"https://wikipedia.org/wiki/Belgian_Congo\" title=\"Belgian Congo\">Belgian Congo</a> is renamed the <a href=\"https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo\" title=\"Democratic Republic of the Congo\">Democratic Republic of the Congo</a>.", "links": [{"title": "Belgian Congo", "link": "https://wikipedia.org/wiki/Belgian_Congo"}, {"title": "Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Democratic_Republic_of_the_Congo"}]}, {"year": "1965", "text": "<PERSON>'s novel, <PERSON><PERSON> was published for the first time. It was named as the world's best-selling science fiction novel in 2003.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s novel, <a href=\"https://wikipedia.org/wiki/Dune_(novel)\" title=\"<PERSON><PERSON> (novel)\"><i>Dune</i></a> was published for the first time. It was named as the world's best-selling science fiction novel in 2003.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s novel, <a href=\"https://wikipedia.org/wiki/Dune_(novel)\" title=\"Du<PERSON> (novel)\"><i>Dune</i></a> was published for the first time. It was named as the world's best-selling science fiction novel in 2003.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dune (novel)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(novel)"}]}, {"year": "1966", "text": "<PERSON> kills 15 people at the University of Texas at Austin before being killed by the police.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/University_of_Texas_tower_shooting\" title=\"University of Texas tower shooting\">kills 15 people</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Texas_at_Austin\" title=\"University of Texas at Austin\">University of Texas at Austin</a> before being killed by the police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/University_of_Texas_tower_shooting\" title=\"University of Texas tower shooting\">kills 15 people</a> at the <a href=\"https://wikipedia.org/wiki/University_of_Texas_at_Austin\" title=\"University of Texas at Austin\">University of Texas at Austin</a> before being killed by the police.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "University of Texas tower shooting", "link": "https://wikipedia.org/wiki/University_of_Texas_tower_shooting"}, {"title": "University of Texas at Austin", "link": "https://wikipedia.org/wiki/University_of_Texas_at_Austin"}]}, {"year": "1966", "text": "Purges of intellectuals and imperialists becomes official China policy at the beginning of the Cultural Revolution.", "html": "1966 - Purges of intellectuals and imperialists becomes official China policy at the beginning of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "no_year_html": "Purges of intellectuals and imperialists becomes official China policy at the beginning of the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a>.", "links": [{"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}]}, {"year": "1968", "text": "The coronation of <PERSON><PERSON>, the 29th Sultan of Brunei, is held.", "html": "1968 - The coronation of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bolkiah\" title=\"<PERSON><PERSON> Bolkiah\"><PERSON><PERSON></a>, the 29th <a href=\"https://wikipedia.org/wiki/Sultan_of_Brunei\" class=\"mw-redirect\" title=\"Sultan of Brunei\">Sultan of Brunei</a>, is held.", "no_year_html": "The coronation of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bolkiah\"><PERSON><PERSON></a>, the 29th <a href=\"https://wikipedia.org/wiki/Sultan_of_Brunei\" class=\"mw-redirect\" title=\"Sultan of Brunei\">Sultan of Brunei</a>, is held.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON> of Brunei", "link": "https://wikipedia.org/wiki/Sultan_of_Brunei"}]}, {"year": "1971", "text": "The Concert for Bangladesh, organized by former <PERSON><PERSON>, is held at Madison Square Garden in New York City.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/The_Concert_for_Bangladesh\" title=\"The Concert for Bangladesh\">The Concert for Bangladesh</a>, organized by former <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is held at Madison Square Garden in New York City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Concert_for_Bangladesh\" title=\"The Concert for Bangladesh\">The Concert for Bangladesh</a>, organized by former <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is held at Madison Square Garden in New York City.", "links": [{"title": "The Concert for Bangladesh", "link": "https://wikipedia.org/wiki/The_Concert_for_Bangladesh"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "Cyprus dispute: The United Nations Security Council authorizes the UNFICYP to create the \"Green Line\", dividing Cyprus into two zones.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Cyprus_dispute\" class=\"mw-redirect\" title=\"Cyprus dispute\">Cyprus dispute</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> authorizes the <a href=\"https://wikipedia.org/wiki/United_Nations_Peacekeeping_Force_in_Cyprus\" title=\"United Nations Peacekeeping Force in Cyprus\">UNFICYP</a> to create the \"<a href=\"https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus\" title=\"United Nations Buffer Zone in Cyprus\">Green Line</a>\", dividing <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> into two zones.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyprus_dispute\" class=\"mw-redirect\" title=\"Cyprus dispute\">Cyprus dispute</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council\" title=\"United Nations Security Council\">United Nations Security Council</a> authorizes the <a href=\"https://wikipedia.org/wiki/United_Nations_Peacekeeping_Force_in_Cyprus\" title=\"United Nations Peacekeeping Force in Cyprus\">UNFICYP</a> to create the \"<a href=\"https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus\" title=\"United Nations Buffer Zone in Cyprus\">Green Line</a>\", dividing <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> into two zones.", "links": [{"title": "Cyprus dispute", "link": "https://wikipedia.org/wiki/Cyprus_dispute"}, {"title": "United Nations Security Council", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council"}, {"title": "United Nations Peacekeeping Force in Cyprus", "link": "https://wikipedia.org/wiki/United_Nations_Peacekeeping_Force_in_Cyprus"}, {"title": "United Nations Buffer Zone in Cyprus", "link": "https://wikipedia.org/wiki/United_Nations_Buffer_Zone_in_Cyprus"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}]}, {"year": "1976", "text": "<PERSON><PERSON> has a severe accident that almost claims his life at the German Grand Prix at Nürburgring.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> has a severe accident that almost claims his life at the <a href=\"https://wikipedia.org/wiki/1976_German_Grand_Prix\" title=\"1976 German Grand Prix\">German Grand Prix</a> at <a href=\"https://wikipedia.org/wiki/N%C3%BCrburgring\" title=\"Nürburgring\">Nürburgring</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> has a severe accident that almost claims his life at the <a href=\"https://wikipedia.org/wiki/1976_German_Grand_Prix\" title=\"1976 German Grand Prix\">German Grand Prix</a> at <a href=\"https://wikipedia.org/wiki/N%C3%BCrburgring\" title=\"Nürburgring\">Nürburgring</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_Lauda"}, {"title": "1976 German Grand Prix", "link": "https://wikipedia.org/wiki/1976_German_Grand_Prix"}, {"title": "Nürburgring", "link": "https://wikipedia.org/wiki/N%C3%BCrburgring"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON> is elected President of Iceland and becomes the world's first democratically elected female head of state.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Vigd%C3%ADs_Finnbogad%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> and becomes the world's first democratically elected female head of state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vigd%C3%ADs_Finnbogad%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_of_Iceland\" title=\"President of Iceland\">President of Iceland</a> and becomes the world's first democratically elected female head of state.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vigd%C3%ADs_Finnbogad%C3%B3ttir"}, {"title": "President of Iceland", "link": "https://wikipedia.org/wiki/President_of_Iceland"}]}, {"year": "1980", "text": "A train crash kills 18 people and injures over 170 more in County Cork, Ireland.", "html": "1980 - A <a href=\"https://wikipedia.org/wiki/Buttevant_Rail_Disaster\" title=\"Buttevant Rail Disaster\">train crash</a> kills 18 people and injures over 170 more in <a href=\"https://wikipedia.org/wiki/County_Cork\" title=\"County Cork\">County Cork</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Buttevant_Rail_Disaster\" title=\"Buttevant Rail Disaster\">train crash</a> kills 18 people and injures over 170 more in <a href=\"https://wikipedia.org/wiki/County_Cork\" title=\"County Cork\">County Cork</a>, <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a>.", "links": [{"title": "Buttevant Rail Disaster", "link": "https://wikipedia.org/wiki/Buttevant_Rail_Disaster"}, {"title": "County Cork", "link": "https://wikipedia.org/wiki/County_Cork"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}]}, {"year": "1981", "text": "MTV begins broadcasting in the United States and airs its first video, \"Video Killed the Radio Star\" by The Buggles.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/MTV\" title=\"MTV\">MTV</a> begins broadcasting in the United States and airs its first video, \"<a href=\"https://wikipedia.org/wiki/Video_Killed_the_Radio_Star\" title=\"Video Killed the Radio Star\">Video Killed the Radio Star</a>\" by <a href=\"https://wikipedia.org/wiki/The_Buggles\" title=\"The Buggles\">The Buggles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/MTV\" title=\"MTV\">MTV</a> begins broadcasting in the United States and airs its first video, \"<a href=\"https://wikipedia.org/wiki/Video_Killed_the_Radio_Star\" title=\"Video Killed the Radio Star\">Video Killed the Radio Star</a>\" by <a href=\"https://wikipedia.org/wiki/The_Buggles\" title=\"The Buggles\">The Buggles</a>.", "links": [{"title": "MTV", "link": "https://wikipedia.org/wiki/MTV"}, {"title": "Video Killed the Radio Star", "link": "https://wikipedia.org/wiki/Video_Killed_the_Radio_Star"}, {"title": "The Buggles", "link": "https://wikipedia.org/wiki/The_Buggles"}]}, {"year": "1984", "text": "Commercial peat-cutters discover the preserved bog body of a man, called Lindow Man, at Lindow Moss, Cheshire, England.", "html": "1984 - Commercial <a href=\"https://wikipedia.org/wiki/Peat\" title=\"Peat\">peat</a>-cutters discover the preserved <a href=\"https://wikipedia.org/wiki/Bog_body\" title=\"Bog body\">bog body</a> of a man, called <a href=\"https://wikipedia.org/wiki/Lindow_Man\" title=\"Lindow Man\">Lindow Man</a>, at <a href=\"https://wikipedia.org/wiki/Lindow_Moss\" title=\"Lindow Moss\">Lindow Moss</a>, <a href=\"https://wikipedia.org/wiki/Cheshire\" title=\"Cheshire\">Cheshire</a>, England.", "no_year_html": "Commercial <a href=\"https://wikipedia.org/wiki/Peat\" title=\"Peat\">peat</a>-cutters discover the preserved <a href=\"https://wikipedia.org/wiki/Bog_body\" title=\"Bog body\">bog body</a> of a man, called <a href=\"https://wikipedia.org/wiki/Lindow_Man\" title=\"Lindow Man\">Lindow Man</a>, at <a href=\"https://wikipedia.org/wiki/Lindow_Moss\" title=\"Lindow Moss\">Lindow Moss</a>, <a href=\"https://wikipedia.org/wiki/Cheshire\" title=\"Cheshire\">Cheshire</a>, England.", "links": [{"title": "Peat", "link": "https://wikipedia.org/wiki/Peat"}, {"title": "Bog body", "link": "https://wikipedia.org/wiki/Bog_body"}, {"title": "Lindow Man", "link": "https://wikipedia.org/wiki/Lindow_Man"}, {"title": "Lindow Moss", "link": "https://wikipedia.org/wiki/Lindow_Moss"}, {"title": "Cheshire", "link": "https://wikipedia.org/wiki/Cheshire"}]}, {"year": "1988", "text": "A British soldier was killed in the Inglis Barracks bombing in London, England.", "html": "1988 - A British soldier was killed in the <a href=\"https://wikipedia.org/wiki/Chronology_of_Provisional_Irish_Republican_Army_actions_(1980%E2%80%931989)\" title=\"Chronology of Provisional Irish Republican Army actions (1980-1989)\">Inglis Barracks bombing</a> in London, England.", "no_year_html": "A British soldier was killed in the <a href=\"https://wikipedia.org/wiki/Chronology_of_Provisional_Irish_Republican_Army_actions_(1980%E2%80%931989)\" title=\"Chronology of Provisional Irish Republican Army actions (1980-1989)\">Inglis Barracks bombing</a> in London, England.", "links": [{"title": "Chronology of Provisional Irish Republican Army actions (1980-1989)", "link": "https://wikipedia.org/wiki/Chronology_of_Provisional_Irish_Republican_Army_actions_(1980%E2%80%931989)"}]}, {"year": "1990", "text": "A plane crash in the Karabakh Range kills 46 people.", "html": "1990 - A <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_E-35D\" title=\"Aeroflot Flight E-35D\">plane crash</a> in the <a href=\"https://wikipedia.org/wiki/Karabakh_Range\" title=\"Karabakh Range\">Karabakh Range</a> kills 46 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_E-35D\" title=\"Aeroflot Flight E-35D\">plane crash</a> in the <a href=\"https://wikipedia.org/wiki/Karabakh_Range\" title=\"Karabakh Range\">Karabakh Range</a> kills 46 people.", "links": [{"title": "Aeroflot Flight E-35D", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_E-35D"}, {"title": "Karabakh Range", "link": "https://wikipedia.org/wiki/Karabakh_Range"}]}, {"year": "1993", "text": "The Great Mississippi and Missouri Rivers Flood of 1993 comes to a peak.", "html": "1993 - The <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1993\" title=\"Great Flood of 1993\">Great Mississippi and Missouri Rivers Flood of 1993</a> comes to a peak.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Flood_of_1993\" title=\"Great Flood of 1993\">Great Mississippi and Missouri Rivers Flood of 1993</a> comes to a peak.", "links": [{"title": "Great Flood of 1993", "link": "https://wikipedia.org/wiki/Great_Flood_of_1993"}]}, {"year": "1998", "text": "Puntland, an autonomous state in northeastern Somalia, was officially established following a constitutional conference in Garowe, Issims and tribal chiefs agreed to create a self-declared government until Somalia recovered.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Puntland\" title=\"Puntland\">Puntland</a>, an <a href=\"https://wikipedia.org/wiki/Autonomous_administrative_division\" title=\"Autonomous administrative division\">autonomous state</a> in northeastern <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, was <a href=\"https://wikipedia.org/wiki/History_of_Puntland#Establishment_of_Puntland\" title=\"History of Puntland\">officially established</a> following a constitutional conference in <a href=\"https://wikipedia.org/wiki/Garoowe\" title=\"Garoowe\">Garowe</a>, <a href=\"https://wikipedia.org/wiki/Somali_aristocratic_and_court_titles#Traditional_leaders_and_officials\" title=\"Somali aristocratic and court titles\">Issims</a> and <a href=\"https://wikipedia.org/wiki/Tribal_chief\" title=\"Tribal chief\">tribal chiefs</a> agreed to create a <a href=\"https://wikipedia.org/wiki/Self-proclaimed\" title=\"Self-proclaimed\">self-declared</a> <a href=\"https://wikipedia.org/wiki/Government_of_Puntland\" title=\"Government of Puntland\">government</a> until Somalia recovered.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Puntland\" title=\"Puntland\">Puntland</a>, an <a href=\"https://wikipedia.org/wiki/Autonomous_administrative_division\" title=\"Autonomous administrative division\">autonomous state</a> in northeastern <a href=\"https://wikipedia.org/wiki/Somalia\" title=\"Somalia\">Somalia</a>, was <a href=\"https://wikipedia.org/wiki/History_of_Puntland#Establishment_of_Puntland\" title=\"History of Puntland\">officially established</a> following a constitutional conference in <a href=\"https://wikipedia.org/wiki/Garoowe\" title=\"Garoowe\">Garowe</a>, <a href=\"https://wikipedia.org/wiki/Somali_aristocratic_and_court_titles#Traditional_leaders_and_officials\" title=\"Somali aristocratic and court titles\">Issims</a> and <a href=\"https://wikipedia.org/wiki/Tribal_chief\" title=\"Tribal chief\">tribal chiefs</a> agreed to create a <a href=\"https://wikipedia.org/wiki/Self-proclaimed\" title=\"Self-proclaimed\">self-declared</a> <a href=\"https://wikipedia.org/wiki/Government_of_Puntland\" title=\"Government of Puntland\">government</a> until Somalia recovered.", "links": [{"title": "Puntland", "link": "https://wikipedia.org/wiki/Puntland"}, {"title": "Autonomous administrative division", "link": "https://wikipedia.org/wiki/Autonomous_administrative_division"}, {"title": "Somalia", "link": "https://wikipedia.org/wiki/Somalia"}, {"title": "History of Puntland", "link": "https://wikipedia.org/wiki/History_of_Puntland#Establishment_of_Puntland"}, {"title": "Garoowe", "link": "https://wikipedia.org/wiki/Garoowe"}, {"title": "Somali aristocratic and court titles", "link": "https://wikipedia.org/wiki/Somali_aristocratic_and_court_titles#Traditional_leaders_and_officials"}, {"title": "Tribal chief", "link": "https://wikipedia.org/wiki/Tribal_chief"}, {"title": "Self-proclaimed", "link": "https://wikipedia.org/wiki/Self-proclaimed"}, {"title": "Government of Puntland", "link": "https://wikipedia.org/wiki/Government_of_Puntland"}]}, {"year": "2004", "text": "A supermarket fire kills 396 people and injures 500 others in Asunción, Paraguay.", "html": "2004 - A <a href=\"https://wikipedia.org/wiki/Ycu%C3%A1_Bola%C3%B1os_supermarket_fire\" title=\"Ycuá Bolaños supermarket fire\">supermarket fire</a> kills 396 people and injures 500 others in <a href=\"https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay\" class=\"mw-redirect\" title=\"Asunción, Paraguay\">Asunción, Paraguay</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Ycu%C3%A1_Bola%C3%B1os_supermarket_fire\" title=\"Ycuá Bolaños supermarket fire\">supermarket fire</a> kills 396 people and injures 500 others in <a href=\"https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay\" class=\"mw-redirect\" title=\"Asunción, Paraguay\">Asunción, Paraguay</a>.", "links": [{"title": "Ycuá Bolaños supermarket fire", "link": "https://wikipedia.org/wiki/Ycu%C3%A1_Bola%C3%B1os_supermarket_fire"}, {"title": "Asunción, Paraguay", "link": "https://wikipedia.org/wiki/Asunci%C3%B3n,_Paraguay"}]}, {"year": "2007", "text": "The I-35W Mississippi River bridge spanning the Mississippi River in Minneapolis, Minnesota, collapses during the evening rush hour, killing 13 people and injuring 145.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/I-35W_Mississippi_River_bridge\" title=\"I-35W Mississippi River bridge\">I-35W Mississippi River bridge</a> spanning the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis, Minnesota</a>, collapses during the evening <a href=\"https://wikipedia.org/wiki/Rush_hour\" title=\"Rush hour\">rush hour</a>, killing 13 people and injuring 145.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/I-35W_Mississippi_River_bridge\" title=\"I-35W Mississippi River bridge\">I-35W Mississippi River bridge</a> spanning the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a> in <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis, Minnesota</a>, collapses during the evening <a href=\"https://wikipedia.org/wiki/Rush_hour\" title=\"Rush hour\">rush hour</a>, killing 13 people and injuring 145.", "links": [{"title": "I-35W Mississippi River bridge", "link": "https://wikipedia.org/wiki/I-35W_Mississippi_River_bridge"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Minneapolis", "link": "https://wikipedia.org/wiki/Minneapolis"}, {"title": "Rush hour", "link": "https://wikipedia.org/wiki/Rush_hour"}]}, {"year": "2008", "text": "The Beijing-Tianjin Intercity Railway begins operation as the fastest commuter rail system in the world.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Beijing%E2%80%93Tianjin_Intercity_Railway\" class=\"mw-redirect\" title=\"Beijing-Tianjin Intercity Railway\">Beijing-Tianjin Intercity Railway</a> begins operation as the fastest commuter rail system in the world.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Beijing%E2%80%93Tianjin_Intercity_Railway\" class=\"mw-redirect\" title=\"Beijing-Tianjin Intercity Railway\">Beijing-Tianjin Intercity Railway</a> begins operation as the fastest commuter rail system in the world.", "links": [{"title": "Beijing-Tianjin Intercity Railway", "link": "https://wikipedia.org/wiki/Beijing%E2%80%93Tianjin_Intercity_Railway"}]}, {"year": "2008", "text": "Eleven mountaineers from international expeditions died on K2, the second-highest mountain on Earth in the worst single accident in the history of K2 mountaineering.", "html": "2008 - Eleven mountaineers from international expeditions died on K2, the second-highest mountain on Earth in <a href=\"https://wikipedia.org/wiki/2008_K2_disaster\" title=\"2008 K2 disaster\">the worst single accident in the history of K2 mountaineering</a>.", "no_year_html": "Eleven mountaineers from international expeditions died on K2, the second-highest mountain on Earth in <a href=\"https://wikipedia.org/wiki/2008_K2_disaster\" title=\"2008 K2 disaster\">the worst single accident in the history of K2 mountaineering</a>.", "links": [{"title": "2008 K2 disaster", "link": "https://wikipedia.org/wiki/2008_K2_disaster"}]}, {"year": "2017", "text": "A suicide attack on a mosque in Herat, Afghanistan kills 20 people.", "html": "2017 - A <a href=\"https://wikipedia.org/wiki/Suicide_attack\" title=\"Suicide attack\">suicide attack</a> on a mosque in Herat, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> kills 20 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Suicide_attack\" title=\"Suicide attack\">suicide attack</a> on a mosque in Herat, <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a> kills 20 people.", "links": [{"title": "Suicide attack", "link": "https://wikipedia.org/wiki/Suicide_attack"}, {"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}]}, {"year": "2023", "text": "Former US President <PERSON> is indicted for his role in the January 6 United States Capitol attack, his third indictment in 2023.", "html": "2023 - Former US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Federal_prosecution_of_<PERSON>_<PERSON>_(election_obstruction_case)\" title=\"Federal prosecution of <PERSON> (election obstruction case)\">indicted</a> for his role in the <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">January 6 United States Capitol attack</a>, his third indictment in 2023.", "no_year_html": "Former US President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Federal_prosecution_of_<PERSON>_<PERSON>_(election_obstruction_case)\" title=\"Federal prosecution of <PERSON> (election obstruction case)\">indicted</a> for his role in the <a href=\"https://wikipedia.org/wiki/January_6_United_States_Capitol_attack\" title=\"January 6 United States Capitol attack\">January 6 United States Capitol attack</a>, his third indictment in 2023.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Federal prosecution of <PERSON> (election obstruction case)", "link": "https://wikipedia.org/wiki/Federal_prosecution_of_<PERSON>_<PERSON>_(election_obstruction_case)"}, {"title": "January 6 United States Capitol attack", "link": "https://wikipedia.org/wiki/January_6_United_States_Capitol_attack"}]}], "Births": [{"year": "10 BC", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 54)", "html": "10 BC - 10 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 54)", "no_year_html": "10 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 54)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>laudius"}]}, {"year": "126", "text": "<PERSON><PERSON><PERSON>, Roman emperor (d. 193)", "html": "126 - <a href=\"https://wikipedia.org/wiki/Pertinax\" title=\"Pertinax\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 193)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pertinax\" title=\"Pertinax\"><PERSON><PERSON><PERSON></a>, Roman emperor (d. 193)", "links": [{"title": "Pertinax", "link": "https://wikipedia.org/wiki/Pertinax"}]}, {"year": "845", "text": "<PERSON><PERSON><PERSON> <PERSON>, Japanese scholar and politician (d. 903)", "html": "845 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_no_<PERSON>zan<PERSON>\" title=\"Su<PERSON>ra no Michizane\"><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese scholar and politician (d. 903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_no_<PERSON>zan<PERSON>\" title=\"Sugawara no Michizane\"><PERSON><PERSON><PERSON> no <PERSON></a>, Japanese scholar and politician (d. 903)", "links": [{"title": "<PERSON><PERSON><PERSON> no <PERSON>chizane", "link": "https://wikipedia.org/wiki/Sugawa<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>e"}]}, {"year": "992", "text": "<PERSON><PERSON><PERSON><PERSON> of Goryeo, Korean king (d. 1031)", "html": "992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>jong_of_Goryeo\" title=\"<PERSON><PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (d. 1031)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>jong_of_Goryeo\" title=\"<PERSON><PERSON><PERSON><PERSON> of Goryeo\"><PERSON><PERSON><PERSON><PERSON> of Goryeo</a>, Korean king (d. 1031)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Hyeonjong_of_Goryeo"}]}, {"year": "1068", "text": "Emperor <PERSON><PERSON> of Jin, Chinese emperor (d. 1123)", "html": "1068 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a>, Chinese emperor (d. 1123)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin\" title=\"Emperor <PERSON><PERSON> of Jin\">Emperor <PERSON><PERSON> of Jin</a>, Chinese emperor (d. 1123)", "links": [{"title": "Emperor <PERSON><PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Jin"}]}, {"year": "1313", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (d. 1364)", "html": "1313 - <a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dgon\" title=\"Emperor Kō<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1364)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_K%C5%8Dgon\" title=\"Emperor Kō<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1364)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_K%C5%8Dgon"}]}, {"year": "1377", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese emperor (d. 1433)", "html": "1377 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ko<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1433)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ko<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1433)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1385", "text": "<PERSON>, 6th Earl of Arundel (d. 1421)", "html": "1385 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Arundel\" title=\"<PERSON>, 6th Earl of Arundel\"><PERSON>, 6th Earl of Arundel</a> (d. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Arundel\" title=\"<PERSON>, 6th Earl of Arundel\"><PERSON>, 6th Earl of Arundel</a> (d. 1421)", "links": [{"title": "<PERSON>, 6th Earl of Arundel", "link": "https://wikipedia.org/wiki/<PERSON>,_6th_Earl_of_Arundel"}]}, {"year": "1410", "text": "<PERSON>, Count of Nassau-Siegen, German count (d. 1475)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (d. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-Siegen\" title=\"<PERSON>, Count of Nassau-Siegen\"><PERSON>, Count of Nassau-Siegen</a>, German count (d. 1475)", "links": [{"title": "<PERSON>, Count of Nassau-Siegen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Nassau-<PERSON>n"}]}, {"year": "1492", "text": "<PERSON>, Prince of Anhalt-Köthen, German prince (d. 1566)", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Anhalt-K%C3%B6then\" title=\"<PERSON>, Prince of Anhalt-Köthen\"><PERSON>, Prince of Anhalt-Köthen</a>, German prince (d. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Anhalt-K%C3%B6then\" title=\"<PERSON>, Prince of Anhalt-Köthen\"><PERSON>, Prince of Anhalt-Köthen</a>, German prince (d. 1566)", "links": [{"title": "<PERSON>, Prince of Anhalt-Köthen", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Anhalt-K%C3%B<PERSON>en"}]}, {"year": "1520", "text": "<PERSON><PERSON><PERSON>, Polish king (d. 1572)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Augustus\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish king (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> II Augustus\"><PERSON><PERSON><PERSON> <PERSON></a>, Polish king (d. 1572)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON>, Scottish theologian and scholar (d. 1622)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and scholar (d. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish theologian and scholar (d. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1555", "text": "<PERSON>, English spirit medium (d. 1597)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spirit medium (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English spirit medium (d. 1597)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "<PERSON>, Spanish author and playwright (d. 1644)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Luis_V%C3%A9<PERSON><PERSON>_de_Guevara\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (d. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_V%C3%A9lez_de_Guevara\" title=\"<PERSON>\"><PERSON></a>, Spanish author and playwright (d. 1644)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_V%C3%A9le<PERSON>_<PERSON>_<PERSON>ra"}]}, {"year": "1626", "text": "<PERSON><PERSON><PERSON>, Montenegrin rabbi and theorist (d. 1676)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/Sabbatai_Zevi\" title=\"Sabbatai Zevi\"><PERSON><PERSON><PERSON></a>, Montenegrin rabbi and theorist (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sabbatai_Zevi\" title=\"Sabbatai Zevi\"><PERSON><PERSON><PERSON></a>, Montenegrin rabbi and theorist (d. 1676)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sabbatai_<PERSON>evi"}]}, {"year": "1630", "text": "<PERSON>, 1st Baron <PERSON> of Chudleigh, English politician, Lord High Treasurer (d. 1673)", "html": "1630 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>_of_Chudleigh\" title=\"<PERSON>, 1st Baron <PERSON> of Chudleigh\"><PERSON>, 1st Baron <PERSON> of Chudleigh</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Chudleigh\" title=\"<PERSON>, 1st Baron <PERSON> of Chudleigh\"><PERSON>, 1st Baron <PERSON> of Chudleigh</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer\" title=\"Lord High Treasurer\">Lord High Treasurer</a> (d. 1673)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Chudleigh", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Chudleigh"}, {"title": "Lord High Treasurer", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer"}]}, {"year": "1659", "text": "<PERSON><PERSON>, Italian painter (d. 1734)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter (d. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian painter (d. 1734)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, German duke and prince (d. 1780)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON>, Duke of Brunswick-Wolfenbüttel\"><PERSON></a>, German duke and prince (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON>, Duke of Brunswick-Wolfenbüttel\"><PERSON></a>, German duke and prince (d. 1780)", "links": [{"title": "<PERSON>, Duke of Brunswick-Wolfenbüttel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Wolfenb%C3%BCttel"}]}, {"year": "1714", "text": "<PERSON>, Welsh painter and academic (d. 1782)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Welsh painter and academic (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)\" title=\"<PERSON> (painter)\"><PERSON></a>, Welsh painter and academic (d. 1782)", "links": [{"title": "<PERSON> (painter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(painter)"}]}, {"year": "1738", "text": "<PERSON>, French general (d. 1794)", "html": "1738 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1744", "text": "<PERSON><PERSON><PERSON>, French soldier, biologist, and academic (d. 1829)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier, biologist, and academic (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French soldier, biologist, and academic (d. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1770", "text": "<PERSON>, American soldier, explorer, and politician, 4th Governor of Missouri Territory (d. 1838)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, explorer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri Territory</a> (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, explorer, and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Missouri\" class=\"mw-redirect\" title=\"List of Governors of Missouri\">Governor of Missouri Territory</a> (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of Governors of Missouri", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Missouri"}]}, {"year": "1779", "text": "<PERSON>, American lawyer, author, and poet (d. 1843)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and poet (d. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, author, and poet (d. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON><PERSON><PERSON>, German-Swiss botanist, biologist, and ornithologist (d. 1851)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss botanist, biologist, and ornithologist (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Swiss botanist, biologist, and ornithologist (d. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "1809", "text": "<PERSON>, American colonel and lawyer (d. 1836)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and lawyer (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and lawyer (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American lawyer and politician (d. 1882)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lawyer and politician (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician (d. 1882)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1818", "text": "<PERSON>, American astronomer and academic (d. 1889)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American novelist, short story writer, and poet (d. 1891)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and poet (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Italian opera singer and educator (d. 1918)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian opera singer and educator (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian opera singer and educator (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American lawyer and politician, 35th United States Secretary of War (d. 1926)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a> (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}]}, {"year": "1856", "text": "<PERSON>, Australian footballer and cricketer (d. 1883)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and cricketer (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and cricketer (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, French lawyer and politician, 13th President of France (d. 1937)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gaston_<PERSON>"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1858", "text": "<PERSON>, Austrian organist and composer (d. 1884)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian organist and composer (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON><PERSON><PERSON>, Romanian engineer and explorer (d. 1918)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>san\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and explorer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ba<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and explorer (d. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, Australian cricketer (d. 1951)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, English painter (d. 1917)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Isobe<PERSON>_Lilian_G<PERSON>ag\" title=\"Isobel Lilian Gloag\"><PERSON><PERSON><PERSON></a>, English painter (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>obe<PERSON>_Lilian_G<PERSON>ag\" title=\"Isobel Lilian Gloag\"><PERSON><PERSON><PERSON></a>, English painter (d. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ag"}]}, {"year": "1871", "text": "<PERSON>, American cricketer and soccer player (d. 1969)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cricketer and soccer player (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cricketer and soccer player (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Estonian-English wrestler and strongman (d. 1968)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English wrestler and strongman (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-English wrestler and strongman (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Greek physician and politician, Prime Minister of Greece (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek physician and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1881", "text": "<PERSON>, German mathematician and academic (d. 1940)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and academic (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Hungarian-German chemist and academic, Nobel Prize laureate (d. 1966)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1889", "text": "<PERSON>, German physicist and academic (d. 1979)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, German physicist and academic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, German physicist and academic (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Swiss lawyer and politician, 52nd President of the Swiss Confederation (d. 1968)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1893", "text": "<PERSON> of Greece (d. 1920)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexander_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1920)", "links": [{"title": "<PERSON> of Greece", "link": "https://wikipedia.org/wiki/Alexander_of_Greece"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Italian cyclist (d. 1927)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cyclist (d. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American composer and musical director (d. 1980)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and musical director (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and musical director (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_St<PERSON>ff"}]}, {"year": "1899", "text": "<PERSON>, English race car driver and businessman (d. 1980)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Australian cricketer and rugby player (d. 1965)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and rugby player (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and rugby player (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Filipino boxer (d. 1925)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(boxer)\" title=\"<PERSON><PERSON> (boxer)\"><PERSON></a>, Filipino boxer (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(boxer)\" title=\"<PERSON><PERSON> (boxer)\"><PERSON></a>, Filipino boxer (d. 1925)", "links": [{"title": "<PERSON><PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(boxer)"}]}, {"year": "1903", "text": "<PERSON>, American historian, author, and academic (d. 1995)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American-Canadian astronomer and academic (d. 1993)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian astronomer and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian astronomer and academic (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Sri Lankan-English mountaineer and explorer (d. 1977)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-English mountaineer and explorer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-English mountaineer and explorer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English painter and illustrator (d. 1974)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American pianist and composer (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, German war photographer (d. 1937)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ta<PERSON>\"><PERSON><PERSON><PERSON></a>, German war photographer (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ta<PERSON>\"><PERSON><PERSON><PERSON></a>, German war photographer (d. 1937)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American journalist and cartoonist (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and cartoonist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and cartoonist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Australian politician, 19th Premier of Western Australia (d. 1979)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1912", "text": "<PERSON><PERSON>, German-Venezuelan sculptor and academic (d. 1994)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Gego\" title=\"G<PERSON>\"><PERSON><PERSON></a>, German-Venezuelan sculptor and academic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gego\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Venezuelan sculptor and academic (d. 1994)", "links": [{"title": "Gego", "link": "https://wikipedia.org/wiki/Gego"}]}, {"year": "1912", "text": "<PERSON>, American actor (d. 1999)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1999)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1914", "text": "<PERSON>, American photographer and composer (d. 1997)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and composer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and composer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Australian painter and educator (d. 2015)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(war_artist)\" title=\"<PERSON> (war artist)\"><PERSON></a>, Australian painter and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(war_artist)\" title=\"<PERSON> (war artist)\"><PERSON></a>, Australian painter and educator (d. 2015)", "links": [{"title": "<PERSON> (war artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(war_artist)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, English-Canadian director, producer, and screenwriter (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian director, producer, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Canadian director, producer, and screenwriter (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (d. 2014)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian author and poet (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9bert\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anne_H%C3%A9bert"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON>, American minister and activist (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister and activist (d. 2013)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English author (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Estonian economist and chess player (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian economist and chess player (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian economist and chess player (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ter"}]}, {"year": "1920", "text": "<PERSON>, British paediatric endocrinologist (d. 2010)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British paediatric endocrinologist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British paediatric endocrinologist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American tennis player, sailor, and sportscaster (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player, sailor, and sportscaster (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player, sailor, and sportscaster (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Australian actress (d. 1990)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 1990)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1922", "text": "<PERSON>, Canadian-American actor (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian-American actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)\" title=\"<PERSON> (Canadian actor)\"><PERSON></a>, Canadian-American actor (d. 2006)", "links": [{"title": "<PERSON> (Canadian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_actor)"}]}, {"year": "1924", "text": "<PERSON> of Saudi Arabia (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\"><PERSON> of Saudi Arabia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia\" title=\"<PERSON> of Saudi Arabia\"><PERSON> of Saudi Arabia</a> (d. 2015)", "links": [{"title": "<PERSON> of Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Saudi_Arabia"}]}, {"year": "1924", "text": "<PERSON>, American canoeist (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, American canoeist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, American canoeist (d. 2018)", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1924", "text": "<PERSON>, American actress and singer (d. 2007)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Barbadian cricketer (d. 1967)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Austrian poet and author (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian poet and author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Palestinian politician, founder of the PFLP (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, founder of the <a href=\"https://wikipedia.org/wiki/PFLP\" class=\"mw-redirect\" title=\"PFLP\">PFLP</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician, founder of the <a href=\"https://wikipedia.org/wiki/PFLP\" class=\"mw-redirect\" title=\"PFLP\">PFLP</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "PFLP", "link": "https://wikipedia.org/wiki/PFLP"}]}, {"year": "1926", "text": "<PERSON>, American basketball player and lawyer (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and lawyer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and lawyer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English TV personality (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English TV personality (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English TV personality (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Chilean oceanographer (d. 2006)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_<PERSON>_<PERSON>%C3%B3pez_Boegeholz\" title=\"<PERSON>\"><PERSON></a>, Chilean oceanographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_L%C3%B3pez_Boegeholz\" title=\"<PERSON>\"><PERSON></a>, Chilean oceanographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_L%C3%B3pez_Boegeholz"}]}, {"year": "1927", "text": "<PERSON>, American bishop (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American director, producer, and screenwriter (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, American director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Georgian actress (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian actress (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Afghan educator and politician, Afghan Minister of Foreign Affairs (d. 1979)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Afghan educator and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Afghanistan)\" title=\"Ministry of Foreign Affairs (Afghanistan)\">Afghan Minister of Foreign Affairs</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Afghan educator and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Afghanistan)\" title=\"Ministry of Foreign Affairs (Afghanistan)\">Afghan Minister of Foreign Affairs</a> (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>in"}, {"title": "Ministry of Foreign Affairs (Afghanistan)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Afghanistan)"}]}, {"year": "1929", "text": "<PERSON>, American roller derby racer (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Roller_derby\" title=\"Roller derby\">roller derby</a> racer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Roller_derby\" title=\"Roller derby\">roller derby</a> racer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Roller derby", "link": "https://wikipedia.org/wiki/Roller_derby"}]}, {"year": "1930", "text": "<PERSON>, English composer (d. 1999)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bart\"><PERSON></a>, English composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bart\"><PERSON></a>, English composer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, French sociologist, anthropologist, and philosopher (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, anthropologist, and philosopher (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sociologist, anthropologist, and philosopher (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actress and writer (d. 1991)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and writer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American lieutenant and politician, 62nd United States Secretary of State (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 62nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, 62nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician, 51st Prime Minister of Hungary (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, 51st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician, 51st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_Gr%C3%B3sz"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1930", "text": "<PERSON>, Trinidadian-American actor, singer, dancer, and choreographer (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American actor, singer, dancer, and choreographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-American actor, singer, dancer, and choreographer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON> <PERSON>, American singer-songwriter and guitarist", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>\" title=\"Ramblin' <PERSON>\"><PERSON><PERSON>' <PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>\" title=\"Ramblin' <PERSON>\"><PERSON><PERSON>' <PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "Ramblin' <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>%27_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, South African cricketer (d. 2016)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer (d. 2016)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American-Israeli rabbi and activist, founded the Jewish Defense League (d. 1990)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli rabbi and activist, founded the <a href=\"https://wikipedia.org/wiki/Jewish_Defense_League\" title=\"Jewish Defense League\">Jewish Defense League</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli rabbi and activist, founded the <a href=\"https://wikipedia.org/wiki/Jewish_Defense_League\" title=\"Jewish Defense League\">Jewish Defense League</a> (d. 1990)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Jewish Defense League", "link": "https://wikipedia.org/wiki/Jewish_Defense_League"}]}, {"year": "1933", "text": "<PERSON>, American actor, singer, director, and producer (d. 2009)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> De<PERSON>uise\"><PERSON></a>, American actor, singer, director, and producer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> DeLuise\"><PERSON></a>, American actor, singer, director, and producer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and manager (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and manager (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Indian actress (d. 1972)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actress (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress, producer, and agent (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and agent (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, producer, and agent (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Czech historian and author (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_T%C5%99e%C5%A1t%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech historian and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_T%C5%99e%C5%A1t%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech historian and author (d. 2007)", "links": [{"title": "Dušan T<PERSON>eštík", "link": "https://wikipedia.org/wiki/Du%C5%A1an_T%C5%99e%C5%A1t%C3%ADk"}]}, {"year": "1934", "text": "<PERSON>, New Zealand cricketer (d. 2000)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer (d. 2000)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1934", "text": "<PERSON>, English graphic designer (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English graphic designer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English graphic designer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English cricketer (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON> <PERSON><PERSON>, British biologist, psychologist, and academic (d. 2000)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British biologist, psychologist, and academic (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British biologist, psychologist, and academic (d. 2000)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Algerian-French fashion designer, co-founded Yves Saint Laurent (d. 2008)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, Algerian-French fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" class=\"mw-redirect\" title=\"Yves <PERSON> (brand)\">Yves <PERSON></a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, Algerian-French fashion designer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" class=\"mw-redirect\" title=\"Yves <PERSON> (brand)\">Yves <PERSON></a> (d. 2008)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)"}, {"title": "Yves Saint Laurent (brand)", "link": "https://wikipedia.org/wiki/Yves_<PERSON>_<PERSON>_(brand)"}]}, {"year": "1936", "text": "<PERSON>, English sociologist, radio host, and academic", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist, radio host, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)\" title=\"<PERSON> (sociologist)\"><PERSON></a>, English sociologist, radio host, and academic", "links": [{"title": "<PERSON> (sociologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sociologist)"}]}, {"year": "1937", "text": "<PERSON>, American lawyer and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Al_D%27Amato\" title=\"Al D'Amato\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_D%27Amato\" title=\"Al D'Amato\"><PERSON></a>, American lawyer and politician", "links": [{"title": "Al D'Amato", "link": "https://wikipedia.org/wiki/Al_D%27Amato"}]}, {"year": "1939", "text": "<PERSON>, English-Canadian physician and politician (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian physician and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian physician and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English bishop and theologian (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and theologian (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and theologian (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American author and photographer (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and photographer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Iranian writer and actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian writer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian writer and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, English cricketer and umpire", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American businessman, founded Cendant", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Cendant\" title=\"Cendant\">Cendant</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Cendant\" title=\"Cendant\">Cendant</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cendant", "link": "https://wikipedia.org/wiki/Cendant"}]}, {"year": "1941", "text": "<PERSON>, American captain and politician, 30th United States Secretary of Commerce (d. 1996)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 30th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 30th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "1941", "text": "<PERSON>-<PERSON>, French songwriter and screenwriter (d. 2004)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French songwriter and screenwriter (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French songwriter and screenwriter (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1995)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian actor, director, producer, and screenwriter", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1944", "text": "<PERSON>, Russian banker and politician (d. 1998)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Russian banker and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Russian banker and politician (d. 1998)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1945", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English singer-songwriter, bass player, and guitarist (d. 2006)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, bass player, and guitarist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter, bass player, and guitarist (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American drummer (d. 2011)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian epidemiologist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian epidemiologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian epidemiologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Jamaican poet and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican poet and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican poet and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ison"}]}, {"year": "1947", "text": "<PERSON><PERSON>, French comics creator and artist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French comics creator and artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Montellier\"><PERSON><PERSON></a>, French comics creator and artist", "links": [{"title": "Chantal Montellier", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Israeli-American screenwriter and producer, founded Marvel Studios", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Avi_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American screenwriter and producer, founded <a href=\"https://wikipedia.org/wiki/Marvel_Studios\" title=\"Marvel Studios\">Marvel Studios</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American screenwriter and producer, founded <a href=\"https://wikipedia.org/wiki/Marvel_Studios\" title=\"Marvel Studios\">Marvel Studios</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avi_Arad"}, {"title": "Marvel Studios", "link": "https://wikipedia.org/wiki/Marvel_Studios"}]}, {"year": "1948", "text": "<PERSON>, American football player (d. 2019)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Cliff_Branch\" title=\"Cliff Branch\"><PERSON> Branch</a>, American football player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cliff_Branch\" title=\"Cliff Branch\"><PERSON></a>, American football player (d. 2019)", "links": [{"title": "Cliff Branch", "link": "https://wikipedia.org/wiki/Cliff_Branch"}]}, {"year": "1948", "text": "<PERSON>, English journalist and author (d. 2006)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Australian writer and commentator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian writer and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian writer and commentator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>dt"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Kyrgyzstani politician, 2nd President of Kyrgyzstan", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rman<PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rman<PERSON>\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Kyrgyzstan\" title=\"President of Kyrgyzstan\">President of Kyrgyzstan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kurman<PERSON>_<PERSON>"}, {"title": "President of Kyrgyzstan", "link": "https://wikipedia.org/wiki/President_of_Kyrgyzstan"}]}, {"year": "1949", "text": "<PERSON>, American poet, author, and musician (d. 2009)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and musician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, author, and musician (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player (d. 2009)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nettles\"><PERSON></a>, American football player (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Nettles"}]}, {"year": "1950", "text": "<PERSON>, American basketball player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_coach)\" title=\"<PERSON> (basketball coach)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_coach)\" title=\"<PERSON> (basketball coach)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_coach)"}]}, {"year": "1951", "text": "<PERSON>, Canadian singer-songwriter and guitarist (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1976)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bolin\"><PERSON></a>, American singer-songwriter and guitarist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Serbian philosopher and politician, 6th Prime Minister of Serbia (d. 2003)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>oran_%C4%90in%C4%91i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian philosopher and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%90in%C4%91i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian philosopher and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Serbia\" title=\"Prime Minister of Serbia\">Prime Minister of Serbia</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_%C4%90in%C4%91i%C4%87"}, {"title": "Prime Minister of Serbia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Serbia"}]}, {"year": "1953", "text": "<PERSON>, American blues singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American blues singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Jamaican-Canadian boxer (d. 2006)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-Canadian boxer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American journalist and author", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Benno_M%C3%B6hl<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ben<PERSON>_M%C3%B6hl<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Benno_M%C3%B6hlmann"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, British lawyer (d. 2020)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British lawyer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British lawyer (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actor and screenwriter (d. 2015)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Negron\"><PERSON></a>, American actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Negron\"><PERSON></a>, American actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1958", "text": "<PERSON>, American guitarist and songwriter (d. 2000)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Buck\"><PERSON></a>, American guitarist and songwriter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American basketball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>W<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, guitarist, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American rapper and songwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck D\"><PERSON></a>, American rapper and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck D\"><PERSON></a>, American rapper and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, American rock singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rock singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rock singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, South African boxer (d. 2013)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African boxer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Mexican-American actor and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Demi%C3%A1n_Bichir\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Demi%C3%A1n_Bichir\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Demi%C3%A1n_Bichir"}]}, {"year": "1963", "text": "<PERSON><PERSON>, American rapper, producer, and actor (d. 2022)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, producer, and actor (d. 2022)", "links": [{"title": "Coolio", "link": "https://wikipedia.org/wiki/Coolio"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Japanese astronaut and engineer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese astronaut and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> W<PERSON>\"><PERSON><PERSON></a>, Japanese astronaut and engineer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ta"}]}, {"year": "1963", "text": "<PERSON>, New Zealand singer-songwriter and guitarist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish businesswoman and politician", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businesswoman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businesswoman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English director and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American club promoter and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/James_St._James\" title=\"James St. James\">James St. James</a>, American club promoter and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/James_St._James\" title=\"James St. James\">James St. James</a>, American club promoter and author", "links": [{"title": "James St. James", "link": "https://wikipedia.org/wiki/James_St._James"}]}, {"year": "1967", "text": "<PERSON>, American baseball player and coach", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "Gregg <PERSON>", "link": "https://wikipedia.org/wiki/Gregg_Jefferies"}]}, {"year": "1967", "text": "<PERSON>, Brazilian director, producer and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Padi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Padi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian director, producer and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Padi<PERSON>ha"}]}, {"year": "1968", "text": "<PERSON>, American basketball player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American heavy metal guitarist and songwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heavy metal guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American heavy metal guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and sportscaster", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Estonian footballer and manager", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player and scout", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English cricketer and journalist (d. 2024)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and journalist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1970)\" title=\"<PERSON> (footballer, born 1970)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1970)\" title=\"<PERSON> (footballer, born 1970)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1970)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1970)"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Dutch cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Swedish singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Norwegian footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American historian, economist, and academic", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, economist, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, economist, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American soccer player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Tempestt_Bledsoe\" title=\"Tempest<PERSON> Bledsoe\"><PERSON><PERSON><PERSON> B<PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tempest<PERSON>_Bledsoe\" title=\"Tempestt Bledsoe\"><PERSON><PERSON><PERSON> B<PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON><PERSON>_Bled<PERSON>e"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Belgian runner", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Veer<PERSON>_<PERSON>\" title=\"<PERSON>eer<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veer<PERSON>_<PERSON>\" title=\"<PERSON>eer<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veer<PERSON>_Dejaeghere"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Spanish mountaineer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ed<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>urn<PERSON> Pa<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish mountaineer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edurn<PERSON>_<PERSON>ban"}]}, {"year": "1974", "text": "<PERSON><PERSON>, American journalist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American journalist", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Cher_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Polish cyclist (d. 2014)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski_(cyclist)\" title=\"<PERSON><PERSON> (cyclist)\"><PERSON><PERSON></a>, Polish cyclist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski_(cyclist)\" title=\"<PERSON><PERSON> (cyclist)\"><PERSON><PERSON></a>, Polish cyclist (d. 2014)", "links": [{"title": "<PERSON><PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%84ski_(cyclist)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, South African cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Trinidadian footballer and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian skier", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech author and illustrator", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Vhrsti\" title=\"Vhrsti\">Vhrst<PERSON></a>, Czech author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vhrsti\" title=\"Vhrsti\">Vhrst<PERSON></a>, Czech author and illustrator", "links": [{"title": "Vhrsti", "link": "https://wikipedia.org/wiki/Vhrsti"}]}, {"year": "1976", "text": "<PERSON>, American animator, producer, screenwriter, and voice actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, screenwriter, and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, producer, screenwriter, and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Danish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%B8<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>nu\" title=\"<PERSON>wank<PERSON> Kanu\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>nu\" title=\"<PERSON>wank<PERSON> Kanu\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wank<PERSON>_Kanu"}]}, {"year": "1976", "text": "<PERSON>, Canadian ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Turkish footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Ea%C5%9F\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%9Ea%C5%9F\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%9Ea%C5%9F"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Romanian-Italian rugby player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/C<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Italian rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, French-Moroccan dancer, choreographer, and actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Haspop\" title=\"Haspop\"><PERSON><PERSON></a>, French-Moroccan dancer, choreographer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haspop\" title=\"Haspop\"><PERSON><PERSON></a>, French-Moroccan dancer, choreographer, and actor", "links": [{"title": "Haspop", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American-Canadian football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Darnerien_McCants\" title=\"Darnerien McCants\"><PERSON><PERSON><PERSON> McC<PERSON></a>, American-Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Darnerien_McCants\" title=\"Darnerien McCants\"><PERSON><PERSON><PERSON>cC<PERSON></a>, American-Canadian football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Darnerien_McCants"}]}, {"year": "1977", "text": "<PERSON>, French singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Japanese wrestler and boxer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler and boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tsu"}]}, {"year": "1978", "text": "<PERSON>, Zimbabwean cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish biathlete", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Ferry\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish biathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>\" title=\"B<PERSON><PERSON><PERSON> Ferry\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish biathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6rn_Ferry"}]}, {"year": "1978", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Ghanaian footballer (d. 2019)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Junior_Agogo\" title=\"Junior Agogo\"><PERSON></a>, Ghanaian footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Junior_Agogo\" title=\"Junior Agogo\">Junior <PERSON></a>, Ghanaian footballer (d. 2019)", "links": [{"title": "Junior Agogo", "link": "https://wikipedia.org/wiki/Junior_<PERSON>gogo"}]}, {"year": "1979", "text": "<PERSON>, Australian-New Zealand rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor, director, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian rugby league player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)\" title=\"<PERSON><PERSON> (Brazilian footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)\" title=\"<PERSON><PERSON> (Brazilian footballer)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (Brazilian footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(Brazilian_footballer)"}]}, {"year": "1980", "text": "<PERSON><PERSON>, French decathlete", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French decathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Chilean footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Esteban_Paredes\" title=\"Esteban Paredes\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esteban_Paredes\" title=\"Esteban Paredes\"><PERSON><PERSON><PERSON></a>, Chilean footballer", "links": [{"title": "Este<PERSON>", "link": "https://wikipedia.org/wiki/Esteban_Paredes"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Norwegian journalist and author", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1981)\" title=\"<PERSON> (footballer, born 1981)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer, born 1981)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_born_1981)"}]}, {"year": "1981", "text": "<PERSON>-<PERSON>, English rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Jordanian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Base<PERSON>_Fathi\" title=\"Basem Fathi\"><PERSON><PERSON></a>, Jordanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Base<PERSON>_Fathi\" title=\"Basem Fathi\"><PERSON><PERSON></a>, Jordanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Basem_Fathi"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, English actress, director, and screenwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Montserrat_Lombard\" title=\"Montserrat Lombard\"><PERSON><PERSON><PERSON></a>, English actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Montserrat_Lombard\" title=\"Montserrat Lombard\"><PERSON><PERSON><PERSON></a>, English actress, director, and screenwriter", "links": [{"title": "Montserrat Lombard", "link": "https://wikipedia.org/wiki/Montserrat_Lombard"}]}, {"year": "1983", "text": "<PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, French footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Swiss decathlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American game designer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American game designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian cyclist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nstei<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Scottish-American soccer player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, South African rugby player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tendai_Mtawarira\" title=\"Tendai Mtawarira\"><PERSON><PERSON> Mtawarira</a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tendai_Mtawarira\" title=\"Tendai Mtawarira\"><PERSON><PERSON> Mtawarira</a>, South African rugby player", "links": [{"title": "Tendai Mtawarira", "link": "https://wikipedia.org/wiki/Tendai_Mtawarira"}]}, {"year": "1985", "text": "<PERSON>, Danish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Slovak footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1an_%C5%A0vento\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%A1an_%C5%A0vento\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1an_%C5%A0vento"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Swedish ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A5lman\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A5lman\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anton_Str%C3%A5lman"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)\" title=\"<PERSON> (footballer, born 1986)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1986)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1986)"}]}, {"year": "1986", "text": "<PERSON>, Russian tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English women's footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English women's footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English women's footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ap<PERSON> Pannu\"><PERSON><PERSON><PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nu\" title=\"<PERSON>ap<PERSON> Pannu\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nu"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Scottish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Norwegian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Pat<PERSON>k_Ma%C5%82ecki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ma%C5%82ecki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patryk_Ma%C5%82ecki"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Serbian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Nemanja_Mati%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nemanja_Mati%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nemanja_Mati%C4%87"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B<PERSON>\" title=\"<PERSON> Bumgarner\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bumgarner\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ner"}]}, {"year": "1989", "text": "<PERSON>, Korean American singer, songwriter, and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean American singer, songwriter, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Young\"><PERSON></a>, Korean American singer, songwriter, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Aledmys_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aledmys_D%C3%ADaz\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aledmys_D%C3%ADaz"}]}, {"year": "1990", "text": "<PERSON>, South African rugby player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>otr_Malarczyk\" title=\"<PERSON>otr Malarczyk\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>r_Malarczyk\" title=\"<PERSON>otr Malarczyk\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Piotr_Malarczyk"}]}, {"year": "1991", "text": "<PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Austin_Rivers\" title=\"Austin Rivers\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Rivers\" title=\"Austin Rivers\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Rivers"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Indian actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mr<PERSON><PERSON>_Thakur\" title=\"Mruna<PERSON> Thakur\"><PERSON><PERSON><PERSON>hak<PERSON></a>, Indian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr<PERSON><PERSON>_Thakur\" title=\"Mruna<PERSON> Thakur\"><PERSON><PERSON><PERSON></a>, Indian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Thakur"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/%C3%81lex_Abrines\" title=\"<PERSON>lex Abrines\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81lex_Abrines\" title=\"Álex Abrines\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81lex_Abrines"}]}, {"year": "1993", "text": "<PERSON>, American actor and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gomaa\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1994", "text": "<PERSON><PERSON>, South African rugby player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Japanese singer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wada\" title=\"<PERSON>yaka Wada\"><PERSON><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Wada\" title=\"<PERSON>yaka Wada\"><PERSON><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayaka_Wada"}]}, {"year": "1995", "text": "<PERSON>, American politician", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>orn\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>orn\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>orn"}]}, {"year": "1996", "text": "<PERSON>, British tennis player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, South Korean actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_2001)\" class=\"mw-redirect\" title=\"<PERSON> (actress, born 2001)\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress,_born_2001)\" class=\"mw-redirect\" title=\"<PERSON> (actress, born 2001)\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON> (actress, born 2001)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(actress,_born_2001)"}]}, {"year": "2001", "text": "<PERSON>, Australian rugby league player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Australian-Samoan rugby league player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ali%27i\" title=\"<PERSON>'i\"><PERSON>i</a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ali%27i\" title=\"<PERSON>i\"><PERSON>i</a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27ali%27i"}]}], "Deaths": [{"year": "30 BC", "text": "<PERSON>, Roman general and politician (b. 83 BC)", "html": "30 BC - 30 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman general and politician (b. 83 BC)", "no_year_html": "30 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman general and politician (b. 83 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "371", "text": "<PERSON><PERSON><PERSON> of Vercelli, Italian bishop and saint (b. 283)", "html": "371 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Vercelli\" title=\"<PERSON><PERSON><PERSON> of Vercelli\"><PERSON><PERSON><PERSON> of Vercelli</a>, Italian bishop and saint (b. 283)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Vercelli\" title=\"<PERSON><PERSON><PERSON> of Vercelli\"><PERSON><PERSON><PERSON> of Vercelli</a>, Italian bishop and saint (b. 283)", "links": [{"title": "<PERSON><PERSON><PERSON> of Vercelli", "link": "https://wikipedia.org/wiki/Eusebius_of_Vercelli"}]}, {"year": "527", "text": "<PERSON>, Byzantine emperor (b. 450)", "html": "527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "690s", "text": "<PERSON><PERSON>, abbot and saint", "html": "690s - 690s - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, abbot and saint", "no_year_html": "690s - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, abbot and saint", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "873", "text": "<PERSON><PERSON><PERSON><PERSON>, duke of Thuringia", "html": "873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Thuringia\" title=\"<PERSON><PERSON><PERSON><PERSON>, Duke of Thuringia\"><PERSON><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Thuringia\" title=\"Thuringia\">Thuringia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Thuringia\" title=\"<PERSON><PERSON><PERSON><PERSON>, Duke of Thuringia\"><PERSON><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Thuringia\" title=\"Thuringia\">Thuringia</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Duke of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Duke_of_Thuringia"}, {"title": "Thuringia", "link": "https://wikipedia.org/wiki/Thuringia"}]}, {"year": "946", "text": "<PERSON>, <PERSON><PERSON> vizier (b. 859)", "html": "946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON> vizier (b. 859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON> ibn <PERSON>\"><PERSON></a>, <PERSON><PERSON> vizier (b. 859)", "links": [{"title": "<PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "946", "text": "Lady <PERSON>, Chinese queen (b. 902)", "html": "946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Lady <PERSON></a>, Chinese queen (b. 902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Lady <PERSON></a>, Chinese queen (b. 902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Xu_<PERSON>"}]}, {"year": "953", "text": "<PERSON><PERSON>, Chinese Khitan empress (b. 879)", "html": "953 - <a href=\"https://wikipedia.org/wiki/Shul%C3%BC_Ping\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Chinese Khitan empress (b. 879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shul%C3%BC_Ping\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese Khitan empress (b. 879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shul%C3%BC_Ping"}]}, {"year": "984", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Winchester", "html": "984 - <a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>_of_Winchester\" title=\"<PERSON><PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Winchester\" title=\"Diocese of Winchester\">Winchester</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%86the<PERSON><PERSON>_of_Winchester\" title=\"<PERSON><PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_Winchester\" title=\"Diocese of Winchester\">Winchester</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Winchester", "link": "https://wikipedia.org/wiki/%C3%86thel<PERSON>_of_Winchester"}, {"title": "Diocese of Winchester", "link": "https://wikipedia.org/wiki/Diocese_of_Winchester"}]}, {"year": "1098", "text": "<PERSON><PERSON><PERSON> <PERSON> Le Puy, French papal legate", "html": "1098 - <a href=\"https://wikipedia.org/wiki/<PERSON>hem<PERSON>_of_Le_Puy\" title=\"<PERSON><PERSON><PERSON> of Le Puy\"><PERSON><PERSON><PERSON> of Le Puy</a>, French papal legate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Le_Puy\" title=\"<PERSON><PERSON><PERSON> of Le Puy\"><PERSON><PERSON><PERSON> of Le Puy</a>, French papal legate", "links": [{"title": "<PERSON><PERSON><PERSON> of Le Puy", "link": "https://wikipedia.org/wiki/Adhemar_of_Le_Puy"}]}, {"year": "1137", "text": "<PERSON>, king of France (b. 1081)", "html": "1137 - <a href=\"https://wikipedia.org/wiki/Louis_VI_of_France\" title=\"<PERSON> VI of France\"><PERSON> VI</a>, king of France (b. 1081)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_VI_of_France\" title=\"<PERSON> VI of France\"><PERSON> VI</a>, king of France (b. 1081)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VI_of_France"}]}, {"year": "1146", "text": "Vse<PERSON><PERSON><PERSON> II of Kiev, Russian prince", "html": "1146 - <a href=\"https://wikipedia.org/wiki/Vsevolod_II_of_Kiev\" title=\"Vsevolod II of Kiev\">Vsevolod II of Kiev</a>, Russian prince", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vsevolod_II_of_Kiev\" title=\"Vsevolod II of Kiev\">Vsevolod II of Kiev</a>, Russian prince", "links": [{"title": "Vsevolod II of Kiev", "link": "https://wikipedia.org/wiki/Vsevolod_II_of_Kiev"}]}, {"year": "1227", "text": "<PERSON><PERSON><PERSON>, Japanese warlord (b. 1179)", "html": "1227 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese warlord (b. 1179)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese warlord (b. 1179)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1252", "text": "<PERSON>, Italian archbishop and explorer (b. 1180)", "html": "1252 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian archbishop and explorer (b. 1180)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian archbishop and explorer (b. 1180)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1299", "text": "<PERSON>, Bishop of Strasbourg (b. 1240)", "html": "1299 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lichtenberg\" title=\"<PERSON> of Lichtenberg\"><PERSON></a>, Bishop of Strasbourg (b. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lichtenberg\" title=\"<PERSON> of Lichtenberg\"><PERSON></a>, Bishop of Strasbourg (b. 1240)", "links": [{"title": "<PERSON> of Lichtenberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1402", "text": "<PERSON>, 1st Duke of York, English politician, Lord Warden of the Cinque Ports (b. 1341)", "html": "1402 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York\" title=\"<PERSON>, 1st Duke of York\"><PERSON>, 1st Duke of York</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1341)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York\" title=\"<PERSON>, 1st Duke of York\"><PERSON>, 1st Duke of York</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1341)", "links": [{"title": "<PERSON>, 1st Duke of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_York"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1457", "text": "<PERSON>, Italian author and educator (b. 1406)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and educator (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and educator (b. 1406)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "<PERSON><PERSON><PERSON>, Italian ruler (b. 1386)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1386)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ruler (b. 1386)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cosimo_de%27_Medici"}]}, {"year": "1494", "text": "<PERSON>, artist and father of <PERSON> (b. c. 1435)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, artist and father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. c. 1435)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, artist and father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. c. 1435)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1541", "text": "<PERSON>, German theologian and scholar (b. 1493)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and scholar (b. 1493)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1543", "text": "<PERSON>, Duke of Saxe-Lauenburg (b. 1488)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (b. 1488)", "links": [{"title": "<PERSON>, Duke of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg"}]}, {"year": "1546", "text": "<PERSON>, French Jesuit theologian (b. 1506)", "html": "1546 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Jesuit theologian (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Jesuit theologian (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1557", "text": "<PERSON><PERSON>, Swedish archbishop, historian, and cartographer (b. 1490)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish archbishop, historian, and cartographer (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish archbishop, historian, and cartographer (b. 1490)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON><PERSON>, Polish-German politician and diplomat (b. 1524)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German politician and diplomat (b. 1524)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-German politician and diplomat (b. 1524)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1589", "text": "<PERSON>, French assassin of <PERSON> of France (b. 1567)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ment\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> of France</a> (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ment\" title=\"<PERSON>\"><PERSON></a>, French assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> III of France\"><PERSON> of France</a> (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jacques_Cl%C3%A9ment"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1603", "text": "<PERSON>, English politician (b. 1563)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1563)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1714", "text": "<PERSON>, Queen of Great Britain (b. 1665)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\"><PERSON>, Queen of Great Britain</a> (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain\" title=\"<PERSON>, Queen of Great Britain\"><PERSON>, Queen of Great Britain</a> (b. 1665)", "links": [{"title": "<PERSON>, Queen of Great Britain", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Great_Britain"}]}, {"year": "1787", "text": "<PERSON><PERSON><PERSON>, Italian bishop and saint (b. 1696)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (b. 1696)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON>, Swedish meteorologist, botanist, and entomologist (b. 1735)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish meteorologist, botanist, and entomologist (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish meteorologist, botanist, and entomologist (b. 1735)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "Sir <PERSON>, 2nd Baronet, English colonel and politician (b. 1720)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English colonel and politician (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_2nd_Baronet\" title=\"Sir <PERSON>, 2nd Baronet\">Sir <PERSON>, 2nd Baronet</a>, English colonel and politician (b. 1720)", "links": [{"title": "Sir <PERSON>, 2nd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_2nd_Baronet"}]}, {"year": "1797", "text": "<PERSON>, Finnish church painter (b. 1754)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish church painter (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish church painter (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON><PERSON><PERSON>, French admiral (b. 1753)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>_<PERSON>%27Aigalliers\" title=\"<PERSON><PERSON><PERSON>Aigalliers\"><PERSON><PERSON><PERSON>Ai<PERSON>iers</a>, French admiral (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_d%27Aigalliers\" title=\"<PERSON><PERSON><PERSON>Aigalliers\"><PERSON><PERSON><PERSON>Aigalliers</a>, French admiral (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_d%27Aigalliers"}]}, {"year": "1807", "text": "<PERSON>, English cricketer (b. c. 1754)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. c. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. c. 1754)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_(cricketer)"}]}, {"year": "1807", "text": "<PERSON>, English actor, philologist, and lexicographer (b. 1732)", "html": "1807 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, English actor, philologist, and lexicographer (b. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(lexicographer)\" title=\"<PERSON> (lexicographer)\"><PERSON></a>, English actor, philologist, and lexicographer (b. 1732)", "links": [{"title": "<PERSON> (lexicographer)", "link": "https://wikipedia.org/wiki/<PERSON>_(lexicographer)"}]}, {"year": "1808", "text": "<PERSON>, English painter and illustrator (b. 1734)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English painter and illustrator (b. 1734)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a>, English painter and illustrator (b. 1734)", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON>, Russian general (b. 1763)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1763)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, German publicist and academic (b. 1775)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and academic (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publicist and academic (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON><PERSON> (Regent) of the Sikh Empire (b. 1817)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <PERSON><PERSON><PERSON> (Regent) of the Sikh Empire (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> <PERSON><PERSON><PERSON> (Regent) of the Sikh Empire (b. 1817)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, American tribal chief (b. 1790)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cherokee_chief)\" title=\"<PERSON> (Cherokee chief)\"><PERSON></a>, American tribal chief (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cherokee_chief)\" title=\"<PERSON> (Cherokee chief)\"><PERSON></a>, American tribal chief (b. 1790)", "links": [{"title": "<PERSON> (Cherokee chief)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Cherokee_chief)"}]}, {"year": "1869", "text": "<PERSON>, Australian politician, 7th Premier of Tasmania (b. 1815)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1869", "text": "<PERSON>, French Priest and founder of the Congregation of the Blessed Sacrament (b. 1811)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Priest and founder of the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Blessed_Sacrament\" title=\"Congregation of the Blessed Sacrament\">Congregation of the Blessed Sacrament</a> (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French Priest and founder of the <a href=\"https://wikipedia.org/wiki/Congregation_of_the_Blessed_Sacrament\" title=\"Congregation of the Blessed Sacrament\">Congregation of the Blessed Sacrament</a> (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Congregation of the Blessed Sacrament", "link": "https://wikipedia.org/wiki/Congregation_of_the_Blessed_Sacrament"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, American frontierswoman and scout (b. 1853)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American frontierswoman and scout (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American frontierswoman and scout (b. 1853)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ami<PERSON>_Jane"}]}, {"year": "1905", "text": "<PERSON>, Swedish gymnast and medical student (b. 1875)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B6berg\" title=\"<PERSON>\"><PERSON></a>, Swedish gymnast and medical student (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>j%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish gymnast and medical student (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sj%C3%B6berg"}]}, {"year": "1911", "text": "<PERSON>, American painter and illustrator (b. 1852)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Edwin_<PERSON>_Abbey\" title=\"Edwin Austin Abbey\"><PERSON></a>, American painter and illustrator (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edwin_<PERSON>_Abbey\" title=\"Edwin Austin Abbey\"><PERSON></a>, American painter and illustrator (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American lawyer and politician (b. 1843)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American cowboy and police officer (b. 1854)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cowboy and police officer (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cowboy and police officer (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Indian freedom fighter, lawyer and journalist (b. 1856)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Bal_Gangadhar_Tilak\" title=\"Bal Gangadhar Tilak\"><PERSON><PERSON></a>, Indian freedom fighter, lawyer and journalist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bal_Gangadhar_Tilak\" title=\"Bal Gangadhar Tilak\"><PERSON><PERSON></a>, Indian freedom fighter, lawyer and journalist (b. 1856)", "links": [{"title": "<PERSON><PERSON>adhar Tilak", "link": "https://wikipedia.org/wiki/Bal_Gangadhar_Tilak"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian politician, 19th Premier of Queensland (b. 1876)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Australian politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1876)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Hungarian engineer (b. 1856)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Don%C3%A1t_B%C3%A1nki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian engineer (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don%C3%A1t_B%C3%A1nki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian engineer (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Don%C3%A1t_B%C3%A1nki"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Australian cricketer (b. 1870)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian cricketer (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Australian cricketer (b. 1870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American painter and academic (b. 1862)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Soviet lieutenant and pilot (b. 1921)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet lieutenant and pilot (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet lieutenant and pilot (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Filipino soldier, lawyer, and politician, 2nd President of the Philippines (b. 1878)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>zon"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1957", "text": "<PERSON>, English writer and poet (b. 1877)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and poet (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English writer and poet (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French race car driver (b. 1921)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American poet (b. 1908)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American mass murderer (b. 1941)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Austrian-German biochemist and academic, Nobel Prize Laureate (b. 1900)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> Laureate (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> Laureate (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1970", "text": "<PERSON>, American actress (b. 1913)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American journalist (b. 1901)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, German physician and physiologist, Nobel Prize laureate (b. 1883)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and physiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Italian composer and educator (b. 1882)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and educator (b. 1882)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German soldier and politician (b. 1893)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (b. 1898)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American captain and pilot (b. 1929)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and pilot (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French race car driver (b. 1944)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1919)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author, playwright, and screenwriter (b. 1923)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and screenwriter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Irish Republican, Hunger Striker", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, Hunger Striker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, Hunger Striker", "links": [{"title": "<PERSON> (hunger striker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Sri Lankan lawyer and politician (b. 1933)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Sri Lankan lawyer and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"T<PERSON> <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Sri Lankan lawyer and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English pianist and composer (b. 1937)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, German-Dutch sociologist, author, and academic (b. 1897)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch sociologist, author, and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch sociologist, author, and academic (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Polish-Swiss chemist and academic, Nobel Prize laureate (b. 1897)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>stein"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian physician and surgeon (b. 1929)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Lucille_Teasdale-Corti\" title=\"Lucille Teasdale-Corti\"><PERSON><PERSON>sdale-Corti</a>, Canadian physician and surgeon (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lucille_Teasdale-Corti\" title=\"Lucille Teasdale-Corti\"><PERSON><PERSON>sdale-Corti</a>, Canadian physician and surgeon (b. 1929)", "links": [{"title": "Lucille Teasdale-Corti", "link": "https://wikipedia.org/wiki/Lucille_Teasdale-Corti"}]}, {"year": "1998", "text": "<PERSON>, Hungarian-British actress (b. 1927)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-British actress (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American football player (b. 1974)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er\" title=\"<PERSON><PERSON> Stringer\"><PERSON><PERSON></a>, American football player (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>er\"><PERSON><PERSON></a>, American football player (b. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "2003", "text": "<PERSON>, Belgian footballer, coach, and manager (b. 1922)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer, coach, and manager (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer, coach, and manager (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French actress and screenwriter (b. 1962)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress and screenwriter (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American physicist and author (b. 1913)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and author (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American journalist (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Al_A<PERSON>owitz\" title=\"<PERSON> Aronowitz\"><PERSON></a>, American journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Aronowitz\" title=\"<PERSON> Aronowitz\"><PERSON></a>, American journalist (b. 1928)", "links": [{"title": "Al <PERSON>", "link": "https://wikipedia.org/wiki/Al_Aronowitz"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Dutch cartoonist and educator (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"<PERSON>im <PERSON>\"><PERSON><PERSON></a>, Dutch cartoonist and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wim_<PERSON>\" title=\"<PERSON>im <PERSON>\"><PERSON><PERSON></a>, Dutch cartoonist and educator (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wim_<PERSON>ost"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Dutch painter and sculptor (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Con<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and sculptor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter and sculptor (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Con<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON> of Saudi Arabia (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia\" title=\"Fahd of Saudi Arabia\">Fahd of Saudi Arabia</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia\" title=\"Fahd of Saudi Arabia\">Fahd of Saudi Arabia</a> (b. 1923)", "links": [{"title": "Fahd of Saudi Arabia", "link": "https://wikipedia.org/wiki/Fahd_of_Saudi_Arabia"}]}, {"year": "2006", "text": "<PERSON>, American illustrator (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Thave<PERSON>\" title=\"<PERSON> Thaves\"><PERSON></a>, American illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>have<PERSON>\" title=\"<PERSON> Thaves\"><PERSON></a>, American illustrator (b. 1924)", "links": [{"title": "<PERSON>s", "link": "https://wikipedia.org/wiki/<PERSON>_Thaves"}]}, {"year": "2006", "text": "<PERSON>, American political scientist and activist (b. 1949)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and activist (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and activist (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Irish singer-songwriter and banjo player (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Makem\"><PERSON></a>, Irish singer-songwriter and banjo player (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tommy Makem\"><PERSON></a>, Irish singer-songwriter and banjo player (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Czech-English actor (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-English actor (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian lawyer and politician (b. 1916)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Filipino politician, 11th President of the Philippines (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Puerto Rican-American activist (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Lebr%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American activist (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>r%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Puerto Rican-American activist (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lolita_Lebr%C3%B3n"}]}, {"year": "2010", "text": "<PERSON>, New Zealand rugby player and cricketer (b. 1910)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and cricketer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and cricketer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Italian footballer and agent (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Aldo_Maldera\" title=\"Aldo Maldera\"><PERSON><PERSON></a>, Italian footballer and agent (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldo_Maldera\" title=\"Aldo Maldera\"><PERSON><PERSON></a>, Italian footballer and agent (b. 1953)", "links": [{"title": "Aldo <PERSON>", "link": "https://wikipedia.org/wiki/Aldo_Maldera"}]}, {"year": "2012", "text": "<PERSON>, American composer and musicologist (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and musicologist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and musicologist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English cricketer and academic (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English journalist and critic (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and critic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and critic (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress and producer (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kobe\"><PERSON></a>, American actress and producer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Babe_Martin\" title=\"Babe Martin\"><PERSON></a>, American baseball player (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babe_Martin\" title=\"Babe Martin\"><PERSON></a>, American baseball player (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American cellist and educator (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and educator (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cellist and educator (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American football player (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian footballer and manager (b. 1973)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian footballer and manager (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Norwegian author (b. 1974)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author (b. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian author (b. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English radio and television host (b. 1955)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio and television host (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio and television host (b. 1955)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "2015", "text": "<PERSON>, German footballer and manager (b. 1968)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, English singer and actress (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Cilla_Black\" title=\"Cilla Black\"><PERSON><PERSON></a>, English singer and actress (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cilla_Black\" title=\"Cilla Black\"><PERSON><PERSON></a>, English singer and actress (b. 1943)", "links": [{"title": "Cilla Black", "link": "https://wikipedia.org/wiki/<PERSON>illa_Black"}]}, {"year": "2015", "text": "<PERSON>, French physicist, philosopher, and author (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Espagnat\" title=\"<PERSON>\"><PERSON></a>, French physicist, philosopher, and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Espagnat\" title=\"<PERSON>\"><PERSON></a>, French physicist, philosopher, and author (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_d%27Espagnat"}]}, {"year": "2015", "text": "<PERSON>, English-Canadian physician and politician (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian physician and politician (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian physician and politician (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese footballer and manager (b. 1948)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Hong_Yuanshuo\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_<PERSON>shuo\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hong_<PERSON>o"}]}, {"year": "2016", "text": "<PERSON> of Romania (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Romania\" title=\"<PERSON> of Romania\">Queen <PERSON> of Romania</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anne_<PERSON>_Romania\" title=\"<PERSON> of Romania\">Queen <PERSON> of Romania</a> (b. 1923)", "links": [{"title": "<PERSON> of Romania", "link": "https://wikipedia.org/wiki/Anne_of_Romania"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American actor and singer (b. 1934)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_Brimley\" title=\"<PERSON><PERSON><PERSON> Brimley\"><PERSON><PERSON><PERSON></a>, American actor and singer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON> Brimley\"><PERSON><PERSON><PERSON></a>, American actor and singer (b. 1934)", "links": [{"title": "Wilford Brimley", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>rimley"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American professional football player (b. 1966)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional football player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American professional football player (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American poker player (b. 1945)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON><PERSON><PERSON>-Sufi, Scottish Islamic scholar and writer (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_as-Sufi\" title=\"<PERSON><PERSON><PERSON><PERSON> as-Sufi\"><PERSON><PERSON><PERSON><PERSON> as-Sufi</a>, Scottish Islamic scholar and writer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_as-Sufi\" title=\"<PERSON><PERSON><PERSON><PERSON> as-Sufi\"><PERSON><PERSON><PERSON><PERSON> as-Sufi</a>, Scottish Islamic scholar and writer (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> as-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_as-Sufi"}]}, {"year": "2021", "text": "<PERSON>, American assistant director, production manager and occasional actor (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assistant director, production manager and occasional actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American assistant director, production manager and occasional actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American writer and artist (b. 1952)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and artist (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}