{"date": "December 20", "url": "https://wikipedia.org/wiki/December_20", "data": {"Events": [{"year": "69", "text": "<PERSON><PERSON> enters Rome to claim the title of Emperor for <PERSON>'s former general <PERSON><PERSON><PERSON><PERSON>.", "html": "69 - AD 69 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Primus\" class=\"mw-redirect\" title=\"<PERSON>ius Primus\"><PERSON><PERSON>rim<PERSON></a> enters <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> to claim the title of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> for <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\"><PERSON></a>'s former general <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a>.", "no_year_html": "AD 69 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Primus\" class=\"mw-redirect\" title=\"Antonius Primus\"><PERSON><PERSON> Primus</a> enters <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> to claim the title of <a href=\"https://wikipedia.org/wiki/Roman_emperor\" title=\"Roman emperor\">Emperor</a> for <a href=\"https://wikipedia.org/wiki/Nero\" title=\"Nero\">Nero</a>'s former general <a href=\"https://wikipedia.org/wiki/Vespasian\" title=\"Vespasian\">Vespasian</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton<PERSON>_Primus"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "Roman emperor", "link": "https://wikipedia.org/wiki/Roman_emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nero"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vespasian"}]}, {"year": "1192", "text": "<PERSON> of England is captured and imprisoned by <PERSON> of Austria on his way home to England after the Third Crusade.", "html": "1192 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is captured and imprisoned by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a> of Austria on his way home to England after the <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> is captured and imprisoned by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON></a> of Austria on his way home to England after the <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/Leopold_<PERSON>,_Duke_of_Austria"}, {"title": "Third Crusade", "link": "https://wikipedia.org/wiki/Third_Crusade"}]}, {"year": "1334", "text": "<PERSON> <PERSON>, a Cistercian monk, is elected Pope <PERSON>.", "html": "1334 - <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">Cardinal</a> <PERSON>, a <a href=\"https://wikipedia.org/wiki/Cistercians\" title=\"Cistercians\">Cistercian</a> monk, is elected <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">Cardinal</a> <PERSON>, a <a href=\"https://wikipedia.org/wiki/Cistercians\" title=\"Cistercians\">Cistercian</a> monk, is elected <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Benedict <PERSON>\">Pope <PERSON></a>.", "links": [{"title": "<PERSON> (Catholic Church)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)"}, {"title": "Cistercians", "link": "https://wikipedia.org/wiki/Cistercians"}, {"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "The Louisiana Purchase is completed at a ceremony in New Orleans.", "html": "1803 - The <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> is completed at a ceremony in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Louisiana_Purchase\" title=\"Louisiana Purchase\">Louisiana Purchase</a> is completed at a ceremony in <a href=\"https://wikipedia.org/wiki/New_Orleans\" title=\"New Orleans\">New Orleans</a>.", "links": [{"title": "Louisiana Purchase", "link": "https://wikipedia.org/wiki/Louisiana_Purchase"}, {"title": "New Orleans", "link": "https://wikipedia.org/wiki/New_Orleans"}]}, {"year": "1808", "text": "Peninsular War: The Siege of Zaragoza begins.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Siege_of_Zaragoza\" class=\"mw-redirect\" title=\"Second Siege of Zaragoza\">Siege of Zaragoza</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Siege_of_Zaragoza\" class=\"mw-redirect\" title=\"Second Siege of Zaragoza\">Siege of Zaragoza</a> begins.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Second Siege of Zaragoza", "link": "https://wikipedia.org/wiki/Second_Siege_of_Zaragoza"}]}, {"year": "1832", "text": "HMS Clio under the command of Captain <PERSON><PERSON> arrives at Port Egmont under orders to take possession of the Falkland Islands.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/HMS_Clio_(1807)\" title=\"HMS Clio (1807)\">HMS <i>Clio</i></a> under the command of Captain <PERSON><PERSON> arrives at <a href=\"https://wikipedia.org/wiki/Port_Egmont\" title=\"Port Egmont\">Port Egmont</a> under orders to <a href=\"https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)\" title=\"Reassertion of British sovereignty over the Falkland Islands (1833)\">take possession</a> of the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Clio_(1807)\" title=\"HMS Clio (1807)\">HMS <i>Clio</i></a> under the command of Captain <PERSON><PERSON> arrives at <a href=\"https://wikipedia.org/wiki/Port_Egmont\" title=\"Port Egmont\">Port Egmont</a> under orders to <a href=\"https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)\" title=\"Reassertion of British sovereignty over the Falkland Islands (1833)\">take possession</a> of the <a href=\"https://wikipedia.org/wiki/Falkland_Islands\" title=\"Falkland Islands\">Falkland Islands</a>.", "links": [{"title": "HMS Clio (1807)", "link": "https://wikipedia.org/wiki/HMS_Clio_(1807)"}, {"title": "Port Egmont", "link": "https://wikipedia.org/wiki/Port_Egmont"}, {"title": "Reassertion of British sovereignty over the Falkland Islands (1833)", "link": "https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)"}, {"title": "Falkland Islands", "link": "https://wikipedia.org/wiki/Falkland_Islands"}]}, {"year": "1848", "text": "French presidential election: Having won the popular vote in a landslide, <PERSON> is inaugurated in the chamber of the National Assembly as the first (and only) president of the French Second Republic.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/1848_French_presidential_election\" title=\"1848 French presidential election\">French presidential election</a>: Having won the popular vote in a landslide, <a href=\"https://wikipedia.org/wiki/Napoleon_III\" title=\"Napoleon III\"><PERSON></a> is inaugurated in the chamber of the <a href=\"https://wikipedia.org/wiki/Palais_Bourbon\" title=\"Palais Bourbon\">National Assembly</a> as the first (and only) president of the <a href=\"https://wikipedia.org/wiki/French_Second_Republic\" title=\"French Second Republic\">French Second Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1848_French_presidential_election\" title=\"1848 French presidential election\">French presidential election</a>: Having won the popular vote in a landslide, <a href=\"https://wikipedia.org/wiki/Napoleon_III\" title=\"Napoleon III\"><PERSON></a> is inaugurated in the chamber of the <a href=\"https://wikipedia.org/wiki/Palais_Bourbon\" title=\"Palais Bourbon\">National Assembly</a> as the first (and only) president of the <a href=\"https://wikipedia.org/wiki/French_Second_Republic\" title=\"French Second Republic\">French Second Republic</a>.", "links": [{"title": "1848 French presidential election", "link": "https://wikipedia.org/wiki/1848_French_presidential_election"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_III"}, {"title": "Palais Bourbon", "link": "https://wikipedia.org/wiki/Palais_Bourbon"}, {"title": "French Second Republic", "link": "https://wikipedia.org/wiki/French_Second_Republic"}]}, {"year": "1860", "text": "South Carolina becomes the first state to attempt to secede from the United States with the South Carolina Declaration of Secession.", "html": "1860 - <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> becomes the first state to attempt to <a href=\"https://wikipedia.org/wiki/Secession\" title=\"Secession\">secede</a> from the United States with the <a href=\"https://wikipedia.org/wiki/South_Carolina_Declaration_of_Secession\" title=\"South Carolina Declaration of Secession\">South Carolina Declaration of Secession</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> becomes the first state to attempt to <a href=\"https://wikipedia.org/wiki/Secession\" title=\"Secession\">secede</a> from the United States with the <a href=\"https://wikipedia.org/wiki/South_Carolina_Declaration_of_Secession\" title=\"South Carolina Declaration of Secession\">South Carolina Declaration of Secession</a>.", "links": [{"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "Secession", "link": "https://wikipedia.org/wiki/Secession"}, {"title": "South Carolina Declaration of Secession", "link": "https://wikipedia.org/wiki/South_Carolina_Declaration_of_Secession"}]}, {"year": "1915", "text": "World War I: The last Australian troops are evacuated from Gallipoli.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The last Australian troops are evacuated from <a href=\"https://wikipedia.org/wiki/Gallipoli_Campaign\" class=\"mw-redirect\" title=\"Gallipoli Campaign\">Gallipoli</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The last Australian troops are evacuated from <a href=\"https://wikipedia.org/wiki/Gallipoli_Campaign\" class=\"mw-redirect\" title=\"Gallipoli Campaign\">Gallipoli</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Gallipoli Campaign", "link": "https://wikipedia.org/wiki/Gallipoli_Campaign"}]}, {"year": "1917", "text": "Cheka, the first Soviet secret police force, is founded.", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Cheka</a>, the first <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> secret police force, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cheka\" title=\"Cheka\">Cheka</a>, the first <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet</a> secret police force, is founded.", "links": [{"title": "Cheka", "link": "https://wikipedia.org/wiki/Cheka"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1924", "text": "<PERSON> is released from Landsberg Prison.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> is released from <a href=\"https://wikipedia.org/wiki/Landsberg_Prison\" title=\"Landsberg Prison\">Landsberg Prison</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> is released from <a href=\"https://wikipedia.org/wiki/Landsberg_Prison\" title=\"Landsberg Prison\">Landsberg Prison</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Landsberg Prison", "link": "https://wikipedia.org/wiki/Landsberg_Prison"}]}, {"year": "1940", "text": "Captain America Comics #1, containing the first appearance of the superhero Captain <PERSON>, is published.", "html": "1940 - <i><a href=\"https://wikipedia.org/wiki/Captain_America_Comics\" title=\"Captain America Comics\">Captain America Comics</a></i> #1, containing the first appearance of the superhero <a href=\"https://wikipedia.org/wiki/Captain_America\" title=\"Captain America\">Captain America</a>, is published.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Captain_America_Comics\" title=\"Captain America Comics\">Captain America Comics</a></i> #1, containing the first appearance of the superhero <a href=\"https://wikipedia.org/wiki/Captain_America\" title=\"Captain America\">Captain America</a>, is published.", "links": [{"title": "Captain America Comics", "link": "https://wikipedia.org/wiki/Captain_America_Comics"}, {"title": "Captain <PERSON>", "link": "https://wikipedia.org/wiki/Captain_America"}]}, {"year": "1941", "text": "World War II: First battle of the American Volunteer Group, better known as the \"Flying Tigers\", in Kunming, China.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First battle of the <a href=\"https://wikipedia.org/wiki/American_Volunteer_Group\" title=\"American Volunteer Group\">American Volunteer Group</a>, better known as the \"<a href=\"https://wikipedia.org/wiki/Flying_Tigers\" title=\"Flying Tigers\">Flying Tigers</a>\", in <a href=\"https://wikipedia.org/wiki/Kunming\" title=\"Kunming\">Kunming</a>, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: First battle of the <a href=\"https://wikipedia.org/wiki/American_Volunteer_Group\" title=\"American Volunteer Group\">American Volunteer Group</a>, better known as the \"<a href=\"https://wikipedia.org/wiki/Flying_Tigers\" title=\"Flying Tigers\">Flying Tigers</a>\", in <a href=\"https://wikipedia.org/wiki/Kunming\" title=\"Kunming\">Kunming</a>, China.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "American Volunteer Group", "link": "https://wikipedia.org/wiki/American_Volunteer_Group"}, {"title": "Flying Tigers", "link": "https://wikipedia.org/wiki/Flying_Tigers"}, {"title": "Ku<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kunming"}]}, {"year": "1942", "text": "World War II: Japanese air forces bomb Calcutta, India.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army_Air_Service\" title=\"Imperial Japanese Army Air Service\">Japanese air forces</a> bomb <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">India</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army_Air_Service\" title=\"Imperial Japanese Army Air Service\">Japanese air forces</a> bomb <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Calcutta</a>, <a href=\"https://wikipedia.org/wiki/British_Raj\" title=\"British Raj\">India</a>.", "links": [{"title": "Imperial Japanese Army Air Service", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army_Air_Service"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}, {"title": "British Raj", "link": "https://wikipedia.org/wiki/British_Raj"}]}, {"year": "1946", "text": "It's a Wonderful Life premieres at the Globe Theatre in New York to mixed reviews. ", "html": "1946 - <i><a href=\"https://wikipedia.org/wiki/It%27s_a_Wonderful_Life\" title=\"It's a Wonderful Life\">It's a Wonderful Life</a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Globe_Theatre_(Broadway,_New_York_City)\" class=\"mw-redirect\" title=\"Globe Theatre (Broadway, New York City)\">Globe Theatre</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York</a> to mixed reviews. <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://web.archive.org/web/20130119035038/http://www.eeweems.com/capra/_wonderful_life.html\">[1]</a>", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/It%27s_a_Wonderful_Life\" title=\"It's a Wonderful Life\">It's a Wonderful Life</a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Globe_Theatre_(Broadway,_New_York_City)\" class=\"mw-redirect\" title=\"Globe Theatre (Broadway, New York City)\">Globe Theatre</a> in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York</a> to mixed reviews. <a rel=\"nofollow\" class=\"external text\" href=\"https://wikipedia.orghttps://web.archive.org/web/20130119035038/http://www.eeweems.com/capra/_wonderful_life.html\">[1]</a>", "links": [{"title": "It's a Wonderful Life", "link": "https://wikipedia.org/wiki/It%27s_a_Wonderful_Life"}, {"title": "Globe Theatre (Broadway, New York City)", "link": "https://wikipedia.org/wiki/Globe_Theatre_(Broadway,_New_York_City)"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1946", "text": "An earthquake in Nankaidō, Japan causes a tsunami which kills at least one thousand people and destroys 36,000 homes.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/1946_Nankai_earthquake\" title=\"1946 Nankai earthquake\">An earthquake in Nankaidō, Japan</a> causes a tsunami which kills at least one thousand people and destroys 36,000 homes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1946_Nankai_earthquake\" title=\"1946 Nankai earthquake\">An earthquake in Nankaidō, Japan</a> causes a tsunami which kills at least one thousand people and destroys 36,000 homes.", "links": [{"title": "1946 Nankai earthquake", "link": "https://wikipedia.org/wiki/1946_Nankai_earthquake"}]}, {"year": "1948", "text": "Indonesian National Revolution: The Dutch military captures Yogyakarta, the temporary capital of the newly formed Republic of Indonesia.", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a>: The Dutch military <a href=\"https://wikipedia.org/wiki/Operation_Kraai\" title=\"Operation Kraai\">captures Yogyakarta</a>, the temporary capital of the newly formed Republic of Indonesia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indonesian_National_Revolution\" title=\"Indonesian National Revolution\">Indonesian National Revolution</a>: The Dutch military <a href=\"https://wikipedia.org/wiki/Operation_Kraai\" title=\"Operation Kraai\">captures Yogyakarta</a>, the temporary capital of the newly formed Republic of Indonesia.", "links": [{"title": "Indonesian National Revolution", "link": "https://wikipedia.org/wiki/Indonesian_National_Revolution"}, {"title": "Operation Kraai", "link": "https://wikipedia.org/wiki/Operation_Kraai"}]}, {"year": "1951", "text": "The EBR-1 in Arco, Idaho becomes the first nuclear power plant to generate electricity. The electricity powered four light bulbs.", "html": "1951 - The <a href=\"https://wikipedia.org/wiki/Experimental_Breeder_Reactor_I\" title=\"Experimental Breeder Reactor I\">EBR-1</a> in <a href=\"https://wikipedia.org/wiki/Arco,_Idaho\" title=\"Arco, Idaho\">Arco, Idaho</a> becomes the first <a href=\"https://wikipedia.org/wiki/Nuclear_power\" title=\"Nuclear power\">nuclear power plant</a> to generate electricity. The electricity powered four light bulbs.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Experimental_Breeder_Reactor_I\" title=\"Experimental Breeder Reactor I\">EBR-1</a> in <a href=\"https://wikipedia.org/wiki/Arco,_Idaho\" title=\"Arco, Idaho\">Arco, Idaho</a> becomes the first <a href=\"https://wikipedia.org/wiki/Nuclear_power\" title=\"Nuclear power\">nuclear power plant</a> to generate electricity. The electricity powered four light bulbs.", "links": [{"title": "Experimental Breeder Reactor I", "link": "https://wikipedia.org/wiki/Experimental_Breeder_Reactor_I"}, {"title": "Arco, Idaho", "link": "https://wikipedia.org/wiki/Arco,_Idaho"}, {"title": "Nuclear power", "link": "https://wikipedia.org/wiki/Nuclear_power"}]}, {"year": "1952", "text": "A United States Air Force C-124 crashes and burns in Moses Lake, Washington, killing 87 of the 115 people on board.", "html": "1952 - A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">C-124</a> <a href=\"https://wikipedia.org/wiki/1952_Moses_Lake_C-124_crash\" title=\"1952 Moses Lake C-124 crash\">crashes</a> and burns in <a href=\"https://wikipedia.org/wiki/Moses_Lake,_Washington\" title=\"Moses Lake, Washington\">Moses Lake, Washington</a>, killing 87 of the 115 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II\" title=\"Douglas C-124 Globemaster II\">C-124</a> <a href=\"https://wikipedia.org/wiki/1952_Moses_Lake_C-124_crash\" title=\"1952 Moses Lake C-124 crash\">crashes</a> and burns in <a href=\"https://wikipedia.org/wiki/Moses_Lake,_Washington\" title=\"Moses Lake, Washington\">Moses Lake, Washington</a>, killing 87 of the 115 people on board.", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Douglas C-124 Globemaster II", "link": "https://wikipedia.org/wiki/Douglas_C-124_Globemaster_II"}, {"title": "1952 Moses Lake C-124 crash", "link": "https://wikipedia.org/wiki/1952_Moses_Lake_C-124_crash"}, {"title": "Moses Lake, Washington", "link": "https://wikipedia.org/wiki/Moses_Lake,_Washington"}]}, {"year": "1955", "text": "Cardiff is proclaimed the capital city of Wales, United Kingdom.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Cardiff\" title=\"Cardiff\">Cardiff</a> is proclaimed the capital city of <a href=\"https://wikipedia.org/wiki/Wales\" title=\"Wales\">Wales</a>, United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cardiff\" title=\"Cardiff\">Cardiff</a> is proclaimed the capital city of <a href=\"https://wikipedia.org/wiki/Wales\" title=\"Wales\">Wales</a>, United Kingdom.", "links": [{"title": "Cardiff", "link": "https://wikipedia.org/wiki/Cardiff"}, {"title": "Wales", "link": "https://wikipedia.org/wiki/Wales"}]}, {"year": "1957", "text": "The initial production version of the Boeing 707 makes its first flight.", "html": "1957 - The initial production version of the <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> makes its first flight.", "no_year_html": "The initial production version of the <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> makes its first flight.", "links": [{"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}]}, {"year": "1960", "text": "Vietnam War: The National Liberation Front of South Vietnam, popularly known as the Viet Cong, is formally established in Tân Lập village, present day Tây Ninh province.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">National Liberation Front of South Vietnam</a>, popularly known as the Viet Cong, is formally established in Tân Lập village, present day <a href=\"https://wikipedia.org/wiki/T%C3%A2y_Ninh\" title=\"Tây Ninh\">Tây Ninh province</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">National Liberation Front of South Vietnam</a>, popularly known as the Viet Cong, is formally established in Tân Lập village, present day <a href=\"https://wikipedia.org/wiki/T%C3%A2y_Ninh\" title=\"Tây Ninh\">Tây Ninh province</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%<PERSON>y_<PERSON>nh"}]}, {"year": "1967", "text": "A Pennsylvania Railroad Budd Metroliner exceeds 249 kilometres per hour (155 mph) on their New York Division, also present-day Amtrak's Northeast Corridor.", "html": "1967 - A <a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a> <a href=\"https://wikipedia.org/wiki/Budd_Metroliner\" title=\"Budd Metroliner\">Budd Metroliner</a> exceeds 249 kilometres per hour (155 mph) on their New York Division, also present-day <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a>'s Northeast Corridor.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Pennsylvania_Railroad\" title=\"Pennsylvania Railroad\">Pennsylvania Railroad</a> <a href=\"https://wikipedia.org/wiki/Budd_Metroliner\" title=\"Budd Metroliner\">Budd Metroliner</a> exceeds 249 kilometres per hour (155 mph) on their New York Division, also present-day <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a>'s Northeast Corridor.", "links": [{"title": "Pennsylvania Railroad", "link": "https://wikipedia.org/wiki/Pennsylvania_Railroad"}, {"title": "Budd Metroliner", "link": "https://wikipedia.org/wiki/Budd_Metroliner"}, {"title": "Amtrak", "link": "https://wikipedia.org/wiki/Amtrak"}]}, {"year": "1968", "text": "The Zodiac Killer murders his first two officially confirmed victims, <PERSON> and <PERSON>, on Lake Herman Road in Benicia, California, United States.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Zodiac_Killer\" title=\"Zodiac Killer\">Zodiac Killer</a> murders his first two officially confirmed victims, <PERSON> and <PERSON>, on Lake Herman Road in <a href=\"https://wikipedia.org/wiki/Benicia,_California\" title=\"Benicia, California\">Benicia, California</a>, United States.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Zodiac_Killer\" title=\"Zodiac Killer\">Zodiac Killer</a> murders his first two officially confirmed victims, <PERSON> and <PERSON>, on Lake Herman Road in <a href=\"https://wikipedia.org/wiki/Benicia,_California\" title=\"Benicia, California\">Benicia, California</a>, United States.", "links": [{"title": "Zodiac Killer", "link": "https://wikipedia.org/wiki/Zod<PERSON>_Killer"}, {"title": "Benicia, California", "link": "https://wikipedia.org/wiki/Benicia,_California"}]}, {"year": "1970", "text": "Koza riot: After a series of hit-and-runs and other vehicular incidents involving American service personnel, roughly 5,000 Okinawans take to the streets, clashing with American law enforcement in protest against the U.S. occupation of Okinawa.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Koza_riot\" title=\"Koza riot\">Koza riot</a>: After a series of hit-and-runs and other vehicular incidents involving <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American</a> service personnel, roughly 5,000 <a href=\"https://wikipedia.org/wiki/Ryukyuan_people\" class=\"mw-redirect\" title=\"Ryukyuan people\">Okinawans</a> take to the streets, clashing with American law enforcement in protest against the <a href=\"https://wikipedia.org/wiki/United_States_Civil_Administration_of_the_Ryukyu_Islands\" title=\"United States Civil Administration of the Ryukyu Islands\">U.S. occupation of Okinawa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Koza_riot\" title=\"Koza riot\">Koza riot</a>: After a series of hit-and-runs and other vehicular incidents involving <a href=\"https://wikipedia.org/wiki/United_States\" title=\"United States\">American</a> service personnel, roughly 5,000 <a href=\"https://wikipedia.org/wiki/Ryukyuan_people\" class=\"mw-redirect\" title=\"Ryukyuan people\">Okinawans</a> take to the streets, clashing with American law enforcement in protest against the <a href=\"https://wikipedia.org/wiki/United_States_Civil_Administration_of_the_Ryukyu_Islands\" title=\"United States Civil Administration of the Ryukyu Islands\">U.S. occupation of Okinawa</a>.", "links": [{"title": "Koza riot", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_riot"}, {"title": "United States", "link": "https://wikipedia.org/wiki/United_States"}, {"title": "Ryukyuan people", "link": "https://wikipedia.org/wiki/Ryukyuan_people"}, {"title": "United States Civil Administration of the Ryukyu Islands", "link": "https://wikipedia.org/wiki/United_States_Civil_Administration_of_the_Ryukyu_Islands"}]}, {"year": "1973", "text": "Assassination of <PERSON>: A car bomb planted by ETA in Madrid kills three people, including the Prime Minister of Spain, Admiral <PERSON>.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">Assassination of <PERSON></a>: A <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a> planted by <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> kills three people, including the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>, Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>\" title=\"Assassination of <PERSON>\">Assassination of <PERSON></a>: A <a href=\"https://wikipedia.org/wiki/Car_bomb\" title=\"Car bomb\">car bomb</a> planted by <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> kills three people, including the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a>, Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Assassination of Luis <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Car bomb", "link": "https://wikipedia.org/wiki/Car_bomb"}, {"title": "ETA (separatist group)", "link": "https://wikipedia.org/wiki/ETA_(separatist_group)"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "With the approval of the State Council, China’s two largest newspapers, the People’s Daily and the Guangming Daily, publish in full for the first time the Second Chinese Character Simplification Scheme.", "html": "1977 - With the approval of the <a href=\"https://wikipedia.org/wiki/State_Council_of_the_People%E2%80%99s_Republic_of_China\" class=\"mw-redirect\" title=\"State Council of the People’s Republic of China\">State Council</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>’s two largest newspapers, the <i><a href=\"https://wikipedia.org/wiki/People%E2%80%99s_Daily\" class=\"mw-redirect\" title=\"People’s Daily\">People’s Daily</a></i> and the <i><a href=\"https://wikipedia.org/wiki/Guangming_Daily\" title=\"Guangming Daily\">Guangming Daily</a></i>, publish in full for the first time the <a href=\"https://wikipedia.org/wiki/Second_round_of_simplified_Chinese_characters\" title=\"Second round of simplified Chinese characters\">Second Chinese Character Simplification Scheme</a>.", "no_year_html": "With the approval of the <a href=\"https://wikipedia.org/wiki/State_Council_of_the_People%E2%80%99s_Republic_of_China\" class=\"mw-redirect\" title=\"State Council of the People’s Republic of China\">State Council</a>, <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>’s two largest newspapers, the <i><a href=\"https://wikipedia.org/wiki/People%E2%80%99s_Daily\" class=\"mw-redirect\" title=\"People’s Daily\">People’s Daily</a></i> and the <i><a href=\"https://wikipedia.org/wiki/Guangming_Daily\" title=\"Guangming Daily\">Guangming Daily</a></i>, publish in full for the first time the <a href=\"https://wikipedia.org/wiki/Second_round_of_simplified_Chinese_characters\" title=\"Second round of simplified Chinese characters\">Second Chinese Character Simplification Scheme</a>.", "links": [{"title": "State Council of the People’s Republic of China", "link": "https://wikipedia.org/wiki/State_Council_of_the_People%E2%80%99s_Republic_of_China"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "People’s Daily", "link": "https://wikipedia.org/wiki/People%E2%80%99s_Daily"}, {"title": "Guangming Daily", "link": "https://wikipedia.org/wiki/Guangming_Daily"}, {"title": "Second round of simplified Chinese characters", "link": "https://wikipedia.org/wiki/Second_round_of_simplified_Chinese_characters"}]}, {"year": "1984", "text": "The Summit Tunnel fire, one of the largest transportation tunnel fires in history, burns after a freight train carrying over one million liters of gasoline derails near the town of Todmorden, England, in the Pennines.", "html": "1984 - The <a href=\"https://wikipedia.org/wiki/Summit_Tunnel_fire\" title=\"Summit Tunnel fire\">Summit Tunnel fire</a>, one of the largest transportation tunnel fires in history, burns after a <a href=\"https://wikipedia.org/wiki/Freight_rail_transport\" class=\"mw-redirect\" title=\"Freight rail transport\">freight train</a> carrying over one million liters of <a href=\"https://wikipedia.org/wiki/Gasoline\" title=\"Gasoline\">gasoline</a> derails near the town of <a href=\"https://wikipedia.org/wiki/Todmorden\" title=\"Todmorden\">Todmorden</a>, England, in the <a href=\"https://wikipedia.org/wiki/Pennines\" title=\"Pennines\">Pennines</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Summit_Tunnel_fire\" title=\"Summit Tunnel fire\">Summit Tunnel fire</a>, one of the largest transportation tunnel fires in history, burns after a <a href=\"https://wikipedia.org/wiki/Freight_rail_transport\" class=\"mw-redirect\" title=\"Freight rail transport\">freight train</a> carrying over one million liters of <a href=\"https://wikipedia.org/wiki/Gasoline\" title=\"Gasoline\">gasoline</a> derails near the town of <a href=\"https://wikipedia.org/wiki/Todmorden\" title=\"Todmorden\">Todmorden</a>, England, in the <a href=\"https://wikipedia.org/wiki/Pennines\" title=\"Pennines\">Pennines</a>.", "links": [{"title": "Summit Tunnel fire", "link": "https://wikipedia.org/wiki/Summit_Tunnel_fire"}, {"title": "Freight rail transport", "link": "https://wikipedia.org/wiki/Freight_rail_transport"}, {"title": "Gasoline", "link": "https://wikipedia.org/wiki/Gasoline"}, {"title": "Todmorden", "link": "https://wikipedia.org/wiki/Todmorden"}, {"title": "Pennines", "link": "https://wikipedia.org/wiki/Pennines"}]}, {"year": "1984", "text": "Disappearance of <PERSON><PERSON> from Greeley, Colorado.  Her remains were discovered on July 23, 2019, located about 24 km (15 mi) southeast of <PERSON><PERSON>'s home. The cause of death \"was a gunshot wound to the head.\"", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Disappearance_and_death_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Disappearance and death of <PERSON><PERSON>\">Disappearance of <PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Gree<PERSON>,_Colorado\" title=\"Greeley, Colorado\">Greeley, Colorado</a>. Her remains were discovered on July 23, 2019, located about 24 km (15 mi) southeast of <PERSON><PERSON>'s home. The cause of death \"was a gunshot wound to the head.\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Disappearance_and_death_of_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Disappearance and death of <PERSON><PERSON>\">Disappearance of <PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>,_Colorado\" title=\"Greeley, Colorado\">Greeley, Colorado</a>. Her remains were discovered on July 23, 2019, located about 24 km (15 mi) southeast of <PERSON><PERSON>'s home. The cause of death \"was a gunshot wound to the head.\"", "links": [{"title": "Disappearance and death of <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Disappearance_and_death_of_<PERSON><PERSON>_<PERSON>"}, {"title": "Greeley, Colorado", "link": "https://wikipedia.org/wiki/Greeley,_Colorado"}]}, {"year": "1985", "text": "Pope <PERSON> announces the institution of World Youth Day.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> announces the institution of <a href=\"https://wikipedia.org/wiki/World_Youth_Day\" title=\"World Youth Day\">World Youth Day</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> announces the institution of <a href=\"https://wikipedia.org/wiki/World_Youth_Day\" title=\"World Youth Day\">World Youth Day</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "World Youth Day", "link": "https://wikipedia.org/wiki/World_Youth_Day"}]}, {"year": "1987", "text": "In the worst peacetime sea disaster, the passenger ferry Doña Paz sinks after colliding with the oil tanker MT Vector in the Tablas Strait of the Philippines, killing an estimated 4,000 people (1,749 official).", "html": "1987 - In the worst peacetime sea disaster, the passenger ferry <i><a href=\"https://wikipedia.org/wiki/MV_Do%C3%B1a_Paz\" title=\"MV Doña Paz\">Doña Paz</a></i> sinks after colliding with the oil tanker <a href=\"https://wikipedia.org/wiki/MT_Vector\" title=\"MT Vector\">MT <i>Vector</i></a> in the <a href=\"https://wikipedia.org/wiki/Tablas_Strait\" title=\"Tablas Strait\">Tablas Strait</a> of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, killing an estimated 4,000 people (1,749 official).", "no_year_html": "In the worst peacetime sea disaster, the passenger ferry <i><a href=\"https://wikipedia.org/wiki/MV_Do%C3%B1a_Paz\" title=\"MV Doña Paz\">Doña Paz</a></i> sinks after colliding with the oil tanker <a href=\"https://wikipedia.org/wiki/MT_Vector\" title=\"MT Vector\">MT <i>Vector</i></a> in the <a href=\"https://wikipedia.org/wiki/Tablas_Strait\" title=\"Tablas Strait\">Tablas Strait</a> of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, killing an estimated 4,000 people (1,749 official).", "links": [{"title": "MV Doña Paz", "link": "https://wikipedia.org/wiki/MV_Do%C3%B1a_Paz"}, {"title": "MT Vector", "link": "https://wikipedia.org/wiki/MT_Vector"}, {"title": "Tablas Strait", "link": "https://wikipedia.org/wiki/Tablas_Strait"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1988", "text": "War on drugs: The United Nations agrees upon and promulgates the United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances, one of three major drug control treaties currently in force.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/War_on_drugs\" title=\"War on drugs\">War on drugs</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> agrees upon and promulgates the <a href=\"https://wikipedia.org/wiki/United_Nations_Convention_Against_Illicit_Traffic_in_Narcotic_Drugs_and_Psychotropic_Substances\" title=\"United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances\">United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances</a>, one of three major drug control treaties currently in force.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_on_drugs\" title=\"War on drugs\">War on drugs</a>: The <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> agrees upon and promulgates the <a href=\"https://wikipedia.org/wiki/United_Nations_Convention_Against_Illicit_Traffic_in_Narcotic_Drugs_and_Psychotropic_Substances\" title=\"United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances\">United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances</a>, one of three major drug control treaties currently in force.", "links": [{"title": "War on drugs", "link": "https://wikipedia.org/wiki/War_on_drugs"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "United Nations Convention Against Illicit Traffic in Narcotic Drugs and Psychotropic Substances", "link": "https://wikipedia.org/wiki/United_Nations_Convention_Against_Illicit_Traffic_in_Narcotic_Drugs_and_Psychotropic_Substances"}]}, {"year": "1989", "text": "The United States invasion of Panama deposes <PERSON>.", "html": "1989 - The <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Panama\" title=\"United States invasion of Panama\">United States invasion of Panama</a> <a href=\"https://wikipedia.org/wiki/Deposition_(politics)\" class=\"mw-redirect\" title=\"Deposition (politics)\">deposes</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Panama\" title=\"United States invasion of Panama\">United States invasion of Panama</a> <a href=\"https://wikipedia.org/wiki/Deposition_(politics)\" class=\"mw-redirect\" title=\"Deposition (politics)\">deposes</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States invasion of Panama", "link": "https://wikipedia.org/wiki/United_States_invasion_of_Panama"}, {"title": "Deposition (politics)", "link": "https://wikipedia.org/wiki/Deposition_(politics)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "A Missouri court sentences the Palestinian militant <PERSON><PERSON> and his wife <PERSON> to death for the honor killing of their daughter <PERSON><PERSON><PERSON>.", "html": "1991 - A Missouri court sentences the Palestinian militant <PERSON><PERSON> and his wife <PERSON> to death for the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">honor killing of their daughter <PERSON><PERSON><PERSON></a>.", "no_year_html": "A Missouri court sentences the Palestinian militant <PERSON><PERSON> and his wife <PERSON> to death for the <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">honor killing of their daughter <PERSON><PERSON><PERSON></a>.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "NATO begins peacekeeping in Bosnia.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> begins peacekeeping in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO</a> begins peacekeeping in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia</a>.", "links": [{"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1995", "text": "American Airlines Flight 965, a Boeing 757, crashes into a mountain 50 km north of Cali, Colombia, killing 159 of the 163 people on board.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_965\" title=\"American Airlines Flight 965\">American Airlines Flight 965</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_757\" title=\"Boeing 757\">Boeing 757</a>, crashes into a mountain 50 km north of <a href=\"https://wikipedia.org/wiki/Cali\" title=\"Cali\">Cali</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 159 of the 163 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_965\" title=\"American Airlines Flight 965\">American Airlines Flight 965</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_757\" title=\"Boeing 757\">Boeing 757</a>, crashes into a mountain 50 km north of <a href=\"https://wikipedia.org/wiki/Cali\" title=\"Cali\">Cali</a>, <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a>, killing 159 of the 163 people on board.", "links": [{"title": "American Airlines Flight 965", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_965"}, {"title": "Boeing 757", "link": "https://wikipedia.org/wiki/Boeing_757"}, {"title": "Cali", "link": "https://wikipedia.org/wiki/Cali"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}]}, {"year": "1999", "text": "Macau is handed over to China by Portugal.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a> is <a href=\"https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Macau\" class=\"mw-redirect\" title=\"Transfer of sovereignty over Macau\">handed over</a> to <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> by <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Macau\" title=\"Macau\">Macau</a> is <a href=\"https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Macau\" class=\"mw-redirect\" title=\"Transfer of sovereignty over Macau\">handed over</a> to <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> by <a href=\"https://wikipedia.org/wiki/Portugal\" title=\"Portugal\">Portugal</a>.", "links": [{"title": "Macau", "link": "https://wikipedia.org/wiki/Macau"}, {"title": "Transfer of sovereignty over Macau", "link": "https://wikipedia.org/wiki/Transfer_of_sovereignty_over_Macau"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "Portugal", "link": "https://wikipedia.org/wiki/Portugal"}]}, {"year": "2004", "text": "A gang of thieves steal £26.5 million worth of currency from the Donegall Square West headquarters of Northern Bank in Belfast, Northern Ireland, United Kingdom, one of the largest bank robberies in British history.", "html": "2004 - A gang of thieves <a href=\"https://wikipedia.org/wiki/Northern_Bank_robbery\" title=\"Northern Bank robbery\">steal £26.5 million</a> worth of currency from the <a href=\"https://wikipedia.org/wiki/Donegall_Square\" title=\"Donegall Square\">Donegall Square</a> West headquarters of <a href=\"https://wikipedia.org/wiki/Northern_Bank\" class=\"mw-redirect\" title=\"Northern Bank\">Northern Bank</a> in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, United Kingdom, one of the largest <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robberies</a> in British history.", "no_year_html": "A gang of thieves <a href=\"https://wikipedia.org/wiki/Northern_Bank_robbery\" title=\"Northern Bank robbery\">steal £26.5 million</a> worth of currency from the <a href=\"https://wikipedia.org/wiki/Donegall_Square\" title=\"Donegall Square\">Donegall Square</a> West headquarters of <a href=\"https://wikipedia.org/wiki/Northern_Bank\" class=\"mw-redirect\" title=\"Northern Bank\">Northern Bank</a> in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>, United Kingdom, one of the largest <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robberies</a> in British history.", "links": [{"title": "Northern Bank robbery", "link": "https://wikipedia.org/wiki/Northern_Bank_robbery"}, {"title": "Donegall Square", "link": "https://wikipedia.org/wiki/Donegall_Square"}, {"title": "Northern Bank", "link": "https://wikipedia.org/wiki/Northern_Bank"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "Bank robbery", "link": "https://wikipedia.org/wiki/Bank_robbery"}]}, {"year": "2004", "text": "Cave Story releases to the public. ", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Cave_Story\" title=\"Cave Story\">Cave Story</a> releases to the public. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cave_Story\" title=\"Cave Story\">Cave Story</a> releases to the public. ", "links": [{"title": "Cave Story", "link": "https://wikipedia.org/wiki/Cave_Story"}]}, {"year": "2007", "text": "<PERSON> becomes the oldest monarch in the history of the United Kingdom, surpassing Queen <PERSON>, who lived for 81 years and 243 days.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a> becomes the oldest monarch in the history of the United Kingdom, surpassing <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a>, who lived for 81 years and 243 days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a> becomes the oldest monarch in the history of the United Kingdom, surpassing <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>\" title=\"Queen <PERSON>\">Queen <PERSON></a>, who lived for 81 years and 243 days.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "2007", "text": "The Portrait of <PERSON> (1904), by the Spanish artist <PERSON>, and <PERSON> by Brazilian modernist painter <PERSON><PERSON><PERSON><PERSON>, are stolen from the São Paulo Museum of Art in Brazil. Both will be recovered a few weeks later.", "html": "2007 - The <i><a href=\"https://wikipedia.org/wiki/Portrait_of_<PERSON>_<PERSON>\" title=\"Portrait of <PERSON>\">Portrait of <PERSON></a></i> (1904), by the Spanish artist <a href=\"https://wikipedia.org/wiki/<PERSON>_Picasso\" title=\"Pablo Picasso\"><PERSON></a>, and <i>O Lavrador de Café</i> by Brazilian modernist painter <a href=\"https://wikipedia.org/wiki/C%C3%A2ndido_Portinari\" class=\"mw-redirect\" title=\"Cândido Portinari\"><PERSON><PERSON><PERSON><PERSON></a>, are stolen from the <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_Museum_of_Art\" title=\"São Paulo Museum of Art\">São Paulo Museum of Art</a> in Brazil. Both will be recovered a few weeks later.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Portrait_of_<PERSON>_<PERSON>\" title=\"Portrait of <PERSON>\">Portrait of <PERSON></a></i> (1904), by the Spanish artist <a href=\"https://wikipedia.org/wiki/<PERSON>_Picasso\" title=\"Pablo Picasso\"><PERSON></a>, and <i>O Lavrador de <PERSON></i> by Brazilian modernist painter <a href=\"https://wikipedia.org/wiki/C%C3%A2ndido_Portinari\" class=\"mw-redirect\" title=\"Cândido Portinari\">C<PERSON><PERSON><PERSON></a>, are stolen from the <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Paulo_Museum_of_Art\" title=\"São Paulo Museum of Art\">São Paulo Museum of Art</a> in Brazil. Both will be recovered a few weeks later.", "links": [{"title": "Portrait of <PERSON>", "link": "https://wikipedia.org/wiki/Portrait_of_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A2ndido_Portinari"}, {"title": "São Paulo Museum of Art", "link": "https://wikipedia.org/wiki/S%C3%A3o_Paulo_Museum_of_Art"}]}, {"year": "2019", "text": "The United States Space Force becomes the first new branch of the United States Armed Forces since 1947.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/United_States_Space_Force\" title=\"United States Space Force\">United States Space Force</a> becomes the first new branch of the United States Armed Forces since 1947.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Space_Force\" title=\"United States Space Force\">United States Space Force</a> becomes the first new branch of the United States Armed Forces since 1947.", "links": [{"title": "United States Space Force", "link": "https://wikipedia.org/wiki/United_States_Space_Force"}]}], "Births": [{"year": "1494", "text": "<PERSON><PERSON>, French mathematician and cartographer (d. 1555)", "html": "1494 - <a href=\"https://wikipedia.org/wiki/Oronce_Fin%C3%A9\" class=\"mw-redirect\" title=\"Oronce Finé\"><PERSON><PERSON></a>, French mathematician and cartographer (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oronce_Fin%C3%A9\" class=\"mw-redirect\" title=\"Oronce Finé\"><PERSON><PERSON></a>, French mathematician and cartographer (d. 1555)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oronce_Fin%C3%A9"}]}, {"year": "1496", "text": "<PERSON>, historian and physician (d. 1575)", "html": "1496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, historian and physician (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, historian and physician (d. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1537", "text": "<PERSON>, king of Sweden (d. 1592)", "html": "1537 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> III of Sweden\"><PERSON></a>, king of Sweden (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON></a>, king of Sweden (d. 1592)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1576", "text": "<PERSON>, Moravian priest and saint (d. 1620)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Moravian priest and saint (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Moravian priest and saint (d. 1620)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON><PERSON> <PERSON>, German scholar and politician (d. 1692)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German scholar and politician (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, German scholar and politician (d. 1692)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1629", "text": "<PERSON>, Dutch painter (d. 1684)", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter (d. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1641", "text": "<PERSON>, Swedish chemist, geologist, and physician (d. 1724)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/Urban_Hj%C3%A4rne\" title=\"Urban Hjärne\"><PERSON></a>, Swedish chemist, geologist, and physician (d. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urban_Hj%C3%A4rne\" title=\"Urban Hjärne\"><PERSON></a>, Swedish chemist, geologist, and physician (d. 1724)", "links": [{"title": "Urban Hjärne", "link": "https://wikipedia.org/wiki/Urban_Hj%C3%A4rne"}]}, {"year": "1740", "text": "<PERSON>, American physician and diplomat (d. 1792)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American physician and diplomat (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON> (diplomat)\"><PERSON></a>, American physician and diplomat (d. 1792)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(diplomat)"}]}, {"year": "1786", "text": "<PERSON>, Italian composer (d. 1853)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, French painter and educator (d. 1845)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French painter and educator (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, Mexican general and president (1855) (d. 1871)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Carrera\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (1855) (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Carrera\" title=\"<PERSON>\"><PERSON></a>, Mexican general and president (1855) (d. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Carrera"}]}, {"year": "1812", "text": "<PERSON>, American poet and educator (d. 1842)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Thurston\" class=\"mw-redirect\" title=\"<PERSON> Thurston\"><PERSON></a>, American poet and educator (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Thurston\" class=\"mw-redirect\" title=\"<PERSON> Thurston\"><PERSON></a>, American poet and educator (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>on"}]}, {"year": "1838", "text": "<PERSON>, English theologian, author, and educator (d. 1926)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian, author, and educator (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English theologian, author, and educator (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1841", "text": "<PERSON>, French academic and politician, Nobel Prize laureate (d. 1932)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1851", "text": "<PERSON><PERSON>, Swedish economist (d. 1926)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Knut_Wicksell\" title=\"Knut Wicksell\"><PERSON><PERSON></a>, Swedish economist (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_Wicksell\" title=\"Knut Wicksell\"><PERSON><PERSON></a>, Swedish economist (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wicksell"}]}, {"year": "1861", "text": "<PERSON>, German actor (d. 1933)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Ferdinand_<PERSON>\" title=\"Ferdinand <PERSON>\"><PERSON></a>, German actor (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_<PERSON>\" title=\"Ferdinand <PERSON>\"><PERSON></a>, German actor (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ferdinand_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Slovenian painter (d. 1926)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian painter (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian painter (d. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American actress and interior decorator (d. 1950)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and interior decorator (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and interior decorator (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, American businessman, founded the Firestone Tire and Rubber Company (d. 1938)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>stone\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company\" title=\"Firestone Tire and Rubber Company\">Firestone Tire and Rubber Company</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>stone\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company\" title=\"Firestone Tire and Rubber Company\">Firestone Tire and Rubber Company</a> (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>stone"}, {"title": "Firestone Tire and Rubber Company", "link": "https://wikipedia.org/wiki/Firestone_Tire_and_Rubber_Company"}]}, {"year": "1869", "text": "<PERSON>, American actor (d. 1956)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American composer and conductor (d. 1937)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese historian, author, and academic (d. 1948)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Kan%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese historian, author, and academic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese historian, author, and academic (d. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%27ichi_<PERSON><PERSON><PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Turkish poet, academic, and politician (d. 1936)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, academic, and politician (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, academic, and politician (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American baseball player and manager (d. 1965)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON>, Indonesian activist and journalist (d. 1972)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian activist and journalist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian activist and journalist (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ddus"}]}, {"year": "1886", "text": "<PERSON>, American tennis player and businessman (d. 1974)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and businessman (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and businessman (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, German-Israeli historian and academic (d. 1980)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Israeli historian and academic (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Israeli historian and academic (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American baseball player and manager (d. 1956)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, French pianist, actress and singer (d. 1958)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, actress and singer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pianist, actress and singer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Czech chemist and academic, Nobel Prize laureate (d. 1967)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Heyrovsk%C3%BD"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1891", "text": "<PERSON>, Swedish triple jumper (d. 1971)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON>\"><PERSON></a>, Swedish triple jumper (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6f\" title=\"<PERSON>\"><PERSON></a>, Swedish triple jumper (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Erik_Alml%C3%B6f"}]}, {"year": "1894", "text": "<PERSON>, Australian lawyer and politician, 12th Prime Minister of Australia (d. 1978)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Greek general and politician, 156th Prime Minister of Greece (d. 1973)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 156th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general and politician, 156th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a> (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1898", "text": "<PERSON>, American actress and singer (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Welsh preacher and physician (d. 1981)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh preacher and physician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh preacher and physician (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, German actress (d. 1964)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rna\" title=\"<PERSON>ssy Arna\"><PERSON><PERSON></a>, German actress (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rna\" title=\"<PERSON><PERSON> Arna\"><PERSON><PERSON></a>, German actress (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Li<PERSON>_<PERSON>rna"}]}, {"year": "1900", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1972)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American physicist and academic, invented the <PERSON> generator (d. 1967)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_generator\" title=\"<PERSON> generator\"><PERSON> generator</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_generator\" title=\"<PERSON> generator\"><PERSON> generator</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> generator", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_generator"}]}, {"year": "1902", "text": "<PERSON>, Duke of Kent (d. 1942)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a> (d. 1942)", "links": [{"title": "<PERSON>, Duke of Kent", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent"}]}, {"year": "1902", "text": "<PERSON>, American philosopher and author (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hook\"><PERSON></a>, American philosopher and author (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sidney_Hook"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, American baseball player, coach, and manager (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian author (d. 1977)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Yevgenia_Ginzburg\" title=\"Yevgenia <PERSON>\">Ye<PERSON><PERSON><PERSON></a>, Russian author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yevgenia_Ginzburg\" title=\"Yevgenia <PERSON>\">Ye<PERSON><PERSON><PERSON></a>, Russian author (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yevgenia_Ginzburg"}]}, {"year": "1905", "text": "<PERSON>, Australian cricketer and sportscaster (d. 1992)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and sportscaster (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer and sportscaster (d. 1992)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27R<PERSON><PERSON>_(cricketer)"}]}, {"year": "1907", "text": "<PERSON>, American soldier and songwriter (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and songwriter (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and songwriter (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American actor and singer (d. 1994)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist and politician (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Vak<PERSON><PERSON>_<PERSON>\" title=\"Vakko<PERSON> Majeed\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ak<PERSON><PERSON>_<PERSON>\" title=\"Vakko<PERSON> Majeed\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vak<PERSON><PERSON>_<PERSON>eed"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, American author (d. 2009)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1914", "text": "<PERSON>, American lieutenant, publisher, and politician (d. 2013)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American lieutenant, publisher, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr<PERSON>\"><PERSON> Jr.</a>, American lieutenant, publisher, and politician (d. 2013)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1915", "text": "<PERSON>, Turkish author and poet (d. 1995)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish author and poet (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish author and poet (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian trade union leader and activist (d. 2010)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader and activist (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader and activist (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American-English physicist, neuropsychologist, and philosopher (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English physicist, neuropsychologist, and philosopher (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English physicist, neuropsychologist, and philosopher (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Turkish poet and author (d. 1997)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Cahit_K%C3%BClebi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cahit_K%C3%BClebi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet and author (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cahit_K%C3%BClebi"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Canadian trade union leader and politician, 43rd Secretary of State for Canada (d. 1988)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader and politician, 43rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trade union leader and politician, 43rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish author (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON>na\" title=\"Väinö Lin<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%A4in%C3%B6_<PERSON>na\" title=\"Väin<PERSON> Linna\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish author (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%A4in%C3%B6_Linna"}]}, {"year": "1921", "text": "<PERSON>, American director, producer, and screenwriter (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American sculptor and painter (d. 2020)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Beverly_Pepper\" title=\"Beverly Pepper\"><PERSON></a>, American sculptor and painter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beverly_Pepper\" title=\"Beverly Pepper\"><PERSON></a>, American sculptor and painter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Chinese-Indonesian businessman and co-founder of Astra International (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Indonesian businessman and co-founder of <a href=\"https://wikipedia.org/wiki/Astra_International\" title=\"Astra International\">Astra International</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Indonesian businessman and co-founder of <a href=\"https://wikipedia.org/wiki/Astra_International\" title=\"Astra International\">Astra International</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Astra International", "link": "https://wikipedia.org/wiki/Astra_International"}]}, {"year": "1924", "text": "<PERSON>, American actor and comedian (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Canadian soldier, lawyer, and politician, 42nd Secretary of State for Canada (d. 1980)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soldier, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1925", "text": "<PERSON>, Italian footballer (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Welsh lawyer and politician, Deputy Prime Minister of the United Kingdom (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1926", "text": "<PERSON>, German lawyer and politician, German Federal Minister of Economics (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Economics_and_Energy\" class=\"mw-redirect\" title=\"Federal Ministry of Economics and Energy\">German Federal Minister of Economics</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_Economics_and_Energy\" class=\"mw-redirect\" title=\"Federal Ministry of Economics and Energy\">German Federal Minister of Economics</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Federal Ministry of Economics and Energy", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_Economics_and_Energy"}]}, {"year": "1927", "text": "<PERSON>, 22nd Seigneur of Sark, English engineer and politician (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_22nd_Seigneur_of_Sark\" title=\"<PERSON>, 22nd Seigneur of Sark\"><PERSON>, 22nd Seigneur of Sark</a>, English engineer and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_22nd_Seigneur_of_Sark\" title=\"<PERSON>, 22nd Seigneur of Sark\"><PERSON>, 22nd Seigneur of Sark</a>, English engineer and politician (d. 2016)", "links": [{"title": "<PERSON>, 22nd Seigneur of Sark", "link": "https://wikipedia.org/wiki/<PERSON>,_22nd_Seigneur_of_Sark"}]}, {"year": "1927", "text": "<PERSON>, American sportscaster (d. 2016)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American sportscaster (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, American sportscaster (d. 2016)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1927", "text": "<PERSON>, South Korean soldier and politician, 7th President of South Korea (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean soldier and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (d. 2015)", "links": [{"title": "<PERSON>m", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>m"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1928", "text": "<PERSON>, Austrian-American pediatric and writer (d. 2008)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pediatric and writer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American pediatric and writer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American basketball player (d. 1961)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lage"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American actress (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "links": [{"title": "Mala Powers", "link": "https://wikipedia.org/wiki/Mala_Powers"}]}, {"year": "1932", "text": "<PERSON>, American actor (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Finnish runner", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>i_<PERSON>en\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>i_<PERSON>en"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Belgian cyclist (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian cyclist (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Pakistani cricketer and sportscaster (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer and sportscaster (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Pakistani cricketer and sportscaster (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American soul singer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Pakistani lawyer and judge, Chief Justice of Pakistan (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani lawyer and judge, <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Pakistan\" title=\"Chief Justice of Pakistan\">Chief Justice of Pakistan</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Pakistan", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Pakistan"}]}, {"year": "1942", "text": "<PERSON>, American sprinter and football player (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and football player (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, French banker and economist", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French banker and economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French banker and economist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian classical pianist, composer, conductor, teacher and human rights activist.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian classical pianist, composer, conductor, teacher and human rights activist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian classical pianist, composer, conductor, teacher and human rights activist.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian television host and journalist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Australian television host and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" title=\"<PERSON> (television presenter)\"><PERSON></a>, Australian television host and journalist", "links": [{"title": "<PERSON> (television presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter, drummer, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, drummer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, drummer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian-Singaporean lawyer and author (d. 2010)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>vakan<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Singaporean lawyer and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vakan<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian-Singaporean lawyer and author (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vakant_Tiwari"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Israeli-English magician and psychic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-English magician and psychic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-English magician and psychic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American basketball player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American basketball player", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1946", "text": "<PERSON>, American politician, 31st United States Secretary of Agriculture, 81st Governor of Georgia", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 31st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>, 81st <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 31st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a>, 81st <a href=\"https://wikipedia.org/wiki/Governor_of_Georgia\" title=\"Governor of Georgia\">Governor of Georgia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Agriculture", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture"}, {"title": "Governor of Georgia", "link": "https://wikipedia.org/wiki/Governor_of_Georgia"}]}, {"year": "1946", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian singer-songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English keyboard player and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Japanese pianist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Malian engineer and politician (d. 2020)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Souma%C3%AFla_Ciss%C3%A9\" title=\"Soumaïla Cissé\"><PERSON><PERSON><PERSON><PERSON></a>, Malian engineer and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Souma%C3%AFla_Ciss%C3%A9\" title=\"Soumaïla Cissé\"><PERSON><PERSON><PERSON><PERSON></a>, Malian engineer and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Souma%C3%AFla_Ciss%C3%A9"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Mexican-American composer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Mexican-American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rquez\" title=\"<PERSON>\"><PERSON></a>, Mexican-American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_M%C3%A1rquez"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Baroness <PERSON>, Northern Irish academic and police ombudsman", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>ual<PERSON>_<PERSON>%27L<PERSON>,_Baroness_<PERSON>%27Loan\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, Northern Irish academic and police ombudsman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27L<PERSON>,_Baroness_<PERSON>%27Loan\" title=\"<PERSON><PERSON><PERSON>, Baroness <PERSON>\"><PERSON><PERSON><PERSON>, Baroness <PERSON></a>, Northern Irish academic and police ombudsman", "links": [{"title": "<PERSON><PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uala_O%27Loan,_<PERSON>_<PERSON>%27Loan"}]}, {"year": "1951", "text": "<PERSON>, American author and activist (d. 2013)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>tter\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American author and poet", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, German politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Turkish lawyer and politician, Turkish Minister of Transport", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Binali_Y%C4%B1ld%C4%B1r%C4%B1m\" title=\"Binali Yıldırım\"><PERSON><PERSON>ldırım</a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport,_Maritime_and_Communication_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Transport, Maritime and Communication (Turkey)\">Turkish Minister of Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Binali_Y%C4%B1ld%C4%B1r%C4%B1m\" title=\"Binali Yıldırım\"><PERSON><PERSON>ldırım</a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Transport,_Maritime_and_Communication_(Turkey)\" class=\"mw-redirect\" title=\"Ministry of Transport, Maritime and Communication (Turkey)\">Turkish Minister of Transport</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Binali_Y%C4%B1ld%C4%B1r%C4%B1m"}, {"title": "Ministry of Transport, Maritime and Communication (Turkey)", "link": "https://wikipedia.org/wiki/Ministry_of_Transport,_Maritime_and_Communication_(Turkey)"}]}, {"year": "1956", "text": "<PERSON>, Mauritanian general and politician, President of Mauritania", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Mauritania\" title=\"List of heads of state of Mauritania\">President of Mauritania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mauritanian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Mauritania\" title=\"List of heads of state of Mauritania\">President of Mauritania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Mauritania", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Mauritania"}]}, {"year": "1956", "text": "<PERSON>, American keyboard player and songwriter (d. 2009)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Guy_<PERSON>\" title=\"Guy Babylon\"><PERSON></a>, American keyboard player and songwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guy_Babylon\" title=\"Guy Babylon\"><PERSON></a>, American keyboard player and songwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Japanese wrestler", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rata"}]}, {"year": "1956", "text": "<PERSON>, Scottish geologist and businessman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Scottish geologist and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, Scottish geologist and businessman", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "1956", "text": "<PERSON>, American disco/R&B singer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disco/R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American disco/R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Cypriot singer-songwriter and actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cypriot singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American high jumper", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American biologist and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cell_biologist)\" title=\"<PERSON> (cell biologist)\"><PERSON></a>, American biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cell_biologist)\" title=\"<PERSON> (cell biologist)\"><PERSON></a>, American biologist and academic", "links": [{"title": "<PERSON> (cell biologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cell_biologist)"}]}, {"year": "1959", "text": "<PERSON>, Scottish scientist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, German runner", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Hildegard_K%C3%B6rner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hildegard_K%C3%B6rner\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hildegard_K%C3%B6rner"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish physicist and politician, 12th Prime Minister of Poland", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish physicist and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish physicist and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Poland\" title=\"Prime Minister of Poland\">Prime Minister of Poland</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Poland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Poland"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Jamaican-Canadian author and educator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-Canadian author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-Canadian author and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, South Korean director, producer, and screenwriter (d. 2020)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean director, producer, and screenwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean director, producer, and screenwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "1961", "text": "<PERSON>, Egyptian singer-songwriter and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Russian-American figure skater and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1968", "text": "<PERSON>, Austrian racing driver", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Swiss-English philosopher and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-English philosopher and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Moroccan runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moroccan runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>z"}]}, {"year": "1970", "text": "<PERSON>, Canadian actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Zimbabwean cricketer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1972", "text": "<PERSON>, Czech ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jan_%C4%8Caloun\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_%C4%8Caloun\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_%C4%8Caloun"}]}, {"year": "1972", "text": "<PERSON>, Norwegian guitarist, songwriter, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Japanese guitarist, songwriter, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Japanese guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, Japanese guitarist, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Croatian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Nenad_Vu%C4%8Dkovi%C4%87_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nenad_Vu%C4%8Dkovi%C4%87_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Nenad_Vu%C4%8Dkovi%C4%87_(footballer)"}]}, {"year": "1978", "text": "<PERSON><PERSON>-sang, South Korean singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-sang\" title=\"<PERSON><PERSON>-sang\"><PERSON><PERSON>-sang</a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-sang\" title=\"<PERSON><PERSON>-sang\"><PERSON><PERSON>-sang</a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>-sang", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-sang"}]}, {"year": "1978", "text": "<PERSON>, Russian-Canadian ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Russian-Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Cameroon footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroon footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroon footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, French runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Tahri\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Tahri\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Tahri\" title=\"Bo<PERSON><PERSON><PERSON>ah Tahri\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Ta<PERSON>i"}]}, {"year": "1979", "text": "<PERSON>, Australian cyclist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1980", "text": "<PERSON>, Mexican footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Israel_Castro\" title=\"Israel Castro\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Castro\" title=\"Israel Castro\"><PERSON></a>, Mexican footballer", "links": [{"title": "Israel Castro", "link": "https://wikipedia.org/wiki/Israel_Castro"}]}, {"year": "1980", "text": "<PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French-Portuguese footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Argentine footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON><PERSON>helis"}]}, {"year": "1981", "text": "<PERSON>, American basketball player and coach", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>vey\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Ivey\"><PERSON></a>, American basketball player and coach", "links": [{"title": "Royal Ivey", "link": "https://wikipedia.org/wiki/Royal_Ivey"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, Pakistani cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cricketer)"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Danish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hill\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hill\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jonah_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Spanish singer and DJ", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Spanish singer and DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Spanish singer and DJ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oway\" title=\"<PERSON><PERSON> Genoway\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>owa<PERSON>\" title=\"<PERSON><PERSON> Genoway\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chay_Genoway"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, American singer and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(singer)"}]}, {"year": "1990", "text": "<PERSON>, Spanish basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marta_<PERSON>gay"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Scottish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_December_1991)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born December 1991)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_December_1991)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, born December 1991)\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, born December 1991)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_December_1991)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Swiss footballer ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>h%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fabian_Sch%C3%A4r"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Russian-American figure skater", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-American figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Italian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Cuban boxer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ram%C3%ADrez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ram%C3%ADrez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Robeisy_Ram%C3%ADrez"}]}, {"year": "1994", "text": "<PERSON>, American football player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON>, Latvian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/An%C5%BEejs_Pase%C4%8D%C5%86iks\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An%C5%BEejs_Pase%C4%8D%C5%86iks\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Latvian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An%C5%BEejs_Pase%C4%8D%C5%86iks"}]}, {"year": "1995", "text": "<PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, English football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/De%27<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De%27<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De%27<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Japanese singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nakamoto\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Nakamoto\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bapp%C3%A9"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Assyrian Swedish rapper and songwriter (d. 2024)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Gaboro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Assyrian Swedish rapper and songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaboro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Assyrian Swedish rapper and songwriter (d. 2024)", "links": [{"title": "Gaboro", "link": "https://wikipedia.org/wiki/Gaboro"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>ri\" title=\"Facundo Pellistri\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>\" title=\"Facundo Pellistri\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>acund<PERSON>", "link": "https://wikipedia.org/wiki/Facund<PERSON>_<PERSON>ri"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Dutch footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marcel<PERSON>_Pitaluga"}]}], "Deaths": [{"year": "69", "text": "<PERSON>, a Roman politician and soldier", "html": "69 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>ius_<PERSON>_(consul_AD_47)\" title=\"Titus <PERSON>lavius <PERSON>us (consul AD 47)\"><PERSON>lavius <PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Roman_people\" title=\"Roman people\">Roman</a> politician and soldier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>ius_<PERSON>_(consul_AD_47)\" title=\"Titus <PERSON>lavius <PERSON>us (consul AD 47)\"><PERSON>lavius <PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Roman_people\" title=\"Roman people\">Roman</a> politician and soldier", "links": [{"title": "<PERSON> (consul AD 47)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(consul_AD_47)"}, {"title": "Roman people", "link": "https://wikipedia.org/wiki/Roman_people"}]}, {"year": "217", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "217 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON> Z<PERSON>hy<PERSON>us\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON> Z<PERSON>hyrinus\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "910", "text": "<PERSON>, king of Asturias", "html": "910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Asturias\" title=\"<PERSON> of Asturias\"><PERSON> III</a>, king of Asturias", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Asturias\" title=\"<PERSON> III of Asturias\"><PERSON> III</a>, king of Asturias", "links": [{"title": "<PERSON> of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Asturias"}]}, {"year": "977", "text": "<PERSON><PERSON>, Japanese statesman (b. 925)", "html": "977 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kane<PERSON>hi\" title=\"Fujiwara no Kanemichi\"><PERSON><PERSON> <PERSON></a>, Japanese statesman (b. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_<PERSON>hi\" title=\"Fujiwara no Kanemichi\"><PERSON><PERSON> no <PERSON></a>, Japanese statesman (b. 925)", "links": [{"title": "<PERSON><PERSON> no <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fuji<PERSON>_<PERSON>_<PERSON>mic<PERSON>"}]}, {"year": "1295", "text": "<PERSON> of Provence, French queen (b. 1221)", "html": "1295 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a>, French queen (b. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Provence\" title=\"<PERSON> of Provence\"><PERSON> of Provence</a>, French queen (b. 1221)", "links": [{"title": "<PERSON> of Provence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Provence"}]}, {"year": "1326", "text": "<PERSON> of Moscow, Russian metropolitan bishop", "html": "1326 - <a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Moscow\" class=\"mw-redirect\" title=\"Saint <PERSON> of Moscow\"><PERSON> of Moscow</a>, Russian metropolitan bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_of_Moscow\" class=\"mw-redirect\" title=\"Saint <PERSON> of Moscow\"><PERSON> of Moscow</a>, Russian metropolitan bishop", "links": [{"title": "<PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Moscow"}]}, {"year": "1340", "text": "<PERSON>, duke of Bavaria (b. 1329)", "html": "1340 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (b. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON></a>, duke of Bavaria (b. 1329)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1355", "text": "<PERSON>, emperor of Serbia  (b. 1308)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a>, emperor of Serbia (b. 1308)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1an\" title=\"<PERSON>\"><PERSON></a>, emperor of Serbia (b. 1308)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Du%C5%A1an"}]}, {"year": "1539", "text": "<PERSON>, Flemish composer (b. 1506)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish composer (b. 1506)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON>, wife of <PERSON> (b. 1499)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON> <PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON></a>, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1499)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1590", "text": "<PERSON><PERSON><PERSON>, French physician and surgeon (b. 1510)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Ambroise_Par%C3%A9\" title=\"<PERSON>bro<PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and surgeon (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ambro<PERSON>_Par%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and surgeon (b. 1510)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ambroise_Par%C3%A9"}]}, {"year": "1658", "text": "<PERSON>, French designer and typefounder (b. 1580)", "html": "1658 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French designer and typefounder (b. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French designer and typefounder (b. 1580)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON><PERSON>, emperor of the Qing Dynasty (b. 1654)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/Kangxi_Emperor\" title=\"Kangxi Emperor\"><PERSON><PERSON></a>, emperor of the Qing Dynasty (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kangxi_Emperor\" title=\"Kangxi Emperor\"><PERSON><PERSON></a>, emperor of the Qing Dynasty (b. 1654)", "links": [{"title": "Kangxi Emperor", "link": "https://wikipedia.org/wiki/Kangxi_Emperor"}]}, {"year": "1723", "text": "<PERSON>, German physician and botanist (b. 1652)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Quirinus_Rivinus\" title=\"<PERSON> Quirinus Rivinus\"><PERSON></a>, German physician and botanist (b. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>uirinus_Rivinus\" title=\"<PERSON> Quirinus Rivinus\"><PERSON></a>, German physician and botanist (b. 1652)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, 2nd Viscount <PERSON>, English field marshal and politician, Governor of Portsmouth (b. 1675)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Portsmouth\" class=\"mw-redirect\" title=\"Governor of Portsmouth\">Governor of Portsmouth</a> (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Viscount_<PERSON>\" title=\"<PERSON>, 2nd Viscount <PERSON>\"><PERSON>, 2nd Viscount <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Portsmouth\" class=\"mw-redirect\" title=\"Governor of Portsmouth\">Governor of Portsmouth</a> (b. 1675)", "links": [{"title": "<PERSON>, 2nd Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Viscount_<PERSON>"}, {"title": "Governor of Portsmouth", "link": "https://wikipedia.org/wiki/Governor_of_Portsmouth"}]}, {"year": "1765", "text": "<PERSON>, dauphin of France (b. 1729)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(son_of_<PERSON>_<PERSON>)\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France (son of <PERSON>)\"><PERSON></a>, da<PERSON>hin of France (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(son_of_<PERSON>_<PERSON>)\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France (son of <PERSON>)\"><PERSON></a>, da<PERSON>hin of France (b. 1729)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON> of France (son of <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(son_of_<PERSON>_<PERSON>)"}]}, {"year": "1768", "text": "<PERSON>, Italian poet and academic (b. 1692)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and academic (b. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and academic (b. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1783", "text": "<PERSON>, Spanish priest and composer (b. 1729)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and composer (b. 1729)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish priest and composer (b. 1729)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American explorer (b. 1788)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Sacagawea\" title=\"Sacagawea\">Saca<PERSON><PERSON><PERSON></a>, American explorer (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sacagawea\" title=\"Sacagawea\">Saca<PERSON><PERSON><PERSON></a>, American explorer (b. 1788)", "links": [{"title": "Sacagawea", "link": "https://wikipedia.org/wiki/Sacagawea"}]}, {"year": "1820", "text": "<PERSON>, American farmer (b. 1750)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(farmer)\" title=\"<PERSON> (farmer)\"><PERSON></a>, American farmer (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(farmer)\" title=\"<PERSON> (farmer)\"><PERSON></a>, American farmer (b. 1750)", "links": [{"title": "<PERSON> (farmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(farmer)"}]}, {"year": "1856", "text": "<PERSON>, Italian activist (b. 1820)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist (b. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian activist (b. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>gna"}]}, {"year": "1862", "text": "<PERSON>, Scottish surgeon and zoologist (b. 1791)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Scottish surgeon and zoologist (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(surgeon)\" title=\"<PERSON> (surgeon)\"><PERSON></a>, Scottish surgeon and zoologist (b. 1791)", "links": [{"title": "<PERSON> (surgeon)", "link": "https://wikipedia.org/wiki/<PERSON>(surgeon)"}]}, {"year": "1880", "text": "<PERSON><PERSON>, Polish-American colonel and lawyer (b. 1797)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American colonel and lawyer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American colonel and lawyer (b. 1797)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American businessman (b. 1840)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian painter and composer (b. 1863)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Upendrakishore_Ray\" class=\"mw-redirect\" title=\"Upendrakishore Ray\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian painter and composer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Upendrakishore_Ray\" class=\"mw-redirect\" title=\"Upendrakishore Ray\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian painter and composer (b. 1863)", "links": [{"title": "<PERSON>endra<PERSON><PERSON> Ray", "link": "https://wikipedia.org/wiki/Upendrakishore_Ray"}]}, {"year": "1916", "text": "<PERSON>, Australian politician, 16th Premier of Queensland (b. 1856)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)\" class=\"mw-redirect\" title=\"<PERSON> (Queensland politician)\"><PERSON></a>, Australian politician, 16th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1856)", "links": [{"title": "<PERSON> (Queensland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Queensland_politician)"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1917", "text": "<PERSON>, French-Argentinian cyclist (b. 1882)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian cyclist (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Argentinian cyclist (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English-Australian politician, 12th Premier of Tasmania (b. 1835)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1920", "text": "<PERSON><PERSON>, English sailor and architect (b. 1863)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Linton_Hope\" title=\"Linton Hope\"><PERSON><PERSON></a>, English sailor and architect (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Linton_Hope\" title=\"Linton Hope\"><PERSON><PERSON></a>, English sailor and architect (b. 1863)", "links": [{"title": "Linton Hope", "link": "https://wikipedia.org/wiki/Linton_Hope"}]}, {"year": "1921", "text": "<PERSON>, German microbiologist (b. 1852)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German microbiologist (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, the founder of G<PERSON><PERSON><PERSON> da Nazaré, also known as <PERSON> (b. 1873).", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Ferreira_Sardo\" title=\"<PERSON>\"><PERSON></a>, the founder of <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> Nazaré\"><PERSON><PERSON><PERSON><PERSON>zar<PERSON></a>, also known as <PERSON> (b. 1873).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON>rre<PERSON>_Sardo\" title=\"<PERSON>\"><PERSON></a>, the founder of <a href=\"https://wikipedia.org/wiki/Gaf<PERSON><PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON> da Nazaré\"><PERSON><PERSON><PERSON><PERSON>zaré</a>, also known as <PERSON> (b. 1873).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_Sardo"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>%C3%A9"}]}, {"year": "1927", "text": "<PERSON>, American golfer and tennis player (b. 1872)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, French lawyer and politician, 8th President of France (b. 1838)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_France\" title=\"President of France\">President of France</a> (b. 1838)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Loubet"}, {"title": "President of France", "link": "https://wikipedia.org/wiki/President_of_France"}]}, {"year": "1935", "text": "<PERSON>, Irish-Australian sergeant, Victoria Cross recipient (b. 1882)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian sergeant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meara\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian sergeant, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Meara"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "1937", "text": "<PERSON>, German general (b. 1865)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American missionary (b. 1850)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American missionary (b. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American archer (b. 1859)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archer (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American archer (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German captain (b. 1894)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Russian-Estonian poet and author (b. 1887)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Estonian poet and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Estonian poet and author (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Maltese lawyer and politician, 6th Prime Minister of Malta (b. 1885)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malta\" title=\"Prime Minister of Malta\">Prime Minister of Malta</a> (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malta", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malta"}]}, {"year": "1954", "text": "<PERSON>, English-American author and screenwriter (b. 1900)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-American author and screenwriter (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, English-American author and screenwriter (b. 1900)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1956", "text": "<PERSON>, Argentinian neurologist and physician (b. 1906)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian neurologist and physician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian neurologist and physician (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Carrillo"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Estonian composer and conductor (b. 1885)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American director and playwright (b. 1904)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hart\" title=\"Moss Hart\"><PERSON></a>, American director and playwright (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moss_Hart\" title=\"Moss Hart\"><PERSON></a>, American director and playwright (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hart"}]}, {"year": "1961", "text": "<PERSON>, Australian soldier and politician, 11th Prime Minister of Australia (b. 1880)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1968", "text": "<PERSON>, American novelist and short story writer, Nobel Prize laureate (b. 1902)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1971", "text": "<PERSON>, American banker and businessman, co-founded The Walt Disney Company (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker and businessman, co-founded <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Italian businessman (b. 1888)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian businessman (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Spanish admiral and politician, 69th President of the Government of Spain (b. 1904; assassinated)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish admiral and politician, 69th <a href=\"https://wikipedia.org/wiki/President_of_the_Government_of_Spain\" class=\"mw-redirect\" title=\"President of the Government of Spain\">President of the Government of Spain</a> (b. 1904; assassinated)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish admiral and politician, 69th <a href=\"https://wikipedia.org/wiki/President_of_the_Government_of_Spain\" class=\"mw-redirect\" title=\"President of the Government of Spain\">President of the Government of Spain</a> (b. 1904; assassinated)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Government of Spain", "link": "https://wikipedia.org/wiki/President_of_the_Government_of_Spain"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and actor (b. 1936)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, English journalist and politician (b. 1896)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French composer and conductor (b. 1905)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Jolivet\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Jo<PERSON>vet\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Jolivet"}]}, {"year": "1976", "text": "<PERSON>, American lawyer and politician, 48th Mayor of Chicago (b. 1902)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Indonesian politician, 1st Governor of West Java (b. 1890)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/So<PERSON>rd<PERSON>_Ka<PERSON>ohadi<PERSON>umo\" title=\"So<PERSON>rd<PERSON> Kartohadi<PERSON>umo\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian politician, 1st Governor of West Java (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Ka<PERSON>oh<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>oh<PERSON>um<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indonesian politician, 1st Governor of West Java (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek actor and director (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor and director (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek actor and director (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Polish-American pianist and composer (b. 1887)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American pianist and composer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American psychologist and academic (b. 1933)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Minister of Defence of the Soviet Union (1976-84) (b. 1908)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Minister of Defence of the Soviet Union\">Minister of Defence of the Soviet Union (1976-84)</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Minister of Defence of the Soviet Union\">Minister of Defence of the Soviet Union (1976-84)</a> (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Defence of the Soviet Union", "link": "https://wikipedia.org/wiki/Minister_of_Defence_of_the_Soviet_Union"}]}, {"year": "1986", "text": "<PERSON>, American baseball player (b. 1959)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, French chef and author (b. 1904)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English wrestler, singer, and sculptor (b. 1903)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English wrestler, singer, and sculptor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English wrestler, singer, and sculptor (b. 1903)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1991", "text": "<PERSON>, Belgian cyclist (b. 1942)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American statistician, author, and academic (b. 1900)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American statistician, author, and academic (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American statistician, author, and academic (b. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Turkish composer and educator (b. 1921)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Nazife_G%C3%BCran\" title=\"Nazife Güran\"><PERSON><PERSON></a>, Turkish composer and educator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazife_G%C3%BCran\" title=\"Nazife Güran\"><PERSON><PERSON></a>, Turkish composer and educator (b. 1921)", "links": [{"title": "<PERSON>fe Güran", "link": "https://wikipedia.org/wiki/Nazife_G%C3%BCran"}]}, {"year": "1994", "text": "<PERSON>, American lawyer, and politician, 54th United States Secretary of State (b. 1909)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician, 54th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, and politician, 54th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Jamaican-American actress (b. 1938)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican-American actress (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American astronomer, astrophysicist, and cosmologist (b. 1934)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, astrophysicist, and cosmologist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer, astrophysicist, and cosmologist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English-American poet and translator (b. 1923)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and translator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American poet and translator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, English cricketer (b. 1919)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American film producer (b. 1946)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Dawn_Steel\" title=\"Dawn Steel\"><PERSON></a>, American film producer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dawn_Steel\" title=\"Dawn Steel\"><PERSON></a>, American film producer (b. 1946)", "links": [{"title": "Dawn Steel", "link": "https://wikipedia.org/wiki/Dawn_Steel"}]}, {"year": "1998", "text": "<PERSON>, English physiologist and biophysicist, Nobel Prize laureate (b. 1916)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physiologist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian-Italian director and screenwriter (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian-Italian director and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian-Italian director and screenwriter (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian-American singer-songwriter and guitarist (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter and guitarist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American singer-songwriter and guitarist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese poet and politician, 1st President of Senegal (b. 1906)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>or\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Senegalese poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Senegalese poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a> (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor"}, {"title": "President of Senegal", "link": "https://wikipedia.org/wiki/President_of_Senegal"}]}, {"year": "2005", "text": "<PERSON>, Hungarian-American mathematician and academic (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician and academic (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American dog breeder and trainer (b. 1929)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dog breeder and trainer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dog breeder and trainer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, English author, poet, and playwright (b. 1932)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American director and producer (b. 1925)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Russian aristocrat and racing driver (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian aristocrat and racing driver (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian aristocrat and racing driver (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actress and singer (b. 1977)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1918)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan academic and politician (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan academic and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. P. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan academic and politician (b. 1914)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Jamaican playwright and screenwriter (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican playwright and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican playwright and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English footballer and manager (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian painter and sculptor (b. 1929)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian pianist and educator (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pianist and educator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Russian runner (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian runner (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "Per<PERSON><PERSON><PERSON><PERSON>, Swedish surgeon and academic (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Per-Ingvar_Br%C3%A5nemark\" title=\"Per-Ingvar Brånemark\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swedish surgeon and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per-Ingvar_Br%C3%A5nemark\" title=\"Per-Ingvar Brånemark\"><PERSON>-<PERSON><PERSON><PERSON></a>, Swedish surgeon and academic (b. 1929)", "links": [{"title": "Per-Ingvar Brånemark", "link": "https://wikipedia.org/wiki/Per-Ingvar_Br%C3%A5nemark"}]}, {"year": "2014", "text": "<PERSON>, English lawyer, politician, and diplomat, British Ambassador to the United States (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_politician)\" title=\"<PERSON> (British politician)\"><PERSON></a>, English lawyer, politician, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to the United States\">British Ambassador to the United States</a> (b. 1915)", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}, {"title": "List of Ambassadors of the United Kingdom to the United States", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_the_United_States"}]}, {"year": "2020", "text": "<PERSON>, British pianist (b. 1920)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Waterman\"><PERSON></a>, British pianist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Waterman\"><PERSON></a>, British pianist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American sociologist (b. 1930)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American sociologist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American football player (b. 1950)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American singer (b. 1965)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Casey Chaos\"><PERSON></a>, American singer (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Chaos\" title=\"Casey Chaos\"><PERSON></a>, American singer (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Casey_Chaos"}]}, {"year": "2024", "text": "<PERSON>, English footballer (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American baseball player (b. 1958)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}