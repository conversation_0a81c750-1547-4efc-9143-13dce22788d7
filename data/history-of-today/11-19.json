{"date": "November 19", "url": "https://wikipedia.org/wiki/November_19", "data": {"Events": [{"year": "461", "text": "<PERSON><PERSON> is declared emperor of the Western Roman Empire. The real power is in the hands of the magister militum Ricimer.", "html": "461 - <a href=\"https://wikipedia.org/wiki/Libius_Severus\" title=\"Libius Severus\"><PERSON><PERSON> Severus</a> is declared emperor of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>. The real power is in the hands of the <i>magister militum</i> <a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\">Ricimer</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Libius_Severus\" title=\"Libius Severus\"><PERSON><PERSON> Severus</a> is declared emperor of the <a href=\"https://wikipedia.org/wiki/Western_Roman_Empire\" title=\"Western Roman Empire\">Western Roman Empire</a>. The real power is in the hands of the <i>magister militum</i> <a href=\"https://wikipedia.org/wiki/Ricimer\" title=\"Ricimer\">Ricimer</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Libius_Severus"}, {"title": "Western Roman Empire", "link": "https://wikipedia.org/wiki/Western_Roman_Empire"}, {"title": "Ricimer", "link": "https://wikipedia.org/wiki/Ricimer"}]}, {"year": "636", "text": "The Rashidun Caliphate defeats the Sasanian Empire at the Battle of al-Qādisiyyah in Iraq.", "html": "636 - The <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\"><PERSON><PERSON> Calip<PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_al-Q%C4%81disiyyah\" class=\"mw-redirect\" title=\"Battle of al-Qādisiyyah\">Battle of al-Qādisiyyah</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rashidun_Caliphate\" title=\"Rashidun Caliphate\"><PERSON><PERSON> Calip<PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Sasanian Empire</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_al-Q%C4%81disiyyah\" class=\"mw-redirect\" title=\"Battle of al-Qādisiyyah\">Battle of al-Qādisiyyah</a> in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rashidun_Caliphate"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "Battle of al-Qādisiyyah", "link": "https://wikipedia.org/wiki/Battle_of_al-Q%C4%81disiyyah"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "1493", "text": "<PERSON> goes ashore on an island called Borinquen he first saw the day before. He names it San Juan Bautista (later renamed again Puerto Rico).", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes ashore on an island called Borinquen he first saw the day before. He names it San Juan Bautista (later renamed again <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christopher_<PERSON>\" title=\"Christopher Columbus\"><PERSON></a> goes ashore on an island called Borinquen he first saw the day before. He names it San Juan Bautista (later renamed again <a href=\"https://wikipedia.org/wiki/Puerto_Rico\" title=\"Puerto Rico\">Puerto Rico</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Puerto Rico", "link": "https://wikipedia.org/wiki/Puerto_Rico"}]}, {"year": "1794", "text": "The United States and the Kingdom of Great Britain sign Jay's Treaty, which attempts to resolve some of the lingering problems left over from the American Revolutionary War.", "html": "1794 - The United States and the Kingdom of Great Britain sign <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay's Treaty</a>, which attempts to resolve some of the lingering problems left over from the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "no_year_html": "The United States and the Kingdom of Great Britain sign <a href=\"https://wikipedia.org/wiki/Jay_Treaty\" title=\"Jay Treaty\">Jay's Treaty</a>, which attempts to resolve some of the lingering problems left over from the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>.", "links": [{"title": "Jay <PERSON>", "link": "https://wikipedia.org/wiki/Jay_Treaty"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1802", "text": "The Garinagu arrive at British Honduras (present-day Belize).", "html": "1802 - The <a href=\"https://wikipedia.org/wiki/Garinagu\" class=\"mw-redirect\" title=\"Garinagu\">Garinagu</a> arrive at British Honduras (present-day <a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Garinagu\" class=\"mw-redirect\" title=\"Garinagu\">Garina<PERSON></a> arrive at British Honduras (present-day <a href=\"https://wikipedia.org/wiki/Belize\" title=\"Belize\">Belize</a>).", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garinagu"}, {"title": "Belize", "link": "https://wikipedia.org/wiki/Belize"}]}, {"year": "1808", "text": "Finnish War: The Convention of Olkijoki in Raahe ends hostilities in Finland.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: The Convention of Olkijoki in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> ends hostilities in <a href=\"https://wikipedia.org/wiki/Finland_under_Swedish_rule\" title=\"Finland under Swedish rule\">Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: The Convention of Olkijoki in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> ends hostilities in <a href=\"https://wikipedia.org/wiki/Finland_under_Swedish_rule\" title=\"Finland under Swedish rule\">Finland</a>.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}, {"title": "Finland under Swedish rule", "link": "https://wikipedia.org/wiki/Finland_under_Swedish_rule"}]}, {"year": "1816", "text": "Warsaw University is established.", "html": "1816 - <a href=\"https://wikipedia.org/wiki/Warsaw_University\" class=\"mw-redirect\" title=\"Warsaw University\">Warsaw University</a> is established.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warsaw_University\" class=\"mw-redirect\" title=\"Warsaw University\">Warsaw University</a> is established.", "links": [{"title": "Warsaw University", "link": "https://wikipedia.org/wiki/Warsaw_University"}]}, {"year": "1847", "text": "The second Canadian railway line, the Montreal and Lachine Railroad, is opened.", "html": "1847 - The second Canadian railway line, the <a href=\"https://wikipedia.org/wiki/Montreal_and_Lachine_Railroad\" title=\"Montreal and Lachine Railroad\">Montreal and Lachine Railroad</a>, is opened.", "no_year_html": "The second Canadian railway line, the <a href=\"https://wikipedia.org/wiki/Montreal_and_Lachine_Railroad\" title=\"Montreal and Lachine Railroad\">Montreal and Lachine Railroad</a>, is opened.", "links": [{"title": "Montreal and Lachine Railroad", "link": "https://wikipedia.org/wiki/Montreal_and_Lachine_Railroad"}]}, {"year": "1863", "text": "American Civil War: U.S. President <PERSON> delivers the Gettysburg Address at the dedication ceremony for the military cemetery at Gettysburg, Pennsylvania.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Gettysburg_Address\" title=\"Gettysburg Address\">Gettysburg Address</a> at the dedication ceremony for the <a href=\"https://wikipedia.org/wiki/Gettysburg_National_Cemetery\" title=\"Gettysburg National Cemetery\">military cemetery</a> at <a href=\"https://wikipedia.org/wiki/Gettysburg,_Pennsylvania\" title=\"Gettysburg, Pennsylvania\">Gettysburg, Pennsylvania</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Gettysburg_Address\" title=\"Gettysburg Address\">Gettysburg Address</a> at the dedication ceremony for the <a href=\"https://wikipedia.org/wiki/Gettysburg_National_Cemetery\" title=\"Gettysburg National Cemetery\">military cemetery</a> at <a href=\"https://wikipedia.org/wiki/Gettysburg,_Pennsylvania\" title=\"Gettysburg, Pennsylvania\">Gettysburg, Pennsylvania</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gettysburg Address", "link": "https://wikipedia.org/wiki/Gettysburg_Address"}, {"title": "Gettysburg National Cemetery", "link": "https://wikipedia.org/wiki/Gettysburg_National_Cemetery"}, {"title": "Gettysburg, Pennsylvania", "link": "https://wikipedia.org/wiki/Gettysburg,_Pennsylvania"}]}, {"year": "1881", "text": "A meteorite lands near the village of Grossliebenthal, southwest of Odesa, Ukraine.", "html": "1881 - A meteorite <a href=\"https://wikipedia.org/wiki/Meteorite_fall#Importance\" title=\"Meteorite fall\">lands</a> near the village of Grossliebenthal, southwest of <a href=\"https://wikipedia.org/wiki/Odesa\" title=\"Odesa\">Odesa</a>, Ukraine.", "no_year_html": "A meteorite <a href=\"https://wikipedia.org/wiki/Meteorite_fall#Importance\" title=\"Meteorite fall\">lands</a> near the village of Grossliebenthal, southwest of <a href=\"https://wikipedia.org/wiki/Odesa\" title=\"Odesa\">Odesa</a>, Ukraine.", "links": [{"title": "Meteorite fall", "link": "https://wikipedia.org/wiki/Meteorite_fall#Importance"}, {"title": "Odesa", "link": "https://wikipedia.org/wiki/Odesa"}]}, {"year": "1885", "text": "Serbo-Bulgarian War: Bulgarian victory in the Battle of Slivnitsa solidifies the unification between the Principality of Bulgaria and Eastern Rumelia.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Serbo-Bulgarian_War\" title=\"Serbo-Bulgarian War\">Serbo-Bulgarian War</a>: Bulgarian victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Slivnitsa\" title=\"Battle of Slivnitsa\">Battle of Slivnitsa</a> solidifies the unification between the <a href=\"https://wikipedia.org/wiki/Principality_of_Bulgaria\" title=\"Principality of Bulgaria\">Principality of Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Eastern_Rumelia\" title=\"Eastern Rumelia\">Eastern Rumelia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbo-Bulgarian_War\" title=\"Serbo-Bulgarian War\">Serbo-Bulgarian War</a>: Bulgarian victory in the <a href=\"https://wikipedia.org/wiki/Battle_of_Slivnitsa\" title=\"Battle of Slivnitsa\">Battle of Slivnitsa</a> solidifies the unification between the <a href=\"https://wikipedia.org/wiki/Principality_of_Bulgaria\" title=\"Principality of Bulgaria\">Principality of Bulgaria</a> and <a href=\"https://wikipedia.org/wiki/Eastern_Rumelia\" title=\"Eastern Rumelia\">Eastern Rumelia</a>.", "links": [{"title": "Serbo-Bulgarian War", "link": "https://wikipedia.org/wiki/Serbo-Bulgarian_War"}, {"title": "Battle of Slivnitsa", "link": "https://wikipedia.org/wiki/Battle_of_Slivnitsa"}, {"title": "Principality of Bulgaria", "link": "https://wikipedia.org/wiki/Principality_of_Bulgaria"}, {"title": "Eastern Rumelia", "link": "https://wikipedia.org/wiki/Eastern_Rumelia"}]}, {"year": "1911", "text": "The Doom Bar in Cornwall claims two ships, Island Maid and <PERSON><PERSON>, the latter killing the entire crew except the captain.", "html": "1911 - The <a href=\"https://wikipedia.org/wiki/Doom_Bar\" title=\"Doom Bar\">Doom Bar</a> in <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> claims two ships, <i>Island Maid</i> and <i><PERSON><PERSON></i>, the latter killing the entire crew except the captain.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Doom_Bar\" title=\"Doom Bar\">Doom Bar</a> in <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> claims two ships, <i>Island Maid</i> and <i><PERSON><PERSON></i>, the latter killing the entire crew except the captain.", "links": [{"title": "Doom Bar", "link": "https://wikipedia.org/wiki/Doom_Bar"}, {"title": "Cornwall", "link": "https://wikipedia.org/wiki/Cornwall"}]}, {"year": "1912", "text": "First Balkan War: The Serbian Army captures Bitola, ending the five-century-long Ottoman rule of Macedonia.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The Serbian Army <a href=\"https://wikipedia.org/wiki/Battle_of_Monastir\" title=\"Battle of Monastir\">captures</a> <a href=\"https://wikipedia.org/wiki/Bitola\" title=\"Bitola\">Bitola</a>, ending the five-century-long Ottoman rule of <a href=\"https://wikipedia.org/wiki/Macedonia_(region)\" title=\"Macedonia (region)\">Macedonia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: The Serbian Army <a href=\"https://wikipedia.org/wiki/Battle_of_Monastir\" title=\"Battle of Monastir\">captures</a> <a href=\"https://wikipedia.org/wiki/Bitola\" title=\"Bitola\">Bitola</a>, ending the five-century-long Ottoman rule of <a href=\"https://wikipedia.org/wiki/Macedonia_(region)\" title=\"Macedonia (region)\">Macedonia</a>.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Monastir", "link": "https://wikipedia.org/wiki/Battle_of_Monastir"}, {"title": "Bitola", "link": "https://wikipedia.org/wiki/Bitola"}, {"title": "Macedonia (region)", "link": "https://wikipedia.org/wiki/Macedonia_(region)"}]}, {"year": "1916", "text": "<PERSON> and <PERSON> establish Goldwyn Pictures.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establish <a href=\"https://wikipedia.org/wiki/Goldwyn_Pictures\" title=\"Goldwyn Pictures\">Goldwyn Pictures</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> establish <a href=\"https://wikipedia.org/wiki/Goldwyn_Pictures\" title=\"Goldwyn Pictures\">Goldwyn Pictures</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Goldwyn Pictures", "link": "https://wikipedia.org/wiki/Goldwyn_Pictures"}]}, {"year": "1941", "text": "World War II: Battle between HMAS Sydney and HSK Kormoran. The two ships sink each other off the coast of Western Australia, with the loss of 645 Australians and about 77 German seamen.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Sinking_of_HMAS_Sydney\" title=\"Sinking of HMAS Sydney\">Battle between HMAS <i>Sydney</i> and HSK <i>Kormoran</i></a>. The two ships sink each other off the coast of Western Australia, with the loss of 645 Australians and about 77 <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> seamen.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Sinking_of_HMAS_Sydney\" title=\"Sinking of HMAS Sydney\">Battle between HMAS <i>Sydney</i> and HSK <i>Kormoran</i></a>. The two ships sink each other off the coast of Western Australia, with the loss of 645 Australians and about 77 <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> seamen.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Sinking of HMAS Sydney", "link": "https://wikipedia.org/wiki/Sinking_of_HMAS_Sydney"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}]}, {"year": "1942", "text": "World War II: Battle of Stalingrad: Soviet Union forces under General <PERSON><PERSON> launch the Operation Uranus counterattacks at Stalingrad, turning the tide of the battle in the USSR's favor.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> launch the <a href=\"https://wikipedia.org/wiki/Operation_Uranus\" title=\"Operation Uranus\">Operation Uranus</a> <a href=\"https://wikipedia.org/wiki/Counterattack\" title=\"Counterattack\">counterattacks</a> at <a href=\"https://wikipedia.org/wiki/Stalingrad\" class=\"mw-redirect\" title=\"Stalingrad\">Stalingrad</a>, turning the tide of the battle in the USSR's favor.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Battle_of_Stalingrad\" title=\"Battle of Stalingrad\">Battle of Stalingrad</a>: <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> forces under General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> launch the <a href=\"https://wikipedia.org/wiki/Operation_Uranus\" title=\"Operation Uranus\">Operation Uranus</a> <a href=\"https://wikipedia.org/wiki/Counterattack\" title=\"Counterattack\">counterattacks</a> at <a href=\"https://wikipedia.org/wiki/Stalingrad\" class=\"mw-redirect\" title=\"Stalingrad\">Stalingrad</a>, turning the tide of the battle in the USSR's favor.", "links": [{"title": "Battle of Stalingrad", "link": "https://wikipedia.org/wiki/Battle_of_Stalingrad"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Operation Uranus", "link": "https://wikipedia.org/wiki/Operation_Uranus"}, {"title": "Counterattack", "link": "https://wikipedia.org/wiki/Counterattack"}, {"title": "Stalingrad", "link": "https://wikipedia.org/wiki/Stalingrad"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON> <PERSON> is crowned the 35th and last <PERSON><PERSON><PERSON> (king) of Buganda, prior to the restoration of the kingdom in 1993.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II_of_Buganda\" title=\"<PERSON><PERSON><PERSON> II of Buganda\"><PERSON><PERSON><PERSON> <PERSON></a> is crowned the 35th and last <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>_of_Buganda\" title=\"<PERSON><PERSON><PERSON> of Buganda\"><PERSON><PERSON><PERSON></a> (king) of <a href=\"https://wikipedia.org/wiki/Buganda\" title=\"Buganda\">Buganda</a>, prior to the restoration of the kingdom in 1993.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Buganda\" title=\"<PERSON><PERSON><PERSON> II of Buganda\"><PERSON><PERSON><PERSON> <PERSON></a> is crowned the 35th and last <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON>_of_Buganda\" title=\"<PERSON><PERSON><PERSON> of Buganda\"><PERSON><PERSON><PERSON></a> (king) of <a href=\"https://wikipedia.org/wiki/Buganda\" title=\"Buganda\">Buganda</a>, prior to the restoration of the kingdom in 1993.", "links": [{"title": "Mutesa II of Buganda", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Buganda"}, {"title": "Kabaka of Buganda", "link": "https://wikipedia.org/wiki/Kabaka_of_Buganda"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uganda"}]}, {"year": "1943", "text": "Holocaust: Nazis liquidate Janowska concentration camp in Lemberg (Lviv), western Ukraine, murdering at least 6,000 Jews after a failed uprising and mass escape attempt.", "html": "1943 - Holocaust: <a href=\"https://wikipedia.org/wiki/Nazis\" class=\"mw-redirect\" title=\"Nazis\">Nazis</a> liquidate <a href=\"https://wikipedia.org/wiki/Janowska_concentration_camp\" title=\"Janowska concentration camp\">Janowska concentration camp</a> in Lemberg (<a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>), western Ukraine, murdering at least 6,000 Jews after a failed uprising and mass escape attempt.", "no_year_html": "Holocaust: <a href=\"https://wikipedia.org/wiki/Nazis\" class=\"mw-redirect\" title=\"Nazis\">Nazis</a> liquidate <a href=\"https://wikipedia.org/wiki/Janowska_concentration_camp\" title=\"Janowska concentration camp\">Janowska concentration camp</a> in Lemberg (<a href=\"https://wikipedia.org/wiki/Lviv\" title=\"Lviv\">Lviv</a>), western Ukraine, murdering at least 6,000 Jews after a failed uprising and mass escape attempt.", "links": [{"title": "Nazis", "link": "https://wikipedia.org/wiki/Nazis"}, {"title": "Janowska concentration camp", "link": "https://wikipedia.org/wiki/Janowska_concentration_camp"}, {"title": "Lviv", "link": "https://wikipedia.org/wiki/Lviv"}]}, {"year": "1944", "text": "World War II: U.S. President <PERSON> announces the sixth War Loan Drive, aimed at selling US$14 billion in war bonds to help pay for the war effort.", "html": "1944 - World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the sixth War Loan Drive, aimed at selling US$14 billion in <a href=\"https://wikipedia.org/wiki/War_bond\" title=\"War bond\">war bonds</a> to help pay for the war effort.", "no_year_html": "World War II: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the sixth War Loan Drive, aimed at selling US$14 billion in <a href=\"https://wikipedia.org/wiki/War_bond\" title=\"War bond\">war bonds</a> to help pay for the war effort.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "War bond", "link": "https://wikipedia.org/wiki/War_bond"}]}, {"year": "1944", "text": "World War II: Thirty members of the Luxembourgish resistance defend the town of Vianden against a larger Waffen-SS attack in the Battle of Vianden.", "html": "1944 - World War II: Thirty members of the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Luxembourg_during_World_War_II#Resistance\" title=\"German occupation of Luxembourg during World War II\">Luxembourgish resistance</a> defend the town of <a href=\"https://wikipedia.org/wiki/Vianden\" title=\"Vianden\">Vianden</a> against a larger <a href=\"https://wikipedia.org/wiki/Waffen-SS\" title=\"Waffen-SS\">Waffen-SS</a> attack in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vianden\" title=\"Battle of Vianden\">Battle of Vianden</a>.", "no_year_html": "World War II: Thirty members of the <a href=\"https://wikipedia.org/wiki/German_occupation_of_Luxembourg_during_World_War_II#Resistance\" title=\"German occupation of Luxembourg during World War II\">Luxembourgish resistance</a> defend the town of <a href=\"https://wikipedia.org/wiki/Vianden\" title=\"Vianden\">Vianden</a> against a larger <a href=\"https://wikipedia.org/wiki/Waffen-SS\" title=\"Waffen-SS\">Waffen-SS</a> attack in the <a href=\"https://wikipedia.org/wiki/Battle_of_Vianden\" title=\"Battle of Vianden\">Battle of Vianden</a>.", "links": [{"title": "German occupation of Luxembourg during World War II", "link": "https://wikipedia.org/wiki/German_occupation_of_Luxembourg_during_World_War_II#Resistance"}, {"title": "Vianden", "link": "https://wikipedia.org/wiki/Vianden"}, {"title": "Waffen-SS", "link": "https://wikipedia.org/wiki/Waffen-SS"}, {"title": "Battle of Vianden", "link": "https://wikipedia.org/wiki/Battle_of_Vianden"}]}, {"year": "1946", "text": "Afghanistan, Iceland and Sweden join the United Nations.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> and Sweden join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afghanistan\" title=\"Afghanistan\">Afghanistan</a>, <a href=\"https://wikipedia.org/wiki/Iceland\" title=\"Iceland\">Iceland</a> and Sweden join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Afghanistan", "link": "https://wikipedia.org/wiki/Afghanistan"}, {"title": "Iceland", "link": "https://wikipedia.org/wiki/Iceland"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1950", "text": "US General <PERSON> becomes Supreme Commander of NATO-Europe.", "html": "1950 - US General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Supreme Commander of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO-Europe</a>.", "no_year_html": "US General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes Supreme Commander of <a href=\"https://wikipedia.org/wiki/NATO\" title=\"NATO\">NATO-Europe</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "NATO", "link": "https://wikipedia.org/wiki/NATO"}]}, {"year": "1952", "text": "Greek Field Marshal <PERSON> becomes the 152nd Prime Minister of Greece.", "html": "1952 - Greek Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the 152nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a>.", "no_year_html": "Greek Field Marshal <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> becomes the 152nd <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece\" class=\"mw-redirect\" title=\"List of Prime Ministers of Greece\">Prime Minister of Greece</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Greece"}]}, {"year": "1954", "text": "Télé Monte Carlo, Europe's oldest private television channel, is launched by <PERSON>.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/T%C3%A9l%C3%A9_Monte_Carlo\" class=\"mw-redirect\" title=\"Télé Monte Carlo\">Télé Monte Carlo</a>, Europe's oldest private television channel, is launched by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\">Rainier III</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%A9l%C3%A9_Monte_Carlo\" class=\"mw-redirect\" title=\"Télé Monte Carlo\">Télé <PERSON> Carlo</a>, Europe's oldest private television channel, is launched by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON><PERSON>, Prince of Monaco\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%A9l%C3%A9_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1955", "text": "National Review publishes its first issue.", "html": "1955 - <i><a href=\"https://wikipedia.org/wiki/National_Review\" title=\"National Review\">National Review</a></i> publishes its first issue.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/National_Review\" title=\"National Review\">National Review</a></i> publishes its first issue.", "links": [{"title": "National Review", "link": "https://wikipedia.org/wiki/National_Review"}]}, {"year": "1967", "text": "The establishment of TVB, the first wireless commercial television station in Hong Kong.", "html": "1967 - The establishment of <a href=\"https://wikipedia.org/wiki/TVB\" title=\"TVB\">TVB</a>, the first wireless commercial <a href=\"https://wikipedia.org/wiki/Television_station\" title=\"Television station\">television station</a> in Hong Kong.", "no_year_html": "The establishment of <a href=\"https://wikipedia.org/wiki/TVB\" title=\"TVB\">TVB</a>, the first wireless commercial <a href=\"https://wikipedia.org/wiki/Television_station\" title=\"Television station\">television station</a> in Hong Kong.", "links": [{"title": "TVB", "link": "https://wikipedia.org/wiki/TVB"}, {"title": "Television station", "link": "https://wikipedia.org/wiki/Television_station"}]}, {"year": "1969", "text": "Apollo program: Apollo 12 astronauts <PERSON> and <PERSON> land at Oceanus Procellarum (the \"Ocean of Storms\") and become the third and fourth humans to walk on the Moon.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_12\" title=\"Apollo 12\">Apollo 12</a> astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bean\"><PERSON></a> land at <i><a href=\"https://wikipedia.org/wiki/Oceanus_Procellarum\" title=\"Oceanus Procellarum\">Oceanus Procellarum</a></i> (the \"Ocean of Storms\") and become the third and fourth <a href=\"https://wikipedia.org/wiki/Human\" title=\"Human\">humans</a> to walk on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <a href=\"https://wikipedia.org/wiki/Apollo_12\" title=\"Apollo 12\">Apollo 12</a> astronauts <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> land at <i><a href=\"https://wikipedia.org/wiki/Oceanus_Procellarum\" title=\"Oceanus Procellarum\">Oceanus Procellarum</a></i> (the \"Ocean of Storms\") and become the third and fourth <a href=\"https://wikipedia.org/wiki/Human\" title=\"Human\">humans</a> to walk on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 12", "link": "https://wikipedia.org/wiki/Apollo_12"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Oceanus Procellarum", "link": "https://wikipedia.org/wiki/Oceanus_Procellarum"}, {"title": "Human", "link": "https://wikipedia.org/wiki/Human"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1969", "text": "Association football player <PERSON><PERSON><PERSON> scores his 1,000th goal.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">Association football</a> player <a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"Pelé\"><PERSON><PERSON><PERSON></a> scores his 1,000th goal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">Association football</a> player <a href=\"https://wikipedia.org/wiki/Pel%C3%A9\" title=\"Pelé\"><PERSON><PERSON><PERSON></a> scores his 1,000th goal.", "links": [{"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pel%C3%A9"}]}, {"year": "1977", "text": "TAP Air Portugal Flight 425 crashes in the Madeira Islands, killing 131.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/TAP_Flight_425\" title=\"TAP Flight 425\">TAP Air Portugal Flight 425</a> crashes in the <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira Islands</a>, killing 131.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TAP_Flight_425\" title=\"TAP Flight 425\">TAP Air Portugal Flight 425</a> crashes in the <a href=\"https://wikipedia.org/wiki/Madeira\" title=\"Madeira\">Madeira Islands</a>, killing 131.", "links": [{"title": "TAP Flight 425", "link": "https://wikipedia.org/wiki/TAP_Flight_425"}, {"title": "Madeira", "link": "https://wikipedia.org/wiki/Madeira"}]}, {"year": "1979", "text": "Iran hostage crisis: Iranian leader <PERSON><PERSON><PERSON><PERSON> orders the release of 13 female and black American hostages being held at the US Embassy in Tehran.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>ya<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>homeini\" title=\"<PERSON><PERSON><PERSON><PERSON>hom<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> orders the release of 13 female and black American hostages being held at the <a href=\"https://wikipedia.org/wiki/US_Embassy_in_Tehran\" class=\"mw-redirect\" title=\"US Embassy in Tehran\">US Embassy in Tehran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran_hostage_crisis\" title=\"Iran hostage crisis\">Iran hostage crisis</a>: <a href=\"https://wikipedia.org/wiki/Iran\" title=\"Iran\">Iranian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_Khomeini\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> orders the release of 13 female and black American hostages being held at the <a href=\"https://wikipedia.org/wiki/US_Embassy_in_Tehran\" class=\"mw-redirect\" title=\"US Embassy in Tehran\">US Embassy in Tehran</a>.", "links": [{"title": "Iran hostage crisis", "link": "https://wikipedia.org/wiki/Iran_hostage_crisis"}, {"title": "Iran", "link": "https://wikipedia.org/wiki/Iran"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ollah_Khomeini"}, {"title": "US Embassy in Tehran", "link": "https://wikipedia.org/wiki/US_Embassy_in_Tehran"}]}, {"year": "1984", "text": "San Juanico disaster: A series of explosions at the Pemex petroleum storage facility at San Juan Ixhuatepec in Mexico City starts a major fire and kills about 500 people.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/San_Juanico_disaster\" title=\"San Juanico disaster\">San Juanico disaster</a>: A series of explosions at the <a href=\"https://wikipedia.org/wiki/Pemex\" title=\"Pemex\">Pemex</a> <a href=\"https://wikipedia.org/wiki/Petroleum\" title=\"Petroleum\">petroleum</a> storage facility at San Juan Ixhuatepec in Mexico City starts a major fire and kills about 500 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San_Juanico_disaster\" title=\"San Juanico disaster\">San Juanico disaster</a>: A series of explosions at the <a href=\"https://wikipedia.org/wiki/Pemex\" title=\"Pemex\">Pemex</a> <a href=\"https://wikipedia.org/wiki/Petroleum\" title=\"Petroleum\">petroleum</a> storage facility at San Juan Ixhuatepec in Mexico City starts a major fire and kills about 500 people.", "links": [{"title": "San Juanico disaster", "link": "https://wikipedia.org/wiki/San_Juanico_disaster"}, {"title": "Pemex", "link": "https://wikipedia.org/wiki/Pemex"}, {"title": "Petroleum", "link": "https://wikipedia.org/wiki/Petroleum"}]}, {"year": "1985", "text": "Cold War: In Geneva, U.S. President <PERSON> and Soviet Union General Secretary <PERSON> meet for the first time.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> General Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Geneva_Summit_(1985)\" title=\"Geneva Summit (1985)\">meet</a> for the first time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: In <a href=\"https://wikipedia.org/wiki/Geneva\" title=\"Geneva\">Geneva</a>, U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> General Secretary <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Geneva_Summit_(1985)\" title=\"Geneva Summit (1985)\">meet</a> for the first time.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Geneva", "link": "https://wikipedia.org/wiki/Geneva"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Geneva Summit (1985)", "link": "https://wikipedia.org/wiki/Geneva_Summit_(1985)"}]}, {"year": "1985", "text": "Pennzoil wins a US$10.53 billion judgment against Texaco, in the largest civil verdict in the history of the United States, stemming from Texaco executing a contract to buy Getty Oil after Pennzoil had entered into an unsigned, yet still binding, buyout contract with Getty.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Pennzoil\" title=\"Pennzoil\">Pennzoil</a> wins a US$10.53 billion judgment against <a href=\"https://wikipedia.org/wiki/Texaco\" title=\"Texaco\">Texaco</a>, in the <a href=\"https://wikipedia.org/wiki/Pennzoil#Texaco,_Inc._v._Pennzoil,_Co.\" title=\"Pennzoil\">largest civil verdict in the history of the United States</a>, stemming from Texaco executing a contract to buy <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil</a> after Pennzoil had entered into an unsigned, yet still binding, buyout contract with Getty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pennzoil\" title=\"Pennzoil\">Pennzoil</a> wins a US$10.53 billion judgment against <a href=\"https://wikipedia.org/wiki/Texaco\" title=\"Texaco\">Texaco</a>, in the <a href=\"https://wikipedia.org/wiki/Pennzoil#Texaco,_Inc._v._Pennzoil,_Co.\" title=\"Pennzoil\">largest civil verdict in the history of the United States</a>, stemming from Texaco executing a contract to buy <a href=\"https://wikipedia.org/wiki/Getty_Oil\" title=\"Getty Oil\">Getty Oil</a> after Pennzoil had entered into an unsigned, yet still binding, buyout contract with Getty.", "links": [{"title": "Pennzoil", "link": "https://wikipedia.org/wiki/Pennzoil"}, {"title": "Texaco", "link": "https://wikipedia.org/wiki/Texaco"}, {"title": "Pennzoil", "link": "https://wikipedia.org/wiki/Pennzoil#Texaco,_Inc._v._Pennzoil,_Co."}, {"title": "Getty Oil", "link": "https://wikipedia.org/wiki/Getty_Oil"}]}, {"year": "1985", "text": "Police in Baling, Malaysia, lay siege to houses occupied by an Islamic sect of about 400 people led by <PERSON>.", "html": "1985 - Police in <a href=\"https://wikipedia.org/wiki/Baling_District\" title=\"Baling District\">Baling</a>, Malaysia, <a href=\"https://wikipedia.org/wiki/Memali_Incident\" title=\"Memali Incident\">lay siege</a> to houses occupied by an Islamic sect of about 400 people led by <PERSON>.", "no_year_html": "Police in <a href=\"https://wikipedia.org/wiki/Baling_District\" title=\"Baling District\">Baling</a>, Malaysia, <a href=\"https://wikipedia.org/wiki/Memali_Incident\" title=\"Memali Incident\">lay siege</a> to houses occupied by an Islamic sect of about 400 people led by <PERSON>.", "links": [{"title": "Baling District", "link": "https://wikipedia.org/wiki/Baling_District"}, {"title": "Memali Incident", "link": "https://wikipedia.org/wiki/Memali_Incident"}]}, {"year": "1988", "text": "Serbian communist representative and future Serbian and Yugoslav president <PERSON><PERSON><PERSON><PERSON> publicly declares that Serbia is under attack from Albanian separatists in Kosovo as well as internal treachery within Yugoslavia and a foreign conspiracy to destroy Serbia and Yugoslavia.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbian</a> communist representative and future Serbian and <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Yugoslav</a> president <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> publicly declares that <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> is under attack from <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> separatists in <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> as well as internal treachery within <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> and a foreign conspiracy to destroy Serbia and Yugoslavia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbian</a> communist representative and future Serbian and <a href=\"https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia\" class=\"mw-redirect\" title=\"Federal Republic of Yugoslavia\">Yugoslav</a> president <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> publicly declares that <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a> is under attack from <a href=\"https://wikipedia.org/wiki/Albanians\" title=\"Albanians\">Albanian</a> separatists in <a href=\"https://wikipedia.org/wiki/Kosovo\" title=\"Kosovo\">Kosovo</a> as well as internal treachery within <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> and a foreign conspiracy to destroy Serbia and Yugoslavia.", "links": [{"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Federal_Republic_of_Yugoslavia"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Albanians", "link": "https://wikipedia.org/wiki/Albanians"}, {"title": "Kosovo", "link": "https://wikipedia.org/wiki/Kosovo"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}]}, {"year": "1994", "text": "In the United Kingdom, the first National Lottery draw is held. A £1 ticket gave a one-in-14-million chance of correctly guessing the winning six out of 49 numbers.", "html": "1994 - In the United Kingdom, the first <a href=\"https://wikipedia.org/wiki/National_Lottery_(United_Kingdom)\" title=\"National Lottery (United Kingdom)\">National Lottery</a> draw is held. A £1 ticket gave a one-in-14-million chance of correctly guessing the winning six out of 49 numbers.", "no_year_html": "In the United Kingdom, the first <a href=\"https://wikipedia.org/wiki/National_Lottery_(United_Kingdom)\" title=\"National Lottery (United Kingdom)\">National Lottery</a> draw is held. A £1 ticket gave a one-in-14-million chance of correctly guessing the winning six out of 49 numbers.", "links": [{"title": "National Lottery (United Kingdom)", "link": "https://wikipedia.org/wiki/National_Lottery_(United_Kingdom)"}]}, {"year": "1996", "text": "Space Shuttle program: Columbia is launched on STS-80, which would become the longest mission in the program at 17 days. On this mission, astronaut <PERSON> becomes the only astronaut to fly on all five space shuttles.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\"><i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-80\" title=\"STS-80\">STS-80</a>, which would become the longest mission in the program at 17 days. On this mission, astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>_Mu<PERSON><PERSON>\" title=\"<PERSON> Mu<PERSON>grave\"><PERSON></a> becomes the only astronaut to fly on all five space shuttles.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\"><i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-80\" title=\"STS-80\">STS-80</a>, which would become the longest mission in the program at 17 days. On this mission, astronaut <a href=\"https://wikipedia.org/wiki/Story_Mu<PERSON>grave\" title=\"<PERSON> Mu<PERSON>grave\"><PERSON></a> becomes the only astronaut to fly on all five space shuttles.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-80", "link": "https://wikipedia.org/wiki/STS-80"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>grave"}]}, {"year": "1996", "text": "A Beechcraft 1900 and a Beechcraft King Air collide at Quincy Regional Airport in Quincy, Illinois, killing 14.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> and a <a href=\"https://wikipedia.org/wiki/Beechcraft_King_Air\" title=\"Beechcraft King Air\">Beechcraft King Air</a> <a href=\"https://wikipedia.org/wiki/1996_Quincy_Airport_disaster\" class=\"mw-redirect\" title=\"1996 Quincy Airport disaster\">collide</a> at <a href=\"https://wikipedia.org/wiki/Quincy_Regional_Airport\" title=\"Quincy Regional Airport\">Quincy Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Quincy,_Illinois\" title=\"Quincy, Illinois\">Quincy, Illinois</a>, killing 14.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Beechcraft_1900\" title=\"Beechcraft 1900\">Beechcraft 1900</a> and a <a href=\"https://wikipedia.org/wiki/Beechcraft_King_Air\" title=\"Beechcraft King Air\">Beechcraft King Air</a> <a href=\"https://wikipedia.org/wiki/1996_Quincy_Airport_disaster\" class=\"mw-redirect\" title=\"1996 Quincy Airport disaster\">collide</a> at <a href=\"https://wikipedia.org/wiki/Quincy_Regional_Airport\" title=\"Quincy Regional Airport\">Quincy Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Quincy,_Illinois\" title=\"Quincy, Illinois\">Quincy, Illinois</a>, killing 14.", "links": [{"title": "Beechcraft 1900", "link": "https://wikipedia.org/wiki/Beechcraft_1900"}, {"title": "Beechcraft King Air", "link": "https://wikipedia.org/wiki/Beechcraft_King_Air"}, {"title": "1996 Quincy Airport disaster", "link": "https://wikipedia.org/wiki/1996_Quincy_Airport_disaster"}, {"title": "Quincy Regional Airport", "link": "https://wikipedia.org/wiki/Quincy_Regional_Airport"}, {"title": "Quincy, Illinois", "link": "https://wikipedia.org/wiki/Quincy,_Illinois"}]}, {"year": "1997", "text": "Space Shuttle Columbia is launched on STS-87.", "html": "1997 - Space Shuttle <i>Columbia</i> is launched on <a href=\"https://wikipedia.org/wiki/STS-87\" title=\"STS-87\">STS-87</a>.", "no_year_html": "Space Shuttle <i>Columbia</i> is launched on <a href=\"https://wikipedia.org/wiki/STS-87\" title=\"STS-87\">STS-87</a>.", "links": [{"title": "STS-87", "link": "https://wikipedia.org/wiki/STS-87"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> scandal: The United States House of Representatives Judiciary Committee begins impeachment hearings against U.S. President <PERSON>.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>%E2%80%93L<PERSON><PERSON><PERSON>_scandal\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON><PERSON><PERSON><PERSON> scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">United States House of Representatives Judiciary Committee</a> begins <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> hearings against U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%E2%80%93L<PERSON><PERSON><PERSON>_scandal\" title=\"<PERSON><PERSON><PERSON> scandal\"><PERSON>-<PERSON><PERSON> scandal</a>: The <a href=\"https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary\" title=\"United States House Committee on the Judiciary\">United States House of Representatives Judiciary Committee</a> begins <a href=\"https://wikipedia.org/wiki/Impeachment\" title=\"Impeachment\">impeachment</a> hearings against U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> scandal", "link": "https://wikipedia.org/wiki/Clinton%E2%80%93L<PERSON><PERSON><PERSON>_scandal"}, {"title": "United States House Committee on the Judiciary", "link": "https://wikipedia.org/wiki/United_States_House_Committee_on_the_Judiciary"}, {"title": "Impeachment", "link": "https://wikipedia.org/wiki/Impeachment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "Shenzhou 1: The People's Republic of China launches its first Shenzhou spacecraft.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Shenzhou_1\" title=\"Shenzhou 1\">Shenzhou 1</a>: The People's Republic of China launches its first <a href=\"https://wikipedia.org/wiki/Shenzhou_spacecraft\" class=\"mw-redirect\" title=\"Shenzhou spacecraft\">Shenzhou spacecraft</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shenzhou_1\" title=\"Shenzhou 1\">Shenzhou 1</a>: The People's Republic of China launches its first <a href=\"https://wikipedia.org/wiki/Shenzhou_spacecraft\" class=\"mw-redirect\" title=\"Shenzhou spacecraft\">Shenzhou spacecraft</a>.", "links": [{"title": "Shenzhou 1", "link": "https://wikipedia.org/wiki/Shenzhou_1"}, {"title": "Shenzhou spacecraft", "link": "https://wikipedia.org/wiki/Shenzhou_spacecraft"}]}, {"year": "1999", "text": "<PERSON> becomes the first person to win the top prize in the TV game show Who Wants to Be a Millionaire?", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_show_contestant)\" title=\"<PERSON> (game show contestant)\"><PERSON></a> becomes the first person to win the top prize in the TV game show <i><a href=\"https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F\" title=\"Who Wants to Be a Millionaire?\">Who Wants to Be a Millionaire?</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(game_show_contestant)\" title=\"<PERSON> (game show contestant)\"><PERSON></a> becomes the first person to win the top prize in the TV game show <i><a href=\"https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F\" title=\"Who Wants to Be a Millionaire?\">Who Wants to Be a Millionaire?</a></i>", "links": [{"title": "<PERSON> (game show contestant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_show_contestant)"}, {"title": "Who Wants to Be a Millionaire?", "link": "https://wikipedia.org/wiki/Who_Wants_to_Be_a_Millionaire%3F"}]}, {"year": "2001", "text": "The Aviation and Transportation Security Act is enacted by the 107th United States Congress in the immediate aftermath of the September 11, 2001 attacks. The Act created the Transportation Security Administration (TSA).", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/Aviation_and_Transportation_Security_Act\" title=\"Aviation and Transportation Security Act\">Aviation and Transportation Security Act</a> is enacted by the <a href=\"https://wikipedia.org/wiki/107th_United_States_Congress\" title=\"107th United States Congress\">107th United States Congress</a> in the immediate aftermath of the <a href=\"https://wikipedia.org/wiki/September_11,_2001_attacks\" class=\"mw-redirect\" title=\"September 11, 2001 attacks\">September 11, 2001 attacks</a>. The Act created the <a href=\"https://wikipedia.org/wiki/Transportation_Security_Administration\" title=\"Transportation Security Administration\">Transportation Security Administration</a> (TSA).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Aviation_and_Transportation_Security_Act\" title=\"Aviation and Transportation Security Act\">Aviation and Transportation Security Act</a> is enacted by the <a href=\"https://wikipedia.org/wiki/107th_United_States_Congress\" title=\"107th United States Congress\">107th United States Congress</a> in the immediate aftermath of the <a href=\"https://wikipedia.org/wiki/September_11,_2001_attacks\" class=\"mw-redirect\" title=\"September 11, 2001 attacks\">September 11, 2001 attacks</a>. The Act created the <a href=\"https://wikipedia.org/wiki/Transportation_Security_Administration\" title=\"Transportation Security Administration\">Transportation Security Administration</a> (TSA).", "links": [{"title": "Aviation and Transportation Security Act", "link": "https://wikipedia.org/wiki/Aviation_and_Transportation_Security_Act"}, {"title": "107th United States Congress", "link": "https://wikipedia.org/wiki/107th_United_States_Congress"}, {"title": "September 11, 2001 attacks", "link": "https://wikipedia.org/wiki/September_11,_2001_attacks"}, {"title": "Transportation Security Administration", "link": "https://wikipedia.org/wiki/Transportation_Security_Administration"}]}, {"year": "2002", "text": "The Greek oil tanker Prestige splits in half and sinks off the coast of Galicia, releasing over 76,000 m3 (20 million US gal) of oil in the largest environmental disaster in Spanish and Portuguese history.", "html": "2002 - The Greek <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <i><a href=\"https://wikipedia.org/wiki/Prestige_(oil_tanker)\" class=\"mw-redirect\" title=\"Prestige (oil tanker)\">Prestige</a></i> splits in half and sinks off the coast of <a href=\"https://wikipedia.org/wiki/Galicia_(Spain)\" title=\"Galicia (Spain)\">Galicia</a>, releasing over 76,000 m (20 million US gal) of oil in the <a href=\"https://wikipedia.org/wiki/Prestige_oil_spill\" title=\"Prestige oil spill\">largest environmental disaster</a> in Spanish and Portuguese history.", "no_year_html": "The Greek <a href=\"https://wikipedia.org/wiki/Oil_tanker\" title=\"Oil tanker\">oil tanker</a> <i><a href=\"https://wikipedia.org/wiki/Prestige_(oil_tanker)\" class=\"mw-redirect\" title=\"Prestige (oil tanker)\">Prestige</a></i> splits in half and sinks off the coast of <a href=\"https://wikipedia.org/wiki/Galicia_(Spain)\" title=\"Galicia (Spain)\">Galicia</a>, releasing over 76,000 m (20 million US gal) of oil in the <a href=\"https://wikipedia.org/wiki/Prestige_oil_spill\" title=\"Prestige oil spill\">largest environmental disaster</a> in Spanish and Portuguese history.", "links": [{"title": "Oil tanker", "link": "https://wikipedia.org/wiki/Oil_tanker"}, {"title": "Prestige (oil tanker)", "link": "https://wikipedia.org/wiki/Prestige_(oil_tanker)"}, {"title": "Galicia (Spain)", "link": "https://wikipedia.org/wiki/Galicia_(Spain)"}, {"title": "Prestige oil spill", "link": "https://wikipedia.org/wiki/Prestige_oil_spill"}]}, {"year": "2004", "text": "The worst brawl in NBA history results in several players being suspended. Several players and fans are charged with assault and battery.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Pacers%E2%80%93Pistons_brawl\" class=\"mw-redirect\" title=\"Pacers-Pistons brawl\">worst brawl</a> in <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">NBA</a> history results in several players being suspended. Several players and fans are charged with <a href=\"https://wikipedia.org/wiki/Assault\" title=\"Assault\">assault</a> and <a href=\"https://wikipedia.org/wiki/Battery_(crime)\" title=\"Battery (crime)\">battery</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pacers%E2%80%93Pistons_brawl\" class=\"mw-redirect\" title=\"Pacers-Pistons brawl\">worst brawl</a> in <a href=\"https://wikipedia.org/wiki/National_Basketball_Association\" title=\"National Basketball Association\">NBA</a> history results in several players being suspended. Several players and fans are charged with <a href=\"https://wikipedia.org/wiki/Assault\" title=\"Assault\">assault</a> and <a href=\"https://wikipedia.org/wiki/Battery_(crime)\" title=\"Battery (crime)\">battery</a>.", "links": [{"title": "Pacers-<PERSON><PERSON><PERSON> brawl", "link": "https://wikipedia.org/wiki/Pacers%E2%80%93Pistons_brawl"}, {"title": "National Basketball Association", "link": "https://wikipedia.org/wiki/National_Basketball_Association"}, {"title": "Assault", "link": "https://wikipedia.org/wiki/Assault"}, {"title": "Battery (crime)", "link": "https://wikipedia.org/wiki/Battery_(crime)"}]}, {"year": "2010", "text": "The first of four explosions takes place at the Pike River Mine in New Zealand. Twenty-nine people are killed in the nation's worst mining disaster since 1914.", "html": "2010 - The first of <a href=\"https://wikipedia.org/wiki/Pike_River_Mine_disaster\" title=\"Pike River Mine disaster\">four explosions</a> takes place at the <a href=\"https://wikipedia.org/wiki/Pike_River_Mine\" title=\"Pike River Mine\">Pike River Mine</a> in New Zealand. Twenty-nine people are killed in the nation's worst mining disaster since 1914.", "no_year_html": "The first of <a href=\"https://wikipedia.org/wiki/Pike_River_Mine_disaster\" title=\"Pike River Mine disaster\">four explosions</a> takes place at the <a href=\"https://wikipedia.org/wiki/Pike_River_Mine\" title=\"Pike River Mine\">Pike River Mine</a> in New Zealand. Twenty-nine people are killed in the nation's worst mining disaster since 1914.", "links": [{"title": "Pike River Mine disaster", "link": "https://wikipedia.org/wiki/Pike_River_Mine_disaster"}, {"title": "Pike River Mine", "link": "https://wikipedia.org/wiki/Pike_River_Mine"}]}, {"year": "2013", "text": "A double suicide bombing at the Iranian embassy in Beirut kills 23 people and injures 160 others.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/2013_Iranian_embassy_bombing_in_Beirut\" title=\"2013 Iranian embassy bombing in Beirut\">double suicide bombing</a> at the <a href=\"https://wikipedia.org/wiki/List_of_diplomatic_missions_of_Iran\" title=\"List of diplomatic missions of Iran\">Iranian embassy</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> kills 23 people and injures 160 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2013_Iranian_embassy_bombing_in_Beirut\" title=\"2013 Iranian embassy bombing in Beirut\">double suicide bombing</a> at the <a href=\"https://wikipedia.org/wiki/List_of_diplomatic_missions_of_Iran\" title=\"List of diplomatic missions of Iran\">Iranian embassy</a> in <a href=\"https://wikipedia.org/wiki/Beirut\" title=\"Beirut\">Beirut</a> kills 23 people and injures 160 others.", "links": [{"title": "2013 Iranian embassy bombing in Beirut", "link": "https://wikipedia.org/wiki/2013_Iranian_embassy_bombing_in_Beirut"}, {"title": "List of diplomatic missions of Iran", "link": "https://wikipedia.org/wiki/List_of_diplomatic_missions_of_Iran"}, {"title": "Beirut", "link": "https://wikipedia.org/wiki/Beirut"}]}, {"year": "2022", "text": "A gunman kills five and injures 17 at Club Q, a gay nightclub in Colorado Springs, Colorado.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Colorado_Springs_nightclub_shooting\" title=\"Colorado Springs nightclub shooting\">A gunman kills five and injures 17</a> at Club Q, a <a href=\"https://wikipedia.org/wiki/Gay_bar\" title=\"Gay bar\">gay nightclub</a> in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colorado_Springs_nightclub_shooting\" title=\"Colorado Springs nightclub shooting\">A gunman kills five and injures 17</a> at Club Q, a <a href=\"https://wikipedia.org/wiki/Gay_bar\" title=\"Gay bar\">gay nightclub</a> in <a href=\"https://wikipedia.org/wiki/Colorado_Springs,_Colorado\" title=\"Colorado Springs, Colorado\">Colorado Springs, Colorado</a>.", "links": [{"title": "Colorado Springs nightclub shooting", "link": "https://wikipedia.org/wiki/Colorado_Springs_nightclub_shooting"}, {"title": "Gay bar", "link": "https://wikipedia.org/wiki/Gay_bar"}, {"title": "Colorado Springs, Colorado", "link": "https://wikipedia.org/wiki/Colorado_Springs,_Colorado"}]}, {"year": "2023", "text": "The 2023 Cricket World Cup final takes place at the Narendra Modi Stadium in Ahmedabad, India, played between host nation India and Australia.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/2023_Cricket_World_Cup_final\" title=\"2023 Cricket World Cup final\">2023 Cricket World Cup final</a> takes place at the <a href=\"https://wikipedia.org/wiki/Narendra_Modi_Stadium\" title=\"Narendra Modi Stadium\">Narendra <PERSON> Stadium</a> in <a href=\"https://wikipedia.org/wiki/Ahmedabad\" title=\"Ahmedabad\">Ahmedabad</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, played between host nation <a href=\"https://wikipedia.org/wiki/India_national_cricket_team\" title=\"India national cricket team\">India</a> and <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2023_Cricket_World_Cup_final\" title=\"2023 Cricket World Cup final\">2023 Cricket World Cup final</a> takes place at the <a href=\"https://wikipedia.org/wiki/Narendra_Modi_Stadium\" title=\"Narendra Modi Stadium\">Narendra Modi Stadium</a> in <a href=\"https://wikipedia.org/wiki/Ahmedabad\" title=\"Ahmedabad\">Ahmedabad</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>, played between host nation <a href=\"https://wikipedia.org/wiki/India_national_cricket_team\" title=\"India national cricket team\">India</a> and <a href=\"https://wikipedia.org/wiki/Australia_national_cricket_team\" title=\"Australia national cricket team\">Australia</a>.", "links": [{"title": "2023 Cricket World Cup final", "link": "https://wikipedia.org/wiki/2023_Cricket_World_Cup_final"}, {"title": "Narendra Modi Stadium", "link": "https://wikipedia.org/wiki/Narendra_Modi_Stadium"}, {"title": "Ahmedabad", "link": "https://wikipedia.org/wiki/Ahmedabad"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "India national cricket team", "link": "https://wikipedia.org/wiki/India_national_cricket_team"}, {"title": "Australia national cricket team", "link": "https://wikipedia.org/wiki/Australia_national_cricket_team"}]}], "Births": [{"year": "1417", "text": "<PERSON>, Count <PERSON><PERSON> of Simmern (d. 1480)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Simmern\" title=\"<PERSON>, Count <PERSON> of Simmern\"><PERSON>, Count <PERSON><PERSON> of Simmern</a> (d. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_of_Simmern\" title=\"<PERSON>, Count <PERSON> of Simmern\"><PERSON>, Count <PERSON><PERSON> of Simmern</a> (d. 1480)", "links": [{"title": "<PERSON>, Count <PERSON><PERSON> of Simmern", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1464", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1526)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ka<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ka<PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (d. 1526)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}]}, {"year": "1503", "text": "<PERSON>, Duke of Parma (d. 1547)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1547)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1547)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1563", "text": "<PERSON>, 1st Earl of Leicester, English poet and politician (d. 1626)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, English poet and politician (d. 1626)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester\" title=\"<PERSON>, 1st Earl of Leicester\"><PERSON>, 1st Earl of Leicester</a>, English poet and politician (d. 1626)", "links": [{"title": "<PERSON>, 1st Earl of Leicester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Leicester"}]}, {"year": "1600", "text": "<PERSON> of England, Scotland, and Ireland (d. 1649)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England, Scotland, and Ireland</a> (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England, Scotland, and Ireland</a> (d. 1649)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>, Dutch historian and diplomat (d. 1669)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and diplomat (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch historian and diplomat (d. 1669)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1617", "text": "<PERSON><PERSON><PERSON>, French painter and educator (d. 1655)", "html": "1617 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and educator (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter and educator (d. 1655)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON><PERSON><PERSON>, French priest and physicist (d. 1770)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and physicist (d. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French priest and physicist (d. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1711", "text": "<PERSON>, Russian physicist, chemist, astronomer, and geographer (d. 1765)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, chemist, astronomer, and geographer (d. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist, chemist, astronomer, and geographer (d. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, Austrian physician (d. 1809)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian physician (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leopold_<PERSON>brugger"}]}, {"year": "1722", "text": "<PERSON>, American lawyer and judge (d. 1810)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (d. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, American general (d. 1818)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON><PERSON><PERSON>, Maltese politician (d. 1830)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese politician (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese politician (d. 1830)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON><PERSON>, Danish sculptor and academic (d. 1844)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish sculptor and academic (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish sculptor and academic (d. 1844)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, American lawyer and politician (d. 1866)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Solomon_Foot\" title=\"Solomon Foot\"><PERSON></a>, American lawyer and politician (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Solomon Foot\"><PERSON></a>, American lawyer and politician (d. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, French diplomat and engineer, developed the Suez Canal (d. 1894)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and engineer, developed the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French diplomat and engineer, developed the <a href=\"https://wikipedia.org/wiki/Suez_Canal\" title=\"Suez Canal\">Suez Canal</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Suez Canal", "link": "https://wikipedia.org/wiki/Suez_Canal"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Slovenian journalist, physician, and politician (d. 1881)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian journalist, physician, and politician (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovenian journalist, physician, and politician (d. 1881)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i<PERSON>s"}]}, {"year": "1812", "text": "<PERSON>, German theologian and politician (d. 1885)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and politician (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and politician (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Indian queen (d. 1858)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jhansi\" title=\"Rani of Jhansi\"><PERSON></a>, Indian queen (d. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jhan<PERSON>\" title=\"Rani of Jhansi\"><PERSON></a>, Indian queen (d. 1858)", "links": [{"title": "<PERSON> of Jhansi", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1831", "text": "<PERSON>, American general, lawyer, and politician, 20th President of the United States (d. 1881)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general, lawyer, and politician, 20th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1833", "text": "<PERSON>, German psychologist, sociologist, and historian (d. 1911)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist, sociologist, and historian (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychologist, sociologist, and historian (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1834", "text": "<PERSON>, German physicist and academic (d. 1924)", "html": "1834 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, German-Swiss philosopher and academic (d. 1896)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss philosopher and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss philosopher and academic (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businessman (d. 1914)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/C._X._<PERSON>\" title=\"C. X. La<PERSON>bee\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._X._<PERSON>\" title=\"C. X. Larrabee\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman (d. 1914)", "links": [{"title": "C. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON>._<PERSON>bee"}]}, {"year": "1845", "text": "<PERSON>, Indian-English astronomer and author (d. 1939)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English astronomer and author (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English astronomer and author (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>-<PERSON><PERSON>, Russian composer, conductor, and educator (d. 1935)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Russian composer, conductor, and educator (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON>-<PERSON></a>, Russian composer, conductor, and educator (d. 1935)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American baseball player and evangelist (d. 1935)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Billy Sunday\"><PERSON></a>, American baseball player and evangelist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sunday\" title=\"Billy Sunday\"><PERSON></a>, American baseball player and evangelist (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sunday"}]}, {"year": "1873", "text": "<PERSON>, the first woman elected to the Parliament of New Zealand (d. 1935)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first woman elected to the Parliament of New Zealand (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first woman elected to the Parliament of New Zealand (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Russian civil servant and politician, 1st Head of State of The Soviet Union (d. 1946)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian civil servant and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of The Soviet Union</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian civil servant and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of The Soviet Union</a> (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Russian-Dutch mathematician and theorist (d. 1964)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Dutch mathematician and theorist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Dutch mathematician and theorist (d. 1964)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1877", "text": "<PERSON>, Italian businessman and politician, founded the Venice Film Festival (d. 1947)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Venice_Film_Festival\" title=\"Venice Film Festival\">Venice Film Festival</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian businessman and politician, founded the <a href=\"https://wikipedia.org/wiki/Venice_Film_Festival\" title=\"Venice Film Festival\">Venice Film Festival</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Venice Film Festival", "link": "https://wikipedia.org/wiki/Venice_Film_Festival"}]}, {"year": "1883", "text": "<PERSON>, Canadian-American actor and singer (d. 1957)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sparks\"><PERSON></a>, Canadian-American actor and singer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ned Sparks\"><PERSON></a>, Canadian-American actor and singer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sparks"}]}, {"year": "1887", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 1955)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1888", "text": "<PERSON>, Cuban-American chess player and theologian (d. 1942)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca\" title=\"<PERSON>\"><PERSON></a>, Cuban-American chess player and theologian (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca\" title=\"<PERSON>\"><PERSON></a>, Cuban-American chess player and theologian (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ra%C3%BAl_Capablanca"}]}, {"year": "1889", "text": "<PERSON>, American actor, singer, and dancer (d. 1966)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, English footballer and coach (d. 1949)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, Welsh poet and politician (d. 1970)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh poet and politician (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Welsh poet and politician (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, French trumpet player (d. 1952)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Voisin\" title=\"<PERSON>\"><PERSON></a>, French trumpet player (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Voisin\" title=\"<PERSON>\"><PERSON></a>, French trumpet player (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Voisin"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese admiral and politician, 14th President of Portugal (d. 1987)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese admiral and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese admiral and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A9rico_Tom%C3%A1s"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1895", "text": "<PERSON>, American photographer (d. 1989)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Dutch footballer and architect (d. 1964)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and architect (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer and architect (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American lieutenant and pilot (d. 1918)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Slovenian philosopher and mountaineer (d. 1924)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>lement_<PERSON>\" title=\"Klement <PERSON>g\"><PERSON><PERSON></a>, Slovenian philosopher and mountaineer (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lement <PERSON>\"><PERSON><PERSON></a>, Slovenian philosopher and mountaineer (d. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>g"}]}, {"year": "1898", "text": "<PERSON>, German-American physicist and academic (d. 2003)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American physicist and academic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, Iranian religious leader and scholar (d. 1992)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Khoe<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-Khoe<PERSON>\"><PERSON></a>, Iranian religious leader and scholar (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Khoe<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> al-Khoei\"><PERSON></a>, Iranian religious leader and scholar (d. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hoe<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American poet and critic (d. 1979)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and critic (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Irish-English ice hockey player and manager (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English ice hockey player and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-English ice hockey player and manager (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Russian mathematician and hydrodynamicist (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and hydrodynamicist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and hydrodynamicist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, German author and politician (d. 1983)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and politician (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and politician (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Russian mathematician (d. 1961)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nina_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Jr., American murderer (d. 1971)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American murderer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American murderer (d. 1971)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1905", "text": "<PERSON>, American actress (d. 1991)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American trombonist, composer and bandleader (d. 1956)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer and bandleader (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, composer and bandleader (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German SS officer (d. 1945)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4dle\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4dle\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franz_Sch%C3%A4dle"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1907", "text": "<PERSON>, Austrian-German artist (d. 1983)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German artist (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German artist (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American author (d. 1991)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Austrian-American theorist, educator, and author (d. 2005).", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American theorist, educator, and author (d. 2005).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American theorist, educator, and author (d. 2005).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Mexican actor (d. 1980).", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez_Moctezuma\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (d. 1980).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3pez_Moctezuma\" title=\"<PERSON>\"><PERSON></a>, Mexican actor (d. 1980).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_L%C3%B3pez_Moctezuma"}]}, {"year": "1910", "text": "<PERSON>, English race car driver, author, and explorer (d. 1970)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver, author, and explorer (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver, author, and explorer (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American bishop (d. 2015)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Romanian-American biologist and physician, Nobel Prize laureate (d. 2008)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American biologist and physician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1912", "text": "<PERSON>, American meteorologist and author (d. 2014)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American meteorologist and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(meteorologist)\" title=\"<PERSON> (meteorologist)\"><PERSON></a>, American meteorologist and author (d. 2014)", "links": [{"title": "<PERSON> (meteorologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(meteorologist)"}]}, {"year": "1915", "text": "<PERSON>, Jr., American pharmacologist and biochemist, Nobel Prize laureate (d. 1974)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1974)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>,_Jr."}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Indian politician, 3rd Prime Minister of India (d. 1984)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> Gandhi\"><PERSON><PERSON></a>, Indian politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Indira_Gandhi"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Italian director and screenwriter (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian director and screenwriter (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pontecorvo"}]}, {"year": "1919", "text": "<PERSON>, English-Canadian actor, singer, and director (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, singer, and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian actor, singer, and director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actress and singer (d. 1991)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American baseball player and coach (d. 1993)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American pastor and educator (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and educator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Indian director, playwright, and composer (d. 1995)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, playwright, and composer (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian director, playwright, and composer (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Ukrainian-Russian linguist, epigrapher, and ethnographer (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian linguist, epigrapher, and ethnographer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian linguist, epigrapher, and ethnographer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Serbian footballer and coach (d. 2008)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian footballer and coach (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1923", "text": "<PERSON>, Jr., American author, critic, and academic (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, critic, and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American author, critic, and academic (d. 2013)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1924", "text": "<PERSON>, American painter and poet (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and poet (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English actor (d. 2024)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)\" title=\"<PERSON> (English actor)\"><PERSON></a>, English actor (d. 2024)", "links": [{"title": "<PERSON> (English actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(English_actor)"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Norwegian-Italian sculptor (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"K<PERSON>\"><PERSON><PERSON></a>, Norwegian-Italian sculptor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"K<PERSON>\"><PERSON><PERSON></a>, Norwegian-Italian sculptor (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>een"}]}, {"year": "1924", "text": "<PERSON>-<PERSON>, English physician and academic (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and academic (d. 2017)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish-English sociologist, historian, and academic (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>yg<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-English sociologist, historian, and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>y<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Polish-English sociologist, historian, and academic (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>g<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American academic and diplomat, 16th United States Ambassador to the United Nations (d. 2006)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and diplomat, 16th <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Italian journalist and politician (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Pi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian journalist and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pino_Rauti"}]}, {"year": "1926", "text": "<PERSON>, Jamaican playwright and screenwriter (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican playwright and screenwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican playwright and screenwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Indian wrestler, actor, and politician (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian wrestler, actor, and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian wrestler, actor, and politician (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American historian and scholar (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman <PERSON>\"><PERSON></a>, Canadian-American historian and scholar (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Cantor\"><PERSON></a>, Canadian-American historian and scholar (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Danish tennis player, referee, and sportscaster (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish tennis player, referee, and sportscaster (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish tennis player, referee, and sportscaster (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American astronomer (d. 2009)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American journalist and talk show host (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and talk show host (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American judge and author", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Swedish footballer and scout (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and scout (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer and scout (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Russian footballer and manager (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1934)\" title=\"<PERSON><PERSON> (footballer, born 1934)\"><PERSON><PERSON></a>, Russian footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1934)\" title=\"<PERSON><PERSON> (footballer, born 1934)\"><PERSON><PERSON></a>, Russian footballer and manager (d. 2011)", "links": [{"title": "<PERSON><PERSON> (footballer, born 1934)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer,_born_1934)"}]}, {"year": "1934", "text": "<PERSON>, English conductor (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor (d. 2022)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conductor)"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON>, Egyptian-American biochemist and scholar (d. 1990)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-American biochemist and scholar (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian-American biochemist and scholar (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American engineer, businessman, and author (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businessman, and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer, businessman, and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and talk show host", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American singer (d. 2012)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer (d. 2012)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1936", "text": "<PERSON>, Taiwanese-American chemist and academic, Nobel Prize laureate", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Taiwanese-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian actor and director (d. 2017) ", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian actor and director (d. 2017) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian actor and director (d. 2017) ", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ljubi%C5%A1a_Samard%C5%BEi%C4%87"}]}, {"year": "1937", "text": "<PERSON>, English psychologist and author", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English psychologist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, South African rugby league player (d. 2011)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby league player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby league player (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian cricketer (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American businessman and philanthropist, founded Turner Broadcasting System", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Turner_Broadcasting_System\" title=\"Turner Broadcasting System\">Turner Broadcasting System</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Turner_Broadcasting_System\" title=\"Turner Broadcasting System\">Turner Broadcasting System</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Turner Broadcasting System", "link": "https://wikipedia.org/wiki/Turner_Broadcasting_System"}]}, {"year": "1939", "text": "<PERSON>, Romanian academic and politician, 3rd President of Romania", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian academic and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American political scientist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter and record producer (d. 2017)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and record producer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and record producer (d. 2017)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American chemist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, author and expert on test-prep (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author and expert on test-prep (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, author and expert on test-prep (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actor and producer (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American captain and politician, 19th United States Secretary of Health and Human Services", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 19th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 19th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "1942", "text": "<PERSON>, English engineer and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>lift\"><PERSON></a>, English engineer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Clift\"><PERSON></a>, English engineer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American golfer (d. 1998)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American fashion designer, founded Calvin Klein Inc.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(fashion_house)\" title=\"<PERSON> (fashion house)\">Calvin Klein Inc.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(fashion_house)\" title=\"<PERSON> (fashion house)\">Calvin Klein Inc.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (fashion house)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_house)"}]}, {"year": "1942", "text": "<PERSON>, American poet and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American saxophonist and educator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Cuban-American baseball player and manager (d. 1990)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Aurel<PERSON>_Monteagudo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player and manager (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rel<PERSON>_Monteagudo\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban-American baseball player and manager (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelio_Monteagudo"}]}, {"year": "1944", "text": "<PERSON>, Greek soprano and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Dutch engineer (d. 2008)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch engineer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American baseball player and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Faroese politician, 10th Prime Minister of the Faroe Islands (d. 2024)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Faroese politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Faroese politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "Lamar <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, French chef and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chef and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Greek-Canadian chess player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian chess player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Canadian chess player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Baron <PERSON> of Thoroton, Scottish lawyer and politician, Lord High Chancellor of Great Britain", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Thoroton\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Thoroton\"><PERSON>, Baron <PERSON> of Thoroton</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Thoroton\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON> of Thoroton\"><PERSON>, Baron <PERSON> of Thoroton</a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Thoroton", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Thoroton"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor (d. 1994)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Egyptian field marshal and politician, 6th President of Egypt", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian field marshal and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian field marshal and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian ice hockey player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actress", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American screenwriter and producer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American colonel, pilot, and astronaut", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Guamanian-American journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guamanian-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guamanian-American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Connor\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lynnis_O%27Connor"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Ukrainian-born computer scientist (d. 2020)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>il<PERSON>\" title=\"<PERSON><PERSON><PERSON> Vilkomir\"><PERSON><PERSON><PERSON></a>, Ukrainian-born computer scientist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Vilkomir\"><PERSON><PERSON><PERSON></a>, Ukrainian-born computer scientist (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sergiy_Vilkomir"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Israeli singer-songwriter and actress (d. 2000)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Of<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actress (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actress (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ofra_Haza"}]}, {"year": "1957", "text": "<PERSON>, American actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Virtue\" title=\"Tom Virtue\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Virtue\" title=\"Tom Virtue\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Virtue"}]}, {"year": "1958", "text": "<PERSON>, English magazine editor (d. 2007)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blow\"><PERSON></a>, English magazine editor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blow\"><PERSON></a>, English magazine editor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian sergeant and politician, 12th Prime Minister of Lithuania", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Al<PERSON><PERSON><PERSON>_<PERSON>kevi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian sergeant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">12th Prime Minister of Lithuania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>kevi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian sergeant and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lithuania\" title=\"Prime Minister of Lithuania\">12th Prime Minister of Lithuania</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Algirdas_Butkevi%C4%8Dius"}, {"title": "Prime Minister of Lithuania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lithuania"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American actor and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Terrence <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Te<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American historian, author, and academic", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American sportscaster and journalist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American bishop, author, and theologian", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON> (bishop)\"><PERSON></a>, American bishop, author, and theologian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON> (bishop)\"><PERSON></a>, American bishop, author, and theologian", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>(bishop)"}]}, {"year": "1959", "text": "<PERSON>, American politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American wrestler and manager (d. 2003)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Miss Elizabeth\">Miss <PERSON></a>, American wrestler and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Elizabeth\" title=\"Miss Elizabeth\">Miss <PERSON></a>, American wrestler and manager (d. 2003)", "links": [{"title": "Miss <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American drummer, songwriter, and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Danish athlete", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, American actress, director, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American lawyer and politician, 12th Governor of Alaska", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Alaska\" class=\"mw-redirect\" title=\"Governor of Alaska\">Governor of Alaska</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Governor_of_Alaska\" class=\"mw-redirect\" title=\"Governor of Alaska\">Governor of Alaska</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alaska", "link": "https://wikipedia.org/wiki/Governor_of_Alaska"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Filipino boxer and trainer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>e%C3%B1alosa\" title=\"Dodie <PERSON>\"><PERSON><PERSON></a>, Filipino boxer and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>e%C3%B1alosa\" title=\"<PERSON>die <PERSON>\"><PERSON><PERSON></a>, Filipino boxer and trainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Pe%C3%B1alosa"}]}, {"year": "1963", "text": "<PERSON>, American actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_(actress)"}]}, {"year": "1963", "text": "<PERSON>, English-American field hockey player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American-English mathematician and academic", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred Diamond\"><PERSON></a>, American-English mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fred Diamond\"><PERSON></a>, American-English mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American saxophonist and flute player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and flute player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Irish footballer and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1964)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1964)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, born 1964)\"><PERSON></a>, Irish footballer and coach", "links": [{"title": "<PERSON> (footballer, born 1964)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1964)"}]}, {"year": "1964", "text": "<PERSON>, South Korean actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, South Korean actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>_(actor)"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English-American engineer and astronaut", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, French footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Scottish actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Douglas_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Paulo S. L<PERSON> <PERSON>, Brazilian cryptographer and academic", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Paulo_S._L._<PERSON><PERSON>_<PERSON>\" title=\"Paulo S. L. <PERSON>\"><PERSON> S. L<PERSON></a>, Brazilian cryptographer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paulo_S._L._<PERSON><PERSON>_<PERSON>\" title=\"Paulo S. L. <PERSON>\">Paulo S. L<PERSON></a>, Brazilian cryptographer and academic", "links": [{"title": "Paulo S. L. M. <PERSON>eto", "link": "https://wikipedia.org/wiki/Paulo_S._L._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, screenwriter, and playwright", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, producer, screenwriter, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, American actor, director, producer, screenwriter, and playwright", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_(filmmaker)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, American rabbi and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ach\" title=\"<PERSON><PERSON><PERSON><PERSON> Bo<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American rabbi and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Bo<PERSON>ach\"><PERSON><PERSON><PERSON><PERSON></a>, American rabbi and author", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ach"}]}, {"year": "1966", "text": "<PERSON>, American sprinter and hurdler", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American chef and author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Di<PERSON>rito\"><PERSON><PERSON></a>, American chef and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Di<PERSON>pirito\"><PERSON><PERSON></a>, American chef and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Georgian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actor and martial artist", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Belgian politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Belgian race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ertu%C4%9Frul_Sa%C4%9Flam\" title=\"Ertuğrul Sağlam\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ertu%C4%9Frul_Sa%C4%9Flam\" title=\"Ertuğrul Sağlam\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer and coach", "links": [{"title": "Ertuğ<PERSON>l <PERSON>", "link": "https://wikipedia.org/wiki/Ertu%C4%9Frul_Sa%C4%9Flam"}]}, {"year": "1969", "text": "<PERSON>, Moroccan-French cyclist and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-French cyclist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan-French cyclist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American author and activist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and activist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English bass player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American motorcycle racer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American R&B singer-songwriter and musician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"<PERSON> Rich\"><PERSON></a>, American R&amp;B singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Rich\" title=\"Tony Rich\"><PERSON></a>, American R&amp;B singer-songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, English-American model and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-American model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, American dancer and choreographer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American dancer and choreographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American basketball player and agent", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and agent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and agent", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress, model and Miss Universe 1994", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, model and <a href=\"https://wikipedia.org/wiki/Miss_Universe_1994\" title=\"Miss Universe 1994\">Miss Universe 1994</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress, model and <a href=\"https://wikipedia.org/wiki/Miss_Universe_1994\" title=\"Miss Universe 1994\">Miss Universe 1994</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Miss Universe 1994", "link": "https://wikipedia.org/wiki/Miss_Universe_1994"}]}, {"year": "1976", "text": "<PERSON>, American businessman, co-founded Twitter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Twitter", "link": "https://wikipedia.org/wiki/Twitter"}]}, {"year": "1976", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Japanese singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Petr_S%C3%BDkora\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petr_S%C3%BDkora\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Petr_S%C3%BDkora"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Stylianos_Venetidis\" title=\"Stylianos Venetidis\"><PERSON><PERSON><PERSON><PERSON> Venetidis</a>, Greek footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stylianos_Venetidis\" title=\"Stylianos Venetidis\"><PERSON><PERSON><PERSON><PERSON> Venetidis</a>, Greek footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stylianos_Venetidis"}]}, {"year": "1977", "text": "<PERSON>, American actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, American gymnast and runner", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast and runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast and runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>i_<PERSON>rug"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Belgian computer programmer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Buytaert\"><PERSON><PERSON></a>, Belgian computer programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Buytaert\"><PERSON><PERSON></a>, Belgian computer programmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dries_Buy<PERSON>ert"}]}, {"year": "1978", "text": "<PERSON>, Canadian singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Czech discus thrower and shot putter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/V%C4%9Bra_Posp%C3%AD%C5%A1ilov%C3%A1-Cechlov%C3%A1\" title=\"V<PERSON>ra Pospíšilová-Cechlová\"><PERSON><PERSON><PERSON>-<PERSON></a>, Czech discus thrower and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C4%9Bra_Posp%C3%AD%C5%A1ilov%C3%A1-Cechlov%C3%A1\" title=\"V<PERSON>ra Pospíšilová-Cechlová\"><PERSON><PERSON><PERSON>spíšilová-<PERSON>lov<PERSON></a>, Czech discus thrower and shot putter", "links": [{"title": "Věra <PERSON>-Cechlová", "link": "https://wikipedia.org/wiki/V%C4%9Bra_Posp%C3%AD%C5%A1ilov%C3%A1-Cechlov%C3%A1"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, New Zealand rower", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Mah%C3%A9_Drysdale\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mah%C3%A9_Drysdale\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand rower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mah%C3%A9_Drysdale"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American director, screenwriter, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(running_back)\" title=\"<PERSON> (running back)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (running back)", "link": "https://wikipedia.org/wiki/<PERSON>_(running_back)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, English footballer and manager", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otis_<PERSON>sby"}]}, {"year": "1980", "text": "<PERSON>, Serbian basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_Radmanovi%C4%87"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Argentine rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADn_Fern%C3%A1ndez_Lobbe\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juan_<PERSON>%C3%ADn_Fern%C3%A1ndez_Lobbe\" title=\"<PERSON>\"><PERSON></a>, Argentine rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Mart%C3%ADn_Fern%C3%A1ndez_Lobbe"}]}, {"year": "1981", "text": "<PERSON>, German race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>er\" title=\"<PERSON>\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>er"}]}, {"year": "1981", "text": "DJ <PERSON><PERSON><PERSON>, South Korean DJ, producer, and songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON><PERSON>\" title=\"DJ <PERSON>ku<PERSON>\">DJ <PERSON></a>, South Korean DJ, producer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON><PERSON>\" title=\"DJ <PERSON><PERSON>\">DJ <PERSON><PERSON></a>, South Korean DJ, producer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Welsh-English cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Welsh-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Welsh-English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1982", "text": "<PERSON>, Puerto Rican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A1nchez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jonathan_S%C3%A1nchez"}]}, {"year": "1983", "text": "<PERSON>, Canadian skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Driver\" title=\"Adam Driver\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Driver\" title=\"Adam Driver\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Polish-Canadian model", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Werbowy\" title=\"<PERSON><PERSON> Werbowy\"><PERSON><PERSON></a>, Polish-Canadian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rbowy\" title=\"<PERSON><PERSON>rbowy\"><PERSON><PERSON></a>, Polish-Canadian model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Daria_Werbowy"}]}, {"year": "1984", "text": "<PERSON>, Uruguayan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1984", "text": "<PERSON>, American activist (d. 2014)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brittany_Maynard"}]}, {"year": "1985", "text": "<PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Chris_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chris_Eagles\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Betty\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American singer-songwriter, dancer, and actress", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Serbian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Milan_Smiljani%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Smiljani%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milan_Smiljani%C4%87"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON>_Soler_Espinosa\" title=\"<PERSON><PERSON><PERSON><PERSON> Soler Espinosa\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON>_<PERSON>er_Espinosa\" title=\"<PERSON><PERSON><PERSON><PERSON> Soler Espinosa\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON>_Soler_Espinosa"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Argentine footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/V%C3%ADctor_Cuesta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%ADctor_Cuesta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Cuesta"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Estonian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Australian footballer (d. 2012)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer,_born_1989)\" title=\"<PERSON> (Australian rules footballer, born 1989)\"><PERSON></a>, Australian footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer,_born_1989)\" title=\"<PERSON> (Australian rules footballer, born 1989)\"><PERSON></a>, Australian footballer (d. 2012)", "links": [{"title": "<PERSON> (Australian rules footballer, born 1989)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_rules_footballer,_born_1989)"}]}, {"year": "1989", "text": "<PERSON>, Russian ski jumper", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian ski jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American rapper", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Tyga\" title=\"Tyga\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tyga\" title=\"Tyga\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "Tyga", "link": "https://wikipedia.org/wiki/Tyga"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American football and soccer player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football and soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>edik<PERSON>_<PERSON>\" title=\"<PERSON>ed<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>edik<PERSON>_<PERSON>\" title=\"<PERSON>ed<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>edik<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Fabien_Antunes\" title=\"Fabien Antunes\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabien_Antunes\" title=\"Fabien Antunes\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabien_Antunes"}]}, {"year": "1991", "text": "<PERSON>, Serbian basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Marina_Markovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Markovi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marina_Markovi%C4%87"}]}, {"year": "1992", "text": "<PERSON>, Australian cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1992", "text": "<PERSON>, Austrian politician", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1993", "text": "<PERSON>, American baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Senegalese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Hungarian model", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American YouTuber", "html": "1996 - <a href=\"https://wikipedia.org/wiki/RiceGum\" title=\"RiceGum\"><PERSON><PERSON><PERSON></a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/RiceGum\" title=\"RiceGum\">Rice<PERSON><PERSON></a>, American YouTuber", "links": [{"title": "RiceGum", "link": "https://wikipedia.org/wiki/RiceGum"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, American YouTuber", "html": "1996 - <a href=\"https://wikipedia.org/wiki/FaZe_Rug\" title=\"FaZe Rug\">FaZe Rug</a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/FaZe_Rug\" title=\"FaZe Rug\">FaZe Rug</a>, American YouTuber", "links": [{"title": "FaZe Rug", "link": "https://wikipedia.org/wiki/FaZe_Rug"}]}, {"year": "1996", "text": "<PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ko<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kotonowaka_Masahiro"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Venezuelan footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>uel_<PERSON>\" title=\"<PERSON><PERSON>res<PERSON>\"><PERSON><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uel_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "496", "text": "<PERSON>", "html": "496 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON> <PERSON><PERSON><PERSON>\">Pope <PERSON><PERSON><PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_I\" title=\"<PERSON> <PERSON><PERSON>sius I\">Pope <PERSON><PERSON><PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "498", "text": "<PERSON> <PERSON><PERSON> II", "html": "498 - <a href=\"https://wikipedia.org/wiki/Pope_Ana<PERSON>sius_II\" title=\"<PERSON> <PERSON><PERSON>sius II\">Pope <PERSON><PERSON><PERSON> II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ana<PERSON>sius_II\" title=\"Pope Ana<PERSON>sius II\">Pope <PERSON><PERSON><PERSON> II</a>", "links": [{"title": "<PERSON> <PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II"}]}, {"year": "930", "text": "<PERSON>, Chinese chief strategist", "html": "930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chief strategist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese chief strategist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1034", "text": "<PERSON><PERSON> <PERSON>, Margrave of Lower Lusatia (b. c. 990)", "html": "1034 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Lower_Lusatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Margrave of Lower Lusatia\"><PERSON><PERSON>, Margrave of Lower Lusatia</a> (b. c. 990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Lower_Lusatia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Margrave of Lower Lusatia\"><PERSON><PERSON>, Margrave of Lower Lusatia</a> (b. c. 990)", "links": [{"title": "<PERSON><PERSON>, Margrave of Lower Lusatia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON><PERSON>_of_Lower_Lusatia"}]}, {"year": "1092", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (b. 1055)", "html": "1092 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> (b. 1055)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON><PERSON> (b. 1055)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1267", "text": "<PERSON>, Franciscan scholar", "html": "1267 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Franciscan scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Franciscan scholar", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>go"}]}, {"year": "1288", "text": "<PERSON>, Margrave of Baden-Baden (b. 1230)", "html": "1288 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (b. 1230)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden\" title=\"<PERSON>, Margrave of Baden-Baden\"><PERSON>, Margrave of Baden-Baden</a> (b. 1230)", "links": [{"title": "<PERSON>, Margrave of Baden-Baden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Baden-Baden"}]}, {"year": "1298", "text": "<PERSON><PERSON><PERSON>, Saxon saint (b. c. 1240)", "html": "1298 - <a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON><PERSON>_of_Hackeborn\" title=\"<PERSON><PERSON><PERSON><PERSON> of Hackeborn\"><PERSON><PERSON><PERSON></a>, Saxon saint (b. c. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Hackeborn\" title=\"<PERSON><PERSON><PERSON><PERSON> of Hackeborn\"><PERSON><PERSON><PERSON></a>, Saxon saint (b. c. 1240)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Hackeborn", "link": "https://wikipedia.org/wiki/Mechthild_of_<PERSON>"}]}, {"year": "1350", "text": "<PERSON> of Brienne, Count of Eu (b. 1315)", "html": "1350 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brien<PERSON>,_Count_of_Eu\" title=\"<PERSON> of Brienne, Count of Eu\"><PERSON> of Brienne, Count of Eu</a> (b. 1315)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brien<PERSON>,_Count_of_Eu\" title=\"<PERSON> of Brienne, Count of Eu\"><PERSON> of Brienne, Count of Eu</a> (b. 1315)", "links": [{"title": "<PERSON> of Brienne, Count of Eu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1481", "text": "<PERSON>, 8th Countess of Norfolk (b. 1472)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Countess_of_Norfolk\" title=\"<PERSON>, 8th Countess of Norfolk\"><PERSON>, 8th Countess of Norfolk</a> (b. 1472)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_8th_Countess_of_Norfolk\" title=\"<PERSON>, 8th Countess of Norfolk\"><PERSON>, 8th Countess of Norfolk</a> (b. 1472)", "links": [{"title": "<PERSON>, 8th Countess of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_8th_Countess_of_Norfolk"}]}, {"year": "1557", "text": "<PERSON><PERSON>, Italian wife of <PERSON><PERSON><PERSON> (b. 1494)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_the_Old\" title=\"<PERSON><PERSON><PERSON> I the Old\"><PERSON><PERSON><PERSON> I the Old</a> (b. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian wife of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_the_Old\" title=\"<PERSON><PERSON><PERSON> I the Old\"><PERSON><PERSON><PERSON> I the Old</a> (b. 1494)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rza"}, {"title": "<PERSON><PERSON><PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_the_Old"}]}, {"year": "1577", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1508)", "html": "1577 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1508)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON><PERSON> of Russia (b. 1554)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/Tsarevich_<PERSON>_<PERSON>_of_Russia\" title=\"Tsarevich <PERSON> of Russia\">Tsarevich <PERSON> of Russia</a> (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsarevich_<PERSON>_<PERSON>_of_Russia\" title=\"Tsarevich <PERSON> of Russia\">Tsarevich <PERSON> of Russia</a> (b. 1554)", "links": [{"title": "<PERSON><PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1649", "text": "<PERSON><PERSON><PERSON>, German scholar and author (b. 1576)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German scholar and author (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German scholar and author (b. 1576)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aspar_<PERSON><PERSON>"}]}, {"year": "1665", "text": "<PERSON>, French-Italian painter (b. 1594)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian painter (b. 1594)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian painter (b. 1594)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1672", "text": "<PERSON>, English bishop and philosopher (b. 1614)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1614)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1679", "text": "<PERSON>, Massachusetts governor (b. 1592)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(colonist)\" title=\"<PERSON> (colonist)\"><PERSON></a>, Massachusetts governor (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(colonist)\" title=\"<PERSON> (colonist)\"><PERSON></a>, Massachusetts governor (b. 1592)", "links": [{"title": "<PERSON> (colonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(colonist)"}]}, {"year": "1692", "text": "<PERSON>, English poet and playwright (b. 1642)", "html": "1692 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1642)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1703", "text": "Man in the Iron Mask, French prisoner", "html": "1703 - <a href=\"https://wikipedia.org/wiki/Man_in_the_Iron_Mask\" title=\"Man in the Iron Mask\">Man in the Iron Mask</a>, French prisoner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man_in_the_Iron_Mask\" title=\"Man in the Iron Mask\">Man in the Iron Mask</a>, French prisoner", "links": [{"title": "Man in the Iron Mask", "link": "https://wikipedia.org/wiki/Man_in_the_Iron_Mask"}]}, {"year": "1723", "text": "<PERSON>, French courtier and soldier (b. 1632)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>aumont\"><PERSON></a>, French courtier and soldier (b. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Caumont\"><PERSON></a>, French courtier and soldier (b. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1772", "text": "<PERSON>, American politician, Colonial Governor of Virginia (b. 1711)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia\" title=\"List of colonial governors of Virginia\">Colonial Governor of Virginia</a> (b. 1711)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)"}, {"title": "List of colonial governors of Virginia", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Virginia"}]}, {"year": "1773", "text": "<PERSON>, 1st Duke of Leinster, Irish soldier and politician (b. 1722)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Leinster\" title=\"<PERSON>, 1st Duke of Leinster\"><PERSON>, 1st Duke of Leinster</a>, Irish soldier and politician (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leinster\" title=\"<PERSON>, 1st Duke of Leinster\"><PERSON>, 1st Duke of Leinster</a>, Irish soldier and politician (b. 1722)", "links": [{"title": "<PERSON>, 1st Duke of Leinster", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Leinster"}]}, {"year": "1785", "text": "<PERSON>, French harpsichord player and composer (b. 1720)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1720)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1798", "text": "<PERSON>, Irish general (b. 1763)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish general (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolfe To<PERSON>\"><PERSON></a>, Irish general (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, Italian composer (b. 1728)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON><PERSON><PERSON>, French dancer and choreographer (b. 1727)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French dancer and choreographer (b. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French dancer and choreographer (b. 1727)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, German mathematician and physicist (b. 1763)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Austrian pianist and composer (b. 1797)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Schubert\"><PERSON></a>, Austrian pianist and composer (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON><PERSON>, Bengali revolutionary (b. 1782)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Titumir\" title=\"Titumir\"><PERSON><PERSON><PERSON><PERSON></a>, Bengali revolutionary (b. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Titumir\" title=\"Titumir\"><PERSON><PERSON><PERSON><PERSON></a>, Bengali revolutionary (b. 1782)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Titumir"}]}, {"year": "1850", "text": "<PERSON>, American colonel, lawyer, and politician, 9th Vice President of the United States (b. 1780)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 9th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Georgian general (b. 1798)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian general (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Georgian general (b. 1798)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, German-English engineer (b. 1823)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English engineer (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English engineer (b. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American poet (b. 1849)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American historian and academic (b. 1810)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and academic (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German chemist (b. 1835)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Swedish-born American labor activist (b. 1879)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Swedish-born American labor activist (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Swedish-born American labor activist (b. 1879)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1918", "text": "<PERSON>, American religious leader, 6th President of The Church of Jesus Christ of Latter-day Saints (b. 1838)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 6th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 6th <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1924", "text": "<PERSON>, American actor, director, producer, and screenwriter (b. 1880)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Thomas <PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (b. 1880)", "links": [{"title": "Thomas H<PERSON>", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>._Ince"}]}, {"year": "1928", "text": "<PERSON>, French actress (b. 1864)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rang%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rang%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_B%C3%A9rang%C3%A8re"}]}, {"year": "1931", "text": "<PERSON>, Chinese poet and translator (b. 1897)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese poet and translator (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese poet and translator (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "1938", "text": "<PERSON>, Ukrainian-Russian philosopher and theologian (b. 1866)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian philosopher and theologian (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian philosopher and theologian (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lev_Shestov"}]}, {"year": "1942", "text": "<PERSON>, Polish painter and critic (b. 1892)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter and critic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish painter and critic (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 29th <PERSON><PERSON><PERSON><PERSON> (b. 1895)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 29th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 29th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1949", "text": "<PERSON>, Belgian painter (b. 1860)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Danish actor (b. 1891)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON> Redal\"><PERSON><PERSON></a>, Danish actor (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>al\" title=\"<PERSON><PERSON> Redal\"><PERSON><PERSON></a>, Danish actor (b. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aage_Redal"}]}, {"year": "1954", "text": "<PERSON>, English footballer and manager (b. 1870)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist and author (b. 1891)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1891)", "links": [{"title": "Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English-American actor (b. 1903)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian archbishop (b. 1892)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress (b. 1899)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Georgian author, poet, and playwright (b. 1880)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian author, poet, and playwright (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Georgian author, poet, and playwright (b. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Italian-French actress (b. 1901)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French actress (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French actress (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American archer (b. 1889)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American priest and soldier, Medal of Honor recipient (b. 1927)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest and soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1968", "text": "<PERSON>, Australian theatre producer and director (b. 1895)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"May <PERSON>\"><PERSON></a>, Australian theatre producer and director (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian theatre producer and director (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor (b. 1903)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Soviet pianist (b. 1899)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet pianist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Yudina"}]}, {"year": "1974", "text": "<PERSON>, American trombonist (b. 1902)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American author and illustrator (b. 1928)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American colonel, lawyer, and politician, 42nd Governor of Indiana (b. 1902)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, lawyer, and politician, 42nd <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Indiana", "link": "https://wikipedia.org/wiki/Governor_of_Indiana"}]}, {"year": "1975", "text": "<PERSON>, German writer in Low German (b. 1887)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German writer in Low German (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German writer in Low German (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON> (novelist), English novelist, (b. 1912)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON> (novelist)</a>, English novelist, (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON> (novelist)</a>, English novelist, (b. 1912)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)"}]}, {"year": "1976", "text": "<PERSON>, Indian-Scottish architect and academic, designed the Coventry Cathedral (b. 1907)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Scottish architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Scottish architect and academic, designed the <a href=\"https://wikipedia.org/wiki/Coventry_Cathedral\" title=\"Coventry Cathedral\">Coventry Cathedral</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coventry Cathedral", "link": "https://wikipedia.org/wiki/Coventry_Cathedral"}]}, {"year": "1983", "text": "<PERSON>, English singer-songwriter and guitarist (b. 1947)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actor, singer, and dancer (b. 1902)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer, and dancer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, singer, and dancer (b. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>it"}]}, {"year": "1985", "text": "<PERSON>, Mexican lyric opera tenor and bolero vocalist (b. 1900)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lyric opera tenor and bolero vocalist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lyric opera tenor and bolero vocalist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American-Greek businesswoman (b. 1950)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek businesswoman (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Greek businesswoman (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American author (b. 1927)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peggy Parish\"><PERSON></a>, American author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peggy_Parish\" title=\"Peggy Parish\"><PERSON></a>, American author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Peggy_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American race car driver (b. 1950)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Grant_Adcox"}]}, {"year": "1990", "text": "<PERSON>, Chinese general and politician (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jen\" title=\"<PERSON> Li-jen\"><PERSON></a>, Chinese general and politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-jen\" title=\"<PERSON> Li-jen\"><PERSON></a>, Chinese general and politician (b. 1900)", "links": [{"title": "<PERSON>jen", "link": "https://wikipedia.org/wiki/<PERSON>_Li-jen"}]}, {"year": "1991", "text": "<PERSON>, Austrian-American actor (b. 1907)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter (b. 1940)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actress (b. 1938)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>i"}]}, {"year": "1998", "text": "<PERSON>, Japanese-American meteorologist and academic (b. 1920)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American meteorologist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American meteorologist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American director, producer, and screenwriter (b. 1928)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English director and producer (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director and producer (b. 1926)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1999", "text": "<PERSON>, Russian-American artist and publisher (b. 1912)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American artist and publisher (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American artist and publisher (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Canadian painter and stained glass artist (b. 1924)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and <a href=\"https://wikipedia.org/wiki/Stained_glass\" title=\"Stained glass\">stained glass</a> artist (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Stained glass", "link": "https://wikipedia.org/wiki/Stained_glass"}]}, {"year": "2003", "text": "<PERSON>, Australian race car driver (b. 1939)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Filipino journalist and composer (b. 1934)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and composer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and composer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>co"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Dutch sculptor and academic (b. 1914)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sculptor and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch sculptor and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, German actor and director (b. 1932)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor and director (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, American author and illustrator (b. 1939)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American singer-songwriter and producer (b. 1942)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English pharmacologist and academic, Nobel Prize laureate (b. 1927)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2005", "text": "<PERSON>, Danish director, producer, and screenwriter (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director, producer, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish director, producer, and screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American football player, coach and scout (b. 1919)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach and scout (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach and scout (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter (b. 1955)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English rugby player and coach (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and coach (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Filipino actor (b. 1948)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actor (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1952)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Turkish director and screenwriter (b. 1916)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/%C3%96mer_L%C3%BCtfi_Akad\" class=\"mw-redirect\" title=\"Ömer Lütfi Akad\"><PERSON><PERSON></a>, Turkish director and screenwriter (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96mer_L%C3%BCtfi_Akad\" class=\"mw-redirect\" title=\"<PERSON>mer Lütfi Akad\"><PERSON><PERSON></a>, Turkish director and screenwriter (b. 1916)", "links": [{"title": "<PERSON><PERSON>fi <PERSON>", "link": "https://wikipedia.org/wiki/%C3%96mer_L%C3%BCtfi_Akad"}]}, {"year": "2011", "text": "<PERSON>, English actor (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1925)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2011", "text": "<PERSON>, American poet and author (b. 1915)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Welsh director and producer (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh director and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh director and producer (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Japanese singer-songwriter (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ya"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1930)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian author (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_<PERSON>y"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Babe_Birrer\" title=\"Babe Birrer\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babe_Birrer\" title=\"Babe Birrer\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rer"}]}, {"year": "2013", "text": "<PERSON>, American pilot and academic (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English journalist, author, and activist (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English biochemist and academic, Nobel Prize laureate (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2013", "text": "<PERSON>, American author and poet (b. 1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_Zolotow"}]}, {"year": "2014", "text": "<PERSON>, English philosopher and academic (b. 1944)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and academic (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Irish-Australian bishop (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian bishop (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian bishop (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American businessman (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American theologian, author, and academic (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian, author, and academic (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Iranian footballer and manager (b. 1950)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian footballer and manager (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German-American actor, director, producer, and screenwriter (b. 1931)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor, director, producer, and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American actor, director, producer, and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Dutch singer-songwriter (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Dutch singer-songwriter (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Dutch singer-songwriter (b. 1946)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "2015", "text": "<PERSON>, American lawyer and politician (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian singer-songwriter and guitarist (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indonesian author, poet, and critic (b. 1953)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian author, poet, and critic (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Ko<PERSON>\"><PERSON><PERSON></a>, Indonesian author, poet, and critic (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American runner and diplomat (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and diplomat (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and diplomat (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mal_W<PERSON>field"}]}, {"year": "2017", "text": "<PERSON>, American cult leader and mass murderer (b. 1934)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader and mass murderer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cult leader and mass murderer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter and record producer (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and record producer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American singer-songwriter and record producer (b. 1938)", "links": [{"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Pete%22_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, Czech tennis player (b. 1968)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player (b. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jana_Novotn%C3%A1"}]}, {"year": "2017", "text": "<PERSON>, American singer and actress (b. 1931)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American singer and songwriter (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor and mixed martial artist (b. 1973)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and mixed martial artist (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and mixed martial artist (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, American mental health activist, First Lady of the United States (1977-1981), and of Georgia (1971-1975) (b. 1927)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mental health activist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (1977-1981), and <a href=\"https://wikipedia.org/wiki/First_ladies_of_Georgia_(U.S._state)\" title=\"First ladies of Georgia (U.S. state)\">of Georgia</a> (1971-1975) (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mental health activist, <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (1977-1981), and <a href=\"https://wikipedia.org/wiki/First_ladies_of_Georgia_(U.S._state)\" title=\"First ladies of Georgia (U.S. state)\">of Georgia</a> (1971-1975) (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}, {"title": "First ladies of Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/First_ladies_of_Georgia_(U.S._state)"}]}, {"year": "2023", "text": "<PERSON>, Scottish poet and publisher (b. 1935)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and publisher (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and publisher (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American sociologist and pastor (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and pastor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and pastor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}