{"date": "July 18", "url": "https://wikipedia.org/wiki/July_18", "data": {"Events": [{"year": "477 BC", "text": "Battle of the Cremera as part of the Roman-Etruscan Wars.  <PERSON><PERSON><PERSON> ambushes and defeats the Roman army.", "html": "477 BC - 477 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Cremera\" title=\"Battle of the Cremera\">Battle of the Cremera</a> as part of the <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#The_Fabian_war_with_Veii_in_483-476_BC\" title=\"Roman-Etruscan Wars\">Roman-Etruscan Wars</a>. <a href=\"https://wikipedia.org/wiki/Veii\" title=\"Veii\">V<PERSON><PERSON></a> ambushes and defeats the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman</a> army.", "no_year_html": "477 BC - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Cremera\" title=\"Battle of the Cremera\">Battle of the Cremera</a> as part of the <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#The_Fabian_war_with_Veii_in_483-476_BC\" title=\"Roman-Etruscan Wars\">Roman-Etruscan Wars</a>. <a href=\"https://wikipedia.org/wiki/Veii\" title=\"Veii\">Veii</a> ambushes and defeats the <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman</a> army.", "links": [{"title": "Battle of the Cremera", "link": "https://wikipedia.org/wiki/Battle_of_the_Cremera"}, {"title": "Roman-Etruscan Wars", "link": "https://wikipedia.org/wiki/Roman%E2%80%93Etruscan_Wars#The_Fabian_war_with_<PERSON><PERSON><PERSON>_in_483-476_BC"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veii"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}]}, {"year": "387 BC[2]", "text": "Roman-Gaulish Wars: Battle of the Allia: A Roman army is defeated by raiding Gauls, leading to the subsequent sacking of Rome.", "html": "387 BC[2] - <a href=\"https://wikipedia.org/wiki/387_BC\" title=\"387 BC\">387 BC</a> - <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman</a>-<a href=\"https://wikipedia.org/wiki/Gaulish_language\" class=\"mw-redirect\" title=\"Gaulish language\">Gaulish</a> Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Allia\" title=\"Battle of the Allia\">Battle of the Allia</a>: A Roman army is defeated by raiding <a href=\"https://wikipedia.org/wiki/Gauls\" title=\"Gauls\">Gauls</a>, leading to the subsequent sacking of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/387_BC\" title=\"387 BC\">387 BC</a> - <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Roman</a>-<a href=\"https://wikipedia.org/wiki/Gaulish_language\" class=\"mw-redirect\" title=\"Gaulish language\">Gaulish</a> Wars: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Allia\" title=\"Battle of the Allia\">Battle of the Allia</a>: A Roman army is defeated by raiding <a href=\"https://wikipedia.org/wiki/Gauls\" title=\"Gauls\">Gauls</a>, leading to the subsequent sacking of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>.", "links": [{"title": "387 BC", "link": "https://wikipedia.org/wiki/387_BC"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}, {"title": "Gaulish language", "link": "https://wikipedia.org/wiki/Gaulish_language"}, {"title": "Battle of the Allia", "link": "https://wikipedia.org/wiki/Battle_of_the_Allia"}, {"title": "Gauls", "link": "https://wikipedia.org/wiki/Gauls"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "362", "text": "Roman-Persian Wars: Emperor <PERSON> arrives at Antioch with a Roman expeditionary force (60,000 men) and stays there for nine months to launch a campaign against the Persian Empire.", "html": "362 - <a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Persian_Wars\" title=\"Roman-Persian Wars\">Roman-Persian Wars</a>: Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> with a Roman expeditionary force (60,000 men) and stays there for nine months to launch a campaign against the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman%E2%80%93Persian_Wars\" title=\"Roman-Persian Wars\">Roman-Persian Wars</a>: Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_(emperor)\" title=\"<PERSON> (emperor)\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a> with a Roman expeditionary force (60,000 men) and stays there for nine months to launch a campaign against the <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian Empire</a>.", "links": [{"title": "Roman-Persian Wars", "link": "https://wikipedia.org/wiki/Roman%E2%80%93Persian_Wars"}, {"title": "<PERSON> (emperor)", "link": "https://wikipedia.org/wiki/<PERSON>_(emperor)"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}]}, {"year": "452", "text": "Sack of Aquileia: After an earlier defeat on the Catalaunian Plains, <PERSON><PERSON><PERSON> lays siege to the metropolis of Aquileia and eventually destroys it.", "html": "452 - <a href=\"https://wikipedia.org/wiki/Sack_of_Aquileia\" title=\"Sack of Aquileia\">Sack of Aquileia</a>: After an earlier <a href=\"https://wikipedia.org/wiki/Battle_of_the_Catalaunian_Plains\" title=\"Battle of the Catalaunian Plains\">defeat on the Catalaunian Plains</a>, <a href=\"https://wikipedia.org/wiki/Attila\" title=\"Attila\">Attila</a> lays siege to the metropolis of <a href=\"https://wikipedia.org/wiki/Aquileia\" title=\"Aquileia\">Aquileia</a> and eventually destroys it.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sack_of_Aquileia\" title=\"Sack of Aquileia\">Sack of Aquileia</a>: After an earlier <a href=\"https://wikipedia.org/wiki/Battle_of_the_Catalaunian_Plains\" title=\"Battle of the Catalaunian Plains\">defeat on the Catalaunian Plains</a>, <a href=\"https://wikipedia.org/wiki/Attila\" title=\"Attila\">Attil<PERSON></a> lays siege to the metropolis of <a href=\"https://wikipedia.org/wiki/Aquileia\" title=\"Aquileia\">Aquileia</a> and eventually destroys it.", "links": [{"title": "Sack of Aquileia", "link": "https://wikipedia.org/wiki/Sack_of_Aquileia"}, {"title": "Battle of the Catalaunian Plains", "link": "https://wikipedia.org/wiki/Battle_of_the_Catalaunian_Plains"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/At<PERSON>a"}, {"title": "A<PERSON><PERSON>ia", "link": "https://wikipedia.org/wiki/Aquileia"}]}, {"year": "645", "text": "Chinese forces under general <PERSON> besiege the strategic fortress city of Anshi (Liaoning) during the Goguryeo-Tang War.", "html": "645 - Chinese forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Shiji\"><PERSON></a> besiege the strategic fortress city of <a href=\"https://wikipedia.org/wiki/Anshan\" title=\"Anshan\"><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Liaoning\" title=\"Liaoning\">Liaoning</a>) during the <a href=\"https://wikipedia.org/wiki/Goguryeo%E2%80%93Tang_War\" title=\"Goguryeo-Tang War\">Goguryeo-Tang War</a>.", "no_year_html": "Chinese forces under general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> besiege the strategic fortress city of <a href=\"https://wikipedia.org/wiki/Anshan\" title=\"Anshan\"><PERSON><PERSON></a> (<a href=\"https://wikipedia.org/wiki/Liaoning\" title=\"Liaoning\">Liaoning</a>) during the <a href=\"https://wikipedia.org/wiki/Goguryeo%E2%80%93Tang_War\" title=\"Goguryeo-Tang War\">Goguryeo-Tang War</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_<PERSON>ji"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Liaoning", "link": "https://wikipedia.org/wiki/Liaoning"}, {"title": "Goguryeo-Tang War", "link": "https://wikipedia.org/wiki/Goguryeo%E2%80%93Tang_War"}]}, {"year": "1195", "text": "Battle of Alarcos: Almohad forces defeat the Castilian army of Alfonso VIII and force its retreat to Toledo.", "html": "1195 - <a href=\"https://wikipedia.org/wiki/Battle_of_Alarcos\" title=\"Battle of Alarcos\">Battle of Alarcos</a>: <a href=\"https://wikipedia.org/wiki/Almohad_Caliphate\" title=\"Almohad Caliphate\">Almohad</a> forces defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> army of <a href=\"https://wikipedia.org/wiki/Alfonso_VIII_of_Castile\" title=\"Alfonso VIII of Castile\"><PERSON></a> and force its retreat to <a href=\"https://wikipedia.org/wiki/Toledo,_Spain\" title=\"Toledo, Spain\">Toledo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Alarcos\" title=\"Battle of Alarcos\">Battle of Alarcos</a>: <a href=\"https://wikipedia.org/wiki/Almohad_Caliphate\" title=\"Almohad Caliphate\">Almohad</a> forces defeat the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castilian</a> army of <a href=\"https://wikipedia.org/wiki/Alfonso_VIII_of_Castile\" title=\"Alfonso VIII of Castile\"><PERSON></a> and force its retreat to <a href=\"https://wikipedia.org/wiki/Toledo,_Spain\" title=\"Toledo, Spain\">Toledo</a>.", "links": [{"title": "Battle of Alarcos", "link": "https://wikipedia.org/wiki/Battle_of_Alarcos"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al<PERSON>had_Caliphate"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Alfonso_VIII_of_Castile"}, {"title": "Toledo, Spain", "link": "https://wikipedia.org/wiki/Toledo,_Spain"}]}, {"year": "1290", "text": "King <PERSON> of England issues the Edict of Expulsion, banishing all Jews (numbering about 16,000) from England.", "html": "1290 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Expulsion\" title=\"Edict of Expulsion\">Edict of Expulsion</a>, banishing all Jews (numbering about 16,000) from England.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Expulsion\" title=\"Edict of Expulsion\">Edict of Expulsion</a>, banishing all Jews (numbering about 16,000) from England.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England"}, {"title": "Edict of Expulsion", "link": "https://wikipedia.org/wiki/Edict_of_Expulsion"}]}, {"year": "1334", "text": "The bishop of Florence blesses the first foundation stone for the new campanile (bell tower) of the Florence Cathedral, designed by the artist <PERSON><PERSON><PERSON>.", "html": "1334 - The bishop of <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> blesses the first foundation stone for the new <i><a href=\"https://wikipedia.org/wiki/Bell_tower\" title=\"Bell tower\">campanile</a></i> (bell tower) of the <a href=\"https://wikipedia.org/wiki/Florence_Cathedral\" title=\"Florence Cathedral\">Florence Cathedral</a>, designed by the artist <a href=\"https://wikipedia.org/wiki/Giotto\" title=\"<PERSON>iot<PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>.", "no_year_html": "The bishop of <a href=\"https://wikipedia.org/wiki/Florence\" title=\"Florence\">Florence</a> blesses the first foundation stone for the new <i><a href=\"https://wikipedia.org/wiki/Bell_tower\" title=\"Bell tower\">campanile</a></i> (bell tower) of the <a href=\"https://wikipedia.org/wiki/Florence_Cathedral\" title=\"Florence Cathedral\">Florence Cathedral</a>, designed by the artist <a href=\"https://wikipedia.org/wiki/Giotto\" title=\"<PERSON>iot<PERSON>\"><PERSON><PERSON><PERSON> di <PERSON></a>.", "links": [{"title": "Florence", "link": "https://wikipedia.org/wiki/Florence"}, {"title": "Bell tower", "link": "https://wikipedia.org/wiki/Bell_tower"}, {"title": "Florence Cathedral", "link": "https://wikipedia.org/wiki/Florence_Cathedral"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1389", "text": "France and England agree to the Truce of Leulinghem, inaugurating a 13-year peace, the longest period of sustained peace during the Hundred Years' War.", "html": "1389 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> agree to the <a href=\"https://wikipedia.org/wiki/Truce_of_Leulinghem\" title=\"Truce of Leulinghem\">Truce of Leulinghem</a>, inaugurating a 13-year peace, the longest period of sustained peace during the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a> agree to the <a href=\"https://wikipedia.org/wiki/Truce_of_Leulinghem\" title=\"Truce of Leulinghem\">Truce of Leulinghem</a>, inaugurating a 13-year peace, the longest period of sustained peace during the <a href=\"https://wikipedia.org/wiki/Hundred_Years%27_War\" title=\"Hundred Years' War\">Hundred Years' War</a>.", "links": [{"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "<PERSON><PERSON><PERSON> of Leulinghem", "link": "https://wikipedia.org/wiki/Truce_of_Le<PERSON>em"}, {"title": "Hundred Years' War", "link": "https://wikipedia.org/wiki/Hundred_Years%27_War"}]}, {"year": "1507", "text": "In Brussels, Prince <PERSON> is crowned Duke of Burgundy and Count of Flanders, a year after inheriting the title.", "html": "1507 - In <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor#Burgundy_and_the_Low_Countries\" title=\"<PERSON>, Holy Roman Emperor\">Prince <PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/Burgundian_Netherlands#Rulers\" title=\"Burgundian Netherlands\">Duke of Burgundy and Count of Flanders</a>, a year after inheriting the title.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor#Burgundy_and_the_Low_Countries\" title=\"<PERSON>, Holy Roman Emperor\">Prince <PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/Burgundian_Netherlands#Rulers\" title=\"Burgundian Netherlands\">Duke of Burgundy and Count of Flanders</a>, a year after inheriting the title.", "links": [{"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor#Burgundy_and_the_Low_Countries"}, {"title": "Burgundian Netherlands", "link": "https://wikipedia.org/wiki/Burgundian_Netherlands#Rulers"}]}, {"year": "1555", "text": "The College of Arms is reincorporated by Royal charter signed by Queen <PERSON> of England and King <PERSON> of Spain.", "html": "1555 - The <a href=\"https://wikipedia.org/wiki/College_of_Arms\" title=\"College of Arms\">College of Arms</a> is reincorporated by <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> signed by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Mary I of England\"><PERSON> of England</a> and King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/College_of_Arms\" title=\"College of Arms\">College of Arms</a> is reincorporated by <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> signed by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_I_of_England\" title=\"Mary I of England\"><PERSON> of England</a> and King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain\" title=\"<PERSON> of Spain\"><PERSON> of Spain</a>.", "links": [{"title": "College of Arms", "link": "https://wikipedia.org/wiki/College_of_Arms"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Spain"}]}, {"year": "1723", "text": "<PERSON> leads the first performance of his cantata <PERSON><PERSON><PERSON><PERSON> mich, <PERSON><PERSON>, und erfahre mein <PERSON>, BWV 136, in Leipzig on the eighth Sunday after Trinity.", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_mich,_<PERSON><PERSON>,_und_er<PERSON>hre_mein_<PERSON><PERSON>,_BWV_136\" title=\"<PERSON><PERSON><PERSON><PERSON> mich, Got<PERSON>, und erfahre mein Her<PERSON>, BWV 136\"><i><PERSON><PERSON><PERSON>che mich, Got<PERSON>, und erfahre mein <PERSON></i>, BWV 136</a>, in Leipzig on the eighth Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads the first performance of his cantata <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_mich,_<PERSON><PERSON>,_und_er<PERSON>hre_mein_<PERSON>,_BWV_136\" title=\"<PERSON><PERSON><PERSON><PERSON> mich, Got<PERSON>, und erfahre mein Her<PERSON>, BWV 136\"><i><PERSON><PERSON><PERSON><PERSON> mich, Got<PERSON>, und erfahre mein <PERSON></i>, BWV 136</a>, in Leipzig on the eighth Sunday after <a href=\"https://wikipedia.org/wiki/Trinity_Sunday\" title=\"Trinity Sunday\">Trinity</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> mich, <PERSON><PERSON>, und erfahre mein <PERSON>z, BWV 136", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ch,_<PERSON>,_und_er<PERSON>hre_mein_<PERSON>,_<PERSON>_136"}, {"title": "Trinity Sunday", "link": "https://wikipedia.org/wiki/Trinity_Sunday"}]}, {"year": "1806", "text": "A gunpowder magazine explosion in Birgu, Malta, kills around 200 people.", "html": "1806 - A <a href=\"https://wikipedia.org/wiki/1806_Birgu_polverista_explosion\" title=\"1806 Birgu polverista explosion\">gunpowder magazine explosion</a> in <a href=\"https://wikipedia.org/wiki/Birgu\" title=\"Birgu\">Birgu</a>, <a href=\"https://wikipedia.org/wiki/Malta_Protectorate\" title=\"Malta Protectorate\">Malta</a>, kills around 200 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1806_Birgu_polverista_explosion\" title=\"1806 Birgu polverista explosion\">gunpowder magazine explosion</a> in <a href=\"https://wikipedia.org/wiki/Birgu\" title=\"Birgu\">Birgu</a>, <a href=\"https://wikipedia.org/wiki/Malta_Protectorate\" title=\"Malta Protectorate\">Malta</a>, kills around 200 people.", "links": [{"title": "1806 Birgu polver<PERSON> explosion", "link": "https://wikipedia.org/wiki/1806_Birgu_polverista_explosion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birgu"}, {"title": "Malta Protectorate", "link": "https://wikipedia.org/wiki/Malta_Protectorate"}]}, {"year": "1812", "text": "The Treaties of Orebro end both the Anglo-Russian and Anglo-Swedish Wars.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Orebro\" class=\"mw-redirect\" title=\"Treaty of Orebro\">Treaties of Orebro</a> end both the <a href=\"https://wikipedia.org/wiki/Anglo-Russian_War_(1807%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Russian War (1807-12)\">Anglo-Russian</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Swedish War (1810-12)\">Anglo-Swedish Wars</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Orebro\" class=\"mw-redirect\" title=\"Treaty of Orebro\">Treaties of Orebro</a> end both the <a href=\"https://wikipedia.org/wiki/Anglo-Russian_War_(1807%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Russian War (1807-12)\">Anglo-Russian</a> and <a href=\"https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)\" class=\"mw-redirect\" title=\"Anglo-Swedish War (1810-12)\">Anglo-Swedish Wars</a>.", "links": [{"title": "Treaty of Orebro", "link": "https://wikipedia.org/wiki/Treaty_of_Orebro"}, {"title": "Anglo-Russian War (1807-12)", "link": "https://wikipedia.org/wiki/Anglo-Russian_War_(1807%E2%80%9312)"}, {"title": "Anglo-Swedish War (1810-12)", "link": "https://wikipedia.org/wiki/Anglo-Swedish_War_(1810%E2%80%9312)"}]}, {"year": "1841", "text": "Coronation of Emperor <PERSON> of Brazil.", "html": "1841 - Coronation of Emperor <a href=\"https://wikipedia.org/wiki/Pedro_II_of_Brazil\" title=\"Pedro II of Brazil\">Pedro II of Brazil</a>.", "no_year_html": "Coronation of Emperor <a href=\"https://wikipedia.org/wiki/Pedro_II_of_Brazil\" title=\"Pedro II of Brazil\">Pedro II of Brazil</a>.", "links": [{"title": "Pedro II of Brazil", "link": "https://wikipedia.org/wiki/Pedro_II_of_Brazil"}]}, {"year": "1857", "text": "<PERSON>, French governor of Senegal, arrives to relieve French forces at Kayes, effectively ending <PERSON> Ha<PERSON>j <PERSON>'s war against the French.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/Louis<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Governor_of_Senegal\" class=\"mw-redirect\" title=\"Governor of Senegal\">governor of Senegal</a>, arrives to relieve French forces at <a href=\"https://wikipedia.org/wiki/Kayes\" title=\"Kayes\"><PERSON><PERSON></a>, effectively ending El Hajj <a href=\"https://wikipedia.org/wiki/El_Hadj_<PERSON>ar_Tall\" class=\"mw-redirect\" title=\"El Hadj <PERSON> Tall\"><PERSON><PERSON></a>'s war against the French.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Governor_of_Senegal\" class=\"mw-redirect\" title=\"Governor of Senegal\">governor of Senegal</a>, arrives to relieve French forces at <a href=\"https://wikipedia.org/wiki/Kayes\" title=\"Kayes\"><PERSON><PERSON></a>, effectively ending El Hajj <a href=\"https://wikipedia.org/wiki/El_Hadj_<PERSON>ar_Tall\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON><PERSON></a>'s war against the French.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}, {"title": "Governor of Senegal", "link": "https://wikipedia.org/wiki/Governor_of_Senegal"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kayes"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "First ascent of Dent Blanche, one of the highest summits in the Alps.", "html": "1862 - First ascent of <a href=\"https://wikipedia.org/wiki/Dent_Blanche\" title=\"Dent Blanche\">Dent Blanche</a>, one of the highest summits in the Alps.", "no_year_html": "First ascent of <a href=\"https://wikipedia.org/wiki/Dent_Blanche\" title=\"Dent Blanche\">Dent Blanche</a>, one of the highest summits in the Alps.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dent_Blanche"}]}, {"year": "1863", "text": "American Civil War: Second Battle of Fort Wagner: One of the first formal African American military units, the 54th Massachusetts Volunteer Infantry, supported by several white regiments, attempts an unsuccessful assault on Confederate-held Battery Wagner.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Fort_Wagner\" title=\"Second Battle of Fort Wagner\">Second Battle of Fort Wagner</a>: One of the first formal <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> military units, the <a href=\"https://wikipedia.org/wiki/54th_Regiment_Massachusetts_Volunteer_Infantry\" class=\"mw-redirect\" title=\"54th Regiment Massachusetts Volunteer Infantry\">54th Massachusetts Volunteer Infantry</a>, supported by several white regiments, attempts an unsuccessful assault on <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a>-held <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Battery Wagner</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Fort_Wagner\" title=\"Second Battle of Fort Wagner\">Second Battle of Fort Wagner</a>: One of the first formal <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> military units, the <a href=\"https://wikipedia.org/wiki/54th_Regiment_Massachusetts_Volunteer_Infantry\" class=\"mw-redirect\" title=\"54th Regiment Massachusetts Volunteer Infantry\">54th Massachusetts Volunteer Infantry</a>, supported by several white regiments, attempts an unsuccessful assault on <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a>-held <a href=\"https://wikipedia.org/wiki/Fort_Wagner\" title=\"Fort Wagner\">Battery Wagner</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Second Battle of Fort Wagner", "link": "https://wikipedia.org/wiki/Second_Battle_of_Fort_Wagner"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "54th Regiment Massachusetts Volunteer Infantry", "link": "https://wikipedia.org/wiki/54th_Regiment_Massachusetts_Volunteer_Infantry"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Fort Wagner", "link": "https://wikipedia.org/wiki/Fort_Wagner"}]}, {"year": "1870", "text": "The First Vatican Council decrees the dogma of papal infallibility.", "html": "1870 - The <a href=\"https://wikipedia.org/wiki/First_Vatican_Council\" title=\"First Vatican Council\">First Vatican Council</a> decrees the dogma of <a href=\"https://wikipedia.org/wiki/Papal_infallibility\" title=\"Papal infallibility\">papal infallibility</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Vatican_Council\" title=\"First Vatican Council\">First Vatican Council</a> decrees the dogma of <a href=\"https://wikipedia.org/wiki/Papal_infallibility\" title=\"Papal infallibility\">papal infallibility</a>.", "links": [{"title": "First Vatican Council", "link": "https://wikipedia.org/wiki/First_Vatican_Council"}, {"title": "Papal infallibility", "link": "https://wikipedia.org/wiki/Papal_infallibility"}]}, {"year": "1872", "text": "The Ballot Act 1872 in the United Kingdom introduced the requirement that parliamentary and local government elections be held by secret ballot.", "html": "1872 - The <a href=\"https://wikipedia.org/wiki/Ballot_Act_1872\" title=\"Ballot Act 1872\">Ballot Act 1872</a> in the United Kingdom introduced the requirement that parliamentary and local government elections be held by secret ballot.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ballot_Act_1872\" title=\"Ballot Act 1872\">Ballot Act 1872</a> in the United Kingdom introduced the requirement that parliamentary and local government elections be held by secret ballot.", "links": [{"title": "Ballot Act 1872", "link": "https://wikipedia.org/wiki/Ballot_Act_1872"}]}, {"year": "1914", "text": "The U.S. Congress forms the Aviation Section, U.S. Signal Corps, giving official status to aircraft within the U.S. Army for the first time.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> forms the <a href=\"https://wikipedia.org/wiki/Aviation_Section,_U.S._Signal_Corps\" title=\"Aviation Section, U.S. Signal Corps\">Aviation Section, U.S. Signal Corps</a>, giving official status to aircraft within the U.S. Army for the first time.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> forms the <a href=\"https://wikipedia.org/wiki/Aviation_Section,_U.S._Signal_Corps\" title=\"Aviation Section, U.S. Signal Corps\">Aviation Section, U.S. Signal Corps</a>, giving official status to aircraft within the U.S. Army for the first time.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Aviation Section, U.S. Signal Corps", "link": "https://wikipedia.org/wiki/Aviation_Section,_U.S._Signal_Corps"}]}, {"year": "1925", "text": "<PERSON> publishes Mein Kampf.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/Mei<PERSON>_<PERSON>mpf\" title=\"Mei<PERSON>mp<PERSON>\"><PERSON><PERSON></a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Adolf Hitler\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/Mein_<PERSON>mpf\" title=\"Mei<PERSON>mp<PERSON>\"><PERSON><PERSON></a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mpf"}]}, {"year": "1942", "text": "World War II: During the Beisfjord massacre in Norway, 15 Norwegian paramilitary guards help members of the SS to kill 288 political prisoners from Yugoslavia.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Beisfjord_massacre\" title=\"Beisfjord massacre\">Beisfjord massacre</a> in Norway, 15 Norwegian paramilitary guards help members of the SS to kill 288 political prisoners from Yugoslavia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: During the <a href=\"https://wikipedia.org/wiki/Beisfjord_massacre\" title=\"Beisfjord massacre\">Beisfjord massacre</a> in Norway, 15 Norwegian paramilitary guards help members of the SS to kill 288 political prisoners from Yugoslavia.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Beisfjord massacre", "link": "https://wikipedia.org/wiki/Beisfjord_massacre"}]}, {"year": "1942", "text": "The Germans test fly the Messerschmitt Me 262 using its jet engines for the first time.", "html": "1942 - The Germans test fly the <a href=\"https://wikipedia.org/wiki/Messerschmitt_Me_262\" title=\"Messerschmitt Me 262\">Messerschmitt Me 262</a> using its <a href=\"https://wikipedia.org/wiki/Junkers_Jumo_004\" title=\"Junkers Jumo 004\">jet engines</a> for the first time.", "no_year_html": "The Germans test fly the <a href=\"https://wikipedia.org/wiki/Messerschmitt_Me_262\" title=\"Messerschmitt Me 262\">Messerschmitt Me 262</a> using its <a href=\"https://wikipedia.org/wiki/Junkers_Jumo_004\" title=\"Junkers Jumo 004\">jet engines</a> for the first time.", "links": [{"title": "Messerschmitt Me 262", "link": "https://wikipedia.org/wiki/Messerschmitt_Me_262"}, {"title": "Junkers Jumo 004", "link": "https://wikipedia.org/wiki/Junkers_Jumo_004"}]}, {"year": "1944", "text": "World War II: <PERSON><PERSON><PERSON> resigns as Prime Minister of Japan because of numerous setbacks in the war effort.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Hideki_T%C5%8Dj%C5%8D\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>jō\"><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> because of numerous setbacks in the war effort.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Hideki_T%C5%8Dj%C5%8D\" class=\"mw-redirect\" title=\"Hi<PERSON><PERSON> Tōjō\"><PERSON><PERSON><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> because of numerous setbacks in the war effort.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hideki_T%C5%8Dj%C5%8D"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1966", "text": "Human spaceflight: Gemini 10 is launched from Cape Kennedy on a 70-hour mission that includes docking with an orbiting Agena target vehicle.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">Human spaceflight</a>: <a href=\"https://wikipedia.org/wiki/Gemini_10\" title=\"Gemini 10\">Gemini 10</a> is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Kennedy</a> on a 70-hour mission that includes docking with an orbiting <a href=\"https://wikipedia.org/wiki/Agena_target_vehicle\" title=\"Agena target vehicle\">Agena target vehicle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">Human spaceflight</a>: <a href=\"https://wikipedia.org/wiki/Gemini_10\" title=\"Gemini 10\">Gemini 10</a> is launched from <a href=\"https://wikipedia.org/wiki/Cape_Canaveral\" title=\"Cape Canaveral\">Cape Kennedy</a> on a 70-hour mission that includes docking with an orbiting <a href=\"https://wikipedia.org/wiki/Agena_target_vehicle\" title=\"Agena target vehicle\">Agena target vehicle</a>.", "links": [{"title": "Human spaceflight", "link": "https://wikipedia.org/wiki/Human_spaceflight"}, {"title": "Gemini 10", "link": "https://wikipedia.org/wiki/Gemini_10"}, {"title": "Cape Canaveral", "link": "https://wikipedia.org/wiki/Cape_Canaveral"}, {"title": "Agena target vehicle", "link": "https://wikipedia.org/wiki/Agena_target_vehicle"}]}, {"year": "1966", "text": "A racially charged incident in a bar sparks the six-day Hough riots in Cleveland, Ohio; 1,700 Ohio National Guard troops intervene to restore order.", "html": "1966 - A racially charged incident in a bar sparks the six-day <a href=\"https://wikipedia.org/wiki/Hough_riots\" title=\"Hough riots\">Hough riots</a> in <a href=\"https://wikipedia.org/wiki/Cleveland\" title=\"Cleveland\">Cleveland, Ohio</a>; 1,700 <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> troops intervene to restore order.", "no_year_html": "A racially charged incident in a bar sparks the six-day <a href=\"https://wikipedia.org/wiki/Hough_riots\" title=\"Hough riots\">Hough riots</a> in <a href=\"https://wikipedia.org/wiki/Cleveland\" title=\"Cleveland\">Cleveland, Ohio</a>; 1,700 <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a> troops intervene to restore order.", "links": [{"title": "Hough riots", "link": "https://wikipedia.org/wiki/Hough_riots"}, {"title": "Cleveland", "link": "https://wikipedia.org/wiki/Cleveland"}, {"title": "Ohio National Guard", "link": "https://wikipedia.org/wiki/Ohio_National_Guard"}]}, {"year": "1968", "text": "Intel is founded in Mountain View, California.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a> is founded in <a href=\"https://wikipedia.org/wiki/Mountain_View,_California\" title=\"Mountain View, California\">Mountain View, California</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel</a> is founded in <a href=\"https://wikipedia.org/wiki/Mountain_View,_California\" title=\"Mountain View, California\">Mountain View, California</a>.", "links": [{"title": "Intel", "link": "https://wikipedia.org/wiki/Intel"}, {"title": "Mountain View, California", "link": "https://wikipedia.org/wiki/Mountain_View,_California"}]}, {"year": "1970", "text": "An Antonov An-22 of the Soviet Air Forces crashes into the Atlantic Ocean, killing all 23 aboard.", "html": "1970 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-22\" title=\"Antonov An-22\">Antonov An-22</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Forces</a> <a href=\"https://wikipedia.org/wiki/1970_Atlantic_Ocean_Antonov_An-22_crash\" title=\"1970 Atlantic Ocean Antonov An-22 crash\">crashes</a> into the Atlantic Ocean, killing all 23 aboard.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-22\" title=\"Antonov An-22\">Antonov An-22</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Air_Forces\" title=\"Soviet Air Forces\">Soviet Air Forces</a> <a href=\"https://wikipedia.org/wiki/1970_Atlantic_Ocean_Antonov_An-22_crash\" title=\"1970 Atlantic Ocean Antonov An-22 crash\">crashes</a> into the Atlantic Ocean, killing all 23 aboard.", "links": [{"title": "Antonov An-22", "link": "https://wikipedia.org/wiki/Antonov_An-22"}, {"title": "Soviet Air Forces", "link": "https://wikipedia.org/wiki/Soviet_Air_Forces"}, {"title": "1970 Atlantic Ocean Antonov An-22 crash", "link": "https://wikipedia.org/wiki/1970_Atlantic_Ocean_Antonov_An-22_crash"}]}, {"year": "1976", "text": "<PERSON> becomes the first person in Olympic Games history to score a perfect 10 in gymnastics at the 1976 Summer Olympics.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83neci\" title=\"<PERSON>\"><PERSON></a> becomes the first person in <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a> history to score a perfect 10 in <a href=\"https://wikipedia.org/wiki/Gymnastics\" title=\"Gymnastics\">gymnastics</a> at the <a href=\"https://wikipedia.org/wiki/1976_Summer_Olympics\" title=\"1976 Summer Olympics\">1976 Summer Olympics</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%83neci\" title=\"<PERSON>\"><PERSON></a> becomes the first person in <a href=\"https://wikipedia.org/wiki/Olympic_Games\" title=\"Olympic Games\">Olympic Games</a> history to score a perfect 10 in <a href=\"https://wikipedia.org/wiki/Gymnastics\" title=\"Gymnastics\">gymnastics</a> at the <a href=\"https://wikipedia.org/wiki/1976_Summer_Olympics\" title=\"1976 Summer Olympics\">1976 Summer Olympics</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nadia_Com%C4%83neci"}, {"title": "Olympic Games", "link": "https://wikipedia.org/wiki/Olympic_Games"}, {"title": "Gymnastics", "link": "https://wikipedia.org/wiki/Gymnastics"}, {"title": "1976 Summer Olympics", "link": "https://wikipedia.org/wiki/1976_Summer_Olympics"}]}, {"year": "1979", "text": "A landslide occurs on the Iliwerung volcano in Indonesia, triggering a tsunami that kills over 530 and leaves 700 missing.", "html": "1979 - A landslide occurs on the <a href=\"https://wikipedia.org/wiki/Iliwerung\" title=\"Iliwerung\">Iliwerung</a> volcano in Indonesia, <a href=\"https://wikipedia.org/wiki/1979_Lembata_tsunami\" title=\"1979 Lembata tsunami\">triggering a tsunami</a> that kills over 530 and leaves 700 missing.", "no_year_html": "A landslide occurs on the <a href=\"https://wikipedia.org/wiki/Iliwerung\" title=\"Iliwerung\">Iliwerung</a> volcano in Indonesia, <a href=\"https://wikipedia.org/wiki/1979_Lembata_tsunami\" title=\"1979 Lembata tsunami\">triggering a tsunami</a> that kills over 530 and leaves 700 missing.", "links": [{"title": "Iliwerung", "link": "https://wikipedia.org/wiki/Iliwerung"}, {"title": "1979 Lembata tsunami", "link": "https://wikipedia.org/wiki/1979_Lembata_tsunami"}]}, {"year": "1981", "text": "A Canadair CL-44 and Sukhoi Su-15 collide in mid-air near Yerevan, Armenia, killing four.", "html": "1981 - A <a href=\"https://wikipedia.org/wiki/Canadair_CL-44\" title=\"Canadair CL-44\">Canadair CL-44</a> and <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-15\" title=\"Sukhoi Su-15\">Sukhoi Su-15</a> <a href=\"https://wikipedia.org/wiki/1981_Armenia_mid-air_collision\" title=\"1981 Armenia mid-air collision\">collide</a> in mid-air near <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, killing four.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Canadair_CL-44\" title=\"Canadair CL-44\">Canadair CL-44</a> and <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-15\" title=\"Sukhoi Su-15\">Sukhoi Su-15</a> <a href=\"https://wikipedia.org/wiki/1981_Armenia_mid-air_collision\" title=\"1981 Armenia mid-air collision\">collide</a> in mid-air near <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a>, <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenia</a>, killing four.", "links": [{"title": "Canadair CL-44", "link": "https://wikipedia.org/wiki/Canadair_CL-44"}, {"title": "Sukhoi Su-15", "link": "https://wikipedia.org/wiki/Sukhoi_Su-15"}, {"title": "1981 Armenia mid-air collision", "link": "https://wikipedia.org/wiki/1981_Armenia_mid-air_collision"}, {"title": "Yerevan", "link": "https://wikipedia.org/wiki/Yerevan"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}]}, {"year": "1982", "text": "Two hundred sixty-eight Guatemalan campesinos (\"peasants\" or \"country people\") are slain in the Plan de Sánchez massacre.", "html": "1982 - Two hundred sixty-eight <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemalan</a> <i>campesinos</i> (\"<a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasants</a>\" or \"country people\") are slain in the <a href=\"https://wikipedia.org/wiki/Plan_de_S%C3%A1nchez_massacre\" title=\"Plan de Sánchez massacre\">Plan de Sánchez massacre</a>.", "no_year_html": "Two hundred sixty-eight <a href=\"https://wikipedia.org/wiki/Guatemala\" title=\"Guatemala\">Guatemalan</a> <i>campesinos</i> (\"<a href=\"https://wikipedia.org/wiki/Peasant\" title=\"Peasant\">peasants</a>\" or \"country people\") are slain in the <a href=\"https://wikipedia.org/wiki/Plan_de_S%C3%A1nchez_massacre\" title=\"Plan de Sánchez massacre\">Plan de Sánchez massacre</a>.", "links": [{"title": "Guatemala", "link": "https://wikipedia.org/wiki/Guatemala"}, {"title": "Peasant", "link": "https://wikipedia.org/wiki/Peasant"}, {"title": "Plan de Sánchez massacre", "link": "https://wikipedia.org/wiki/Plan_de_S%C3%A1nchez_massacre"}]}, {"year": "1984", "text": "<PERSON>'s massacre in San Ysidro, California: <PERSON> kills 21 people and injures 19 others before being shot dead by police.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/San_Ysidro_McDonald%27s_massacre\" title=\"San Ysidro McDonald's massacre\"><PERSON>'s massacre</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_Ysidro,_San_Diego\" title=\"San Ysidro, San Diego\">San Ysidro, California</a>: <PERSON> kills 21 people and injures 19 others before being shot dead by police.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/San_Ysidro_McDonald%27s_massacre\" title=\"San Ysidro McDonald's massacre\"><PERSON>'s massacre</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>_Y<PERSON>,_San_Diego\" title=\"San Ysidro, San Diego\">San Ysidro, California</a>: <PERSON> kills 21 people and injures 19 others before being shot dead by police.", "links": [{"title": "San Ysidro McDonald's massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27s_massacre"}, {"title": "San Ysidro, San Diego", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_San_Diego"}]}, {"year": "1992", "text": "A picture of <PERSON> was taken, which became the first ever photo posted to the World Wide Web.", "html": "1992 - A picture of <a href=\"https://wikipedia.org/wiki/Les_Horribles_Cernettes\" title=\"Les Horribles Cernettes\">Les Horribles Cernettes</a> was taken, which became the first ever photo posted to the <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a>.", "no_year_html": "A picture of <a href=\"https://wikipedia.org/wiki/Les_Horribles_Cernettes\" title=\"Les Horribles Cernettes\">Les Horribles Cernettes</a> was taken, which became the first ever photo posted to the <a href=\"https://wikipedia.org/wiki/World_Wide_Web\" title=\"World Wide Web\">World Wide Web</a>.", "links": [{"title": "Les Horribles Cernettes", "link": "https://wikipedia.org/wiki/Les_Horrible<PERSON>_Cernettes"}, {"title": "World Wide Web", "link": "https://wikipedia.org/wiki/World_Wide_Web"}]}, {"year": "1994", "text": "The bombing of the Asociación Mutual Israelita Argentina (Argentine Jewish Community Center) in Buenos Aires kills 85 people (mostly Jewish) and injures 300.", "html": "1994 - The <a href=\"https://wikipedia.org/wiki/AMIA_bombing\" title=\"AMIA bombing\">bombing</a> of the <a href=\"https://wikipedia.org/wiki/Asociaci%C3%B3n_Mutual_Israelita_Argentina\" title=\"Asociación Mutual Israelita Argentina\">Asociación Mutual Israelita Argentina</a> (Argentine Jewish Community Center) in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a> kills 85 people (mostly Jewish) and injures 300.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/AMIA_bombing\" title=\"AMIA bombing\">bombing</a> of the <a href=\"https://wikipedia.org/wiki/Asociaci%C3%B3n_Mutual_Israelita_Argentina\" title=\"Asociación Mutual Israelita Argentina\">Asociación Mutual Israelita Argentina</a> (Argentine Jewish Community Center) in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a> kills 85 people (mostly Jewish) and injures 300.", "links": [{"title": "AMIA bombing", "link": "https://wikipedia.org/wiki/AMIA_bombing"}, {"title": "Asociación Mutual Israelita Argentina", "link": "https://wikipedia.org/wiki/Asociaci%C3%B3n_Mutual_Israelita_Argentina"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "1994", "text": "Rwandan genocide: The Rwandan Patriotic Front takes control of Gisenyi and north western Rwanda, forcing the interim government into Zaire and ending the genocide.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a>: The <a href=\"https://wikipedia.org/wiki/Rwandan_Patriotic_Front\" title=\"Rwandan Patriotic Front\">Rwandan Patriotic Front</a> takes control of <a href=\"https://wikipedia.org/wiki/Gisenyi\" title=\"Gisenyi\">Gisenyi</a> and north western <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a>, forcing the interim government into <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a> and ending the genocide.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rwandan_genocide\" title=\"Rwandan genocide\">Rwandan genocide</a>: The <a href=\"https://wikipedia.org/wiki/Rwandan_Patriotic_Front\" title=\"Rwandan Patriotic Front\">Rwandan Patriotic Front</a> takes control of <a href=\"https://wikipedia.org/wiki/Gisenyi\" title=\"Gisenyi\">Gisenyi</a> and north western <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwanda</a>, forcing the interim government into <a href=\"https://wikipedia.org/wiki/Zaire\" title=\"Zaire\">Zaire</a> and ending the genocide.", "links": [{"title": "Rwandan genocide", "link": "https://wikipedia.org/wiki/Rwandan_genocide"}, {"title": "Rwandan Patriotic Front", "link": "https://wikipedia.org/wiki/Rwandan_Patriotic_Front"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gisenyi"}, {"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}, {"title": "Zaire", "link": "https://wikipedia.org/wiki/Zaire"}]}, {"year": "1995", "text": "On the Caribbean island of Montserrat, the Soufrière Hills volcano erupts. Over the course of several years, it devastates the island, destroying the capital, forcing most of the population to flee.", "html": "1995 - On the Caribbean island of <a href=\"https://wikipedia.org/wiki/Montserrat\" title=\"Montserrat\">Montserrat</a>, the <a href=\"https://wikipedia.org/wiki/Soufri%C3%A8re_Hills\" title=\"Soufrière Hills\">Soufrière Hills</a> volcano erupts. Over the course of several years, it devastates the island, destroying the capital, forcing most of the population to flee.", "no_year_html": "On the Caribbean island of <a href=\"https://wikipedia.org/wiki/Montserrat\" title=\"Montserrat\">Montserrat</a>, the <a href=\"https://wikipedia.org/wiki/Soufri%C3%A8re_Hills\" title=\"Soufrière Hills\">Soufrière Hills</a> volcano erupts. Over the course of several years, it devastates the island, destroying the capital, forcing most of the population to flee.", "links": [{"title": "Montserrat", "link": "https://wikipedia.org/wiki/Montserrat"}, {"title": "Soufrière Hills", "link": "https://wikipedia.org/wiki/Soufri%C3%A8re_Hills"}]}, {"year": "1996", "text": "Storms provoke severe flooding on the Saguenay River, beginning one of Quebec's costliest natural disasters ever.", "html": "1996 - Storms provoke <a href=\"https://wikipedia.org/wiki/Saguenay_flood\" title=\"Saguenay flood\">severe flooding</a> on the <a href=\"https://wikipedia.org/wiki/Saguenay_River\" title=\"Saguenay River\">Saguenay River</a>, beginning one of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>'s costliest <a href=\"https://wikipedia.org/wiki/Natural_disaster\" title=\"Natural disaster\">natural disasters</a> ever.", "no_year_html": "Storms provoke <a href=\"https://wikipedia.org/wiki/Saguenay_flood\" title=\"Saguenay flood\">severe flooding</a> on the <a href=\"https://wikipedia.org/wiki/Saguenay_River\" title=\"Saguenay River\">Saguenay River</a>, beginning one of <a href=\"https://wikipedia.org/wiki/Quebec\" title=\"Quebec\">Quebec</a>'s costliest <a href=\"https://wikipedia.org/wiki/Natural_disaster\" title=\"Natural disaster\">natural disasters</a> ever.", "links": [{"title": "Saguenay flood", "link": "https://wikipedia.org/wiki/Saguenay_flood"}, {"title": "Saguenay River", "link": "https://wikipedia.org/wiki/Saguenay_River"}, {"title": "Quebec", "link": "https://wikipedia.org/wiki/Quebec"}, {"title": "Natural disaster", "link": "https://wikipedia.org/wiki/Natural_disaster"}]}, {"year": "1996", "text": "Battle of Mullaitivu: The Liberation Tigers of Tamil Eelam capture the Sri Lanka Army's base, killing over 1,200 soldiers.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Battle_of_Mullaitivu_(1996)\" title=\"Battle of Mullaitivu (1996)\">Battle of Mullaitivu</a>: The <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a> capture the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Army\" title=\"Sri Lanka Army\">Sri Lanka Army</a>'s base, killing over 1,200 soldiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Mullaitivu_(1996)\" title=\"Battle of Mullaitivu (1996)\">Battle of Mullaitivu</a>: The <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a> capture the <a href=\"https://wikipedia.org/wiki/Sri_Lanka_Army\" title=\"Sri Lanka Army\">Sri Lanka Army</a>'s base, killing over 1,200 soldiers.", "links": [{"title": "Battle of Mullaitivu (1996)", "link": "https://wikipedia.org/wiki/Battle_of_Mullaitivu_(1996)"}, {"title": "Liberation Tigers of Tamil Eelam", "link": "https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam"}, {"title": "Sri Lanka Army", "link": "https://wikipedia.org/wiki/Sri_Lanka_Army"}]}, {"year": "2002", "text": "A Consolidated PB4Y-2 Privateer crashes near Estes Park, Colorado, killing both crew members.", "html": "2002 - A <a href=\"https://wikipedia.org/wiki/Consolidated_PB4Y-2_Privateer\" title=\"Consolidated PB4Y-2 Privateer\">Consolidated PB4Y-2 Privateer</a> <a href=\"https://wikipedia.org/wiki/2002_United_States_airtanker_crashes\" title=\"2002 United States airtanker crashes\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Estes_Park,_Colorado\" title=\"Estes Park, Colorado\">Estes Park, Colorado</a>, killing both crew members.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Consolidated_PB4Y-2_Privateer\" title=\"Consolidated PB4Y-2 Privateer\">Consolidated PB4Y-2 Privateer</a> <a href=\"https://wikipedia.org/wiki/2002_United_States_airtanker_crashes\" title=\"2002 United States airtanker crashes\">crashes</a> near <a href=\"https://wikipedia.org/wiki/Estes_Park,_Colorado\" title=\"Estes Park, Colorado\">Estes Park, Colorado</a>, killing both crew members.", "links": [{"title": "Consolidated PB4Y-2 Privateer", "link": "https://wikipedia.org/wiki/Consolidated_PB4Y-2_Privateer"}, {"title": "2002 United States airtanker crashes", "link": "https://wikipedia.org/wiki/2002_United_States_airtanker_crashes"}, {"title": "Estes Park, Colorado", "link": "https://wikipedia.org/wiki/Estes_Park,_Colorado"}]}, {"year": "2012", "text": "At least seven people are killed and 32 others are injured after a bomb explodes on an Israeli tour bus at Burgas Airport, Bulgaria.", "html": "2012 - At least seven people are killed and 32 others are injured after a <a href=\"https://wikipedia.org/wiki/2012_Burgas_bus_bombing\" title=\"2012 Burgas bus bombing\">bomb explodes</a> on an <a href=\"https://wikipedia.org/wiki/Israelis\" title=\"Israelis\">Israeli</a> tour bus at <a href=\"https://wikipedia.org/wiki/Burgas_Airport\" title=\"Burgas Airport\">Burgas Airport</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>.", "no_year_html": "At least seven people are killed and 32 others are injured after a <a href=\"https://wikipedia.org/wiki/2012_Burgas_bus_bombing\" title=\"2012 Burgas bus bombing\">bomb explodes</a> on an <a href=\"https://wikipedia.org/wiki/Israelis\" title=\"Israelis\">Israeli</a> tour bus at <a href=\"https://wikipedia.org/wiki/Burgas_Airport\" title=\"Burgas Airport\">Burgas Airport</a>, <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>.", "links": [{"title": "2012 Burgas bus bombing", "link": "https://wikipedia.org/wiki/2012_Burgas_bus_bombing"}, {"title": "Israelis", "link": "https://wikipedia.org/wiki/Israelis"}, {"title": "Burgas Airport", "link": "https://wikipedia.org/wiki/Burgas_Airport"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}]}, {"year": "2013", "text": "The Government of Detroit, with up to $20 billion in debt, files for the largest municipal bankruptcy in U.S. history.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Government_of_Detroit\" title=\"Government of Detroit\">Government of Detroit</a>, with up to $20 billion in debt, files for the <a href=\"https://wikipedia.org/wiki/Detroit_bankruptcy\" title=\"Detroit bankruptcy\">largest municipal bankruptcy</a> in U.S. history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Government_of_Detroit\" title=\"Government of Detroit\">Government of Detroit</a>, with up to $20 billion in debt, files for the <a href=\"https://wikipedia.org/wiki/Detroit_bankruptcy\" title=\"Detroit bankruptcy\">largest municipal bankruptcy</a> in U.S. history.", "links": [{"title": "Government of Detroit", "link": "https://wikipedia.org/wiki/Government_of_Detroit"}, {"title": "Detroit bankruptcy", "link": "https://wikipedia.org/wiki/Detroit_bankruptcy"}]}, {"year": "2014", "text": "The Islamic State of Iraq and the Levant requires Christians to either accept dhimmi status, emigrate from ISIL lands, or be killed.", "html": "2014 - The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> requires Christians to either accept <a href=\"https://wikipedia.org/wiki/Dhimmi\" title=\"Dhimmi\">dhimmi</a> status, emigrate from ISIL lands, or be killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> requires Christians to either accept <a href=\"https://wikipedia.org/wiki/Dhimmi\" title=\"Dhimmi\">dhimmi</a> status, emigrate from ISIL lands, or be killed.", "links": [{"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dhimmi"}]}, {"year": "2019", "text": "A man sets fire to an anime studio in Fushimi-ku, Kyoto, Japan, killing 36 people and injuring dozens of others.", "html": "2019 - A man <a href=\"https://wikipedia.org/wiki/Kyoto_Animation_arson_attack\" title=\"Kyoto Animation arson attack\">sets fire to an anime studio</a> in <a href=\"https://wikipedia.org/wiki/Fu<PERSON><PERSON>-ku,_Kyoto\" title=\"Fushimi-ku, Kyoto\">Fushimi-ku, Kyoto</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing 36 people and injuring dozens of others.", "no_year_html": "A man <a href=\"https://wikipedia.org/wiki/Kyoto_Animation_arson_attack\" title=\"Kyoto Animation arson attack\">sets fire to an anime studio</a> in <a href=\"https://wikipedia.org/wiki/Fushi<PERSON>-ku,_Kyoto\" title=\"Fushimi-ku, Kyoto\">Fushimi-ku, Kyoto</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a>, killing 36 people and injuring dozens of others.", "links": [{"title": "Kyoto Animation arson attack", "link": "https://wikipedia.org/wiki/Kyoto_Animation_arson_attack"}, {"title": "Fushimi-ku, Kyoto", "link": "https://wikipedia.org/wiki/Fushimi-ku,_Kyoto"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}], "Births": [{"year": "1013", "text": "<PERSON> of Reichenau, German composer, mathematician, and astronomer (b. 1013)", "html": "1013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reichenau\" title=\"<PERSON> of Reichenau\"><PERSON> of Reichenau</a>, German composer, mathematician, and astronomer (b. 1013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reichena<PERSON>\" title=\"<PERSON> of Reichenau\"><PERSON> of Reichenau</a>, German composer, mathematician, and astronomer (b. 1013)", "links": [{"title": "<PERSON> Reichenau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Reich<PERSON>"}]}, {"year": "1501", "text": "<PERSON> of Austria, queen of Denmark (d. 1526)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/Isabella_of_Austria\" title=\"Isabella of Austria\">Isabella of Austria</a>, queen of Denmark (d. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_Austria\" title=\"Isabella of Austria\">Isabella of Austria</a>, queen of Denmark (d. 1526)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/Isabella_of_Austria"}]}, {"year": "1504", "text": "<PERSON>, Swiss pastor and reformer (d. 1575)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and reformer (d. 1575)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and reformer (d. 1575)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1534", "text": "<PERSON><PERSON><PERSON>, German theologian (d. 1583)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>us_Ursinus\" class=\"mw-redirect\" title=\"<PERSON><PERSON>us Ursinus\"><PERSON><PERSON><PERSON></a>, German theologian (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ursinus\" class=\"mw-redirect\" title=\"<PERSON><PERSON>us Ursinus\"><PERSON><PERSON><PERSON></a>, German theologian (d. 1583)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ursinus"}]}, {"year": "1552", "text": "<PERSON>, Holy Roman Emperor (d. 1612)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON></a>, Holy Roman Emperor (d. 1612)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1634", "text": "<PERSON>, Dutch politician, Governor-general of the Dutch East Indies (d. 1695)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"Governor-general of the Dutch East Indies\">Governor-general of the Dutch East Indies</a> (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"Governor-general of the Dutch East Indies\">Governor-general of the Dutch East Indies</a> (d. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-general of the Dutch East Indies", "link": "https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies"}]}, {"year": "1659", "text": "<PERSON><PERSON><PERSON><PERSON>, French painter (d. 1743)", "html": "1659 - <a href=\"https://wikipedia.org/wiki/Hyacinthe_Rigaud\" title=\"Hyacinthe Rigaud\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hyacinthe_Rigaud\" title=\"Hyacinthe Rigaud\"><PERSON><PERSON><PERSON><PERSON></a>, French painter (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hyacinthe_Rigaud"}]}, {"year": "1670", "text": "<PERSON>, Italian cellist and composer (d. 1747)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cellist and composer (d. 1747)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1702", "text": "<PERSON>, Polish noble (d. 1735)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish noble (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish noble (d. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1718", "text": "<PERSON><PERSON>, Italian poet, playwright, and critic (d. 1808)", "html": "1718 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, playwright, and critic (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian poet, playwright, and critic (d. 1808)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1720", "text": "<PERSON>, English ornithologist and ecologist (d. 1793)", "html": "1720 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ecologist (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ornithologist and ecologist (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON> of Bavaria, <PERSON><PERSON><PERSON><PERSON> of Saxony (d. 1780)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Bavaria\" title=\"Duchess <PERSON> of Bavaria\"><PERSON> of Bavaria</a>, <PERSON><PERSON><PERSON><PERSON> of Saxony (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Bavaria\" title=\"Duchess <PERSON> of Bavaria\"><PERSON> of Bavaria</a>, <PERSON><PERSON><PERSON><PERSON> of Saxony (d. 1780)", "links": [{"title": "Duchess <PERSON> of Bavaria", "link": "https://wikipedia.org/wiki/Duchess_<PERSON>_<PERSON>_of_Bavaria"}]}, {"year": "1750", "text": "<PERSON>, duke of Östergötland (d. 1803)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Prince <PERSON> of Sweden\"><PERSON></a>, duke of Östergötland (d. 1803)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Prince <PERSON> of Sweden\"><PERSON></a>, duke of Östergötland (d. 1803)", "links": [{"title": "Prince <PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON><PERSON>, German philosopher and academic (d. 1879)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Immanu<PERSON>_<PERSON>\" title=\"Immanu<PERSON>\">I<PERSON><PERSON><PERSON></a>, German philosopher and academic (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Immanu<PERSON>\">I<PERSON><PERSON><PERSON></a>, German philosopher and academic (d. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, English author and poet (d. 1863)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thackeray\" title=\"<PERSON> Thackeray\"><PERSON></a>, English author and poet (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Thackeray\" title=\"<PERSON> Thackeray\"><PERSON></a>, English author and poet (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ace_Thackeray"}]}, {"year": "1818", "text": "<PERSON>, Swedish lawyer and politician, 1st Prime Minister of Sweden (d. 1896)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a> (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1821", "text": "<PERSON>, French soprano and composer (d. 1910)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano and composer (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soprano and composer (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON><PERSON><PERSON>, Bulgarian priest and activist (d. 1873)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian priest and activist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian priest and activist (d. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, 13th President of Liberia (d. 1908)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, 13th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, 13th <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1908)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(politician)"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1843", "text": "<PERSON>, American marshal (d. 1905)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American marshal (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American marshal (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "1845", "text": "<PERSON>, French poet (d. 1875)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French poet (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French poet (d. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tristan_Corbi%C3%A8re"}]}, {"year": "1848", "text": "<PERSON><PERSON> <PERSON><PERSON>, English cricketer and physician (d. 1915)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and physician (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English cricketer and physician (d. 1915)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON><PERSON>, Dutch physicist and academic, Nobel Prize laureate (d. 1928)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian physician, one of the first Indian women to obtain a degree (d. 1923)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Kadambini_Ganguly\" title=\"Kadam<PERSON><PERSON> Ganguly\"><PERSON><PERSON><PERSON><PERSON></a>, Indian physician, one of the first Indian women to obtain a degree (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kadam<PERSON><PERSON>_<PERSON>\" title=\"Ka<PERSON><PERSON><PERSON> Gangul<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian physician, one of the first Indian women to obtain a degree (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>uly"}]}, {"year": "1864", "text": "<PERSON>, 1st Viscount <PERSON>, English politician, Chancellor of the Exchequer (d. 1937)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Exchequer\" title=\"Chancellor of the Exchequer\">Chancellor of the Exchequer</a> (d. 1937)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Chancellor of the Exchequer", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Exchequer"}]}, {"year": "1867", "text": "<PERSON>, American philanthropist and activist (d. 1932)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and activist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and activist (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Italian painter (d. 1958)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON>, Japanese actress and dancer (d. 1946)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and dancer (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese actress and dancer (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Czech composer and conductor of military bands (d. 1916)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech composer and conductor of military bands (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julius_Fu%C4%8D%C3%<PERSON><PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Czech composer and conductor of military bands (d. 1916)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/Julius_Fu%C4%8D%C3%AD<PERSON>_(composer)"}]}, {"year": "1881", "text": "<PERSON>, Canadian-American baseball player (d. 1921)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American baseball player (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Italian cardinal (d. 1979)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American general (d. 1945)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON> Jr.</a>, American general (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American general (d. 1945)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian military officer and politician, Minister President of Norway (d. 1945)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian military officer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Minister President of Norway</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian military officer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway\" title=\"List of heads of government of Norway\">Minister President of Norway</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vidku<PERSON>_<PERSON>ui<PERSON>ling"}, {"title": "List of heads of government of Norway", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Norway"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON>, Japanese politician, 13th Lord Keeper of the Privy Seal of Japan (d. 1977)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 13th <a href=\"https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan\" title=\"Lord Keeper of the Privy Seal of Japan\">Lord Keeper of the Privy Seal of Japan</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician, 13th <a href=\"https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan\" title=\"Lord Keeper of the Privy Seal of Japan\">Lord Keeper of the Privy Seal of Japan</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dichi_Kido"}, {"title": "Lord Keeper of the Privy Seal of Japan", "link": "https://wikipedia.org/wiki/Lord_Keeper_of_the_Privy_Seal_of_Japan"}]}, {"year": "1890", "text": "<PERSON>, Australian educator and politician, 15th Prime Minister of Australia (d. 1983)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 15th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}]}, {"year": "1892", "text": "<PERSON>, Brazilian footballer (d. 1969)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, 12th Earl of Airlie, Scottish peer, soldier and courtier (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Airlie\" title=\"<PERSON>, 12th Earl of Airlie\"><PERSON>, 12th Earl of Airlie</a>, Scottish peer, soldier and courtier (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Airlie\" title=\"<PERSON>, 12th Earl of Airlie\"><PERSON>, 12th Earl of Airlie</a>, Scottish peer, soldier and courtier (d. 1968)", "links": [{"title": "<PERSON>, 12th Earl of Airlie", "link": "https://wikipedia.org/wiki/<PERSON>,_12th_Earl_of_Air<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Russian-American ballerina (d. 1991)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American gangster (d. 1954)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\">Machine Gun <PERSON></a>, American gangster (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\"><PERSON> Gun <PERSON></a>, American gangster (d. 1954)", "links": [{"title": "<PERSON> Gun <PERSON> (gangster)", "link": "https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)"}]}, {"year": "1897", "text": "<PERSON>, English race car driver and engineer (d. 1935)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and engineer (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Scottish-English actor (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-English actor (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-English actor (d. 1979)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1899", "text": "<PERSON>, German soldier and politician, 8th Mayor of Marburg (d. 1942)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Marburg\" class=\"mw-redirect\" title=\"Mayor of Marburg\">Mayor of Marburg</a> (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of Marburg", "link": "https://wikipedia.org/wiki/Mayor_of_Marburg"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, French lawyer and author (d. 1999)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and author (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French lawyer and author (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, American author (d. 1984)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, American author (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, American author (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1902", "text": "<PERSON><PERSON>, American actor (d. 1978)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chill_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian-American academic and politician (d. 1992)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/S._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American academic and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-American academic and politician (d. 1992)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American director, playwright, and screenwriter (d. 1963)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and screenwriter (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, playwright, and screenwriter (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American mystic and activist (d. 1981)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Peace_Pilgrim\" title=\"Peace Pilgrim\"><PERSON> Pilgrim</a>, American mystic and activist (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peace_Pilgrim\" title=\"Peace Pilgrim\"><PERSON> Pilgrim</a>, American mystic and activist (d. 1981)", "links": [{"title": "Peace Pilgrim", "link": "https://wikipedia.org/wiki/Peace_Pilgrim"}]}, {"year": "1908", "text": "<PERSON><PERSON>, Mexican-American actress and dancer (d. 1944)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Lu<PERSON>_V%C3%A9lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American actress and dancer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lupe_V%C3%A9lez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American actress and dancer (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lupe_V%C3%A9lez"}]}, {"year": "1908", "text": "<PERSON>, American mathematician, statistician, and transportation economist (d. 1997)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and transportation economist (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, statistician, and transportation economist (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON>, Indian poet, critic, and academic (d. 1982)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/B<PERSON>nu_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, critic, and academic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"B<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, critic, and academic (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1909", "text": "<PERSON>, Belarusian-Russian economist and politician, Soviet Minister of Foreign Affairs (d. 1989)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belarusian-Russian economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Soviet Union)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)"}]}, {"year": "1909", "text": "<PERSON>, Afghan commander and politician, 1st President of Afghanistan (d. 1978)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan commander and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Afghan commander and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Afghanistan", "link": "https://wikipedia.org/wiki/President_of_Afghanistan"}]}, {"year": "1909", "text": "<PERSON>, American singer and actress (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian businessman (d. 1989)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Diptendu_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dipt<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian businessman (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Diptendu_<PERSON>nick"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Senegalese politician; 1st Prime Minister of Senegal (d. 2009)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Mamadou_Dia\" title=\"Mamadou Dia\"><PERSON><PERSON><PERSON></a>, Senegalese politician; 1st Prime Minister of Senegal (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mamadou_Dia\" title=\"Mamadou Dia\"><PERSON><PERSON><PERSON></a>, Senegalese politician; 1st Prime Minister of Senegal (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1911", "text": "<PERSON>, Canadian-American actor, producer, and screenwriter (d. 2003)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, producer, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, producer, and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yn"}]}, {"year": "1913", "text": "<PERSON>, American actor and comedian (d. 1997)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Red_S<PERSON>ton\" title=\"Red Skelton\"><PERSON></a>, American actor and comedian (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Skelton\" title=\"Red Skelton\"><PERSON></a>, American actor and comedian (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Red_Skelton"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Italian cyclist (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cyclist (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, French footballer (d. 2004)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Brazilian clown and actor (d. 2006)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Carequinha\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian clown and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carequinha\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian clown and actor (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carequinha"}]}, {"year": "1915", "text": "<PERSON>, British Royal Navy officer (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Navy officer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Royal Navy officer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American physicist  (d. 2019)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, French singer and guitarist (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Henri_<PERSON>\" title=\"Henri <PERSON>\"><PERSON></a>, French singer and guitarist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henri_<PERSON>\" title=\"Henri Salvador\"><PERSON></a>, French singer and guitarist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Austrian-born British economics professor (d. 2019)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British economics professor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born British economics professor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, South African lawyer and politician, 1st President of South Africa, Nobel Prize laureate (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_South_Africa\" title=\"President of South Africa\">President of South Africa</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nelson_Mandela"}, {"title": "President of South Africa", "link": "https://wikipedia.org/wiki/President_of_South_Africa"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Italian actress (d. 1991)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian actress (d. 1991)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, English race car driver and businessman (d. 1982)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver and businessman (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English brewer, founded Ringwood Brewery (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/Ringwood_Brewery\" title=\"Ringwood Brewery\">Ringwood Brewery</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/Ringwood_Brewery\" title=\"Ringwood Brewery\">Ringwood Brewery</a> (d. 2014)", "links": [{"title": "<PERSON> (brewer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)"}, {"title": "Ringwood Brewery", "link": "https://wikipedia.org/wiki/Ringwood_Brewery"}]}, {"year": "1921", "text": "<PERSON>, American psychiatrist and academic (d. 2021)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American colonel, astronaut, and politician (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, astronaut, and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, astronaut, and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English-French director and producer (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French director and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French director and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German actor (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American  physicist, historian, and philosopher (d. 1996)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, historian, and philosopher (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist, historian, and philosopher (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American engineer and businessman (d. 1997)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Danish swimmer (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Inge_S%C3%B8<PERSON><PERSON>\" title=\"Inge <PERSON>ø<PERSON>\"><PERSON><PERSON></a>, Danish swimmer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_S%C3%B8<PERSON><PERSON>\" title=\"Inge <PERSON>ø<PERSON>\"><PERSON><PERSON></a>, Danish swimmer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inge_S%C3%B8<PERSON>sen"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Italian actor", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Australian runner and hurdler (d. 2004)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and hurdler (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian runner and hurdler (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German lawyer and politician, German Federal Minister of the Interior (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">German Federal Minister of the Interior</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">German Federal Minister of the Interior</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Federal Ministry of the Interior (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)"}]}, {"year": "1925", "text": "<PERSON>, Australian Modernist architect (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Australian Modernist architect (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, Australian Modernist architect (d. 2022)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American baseball relief pitcher (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball relief pitcher (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball relief pitcher (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>all"}]}, {"year": "1926", "text": "<PERSON>, Canadian author and academic (d. 1987)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and academic (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American actress (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ber"}]}, {"year": "1926", "text": "<PERSON>, French politician and medical doctor (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and medical doctor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and medical doctor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Finnish film director and screenwriter (d. 2023)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish film director and screenwriter (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English poet (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet (d. 2001)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1927", "text": "<PERSON><PERSON>, Pakistani ghazal singer and playback singer (d. 2012)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani <i><a href=\"https://wikipedia.org/wiki/Ghazal\" title=\"Ghazal\">ghazal</a></i> singer and <a href=\"https://wikipedia.org/wiki/Playback_singer\" title=\"Playback singer\">playback singer</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani <i><a href=\"https://wikipedia.org/wiki/Ghazal\" title=\"Ghazal\">ghazal</a></i> singer and <a href=\"https://wikipedia.org/wiki/Playback_singer\" title=\"Playback singer\">playback singer</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Playback singer", "link": "https://wikipedia.org/wiki/Playback_singer"}]}, {"year": "1927", "text": "<PERSON>, German conductor and educator (d. 2015)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and educator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German conductor and educator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Spanish republican, political activist, and author (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa-Trevijano\" title=\"<PERSON>\"><PERSON></a>, Spanish republican, political activist, and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa-Trevijano\" title=\"<PERSON>\"><PERSON></a>, Spanish republican, political activist, and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antonio_Garc%C3%ADa-Trevijano"}]}, {"year": "1927", "text": "<PERSON>, Canadian politician (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American gangster, member of the Bonanno Crime Family (d. 1982)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster, member of the Bonanno Crime Family (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gangster, member of the Bonanno Crime Family (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Italian priest and author (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, American internet personality", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>winkle\" title=\"Baddiewinkle\"><PERSON><PERSON><PERSON><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>winkle\" title=\"Bad<PERSON>winkle\"><PERSON><PERSON><PERSON><PERSON></a>, American internet personality", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>kle"}]}, {"year": "1929", "text": "<PERSON>, American figure skater and actor (d. 2025)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actor (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "Screamin' <PERSON>, American R&B singer-songwriter, musician, and actor (d. 2000)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Screamin%27_<PERSON>_<PERSON>\" title=\"Screamin' <PERSON>\">Screamin' <PERSON></a>, American R&amp;B singer-songwriter, musician, and actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Screamin%27_<PERSON>_<PERSON>\" title=\"Screamin' <PERSON>\">Screamin' <PERSON></a>, American R&amp;B singer-songwriter, musician, and actor (d. 2000)", "links": [{"title": "Screamin' <PERSON>", "link": "https://wikipedia.org/wiki/Screamin%27_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American director and screenwriter (d. 2017)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, French actor, director, producer, and screenwriter (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, producer, and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Russian poet and playwright (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and playwright (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian poet and playwright (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English director, playwright, and screenwriter (d. 2024)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, playwright, and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, playwright, and screenwriter (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American actress (d. 2007)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American former figure skater and physician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American former figure skater and physician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Albright\"><PERSON><PERSON></a>, American former figure skater and physician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Albright"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Indian guru, 69th <PERSON><PERSON><PERSON><PERSON> (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian guru, 69th <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian guru, 69th <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Polish chemist and academic, Nobel Prize laureate", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oa<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1937", "text": "<PERSON>, American journalist and author (d. 2005)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English footballer (d. 2012)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1938)\" title=\"<PERSON> (footballer, born 1938)\"><PERSON></a>, English footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1938)\" title=\"<PERSON> (footballer, born 1938)\"><PERSON></a>, English footballer (d. 2012)", "links": [{"title": "<PERSON> (footballer, born 1938)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1938)"}]}, {"year": "1938", "text": "<PERSON>, Scottish keyboard player and manager (d. 1985)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish keyboard player and manager (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, Scottish keyboard player and manager (d. 1985)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1938", "text": "<PERSON>, Dutch director, producer, and screenwriter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English rock and jazz keyboard player", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock and jazz keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rock and jazz keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American football player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1939)\" title=\"<PERSON> (American football, born 1939)\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1939)\" title=\"<PERSON> (American football, born 1939)\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON> (American football, born 1939)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football,_born_1939)"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American baseball player, manager, and executive", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, German songwriter and producer (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German songwriter and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German songwriter and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and guitarist (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian footballer (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian footballer (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Swiss politician, 84th President of the Swiss Confederation", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, 84th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss politician, 84th <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1943", "text": "<PERSON>, American historian and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English hurdler and author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hurdler and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English hurdler and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Irish Republican politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_politician)\" title=\"<PERSON> (Northern Ireland politician)\"><PERSON></a>, Irish Republican politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_politician)\" title=\"<PERSON> (Northern Ireland politician)\"><PERSON></a>, Irish Republican politician", "links": [{"title": "<PERSON> (Northern Ireland politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Northern_Ireland_politician)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actress (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actress (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American publisher and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Puerto Rican-American wrestler and promoter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Sr.\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American wrestler and promoter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_Sr.\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American wrestler and promoter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_<PERSON>%C3%B3n_Sr."}]}, {"year": "1948", "text": "<PERSON>, American journalist and activist (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3rdo<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3rdo<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jeanne_C%C3%B3rdova"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, German biochemist and academic, Nobel Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1949", "text": "<PERSON>, Australian cricketer and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English businessman, founded Virgin Group", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Virgin_Group\" title=\"Virgin Group\">Virgin Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Virgin_Group\" title=\"Virgin Group\">Virgin Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virgin Group", "link": "https://wikipedia.org/wiki/Virgin_Group"}]}, {"year": "1950", "text": "<PERSON>, American computer scientist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>eftherakis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>eftherakis"}]}, {"year": "1950", "text": "<PERSON>, American disco singer and actor (d. 2001)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Village_People)\" class=\"mw-redirect\" title=\"<PERSON> (Village People)\"><PERSON></a>, American disco singer and actor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Village_People)\" class=\"mw-redirect\" title=\"<PERSON> (Village People)\"><PERSON></a>, American disco singer and actor (d. 2001)", "links": [{"title": "<PERSON> (Village People)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Village_People)"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Pakistani-American businessman and sports executive", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-American businessman and sports executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-American businessman and sports executive", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian political scientist, academic, and politician (d. 2011)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political scientist, academic, and politician (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political scientist, academic, and politician (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American educator and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Belgian chemist, academic, and politician, 68th Prime Minister of Belgium", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian chemist, academic, and politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian chemist, academic, and politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a>", "links": [{"title": "Elio Di Rupo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Di_R<PERSON>o"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American actress", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Margo_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter, mandolin player, and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, mandolin player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, mandolin player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Austrian painter and sculptor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Bernd_<PERSON>ching\" title=\"Bernd Fasching\"><PERSON><PERSON></a>, Austrian painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Bernd Fasching\"><PERSON><PERSON></a>, Austrian painter and sculptor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English golfer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English guitarist, songwriter, and producer (d. 2022)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English journalist and author", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English footballer and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Finnish footballer, coach, and manager", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian comedian, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Austrian-Luxembourgian skier", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Luxembourgian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Luxembourgian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Panamanian economist and politician, 35th President of Panama", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian economist and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Panamanian economist and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Torrijos"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "1964", "text": "<PERSON>, American talk show host", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian soprano", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian soprano", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American decathlete and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American decathlete and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, American decathlete and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dan_O%27Brien"}]}, {"year": "1967", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Diesel\" title=\"Vin Diesel\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Diesel\" title=\"Vin Diesel\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "Vin Diesel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand-Australian actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian rugby player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "The <PERSON>, Japanese wrestler and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/The_Great_Sasuke\" title=\"The Great Sasuke\">The Great Sasuke</a>, Japanese wrestler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Great_Sasuke\" title=\"The Great Sasuke\">The Great Sasuke</a>, Japanese wrestler and politician", "links": [{"title": "The Great Sasuke", "link": "https://wikipedia.org/wiki/The_Great_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American basketball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hardaway\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hardaway\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian singer-songwriter and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, British poet", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, British poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, British poet", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hunter\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English rapper and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/M.I.A<PERSON>_(rapper)\" title=\"<PERSON>.I.A. (rapper)\">M.I.A.</a>, English rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M.I.A<PERSON>_(rapper)\" title=\"M.I.A. (rapper)\"><PERSON>.I.A.</a>, English rapper and producer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>.I.<PERSON><PERSON>_(rapper)"}]}, {"year": "1976", "text": "<PERSON>, Spanish actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elsa_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, South Korean actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>-hee\"><PERSON>he<PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>-hee\"><PERSON>hee</a>, South Korean actress", "links": [{"title": "<PERSON>hee", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee"}]}, {"year": "1977", "text": "<PERSON>, Russian chess player and author", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian chess player and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Argentinian actress, singer, and dancer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ada<PERSON> Guerrero\"><PERSON><PERSON></a>, Argentinian actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ada<PERSON> Guerrero\"><PERSON><PERSON></a>, Argentinian actress, singer, and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Guerrero"}]}, {"year": "1978", "text": "<PERSON>, Irish rugby player and sportscaster", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American murderer responsible for making false rape allegations in the Duke lacrosse case", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Crystal_Mangum\" title=\"Crystal Mangum\"><PERSON></a>, American murderer responsible for making false rape allegations in the <a href=\"https://wikipedia.org/wiki/Duke_lacrosse_case\" class=\"mw-redirect\" title=\"Duke lacrosse case\">Duke lacrosse case</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crystal_Mangum\" title=\"Crystal Mangum\"><PERSON></a>, American murderer responsible for making false rape allegations in the <a href=\"https://wikipedia.org/wiki/Duke_lacrosse_case\" class=\"mw-redirect\" title=\"Duke lacrosse case\">Duke lacrosse case</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Crystal_Mangum"}, {"title": "Duke lacrosse case", "link": "https://wikipedia.org/wiki/Duke_lacrosse_case"}]}, {"year": "1978", "text": "<PERSON><PERSON>, South Korean actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-wook\" title=\"<PERSON><PERSON>-wook\"><PERSON><PERSON>wook</a>, South Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-wook\" title=\"<PERSON><PERSON>-wook\"><PERSON><PERSON>-wook</a>, South Korean actor", "links": [{"title": "<PERSON><PERSON>wook", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-wook"}]}, {"year": "1978", "text": "<PERSON>, American baseball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ben Sheets\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ben Sheets\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON><PERSON>, French journalist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French journalist", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A9lis<PERSON>_<PERSON>u"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>ion_Branch\" title=\"Deion Branch\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ion_Branch\" title=\"Deion Branch\"><PERSON><PERSON></a>, American football player", "links": [{"title": "Deion Branch", "link": "https://wikipedia.org/wiki/Deion_Branch"}]}, {"year": "1979", "text": "<PERSON>, American wrestler and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mercury\"><PERSON></a>, American wrestler and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joey Mercury\"><PERSON></a>, American wrestler and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American-Israeli basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Israeli basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Japanese actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ry%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ry%C5%8D<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ry%C5%8Dko_<PERSON>ue"}]}, {"year": "1981", "text": "<PERSON>, German ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Indian actress, singer, and film producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, singer, and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, singer, and film producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Honduran footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Suadi Arabian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Suadi Arabian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Suadi Arabian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Uruguayan footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter and drummer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Estonian decathlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian decathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mik<PERSON>_<PERSON>ll"}]}, {"year": "1983", "text": "<PERSON>, German footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American mixed martial artist and boxer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Panagiotis_Lagos\" title=\"Panagiotis Lagos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Panagiotis_Lagos\" title=\"Panagiotis Lagos\"><PERSON><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "Panagiotis <PERSON>", "link": "https://wikipedia.org/wiki/Panagiotis_Lagos"}]}, {"year": "1985", "text": "<PERSON>, English actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1986", "text": "<PERSON>, Russian ice dancer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Indonesian badminton player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, German-Tunisian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/%C3%84<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Tunisian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%84<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>luz"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, German footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Mexican boxer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>elo_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American wrestler and television personality", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and television personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Venezuelan baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Eugenio_Su%C3%A1rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eugenio_Su%C3%A1rez"}]}, {"year": "1993", "text": "<PERSON>, South Korean singer and actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>-<PERSON>\"><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-min"}]}, {"year": "1993", "text": "<PERSON>, Australian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, East Timorese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, East Timorese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, East Timorese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Soares"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Swedish rapper and singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Yung <PERSON>n\"><PERSON><PERSON></a>, Swedish rapper and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>n\"><PERSON><PERSON></a>, Swedish rapper and singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yung_<PERSON>n"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian cricketer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Smriti_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Man<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Smrit<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Man<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smriti_<PERSON>na"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Miss South Africa 2020", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Miss South Africa 2020", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Miss South Africa 2020", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>sida"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Ba<PERSON>_<PERSON>\" title=\"Ba<PERSON>ebay<PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ba<PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1997", "text": "<PERSON>, American sprinter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Argentine BMX rider", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine BMX rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine BMX rider", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "707", "text": "Emperor <PERSON><PERSON> of Japan (b. 683)", "html": "707 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON></a> of Japan (b. 683)", "links": [{"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "715", "text": "<PERSON>, Umayyad general (b. 695)", "html": "715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Umayyad general (b. 695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> bin <PERSON>\"><PERSON> bin <PERSON></a>, Umayyad general (b. 695)", "links": [{"title": "<PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "912", "text": "<PERSON>, Chinese emperor (b. 852)", "html": "912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "924", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>, <PERSON><PERSON> vizier (b. 855)", "html": "924 - <a href=\"https://wikipedia.org/wiki/<PERSON>%27l-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, <PERSON><PERSON> vizier (b. 855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%27l-<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON></a>, <PERSON><PERSON> vizier (b. 855)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibn <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>%27l-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "928", "text": "<PERSON>, patriarch of Constantinople", "html": "928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON></a>, patriarch of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON> II</a>, patriarch of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "984", "text": "<PERSON>, bishop of Metz", "html": "984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Metz\" title=\"<PERSON> of Metz\"><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz\" title=\"Roman Catholic Diocese of Metz\">Metz</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Metz\" title=\"<PERSON> of Metz\"><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz\" title=\"Roman Catholic Diocese of Metz\">Metz</a>", "links": [{"title": "<PERSON> I of Metz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Metz"}, {"title": "Roman Catholic Diocese of Metz", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Metz"}]}, {"year": "1100", "text": "<PERSON> of Bouillon, Frankish knight (b. 1016)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>, Frankish knight (b. 1016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bouillon\" title=\"<PERSON> of Bouillon\"><PERSON> of Bouillon</a>, Frankish knight (b. 1016)", "links": [{"title": "<PERSON> of Bouillon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bouillon"}]}, {"year": "1185", "text": "<PERSON>, first Archbishop of Uppsala (b. before 1143)", "html": "1185 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Uppsala)\" title=\"<PERSON> (archbishop of Uppsala)\"><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/Archbishop_of_Uppsala\" title=\"Archbishop of Uppsala\">Archbishop of Uppsala</a> (b. before 1143)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Uppsala)\" title=\"<PERSON> (archbishop of Uppsala)\"><PERSON></a>, first <a href=\"https://wikipedia.org/wiki/Archbishop_of_Uppsala\" title=\"Archbishop of Uppsala\">Archbishop of Uppsala</a> (b. before 1143)", "links": [{"title": "<PERSON> (archbishop of Uppsala)", "link": "https://wikipedia.org/wiki/<PERSON>_(archbishop_of_Uppsala)"}, {"title": "Archbishop of Uppsala", "link": "https://wikipedia.org/wiki/Archbishop_of_Uppsala"}]}, {"year": "1194", "text": "<PERSON> of Lusignan, king consort of Jerusalem (b. c. 1150)", "html": "1194 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lusign<PERSON>\" title=\"Guy of Lusignan\"><PERSON> of Lusignan</a>, king consort of Jerusalem (b. c. 1150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guy of Lusignan\"><PERSON> of Lusignan</a>, king consort of Jerusalem (b. c. 1150)", "links": [{"title": "<PERSON> of Lusignan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1232", "text": "<PERSON>, Marcher Lord of Bramber and Gower", "html": "1232 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Marcher Lord of Bramber and Gower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Marcher Lord of Bramber and Gower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1270", "text": "<PERSON><PERSON><PERSON> of Savoy, Archbishop of Canterbury", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Savoy_(bishop)\" title=\"<PERSON><PERSON><PERSON> of Savoy (bishop)\"><PERSON><PERSON><PERSON> of Savoy, Archbishop of Canterbury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Savoy_(bishop)\" title=\"<PERSON><PERSON><PERSON> of Savoy (bishop)\"><PERSON><PERSON><PERSON> of Savoy, Archbishop of Canterbury</a>", "links": [{"title": "<PERSON><PERSON><PERSON> of Savoy (bishop)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Savoy_(bishop)"}]}, {"year": "1300", "text": "<PERSON>, Italian religious leader, founded the Apostolic Brethren (b. 1240)", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Apostolic_Brethren\" title=\"Apostolic Brethren\">Apostolic Brethren</a> (b. 1240)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Apostolic_Brethren\" title=\"Apostolic Brethren\">Apostolic Brethren</a> (b. 1240)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Apostolic Brethren", "link": "https://wikipedia.org/wiki/Apostolic_Brethren"}]}, {"year": "1450", "text": "<PERSON>, Duke of Brittany (b. 1414)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1414)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1488", "text": "<PERSON><PERSON>, Italian explorer (b. 1432)", "html": "1488 - <a href=\"https://wikipedia.org/wiki/Alvise_C<PERSON>mosto\" title=\"Alvise Cadamosto\"><PERSON><PERSON></a>, Italian explorer (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vise_<PERSON>most<PERSON>\" title=\"Alvise Cadamosto\"><PERSON><PERSON></a>, Italian explorer (b. 1432)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alvise_Cadamosto"}]}, {"year": "1566", "text": "<PERSON><PERSON><PERSON>, Spanish bishop and historian (b. c.1484)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_de_las_Casas\" title=\"Bartolo<PERSON> de las Casas\"><PERSON><PERSON><PERSON> las Casas</a>, Spanish bishop and historian (b. c.1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bartolom%C3%A9_de_las_Casas\" title=\"Bartolo<PERSON> de las Casas\"><PERSON><PERSON><PERSON> las Casas</a>, Spanish bishop and historian (b. c.1484)", "links": [{"title": "Bartolomé de las Casas", "link": "https://wikipedia.org/wiki/Bartolom%C3%A9_de_las_Casas"}]}, {"year": "1591", "text": "<PERSON><PERSON>, Slovenian composer (b. 1550)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gallus\"><PERSON><PERSON></a>, Slovenian composer (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Gallus\"><PERSON><PERSON></a>, Slovenian composer (b. 1550)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1608", "text": "<PERSON>, Elector of Brandenburg (b. 1546)", "html": "1608 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (b. 1546)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON>, Elector of Brandenburg</a> (b. 1546)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1610", "text": "<PERSON><PERSON><PERSON>, Italian painter (b. 1571)", "html": "1610 - <a href=\"https://wikipedia.org/wiki/Caravaggio\" title=\"Caravaggio\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caravaggio\" title=\"Caravaggio\"><PERSON><PERSON><PERSON></a>, Italian painter (b. 1571)", "links": [{"title": "Caravaggio", "link": "https://wikipedia.org/wiki/Caravaggio"}]}, {"year": "1639", "text": "<PERSON> of Saxe-Weimar, German general (b. 1604)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Weimar\" title=\"<PERSON> of Saxe-Weimar\"><PERSON> of Saxe-Weimar</a>, German general (b. 1604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxe-Weimar\" title=\"<PERSON> of Saxe-Weimar\"><PERSON> of Saxe-Weimar</a>, German general (b. 1604)", "links": [{"title": "<PERSON> of Saxe-Weimar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxe-Weimar"}]}, {"year": "1650", "text": "<PERSON>, English Royalist, hanged in London by Parliamentary forces as a spy (b. 1615)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Royalist, hanged in London by Parliamentary forces as a spy (b. 1615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Royalist, hanged in London by Parliamentary forces as a spy (b. 1615)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1695", "text": "<PERSON>, Dutch politician, Governor-general of the Dutch East Indies (b. 1634)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"Governor-general of the Dutch East Indies\">Governor-general of the Dutch East Indies</a> (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician, <a href=\"https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies\" class=\"mw-redirect\" title=\"Governor-general of the Dutch East Indies\">Governor-general of the Dutch East Indies</a> (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor-general of the Dutch East Indies", "link": "https://wikipedia.org/wiki/Governor-general_of_the_Dutch_East_Indies"}]}, {"year": "1698", "text": "<PERSON>, Swiss theologian and author (b. 1633)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and author (b. 1633)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1721", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1684)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1684)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1730", "text": "<PERSON>, du<PERSON> <PERSON>, French general (b. 1644)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_de_<PERSON>f<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French general (b. 1644)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>,_duc_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, duc de <PERSON>\"><PERSON>, duc <PERSON></a>, French general (b. 1644)", "links": [{"title": "<PERSON>, duc de <PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>,_du<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1756", "text": "<PERSON>, Dutch poet and playwright (b. 1683)", "html": "1756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and playwright (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch poet and playwright (b. 1683)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Scottish-American admiral and diplomat (b. 1747)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American admiral and diplomat (b. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American admiral and diplomat (b. 1747)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "<PERSON>, English novelist  (b. 1775)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist (b. 1775)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jane <PERSON>\"><PERSON></a>, English novelist (b. 1775)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON>, Maltese merchant and rebel leader (b. 1777)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese merchant and rebel leader (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese merchant and rebel leader (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American colonel (b. 1837)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Mexican lawyer and politician, 26th President of Mexico (b. 1806)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1rez\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician, 26th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_Ju%C3%A1rez"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1884", "text": "<PERSON>, Austrian geologist and academic (b. 1829)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and academic (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist and academic (b. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, English journalist, author, and activist, co-founded the Women's Suffrage Journal (b. 1827)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist, co-founded the <i><a href=\"https://wikipedia.org/wiki/Women%27s_Suffrage_Journal\" title=\"Women's Suffrage Journal\">Women's Suffrage Journal</a></i> (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and activist, co-founded the <i><a href=\"https://wikipedia.org/wiki/Women%27s_Suffrage_Journal\" title=\"Women's Suffrage Journal\">Women's Suffrage Journal</a></i> (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Women's Suffrage Journal", "link": "https://wikipedia.org/wiki/Women%27s_Suffrage_Journal"}]}, {"year": "1892", "text": "<PERSON>, English travel agent, founded the Thomas Cook Group (b. 1808)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English travel agent, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Thomas Cook Group\">Thomas Cook Group</a> (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English travel agent, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Group\" title=\"Thomas Cook Group\">Thomas <PERSON> Group</a> (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thomas <PERSON> Group", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON><PERSON><PERSON>, American novelist and journalist (b. 1832)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and journalist (b. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American novelist and journalist (b. 1832)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horatio_<PERSON>ger"}]}, {"year": "1916", "text": "<PERSON>, American journalist and author (b. 1835)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian cardinal (b. 1840)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Louis-<PERSON>_B%C3%A9gin\" title=\"<PERSON><PERSON>Na<PERSON><PERSON> B<PERSON>gin\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis-<PERSON>_B%C3%A9gin\" title=\"<PERSON><PERSON>Na<PERSON><PERSON> B<PERSON>gin\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian cardinal (b. 1840)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Nazaire_B%C3%A9gin"}]}, {"year": "1932", "text": "<PERSON>, French author and diplomat, French Ambassador to the United States (b. 1855)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French Ambassador to the United States</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States\" class=\"mw-redirect\" title=\"List of French ambassadors to the United States\">French Ambassador to the United States</a> (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of French ambassadors to the United States", "link": "https://wikipedia.org/wiki/List_of_French_ambassadors_to_the_United_States"}]}, {"year": "1937", "text": "<PERSON>, English poet and academic (b. 1908)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON> of Romania (b. 1875)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Marie_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marie_of_Romania\" title=\"<PERSON> of Romania\"><PERSON> of Romania</a> (b. 1875)", "links": [{"title": "Marie of Romania", "link": "https://wikipedia.org/wiki/Marie_of_Romania"}]}, {"year": "1944", "text": "<PERSON>, English author, poet, and playwright (b. 1870)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Estonian footballer and ice hockey player (b. 1906)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tipner\" title=\"<PERSON><PERSON> Tipner\"><PERSON><PERSON></a>, Estonian footballer and ice hockey player (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Tipner\" title=\"<PERSON><PERSON> Tipner\"><PERSON><PERSON></a>, Estonian footballer and ice hockey player (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Tipner"}]}, {"year": "1948", "text": "<PERSON>, Finnish historian, academic, and politician (b. 1877)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish historian, academic, and politician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish historian, academic, and politician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Czech composer and educator (b. 1870)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/V%C3%ADt%C4%9B<PERSON><PERSON>_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer and educator (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%ADt%C4%9B<PERSON><PERSON>_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Czech composer and educator (b. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADt%C4%9Bzslav_Nov%C3%A1k"}]}, {"year": "1949", "text": "<PERSON>, Guatemalan Army colonel and briefly Guatemalan head of state (b. 1905)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan Army colonel and briefly Guatemalan head of state (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guatemalan Army colonel and briefly Guatemalan head of state (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American critic and biographer (b. 1885)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American critic and biographer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American critic and biographer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Belgian architect and historian (b. 1862)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian architect and historian (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian architect and historian (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON> <PERSON>, American gangster (b. 1895)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\">Machine Gun <PERSON></a>, American gangster (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)\" title=\"Machine Gun Kelly (gangster)\">Machine Gun <PERSON></a>, American gangster (b. 1895)", "links": [{"title": "<PERSON> Gun <PERSON> (gangster)", "link": "https://wikipedia.org/wiki/Machine_Gun_<PERSON>_(gangster)"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1942)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Belgian physiologist and academic, Nobel Prize laureate (b. 1892)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Corneille_Heymans\" title=\"Corneille Heymans\"><PERSON><PERSON><PERSON></a>, Belgian physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Corneille_<PERSON>mans\" title=\"Corneille Heymans\"><PERSON><PERSON><PERSON></a>, Belgian physiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1892)", "links": [{"title": "Corneille <PERSON>", "link": "https://wikipedia.org/wiki/Corneille_Heymans"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1969", "text": "<PERSON>, American educator and secretary (b. 1940)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and secretary (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and secretary (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English actor (b. 1910)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American illustrator (b. 1941)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%93\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%93\" title=\"<PERSON>\"><PERSON></a>, American illustrator (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vaughn_Bod%C4%93"}]}, {"year": "1981", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Swedish lawyer (b. 1890)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Sonja_Branting-Westerst%C3%A5hl\" title=\"Son<PERSON> Branting-Westerståhl\"><PERSON><PERSON></a>, Swedish lawyer (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sonja_Branting-Westerst%C3%A5hl\" title=\"Son<PERSON> Branting-Westerståhl\"><PERSON><PERSON></a>, Swedish lawyer (b. 1890)", "links": [{"title": "<PERSON><PERSON>-Westerståhl", "link": "https://wikipedia.org/wiki/Sonja_Branting-Westerst%C3%A5hl"}]}, {"year": "1982", "text": "<PERSON>, Russian-American linguist and theorist (b. 1896)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American linguist and theorist (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American linguist and theorist (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English actress (b. 1914)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Estonian director and screenwriter (b. 1926)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian director and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Brazilian sociologist, anthropologist, historian, writer, painter, journalist and congressman (b. 1907)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sociologist, anthropologist, historian, writer, painter, journalist and congressman (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian sociologist, anthropologist, historian, writer, painter, journalist and congressman (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, German singer-songwriter, keyboard player, and actress (b. 1938)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, keyboard player, and actress (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter, keyboard player, and actress (b. 1938)", "links": [{"title": "Nico", "link": "https://wikipedia.org/wiki/Nico"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Portuguese composer and conductor (b. 1924)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> B<PERSON> Santos\"><PERSON><PERSON></a>, Portuguese composer and conductor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Santos\"><PERSON><PERSON></a>, Portuguese composer and conductor (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American baseball player (b. 1954)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American model and actress (b. 1967)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American psychiatrist and author (b. 1896)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychiatrist and author (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, South Korean politician, 2nd President of South Korea (b. 1897)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South Korean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of South Korea</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "2001", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1945)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1a\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mimi_Fari%C3%B1a"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Turkish journalist and author (b. 1924)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist and author (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Metin_<PERSON><PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Belgian-French historian and author (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>t\" title=\"<PERSON>\"><PERSON></a>, Belgian-French historian and author (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French historian and author (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Cast<PERSON>t"}]}, {"year": "2004", "text": "<PERSON><PERSON>, French wine maker (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/%C3%89mile_Peynaud\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French wine maker (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89mile_Pey<PERSON>ud\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French wine maker (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Australian cyclist and rower (b. 1976)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist and rower (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist and rower (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American general (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>moreland"}]}, {"year": "2006", "text": "<PERSON>, American theater writer (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, American theater writer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)\" title=\"<PERSON> (critic)\"><PERSON></a>, American theater writer (b. 1917)", "links": [{"title": "<PERSON> (critic)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(critic)"}]}, {"year": "2007", "text": "<PERSON>, American tenor (b. 1952)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tenor (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Japanese politician (b. 1908)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Japanese politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Japanese politician (b. 1908)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)"}]}, {"year": "2009", "text": "<PERSON>, English soldier (b. 1896)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, English actress (b. 1925)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Lithuanian-Israeli rabbi and author (b. 1910)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>sef <PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli rabbi and author (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>sef <PERSON>om <PERSON>hi<PERSON>\"><PERSON><PERSON></a>, Lithuanian-Israeli rabbi and author (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>hiv"}]}, {"year": "2012", "text": "<PERSON>, French politician and diplomat, French Minister of Foreign Affairs (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign and European Affairs (France)\">French Minister of Foreign Affairs</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>-<PERSON>"}, {"title": "Ministry of Foreign and European Affairs (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_and_European_Affairs_(France)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Syrian general and politician, Syrian Minister of Defense (b. 1947)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)\" title=\"Ministry of Defense (Syria)\">Syrian Minister of Defense</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Syrian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)\" title=\"Ministry of Defense (Syria)\">Syrian Minister of Defense</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>wo<PERSON>_<PERSON>a"}, {"title": "Ministry of Defense (Syria)", "link": "https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Syrian general and politician (b. 1950)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian general and politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian general and politician (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Syrian general and politician, Syrian Minister of Defense (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)\" title=\"Ministry of Defense (Syria)\">Syrian Minister of Defense</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian general and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)\" title=\"Ministry of Defense (Syria)\">Syrian Minister of Defense</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ministry of Defense (Syria)", "link": "https://wikipedia.org/wiki/Ministry_of_Defense_(Syria)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indian actor (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian poet, songwriter, and actor (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON></a>, Indian poet, songwriter, and actor (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(poet)\" title=\"<PERSON><PERSON><PERSON> (poet)\"><PERSON><PERSON><PERSON></a>, Indian poet, songwriter, and actor (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(poet)"}]}, {"year": "2013", "text": "<PERSON>,  French-American cardiologist and academic (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American cardiologist and academic (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American cardiologist and academic (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German footballer (b. 1980)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Brazilian journalist, author, and academic (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, author, and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian journalist, author, and academic (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON><PERSON>_<PERSON>ibeiro"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Austrian-Spanish actor, director, and screenwriter (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%B6nherr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Spanish actor, director, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>h%C3%B6nherr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Spanish actor, director, and screenwriter (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dietmar_Sch%C3%B6nherr"}]}, {"year": "2015", "text": "<PERSON>, American actor (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American food critic (b. 1960)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jonathan Gold\"><PERSON></a>, American food critic (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jonathan Gold\"><PERSON></a>, American food critic (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American radio personality (b. 1938)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio personality (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio personality (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English comedian (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27C<PERSON><PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian (b. 1939)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(comedian)"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON>, Indian politician, former Chief Minister of Kerala (b. 1943)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, former <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, former <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Kerala\" class=\"mw-redirect\" title=\"Chief Minister of Kerala\">Chief Minister of Kerala</a> (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oom<PERSON>_<PERSON>dy"}, {"title": "Chief Minister of Kerala", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Kerala"}]}, {"year": "2024", "text": "<PERSON>, American political commentator and television host (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and television host (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political commentator and television host (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American football player (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American comedian and actor (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}