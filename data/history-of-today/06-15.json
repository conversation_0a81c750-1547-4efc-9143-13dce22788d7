{"date": "June 15", "url": "https://wikipedia.org/wiki/June_15", "data": {"Events": [{"year": "763 BC", "text": "Assyrians record a solar eclipse that is later used to fix the chronology of Mesopotamian history.", "html": "763 BC - 763 BC - <a href=\"https://wikipedia.org/wiki/Assyria\" title=\"Assyria\">Assyrians</a> record <a href=\"https://wikipedia.org/wiki/Assyrian_eclipse\" title=\"Assyrian eclipse\">a solar eclipse</a> that is later used to fix the <a href=\"https://wikipedia.org/wiki/Chronology\" title=\"Chronology\">chronology</a> of <a href=\"https://wikipedia.org/wiki/Mesopotamia\" title=\"Mesopotamia\">Mesopotamian</a> history.", "no_year_html": "763 BC - <a href=\"https://wikipedia.org/wiki/Assyria\" title=\"Assyria\">Assyrians</a> record <a href=\"https://wikipedia.org/wiki/Assyrian_eclipse\" title=\"Assyrian eclipse\">a solar eclipse</a> that is later used to fix the <a href=\"https://wikipedia.org/wiki/Chronology\" title=\"Chronology\">chronology</a> of <a href=\"https://wikipedia.org/wiki/Mesopotamia\" title=\"Mesopotamia\">Mesopotamian</a> history.", "links": [{"title": "Assyria", "link": "https://wikipedia.org/wiki/Assyria"}, {"title": "Assyrian eclipse", "link": "https://wikipedia.org/wiki/Assyrian_eclipse"}, {"title": "Chronology", "link": "https://wikipedia.org/wiki/Chronology"}, {"title": "Mesopotamia", "link": "https://wikipedia.org/wiki/Mesopotamia"}]}, {"year": "844", "text": "<PERSON> is crowned as king of Italy at Rome by pope <PERSON><PERSON><PERSON>.", "html": "844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Italy\" title=\"<PERSON> II of Italy\"><PERSON> II</a> is crowned as king of <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a> at <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> by pope <a href=\"https://wikipedia.org/wiki/Pope_Sergius_II\" title=\"Pope Sergius II\">Sergius II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Italy\" title=\"<PERSON> II of Italy\"><PERSON> II</a> is crowned as king of <a href=\"https://wikipedia.org/wiki/Italy\" title=\"Italy\">Italy</a> at <a href=\"https://wikipedia.org/wiki/Rome\" title=\"Rome\">Rome</a> by pope <a href=\"https://wikipedia.org/wiki/Pope_Sergius_II\" title=\"Pope Sergius II\">Sergius II</a>.", "links": [{"title": "<PERSON> of Italy", "link": "https://wikipedia.org/wiki/Louis_II_of_Italy"}, {"title": "Italy", "link": "https://wikipedia.org/wiki/Italy"}, {"title": "Rome", "link": "https://wikipedia.org/wiki/Rome"}, {"title": "<PERSON> <PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_Sergi<PERSON>_II"}]}, {"year": "923", "text": "Battle of Soissons: King <PERSON> of France is killed and King <PERSON> Simple is arrested by the supporters of Duke <PERSON> of Burgundy.", "html": "923 - <a href=\"https://wikipedia.org/wiki/Battle_of_Soissons_(923)\" title=\"Battle of Soissons (923)\">Battle of Soissons</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON> of France</a> is killed and King <a href=\"https://wikipedia.org/wiki/Charles_the_Simple\" title=\"<PERSON> the Simple\"><PERSON> Simple</a> is arrested by the supporters of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_France\" title=\"<PERSON> of France\"><PERSON> of Burgundy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Soissons_(923)\" title=\"Battle of Soissons (923)\">Battle of Soissons</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> is killed and King <a href=\"https://wikipedia.org/wiki/Charles_the_Simple\" title=\"<PERSON> the Simple\"><PERSON></a> is arrested by the supporters of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France\" title=\"<PERSON> of France\"><PERSON> of Burgundy</a>.", "links": [{"title": "Battle of Soissons (923)", "link": "https://wikipedia.org/wiki/Battle_of_Soissons_(923)"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Simple"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Rudolph_of_France"}]}, {"year": "1184", "text": "The naval Battle of Fimreite is won by the Birkebeiner pretender <PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON> takes the Norwegian throne and King <PERSON> of Norway is killed.", "html": "1184 - The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Fimreite\" title=\"Battle of Fimreite\">Battle of Fimreite</a> is won by the Birk<PERSON><PERSON>er pretender <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>. <PERSON><PERSON><PERSON><PERSON> takes the Norwegian throne and King <a href=\"https://wikipedia.org/wiki/Magnus_V_of_Norway\" class=\"mw-redirect\" title=\"Magnus V of Norway\"><PERSON> of Norway</a> is killed.", "no_year_html": "The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Fimreite\" title=\"Battle of Fimreite\">Battle of Fimreite</a> is won by the B<PERSON><PERSON><PERSON>er pretender <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>. <PERSON><PERSON><PERSON><PERSON> takes the Norwegian throne and King <a href=\"https://wikipedia.org/wiki/Magnus_V_of_Norway\" class=\"mw-redirect\" title=\"Magnus V of Norway\"><PERSON> of Norway</a> is killed.", "links": [{"title": "Battle of Fimreite", "link": "https://wikipedia.org/wiki/Battle_of_Fimreite"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> V of Norway", "link": "https://wikipedia.org/wiki/Magnus_V_of_Norway"}]}, {"year": "1215", "text": "King <PERSON> of England puts his seal to Magna Carta.", "html": "1215 - King <a href=\"https://wikipedia.org/wiki/John_of_England\" class=\"mw-redirect\" title=\"John of England\"><PERSON> of England</a> puts his <a href=\"https://wikipedia.org/wiki/Seal_(emblem)\" title=\"Seal (emblem)\">seal</a> to <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/John_<PERSON>_England\" class=\"mw-redirect\" title=\"John of England\"><PERSON> of England</a> puts his <a href=\"https://wikipedia.org/wiki/Seal_(emblem)\" title=\"Seal (emblem)\">seal</a> to <a href=\"https://wikipedia.org/wiki/Magna_Carta\" title=\"Magna Carta\">Magna Carta</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_of_England"}, {"title": "Seal (emblem)", "link": "https://wikipedia.org/wiki/Seal_(emblem)"}, {"title": "Magna Carta", "link": "https://wikipedia.org/wiki/Magna_Carta"}]}, {"year": "1219", "text": "Northern Crusades: Danish victory at the Battle of Lindanise (modern-day Tallinn) establishes the Danish Duchy of Estonia.", "html": "1219 - <a href=\"https://wikipedia.org/wiki/Northern_Crusades\" title=\"Northern Crusades\">Northern Crusades</a>: Danish victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lyndanisse\" title=\"Battle of Lyndanisse\">Battle of Lindanise</a> (modern-day <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>) establishes the Danish <a href=\"https://wikipedia.org/wiki/Danish_Estonia\" class=\"mw-redirect\" title=\"Danish Estonia\">Duchy of Estonia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northern_Crusades\" title=\"Northern Crusades\">Northern Crusades</a>: Danish victory at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lyndanisse\" title=\"Battle of Lyndanisse\">Battle of Lindanise</a> (modern-day <a href=\"https://wikipedia.org/wiki/Tallinn\" title=\"Tallinn\">Tallinn</a>) establishes the Danish <a href=\"https://wikipedia.org/wiki/Danish_Estonia\" class=\"mw-redirect\" title=\"Danish Estonia\">Duchy of Estonia</a>.", "links": [{"title": "Northern Crusades", "link": "https://wikipedia.org/wiki/Northern_Crusades"}, {"title": "Battle of Lyndanisse", "link": "https://wikipedia.org/wiki/Battle_of_<PERSON><PERSON><PERSON>isse"}, {"title": "Tallinn", "link": "https://wikipedia.org/wiki/Tallinn"}, {"title": "Danish Estonia", "link": "https://wikipedia.org/wiki/Danish_Estonia"}]}, {"year": "1246", "text": "With the death of <PERSON>, Duke of Austria, the Babenberg dynasty ends in Austria.", "html": "1246 - With the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a>, the <a href=\"https://wikipedia.org/wiki/Babenberg_dynasty\" class=\"mw-redirect\" title=\"Babenberg dynasty\">Babenberg dynasty</a> ends in Austria.", "no_year_html": "With the death of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a>, the <a href=\"https://wikipedia.org/wiki/Babenberg_dynasty\" class=\"mw-redirect\" title=\"Babenberg dynasty\">Babenberg dynasty</a> ends in Austria.", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}, {"title": "Babenberg dynasty", "link": "https://wikipedia.org/wiki/Babenberg_dynasty"}]}, {"year": "1300", "text": "The city of Bilbao is founded.", "html": "1300 - The city of <a href=\"https://wikipedia.org/wiki/Bilbao\" title=\"Bilbao\">Bilbao</a> is founded.", "no_year_html": "The city of <a href=\"https://wikipedia.org/wiki/Bilbao\" title=\"Bilbao\">Bilbao</a> is founded.", "links": [{"title": "Bilbao", "link": "https://wikipedia.org/wiki/Bilbao"}]}, {"year": "1310", "text": "The Tiepolo conspiracy, seeking to seize power in the Republic of Venice, is thwarted after bloody street clashes in Venice. The suppression of the revolt will lead to the creation of the Council of Ten.", "html": "1310 - The <a href=\"https://wikipedia.org/wiki/Tiepolo_conspiracy\" title=\"Tiepolo conspiracy\">Tiepolo conspiracy</a>, seeking to seize power in the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, is thwarted after bloody street clashes in Venice. The suppression of the revolt will lead to the creation of the <a href=\"https://wikipedia.org/wiki/Council_of_Ten\" title=\"Council of Ten\">Council of Ten</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tiepolo_conspiracy\" title=\"Tiepolo conspiracy\">Tiepolo conspiracy</a>, seeking to seize power in the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>, is thwarted after bloody street clashes in Venice. The suppression of the revolt will lead to the creation of the <a href=\"https://wikipedia.org/wiki/Council_of_Ten\" title=\"Council of Ten\">Council of Ten</a>.", "links": [{"title": "Tiepolo conspiracy", "link": "https://wikipedia.org/wiki/Tiepolo_conspiracy"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}, {"title": "Council of Ten", "link": "https://wikipedia.org/wiki/Council_of_Ten"}]}, {"year": "1312", "text": "At the Battle of Rozgony, King <PERSON> of Hungary wins a decisive victory over the family of Palatine <PERSON>.", "html": "1312 - At the <a href=\"https://wikipedia.org/wiki/Battle_of_Rozgony\" title=\"Battle of Rozgony\">Battle of Rozgony</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> wins a decisive victory over the family of <a href=\"https://wikipedia.org/wiki/Palatine_of_Hungary\" title=\"Palatine of Hungary\">Palatine</a> <a href=\"https://wikipedia.org/wiki/Amade_Aba\" class=\"mw-redirect\" title=\"Amade Aba\"><PERSON><PERSON></a>.", "no_year_html": "At the <a href=\"https://wikipedia.org/wiki/Battle_of_Rozgony\" title=\"Battle of Rozgony\">Battle of Rozgony</a>, King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> of Hungary\"><PERSON> of Hungary</a> wins a decisive victory over the family of <a href=\"https://wikipedia.org/wiki/Palatine_of_Hungary\" title=\"Palatine of Hungary\">Palatine</a> <a href=\"https://wikipedia.org/wiki/Amade_Aba\" class=\"mw-redirect\" title=\"Amade Aba\"><PERSON><PERSON></a>.", "links": [{"title": "Battle of Rozgony", "link": "https://wikipedia.org/wiki/Battle_of_Rozgony"}, {"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary"}, {"title": "Palatine of Hungary", "link": "https://wikipedia.org/wiki/Palatine_of_Hungary"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amade_<PERSON>ba"}]}, {"year": "1389", "text": "Battle of Kosovo: The Ottoman Empire defeats Serbs and Bosnians.", "html": "1389 - <a href=\"https://wikipedia.org/wiki/Battle_of_Kosovo\" title=\"Battle of Kosovo\">Battle of Kosovo</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> defeats <a href=\"https://wikipedia.org/wiki/Serbs\" title=\"Serbs\">Serbs</a> and <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnians</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Kosovo\" title=\"Battle of Kosovo\">Battle of Kosovo</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> defeats <a href=\"https://wikipedia.org/wiki/Serbs\" title=\"Serbs\">Serbs</a> and <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnians</a>.", "links": [{"title": "Battle of Kosovo", "link": "https://wikipedia.org/wiki/Battle_of_Kosovo"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Serbs", "link": "https://wikipedia.org/wiki/Serbs"}, {"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}]}, {"year": "1410", "text": "In a decisive battle at Onon River, the Mongol forces of Oljei Temur were decimated by the Chinese armies of the Yongle Emperor.", "html": "1410 - In a decisive <a href=\"https://wikipedia.org/wiki/Yongle_Emperor%27s_campaigns_against_the_Mongols\" title=\"Yongle Emperor's campaigns against the Mongols\">battle at Onon River</a>, the Mongol forces of <a href=\"https://wikipedia.org/wiki/Oljei_Temur\" class=\"mw-redirect\" title=\"Oljei Temur\"><PERSON><PERSON><PERSON><PERSON></a> were decimated by the Chinese armies of the <a href=\"https://wikipedia.org/wiki/Yongle_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>.", "no_year_html": "In a decisive <a href=\"https://wikipedia.org/wiki/<PERSON>le_Emperor%27s_campaigns_against_the_Mongols\" title=\"Yongle Emperor's campaigns against the Mongols\">battle at Onon River</a>, the Mongol forces of <a href=\"https://wikipedia.org/wiki/Ol<PERSON><PERSON>_Temur\" class=\"mw-redirect\" title=\"Oljei Temur\"><PERSON><PERSON><PERSON><PERSON></a> were decimated by the Chinese armies of the <a href=\"https://wikipedia.org/wiki/Yongle_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a>.", "links": [{"title": "Yongle Emperor's campaigns against the Mongols", "link": "https://wikipedia.org/wiki/Yongle_Emperor%27s_campaigns_against_the_Mongols"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>mur"}, {"title": "Yongle Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1410", "text": "Ottoman Interregnum: <PERSON><PERSON><PERSON><PERSON> defeats his brother <PERSON> outside the Byzantine capital, Constantinople.", "html": "1410 - <a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>: <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi\" title=\"Süleyman Çelebi\"><PERSON>üleyman Çelebi</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Kosmidion\" title=\"Battle of Kosmidion\">defeats</a> his brother <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"Musa Çelebi\"><PERSON> Çelebi</a> outside the Byzantine capital, <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_Interregnum\" title=\"Ottoman Interregnum\">Ottoman Interregnum</a>: <a href=\"https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi\" title=\"Süleyman Çelebi\"><PERSON>üleyman Çelebi</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Kosmidion\" title=\"Battle of Kosmidion\">defeats</a> his brother <a href=\"https://wikipedia.org/wiki/Musa_%C3%87elebi\" title=\"Musa Çelebi\"><PERSON> Çelebi</a> outside the Byzantine capital, <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a>.", "links": [{"title": "Ottoman Interregnum", "link": "https://wikipedia.org/wiki/Ottoman_Interregnum"}, {"title": "Süleyman <PERSON>elebi", "link": "https://wikipedia.org/wiki/S%C3%BCleyman_%C3%87elebi"}, {"title": "Battle of Kosmidion", "link": "https://wikipedia.org/wiki/Battle_of_Kosmidion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Musa_%C3%87elebi"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}]}, {"year": "1502", "text": "<PERSON> lands on the island of Martinique on his fourth voyage.", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> lands on the island of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> on his fourth voyage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Christopher <PERSON>\"><PERSON></a> lands on the island of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> on his fourth voyage.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1520", "text": "Pope <PERSON> threatens to excommunicate <PERSON> in Exsurge <PERSON><PERSON>.", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> threatens to <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicate</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <i><a href=\"https://wikipedia.org/wiki/Exsurge_Domine\" title=\"Exsurge Domine\">Exsurge Domine</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> threatens to <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicate</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in <i><a href=\"https://wikipedia.org/wiki/Exsurge_Domine\" title=\"Exsurge Domine\">Exsurge Domine</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ex<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Exsurge_Domine"}]}, {"year": "1607", "text": "Virginia Colonists finished building James's Fort, to defend against Spanish and Indian attacks.", "html": "1607 - Virginia Colonists finished building <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">James's Fort</a>, to defend against Spanish and Indian attacks.", "no_year_html": "Virginia Colonists finished building <a href=\"https://wikipedia.org/wiki/Jamestown,_Virginia\" title=\"Jamestown, Virginia\">James's Fort</a>, to defend against Spanish and Indian attacks.", "links": [{"title": "Jamestown, Virginia", "link": "https://wikipedia.org/wiki/Jamestown,_Virginia"}]}, {"year": "1648", "text": "<PERSON> is hanged in Boston for witchcraft in the first such execution for the Massachusetts Bay Colony.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan_midwife)\" title=\"<PERSON> (Puritan midwife)\"><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a> for <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in the first such execution for the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan_midwife)\" title=\"<PERSON> (Puritan midwife)\"><PERSON></a> is hanged in <a href=\"https://wikipedia.org/wiki/Boston\" title=\"Boston\">Boston</a> for <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a> in the first such execution for the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a>.", "links": [{"title": "<PERSON> (Puritan midwife)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Puritan_midwife)"}, {"title": "Boston", "link": "https://wikipedia.org/wiki/Boston"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}, {"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}]}, {"year": "1667", "text": "The first human blood transfusion is administered by Dr. <PERSON><PERSON><PERSON>.", "html": "1667 - The first human <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusion</a> is administered by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "The first human <a href=\"https://wikipedia.org/wiki/Blood_transfusion\" title=\"Blood transfusion\">blood transfusion</a> is administered by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Blood transfusion", "link": "https://wikipedia.org/wiki/Blood_transfusion"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1670", "text": "The first stone of Fort Ricasoli is laid down in Malta.", "html": "1670 - The first stone of <a href=\"https://wikipedia.org/wiki/Fort_Ricasoli\" title=\"Fort Ricasoli\">Fort Ricasoli</a> is laid down in Malta.", "no_year_html": "The first stone of <a href=\"https://wikipedia.org/wiki/Fort_Ricasoli\" title=\"Fort Ricasoli\">Fort Ricasoli</a> is laid down in Malta.", "links": [{"title": "Fort Ricasoli", "link": "https://wikipedia.org/wiki/Fort_Ricasoli"}]}, {"year": "1752", "text": "<PERSON> proves that lightning is electricity (traditional date, the exact date is unknown).", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proves that <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> is <a href=\"https://wikipedia.org/wiki/Electricity\" title=\"Electricity\">electricity</a> (traditional date, the exact date is unknown).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proves that <a href=\"https://wikipedia.org/wiki/Lightning\" title=\"Lightning\">lightning</a> is <a href=\"https://wikipedia.org/wiki/Electricity\" title=\"Electricity\">electricity</a> (traditional date, the exact date is unknown).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lightning", "link": "https://wikipedia.org/wiki/Lightning"}, {"title": "Electricity", "link": "https://wikipedia.org/wiki/Electricity"}]}, {"year": "1776", "text": "Delaware Separation Day: Delaware votes to suspend government under the British Crown and separate officially from Pennsylvania.", "html": "1776 - Delaware Separation Day: <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> votes to suspend government under the British Crown and separate officially from <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "no_year_html": "Delaware Separation Day: <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> votes to suspend government under the British Crown and separate officially from <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a>.", "links": [{"title": "Delaware", "link": "https://wikipedia.org/wiki/Delaware"}, {"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}]}, {"year": "1800", "text": "The Provisional Army of the United States is dissolved.", "html": "1800 - The <a href=\"https://wikipedia.org/wiki/Provisional_Army_of_the_United_States\" title=\"Provisional Army of the United States\">Provisional Army of the United States</a> is dissolved.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Provisional_Army_of_the_United_States\" title=\"Provisional Army of the United States\">Provisional Army of the United States</a> is dissolved.", "links": [{"title": "Provisional Army of the United States", "link": "https://wikipedia.org/wiki/Provisional_Army_of_the_United_States"}]}, {"year": "1804", "text": "New Hampshire approves the Twelfth Amendment to the United States Constitution, ratifying the document.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> approves the <a href=\"https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution\" title=\"Twelfth Amendment to the United States Constitution\">Twelfth Amendment to the United States Constitution</a>, ratifying the document.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Hampshire\" title=\"New Hampshire\">New Hampshire</a> approves the <a href=\"https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution\" title=\"Twelfth Amendment to the United States Constitution\">Twelfth Amendment to the United States Constitution</a>, ratifying the document.", "links": [{"title": "New Hampshire", "link": "https://wikipedia.org/wiki/New_Hampshire"}, {"title": "Twelfth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1808", "text": "<PERSON> becomes King of Spain.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Spain\" class=\"mw-redirect\" title=\"King of Spain\">King of Spain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/King_of_Spain\" class=\"mw-redirect\" title=\"King of Spain\">King of Spain</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "King of Spain", "link": "https://wikipedia.org/wiki/King_of_Spain"}]}, {"year": "1834", "text": "The looting of Safed commences.", "html": "1834 - The <a href=\"https://wikipedia.org/wiki/1834_looting_of_Safed\" title=\"1834 looting of Safed\">looting of Safed</a> commences.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1834_looting_of_Safed\" title=\"1834 looting of Safed\">looting of Safed</a> commences.", "links": [{"title": "1834 looting of Safed", "link": "https://wikipedia.org/wiki/1834_looting_of_Safed"}]}, {"year": "1836", "text": "Arkansas is admitted as the 25th U.S. state.", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Arkansas\" title=\"Arkansas\">Arkansas</a> is admitted as the 25th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arkansas\" title=\"Arkansas\">Arkansas</a> is admitted as the 25th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Arkansas", "link": "https://wikipedia.org/wiki/Arkansas"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1844", "text": "<PERSON> receives a patent for vulcanization, a process to strengthen rubber.", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Vulcanization\" title=\"Vulcanization\">vulcanization</a>, a process to strengthen <a href=\"https://wikipedia.org/wiki/Rubber\" class=\"mw-redirect\" title=\"Rubber\">rubber</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> receives a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for <a href=\"https://wikipedia.org/wiki/Vulcanization\" title=\"Vulcanization\">vulcanization</a>, a process to strengthen <a href=\"https://wikipedia.org/wiki/Rubber\" class=\"mw-redirect\" title=\"Rubber\">rubber</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "Vulcanization", "link": "https://wikipedia.org/wiki/Vulcanization"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rubber"}]}, {"year": "1846", "text": "The Oregon Treaty extends the border between the United States and British North America, established by the Treaty of 1818, westward to the Pacific Ocean.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Oregon_Treaty\" title=\"Oregon Treaty\">Oregon Treaty</a> extends the border between the United States and <a href=\"https://wikipedia.org/wiki/British_North_America\" title=\"British North America\">British North America</a>, established by the <a href=\"https://wikipedia.org/wiki/Treaty_of_1818\" title=\"Treaty of 1818\">Treaty of 1818</a>, westward to the Pacific Ocean.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Oregon_Treaty\" title=\"Oregon Treaty\">Oregon Treaty</a> extends the border between the United States and <a href=\"https://wikipedia.org/wiki/British_North_America\" title=\"British North America\">British North America</a>, established by the <a href=\"https://wikipedia.org/wiki/Treaty_of_1818\" title=\"Treaty of 1818\">Treaty of 1818</a>, westward to the Pacific Ocean.", "links": [{"title": "Oregon Treaty", "link": "https://wikipedia.org/wiki/Oregon_Treaty"}, {"title": "British North America", "link": "https://wikipedia.org/wiki/British_North_America"}, {"title": "Treaty of 1818", "link": "https://wikipedia.org/wiki/Treaty_of_1818"}]}, {"year": "1859", "text": "Ambiguity in the Oregon Treaty leads to the \"Northwestern Boundary Dispute\" between American and British/Canadian settlers.", "html": "1859 - Ambiguity in the Oregon Treaty leads to the \"<a href=\"https://wikipedia.org/wiki/Pig_War_(1859)\" title=\"Pig War (1859)\">Northwestern Boundary Dispute</a>\" between American and British/Canadian settlers.", "no_year_html": "Ambiguity in the Oregon Treaty leads to the \"<a href=\"https://wikipedia.org/wiki/Pig_War_(1859)\" title=\"Pig War (1859)\">Northwestern Boundary Dispute</a>\" between American and British/Canadian settlers.", "links": [{"title": "Pig War (1859)", "link": "https://wikipedia.org/wiki/Pig_War_(1859)"}]}, {"year": "1864", "text": "American Civil War: The Second Battle of Petersburg begins.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Petersburg\" title=\"Second Battle of Petersburg\">Second Battle of Petersburg</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Petersburg\" title=\"Second Battle of Petersburg\">Second Battle of Petersburg</a> begins.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Second Battle of Petersburg", "link": "https://wikipedia.org/wiki/Second_Battle_of_Petersburg"}]}, {"year": "1864", "text": "Arlington National Cemetery is established when 200 acres (0.81 km2) of the Arlington estate (formerly owned by the family of Confederate General <PERSON>) are officially set aside as a military cemetery by U.S. Secretary of War <PERSON>.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a> is established when 200 acres (0.81 km) of the Arlington estate (formerly owned by the family of Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>) are officially set aside as a military <a href=\"https://wikipedia.org/wiki/Cemetery\" title=\"Cemetery\">cemetery</a> by <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">U.S. Secretary of War</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arlington_National_Cemetery\" title=\"Arlington National Cemetery\">Arlington National Cemetery</a> is established when 200 acres (0.81 km) of the Arlington estate (formerly owned by the family of Confederate General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>) are officially set aside as a military <a href=\"https://wikipedia.org/wiki/Cemetery\" title=\"Cemetery\">cemetery</a> by <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">U.S. Secretary of War</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Edwin <PERSON>\"><PERSON></a>.", "links": [{"title": "Arlington National Cemetery", "link": "https://wikipedia.org/wiki/Arlington_National_Cemetery"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Cemetery", "link": "https://wikipedia.org/wiki/Cemetery"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON> becomes the first African American cadet to graduate from the United States Military Academy.", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lipper\" title=\"<PERSON>lipper\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> cadet to graduate from the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lipper\" title=\"<PERSON> Flipper\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/African_American\" class=\"mw-redirect\" title=\"African American\">African American</a> cadet to graduate from the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "African American", "link": "https://wikipedia.org/wiki/African_American"}, {"title": "United States Military Academy", "link": "https://wikipedia.org/wiki/United_States_Military_Academy"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON><PERSON> takes a series of photographs to prove that all four feet of a horse leave the ground when it runs; the study becomes the basis of motion pictures.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> takes <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_at_a_Gallop\" class=\"mw-redirect\" title=\"<PERSON><PERSON> at a Gallop\">a series of photographs</a> to prove that all four feet of a horse leave the ground when it runs; the study becomes the basis of <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion pictures</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> takes <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_at_a_Gallop\" class=\"mw-redirect\" title=\"<PERSON><PERSON> at a Gallop\">a series of photographs</a> to prove that all four feet of a horse leave the ground when it runs; the study becomes the basis of <a href=\"https://wikipedia.org/wiki/Film\" title=\"Film\">motion pictures</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> at a Gallop", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_at_a_Gallop"}, {"title": "Film", "link": "https://wikipedia.org/wiki/Film"}]}, {"year": "1888", "text": "Crown Prince <PERSON> becomes Kaiser <PERSON>; he will be the last Emperor of the German Empire. Due to the death of his predecessors <PERSON> and <PERSON>, 1888 is the Year of the Three Emperors.", "html": "1888 - Crown Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\">Wilhelm</a> becomes Kaiser Wilhelm II; he will be the last Emperor of the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>. Due to the death of his predecessors <a href=\"https://wikipedia.org/wiki/William_I,_German_Emperor\" title=\"William I, German Emperor\"><PERSON> I</a> and <a href=\"https://wikipedia.org/wiki/Frederick_III,_German_Emperor\" title=\"Frederick III, German Emperor\"><PERSON> III</a>, 1888 is the <a href=\"https://wikipedia.org/wiki/Year_of_the_Three_Emperors\" title=\"Year of the Three Emperors\">Year of the Three Emperors</a>.", "no_year_html": "Crown Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_German_Emperor\" class=\"mw-redirect\" title=\"Wilhelm II, German Emperor\"><PERSON></a> becomes Kaiser Wilhelm II; he will be the last Emperor of the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German Empire</a>. Due to the death of his predecessors <a href=\"https://wikipedia.org/wiki/William_I,_German_Emperor\" title=\"William I, German Emperor\"><PERSON> I</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"Frederick III, German Emperor\"><PERSON> III</a>, 1888 is the <a href=\"https://wikipedia.org/wiki/Year_of_the_Three_Emperors\" title=\"Year of the Three Emperors\">Year of the Three Emperors</a>.", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}, {"title": "Year of the Three Emperors", "link": "https://wikipedia.org/wiki/Year_of_the_Three_Emperors"}]}, {"year": "1896", "text": "One of the deadliest tsunamis in Japan's history kills more than 22,000 people.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/1896_Sanriku_earthquake\" title=\"1896 Sanriku earthquake\">One of the deadliest tsunamis</a> in Japan's history kills more than 22,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1896_Sanriku_earthquake\" title=\"1896 Sanriku earthquake\">One of the deadliest tsunamis</a> in Japan's history kills more than 22,000 people.", "links": [{"title": "1896 Sanriku earthquake", "link": "https://wikipedia.org/wiki/1896_Sanriku_earthquake"}]}, {"year": "1904", "text": "A fire aboard the steamboat SS <PERSON> in New York City's East River kills 1,000.", "html": "1904 - A fire aboard the steamboat <a href=\"https://wikipedia.org/wiki/PS_General_<PERSON>\" title=\"PS <PERSON>\">SS <i>General <PERSON></i></a> in New York City's <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a> kills 1,000.", "no_year_html": "A fire aboard the steamboat <a href=\"https://wikipedia.org/wiki/PS_General_<PERSON>\" title=\"PS General <PERSON>\">SS <i>General <PERSON></i></a> in New York City's <a href=\"https://wikipedia.org/wiki/East_River\" title=\"East River\">East River</a> kills 1,000.", "links": [{"title": "PS <PERSON>", "link": "https://wikipedia.org/wiki/PS_General_<PERSON>"}, {"title": "East River", "link": "https://wikipedia.org/wiki/East_River"}]}, {"year": "1916", "text": "United States President <PERSON> signs a bill incorporating the Boy Scouts of America, making them the only American youth organization with a federal charter.", "html": "1916 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill incorporating the <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a>, making them the only American youth organization with a federal charter.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs a bill incorporating the <a href=\"https://wikipedia.org/wiki/Boy_Scouts_of_America\" class=\"mw-redirect\" title=\"Boy Scouts of America\">Boy Scouts of America</a>, making them the only American youth organization with a federal charter.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Boy Scouts of America", "link": "https://wikipedia.org/wiki/Boy_Scouts_of_America"}]}, {"year": "1919", "text": "<PERSON> and <PERSON> complete the first nonstop transatlantic flight when they reach Clifden, County Galway, Ireland.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" class=\"mw-redirect\" title=\"<PERSON> (aviator)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(aviator)\" class=\"mw-redirect\" title=\"<PERSON> (aviator)\"><PERSON></a> complete the first nonstop <a href=\"https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>\" title=\"Transatlantic flight of <PERSON><PERSON> and <PERSON>\">transatlantic flight</a> when they reach <a href=\"https://wikipedia.org/wiki/Clifden\" title=\"Clifden\">C<PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/County_Galway\" title=\"County Galway\">County Galway</a>, Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)\" class=\"mw-redirect\" title=\"<PERSON> (aviator)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>(aviator)\" class=\"mw-redirect\" title=\"<PERSON> (aviator)\"><PERSON></a> complete the first nonstop <a href=\"https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>\" title=\"Transatlantic flight of <PERSON><PERSON> and Brown\">transatlantic flight</a> when they reach <a href=\"https://wikipedia.org/wiki/Clifden\" title=\"Clifden\">C<PERSON><PERSON>den</a>, <a href=\"https://wikipedia.org/wiki/County_Galway\" title=\"County Galway\">County Galway</a>, Ireland.", "links": [{"title": "<PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)"}, {"title": "<PERSON> (aviator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(aviator)"}, {"title": "Transatlantic flight of <PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Transatlantic_flight_of_<PERSON><PERSON>_and_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Clifden"}, {"title": "County Galway", "link": "https://wikipedia.org/wiki/County_Galway"}]}, {"year": "1920", "text": "Following the 1920 Schleswig plebiscites, Northern Schleswig is transferred from Germany to Denmark.", "html": "1920 - Following the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>, Northern Schleswig is transferred from Germany to Denmark.", "no_year_html": "Following the <a href=\"https://wikipedia.org/wiki/1920_Schleswig_plebiscites\" title=\"1920 Schleswig plebiscites\">1920 Schleswig plebiscites</a>, Northern Schleswig is transferred from Germany to Denmark.", "links": [{"title": "1920 Schleswig plebiscites", "link": "https://wikipedia.org/wiki/1920_Schleswig_plebiscites"}]}, {"year": "1921", "text": "<PERSON><PERSON> earns her pilot's license, becoming the first female pilot of African-American descent.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> earns her pilot's license, becoming the first female pilot of African-American descent.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> earns her pilot's license, becoming the first female pilot of African-American descent.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "The United States Great Smoky Mountains National Park is founded.", "html": "1934 - The United States <a href=\"https://wikipedia.org/wiki/Great_Smoky_Mountains_National_Park\" title=\"Great Smoky Mountains National Park\">Great Smoky Mountains National Park</a> is founded.", "no_year_html": "The United States <a href=\"https://wikipedia.org/wiki/Great_Smoky_Mountains_National_Park\" title=\"Great Smoky Mountains National Park\">Great Smoky Mountains National Park</a> is founded.", "links": [{"title": "Great Smoky Mountains National Park", "link": "https://wikipedia.org/wiki/Great_Smoky_Mountains_National_Park"}]}, {"year": "1936", "text": "First flight of the Vickers Wellington bomber.", "html": "1936 - First flight of the <a href=\"https://wikipedia.org/wiki/Vickers_Wellington\" title=\"Vickers Wellington\">Vickers Wellington</a> bomber.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Vickers_Wellington\" title=\"Vickers Wellington\">Vickers Wellington</a> bomber.", "links": [{"title": "Vickers Wellington", "link": "https://wikipedia.org/wiki/Vickers_Wellington"}]}, {"year": "1937", "text": "A German expedition led by <PERSON> loses sixteen members in an avalanche on Nanga Parbat. It is the worst single disaster to occur on an 8000m peak.", "html": "1937 - A German expedition led by <a href=\"https://wikipedia.org/wiki/Karl_Wien\" title=\"Karl Wien\"><PERSON></a> loses sixteen members in an <a href=\"https://wikipedia.org/wiki/Avalanche\" title=\"Avalanche\">avalanche</a> on <a href=\"https://wikipedia.org/wiki/Nanga_Parbat\" title=\"Nanga Parbat\">Nanga Parbat</a>. It is the worst single disaster to occur on an <a href=\"https://wikipedia.org/wiki/Eight-thousander\" title=\"Eight-thousander\">8000m peak</a>.", "no_year_html": "A German expedition led by <a href=\"https://wikipedia.org/wiki/Karl_Wien\" title=\"Karl Wien\">Karl Wien</a> loses sixteen members in an <a href=\"https://wikipedia.org/wiki/Avalanche\" title=\"Avalanche\">avalanche</a> on <a href=\"https://wikipedia.org/wiki/Nanga_Parbat\" title=\"Nanga Parbat\">Nanga Parbat</a>. It is the worst single disaster to occur on an <a href=\"https://wikipedia.org/wiki/Eight-thousander\" title=\"Eight-thousander\">8000m peak</a>.", "links": [{"title": "Karl Wien", "link": "https://wikipedia.org/wiki/Karl_Wien"}, {"title": "Avalanche", "link": "https://wikipedia.org/wiki/Avalanche"}, {"title": "Nanga Parbat", "link": "https://wikipedia.org/wiki/Nanga_Parbat"}, {"title": "Eight-thousander", "link": "https://wikipedia.org/wiki/Eight-thousander"}]}, {"year": "1940", "text": "World War II: Operation Aerial begins: Allied troops start to evacuate France, following Germany's takeover of Paris and most of the nation.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Aerial\" title=\"Operation Aerial\">Operation Aerial</a> begins: Allied troops start to evacuate France, following Germany's takeover of Paris and most of the nation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Operation_Aerial\" title=\"Operation Aerial\">Operation Aerial</a> begins: Allied troops start to evacuate France, following Germany's takeover of Paris and most of the nation.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Operation Aerial", "link": "https://wikipedia.org/wiki/Operation_Aerial"}]}, {"year": "1944", "text": "World War II: The United States invades Saipan, capital of Japan's South Seas Mandate.", "html": "1944 - World War II: The United States <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">invades Saipan</a>, capital of Japan's <a href=\"https://wikipedia.org/wiki/South_Seas_Mandate\" title=\"South Seas Mandate\">South Seas Mandate</a>.", "no_year_html": "World War II: The United States <a href=\"https://wikipedia.org/wiki/Battle_of_Saipan\" title=\"Battle of Saipan\">invades Saipan</a>, capital of Japan's <a href=\"https://wikipedia.org/wiki/South_Seas_Mandate\" title=\"South Seas Mandate\">South Seas Mandate</a>.", "links": [{"title": "Battle of Saipan", "link": "https://wikipedia.org/wiki/Battle_of_Saipan"}, {"title": "South Seas Mandate", "link": "https://wikipedia.org/wiki/South_Seas_Mandate"}]}, {"year": "1944", "text": "In the Saskatchewan general election, the CCF, led by <PERSON>, is elected and forms the first socialist government in North America.", "html": "1944 - In the <a href=\"https://wikipedia.org/wiki/1944_Saskatchewan_general_election\" title=\"1944 Saskatchewan general election\">Saskatchewan general election</a>, the <a href=\"https://wikipedia.org/wiki/Co-operative_Commonwealth_Federation\" title=\"Co-operative Commonwealth Federation\">CCF</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is elected and forms the first <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">socialist</a> government in North America.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/1944_Saskatchewan_general_election\" title=\"1944 Saskatchewan general election\">Saskatchewan general election</a>, the <a href=\"https://wikipedia.org/wiki/Co-operative_Commonwealth_Federation\" title=\"Co-operative Commonwealth Federation\">CCF</a>, led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is elected and forms the first <a href=\"https://wikipedia.org/wiki/Socialism\" title=\"Socialism\">socialist</a> government in North America.", "links": [{"title": "1944 Saskatchewan general election", "link": "https://wikipedia.org/wiki/1944_Saskatchewan_general_election"}, {"title": "Co-operative Commonwealth Federation", "link": "https://wikipedia.org/wiki/Co-operative_Commonwealth_Federation"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Socialism", "link": "https://wikipedia.org/wiki/Socialism"}]}, {"year": "1970", "text": "<PERSON> goes on trial for the <PERSON> murders.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> murders.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> goes on trial for the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> murders.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "Red Army Faction co-founder <PERSON><PERSON><PERSON><PERSON> is captured by police in Langenhagen.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> co-founder <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is captured by police in <a href=\"https://wikipedia.org/wiki/Langenhagen\" title=\"Langenhagen\">Langenhagen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> co-founder <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is captured by police in <a href=\"https://wikipedia.org/wiki/Langenhagen\" title=\"Langenhagen\">Langenhagen</a>.", "links": [{"title": "Red Army Faction", "link": "https://wikipedia.org/wiki/Red_Army_Faction"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Langenhagen", "link": "https://wikipedia.org/wiki/Langenhagen"}]}, {"year": "1972", "text": "Cathay Pacific Flight 700Z is destroyed by a bomb over Pleiku, Vietnam (then South Vietnam) and kills 81 people.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Cathay_Pacific_Flight_700Z\" title=\"Cathay Pacific Flight 700Z\">Cathay Pacific Flight 700Z</a> is destroyed by a bomb over <a href=\"https://wikipedia.org/wiki/Pleiku\" title=\"Pleiku\">Pleiku</a>, <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> (then <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>) and kills 81 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cathay_Pacific_Flight_700Z\" title=\"Cathay Pacific Flight 700Z\">Cathay Pacific Flight 700Z</a> is destroyed by a bomb over <a href=\"https://wikipedia.org/wiki/Pleiku\" title=\"Pleiku\">Pleiku</a>, <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> (then <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>) and kills 81 people.", "links": [{"title": "Cathay Pacific Flight 700Z", "link": "https://wikipedia.org/wiki/Cathay_Pacific_Flight_700Z"}, {"title": "Pleik<PERSON>", "link": "https://wikipedia.org/wiki/Pleiku"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1977", "text": "After the death of dictator <PERSON> in 1975, the first democratic elections take place in Spain.", "html": "1977 - After the death of dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in 1975, the first democratic <a href=\"https://wikipedia.org/wiki/1977_Spanish_general_election\" title=\"1977 Spanish general election\">elections</a> take place in <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "no_year_html": "After the death of dictator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in 1975, the first democratic <a href=\"https://wikipedia.org/wiki/1977_Spanish_general_election\" title=\"1977 Spanish general election\">elections</a> take place in <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Franco"}, {"title": "1977 Spanish general election", "link": "https://wikipedia.org/wiki/1977_Spanish_general_election"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "1978", "text": "<PERSON> <PERSON> of Jordan marries Jordanian-American <PERSON>, who takes the name <PERSON>.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\">King <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\"><PERSON></a> marries Jordanian-American <PERSON>, who takes the name <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Jordan\" title=\"Queen <PERSON>or of Jordan\">Queen <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\">King <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\"><PERSON></a> marries Jordanian-American <PERSON>, who takes the name <a href=\"https://wikipedia.org/wiki/Queen_<PERSON>or_of_Jordan\" title=\"Queen <PERSON>or of Jordan\">Queen <PERSON><PERSON></a>.", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Queen <PERSON><PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Queen_<PERSON><PERSON>_of_Jordan"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>'s painting <PERSON><PERSON> is attacked by a man (later judged insane) who throws sulfuric acid on the canvas and cuts it twice with a knife.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/Dana%C3%AB_(Re<PERSON><PERSON><PERSON>_painting)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Remb<PERSON><PERSON> painting)\"><PERSON><PERSON></a></i> is attacked by a man (later judged insane) who throws <a href=\"https://wikipedia.org/wiki/Sulfuric_acid\" title=\"Sulfuric acid\">sulfuric acid</a> on the canvas and cuts it twice with a knife.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>'s painting <i><a href=\"https://wikipedia.org/wiki/Dana%C3%AB_(Rembrandt_painting)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (Rembrand<PERSON> painting)\"><PERSON><PERSON></a></i> is attacked by a man (later judged insane) who throws <a href=\"https://wikipedia.org/wiki/Sulfuric_acid\" title=\"Sulfuric acid\">sulfuric acid</a> on the canvas and cuts it twice with a knife.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>t"}, {"title": "<PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON> painting)", "link": "https://wikipedia.org/wiki/Dana%C3%AB_(<PERSON><PERSON><PERSON><PERSON>_painting)"}, {"title": "Sulfuric acid", "link": "https://wikipedia.org/wiki/Sulfuric_acid"}]}, {"year": "1991", "text": "In the Philippines, Mount Pinatubo erupts in the second largest volcanic eruption of the 20th century, killing over 800 people.", "html": "1991 - In the Philippines, <a href=\"https://wikipedia.org/wiki/Mount_Pinatubo\" title=\"Mount Pinatubo\">Mount Pinatubo</a> erupts in the <a href=\"https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo\" title=\"1991 eruption of Mount Pinatubo\">second largest volcanic eruption of the 20th century</a>, killing over 800 people.", "no_year_html": "In the Philippines, <a href=\"https://wikipedia.org/wiki/Mount_Pinatubo\" title=\"Mount Pinatubo\">Mount Pinatubo</a> erupts in the <a href=\"https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo\" title=\"1991 eruption of Mount Pinatubo\">second largest volcanic eruption of the 20th century</a>, killing over 800 people.", "links": [{"title": "Mount Pinatubo", "link": "https://wikipedia.org/wiki/Mount_Pinatubo"}, {"title": "1991 eruption of Mount Pinatubo", "link": "https://wikipedia.org/wiki/1991_eruption_of_Mount_Pinatubo"}]}, {"year": "1992", "text": "The United States Supreme Court rules in United States v<PERSON> <PERSON><PERSON> that it is permissible for the United States to forcibly extradite suspects in foreign countries and bring them to the United States for trial, without approval from those other countries.", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/United_States_v<PERSON>_<PERSON>\" title=\"United States v<PERSON> <PERSON>\">United States v<PERSON></a></i> that it is permissible for the United States to forcibly extradite suspects in foreign countries and bring them to the United States for trial, without approval from those other countries.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> rules in <i><a href=\"https://wikipedia.org/wiki/United_States_v<PERSON>_<PERSON>\" title=\"United States v<PERSON> <PERSON>\">United States v<PERSON></a></i> that it is permissible for the United States to forcibly extradite suspects in foreign countries and bring them to the United States for trial, without approval from those other countries.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "United States v. <PERSON>", "link": "https://wikipedia.org/wiki/United_States_v._<PERSON>"}]}, {"year": "1996", "text": "The Troubles: The Provisional Irish Republican Army (IRA) detonates a powerful truck bomb in the middle of Manchester, England, devastating the city centre and injuring 200 people.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> (IRA) detonates <a href=\"https://wikipedia.org/wiki/1996_Manchester_bombing\" title=\"1996 Manchester bombing\">a powerful truck bomb</a> in the middle of Manchester, England, devastating the <a href=\"https://wikipedia.org/wiki/Manchester_city_centre\" title=\"Manchester city centre\">city centre</a> and injuring 200 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> (IRA) detonates <a href=\"https://wikipedia.org/wiki/1996_Manchester_bombing\" title=\"1996 Manchester bombing\">a powerful truck bomb</a> in the middle of Manchester, England, devastating the <a href=\"https://wikipedia.org/wiki/Manchester_city_centre\" title=\"Manchester city centre\">city centre</a> and injuring 200 people.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "1996 Manchester bombing", "link": "https://wikipedia.org/wiki/1996_Manchester_bombing"}, {"title": "Manchester city centre", "link": "https://wikipedia.org/wiki/Manchester_city_centre"}]}, {"year": "2001", "text": "Leaders of China, Russia, Kazakhstan, Kyrgyzstan, Tajikistan and Uzbekistan formed the Shanghai Cooperation Organisation.", "html": "2001 - Leaders of China, Russia, Kazakhstan, Kyrgyzstan, Tajikistan and Uzbekistan formed the <a href=\"https://wikipedia.org/wiki/Shanghai_Cooperation_Organisation\" title=\"Shanghai Cooperation Organisation\">Shanghai Cooperation Organisation</a>.", "no_year_html": "Leaders of China, Russia, Kazakhstan, Kyrgyzstan, Tajikistan and Uzbekistan formed the <a href=\"https://wikipedia.org/wiki/Shanghai_Cooperation_Organisation\" title=\"Shanghai Cooperation Organisation\">Shanghai Cooperation Organisation</a>.", "links": [{"title": "Shanghai Cooperation Organisation", "link": "https://wikipedia.org/wiki/Shanghai_Cooperation_Organisation"}]}, {"year": "2007", "text": "The Nokkakivi Amusement Park is opened in Lievestuore, Laukaa, Finland.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Nokkakivi\" title=\"Nokkakivi\">Nokkakivi Amusement Park</a> is opened in <a href=\"https://wikipedia.org/wiki/Lievestuore\" title=\"Lievestuore\">Lievestuore</a>, <a href=\"https://wikipedia.org/wiki/Laukaa\" title=\"Lauka<PERSON>\">Lau<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Nokkakivi\" title=\"Nokkakivi\">Nokkakivi Amusement Park</a> is opened in <a href=\"https://wikipedia.org/wiki/Lievestuore\" title=\"Lievestuore\">Lievestuore</a>, <a href=\"https://wikipedia.org/wiki/Laukaa\" title=\"Laukaa\">Lauka<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nokkakivi"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lievestuore"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Laukaa"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "2012", "text": "<PERSON> becomes the first person to successfully tightrope walk directly over Niagara Falls.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to successfully <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope walk</a> directly over <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to successfully <a href=\"https://wikipedia.org/wiki/Tightrope_walking\" title=\"Tightrope walking\">tightrope walk</a> directly over <a href=\"https://wikipedia.org/wiki/Niagara_Falls\" title=\"Niagara Falls\">Niagara Falls</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tightrope walking", "link": "https://wikipedia.org/wiki/Tightrope_walking"}, {"title": "Niagara Falls", "link": "https://wikipedia.org/wiki/Niagara_Falls"}]}, {"year": "2013", "text": "A bomb explodes on a bus in the Pakistani city of Quetta, killing at least 25 people and wounding 22 others.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/June_2013_Quetta_attacks\" title=\"June 2013 Quetta attacks\">bomb explodes</a> on a bus in the <a href=\"https://wikipedia.org/wiki/Pakistani\" class=\"mw-redirect\" title=\"Pakistani\">Pakistani</a> city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a>, killing at least 25 people and wounding 22 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/June_2013_Quetta_attacks\" title=\"June 2013 Quetta attacks\">bomb explodes</a> on a bus in the <a href=\"https://wikipedia.org/wiki/Pakistani\" class=\"mw-redirect\" title=\"Pakistani\">Pakistani</a> city of <a href=\"https://wikipedia.org/wiki/Quetta\" title=\"Quetta\">Quetta</a>, killing at least 25 people and wounding 22 others.", "links": [{"title": "June 2013 Quetta attacks", "link": "https://wikipedia.org/wiki/June_2013_Quetta_attacks"}, {"title": "Pakistani", "link": "https://wikipedia.org/wiki/Pakistani"}, {"title": "Quetta", "link": "https://wikipedia.org/wiki/Quetta"}]}, {"year": "2022", "text": "Microsoft retires its ubiquitous Internet Explorer after 26 years in favor of its new browser, Microsoft Edge.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> retires its ubiquitous <a href=\"https://wikipedia.org/wiki/Internet_Explorer\" title=\"Internet Explorer\">Internet Explorer</a> after 26 years in favor of its new browser, <a href=\"https://wikipedia.org/wiki/Microsoft_Edge\" title=\"Microsoft Edge\">Microsoft Edge</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a> retires its ubiquitous <a href=\"https://wikipedia.org/wiki/Internet_Explorer\" title=\"Internet Explorer\">Internet Explorer</a> after 26 years in favor of its new browser, <a href=\"https://wikipedia.org/wiki/Microsoft_Edge\" title=\"Microsoft Edge\">Microsoft Edge</a>.", "links": [{"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}, {"title": "Internet Explorer", "link": "https://wikipedia.org/wiki/Internet_Explorer"}, {"title": "Microsoft Edge", "link": "https://wikipedia.org/wiki/Microsoft_Edge"}]}], "Births": [{"year": "1330", "text": "<PERSON>, the Black Prince of England (d. 1376)", "html": "1330 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_the_Black_Prince\" class=\"mw-redirect\" title=\"<PERSON>, the Black Prince\"><PERSON>, the Black Prince</a> of England (d. 1376)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_the_Black_Prince\" class=\"mw-redirect\" title=\"<PERSON>, the Black Prince\"><PERSON>, the Black Prince</a> of England (d. 1376)", "links": [{"title": "<PERSON>, the Black Prince", "link": "https://wikipedia.org/wiki/<PERSON>,_the_<PERSON>_<PERSON>"}]}, {"year": "1479", "text": "<PERSON>, Italian model, subject of the <PERSON> (d. 1542)", "html": "1479 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>do\"><PERSON></a>, Italian model, subject of the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (d. 1542)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> G<PERSON>condo\"><PERSON></a>, Italian model, subject of the <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a></i> (d. 1542)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, 1st Duke of Richmond and Somerset, English politician, Lord Lieutenant of Ireland (d. 1536)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Richmond and Somerset\"><PERSON>, 1st Duke of Richmond and Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Richmond and Somerset\"><PERSON>, 1st Duke of Richmond and Somerset</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (d. 1536)", "links": [{"title": "<PERSON>, 1st Duke of Richmond and Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Richmond_and_Somerset"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1542", "text": "<PERSON>, English captain and explorer (d. 1591)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and explorer (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and explorer (d. 1591)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1549", "text": "<PERSON>, English noblewoman (d. 1605)", "html": "1549 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (d. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (d. 1605)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1553", "text": "Arch<PERSON><PERSON> of Austria (d. 1595)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Archduke_<PERSON>_of_Austria\" title=\"Archduke <PERSON> of Austria\">Archdu<PERSON> of Austria</a> (d. 1595)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arch<PERSON><PERSON>_<PERSON>_of_Austria\" title=\"Archdu<PERSON> of Austria\">Arch<PERSON><PERSON> of Austria</a> (d. 1595)", "links": [{"title": "Arch<PERSON><PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1605", "text": "<PERSON>, English poet and playwright (d. 1635)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (d. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, English poet and playwright (d. 1635)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(poet)"}]}, {"year": "1623", "text": "<PERSON><PERSON><PERSON>, Dutch politician (d. 1672)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>itt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch politician (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>itt\" title=\"<PERSON><PERSON><PERSON>itt\"><PERSON><PERSON><PERSON></a>, Dutch politician (d. 1672)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>itt"}]}, {"year": "1624", "text": "<PERSON><PERSON>, German orientalist and philologist (d. 1704)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German orientalist and philologist (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German orientalist and philologist (d. 1704)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, French mathematician and theologian (d. 1715)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theologian (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theologian (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, 1st Earl of Godolphin, English politician (d. 1712)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Godolphin\" title=\"<PERSON>, 1st Earl of Godolphin\"><PERSON>, 1st Earl of Godolphin</a>, English politician (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Godolphin\" title=\"<PERSON>, 1st Earl of Godolphin\"><PERSON>, 1st Earl of Godolphin</a>, English politician (d. 1712)", "links": [{"title": "<PERSON>, 1st Earl of Godolphin", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Godolphin"}]}, {"year": "1749", "text": "<PERSON>, German organist, composer, and theorist (d. 1814)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist, composer, and theorist (d. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German organist, composer, and theorist (d. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1754", "text": "<PERSON>, Spanish chemist and mineralogist (d. 1796)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish chemist and mineralogist (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish chemist and mineralogist (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1755", "text": "<PERSON>, comte <PERSON>, French chemist and entomologist (d. 1809)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON>, comte <PERSON></a>, French chemist and entomologist (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_comte_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, comte <PERSON>\"><PERSON>, comte <PERSON></a>, French chemist and entomologist (d. 1809)", "links": [{"title": "<PERSON>, comte de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, German cellist, composer, and conductor (d. 1826)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist, composer, and conductor (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cellist, composer, and conductor (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON><PERSON>, Japanese priest and poet (d. 1827)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese priest and poet (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese priest and poet (d. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON>, English orientalist (d. 1837)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English orientalist (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1767", "text": "<PERSON>, American wife of <PERSON> (d. 1828)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, Welsh physician and academic (d. 1841)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh physician and academic (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh physician and academic (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, American minister, author, and activist (d. 1883)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, author, and activist (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister, author, and activist (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Swiss chocolatier (d. 1874)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss chocolatier (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles-Am%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Swiss chocolatier (d. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Charles-Am%C3%A9d%C3%A9e_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Scottish-Australian colonel and explorer (d. 1855)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Scottish-Australian colonel and explorer (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a>, Scottish-Australian colonel and explorer (d. 1855)", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_(explorer)"}]}, {"year": "1801", "text": "<PERSON>, American merchant and politician, 3rd Mayor of Chicago (d. 1883)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American merchant and politician, 3rd <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1805", "text": "<PERSON>, American businessman and politician, 1st Mayor of Chicago (d. 1877)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 1st <a href=\"https://wikipedia.org/wiki/Mayor_of_Chicago\" title=\"Mayor of Chicago\">Mayor of Chicago</a> (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Chicago", "link": "https://wikipedia.org/wiki/Mayor_of_Chicago"}]}, {"year": "1809", "text": "<PERSON><PERSON><PERSON>, Canadian poet and historian (d. 1866)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian poet and historian (d. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian poet and historian (d. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, Italian anatomist (d. 1876)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian anatomist (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1835", "text": "<PERSON><PERSON>, American actress, painter, and poet (d. 1868)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American actress, painter, and poet (d. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, painter, and poet (d. 1868)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON>, Norwegian pianist and composer (d. 1907)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian pianist and composer (d. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala, Indian bishop and saint (d. 1902)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Parumala\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala</a>, Indian bishop and saint (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_of_Parumala\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala</a>, Indian bishop and saint (d. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Parumala", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_of_Parumala"}]}, {"year": "1872", "text": "<PERSON>, English swimmer and water polo player (d. 1950)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English swimmer and water polo player (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English swimmer and water polo player (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Norwegian-Canadian skier (d. 1987)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Canadian skier (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-Canadian skier (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Indian-American golfer (d. 1955)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American golfer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-American golfer (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Japanese lieutenant general in the Imperial Japanese Army (d. 1945)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lieutenant general in the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese lieutenant general in the <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Army\" title=\"Imperial Japanese Army\">Imperial Japanese Army</a> (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Imperial Japanese Army", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Army"}]}, {"year": "1884", "text": "<PERSON>, American actor, director, and screenwriter (d. 1944)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, British racing driver (d. 1970)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver (d. 1970)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(racing_driver)"}]}, {"year": "1888", "text": "<PERSON>, English Jesuit priest (d. 1976)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, English Jesuit priest (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Arcy\" title=\"<PERSON>\"><PERSON></a>, English Jesuit priest (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martin_D%27Arcy"}]}, {"year": "1888", "text": "<PERSON>, Mexican poet and author (d. 1921)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_L%C3%B3pez_Velarde"}]}, {"year": "1890", "text": "<PERSON>, German oceanographer and academic (d. 1977)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Georg_W%C3%BCst\" title=\"<PERSON>\"><PERSON></a>, German oceanographer and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Georg_W%C3%BCst\" title=\"<PERSON>\"><PERSON></a>, German oceanographer and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Georg_W%C3%BCst"}]}, {"year": "1894", "text": "<PERSON>, American composer and conductor (d. 1981)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Ukrainian-Russian mathematician and theorist (d. 1947)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and theorist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and theorist (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, German-American physiologist and academic (d. 1986)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American physiologist and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American physiologist and academic (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hold"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, German philosopher and academic (d. 1984)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Gotthard_G%C3%BCnther\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and academic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gotthard_G%C3%BCnther\" title=\"<PERSON><PERSON><PERSON> G<PERSON>\"><PERSON><PERSON><PERSON></a>, German philosopher and academic (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gotthard_G%C3%BCnther"}]}, {"year": "1900", "text": "<PERSON>, German-American composer and conductor (d. 1996)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and conductor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American composer and conductor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Russian-Estonian architect (d. 1963)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Elmar_Lohk\" title=\"Elmar Lohk\"><PERSON><PERSON></a>, Russian-Estonian architect (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elmar_Lo<PERSON>k\" title=\"<PERSON><PERSON> Lohk\"><PERSON><PERSON></a>, Russian-Estonian architect (d. 1963)", "links": [{"title": "Elmar <PERSON>", "link": "https://wikipedia.org/wiki/Elmar_<PERSON>k"}]}, {"year": "1902", "text": "<PERSON>, German-American psychologist and psychoanalyst (d. 1994)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and psychoanalyst (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and psychoanalyst (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English-American mathematician and author (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American mathematician and author (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, Belgian SS officer (d. 1994)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/L%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_<PERSON><PERSON>e"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1907", "text": "<PERSON>, English actor and educator (d. 1975)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and educator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and educator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Greek-American soprano and educator (d. 2002)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American soprano and educator (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American soprano and educator (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, English-American pianist, composer, and conductor (d. 1990)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English-American pianist, composer, and conductor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English-American pianist, composer, and conductor (d. 1990)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, English author, created The Railway Series, the basis for <PERSON> the Tank Engine (d. 1997)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>wdry\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English author, created <a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a>, the basis for <a href=\"https://wikipedia.org/wiki/Thomas_the_Tank_Engine\" title=\"Thomas the Tank Engine\">Thomas the Tank Engine</a> (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>wdr<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English author, created <a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a>, the basis for <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Tank_Engine\" title=\"Thomas the Tank Engine\">Thomas the Tank Engine</a> (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>dry"}, {"title": "The Railway Series", "link": "https://wikipedia.org/wiki/The_Railway_Series"}, {"title": "<PERSON> the Tank Engine", "link": "https://wikipedia.org/wiki/Thomas_the_Tank_Engine"}]}, {"year": "1913", "text": "<PERSON>, American songwriter, composer, and screenwriter (d. 1988)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Ada<PERSON>\"><PERSON></a>, American songwriter, composer, and screenwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Adair\"><PERSON></a>, American songwriter, composer, and screenwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Russian politician (d. 1984)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Romanian-American cartoonist (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American cartoonist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American cartoonist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American cartoonist (d. 2006)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, Danish ballet dancer, choreographer, and educator (d. 2018)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ballet dancer, choreographer, and educator (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish ballet dancer, choreographer, and educator (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Theilade"}]}, {"year": "1915", "text": "<PERSON>, American biologist and virologist, Nobel Prize laureate (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biologist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1916", "text": "<PERSON>, Polish-American fashion designer (d. 1989)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American fashion designer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American fashion designer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Argentinian pianist, composer, and conductor (d. 2016)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Ho<PERSON><PERSON>_<PERSON>g%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian pianist, composer, and conductor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>g%C3%A1n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian pianist, composer, and conductor (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Horacio_Salg%C3%A1n"}]}, {"year": "1916", "text": "<PERSON>, American political scientist and economist, Nobel Prize laureate (d. 2001)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and economist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1917", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2010)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)\" class=\"mw-redirect\" title=\"<PERSON> (chemist)\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(chemist)\" class=\"mw-redirect\" title=\"<PERSON> (chemist)\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2010)", "links": [{"title": "<PERSON> (chemist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chemist)"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Greek singer-songwriter (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer-songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>itsaris"}]}, {"year": "1917", "text": "<PERSON><PERSON>, American actor and producer (d. 1996)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>h_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>h_<PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Chadian politician, 1st President of Chad (d. 1975)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>e\" title=\"<PERSON>\"><PERSON></a>, Chadian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Chad\" class=\"mw-redirect\" title=\"President of Chad\">President of Chad</a> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>e\" title=\"<PERSON>\"><PERSON></a>, Chadian politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Chad\" class=\"mw-redirect\" title=\"President of Chad\">President of Chad</a> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Tombalbaye"}, {"title": "President of Chad", "link": "https://wikipedia.org/wiki/President_of_Chad"}]}, {"year": "1920", "text": "<PERSON>, American race car driver (d. 1957)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American race car driver (d. 1957)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Russian actress (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian actress (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alla_Kazanskaya"}]}, {"year": "1920", "text": "<PERSON>, Canadian businessman, founded <PERSON> the Record Man (d. 2012)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Record_Man\" title=\"<PERSON> the Record Man\"><PERSON> the Record Man</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Record_Man\" title=\"<PERSON> the Record Man\"><PERSON> the Record Man</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the Record Man", "link": "https://wikipedia.org/wiki/<PERSON>_the_Record_Man"}]}, {"year": "1920", "text": "<PERSON>, Italian actor, director, and screenwriter (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor, director, and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American pianist and composer (d. 1977)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American pianist and composer (d. 1999)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1999)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Swedish actor and director (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actor and director (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, English-Australian lieutenant, judge, and politician, 20th Governor-General of Australia (d. 2017)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian lieutenant, judge, and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-Australian lieutenant, judge, and politician, 20th <a href=\"https://wikipedia.org/wiki/Governor-General_of_Australia\" title=\"Governor-General of Australia\">Governor-General of Australia</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor-General of Australia", "link": "https://wikipedia.org/wiki/Governor-General_of_Australia"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Swedish author and psychologist (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/H%C3%A9di_<PERSON>ied\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and psychologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9di_<PERSON>ied\" title=\"<PERSON><PERSON><PERSON>ied\"><PERSON><PERSON><PERSON></a>, Swedish author and psychologist (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9di_Fried"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Israeli general and politician, 7th President of Israel (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli general and politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_Israel\" title=\"President of Israel\">President of Israel</a> (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Israel", "link": "https://wikipedia.org/wiki/President_of_Israel"}]}, {"year": "1925", "text": "<PERSON>, English journalist and author (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English journalist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English journalist and author (d. 2018)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Turkish poet, author, and critic (d. 2005)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Attil%C3%A2_%C4%B0lhan\" title=\"Attil<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, author, and critic (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Attil%C3%A2_%C4%B0lhan\" title=\"Attil<PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish poet, author, and critic (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attil%C3%A2_%C4%B0lhan"}]}, {"year": "1926", "text": "<PERSON>, Sri Lankan Tamil lawyer and politician (d. 1975)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan Tamil lawyer and politician (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American illustrator (d. 1993)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani poet and author (d. 1978)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON><PERSON>\" title=\"<PERSON>-<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani poet and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>-<PERSON><PERSON>\" title=\"<PERSON>-<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani poet and author (d. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Italian author and illustrator (d. 1995)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American author and academic (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez\" title=\"<PERSON>\"><PERSON></a>, American author and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miguel_M%C3%A9ndez"}]}, {"year": "1930", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English air marshal", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON> (RAF officer)\"><PERSON></a>, English air marshal", "links": [{"title": "<PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>(RAF_officer)"}]}, {"year": "1932", "text": "<PERSON>, Baron <PERSON>, Iranian-English businessman and politician", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_Alliance\" title=\"David Alliance, Baron Alliance\"><PERSON>, Baron <PERSON></a>, Iranian-English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_<PERSON>,_Baron_Alliance\" title=\"David Alliance, Baron Alliance\"><PERSON>, Baron <PERSON></a>, Iranian-English businessman and politician", "links": [{"title": "<PERSON>, Baron Alliance", "link": "https://wikipedia.org/wiki/David_Alliance,_Baron_Alliance"}]}, {"year": "1932", "text": "<PERSON>, American lawyer and politician, 52nd Governor of New York (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/Governor_of_New_York\" title=\"Governor of New York\">Governor of New York</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New York", "link": "https://wikipedia.org/wiki/Governor_of_New_York"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Indian singer (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian singer (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American-Canadian football player and sportscaster (d. 1999)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and sportscaster (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian football player and sportscaster (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Iranian politician, 2nd President of Iran (d. 1981)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Iran\" title=\"President of Iran\">President of Iran</a> (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "President of Iran", "link": "https://wikipedia.org/wiki/President_of_Iran"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Serbian political caricaturist", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Predrag_Koraksi%C4%87_Corax\" title=\"Predrag <PERSON>\">Predrag <PERSON></a>, Serbian political <a href=\"https://wikipedia.org/wiki/List_of_caricaturists\" title=\"List of caricaturists\">caricaturist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Predrag_<PERSON>%C4%87_Corax\" title=\"Predrag <PERSON>\">Predrag <PERSON></a>, Serbian political <a href=\"https://wikipedia.org/wiki/List_of_caricaturists\" title=\"List of caricaturists\">caricaturist</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Predrag_Koraksi%C4%87_Corax"}, {"title": "List of caricaturists", "link": "https://wikipedia.org/wiki/List_of_caricaturists"}]}, {"year": "1934", "text": "<PERSON>, American R&B singer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Garnett\"><PERSON></a>, American R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>arnett\"><PERSON></a>, American R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American cardinal (d. 2019)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Swiss-Canadian author and screenwriter", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Swiss-Canadian author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Swiss-Canadian author and screenwriter", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "1937", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2002)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American baseball player and coach", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left_fielder)\" class=\"mw-redirect\" title=\"<PERSON> (left fielder)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left_fielder)\" class=\"mw-redirect\" title=\"<PERSON> (left fielder)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (left fielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left_fielder)"}]}, {"year": "1939", "text": "<PERSON>, American activist and businessman, founded the American Civil Rights Institute", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and businessman, founded the <a href=\"https://wikipedia.org/wiki/American_Civil_Rights_Institute\" title=\"American Civil Rights Institute\">American Civil Rights Institute</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and businessman, founded the <a href=\"https://wikipedia.org/wiki/American_Civil_Rights_Institute\" title=\"American Civil Rights Institute\">American Civil Rights Institute</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Civil Rights Institute", "link": "https://wikipedia.org/wiki/American_Civil_Rights_Institute"}]}, {"year": "1941", "text": "<PERSON>, American illustrator (d. 2022)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer-songwriter (d. 1994)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian broadcaster, founded Astral Media (d. 2022)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian broadcaster, founded <a href=\"https://wikipedia.org/wiki/Astral_Media\" title=\"Astral Media\">Astral Media</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian broadcaster, founded <a href=\"https://wikipedia.org/wiki/Astral_Media\" title=\"Astral Media\">Astral Media</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Astral Media", "link": "https://wikipedia.org/wiki/Astral_Media"}]}, {"year": "1942", "text": "<PERSON>, American diplomat", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American diplomat", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian sprinter (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian sprinter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French singer and actor (d. 2017)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Danish politician, 38th Prime Minister of Denmark", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish politician, 38th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Denmark\" title=\"Prime Minister of Denmark\">Prime Minister of Denmark</a>", "links": [{"title": "Po<PERSON> N<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Denmark", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Denmark"}]}, {"year": "1944", "text": "<PERSON>, American police officer and academic (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American police officer and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Filipino judge and politician (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino judge and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino judge and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Guinean cardinal", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guinean cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guinean cardinal", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American colonel", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English rock singer-songwriter, musician, and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Noddy_Holder\" title=\"Noddy Holder\"><PERSON><PERSON>er</a>, English rock singer-songwriter, musician, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Noddy_Holder\" title=\"No<PERSON> Holder\"><PERSON><PERSON>er</a>, English rock singer-songwriter, musician, and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Noddy_Holder"}]}, {"year": "1946", "text": "<PERSON>, American paleontologist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(paleontologist)\" title=\"<PERSON> (paleontologist)\"><PERSON></a>, American paleontologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(paleontologist)\" title=\"<PERSON> (paleontologist)\"><PERSON></a>, American paleontologist and academic", "links": [{"title": "<PERSON> (paleontologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(paleontologist)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Egyptian-Greek singer-songwriter and bass player (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> R<PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek singer-songwriter and bass player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> R<PERSON>\"><PERSON><PERSON></a>, Egyptian-Greek singer-songwriter and bass player (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/De<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American photographer and journalist (d. 1984)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American football player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English politician and diplomat, Governor of Anguilla", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_Anguilla\" title=\"Governor of Anguilla\">Governor of Anguilla</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor_of_Anguilla\" title=\"Governor of Anguilla\">Governor of Anguilla</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Anguilla", "link": "https://wikipedia.org/wiki/Governor_of_Anguilla"}]}, {"year": "1948", "text": "<PERSON>, Scottish footballer, academic, and politician, 2nd First Minister of Scotland", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, academic, and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer, academic, and politician, 2nd <a href=\"https://wikipedia.org/wiki/First_Minister_of_Scotland\" title=\"First Minister of Scotland\">First Minister of Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Minister of Scotland", "link": "https://wikipedia.org/wiki/First_Minister_of_Scotland"}]}, {"year": "1949", "text": "<PERSON>, American baseball player and manager", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English actor and director", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Australian singer-songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor, comedian, and screenwriter (d. 2000)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Turkish ophthalmologist and professor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/U%C4%9F<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish ophthalmologist and professor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U%C4%9F<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish ophthalmologist and professor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U%C4%9Fur_<PERSON>er"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Ghanaian nurse and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian nurse and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian nurse and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American choreographer and television host", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American choreographer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American choreographer and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Indian-English businessman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ttal\"><PERSON></a>, Indian-English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mittal\"><PERSON></a>, Indian-English businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ttal"}]}, {"year": "1951", "text": "<PERSON>, American magazine and newspaper editor (Manhattan, inc., New York Post)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Jane_Amsterdam\" title=\"Jane Amsterdam\"><PERSON> Amsterdam</a>, American magazine and newspaper editor (<i><a href=\"https://wikipedia.org/wiki/Manhattan,_inc.\" title=\"Manhattan, inc.\">Manhattan, inc.</a></i>, <i><a href=\"https://wikipedia.org/wiki/New_York_Post\" title=\"New York Post\">New York Post</a></i>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jane_Amsterdam\" title=\"Jane Amsterdam\"><PERSON> Amsterdam</a>, American magazine and newspaper editor (<i><a href=\"https://wikipedia.org/wiki/Manhattan,_inc.\" title=\"Manhattan, inc.\">Manhattan, inc.</a></i>, <i><a href=\"https://wikipedia.org/wiki/New_York_Post\" title=\"New York Post\">New York Post</a></i>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jane_Amsterdam"}, {"title": "Manhattan, inc.", "link": "https://wikipedia.org/wiki/Manhattan,_inc."}, {"title": "New York Post", "link": "https://wikipedia.org/wiki/New_York_Post"}]}, {"year": "1951", "text": "<PERSON>, American painter (d. 2000)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English politician, Secretary of State for Wales", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Wales\" title=\"Secretary of State for Wales\">Secretary of State for Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Wales", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Wales"}]}, {"year": "1951", "text": "<PERSON>, American rock singer-songwriter and musician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American rock singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American rock singer-songwriter and musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, Additional Solicitor General of India", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Additional_Solicitor_General_of_India\" title=\"Additional Solicitor General of India\">Additional Solicitor General of India</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Additional_Solicitor_General_of_India\" title=\"Additional Solicitor General of India\">Additional Solicitor General of India</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Additional Solicitor General of India", "link": "https://wikipedia.org/wiki/Additional_Solicitor_General_of_India"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Lithuanian long jumper", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Vil<PERSON>_Bardauskien%C4%97\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vil<PERSON>_Bardauskien%C4%97\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian long jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilma_Bardauskien%C4%97"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Swedish racing driver and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish racing driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish racing driver and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gh"}]}, {"year": "1953", "text": "<PERSON>, Chinese engineer and politician, General Secretary of the Communist Party and President of China", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_China\" class=\"mw-redirect\" title=\"General Secretary of the Communist Party of China\">General Secretary of the Communist Party</a> and <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of China</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese engineer and politician, <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_China\" class=\"mw-redirect\" title=\"General Secretary of the Communist Party of China\">General Secretary of the Communist Party</a> and <a href=\"https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"President of the People's Republic of China\">President of China</a>", "links": [{"title": "Xi <PERSON>ping", "link": "https://wikipedia.org/wiki/Xi_<PERSON>"}, {"title": "General Secretary of the Communist Party of China", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_China"}, {"title": "President of the People's Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_People%27s_Republic_of_China"}]}, {"year": "1953", "text": "<PERSON>, English cellist and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cellist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American country music singer and keyboard player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American country music singer and keyboard player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Rwandan humanitarian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rwandan humanitarian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Rwandan humanitarian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech discus thrower and shot putter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Zde%C5%88ka_%C5%A0ilhav%C3%A1\" title=\"Zdeňka Šilhavá\">Zdeňka Šilhavá</a>, Czech discus thrower and shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zde%C5%88ka_%C5%A0ilhav%C3%A1\" title=\"Zdeňka Šilhavá\">Zdeň<PERSON> Šilhavá</a>, Czech discus thrower and shot putter", "links": [{"title": "Zdeňka Šilhavá", "link": "https://wikipedia.org/wiki/Zde%C5%88ka_%C5%A0ilhav%C3%A1"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Australian swimmer (d. 1996)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Beverley_Whitfield\" title=\"Beverley Whitfield\"><PERSON><PERSON><PERSON></a>, Australian swimmer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beverley_Whitfield\" title=\"Beverley Whitfield\"><PERSON><PERSON><PERSON></a>, Australian swimmer (d. 1996)", "links": [{"title": "Beverley Whitfield", "link": "https://wikipedia.org/wiki/Beverley_Whitfield"}]}, {"year": "1955", "text": "<PERSON>, American actress, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dr<PERSON>\"><PERSON></a>, American actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Draper\"><PERSON></a>, American actress, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American model and actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Russian-Ukrainian journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Ukrainian journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Ukrainian journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American baseball player and coach", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1958", "text": "<PERSON>, American baseball player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian racing driver (d. 1982)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 1982)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Scottish footballer and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Alan_Brazil\" title=\"Alan Brazil\"><PERSON></a>, Scottish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alan_Brazil\" title=\"Alan Brazil\"><PERSON></a>, Scottish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_Brazil"}]}, {"year": "1959", "text": "<PERSON>, American model and actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, French actress, producer, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Mich%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mich%C3%A8le_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French actress, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mich%C3%A8le_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Dutch field hockey player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch field hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Northern Irish boxer and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish boxer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish boxer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American wrestler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American wrestler (d. 2012)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2012)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)"}]}, {"year": "1962", "text": "<PERSON>, English actor, satirist, director, and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, English actor, satirist, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(satirist)\" title=\"<PERSON> (satirist)\"><PERSON></a>, English actor, satirist, director, and producer", "links": [{"title": "<PERSON> (satirist)", "link": "https://wikipedia.org/wiki/<PERSON>_(satirist)"}]}, {"year": "1962", "text": "<PERSON>, Hungarian soprano", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1963", "text": "<PERSON>, American actress, director, and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Venezuelan actress (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lourdes_Valera"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, American actress and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Danish footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Belgian rower", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Annelies_Bredael\" title=\"Annelies Bredael\"><PERSON><PERSON> Bred<PERSON></a>, Belgian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Annelies_Bredael\" title=\"Annelies Bredael\"><PERSON><PERSON> Bred<PERSON></a>, Belgian rower", "links": [{"title": "Annelies Bredael", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Bredael"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Kazakhstani politician, 7th Prime Minister of Kazakhstan", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan\" title=\"Prime Minister of Kazakhstan\">Prime Minister of Kazakhstan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kazakhstani politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan\" title=\"Prime Minister of Kazakhstan\">Prime Minister of Kazakhstan</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Kazakhstan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Kazakhstan"}]}, {"year": "1965", "text": "<PERSON>, American lawyer and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Washington_politician)\" title=\"<PERSON> (Washington politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Washington_politician)\" title=\"<PERSON> (Washington politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (Washington politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Washington_politician)"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Latvian politician, 9th President of Latvia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Rai<PERSON>s_V%C4%93jonis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rai<PERSON><PERSON>_V%C4%93jonis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Raimonds_V%C4%93jonis"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian swimmer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/K%C3%A1roly_G%C3%BCttler\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A1roly_G%C3%BCttler\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A1roly_G%C3%BCttler"}]}, {"year": "1969", "text": "<PERSON>, Canadian ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jesse_B%C3%A9langer"}]}, {"year": "1969", "text": "<PERSON>, American rapper, producer, and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ice_Cube\" title=\"Ice Cube\"><PERSON> Cube</a>, American rapper, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ice_Cube\" title=\"Ice Cube\"><PERSON> Cube</a>, American rapper, producer, and actor", "links": [{"title": "Ice Cube", "link": "https://wikipedia.org/wiki/Ice_Cube"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American singer and actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_DeLe%C3%B3n\" title=\"<PERSON><PERSON>eón\"><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_DeLe%C3%B3n\" title=\"<PERSON><PERSON>e<PERSON>\"><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idalis_DeLe%C3%B3n"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_Galakteros\" title=\"<PERSON><PERSON><PERSON> Galakteros\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Na<PERSON><PERSON>_Galakteros\" title=\"<PERSON><PERSON><PERSON> Galakteros\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>s <PERSON>", "link": "https://wikipedia.org/wiki/Nasos_Galakteros"}]}, {"year": "1969", "text": "<PERSON>, German footballer and sportscaster", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Kenyan cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, French tennis player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9dric_Pioline"}]}, {"year": "1970", "text": "<PERSON>, American soldier and author", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian rugby league player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, French soprano", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ga%C3%ABlle_M%C3%A9chaly\" title=\"G<PERSON><PERSON><PERSON>chal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ga%C3%ABlle_M%C3%A9chaly\" title=\"<PERSON><PERSON><PERSON><PERSON> Méchal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French soprano", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ga%C3%ABlle_M%C3%A9chaly"}]}, {"year": "1970", "text": "<PERSON>, American actress and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Croatian basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/%C5%BDan_<PERSON>k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%B<PERSON>an_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian basketball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%B<PERSON>an_Ta<PERSON>k"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor, musician, and film producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician, and film producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, musician, and film producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American golfer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Norwegian footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Tore <PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor and singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor and model", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Czech decathlete", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ryba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ryba\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech decathlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_Ryba"}]}, {"year": "1977", "text": "<PERSON>, American basketball player and manager", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Dutch footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Zach_<PERSON>\" title=\"Zach Day\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zach_<PERSON>\" title=\"Zach Day\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Belarusian sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Yulia_Nestsiarenka\" title=\"Yu<PERSON> Nestsiarenka\"><PERSON><PERSON>estsiar<PERSON></a>, Belarusian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yulia_Nestsiarenka\" title=\"Yulia Nestsiarenka\"><PERSON><PERSON>estsiar<PERSON></a>, Belarusian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yulia_Nestsiarenka"}]}, {"year": "1979", "text": "<PERSON>, German footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Dutch racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, Dutch racing driver", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1980", "text": "<PERSON>, Australian rugby player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1980)\" title=\"<PERSON> (rugby union, born 1980)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1980)\" title=\"<PERSON> (rugby union, born 1980)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby union, born 1980)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1980)"}]}, {"year": "1981", "text": "<PERSON>, Ghanaian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, New Zealand rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Bangladeshi cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON> (cricketer)\"><PERSON><PERSON></a>, Bangladeshi cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON><PERSON> (cricketer)\"><PERSON><PERSON></a>, Bangladeshi cricketer", "links": [{"title": "<PERSON><PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(cricketer)"}]}, {"year": "1983", "text": "<PERSON>, Australian singer-songwriter and guitarist[citation needed]", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Laura_Imbruglia"}]}, {"year": "1983", "text": "<PERSON>, Canadian fencer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, Canadian fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)\" title=\"<PERSON> (fencer)\"><PERSON></a>, Canadian fencer", "links": [{"title": "<PERSON> (fencer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fencer)"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Czech tennis player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_H<PERSON>nov%C3%A1"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Colombian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edison_Toloza"}]}, {"year": "1985", "text": "<PERSON>, American comedian, actress, and writer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Australian rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Spanish cyclist (d. 2012)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish cyclist (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%AD<PERSON>_<PERSON><PERSON>do"}]}, {"year": "1989", "text": "<PERSON>, American race car driver (d. 2016)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Polish footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Micha%C5%82_Kopczy%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Micha%C5%82_Kopczy%C5%84ski\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Micha%C5%82_Ko<PERSON><PERSON>y%C5%84ski"}]}, {"year": "1992", "text": "<PERSON>, Egyptian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Dutch heptathlete and sprinter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch heptathlete and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch heptathlete and sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sc<PERSON>pers"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Bosnian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bosnian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>%C5%BEi%C4%87"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Basque footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Basque footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Basque footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Barbadian athlete", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Tia-<PERSON><PERSON>_<PERSON>\" title=\"Tia-Adana Belle\">Tia-<PERSON><PERSON></a>, Barbadian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia-<PERSON><PERSON>_<PERSON>\" title=\"Tia-Adana Belle\">Tia-<PERSON><PERSON></a>, Barbadian athlete", "links": [{"title": "Tia-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia-<PERSON><PERSON>_Belle"}]}, {"year": "1996", "text": "<PERSON>, Norwegian singer-songwriter", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Norwegian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, Norwegian singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1997", "text": "<PERSON>, American gymnast", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "923", "text": "<PERSON> of France (b. 866)", "html": "923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> (b. 866)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}]}, {"year": "948", "text": "<PERSON><PERSON>, Byzantine Emperor (b. c. 870)", "html": "948 - <a href=\"https://wikipedia.org/wiki/Romanos_I\" class=\"mw-redirect\" title=\"Romanos I\"><PERSON><PERSON></a>, Byzantine Emperor (b. c. 870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romanos_I\" class=\"mw-redirect\" title=\"Romanos I\"><PERSON><PERSON></a>, Byzantine Emperor (b. c. 870)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "952", "text": "<PERSON><PERSON>, Chinese general", "html": "952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o\" title=\"Murong Yanchao\"><PERSON><PERSON></a>, Chinese general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o\" title=\"Murong Yanchao\"><PERSON><PERSON></a>, Chinese general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murong_<PERSON>o"}]}, {"year": "960", "text": "<PERSON><PERSON><PERSON><PERSON> of Winchester, English princess and saint", "html": "960 - <a href=\"https://wikipedia.org/wiki/<PERSON>ad<PERSON><PERSON>_of_Winchester\" title=\"E<PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON><PERSON> of Winchester</a>, English princess and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ad<PERSON><PERSON>_of_Winchester\" title=\"E<PERSON><PERSON><PERSON> of Winchester\"><PERSON><PERSON><PERSON><PERSON> of Winchester</a>, English princess and saint", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Winchester", "link": "https://wikipedia.org/wiki/Eadburh_of_Winchester"}]}, {"year": "970", "text": "<PERSON><PERSON>, bishop of Passau", "html": "970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Bishop_of_Passau\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Bishop of Passau\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Passau\" title=\"Roman Catholic Diocese of Passau\">Passau</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Bishop_of_Passau\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Bishop of Passau\"><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Passau\" title=\"Roman Catholic Diocese of Passau\">Passau</a>", "links": [{"title": "<PERSON><PERSON>, Bishop of Passau", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Bishop_of_Passau"}, {"title": "Roman Catholic Diocese of Passau", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Passau"}]}, {"year": "991", "text": "<PERSON><PERSON><PERSON>, Byzantine wife of <PERSON>, Holy Roman Emperor (b. 960)", "html": "991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Byzantine wife of <a href=\"https://wikipedia.org/wiki/<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>phanu"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1073", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1034)", "html": "1073 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D\" title=\"Emperor Go-Sanjō\">Emperor Go-Sanjō</a> of Japan (b. 1034)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D\" title=\"Emperor Go-Sanjō\">Emperor <PERSON>-Sanjō</a> of Japan (b. 1034)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Sanj%C5%8D"}]}, {"year": "1184", "text": "<PERSON>, King of Norway (b. 1156)", "html": "1184 - <a href=\"https://wikipedia.org/wiki/Magnus_V_of_Norway\" class=\"mw-redirect\" title=\"Magnus V of Norway\"><PERSON></a>, King of Norway (b. 1156)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magnus_V_of_Norway\" class=\"mw-redirect\" title=\"Magnus V of Norway\"><PERSON></a>, King of Norway (b. 1156)", "links": [{"title": "<PERSON> V of Norway", "link": "https://wikipedia.org/wiki/Magnus_V_of_Norway"}]}, {"year": "1189", "text": "<PERSON><PERSON>, Japanese general (b. 1159)", "html": "1189 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yo<PERSON>tsu<PERSON>\" title=\"<PERSON><PERSON> no Yoshitsune\"><PERSON><PERSON> no <PERSON></a>, Japanese general (b. 1159)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yo<PERSON>tsu<PERSON>\" title=\"<PERSON><PERSON> no Yoshitsune\"><PERSON><PERSON> no <PERSON></a>, Japanese general (b. 1159)", "links": [{"title": "<PERSON><PERSON> no <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1246", "text": "<PERSON>, Duke of Austria (b. 1219)", "html": "1246 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1219)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1219)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1337", "text": "<PERSON>, Italian Franciscan and leader of a group of <PERSON><PERSON><PERSON> (b. 1247)", "html": "1337 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Franciscan and leader of a group of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Franciscan and leader of a group of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1247)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1341", "text": "<PERSON><PERSON><PERSON>, Byzantine emperor (b. 1297)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/Andronikos_III_Palaiologos\" title=\"Andronikos III Palaiologos\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 1297)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andronikos_III_Palaiologos\" title=\"Andronikos III Palaiologos\"><PERSON><PERSON><PERSON></a>, Byzantine emperor (b. 1297)", "links": [{"title": "Andronikos III Palaiologos", "link": "https://wikipedia.org/wiki/Andronikos_III_Palaiologos"}]}, {"year": "1381", "text": "<PERSON>, English lawyer and judge (b. 1346)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1346)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge (b. 1346)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1381", "text": "<PERSON><PERSON>, English rebel leader (b. 1341)", "html": "1381 - <a href=\"https://wikipedia.org/wiki/Wat_<PERSON>\" title=\"Wat <PERSON>\"><PERSON><PERSON> <PERSON></a>, English rebel leader (b. 1341)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wat_<PERSON>\" title=\"Wat <PERSON>\"><PERSON><PERSON> <PERSON></a>, English rebel leader (b. 1341)", "links": [{"title": "Wat Tyler", "link": "https://wikipedia.org/wiki/Wat_<PERSON>"}]}, {"year": "1383", "text": "<PERSON>, Byzantine emperor (b. 1292)", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1292)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1383", "text": "<PERSON>, Byzantine emperor", "html": "1383 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1389", "text": "<PERSON><PERSON> of Serbia (b. 1329)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/Lazar_of_Serbia\" title=\"Lazar of Serbia\"><PERSON><PERSON> of Serbia</a> (b. 1329)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lazar_of_Serbia\" title=\"La<PERSON> of Serbia\"><PERSON><PERSON> of Serbia</a> (b. 1329)", "links": [{"title": "<PERSON>zar of Serbia", "link": "https://wikipedia.org/wiki/Lazar_of_Serbia"}]}, {"year": "1389", "text": "<PERSON><PERSON>, Ottoman Sultan (b. 1319)", "html": "1389 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rad I\"><PERSON><PERSON></a>, Ottoman Sultan (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rad I\"><PERSON><PERSON> <PERSON></a>, Ottoman Sultan (b. 1319)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_I"}]}, {"year": "1389", "text": "<PERSON><PERSON>, Serbian knight.", "html": "1389 - <a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Obili%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian knight.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milo%C5%A1_Obili%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian knight.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milo%C5%A1_Obili%C4%87"}]}, {"year": "1416", "text": "<PERSON>, Duke of Berry (b. 1340)", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Berry\" title=\"<PERSON>, Duke of Berry\"><PERSON>, Duke of Berry</a> (b. 1340)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Berry\" title=\"<PERSON>, Duke of Berry\"><PERSON>, Duke of Berry</a> (b. 1340)", "links": [{"title": "<PERSON>, Duke of Berry", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Berry"}]}, {"year": "1467", "text": "<PERSON>, Duke of Burgundy (b. 1396)", "html": "1467 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Burgundy\"><PERSON>, Duke of Burgundy</a> (b. 1396)", "links": [{"title": "<PERSON>, Duke of Burgundy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Burgundy"}]}, {"year": "1521", "text": "<PERSON><PERSON>, Hungarian cardinal (b. 1442)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_Bak%C3%B3cz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian cardinal (b. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tam%C3%A1s_Bak%C3%B3cz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian cardinal (b. 1442)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tam%C3%A1s_Bak%C3%B3cz"}]}, {"year": "1614", "text": "<PERSON>, 1st Earl of Northampton, English courtier and politician, Lord Warden of the Cinque Ports (b. 1540)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northampton\" title=\"<PERSON>, 1st Earl of Northampton\"><PERSON>, 1st Earl of Northampton</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Northampton\" title=\"<PERSON>, 1st Earl of Northampton\"><PERSON>, 1st Earl of Northampton</a>, English courtier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports\" title=\"Lord Warden of the Cinque Ports\">Lord Warden of the Cinque Ports</a> (b. 1540)", "links": [{"title": "<PERSON>, 1st Earl of Northampton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Northampton"}, {"title": "Lord Warden of the Cinque Ports", "link": "https://wikipedia.org/wiki/Lord_Warden_of_the_Cinque_Ports"}]}, {"year": "1724", "text": "<PERSON>, English minister and politician (b. 1674)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and politician (b. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and politician (b. 1674)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1768", "text": "<PERSON>, Scottish mathematician and optician (b. 1710)", "html": "1768 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and optician (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Scottish mathematician and optician (b. 1710)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON>, French organist and composer (b. 1694)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (b. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and composer (b. 1694)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Scottish poet and academic (b. 1777)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and academic (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and academic (b. 1777)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "1849", "text": "<PERSON>, American lawyer and politician, 11th President of the United States (b. 1795)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Dutch-French painter and academic (b. 1795)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French painter and academic (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch-French painter and academic (b. 1795)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON><PERSON><PERSON>, Croatian violinist and composer (b. 1862)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>re%C5%BEma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian violinist and composer (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%BEma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian violinist and composer (b. 1862)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franjo_Kre%C5%BEma"}]}, {"year": "1888", "text": "<PERSON>, German Emperor (b. 1831)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"Frederick III, German Emperor\"><PERSON>, German Emperor</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"<PERSON> III, German Emperor\"><PERSON> III, German Emperor</a> (b. 1831)", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}]}, {"year": "1889", "text": "<PERSON><PERSON>, Romanian journalist, author, and poet (b. 1850)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist, author, and poet (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian journalist, author, and poet (b. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 10th <PERSON><PERSON><PERSON><PERSON> (b. 1822)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Unry%C5%AB_Ky%C5%ABkichi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 10th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Unry%C5%AB_Ky%C5%ABkichi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 10th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1822)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Unry%C5%AB_Ky%C5%ABkichi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Norwegian physicist and academic (b. 1867)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Kristian B<PERSON>\"><PERSON><PERSON></a>, Norwegian physicist and academic (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Kristian B<PERSON>\"><PERSON><PERSON></a>, Norwegian physicist and academic (b. 1867)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kristian_Birk<PERSON>nd"}]}, {"year": "1934", "text": "<PERSON>, French cellist and composer (b. 1857)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cellist and composer (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, German painter and illustrator (b. 1880)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and illustrator (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, German neurologist and physician (b. 1873)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and physician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German neurologist and physician (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Otf<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English mystic and author (b. 1875)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and author (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mystic and author (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "Count <PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Austrian diplomat", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>-<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>-<PERSON><PERSON>\">Count <PERSON>-<PERSON><PERSON></a>, Austrian diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>-<PERSON><PERSON>-<PERSON>\" class=\"mw-redirect\" title=\"Count <PERSON>-<PERSON><PERSON>\">Count <PERSON>-<PERSON><PERSON></a>, Austrian diplomat", "links": [{"title": "Count <PERSON>Po<PERSON>ly-Dietrichstein", "link": "https://wikipedia.org/wiki/Count_<PERSON>_<PERSON>_<PERSON>-<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (b. 1923)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian racing driver (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giulio_<PERSON>abi<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and author (b. 1899)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish journalist and author (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Swiss pianist and conductor (b. 1877)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pianist and conductor (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Finnish runner (b. 1885)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish runner (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish runner (b. 1885)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American baseball player, coach, and umpire (b. 1880)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and umpire (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and umpire (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American guitarist and songwriter (b. 1925)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American biochemist and virologist, Nobel Prize laureate (b. 1904)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1976", "text": "<PERSON>, American baseball player, coach, and manager (b. 1896)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American playwright, composer, and conductor (b. 1902)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, composer, and conductor (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, composer, and conductor (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American sprinter (b. 1927)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Canadian lawyer and politician (b. 1912)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Irish actor (b. 1926)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American businessman and politician, 49th Governor of Kentucky (b. 1898)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 49th <a href=\"https://wikipedia.org/wiki/Governor_of_Kentucky\" title=\"Governor of Kentucky\">Governor of Kentucky</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Kentucky", "link": "https://wikipedia.org/wiki/Governor_of_Kentucky"}]}, {"year": "1991", "text": "<PERSON>, Saint Lucian economist and academic, Nobel Prize laureate (b. 1915)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" class=\"mw-redirect\" title=\"<PERSON> (economist)\"><PERSON></a>, Saint Lucian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(economist)\" class=\"mw-redirect\" title=\"<PERSON> (economist)\"><PERSON></a>, Saint Lucian economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1992", "text": "<PERSON>, American animator, producer, and screenwriter (b. 1940)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck Menville\"><PERSON></a>, American animator, producer, and screenwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Chuck Menville\"><PERSON></a>, American animator, producer, and screenwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian painter (b. 1939)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American commander, lawyer, and politician, 61st United States Secretary of the Treasury (b. 1917)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 61st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, lawyer, and politician, 61st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1993", "text": "<PERSON>, English racing driver and sportscaster (b. 1947)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and sportscaster (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Hunt\"><PERSON></a>, English racing driver and sportscaster (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Greek composer and theorist (b. 1925)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and theorist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek composer and theorist (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American physicist and inventor, invented the Atanasoff-Berry computer (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and inventor, invented the <a href=\"https://wikipedia.org/wiki/Atanasoff%E2%80%93Berry_computer\" title=\"At<PERSON><PERSON>ff-<PERSON> computer\">Atanasoff-Berry computer</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and inventor, invented the <a href=\"https://wikipedia.org/wiki/Atanasoff%E2%80%93Berry_computer\" title=\"At<PERSON>soff-Berry computer\">Atanasoff-Berry computer</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Atanasoff-Berry computer", "link": "https://wikipedia.org/wiki/Atanasoff%E2%80%93B<PERSON>_computer"}]}, {"year": "1996", "text": "<PERSON>, American singer and actress (b. 1917)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "Sir <PERSON>, 1st Baronet, Scottish general and politician (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Scottish general and politician (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, Scottish general and politician (b. 1911)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1996", "text": "<PERSON>, American wrestler (b. 1946)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Canadian lawyer and politician (b. 1906)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Omer_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Omer_C%C3%B4t%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer and politician (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Omer_C%C3%B4t%C3%A9"}]}, {"year": "2000", "text": "<PERSON>, French author, poet, and playwright (b. 1907)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and playwright (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French cinematographer (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, South Korean general and martial artist, founded <PERSON><PERSON><PERSON><PERSON> (b. 1918)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hong Hi\"><PERSON></a>, South Korean general and martial artist, founded <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">Taekwondo</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Hi\"><PERSON></a>, South Korean general and martial artist, founded <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">Taekwondo</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Taekwondo", "link": "https://wikipedia.org/wiki/Taekwondo"}]}, {"year": "2003", "text": "<PERSON>, Canadian-American actor (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yn"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Turkish politician and mayor of İzmir (b. 1952)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina\" title=\"<PERSON><PERSON> Piriştina\"><PERSON><PERSON></a>, Turkish politician and mayor of İzmir (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina\" title=\"<PERSON>met Piriştina\"><PERSON><PERSON></a>, Turkish politician and mayor of İzmir (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ahmet_Piri%C5%9Ftina"}]}, {"year": "2005", "text": "<PERSON>, French actress (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Belgian-French comedian and clown (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French comedian and clown (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French comedian and clown (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, New Zealand cricketer (b. 1910)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Argentine boxer (b. 1953)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine boxer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine boxer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Canadian ice hockey player (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American herpetologist and academic (b. 1910)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American herpetologist and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American herpetologist and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American economist and author (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American tennis player and sportscaster (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player and sportscaster (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, American tennis player and sportscaster (b. 1935)", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "2012", "text": "<PERSON>, Mexican economist and politician, 10th Governor of Guerrero (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Israel_Nogue<PERSON>_<PERSON>\" title=\"Israel Nogueda <PERSON>tero\">Israel <PERSON></a>, Mexican economist and politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_Guerrero\" class=\"mw-redirect\" title=\"Governor of Guerrero\">Governor of Guerrero</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_Nogue<PERSON>_<PERSON>\" title=\"Israel Nogue<PERSON>\">Israel <PERSON></a>, Mexican economist and politician, 10th <a href=\"https://wikipedia.org/wiki/Governor_of_Guerrero\" class=\"mw-redirect\" title=\"Governor of Guerrero\">Governor of Guerrero</a> (b. 1935)", "links": [{"title": "Israel Nogueda <PERSON>", "link": "https://wikipedia.org/wiki/Israel_Nogue<PERSON>_<PERSON>"}, {"title": "Governor of Guerrero", "link": "https://wikipedia.org/wiki/Governor_of_Guerrero"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, German footballer and manager (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Argentinian racing driver (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Froil%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Froil%C3%A1n_Gonz%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Froil%C3%A1n_Gonz%C3%A1lez"}]}, {"year": "2013", "text": "<PERSON>, Australian director and producer (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Rourke\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dennis_O%27Rourke"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2014", "text": "<PERSON>, French actor and businessman (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and businessman (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and businessman (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American radio host, producer, and voice actor, co-created American Top 40 (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, producer, and voice actor, co-created <i><a href=\"https://wikipedia.org/wiki/American_Top_40\" title=\"American Top 40\">American Top 40</a></i> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host, producer, and voice actor, co-created <i><a href=\"https://wikipedia.org/wiki/American_Top_40\" title=\"American Top 40\">American Top 40</a></i> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Top 40", "link": "https://wikipedia.org/wiki/American_Top_40"}]}, {"year": "2014", "text": "<PERSON>, American short story writer and novelist (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and novelist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian businessman and philanthropist, co-founded <PERSON>co <PERSON> (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Banco_Safra\" title=\"Banco Safra\"><PERSON><PERSON></a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Banco_Safra\" title=\"Banco Safra\"><PERSON><PERSON></a> (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Banco Safra", "link": "https://wikipedia.org/wiki/Banco_Safra"}]}, {"year": "2015", "text": "<PERSON>, American businessman, founded the Tracinda Corporation (b. 1917)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Tracinda_Corporation\" class=\"mw-redirect\" title=\"Tracinda Corporation\">Tracinda Corporation</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Tracinda_Corporation\" class=\"mw-redirect\" title=\"Tracinda Corporation\">Tracinda Corporation</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_<PERSON>n"}, {"title": "Tracinda Corporation", "link": "https://wikipedia.org/wiki/Tracinda_Corporation"}]}, {"year": "2016", "text": "<PERSON>, American author (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON> \"<PERSON><PERSON>, American Blues guitarist (b. 1929)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar%22_<PERSON>\" title='<PERSON> \"Guitar\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American Blues guitarist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Guitar%22_<PERSON>\" title='<PERSON> \"Guitar\" Murphy'><PERSON> \"Guitar\" <PERSON></a>, American Blues guitarist (b. 1929)", "links": [{"title": "<PERSON> \"Guitar\" <PERSON>", "link": "https://wikipedia.org/wiki/Matt_%22G<PERSON>ar%22_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Italian film director (b. 1923)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film director (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian film director (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, English actress and politician (b. 1936)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and politician (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American chef (b. 1979)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef (b. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(chef)\" title=\"<PERSON> (chef)\"><PERSON></a>, American chef (b. 1979)", "links": [{"title": "<PERSON> (chef)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chef)"}]}, {"year": "2024", "text": "<PERSON><PERSON>, English-born Montenegrin footballer (b. 1997)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-born Montenegrin footballer (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English-born Montenegrin footballer (b. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}