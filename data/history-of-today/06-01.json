{"date": "June 1", "url": "https://wikipedia.org/wiki/June_1", "data": {"Events": [{"year": "1252", "text": "<PERSON> is proclaimed king of Castile and León.", "html": "1252 - <a href=\"https://wikipedia.org/wiki/Alfonso_X\" class=\"mw-redirect\" title=\"Alfonso X\"><PERSON> X</a> is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_X\" class=\"mw-redirect\" title=\"Alfonso X\"><PERSON> X</a> is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfonso_X"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1298", "text": "Residents of Riga and the Grand Duchy of Lithuania defeat the Livonian Order in the Battle of Turaida.", "html": "1298 - Residents of <a href=\"https://wikipedia.org/wiki/Riga\" title=\"Riga\">Riga</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeat the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Turaida\" title=\"Battle of Turaida\">Battle of Turaida</a>.", "no_year_html": "Residents of <a href=\"https://wikipedia.org/wiki/Riga\" title=\"Riga\">Riga</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeat the <a href=\"https://wikipedia.org/wiki/Livonian_Order\" title=\"Livonian Order\">Livonian Order</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Turaida\" title=\"Battle of Turaida\">Battle of Turaida</a>.", "links": [{"title": "Riga", "link": "https://wikipedia.org/wiki/Riga"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Livonian Order", "link": "https://wikipedia.org/wiki/Livonian_Order"}, {"title": "Battle of Turaida", "link": "https://wikipedia.org/wiki/Battle_of_Turaida"}]}, {"year": "1495", "text": "A monk, <PERSON>, records the first known batch of Scotch whisky.", "html": "1495 - A <a href=\"https://wikipedia.org/wiki/Monk\" title=\"Monk\">monk</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, records the first known batch of <a href=\"https://wikipedia.org/wiki/Scotch_whisky\" title=\"Scotch whisky\">Scotch whisky</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Monk\" title=\"Monk\">monk</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, records the first known batch of <a href=\"https://wikipedia.org/wiki/Scotch_whisky\" title=\"Scotch whisky\">Scotch whisky</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Monk"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Scotch whisky", "link": "https://wikipedia.org/wiki/Scotch_whisky"}]}, {"year": "1533", "text": "<PERSON> is crowned Queen of England.", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">Queen</a> of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">Queen</a> of England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Queen consort", "link": "https://wikipedia.org/wiki/Queen_consort"}]}, {"year": "1535", "text": "Combined forces loyal to <PERSON> attack and expel the Ottomans from Tunis during the Conquest of Tunis.", "html": "1535 - Combined forces loyal to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a> attack and expel the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a> from <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a> during the <a href=\"https://wikipedia.org/wiki/Conquest_of_Tunis_(1535)\" title=\"Conquest of Tunis (1535)\">Conquest of Tunis</a>.", "no_year_html": "Combined forces loyal to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON></a> attack and expel the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottomans</a> from <a href=\"https://wikipedia.org/wiki/Tunis\" title=\"Tunis\">Tunis</a> during the <a href=\"https://wikipedia.org/wiki/Conquest_of_Tunis_(1535)\" title=\"Conquest of Tunis (1535)\">Conquest of Tunis</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nis"}, {"title": "Conquest of Tunis (1535)", "link": "https://wikipedia.org/wiki/Conquest_of_Tunis_(1535)"}]}, {"year": "1648", "text": "The Roundheads defeat the Cavaliers at the Battle of Maidstone in the Second English Civil War.", "html": "1648 - The <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Roundheads</a> defeat the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Cavaliers</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Maidstone\" title=\"Battle of Maidstone\">Battle of Maidstone</a> in the <a href=\"https://wikipedia.org/wiki/Second_English_Civil_War\" title=\"Second English Civil War\">Second English Civil War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roundhead\" title=\"Roundhead\">Roundheads</a> defeat the <a href=\"https://wikipedia.org/wiki/Cavalier\" title=\"Cavalier\">Cavaliers</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Maidstone\" title=\"Battle of Maidstone\">Battle of Maidstone</a> in the <a href=\"https://wikipedia.org/wiki/Second_English_Civil_War\" title=\"Second English Civil War\">Second English Civil War</a>.", "links": [{"title": "Roundhead", "link": "https://wikipedia.org/wiki/Roundhead"}, {"title": "Cavalier", "link": "https://wikipedia.org/wiki/Cavalier"}, {"title": "Battle of Maidstone", "link": "https://wikipedia.org/wiki/Battle_of_Maidstone"}, {"title": "Second English Civil War", "link": "https://wikipedia.org/wiki/Second_English_Civil_War"}]}, {"year": "1649", "text": "Start of the <PERSON><PERSON>roy Revolt: Filipinos in Northern Samar led by <PERSON><PERSON><PERSON> revolt against Spanish colonial authorities.", "html": "1649 - Start of the <a href=\"https://wikipedia.org/wiki/Philippine_revolts_against_Spain#<PERSON><PERSON><PERSON>_Revolt_(1649-1650)\" title=\"Philippine revolts against Spain\"><PERSON><PERSON><PERSON>olt</a>: Filipinos in <a href=\"https://wikipedia.org/wiki/Northern_Samar\" title=\"Northern Samar\">Northern Samar</a> led by <a href=\"https://wikipedia.org/wiki/Agus<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> revolt against Spanish colonial authorities.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/Philippine_revolts_against_Spain#<PERSON><PERSON><PERSON>_Revolt_(1649-1650)\" title=\"Philippine revolts against Spain\"><PERSON><PERSON><PERSON>olt</a>: Filipinos in <a href=\"https://wikipedia.org/wiki/Northern_Samar\" title=\"Northern Samar\">Northern Samar</a> led by <a href=\"https://wikipedia.org/wiki/Agus<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> revolt against Spanish colonial authorities.", "links": [{"title": "Philippine revolts against Spain", "link": "https://wikipedia.org/wiki/Philippine_revolts_against_Spain#<PERSON><PERSON><PERSON>_Revolt_(1649-1650)"}, {"title": "Northern Samar", "link": "https://wikipedia.org/wiki/Northern_Samar"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1670", "text": "In Dover, England, <PERSON> of England and <PERSON> of <PERSON> sign the Secret Treaty of Dover, which will force England into the Third Anglo-Dutch War.", "html": "1670 - In <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover</a>, England, <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"Charles II of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a> sign the <a href=\"https://wikipedia.org/wiki/Secret_Treaty_of_Dover\" title=\"Secret Treaty of Dover\">Secret Treaty of Dover</a>, which will force England into the <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Dover\" title=\"Dover\">Dover</a>, England, <a href=\"https://wikipedia.org/wiki/Charles_II_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a> sign the <a href=\"https://wikipedia.org/wiki/Secret_Treaty_of_Dover\" title=\"Secret Treaty of Dover\">Secret Treaty of Dover</a>, which will force England into the <a href=\"https://wikipedia.org/wiki/Third_Anglo-Dutch_War\" title=\"Third Anglo-Dutch War\">Third Anglo-Dutch War</a>.", "links": [{"title": "Dover", "link": "https://wikipedia.org/wiki/Dover"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}, {"title": "Secret Treaty of Dover", "link": "https://wikipedia.org/wiki/Secret_Treaty_of_Dover"}, {"title": "Third Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Third_Anglo-Dutch_War"}]}, {"year": "1676", "text": "Battle of Öland: allied Danish-Dutch forces defeat the Swedish navy in the Baltic Sea, during the Scanian War (1675-79).", "html": "1676 - <a href=\"https://wikipedia.org/wiki/Battle_of_%C3%96land\" title=\"Battle of Öland\">Battle of Öland</a>: allied Danish-Dutch forces defeat the Swedish navy in the Baltic Sea, during the <a href=\"https://wikipedia.org/wiki/Scanian_War\" title=\"Scanian War\">Scanian War</a> (1675-79).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_%C3%96land\" title=\"Battle of Öland\">Battle of Öland</a>: allied Danish-Dutch forces defeat the Swedish navy in the Baltic Sea, during the <a href=\"https://wikipedia.org/wiki/Scanian_War\" title=\"Scanian War\">Scanian War</a> (1675-79).", "links": [{"title": "Battle of Öland", "link": "https://wikipedia.org/wiki/Battle_of_%C3%96land"}, {"title": "Scanian War", "link": "https://wikipedia.org/wiki/Scanian_War"}]}, {"year": "1679", "text": "The Scottish Covenanters defeat <PERSON> of Claverhouse at the Battle of Drumclog.", "html": "1679 - The Scottish <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanters</a> defeat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Claverhouse\" class=\"mw-redirect\" title=\"<PERSON> of Claverhouse\"><PERSON> of Claverhouse</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Drumclog\" title=\"Battle of Drumclog\">Battle of Drumclog</a>.", "no_year_html": "The Scottish <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Covenanters</a> defeat <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Claverhouse\" class=\"mw-redirect\" title=\"<PERSON> of Claverhouse\"><PERSON> of Claverhouse</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Drumclog\" title=\"Battle of Drumclog\">Battle of Drumclog</a>.", "links": [{"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}, {"title": "<PERSON> of Claverhouse", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Battle of Drumclog", "link": "https://wikipedia.org/wiki/Battle_of_Drumclog"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON><PERSON> rescues 14 sailors at the Cape of Good Hope from the sinking ship De Jonge Thomas by riding his horse into the sea seven times. Both he and his horse, <PERSON><PERSON>, are drowned on his eighth attempt.", "html": "1773 - <a href=\"https://wikipedia.org/wiki/W<PERSON>ra<PERSON>_Woltemade\" title=\"<PERSON><PERSON><PERSON><PERSON> Wolt<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> rescues 14 sailors at the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a> from the sinking ship <i>De <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> by riding his horse into the sea seven times. Both he and his horse, <PERSON><PERSON>, are drowned on his eighth attempt.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ra<PERSON>_Woltemade\" title=\"<PERSON><PERSON><PERSON><PERSON> Wolt<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> rescues 14 sailors at the <a href=\"https://wikipedia.org/wiki/Cape_of_Good_Hope\" title=\"Cape of Good Hope\">Cape of Good Hope</a> from the sinking ship <i>De <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a></i> by riding his horse into the sea seven times. Both he and his horse, <PERSON><PERSON>, are drowned on his eighth attempt.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>emade"}, {"title": "Cape of Good Hope", "link": "https://wikipedia.org/wiki/Cape_of_Good_Hope"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1779", "text": "The court-martial for malfeasance of <PERSON>, a general in the Continental Army during the American Revolutionary War, begins.", "html": "1779 - The <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martial</a> for <a href=\"https://wikipedia.org/wiki/Malfeasance\" class=\"mw-redirect\" title=\"Malfeasance\">malfeasance</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">general</a> in the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Court-martial\" title=\"Court-martial\">court-martial</a> for <a href=\"https://wikipedia.org/wiki/Malfeasance\" class=\"mw-redirect\" title=\"Malfeasance\">malfeasance</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/General_officer\" title=\"General officer\">general</a> in the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> during the <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>, begins.", "links": [{"title": "Court-martial", "link": "https://wikipedia.org/wiki/Court-martial"}, {"title": "Malfeasance", "link": "https://wikipedia.org/wiki/Malfeasance"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General officer", "link": "https://wikipedia.org/wiki/General_officer"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}]}, {"year": "1792", "text": "Kentucky is admitted as the 15th state of the United States.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> is <a href=\"https://wikipedia.org/wiki/Admission_to_the_Union\" title=\"Admission to the Union\">admitted</a> as the 15th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kentucky\" title=\"Kentucky\">Kentucky</a> is <a href=\"https://wikipedia.org/wiki/Admission_to_the_Union\" title=\"Admission to the Union\">admitted</a> as the 15th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">state</a> of the United States.", "links": [{"title": "Kentucky", "link": "https://wikipedia.org/wiki/Kentucky"}, {"title": "Admission to the Union", "link": "https://wikipedia.org/wiki/Admission_to_the_Union"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1794", "text": "The battle of the Glorious First of June is fought, the first naval engagement between Britain and France during the French Revolutionary Wars.", "html": "1794 - The battle of the <a href=\"https://wikipedia.org/wiki/Glorious_First_of_June\" title=\"Glorious First of June\">Glorious First of June</a> is fought, the first naval engagement between Britain and France during the <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>.", "no_year_html": "The battle of the <a href=\"https://wikipedia.org/wiki/Glorious_First_of_June\" title=\"Glorious First of June\">Glorious First of June</a> is fought, the first naval engagement between Britain and France during the <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>.", "links": [{"title": "Glorious First of June", "link": "https://wikipedia.org/wiki/Glorious_First_of_June"}, {"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}]}, {"year": "1796", "text": "Tennessee is admitted as the 16th state of the United States.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> is admitted as the 16th state of the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tennessee\" title=\"Tennessee\">Tennessee</a> is admitted as the 16th state of the United States.", "links": [{"title": "Tennessee", "link": "https://wikipedia.org/wiki/Tennessee"}]}, {"year": "1812", "text": "War of 1812: U.S. President <PERSON> asks the Congress to declare war on the United Kingdom.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a> asks the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> to declare war on the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"James Madison\"><PERSON></a> asks the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a> to declare war on the United Kingdom.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1813", "text": "Capture of USS Chesapeake.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/Capture_of_USS_Chesapeake\" title=\"Capture of USS Chesapeake\">Capture of USS <i>Chesapeake</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Capture_of_USS_Chesapeake\" title=\"Capture of USS Chesapeake\">Capture of USS <i>Chesapeake</i></a>.", "links": [{"title": "Capture of USS Chesapeake", "link": "https://wikipedia.org/wiki/Capture_of_USS_Chesapeake"}]}, {"year": "1815", "text": "Napoleon promulgates a revised Constitution after it passes a plebiscite.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> promulgates a revised <a href=\"https://wikipedia.org/wiki/Charter_of_1815\" title=\"Charter of 1815\">Constitution</a> after it passes a plebiscite.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> promulgates a revised <a href=\"https://wikipedia.org/wiki/Charter_of_1815\" title=\"Charter of 1815\">Constitution</a> after it passes a plebiscite.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Charter of 1815", "link": "https://wikipedia.org/wiki/Charter_of_1815"}]}, {"year": "1831", "text": "<PERSON> becomes the first European at the North Magnetic Pole.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European at the <a href=\"https://wikipedia.org/wiki/North_Magnetic_Pole\" class=\"mw-redirect\" title=\"North Magnetic Pole\">North Magnetic Pole</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European at the <a href=\"https://wikipedia.org/wiki/North_Magnetic_Pole\" class=\"mw-redirect\" title=\"North Magnetic Pole\">North Magnetic Pole</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "North Magnetic Pole", "link": "https://wikipedia.org/wiki/North_Magnetic_Pole"}]}, {"year": "1849", "text": "Territorial Governor <PERSON> declared the Territory of Minnesota officially established.", "html": "1849 - Territorial Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declared the <a href=\"https://wikipedia.org/wiki/Territory_of_Minnesota\" class=\"mw-redirect\" title=\"Territory of Minnesota\">Territory of Minnesota</a> officially established.", "no_year_html": "Territorial Governor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> declared the <a href=\"https://wikipedia.org/wiki/Territory_of_Minnesota\" class=\"mw-redirect\" title=\"Territory of Minnesota\">Territory of Minnesota</a> officially established.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Territory of Minnesota", "link": "https://wikipedia.org/wiki/Territory_of_Minnesota"}]}, {"year": "1854", "text": "Åland War: The British navy destroys merchant ships and about 16,000 tar barrels of the wholesale stocks area in Oulu, Grand Duchy of Finland.", "html": "1854 - <a href=\"https://wikipedia.org/wiki/%C3%85land_War\" title=\"Åland War\">Åland War</a>: The <a href=\"https://wikipedia.org/wiki/British_navy\" class=\"mw-redirect\" title=\"British navy\">British navy</a> destroys <a href=\"https://wikipedia.org/wiki/Merchant_ship\" title=\"Merchant ship\">merchant ships</a> and about 16,000 <a href=\"https://wikipedia.org/wiki/Tar\" title=\"Tar\">tar barrels</a> of the wholesale stocks area in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%85land_War\" title=\"Åland War\">Åland War</a>: The <a href=\"https://wikipedia.org/wiki/British_navy\" class=\"mw-redirect\" title=\"British navy\">British navy</a> destroys <a href=\"https://wikipedia.org/wiki/Merchant_ship\" title=\"Merchant ship\">merchant ships</a> and about 16,000 <a href=\"https://wikipedia.org/wiki/Tar\" title=\"Tar\">tar barrels</a> of the wholesale stocks area in <a href=\"https://wikipedia.org/wiki/Oulu\" title=\"Oulu\">Oulu</a>, <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Finland\" title=\"Grand Duchy of Finland\">Grand Duchy of Finland</a>.", "links": [{"title": "Åland War", "link": "https://wikipedia.org/wiki/%C3%85land_War"}, {"title": "British navy", "link": "https://wikipedia.org/wiki/British_navy"}, {"title": "Merchant ship", "link": "https://wikipedia.org/wiki/Merchant_ship"}, {"title": "Tar", "link": "https://wikipedia.org/wiki/Tar"}, {"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}, {"title": "Grand Duchy of Finland", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Finland"}]}, {"year": "1855", "text": "The American adventurer <PERSON> conquers Nicaragua.", "html": "1855 - The American <a href=\"https://wikipedia.org/wiki/Adventurer\" class=\"mw-redirect\" title=\"Adventurer\">adventurer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "no_year_html": "The American <a href=\"https://wikipedia.org/wiki/Adventurer\" class=\"mw-redirect\" title=\"Adventurer\">adventurer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filibuster)\" title=\"<PERSON> (filibuster)\"><PERSON></a> conquers <a href=\"https://wikipedia.org/wiki/Nicaragua\" title=\"Nicaragua\">Nicaragua</a>.", "links": [{"title": "Adventurer", "link": "https://wikipedia.org/wiki/Adventurer"}, {"title": "<PERSON> (filibuster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filibuster)"}, {"title": "Nicaragua", "link": "https://wikipedia.org/wiki/Nicaragua"}]}, {"year": "1857", "text": "<PERSON>'s Les Fleurs du mal is published.", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Les_Fleurs_du_mal\" title=\"Les Fleurs du mal\">Les Fleurs du mal</a></i> is published.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/Les_Fleurs_du_mal\" title=\"Les Fleurs du mal\">Les Fleurs du mal</a></i> is published.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Les Fleurs du mal", "link": "https://wikipedia.org/wiki/<PERSON>_F<PERSON><PERSON>_du_mal"}]}, {"year": "1857", "text": "The Revolution of the Ganhadores begins in Salvador, Bahia, Brazil.", "html": "1857 - The <a href=\"https://wikipedia.org/wiki/Revolution_of_the_Ganhadores\" title=\"Revolution of the Ganhadores\">Revolution of the Ganhadores</a> begins in <a href=\"https://wikipedia.org/wiki/Salvador,_Bahia\" title=\"Salvador, Bahia\">Salvador, Bahia</a>, Brazil.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Revolution_of_the_Ganhadores\" title=\"Revolution of the Ganhadores\">Revolution of the Ganhadores</a> begins in <a href=\"https://wikipedia.org/wiki/Salvador,_Bahia\" title=\"Salvador, Bahia\">Salvador, Bahia</a>, Brazil.", "links": [{"title": "Revolution of the Ganhadores", "link": "https://wikipedia.org/wiki/Revolution_of_the_Ganhadores"}, {"title": "Salvador, Bahia", "link": "https://wikipedia.org/wiki/Salvador,_Bahia"}]}, {"year": "1861", "text": "American Civil War: The Battle of Fairfax Court House is fought.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fairfax_Court_House_(June_1861)\" class=\"mw-redirect\" title=\"Battle of Fairfax Court House (June 1861)\">Battle of Fairfax Court House</a> is fought.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Fairfax_Court_House_(June_1861)\" class=\"mw-redirect\" title=\"Battle of Fairfax Court House (June 1861)\">Battle of Fairfax Court House</a> is fought.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Fairfax Court House (June 1861)", "link": "https://wikipedia.org/wiki/Battle_of_Fairfax_Court_House_(June_1861)"}]}, {"year": "1862", "text": "American Civil War: Peninsula Campaign: The Battle of Seven Pines (or the Battle of Fair Oaks) ends inconclusively, with both sides claiming victory.", "html": "1862 - American Civil War: <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Pines\" title=\"Battle of Seven Pines\">Battle of Seven Pines</a> (or the Battle of Fair Oaks) ends inconclusively, with both sides claiming victory.", "no_year_html": "American Civil War: <a href=\"https://wikipedia.org/wiki/Peninsula_Campaign\" class=\"mw-redirect\" title=\"Peninsula Campaign\">Peninsula Campaign</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Seven_Pines\" title=\"Battle of Seven Pines\">Battle of Seven Pines</a> (or the Battle of Fair Oaks) ends inconclusively, with both sides claiming victory.", "links": [{"title": "Peninsula Campaign", "link": "https://wikipedia.org/wiki/Peninsula_Campaign"}, {"title": "Battle of Seven Pines", "link": "https://wikipedia.org/wiki/Battle_of_Seven_Pines"}]}, {"year": "1868", "text": "The Treaty of Bosque Redondo is signed, allowing the Navajo to return to their lands in Arizona and New Mexico.", "html": "1868 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Bosque_Redondo\" title=\"Treaty of Bosque Redondo\">Treaty of Bosque Redondo</a> is signed, allowing the <a href=\"https://wikipedia.org/wiki/Navajo\" title=\"Navajo\">Navajo</a> to return to their lands in <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> and <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Bosque_Redondo\" title=\"Treaty of Bosque Redondo\">Treaty of Bosque Redondo</a> is signed, allowing the <a href=\"https://wikipedia.org/wiki/Navajo\" title=\"Navajo\">Navajo</a> to return to their lands in <a href=\"https://wikipedia.org/wiki/Arizona\" title=\"Arizona\">Arizona</a> and <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>.", "links": [{"title": "Treaty of Bosque Redondo", "link": "https://wikipedia.org/wiki/Treaty_of_Bosque_Redondo"}, {"title": "Navajo", "link": "https://wikipedia.org/wiki/Navajo"}, {"title": "Arizona", "link": "https://wikipedia.org/wiki/Arizona"}, {"title": "New Mexico", "link": "https://wikipedia.org/wiki/New_Mexico"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, the last dynastic <PERSON>, is killed in the Anglo-Zulu War.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Napol%C3%A<PERSON><PERSON>,_Prince_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the last dynastic <a href=\"https://wikipedia.org/wiki/House_of_<PERSON>\" title=\"House of <PERSON>\"><PERSON></a>, is killed in the <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>,_Prince_Imperial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, the last dynastic <a href=\"https://wikipedia.org/wiki/<PERSON>_of_<PERSON>\" title=\"House of Bonaparte\"><PERSON></a>, is killed in the <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prince Imperial", "link": "https://wikipedia.org/wiki/Napol%C3%A9on,_Prince_<PERSON>"}, {"title": "House of Bonaparte", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}]}, {"year": "1890", "text": "The United States Census Bureau begins using <PERSON>'s tabulating machine to count census returns.", "html": "1890 - The <a href=\"https://wikipedia.org/wiki/United_States_Census_Bureau\" title=\"United States Census Bureau\">United States Census Bureau</a> begins using <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Tabulating_machine\" title=\"Tabulating machine\">tabulating machine</a> to count <a href=\"https://wikipedia.org/wiki/Census\" title=\"Census\">census</a> returns.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Census_Bureau\" title=\"United States Census Bureau\">United States Census Bureau</a> begins using <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Tabulating_machine\" title=\"Tabulating machine\">tabulating machine</a> to count <a href=\"https://wikipedia.org/wiki/Census\" title=\"Census\">census</a> returns.", "links": [{"title": "United States Census Bureau", "link": "https://wikipedia.org/wiki/United_States_Census_Bureau"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Tabulating machine", "link": "https://wikipedia.org/wiki/Tabulating_machine"}, {"title": "Census", "link": "https://wikipedia.org/wiki/Census"}]}, {"year": "1913", "text": "The Greek-Serbian Treaty of Alliance is signed, paving the way for the Second Balkan War.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Greek%E2%80%93Serbian_Alliance_of_1913\" title=\"Greek-Serbian Alliance of 1913\">Greek-Serbian Treaty of Alliance</a> is signed, paving the way for the <a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greek%E2%80%93Serbian_Alliance_of_1913\" title=\"Greek-Serbian Alliance of 1913\">Greek-Serbian Treaty of Alliance</a> is signed, paving the way for the <a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>.", "links": [{"title": "Greek-Serbian Alliance of 1913", "link": "https://wikipedia.org/wiki/Greek%E2%80%93Serbian_Alliance_of_1913"}, {"title": "Second Balkan War", "link": "https://wikipedia.org/wiki/Second_Balkan_War"}]}, {"year": "1916", "text": "The United States Senate confirms the appointment of <PERSON> to the United States Supreme Court, making him the first Jew to be an Associate Justice.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> confirms the appointment of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a>, making him the first <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jew</a> to be an <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> confirms the appointment of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to the <a href=\"https://wikipedia.org/wiki/United_States_Supreme_Court\" class=\"mw-redirect\" title=\"United States Supreme Court\">United States Supreme Court</a>, making him the first <a href=\"https://wikipedia.org/wiki/Jews\" title=\"Jews\">Jew</a> to be an <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">Associate Justice</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Brandeis"}, {"title": "United States Supreme Court", "link": "https://wikipedia.org/wiki/United_States_Supreme_Court"}, {"title": "Jews", "link": "https://wikipedia.org/wiki/Jews"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}]}, {"year": "1918", "text": "World War I: Western Front: Battle of Belleau Wood: Allied Forces under <PERSON> and <PERSON> engage Imperial German Forces under <PERSON>, German Crown Prince.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a>: Allied Forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/James_<PERSON>d\" title=\"James Harbord\"><PERSON>d</a> engage Imperial German Forces under <a href=\"https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince\" title=\"<PERSON>, German Crown Prince\"><PERSON>, German Crown Prince</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Belleau_Wood\" title=\"Battle of Belleau Wood\">Battle of Belleau Wood</a>: Allied Forces under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/James_Harbord\" title=\"James Harbord\"><PERSON></a> engage Imperial German Forces under <a href=\"https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince\" title=\"<PERSON>, German Crown Prince\"><PERSON>, German Crown Prince</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Western Front (World War I)", "link": "https://wikipedia.org/wiki/Western_Front_(World_War_I)"}, {"title": "Battle of Belleau Wood", "link": "https://wikipedia.org/wiki/Battle_of_Belleau_Wood"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, German Crown Prince", "link": "https://wikipedia.org/wiki/<PERSON>,_German_Crown_Prince"}]}, {"year": "1919", "text": "Prohibition comes into force in Finland.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Prohibition_in_Finland\" class=\"mw-redirect\" title=\"Prohibition in Finland\">Prohibition</a> comes into force in Finland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prohibition_in_Finland\" class=\"mw-redirect\" title=\"Prohibition in Finland\">Prohibition</a> comes into force in Finland.", "links": [{"title": "Prohibition in Finland", "link": "https://wikipedia.org/wiki/Prohibition_in_Finland"}]}, {"year": "1922", "text": "The Royal Ulster Constabulary is founded.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Royal_Ulster_Constabulary\" title=\"Royal Ulster Constabulary\">Royal Ulster Constabulary</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Ulster_Constabulary\" title=\"Royal Ulster Constabulary\">Royal Ulster Constabulary</a> is founded.", "links": [{"title": "Royal Ulster Constabulary", "link": "https://wikipedia.org/wiki/Royal_Ulster_Constabulary"}]}, {"year": "1929", "text": "The 1st Conference of the Communist Parties of Latin America is held in Buenos Aires.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/1st_Conference_of_the_Communist_Parties_of_Latin_America\" title=\"1st Conference of the Communist Parties of Latin America\">1st Conference of the Communist Parties of Latin America</a> is held in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1st_Conference_of_the_Communist_Parties_of_Latin_America\" title=\"1st Conference of the Communist Parties of Latin America\">1st Conference of the Communist Parties of Latin America</a> is held in <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a>.", "links": [{"title": "1st Conference of the Communist Parties of Latin America", "link": "https://wikipedia.org/wiki/1st_Conference_of_the_Communist_Parties_of_Latin_America"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}]}, {"year": "1930", "text": "The Deccan Queen is introduced as first intercity train between Bombay VT (Now Mumbai CST) and Poona (Pune) to run on electric locomotives.", "html": "1930 - The <a href=\"https://wikipedia.org/wiki/Deccan_Queen\" title=\"Deccan Queen\">Deccan Queen</a> is introduced as first intercity train between Bombay VT (Now <a href=\"https://wikipedia.org/wiki/Mumbai_CST\" class=\"mw-redirect\" title=\"Mumbai CST\">Mumbai CST</a>) and Poona (<a href=\"https://wikipedia.org/wiki/Pune_Junction_railway_station\" title=\"Pune Junction railway station\">Pune</a>) to run on electric locomotives.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Deccan_Queen\" title=\"Deccan Queen\">Deccan Queen</a> is introduced as first intercity train between Bombay VT (Now <a href=\"https://wikipedia.org/wiki/Mumbai_CST\" class=\"mw-redirect\" title=\"Mumbai CST\">Mumbai CST</a>) and Poona (<a href=\"https://wikipedia.org/wiki/Pune_Junction_railway_station\" title=\"Pune Junction railway station\">Pune</a>) to run on electric locomotives.", "links": [{"title": "Deccan Queen", "link": "https://wikipedia.org/wiki/Deccan_Queen"}, {"title": "Mumbai CST", "link": "https://wikipedia.org/wiki/Mumbai_CST"}, {"title": "Pune Junction railway station", "link": "https://wikipedia.org/wiki/Pune_Junction_railway_station"}]}, {"year": "1939", "text": "First flight of the German Focke-Wulf Fw 190 fighter aircraft.", "html": "1939 - First flight of the German <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_190\" title=\"Focke-Wulf Fw 190\">Focke-Wulf Fw 190</a> <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>.", "no_year_html": "First flight of the German <a href=\"https://wikipedia.org/wiki/Focke-Wulf_Fw_190\" title=\"Focke-Wulf Fw 190\">Focke-Wulf Fw 190</a> <a href=\"https://wikipedia.org/wiki/Fighter_aircraft\" title=\"Fighter aircraft\">fighter aircraft</a>.", "links": [{"title": "Focke-<PERSON><PERSON>w 190", "link": "https://wikipedia.org/wiki/Focke-<PERSON><PERSON>_Fw_190"}, {"title": "Fighter aircraft", "link": "https://wikipedia.org/wiki/Fighter_aircraft"}]}, {"year": "1941", "text": "World War II: The Battle of Crete ends as Crete capitulates to Germany.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Crete\" title=\"Battle of Crete\">Battle of Crete</a> ends as <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> capitulates to Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Crete\" title=\"Battle of Crete\">Battle of Crete</a> ends as <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a> capitulates to Germany.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Crete", "link": "https://wikipedia.org/wiki/Battle_of_Crete"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}]}, {"year": "1941", "text": "The Farhud, a massive pogrom in Iraq, starts and as a result, many Iraqi Jews are forced to leave their homes.", "html": "1941 - The <a href=\"https://wikipedia.org/wiki/Farhud\" title=\"Farhud\">Far<PERSON><PERSON></a>, a massive pogrom in Iraq, starts and as a result, many Iraqi Jews are forced to leave their homes.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Farhud\" title=\"Farhud\">Farhud</a>, a massive pogrom in Iraq, starts and as a result, many Iraqi Jews are forced to leave their homes.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Farhud"}]}, {"year": "1943", "text": "BOAC Flight 777 is shot down over the Bay of Biscay by German Junkers Ju 88s, killing British actor <PERSON> and leading to speculation that it was actually an attempt to kill British Prime Minister <PERSON>.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/BOAC_Flight_777\" title=\"BOAC Flight 777\">BOAC Flight 777</a> is shot down over the <a href=\"https://wikipedia.org/wiki/Bay_of_Biscay\" title=\"Bay of Biscay\">Bay of Biscay</a> by German <a href=\"https://wikipedia.org/wiki/Junkers_Ju_88\" title=\"Junkers Ju 88\">Junkers Ju 88s</a>, killing British actor <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a> and leading to speculation that it was actually an attempt to kill British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BOAC_Flight_777\" title=\"BOAC Flight 777\">BOAC Flight 777</a> is shot down over the <a href=\"https://wikipedia.org/wiki/Bay_of_Biscay\" title=\"Bay of Biscay\">Bay of Biscay</a> by German <a href=\"https://wikipedia.org/wiki/Junkers_Ju_88\" title=\"Junkers Ju 88\">Junkers Ju 88s</a>, killing British actor <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a> and leading to speculation that it was actually an attempt to kill British <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "BOAC Flight 777", "link": "https://wikipedia.org/wiki/BOAC_Flight_777"}, {"title": "Bay of Biscay", "link": "https://wikipedia.org/wiki/Bay_of_Biscay"}, {"title": "Junkers Ju 88", "link": "https://wikipedia.org/wiki/Junkers_Ju_88"}, {"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, \"Conducator\" (\"Leader\") of Romania during World War II, is executed.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, \"Conducator\" (\"Leader\") of Romania during World War II, is executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, \"Conducator\" (\"Leader\") of Romania during World War II, is executed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "The Declaration of Conscience speech, by U.S. Senator from Maine, <PERSON>, is delivered in response to <PERSON>'s speech at Wheeling, West Virginia.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Declaration_of_Conscience\" title=\"Declaration of Conscience\">Declaration of Conscience</a> speech, by U.S. Senator from <a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is delivered in response to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s speech at <a href=\"https://wikipedia.org/wiki/Wheeling,_West_Virginia\" title=\"Wheeling, West Virginia\">Wheeling, West Virginia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Declaration_of_Conscience\" title=\"Declaration of Conscience\">Declaration of Conscience</a> speech, by U.S. Senator from <a href=\"https://wikipedia.org/wiki/Maine\" title=\"Maine\">Maine</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, is delivered in response to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s speech at <a href=\"https://wikipedia.org/wiki/Wheeling,_West_Virginia\" title=\"Wheeling, West Virginia\">Wheeling, West Virginia</a>.", "links": [{"title": "Declaration of Conscience", "link": "https://wikipedia.org/wiki/Declaration_of_Conscience"}, {"title": "Maine", "link": "https://wikipedia.org/wiki/Maine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Wheeling, West Virginia", "link": "https://wikipedia.org/wiki/Wheeling,_West_Virginia"}]}, {"year": "1950", "text": "The Chinchaga fire ignites. By September, it would become the largest single fire on record in North America.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Chinchaga_fire\" title=\"Chinchaga fire\">Chinchaga fire</a> ignites. By September, it would become the largest single fire on record in North America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chinchaga_fire\" title=\"Chinchaga fire\">Chinchaga fire</a> ignites. By September, it would become the largest single fire on record in North America.", "links": [{"title": "Chinchaga fire", "link": "https://wikipedia.org/wiki/Chinchaga_fire"}]}, {"year": "1958", "text": "<PERSON> comes out of retirement to lead France by decree for six months.", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> comes out of retirement to lead France by decree for six months.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> comes out of retirement to lead France by decree for six months.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "The Canadian Bank of Commerce and Imperial Bank of Canada merge to form the Canadian Imperial Bank of Commerce, the largest bank merger in Canadian history.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/Canadian_Bank_of_Commerce\" title=\"Canadian Bank of Commerce\">Canadian Bank of Commerce</a> and <a href=\"https://wikipedia.org/wiki/Imperial_Bank_of_Canada\" title=\"Imperial Bank of Canada\">Imperial Bank of Canada</a> merge to form the <a href=\"https://wikipedia.org/wiki/Canadian_Imperial_Bank_of_Commerce\" title=\"Canadian Imperial Bank of Commerce\">Canadian Imperial Bank of Commerce</a>, the largest bank merger in Canadian history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Canadian_Bank_of_Commerce\" title=\"Canadian Bank of Commerce\">Canadian Bank of Commerce</a> and <a href=\"https://wikipedia.org/wiki/Imperial_Bank_of_Canada\" title=\"Imperial Bank of Canada\">Imperial Bank of Canada</a> merge to form the <a href=\"https://wikipedia.org/wiki/Canadian_Imperial_Bank_of_Commerce\" title=\"Canadian Imperial Bank of Commerce\">Canadian Imperial Bank of Commerce</a>, the largest bank merger in Canadian history.", "links": [{"title": "Canadian Bank of Commerce", "link": "https://wikipedia.org/wiki/Canadian_Bank_of_Commerce"}, {"title": "Imperial Bank of Canada", "link": "https://wikipedia.org/wiki/Imperial_Bank_of_Canada"}, {"title": "Canadian Imperial Bank of Commerce", "link": "https://wikipedia.org/wiki/Canadian_Imperial_Bank_of_Commerce"}]}, {"year": "1962", "text": "<PERSON>, former SS officer in Nazi Germany, is hanged in Israel for having committed crimes against humanity, war crimes, and other offenses.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/<PERSON>hutzstaffel\" title=\"Schutzstaffel\">SS</a> officer in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, is hanged in <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> for having committed <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>, and other offenses.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former <a href=\"https://wikipedia.org/wiki/Schutzstaffel\" title=\"Schutzstaffel\">SS</a> officer in <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a>, is hanged in <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> for having committed <a href=\"https://wikipedia.org/wiki/Crimes_against_humanity\" title=\"Crimes against humanity\">crimes against humanity</a>, <a href=\"https://wikipedia.org/wiki/War_crime\" title=\"War crime\">war crimes</a>, and other offenses.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Crimes against humanity", "link": "https://wikipedia.org/wiki/Crimes_against_humanity"}, {"title": "War crime", "link": "https://wikipedia.org/wiki/War_crime"}]}, {"year": "1964", "text": "Kenya becomes a republic with <PERSON><PERSON>  as its first President.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> becomes a republic with <a href=\"https://wikipedia.org/wiki/Jomo_Kenyatta\" title=\"Jomo Kenyatta\"><PERSON><PERSON></a> as its first <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">President</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> becomes a republic with <a href=\"https://wikipedia.org/wiki/Jomo_Kenyatta\" title=\"Jomo Kenyatta\"><PERSON><PERSON></a> as its first <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">President</a>.", "links": [{"title": "Kenya", "link": "https://wikipedia.org/wiki/Kenya"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jomo_Kenyatta"}, {"title": "President (government title)", "link": "https://wikipedia.org/wiki/President_(government_title)"}]}, {"year": "1974", "text": "The Heimlich maneuver for rescuing choking victims is published in the journal Emergency Medicine.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_maneuver\" class=\"mw-redirect\" title=\"Heimlich maneuver\">Heimlich maneuver</a> for rescuing choking victims is published in the journal <i>Emergency Medicine</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_maneuver\" class=\"mw-redirect\" title=\"Heimlich maneuver\">Heimlich maneuver</a> for rescuing choking victims is published in the journal <i>Emergency Medicine</i>.", "links": [{"title": "<PERSON><PERSON><PERSON> maneuver", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "The Patriotic Union of Kurdistan was founded by <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and others.", "html": "1975 - The <a href=\"https://wikipedia.org/wiki/Patriotic_Union_of_Kurdistan\" title=\"Patriotic Union of Kurdistan\">Patriotic Union of Kurdistan</a> was founded by <a href=\"https://wikipedia.org/wiki/Jalal_Talabani\" title=\"<PERSON>alal Talabani\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Fuad_Masum\" title=\"Fuad <PERSON>sum\"><PERSON><PERSON></a> and others.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Patriotic_Union_of_Kurdistan\" title=\"Patriotic Union of Kurdistan\">Patriotic Union of Kurdistan</a> was founded by <a href=\"https://wikipedia.org/wiki/Jalal_Talabani\" title=\"<PERSON>alal Talabani\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Fuad_Masum\" title=\"<PERSON>ad Masum\"><PERSON><PERSON></a> and others.", "links": [{"title": "Patriotic Union of Kurdistan", "link": "https://wikipedia.org/wiki/Patriotic_Union_of_Kurdistan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J<PERSON>l_Talabani"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ad_Masum"}]}, {"year": "1976", "text": "Aeroflot Flight 418 crashes in Bioko, Equatorial Guinea, killing 46.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_418\" title=\"Aeroflot Flight 418\">Aeroflot Flight 418</a> crashes in <a href=\"https://wikipedia.org/wiki/Bioko\" title=\"Bioko\">Bioko</a>, Equatorial Guinea, killing 46.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_418\" title=\"Aeroflot Flight 418\">Aeroflot Flight 418</a> crashes in <a href=\"https://wikipedia.org/wiki/Bioko\" title=\"Bioko\">Bioko</a>, Equatorial Guinea, killing 46.", "links": [{"title": "Aeroflot Flight 418", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_418"}, {"title": "Bioko", "link": "https://wikipedia.org/wiki/Bioko"}]}, {"year": "1978", "text": "The first international applications under the Patent Cooperation Treaty are filed.", "html": "1978 - The first international applications under the <a href=\"https://wikipedia.org/wiki/Patent_Cooperation_Treaty\" title=\"Patent Cooperation Treaty\">Patent Cooperation Treaty</a> are filed.", "no_year_html": "The first international applications under the <a href=\"https://wikipedia.org/wiki/Patent_Cooperation_Treaty\" title=\"Patent Cooperation Treaty\">Patent Cooperation Treaty</a> are filed.", "links": [{"title": "Patent Cooperation Treaty", "link": "https://wikipedia.org/wiki/Patent_Cooperation_Treaty"}]}, {"year": "1979", "text": "The first black-led government of Rhodesia (now Zimbabwe) in 90 years takes power.", "html": "1979 - The first black-led government of <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> (now <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>) in 90 years takes power.", "no_year_html": "The first black-led government of <a href=\"https://wikipedia.org/wiki/Rhodesia\" title=\"Rhodesia\">Rhodesia</a> (now <a href=\"https://wikipedia.org/wiki/Zimbabwe\" title=\"Zimbabwe\">Zimbabwe</a>) in 90 years takes power.", "links": [{"title": "Rhodesia", "link": "https://wikipedia.org/wiki/Rhodesia"}, {"title": "Zimbabwe", "link": "https://wikipedia.org/wiki/Zimbabwe"}]}, {"year": "1980", "text": "Cable News Network (CNN) begins broadcasting.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/CNN\" title=\"CNN\">Cable News Network</a> (CNN) begins <a href=\"https://wikipedia.org/wiki/Broadcasting\" title=\"Broadcasting\">broadcasting</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CNN\" title=\"CNN\">Cable News Network</a> (CNN) begins <a href=\"https://wikipedia.org/wiki/Broadcasting\" title=\"Broadcasting\">broadcasting</a>.", "links": [{"title": "CNN", "link": "https://wikipedia.org/wiki/CNN"}, {"title": "Broadcasting", "link": "https://wikipedia.org/wiki/Broadcasting"}]}, {"year": "1988", "text": "European Central Bank is founded in Brussels.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a> is founded in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a> is founded in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>.", "links": [{"title": "European Central Bank", "link": "https://wikipedia.org/wiki/European_Central_Bank"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}]}, {"year": "1988", "text": "The Intermediate-Range Nuclear Forces Treaty comes into effect.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty\" title=\"Intermediate-Range Nuclear Forces Treaty\">Intermediate-Range Nuclear Forces Treaty</a> comes into effect.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty\" title=\"Intermediate-Range Nuclear Forces Treaty\">Intermediate-Range Nuclear Forces Treaty</a> comes into effect.", "links": [{"title": "Intermediate-Range Nuclear Forces Treaty", "link": "https://wikipedia.org/wiki/Intermediate-Range_Nuclear_Forces_Treaty"}]}, {"year": "1990", "text": "Cold War: <PERSON> and <PERSON> sign a treaty to end chemical weapon production.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign a <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a> to end <a href=\"https://wikipedia.org/wiki/Chemical_weapon\" title=\"Chemical weapon\">chemical weapon</a> production.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign a <a href=\"https://wikipedia.org/wiki/Treaty\" title=\"Treaty\">treaty</a> to end <a href=\"https://wikipedia.org/wiki/Chemical_weapon\" title=\"Chemical weapon\">chemical weapon</a> production.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Treaty", "link": "https://wikipedia.org/wiki/Treaty"}, {"title": "Chemical weapon", "link": "https://wikipedia.org/wiki/Chemical_weapon"}]}, {"year": "1993", "text": "Dobrinja mortar attack: Thirteen are killed and 133 wounded when Serb mortar shells are fired at a soccer game in Dobrinja, west of Sarajevo.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Dobrinja_mortar_attack\" title=\"Dobrinja mortar attack\">Dobrinja mortar attack</a>: Thirteen are killed and 133 wounded when Serb mortar shells are fired at a soccer game in <a href=\"https://wikipedia.org/wiki/Dobrinja\" title=\"Dobrinja\">Dobrinja</a>, west of <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dobrinja_mortar_attack\" title=\"Dobrinja mortar attack\">Dobrinja mortar attack</a>: Thirteen are killed and 133 wounded when Serb mortar shells are fired at a soccer game in <a href=\"https://wikipedia.org/wiki/Dobrinja\" title=\"Dobrinja\">Dobrinja</a>, west of <a href=\"https://wikipedia.org/wiki/Sarajevo\" title=\"Sarajevo\">Sarajevo</a>.", "links": [{"title": "Dobrinja mortar attack", "link": "https://wikipedia.org/wiki/Dobrinja_mortar_attack"}, {"title": "Dobrinja", "link": "https://wikipedia.org/wiki/Dobrinja"}, {"title": "Sarajevo", "link": "https://wikipedia.org/wiki/Sarajevo"}]}, {"year": "1994", "text": "Republic of South Africa becomes a republic in the Commonwealth of Nations.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Republic_of_South_Africa\" class=\"mw-redirect\" title=\"Republic of South Africa\">Republic of South Africa</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic_in_the_Commonwealth_of_Nations\" class=\"mw-redirect\" title=\"Republic in the Commonwealth of Nations\">republic in the Commonwealth of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_South_Africa\" class=\"mw-redirect\" title=\"Republic of South Africa\">Republic of South Africa</a> becomes a <a href=\"https://wikipedia.org/wiki/Republic_in_the_Commonwealth_of_Nations\" class=\"mw-redirect\" title=\"Republic in the Commonwealth of Nations\">republic in the Commonwealth of Nations</a>.", "links": [{"title": "Republic of South Africa", "link": "https://wikipedia.org/wiki/Republic_of_South_Africa"}, {"title": "Republic in the Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Republic_in_the_Commonwealth_of_Nations"}]}, {"year": "1999", "text": "American Airlines Flight 1420 slides and crashes while landing at Little Rock National Airport, killing 11 people on a flight from Dallas to Little Rock.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_1420\" title=\"American Airlines Flight 1420\">American Airlines Flight 1420</a> slides and crashes while landing at <a href=\"https://wikipedia.org/wiki/Little_Rock_National_Airport\" class=\"mw-redirect\" title=\"Little Rock National Airport\">Little Rock National Airport</a>, killing 11 people on a flight from <a href=\"https://wikipedia.org/wiki/Dallas\" title=\"Dallas\">Dallas</a> to <a href=\"https://wikipedia.org/wiki/Little_Rock\" class=\"mw-redirect\" title=\"Little Rock\">Little Rock</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_1420\" title=\"American Airlines Flight 1420\">American Airlines Flight 1420</a> slides and crashes while landing at <a href=\"https://wikipedia.org/wiki/Little_Rock_National_Airport\" class=\"mw-redirect\" title=\"Little Rock National Airport\">Little Rock National Airport</a>, killing 11 people on a flight from <a href=\"https://wikipedia.org/wiki/Dallas\" title=\"Dallas\">Dallas</a> to <a href=\"https://wikipedia.org/wiki/Little_Rock\" class=\"mw-redirect\" title=\"Little Rock\">Little Rock</a>.", "links": [{"title": "American Airlines Flight 1420", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_1420"}, {"title": "Little Rock National Airport", "link": "https://wikipedia.org/wiki/Little_Rock_National_Airport"}, {"title": "Dallas", "link": "https://wikipedia.org/wiki/Dallas"}, {"title": "Little Rock", "link": "https://wikipedia.org/wiki/Little_Rock"}]}, {"year": "2001", "text": "Nepalese royal massacre: Crown Prince <PERSON><PERSON> of Nepal shoots and kills several members of his family including his father and mother.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Nepalese_royal_massacre\" title=\"Nepalese royal massacre\">Nepalese royal massacre</a>: Crown Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> shoots and kills several members of his family including his father and mother.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nepalese_royal_massacre\" title=\"Nepalese royal massacre\">Nepalese royal massacre</a>: Crown Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> shoots and kills several members of his family including his father and mother.", "links": [{"title": "Nepalese royal massacre", "link": "https://wikipedia.org/wiki/Nepalese_royal_massacre"}, {"title": "Dipendra of Nepal", "link": "https://wikipedia.org/wiki/Dipendra_of_Nepal"}]}, {"year": "2001", "text": "Dolphinarium discotheque massacre: A Hamas suicide bomber kills 21 at a disco in Tel Aviv.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Dolphinarium_discotheque_massacre\" title=\"Dolphinarium discotheque massacre\">Dolphinarium discotheque massacre</a>: A <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> suicide bomber kills 21 at a disco in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dolphinarium_discotheque_massacre\" title=\"Dolphinarium discotheque massacre\">Dolphinarium discotheque massacre</a>: A <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a> suicide bomber kills 21 at a disco in <a href=\"https://wikipedia.org/wiki/Tel_Aviv\" title=\"Tel Aviv\">Tel Aviv</a>.", "links": [{"title": "Dolphinarium discotheque massacre", "link": "https://wikipedia.org/wiki/Dolphinarium_discotheque_massacre"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}, {"title": "Tel Aviv", "link": "https://wikipedia.org/wiki/Tel_Aviv"}]}, {"year": "2004", "text": "Oklahoma City bombing co-conspirator <PERSON> is sentenced to 161 consecutive life terms without the possibility of parole.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a> co-conspirator <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 161 consecutive life terms without the possibility of parole.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a> co-conspirator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 161 consecutive life terms without the possibility of parole.", "links": [{"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "A fire on the back lot of Universal Studios breaks out, destroying the attraction King Kong Encounter and a large archive of master tapes for music and film, the full extent of which was not revealed until 2019.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/2008_Universal_fire\" class=\"mw-redirect\" title=\"2008 Universal fire\">A fire on the back lot of Universal Studios</a> breaks out, destroying the attraction <a href=\"https://wikipedia.org/wiki/King_Kong_Encounter\" title=\"King Kong Encounter\">King Kong Encounter</a> and a large archive of master tapes for music and film, the full extent of which was not revealed until 2019.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2008_Universal_fire\" class=\"mw-redirect\" title=\"2008 Universal fire\">A fire on the back lot of Universal Studios</a> breaks out, destroying the attraction <a href=\"https://wikipedia.org/wiki/King_Kong_Encounter\" title=\"King Kong Encounter\">King Kong Encounter</a> and a large archive of master tapes for music and film, the full extent of which was not revealed until 2019.", "links": [{"title": "2008 Universal fire", "link": "https://wikipedia.org/wiki/2008_Universal_fire"}, {"title": "King Kong Encounter", "link": "https://wikipedia.org/wiki/King_Kong_Encounter"}]}, {"year": "2009", "text": "Air France Flight 447 crashes into the Atlantic Ocean off the coast of Brazil on a flight from Rio de Janeiro to Paris. All 228 passengers and crew are killed.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Air_France_Flight_447\" title=\"Air France Flight 447\">Air France Flight 447</a> crashes into the Atlantic Ocean off the coast of Brazil on a flight from <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a> to Paris. All 228 passengers and crew are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_France_Flight_447\" title=\"Air France Flight 447\">Air France Flight 447</a> crashes into the Atlantic Ocean off the coast of Brazil on a flight from <a href=\"https://wikipedia.org/wiki/Rio_de_Janeiro\" title=\"Rio de Janeiro\">Rio de Janeiro</a> to Paris. All 228 passengers and crew are killed.", "links": [{"title": "Air France Flight 447", "link": "https://wikipedia.org/wiki/Air_France_Flight_447"}, {"title": "Rio de Janeiro", "link": "https://wikipedia.org/wiki/Rio_de_Janeiro"}]}, {"year": "2009", "text": "General Motors files for Chapter 11 bankruptcy. It is the fourth largest United States bankruptcy in history.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> <a href=\"https://wikipedia.org/wiki/General_Motors_Chapter_11_reorganization\" title=\"General Motors Chapter 11 reorganization\">files</a> for <a href=\"https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code\" title=\"Chapter 11, Title 11, United States Code\">Chapter 11</a> <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a>. It is the fourth largest United States bankruptcy in history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/General_Motors\" title=\"General Motors\">General Motors</a> <a href=\"https://wikipedia.org/wiki/General_Motors_Chapter_11_reorganization\" title=\"General Motors Chapter 11 reorganization\">files</a> for <a href=\"https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code\" title=\"Chapter 11, Title 11, United States Code\">Chapter 11</a> <a href=\"https://wikipedia.org/wiki/Bankruptcy\" title=\"Bankruptcy\">bankruptcy</a>. It is the fourth largest United States bankruptcy in history.", "links": [{"title": "General Motors", "link": "https://wikipedia.org/wiki/General_Motors"}, {"title": "General Motors Chapter 11 reorganization", "link": "https://wikipedia.org/wiki/General_Motors_Chapter_11_reorganization"}, {"title": "Chapter 11, Title 11, United States Code", "link": "https://wikipedia.org/wiki/Chapter_11,_Title_11,_United_States_Code"}, {"title": "Bankruptcy", "link": "https://wikipedia.org/wiki/Bankruptcy"}]}, {"year": "2011", "text": "A rare tornado outbreak occurs in New England; a strong EF3 tornado strikes Springfield, Massachusetts, during the event, killing four people.", "html": "2011 - A <a href=\"https://wikipedia.org/wiki/2011_New_England_tornado_outbreak\" title=\"2011 New England tornado outbreak\">rare tornado outbreak</a> occurs in <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a>; a strong EF3 tornado strikes <a href=\"https://wikipedia.org/wiki/Springfield,_Massachusetts\" title=\"Springfield, Massachusetts\">Springfield, Massachusetts</a>, during the event, killing four people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2011_New_England_tornado_outbreak\" title=\"2011 New England tornado outbreak\">rare tornado outbreak</a> occurs in <a href=\"https://wikipedia.org/wiki/New_England\" title=\"New England\">New England</a>; a strong EF3 tornado strikes <a href=\"https://wikipedia.org/wiki/Springfield,_Massachusetts\" title=\"Springfield, Massachusetts\">Springfield, Massachusetts</a>, during the event, killing four people.", "links": [{"title": "2011 New England tornado outbreak", "link": "https://wikipedia.org/wiki/2011_New_England_tornado_outbreak"}, {"title": "New England", "link": "https://wikipedia.org/wiki/New_England"}, {"title": "Springfield, Massachusetts", "link": "https://wikipedia.org/wiki/Springfield,_Massachusetts"}]}, {"year": "2011", "text": "Space Shuttle Endeavour makes its final landing after 25 flights.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> makes its final landing after 25 flights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\">Space Shuttle <i>Endeavour</i></a> makes its final landing after 25 flights.", "links": [{"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}]}, {"year": "2015", "text": "A ship carrying 458 people capsizes in the Yangtze river in China's Hubei province, killing 442 people.", "html": "2015 - A ship carrying 458 people <a href=\"https://wikipedia.org/wiki/Sinking_of_Dongfang_zhi_Xing\" title=\"Sinking of Dongfang zhi Xing\">capsizes</a> in the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtz<PERSON>\">Yangtze</a> river in China's <a href=\"https://wikipedia.org/wiki/Hubei\" title=\"Hubei\">Hubei</a> province, killing 442 people.", "no_year_html": "A ship carrying 458 people <a href=\"https://wikipedia.org/wiki/Sinking_of_Dongfang_zhi_Xing\" title=\"Sinking of Dongfang zhi Xing\">capsizes</a> in the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtz<PERSON>\">Yangtze</a> river in China's <a href=\"https://wikipedia.org/wiki/Hubei\" title=\"Hubei\">Hubei</a> province, killing 442 people.", "links": [{"title": "Sinking of Dongfang zhi Xing", "link": "https://wikipedia.org/wiki/Sinking_of_Dongfang_zhi_Xing"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yang<PERSON>e"}, {"title": "Hubei", "link": "https://wikipedia.org/wiki/Hubei"}]}], "Births": [{"year": "1134", "text": "<PERSON>, Count of Nantes (d. 1158)", "html": "1134 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nantes\" title=\"<PERSON>, Count of Nantes\"><PERSON>, Count of Nantes</a> (d. 1158)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Count_of_Nantes\" title=\"<PERSON>, Count of Nantes\"><PERSON>, Count of Nantes</a> (d. 1158)", "links": [{"title": "<PERSON>, Count of Nantes", "link": "https://wikipedia.org/wiki/<PERSON>,_Count_<PERSON>_<PERSON>"}]}, {"year": "1300", "text": "<PERSON> Brotherton, 1st Earl of Norfolk, English politician, Lord Marshal of England (d. 1338)", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brother<PERSON>,_1st_Earl_of_Norfolk\" title=\"<PERSON> Brotherton, 1st Earl of Norfolk\"><PERSON> Brotherton, 1st Earl of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Marshal_of_England\" class=\"mw-redirect\" title=\"Lord Marshal of England\">Lord Marshal of England</a> (d. 1338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brother<PERSON>,_1st_Earl_of_Norfolk\" title=\"<PERSON> Brotherton, 1st Earl of Norfolk\"><PERSON> Brotherton, 1st Earl of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Marshal_of_England\" class=\"mw-redirect\" title=\"Lord Marshal of England\">Lord Marshal of England</a> (d. 1338)", "links": [{"title": "<PERSON> Brotherton, 1st Earl of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Norfolk"}, {"title": "Lord Marshal of England", "link": "https://wikipedia.org/wiki/Lord_Marshal_of_England"}]}, {"year": "1451", "text": "<PERSON>, 1st Baron <PERSON> (d. 1508)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1508)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1460", "text": "<PERSON><PERSON>, Count of East Frisia, German noble (d. 1491)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON><PERSON>, Count of East Frisia\"><PERSON><PERSON>, Count of East Frisia</a>, German noble (d. 1491)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia\" title=\"<PERSON><PERSON> <PERSON>, Count of East Frisia\"><PERSON><PERSON>, Count of East Frisia</a>, German noble (d. 1491)", "links": [{"title": "<PERSON><PERSON>, Count of East Frisia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_East_Frisia"}]}, {"year": "1480", "text": "<PERSON><PERSON><PERSON>, Polish bishop (d. 1550)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/Tied<PERSON>_Giese\" title=\"Tiedemann Giese\"><PERSON><PERSON><PERSON></a>, Polish bishop (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tied<PERSON>_Giese\" title=\"Tiedemann Giese\"><PERSON><PERSON><PERSON></a>, Polish bishop (d. 1550)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1498", "text": "<PERSON><PERSON><PERSON>, Dutch painter (d. 1574)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1574)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (d. 1574)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1522", "text": "<PERSON><PERSON><PERSON>, Dutch writer and scholar (d. 1590)", "html": "1522 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch writer and scholar (d. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch writer and scholar (d. 1590)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1563", "text": "<PERSON>, 1st Earl of Salisbury, English politician, Secretary of State for England (d. 1612)", "html": "1563 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Salisbury\" title=\"<PERSON>, 1st Earl of Salisbury\"><PERSON>, 1st Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">Secretary of State for England</a> (d. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Salisbury\" title=\"<PERSON>, 1st Earl of Salisbury\"><PERSON>, 1st Earl of Salisbury</a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_(England)\" title=\"Secretary of State (England)\">Secretary of State for England</a> (d. 1612)", "links": [{"title": "<PERSON>, 1st Earl of Salisbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Salisbury"}, {"title": "Secretary of State (England)", "link": "https://wikipedia.org/wiki/Secretary_of_State_(England)"}]}, {"year": "1612", "text": "<PERSON><PERSON>, Dutch painter (d. 1680)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/Frans_Post\" title=\"Frans Post\"><PERSON><PERSON></a>, Dutch painter (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frans_Post\" title=\"Frans Post\"><PERSON><PERSON></a>, Dutch painter (d. 1680)", "links": [{"title": "Frans Post", "link": "https://wikipedia.org/wiki/Frans_Post"}]}, {"year": "1633", "text": "<PERSON><PERSON>, Italian astronomer and academic (d. 1687)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer and academic (d. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian astronomer and academic (d. 1687)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1637", "text": "<PERSON>, French missionary and explorer (d. 1675)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and explorer (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and explorer (d. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1653", "text": "<PERSON>, French organist and composer (d. 1704)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French organist and composer (d. 1704)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1675", "text": "<PERSON>, marchese di <PERSON>i, Italian archaeologist and playwright (d. 1755)", "html": "1675 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_di_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, marchese di Maffei\"><PERSON>, marchese di <PERSON></a>, Italian archaeologist and playwright (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_di_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, marchese di Maffei\"><PERSON>, marchese di <PERSON></a>, Italian archaeologist and playwright (d. 1755)", "links": [{"title": "<PERSON>, marchese di Maffei", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marchese_<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, Irish priest and missionary, founded the Irish Christian Brothers (d. 1844)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and missionary, founded the <a href=\"https://wikipedia.org/wiki/Irish_Christian_Brothers\" class=\"mw-redirect\" title=\"Irish Christian Brothers\">Irish Christian Brothers</a> (d. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish priest and missionary, founded the <a href=\"https://wikipedia.org/wiki/Irish_Christian_Brothers\" class=\"mw-redirect\" title=\"Irish Christian Brothers\">Irish Christian Brothers</a> (d. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Irish Christian Brothers", "link": "https://wikipedia.org/wiki/Irish_Christian_Brothers"}]}, {"year": "1765", "text": "<PERSON><PERSON>, mistress and wife of <PERSON> (d. 1816)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, mistress and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, mistress and wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> (d. 1816)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, German author (d. 1849)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, Austrian actor and playwright (d. 1836)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian actor and playwright (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, French physicist and engineer (d. 1832)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and engineer (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, Australian educator and politician, Chief Secretary of New South Wales (d. 1879)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales\" title=\"Chief Secretary of New South Wales\">Chief Secretary of New South Wales</a> (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales\" title=\"Chief Secretary of New South Wales\">Chief Secretary of New South Wales</a> (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Secretary of New South Wales", "link": "https://wikipedia.org/wiki/Chief_Secretary_of_New_South_Wales"}]}, {"year": "1801", "text": "<PERSON>, American religious leader, 2nd President of The Church of Jesus Christ of Latter-day Saints (d. 1877)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 2nd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader, 2nd <a href=\"https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints\" class=\"mw-redirect\" title=\"President of The Church of Jesus Christ of Latter-day Saints\">President of The Church of Jesus Christ of Latter-day Saints</a> (d. 1877)", "links": [{"title": "<PERSON> Young", "link": "https://wikipedia.org/wiki/<PERSON>_Young"}, {"title": "President of The Church of Jesus Christ of Latter-day Saints", "link": "https://wikipedia.org/wiki/President_of_The_Church_of_Jesus_Christ_of_Latter-day_Saints"}]}, {"year": "1804", "text": "<PERSON>, Russian composer (d. 1857)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, English-Australian politician, 3rd Premier of New South Wales (d. 1881)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1881)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1815", "text": "<PERSON> of Greece (d. 1862)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Otto_of_Greece\" title=\"<PERSON> of Greece\"><PERSON> of Greece</a> (d. 1862)", "links": [{"title": "Otto of Greece", "link": "https://wikipedia.org/wiki/Otto_of_Greece"}]}, {"year": "1819", "text": "<PERSON>, Duke of Modena (d. 1875)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Modena\" title=\"<PERSON>, Duke of Modena\"><PERSON>, Duke of Modena</a> (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Modena\" title=\"<PERSON>, Duke of Modena\"><PERSON>, Duke of Modena</a> (d. 1875)", "links": [{"title": "<PERSON>, Duke of Modena", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Modena"}]}, {"year": "1822", "text": "<PERSON><PERSON>, Viscountess <PERSON>, English portrait photographer (d. 1865)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Viscountess_<PERSON>ward<PERSON>\" title=\"<PERSON><PERSON>, Viscountess <PERSON>\"><PERSON><PERSON>, Viscountess <PERSON></a>, English portrait photographer (d. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Viscountess_<PERSON>ward<PERSON>\" title=\"<PERSON><PERSON>, Viscountess Hawarden\"><PERSON><PERSON>, Viscountess <PERSON></a>, English portrait photographer (d. 1865)", "links": [{"title": "<PERSON><PERSON>, Viscountess <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Viscountess_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, American general (d. 1864)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, American general (d. 1879)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American lawyer, associate justice of the U.S. Supreme Court, and politician; Attorney General of Kentucky (d. 1911)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">associate justice</a> of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a>, and politician; <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Kentucky\" title=\"Attorney General of Kentucky\">Attorney General of Kentucky</a> (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, <a href=\"https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States\" title=\"Associate Justice of the Supreme Court of the United States\">associate justice</a> of the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a>, and politician; <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Kentucky\" title=\"Attorney General of Kentucky\">Attorney General of Kentucky</a> (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Associate Justice of the Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Associate_Justice_of_the_Supreme_Court_of_the_United_States"}, {"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "Attorney General of Kentucky", "link": "https://wikipedia.org/wiki/Attorney_General_of_Kentucky"}]}, {"year": "1843", "text": "<PERSON>, Scottish physician and missionary, developed fingerprinting (d. 1930)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and missionary, developed <a href=\"https://wikipedia.org/wiki/Fingerprinting\" class=\"mw-redirect\" title=\"Fingerprinting\">fingerprinting</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and missionary, developed <a href=\"https://wikipedia.org/wiki/Fingerprinting\" class=\"mw-redirect\" title=\"Fingerprinting\">fingerprinting</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Fingerprinting", "link": "https://wikipedia.org/wiki/Fingerprinting"}]}, {"year": "1869", "text": "<PERSON>, German philologist (d. 1915)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_W%C3%BCnsch\" title=\"<PERSON>\"><PERSON></a>, German philologist (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnsch\" title=\"<PERSON>\"><PERSON></a>, German philologist (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Richard_W%C3%BCnsch"}]}, {"year": "1873", "text": "<PERSON>, Bessarabian politician (d. 1955)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bessarabian politician (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bessarabian politician (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Russian botanist (d. 1931)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian botanist (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian botanist (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, English author and poet (d. 1967)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American triathlete and gymnast (d. 1956)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete and gymnast (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American triathlete and gymnast (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ich"}]}, {"year": "1887", "text": "<PERSON>, English actor (d. 1974)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, English linguist and philosopher (d. 1957)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and philosopher (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English linguist and philosopher (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, American actor (d. 1949)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON>, sovereign of the Kingdom of Afghanistan, (d. 1960)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, sovereign of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Afghanistan\" title=\"Kingdom of Afghanistan\">Kingdom of Afghanistan</a>, (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, sovereign of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Afghanistan\" title=\"Kingdom of Afghanistan\">Kingdom of Afghanistan</a>, (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Kingdom of Afghanistan", "link": "https://wikipedia.org/wiki/Kingdom_of_Afghanistan"}]}, {"year": "1896", "text": "<PERSON>, British bandleader (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Sydney_Kyte\" title=\"Sydney Kyte\"><PERSON></a>, British bandleader (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney_Kyte\" title=\"Sydney Kyte\"><PERSON></a>, British bandleader (d. 1981)", "links": [{"title": "Sydney Kyte", "link": "https://wikipedia.org/wiki/Sydney_Kyte"}]}, {"year": "1901", "text": "<PERSON><PERSON> <PERSON>, Canadian ice hockey player, referee, and manager (d. 1990)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Hap_Day\" title=\"Hap Day\">Hap Day</a>, Canadian ice hockey player, referee, and manager (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hap_Day\" title=\"Hap Day\">Hap <PERSON></a>, Canadian ice hockey player, referee, and manager (d. 1990)", "links": [{"title": "Hap Day", "link": "https://wikipedia.org/wiki/Hap_Day"}]}, {"year": "1901", "text": "<PERSON>, Australian rugby league player  (d. 1978)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1978)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1901", "text": "<PERSON>, English-American playwright and director (d. 1957)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American playwright and director (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American playwright and director (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Canadian bishop and martyr (d. 1973)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop and martyr (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Canadian bishop and martyr (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Norwegian linguist and academic (d. 1986)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, Norwegian linguist and academic (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)\" title=\"<PERSON> (linguist)\"><PERSON></a>, Norwegian linguist and academic (d. 1986)", "links": [{"title": "<PERSON> (linguist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(linguist)"}]}, {"year": "1905", "text": "<PERSON>, English-American actor (d. 1956)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Czech philosopher (d. 1977)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dka\" title=\"<PERSON>\"><PERSON></a>, Czech philosopher (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dka\" title=\"<PERSON>\"><PERSON></a>, Czech philosopher (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C4%8Dka"}]}, {"year": "1907", "text": "<PERSON>, English airman and engineer, developed the jet engine (d. 1996)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English airman and engineer, developed the <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English airman and engineer, developed the <a href=\"https://wikipedia.org/wiki/Jet_engine\" title=\"Jet engine\">jet engine</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jet engine", "link": "https://wikipedia.org/wiki/Jet_engine"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovakian-Israeli philologist and linguist (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovakian-Israeli philologist and linguist (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovakian-Israeli philologist and linguist (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Hungarian communist leader, Chairman of the Council of Ministers of the People's Republic of Hungary (d. 1996)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/G<PERSON>la_K%C3%A1llai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian communist leader, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Chairman</a> of the <a href=\"https://wikipedia.org/wiki/Council_of_Ministers_of_the_People%27s_Republic_of_Hungary\" class=\"mw-redirect\" title=\"Council of Ministers of the People's Republic of Hungary\">Council of Ministers of the People's Republic of Hungary</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_K%C3%A1llai\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian communist leader, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Chairman</a> of the <a href=\"https://wikipedia.org/wiki/Council_of_Ministers_of_the_People%27s_Republic_of_Hungary\" class=\"mw-redirect\" title=\"Council of Ministers of the People's Republic of Hungary\">Council of Ministers of the People's Republic of Hungary</a> (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gyula_K%C3%A1llai"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}, {"title": "Council of Ministers of the People's Republic of Hungary", "link": "https://wikipedia.org/wiki/Council_of_Ministers_of_the_People%27s_Republic_of_Hungary"}]}, {"year": "1912", "text": "<PERSON>, Austrian geologist, author, and mountaineer (d. 1987)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist, author, and mountaineer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian geologist, author, and mountaineer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English journalist and politician (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American actor (d. 2004)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2004)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1917", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1920", "text": "<PERSON>, American actor and producer (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American composer and bandleader (d. 1985)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and bandleader (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>iddle\"><PERSON></a>, American composer and bandleader (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American model and actress (d. 1991)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, Swedish singer-songwriter and pianist (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Po<PERSON>_<PERSON>el\" title=\"Povel Ramel\"><PERSON><PERSON></a>, Swedish singer-songwriter and pianist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Povel_<PERSON>el\" title=\"Povel Ramel\"><PERSON><PERSON></a>, Swedish singer-songwriter and pianist (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>vel_Ramel"}]}, {"year": "1924", "text": "<PERSON>, American minister and activist (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Venezuelan teacher (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Dilia_D%C3%ADaz_Cisneros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan teacher (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dilia_D%C3%ADaz_Cisneros\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan teacher (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dilia_D%C3%ADaz_Cisneros"}]}, {"year": "1926", "text": "<PERSON>, English footballer (d. 1994)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor, singer, producer, and screenwriter (d. 2012)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American model and actress (d. 1962)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Monroe\"><PERSON></a>, American model and actress (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English international footballer and teacher (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English international footballer and teacher (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English international footballer and teacher (d. 2011)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1926", "text": "<PERSON>, American soldier and politician, 14th United States Secretary of Health and Human Services (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 14th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services\" title=\"United States Secretary of Health and Human Services\">United States Secretary of Health and Human Services</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Health and Human Services", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Health_and_Human_Services"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Soviet Ukrainian pilot and astronaut (d. 1971)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet Ukrainian pilot and astronaut (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Soviet Ukrainian pilot and astronaut (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ky"}]}, {"year": "1928", "text": "<PERSON>, Australian actor and composer (d. 2014)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and composer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and composer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English actor and screenwriter (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>house\"><PERSON></a>, English actor and screenwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Indian actress (d. 1981)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Narg<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rg<PERSON>\" title=\"<PERSON>rg<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress (d. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nargis"}]}, {"year": "1929", "text": "<PERSON>, American academic and Thirteenth Librarian of Congress (d. 2018)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and Thirteenth Librarian of Congress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and Thirteenth Librarian of Congress (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, New Zealand cricketer (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English actor (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Austrian footballer (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian footballer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, New Zealand cricketer (d. 2023)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American historian and critic (d. 1994)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and critic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and critic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Palauan politician, 1st President of Palau (d. 1985)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palauan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Palau\" title=\"President of Palau\">President of Palau</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palauan politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Palau\" title=\"President of Palau\">President of Palau</a> (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>iik"}, {"title": "President of Palau", "link": "https://wikipedia.org/wiki/President_of_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American lieutenant and politician (d. 2010)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American lieutenant and politician (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)\" title=\"<PERSON> (Texas politician)\"><PERSON></a>, American lieutenant and politician (d. 2010)", "links": [{"title": "<PERSON> (Texas politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Texas_politician)"}]}, {"year": "1934", "text": "<PERSON>, American singer-songwriter and actor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor, director, producer and screenwriter (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer and screenwriter (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American author (d. 2002)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, <PERSON> of Thames Bank, English architect, founded Foster and Partners", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Thames_Bank\" title=\"<PERSON>, Baron <PERSON> of Thames Bank\"><PERSON>, Baron <PERSON> of Thames Bank</a>, English architect, founded <a href=\"https://wikipedia.org/wiki/Foster_and_Partners\" title=\"Foster and Partners\">Foster and Partners</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Thames_Bank\" title=\"<PERSON>, Baron <PERSON> of Thames Bank\"><PERSON>, Baron <PERSON> of Thames Bank</a>, English architect, founded <a href=\"https://wikipedia.org/wiki/Foster_and_Partners\" title=\"Foster and Partners\">Foster and Partners</a>", "links": [{"title": "<PERSON>, <PERSON> of Thames Bank", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Thames_Bank"}, {"title": "Foster and Partners", "link": "https://wikipedia.org/wiki/Foster_and_Partners"}]}, {"year": "1935", "text": "<PERSON>, American minister and television host (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Reverend_<PERSON><PERSON>\" title=\"Reverend <PERSON><PERSON>\">Reverend <PERSON><PERSON></a>, American minister and television host (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reverend_<PERSON><PERSON>\" title=\"Reverend <PERSON><PERSON>\">Reverend <PERSON><PERSON></a>, American minister and television host (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American computer scientist and academic (d. 2013)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Soviet and Russian wrestler (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Ana<PERSON><PERSON>_Albul\" title=\"Anatoly Albul\"><PERSON><PERSON><PERSON></a>, Soviet and Russian wrestler (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Anatoly Albul\"><PERSON><PERSON><PERSON></a>, Soviet and Russian wrestler (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anatoly_Albul"}]}, {"year": "1936", "text": "<PERSON>, Canadian politician (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Bosnian actor (d. 2010)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian actor (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English illustrator and animator", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and animator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and animator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American actor and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Morgan <PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Freeman\" title=\"Morgan Freeman\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Freeman"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Irish actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Australian neuroscientist and author (d. 2015)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian neuroscientist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian neuroscientist and author (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, American actor and comedian (d. 1992)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Little\"><PERSON><PERSON><PERSON></a>, American actor and comedian (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Little\"><PERSON><PERSON><PERSON></a>, American actor and comedian (d. 1992)", "links": [{"title": "Cleavon Little", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Little"}]}, {"year": "1940", "text": "<PERSON>, American actor (d. 2019)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>jon<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Greek writer and actress (d. 1993)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek writer and actress (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek writer and actress (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katerina_Gogou"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American physicist, astronomer, and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist, astronomer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physicist, astronomer, and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and manager (d. 2015)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Japanese architect, designed the Torre Realia BCN and Hotel Porta Fira", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Torre_Realia_BCN\" title=\"Torre Realia BCN\">Torre Realia BCN</a> and <a href=\"https://wikipedia.org/wiki/Hotel_Porta_Fira\" title=\"Hotel Porta Fira\">Hotel Porta Fira</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese architect, designed the <a href=\"https://wikipedia.org/wiki/Torre_Realia_BCN\" title=\"Torre Realia BCN\">Torre Realia BCN</a> and <a href=\"https://wikipedia.org/wiki/Hotel_Porta_Fira\" title=\"Hotel Porta Fira\">Hotel Porta Fira</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}, {"title": "Torre Realia BCN", "link": "https://wikipedia.org/wiki/Torre_Realia_BCN"}, {"title": "Hotel Porta Fira", "link": "https://wikipedia.org/wiki/Hotel_Porta_Fira"}]}, {"year": "1941", "text": "<PERSON>, Russian physicist and astronomer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and astronomer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Pakistani-English physician and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani-English physician and academic", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Italian singer and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Orietta_Berti"}]}, {"year": "1943", "text": "<PERSON>, American pianist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, South African cricketer (d. 2004)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot\" title=\"Lorrie Wilmot\"><PERSON><PERSON></a>, South African cricketer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot\" title=\"Lorrie Wilmot\"><PERSON><PERSON></a>, South African cricketer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wilmot"}]}, {"year": "1944", "text": "<PERSON>, British neurobiologist (d. 2022)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British neurobiologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British neurobiologist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Chinese-Hong Kong actress (d. 2008)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-Hong Kong actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American soprano and actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1947", "text": "<PERSON>, English businessman, founded the McLaren Group", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/McLaren_Group\" title=\"McLaren Group\">McLaren Group</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/McLaren_Group\" title=\"McLaren Group\">McLaren Group</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McLaren Group", "link": "https://wikipedia.org/wiki/McLaren_Group"}]}, {"year": "1947", "text": "<PERSON>, Welsh actor and singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English guitarist, songwriter, and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actor (d. 2017)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Booth<PERSON>\"><PERSON></a>, American actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boothe\"><PERSON></a>, American actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Powers_Boothe"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Czech Roman Catholic priest, philosopher, theologian and scholar", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hal%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech Roman Catholic priest, philosopher, theologian and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hal%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech Roman Catholic priest, philosopher, theologian and scholar", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Hal%C3%ADk"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player (d. 2006)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Canadian businessman and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"Cha<PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1950", "text": "<PERSON>, English educator and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American author and screenwriter (d. 1999)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (d. 1999)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/%C5%9Eenol_G%C3%BCne%C5%9F\" title=\"Şenol Güneş\"><PERSON><PERSON><PERSON> Gü<PERSON>ş</a>, Turkish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9Eenol_G%C3%BCne%C5%9F\" title=\"Şenol Güneş\"><PERSON><PERSON><PERSON>ü<PERSON>ş</a>, Turkish footballer and manager", "links": [{"title": "Şenol Güneş", "link": "https://wikipedia.org/wiki/%C5%9Eenol_G%C3%BCne%C5%9F"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Romanian shot putter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian shot putter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian shot putter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American entrepreneur and race car driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ted Field\"><PERSON></a>, American entrepreneur and race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ted Field\"><PERSON></a>, American entrepreneur and race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ted_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American serial killer and arsonist: 73 ", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and arsonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and arsonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, <PERSON> of Derwent, English lawyer and judge", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lady_<PERSON>_of_Derwent\" title=\"<PERSON>, <PERSON> of Derwent\"><PERSON>, <PERSON> of Derwent</a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lady_<PERSON>_of_Derwent\" title=\"<PERSON>, <PERSON> of Derwent\"><PERSON>, Lady <PERSON> of Derwent</a>, English lawyer and judge", "links": [{"title": "<PERSON>, <PERSON> of Derwent", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Derwen<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler (d. 2016)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>of<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>of<PERSON><PERSON>_<PERSON>gu\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yonof<PERSON><PERSON>_<PERSON>tsugu"}]}, {"year": "1955", "text": "<PERSON>, New Zealand runner", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist, 26th White House Press Secretary (d. 2008)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 26th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 26th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1956", "text": "<PERSON>, French writer and journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, German author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Petra_<PERSON>bach"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Mongolian lawyer and politician, 3rd President of Mongolia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Nam<PERSON><PERSON>_En<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nam<PERSON><PERSON>_En<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongolian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Mongolia\" title=\"President of Mongolia\">President of Mongolia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nam<PERSON><PERSON>_<PERSON>ar"}, {"title": "President of Mongolia", "link": "https://wikipedia.org/wiki/President_of_Mongolia"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON><PERSON>, Belarusian triple jumper (d. 2019)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian triple jumper (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belarusian triple jumper (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English racing driver and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English musician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian ice hockey player and coach (d. 2012)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and coach (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian footballer and manager", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1960)\" title=\"<PERSON> (footballer, born 1960)\"><PERSON></a>, Russian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1960)\" title=\"<PERSON> (footballer, born 1960)\"><PERSON></a>, Russian footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1960)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1960)"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Cypriot politician, 8th Cypriot Minister of Foreign Affairs", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs of Cyprus\">Cypriot Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cypriot politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_Cyprus\" class=\"mw-redirect\" title=\"List of Ministers of Foreign Affairs of Cyprus\">Cypriot Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers of Foreign Affairs of Cyprus", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Foreign_Affairs_of_Cyprus"}]}, {"year": "1960", "text": "<PERSON>, American politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Russian gymnast (d. 2006)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian ice hockey player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1961", "text": "<PERSON>, Swiss shot putter and bobsledder", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnth%C3%B6r\" title=\"<PERSON>\"><PERSON></a>, Swiss shot putter and bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCnth%C3%B6r\" title=\"<PERSON>\"><PERSON></a>, Swiss shot putter and bobsledder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Werner_G%C3%BCnth%C3%B6r"}]}, {"year": "1961", "text": "<PERSON>, American golfer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_(golfer)"}]}, {"year": "1961", "text": "<PERSON>, Slovakian-German pianist and composer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Slovakian-German pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Slovakian-German pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Russian oligarch, mercenary chief and restaurateur (d. 2023)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian oligarch, mercenary chief and restaurateur (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian oligarch, mercenary chief and restaurateur (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ye<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Borkelmans\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Borkelmans\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mans"}]}, {"year": "1963", "text": "<PERSON>, Scottish physicist and academic", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish physicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English actor and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Russian skier", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Russian sprinter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American football player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian actor and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, German aviator", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German aviator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German aviator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Mexican footballer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Postigo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Postigo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Garc%C3%ADa_Postigo"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>,  American soccer player, manager, and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player, manager, and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON>, British-Barbadian soca singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Barbadian soca singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hi<PERSON>\"><PERSON></a>, British-Barbadian soca singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>,  Cuban-American actor and singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian swimmer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>eve"}]}, {"year": "1973", "text": "<PERSON>, Australian actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, German-American model, fashion designer, and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American model, fashion designer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American model, fashion designer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Canadian-American singer-songwriter, guitarist, producer, and actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter, guitarist, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American singer-songwriter, guitarist, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1974", "text": "<PERSON>, Danish cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Danish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cyclist)\" title=\"<PERSON> (cyclist)\"><PERSON></a>, Danish cyclist", "links": [{"title": "<PERSON> (cyclist)", "link": "https://wikipedia.org/wiki/<PERSON>(cyclist)"}]}, {"year": "1974", "text": "<PERSON>, English politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Czech-Swiss ice hockey player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ro%C5%A1ek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Swiss ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ro%C5%A1ek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-Swiss ice hockey player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Michal_Gro%C5%A1ek"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, German politician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Petry\"><PERSON><PERSON><PERSON></a>, German politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Fr<PERSON><PERSON> Petry\"><PERSON><PERSON><PERSON></a>, German politician", "links": [{"title": "Fr<PERSON>ke Petry", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Petry"}]}, {"year": "1976", "text": "<PERSON><PERSON>, English sprinter and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Marlon_Devonish\" title=\"Marlon Devonish\"><PERSON><PERSON></a>, English sprinter and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marlon_Devonish\" title=\"Marlon Devonish\"><PERSON><PERSON></a>, English sprinter and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marlon_Devonish"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Russian and Kyrgyzstani freestyle wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian and Kyrgyzstani freestyle wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian and Kyrgyzstani freestyle wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American baseball player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Italian high jumper", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Anton<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Antonietta Di <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anton<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Antonietta Di <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian high jumper", "links": [{"title": "Antonietta Di <PERSON>o", "link": "https://wikipedia.org/wiki/Antonietta_Di_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swedish game designer, founded Mojang", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish game designer, founded <a href=\"https://wikipedia.org/wiki/Mojang\" class=\"mw-redirect\" title=\"Mojang\">Mojang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish game designer, founded <a href=\"https://wikipedia.org/wiki/Mojang\" class=\"mw-redirect\" title=\"Mojang\">Mojang</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mojan<PERSON>", "link": "https://wikipedia.org/wiki/Mojang"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brandi_Carlile"}]}, {"year": "1981", "text": "<PERSON>, American comedian and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Venezuelan baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Russian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Belgian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Chilean footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Dutch racing driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American comedian", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Ethiopian runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tirunesh_Dibaba\" title=\"Tirunesh Dibaba\">Tirune<PERSON></a>, Ethiopian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tirunesh_Dibaba\" title=\"Tirunesh Dibaba\">Tirune<PERSON></a>, Ethiopian runner", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tirunesh_Dibaba"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1986", "text": "<PERSON>, Kenyan runner", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chinedu_Obasi\" title=\"Chinedu Obasi\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chinedu_Obasi\" title=\"Chinedu Obasi\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chinedu_Obasi"}]}, {"year": "1986", "text": "<PERSON>, New Zealand rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Slovakian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Hars%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zolt%C3%A1n_Hars%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovakian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zolt%C3%A1n_Hars%C3%A1nyi"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Cuban pole vaulter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Mexican footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_Hern%C3%A1ndez"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Ukrainian/Russian volleyball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(volleyball)\" title=\"<PERSON><PERSON> (volleyball)\"><PERSON><PERSON></a>, Ukrainian/Russian <a href=\"https://wikipedia.org/wiki/Volleyball\" title=\"Volleyball\">volleyball</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(volleyball)\" title=\"<PERSON><PERSON> (volleyball)\"><PERSON><PERSON></a>, Ukrainian/Russian <a href=\"https://wikipedia.org/wiki/Volleyball\" title=\"Volleyball\">volleyball</a> player", "links": [{"title": "<PERSON><PERSON> (volleyball)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(volleyball)"}, {"title": "Volleyball", "link": "https://wikipedia.org/wiki/Volleyball"}]}, {"year": "1989", "text": "<PERSON>, Kenyan runner", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Ecuadoran footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1os\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Ecuadoran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1os\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Ecuadoran footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miller_Bola%C3%B1os"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Spanish golfer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ciganda\"><PERSON><PERSON></a>, Spanish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>iganda\" title=\"<PERSON><PERSON> Ciganda\"><PERSON><PERSON></a>, Spanish golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Carlota_Ciganda"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, German-American actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON><PERSON>_Taishi"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Lithuanian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1999", "text": "Technoblade, American YouTuber and streamer (d. 2022)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Technoblade\" title=\"Technoblade\">Technoblade</a>, American YouTuber and streamer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Technoblade\" title=\"Technoblade\">Technoblade</a>, American YouTuber and streamer (d. 2022)", "links": [{"title": "Technoblade", "link": "https://wikipedia.org/wiki/Technoblade"}]}], "Deaths": [{"year": "195 BC", "text": "Emperor <PERSON><PERSON> of Han (b. 256 BC)", "html": "195 BC - 195 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a> (b. 256 BC)", "no_year_html": "195 BC - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han\" title=\"Emperor <PERSON><PERSON> of Han\">Emperor <PERSON><PERSON> of Han</a> (b. 256 BC)", "links": [{"title": "Emperor <PERSON><PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Han"}]}, {"year": "193", "text": "<PERSON>, Roman Emperor (b. 133)", "html": "193 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Didius <PERSON>us\"><PERSON></a>, Roman Emperor (b. 133)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Didius <PERSON>us\"><PERSON></a>, Roman Emperor (b. 133)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "352", "text": "<PERSON><PERSON>, Emperor of Ran Wei during the Sixteen Kingdoms", "html": "352 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Emperor of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Sixteen_Kingdoms\" title=\"Sixteen Kingdoms\">Sixteen Kingdoms</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Emperor of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Sixteen_Kingdoms\" title=\"Sixteen Kingdoms\">Sixteen Kingdoms</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sixteen Kingdoms", "link": "https://wikipedia.org/wiki/Sixteen_Kingdoms"}]}, {"year": "654", "text": "<PERSON><PERSON><PERSON><PERSON>, patriarch of Constantinople", "html": "654 - <a href=\"https://wikipedia.org/wiki/Pyrrhus_of_Constantinople\" title=\"Pyrrhus of Constantinople\">Pyrr<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">patriarch of Constantinople</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pyrrhus_of_Constantinople\" title=\"Pyrrhus of Constantinople\">Pyrr<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople\" title=\"Ecumenical Patriarch of Constantinople\">patriarch of Constantinople</a>", "links": [{"title": "Pyrrhus of Constantinople", "link": "https://wikipedia.org/wiki/Pyrrhus_of_Constantinople"}, {"title": "Ecumenical Patriarch of Constantinople", "link": "https://wikipedia.org/wiki/Ecumenical_Patriarch_of_Constantinople"}]}, {"year": "829", "text": "<PERSON>, general of the Tang Dynasty", "html": "829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Li Tongjie\"><PERSON></a>, general of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Tongjie\"><PERSON></a>, general of the Tang Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Li_Tongjie"}]}, {"year": "847", "text": "<PERSON>, empress of the Tang Dynasty", "html": "847 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Tang_dynasty)\" title=\"Empress <PERSON><PERSON> (Tang dynasty)\"><PERSON></a>, empress of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Tang_dynasty)\" title=\"Empress <PERSON><PERSON> (Tang dynasty)\"><PERSON></a>, empress of the Tang Dynasty", "links": [{"title": "Empress <PERSON><PERSON> (Tang dynasty)", "link": "https://wikipedia.org/wiki/Empress_<PERSON><PERSON>_<PERSON>_(Tang_dynasty)"}]}, {"year": "896", "text": "<PERSON><PERSON><PERSON>, Syriac Orthodox patriarch of Antioch", "html": "896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Romanus\" title=\"<PERSON><PERSON>ius Romanus\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox</a> patriarch of <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Romanus\" title=\"<PERSON><PERSON><PERSON> Romanus\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox</a> patriarch of <a href=\"https://wikipedia.org/wiki/Antioch\" title=\"Antioch\">Antioch</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>us"}, {"title": "Syriac Orthodox Church", "link": "https://wikipedia.org/wiki/Syriac_Orthodox_Church"}, {"title": "Antioch", "link": "https://wikipedia.org/wiki/Antioch"}]}, {"year": "932", "text": "<PERSON><PERSON><PERSON><PERSON>, duke of Saxony", "html": "932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Count_of_Merseburg\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Merseburg\"><PERSON><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Saxony\" title=\"Duchy of Saxony\">Saxony</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Count_of_Merseburg\" title=\"<PERSON><PERSON><PERSON><PERSON>, Count of Merseburg\"><PERSON><PERSON><PERSON><PERSON></a>, duke of <a href=\"https://wikipedia.org/wiki/Duchy_of_Saxony\" title=\"Duchy of Saxony\">Saxony</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Count of Merseburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>,_Count_of_Merseburg"}, {"title": "Duchy of Saxony", "link": "https://wikipedia.org/wiki/Duchy_of_Saxony"}]}, {"year": "1146", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou, Duchess regent of Brittany (b. 1068)", "html": "1146 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>arde_of_Anjou_(d._1146)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou (d. 1146)\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou</a>, Duchess regent of Brittany (b. 1068)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_of_Anjou_(d._1146)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou (d. 1146)\"><PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou</a>, Duchess regent of Brittany (b. 1068)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> of Anjou (d. 1146)", "link": "https://wikipedia.org/wiki/Ermengard<PERSON>_of_Anjou_(d._1146)"}]}, {"year": "1186", "text": "<PERSON><PERSON>, Japanese warlord", "html": "1186 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yukiie\" title=\"Minamoto no Yukiie\"><PERSON><PERSON> no Yukiie</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> warlord", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_no_Yukiie\" title=\"Minamoto no Yukiie\"><PERSON><PERSON> no Yukiie</a>, <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japanese</a> warlord", "links": [{"title": "<PERSON><PERSON> no <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>ie"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1220", "text": "<PERSON>, 1st Earl of Hereford (b. 1176)", "html": "1220 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Hereford\" title=\"<PERSON>, 1st Earl of Hereford\"><PERSON>, 1st Earl of Hereford</a> (b. 1176)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Earl_of_Hereford\" title=\"<PERSON>, 1st Earl of Hereford\"><PERSON>, 1st Earl of Hereford</a> (b. 1176)", "links": [{"title": "<PERSON>, 1st Earl of Hereford", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Hereford"}]}, {"year": "1310", "text": "<PERSON>, French mystic", "html": "1310 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mystic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1354", "text": "<PERSON><PERSON><PERSON> (b. 1293)", "html": "1354 - <a href=\"https://wikipedia.org/wiki/Kitaba<PERSON>_Chikafusa\" title=\"Kitabatake Chikafusa\">Kit<PERSON><PERSON> Chikafusa</a> (b. 1293)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kitaba<PERSON>_Chikafusa\" title=\"Kitaba<PERSON> Chikafusa\"><PERSON><PERSON><PERSON> Chikafusa</a> (b. 1293)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kit<PERSON><PERSON>_Chi<PERSON>"}]}, {"year": "1434", "text": "King <PERSON><PERSON><PERSON><PERSON> II of Poland", "html": "1434 - King <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_Jagie%C5%82%C5%82o\" title=\"Władysław II Jagiełło\">Wlad<PERSON>laus II of Poland</a>", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_Jagie%C5%82%C5%82o\" title=\"<PERSON>ła<PERSON>sław II Jagiełło\"><PERSON><PERSON><PERSON><PERSON> II of Poland</a>", "links": [{"title": "Władysław II Jagiełło", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_II_Jagie%C5%82%C5%82o"}]}, {"year": "1449", "text": "<PERSON><PERSON><PERSON>, Lady of Rimini (b. 1428)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/Polissena_Sforza\" title=\"Polissena Sforza\"><PERSON><PERSON><PERSON></a>, Lady of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1428)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polissena_Sforza\" title=\"Polissena Sforza\"><PERSON><PERSON><PERSON></a>, Lady of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1428)", "links": [{"title": "Polissena Sforza", "link": "https://wikipedia.org/wiki/Polissena_Sforza"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rimini"}]}, {"year": "1571", "text": "<PERSON>, English martyr (b. 1504)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English martyr (b. 1504)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, English martyr (b. 1504)", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1616", "text": "<PERSON>, Japanese shogun (b. 1543)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa <PERSON>u</a>, Japanese shogun (b. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_Ieyasu\" title=\"Tokugawa Ieyasu\">Tokugawa <PERSON>u</a>, Japanese shogun (b. 1543)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>u"}]}, {"year": "1625", "text": "<PERSON><PERSON>, French author and poet (b. 1568)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9\" title=\"<PERSON><PERSON>'<PERSON>\"><PERSON><PERSON></a>, French author and poet (b. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9\" title=\"<PERSON><PERSON>'Urf<PERSON>\"><PERSON><PERSON></a>, French author and poet (b. 1568)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Honor%C3%A9_d%27Urf%C3%A9"}]}, {"year": "1639", "text": "<PERSON><PERSON><PERSON>, German composer (b. 1579)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German composer (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German composer (b. 1579)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, English-American martyr (b. 1611)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American martyr (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American martyr (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1662", "text": "<PERSON>, Chinese emperor (b. 1623)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese emperor (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1681", "text": "<PERSON><PERSON><PERSON>, Dutch genre painter (b. 1607)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/Cornelis_Saftleven\" title=\"Cornelis Saftleven\"><PERSON><PERSON><PERSON></a>, Dutch genre painter (b. 1607)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cornelis_Saftleven\" title=\"Cornelis Saftleven\"><PERSON><PERSON><PERSON></a>, Dutch genre painter (b. 1607)", "links": [{"title": "Cornelis <PERSON>lev<PERSON>", "link": "https://wikipedia.org/wiki/Cornelis_Saftleven"}]}, {"year": "1710", "text": "<PERSON>, Scottish admiral and politician (b. 1642)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Scottish admiral and politician (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a>, Scottish admiral and politician (b. 1642)", "links": [{"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}]}, {"year": "1740", "text": "<PERSON>, Swiss theologian (b. 1657)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian (b. 1657)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, American pastor and academic (b. 1689)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and academic (b. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and academic (b. 1689)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1773", "text": "<PERSON><PERSON><PERSON><PERSON>, South African folk hero (b. 1708)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>olt<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African folk hero (b. 1708)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>olt<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African folk hero (b. 1708)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON><PERSON>_<PERSON>emade"}]}, {"year": "1795", "text": "<PERSON><PERSON><PERSON>, French anatomist and surgeon (b. 1744)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French anatomist and surgeon (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French anatomist and surgeon (b. 1744)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1815", "text": "<PERSON><PERSON><PERSON>, French general and politician, French Minister of War (b. 1753)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON>, French general and politician, French Minister of War (b. 1770)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(France)\" class=\"mw-redirect\" title=\"Minister of Defence (France)\">French Minister of War</a> (b. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Defence (France)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(France)"}]}, {"year": "1826", "text": "<PERSON><PERSON> <PERSON><PERSON>, French pastor and philanthropist (b. 1740)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. F. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, French pastor and philanthropist (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON>._<PERSON>\" title=\"J. F. O<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, French pastor and philanthropist (b. 1740)", "links": [{"title": "J. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON><PERSON>_O<PERSON>lin"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian religious leader (b. 1781)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/Swami<PERSON>ayan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader (b. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swami<PERSON>ayan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian religious leader (b. 1781)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1833", "text": "<PERSON>, American lawyer and politician, 2nd United States Secretary of the Treasury, 24th Governor of Connecticut (b. 1760)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>, 24th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a> (b. 1760)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1841", "text": "<PERSON>, Scottish painter and academic (b. 1785)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish painter and academic (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish painter and academic (b. 1785)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1846", "text": "<PERSON> (b. 1765)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XVI\" title=\"Pope Gregory XVI\"><PERSON> <PERSON> XVI</a> (b. 1765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XVI\"><PERSON> <PERSON> XVI</a> (b. 1765)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American captain (b. 1825)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"John <PERSON>\"><PERSON></a>, American captain (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Chinese rebel, led the Taiping Rebellion (b. 1812)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Hong_Xiu<PERSON>n\" title=\"Hong Xi<PERSON>n\"><PERSON></a>, Chinese rebel, led the <a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a> (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hong_Xiu<PERSON>n\" title=\"Hong Xi<PERSON>\"><PERSON></a>, Chinese rebel, led the <a href=\"https://wikipedia.org/wiki/Taiping_Rebellion\" title=\"Taiping Rebellion\">Taiping Rebellion</a> (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hong_<PERSON>"}, {"title": "Taiping Rebellion", "link": "https://wikipedia.org/wiki/Taiping_Rebellion"}]}, {"year": "1868", "text": "<PERSON>, American lawyer and politician, 15th President of the United States (b. 1791)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 15th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1872", "text": "<PERSON>, Sr., American publisher, founded the New York Herald  (b. 1795)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American publisher, founded the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> (b. 1795)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}, {"title": "New York Herald", "link": "https://wikipedia.org/wiki/New_York_Herald"}]}, {"year": "1873", "text": "<PERSON>, Canadian journalist and politician, 5th Premier of Nova Scotia (b. 1804)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1876", "text": "<PERSON><PERSON>, Bulgarian poet and journalist (b. 1848)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and journalist (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet and journalist (b. 1848)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>v"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON> of France (b. 1856)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Napol%C3%A<PERSON><PERSON>,_Prince_Imperial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Prince <PERSON></a> of France (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napol%C3%A<PERSON><PERSON>,_Prince_Imperial\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>, Prince <PERSON>\"><PERSON><PERSON><PERSON><PERSON>, Prince <PERSON></a> of France (b. 1856)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>, Prince Imperial", "link": "https://wikipedia.org/wiki/Napol%C3%A9on,_Prince_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American painter (b. 1867)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American politician, 28th Vice President of the United States (b. 1854)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 28th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 28th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1927", "text": "<PERSON>, American accused murderer (b. 1860)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accused murderer (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American accused murderer (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON>, Irish historian, philologist, and scholar (b. 1861)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. B. Bury\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish historian, philologist, and scholar (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON>\" title=\"J. B. Bury\"><PERSON><PERSON> <PERSON><PERSON></a>, Irish historian, philologist, and scholar (b. 1861)", "links": [{"title": "J. B. Bury", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "Sir <PERSON>, 3rd Baronet, English colonel and polo player (b. 1867)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English colonel and polo player (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_3rd_Baronet\" title=\"Sir <PERSON>, 3rd Baronet\">Sir <PERSON>, 3rd Baronet</a>, English colonel and polo player (b. 1867)", "links": [{"title": "Sir <PERSON>, 3rd Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_3rd_Baronet"}]}, {"year": "1935", "text": "<PERSON>, Romanian-Hungarian general (b. 1857)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>u%C3%9Fenburg\" title=\"<PERSON> Straußenburg\"><PERSON>nburg</a>, Romanian-Hungarian general (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%9Fenburg\" title=\"<PERSON> Straußenburg\"><PERSON> von <PERSON>raußenburg</a>, Romanian-Hungarian general (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>%C3%9Fenburg"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian-French author and playwright (b. 1901)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON>_<PERSON>rv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-French author and playwright (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_von_<PERSON>rv%C3%A1th\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian-French author and playwright (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_von_Horv%C3%A1th"}]}, {"year": "1941", "text": "<PERSON>, German neurologist and academic (b. 1873)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neurologist and academic (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, New Zealand-English author (b. 1884)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English author (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English actor, director, and producer (b. 1893)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and producer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor, director, and producer (b. 1893)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, English-German businessman and philanthropist (b. 1899)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Wilfrid_Israel\" title=\"Wilfrid Israel\">W<PERSON><PERSON><PERSON></a>, English-German businessman and philanthropist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilfrid_Israel\" title=\"Wilfrid Israel\">W<PERSON><PERSON><PERSON></a>, English-German businessman and philanthropist (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilfrid_Israel"}]}, {"year": "1946", "text": "<PERSON>, Romanian marshal and politician, 43rd Prime Minister of Romania (b. 1882)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian marshal and politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian marshal and politician, 43rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a> (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1948", "text": "<PERSON>, Russian-American cartoonist (b. 1900)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American cartoonist (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American cartoonist (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American psychologist and philosopher (b. 1859)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and philosopher (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Croatian painter and illustrator (b. 1870)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter and illustrator (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter and illustrator (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vidovi%C4%87"}]}, {"year": "1954", "text": "<PERSON>, Danish-German journalist and author (b. 1869)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ex%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Danish-German journalist and author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B8\" title=\"<PERSON>\"><PERSON></a>, Danish-German journalist and author (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Nex%C3%B8"}]}, {"year": "1960", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1883)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German-Austrian sister of <PERSON> (b. 1896)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian sister of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian sister of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, a German Nazi SS-Obersturmbannführer  (b. 1906)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a German <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi</a> <a href=\"https://wikipedia.org/wiki/<PERSON>hutzstaffel\" title=\"Schutzstaffel\">SS</a>-<i><a href=\"https://wikipedia.org/wiki/Obersturmbannf%C3%BChrer\" title=\"Obersturmbannführer\"><PERSON>bersturmbannführer</a></i> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a German <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi</a> <a href=\"https://wikipedia.org/wiki/<PERSON>hutzstaffel\" title=\"Schutzstaffel\">SS</a>-<i><a href=\"https://wikipedia.org/wiki/Obersturmbannf%C3%BChrer\" title=\"Obersturmbannführer\"><PERSON>bersturmbannführer</a></i> (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "<PERSON><PERSON><PERSON>sta<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>tzstaffel"}, {"title": "Obersturmbannführer", "link": "https://wikipedia.org/wiki/Obersturmbannf%C3%BChrer"}]}, {"year": "1963", "text": "<PERSON>, Australian politician, 24th Premier of Tasmania (b. 1874)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician, 24th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (b. 1874)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American football player and coach, founded the Green Bay Packers (b. 1898)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach, founded the <a href=\"https://wikipedia.org/wiki/Green_Bay_Packers\" title=\"Green Bay Packers\">Green Bay Packers</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach, founded the <a href=\"https://wikipedia.org/wiki/Green_Bay_Packers\" title=\"Green Bay Packers\">Green Bay Packers</a> (b. 1898)", "links": [{"title": "Curly <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Green Bay Packers", "link": "https://wikipedia.org/wiki/Green_Bay_Packers"}]}, {"year": "1966", "text": "<PERSON>, American drummer and bandleader (b. 1873)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Papa_<PERSON>_<PERSON>\" title=\"Papa <PERSON>\">Papa <PERSON></a>, American drummer and bandleader (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Papa_<PERSON>_<PERSON>\" title=\"Papa <PERSON>\">Papa <PERSON></a>, American drummer and bandleader (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American author and activist (b. 1880)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian playwright, journalist, and politician (b. 1912)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian playwright, journalist, and politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian playwright, journalist, and politician (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Norwegian speed skater (b. 1904)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian speed skater (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian speed skater (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, American theologian and academic (b. 1892)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theologian and academic (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theologian and academic (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German physician and academic, Nobel Prize laureate (b. 1904)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1980", "text": "<PERSON>, American businessman, founded the ACNielsen company (b. 1897)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/ACNielsen\" class=\"mw-redirect\" title=\"ACNielsen\">ACNielsen company</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/ACNielsen\" class=\"mw-redirect\" title=\"ACNielsen\">ACNielsen company</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American lawyer and politician (b. 1883)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Count of Flanders (b. 1903)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Count_of_Flanders\" title=\"Prince <PERSON>, Count of Flanders\">Prince <PERSON>, Count of Flanders</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Count_of_Flanders\" title=\"Prince <PERSON>, Count of Flanders\">Prince <PERSON>, Count of Flanders</a> (b. 1903)", "links": [{"title": "<PERSON>, Count of Flanders", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Count_of_Flanders"}]}, {"year": "1983", "text": "<PERSON>, German writer (b. 1900)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German writer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German writer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English actor and soldier (b. 1918)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and soldier (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and soldier (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Austrian racing driver (b. 1958)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian racing driver (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Lebanese lawyer and politician, 32nd Prime Minister of Lebanon (b. 1921)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lebanese lawyer and politician, 32nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lebanon\" title=\"Prime Minister of Lebanon\">Prime Minister of Lebanon</a> (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Lebanon", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lebanon"}]}, {"year": "1988", "text": "<PERSON>, Austrian philosopher from the Vienna Circle (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Italian engineer, designed the Ferrari Lampredi engine (b. 1917)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Aurelio_Lampredi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer, designed the <a href=\"https://wikipedia.org/wiki/Ferrari_Lampredi_engine\" title=\"Ferrari Lampredi engine\">Ferrari Lampredi engine</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aurel<PERSON>_Lampredi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian engineer, designed the <a href=\"https://wikipedia.org/wiki/Ferrari_Lampredi_engine\" title=\"Ferrari Lampredi engine\">Ferrari Lampredi engine</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurel<PERSON>_Lampredi"}, {"title": "Ferrari Lampredi engine", "link": "https://wikipedia.org/wiki/Ferrari_Lampredi_engine"}]}, {"year": "1991", "text": "<PERSON>, American singer-songwriter (b. 1941)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Indian politician, 6th President of India (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1999", "text": "<PERSON>, English engineer, invented the hovercraft (b. 1910)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Hovercraft\" title=\"Hovercraft\">hovercraft</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, invented the <a href=\"https://wikipedia.org/wiki/Hovercraft\" title=\"Hovercraft\">hovercraft</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hovercraft", "link": "https://wikipedia.org/wiki/Hovercraft"}]}, {"year": "2000", "text": "<PERSON>, American drummer, composer, and producer (b. 1923)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, composer, and producer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer, composer, and producer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American cartoonist, created <PERSON> (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Menace_(U.S._comics)\" title=\"<PERSON> (U.S. comics)\"><PERSON> the Men<PERSON></a></i> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Menace_(U.S._comics)\" title=\"<PERSON> the <PERSON> (U.S. comics)\"><PERSON> the <PERSON></a></i> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the <PERSON>ace (U.S. comics)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Menace_(U.S._comics)"}]}, {"year": "2001", "text": "notable victims of the Nepalese royal massacre\n<PERSON><PERSON><PERSON><PERSON> of Nepal (b. 1949)\n<PERSON><PERSON><PERSON> of Nepal (b. 1945)\n<PERSON><PERSON><PERSON> of Nepal (b. 1950)\nPrince <PERSON> of Nepal (b. 1978)\nPrincess <PERSON><PERSON><PERSON> of Nepal (b. 1976)", "html": "2001 - notable victims of the <a href=\"https://wikipedia.org/wiki/Nepalese_royal_massacre\" title=\"Nepalese royal massacre\">Nepalese royal massacre</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aishwarya_of_Nepal\" class=\"mw-redirect\" title=\"Aishwar<PERSON> of Nepal\"><PERSON><PERSON><PERSON><PERSON> of Nepal</a> (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Birendra_of_Nepal\" title=\"Birendra of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Dhirendra_of_Nepal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1950)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Nepal\" title=\"Prince <PERSON> of Nepal\">Prince <PERSON> of Nepal</a> (b. 1978)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Princess_Shruti_of_Nepal\" title=\"Princess <PERSON><PERSON> of Nepal\">Princess <PERSON><PERSON><PERSON> of Nepal</a> (b. 1976)</li>\n</ul>", "no_year_html": "notable victims of the <a href=\"https://wikipedia.org/wiki/Nepalese_royal_massacre\" title=\"Nepalese royal massacre\">Nepalese royal massacre</a>\n<ul>\n<li>\n<a href=\"https://wikipedia.org/wiki/Aishwarya_of_Nepal\" class=\"mw-redirect\" title=\"Aishwar<PERSON> of Nepal\"><PERSON><PERSON><PERSON><PERSON> of Nepal</a> (b. 1949)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Birendra_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1945)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Dhirendra_of_Nepal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1950)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Nepal\" title=\"Prince <PERSON> of Nepal\">Prince <PERSON> of Nepal</a> (b. 1978)</li>\n<li>\n<a href=\"https://wikipedia.org/wiki/Princess_Shr<PERSON>_of_Nepal\" title=\"Princess <PERSON><PERSON> of Nepal\">Princess <PERSON><PERSON> of Nepal</a> (b. 1976)</li>\n</ul>", "links": [{"title": "Nepalese royal massacre", "link": "https://wikipedia.org/wiki/Nepalese_royal_massacre"}, {"title": "Aishwarya of Nepal", "link": "https://wikipedia.org/wiki/Aishwarya_of_Nepal"}, {"title": "<PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ndra_of_Nepal"}, {"title": "Dhirendra of Nepal", "link": "https://wikipedia.org/wiki/Dhirendra_of_Nepal"}, {"title": "Prince <PERSON> of Nepal", "link": "https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_of_Nepal"}, {"title": "Princess <PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Nepal"}]}, {"year": "<PERSON><PERSON><PERSON><PERSON> of Nepal (b. 1949)", "text": null, "html": "<PERSON><PERSON><PERSON><PERSON> of Nepal (b. 1949) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Aishwarya_of_Nepal\" class=\"mw-redirect\" title=\"<PERSON>shwar<PERSON> of Nepal\"><PERSON><PERSON>war<PERSON> of Nepal</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Aishwarya_of_Nepal\" class=\"mw-redirect\" title=\"Aishwarya of Nepal\">Aishwarya of Nepal</a> (b. 1949)", "links": [{"title": "Aishwarya of Nepal", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Aishwarya_of_Nepal"}]}, {"year": "<PERSON><PERSON><PERSON> of Nepal (b. 1945)", "text": null, "html": "<PERSON><PERSON><PERSON> of Nepal (b. 1945) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/B<PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Nepal\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON><PERSON>ndra_of_Nepal"}]}, {"year": "<PERSON><PERSON><PERSON> of Nepal (b. 1950)", "text": null, "html": "<PERSON><PERSON><PERSON> of Nepal (b. 1950) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Dhire<PERSON>_of_Nepal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Dhirendra_of_Nepal\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> of Nepal\"><PERSON><PERSON><PERSON> of Nepal</a> (b. 1950)", "links": [{"title": "Dhirendra of Nepal", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Dhirendra_of_Nepal"}]}, {"year": "<PERSON> of Nepal (b. 1978)", "text": null, "html": "Prince <PERSON> of Nepal (b. 1978) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>_of_Nepal\" title=\"Prince <PERSON> of Nepal\">Prince <PERSON> of Nepal</a> (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Prince_<PERSON><PERSON>_of_Nepal\" title=\"Prince <PERSON> of Nepal\">Prince <PERSON> of Nepal</a> (b. 1978)", "links": [{"title": "Prince <PERSON> of Nepal", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_of_Nepal"}]}, {"year": "Princess <PERSON><PERSON><PERSON> of Nepal (b. 1976)", "text": null, "html": "Princess <PERSON><PERSON><PERSON> of Nepal (b. 1976) - <a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Nepal\" title=\"Princess <PERSON><PERSON><PERSON> of Nepal\">Princess <PERSON><PERSON><PERSON> of Nepal</a> (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.orghttps://wikipedia.org/wiki/Princess_<PERSON>hr<PERSON>_of_Nepal\" title=\"Princess <PERSON><PERSON><PERSON> of Nepal\">Princess <PERSON><PERSON><PERSON> of Nepal</a> (b. 1976)", "links": [{"title": "Princess <PERSON><PERSON><PERSON> of Nepal", "link": "https://wikipedia.orghttps://wikipedia.org/wiki/Princess_<PERSON><PERSON><PERSON>_of_Nepal"}]}, {"year": "2002", "text": "<PERSON><PERSON>, South African cricketer (b. 1969)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cricketer (b. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American historian and author (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/William_Manchester\" title=\"William Manchester\"><PERSON></a>, American historian and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Manchester\" title=\"William Manchester\"><PERSON></a>, American historian and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American physician (b. 1902)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American basketball player and coach (b. 1924)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American singer and songwriter (b. 1975)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and songwriter (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and songwriter (b. 1975)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "2008", "text": "<PERSON>, Israeli journalist and politician, 17th Justice Minister of Israel (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Justice_Minister_of_Israel\" class=\"mw-redirect\" title=\"Justice Minister of Israel\">Justice Minister of Israel</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli journalist and politician, 17th <a href=\"https://wikipedia.org/wiki/Justice_Minister_of_Israel\" class=\"mw-redirect\" title=\"Justice Minister of Israel\">Justice Minister of Israel</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Justice Minister of Israel", "link": "https://wikipedia.org/wiki/Justice_Minister_of_Israel"}]}, {"year": "2008", "text": "<PERSON>, French fashion designer, founded Saint Laurent Paris (b. 1936)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Saint Laurent Paris\"><PERSON></a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)\" title=\"<PERSON> (designer)\"><PERSON></a>, French fashion designer, founded <a href=\"https://wikipedia.org/wiki/Saint_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Saint Laurent Paris\">Saint <PERSON></a> (b. 1936)", "links": [{"title": "<PERSON> (designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(designer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Irish horse trainer (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish horse trainer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brien\" title=\"<PERSON>\"><PERSON></a>, Irish horse trainer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vincent_O%27Brien"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Japanese dancer (b. 1906)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese dancer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese dancer (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Russian poet (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Iranian humanitarian and activist (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian humanitarian and activist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian humanitarian and activist (b. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American saxophonist and composer (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and composer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist and composer (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON>, Irish educator and politician, 19th Irish Minister of Defence (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON><PERSON>_Faulkner\" title=\"<PERSON><PERSON><PERSON><PERSON> Faulkner\"><PERSON><PERSON><PERSON><PERSON></a>, Irish educator and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)\" title=\"Minister for Defence (Ireland)\">Irish Minister of Defence</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%<PERSON><PERSON><PERSON>_Faulkner\" title=\"<PERSON><PERSON><PERSON><PERSON> Faulkner\"><PERSON><PERSON><PERSON><PERSON></a>, Irish educator and politician, 19th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)\" title=\"Minister for Defence (Ireland)\">Irish Minister of Defence</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister for Defence (Ireland)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Ireland)"}]}, {"year": "2012", "text": "<PERSON>, Slovak politician (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Milan_Ga%C4%BEa\" title=\"Milan Gaľa\"><PERSON></a>, Slovak politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Ga%C4%BEa\" title=\"Milan Gaľa\"><PERSON></a>, Slovak politician (b. 1953)", "links": [{"title": "Milan Gaľa", "link": "https://wikipedia.org/wiki/Milan_Ga%C4%BEa"}]}, {"year": "2013", "text": "<PERSON>, Canadian lawyer and politician, 33rd Solicitor General of Canada (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 33rd <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_Canada\" title=\"Solicitor General of Canada\">Solicitor General of Canada</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General of Canada", "link": "https://wikipedia.org/wiki/Solicitor_General_of_Canada"}]}, {"year": "2014", "text": "<PERSON>, American actress (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Ukrainian sailor (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian sailor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian sailor (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Moldovan physician and politician, Moldovan Minister of Health (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Timofei_Mo%C8%99neaga\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Moldova)\" title=\"Ministry of Health (Moldova)\">Moldovan Minister of Health</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tim<PERSON>ei_Mo%C8%99neaga\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Moldovan physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Moldova)\" title=\"Ministry of Health (Moldova)\">Moldovan Minister of Health</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Timofei_Mo%C8%99neaga"}, {"title": "Ministry of Health (Moldova)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Moldova)"}]}, {"year": "2015", "text": "<PERSON>, Scottish journalist and politician (b. 1959)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist and politician (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian educator and politician, 42nd Premier of Victoria (b. 1938) ", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1938) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian educator and politician, 42nd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1938) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "2015", "text": "<PERSON>, Dominican lawyer and politician, 6th President of Dominica (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nicholas <PERSON>\"><PERSON></a>, Dominican lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Dominica\" class=\"mw-redirect\" title=\"List of Presidents of Dominica\">President of Dominica</a> (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicholas_<PERSON>\" title=\"Nicholas <PERSON>\"><PERSON></a>, Dominican lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Dominica\" class=\"mw-redirect\" title=\"List of Presidents of Dominica\">President of Dominica</a> (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nicholas_<PERSON>"}, {"title": "List of Presidents of Dominica", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Dominica"}]}, {"year": "2015", "text": "<PERSON>, Canadian economist and politician, 26th Premier of Quebec (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician, 26th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "2015", "text": "<PERSON>, American singer-songwriter (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, Serbian pop-folk singer (b. 1956)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian pop-folk singer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Serbian pop-folk singer (b. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinan_<PERSON>%C4%87"}]}, {"year": "2019", "text": "<PERSON><PERSON>, Indonesian politician, 6th First Lady of Indonesia. (b. 1952)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian politician, 6th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Indonesia\" class=\"mw-redirect\" title=\"First Lady of Indonesia\">First Lady of Indonesia</a>. (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian politician, 6th <a href=\"https://wikipedia.org/wiki/First_Lady_of_Indonesia\" class=\"mw-redirect\" title=\"First Lady of Indonesia\">First Lady of Indonesia</a>. (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, {"title": "First Lady of Indonesia", "link": "https://wikipedia.org/wiki/First_Lady_of_Indonesia"}]}, {"year": "2024", "text": "<PERSON>, Burmese general and politician (b. 1927)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> O<PERSON>\"><PERSON></a>, Burmese general and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> O<PERSON>\"><PERSON></a>, Burmese general and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tin_<PERSON>o"}]}]}}