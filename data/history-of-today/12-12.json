{"date": "December 12", "url": "https://wikipedia.org/wiki/December_12", "data": {"Events": [{"year": "627", "text": "Battle of Nineveh: A Byzantine army under Emperor <PERSON><PERSON><PERSON> defeats Emperor <PERSON><PERSON><PERSON><PERSON>'s Persian forces, commanded by General <PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "627 - <a href=\"https://wikipedia.org/wiki/Battle_of_Nineveh_(627)\" title=\"Battle of Nineveh (627)\">Battle of Nineveh</a>: A <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats Emperor <a href=\"https://wikipedia.org/wiki/Khosrau_II\" class=\"mw-redirect\" title=\"Khosrau II\">Khosrau II</a>'s <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian</a> forces, commanded by <a href=\"https://wikipedia.org/wiki/Rhahzadh\" title=\"Rhahzadh\">General <PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Nineveh_(627)\" title=\"Battle of Nineveh (627)\">Battle of Nineveh</a>: A <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine</a> army under Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> defeats Emperor <a href=\"https://wikipedia.org/wiki/Khosrau_II\" class=\"mw-redirect\" title=\"Khosrau II\">Khosrau II</a>'s <a href=\"https://wikipedia.org/wiki/Sasanian_Empire\" title=\"Sasanian Empire\">Persian</a> forces, commanded by <a href=\"https://wikipedia.org/wiki/Rhahzadh\" title=\"Rhahzadh\">General <PERSON><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Battle of Nineveh (627)", "link": "https://wikipedia.org/wiki/Battle_of_Nineveh_(627)"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Khosrau II", "link": "https://wikipedia.org/wiki/Khosrau_II"}, {"title": "Sasanian Empire", "link": "https://wikipedia.org/wiki/Sasanian_Empire"}, {"title": "R<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>h"}]}, {"year": "1388", "text": "<PERSON> of Enghien sells the lordship of Argos and Nauplia to the Republic of Venice.", "html": "1388 - <a href=\"https://wikipedia.org/wiki/Maria_of_Enghien\" title=\"<PERSON> of Enghien\"><PERSON> of Enghien</a> sells the lordship of <a href=\"https://wikipedia.org/wiki/Argos_and_Nauplia\" class=\"mw-redirect\" title=\"Argos and Nauplia\">Argos and Nauplia</a> to the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_of_Enghien\" title=\"<PERSON> of Enghien\"><PERSON> of Enghien</a> sells the lordship of <a href=\"https://wikipedia.org/wiki/Argos_and_Nauplia\" class=\"mw-redirect\" title=\"Argos and Nauplia\">Argos and Nauplia</a> to the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a>.", "links": [{"title": "<PERSON> En<PERSON>ien", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Argos and Nauplia", "link": "https://wikipedia.org/wiki/Argos_and_Nauplia"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1787", "text": "Pennsylvania becomes the second state to ratify the US Constitution.", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> becomes the second state to ratify the <a href=\"https://wikipedia.org/wiki/US_Constitution\" class=\"mw-redirect\" title=\"US Constitution\">US Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> becomes the second state to ratify the <a href=\"https://wikipedia.org/wiki/US_Constitution\" class=\"mw-redirect\" title=\"US Constitution\">US Constitution</a>.", "links": [{"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "US Constitution", "link": "https://wikipedia.org/wiki/US_Constitution"}]}, {"year": "1862", "text": "American Civil War: USS Cairo sinks on the Yazoo River.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/USS_Cairo\" title=\"USS Cairo\">USS <i>Cairo</i></a> sinks on the <a href=\"https://wikipedia.org/wiki/Yazoo_River\" title=\"Yazoo River\">Yazoo River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/USS_Cairo\" title=\"USS Cairo\">USS <i>Cairo</i></a> sinks on the <a href=\"https://wikipedia.org/wiki/Yazoo_River\" title=\"Yazoo River\">Yazoo River</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "USS Cairo", "link": "https://wikipedia.org/wiki/USS_Cairo"}, {"title": "Yazoo River", "link": "https://wikipedia.org/wiki/Yazoo_River"}]}, {"year": "1866", "text": "Oaks explosion: The worst mining disaster in England kills 361 miners and rescuers.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Oaks_explosion\" title=\"Oaks explosion\">Oaks explosion</a>: The worst mining disaster in England kills 361 miners and rescuers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oaks_explosion\" title=\"Oaks explosion\">Oaks explosion</a>: The worst mining disaster in England kills 361 miners and rescuers.", "links": [{"title": "Oaks explosion", "link": "https://wikipedia.org/wiki/Oaks_explosion"}]}, {"year": "1870", "text": "<PERSON> of South Carolina becomes the second black U.S. congressman.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> becomes the second black <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. congressman</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> becomes the second black <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. congressman</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> receives the first transatlantic radio signal (the letter \"S\" [•••] in Morse Code), at Signal Hill in St John's, Newfoundland.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> receives the first transatlantic radio signal (the letter \"S\" [•••] in Morse Code), at <a href=\"https://wikipedia.org/wiki/Signal_Hill,_St._John%27s\" title=\"Signal Hill, St. John's\">Signal Hill</a> in <a href=\"https://wikipedia.org/wiki/St_John%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St John's, Newfoundland\">St John's, Newfoundland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a> receives the first transatlantic radio signal (the letter \"S\" [•••] in Morse Code), at <a href=\"https://wikipedia.org/wiki/Signal_Hill,_St._John%27s\" title=\"Signal Hill, St. John's\">Signal Hill</a> in <a href=\"https://wikipedia.org/wiki/St_John%27s,_Newfoundland\" class=\"mw-redirect\" title=\"St John's, Newfoundland\">St John's, Newfoundland</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Signal Hill, St. John's", "link": "https://wikipedia.org/wiki/Signal_Hill,_St._John%27s"}, {"title": "St John's, Newfoundland", "link": "https://wikipedia.org/wiki/St_John%27s,_Newfoundland"}]}, {"year": "1915", "text": "<PERSON> declares the establishment of the Empire of China and proclaims himself Emperor.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Yuan_Shi<PERSON>\" title=\"Yuan Shikai\"><PERSON></a> declares the establishment of the <a href=\"https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)\" title=\"Empire of China (1915-1916)\">Empire of China</a> and proclaims himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yuan_Shi<PERSON>\" title=\"Yuan Shikai\"><PERSON></a> declares the establishment of the <a href=\"https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)\" title=\"Empire of China (1915-1916)\">Empire of China</a> and proclaims himself <a href=\"https://wikipedia.org/wiki/Emperor_of_China\" title=\"Emperor of China\">Emperor</a>.", "links": [{"title": "Yuan Shikai", "link": "https://wikipedia.org/wiki/Yuan_Shikai"}, {"title": "Empire of China (1915-1916)", "link": "https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)"}, {"title": "Emperor of China", "link": "https://wikipedia.org/wiki/Emperor_of_China"}]}, {"year": "1917", "text": "Father <PERSON> founds Boys Town as a farm village for wayward boys.", "html": "1917 - Father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds <a href=\"https://wikipedia.org/wiki/Boys_Town_(organization)\" title=\"Boys Town (organization)\">Boys Town</a> as a farm village for wayward boys.", "no_year_html": "Father <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> founds <a href=\"https://wikipedia.org/wiki/Boys_Town_(organization)\" title=\"Boys Town (organization)\">Boys Town</a> as a farm village for wayward boys.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Boys Town (organization)", "link": "https://wikipedia.org/wiki/Boys_Town_(organization)"}]}, {"year": "1935", "text": "The Lebensborn Project, a Nazi reproduction program, is founded by <PERSON>.", "html": "1935 - The <a href=\"https://wikipedia.org/wiki/Lebensborn\" title=\"Lebensborn\">Lebensborn</a> Project, a Nazi reproduction program, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lebensborn\" title=\"Lebensborn\">Lebensborn</a> Project, a Nazi reproduction program, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Lebensborn", "link": "https://wikipedia.org/wiki/Lebensborn"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "The beginning of Xi'an incident. As a result, <PERSON> is captured.", "html": "1936 - The beginning of <a href=\"https://wikipedia.org/wiki/Xi%27an_Incident\" title=\"Xi'an Incident\">Xi'an incident</a>. As a result, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured.", "no_year_html": "The beginning of <a href=\"https://wikipedia.org/wiki/Xi%27an_Incident\" title=\"Xi'an Incident\">Xi'an incident</a>. As a result, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured.", "links": [{"title": "Xi'an Incident", "link": "https://wikipedia.org/wiki/Xi%27an_Incident"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1937", "text": "Second Sino-Japanese War: USS Panay incident: Japanese aircraft bomb and sink U.S. gunboat USS Panay on the Yangtze river in China.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/USS_Panay_incident\" title=\"USS Panay incident\">USS <i>Panay</i> incident</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> aircraft bomb and sink U.S. gunboat <a href=\"https://wikipedia.org/wiki/USS_Panay_(PR-5)\" title=\"USS Panay (PR-5)\">USS <i>Panay</i></a> on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river in China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Sino-Japanese_War\" title=\"Second Sino-Japanese War\">Second Sino-Japanese War</a>: <a href=\"https://wikipedia.org/wiki/USS_Panay_incident\" title=\"USS Panay incident\">USS <i>Panay</i> incident</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> aircraft bomb and sink U.S. gunboat <a href=\"https://wikipedia.org/wiki/USS_Panay_(PR-5)\" title=\"USS Panay (PR-5)\">USS <i>Panay</i></a> on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river in China.", "links": [{"title": "Second Sino-Japanese War", "link": "https://wikipedia.org/wiki/Second_Sino-Japanese_War"}, {"title": "USS Panay incident", "link": "https://wikipedia.org/wiki/USS_Panay_incident"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "USS Panay (PR-5)", "link": "https://wikipedia.org/wiki/USS_Panay_(PR-5)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yang<PERSON>e"}]}, {"year": "1939", "text": "HMS Duchess sinks after a collision with HMS Barham off the coast of Scotland with the loss of 124 men.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/HMS_Duchess_(H64)\" title=\"HMS Duchess (H64)\">HMS <i>Duchess</i></a> sinks after a collision with <a href=\"https://wikipedia.org/wiki/HMS_Barham_(04)\" title=\"HMS Barham (04)\">HMS <i>Barham</i></a> off the coast of Scotland with the loss of 124 men.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Duchess_(H64)\" title=\"HMS Duchess (H64)\">HMS <i>Duchess</i></a> sinks after a collision with <a href=\"https://wikipedia.org/wiki/HMS_Barham_(04)\" title=\"HMS Barham (04)\">HMS <i>Barham</i></a> off the coast of Scotland with the loss of 124 men.", "links": [{"title": "HMS Duchess (H64)", "link": "https://wikipedia.org/wiki/<PERSON>_Duchess_(H64)"}, {"title": "HMS Barham (04)", "link": "https://wikipedia.org/wiki/HMS_Barham_(04)"}]}, {"year": "1939", "text": "Winter War: The Battle of Tolvajärvi, also known as the first major Finnish victory in the Winter War, begins.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tolvaj%C3%A4rvi\" title=\"Battle of Tolvajärvi\">Battle of Tolvajärvi</a>, also known as the first major <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finnish</a> victory in the Winter War, begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tolvaj%C3%A4rvi\" title=\"Battle of Tolvajärvi\">Battle of Tolvajärvi</a>, also known as the first major <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finnish</a> victory in the Winter War, begins.", "links": [{"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Battle of Tolvajärvi", "link": "https://wikipedia.org/wiki/Battle_of_Tolvaj%C3%A4rvi"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "1941", "text": "World War II: Fifty-four Japanese A6M Zero fighters raid Batangas Field, Philippines. <PERSON><PERSON><PERSON> and four Filipino fighter pilots fend them off; <PERSON> is killed.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Fifty-four <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> <a href=\"https://wikipedia.org/wiki/A6M_Zero\" class=\"mw-redirect\" title=\"A6M Zero\">A6M Zero</a> fighters raid <a href=\"https://wikipedia.org/wiki/Batangas\" title=\"Batangas\">Batangas</a> Field, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>. <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Villamor\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and four <a href=\"https://wikipedia.org/wiki/Philippine\" class=\"mw-redirect\" title=\"Philippine\">Filipino</a> fighter pilots fend them off; <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON><PERSON>\" title=\"César <PERSON>\"><PERSON></a> is killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Fifty-four <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> <a href=\"https://wikipedia.org/wiki/A6M_Zero\" class=\"mw-redirect\" title=\"A6M Zero\">A6M Zero</a> fighters raid <a href=\"https://wikipedia.org/wiki/Batangas\" title=\"Batangas\">Batangas</a> Field, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>. <a href=\"https://wikipedia.org/wiki/Jes%C3%BAs_Villamor\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and four <a href=\"https://wikipedia.org/wiki/Philippine\" class=\"mw-redirect\" title=\"Philippine\">Filipino</a> fighter pilots fend them off; <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is killed.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "A6M Zero", "link": "https://wikipedia.org/wiki/A6M_Zero"}, {"title": "Batangas", "link": "https://wikipedia.org/wiki/Batangas"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jes%C3%BAs_Villamor"}, {"title": "Philippine", "link": "https://wikipedia.org/wiki/Philippine"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Basa"}]}, {"year": "1941", "text": "The Holocaust: <PERSON> declares the imminent extermination of the Jews at a meeting in the Reich Chancellery.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> declares the imminent extermination of the Jews at a <a href=\"https://wikipedia.org/wiki/Reich_Chancellery_meeting_of_12_December_1941\" title=\"Reich Chancellery meeting of 12 December 1941\">meeting in the Reich Chancellery</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> declares the imminent extermination of the Jews at a <a href=\"https://wikipedia.org/wiki/Reich_Chancellery_meeting_of_12_December_1941\" title=\"Reich Chancellery meeting of 12 December 1941\">meeting in the Reich Chancellery</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reich Chancellery meeting of 12 December 1941", "link": "https://wikipedia.org/wiki/Reich_Chancellery_meeting_of_12_December_1941"}]}, {"year": "1945", "text": "The People's Republic of Korea is outlawed in the South, by order of the United States Army Military Government in Korea.", "html": "1945 - The <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Korea\" title=\"People's Republic of Korea\">People's Republic of Korea</a> is outlawed in the <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South</a>, by order of the <a href=\"https://wikipedia.org/wiki/United_States_Army_Military_Government_in_Korea\" title=\"United States Army Military Government in Korea\">United States Army Military Government in Korea</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/People%27s_Republic_of_Korea\" title=\"People's Republic of Korea\">People's Republic of Korea</a> is outlawed in the <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South</a>, by order of the <a href=\"https://wikipedia.org/wiki/United_States_Army_Military_Government_in_Korea\" title=\"United States Army Military Government in Korea\">United States Army Military Government in Korea</a>.", "links": [{"title": "People's Republic of Korea", "link": "https://wikipedia.org/wiki/People%27s_Republic_of_Korea"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "United States Army Military Government in Korea", "link": "https://wikipedia.org/wiki/United_States_Army_Military_Government_in_Korea"}]}, {"year": "1946", "text": "United Nations Security Council Resolution 13 relating to acceptance of Siam (now Thailand) to the United Nations is adopted.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_13\" title=\"United Nations Security Council Resolution 13\">United Nations Security Council Resolution 13</a> relating to acceptance of Siam (now <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_13\" title=\"United Nations Security Council Resolution 13\">United Nations Security Council Resolution 13</a> relating to acceptance of Siam (now <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a>) to the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> is adopted.", "links": [{"title": "United Nations Security Council Resolution 13", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_13"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1956", "text": "United Nations Security Council Resolution 121 relating to acceptance of Japan to the United Nations is adopted.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_121\" title=\"United Nations Security Council Resolution 121\">United Nations Security Council Resolution 121</a> relating to acceptance of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> to the United Nations is adopted.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_121\" title=\"United Nations Security Council Resolution 121\">United Nations Security Council Resolution 121</a> relating to acceptance of <a href=\"https://wikipedia.org/wiki/Japan\" title=\"Japan\">Japan</a> to the United Nations is adopted.", "links": [{"title": "United Nations Security Council Resolution 121", "link": "https://wikipedia.org/wiki/United_Nations_Security_Council_Resolution_121"}, {"title": "Japan", "link": "https://wikipedia.org/wiki/Japan"}]}, {"year": "1963", "text": "Kenya declares independence from Great Britain.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> declares independence from Great Britain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kenya\" title=\"Kenya\">Kenya</a> declares independence from Great Britain.", "links": [{"title": "Kenya", "link": "https://wikipedia.org/wiki/Kenya"}]}, {"year": "1969", "text": "The Piazza Fontana bombing; a bomb explodes at the headquarters of Banca Nazionale dell'Agricoltura (the National Agricultural Bank) in Piazza Fontana in Milan, Italy, killing 17 people and wounding 88. The same afternoon, three more bombs are detonated in Rome and Milan, and another is found unexploded.", "html": "1969 - The <a href=\"https://wikipedia.org/wiki/Piazza_Fontana_bombing\" title=\"Piazza Fontana bombing\">Piazza Fontana bombing</a>; a bomb explodes at the headquarters of <a href=\"https://wikipedia.org/wiki/Banca_Nazionale_dell%27Agricoltura\" title=\"Banca Nazionale dell'Agricoltura\">Banca Nazionale dell'Agricoltura</a> (the National Agricultural Bank) in Piazza Fontana in Milan, Italy, killing 17 people and wounding 88. The same afternoon, three more bombs are detonated in Rome and Milan, and another is found unexploded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Piazza_Fontana_bombing\" title=\"Piazza Fontana bombing\">Piazza Fontana bombing</a>; a bomb explodes at the headquarters of <a href=\"https://wikipedia.org/wiki/Banca_Nazionale_dell%27Agricoltura\" title=\"Banca Nazionale dell'Agricoltura\">Banca Nazionale dell'Agricoltura</a> (the National Agricultural Bank) in Piazza Fontana in Milan, Italy, killing 17 people and wounding 88. The same afternoon, three more bombs are detonated in Rome and Milan, and another is found unexploded.", "links": [{"title": "Piazza Fontana bombing", "link": "https://wikipedia.org/wiki/Pi<PERSON>_Fontana_bombing"}, {"title": "Banca Nazionale dell'Agricoltura", "link": "https://wikipedia.org/wiki/Banca_Nazionale_dell%27Agricoltura"}]}, {"year": "1979", "text": "The 8.2 Mw  Tumaco earthquake shakes Colombia and Ecuador with a maximum Mercalli intensity of IX (Violent), killing 300-600, and generating a large tsunami.", "html": "1979 - The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1979_Tumaco_earthquake\" title=\"1979 Tumaco earthquake\">Tumaco earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> and <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing 300-600, and generating a large tsunami.", "no_year_html": "The 8.2 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1979_Tumaco_earthquake\" title=\"1979 Tumaco earthquake\">Tumaco earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Colombia\" title=\"Colombia\">Colombia</a> and <a href=\"https://wikipedia.org/wiki/Ecuador\" title=\"Ecuador\">Ecuador</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>), killing 300-600, and generating a large tsunami.", "links": [{"title": "1979 Tumaco earthquake", "link": "https://wikipedia.org/wiki/1979_Tumaco_earthquake"}, {"title": "Colombia", "link": "https://wikipedia.org/wiki/Colombia"}, {"title": "Ecuador", "link": "https://wikipedia.org/wiki/Ecuador"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1979", "text": "Coup d'état of December Twelfth occurs in South Korea.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_December_Twelfth\" title=\"Coup d'état of December Twelfth\">Coup d'état of December Twelfth</a> occurs in South Korea.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_December_Twelfth\" title=\"Coup d'état of December Twelfth\">Coup d'état of December Twelfth</a> occurs in South Korea.", "links": [{"title": "Coup d'état of December Twelfth", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_December_Twelfth"}]}, {"year": "1985", "text": "Arrow Air Flight 1285R, a McDonnell Douglas DC-8, crashes after takeoff in Gander, Newfoundland, killing all 256 people on board, including 236 members of the United States Army's 101st Airborne Division.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Arrow_Air_Flight_1285R\" title=\"Arrow Air Flight 1285R\">Arrow Air Flight 1285R</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-8\" class=\"mw-redirect\" title=\"McDonnell Douglas DC-8\">McDonnell Douglas DC-8</a>, crashes after takeoff in <a href=\"https://wikipedia.org/wiki/Gander,_Newfoundland_and_Labrador\" title=\"Gander, Newfoundland and Labrador\">Gander, Newfoundland</a>, killing all 256 people on board, including 236 members of the United States Army's <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arrow_Air_Flight_1285R\" title=\"Arrow Air Flight 1285R\">Arrow Air Flight 1285R</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-8\" class=\"mw-redirect\" title=\"McDonnell Douglas DC-8\">McDonnell Douglas DC-8</a>, crashes after takeoff in <a href=\"https://wikipedia.org/wiki/Gander,_Newfoundland_and_Labrador\" title=\"Gander, Newfoundland and Labrador\">Gander, Newfoundland</a>, killing all 256 people on board, including 236 members of the United States Army's <a href=\"https://wikipedia.org/wiki/101st_Airborne_Division\" title=\"101st Airborne Division\">101st Airborne Division</a>.", "links": [{"title": "Arrow Air Flight 1285R", "link": "https://wikipedia.org/wiki/Arrow_Air_Flight_1285R"}, {"title": "McDonnell Douglas DC-8", "link": "https://wikipedia.org/wiki/<PERSON>_Douglas_DC-8"}, {"title": "Gander, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Gander,_Newfoundland_and_Labrador"}, {"title": "101st Airborne Division", "link": "https://wikipedia.org/wiki/101st_Airborne_Division"}]}, {"year": "1988", "text": "The Clapham Junction rail crash kills thirty-five and injures hundreds after two collisions of three commuter trains—one of the worst train crashes in the United Kingdom.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Clapham_Junction_rail_crash\" title=\"Clapham Junction rail crash\">Clapham Junction rail crash</a> kills thirty-five and injures hundreds after two collisions of three commuter trains—one of the worst train crashes in the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Clapham_Junction_rail_crash\" title=\"Clapham Junction rail crash\">Clapham Junction rail crash</a> kills thirty-five and injures hundreds after two collisions of three commuter trains—one of the worst train crashes in the United Kingdom.", "links": [{"title": "Clapham Junction rail crash", "link": "https://wikipedia.org/wiki/Clapham_Junction_rail_crash"}]}, {"year": "1999", "text": "A magnitude 7.3 earthquake hits the Philippines's main island of Luzon, killing six people, injuring 40, and causing power outages that affected the capital Manila.", "html": "1999 - A <a href=\"https://wikipedia.org/wiki/1999_Luzon_earthquake\" title=\"1999 Luzon earthquake\">magnitude 7.3 earthquake</a> hits the Philippines's main island of <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a>, killing six people, injuring 40, and causing power outages that affected the capital <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1999_Luzon_earthquake\" title=\"1999 Luzon earthquake\">magnitude 7.3 earthquake</a> hits the Philippines's main island of <a href=\"https://wikipedia.org/wiki/Luzon\" title=\"Luzon\">Luzon</a>, killing six people, injuring 40, and causing power outages that affected the capital <a href=\"https://wikipedia.org/wiki/Manila\" title=\"Manila\">Manila</a>.", "links": [{"title": "1999 Luzon earthquake", "link": "https://wikipedia.org/wiki/1999_Luzon_earthquake"}, {"title": "Luzon", "link": "https://wikipedia.org/wiki/Luzon"}, {"title": "Manila", "link": "https://wikipedia.org/wiki/Manila"}]}, {"year": "2000", "text": "The United States Supreme Court releases its decision in <PERSON> v. <PERSON>.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> releases its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v<PERSON>_<PERSON>\" title=\"<PERSON> v<PERSON>\"><PERSON> v. <PERSON></a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">United States Supreme Court</a> releases its decision in <i><a href=\"https://wikipedia.org/wiki/<PERSON>_v._<PERSON>\" title=\"<PERSON> v. <PERSON>\"><PERSON> v<PERSON></a></i>.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "<PERSON> v. <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_v._<PERSON>"}]}, {"year": "2001", "text": "Prime Minister of Vietnam <PERSON><PERSON> announces the decision on upgrading the Phong Nha-Kẻ Bàng nature reserve to a national park, providing information on projects for the conservation and development of the park and revised maps.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vietnam\" title=\"Prime Minister of Vietnam\">Prime Minister of Vietnam</a> <a href=\"https://wikipedia.org/wiki/Phan_V%C4%83n_Kh%E1%BA%A3i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces the decision on upgrading the Phong Nha-Kẻ Bàng nature reserve to a <a href=\"https://wikipedia.org/wiki/Phong_Nha-K%E1%BA%BB_B%C3%A0ng_National_Park\" class=\"mw-redirect\" title=\"Phong Nha-Kẻ Bàng National Park\">national park</a>, providing information on projects for the conservation and development of the park and revised maps.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Vietnam\" title=\"Prime Minister of Vietnam\">Prime Minister of Vietnam</a> <a href=\"https://wikipedia.org/wiki/Phan_V%C4%83n_Kh%E1%BA%A3i\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> announces the decision on upgrading the Phong Nha-Kẻ Bàng nature reserve to a <a href=\"https://wikipedia.org/wiki/Phong_Nha-K%E1%BA%BB_B%C3%A0ng_National_Park\" class=\"mw-redirect\" title=\"Phong Nha-Kẻ Bàng National Park\">national park</a>, providing information on projects for the conservation and development of the park and revised maps.", "links": [{"title": "Prime Minister of Vietnam", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Phan_V%C4%83n_Kh%E1%BA%A3i"}, {"title": "Phong Nha-Kẻ Bàng National Park", "link": "https://wikipedia.org/wiki/Phong_Nha-K%E1%BA%BB_B%C3%A0ng_National_Park"}]}, {"year": "2012", "text": "North Korea successfully launches its first satellite, Kwangmyŏngsŏng-3 Unit 2.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> successfully <a href=\"https://wikipedia.org/wiki/Timeline_of_first_orbital_launches_by_country\" title=\"Timeline of first orbital launches by country\">launches its first satellite</a>, <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-3_Unit_2\" title=\"Kwangmyŏngsŏng-3 Unit 2\">Kwangmyŏngsŏng-3 Unit 2</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> successfully <a href=\"https://wikipedia.org/wiki/Timeline_of_first_orbital_launches_by_country\" title=\"Timeline of first orbital launches by country\">launches its first satellite</a>, <a href=\"https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-3_Unit_2\" title=\"Kwangmyŏngsŏng-3 Unit 2\">Kwangmyŏngsŏng-3 Unit 2</a>.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Timeline of first orbital launches by country", "link": "https://wikipedia.org/wiki/Timeline_of_first_orbital_launches_by_country"}, {"title": "Kwangmyŏngsŏng-3 Unit 2", "link": "https://wikipedia.org/wiki/Kwangmy%C5%8Fngs%C5%8Fng-3_Unit_2"}]}, {"year": "2015", "text": "The Paris Agreement relating to United Nations Framework Convention on Climate Change is adopted.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris Agreement</a> relating to <a href=\"https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change\" title=\"United Nations Framework Convention on Climate Change\">United Nations Framework Convention on Climate Change</a> is adopted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Paris_Agreement\" title=\"Paris Agreement\">Paris Agreement</a> relating to <a href=\"https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change\" title=\"United Nations Framework Convention on Climate Change\">United Nations Framework Convention on Climate Change</a> is adopted.", "links": [{"title": "Paris Agreement", "link": "https://wikipedia.org/wiki/Paris_Agreement"}, {"title": "United Nations Framework Convention on Climate Change", "link": "https://wikipedia.org/wiki/United_Nations_Framework_Convention_on_Climate_Change"}]}, {"year": "2021", "text": "Dutch Formula One racing driver <PERSON> wins the controversial 2021 Abu Dhabi Grand Prix, beating seven-time World Champion <PERSON> to become the first Formula One World Champion to come from the Netherlands.", "html": "2021 - Dutch <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> racing driver <a href=\"https://wikipedia.org/wiki/Max_V<PERSON>tappen\" title=\"<PERSON> V<PERSON>ppen\"><PERSON></a> wins the controversial <a href=\"https://wikipedia.org/wiki/2021_Abu_Dhabi_Grand_Prix\" title=\"2021 Abu Dhabi Grand Prix\">2021 Abu Dhabi Grand Prix</a>, beating seven-time World Champion <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become the first Formula One World Champion to come from <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">the Netherlands</a>.", "no_year_html": "Dutch <a href=\"https://wikipedia.org/wiki/Formula_One\" title=\"Formula One\">Formula One</a> racing driver <a href=\"https://wikipedia.org/wiki/Max_Verstappen\" title=\"<PERSON> V<PERSON>ppen\"><PERSON></a> wins the controversial <a href=\"https://wikipedia.org/wiki/2021_Abu_Dhabi_Grand_Prix\" title=\"2021 Abu Dhabi Grand Prix\">2021 Abu Dhabi Grand Prix</a>, beating seven-time World Champion <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to become the first Formula One World Champion to come from <a href=\"https://wikipedia.org/wiki/Netherlands\" title=\"Netherlands\">the Netherlands</a>.", "links": [{"title": "Formula One", "link": "https://wikipedia.org/wiki/Formula_One"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vers<PERSON>ppen"}, {"title": "2021 Abu Dhabi Grand Prix", "link": "https://wikipedia.org/wiki/2021_Abu_Dhabi_Grand_Prix"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Netherlands", "link": "https://wikipedia.org/wiki/Netherlands"}]}, {"year": "2024", "text": "Indian Grandmaster <PERSON><PERSON><PERSON> became the undisputed World Chess Champion in a tournament held in Singapore, making him the 18th and the youngest champion in chess history.", "html": "2024 - Indian <a href=\"https://wikipedia.org/wiki/Grandmaster_(chess)\" title=\"Grandmaster (chess)\">Grandmaster</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> became the undisputed <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_2024\" title=\"World Chess Championship 2024\">World Chess Champion</a> in a tournament held in Singapore, making him the 18th and the <a href=\"https://wikipedia.org/wiki/List_of_youngest_grandmasters\" class=\"mw-redirect\" title=\"List of youngest grandmasters\">youngest champion</a> in chess history.", "no_year_html": "Indian <a href=\"https://wikipedia.org/wiki/Grandmaster_(chess)\" title=\"Grandmaster (chess)\">Grandmaster</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dommaraju\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> became the undisputed <a href=\"https://wikipedia.org/wiki/World_Chess_Championship_2024\" title=\"World Chess Championship 2024\">World Chess Champion</a> in a tournament held in Singapore, making him the 18th and the <a href=\"https://wikipedia.org/wiki/List_of_youngest_grandmasters\" class=\"mw-redirect\" title=\"List of youngest grandmasters\">youngest champion</a> in chess history.", "links": [{"title": "Grandma<PERSON> (chess)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(chess)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON>sh_Dommaraju"}, {"title": "World Chess Championship 2024", "link": "https://wikipedia.org/wiki/World_Chess_Championship_2024"}, {"title": "List of youngest grandmasters", "link": "https://wikipedia.org/wiki/List_of_youngest_grandmasters"}]}], "Births": [{"year": "1526", "text": "<PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz, Spanish admiral (d. 1588)", "html": "1526 - <a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A1n,_1st_Marquis_of_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz\"><PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz</a>, Spanish admiral (d. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%C3%A1n,_1st_Marquis_of_Santa_Cruz\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz\"><PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz</a>, Spanish admiral (d. 1588)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Marquis of Santa Cruz", "link": "https://wikipedia.org/wiki/%C3%81l<PERSON>_<PERSON>_<PERSON>%C3%A1n,_1st_Marquis_of_Santa_Cruz"}]}, {"year": "1685", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian pianist and composer (d. 1743)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and composer (d. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian pianist and composer (d. 1743)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1724", "text": "<PERSON>, 1st Viscount <PERSON>, English admiral and politician (d. 1816)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English admiral and politician (d. 1816)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1786", "text": "<PERSON>, American lawyer, judge, and politician, 21st United States Secretary of State (d. 1857)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician, 21st <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1799", "text": "<PERSON>, Russian painter (d. 1852)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, American businessman, co-founded Wells Fargo and American Express (d. 1878)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> and <a href=\"https://wikipedia.org/wiki/American_Express\" title=\"American Express\">American Express</a> (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Henry Wells\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Wells_Fargo\" title=\"Wells Fargo\">Wells Fargo</a> and <a href=\"https://wikipedia.org/wiki/American_Express\" title=\"American Express\">American Express</a> (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wells Fargo", "link": "https://wikipedia.org/wiki/Wells_Fargo"}, {"title": "American Express", "link": "https://wikipedia.org/wiki/American_Express"}]}, {"year": "1806", "text": "<PERSON>, American general (d. 1871)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Stand_Watie\" title=\"Stand Watie\"><PERSON> Watie</a>, American general (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stand_Watie\" title=\"Stand Watie\"><PERSON> Watie</a>, American general (d. 1871)", "links": [{"title": "Stand Watie", "link": "https://wikipedia.org/wiki/Stand_Watie"}]}, {"year": "1812", "text": "<PERSON>, Canadian lawyer and politician, 1st Premier of Ontario (d. 1872)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Ontario\" title=\"Premier of Ontario\">Premier of Ontario</a> (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Ontario", "link": "https://wikipedia.org/wiki/Premier_of_Ontario"}]}, {"year": "1821", "text": "<PERSON><PERSON>, French novelist (d. 1880)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French novelist (d. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Confederate general (d. 1897)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Confederate general (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Confederate general (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, German journalist and historian (d. 1901)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and historian (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and historian (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Adolf_B%C3%B6<PERSON>cher"}]}, {"year": "1845", "text": "<PERSON>, American architect, designed the American Surety Building and Bank of the Metropolis (d. 1903)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/American_Surety_Building\" title=\"American Surety Building\">American Surety Building</a> and <a href=\"https://wikipedia.org/wiki/Bank_of_the_Metropolis\" title=\"Bank of the Metropolis\">Bank of the Metropolis</a> (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/American_Surety_Building\" title=\"American Surety Building\">American Surety Building</a> and <a href=\"https://wikipedia.org/wiki/Bank_of_the_Metropolis\" title=\"Bank of the Metropolis\">Bank of the Metropolis</a> (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Surety Building", "link": "https://wikipedia.org/wiki/American_Surety_Building"}, {"title": "Bank of the Metropolis", "link": "https://wikipedia.org/wiki/Bank_of_the_Metropolis"}]}, {"year": "1863", "text": "<PERSON><PERSON>, Norwegian painter (d. 1944)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian painter (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian painter (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Swiss chemist and academic, Nobel Prize laureate (d. 1919)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1870", "text": "<PERSON>, American businessman, co-founded Hughes Tool Company (d. 1912)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Hughes_Tool_Company\" title=\"Hughes Tool Company\">Hughes Tool Company</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Walter <PERSON> Sharp\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Hughes_Tool_Company\" title=\"Hughes Tool Company\">Hughes Tool Company</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hughes Tool Company", "link": "https://wikipedia.org/wiki/Hughes_Tool_Company"}]}, {"year": "1876", "text": "<PERSON>, American hurdler and runner (d. 1928)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and runner (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American hurdler and runner (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, French school teacher, resistance fighter during World War I and World War II and author (d. 1966)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French school teacher, resistance fighter during World War I and World War II and author (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French school teacher, resistance fighter during World War I and World War II and author (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American actor (d. 1973)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Estonian footballer (d. 1942)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, English pianist and composer (d. 2015)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American boxer (d. 1988)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English author (d. 2000)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Brian\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_O%27Brian"}]}, {"year": "1915", "text": "<PERSON>, American singer, actor, and producer (d. 1998)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sinatra\" title=\"Frank Sinatra\"><PERSON></a>, American singer, actor, and producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sinatra\" title=\"Frank Sinatra\"><PERSON></a>, American singer, actor, and producer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American singer and pianist (d. 1999)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)\" title=\"<PERSON> (jazz singer)\"><PERSON></a>, American singer and pianist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)\" title=\"<PERSON> (jazz singer)\"><PERSON></a>, American singer and pianist (d. 1999)", "links": [{"title": "<PERSON> (jazz singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jazz_singer)"}]}, {"year": "1920", "text": "<PERSON>, Czech race walker (d. 1999)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEal\" title=\"<PERSON>\"><PERSON></a>, Czech race walker (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEal\" title=\"<PERSON>\"><PERSON></a>, Czech race walker (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%BEal"}]}, {"year": "1923", "text": "<PERSON>, American game show host and producer (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game show host and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American musician, composer, and producer (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, composer, and producer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, composer, and producer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian pianist, composer and nun (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Emahoy_Tsegu%C3%A9-<PERSON><PERSON>_<PERSON>u%C3%A8brou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Ethiopian pianist, composer and nun (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em<PERSON><PERSON>_Tsegu%C3%A9-<PERSON><PERSON>_<PERSON>u%C3%A8brou\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Ethiopian pianist, composer and nun (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emahoy_Tsegu%C3%A9-<PERSON><PERSON>_Gu%C3%A8brou"}]}, {"year": "1924", "text": "<PERSON>, American politician, 105th Mayor of New York City (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 105th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 105th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1925", "text": "<PERSON>, Canadian ice hockey player (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player (d. 2009)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 1985)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Ukrainian-Russian pianist and composer (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist and composer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian pianist and composer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American inventor and businessman, co-founded the Intel Corporation (d. 1990)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel Corporation</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor and businessman, co-founded the <a href=\"https://wikipedia.org/wiki/Intel\" title=\"Intel\">Intel Corporation</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Intel", "link": "https://wikipedia.org/wiki/Intel"}]}, {"year": "1928", "text": "<PERSON>, American painter and academic (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Japanese pianist and composer", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American basketball player and coach", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German sprinter (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German sprinter (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Mexican lawyer and politician, 52nd President of Mexico (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Madrid\" title=\"Miguel <PERSON> la Madrid\"><PERSON></a>, Mexican lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Madrid\" title=\"Miguel <PERSON> Madrid\"><PERSON></a>, Mexican lawyer and politician, 52nd <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Madrid"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Romanian high jumper and educator (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Iolanda_Bala%C8%99\" title=\"Iolanda Balaș\"><PERSON><PERSON><PERSON></a>, Romanian high jumper and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iolanda_Bala%C8%99\" title=\"Iolanda Balaș\"><PERSON><PERSON><PERSON></a>, Romanian high jumper and educator (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iolanda_Bala%C8%99"}]}, {"year": "1937", "text": "<PERSON>, American singer, musician, and actress", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, musician, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, musician, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English pianist, composer, and academic (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Indian politician, Indian Minister of Agriculture", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(India)\" class=\"mw-redirect\" title=\"Minister of Agriculture (India)\">Indian Minister of Agriculture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(India)\" class=\"mw-redirect\" title=\"Minister of Agriculture (India)\">Indian Minister of Agriculture</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Agriculture (India)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(India)"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American singer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American jazz pianist, composer and arranger", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American jazz pianist, composer and arranger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(pianist)\" title=\"<PERSON> (pianist)\"><PERSON></a>, American jazz pianist, composer and arranger", "links": [{"title": "<PERSON> (pianist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pianist)"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American musician and songwriter (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and songwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American musician and songwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Jr., American singer-songwriter, saxophonist, and producer (d. 1999)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer-songwriter, saxophonist, and producer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>r_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American singer-songwriter, saxophonist, and producer (d. 1999)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>r_<PERSON>,_Jr."}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Icelandic politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/G%C3%ADsli_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%ADsli_<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%ADsli_<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American drummer, composer, and producer (d. 1997)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer, composer, and producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)\" title=\"<PERSON> (drummer)\"><PERSON></a>, American drummer, composer, and producer (d. 1997)", "links": [{"title": "<PERSON> (drummer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(drummer)"}]}, {"year": "1946", "text": "<PERSON>, Brazilian racing driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Wings_Hauser\" title=\"Wings Hauser\"><PERSON> Hauser</a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wings_Hauser\" title=\"Wings Hauser\"><PERSON> Hauser</a>, American actor", "links": [{"title": "Wings Hauser", "link": "https://wikipedia.org/wiki/<PERSON>_Hauser"}]}, {"year": "1947", "text": "<PERSON>, American writer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English journalist and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English journalist and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1948", "text": "<PERSON>, American basketball player (d. 2009)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player (d. 2009)", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1948", "text": "<PERSON>, English football player and coach", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English actor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Malagasy businessman and politician, President of Madagascar", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President of Madagascar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malagasy businessman and politician, <a href=\"https://wikipedia.org/wiki/President_of_Madagascar\" class=\"mw-redirect\" title=\"President of Madagascar\">President of Madagascar</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Madagascar", "link": "https://wikipedia.org/wiki/President_of_Madagascar"}]}, {"year": "1950", "text": "<PERSON>, Mexican journalist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, German economist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>beck\"><PERSON><PERSON></a>, German economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> F<PERSON>beck\"><PERSON><PERSON></a>, German economist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Canadian ice hockey player, coach, and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player, coach, and manager", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Pakistani politician, Pakistani Minister of Interior (d. 2022)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)\" title=\"Ministry of Interior (Pakistan)\">Pakistani Minister of Interior</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)\" title=\"Ministry of Interior (Pakistan)\">Pakistani Minister of Interior</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Interior (Pakistan)", "link": "https://wikipedia.org/wiki/Ministry_of_Interior_(Pakistan)"}]}, {"year": "1952", "text": "<PERSON>, American gymnast", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian lawyer and politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Australian lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1953", "text": "<PERSON>, Mexican-American football player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Mexican-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9n\" title=\"<PERSON>\"><PERSON></a>, Mexican-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_Septi%C3%A9n"}]}, {"year": "1955", "text": "<PERSON>, Belgian cyclist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}]}, {"year": "1956", "text": "<PERSON>, Dutch cyclist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer and musician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}]}, {"year": "1958", "text": "<PERSON><PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, German discus thrower", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Russian race walker", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian race walker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American tennis player and sportscaster", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Austin\" title=\"Tracy Austin\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tracy_Austin\" title=\"Tracy Austin\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tracy_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Mexican-American runner", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ios"}]}, {"year": "1962", "text": "<PERSON>, American football player and radio host", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Mexican businessman and politician (d. 2012)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican businessman and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American football player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Haywood Jeffires\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Haywood Jeffires\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "Haywood Jeffires", "link": "https://wikipedia.org/wiki/Haywood_Jeffires"}]}, {"year": "1964", "text": "<PERSON><PERSON>, American wrestler", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1965", "text": "<PERSON>, American funk and R&B drummer (d. 2023)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American funk and R&amp;B drummer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American funk and R&amp;B drummer (d. 2023)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1965", "text": "<PERSON>, English rugby union player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby union player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby union player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American football player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Slovenian footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Sa%C5%A1o_Udovi%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa%C5%A1o_Udovi%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sa%C5%A1o_Udovi%C4%8D"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Kenyan runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English-Italian long jumper", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON> May</a>, English-Italian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May\" title=\"<PERSON> May\"><PERSON></a>, English-Italian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1969", "text": "<PERSON>, German discus thrower", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress and director", "html": "1970 - <a href=\"https://wikipedia.org/wiki/M%C3%A4dchen_Amick\" title=\"<PERSON><PERSON><PERSON><PERSON> Amick\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A4dchen_Amick\" title=\"<PERSON><PERSON><PERSON><PERSON> Amick\"><PERSON><PERSON><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A4dchen_Amick"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Regina_Hall\" title=\"Regina Hall\"><PERSON> Hall</a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Regina_Hall\" title=\"Regina Hall\"><PERSON> Hall</a>, American actress", "links": [{"title": "Regina Hall", "link": "https://wikipedia.org/wiki/Regina_Hall"}]}, {"year": "1971", "text": "<PERSON>, Kenyan runner", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English footballer and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian rugby league player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Craig Field\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Craig_<PERSON>\" title=\"Craig Field\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Craig_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Kenyan-Danish runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Danish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-Danish runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Greek sprinter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>idis"}]}, {"year": "1974", "text": "<PERSON>, Kenyan-American runner", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan-American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Peruvian footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>lbert<PERSON>_<PERSON>ano\" title=\"<PERSON>lberto <PERSON>ano\"><PERSON><PERSON><PERSON></a>, Peruvian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>lbert<PERSON>_<PERSON>ano\" title=\"<PERSON>lbert<PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actress, neuroscientist, and author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/May<PERSON>_<PERSON>\" title=\"May<PERSON>\"><PERSON><PERSON></a>, American actress, neuroscientist, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May<PERSON>_<PERSON>\" title=\"May<PERSON>\"><PERSON><PERSON></a>, American actress, neuroscientist, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ik"}]}, {"year": "1975", "text": "<PERSON>, Australian footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Cuban hurdler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hern%C3%<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, Cuban hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hern%C3%<PERSON><PERSON><PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (athlete)\"><PERSON><PERSON></a>, Cuban hurdler", "links": [{"title": "<PERSON><PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hern%C3%<PERSON><PERSON><PERSON>_(athlete)"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Orlando_Hudson\" title=\"Orlando Hudson\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Hudson\" title=\"Orlando Hudson\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Hudson"}]}, {"year": "1977", "text": "<PERSON>, English decathlete and bobsledder", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English decathlete and bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English decathlete and bobsledder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Canadian ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1977)\" title=\"<PERSON> (ice hockey, born 1977)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1977)\" title=\"<PERSON> (ice hockey, born 1977)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1977)"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner (d. 2010)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Romanian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Go<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Go<PERSON>\" title=\"<PERSON><PERSON> Go<PERSON>\"><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Pedro_R%C3%ADos\" title=\"Pedro <PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_R%C3%ADos\" title=\"Pedro <PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_R%C3%ADos"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player and commentator", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Russian tennis player and coach", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Finnish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Danish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Greek-American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Rwandan footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Daddy_<PERSON>\" title=\"Daddy Birori\"><PERSON></a>, Rwandan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daddy_<PERSON><PERSON>\" title=\"Daddy Birori\"><PERSON></a>, Rwandan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/P%C3%ABrparim_<PERSON>\" title=\"<PERSON>ërpari<PERSON> Hetem<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%ABrpari<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>rp<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%ABrpari<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Slovenian long jumper", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Slovenian long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8D\" title=\"<PERSON>\"><PERSON></a>, Slovenian long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nina_Kolari%C4%8D"}]}, {"year": "1986", "text": "<PERSON><PERSON> <PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"T. J<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Israeli Olympic marathon runner", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>emtai_Salpeter\" title=\"Lonah Chemtai Salpeter\"><PERSON><PERSON></a>, Israeli Olympic marathon runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>emtai_Salpeter\" title=\"Lonah Chemtai Salpeter\"><PERSON><PERSON></a>, Israeli Olympic marathon runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nah_Chemtai_Salpeter"}]}, {"year": "1990", "text": "<PERSON>, Kenyan runner", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>win"}]}, {"year": "1990", "text": "<PERSON>, Nigerian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American football player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian-Samoan rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-Samoan rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American student imprisoned in North Korea (d. 2017)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American student imprisoned in <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American student imprisoned in <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Otto_<PERSON>"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}]}, {"year": "1996", "text": "<PERSON>, American actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2001", "text": "<PERSON>, French footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "884", "text": "King <PERSON><PERSON> II of the Franks (born c.866; hunting accident)", "html": "884 - <PERSON> <a href=\"https://wikipedia.org/wiki/Carloman_II\" title=\"Carloman II\">Carloman II</a> of the Franks (born c.866; <a href=\"https://wikipedia.org/wiki/Hunting\" title=\"Hunting\">hunting</a> accident)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Carloman_II\" title=\"Carloman II\">Carloman II</a> of the Franks (born c.866; <a href=\"https://wikipedia.org/wiki/Hunting\" title=\"Hunting\">hunting</a> accident)", "links": [{"title": "Carloman II", "link": "https://wikipedia.org/wiki/Carloman_II"}, {"title": "Hunting", "link": "https://wikipedia.org/wiki/Hunting"}]}, {"year": "1296", "text": "<PERSON> of Mar, first wife of <PERSON> (b. 1277)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Mar\"><PERSON> of Mar</a>, first wife of <PERSON> (b. 1277)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Mar\" title=\"<PERSON> of Mar\"><PERSON> of Mar</a>, first wife of <PERSON> (b. 1277)", "links": [{"title": "<PERSON> of Mar", "link": "https://wikipedia.org/wiki/Isabella_of_Mar"}]}, {"year": "1572", "text": "<PERSON><PERSON><PERSON>, Dog<PERSON><PERSON> of Venice, botanist, author", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> of Venice, botanist, author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON><PERSON> of Venice, botanist, author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1751", "text": "<PERSON>, 1st Viscount <PERSON>, English philosopher and politician, Secretary at War (b. 1678)", "html": "1751 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Bolingbroke\" title=\"<PERSON>, 1st Viscount Bolingbroke\"><PERSON>, 1st Viscount Boling<PERSON>ke</a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1678)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Bolingbro<PERSON>\" title=\"<PERSON>, 1st Viscount Boling<PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Secretary_at_War\" title=\"Secretary at War\">Secretary at War</a> (b. 1678)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Viscount_<PERSON>ling<PERSON>"}, {"title": "Secretary at War", "link": "https://wikipedia.org/wiki/Secretary_at_War"}]}, {"year": "1766", "text": "<PERSON>, German philosopher, author, and critic (b. 1700)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, author, and critic (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, author, and critic (b. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian author (b. 1742)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/Meshulla<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Meshullam Fe<PERSON>\"><PERSON>shu<PERSON><PERSON></a>, Ukrainian author (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meshulla<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Meshullam Feivush <PERSON>er\">Meshu<PERSON><PERSON> <PERSON><PERSON></a>, Ukrainian author (b. 1742)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>shu<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1803", "text": "<PERSON> <PERSON> of Sweden (b. 1750)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Prince <PERSON> of Sweden\">Prince <PERSON> of Sweden</a> (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Prince <PERSON> of Sweden\">Prince <PERSON> of Sweden</a> (b. 1750)", "links": [{"title": "Prince <PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1858", "text": "<PERSON>, Canadian archeologist and politician, 1st Mayor of Montreal (b. 1787)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(1787%E2%80%931858)\" class=\"mw-redirect\" title=\"<PERSON> (1787-1858)\"><PERSON></a>, Canadian archeologist and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Montreal_mayors\" class=\"mw-redirect\" title=\"List of Montreal mayors\">Mayor of Montreal</a> (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(1787%E2%80%931858)\" class=\"mw-redirect\" title=\"<PERSON> (1787-1858)\"><PERSON></a>, Canadian archeologist and politician, 1st <a href=\"https://wikipedia.org/wiki/List_of_Montreal_mayors\" class=\"mw-redirect\" title=\"List of Montreal mayors\">Mayor of Montreal</a> (b. 1787)", "links": [{"title": "<PERSON> (1787-1858)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1787%E2%80%931858)"}, {"title": "List of Montreal mayors", "link": "https://wikipedia.org/wiki/List_of_Montreal_mayors"}]}, {"year": "1889", "text": "<PERSON>, Ukrainian-Russian mathematician and theorist (b. 1804)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and theorist (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Russian mathematician and theorist (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, Canadian lawyer, judge, and politician, 4th Prime Minister of Canada (b. 1845)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, judge, and politician, 4th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Ethiopian emperor (b. 1844)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Meneli<PERSON>_II\" title=\"Menelik II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ethiopian emperor (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II\" title=\"Menelik II\"><PERSON><PERSON><PERSON> <PERSON></a>, Ethiopian emperor (b. 1844)", "links": [{"title": "Menelik II", "link": "https://wikipedia.org/wiki/<PERSON>elik_II"}]}, {"year": "1921", "text": "<PERSON>, American astronomer and academic (b. 1868)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and academic (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French author and poet (b. 1903)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Norwegian skier (b. 1894)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ug"}]}, {"year": "1939", "text": "<PERSON>, <PERSON>., American actor, producer, and screenwriter (b. 1883)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American actor, producer, and screenwriter (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a>, American actor, producer, and screenwriter (b. 1883)", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>."}]}, {"year": "1941", "text": "<PERSON>, Filipino lieutenant and pilot (b. 1915)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lieutenant and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lieutenant and pilot (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Basa"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, British biochemist (b. 1885)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British biochemist (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British biochemist (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American singer (b. 1907)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer (b. 1907)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian lawyer and politician, 1st Lieutenant Governor of Newfoundland (b. 1900)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador\" title=\"Lieutenant Governor of Newfoundland and Labrador\">Lieutenant Governor of Newfoundland</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador\" title=\"Lieutenant Governor of Newfoundland and Labrador\">Lieutenant Governor of Newfoundland</a> (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Lieutenant Governor of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Newfoundland_and_Labrador"}]}, {"year": "1966", "text": "<PERSON>, Austrian-American swimmer (b. 1880)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American swimmer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1902)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Tallulah_Bankhead\" title=\"Tallulah Bankhead\"><PERSON><PERSON><PERSON></a>, American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tallulah_Bankhead\" title=\"Tallulah Bankhead\"><PERSON><PERSON><PERSON></a>, American actress (b. 1902)", "links": [{"title": "Tallulah Bankhead", "link": "https://wikipedia.org/wiki/Tallulah_Bankhead"}]}, {"year": "1970", "text": "<PERSON>, Australian politician (b. 1889)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English colonel and cricketer (b. 1884)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English colonel and cricketer (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English colonel and cricketer (b. 1884)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1980", "text": "<PERSON>, Canadian lawyer and politician, 19th Premier of Quebec (b. 1912)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1985", "text": "<PERSON>, American actress (b. 1923)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian historian and politician, 35th Prime Minister of Hungary (b. 1932)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian historian and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian historian and politician, 35th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>l"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1994", "text": "<PERSON>, American colonel, pilot, and astronaut (b. 1933)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American journalist, author, and critic (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and critic (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian-Russian mathematician and academic (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON>vgen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian-Russian mathematician and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Evgen<PERSON>_<PERSON>\" title=\"<PERSON>vgen<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian-Russian mathematician and academic (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evgenii_Landis"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American soldier, lawyer, and politician, 41st Governor of Florida (b. 1930)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lawton_Chiles\" title=\"Lawton Chiles\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lawton_Chiles\" title=\"Lawton Chiles\"><PERSON><PERSON></a>, American soldier, lawyer, and politician, 41st <a href=\"https://wikipedia.org/wiki/Governor_of_Florida\" title=\"Governor of Florida\">Governor of Florida</a> (b. 1930)", "links": [{"title": "<PERSON>ton <PERSON>s", "link": "https://wikipedia.org/wiki/Lawton_Chiles"}, {"title": "Governor of Florida", "link": "https://wikipedia.org/wiki/Governor_of_Florida"}]}, {"year": "1998", "text": "<PERSON>, American captain and politician (b. 1922)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American captain and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American painter and illustrator (b. 1904)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American novelist, short story writer, and playwright(b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright(b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and playwright(b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Italian geologist and explorer (b. 1897)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian geologist and explorer (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian geologist and explorer (b. 1897)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ardito_Desio"}]}, {"year": "2002", "text": "<PERSON>, American historian and author (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American historian and author (b. 1908)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Azerbaijani general and politician, 3rd President of Azerbaijan (b. 1923)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Azerbaijan\" title=\"President of Azerbaijan\">President of Azerbaijan</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Azerbaijani general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Azerbaijan\" title=\"President of Azerbaijan\">President of Azerbaijan</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Azerbaijan", "link": "https://wikipedia.org/wiki/President_of_Azerbaijan"}]}, {"year": "2005", "text": "<PERSON>, American actor and producer (b. 1956)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Danish actress (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish actress (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Lebanese journalist and politician (b. 1957)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese journalist and politician (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lebanese journalist and politician (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American basketball player (b. 1928)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actor (b. 1935)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American attorney and politician, 39th Governor of Pennsylvania (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician, 39th <a href=\"https://wikipedia.org/wiki/Governor_of_Pennsylvania\" class=\"mw-redirect\" title=\"Governor of Pennsylvania\">Governor of Pennsylvania</a> (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Pennsylvania", "link": "https://wikipedia.org/wiki/Governor_of_Pennsylvania"}]}, {"year": "2006", "text": "<PERSON>, American engineer and businessman, co-founded Seagate Technology (b. 1930)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Seagate_Technology\" title=\"Seagate Technology\">Seagate Technology</a> (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Seagate_Technology\" title=\"Seagate Technology\">Seagate Technology</a> (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seagate Technology", "link": "https://wikipedia.org/wiki/Seagate_Technology"}]}, {"year": "2007", "text": "<PERSON>, Lebanese general (b. 1953)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_al-Hajj\" title=\"<PERSON>\"><PERSON></a>, Lebanese general (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_al-Hajj\" title=\"<PERSON>\"><PERSON></a>, Lebanese general (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_al-Hajj"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American singer-songwriter, guitarist, and producer (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American cardinal and theologian (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal and theologian (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal and theologian (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1916)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Scottish race car driver, founded Tom <PERSON>haw Racing (b. 1946)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Racing\" title=\"Tom Walkinshaw Racing\">Tom <PERSON> Racing</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish race car driver, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Tom Walkinshaw Racing\"><PERSON></a> (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American banker, publisher, and philanthropist, founded the Allbritton Communications Company (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, publisher, and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Allbritton_Communications_Company\" class=\"mw-redirect\" title=\"Allbritton Communications Company\">Allbritton Communications Company</a> (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American banker, publisher, and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Allbritton_Communications_Company\" class=\"mw-redirect\" title=\"Allbritton Communications Company\">Allbritton Communications Company</a> (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Allbritton Communications Company", "link": "https://wikipedia.org/wiki/Allbritton_Communications_Company"}]}, {"year": "2012", "text": "<PERSON>, English rugby player (b. 1987)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actor, director, screenwriter, author, educator, and activist (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, author, educator, and activist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, screenwriter, author, educator, and activist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Bangladeshi journalist and politician (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi journalist and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Bangladeshi journalist and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American author and illustrator, created <PERSON> the Big Red Dog (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Big_Red_Dog\" title=\"<PERSON> the Big Red Dog\"><PERSON> the Big Red Dog</a></i> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator, created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Big_Red_Dog\" title=\"<PERSON> the Big Red Dog\"><PERSON> the Big Red Dog</a></i> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the Big Red Dog", "link": "https://wikipedia.org/wiki/<PERSON>_the_Big_Red_Dog"}]}, {"year": "2014", "text": "<PERSON><PERSON>-<PERSON>, English mathematician, historian, and academic (b. 1941)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, English mathematician, historian, and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON></a>, English mathematician, historian, and academic (b. 1941)", "links": [{"title": "<PERSON><PERSON>-Guinness", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Dutch footballer (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Fr<PERSON>_<PERSON>en\" title=\"<PERSON><PERSON>en\"><PERSON><PERSON></a>, Dutch footballer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American politician, White House Deputy Chief of Staff (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff\" title=\"White House Deputy Chief of Staff\">White House Deputy Chief of Staff</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff\" title=\"White House Deputy Chief of Staff\">White House Deputy Chief of Staff</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "White House Deputy Chief of Staff", "link": "https://wikipedia.org/wiki/White_House_Deputy_Chief_of_Staff"}]}, {"year": "2016", "text": "<PERSON>, Australian-American novelist, short story writer, and essayist (b. 1931)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American novelist, short story writer, and essayist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American novelist, short story writer, and essayist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American politician and attorney, 43rd Mayor of San Francisco (b. 1952)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician and attorney, 43rd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician and attorney, 43rd <a href=\"https://wikipedia.org/wiki/Mayor_of_San_Francisco\" title=\"Mayor of San Francisco\">Mayor of San Francisco</a> (b. 1952)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Mayor of San Francisco", "link": "https://wikipedia.org/wiki/Mayor_of_San_Francisco"}]}, {"year": "2017", "text": "<PERSON>, American singer and songwriter  (b. 1955)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_DiNizio"}]}, {"year": "2019", "text": "<PERSON>, American actor (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English author (b. 1931)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "2020", "text": "<PERSON>, American actress, dancer, and choreographer (b. 1949)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, dancer, and choreographer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ink<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Mexican actor, ranchera singer, and film producer (b. 1940)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Fern%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, ranchera singer, and film producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Mexican actor, ranchera singer, and film producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Fern%C3%A1ndez"}]}, {"year": "2021", "text": "<PERSON>, American politician and environmental advocate (b. 1924)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and environmental advocate (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and environmental advocate (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": " <PERSON><PERSON><PERSON><PERSON>, Tongan politician and military officer, Deputy Prime Minister (b. 1955)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and military officer, Deputy Prime Minister (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi\" title=\"<PERSON>ʻ<PERSON><PERSON>aul<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tongan politician and military officer, Deputy Prime Minister (b. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ma%CA%BBafu_Tukui%CA%BBaulahi"}]}]}}