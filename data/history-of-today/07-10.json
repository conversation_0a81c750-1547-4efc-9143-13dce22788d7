{"date": "July 10", "url": "https://wikipedia.org/wiki/July_10", "data": {"Events": [{"year": "138", "text": "Emperor <PERSON><PERSON> of Rome dies of heart failure at his residence on the bay of Naples, Baiae; he is buried at Rome in the Tomb of <PERSON><PERSON> beside his late wife, <PERSON><PERSON><PERSON>.", "html": "138 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>rian\" title=\"Hadrian\"><PERSON><PERSON></a> of Rome dies of <a href=\"https://wikipedia.org/wiki/Heart_failure\" title=\"Heart failure\">heart failure</a> at his residence on the bay of Naples, <a href=\"https://wikipedia.org/wiki/Baiae\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>; he is buried at <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> in the <a href=\"https://wikipedia.org/wiki/Castel_Sant%27Angelo\" title=\"Castel Sant'Angelo\">Tomb of <PERSON>rian</a> beside his late wife, <a href=\"https://wikipedia.org/wiki/Vibia_Sabina\" title=\"Vibia Sabina\">Vibia <PERSON></a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>rian\" title=\"Hadrian\"><PERSON><PERSON></a> of Rome dies of <a href=\"https://wikipedia.org/wiki/Heart_failure\" title=\"Heart failure\">heart failure</a> at his residence on the bay of Naples, <a href=\"https://wikipedia.org/wiki/Baiae\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>; he is buried at <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> in the <a href=\"https://wikipedia.org/wiki/Castel_Sant%27Angelo\" title=\"Castel Sant'Angelo\">Tomb of <PERSON>rian</a> beside his late wife, <a href=\"https://wikipedia.org/wiki/Vibia_Sabina\" title=\"Vibia Sabina\">Vibia Sabina</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Heart failure", "link": "https://wikipedia.org/wiki/Heart_failure"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ae"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Castel Sant'Angelo", "link": "https://wikipedia.org/wiki/Castel_Sant%27Angelo"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vibia_Sabina"}]}, {"year": "420", "text": "Having usurped the throne of Emperor <PERSON> of <PERSON>, <PERSON> proclaims himself Emperor of the Liu Song dynasty.", "html": "420 - Having usurped the throne of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_<PERSON>\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Song\" title=\"Emperor Wu of Song\"><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Southern_dynasties\" title=\"List of emperors of the Southern dynasties\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Liu_Song_dynasty\" title=\"Liu Song dynasty\">Liu Song dynasty</a>.", "no_year_html": "Having usurped the throne of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, <a href=\"https://wikipedia.org/wiki/Emperor_Wu_of_Song\" title=\"Emperor <PERSON> of Song\"><PERSON></a> proclaims himself <a href=\"https://wikipedia.org/wiki/List_of_emperors_of_the_Southern_dynasties\" title=\"List of emperors of the Southern dynasties\">Emperor</a> of the <a href=\"https://wikipedia.org/wiki/Liu_Song_dynasty\" title=\"Liu Song dynasty\">Liu Song dynasty</a>.", "links": [{"title": "Emperor <PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Emperor <PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of emperors of the Southern dynasties", "link": "https://wikipedia.org/wiki/List_of_emperors_of_the_Southern_dynasties"}, {"title": "Liu Song dynasty", "link": "https://wikipedia.org/wiki/Liu_Song_dynasty"}]}, {"year": "645", "text": "Isshi Incident: Prince <PERSON><PERSON> and <PERSON><PERSON> <PERSON> assassinate <PERSON><PERSON> <PERSON> during a coup d'état at the imperial palace.", "html": "645 - <a href=\"https://wikipedia.org/wiki/Isshi_Incident\" class=\"mw-redirect\" title=\"Isshi Incident\">Isshi Incident</a>: Prince <a href=\"https://wikipedia.org/wiki/Emperor_Tenji\" title=\"Emperor Tenji\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kamatari\" title=\"Fujiwara no Kamatari\">Fujiwara no Kamatari</a> assassinate <a href=\"https://wikipedia.org/wiki/Soga_no_Iruka\" title=\"Soga no Iruka\">Soga no Iruka</a> during a <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> at the imperial palace.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isshi_Incident\" class=\"mw-redirect\" title=\"Isshi Incident\">Isshi Incident</a>: Prince <a href=\"https://wikipedia.org/wiki/Emperor_Tenji\" title=\"Emperor Tenji\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Kamatari\" title=\"Fujiwara no Kamatari\">Fujiwara no Kamatari</a> assassinate <a href=\"https://wikipedia.org/wiki/Soga_no_Iruka\" title=\"Soga no Iruka\">Soga no Iruka</a> during a <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> at the imperial palace.", "links": [{"title": "Isshi Incident", "link": "https://wikipedia.org/wiki/Isshi_Incident"}, {"title": "Emperor <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON> no <PERSON>matari", "link": "https://wikipedia.org/wiki/Fujiwara_no_Kamatari"}, {"title": "<PERSON>ga no Iruka", "link": "https://wikipedia.org/wiki/So<PERSON>_no_<PERSON><PERSON>a"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "988", "text": "The Norse King <PERSON><PERSON><PERSON><PERSON><PERSON> recognises <PERSON><PERSON><PERSON>, High King of Ireland, and agrees to pay taxes and accept Brehon Law; the event is considered to be the founding of the city of Dublin.", "html": "988 - The Norse King <a href=\"https://wikipedia.org/wiki/Gl%C3%BAniairn\" title=\"Glúniairn\">Glúniairn</a> recognises <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> mac <PERSON></a>, High King of Ireland, and agrees to pay taxes and accept <a href=\"https://wikipedia.org/wiki/Brehon_Law\" class=\"mw-redirect\" title=\"Brehon Law\">Brehon Law</a>; the event is considered to be the founding of the city of <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "no_year_html": "The Norse King <a href=\"https://wikipedia.org/wiki/Gl%C3%BAniairn\" title=\"Glúniairn\">Glúniairn</a> recognises <a href=\"https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, High King of Ireland, and agrees to pay taxes and accept <a href=\"https://wikipedia.org/wiki/Brehon_Law\" class=\"mw-redirect\" title=\"Brehon Law\">Brehon Law</a>; the event is considered to be the founding of the city of <a href=\"https://wikipedia.org/wiki/Dublin\" title=\"Dublin\">Dublin</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gl%C3%BAniairn"}, {"title": "<PERSON><PERSON><PERSON> mac <PERSON>", "link": "https://wikipedia.org/wiki/M%C3%<PERSON><PERSON>_<PERSON>nail<PERSON>_<PERSON>_Domnaill"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Brehon_Law"}, {"title": "Dublin", "link": "https://wikipedia.org/wiki/Dublin"}]}, {"year": "1086", "text": "King <PERSON><PERSON> of Denmark is killed by rebellious peasants.", "html": "1086 - King <a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\">Canute IV of Denmark</a> is killed by rebellious peasants.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\">Can<PERSON> IV of Denmark</a> is killed by rebellious peasants.", "links": [{"title": "Canute IV of Denmark", "link": "https://wikipedia.org/wiki/Canute_IV_of_Denmark"}]}, {"year": "1212", "text": "The most severe of several early fires of London burns most of the city to the ground.", "html": "1212 - The most severe of several <a href=\"https://wikipedia.org/wiki/Early_fires_of_London\" title=\"Early fires of London\">early fires of London</a> burns most of the city to the ground.", "no_year_html": "The most severe of several <a href=\"https://wikipedia.org/wiki/Early_fires_of_London\" title=\"Early fires of London\">early fires of London</a> burns most of the city to the ground.", "links": [{"title": "Early fires of London", "link": "https://wikipedia.org/wiki/Early_fires_of_London"}]}, {"year": "1290", "text": "<PERSON><PERSON><PERSON>, King of Hungary, is assassinated at the castle of Körösszeg (modern-day Cheresig in Romania).", "html": "1290 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"Ladislaus IV of Hungary\">La<PERSON>laus IV</a>, <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a>, is assassinated at the castle of <a href=\"https://wikipedia.org/wiki/Toboliu\" title=\"Toboliu\">Körösszeg</a> (modern-day Cheresig in <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"La<PERSON>laus IV of Hungary\"><PERSON><PERSON><PERSON> IV</a>, <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a>, is assassinated at the castle of <a href=\"https://wikipedia.org/wiki/Toboliu\" title=\"Toboliu\">Körösszeg</a> (modern-day Cheresig in <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>).", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>laus_IV_of_Hungary"}, {"title": "King of Hungary", "link": "https://wikipedia.org/wiki/King_of_Hungary"}, {"title": "Toboliu", "link": "https://wikipedia.org/wiki/Toboliu"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}]}, {"year": "1460", "text": "<PERSON>, 16th Earl of Warwick, defeats the king's Lancastrian forces and takes King <PERSON> prisoner in the Battle of Northampton.", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, defeats the king's <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lancastrian</a> forces and takes <a href=\"https://wikipedia.org/wiki/Henry_VI_of_England\" title=\"Henry VI of England\">King <PERSON> VI</a> prisoner in the <a href=\"https://wikipedia.org/wiki/Battle_of_Northampton_(1460)\" title=\"Battle of Northampton (1460)\">Battle of Northampton</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_16th_Earl_of_Warwick\" title=\"<PERSON>, 16th Earl of Warwick\"><PERSON>, 16th Earl of Warwick</a>, defeats the king's <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lanc<PERSON>rian</a> forces and takes <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" title=\"<PERSON> VI of England\">King <PERSON> VI</a> prisoner in the <a href=\"https://wikipedia.org/wiki/Battle_of_Northampton_(1460)\" title=\"Battle of Northampton (1460)\">Battle of Northampton</a>.", "links": [{"title": "<PERSON>, 16th Earl of Warwick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_16th_Earl_of_Warwick"}, {"title": "House of Lancaster", "link": "https://wikipedia.org/wiki/House_of_Lancaster"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}, {"title": "Battle of Northampton (1460)", "link": "https://wikipedia.org/wiki/Battle_of_Northampton_(1460)"}]}, {"year": "1499", "text": "The Portuguese explorer <PERSON><PERSON> returns to Lisbon after discovering the sea route to India as a companion of <PERSON><PERSON>.", "html": "1499 - The Portuguese explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> after discovering the sea route to India as a companion of <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a>.", "no_year_html": "The Portuguese explorer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> returns to <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> after discovering the sea route to India as a companion of <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\">Vasco da Gama</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}]}, {"year": "1512", "text": "The Spanish conquest of Iberian Navarre commences with the capture of Goizueta.", "html": "1512 - The <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre\" title=\"Spanish conquest of Iberian Navarre\">Spanish conquest of Iberian Navarre</a> commences with the capture of <a href=\"https://wikipedia.org/wiki/Goizueta,_Navarre\" title=\"Goizueta, Navarre\">Goizueta</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre\" title=\"Spanish conquest of Iberian Navarre\">Spanish conquest of Iberian Navarre</a> commences with the capture of <a href=\"https://wikipedia.org/wiki/Goizueta,_Navarre\" title=\"Goizueta, Navarre\">Goizueta</a>.", "links": [{"title": "Spanish conquest of Iberian Navarre", "link": "https://wikipedia.org/wiki/Spanish_conquest_of_Iberian_Navarre"}, {"title": "Goizueta, Navarre", "link": "https://wikipedia.org/wiki/Goizueta,_Navarre"}]}, {"year": "1519", "text": "<PERSON> declares the Ming dynasty's Zhengde Emperor a usurper, beginning the Prince of Ning rebellion, and leads his army north in an attempt to capture Nanjing.", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON>de_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a> a usurper, beginning the <a href=\"https://wikipedia.org/wiki/Prince_of_Ning_rebellion\" title=\"Prince of Ning rebellion\">Prince of Ning rebellion</a>, and leads his army north in an attempt to capture <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> declares the <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming dynasty</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor\" title=\"Zhengde Emperor\">Zhengde Emperor</a> a usurper, beginning the <a href=\"https://wikipedia.org/wiki/Prince_of_Ning_rebellion\" title=\"Prince of Ning rebellion\">Prince of Ning rebellion</a>, and leads his army north in an attempt to capture <a href=\"https://wikipedia.org/wiki/Nanjing\" title=\"Nanjing\">Nanjing</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>de Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}, {"title": "Prince of Ning rebellion", "link": "https://wikipedia.org/wiki/Prince_of_<PERSON><PERSON>_rebellion"}, {"title": "Nanjing", "link": "https://wikipedia.org/wiki/Nanjing"}]}, {"year": "1553", "text": "Lady <PERSON> takes the throne of England.", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a> takes the throne of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\">Lady <PERSON></a> takes the throne of England.", "links": [{"title": "Lady <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1584", "text": "<PERSON> Orange is assassinated in his home in Delft, Holland, by <PERSON><PERSON><PERSON><PERSON>.", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> I of Orange\"><PERSON> of Orange</a> is assassinated in his home in <a href=\"https://wikipedia.org/wiki/Delft\" title=\"Delft\">Delft</a>, <a href=\"https://wikipedia.org/wiki/Holland\" title=\"Holland\">Holland</a>, by <a href=\"https://wikipedia.org/wiki/Balthasar_G%C3%A9rard\" title=\"<PERSON>lt<PERSON><PERSON>\"><PERSON>lthasa<PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"William I of Orange\"><PERSON> of Orange</a> is assassinated in his home in <a href=\"https://wikipedia.org/wiki/Delft\" title=\"Delft\">Delft</a>, <a href=\"https://wikipedia.org/wiki/Holland\" title=\"Holland\">Holland</a>, by <a href=\"https://wikipedia.org/wiki/Balthasar_G%C3%A9rard\" title=\"<PERSON>lt<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON> Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange"}, {"title": "Delft", "link": "https://wikipedia.org/wiki/Delft"}, {"title": "Holland", "link": "https://wikipedia.org/wiki/Holland"}, {"title": "Balthasar <PERSON>", "link": "https://wikipedia.org/wiki/Balthasar_G%C3%A9rard"}]}, {"year": "1645", "text": "English Civil War: The Battle of Langport takes place.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Langport\" title=\"Battle of Langport\">Battle of Langport</a> takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/English_Civil_War\" title=\"English Civil War\">English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Langport\" title=\"Battle of Langport\">Battle of Langport</a> takes place.", "links": [{"title": "English Civil War", "link": "https://wikipedia.org/wiki/English_Civil_War"}, {"title": "Battle of Langport", "link": "https://wikipedia.org/wiki/Battle_of_Langport"}]}, {"year": "1668", "text": "Anglo-Spanish War (1654-1671): Notable Buccaneer <PERSON> with an English Privateer force lands at Porto Bello in an attempt to capture the fortified and lucrative Spanish city.", "html": "1668 - <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War (1654-1671)</a>: Notable <a href=\"https://wikipedia.org/wiki/Buccaneer\" title=\"Buccaneer\">Buccaneer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> with an English <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">Privateer</a> force lands at <a href=\"https://wikipedia.org/wiki/Portobelo,_Col%C3%B3n\" title=\"Portobelo, Colón\">Porto Bello</a> in an attempt to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_raid_on_Porto_Bello\" title=\"<PERSON>'s raid on Porto Bello\">capture the fortified and lucrative Spanish city</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)\" title=\"Anglo-Spanish War (1654-1660)\">Anglo-Spanish War (1654-1671)</a>: Notable <a href=\"https://wikipedia.org/wiki/Buccaneer\" title=\"Buccaneer\">Buccaneer</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> with an English <a href=\"https://wikipedia.org/wiki/Privateer\" title=\"Privateer\">Privateer</a> force lands at <a href=\"https://wikipedia.org/wiki/Portobelo,_Col%C3%B3n\" title=\"Portobelo, Colón\">Porto Bello</a> in an attempt to <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_raid_on_Porto_Bello\" title=\"<PERSON>'s raid on Porto Bello\">capture the fortified and lucrative Spanish city</a>.", "links": [{"title": "Anglo-Spanish War (1654-1660)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%931660)"}, {"title": "B<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uc<PERSON>er"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Privateer", "link": "https://wikipedia.org/wiki/Privateer"}, {"title": "Portobelo, Colón", "link": "https://wikipedia.org/wiki/Portobelo,_Col%C3%B3n"}, {"title": "<PERSON>'s raid on Porto Bello", "link": "https://wikipedia.org/wiki/<PERSON>_Morgan%27s_raid_on_Porto_Bello"}]}, {"year": "1778", "text": "American Revolution: <PERSON> of <PERSON> declares war on the Kingdom of Great Britain.", "html": "1778 - <a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> of France</a> declares war on the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolution\" title=\"American Revolution\">American Revolution</a>: <a href=\"https://wikipedia.org/wiki/Louis_XVI_of_France\" class=\"mw-redirect\" title=\"Louis XVI of France\"><PERSON> of <PERSON></a> declares war on the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Kingdom of Great Britain</a>.", "links": [{"title": "American Revolution", "link": "https://wikipedia.org/wiki/American_Revolution"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_France"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}]}, {"year": "1789", "text": "<PERSON> reaches the Mackenzie River delta.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> reaches the <a href=\"https://wikipedia.org/wiki/Mackenzie_River\" title=\"Mackenzie River\">Mackenzie River</a> delta.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(explorer)\" title=\"<PERSON> (explorer)\"><PERSON></a> reaches the <a href=\"https://wikipedia.org/wiki/Mackenzie_River\" title=\"Mackenzie River\">Mackenzie River</a> delta.", "links": [{"title": "<PERSON> (explorer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(explorer)"}, {"title": "Mackenzie River", "link": "https://wikipedia.org/wiki/Mackenzie_River"}]}, {"year": "1806", "text": "The Vellore Mutiny is the first instance of a mutiny by Indian sepoys against the British East India Company.", "html": "1806 - The <a href=\"https://wikipedia.org/wiki/Vellore_Mutiny\" title=\"Vellore Mutiny\">Vellore Mutiny</a> is the first instance of a mutiny by Indian <a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">sepoys</a> against the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vellore_Mutiny\" title=\"Vellore Mutiny\">Vellore Mutiny</a> is the first instance of a mutiny by Indian <a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">sepoys</a> against the <a href=\"https://wikipedia.org/wiki/British_East_India_Company\" class=\"mw-redirect\" title=\"British East India Company\">British East India Company</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>re_Mutiny"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oy"}, {"title": "British East India Company", "link": "https://wikipedia.org/wiki/British_East_India_Company"}]}, {"year": "1832", "text": "U.S. President <PERSON> vetoes a bill that would re-charter the Second Bank of the United States.", "html": "1832 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> vetoes a bill that would re-charter the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> vetoes a bill that would re-charter the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Bank of the United States", "link": "https://wikipedia.org/wiki/Second_Bank_of_the_United_States"}]}, {"year": "1850", "text": "U.S. President <PERSON><PERSON> is sworn in, a day after becoming president upon <PERSON>'s death.", "html": "1850 - U.S. President <a href=\"https://wikipedia.org/wiki/Millard_Fillmore\" title=\"Millard Fillmore\"><PERSON><PERSON></a> is sworn in, a day after becoming president upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/Millard_Fillmore\" title=\"Millard Fillmore\"><PERSON><PERSON></a> is sworn in, a day after becoming president upon <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s death.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Millard_Fillmore"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "The then-villa of Mayagüez, Puerto Rico, formally receives its city charter from the Royal Crown of Spain.", "html": "1877 - The then-<a href=\"https://wikipedia.org/wiki/Villa#Post-Roman_era\" title=\"Villa\">villa</a> of <a href=\"https://wikipedia.org/wiki/Mayag%C3%<PERSON><PERSON>,_Puerto_Rico\" title=\"Mayagüez, Puerto Rico\">Mayagüez, Puerto Rico</a>, formally receives its city charter from the Royal Crown of Spain.", "no_year_html": "The then-<a href=\"https://wikipedia.org/wiki/Villa#Post-Roman_era\" title=\"Villa\">villa</a> of <a href=\"https://wikipedia.org/wiki/Mayag%C3%<PERSON><PERSON>,_Puerto_Rico\" title=\"Mayagüez, Puerto Rico\">Mayagüez, Puerto Rico</a>, formally receives its city charter from the Royal Crown of Spain.", "links": [{"title": "Villa", "link": "https://wikipedia.org/wiki/Villa#Post-Roman_era"}, {"title": "Mayagüez, Puerto Rico", "link": "https://wikipedia.org/wiki/Mayag%C3%<PERSON>ez,_Puerto_Rico"}]}, {"year": "1882", "text": "War of the Pacific: Chile suffers its last military defeat in the Battle of La Concepción when a garrison of 77 men is annihilated by a 1,300-strong Peruvian force, many of them armed with spears.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Chile suffers its last military defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_La_Concepci%C3%B3n\" title=\"Battle of La Concepción\">Battle of La Concepción</a> when a garrison of 77 men is annihilated by a 1,300-strong Peruvian force, many of them armed with spears.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Chile suffers its last military defeat in the <a href=\"https://wikipedia.org/wiki/Battle_of_La_Concepci%C3%B3n\" title=\"Battle of La Concepción\">Battle of La Concepción</a> when a garrison of 77 men is annihilated by a 1,300-strong Peruvian force, many of them armed with spears.", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "Battle of La Concepción", "link": "https://wikipedia.org/wiki/Battle_of_La_Concepci%C3%B3n"}]}, {"year": "1883", "text": "War of the Pacific: Chileans led by <PERSON> defeat <PERSON>'s Peruvian army at the Battle of Huamachuco, hastening the end of the war.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Chileans led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>'s Peruvian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Huamachuco\" title=\"Battle of Huamachuco\">Battle of Huamachuco</a>, hastening the end of the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Pacific\" title=\"War of the Pacific\">War of the Pacific</a>: Chileans led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeat <a href=\"https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a>'s Peruvian army at the <a href=\"https://wikipedia.org/wiki/Battle_of_Huamachuco\" title=\"Battle of Huamachuco\">Battle of Huamachuco</a>, hastening the end of the war.", "links": [{"title": "War of the Pacific", "link": "https://wikipedia.org/wiki/War_of_the_Pacific"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9s_Avelino_C%C3%A1ceres"}, {"title": "Battle of Huamachuco", "link": "https://wikipedia.org/wiki/Battle_of_Huamachuco"}]}, {"year": "1890", "text": "Wyoming is admitted as the 44th U.S. state.", "html": "1890 - <a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> is admitted as the 44th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wyoming\" title=\"Wyoming\">Wyoming</a> is admitted as the 44th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Wyoming", "link": "https://wikipedia.org/wiki/Wyoming"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1920", "text": "<PERSON> becomes Prime Minister of Canada.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1921", "text": "Belfast's Bloody Sunday occurs with 20 killings, at least 100 wounded and 200 homes destroyed during rioting and gun battles in Belfast, Northern Ireland.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1921)\" title=\"Bloody Sunday (1921)\">Belfast's Bloody Sunday</a> occurs with 20 killings, at least 100 wounded and 200 homes destroyed during rioting and gun battles in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bloody_Sunday_(1921)\" title=\"Bloody Sunday (1921)\">Belfast's Bloody Sunday</a> occurs with 20 killings, at least 100 wounded and 200 homes destroyed during rioting and gun battles in <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a>.", "links": [{"title": "Bloody Sunday (1921)", "link": "https://wikipedia.org/wiki/Bloody_Sunday_(1921)"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON> wins the 1,500 m and 5,000 m events at the Paris Olympics, with just an hour between the two races.", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> wins the 1,500 m and 5,000 m events at the <a href=\"https://wikipedia.org/wiki/1924_Summer_Olympics\" title=\"1924 Summer Olympics\">Paris Olympics</a>, with just an hour between the two races.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> wins the 1,500 m and 5,000 m events at the <a href=\"https://wikipedia.org/wiki/1924_Summer_Olympics\" title=\"1924 Summer Olympics\">Paris Olympics</a>, with just an hour between the two races.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>av<PERSON>_<PERSON>"}, {"title": "1924 Summer Olympics", "link": "https://wikipedia.org/wiki/1924_Summer_Olympics"}]}, {"year": "1925", "text": "Scopes trial: In Dayton, Tennessee, the so-called \"Monkey Trial\" begins of <PERSON>, a young high school science teacher accused of teaching evolution in violation of the Butler Act.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_trial\" title=\"<PERSON><PERSON><PERSON> trial\"><PERSON><PERSON><PERSON> trial</a>: In <a href=\"https://wikipedia.org/wiki/Dayton,_Tennessee\" title=\"Dayton, Tennessee\">Dayton, Tennessee</a>, the so-called \"Monkey Trial\" begins of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a young high school science teacher accused of teaching <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> in violation of the <a href=\"https://wikipedia.org/wiki/Butler_Act\" title=\"Butler Act\">Butler Act</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_trial\" title=\"<PERSON><PERSON><PERSON> trial\"><PERSON><PERSON><PERSON> trial</a>: In <a href=\"https://wikipedia.org/wiki/Dayton,_Tennessee\" title=\"Dayton, Tennessee\">Dayton, Tennessee</a>, the so-called \"Monkey Trial\" begins of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a young high school science teacher accused of teaching <a href=\"https://wikipedia.org/wiki/Evolution\" title=\"Evolution\">evolution</a> in violation of the <a href=\"https://wikipedia.org/wiki/Butler_Act\" title=\"Butler Act\">Butler Act</a>.", "links": [{"title": "Scopes trial", "link": "https://wikipedia.org/wiki/Sc<PERSON>s_trial"}, {"title": "Dayton, Tennessee", "link": "https://wikipedia.org/wiki/Dayton,_Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Evolution", "link": "https://wikipedia.org/wiki/Evolution"}, {"title": "Butler Act", "link": "https://wikipedia.org/wiki/Butler_Act"}]}, {"year": "1927", "text": "<PERSON>, Vice-President of the Executive Council of the Irish Free State, is assassinated by the IRA.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Higgins\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Teachta_D%C3%A1la\" title=\"Teachta Dála\">TD</a>, Vice-President of the Executive Council of the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>, is assassinated by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Higgins\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Teachta_D%C3%A1la\" title=\"Teachta Dála\">TD</a>, Vice-President of the Executive Council of the <a href=\"https://wikipedia.org/wiki/Irish_Free_State\" title=\"Irish Free State\">Irish Free State</a>, is assassinated by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">IRA</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Higgins"}, {"title": "Teachta Dála", "link": "https://wikipedia.org/wiki/Teachta_D%C3%A1la"}, {"title": "Irish Free State", "link": "https://wikipedia.org/wiki/Irish_Free_State"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}]}, {"year": "1938", "text": "<PERSON> begins a 91-hour airplane flight around the world that will set a new record.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a 91-hour <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> flight around the world that will set a new record.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins a 91-hour <a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">airplane</a> flight around the world that will set a new record.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Airplane", "link": "https://wikipedia.org/wiki/Airplane"}]}, {"year": "1940", "text": "World War II: The Vichy government is established in France.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Vichy_government\" class=\"mw-redirect\" title=\"Vichy government\">Vichy government</a> is established in France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Vichy_government\" class=\"mw-redirect\" title=\"Vichy government\">Vichy government</a> is established in France.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON> government", "link": "https://wikipedia.org/wiki/Vichy_government"}]}, {"year": "1940", "text": "World War II: Six days before <PERSON> issues his Directive 16 to the combined Wehrmacht armed forces for Operation Sea Lion, the Kanalkampf shipping attacks begin against British maritime convoys in the leadup to initiating the Battle of Britain.", "html": "1940 - World War II: Six days before <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> issues his <a href=\"https://wikipedia.org/wiki/List_of_Adolf_Hitler%27s_directives\" title=\"List of Adolf Hitler's directives\">Directive 16</a> to the combined <i><a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a></i> armed forces for <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>, the <i><a href=\"https://wikipedia.org/wiki/Kanalkampf\" title=\"Kanalkampf\">Kanalkampf</a></i> shipping attacks begin against British maritime convoys in the leadup to initiating the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a>.", "no_year_html": "World War II: Six days before <a href=\"https://wikipedia.org/wiki/Adolf_Hitler\" title=\"Adolf Hitler\"><PERSON></a> issues his <a href=\"https://wikipedia.org/wiki/List_of_Adolf_Hitler%27s_directives\" title=\"List of Adolf <PERSON>'s directives\">Directive 16</a> to the combined <i><a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a></i> armed forces for <a href=\"https://wikipedia.org/wiki/Operation_Sea_Lion\" title=\"Operation Sea Lion\">Operation Sea Lion</a>, the <i><a href=\"https://wikipedia.org/wiki/Kanalkampf\" title=\"Kanalkampf\">Kanalkampf</a></i> shipping attacks begin against British maritime convoys in the leadup to initiating the <a href=\"https://wikipedia.org/wiki/Battle_of_Britain\" title=\"Battle of Britain\">Battle of Britain</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of <PERSON>'s directives", "link": "https://wikipedia.org/wiki/List_of_Adolf_Hitler%27s_directives"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}, {"title": "Operation Sea Lion", "link": "https://wikipedia.org/wiki/Operation_Sea_Lion"}, {"title": "Kanalkampf", "link": "https://wikipedia.org/wiki/Kanalkampf"}, {"title": "Battle of Britain", "link": "https://wikipedia.org/wiki/Battle_of_Britain"}]}, {"year": "1941", "text": "Jedwabne pogrom: Massacre of Polish Jews living in and near the village of Jedwabne.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Jed<PERSON>ne_pogrom\" title=\"Jedwabne pogrom\">Jedwabne pogrom</a>: Massacre of Polish Jews living in and near the village of <a href=\"https://wikipedia.org/wiki/Jed<PERSON><PERSON>\" title=\"Jedwab<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jed<PERSON>ne_pogrom\" title=\"Jedwabne pogrom\">Jedwabne pogrom</a>: Massacre of Polish Jews living in and near the village of <a href=\"https://wikipedia.org/wiki/Jed<PERSON><PERSON>\" title=\"Jedwabne\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON> pogrom", "link": "https://wikipedia.org/wiki/Jedwabne_pogrom"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jed<PERSON>ne"}]}, {"year": "1942", "text": "World War II: An American pilot spots a downed, intact Mitsubishi A6M Zero on Akutan Island (the \"Akutan Zero\"), which the US Navy then uses to learn the aircraft's flight characteristics.", "html": "1942 - World War II: An American pilot spots a downed, intact <a href=\"https://wikipedia.org/wiki/Mitsubishi_A6M_Zero\" title=\"Mitsubishi A6M Zero\">Mitsubishi A6M Zero</a> on <a href=\"https://wikipedia.org/wiki/Akutan_Island\" title=\"Akutan Island\">Akutan Island</a> (the \"<a href=\"https://wikipedia.org/wiki/Akutan_Zero\" title=\"Akutan Zero\">Akutan Zero</a>\"), which the US Navy then uses to learn the aircraft's flight characteristics.", "no_year_html": "World War II: An American pilot spots a downed, intact <a href=\"https://wikipedia.org/wiki/Mitsubishi_A6M_Zero\" title=\"Mitsubishi A6M Zero\">Mitsubishi A6M Zero</a> on <a href=\"https://wikipedia.org/wiki/Akutan_Island\" title=\"Akutan Island\">Akutan Island</a> (the \"<a href=\"https://wikipedia.org/wiki/Akutan_Zero\" title=\"Akutan Zero\">Akutan Zero</a>\"), which the US Navy then uses to learn the aircraft's flight characteristics.", "links": [{"title": "Mitsubishi A6M Zero", "link": "https://wikipedia.org/wiki/Mitsubishi_A6M_Zero"}, {"title": "Akutan Island", "link": "https://wikipedia.org/wiki/Akutan_Island"}, {"title": "Akutan Zero", "link": "https://wikipedia.org/wiki/Akutan_Zero"}]}, {"year": "1943", "text": "World War II: Operation Husky begins in Sicily.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/Operation_Husky\" class=\"mw-redirect\" title=\"Operation Husky\">Operation Husky</a> begins in Sicily.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Operation_Husky\" class=\"mw-redirect\" title=\"Operation Husky\">Operation Husky</a> begins in Sicily.", "links": [{"title": "Operation Husky", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON> is recommended as the first Governor-General of Pakistan by the British Prime Minister, <PERSON>.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is recommended as the first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> by the British Prime Minister, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is recommended as the first <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> by the British Prime Minister, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Pakistan", "link": "https://wikipedia.org/wiki/Governor-General_of_Pakistan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "Korean War: Armistice negotiations begin at Kaesong.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Armistice negotiations begin at <a href=\"https://wikipedia.org/wiki/Kaesong\" title=\"Kaesong\">Kaesong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Armistice negotiations begin at <a href=\"https://wikipedia.org/wiki/Kaesong\" title=\"Kaesong\">Kaesong</a>.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kaesong"}]}, {"year": "1962", "text": "Telstar, the world's first communications satellite, is launched into orbit.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Telstar_1\" title=\"Telstar 1\">Telstar</a>, the world's first <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellite</a>, is launched into orbit.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Telstar_1\" title=\"Telstar 1\">Telstar</a>, the world's first <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communications satellite</a>, is launched into orbit.", "links": [{"title": "Telstar 1", "link": "https://wikipedia.org/wiki/Telstar_1"}, {"title": "Communications satellite", "link": "https://wikipedia.org/wiki/Communications_satellite"}]}, {"year": "1966", "text": "The Chicago Freedom Movement, co-founded by <PERSON>, holds a rally at Soldier Field in Chicago; as many as 60,000 people attend.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Chicago_Freedom_Movement\" title=\"Chicago Freedom Movement\">Chicago Freedom Movement</a>, co-founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, holds a rally at <a href=\"https://wikipedia.org/wiki/Soldier_Field\" title=\"Soldier Field\">Soldier Field</a> in Chicago; as many as 60,000 people attend.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chicago_Freedom_Movement\" title=\"Chicago Freedom Movement\">Chicago Freedom Movement</a>, co-founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, holds a rally at <a href=\"https://wikipedia.org/wiki/Soldier_Field\" title=\"Soldier Field\">Soldier Field</a> in Chicago; as many as 60,000 people attend.", "links": [{"title": "Chicago Freedom Movement", "link": "https://wikipedia.org/wiki/Chicago_Freedom_Movement"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Soldier Field", "link": "https://wikipedia.org/wiki/Soldier_Field"}]}, {"year": "1973", "text": "The Bahamas gains full independence within the Commonwealth of Nations.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/The_Bahamas\" title=\"The Bahamas\">The Bahamas</a> gains full independence within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Bahamas\" title=\"The Bahamas\">The Bahamas</a> gains full independence within the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_Nations\" title=\"Commonwealth of Nations\">Commonwealth of Nations</a>.", "links": [{"title": "The Bahamas", "link": "https://wikipedia.org/wiki/The_Bahamas"}, {"title": "Commonwealth of Nations", "link": "https://wikipedia.org/wiki/Commonwealth_of_Nations"}]}, {"year": "1974", "text": "An EgyptAir Tupolev Tu-154 stalls and crashes at Cairo International Airport, killing all six people on board.", "html": "1974 - An <a href=\"https://wikipedia.org/wiki/1974_EgyptAir_Tupolev_Tu-154_crash\" title=\"1974 EgyptAir Tupolev Tu-154 crash\">EgyptAir Tupolev Tu-154 stalls and crashes</a> at <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a>, killing all six people on board.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1974_EgyptAir_Tupolev_Tu-154_crash\" title=\"1974 EgyptAir Tupolev Tu-154 crash\">EgyptAir Tupolev Tu-154 stalls and crashes</a> at <a href=\"https://wikipedia.org/wiki/Cairo_International_Airport\" title=\"Cairo International Airport\">Cairo International Airport</a>, killing all six people on board.", "links": [{"title": "1974 EgyptAir Tupolev Tu-154 crash", "link": "https://wikipedia.org/wiki/1974_EgyptAir_Tupolev_Tu-154_crash"}, {"title": "Cairo International Airport", "link": "https://wikipedia.org/wiki/Cairo_International_Airport"}]}, {"year": "1976", "text": "Four mercenaries (one American and three British) are executed in Angola following the Luanda Trial.", "html": "1976 - Four mercenaries (one American and three British) are executed in <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a> following the <a href=\"https://wikipedia.org/wiki/Luanda_Trial\" title=\"Luanda Trial\">Luanda Trial</a>.", "no_year_html": "Four mercenaries (one American and three British) are executed in <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a> following the <a href=\"https://wikipedia.org/wiki/Luanda_Trial\" title=\"Luanda Trial\">Luanda Trial</a>.", "links": [{"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Trial"}]}, {"year": "1978", "text": "President <PERSON><PERSON><PERSON> of Mauritania is ousted in a bloodless coup d'état.", "html": "1978 - <a href=\"https://wikipedia.org/wiki/1978\" title=\"1978\">1978</a> - President <a href=\"https://wikipedia.org/wiki/Moktar_<PERSON>uld_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Mauritania\" title=\"Mauritania\">Mauritania</a> is ousted in a bloodless <a href=\"https://wikipedia.org/wiki/1978_Mauritanian_coup_d%27%C3%A9tat\" title=\"1978 Mauritanian coup d'état\">coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1978\" title=\"1978\">1978</a> - President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uld_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Mauritania\" title=\"Mauritania\">Mauritania</a> is ousted in a bloodless <a href=\"https://wikipedia.org/wiki/1978_Mauritanian_coup_d%27%C3%A9tat\" title=\"1978 Mauritanian coup d'état\">coup d'état</a>.", "links": [{"title": "1978", "link": "https://wikipedia.org/wiki/1978"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Mauritania", "link": "https://wikipedia.org/wiki/Mauritania"}, {"title": "1978 Mauritanian coup d'état", "link": "https://wikipedia.org/wiki/1978_Mauritanian_coup_d%27%C3%A9tat"}]}, {"year": "1985", "text": "The Greenpeace vessel Rainbow Warrior is bombed and sunk in Auckland harbour by French DGSE agents, killing <PERSON>.", "html": "1985 - The <a href=\"https://wikipedia.org/wiki/Greenpeace\" title=\"Greenpeace\">Greenpeace</a> vessel <i><a href=\"https://wikipedia.org/wiki/Rainbow_Warrior_(1978)\" class=\"mw-redirect\" title=\"Rainbow Warrior (1978)\">Rainbow Warrior</a></i> is <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Rainbow_Warrior\" title=\"Sinking of the Rainbow Warrior\">bombed and sunk</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a> harbour by French <a href=\"https://wikipedia.org/wiki/DGSE\" class=\"mw-redirect\" title=\"DGSE\">DGSE</a> agents, killing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Greenpeace\" title=\"Greenpeace\">Greenpeace</a> vessel <i><a href=\"https://wikipedia.org/wiki/Rainbow_Warrior_(1978)\" class=\"mw-redirect\" title=\"Rainbow Warrior (1978)\">Rainbow Warrior</a></i> is <a href=\"https://wikipedia.org/wiki/Sinking_of_the_Rainbow_Warrior\" title=\"Sinking of the Rainbow Warrior\">bombed and sunk</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a> harbour by French <a href=\"https://wikipedia.org/wiki/DGSE\" class=\"mw-redirect\" title=\"DGSE\">DGSE</a> agents, killing <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Greenpeace", "link": "https://wikipedia.org/wiki/Greenpeace"}, {"title": "Rainbow Warrior (1978)", "link": "https://wikipedia.org/wiki/Rainbow_Warrior_(1978)"}, {"title": "Sinking of the Rainbow Warrior", "link": "https://wikipedia.org/wiki/Sinking_of_the_Rainbow_Warrior"}, {"title": "Auckland", "link": "https://wikipedia.org/wiki/Auckland"}, {"title": "DGSE", "link": "https://wikipedia.org/wiki/DGSE"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "An Aeroflot Tupolev Tu-154 stalls and crashes near Uchkuduk, Uzbekistan (then part of the Soviet Union), killing all 200 people on board in the USSR's worst-ever airline disaster.", "html": "1985 - An <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_5143\" title=\"Aeroflot Flight 5143\">Aeroflot Tupolev Tu-154 stalls and crashes</a> near <a href=\"https://wikipedia.org/wiki/Uchkuduk\" class=\"mw-redirect\" title=\"Uchkuduk\">Uchkuduk</a>, <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a> (then part of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>), killing all 200 people on board in the USSR's worst-ever airline disaster.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_5143\" title=\"Aeroflot Flight 5143\">Aeroflot Tupolev Tu-154 stalls and crashes</a> near <a href=\"https://wikipedia.org/wiki/Uchkuduk\" class=\"mw-redirect\" title=\"Uchkuduk\">Uch<PERSON>duk</a>, <a href=\"https://wikipedia.org/wiki/Uzbekistan\" title=\"Uzbekistan\">Uzbekistan</a> (then part of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>), killing all 200 people on board in the USSR's worst-ever airline disaster.", "links": [{"title": "Aeroflot Flight 5143", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_5143"}, {"title": "Uchkuduk", "link": "https://wikipedia.org/wiki/Uchkuduk"}, {"title": "Uzbekistan", "link": "https://wikipedia.org/wiki/Uzbekistan"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1991", "text": "The South African cricket team is readmitted into the International Cricket Council following the end of Apartheid.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/South_African_cricket_team\" class=\"mw-redirect\" title=\"South African cricket team\">South African cricket team</a> is readmitted into the <a href=\"https://wikipedia.org/wiki/International_Cricket_Council\" title=\"International Cricket Council\">International Cricket Council</a> following the end of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_African_cricket_team\" class=\"mw-redirect\" title=\"South African cricket team\">South African cricket team</a> is readmitted into the <a href=\"https://wikipedia.org/wiki/International_Cricket_Council\" title=\"International Cricket Council\">International Cricket Council</a> following the end of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">Apartheid</a>.", "links": [{"title": "South African cricket team", "link": "https://wikipedia.org/wiki/South_African_cricket_team"}, {"title": "International Cricket Council", "link": "https://wikipedia.org/wiki/International_Cricket_Council"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}]}, {"year": "1991", "text": "<PERSON> takes office as the first elected President of Russia.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes office as the first elected <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> takes office as the first elected <a href=\"https://wikipedia.org/wiki/President_of_Russia\" title=\"President of Russia\">President of Russia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Russia", "link": "https://wikipedia.org/wiki/President_of_Russia"}]}, {"year": "1991", "text": "A Beechcraft Model 99 crashes near Birmingham Municipal Airport (now Birmingham-Shuttlesworth International Airport) in Birmingham, Alabama, killing 13 of the 15 people on board.", "html": "1991 - A <a href=\"https://wikipedia.org/wiki/L%27Express_Airlines_Flight_508\" title=\"L'Express Airlines Flight 508\">Beechcraft Model 99 crashes</a> near Birmingham Municipal Airport (now <a href=\"https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport\" title=\"Birmingham-Shuttlesworth International Airport\">Birmingham-Shuttlesworth International Airport</a>) in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, killing 13 of the 15 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/L%27Express_Airlines_Flight_508\" title=\"L'Express Airlines Flight 508\">Beechcraft Model 99 crashes</a> near Birmingham Municipal Airport (now <a href=\"https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport\" title=\"Birmingham-Shuttlesworth International Airport\">Birmingham-Shuttlesworth International Airport</a>) in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, killing 13 of the 15 people on board.", "links": [{"title": "L'Express Airlines Flight 508", "link": "https://wikipedia.org/wiki/L%27Express_Airlines_Flight_508"}, {"title": "Birmingham-Shuttlesworth International Airport", "link": "https://wikipedia.org/wiki/Birmingham%E2%80%93Shuttlesworth_International_Airport"}, {"title": "Birmingham, Alabama", "link": "https://wikipedia.org/wiki/Birmingham,_Alabama"}]}, {"year": "1992", "text": "In Miami, former Panamanian leader <PERSON> is sentenced to 40 years in prison for drug and racketeering violations.", "html": "1992 - In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, former <a href=\"https://wikipedia.org/wiki/Panamanian\" class=\"mw-redirect\" title=\"Panamanian\">Panamanian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 40 years in prison for <a href=\"https://wikipedia.org/wiki/Recreational_drug_use\" title=\"Recreational drug use\">drug</a> and <a href=\"https://wikipedia.org/wiki/Racketeering\" title=\"Racketeering\">racketeering</a> violations.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Miami\" title=\"Miami\">Miami</a>, former <a href=\"https://wikipedia.org/wiki/Panamanian\" class=\"mw-redirect\" title=\"Panamanian\">Panamanian</a> leader <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to 40 years in prison for <a href=\"https://wikipedia.org/wiki/Recreational_drug_use\" title=\"Recreational drug use\">drug</a> and <a href=\"https://wikipedia.org/wiki/Racketeering\" title=\"Racketeering\">racketeering</a> violations.", "links": [{"title": "Miami", "link": "https://wikipedia.org/wiki/Miami"}, {"title": "Panamanian", "link": "https://wikipedia.org/wiki/Panamanian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Recreational drug use", "link": "https://wikipedia.org/wiki/Recreational_drug_use"}, {"title": "Racketeering", "link": "https://wikipedia.org/wiki/Racketeering"}]}, {"year": "1995", "text": "Burmese politician <PERSON><PERSON> is released from house arrest.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burmese</a> politician <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> is released from house arrest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Burmese</a> politician <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Suu Kyi</a> is released from house arrest.", "links": [{"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "1995", "text": "The NIOSH air filtration ratings update with the enactment of 42 CFR 84, previously published in the Federal Register. The new regulation includes rules governing the new N95 respirator standard.", "html": "1995 - The <a href=\"https://wikipedia.org/wiki/NIOSH_air_filtration_rating\" title=\"NIOSH air filtration rating\">NIOSH air filtration ratings</a> update with the enactment of 42 CFR 84, previously published in the <a href=\"https://wikipedia.org/wiki/Federal_Register\" title=\"Federal Register\">Federal Register</a>. The new regulation includes rules governing the new <a href=\"https://wikipedia.org/wiki/N95_respirator\" title=\"N95 respirator\">N95 respirator</a> standard.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/NIOSH_air_filtration_rating\" title=\"NIOSH air filtration rating\">NIOSH air filtration ratings</a> update with the enactment of 42 CFR 84, previously published in the <a href=\"https://wikipedia.org/wiki/Federal_Register\" title=\"Federal Register\">Federal Register</a>. The new regulation includes rules governing the new <a href=\"https://wikipedia.org/wiki/N95_respirator\" title=\"N95 respirator\">N95 respirator</a> standard.", "links": [{"title": "NIOSH air filtration rating", "link": "https://wikipedia.org/wiki/NIOSH_air_filtration_rating"}, {"title": "Federal Register", "link": "https://wikipedia.org/wiki/Federal_Register"}, {"title": "N95 respirator", "link": "https://wikipedia.org/wiki/N95_respirator"}]}, {"year": "1997", "text": "In London, scientists report the findings of the DNA analysis of a Neanderthal skeleton which supports the \"out of Africa theory\" of human evolution, placing an \"African Eve\" at 100,000 to 200,000 years ago.", "html": "1997 - In London, scientists report the findings of the <a href=\"https://wikipedia.org/wiki/DNA\" title=\"DNA\">DNA</a> analysis of a <a href=\"https://wikipedia.org/wiki/Neanderthal\" title=\"Neanderthal\">Neanderthal</a> skeleton which supports the \"<a href=\"https://wikipedia.org/wiki/Out_of_Africa_theory\" class=\"mw-redirect\" title=\"Out of Africa theory\">out of Africa theory</a>\" of <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a>, placing an \"<a href=\"https://wikipedia.org/wiki/African_Eve\" class=\"mw-redirect\" title=\"African Eve\">African Eve</a>\" at 100,000 to 200,000 years ago.", "no_year_html": "In London, scientists report the findings of the <a href=\"https://wikipedia.org/wiki/DNA\" title=\"DNA\">DNA</a> analysis of a <a href=\"https://wikipedia.org/wiki/Neanderthal\" title=\"Neanderthal\">Neanderthal</a> skeleton which supports the \"<a href=\"https://wikipedia.org/wiki/Out_of_Africa_theory\" class=\"mw-redirect\" title=\"Out of Africa theory\">out of Africa theory</a>\" of <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a>, placing an \"<a href=\"https://wikipedia.org/wiki/African_Eve\" class=\"mw-redirect\" title=\"African Eve\">African Eve</a>\" at 100,000 to 200,000 years ago.", "links": [{"title": "DNA", "link": "https://wikipedia.org/wiki/DNA"}, {"title": "<PERSON><PERSON><PERSON>thal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Out of Africa theory", "link": "https://wikipedia.org/wiki/Out_of_Africa_theory"}, {"title": "Human evolution", "link": "https://wikipedia.org/wiki/Human_evolution"}, {"title": "African Eve", "link": "https://wikipedia.org/wiki/African_Eve"}]}, {"year": "1997", "text": "<PERSON>, a member of Partido Popular (Spain), is kidnapped (and later murdered) in the Basque city of Ermua by ETA members, sparking widespread protests.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a member of <a href=\"https://wikipedia.org/wiki/People%27s_Party_(Spain)\" title=\"People's Party (Spain)\">Partido Popular</a> (Spain), is kidnapped (and later murdered) in the Basque city of <a href=\"https://wikipedia.org/wiki/Ermua\" title=\"Ermua\">Ermua</a> by <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> members, sparking widespread protests.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81nge<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a member of <a href=\"https://wikipedia.org/wiki/People%27s_Party_(Spain)\" title=\"People's Party (Spain)\">Partido Popular</a> (Spain), is kidnapped (and later murdered) in the Basque city of <a href=\"https://wikipedia.org/wiki/Ermua\" title=\"Ermua\">Ermua</a> by <a href=\"https://wikipedia.org/wiki/ETA_(separatist_group)\" title=\"ETA (separatist group)\">ETA</a> members, sparking widespread protests.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81ngel_<PERSON>"}, {"title": "People's Party (Spain)", "link": "https://wikipedia.org/wiki/People%27s_Party_(Spain)"}, {"title": "E<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ermua"}, {"title": "ETA (separatist group)", "link": "https://wikipedia.org/wiki/ETA_(separatist_group)"}]}, {"year": "1998", "text": "Catholic Church sexual abuse cases: The Diocese of Dallas agrees to pay $23.4 million to nine former altar boys who claim they were sexually abused by <PERSON>, a former priest.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Catholic_Church_sexual_abuse_cases\" title=\"Catholic Church sexual abuse cases\">Catholic Church sexual abuse cases</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Dallas\" title=\"Roman Catholic Diocese of Dallas\">Diocese of Dallas</a> agrees to pay $23.4 million to nine former altar boys who claim they were <a href=\"https://wikipedia.org/wiki/Child_sexual_abuse\" title=\"Child sexual abuse\">sexually abused</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former priest.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catholic_Church_sexual_abuse_cases\" title=\"Catholic Church sexual abuse cases\">Catholic Church sexual abuse cases</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Dallas\" title=\"Roman Catholic Diocese of Dallas\">Diocese of Dallas</a> agrees to pay $23.4 million to nine former altar boys who claim they were <a href=\"https://wikipedia.org/wiki/Child_sexual_abuse\" title=\"Child sexual abuse\">sexually abused</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a former priest.", "links": [{"title": "Catholic Church sexual abuse cases", "link": "https://wikipedia.org/wiki/Catholic_Church_sexual_abuse_cases"}, {"title": "Roman Catholic Diocese of Dallas", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Dallas"}, {"title": "Child sexual abuse", "link": "https://wikipedia.org/wiki/Child_sexual_abuse"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "In women's association football, the United States defeats China in a penalty shoot-out at the Rose Bowl near Los Angeles to win the final match of the 1999 FIFA Women's World Cup. Watched by 90,185 spectators, the final sets a new world record for attendance at a women's sporting event.", "html": "1999 - In women's <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a>, the <a href=\"https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team\" title=\"United States women's national soccer team\">United States</a> defeats <a href=\"https://wikipedia.org/wiki/China_women%27s_national_football_team\" title=\"China women's national football team\">China</a> in a <a href=\"https://wikipedia.org/wiki/Penalty_shoot-out_(association_football)\" title=\"Penalty shoot-out (association football)\">penalty shoot-out</a> at the <a href=\"https://wikipedia.org/wiki/Rose_Bowl_(stadium)\" title=\"Rose Bowl (stadium)\">Rose Bowl</a> near <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a> to win the <a href=\"https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup_Final\" class=\"mw-redirect\" title=\"1999 FIFA Women's World Cup Final\">final match</a> of the <a href=\"https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup\" title=\"1999 FIFA Women's World Cup\">1999 FIFA Women's World Cup</a>. Watched by 90,185 spectators, the final sets a new world record for attendance at a women's sporting event.", "no_year_html": "In women's <a href=\"https://wikipedia.org/wiki/Association_football\" title=\"Association football\">association football</a>, the <a href=\"https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team\" title=\"United States women's national soccer team\">United States</a> defeats <a href=\"https://wikipedia.org/wiki/China_women%27s_national_football_team\" title=\"China women's national football team\">China</a> in a <a href=\"https://wikipedia.org/wiki/Penalty_shoot-out_(association_football)\" title=\"Penalty shoot-out (association football)\">penalty shoot-out</a> at the <a href=\"https://wikipedia.org/wiki/Rose_Bowl_(stadium)\" title=\"Rose Bowl (stadium)\">Rose Bowl</a> near <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a> to win the <a href=\"https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup_Final\" class=\"mw-redirect\" title=\"1999 FIFA Women's World Cup Final\">final match</a> of the <a href=\"https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup\" title=\"1999 FIFA Women's World Cup\">1999 FIFA Women's World Cup</a>. Watched by 90,185 spectators, the final sets a new world record for attendance at a women's sporting event.", "links": [{"title": "Association football", "link": "https://wikipedia.org/wiki/Association_football"}, {"title": "United States women's national soccer team", "link": "https://wikipedia.org/wiki/United_States_women%27s_national_soccer_team"}, {"title": "China women's national football team", "link": "https://wikipedia.org/wiki/China_women%27s_national_football_team"}, {"title": "Penalty shoot-out (association football)", "link": "https://wikipedia.org/wiki/Penalty_shoot-out_(association_football)"}, {"title": "Rose Bowl (stadium)", "link": "https://wikipedia.org/wiki/Rose_Bowl_(stadium)"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}, {"title": "1999 FIFA Women's World Cup Final", "link": "https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup_Final"}, {"title": "1999 FIFA Women's World Cup", "link": "https://wikipedia.org/wiki/1999_FIFA_Women%27s_World_Cup"}]}, {"year": "2000", "text": "EADS, the world's second-largest aerospace group is formed by the merger of Aérospatiale-Matra, DASA, and CASA.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/EADS\" class=\"mw-redirect\" title=\"EADS\">EADS</a>, the world's second-largest aerospace group is formed by the merger of <a href=\"https://wikipedia.org/wiki/A%C3%A9rospatiale-Matra\" class=\"mw-redirect\" title=\"Aérospatiale-Matra\">Aérospatiale-Matra</a>, <a href=\"https://wikipedia.org/wiki/DaimlerChrysler_Aerospace\" class=\"mw-redirect\" title=\"DaimlerChrysler Aerospace\">DASA</a>, and <a href=\"https://wikipedia.org/wiki/Construcciones_Aeron%C3%A1uticas_SA\" class=\"mw-redirect\" title=\"Construcciones Aeronáuticas SA\">CASA</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/EADS\" class=\"mw-redirect\" title=\"EADS\">EADS</a>, the world's second-largest aerospace group is formed by the merger of <a href=\"https://wikipedia.org/wiki/A%C3%A9rospatiale-Matra\" class=\"mw-redirect\" title=\"Aérospatiale-Matra\">Aérospatiale-Matra</a>, <a href=\"https://wikipedia.org/wiki/DaimlerChrysler_Aerospace\" class=\"mw-redirect\" title=\"DaimlerChrysler Aerospace\">DASA</a>, and <a href=\"https://wikipedia.org/wiki/Construcciones_Aeron%C3%A1uticas_SA\" class=\"mw-redirect\" title=\"Construcciones Aeronáuticas SA\">CASA</a>.", "links": [{"title": "EADS", "link": "https://wikipedia.org/wiki/EADS"}, {"title": "Aérospatiale-Matra", "link": "https://wikipedia.org/wiki/A%C3%A9rospatiale-Matra"}, {"title": "DaimlerChrysler Aerospace", "link": "https://wikipedia.org/wiki/DaimlerChrysler_Aerospace"}, {"title": "Construcciones Aeronáuticas SA", "link": "https://wikipedia.org/wiki/Construcciones_Aeron%C3%A1uticas_SA"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON> succeeds his father <PERSON><PERSON><PERSON> as President of Syria.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> succeeds his father <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/President_of_Syria\" title=\"President of Syria\">President of Syria</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "President of Syria", "link": "https://wikipedia.org/wiki/President_of_Syria"}]}, {"year": "2002", "text": "The Massacre of the Innocents, a painting by <PERSON>, is sold at a Sotheby's auction for £49.5 million (US$76.2 million) to <PERSON>.", "html": "2002 - <i><a href=\"https://wikipedia.org/wiki/Massacre_of_the_Innocents_(<PERSON><PERSON><PERSON>)\" title=\"Massacre of the Innocents (<PERSON><PERSON><PERSON>)\">The Massacre of the Innocents</a></i>, a painting by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Peter <PERSON>\"><PERSON></a>, is sold at a <a href=\"https://wikipedia.org/wiki/Sotheby%27s\" title=\"Sotheby's\">Sotheby's</a> auction for £49.5 million (US$76.2 million) to <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_<PERSON>_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\">Lord <PERSON></a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Massacre_of_the_Innocents_(<PERSON><PERSON><PERSON>)\" title=\"Massacre of the Innocents (<PERSON><PERSON><PERSON>)\">The Massacre of the Innocents</a></i>, a painting by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Peter <PERSON>\"><PERSON></a>, is sold at a <a href=\"https://wikipedia.org/wiki/Sotheby%27s\" title=\"Sothe<PERSON>'s\">Sotheby's</a> auction for £49.5 million (US$76.2 million) to <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_<PERSON>_Fleet\" title=\"<PERSON>, 2nd Baron <PERSON> of Fleet\">Lord <PERSON></a>.", "links": [{"title": "Massacre of the Innocents (<PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/Massacre_of_the_Innocents_(<PERSON><PERSON><PERSON>)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sotheby's", "link": "https://wikipedia.org/wiki/Sotheby%27s"}, {"title": "<PERSON>, 2nd Baron <PERSON> of Fleet", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>_of_Fleet"}]}, {"year": "2006", "text": "A Pakistan International Airlines Fokker F27 Friendship crashes near Multan International Airport, killing all 45 people on board.", "html": "2006 - A <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_688\" title=\"Pakistan International Airlines Flight 688\">Pakistan International Airlines Fokker F27 Friendship crashes</a> near <a href=\"https://wikipedia.org/wiki/Multan_International_Airport\" title=\"Multan International Airport\">Multan International Airport</a>, killing all 45 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_688\" title=\"Pakistan International Airlines Flight 688\">Pakistan International Airlines Fokker F27 Friendship crashes</a> near <a href=\"https://wikipedia.org/wiki/Multan_International_Airport\" title=\"Multan International Airport\">Multan International Airport</a>, killing all 45 people on board.", "links": [{"title": "Pakistan International Airlines Flight 688", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_688"}, {"title": "Multan International Airport", "link": "https://wikipedia.org/wiki/Multan_International_Airport"}]}, {"year": "2007", "text": "<PERSON><PERSON> begins the first solo human-powered circumnavigation of the world.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>rden_Eru%C3%A7\" title=\"<PERSON><PERSON>ç\"><PERSON><PERSON></a> begins the first solo human-powered <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigation</a> of the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rden_Eru%C3%A7\" title=\"<PERSON><PERSON>ruç\"><PERSON><PERSON></a> begins the first solo human-powered <a href=\"https://wikipedia.org/wiki/Circumnavigation\" title=\"Circumnavigation\">circumnavigation</a> of the world.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erden_Eru%C3%A7"}, {"title": "Circumnavigation", "link": "https://wikipedia.org/wiki/Circumnavigation"}]}, {"year": "2008", "text": "Former Macedonian Interior Minister <PERSON><PERSON><PERSON> is acquitted of all war-crimes charges by a United Nations tribunal.", "html": "2008 - Former Macedonian Interior Minister <a href=\"https://wikipedia.org/wiki/Ljube_Bo%C5%A1koski\" title=\"Ljube Boškoski\"><PERSON><PERSON><PERSON></a> is acquitted of all war-crimes charges by a <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> tribunal.", "no_year_html": "Former Macedonian Interior Minister <a href=\"https://wikipedia.org/wiki/Ljube_Bo%C5%A1koski\" title=\"Ljube Boškoski\"><PERSON><PERSON><PERSON></a> is acquitted of all war-crimes charges by a <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> tribunal.", "links": [{"title": "Ljube Boškoski", "link": "https://wikipedia.org/wiki/Ljube_Bo%C5%A1koski"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "2011", "text": "Russian cruise ship Bulgaria sinks in the Volga River near Syukeyevo, Tatarstan, causing 122 deaths.", "html": "2011 - Russian cruise ship <i><a href=\"https://wikipedia.org/wiki/Bulgaria_(ship)\" title=\"Bulgaria (ship)\">Bulgaria</a></i> sinks in the <a href=\"https://wikipedia.org/wiki/Volga\" title=\"Volga\">Volga</a> River near <a href=\"https://wikipedia.org/wiki/Syukeyevo\" title=\"Syukeyevo\">Syukeyevo</a>, <a href=\"https://wikipedia.org/wiki/Tatarstan\" title=\"Tatarstan\">Tatarstan</a>, causing 122 deaths.", "no_year_html": "Russian cruise ship <i><a href=\"https://wikipedia.org/wiki/Bulgaria_(ship)\" title=\"Bulgaria (ship)\">Bulgaria</a></i> sinks in the <a href=\"https://wikipedia.org/wiki/Volga\" title=\"Volga\">Volga</a> River near <a href=\"https://wikipedia.org/wiki/Syukeyevo\" title=\"Syukeyevo\">Syukeyevo</a>, <a href=\"https://wikipedia.org/wiki/Tatarstan\" title=\"Tatarstan\">Tatarstan</a>, causing 122 deaths.", "links": [{"title": "Bulgaria (ship)", "link": "https://wikipedia.org/wiki/Bulgaria_(ship)"}, {"title": "Volga", "link": "https://wikipedia.org/wiki/Volga"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Tatarstan", "link": "https://wikipedia.org/wiki/Tatarstan"}]}, {"year": "2011", "text": "Amid widespread backlash to revelations of phone hacking, the British weekly tabloid newspaper News of the World publishes its final issue and shuts down after nearly 168 years in print.", "html": "2011 - Amid widespread backlash to revelations of <a href=\"https://wikipedia.org/wiki/News_International_phone_hacking_scandal\" title=\"News International phone hacking scandal\">phone hacking</a>, the British weekly <a href=\"https://wikipedia.org/wiki/Tabloid_(newspaper_format)\" title=\"Tabloid (newspaper format)\">tabloid</a> newspaper <i><a href=\"https://wikipedia.org/wiki/News_of_the_World\" title=\"News of the World\">News of the World</a></i> publishes its final issue and shuts down after nearly 168 years in print.", "no_year_html": "Amid widespread backlash to revelations of <a href=\"https://wikipedia.org/wiki/News_International_phone_hacking_scandal\" title=\"News International phone hacking scandal\">phone hacking</a>, the British weekly <a href=\"https://wikipedia.org/wiki/Tabloid_(newspaper_format)\" title=\"Tabloid (newspaper format)\">tabloid</a> newspaper <i><a href=\"https://wikipedia.org/wiki/News_of_the_World\" title=\"News of the World\">News of the World</a></i> publishes its final issue and shuts down after nearly 168 years in print.", "links": [{"title": "News International phone hacking scandal", "link": "https://wikipedia.org/wiki/News_International_phone_hacking_scandal"}, {"title": "Tabloid (newspaper format)", "link": "https://wikipedia.org/wiki/Tabloid_(newspaper_format)"}, {"title": "News of the World", "link": "https://wikipedia.org/wiki/News_of_the_World"}]}, {"year": "2012", "text": "The Episcopal Church USA allows same-sex marriage.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Episcopal_Church_USA\" class=\"mw-redirect\" title=\"Episcopal Church USA\">Episcopal Church USA</a> allows <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Episcopal_Church_USA\" class=\"mw-redirect\" title=\"Episcopal Church USA\">Episcopal Church USA</a> allows <a href=\"https://wikipedia.org/wiki/Same-sex_marriage\" title=\"Same-sex marriage\">same-sex marriage</a>.", "links": [{"title": "Episcopal Church USA", "link": "https://wikipedia.org/wiki/Episcopal_Church_USA"}, {"title": "Same-sex marriage", "link": "https://wikipedia.org/wiki/Same-sex_marriage"}]}, {"year": "2016", "text": "Portugal defeats France in the UEFA Euro 2016 Final to win their first European title.", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Portugal_National_Football_Team\" class=\"mw-redirect\" title=\"Portugal National Football Team\">Portugal</a> defeats <a href=\"https://wikipedia.org/wiki/France_National_Football_Team\" class=\"mw-redirect\" title=\"France National Football Team\">France</a> in the <a href=\"https://wikipedia.org/wiki/UEFA_Euro_2016_Final\" class=\"mw-redirect\" title=\"UEFA Euro 2016 Final\">UEFA Euro 2016 Final</a> to win their first <a href=\"https://wikipedia.org/wiki/UEFA_European_Championship\" title=\"UEFA European Championship\">European title</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Portugal_National_Football_Team\" class=\"mw-redirect\" title=\"Portugal National Football Team\">Portugal</a> defeats <a href=\"https://wikipedia.org/wiki/France_National_Football_Team\" class=\"mw-redirect\" title=\"France National Football Team\">France</a> in the <a href=\"https://wikipedia.org/wiki/UEFA_Euro_2016_Final\" class=\"mw-redirect\" title=\"UEFA Euro 2016 Final\">UEFA Euro 2016 Final</a> to win their first <a href=\"https://wikipedia.org/wiki/UEFA_European_Championship\" title=\"UEFA European Championship\">European title</a>.", "links": [{"title": "Portugal National Football Team", "link": "https://wikipedia.org/wiki/Portugal_National_Football_Team"}, {"title": "France National Football Team", "link": "https://wikipedia.org/wiki/France_National_Football_Team"}, {"title": "UEFA Euro 2016 Final", "link": "https://wikipedia.org/wiki/UEFA_Euro_2016_Final"}, {"title": "UEFA European Championship", "link": "https://wikipedia.org/wiki/UEFA_European_Championship"}]}, {"year": "2017", "text": "Iraqi Civil War: Mosul is declared fully liberated from the Islamic State of Iraq and the Levant by the government of Iraq.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/War_in_Iraq_(2013%E2%80%932017)\" title=\"War in Iraq (2013-2017)\">Iraqi Civil War</a>: <a href=\"https://wikipedia.org/wiki/Mosul\" title=\"Mo<PERSON>\"><PERSON><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/Battle_of_Mosul_(2016%E2%80%932017)\" title=\"Battle of Mosul (2016-2017)\">fully liberated</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> by the government of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_in_Iraq_(2013%E2%80%932017)\" title=\"War in Iraq (2013-2017)\">Iraqi Civil War</a>: <a href=\"https://wikipedia.org/wiki/Mosul\" title=\"Mo<PERSON>\"><PERSON><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/Battle_of_Mosul_(2016%E2%80%932017)\" title=\"Battle of Mosul (2016-2017)\">fully liberated</a> from the <a href=\"https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant\" class=\"mw-redirect\" title=\"Islamic State of Iraq and the Levant\">Islamic State of Iraq and the Levant</a> by the government of <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a>.", "links": [{"title": "War in Iraq (2013-2017)", "link": "https://wikipedia.org/wiki/War_in_Iraq_(2013%E2%80%932017)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mosul"}, {"title": "Battle of Mosul (2016-2017)", "link": "https://wikipedia.org/wiki/Battle_of_Mosul_(2016%E2%80%932017)"}, {"title": "Islamic State of Iraq and the Levant", "link": "https://wikipedia.org/wiki/Islamic_State_of_Iraq_and_the_Levant"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}]}, {"year": "2018", "text": "<PERSON><PERSON> cave rescue: A group of Thai school children and their football coach are all rescued from a cave after being stuck there for 18 days; one Thai Navy SEAL diver dies during the rescue mission.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Tham_<PERSON>ang_cave_rescue\" title=\"Tham Luang cave rescue\">Tham Luang cave rescue</a>: A group of Thai school children and their football coach are all rescued from a cave after being stuck there for 18 days; one Thai Navy SEAL diver dies during the rescue mission.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tham_<PERSON>ang_cave_rescue\" title=\"Tham Luang cave rescue\">Tham Luang cave rescue</a>: A group of Thai school children and their football coach are all rescued from a cave after being stuck there for 18 days; one Thai Navy SEAL diver dies during the rescue mission.", "links": [{"title": "<PERSON><PERSON> cave rescue", "link": "https://wikipedia.org/wiki/T<PERSON>_<PERSON><PERSON>_cave_rescue"}]}, {"year": "2019", "text": "The final Volkswagen Beetle rolls off the line in Puebla, Mexico; the last of 5,961 \"Special Edition\" cars will be exhibited in a museum.", "html": "2019 - The final <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> rolls off the line in <a href=\"https://wikipedia.org/wiki/Puebla_(city)\" title=\"Puebla (city)\">Puebla</a>, Mexico; the last of 5,961 \"Special Edition\" cars will be exhibited in a museum.", "no_year_html": "The final <a href=\"https://wikipedia.org/wiki/Volkswagen_Beetle\" title=\"Volkswagen Beetle\">Volkswagen Beetle</a> rolls off the line in <a href=\"https://wikipedia.org/wiki/Puebla_(city)\" title=\"Puebla (city)\">Puebla</a>, Mexico; the last of 5,961 \"Special Edition\" cars will be exhibited in a museum.", "links": [{"title": "Volkswagen Beetle", "link": "https://wikipedia.org/wiki/Volkswagen_Beetle"}, {"title": "Puebla (city)", "link": "https://wikipedia.org/wiki/Puebla_(city)"}]}], "Births": [{"year": "1419", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (d. 1471)", "html": "1419 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Hanazono\" title=\"Emperor Go-Hanazono\">Emperor <PERSON><PERSON>Hanazono</a> of Japan (d. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Han<PERSON>ono\" title=\"Emperor Go-Hanazono\">Emperor <PERSON><PERSON>Hanazono</a> of Japan (d. 1471)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1451", "text": "<PERSON> of Scotland (d. 1488)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> III of Scotland\"><PERSON> of Scotland</a> (d. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland\" title=\"<PERSON> III of Scotland\"><PERSON> of Scotland</a> (d. 1488)", "links": [{"title": "<PERSON> of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Scotland"}]}, {"year": "1501", "text": "<PERSON>, Korean poet and scholar (d. 1572)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Shi<PERSON>\"><PERSON></a>, Korean poet and scholar (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Shik\"><PERSON></a>, Korean poet and scholar (d. 1572)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>k"}]}, {"year": "1509", "text": "<PERSON>, French pastor and theologian (d. 1564)", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pastor and theologian (d. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1515", "text": "<PERSON>, Viceroy of Peru (d. 1582)", "html": "1515 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Toledo\" title=\"Francisco de Toledo\"><PERSON> Toledo</a>, Viceroy of Peru (d. 1582)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Toledo\" title=\"Francisco de Toledo\"><PERSON> Toledo</a>, Viceroy of Peru (d. 1582)", "links": [{"title": "Francisco de Toledo", "link": "https://wikipedia.org/wiki/Francisco_de_Toledo"}]}, {"year": "1517", "text": "<PERSON><PERSON>, French cardinal (d. 1571)", "html": "1517 - <a href=\"https://wikipedia.org/wiki/Odet_de_Coligny\" title=\"<PERSON><PERSON> de Coligny\"><PERSON><PERSON></a>, French cardinal (d. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Odet_de_Coligny\" title=\"Odet de Coligny\"><PERSON><PERSON></a>, French cardinal (d. 1571)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odet_de_Coligny"}]}, {"year": "1533", "text": "<PERSON>, Italian diplomat (d. 1611)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian diplomat (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian diplomat (d. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1592", "text": "<PERSON>, French genealogist and historian (d. 1660)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier\" title=\"<PERSON>\"><PERSON></a>, French genealogist and historian (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier\" title=\"<PERSON>\"><PERSON></a>, French genealogist and historian (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier"}]}, {"year": "1614", "text": "<PERSON>, 1st Earl of Anglesey, Irish-English politician (d. 1686)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, Irish-English politician (d. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Anglesey\" title=\"<PERSON>, 1st Earl of Anglesey\"><PERSON>, 1st Earl of Anglesey</a>, Irish-English politician (d. 1686)", "links": [{"title": "<PERSON>, 1st Earl of Anglesey", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Anglesey"}]}, {"year": "1625", "text": "<PERSON>, French adventurer (d. 1703)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French adventurer (d. 1703)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French adventurer (d. 1703)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1638", "text": "<PERSON>, Flemish painter (d. 1685)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1685)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>, Flemish painter (d. 1685)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON>, German theologian and academic (d. 1711)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and academic (d. 1711)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1682", "text": "<PERSON>, English mathematician and astronomer (d. 1716)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and astronomer (d. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1723", "text": "<PERSON>, English lawyer, judge, and politician (d. 1780)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer, judge, and politician (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Swedish noble and agronomist (d. 1786)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish noble and agronomist (d. 1786)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish noble and agronomist (d. 1786)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Ekeblad"}]}, {"year": "1752", "text": "<PERSON> (soldier), American Revolutionary War colonel, politician, foreign minister and entrepreneur. ", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON> (soldier)</a>, American Revolutionary War colonel, politician, foreign minister and entrepreneur. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\"><PERSON> (soldier)</a>, American Revolutionary War colonel, politician, foreign minister and entrepreneur. ", "links": [{"title": "<PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_(soldier)"}]}, {"year": "1752", "text": "<PERSON><PERSON> <PERSON>, United States federal judge (d. 1827)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"St. George Tucker\"><PERSON><PERSON> <PERSON></a>, United States federal judge (d. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"St. George Tucker\"><PERSON><PERSON> <PERSON></a>, United States federal judge (d. 1827)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, American lawyer and politician, 11th Vice President of the United States (d. 1864)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1802", "text": "<PERSON>, Scottish geologist and publisher, co-founded Chambers Harrap (d. 1871)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher_born_1802)\" class=\"mw-redirect\" title=\"<PERSON> (publisher born 1802)\"><PERSON></a>, Scottish geologist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/Chambers_Harrap\" class=\"mw-redirect\" title=\"Chambers Harrap\"><PERSON></a> (d. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(publisher_born_1802)\" class=\"mw-redirect\" title=\"<PERSON> (publisher born 1802)\"><PERSON></a>, Scottish geologist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>rrap\" class=\"mw-redirect\" title=\"<PERSON> Harrap\"><PERSON>rra<PERSON></a> (d. 1871)", "links": [{"title": "<PERSON> (publisher born 1802)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(publisher_born_1802)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chambers_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, British fly fishing author, artisan and Australian pioneer (d. 1860)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fly fishing author, artisan and Australian pioneer (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British fly fishing author, artisan and Australian pioneer (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1804", "text": "<PERSON>, American religious leader (d. 1879)", "html": "1804 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader (d. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, German geologist and palaeontologist (d. 1889)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and palaeontologist (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geologist and palaeontologist (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer, judge, and politician (d. 1908)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Louis-Napol%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, Danish-French painter (d. 1903)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-French painter (d. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-French painter (d. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, American astronomer (d. 1897)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American astronomer (d. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON><PERSON>, Polish violinist and composer (d. 1880)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish violinist and composer (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish violinist and composer (d. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wski"}]}, {"year": "1839", "text": "<PERSON><PERSON><PERSON>, German brewer, co-founded Anheuser-Busch (d. 1913)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German brewer, co-founded <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>-Busch\" title=\"Anheuser-Busch\">Anheus<PERSON><PERSON><PERSON></a> (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German brewer, co-founded <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>-Busch\" title=\"Anheuser-Busch\"><PERSON>heus<PERSON><PERSON></a> (d. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Anheuser-Busch", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Busch"}]}, {"year": "1856", "text": "<PERSON>, Serbian-American electrical and mechanical engineer (d. 1943)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American electrical and mechanical engineer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian-American electrical and mechanical engineer (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Australian businessman and politician, 4th Australian Minister for Defence (d. 1926)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Australian Minister for Defence</a> (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Chapman"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1867", "text": "<PERSON> Baden (d. 1929)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Baden\" title=\"Prince <PERSON> of Baden\">Prince <PERSON> of Baden</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Baden\" title=\"Prince <PERSON> of Baden\">Prince <PERSON> of Baden</a> (d. 1929)", "links": [{"title": "Prince <PERSON> of Baden", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Baden"}]}, {"year": "1871", "text": "<PERSON>, French novelist, critic, and essayist (d. 1922)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, critic, and essayist (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, critic, and essayist (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, Russian sculptor (d. 1971)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian sculptor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American educator and activist (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and activist (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hungarian politician (d. 1973)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m\" title=\"<PERSON><PERSON><PERSON><PERSON> Pattantyús-Ábrahám\"><PERSON><PERSON><PERSON><PERSON>tant<PERSON>-<PERSON><PERSON><PERSON></a>, Hungarian politician (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m\" title=\"<PERSON><PERSON><PERSON><PERSON> Pattantyús-Ábrahám\"><PERSON><PERSON><PERSON><PERSON>-<PERSON></a>, Hungarian politician (d. 1973)", "links": [{"title": "Dezső Pattantyús-Ábrahám", "link": "https://wikipedia.org/wiki/Dezs%C5%91_Pattanty%C3%BAs-%C3%81brah%C3%A1m"}]}, {"year": "1877", "text": "<PERSON>, German zoologist (d. 1935)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German zoologist (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, German painter and sculptor (d. 1943)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON><PERSON>, American society leader, philanthropist, patron and collector of the arts (d. 1975)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American society leader, philanthropist, patron and collector of the arts (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American society leader, philanthropist, patron and collector of the arts (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gg"}]}, {"year": "1883", "text": "<PERSON>, German general (d. 1948)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Estonian playwright and politician (d. 1952)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian playwright and politician (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian playwright and politician (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Greek-Italian painter and set designer (d. 1978)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Italian painter and set designer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Italian painter and set designer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Japanese evangelist, author, and activist (d. 1960)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese evangelist, author, and activist (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese evangelist, author, and activist (d. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gawa"}]}, {"year": "1891", "text": "<PERSON>, American medical researcher and physicist (d. 1982)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical researcher and physicist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American medical researcher and physicist (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American composer (d. 1969)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, German composer and educator (d. 1982)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian politician (d. 1981)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_C<PERSON>grain\" title=\"Thér<PERSON><PERSON>grain\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian politician (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Casgrain\" title=\"Thér<PERSON><PERSON> Casgrain\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian politician (d. 1981)", "links": [{"title": "Thérèse Casgrain", "link": "https://wikipedia.org/wiki/Th%C3%A9r%C3%A8se_Casgrain"}]}, {"year": "1897", "text": "<PERSON><PERSON>, American gangster (d. 1931)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Legs_Diamond\" title=\"Legs Diamond\"><PERSON><PERSON></a>, American gangster (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Legs_Diamond\" title=\"Legs Diamond\"><PERSON><PERSON></a>, American gangster (d. 1931)", "links": [{"title": "Legs Diamond", "link": "https://wikipedia.org/wiki/Legs_Diamond"}]}, {"year": "1897", "text": "<PERSON>, German general and engineer (d. 1957)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and engineer (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and engineer (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Swedish actress (d. 1975)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Bj%C3%B6rling\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9e_Bj%C3%B6rling\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish actress (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_Bj%C3%B6rling"}]}, {"year": "1899", "text": "<PERSON>, American actor, director, and screenwriter (d. 1936)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and screenwriter (d. 1936)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Swiss cyclist (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss cyclist (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss cyclist (d. 1978)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ter"}]}, {"year": "1900", "text": "<PERSON>, Lithuanian-American songwriter (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Mitchell_Parish\" title=\"Mitchell Parish\">Mitchell Parish</a>, Lithuanian-American songwriter (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mitchell_Parish\" title=\"Mitchell Parish\"><PERSON> Parish</a>, Lithuanian-American songwriter (d. 1993)", "links": [{"title": "Mitchell Parish", "link": "https://wikipedia.org/wiki/Mitchell_Parish"}]}, {"year": "1900", "text": "<PERSON>, Russian monk and mystic (d. 1979)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian monk and mystic (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian monk and mystic (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ever<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1958)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1902", "text": "<PERSON><PERSON><PERSON>, Cuban poet, journalist, and activist (d. 1989)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Guill%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban poet, journalist, and activist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicol%C3%A1s_Guill%C3%A9n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cuban poet, journalist, and activist (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nicol%C3%A1s_Guill%C3%A9n"}]}, {"year": "1903", "text": "<PERSON>, German SS officer and jurist (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Werner Best\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and jurist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Werner Best\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer and jurist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1903", "text": "<PERSON>, English author (d. 1969)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, French-American actress (d. 1994)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American actress (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, American journalist and author (d. 2002)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American actor (d. 1971)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, German physician (d. 1948)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Siever<PERSON>\"><PERSON><PERSON></a>, German physician (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>evers"}]}, {"year": "1907", "text": "<PERSON>, American singer and guitarist (d. 1941)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Boy_Fuller\" title=\"Blind Boy Fuller\"><PERSON> Boy <PERSON></a>, American singer and guitarist (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Boy_Fuller\" title=\"Blind Boy Fuller\"><PERSON> Boy <PERSON></a>, American singer and guitarist (d. 1941)", "links": [{"title": "Blind Boy <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Boy_Fuller"}]}, {"year": "1909", "text": "<PERSON>, English lieutenant and businessman (d. 1981)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hotel_owner)\" title=\"<PERSON> (hotel owner)\"><PERSON></a>, English lieutenant and businessman (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hotel_owner)\" title=\"<PERSON> (hotel owner)\"><PERSON></a>, English lieutenant and businessman (d. 1981)", "links": [{"title": "<PERSON> (hotel owner)", "link": "https://wikipedia.org/wiki/<PERSON>_(hotel_owner)"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, English comedian and character actor (d. 1990)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, English comedian and character actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English comedian and character actor (d. 1990)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, American trumpeter and bandleader (d. 1985)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpeter and bandleader (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American trumpeter and bandleader (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Spanish author, poet, and playwright (d. 1985)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Salvador_Espriu\" title=\"Salvador Espriu\"><PERSON></a>, Spanish author, poet, and playwright (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salvador_Espriu\" title=\"Salvador Espriu\"><PERSON></a>, Spanish author, poet, and playwright (d. 1985)", "links": [{"title": "Salvador Espriu", "link": "https://wikipedia.org/wiki/Salvador_Espriu"}]}, {"year": "1914", "text": "<PERSON>, Canadian-American illustrator, co-created <PERSON> (d. 1992)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator, co-created <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Superman\">Superman</a> (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American illustrator, co-created <a href=\"https://wikipedia.org/wiki/Superman\" title=\"Superman\">Superman</a> (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Indonesian film director (d. 2001)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rip\" title=\"<PERSON><PERSON> Urip\"><PERSON><PERSON></a>, Indonesian film director (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Urip\"><PERSON><PERSON></a>, Indonesian film director (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Re<PERSON>_Urip"}]}, {"year": "1916", "text": "<PERSON>, Canadian journalist (d. 1972)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and scout (d. 2000)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and scout (d. 2000)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1917", "text": "<PERSON>, American television host known as \"Mr. Wizard\" (d. 2007)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host known as \"Mr. Wizard\" (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host known as \"Mr. Wizard\" (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English cartoonist (d. 1998)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Australian-English journalist and author (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American baseball player (d. 2018)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, Professor Emeritus of Chemistry at Occidental College (d. 2018)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Professor Emeritus of Chemistry at Occidental College (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Professor Emeritus of Chemistry at Occidental College (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American race driver and engineer (d. 1998)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race driver and engineer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race driver and engineer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, French author, poet, and critic (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and critic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author, poet, and critic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English actor and singer (d. 2009)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English actor and singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" class=\"mw-redirect\" title=\"<PERSON> (singer)\"><PERSON></a>, English actor and singer (d. 2009)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1920", "text": "<PERSON>, American journalist (d. 2003)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2006)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1920", "text": "<PERSON>, English footballer (d. 2002)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American illustrator, created the <PERSON><PERSON> (d. 2001)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Ball\"><PERSON></a>, American illustrator, created the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Ball\"><PERSON></a>, American illustrator, created the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Smiley"}]}, {"year": "1921", "text": "<PERSON>, American actress (d. 1988)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, U.S. Army Major General (d. 2022)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army Major General (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, U.S. Army Major General (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American activist, co-founded the Special Olympics (d. 2009)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist, co-founded the <a href=\"https://wikipedia.org/wiki/Special_Olympics\" title=\"Special Olympics\">Special Olympics</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Special Olympics", "link": "https://wikipedia.org/wiki/Special_Olympics"}]}, {"year": "1922", "text": "<PERSON>, American author and playwright (d. 2003)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Jamaican sprinter (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American boxer and actor (d. 2017)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Mexican singer and actress (d. 2001)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican singer and actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican singer and actress (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American soldier (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_States_Navy)\" title=\"<PERSON> (United States Navy)\"><PERSON></a>, American soldier (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_States_Navy)\" title=\"<PERSON> (United States Navy)\"><PERSON></a>, American soldier (d. 1994)", "links": [{"title": "<PERSON> (United States Navy)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(United_States_Navy)"}]}, {"year": "1923", "text": "<PERSON>, Canadian actress and producer (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author and academic (d. 1987)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and academic (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and academic (d. 1987)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American basketball player and coach (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American wrestler (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Bobo_Brazil\" title=\"Bobo Brazil\"><PERSON><PERSON></a>, American wrestler (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bobo_Brazil\" title=\"Bobo Brazil\"><PERSON><PERSON></a>, American wrestler (d. 1998)", "links": [{"title": "Bobo Brazil", "link": "https://wikipedia.org/wiki/Bobo_Brazil"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON>, Malaysian physician and politician, 4th and 7th Prime Minister of Malaysia", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian physician and politician, 4th and 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian physician and politician, 4th and 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}]}, {"year": "1925", "text": "<PERSON>, American Roman Catholic bishop (d. 2023)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic bishop (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Roman Catholic bishop (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor, magician, songwriter, and novelist (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, magician, songwriter, and novelist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, magician, songwriter, and novelist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 1993)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Russian mathematician and academic (d. 2018)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian mathematician and academic (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tt"}]}, {"year": "1927", "text": "<PERSON>, American soldier and politician, 106th Mayor of New York City (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 106th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 106th <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}]}, {"year": "1927", "text": "<PERSON>, American actor", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American investigative reporter (d. 1976)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American investigative reporter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American investigative reporter (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, French painter and illustrator (d. 1999)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and illustrator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Argentinian-Italian race car driver and businessman, founded <PERSON> (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-Italian race car driver and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American-Israeli rabbi and scholar (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli rabbi and scholar (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Israeli rabbi and scholar (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American baseball player (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1960s_outfielder)\" title=\"<PERSON> (1960s outfielder)\"><PERSON></a>, American baseball player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1960s_outfielder)\" title=\"<PERSON> (1960s outfielder)\"><PERSON></a>, American baseball player (d. 2023)", "links": [{"title": "<PERSON> (1960s outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1960s_outfielder)"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Scottish lawyer and politician (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish lawyer and politician (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American author and screenwriter (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian golfer (d. 2004)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Norman\"><PERSON></a>, Canadian golfer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Norman\"><PERSON></a>, Canadian golfer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Venezuelan politician; 21st Vice President of Venezuela (d. 2020)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician; 21st Vice President of Venezuela (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician; 21st Vice President of Venezuela (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>l"}]}, {"year": "1930", "text": "<PERSON>, Canadian actor (d. 2004)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, American physician, author, and pioneer in occupational and environmental health (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician, author, and pioneer in occupational and environmental health (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American physician, author, and pioneer in occupational and environmental health (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English soprano and actress (d. 2022)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soprano and actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor and screenwriter (d. 1968)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)\" title=\"<PERSON> (actor, born 1931)\"><PERSON></a>, American actor and screenwriter (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)\" title=\"<PERSON> (actor, born 1931)\"><PERSON></a>, American actor and screenwriter (d. 1968)", "links": [{"title": "<PERSON> (actor, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1931)"}]}, {"year": "1931", "text": "<PERSON>, American composer and songwriter (d. 2019)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American author (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_May"}]}, {"year": "1931", "text": "<PERSON>, Canadian short story writer, Nobel Prize laureate (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1932", "text": "<PERSON>, Italian race car driver (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Filipino-American actress, singer and dancer", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino-American actress, singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino-American actress, singer and dancer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, German athlete", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fger\" title=\"<PERSON>\"><PERSON></a>, German athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%9Fger\" title=\"<PERSON>\"><PERSON></a>, German athlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Manfred_Preu%C3%9Fger"}]}, {"year": "1933", "text": "Jump<PERSON>' <PERSON>, American rockabilly singer-songwriter (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Jumpin%27_<PERSON>_<PERSON>\" title=\"Jumpin' <PERSON>\">Jumpin' <PERSON></a>, American rockabilly singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jumpin%27_<PERSON>_<PERSON>\" title=\"Jumpin' <PERSON>\">Jumpin' <PERSON></a>, American rockabilly singer-songwriter (d. 2006)", "links": [{"title": "Jumpin' <PERSON>", "link": "https://wikipedia.org/wiki/Jumpin%27_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Taiwanese decathlete and pole vaulter (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese decathlete and pole vaulter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Taiwanese decathlete and pole vaulter (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actor (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American puppeteer and voice actor (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer and voice actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American puppeteer and voice actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American Catholic religious sister and educator", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Catholic religious sister and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Catholic religious sister and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian politician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Tuckey\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, New Zealand rugby player and businessman (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and businessman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and businessman (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American businessman, co-founded Genentech", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Genentech\" title=\"Genentech\">Genentech</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/Genentech\" title=\"Genentech\">Genentech</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Genentech", "link": "https://wikipedia.org/wiki/Genentech"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Estonian journalist and politician", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American farmer and politician (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Swedish politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, French architect (d. 2018)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American trumpet player and composer (d. 1972)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Irish-English footballer and manager (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1939)\" title=\"<PERSON> (footballer, born 1939)\"><PERSON></a>, Irish-English footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1939)\" title=\"<PERSON> (footballer, born 1939)\"><PERSON></a>, Irish-English footballer and manager (d. 2012)", "links": [{"title": "<PERSON> (footballer, born 1939)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1939)"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Turkish political scientist, journalist and educator (d. 1999)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1\" title=\"<PERSON><PERSON> Taner Kışlalı\"><PERSON><PERSON></a>, Turkish political scientist, journalist and educator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1\" title=\"<PERSON><PERSON> Taner Kışlalı\"><PERSON><PERSON></a>, Turkish political scientist, journalist and educator (d. 1999)", "links": [{"title": "<PERSON><PERSON> Taner Kışlalı", "link": "https://wikipedia.org/wiki/Ahmet_Taner_K%C4%B1%C5%9Flal%C4%B1"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American singer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON>, Indian-English economist and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON>\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, Indian-English economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON><PERSON>, <PERSON>\"><PERSON><PERSON><PERSON>, Baron <PERSON></a>, Indian-English economist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American soprano and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English pianist and composer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian cricketer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian film producer (d. 2012)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film producer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film producer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American anthologist, author, and critic (d. 2016)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist, author, and critic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist, author, and critic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American actor and director", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1941", "text": "<PERSON>, English singer-songwriter, producer, and actor (d. 2020)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, producer, and actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter and producer (d. 2010)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Belarusian general, pilot, and astronaut", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian general, pilot, and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian general, pilot, and astronaut", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American singer-songwriter and guitarist (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and guitarist (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Angolan politician; 1st Prime Minister of Angola", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_do_Nascimento\" title=\"Lopo do Nascimento\"><PERSON><PERSON> Nascimento</a>, Angolan politician; 1st Prime Minister of Angola", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lo<PERSON>_do_Nascimento\" title=\"Lopo do Nascimento\"><PERSON><PERSON> Nascimento</a>, Angolan politician; 1st Prime Minister of Angola", "links": [{"title": "Lopo do Nascimento", "link": "https://wikipedia.org/wiki/Lopo_do_Nascimento"}]}, {"year": "1943", "text": "<PERSON>, American tennis player and journalist (d. 1993)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and journalist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and journalist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON>, Zambian politician", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>wan<PERSON>\" title=\"<PERSON><PERSON><PERSON> Mbikusita-Lewanika\"><PERSON><PERSON><PERSON></a>, Zambian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>ita-Lewanika\"><PERSON><PERSON><PERSON></a>, Zambian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/In<PERSON><PERSON>_Mbi<PERSON><PERSON>-Lewanika"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English  motorcycle racer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English archaeologist and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Glass\"><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and manager", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English sportscaster (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, French director, producer, and screenwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1945", "text": "<PERSON>, English tennis player and sportscaster", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Virginia_Wade\" title=\"<PERSON> Wade\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Wade\" title=\"<PERSON> Wade\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Virginia_Wade"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, French race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Taiwanese actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1946)\" title=\"<PERSON> (actor, born 1946)\"><PERSON></a>, Taiwanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1946)\" title=\"<PERSON> (actor, born 1946)\"><PERSON></a>, Taiwanese actor", "links": [{"title": "<PERSON> (actor, born 1946)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor,_born_1946)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter, producer, and actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>o_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American painter (d. 2013)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Russian figure skater, ballet dancer, actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater, ballet dancer, actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian figure skater, ballet dancer, actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Natalya_Se<PERSON>kh"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and producer (d. 2004)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter and producer (d. 2004)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1949", "text": "<PERSON>, Polish mountaineer and author (d. 2023)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish mountaineer and author (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%84ska\" title=\"<PERSON>\"><PERSON></a>, Polish mountaineer and author (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Czerwi%C5%84ska"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Indian cricketer and sportscaster", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English colonel, lawyer, and politician, British Minister of State for Agriculture", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture,_Fisheries_and_Food_(United_Kingdom)\" title=\"Ministry of Agriculture, Fisheries and Food (United Kingdom)\">British Minister of State for Agriculture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture,_Fisheries_and_Food_(United_Kingdom)\" title=\"Ministry of Agriculture, Fisheries and Food (United Kingdom)\">British Minister of State for Agriculture</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Agriculture, Fisheries and Food (United Kingdom)", "link": "https://wikipedia.org/wiki/Ministry_of_Agriculture,_Fisheries_and_Food_(United_Kingdom)"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, President of Greece, Greek lawyer and politician, Greek Minister for the Interior", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Greece)\" title=\"Ministry of the Interior (Greece)\">Greek Minister for the Interior</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/President_of_Greece\" title=\"President of Greece\">President of Greece</a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_the_Interior_(Greece)\" title=\"Ministry of the Interior (Greece)\">Greek Minister for the Interior</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Greece", "link": "https://wikipedia.org/wiki/President_of_Greece"}, {"title": "Ministry of the Interior (Greece)", "link": "https://wikipedia.org/wiki/Ministry_of_the_Interior_(Greece)"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Indian Politician and Union Home Minister of India", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Politician and Union Home Minister of India", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian Politician and Union Home Minister of India", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Dutch politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rik_<PERSON><PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American musician and wrestler (d. 2011)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Zoogz_Rift\" title=\"Zoogz Rift\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American musician and wrestler (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zoogz_Rift\" title=\"Zoogz Rift\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, American musician and wrestler (d. 2011)", "links": [{"title": "Zoogz Rift", "link": "https://wikipedia.org/wiki/Zoogz_Rift"}]}, {"year": "1954", "text": "<PERSON>, American football player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter and keyboard player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Ten<PERSON>\"><PERSON></a>, English singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, English educator and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English educator and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Australian rugby league player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American lawyer and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Malaysian football manager", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Malaysian football manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON> (footballer)\"><PERSON><PERSON></a>, Malaysian football manager", "links": [{"title": "<PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1957", "text": "<PERSON>, Canadian rock guitarist and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Derry_Grehan\" title=\"Derry Grehan\"><PERSON></a>, Canadian rock guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Derry_Grehan\" title=\"Derry Grehan\"><PERSON></a>, Canadian rock guitarist and songwriter", "links": [{"title": "Derry Grehan", "link": "https://wikipedia.org/wiki/Derry_G<PERSON>han"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, American banjo player and songwriter", "html": "1958 - <a href=\"https://wikipedia.org/wiki/B%C3%A9la_<PERSON>leck\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American banjo player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9la_<PERSON>leck\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American banjo player and songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Fleck"}]}, {"year": "1958", "text": "<PERSON>, Irish actress and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American director and cinematographer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer, drummer and songwriter (d. 2006)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Sandy_West\" title=\"Sandy West\"><PERSON></a>, American singer, drummer and songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sandy_West\" title=\"Sandy West\"><PERSON></a>, American singer, drummer and songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sandy_West"}]}, {"year": "1960", "text": "<PERSON>, Puerto Rican-American convicted kidnapper and rapist (d. 2013)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American convicted kidnapper and rapist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American convicted kidnapper and rapist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Hong Kong singer and film actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer and film actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hong Kong singer and film actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English guitarist and radio DJ", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and radio DJ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and radio DJ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Welsh motorcycle racer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian tennis player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American football player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Urban_Meyer"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Belgian cyclist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American golfer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "Princess <PERSON> of Greece and Denmark, European Princess", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a>, European Princess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a>, European Princess", "links": [{"title": "Princess <PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American wrestler (d. 2006)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Austrian skier and mountaineer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and mountaineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian skier and mountaineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1966", "text": "<PERSON>, Swedish business executive", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Anna_Br%C3%A5kenhielm\" title=\"<PERSON>\"><PERSON></a>, Swedish business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_Br%C3%A5kenhielm\" title=\"<PERSON>\"><PERSON></a>, Swedish business executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_Br%C3%A5kenhielm"}]}, {"year": "1967", "text": "<PERSON>, American professional monster truck driver", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional monster truck driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional monster truck driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English journalist and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese model, actor and television presenter", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model, actor and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese model, actor and television presenter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, South Korean-American lawyer, author, and educator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American lawyer, author, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean-American lawyer, author, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American baseball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English singer-songwriter and dancer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Orange\"><PERSON></a>, English singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Barbadian footballer and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Colombian-American actress and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sof%C3%ADa_Vergara\" title=\"Sof<PERSON> Vergara\"><PERSON><PERSON><PERSON></a>, Colombian-American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sof%C3%ADa_Vergara\" title=\"<PERSON><PERSON><PERSON> Vergara\"><PERSON><PERSON><PERSON></a>, Colombian-American actress and producer", "links": [{"title": "Sofía Vergara", "link": "https://wikipedia.org/wiki/Sof%C3%ADa_Vergara"}]}, {"year": "1972", "text": "<PERSON><PERSON>, German-Swiss singer-songwriter, pianist, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tilo Wolff\"><PERSON><PERSON></a>, German-Swiss singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Wolff\"><PERSON><PERSON></a>, German-Swiss singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tilo_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Irish singer-songwriter, musician, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_May\" title=\"<PERSON><PERSON><PERSON> May\"><PERSON><PERSON><PERSON> May</a>, Irish singer-songwriter, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_May\" title=\"<PERSON><PERSON><PERSON> May\"><PERSON><PERSON><PERSON> May</a>, Irish singer-songwriter, musician, and producer", "links": [{"title": "Imelda May", "link": "https://wikipedia.org/wiki/I<PERSON>da_May"}]}, {"year": "1974", "text": "<PERSON>, American insurance executive (d. 2024)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, American insurance executive (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, American insurance executive (d. 2024)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_(businessman)"}]}, {"year": "1975", "text": "<PERSON>, American businessman", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>stone"}]}, {"year": "1975", "text": "<PERSON>, American race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Icelandic actor (d. 2018)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Stef%C3%A1<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stef%C3%A1<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic actor (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stef%C3%A1<PERSON>_<PERSON>_<PERSON>ef%C3%A1<PERSON>son"}]}, {"year": "1975", "text": "<PERSON>, English race car driver", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Edm%C3%ADlson\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edm%C3%ADlson\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edm%C3%ADlson"}]}, {"year": "1976", "text": "<PERSON>, American singer and guitarist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Allman\"><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Australian footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>de\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>de\" title=\"<PERSON><PERSON><PERSON>de\"><PERSON><PERSON><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>de"}]}, {"year": "1976", "text": "<PERSON>, German footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, English actor", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Chiwetel_Ejiofor\" title=\"Chiwetel Ejiofor\">Chiwe<PERSON> Ejiofor</a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chiwetel_Ejiofor\" title=\"Chiwetel Ejiofor\">Chiwetel Ejiofor</a>, English actor", "links": [{"title": "Chiwetel Ejiofor", "link": "https://wikipedia.org/wiki/Chiwetel_Ejiofor"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Cameroon footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroon footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Cameroon footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na"}]}, {"year": "1979", "text": "<PERSON>, Korean actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "1980", "text": "<PERSON>, Mexican singer-songwriter and keyboard player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alejandro_Mill%C3%A1n"}]}, {"year": "1980", "text": "<PERSON>, American race car driver (d. 2000)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Petty\"><PERSON></a>, American race car driver (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Brazilian singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor, director, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, actress, and fashion designer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actress, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actress, and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Aleksandar_<PERSON>ev\" title=\"Aleksan<PERSON>ev\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksan<PERSON>_<PERSON>ev\" title=\"<PERSON>ek<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American guitarist and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Ukrainian-American television host", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Polish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian actor and director", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Australian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, Australian actor and director", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON><PERSON> (footballer, born 1983)\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1983)\" title=\"<PERSON><PERSON> (footballer, born 1983)\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1983)"}]}, {"year": "1983", "text": "<PERSON>, Korean entertainer and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-chul\" title=\"<PERSON>ul\"><PERSON></a>, Korean entertainer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ul\" title=\"<PERSON>ul\"><PERSON></a>, Korean entertainer and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-chul"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_In%C3%A1cio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9_In%C3%A1cio\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jos%C3%A9_In%C3%A1cio"}]}, {"year": "1983", "text": "<PERSON>, Filipino basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Australian rugby league player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Greek footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, South Korean footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON> <PERSON><PERSON>, American ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/B._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>n"}]}, {"year": "1985", "text": "<PERSON>, German footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mario_G%C3%B3mez"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Belgian politician", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Funda_Oru\" title=\"Funda Oru\"><PERSON><PERSON></a>, Belgian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Funda_Oru\" title=\"Funda Oru\"><PERSON><PERSON></a>, Belgian politician", "links": [{"title": "Funda Oru", "link": "https://wikipedia.org/wiki/Funda_Oru"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American actress, director, and producer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, New Zealand BMX rider", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(BMX_rider)\" title=\"<PERSON> (BMX rider)\"><PERSON></a>, New Zealand BMX rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(BMX_rider)\" title=\"<PERSON> (BMX rider)\"><PERSON></a>, New Zealand BMX rider", "links": [{"title": "<PERSON> (BMX rider)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(BMX_rider)"}]}, {"year": "1990", "text": "<PERSON>, Australian rugby league player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Toshiki\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Toshi<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yon<PERSON><PERSON>_<PERSON>shiki"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Daish%C5%8Dmaru_Sh%C5%8Dgo\" title=\"Dai<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Daish%C5%8Dmaru_Sh%C5%8Dgo\" title=\"Daish<PERSON>mar<PERSON>go\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Daish%C5%8Dmaru_Sh%C5%8Dgo"}]}, {"year": "1999", "text": "<PERSON>, Portuguese composer and singer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/April_<PERSON>\" title=\"April <PERSON>\"><PERSON></a>, Portuguese composer and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/April_<PERSON>\" title=\"April <PERSON>\"><PERSON></a>, Portuguese composer and singer", "links": [{"title": "April Ivy", "link": "https://wikipedia.org/wiki/April_Ivy"}]}, {"year": "2001", "text": "<PERSON><PERSON>, American actress", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Me<PERSON>ed\" title=\"<PERSON><PERSON> Merced\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Me<PERSON>ed\" title=\"<PERSON><PERSON> Merced\"><PERSON><PERSON></a>, American actress", "links": [{"title": "Isabela Merced", "link": "https://wikipedia.org/wiki/Isabela_Merced"}]}, {"year": "2002", "text": "<PERSON>, Australian rugby league player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Thai celebrity pygmy hippopotamus", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai celebrity <a href=\"https://wikipedia.org/wiki/Pygmy_hippopotamus\" title=\"Pygmy hippopotamus\">pygmy hippopotamus</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Thai celebrity <a href=\"https://wikipedia.org/wiki/Pygmy_hippopotamus\" title=\"Pygmy hippopotamus\">pygmy hippopotamus</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Deng"}, {"title": "Pygmy hippopotamus", "link": "https://wikipedia.org/wiki/Pygmy_hippopotamus"}]}], "Deaths": [{"year": "138", "text": "<PERSON><PERSON>, Roman emperor (b. 76)", "html": "138 - <a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a>, Roman emperor (b. 76)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hadrian\" title=\"Hadrian\"><PERSON><PERSON></a>, Roman emperor (b. 76)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "645", "text": "<PERSON><PERSON>, Japanese politician", "html": "645 - <a href=\"https://wikipedia.org/wiki/Soga_no_<PERSON><PERSON><PERSON>\" title=\"Soga no Iruka\"><PERSON><PERSON> no <PERSON><PERSON></a>, Japanese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Soga_no_I<PERSON>a\" title=\"Soga no Iruka\"><PERSON><PERSON> no <PERSON><PERSON></a>, Japanese politician", "links": [{"title": "<PERSON>ga no Iruka", "link": "https://wikipedia.org/wiki/So<PERSON>_no_<PERSON><PERSON>a"}]}, {"year": "649", "text": "<PERSON>, Chinese emperor (b. 598)", "html": "649 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON></a>, Chinese emperor (b. 598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\"><PERSON></a>, Chinese emperor (b. 598)", "links": [{"title": "Emperor <PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang"}]}, {"year": "772", "text": "<PERSON><PERSON><PERSON><PERSON> of Temse, Frankish noblewoman", "html": "772 - <a href=\"https://wikipedia.org/wiki/Am<PERSON><PERSON><PERSON>_of_Temse\" title=\"<PERSON><PERSON><PERSON><PERSON> of Temse\"><PERSON><PERSON><PERSON><PERSON> of Temse</a>, Frankish noblewoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Temse\" title=\"<PERSON><PERSON><PERSON><PERSON> of Temse\"><PERSON><PERSON><PERSON><PERSON> of Temse</a>, Frankish noblewoman", "links": [{"title": "Amalberga of Temse", "link": "https://wikipedia.org/wiki/Amalberga_of_Temse"}]}, {"year": "831", "text": "<PERSON><PERSON><PERSON><PERSON> bint <PERSON>, <PERSON>id Princess", "html": "831 - <a href=\"https://wikipedia.org/wiki/Zubaida<PERSON>_bint_Ja%60far\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> bint Ja`far\"><PERSON><PERSON><PERSON><PERSON> bint Ja`far</a>, <PERSON><PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>uba<PERSON><PERSON>_bint_Ja%60far\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> bint Ja`far\"><PERSON><PERSON><PERSON><PERSON> bint Ja`far</a>, <PERSON><PERSON>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> bint Ja`far", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_bint_Ja%60far"}]}, {"year": "983", "text": "<PERSON>, pope of the Catholic Church", "html": "983 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VII\" title=\"Pope Benedict VII\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VII\" title=\"Pope Benedict VII\"><PERSON></a>, pope of the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a>", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "994", "text": "<PERSON>, margrave of Austria", "html": "994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Margrave of Austria\"><PERSON></a>, margrave of Austria", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria\" title=\"<PERSON>, Mar<PERSON> of Austria\"><PERSON></a>, margrave of Austria", "links": [{"title": "<PERSON>, Margrave of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Austria"}]}, {"year": "1086", "text": "<PERSON><PERSON> <PERSON>, king of Denmark (b. 1043)", "html": "1086 - <a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\"><PERSON><PERSON> IV</a>, king of Denmark (b. 1043)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canute_IV_of_Denmark\" title=\"Canute IV of Denmark\"><PERSON><PERSON> IV</a>, king of Denmark (b. 1043)", "links": [{"title": "Canute IV of Denmark", "link": "https://wikipedia.org/wiki/Canute_IV_of_Denmark"}]}, {"year": "1103", "text": "<PERSON>, king of Denmark (b. 1060)", "html": "1103 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON></a>, king of Denmark (b. 1060)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1290", "text": "<PERSON><PERSON><PERSON> IV, king of Hungary (b. 1262)", "html": "1290 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"<PERSON><PERSON><PERSON> IV of Hungary\"><PERSON><PERSON><PERSON> IV</a>, king of Hungary (b. 1262)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Hungary\" title=\"<PERSON><PERSON><PERSON> IV of Hungary\"><PERSON><PERSON><PERSON> IV</a>, king of Hungary (b. 1262)", "links": [{"title": "<PERSON><PERSON><PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>laus_IV_of_Hungary"}]}, {"year": "1460", "text": "<PERSON>, 1st Duke of Buckingham, English commander and politician, Lord High Constable of England (b. 1402)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Buckingham\" title=\"<PERSON>, 1st Duke of Buckingham\"><PERSON>, 1st Duke of Buckingham</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1402)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Buckingham\" title=\"<PERSON>, 1st Duke of Buckingham\"><PERSON>, 1st Duke of Buckingham</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1402)", "links": [{"title": "<PERSON>, 1st Duke of Buckingham", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Buckingham"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1460", "text": "<PERSON>, 2nd Earl of Shrewsbury, English nobleman (b. c. 1413)", "html": "1460 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Shrewsbury\" title=\"<PERSON>, 2nd Earl of Shrewsbury\"><PERSON>, 2nd Earl of Shrewsbury</a>, English nobleman (b. c. 1413)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Shrewsbury\" title=\"<PERSON>, 2nd Earl of Shrewsbury\"><PERSON>, 2nd Earl of Shrewsbury</a>, English nobleman (b. c. 1413)", "links": [{"title": "<PERSON>, 2nd Earl of Shrewsbury", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shrewsbury"}]}, {"year": "1461", "text": "<PERSON>, king of Bosnia (b. 1411)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bosnia\" title=\"<PERSON> of Bosnia\"><PERSON></a>, king of Bosnia (b. 1411)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thomas_of_Bosnia\" title=\"<PERSON> of Bosnia\"><PERSON></a>, king of Bosnia (b. 1411)", "links": [{"title": "<PERSON> of Bosnia", "link": "https://wikipedia.org/wiki/Thomas_<PERSON>_Bosnia"}]}, {"year": "1473", "text": "<PERSON>, king of Cyprus", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> II of Cyprus\"><PERSON> II</a>, king of Cyprus", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> II of Cyprus\"><PERSON> II</a>, king of Cyprus", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/James_II_of_Cyprus"}]}, {"year": "1480", "text": "<PERSON> of Anjou, French nobleman (b. 1400)", "html": "1480 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, French nobleman (b. 1400)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou\" title=\"<PERSON> of Anjou\"><PERSON> of Anjou</a>, French nobleman (b. 1400)", "links": [{"title": "<PERSON> of Anjou", "link": "https://wikipedia.org/wiki/Ren%C3%A9_of_Anjou"}]}, {"year": "1510", "text": "<PERSON>, queen of Cyprus (b. 1454)", "html": "1510 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of Cyprus (b. 1454)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of Cyprus (b. 1454)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1576", "text": "<PERSON><PERSON><PERSON> Garzia di Toledo, Italian noble (b. 1553)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/Eleonora_di_Garzia_di_Toledo\" title=\"Eleonora di Garzia di Toledo\"><PERSON><PERSON><PERSON> di Garzia di Toledo</a>, Italian noble (b. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eleonora_di_Garzia_di_Toledo\" title=\"Eleonora di Garzia di Toledo\"><PERSON>eonora di Garzia di Toledo</a>, Italian noble (b. 1553)", "links": [{"title": "Eleonora di Garzia di Toledo", "link": "https://wikipedia.org/wiki/Eleonora_di_Garzia_di_Toledo"}]}, {"year": "1559", "text": "<PERSON>, king of France (b. 1519)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, king of France (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> II of France\"><PERSON> II</a>, king of France (b. 1519)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}]}, {"year": "1584", "text": "<PERSON>, Dutch nobleman (b. 1533)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"<PERSON> I of Orange\"><PERSON></a>, Dutch nobleman (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Orange\" class=\"mw-redirect\" title=\"<PERSON> I of Orange\"><PERSON></a>, Dutch nobleman (b. 1533)", "links": [{"title": "<PERSON> Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Orange"}]}, {"year": "1590", "text": "<PERSON>, archduke of Austria (b. 1540)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON></a>, archduke of Austria (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON></a>, archduke of Austria (b. 1540)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>ke_of_Austria"}]}, {"year": "1594", "text": "<PERSON>, Italian organist and composer (b. 1554)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1554)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1603", "text": "<PERSON>, Spanish archbishop and academic (b. 1538)", "html": "1603 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s_i_Borrull\" title=\"<PERSON>ull\"><PERSON></a>, Spanish archbishop and academic (b. 1538)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s_i_Borrull\" title=\"<PERSON> Borrull\"><PERSON></a>, Spanish archbishop and academic (b. 1538)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8s_i_Borrull"}]}, {"year": "1621", "text": "<PERSON>, French commander (b. 1571)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_of_Bucquoy\" class=\"mw-redirect\" title=\"<PERSON>, Count of Bucquoy\"><PERSON></a>, French commander (b. 1571)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_of_Bucquoy\" class=\"mw-redirect\" title=\"<PERSON>, Count of Bucquoy\"><PERSON></a>, French commander (b. 1571)", "links": [{"title": "<PERSON>, Count of Bucquoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_of_Bucquoy"}]}, {"year": "1653", "text": "<PERSON>, French librarian and scholar (b. 1600)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French librarian and scholar (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French librarian and scholar (b. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Naud%C3%A9"}]}, {"year": "1680", "text": "<PERSON>, French priest and scholar (b. 1643)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, French priest and scholar (b. 1643)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ri\" title=\"<PERSON>\"><PERSON></a>, French priest and scholar (b. 1643)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Mor%C3%A9ri"}]}, {"year": "1683", "text": "<PERSON>, French historian and author (b. 1610)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Eudes_de_M%C3%A9zeray\" title=\"<PERSON> M<PERSON>ay\"><PERSON></a>, French historian and author (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Eudes_de_M%C3%A9zeray\" title=\"<PERSON>zeray\"><PERSON></a>, French historian and author (b. 1610)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Eudes_de_M%C3%A9zeray"}]}, {"year": "1686", "text": "<PERSON>, English bishop and academic (b. 1625)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and academic (b. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and academic (b. 1625)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)"}]}, {"year": "1776", "text": "<PERSON>, English lawyer and minister (b. 1704)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English lawyer and minister (b. 1704)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English lawyer and minister (b. 1704)", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}]}, {"year": "1794", "text": "<PERSON><PERSON>, French general (b. 1754)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, French general (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>pard de <PERSON>\"><PERSON><PERSON> <PERSON></a>, French general (b. 1754)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, English painter and academic (b. 1724)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, French photographer and physicist, invented the daguerreotype (b. 1787)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and physicist, invented the <a href=\"https://wikipedia.org/wiki/Daguerreotype\" title=\"Daguerreotype\">daguerreotype</a> (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French photographer and physicist, invented the <a href=\"https://wikipedia.org/wiki/Daguerreotype\" title=\"Daguerreotype\">daguerreotype</a> (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Daguerreotype", "link": "https://wikipedia.org/wiki/Daguerreotype"}]}, {"year": "1863", "text": "<PERSON>, American author and educator (b. 1779)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1779)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, German architect and academic (b. 1812)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (b. 1812)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German architect and academic (b. 1812)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American chess player (b. 1837)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American organist and composer (b. 1839)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (b. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and composer (b. 1839)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Dutch painter (b. 1831)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">He<PERSON><PERSON></a>, Dutch painter (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter (b. 1831)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, 1st Baron <PERSON>, British admiral (b. 1841)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, British admiral (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, British admiral (b. 1841)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, French actress (b. 1866)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON>li%C3%A8re\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%88ve_<PERSON>li%C3%A8re\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1866)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%88ve_Lavalli%C3%A8re"}]}, {"year": "1938", "text": "<PERSON>, 15th president of Liberia (b. 1854)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 15th president of Liberia (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 15th president of Liberia (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American pianist, composer, and bandleader (b. 1890)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Jelly <PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Jelly <PERSON> Morton\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, English actor (b. 1868)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Wright\"><PERSON><PERSON></a>, English actor (b. 1868)", "links": [{"title": "<PERSON><PERSON> Wright", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American-Argentinian engineer (b. 1882)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian engineer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Argentinian engineer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Danish organist and composer (b. 1893)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish organist and composer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish organist and composer (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Italian mob boss (b. 1877)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>og<PERSON>_<PERSON>izzini\" title=\"Calogero Vizzini\"><PERSON><PERSON><PERSON></a>, Italian mob boss (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Calogero_<PERSON>izzini\" title=\"Calogero Vizzini\"><PERSON><PERSON><PERSON></a>, Italian mob boss (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Calogero_Vizzini"}]}, {"year": "1956", "text": "<PERSON>, American baseball player (b. 1898)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Giard\"><PERSON></a>, American baseball player (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Joe Giard\"><PERSON></a>, American baseball player (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Norwegian actor and sculptor (b. 1876)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/S%C3%A6bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian actor and sculptor (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A6bj%C3%B8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Norwegian actor and sculptor (b. 1876)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A6bj%C3%B8rn_<PERSON><PERSON>ahl"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Israeli rabbi and politician (b. 1875)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rabbi and politician (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli rabbi and politician (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English rugby player and sportscaster (b. 1893)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Icelandic academic and politician, 13th Prime Minister of Iceland (b. 1908)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1908)\" title=\"<PERSON><PERSON><PERSON> (born 1908)\"><PERSON><PERSON><PERSON></a>, Icelandic academic and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(born_1908)\" title=\"<PERSON><PERSON><PERSON> (born 1908)\"><PERSON><PERSON><PERSON></a>, Icelandic academic and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Iceland\" title=\"Prime Minister of Iceland\">Prime Minister of Iceland</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON> (born 1908)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(born_1908)"}, {"title": "Prime Minister of Iceland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Iceland"}]}, {"year": "1971", "text": "<PERSON>, French boxer (b. 1924)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French boxer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American pianist, composer, and bandleader (b. 1887)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Lovie_Austin\" title=\"Lovie Austin\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lovie_Austin\" title=\"Lovie Austin\"><PERSON><PERSON></a>, American pianist, composer, and bandleader (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lovie_Austin"}]}, {"year": "1978", "text": "<PERSON>, American businessman and philanthropist, founded the Asia Society (b. 1906)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Asia_Society\" title=\"Asia Society\">Asia Society</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON> III</a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Asia_Society\" title=\"Asia Society\">Asia Society</a> (b. 1906)", "links": [{"title": "<PERSON> III", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Asia Society", "link": "https://wikipedia.org/wiki/Asia_Society"}]}, {"year": "1979", "text": "<PERSON>, American conductor (b. 1894)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American author and screenwriter (b. 1908)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Dutch photographer (b. 1950)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch photographer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch photographer (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Polish mountaineer and author (b. 1940)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON><PERSON><PERSON> (mountaineer)\"><PERSON><PERSON><PERSON></a>, Polish mountaineer and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(mountaineer)\" title=\"<PERSON><PERSON><PERSON> (mountaineer)\"><PERSON><PERSON><PERSON></a>, Polish mountaineer and author (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(mountaineer)"}]}, {"year": "1987", "text": "<PERSON>, American record producer, critic, and activist (b. 1910)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American record producer, critic, and activist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(producer)\" class=\"mw-redirect\" title=\"<PERSON> (producer)\"><PERSON></a>, American record producer, critic, and activist (b. 1910)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_(producer)"}]}, {"year": "1989", "text": "<PERSON>, American voice actor (b. 1908)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice actor (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Blanc\"><PERSON></a>, American voice actor (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American author and poet (b. 1901)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American screenwriter and producer (b. 1924)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and politician (b. 1908)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish lawyer and politician (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Estonian author (b. 1928)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian author (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ud"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist and politician (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Vak<PERSON><PERSON>_<PERSON>\" title=\"Vakko<PERSON> Majeed\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ak<PERSON><PERSON>_<PERSON>\" title=\"Vakko<PERSON> Majeed\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vak<PERSON><PERSON>_<PERSON>eed"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Canadian politician, 23rd Lieutenant Governor of Quebec (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B4t%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B4t%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian politician, 23rd <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%B4t%C3%A9"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Greek general (b. 1943)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek general (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American author (b. 1933)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, English author (b. 1908)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Baron <PERSON>, German-English lawyer and politician, Attorney General for England and Wales (b. 1902)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Baron <PERSON></a>, German-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, German-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a> (b. 1902)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "2004", "text": "<PERSON><PERSON>, Russian-American ballerina and actress (b. 1922)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American ballerina and actress (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian-American ballerina and actress (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pati_<PERSON>s"}]}, {"year": "2005", "text": "<PERSON><PERSON> <PERSON><PERSON>, English author (b. 1940)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English author (b. 1940)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Chechen terrorist rebel leader (b. 1965)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chechen terrorist rebel leader (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chechen terrorist rebel leader (b. 1965)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Swedish politician (b. 1920)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bladh\" title=\"<PERSON><PERSON><PERSON> Bladh\"><PERSON><PERSON><PERSON></a>, Swedish politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Bladh\" title=\"<PERSON><PERSON><PERSON> Bladh\"><PERSON><PERSON><PERSON></a>, Swedish politician (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>nart_Bladh"}]}, {"year": "2007", "text": "<PERSON>, chancellor of Faridia University. (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_(education)\" title=\"Chancellor (education)\">chancellor</a> of <a href=\"https://wikipedia.org/wiki/Jamia_Faridia\" title=\"Jamia Faridia\">Faridia University</a>. (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Chancellor_(education)\" title=\"Chancellor (education)\">chancellor</a> of <a href=\"https://wikipedia.org/wiki/Jamia_Faridia\" title=\"Jamia Faridia\">Faridia University</a>. (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chancellor (education)", "link": "https://wikipedia.org/wiki/Chancellor_(education)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jamia_Faridia"}]}, {"year": "2007", "text": "<PERSON>, American cartoonist and author (b. 1949)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and author (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and author (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Japanese-American wrestler and businessman, founded <PERSON><PERSON><PERSON> (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Aoki\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Aoki\"><PERSON><PERSON><PERSON></a>, Japanese-American wrestler and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ana"}]}, {"year": "2008", "text": "<PERSON>, American golfer (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, Norwegian writer (b. 1917)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Canadian soprano and educator (b. 1921)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and educator (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian soprano and educator (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, French dancer and choreographer (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dancer and choreographer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dancer and choreographer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Filipino actor, singer, and producer (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, singer, and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, singer, and producer (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and politician (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, German-Dutch journalist and author (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch journalist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Dutch journalist and author (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, German lieutenant (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Russian-German composer (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German composer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-German composer (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American businessman (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Polish poet and linguist (b. 1929)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and linguist (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and linguist (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>ara"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Spanish journalist (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish journalist (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Concha_Garc%C3%ADa_Campoy"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and jurist (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, South Korean golfer (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>-hee\"><PERSON>hee</a>, South Korean golfer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee\" title=\"<PERSON>-hee\"><PERSON>-hee</a>, South Korean golfer (b. 1956)", "links": [{"title": "<PERSON>e", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hee"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author and academic (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>lana<PERSON>_Mahapatra\" title=\"<PERSON><PERSON>lana<PERSON> Mahapatra\"><PERSON><PERSON><PERSON><PERSON> Mahapatra</a>, Indian author and academic (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>lana<PERSON>_Mahapatra\" title=\"Gokulana<PERSON> Mahapatra\"><PERSON><PERSON><PERSON><PERSON>tra</a>, Indian author and academic (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> Ma<PERSON>", "link": "https://wikipedia.org/wiki/Gokulana<PERSON>_Ma<PERSON>patra"}]}, {"year": "2014", "text": "<PERSON>, American lawyer and judge (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Lithuanian-American businessman and philanthropist (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American businessman and philanthropist (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian-American businessman and philanthropist (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American ecologist and academic (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and academic (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ecologist and academic (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Indian actress, dancer, and choreographer (b. 1912)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actress, dancer, and choreographer (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1934)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Welsh-American actor and director (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Egyptian actor (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian actor (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian tenor (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tenor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, German politician (b. 1922)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German politician (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "2018", "text": "<PERSON>,  American author and television producer (b. 1917)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and television producer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and television producer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Dutch short track speed skater (b. 1992) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch short track speed skater (b. 1992) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch short track speed skater (b. 1992) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, English footballer and manager (b. 1935)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Canadian outlaw biker (b. 1953)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian outlaw biker (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian outlaw biker (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": " <PERSON>, American Air Force officer, test pilot, and NASA astronaut (b. 1932)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force officer, test pilot, and NASA astronaut (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force officer, test pilot, and NASA astronaut (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Native American Artist (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American Artist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American Artist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American musician (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}