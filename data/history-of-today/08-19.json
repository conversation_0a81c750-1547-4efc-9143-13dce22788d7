{"date": "August 19", "url": "https://wikipedia.org/wiki/August_19", "data": {"Events": [{"year": "295 BC", "text": "The first temple to <PERSON>, the Roman goddess of love, beauty and fertility, is dedicated by <PERSON><PERSON><PERSON> during the Third Samnite War.", "html": "295 BC - 295 BC - The first temple to <a href=\"https://wikipedia.org/wiki/Venus_(mythology)\" title=\"Venus (mythology)\"><PERSON></a>, the Roman goddess of love, beauty and fertility, is dedicated by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_(consul_292_BC)\" title=\"<PERSON><PERSON><PERSON>abius <PERSON> (consul 292 BC)\"><PERSON><PERSON><PERSON>abius <PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Third_Samnite_War\" class=\"mw-redirect\" title=\"Third Samnite War\">Third Samnite War</a>.", "no_year_html": "295 BC - The first temple to <a href=\"https://wikipedia.org/wiki/Venus_(mythology)\" title=\"Venus (mythology)\"><PERSON></a>, the Roman goddess of love, beauty and fertility, is dedicated by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>abi<PERSON>_<PERSON>_<PERSON>_(consul_292_BC)\" title=\"<PERSON><PERSON><PERSON> Fabius <PERSON> (consul 292 BC)\"><PERSON><PERSON><PERSON> Fabius <PERSON></a> during the <a href=\"https://wikipedia.org/wiki/Third_Samnite_War\" class=\"mw-redirect\" title=\"Third Samnite War\">Third Samnite War</a>.", "links": [{"title": "<PERSON> (mythology)", "link": "https://wikipedia.org/wiki/<PERSON>_(mythology)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (consul 292 BC)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_(consul_292_BC)"}, {"title": "Third Samnite War", "link": "https://wikipedia.org/wiki/Third_Samnite_War"}]}, {"year": "43 BC", "text": "Gaius <PERSON>, later known as <PERSON>, compels the Roman Senate to elect him Consul.", "html": "43 BC - 43 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Gaius Julius Caesar <PERSON>avianus</a>, later known as <PERSON>, compels the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> to elect him <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Consul</a>.", "no_year_html": "43 BC - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Gaius Julius Caesar <PERSON>avianus</a>, later known as <PERSON>, compels the <a href=\"https://wikipedia.org/wiki/Roman_Senate\" title=\"Roman Senate\">Roman Senate</a> to elect him <a href=\"https://wikipedia.org/wiki/Roman_consul\" title=\"Roman consul\">Consul</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}, {"title": "Roman Senate", "link": "https://wikipedia.org/wiki/Roman_Senate"}, {"title": "Roman consul", "link": "https://wikipedia.org/wiki/Roman_consul"}]}, {"year": "947", "text": "<PERSON>, a Kharijite rebel leader, is defeated and killed in the Hodna Mountains in modern-day Algeria by Fatimid forces.", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Yazid\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Kharijites\" title=\"Kharijites\">Kharijite</a> rebel leader, is defeated and killed in the <a href=\"https://wikipedia.org/wiki/Hodna_Mountains\" title=\"Hodna Mountains\">Hodna Mountains</a> in modern-day <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> by <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\">Fatimid</a> forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Yazid\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Kharijites\" title=\"Kharijites\">Kharijite</a> rebel leader, is defeated and killed in the <a href=\"https://wikipedia.org/wiki/Hodna_Mountains\" title=\"Hodna Mountains\">Hodna Mountains</a> in modern-day <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a> by <a href=\"https://wikipedia.org/wiki/Fatimid_Caliphate\" title=\"Fatimid Caliphate\">Fatimid</a> forces.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kharijites"}, {"title": "Hodna Mountains", "link": "https://wikipedia.org/wiki/Hodna_Mountains"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatimid_Caliphate"}]}, {"year": "1153", "text": "<PERSON> of Jerusalem takes control of the Kingdom of Jerusalem from his mother <PERSON><PERSON><PERSON>, and also captures Ascalon.", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> of Jerusalem</a> takes control of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a> from his mother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON></a>, and also <a href=\"https://wikipedia.org/wiki/Siege_of_Ascalon\" title=\"Siege of Ascalon\">captures Ascalon</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> III of Jerusalem\"><PERSON> of Jerusalem</a> takes control of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Jerusalem\" title=\"Kingdom of Jerusalem\">Kingdom of Jerusalem</a> from his mother <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem\" title=\"<PERSON><PERSON><PERSON>, Queen of Jerusalem\"><PERSON><PERSON><PERSON></a>, and also <a href=\"https://wikipedia.org/wiki/Siege_of_Ascalon\" title=\"Siege of Ascalon\">captures Ascalon</a>.", "links": [{"title": "<PERSON> III of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_III_of_Jerusalem"}, {"title": "Kingdom of Jerusalem", "link": "https://wikipedia.org/wiki/Kingdom_of_Jerusalem"}, {"title": "<PERSON><PERSON><PERSON>, Queen of Jerusalem", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Queen_of_Jerusalem"}, {"title": "Siege of Ascalon", "link": "https://wikipedia.org/wiki/Siege_of_Ascalon"}]}, {"year": "1458", "text": "<PERSON> <PERSON> is elected the 211th Pope.", "html": "1458 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_II\" title=\"Pope Pius II\">Pope <PERSON> II</a> is elected the 211th Pope.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II\" title=\"Pope Pius II\">Pope <PERSON> II</a> is elected the 211th Pope.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1504", "text": "In Ireland, the Hiberno-Norman <PERSON> (Burkes) and Cambro-Norman Fitzgeralds fight in the Battle of Knockdoe.", "html": "1504 - In Ireland, the <a href=\"https://wikipedia.org/wiki/Hiberno-Normans\" class=\"mw-redirect\" title=\"Hiberno-Normans\">Hiberno-Norman</a> <PERSON> (Burke<PERSON>) and <a href=\"https://wikipedia.org/wiki/Cambro-Normans\" title=\"Cambro-Normans\">Cambro-Norman</a> Fitzgeralds fight in the <a href=\"https://wikipedia.org/wiki/Battle_of_Knockdoe\" title=\"Battle of Knockdoe\">Battle of Knockdoe</a>.", "no_year_html": "In Ireland, the <a href=\"https://wikipedia.org/wiki/Hiberno-Normans\" class=\"mw-redirect\" title=\"Hiberno-Normans\">Hiberno-Norman</a> <PERSON> (Burke<PERSON>) and <a href=\"https://wikipedia.org/wiki/Cambro-Normans\" title=\"Cambro-Normans\">Cambro-Norman</a> Fitzgeralds fight in the <a href=\"https://wikipedia.org/wiki/Battle_of_Knockdoe\" title=\"Battle of Knockdoe\">Battle of Knockdoe</a>.", "links": [{"title": "Hiberno-Normans", "link": "https://wikipedia.org/wiki/Hiberno-Normans"}, {"title": "Cambro-Normans", "link": "https://wikipedia.org/wiki/Cambro-Normans"}, {"title": "Battle of Knockdoe", "link": "https://wikipedia.org/wiki/Battle_of_Knockdoe"}]}, {"year": "1561", "text": "<PERSON>, Queen of Scots, aged 18, returns to Scotland after spending 13 years in France.", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, aged 18, returns to <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> after spending 13 years in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, aged 18, returns to <a href=\"https://wikipedia.org/wiki/Kingdom_of_Scotland\" title=\"Kingdom of Scotland\">Scotland</a> after spending 13 years in <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Kingdom of Scotland", "link": "https://wikipedia.org/wiki/Kingdom_of_Scotland"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}]}, {"year": "1604", "text": "Eighty Years War: a besieging Dutch and English army led by <PERSON> of Orange forces the Spanish garrison of Sluis to capitulate.", "html": "1604 - <a href=\"https://wikipedia.org/wiki/Eighty_Years_War\" class=\"mw-redirect\" title=\"Eighty Years War\">Eighty Years War</a>: a besieging Dutch and English army led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> of Orange\"><PERSON> Orange</a> forces the Spanish garrison of <a href=\"https://wikipedia.org/wiki/Sluis\" title=\"Slu<PERSON>\"><PERSON>lu<PERSON></a> to capitulate.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years_War\" class=\"mw-redirect\" title=\"Eighty Years War\">Eighty Years War</a>: a besieging Dutch and English army led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Orange\" class=\"mw-redirect\" title=\"<PERSON> Orange\"><PERSON> Orange</a> forces the Spanish garrison of <a href=\"https://wikipedia.org/wiki/Sluis\" title=\"<PERSON>lu<PERSON>\"><PERSON><PERSON><PERSON></a> to capitulate.", "links": [{"title": "Eighty Years War", "link": "https://wikipedia.org/wiki/Eighty_Years_War"}, {"title": "<PERSON> of Orange", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>luis"}]}, {"year": "1612", "text": "The \"Samlesbury witches\", three women from the Lancashire village of Samlesbury, England, are put on trial, accused of practicing witchcraft, one of the most famous witch trials in British history.", "html": "1612 - The \"<a href=\"https://wikipedia.org/wiki/Samlesbury_witches\" title=\"Samlesbury witches\">Samlesbury witches</a>\", three women from the <a href=\"https://wikipedia.org/wiki/Lancashire\" title=\"Lancashire\">Lancashire</a> village of <a href=\"https://wikipedia.org/wiki/Samlesbury\" title=\"Samlesbury\">Samlesbury</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, are put on trial, accused of practicing <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a>, one of the most famous <a href=\"https://wikipedia.org/wiki/Witch-hunt\" class=\"mw-redirect\" title=\"Witch-hunt\">witch trials</a> in British history.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/Samlesbury_witches\" title=\"Samlesbury witches\">Samlesbury witches</a>\", three women from the <a href=\"https://wikipedia.org/wiki/Lancashire\" title=\"Lancashire\">Lancashire</a> village of <a href=\"https://wikipedia.org/wiki/Samlesbury\" title=\"Samlesbury\">Samlesbury</a>, <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>, are put on trial, accused of practicing <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a>, one of the most famous <a href=\"https://wikipedia.org/wiki/Witch-hunt\" class=\"mw-redirect\" title=\"Witch-hunt\">witch trials</a> in British history.", "links": [{"title": "Samlesbury witches", "link": "https://wikipedia.org/wiki/Samlesbury_witches"}, {"title": "Lancashire", "link": "https://wikipedia.org/wiki/Lancashire"}, {"title": "Samlesbury", "link": "https://wikipedia.org/wiki/Samlesbury"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}, {"title": "Witch-hunt", "link": "https://wikipedia.org/wiki/Witch-hunt"}]}, {"year": "1666", "text": "Second Anglo-Dutch War: Rear Admiral <PERSON> leads a raid on the Dutch island of Terschelling, destroying 150 merchant ships, an act later known as \"<PERSON>'s Bonfire\".", "html": "1666 - <a href=\"https://wikipedia.org/wiki/Second_Anglo-Dutch_War\" title=\"Second Anglo-Dutch War\">Second Anglo-Dutch War</a>: Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a> leads a raid on the Dutch island of <a href=\"https://wikipedia.org/wiki/Terschelling\" title=\"Terschelling\">Terschelling</a>, destroying 150 merchant ships, an act later known as \"<a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Bonfire\" title=\"<PERSON>'s Bonfire\"><PERSON>'s Bonfire</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Anglo-Dutch_War\" title=\"Second Anglo-Dutch War\">Second Anglo-Dutch War</a>: Rear Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)\" title=\"<PERSON> (Royal Navy officer)\"><PERSON></a> leads a raid on the Dutch island of <a href=\"https://wikipedia.org/wiki/Terschelling\" title=\"Terschelling\">Terschelling</a>, destroying 150 merchant ships, an act later known as \"<a href=\"https://wikipedia.org/wiki/<PERSON>%27s_Bonfire\" title=\"<PERSON>'s Bonfire\"><PERSON>'s Bonfire</a>\".", "links": [{"title": "Second Anglo-Dutch War", "link": "https://wikipedia.org/wiki/Second_Anglo-Dutch_War"}, {"title": "<PERSON> (Royal Navy officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Royal_Navy_officer)"}, {"title": "Terschelling", "link": "https://wikipedia.org/wiki/Terschelling"}, {"title": "<PERSON>'s Bonfire", "link": "https://wikipedia.org/wiki/Holmes%27s_Bonfire"}]}, {"year": "1692", "text": "Salem witch trials: In Salem, province of Massachusetts Bay, five people, one woman and four men, including a clergyman, are executed after being convicted of witchcraft.", "html": "1692 - <a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>: In <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, province of Massachusetts Bay</a>, five people, one woman and four men, including a clergyman, are executed after being convicted of witchcraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salem_witch_trials\" title=\"Salem witch trials\">Salem witch trials</a>: In <a href=\"https://wikipedia.org/wiki/Salem,_Massachusetts\" title=\"Salem, Massachusetts\">Salem, province of Massachusetts Bay</a>, five people, one woman and four men, including a clergyman, are executed after being convicted of witchcraft.", "links": [{"title": "Salem witch trials", "link": "https://wikipedia.org/wiki/Salem_witch_trials"}, {"title": "Salem, Massachusetts", "link": "https://wikipedia.org/wiki/Salem,_Massachusetts"}]}, {"year": "1745", "text": "Prince <PERSON> raises his standard in Glenfinnan: The start of the Second Jacobite Rebellion, known as \"the 45\".", "html": "1745 - Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> raises his standard in <a href=\"https://wikipedia.org/wiki/Glenfinnan\" title=\"Glenfinnan\">Glen<PERSON>nan</a>: The start of the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">Second Jacobite Rebellion</a>, known as \"the 45\".", "no_year_html": "Prince <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> raises his standard in <a href=\"https://wikipedia.org/wiki/Glenfinnan\" title=\"Glenfinnan\">Glenfinnan</a>: The start of the <a href=\"https://wikipedia.org/wiki/Jacobite_rising_of_1745\" title=\"Jacobite rising of 1745\">Second Jacobite Rebellion</a>, known as \"the 45\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Glen<PERSON>nan"}, {"title": "Jacobite rising of 1745", "link": "https://wikipedia.org/wiki/Jacobite_rising_of_1745"}]}, {"year": "1745", "text": "Ottoman-Persian War: In the Battle of Kars, the Ottoman army is routed by Persian forces led by <PERSON><PERSON>.", "html": "1745 - <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Persian_War_(1743%E2%80%9346)\" class=\"mw-redirect\" title=\"Ottoman-Persian War (1743-46)\">Ottoman-Persian War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Kars_(1745)\" title=\"Battle of Kars (1745)\">Battle of Kars</a>, the Ottoman army is routed by Persian forces led by <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Na<PERSON> Shah\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Persian_War_(1743%E2%80%9346)\" class=\"mw-redirect\" title=\"Ottoman-Persian War (1743-46)\">Ottoman-Persian War</a>: In the <a href=\"https://wikipedia.org/wiki/Battle_of_Kars_(1745)\" title=\"Battle of Kars (1745)\">Battle of Kars</a>, the Ottoman army is routed by Persian forces led by <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>\" title=\"Na<PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Ottoman-Persian War (1743-46)", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Persian_War_(1743%E2%80%9346)"}, {"title": "Battle of Kars (1745)", "link": "https://wikipedia.org/wiki/Battle_of_Kars_(1745)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1759", "text": "Battle of Lagos: Naval battle during the Seven Years' War between Great Britain and France.", "html": "1759 - <a href=\"https://wikipedia.org/wiki/Battle_of_Lagos\" title=\"Battle of Lagos\">Battle of Lagos</a>: Naval battle during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a> between Great Britain and France.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Lagos\" title=\"Battle of Lagos\">Battle of Lagos</a>: Naval battle during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a> between Great Britain and France.", "links": [{"title": "Battle of Lagos", "link": "https://wikipedia.org/wiki/Battle_of_Lagos"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1772", "text": "<PERSON> of Sweden stages a coup d'état, in which he assumes power and enacts a new constitution that divides power between the Riksdag and the King.", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Gustav III of Sweden\"><PERSON> of Sweden</a> stages <a href=\"https://wikipedia.org/wiki/Revolution_of_1772\" title=\"Revolution of 1772\">a coup d'état</a>, in which he assumes power and enacts a new constitution that divides power between the <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">Riksdag</a> and the King.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Gustav III of Sweden\"><PERSON> of Sweden</a> stages <a href=\"https://wikipedia.org/wiki/Revolution_of_1772\" title=\"Revolution of 1772\">a coup d'état</a>, in which he assumes power and enacts a new constitution that divides power between the <a href=\"https://wikipedia.org/wiki/Riksdag_of_the_Estates\" title=\"Riksdag of the Estates\">Riksdag</a> and the King.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Revolution of 1772", "link": "https://wikipedia.org/wiki/Revolution_of_1772"}, {"title": "Riksdag of the Estates", "link": "https://wikipedia.org/wiki/Riksdag_of_the_Estates"}]}, {"year": "1782", "text": "American Revolutionary War: Battle of Blue Licks: The last major engagement of the war, almost ten months after the surrender of the British commander <PERSON> following the Siege of Yorktown.", "html": "1782 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Blue_Licks\" title=\"Battle of Blue Licks\">Battle of Blue Licks</a>: The last major engagement of the war, almost ten months after the surrender of the British <a href=\"https://wikipedia.org/wiki/Commander\" title=\"Commander\">commander</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> following the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">Siege of Yorktown</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Blue_Licks\" title=\"Battle of Blue Licks\">Battle of Blue Licks</a>: The last major engagement of the war, almost ten months after the surrender of the British <a href=\"https://wikipedia.org/wiki/Commander\" title=\"Commander\">commander</a> <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st <PERSON>\"><PERSON></a> following the <a href=\"https://wikipedia.org/wiki/Siege_of_Yorktown\" title=\"Siege of Yorktown\">Siege of Yorktown</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Blue Licks", "link": "https://wikipedia.org/wiki/Battle_of_Blue_Licks"}, {"title": "Commander", "link": "https://wikipedia.org/wiki/Commander"}, {"title": "<PERSON>, 1st Marquess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_<PERSON>"}, {"title": "Siege of Yorktown", "link": "https://wikipedia.org/wiki/Siege_of_Yorktown"}]}, {"year": "1812", "text": "War of 1812: American frigate USS Constitution defeats the British frigate HMS Guerriere off the coast of Nova Scotia, Canada earning the nickname \"Old Ironsides\".", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American frigate <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> <a href=\"https://wikipedia.org/wiki/USS_Constitution_vs_HMS_Guerriere\" title=\"USS Constitution vs HMS Guerriere\">defeats</a> the British frigate <a href=\"https://wikipedia.org/wiki/HMS_Guerriere_(1806)\" title=\"HMS Guerriere (1806)\">HMS <i>Guerriere</i></a> off the coast of <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada earning the nickname \"Old Ironsides\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American frigate <a href=\"https://wikipedia.org/wiki/USS_Constitution\" title=\"USS Constitution\">USS <i>Constitution</i></a> <a href=\"https://wikipedia.org/wiki/USS_Constitution_vs_HMS_Guerriere\" title=\"USS Constitution vs HMS Guerriere\">defeats</a> the British frigate <a href=\"https://wikipedia.org/wiki/HMS_Guerriere_(1806)\" title=\"HMS Guerriere (1806)\">HMS <i>Guerriere</i></a> off the coast of <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a>, Canada earning the nickname \"Old Ironsides\".", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "USS Constitution", "link": "https://wikipedia.org/wiki/USS_Constitution"}, {"title": "USS Constitution vs HMS Guerriere", "link": "https://wikipedia.org/wiki/USS_Constitution_vs_HMS_Guerriere"}, {"title": "HMS Guerriere (1806)", "link": "https://wikipedia.org/wiki/HMS_Guerriere_(1806)"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}]}, {"year": "1813", "text": "<PERSON><PERSON><PERSON><PERSON> joins Argentina's Second Triumvirate.", "html": "1813 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> joins Argentina's <a href=\"https://wikipedia.org/wiki/Second_Triumvirate_(Argentina)\" title=\"Second Triumvirate (Argentina)\">Second Triumvirate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">G<PERSON><PERSON><PERSON></a> joins Argentina's <a href=\"https://wikipedia.org/wiki/Second_Triumvirate_(Argentina)\" title=\"Second Triumvirate (Argentina)\">Second Triumvirate</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ger<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Second Triumvirate (Argentina)", "link": "https://wikipedia.org/wiki/Second_Triumvirate_(Argentina)"}]}, {"year": "1839", "text": "The French government announces that <PERSON>'s photographic process is a gift \"free to the world\".", "html": "1839 - The French government announces that <a href=\"https://wikipedia.org/wiki/Louis<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Daguerreotype\" title=\"Daguerreotype\">photographic process</a> is a gift \"free to the world\".", "no_year_html": "The French government announces that <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Daguerreotype\" title=\"Daguerreotype\">photographic process</a> is a gift \"free to the world\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Daguerreotype", "link": "https://wikipedia.org/wiki/Daguerreotype"}]}, {"year": "1848", "text": "California Gold Rush: The New York Herald breaks the news to the East Coast of the United States of the gold rush in California (although the rush started in January).", "html": "1848 - <a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>: The <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> breaks the news to the <a href=\"https://wikipedia.org/wiki/East_Coast_of_the_United_States\" title=\"East Coast of the United States\">East Coast of the United States</a> of the <a href=\"https://wikipedia.org/wiki/Gold_rush\" title=\"Gold rush\">gold rush</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> (although the rush started in January).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>: The <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i> breaks the news to the <a href=\"https://wikipedia.org/wiki/East_Coast_of_the_United_States\" title=\"East Coast of the United States\">East Coast of the United States</a> of the <a href=\"https://wikipedia.org/wiki/Gold_rush\" title=\"Gold rush\">gold rush</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> (although the rush started in January).", "links": [{"title": "California Gold Rush", "link": "https://wikipedia.org/wiki/California_Gold_Rush"}, {"title": "New York Herald", "link": "https://wikipedia.org/wiki/New_York_Herald"}, {"title": "East Coast of the United States", "link": "https://wikipedia.org/wiki/East_Coast_of_the_United_States"}, {"title": "Gold rush", "link": "https://wikipedia.org/wiki/Gold_rush"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}]}, {"year": "1854", "text": "The First Sioux War begins when United States Army soldiers kill Lakota chief <PERSON><PERSON><PERSON> <PERSON> and in return are massacred.", "html": "1854 - The <a href=\"https://wikipedia.org/wiki/First_Sioux_War\" class=\"mw-redirect\" title=\"First Sioux War\">First Sioux War</a> begins when <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> soldiers kill <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> chief <a href=\"https://wikipedia.org/wiki/Conquering_Bear\" title=\"Conquering Bear\">Conquering Bear</a> and in return are <a href=\"https://wikipedia.org/wiki/Grattan_massacre\" title=\"Grattan massacre\">massacred</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/First_Sioux_War\" class=\"mw-redirect\" title=\"First Sioux War\">First Sioux War</a> begins when <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> soldiers kill <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> chief <a href=\"https://wikipedia.org/wiki/Conquering_Bear\" title=\"Conquering Bear\">Conquering Bear</a> and in return are <a href=\"https://wikipedia.org/wiki/Grattan_massacre\" title=\"Grattan massacre\">massacred</a>.", "links": [{"title": "First Sioux War", "link": "https://wikipedia.org/wiki/First_Sioux_War"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "Conquering Bear", "link": "https://wikipedia.org/wiki/Conquering_<PERSON>"}, {"title": "Grattan massacre", "link": "https://wikipedia.org/wiki/Grattan_massacre"}]}, {"year": "1861", "text": "First ascent of <PERSON><PERSON>, fifth highest summit in the Alps.", "html": "1861 - First ascent of <a href=\"https://wikipedia.org/wiki/Weisshorn\" title=\"Weisshorn\">Weisshorn</a>, fifth highest summit in the Alps.", "no_year_html": "First ascent of <a href=\"https://wikipedia.org/wiki/Weisshorn\" title=\"Weisshorn\">Weisshorn</a>, fifth highest summit in the Alps.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1862", "text": "Dakota War: During an uprising in Minnesota, Lakota warriors decide not to attack heavily defended Fort Ridgely and instead turn to the settlement of New Ulm, killing white settlers along the way.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Dakota_War_of_1862\" title=\"Dakota War of 1862\">Dakota War</a>: During an uprising in <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> warriors decide not to attack heavily defended <a href=\"https://wikipedia.org/wiki/Fort_Ridgely\" title=\"Fort Ridgely\">Fort Ridgely</a> and instead turn to the settlement of <a href=\"https://wikipedia.org/wiki/New_Ulm,_Minnesota\" title=\"New Ulm, Minnesota\">New Ulm</a>, killing white settlers along the way.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dakota_War_of_1862\" title=\"Dakota War of 1862\">Dakota War</a>: During an uprising in <a href=\"https://wikipedia.org/wiki/Minnesota\" title=\"Minnesota\">Minnesota</a>, <a href=\"https://wikipedia.org/wiki/Lakota_people\" title=\"Lakota people\">Lakota</a> warriors decide not to attack heavily defended <a href=\"https://wikipedia.org/wiki/Fort_Ridgely\" title=\"Fort Ridgely\">Fort Ridgely</a> and instead turn to the settlement of <a href=\"https://wikipedia.org/wiki/New_Ulm,_Minnesota\" title=\"New Ulm, Minnesota\">New Ulm</a>, killing white settlers along the way.", "links": [{"title": "Dakota War of 1862", "link": "https://wikipedia.org/wiki/Dakota_War_of_1862"}, {"title": "Minnesota", "link": "https://wikipedia.org/wiki/Minnesota"}, {"title": "Lakota people", "link": "https://wikipedia.org/wiki/Lakota_people"}, {"title": "Fort Ridgely", "link": "https://wikipedia.org/wiki/Fort_Ridgely"}, {"title": "New Ulm, Minnesota", "link": "https://wikipedia.org/wiki/New_Ulm,_Minnesota"}]}, {"year": "1903", "text": "The Transfiguration Uprising breaks out in East Thrace, resulting in the establishment of the Strandzha Commune.", "html": "1903 - The <a href=\"https://wikipedia.org/wiki/Ilinden%E2%80%93Preobrazhenie_Uprising\" title=\"Ilinden-Preobrazhenie Uprising\">Transfiguration Uprising</a> breaks out in <a href=\"https://wikipedia.org/wiki/East_Thrace\" title=\"East Thrace\">East Thrace</a>, resulting in the establishment of the <a href=\"https://wikipedia.org/wiki/Strandzha_Commune\" title=\"Strandzha Commune\">Strandzha Commune</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Ilinden%E2%80%93Preobrazhenie_Uprising\" title=\"Ilinden-Preobrazhenie Uprising\">Transfiguration Uprising</a> breaks out in <a href=\"https://wikipedia.org/wiki/East_Thrace\" title=\"East Thrace\">East Thrace</a>, resulting in the establishment of the <a href=\"https://wikipedia.org/wiki/Strandzha_Commune\" title=\"Strandzha Commune\">Strandzha Commune</a>.", "links": [{"title": "Ilinden-Preobrazhenie Uprising", "link": "https://wikipedia.org/wiki/Ilinden%E2%80%93Preobrazhenie_Uprising"}, {"title": "East Thrace", "link": "https://wikipedia.org/wiki/East_Thrace"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Strandzha_Commune"}]}, {"year": "1909", "text": "The Indianapolis Motor Speedway opens for automobile racing. <PERSON> and his mechanic are killed during the first day's events.", "html": "1909 - The <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis Motor Speedway</a> opens for <a href=\"https://wikipedia.org/wiki/Automobile_racing\" class=\"mw-redirect\" title=\"Automobile racing\">automobile racing</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his mechanic are killed during the first day's events.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Indianapolis_Motor_Speedway\" title=\"Indianapolis Motor Speedway\">Indianapolis Motor Speedway</a> opens for <a href=\"https://wikipedia.org/wiki/Automobile_racing\" class=\"mw-redirect\" title=\"Automobile racing\">automobile racing</a>. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his mechanic are killed during the first day's events.", "links": [{"title": "Indianapolis Motor Speedway", "link": "https://wikipedia.org/wiki/Indianapolis_Motor_Speedway"}, {"title": "Automobile racing", "link": "https://wikipedia.org/wiki/Automobile_racing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "The Tambov Rebellion breaks out, in response to the Bolshevik policy of Prodrazvyorstka.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Tambov_Rebellion\" title=\"Tambov Rebellion\">Tambov Rebellion</a> breaks out, in response to the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> policy of <i><a href=\"https://wikipedia.org/wiki/Prodrazvyorstka\" class=\"mw-redirect\" title=\"Prodrazvyorstka\">Prodrazvyorstka</a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Tambov_Rebellion\" title=\"Tambov Rebellion\">Tambov Rebellion</a> breaks out, in response to the <a href=\"https://wikipedia.org/wiki/Bolshevik\" class=\"mw-redirect\" title=\"Bolshevik\">Bolshevik</a> policy of <i><a href=\"https://wikipedia.org/wiki/Prodrazvyorstka\" class=\"mw-redirect\" title=\"Prodrazvyorstka\">Prodrazvyorstka</a></i>.", "links": [{"title": "Tambov Rebellion", "link": "https://wikipedia.org/wiki/Tambov_Rebellion"}, {"title": "Bolshevik", "link": "https://wikipedia.org/wiki/Bolshevik"}, {"title": "Prodrazvyorstka", "link": "https://wikipedia.org/wiki/Prodrazvyorstka"}]}, {"year": "1927", "text": "Patriarch <PERSON><PERSON><PERSON> of Moscow proclaims the declaration of loyalty of the Russian Orthodox Church to the Soviet Union.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON><PERSON> of Moscow</a> proclaims the declaration of loyalty of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a> to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patriarch_<PERSON><PERSON><PERSON>_of_Moscow\" title=\"Patriarch <PERSON><PERSON><PERSON> of Moscow\">Patriarch <PERSON><PERSON><PERSON> of Moscow</a> proclaims the declaration of loyalty of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a> to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Patriarch <PERSON><PERSON><PERSON> of Moscow", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Moscow"}, {"title": "Russian Orthodox Church", "link": "https://wikipedia.org/wiki/Russian_Orthodox_Church"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1934", "text": "The first All-American Soap Box Derby is held in Dayton, Ohio.", "html": "1934 - The first All-American <a href=\"https://wikipedia.org/wiki/Soap_Box_Derby\" title=\"Soap Box Derby\">Soap Box Derby</a> is held in <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>.", "no_year_html": "The first All-American <a href=\"https://wikipedia.org/wiki/Soap_Box_Derby\" title=\"Soap Box Derby\">Soap Box Derby</a> is held in <a href=\"https://wikipedia.org/wiki/Dayton,_Ohio\" title=\"Dayton, Ohio\">Dayton, Ohio</a>.", "links": [{"title": "Soap Box Derby", "link": "https://wikipedia.org/wiki/Soap_Box_Derby"}, {"title": "Dayton, Ohio", "link": "https://wikipedia.org/wiki/Dayton,_Ohio"}]}, {"year": "1934", "text": "The German referendum of 1934 approves <PERSON>'s appointment as head of state with the title of Führer.", "html": "1934 - The <a href=\"https://wikipedia.org/wiki/German_referendum,_1934\" class=\"mw-redirect\" title=\"German referendum, 1934\">German referendum of 1934</a> approves <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s appointment as head of state with the title of <a href=\"https://wikipedia.org/wiki/F%C3%BChrer\" title=\"Führer\">Führer</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/German_referendum,_1934\" class=\"mw-redirect\" title=\"German referendum, 1934\">German referendum of 1934</a> approves <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s appointment as head of state with the title of <a href=\"https://wikipedia.org/wiki/F%C3%BChrer\" title=\"Führer\">Führer</a>.", "links": [{"title": "German referendum, 1934", "link": "https://wikipedia.org/wiki/German_referendum,_1934"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Führer", "link": "https://wikipedia.org/wiki/F%C3%<PERSON>hrer"}]}, {"year": "1936", "text": "The Great Purge of the Soviet Union begins when the first of the Moscow Trials is convened.", "html": "1936 - The <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins when the first of the <a href=\"https://wikipedia.org/wiki/Moscow_Trials\" class=\"mw-redirect\" title=\"Moscow Trials\">Moscow Trials</a> is convened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a> of the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> begins when the first of the <a href=\"https://wikipedia.org/wiki/Moscow_Trials\" class=\"mw-redirect\" title=\"Moscow Trials\">Moscow Trials</a> is convened.", "links": [{"title": "Great Purge", "link": "https://wikipedia.org/wiki/Great_Purge"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Moscow Trials", "link": "https://wikipedia.org/wiki/Moscow_Trials"}]}, {"year": "1940", "text": "First flight of the B-25 Mitchell medium bomber.", "html": "1940 - First flight of the <a href=\"https://wikipedia.org/wiki/North_American_B-25_Mitchell\" title=\"North American B-25 Mitchell\">B-25 <PERSON></a> <a href=\"https://wikipedia.org/wiki/Medium_bomber\" title=\"Medium bomber\">medium bomber</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/North_American_B-25_Mitchell\" title=\"North American B-25 Mitchell\">B-25 <PERSON></a> <a href=\"https://wikipedia.org/wiki/Medium_bomber\" title=\"Medium bomber\">medium bomber</a>.", "links": [{"title": "North American B-25 Mitchell", "link": "https://wikipedia.org/wiki/North_American_B-25_<PERSON>"}, {"title": "Medium bomber", "link": "https://wikipedia.org/wiki/Medium_bomber"}]}, {"year": "1941", "text": "Germany and Romania sign the Tiraspol Agreement, rendering the region of Transnistria under control of the latter.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> sign the <a href=\"https://wikipedia.org/wiki/Tiraspol_Agreement\" title=\"Tiraspol Agreement\">Tiraspol Agreement</a>, rendering the region of <a href=\"https://wikipedia.org/wiki/Transnistria_Governorate\" title=\"Transnistria Governorate\">Transnistria</a> under control of the latter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a> sign the <a href=\"https://wikipedia.org/wiki/Tiraspol_Agreement\" title=\"Tiraspol Agreement\">Tiraspol Agreement</a>, rendering the region of <a href=\"https://wikipedia.org/wiki/Transnistria_Governorate\" title=\"Transnistria Governorate\">Transnistria</a> under control of the latter.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Tiraspol Agreement", "link": "https://wikipedia.org/wiki/Tiraspol_Agreement"}, {"title": "Transnistria Governorate", "link": "https://wikipedia.org/wiki/Transnistria_Governorate"}]}, {"year": "1942", "text": "World War II: Operation Jubilee (The Dieppe Raid): The 2nd Canadian Infantry Division leads an amphibious assault by allied forces on Dieppe, France and fails.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dieppe_Raid\" title=\"Dieppe Raid\">Operation Jubilee</a> (The Dieppe Raid): The <a href=\"https://wikipedia.org/wiki/2nd_Canadian_Infantry_Division\" class=\"mw-redirect\" title=\"2nd Canadian Infantry Division\">2nd Canadian Infantry Division</a> leads an <a href=\"https://wikipedia.org/wiki/Amphibious_warfare\" title=\"Amphibious warfare\">amphibious assault</a> by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">allied forces</a> on <a href=\"https://wikipedia.org/wiki/Dieppe\" title=\"Dieppe\">Dieppe</a>, France and fails.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dieppe_Raid\" title=\"Dieppe Raid\">Operation Jubilee</a> (The Dieppe Raid): The <a href=\"https://wikipedia.org/wiki/2nd_Canadian_Infantry_Division\" class=\"mw-redirect\" title=\"2nd Canadian Infantry Division\">2nd Canadian Infantry Division</a> leads an <a href=\"https://wikipedia.org/wiki/Amphibious_warfare\" title=\"Amphibious warfare\">amphibious assault</a> by <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">allied forces</a> on <a href=\"https://wikipedia.org/wiki/Dieppe\" title=\"Dieppe\">Dieppe</a>, France and fails.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Raid"}, {"title": "2nd Canadian Infantry Division", "link": "https://wikipedia.org/wiki/2nd_Canadian_Infantry_Division"}, {"title": "Amphibious warfare", "link": "https://wikipedia.org/wiki/Amphibious_warfare"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dieppe"}]}, {"year": "1944", "text": "World War II: Liberation of Paris: Paris, France rises against German occupation with the help of Allied troops.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">Liberation of Paris</a>: Paris, France rises against German occupation with the help of Allied troops.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Liberation_of_Paris\" title=\"Liberation of Paris\">Liberation of Paris</a>: Paris, France rises against German occupation with the help of Allied troops.", "links": [{"title": "Liberation of Paris", "link": "https://wikipedia.org/wiki/Liberation_of_Paris"}]}, {"year": "1945", "text": "August Revolution: Viet Minh led by <PERSON> take power in Hanoi, Vietnam.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/August_Revolution\" title=\"August Revolution\">August Revolution</a>: <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\"><PERSON><PERSON></a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take power in Hanoi, Vietnam.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Revolution\" title=\"August Revolution\">August Revolution</a>: <a href=\"https://wikipedia.org/wiki/V<PERSON>_Minh\" title=\"Viet Minh\"><PERSON><PERSON></a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> take power in Hanoi, Vietnam.", "links": [{"title": "August Revolution", "link": "https://wikipedia.org/wiki/August_Revolution"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "Cold War: The CIA and MI6 help to overthrow the government of <PERSON> in Iran and reinstate the Shah <PERSON>.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> and <a href=\"https://wikipedia.org/wiki/Secret_Intelligence_Service\" class=\"mw-redirect\" title=\"Secret Intelligence Service\">MI6</a> help to <a href=\"https://wikipedia.org/wiki/1953_Iranian_coup_d%27%C3%A9tat\" title=\"1953 Iranian coup d'état\">overthrow</a> the government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Iran and reinstate the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\">Shah</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> and <a href=\"https://wikipedia.org/wiki/Secret_Intelligence_Service\" class=\"mw-redirect\" title=\"Secret Intelligence Service\">MI6</a> help to <a href=\"https://wikipedia.org/wiki/1953_Iranian_coup_d%27%C3%A9tat\" title=\"1953 Iranian coup d'état\">overthrow</a> the government of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in Iran and reinstate the <a href=\"https://wikipedia.org/wiki/Shah\" title=\"<PERSON>\">Shah</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "Secret Intelligence Service", "link": "https://wikipedia.org/wiki/Secret_Intelligence_Service"}, {"title": "1953 Iranian coup d'état", "link": "https://wikipedia.org/wiki/1953_Iranian_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shah"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "In the Northeast United States, severe flooding caused by Hurricane <PERSON>, claims 200 lives.", "html": "1955 - In the <a href=\"https://wikipedia.org/wiki/Northeastern_United_States\" title=\"Northeastern United States\">Northeast United States</a>, severe flooding caused by <a href=\"https://wikipedia.org/wiki/Hurricane_Diane\" title=\"Hurricane Diane\">Hurricane <PERSON></a>, claims 200 lives.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Northeastern_United_States\" title=\"Northeastern United States\">Northeast United States</a>, severe flooding caused by <a href=\"https://wikipedia.org/wiki/Hurricane_Diane\" title=\"Hurricane Diane\">Hurricane <PERSON></a>, claims 200 lives.", "links": [{"title": "Northeastern United States", "link": "https://wikipedia.org/wiki/Northeastern_United_States"}, {"title": "Hurricane Diane", "link": "https://wikipedia.org/wiki/Hurricane_Diane"}]}, {"year": "1960", "text": "Cold War: In Moscow, Russia, Soviet Union, downed American U-2 pilot <PERSON> is sentenced to ten years imprisonment by the Soviet Union for espionage.", "html": "1960 - Cold War: In Moscow, Russia, Soviet Union, downed American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U-2</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to ten years imprisonment by the Soviet Union for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "no_year_html": "Cold War: In Moscow, Russia, Soviet Union, downed American <a href=\"https://wikipedia.org/wiki/Lockheed_U-2\" title=\"Lockheed U-2\">U-2</a> pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sentenced to ten years imprisonment by the Soviet Union for <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a>.", "links": [{"title": "Lockheed U-2", "link": "https://wikipedia.org/wiki/Lockheed_U-2"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}]}, {"year": "1960", "text": "Sputnik program: Korabl-Sputnik 2: The Soviet Union launches the satellite with the dogs <PERSON><PERSON> and <PERSON>rel<PERSON>, 40 mice, two rats and a variety of plants.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/List_of_spacecraft_called_Sputnik\" title=\"List of spacecraft called Sputnik\">Sputnik program</a>: <i><a href=\"https://wikipedia.org/wiki/Korabl-Sputnik_2\" title=\"Korabl-Sputnik 2\">Korabl-Sputnik 2</a></i>: The Soviet Union launches the satellite with the <a href=\"https://wikipedia.org/wiki/Dog\" title=\"Dog\">dogs</a> <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs#Belka_and_Strelka\" title=\"Soviet space dogs\">Belka and Strelka</a>, 40 mice, two rats and a variety of plants.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_spacecraft_called_Sputnik\" title=\"List of spacecraft called Sputnik\">Sputnik program</a>: <i><a href=\"https://wikipedia.org/wiki/Korabl-Sputnik_2\" title=\"Korabl-Sputnik 2\">Korabl-Sputnik 2</a></i>: The Soviet Union launches the satellite with the <a href=\"https://wikipedia.org/wiki/Dog\" title=\"Dog\">dogs</a> <a href=\"https://wikipedia.org/wiki/Soviet_space_dogs#Belka_and_Strelka\" title=\"Soviet space dogs\">Belka and Strelka</a>, 40 mice, two rats and a variety of plants.", "links": [{"title": "List of spacecraft called Sputnik", "link": "https://wikipedia.org/wiki/List_of_spacecraft_called_Sputnik"}, {"title": "Korabl-Sputnik 2", "link": "https://wikipedia.org/wiki/Korabl-Sputnik_2"}, {"title": "Dog", "link": "https://wikipedia.org/wiki/Dog"}, {"title": "Soviet space dogs", "link": "https://wikipedia.org/wiki/Soviet_space_dogs#Bel<PERSON>_and_Strelka"}]}, {"year": "1964", "text": "Syncom 3, the first geostationary communication satellite, is launched. Two months later, it would enable live coverage of the 1964 Summer Olympics.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Syncom\" title=\"Syncom\">Syncom</a> 3, the first <a href=\"https://wikipedia.org/wiki/Geostationary_communication_satellite\" class=\"mw-redirect\" title=\"Geostationary communication satellite\">geostationary communication satellite</a>, is launched. Two months later, it would enable live coverage of the <a href=\"https://wikipedia.org/wiki/1964_Summer_Olympics\" title=\"1964 Summer Olympics\">1964 Summer Olympics</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syncom\" title=\"Syncom\">Syncom</a> 3, the first <a href=\"https://wikipedia.org/wiki/Geostationary_communication_satellite\" class=\"mw-redirect\" title=\"Geostationary communication satellite\">geostationary communication satellite</a>, is launched. Two months later, it would enable live coverage of the <a href=\"https://wikipedia.org/wiki/1964_Summer_Olympics\" title=\"1964 Summer Olympics\">1964 Summer Olympics</a>.", "links": [{"title": "Syncom", "link": "https://wikipedia.org/wiki/Syncom"}, {"title": "Geostationary communication satellite", "link": "https://wikipedia.org/wiki/Geostationary_communication_satellite"}, {"title": "1964 Summer Olympics", "link": "https://wikipedia.org/wiki/1964_Summer_Olympics"}]}, {"year": "1965", "text": "Japanese prime minister <PERSON><PERSON><PERSON> becomes the first post-World War II sitting prime minister to visit Okinawa Prefecture.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese prime minister</a> <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_Sat%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first post-World War II sitting prime minister to visit <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa Prefecture</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Japanese prime minister</a> <a href=\"https://wikipedia.org/wiki/Eisa<PERSON>_Sat%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first post-World War II sitting prime minister to visit <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa Prefecture</a>.", "links": [{"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eisaku_Sat%C5%8D"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}]}, {"year": "1978", "text": "In Iran, the Cinema Rex fire causes more than 400 deaths.", "html": "1978 - In Iran, the <a href=\"https://wikipedia.org/wiki/Cinema_Rex_fire\" title=\"Cinema Rex fire\">Cinema Rex fire</a> causes more than 400 deaths.", "no_year_html": "In Iran, the <a href=\"https://wikipedia.org/wiki/Cinema_Rex_fire\" title=\"Cinema Rex fire\">Cinema Rex fire</a> causes more than 400 deaths.", "links": [{"title": "Cinema Rex fire", "link": "https://wikipedia.org/wiki/Cinema_Rex_fire"}]}, {"year": "1980", "text": "Saudia Flight 163, a Lockheed L-1011 TriStar burns after making an emergency landing at Riyadh International Airport in Riyadh, Saudi Arabia, killing 301 people.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Saudia_Flight_163\" title=\"Saudia Flight 163\">Saudia Flight 163</a>, a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1011_TriStar\" title=\"Lockheed L-1011 TriStar\">Lockheed L-1011 TriStar</a> burns after making an emergency landing at <a href=\"https://wikipedia.org/wiki/Riyadh_Air_Base\" class=\"mw-redirect\" title=\"Riyadh Air Base\">Riyadh International Airport</a> in <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing 301 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saudia_Flight_163\" title=\"Saudia Flight 163\">Saudia Flight 163</a>, a <a href=\"https://wikipedia.org/wiki/Lockheed_L-1011_TriStar\" title=\"Lockheed L-1011 TriStar\">Lockheed L-1011 TriStar</a> burns after making an emergency landing at <a href=\"https://wikipedia.org/wiki/Riyadh_Air_Base\" class=\"mw-redirect\" title=\"Riyadh Air Base\">Riyadh International Airport</a> in <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing 301 people.", "links": [{"title": "Saudia Flight 163", "link": "https://wikipedia.org/wiki/Saudia_Flight_163"}, {"title": "Lockheed L-1011 TriStar", "link": "https://wikipedia.org/wiki/Lockheed_L-1011_TriStar"}, {"title": "Riyadh Air Base", "link": "https://wikipedia.org/wiki/Riyadh_Air_Base"}, {"title": "Riyadh", "link": "https://wikipedia.org/wiki/Riyadh"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "1980", "text": "Otłoczyn railway accident: In Poland's worst post-war railway accident, 67 people lose their lives and a further 62 are injured.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Ot%C5%82oczyn_railway_accident\" title=\"Otłoczyn railway accident\">Otłoczyn railway accident</a>: In Poland's worst post-war railway accident, 67 people lose their lives and a further 62 are injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ot%C5%82oczyn_railway_accident\" title=\"Otłoczyn railway accident\">Otłoczyn railway accident</a>: In Poland's worst post-war railway accident, 67 people lose their lives and a further 62 are injured.", "links": [{"title": "Otłoczyn railway accident", "link": "https://wikipedia.org/wiki/Ot%C5%82oczyn_railway_accident"}]}, {"year": "1981", "text": "Gulf of Sidra Incident: United States F-14A Tomcat fighters intercept and shoot down two Libyan Sukhoi Su-22 fighter jets over the Gulf of Sidra.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Gulf_of_Sidra_incident_(1981)\" title=\"Gulf of Sidra incident (1981)\">Gulf of Sidra Incident</a>: United States <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14A Tomcat</a> fighters intercept and shoot down two <a href=\"https://wikipedia.org/wiki/Libyan_Air_Force_(1951%E2%80%932011)\" class=\"mw-redirect\" title=\"Libyan Air Force (1951-2011)\">Libyan</a> <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-17\" title=\"Sukhoi Su-17\">Sukhoi Su-22</a> fighter jets over the <a href=\"https://wikipedia.org/wiki/Gulf_of_Sidra\" title=\"Gulf of Sidra\">Gulf of Sidra</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_of_Sidra_incident_(1981)\" title=\"Gulf of Sidra incident (1981)\">Gulf of Sidra Incident</a>: United States <a href=\"https://wikipedia.org/wiki/Grumman_F-14_Tomcat\" title=\"Grumman F-14 Tomcat\">F-14A Tomcat</a> fighters intercept and shoot down two <a href=\"https://wikipedia.org/wiki/Libyan_Air_Force_(1951%E2%80%932011)\" class=\"mw-redirect\" title=\"Libyan Air Force (1951-2011)\">Libyan</a> <a href=\"https://wikipedia.org/wiki/Sukhoi_Su-17\" title=\"Sukhoi Su-17\">Sukhoi Su-22</a> fighter jets over the <a href=\"https://wikipedia.org/wiki/Gulf_of_Sidra\" title=\"Gulf of Sidra\">Gulf of Sidra</a>.", "links": [{"title": "Gulf of Sidra incident (1981)", "link": "https://wikipedia.org/wiki/Gulf_of_Sidra_incident_(1981)"}, {"title": "Grumman F-14 Tomcat", "link": "https://wikipedia.org/wiki/G<PERSON>man_F-14_<PERSON><PERSON>"}, {"title": "Libyan Air Force (1951-2011)", "link": "https://wikipedia.org/wiki/Libyan_Air_Force_(1951%E2%80%932011)"}, {"title": "Sukhoi Su-17", "link": "https://wikipedia.org/wiki/Sukhoi_Su-17"}, {"title": "Gulf of Sidra", "link": "https://wikipedia.org/wiki/Gulf_of_Sidra"}]}, {"year": "1987", "text": "Hungerford massacre: In the United Kingdom, <PERSON> kills sixteen people with a semi-automatic rifle and then commits suicide.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Hungerford_massacre\" title=\"Hungerford massacre\">Hungerford massacre</a>: In the United Kingdom, <PERSON> kills sixteen people with a <a href=\"https://wikipedia.org/wiki/Semi-automatic_rifle\" title=\"Semi-automatic rifle\">semi-automatic rifle</a> and then commits suicide.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hungerford_massacre\" title=\"Hungerford massacre\">Hungerford massacre</a>: In the United Kingdom, <PERSON> kills sixteen people with a <a href=\"https://wikipedia.org/wiki/Semi-automatic_rifle\" title=\"Semi-automatic rifle\">semi-automatic rifle</a> and then commits suicide.", "links": [{"title": "Hungerford massacre", "link": "https://wikipedia.org/wiki/Hungerford_massacre"}, {"title": "Semi-automatic rifle", "link": "https://wikipedia.org/wiki/Semi-automatic_rifle"}]}, {"year": "1989", "text": "Polish president <PERSON><PERSON><PERSON><PERSON><PERSON> nominates Solidarity activist <PERSON><PERSON><PERSON> to be the first non-communist prime minister in 42 years.", "html": "1989 - Polish president <a href=\"https://wikipedia.org/wiki/Wojcie<PERSON>_Jaruzelski\" title=\"Wojcie<PERSON> Jaruzel<PERSON>\">Wojcie<PERSON></a> nominates <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> activist <a href=\"https://wikipedia.org/wiki/Tadeusz_Mazowiecki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to be the first non-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> in 42 years.", "no_year_html": "Polish president <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>zel<PERSON>\" title=\"Wojcie<PERSON>aruzel<PERSON>\">Woj<PERSON><PERSON></a> nominates <a href=\"https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)\" title=\"Solidarity (Polish trade union)\">Solidarity</a> activist <a href=\"https://wikipedia.org/wiki/Tadeusz_Mazowiecki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to be the first non-<a href=\"https://wikipedia.org/wiki/Communism\" title=\"Communism\">communist</a> <a href=\"https://wikipedia.org/wiki/Prime_minister\" title=\"Prime minister\">prime minister</a> in 42 years.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>"}, {"title": "Solidarity (Polish trade union)", "link": "https://wikipedia.org/wiki/Solidarity_(Polish_trade_union)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zowiecki"}, {"title": "Communism", "link": "https://wikipedia.org/wiki/Communism"}, {"title": "Prime minister", "link": "https://wikipedia.org/wiki/Prime_minister"}]}, {"year": "1989", "text": "Several hundred East Germans cross the frontier between Hungary and Austria during the Pan-European Picnic, part of the events that began the process of the Fall of the Berlin Wall.", "html": "1989 - Several hundred <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germans</a> cross the frontier between Hungary and Austria during the <a href=\"https://wikipedia.org/wiki/Pan-European_Picnic\" title=\"Pan-European Picnic\">Pan-European Picnic</a>, part of the events that began the process of the <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">Fall of the Berlin Wall</a>.", "no_year_html": "Several hundred <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germans</a> cross the frontier between Hungary and Austria during the <a href=\"https://wikipedia.org/wiki/Pan-European_Picnic\" title=\"Pan-European Picnic\">Pan-European Picnic</a>, part of the events that began the process of the <a href=\"https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall\" title=\"Fall of the Berlin Wall\">Fall of the Berlin Wall</a>.", "links": [{"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}, {"title": "Pan-European Picnic", "link": "https://wikipedia.org/wiki/Pan-European_Picnic"}, {"title": "Fall of the Berlin Wall", "link": "https://wikipedia.org/wiki/Fall_of_the_Berlin_Wall"}]}, {"year": "1991", "text": "Dissolution of the Soviet Union: The August Coup begins when Soviet President <PERSON> is placed under house arrest while on holiday in the town of Foros, Ukraine.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">Dissolution of the Soviet Union</a>: The <a href=\"https://wikipedia.org/wiki/1991_Soviet_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"1991 Soviet coup d'état attempt\">August <PERSON></a> begins when <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">Soviet President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is placed under <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house arrest</a> while on holiday in the town of <a href=\"https://wikipedia.org/wiki/Foros,_Ukraine\" class=\"mw-redirect\" title=\"Foros, Ukraine\">Foros, Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">Dissolution of the Soviet Union</a>: The <a href=\"https://wikipedia.org/wiki/1991_Soviet_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"1991 Soviet coup d'état attempt\">August <PERSON></a> begins when <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">Soviet President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is placed under <a href=\"https://wikipedia.org/wiki/House_arrest\" title=\"House arrest\">house arrest</a> while on holiday in the town of <a href=\"https://wikipedia.org/wiki/Foros,_Ukraine\" class=\"mw-redirect\" title=\"Foros, Ukraine\">Foros, Ukraine</a>.", "links": [{"title": "Dissolution of the Soviet Union", "link": "https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union"}, {"title": "1991 Soviet coup d'état attempt", "link": "https://wikipedia.org/wiki/1991_Soviet_coup_d%27%C3%A9tat_attempt"}, {"title": "President of the Soviet Union", "link": "https://wikipedia.org/wiki/President_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "House arrest", "link": "https://wikipedia.org/wiki/House_arrest"}, {"title": "Foros, Ukraine", "link": "https://wikipedia.org/wiki/Foros,_Ukraine"}]}, {"year": "1991", "text": "Crown Heights riot begins.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Crown_Heights_riot\" title=\"Crown Heights riot\">Crown Heights riot</a> begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crown_Heights_riot\" title=\"Crown Heights riot\">Crown Heights riot</a> begins.", "links": [{"title": "Crown Heights riot", "link": "https://wikipedia.org/wiki/Crown_Heights_riot"}]}, {"year": "1999", "text": "In Belgrade, Yugoslavia, tens of thousands of Serbians rally to demand the resignation of Federal Republic of Yugoslavia President <PERSON><PERSON><PERSON><PERSON>.", "html": "1999 - In <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>, <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Yugoslavia</a>, tens of thousands of <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbians</a> rally to demand the resignation of <a href=\"https://wikipedia.org/wiki/President_of_Serbia_and_Montenegro\" title=\"President of Serbia and Montenegro\">Federal Republic of Yugoslavia President</a> <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>, <a href=\"https://wikipedia.org/wiki/Serbia_and_Montenegro\" title=\"Serbia and Montenegro\">Yugoslavia</a>, tens of thousands of <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbians</a> rally to demand the resignation of <a href=\"https://wikipedia.org/wiki/President_of_Serbia_and_Montenegro\" title=\"President of Serbia and Montenegro\">Federal Republic of Yugoslavia President</a> <a href=\"https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}, {"title": "Serbia and Montenegro", "link": "https://wikipedia.org/wiki/Serbia_and_Montenegro"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "President of Serbia and Montenegro", "link": "https://wikipedia.org/wiki/President_of_Serbia_and_Montenegro"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Slobodan_Milo%C5%A1evi%C4%87"}]}, {"year": "2002", "text": "Khankala Mi-26 crash: A Russian Mil Mi-26 helicopter carrying troops is hit by a Chechen missile outside Grozny, killing 118 soldiers.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Khankala_Mi-26_crash\" class=\"mw-redirect\" title=\"Khankala Mi-26 crash\">Khankala Mi-26 crash</a>: A Russian <a href=\"https://wikipedia.org/wiki/Mil_Mi-26\" title=\"Mil Mi-26\">Mil Mi-26</a> helicopter carrying troops is hit by a <a href=\"https://wikipedia.org/wiki/Chechens\" title=\"Chechens\">Chechen</a> missile outside <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, killing 118 soldiers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khankala_Mi-26_crash\" class=\"mw-redirect\" title=\"Khankala Mi-26 crash\">Khankala Mi-26 crash</a>: A Russian <a href=\"https://wikipedia.org/wiki/Mil_Mi-26\" title=\"Mil Mi-26\">Mil Mi-26</a> helicopter carrying troops is hit by a <a href=\"https://wikipedia.org/wiki/Chechens\" title=\"Chechens\">Chechen</a> missile outside <a href=\"https://wikipedia.org/wiki/Grozny\" title=\"Grozny\">Grozny</a>, killing 118 soldiers.", "links": [{"title": "Khankala Mi-26 crash", "link": "https://wikipedia.org/wiki/Khankala_Mi-26_crash"}, {"title": "Mil Mi-26", "link": "https://wikipedia.org/wiki/Mil_Mi-26"}, {"title": "Chechens", "link": "https://wikipedia.org/wiki/Chechens"}, {"title": "Grozny", "link": "https://wikipedia.org/wiki/Grozny"}]}, {"year": "2003", "text": "A truck-bomb attack on United Nations headquarters in Iraq kills the agency's top envoy <PERSON><PERSON><PERSON><PERSON> and 21 other employees.", "html": "2003 - A <a href=\"https://wikipedia.org/wiki/Canal_Hotel_bombing\" title=\"Canal Hotel bombing\">truck-bomb attack</a> on <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> headquarters in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> kills the agency's top envoy <a href=\"https://wikipedia.org/wiki/S%C3%A9rgio_<PERSON>ieira_de_Mello\" title=\"<PERSON><PERSON>rg<PERSON>\">S<PERSON>rg<PERSON> V<PERSON>ira <PERSON>lo</a> and 21 other employees.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Canal_Hotel_bombing\" title=\"Canal Hotel bombing\">truck-bomb attack</a> on <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a> headquarters in <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> kills the agency's top envoy <a href=\"https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>ieira_de_Mello\" title=\"Sérgio V<PERSON>ira <PERSON>\"><PERSON><PERSON>rg<PERSON>ira <PERSON>lo</a> and 21 other employees.", "links": [{"title": "Canal Hotel bombing", "link": "https://wikipedia.org/wiki/Canal_Hotel_bombing"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON> bus bombing: A suicide attack on a bus in Jerusalem, planned by Hamas, kills 23 Israelis, seven of them children.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_bus_bombing\" title=\"<PERSON><PERSON><PERSON> bus bombing\"><PERSON><PERSON><PERSON> bus bombing</a>: A suicide attack on a bus in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, planned by <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>, kills 23 Israelis, seven of them children.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_bus_bombing\" title=\"<PERSON><PERSON><PERSON> bus bombing\"><PERSON><PERSON><PERSON> bus bombing</a>: A suicide attack on a bus in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>, planned by <a href=\"https://wikipedia.org/wiki/Hamas\" title=\"Hamas\">Hamas</a>, kills 23 Israelis, seven of them children.", "links": [{"title": "<PERSON><PERSON><PERSON> bus bombing", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_bus_bombing"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Hamas", "link": "https://wikipedia.org/wiki/Hamas"}]}, {"year": "2004", "text": "Google Inc. has its initial public offering on Nasdaq.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google Inc.</a> has its <a href=\"https://wikipedia.org/wiki/Initial_public_offering\" title=\"Initial public offering\">initial public offering</a> on <a href=\"https://wikipedia.org/wiki/Nasdaq\" title=\"Nasdaq\">Nasdaq</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Google\" title=\"Google\">Google Inc.</a> has its <a href=\"https://wikipedia.org/wiki/Initial_public_offering\" title=\"Initial public offering\">initial public offering</a> on <a href=\"https://wikipedia.org/wiki/Nasdaq\" title=\"Nasdaq\">Nasdaq</a>.", "links": [{"title": "Google", "link": "https://wikipedia.org/wiki/Google"}, {"title": "Initial public offering", "link": "https://wikipedia.org/wiki/Initial_public_offering"}, {"title": "Nasdaq", "link": "https://wikipedia.org/wiki/Nasdaq"}]}, {"year": "2005", "text": "The first-ever joint military exercise between Russia and China, called Peace Mission 2005 begins.", "html": "2005 - The first-ever joint military exercise between Russia and China, called <a href=\"https://wikipedia.org/wiki/Peace_Mission_2005\" title=\"Peace Mission 2005\">Peace Mission 2005</a> begins.", "no_year_html": "The first-ever joint military exercise between Russia and China, called <a href=\"https://wikipedia.org/wiki/Peace_Mission_2005\" title=\"Peace Mission 2005\">Peace Mission 2005</a> begins.", "links": [{"title": "Peace Mission 2005", "link": "https://wikipedia.org/wiki/Peace_Mission_2005"}]}, {"year": "2009", "text": "A series of bombings in Baghdad, Iraq, kills 101 and injures 565 others.", "html": "2009 - A <a href=\"https://wikipedia.org/wiki/August_2009_Baghdad_bombings\" title=\"August 2009 Baghdad bombings\">series of bombings</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, Iraq, kills 101 and injures 565 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/August_2009_Baghdad_bombings\" title=\"August 2009 Baghdad bombings\">series of bombings</a> in <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>, Iraq, kills 101 and injures 565 others.", "links": [{"title": "August 2009 Baghdad bombings", "link": "https://wikipedia.org/wiki/August_2009_Baghdad_bombings"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "2010", "text": "Operation Iraqi Freedom ends, with the last of the United States brigade combat teams crossing the border to Kuwait.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Operation Iraqi Freedom</a> ends, with the last of the United States <a href=\"https://wikipedia.org/wiki/Brigade\" title=\"Brigade\">brigade</a> combat teams crossing the border to <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraq_War\" title=\"Iraq War\">Operation Iraqi Freedom</a> ends, with the last of the United States <a href=\"https://wikipedia.org/wiki/Brigade\" title=\"Brigade\">brigade</a> combat teams crossing the border to <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>.", "links": [{"title": "Iraq War", "link": "https://wikipedia.org/wiki/Iraq_War"}, {"title": "Brigade", "link": "https://wikipedia.org/wiki/Brigade"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}]}, {"year": "2013", "text": "The Dhamara Ghat train accident kills at least 37 people in the Indian state of Bihar.", "html": "2013 - The <a href=\"https://wikipedia.org/wiki/Dhamara_Ghat_train_accident\" title=\"Dhamara Ghat train accident\">Dhamara Ghat train accident</a> kills at least 37 people in the Indian state of <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dhamara_Ghat_train_accident\" title=\"Dhamara Ghat train accident\">Dhamara Ghat train accident</a> kills at least 37 people in the Indian state of <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>.", "links": [{"title": "Dhamara Ghat train accident", "link": "https://wikipedia.org/wiki/Dhamara_Ghat_train_accident"}, {"title": "Bihar", "link": "https://wikipedia.org/wiki/Bihar"}]}, {"year": "2017", "text": "Tens of thousands of farmed non-native Atlantic salmon are accidentally released into the wild in Washington waters in the 2017 Cypress Island Atlantic salmon pen break.", "html": "2017 - Tens of thousands of farmed non-native <a href=\"https://wikipedia.org/wiki/Atlantic_salmon\" title=\"Atlantic salmon\">Atlantic salmon</a> are accidentally released into the wild in <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> waters in the <a href=\"https://wikipedia.org/wiki/Cypress_Island_Atlantic_salmon_pen_break\" title=\"Cypress Island Atlantic salmon pen break\">2017 Cypress Island Atlantic salmon pen break</a>.", "no_year_html": "Tens of thousands of farmed non-native <a href=\"https://wikipedia.org/wiki/Atlantic_salmon\" title=\"Atlantic salmon\">Atlantic salmon</a> are accidentally released into the wild in <a href=\"https://wikipedia.org/wiki/Washington_(state)\" title=\"Washington (state)\">Washington</a> waters in the <a href=\"https://wikipedia.org/wiki/Cypress_Island_Atlantic_salmon_pen_break\" title=\"Cypress Island Atlantic salmon pen break\">2017 Cypress Island Atlantic salmon pen break</a>.", "links": [{"title": "Atlantic salmon", "link": "https://wikipedia.org/wiki/Atlantic_salmon"}, {"title": "Washington (state)", "link": "https://wikipedia.org/wiki/Washington_(state)"}, {"title": "Cypress Island Atlantic salmon pen break", "link": "https://wikipedia.org/wiki/Cypress_Island_Atlantic_salmon_pen_break"}]}], "Births": [{"year": "232", "text": "<PERSON>, Roman emperor (d. 282)", "html": "232 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bus\" class=\"mw-redirect\" title=\"<PERSON>relius <PERSON>bus\"><PERSON></a>, Roman emperor (d. 282)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>bus\" class=\"mw-redirect\" title=\"<PERSON> Aurelius Probus\"><PERSON></a>, Roman emperor (d. 282)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>bus"}]}, {"year": "1342", "text": "<PERSON> of Bohemia, duchess of Austria (d. 1395)", "html": "1342 - <a href=\"https://wikipedia.org/wiki/Catherine_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a>, duchess of Austria (d. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Catherine_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a>, duchess of Austria (d. 1395)", "links": [{"title": "Catherine of Bohemia", "link": "https://wikipedia.org/wiki/Catherine_of_Bohemia"}]}, {"year": "1398", "text": "<PERSON><PERSON><PERSON>, 1st Marquis of Santillana, Spanish poet and politician (d. 1458)", "html": "1398 - <a href=\"https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Santillana\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of Santillana\"><PERSON><PERSON><PERSON>, 1st Marquis of Santillana</a>, Spanish poet and politician (d. 1458)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Santillana\" title=\"<PERSON><PERSON><PERSON>, 1st Marquis of Santillana\"><PERSON><PERSON><PERSON>, 1st Marquis of Santillana</a>, Spanish poet and politician (d. 1458)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st Marquis of Santillana", "link": "https://wikipedia.org/wiki/%C3%8D%C3%B1igo_L%C3%B3<PERSON><PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Santillana"}]}, {"year": "1570", "text": "<PERSON><PERSON><PERSON>, Italian violinist and composer (probable; d. 1630)", "html": "1570 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (probable; d. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian violinist and composer (probable; d. 1630)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1583", "text": "<PERSON><PERSON><PERSON>, Chinese prince and statesman (d. 1648)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/Dai%C5%A1an\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese prince and statesman (d. 1648)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dai%C5%A1an\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese prince and statesman (d. 1648)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dai%C5%A1an"}]}, {"year": "1590", "text": "<PERSON>, 1st Earl of Holland, English soldier and politician, Lord Lieutenant of Berkshire (d. 1649)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Holland\" title=\"<PERSON>, 1st Earl of Holland\"><PERSON>, 1st Earl of Holland</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire\" title=\"Lord Lieutenant of Berkshire\">Lord Lieutenant of Berkshire</a> (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Holland\" title=\"<PERSON>, 1st Earl of Holland\"><PERSON>, 1st Earl of Holland</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire\" title=\"Lord Lieutenant of Berkshire\">Lord Lieutenant of Berkshire</a> (d. 1649)", "links": [{"title": "<PERSON>, 1st Earl of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Holland"}, {"title": "Lord Lieutenant of Berkshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Berkshire"}]}, {"year": "1596", "text": "<PERSON>, queen of Bohemia (d. 1662)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON>, Queen of Bohemia\"><PERSON></a>, queen of Bohemia (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia\" title=\"<PERSON>, Queen of Bohemia\"><PERSON></a>, queen of Bohemia (d. 1662)", "links": [{"title": "<PERSON>, Queen of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Bohemia"}]}, {"year": "1609", "text": "<PERSON>, Flemish painter (d. 1661)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1661)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1661)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>yt"}]}, {"year": "1621", "text": "<PERSON><PERSON><PERSON>, Dutch painter, etcher, and poet (d. 1674)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> E<PERSON>hout\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch painter, etcher, and poet (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> E<PERSON>hout\"><PERSON><PERSON><PERSON> <PERSON></a>, Dutch painter, etcher, and poet (d. 1674)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON>, English poet, literary critic and playwright (d. 1700)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, literary critic and playwright (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, literary critic and playwright (d. 1700)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, English astronomer and academic (d. 1719)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (d. 1719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON><PERSON><PERSON>, English journalist and politician (d. 1737)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and politician (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and politician (d. 1737)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1689 (baptized)", "text": "<PERSON>, English author and publisher (d. 1761)", "html": "1689 (baptized) - <a href=\"https://wikipedia.org/wiki/1689\" title=\"1689\">1689</a> (baptized) - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1689\" title=\"1689\">1689</a> (baptized) - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 1761)", "links": [{"title": "1689", "link": "https://wikipedia.org/wiki/1689"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1711", "text": "<PERSON>, English admiral and politician (d. 1761)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (d. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (d. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON><PERSON><PERSON>, marquis <PERSON>, French soldier and diplomat (d. 1781)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_<PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, marquis de <PERSON>\"><PERSON><PERSON><PERSON>, marquis de <PERSON></a>, French soldier and diplomat (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_de_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, marquis de <PERSON>\"><PERSON><PERSON><PERSON>, marquis de <PERSON></a>, French soldier and diplomat (d. 1781)", "links": [{"title": "<PERSON><PERSON><PERSON>, marquis de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1743", "text": "<PERSON>, French mistress of <PERSON> of <PERSON> (d. 1793)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame <PERSON>\">Madame <PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> of France</a> (d. 1793)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame <PERSON>\">Madame <PERSON></a>, French mistress of <a href=\"https://wikipedia.org/wiki/Louis_XV_of_France\" class=\"mw-redirect\" title=\"Louis XV of France\"><PERSON> of France</a> (d. 1793)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> XV of France", "link": "https://wikipedia.org/wiki/Louis_XV_of_France"}]}, {"year": "1777", "text": "<PERSON>, king of the Two Sicilies (d. 1830)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON></a>, king of the Two Sicilies (d. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies\" title=\"<PERSON> I of the Two Sicilies\"><PERSON></a>, king of the Two Sicilies (d. 1830)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_the_Two_Sicilies"}]}, {"year": "1815", "text": "<PERSON><PERSON>, American editor and children's book writer (d. 1893)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American editor and children's book writer (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American editor and children's book writer (d. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Woods_Baker"}]}, {"year": "1819", "text": "<PERSON>, Luxembourger-Dutch politician, Prime Minister of the Netherlands (d. 1894)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-Dutch politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-Dutch politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1830", "text": "<PERSON>, German chemist (d. 1895)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1835", "text": "<PERSON>, Australian cricketer and pioneer of Australian rules football (d. 1880)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and pioneer of <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and pioneer of <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> (d. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australian rules football", "link": "https://wikipedia.org/wiki/Australian_rules_football"}]}, {"year": "1843", "text": "<PERSON><PERSON> <PERSON><PERSON>, American minister and theologian (d. 1921)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister and theologian (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American minister and theologian (d. 1921)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Spanish religious leader, 24th Superior General of the Society of Jesus (d. 1906)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish religious leader, 24th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Spanish religious leader, 24th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mart%C3%ADn"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1848", "text": "<PERSON><PERSON>, French painter and engineer (d. 1894)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and engineer (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and engineer (d. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian politician and diplomat (d. 1910)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian politician and diplomat (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian politician and diplomat (d. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>m_Nabuco"}]}, {"year": "1858", "text": "<PERSON>, English horticulturalist (d. 1934)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English horticulturalist (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, American businessman and philanthropist (d. 1965)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON>, American engineer and pilot, co-founded the Wright Company (d. 1948)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Or<PERSON> Wright\"><PERSON><PERSON></a>, American engineer and pilot, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Or<PERSON> Wright\"><PERSON><PERSON></a>, American engineer and pilot, co-founded the <a href=\"https://wikipedia.org/wiki/Wright_Company\" title=\"Wright Company\">Wright Company</a> (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Wright Company", "link": "https://wikipedia.org/wiki/Wright_Company"}]}, {"year": "1873", "text": "<PERSON>, American actor and producer (d. 1959)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Filipino soldier, lawyer, and politician, 2nd President of the Philippines (d. 1944)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino soldier, lawyer, and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>zon"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1881", "text": "<PERSON>, Romanian violinist, pianist, composer, and conductor (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian violinist, pianist, composer, and conductor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian violinist, pianist, composer, and conductor (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, 1st Baron <PERSON> (d. 1954)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a> (d. 1954)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, French fashion designer, founded the Chanel Company (d. 1971)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>co_Chanel\" title=\"Coco Chanel\"><PERSON><PERSON></a>, French fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Chanel\">Chanel Company</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Chanel\" title=\"Coco Chanel\"><PERSON><PERSON></a>, French fashion designer, founded the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>el\">Chanel Company</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>co_Chanel"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Portuguese admiral and politician, 9th President of Portugal (d. 1965)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mendes_Cabe%C3%A7adas\" title=\"<PERSON>\"><PERSON></a>, Portuguese admiral and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Mendes_<PERSON>abe%C3%A7adas\" title=\"<PERSON>\"><PERSON></a>, Portuguese admiral and politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Portugal\" title=\"President of Portugal\">President of Portugal</a> (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON>_Cabe%C3%A7adas"}, {"title": "President of Portugal", "link": "https://wikipedia.org/wiki/President_of_Portugal"}]}, {"year": "1885", "text": "<PERSON>, American labor reformer and researcher (d. 1969)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor reformer and researcher (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor reformer and researcher (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Indian lawyer and politician (d. 1943)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/S._Saty<PERSON>urti\" title=\"S. Saty<PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S._Saty<PERSON>urti\" title=\"S. Saty<PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician (d. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S._<PERSON><PERSON><PERSON><PERSON>i"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Sri Lankan lawyer, academic, and politician (d. 1985)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/C._<PERSON>m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer, academic, and politician (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON>m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer, academic, and politician (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Suntharalingam"}]}, {"year": "1899", "text": "<PERSON><PERSON>, American actress (d. 1988)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 1988)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, French author and adventurer (d. 1962)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Gontran_de_Poncins\" title=\"Gontran de Poncins\"><PERSON><PERSON><PERSON></a>, French author and adventurer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gontran_de_Poncins\" title=\"Gontran de Poncins\"><PERSON><PERSON><PERSON></a>, French author and adventurer (d. 1962)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English philosopher, author, and academic (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, author, and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher, author, and academic (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American archaeologist and art historian (d. 2001)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and art historian (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and art historian (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American poet (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Nash\"><PERSON></a>, American poet (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ogden_Nash\" title=\"<PERSON> Nash\"><PERSON></a>, American poet (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ogden_Nash"}]}, {"year": "1903", "text": "<PERSON>, American novelist and short story writer (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ens\"><PERSON></a>, American novelist and short story writer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Cozzens\"><PERSON></a>, American novelist and short story writer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, English engineer and businessman (d. 1963)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer and businessman (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, American inventor, invented the Fusor (d. 1971)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Fusor\" title=\"Fusor\"><PERSON>sor</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil<PERSON>_<PERSON>ns<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American inventor, invented the <a href=\"https://wikipedia.org/wiki/Fusor\" title=\"Fusor\">Fusor</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Philo_Farnsworth"}, {"title": "Fusor", "link": "https://wikipedia.org/wiki/Fusor"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Indian historian, author, and scholar (d. 1979)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian, author, and scholar (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian, author, and scholar (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>i"}]}, {"year": "1909", "text": "<PERSON>, New Zealand rugby player (d. 1988)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ronald King\"><PERSON></a>, New Zealand rugby player (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON> <PERSON><PERSON><PERSON>, first woman of Indian origin to be canonized as a saint by the Catholic Church (d. 1946)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON><PERSON><PERSON>\">Saint <PERSON><PERSON><PERSON></a>, first woman of Indian origin to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Saint <PERSON><PERSON><PERSON>\">Saint <PERSON><PERSON></a>, first woman of Indian origin to be <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> as a saint by the <a href=\"https://wikipedia.org/wiki/Catholic_Church\" title=\"Catholic Church\">Catholic Church</a> (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saint_Alphonsa"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "Catholic Church", "link": "https://wikipedia.org/wiki/Catholic_Church"}]}, {"year": "1911", "text": "<PERSON>, Dutch psychiatrist and author (d. 2004)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch psychiatrist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch psychiatrist and author (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Australian rugby league player, coach, and boxer (d. 1958)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and boxer (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and boxer (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Narvo"}]}, {"year": "1913", "text": "<PERSON>, Greek engineer and academic (d. 2004)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek engineer and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek engineer and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Indian-English soldier and author (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Indian-English soldier and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, Indian-English soldier and author (d. 1993)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 2003)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2003)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Hungarian footballer and manager (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian footballer and manager (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lajos_Bar%C3%B3ti"}]}, {"year": "1914", "text": "<PERSON><PERSON>, Japanese composer (d. 1955)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON> Hayasaka\"><PERSON><PERSON></a>, Japanese composer (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Fu<PERSON> Hayasaka\"><PERSON><PERSON></a>, Japanese composer (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, British barrister and judge (d. 2005)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British barrister and judge (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British barrister and judge (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_He<PERSON>bron"}]}, {"year": "1915", "text": "<PERSON>, Jr., American journalist and screenwriter (d. 2000)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and screenwriter (d. 2000)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1915", "text": "<PERSON>, Canadian businessman (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English racing driver and businessman (d. 1987)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and businessman (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver and businessman (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American singer-songwriter and pianist (d. 1996)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American publisher and politician (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American screenwriter and producer (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, Australian rugby league player (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player (d. 1994)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1923", "text": "<PERSON>, English computer scientist, inventor of relational model of data (d. 2003)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist, inventor of <a href=\"https://wikipedia.org/wiki/Relational_model\" title=\"Relational model\">relational model of data</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist, inventor of <a href=\"https://wikipedia.org/wiki/Relational_model\" title=\"Relational model\">relational model of data</a> (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>dd"}, {"title": "Relational model", "link": "https://wikipedia.org/wiki/Relational_model"}]}, {"year": "1924", "text": "<PERSON>, Canadian physicist and academic, Nobel Prize laureate (d. 2011)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1924", "text": "<PERSON>, American actor, director, and opera singer (d. 2003)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and opera singer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, and opera singer (d. 2003)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1925", "text": "<PERSON>, Canadian poet and playwright (d. 1971)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and playwright (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and playwright (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor and author (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and author (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Angus_Scrimm"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Indian Hindi writer (d. 1998)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>v <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Indian Hindi writer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>v <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Indian Hindi writer (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English journalist, author, and broadcaster (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and broadcaster (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, author, and broadcaster (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American basketball player and coach (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)\" title=\"<PERSON> (basketball, born 1929)\"><PERSON></a>, American basketball player and coach (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)\" title=\"<PERSON> (basketball, born 1929)\"><PERSON></a>, American basketball player and coach (d. 2016)", "links": [{"title": "<PERSON> (basketball, born 1929)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1929)"}]}, {"year": "1929", "text": "<PERSON>, Romanian-German neurologist and academic (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German neurologist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-German neurologist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>._Pet<PERSON>ici"}]}, {"year": "1930", "text": "<PERSON>, American author and educator (d. 2009)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American jockey and author (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and author (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and author (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American lawyer and politician, 75th Governor of Vermont", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 75th <a href=\"https://wikipedia.org/wiki/Governor_of_Vermont\" title=\"Governor of Vermont\">Governor of Vermont</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Vermont", "link": "https://wikipedia.org/wiki/Governor_of_Vermont"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Thai politician, Prime Minister (1995-1996) (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-archa\" title=\"<PERSON><PERSON><PERSON>-archa\"><PERSON><PERSON><PERSON>-archa</a>, Thai politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister</a> (1995-1996) (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-archa\" title=\"<PERSON><PERSON><PERSON>-archa\"><PERSON><PERSON><PERSON>-arch<PERSON></a>, Thai politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister</a> (1995-1996) (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>a", "link": "https://wikipedia.org/wiki/<PERSON>har<PERSON>_<PERSON><PERSON>-archa"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American model and photographer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model and photographer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Cirone"}]}, {"year": "1933", "text": "<PERSON>, English microbiologist and geneticist", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English microbiologist and geneticist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English microbiologist and geneticist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American actress", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1934", "text": "<PERSON>, American soldier, lawyer, and politician (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American tennis player and ophthalmologist", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and ophthalmologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American tennis player and ophthalmologist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9e_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American baseball player and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American priest, theologian, and academic (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, theologian, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, theologian, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English journalist, founded The Oldie", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, founded <i><a href=\"https://wikipedia.org/wiki/The_Oldie\" title=\"The Oldie\">The Oldie</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, founded <i><a href=\"https://wikipedia.org/wiki/The_Oldie\" title=\"The Oldie\">The Oldie</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Oldie", "link": "https://wikipedia.org/wiki/The_Oldie"}]}, {"year": "1937", "text": "<PERSON>, American composer and conductor (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Argentine conductor and musician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine conductor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentine conductor and musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English drummer and songwriter (d. 2019)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer and songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English songwriter, singer, and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English songwriter, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, English songwriter, singer, and producer", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(songwriter)"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2020)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American model and actress", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jill St. John\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Jill St. John\"><PERSON></a>, American model and actress", "links": [{"title": "Jill St. John", "link": "https://wikipedia.org/wiki/Jill_St._John"}]}, {"year": "1941", "text": "<PERSON>, Australian rugby league player, priest, and businessman", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, priest, and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, priest, and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Greek educator and politician (d. 2009)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek educator and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek educator and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American actor, lawyer, and politician (d. 2015)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, lawyer, and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, lawyer, and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English pop singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, New Zealand rugby player (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Sid_Going\" title=\"Sid Going\"><PERSON></a>, New Zealand rugby player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sid_Going\" title=\"Sid Going\"><PERSON></a>, New Zealand rugby player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sid_Going"}]}, {"year": "1943", "text": "<PERSON>, English pop singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pop singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American basketball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Johnson\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Johnson"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Swedish author and poet (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Bodil_Malmsten\" title=\"Bo<PERSON><PERSON> Malmsten\"><PERSON><PERSON><PERSON></a>, Swedish author and poet (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo<PERSON><PERSON>_Malms<PERSON>\" title=\"<PERSON><PERSON><PERSON> Malms<PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish author and poet (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American country music singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Chinese-American businessman and philanthropist, co-founded Computer Associates International (d. 2018)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Computer_Associates_International\" class=\"mw-redirect\" title=\"Computer Associates International\">Computer Associates International</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American businessman and philanthropist, co-founded <a href=\"https://wikipedia.org/wiki/Computer_Associates_International\" class=\"mw-redirect\" title=\"Computer Associates International\">Computer Associates International</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Computer Associates International", "link": "https://wikipedia.org/wiki/Computer_Associates_International"}]}, {"year": "1945", "text": "<PERSON>, American author and illustrator (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, 9th Duke of Wellington, English politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_9th_Duke_of_Wellington\" title=\"<PERSON>, 9th Duke of Wellington\"><PERSON>, 9th Duke of Wellington</a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_9th_Duke_of_Wellington\" title=\"<PERSON>, 9th Duke of Wellington\"><PERSON>, 9th Duke of Wellington</a>, English politician", "links": [{"title": "<PERSON>, 9th Duke of Wellington", "link": "https://wikipedia.org/wiki/<PERSON>,_9th_Duke_of_Wellington"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American general and astronaut", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and politician, 42nd President of the United States", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 42nd <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1946", "text": "<PERSON>, American film producer (d. 1997)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Dawn_Steel\" title=\"Dawn Steel\"><PERSON></a>, American film producer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dawn_Steel\" title=\"Dawn Steel\"><PERSON></a>, American film producer (d. 1997)", "links": [{"title": "Dawn Steel", "link": "https://wikipedia.org/wiki/Dawn_Steel"}]}, {"year": "1947", "text": "<PERSON>, English actor and screenwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American football player and coach (d. 2007)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American conductor and director", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian mathematician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Anu%C5%A1ka_Ferligoj\" title=\"Anuš<PERSON> Ferligoj\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anu%C5%A1ka_Ferligoj\" title=\"Anu<PERSON><PERSON> Ferligoj\"><PERSON><PERSON><PERSON><PERSON>rl<PERSON></a>, Slovenian mathematician", "links": [{"title": "Anuš<PERSON> Ferligoj", "link": "https://wikipedia.org/wiki/Anu%C5%A1ka_Ferligoj"}]}, {"year": "1948", "text": "<PERSON>, English actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American activist and author, former Second Lady of the United States", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tip<PERSON>\"><PERSON><PERSON><PERSON></a>, American activist and author, former <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tip<PERSON>\"><PERSON><PERSON><PERSON></a>, American activist and author, former <a href=\"https://wikipedia.org/wiki/Second_Lady_of_the_United_States\" class=\"mw-redirect\" title=\"Second Lady of the United States\">Second Lady of the United States</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "Second Lady of the United States", "link": "https://wikipedia.org/wiki/Second_Lady_of_the_United_States"}]}, {"year": "1948", "text": "<PERSON>, Australian actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" class=\"mw-redirect\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)\" class=\"mw-redirect\" title=\"<PERSON> (Australian actor)\"><PERSON></a>, Australian actor", "links": [{"title": "<PERSON> (Australian actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_actor)"}]}, {"year": "1948", "text": "<PERSON>, Irish golfer and architect (d. 2016)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, Irish golfer and architect (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, Irish golfer and architect (d. 2016)", "links": [{"title": "<PERSON> Jnr", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Connor_Jnr"}]}, {"year": "1949", "text": "<PERSON>, Pakistani-English bishop", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani-English bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, English journalist and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Indian author and teacher, head of Infosys Foundation", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and teacher, head of <a href=\"https://wikipedia.org/wiki/Infosys_Foundation\" title=\"Infosys Foundation\">Infosys Foundation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian author and teacher, head of <a href=\"https://wikipedia.org/wiki/Infosys_Foundation\" title=\"Infosys Foundation\">Infosys Foundation</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}, {"title": "Infosys Foundation", "link": "https://wikipedia.org/wiki/Infosys_Foundation"}]}, {"year": "1951", "text": "<PERSON>, English bass player and songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Argentinian singer-songwriter, guitarist, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and director", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, German equestrian (d. 2024)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Grillo\"><PERSON><PERSON></a>, German equestrian (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Grillo\"><PERSON><PERSON></a>, German equestrian (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1952", "text": "<PERSON>, Canadian ice hockey player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1954", "text": "<PERSON>, Argentinian racing driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "Mary<PERSON><PERSON>, Australian actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Baroness <PERSON> of <PERSON>thal, Dominica-born English lawyer and politician, Attorney General for England and Wales", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_Scotland_of_Asthal\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON> of Asthal\"><PERSON>, Baroness <PERSON> of Asthal</a>, <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>-born English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patricia_<PERSON>,_Baroness_Scotland_of_Asthal\" class=\"mw-redirect\" title=\"<PERSON>, Baroness Scotland of Asthal\"><PERSON>, Baroness Scotland of Asthal</a>, <a href=\"https://wikipedia.org/wiki/Dominica\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>-born English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales\" title=\"Attorney General for England and Wales\">Attorney General for England and Wales</a>", "links": [{"title": "<PERSON>, Baroness <PERSON> of Asthal", "link": "https://wikipedia.org/wiki/Patricia_<PERSON>,_Baroness_Scotland_of_Asthal"}, {"title": "Dominica", "link": "https://wikipedia.org/wiki/Dominica"}, {"title": "Attorney General for England and Wales", "link": "https://wikipedia.org/wiki/Attorney_General_for_England_and_Wales"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and manager", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yost\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actor, director, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ark<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Guatemalan journalist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Rub%C3%A9n_Zamora\" title=\"<PERSON>\"><PERSON></a>, Guatemalan journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Rub%C3%A9n_Zamora\" title=\"<PERSON>\"><PERSON></a>, Guatemalan journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Rub%C3%A9n_Zamora"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Dutch cricketer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1957", "text": "<PERSON>, American actor and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English cricketer and umpire", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and umpire", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Italian footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Belgian high jumper", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Dutch trade union leader and politician, Dutch Minister of Agriculture", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Gerda_Verburg\" title=\"Gerda Verburg\"><PERSON><PERSON><PERSON></a>, Dutch trade union leader and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture,_Nature_and_Food_Quality_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Agriculture, Nature and Food Quality (Netherlands)\">Dutch Minister of Agriculture</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gerda_Verburg\" title=\"Gerda Verburg\"><PERSON><PERSON><PERSON></a>, Dutch trade union leader and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Agriculture,_Nature_and_Food_Quality_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Agriculture, Nature and Food Quality (Netherlands)\">Dutch Minister of Agriculture</a>", "links": [{"title": "Gerda Verburg", "link": "https://wikipedia.org/wiki/Gerda_Verburg"}, {"title": "Ministry of Agriculture, Nature and Food Quality (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Agriculture,_Nature_and_Food_Quality_(Netherlands)"}]}, {"year": "1958", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American football player and sportscaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1oz\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anthony_Mu%C3%B1oz"}]}, {"year": "1958", "text": "<PERSON>, Australian physician and politician, 47th Minister for Defence for Australia", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and politician, 47th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Minister for Defence for Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physician and politician, 47th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Australia)\" title=\"Minister for Defence (Australia)\">Minister for Defence for Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Australia)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Australia)"}]}, {"year": "1958", "text": "<PERSON>, American politician and businessman, 48th Governor of Michigan", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and businessman, 48th <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1958", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby league player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American basketball player", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Danish-American football player", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish-American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and commentator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English author and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American actor", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Sedgwick\" title=\"<PERSON><PERSON> Sedgwick\"><PERSON><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dgwick\" title=\"<PERSON><PERSON> Sedgwick\"><PERSON><PERSON></a>, American actress and producer", "links": [{"title": "Kyra <PERSON>", "link": "https://wikipedia.org/wiki/Kyra_Sedgwick"}]}, {"year": "1965", "text": "<PERSON>, Australian rower", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, Australian rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)\" title=\"<PERSON> (rower)\"><PERSON></a>, Australian rower", "links": [{"title": "<PERSON> (rower)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rower)"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Indian spiritual leader", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Khan<PERSON><PERSON>_R<PERSON>poche\" title=\"<PERSON><PERSON><PERSON> Rinpoche\"><PERSON><PERSON><PERSON></a>, Indian spiritual leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_R<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON> Rinpoche\"><PERSON><PERSON><PERSON></a>, Indian spiritual leader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Indian-American business executive, chairman and CEO of Microsoft", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American business executive, chairman and CEO of <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian-American business executive, chairman and CEO of <a href=\"https://wikipedia.org/wiki/Microsoft\" title=\"Microsoft\">Microsoft</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "Microsoft", "link": "https://wikipedia.org/wiki/Microsoft"}]}, {"year": "1969", "text": "<PERSON>, American professional wrestler and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON>.\"><PERSON>.</a>, American professional wrestler and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American professional wrestler and politician", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1969", "text": "<PERSON>, American rapper (d. 2011)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dogg\"><PERSON></a>, American rapper (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dogg\"><PERSON></a>, American rapper (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2023)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American rapper", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Fat_Joe\" title=\"Fat Joe\"><PERSON> Joe</a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fat_Joe\" title=\"Fat Joe\"><PERSON> Joe</a>, American rapper", "links": [{"title": "Fat Joe", "link": "https://wikipedia.org/wiki/<PERSON>_Joe"}]}, {"year": "1971", "text": "<PERSON>, Dominican-American tennis player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Dominican-American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1ndez\" title=\"<PERSON>\"><PERSON></a>, Dominican-American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1ndez"}]}, {"year": "1971", "text": "<PERSON>, Portuguese footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Vieira_Pinto\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Vieira_Pinto\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Argentinian footballer and manager", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Italian footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American basketball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Australian actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ta<PERSON> Walton\"><PERSON><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Walton\"><PERSON><PERSON></a>, Australian actress", "links": [{"title": "Tasma Walton", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Walton"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trac<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gine_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gine_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gin<PERSON>_<PERSON>ssagne"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Spanish cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Iban_<PERSON>\" title=\"Iban Mayo\"><PERSON><PERSON></a>, Spanish cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iban_Mayo\" title=\"Iban Mayo\"><PERSON><PERSON></a>, Spanish cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Czech game designer ", "html": "1978 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Dvorsk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech game designer ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Dvorsk%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech game designer ", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakub_Dvorsk%C3%BD"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Swiss footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Oumar_Kond%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oumar_Kond%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oumar_Kond%C3%A9"}]}, {"year": "1980", "text": "<PERSON>, Scottish singer-songwriter, guitarist, and actor (d. 2022)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and actor (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter, guitarist, and actor (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, South Korean singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Welsh footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American bass player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1981", "text": "<PERSON>, English rugby player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> P<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> P<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Taylor_Pyatt"}]}, {"year": "1981", "text": "<PERSON>, American football player and wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Belgian pole vaulter", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American professional mixed martial artist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American professional mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American professional mixed martial artist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ipe_Mi<PERSON>ic"}]}, {"year": "1982", "text": "<PERSON>, Canadian ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English racing driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Australian singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, South African-Australian actress and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-Australian actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African-Australian actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>sok"}]}, {"year": "1984", "text": "<PERSON>, English actor and screenwriter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)\" title=\"<PERSON> (footballer, born 1984)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1984)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1984)"}]}, {"year": "1985", "text": "<PERSON>, American actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American snowboarder", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Greek footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Japanese volleyball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese volleyball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer and songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Jamaican-American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Dutch swimmer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, German racing driver", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Nico_H%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nico_H%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nico_H%C3%BClk<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kirk_<PERSON>ins"}]}, {"year": "1988", "text": "<PERSON>, American author", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player, rapper, actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, rapper, actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, rapper, actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Scottish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Saudi Arabian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Salem_Al-Dawsari\" title=\"Salem Al-Dawsari\"><PERSON> Al-<PERSON></a>, Saudi Arabian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salem_Al-Dawsari\" title=\"Salem Al-Dawsari\"><PERSON>-<PERSON></a>, Saudi Arabian footballer", "links": [{"title": "Salem Al-Dawsari", "link": "https://wikipedia.org/wiki/Salem_Al-Dawsari"}]}, {"year": "1992", "text": "<PERSON>, Czech ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Fijian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Fijian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pio_Seci"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian pentathlete and heptathlete", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Nafissatou_Thiam\" title=\"Nafissatou Thiam\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian pentathlete and heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nafissatou_Thiam\" title=\"Nafissatou Thiam\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belgian pentathlete and heptathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nafi<PERSON><PERSON><PERSON>_Thiam"}]}, {"year": "1994", "text": "<PERSON>, Colombian cyclist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, South Korean singer and actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)\" title=\"<PERSON><PERSON> (entertainer)\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON> (entertainer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(entertainer)"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Australian rugby league player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American actor and musician", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Australian rugby league player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American basketball player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Finnish basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Awa<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "607 BC", "text": "<PERSON> of Jin, Chinese monarch", "html": "607 BC - 607 BC - <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_Jin\" title=\"Duke <PERSON> of Jin\">Duke <PERSON> of Jin</a>, Chinese monarch", "no_year_html": "607 BC - <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_Jin\" title=\"Duke <PERSON> of Jin\">Duke <PERSON> of Jin</a>, Chinese monarch", "links": [{"title": "<PERSON> of Jin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "14", "text": "<PERSON>, Roman emperor (b. 63 BC)", "html": "14 - AD 14 - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a>, Roman emperor (b. 63 BC)", "no_year_html": "AD 14 - <a href=\"https://wikipedia.org/wiki/Augustus\" title=\"Augustus\">Augustus</a>, Roman emperor (b. 63 BC)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Augustus"}]}, {"year": "780", "text": "<PERSON><PERSON><PERSON>, English abbot and saint", "html": "780 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English abbot and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English abbot and saint", "links": [{"title": "C<PERSON>an", "link": "https://wikipedia.org/wiki/Credan"}]}, {"year": "947", "text": "<PERSON>, Kharijite rebel leader (b. 873)", "html": "947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kharijite rebel leader (b. 873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kharijite rebel leader (b. 873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "998", "text": "<PERSON><PERSON>, Japanese noble, statesman and calligrapher (b. 944)", "html": "998 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Sukemasa\" title=\"Fujiwara no Sukemasa\"><PERSON><PERSON> no Su<PERSON></a>, Japanese noble, statesman and calligrapher (b. 944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Sukemasa\" title=\"Fujiwara no Sukemasa\"><PERSON><PERSON> no <PERSON></a>, Japanese noble, statesman and calligrapher (b. 944)", "links": [{"title": "<PERSON><PERSON> no <PERSON><PERSON>masa", "link": "https://wikipedia.org/wiki/Fujiwara_<PERSON>_<PERSON><PERSON>a"}]}, {"year": "1072", "text": "<PERSON><PERSON>, Duchess of Brittany (b. 1037)", "html": "1072 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON>, Duchess of Brittany</a> (b. 1037)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany\" title=\"<PERSON><PERSON>, Duchess of Brittany\"><PERSON><PERSON>, Duchess of Brittany</a> (b. 1037)", "links": [{"title": "<PERSON><PERSON>, Duchess of Brittany", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Duchess_of_Brittany"}]}, {"year": "1085", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Muslim scholar and imam (b. 1028)", "html": "1085 - <a href=\"https://wikipedia.org/wiki/Al-Ju<PERSON>ni\" title=\"Al-Juwayni\"><PERSON><PERSON><PERSON><PERSON></a>, Muslim scholar and imam (b. 1028)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Juwayni\" title=\"Al-Juwayni\"><PERSON><PERSON><PERSON></a>, Muslim scholar and imam (b. 1028)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Juwayni"}]}, {"year": "1186", "text": "<PERSON>, Duke of Brittany (b. 1158)", "html": "1186 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1158)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1158)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1245", "text": "<PERSON>, Count of Provence (b. 1195)", "html": "1245 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Provence\" class=\"mw-redirect\" title=\"<PERSON>, Count of Provence\"><PERSON>, Count of Provence</a> (b. 1195)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Provence\" class=\"mw-redirect\" title=\"<PERSON>, Count of Provence\"><PERSON>, Count of Provence</a> (b. 1195)", "links": [{"title": "<PERSON>, Count of Provence", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Count_of_Provence"}]}, {"year": "1284", "text": "<PERSON><PERSON><PERSON>, Earl of Chester (b. 1273)", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Chester\" title=\"<PERSON><PERSON><PERSON>, Earl of Chester\"><PERSON><PERSON><PERSON>, Earl of Chester</a> (b. 1273)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Chester\" title=\"<PERSON><PERSON><PERSON>, Earl of Chester\"><PERSON><PERSON><PERSON>, Earl of Chester</a> (b. 1273)", "links": [{"title": "<PERSON><PERSON><PERSON>, Earl of Chester", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Earl_of_Chester"}]}, {"year": "1297", "text": "<PERSON> of Toulouse, French bishop and saint (b. 1274)", "html": "1297 - <a href=\"https://wikipedia.org/wiki/Louis_of_Toulouse\" title=\"<PERSON> of Toulouse\"><PERSON> of Toulouse</a>, French bishop and saint (b. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_of_<PERSON>\" title=\"<PERSON> of Toulouse\"><PERSON> of Toulouse</a>, French bishop and saint (b. 1274)", "links": [{"title": "<PERSON> of Toulouse", "link": "https://wikipedia.org/wiki/Louis_of_Toulouse"}]}, {"year": "1457", "text": "<PERSON>, Italian painter (b. 1421)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter (b. 1421)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1470", "text": "<PERSON>, French cardinal (b. 1406)", "html": "1470 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Cardinal_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinal</a> (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)\" title=\"Cardinal (Catholic Church)\">cardinal</a> (b. 1406)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> (Catholic Church)", "link": "https://wikipedia.org/wiki/<PERSON>_(Catholic_Church)"}]}, {"year": "1493", "text": "<PERSON>, Holy Roman Emperor (b. 1415)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (b. 1415)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1506", "text": "King <PERSON> of Poland (b. 1461)", "html": "1506 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Poland (b. 1461)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of Poland (b. 1461)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1541", "text": "<PERSON>, Venetian admiral and statesman (b. 1469)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (b. 1469)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (b. 1469)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, Italian architect, designed the Church of San Giorgio Maggiore and Il Redentore (b. 1508)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Church_of_San_Giorgio_Maggiore\" class=\"mw-redirect\" title=\"Church of San Giorgio Maggiore\">Church of San Giorgio Maggiore</a> and <a href=\"https://wikipedia.org/wiki/Il_Redentore\" title=\"Il Redentore\">Il Redentore</a> (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian architect, designed the <a href=\"https://wikipedia.org/wiki/Church_of_San_Giorgio_Maggiore\" class=\"mw-redirect\" title=\"Church of San Giorgio Maggiore\">Church of San Giorgio Maggiore</a> and <a href=\"https://wikipedia.org/wiki/Il_Redentore\" title=\"Il Redentore\">Il Redentore</a> (b. 1508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Church of San Giorgio Maggiore", "link": "https://wikipedia.org/wiki/Church_of_San_<PERSON>_<PERSON>"}, {"title": "Il Redentore", "link": "https://wikipedia.org/wiki/Il_Redentore"}]}, {"year": "1646", "text": "<PERSON>, Scottish theologian and academic (b. 1583)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, Scottish theologian and academic (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a>, Scottish theologian and academic (b. 1583)", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}]}, {"year": "1654", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Bohemian rabbi (b. 1579)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/Yom-Tov_<PERSON>_<PERSON>er\" title=\"Yom-Tov Li<PERSON>ann <PERSON>er\">Yo<PERSON>-<PERSON><PERSON></a>, Bohemian rabbi (b. 1579)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yom-Tov_<PERSON>_<PERSON>er\" title=\"Yom-Tov Lipmann Heller\">Yom-To<PERSON></a>, Bohemian rabbi (b. 1579)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yo<PERSON>-<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1662", "text": "<PERSON><PERSON><PERSON>, French mathematician, physicist, and philosopher (b. 1623)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and philosopher (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and philosopher (b. 1623)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1674", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech architect (d. 1766)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Maxmili%C3%A1n_Ka%C5%88ka\" title=\"František Maxmilián <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech architect (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_Maxmili%C3%A1n_Ka%C5%88ka\" title=\"František <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech architect (d. 1766)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_Maxmili%C3%A1n_Ka%C5%88ka"}]}, {"year": "1680", "text": "<PERSON>, French priest, founded the Congregation of Jesus and Mary (b. 1601)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_<PERSON>_and_Mary\" title=\"Congregation of Jesus and Mary\">Congregation of Jesus and Mary</a> (b. 1601)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French priest, founded the <a href=\"https://wikipedia.org/wiki/Congregation_of_<PERSON>_and_<PERSON>\" title=\"Congregation of Jesus and Mary\">Congregation of Jesus and Mary</a> (b. 1601)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Congregation of Jesus and Mary", "link": "https://wikipedia.org/wiki/Congregation_of_<PERSON>_and_<PERSON>"}]}, {"year": "1691", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ottoman commander and politician, 117th Grand Vizier of the Ottoman Empire (b. 1637)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1l_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Fazıl <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman commander and politician, 117th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1l_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ıl <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ottoman commander and politician, 117th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1637)", "links": [{"title": "Köprülü Fazıl Mustafa Pasha", "link": "https://wikipedia.org/wiki/K%C3%B6pr%C3%BCl%C3%BC_Faz%C4%B1l_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1702", "text": "<PERSON>, 11th Earl of Kent, English politician (b. 1645)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Kent\" title=\"<PERSON>, 11th Earl of Kent\"><PERSON>, 11th Earl of Kent</a>, English politician (b. 1645)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_11th_Earl_of_Kent\" title=\"<PERSON>, 11th Earl of Kent\"><PERSON>, 11th Earl of Kent</a>, English politician (b. 1645)", "links": [{"title": "<PERSON>, 11th Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_11th_Earl_of_Kent"}]}, {"year": "1753", "text": "<PERSON>, German engineer and architect, designed Basilica of the Fourteen Holy Helpers (b. 1687)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer and architect, designed <a href=\"https://wikipedia.org/wiki/Basilica_of_the_Fourteen_Holy_Helpers\" title=\"Basilica of the Fourteen Holy Helpers\">Basilica of the Fourteen Holy Helpers</a> (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German engineer and architect, designed <a href=\"https://wikipedia.org/wiki/Basilica_of_the_Fourteen_Holy_Helpers\" title=\"Basilica of the Fourteen Holy Helpers\">Basilica of the Fourteen Holy Helpers</a> (b. 1687)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Basilica of the Fourteen Holy Helpers", "link": "https://wikipedia.org/wiki/Basilica_of_the_Fourteen_Holy_Helpers"}]}, {"year": "1808", "text": "<PERSON><PERSON>, Swedish admiral and shipbuilder (b. 1721)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish admiral and shipbuilder (b. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish admiral and shipbuilder (b. 1721)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, French mathematician and astronomer (b. 1749)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (b. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (b. 1749)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American lawyer and politician, 24th United States Attorney General (b. 1810)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 24th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1889", "text": "<PERSON>, French author, poet, and playwright (b. 1838)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-Adam\" title=\"<PERSON> l'Isle-Adam\"><PERSON>Isle<PERSON>Adam</a>, French author, poet, and playwright (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-Adam\" title=\"<PERSON> l'Isle-Adam\"><PERSON>Isle<PERSON>Adam</a>, French author, poet, and playwright (b. 1838)", "links": [{"title": "<PERSON>'Isle-Adam", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%27Isle-<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American Old West outlaw, gunfighter (b. 1853)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Old West outlaw, gunfighter (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Old West outlaw, gunfighter (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Belgian violinist, composer, and conductor (b. 1833)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian violinist, composer, and conductor (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian violinist, composer, and conductor (b. 1833)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, German religious leader, 25th Superior General of the Society of Jesus (b. 1844)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German religious leader, 25th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German religious leader, 25th <a href=\"https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus\" class=\"mw-redirect\" title=\"Superior General of the Society of Jesus\">Superior General of the Society of Jesus</a> (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Superior General of the Society of Jesus", "link": "https://wikipedia.org/wiki/Superior_General_of_the_Society_of_Jesus"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish poet and educator (b. 1867)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Tev<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>v<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet and educator (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tev<PERSON><PERSON>_<PERSON>\" title=\"Tev<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish poet and educator (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tevfik_<PERSON>ret"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian sociologist and economist (b. 1845)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Vil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian sociologist and economist (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian sociologist and economist (b. 1845)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1928", "text": "<PERSON><PERSON>, Greek banker and diplomat, 97th Prime Minister of Greece (b. 1838)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek banker and diplomat, 97th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek banker and diplomat, 97th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1838)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1929", "text": "<PERSON>, Russian critic and producer, founded Ballets Russes (b. 1872)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian critic and producer, founded <a href=\"https://wikipedia.org/wiki/Ballets_Russes\" title=\"Ballets Russes\">Ballets Russes</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian critic and producer, founded <a href=\"https://wikipedia.org/wiki/Ballets_Russes\" title=\"Ballets Russes\">Ballets Russes</a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ballets Russes", "link": "https://wikipedia.org/wiki/Ballets_Russes"}]}, {"year": "1932", "text": "<PERSON>, French painter (b. 1861)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (b. 1861)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Spanish poet, playwright, and director (b. 1898)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Lorca\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, playwright, and director (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa_Lorca\" title=\"<PERSON>\"><PERSON></a>, Spanish poet, playwright, and director (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Federico_Garc%C3%ADa_Lorca"}]}, {"year": "1942", "text": "<PERSON>, Estonian footballer (b. 1901)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Kraków-born painter (b. 1858)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kraków-born painter (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kraków-born painter (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English conductor (b. 1869)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English conductor (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Chilean philanthropist (b. 1875)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Burgos\" title=\"Tomás Burgos\"><PERSON><PERSON></a>, Chilean philanthropist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Burgos\" title=\"Tomás Burgos\"><PERSON><PERSON></a>, Chilean philanthropist (b. 1875)", "links": [{"title": "<PERSON>ás <PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Burgos"}]}, {"year": "1950", "text": "<PERSON>, Italian physicist and engineer (b. 1871)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and engineer (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physicist and engineer (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician, 30th Prime Minister of Italy (b. 1881)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 30th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alcide_<PERSON>_<PERSON>i"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1957", "text": "<PERSON>, English soldier and painter (b. 1890)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and painter (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Luxembourg-born American author and publisher (b. 1884)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourg-born American author and publisher (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourg-born American author and publisher (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Polish-English journalist and historian (b. 1907)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English journalist and historian (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-English journalist and historian (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Ukrainian-American physicist and cosmologist (b. 1904)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American physicist and cosmologist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American physicist and cosmologist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Polish soldier and historian (b. 1909)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Jasienica\" title=\"<PERSON><PERSON>ł J<PERSON>eni<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and historian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pawe%C5%82_Jasienica\" title=\"<PERSON><PERSON>ł <PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and historian (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pawe%C5%82_<PERSON>asi<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American race car driver and engineer (b. 1937)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and engineer (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Scottish-English actor (b. 1900)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English actor (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alastair_Sim"}]}, {"year": "1976", "text": "<PERSON>, New Zealand cricketer (b. 1946)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian shot putter and discus thrower (b. 1914)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kreek\" title=\"Aleksander Kreek\"><PERSON>ek<PERSON><PERSON> Kreek</a>, Estonian shot putter and discus thrower (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kreek\" title=\"Aleksander Kreek\"><PERSON><PERSON><PERSON><PERSON> Kreek</a>, Estonian shot putter and discus thrower (b. 1914)", "links": [{"title": "Aleksander Kreek", "link": "https://wikipedia.org/wiki/Aleksander_Kreek"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American comedian and actor (b. 1890)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American comedian and actor (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German-Swiss businessman, father of <PERSON> (b. 1889)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman, father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman, father of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English actress, singer, and dancer (b. 1907)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress, singer, and dancer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Estonian wrestler (b. 1908)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/August_Neo\" title=\"August Neo\">August <PERSON></a>, Estonian wrestler (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Neo\" title=\"August Neo\">August <PERSON></a>, Estonian wrestler (b. 1908)", "links": [{"title": "August Neo", "link": "https://wikipedia.org/wiki/August_Neo"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, English actress (b. 1906)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Her<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English actress (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Australian rugby player (b. 1910)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Viv_Thicknesse\" title=\"Viv Thicknesse\"><PERSON><PERSON></a>, Australian rugby player (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Viv_Thicknesse\" title=\"Viv Thicknesse\"><PERSON><PERSON></a>, Australian rugby player (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv_Thicknesse"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Bangladeshi actor, director, and playwright (b. 1929)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi actor, director, and playwright (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi actor, director, and playwright (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Utpal_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American chemist and biologist, Nobel Prize laureate (b. 1901)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American chemist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1995", "text": "<PERSON>, French composer and musicologist (b. 1910)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and musicologist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and musicologist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Indian poet, author, and educator (b. 1948)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rahma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Brah<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian poet, author, and educator (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Brahma\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Brahma\"><PERSON><PERSON><PERSON></a>, Indian poet, author, and educator (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ma"}]}, {"year": "2001", "text": "<PERSON>, South African journalist and activist (b. 1933)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and activist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist and activist (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Honduran lawyer and politician, President of Honduras (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Honduran lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_Honduras\" title=\"President of Honduras\">President of Honduras</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Honduras", "link": "https://wikipedia.org/wiki/President_of_Honduras"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian diplomat (b. 1948)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian diplomat (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A9<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>lo\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian diplomat (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A9rg<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English academic and politician, Chancellor of the Duchy of Lancaster (b. 1949)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "2008", "text": "<PERSON>, Zambian lawyer and politician, 3rd President of Zambia (b. 1948)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zambian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Zambia\" title=\"President of Zambia\">President of Zambia</a> (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Zambia", "link": "https://wikipedia.org/wiki/President_of_Zambia"}]}, {"year": "2009", "text": "<PERSON>, American television producer, created 60 Minutes (b. 1922)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, created <i><a href=\"https://wikipedia.org/wiki/60_Minutes\" title=\"60 Minutes\">60 Minutes</a></i> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television producer, created <i><a href=\"https://wikipedia.org/wiki/60_Minutes\" title=\"60 Minutes\">60 Minutes</a></i> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "60 Minutes", "link": "https://wikipedia.org/wiki/60_Minutes"}]}, {"year": "2011", "text": "<PERSON><PERSON>, Chilean director and producer (b. 1941)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Ra%C3%<PERSON><PERSON>_<PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Chilean director and producer (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%<PERSON><PERSON>_<PERSON>_(director)\" title=\"<PERSON><PERSON> (director)\"><PERSON><PERSON></a>, Chilean director and producer (b. 1941)", "links": [{"title": "<PERSON><PERSON> (director)", "link": "https://wikipedia.org/wiki/Ra%C3%<PERSON><PERSON>_<PERSON>_(director)"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American journalist and critic (b. 1921)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and critic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and critic (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Norwegian canoe racer (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian canoe racer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian canoe racer (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>n"}]}, {"year": "2012", "text": "<PERSON>, English-American director and producer (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American director and producer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American poet and academic (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, Saudi Arabian prince (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Musa%27<PERSON>_bin_<PERSON><PERSON><PERSON>_Al_Saud\" title=\"<PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian prince (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Musa%27<PERSON>_bin_<PERSON><PERSON><PERSON>_<PERSON>_Saud\" title=\"<PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON></a>, Saudi Arabian prince (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Musa%27id_bin_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American director and producer (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Afghan politician, 8th President of Afghanistan (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Afghan politician, 8th <a href=\"https://wikipedia.org/wiki/President_of_Afghanistan\" title=\"President of Afghanistan\">President of Afghanistan</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Afghanistan", "link": "https://wikipedia.org/wiki/President_of_Afghanistan"}]}, {"year": "2013", "text": "<PERSON>, American singer-songwriter (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Donna_Hightower"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Palestinian poet and journalist (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian poet and journalist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Palestinian poet and journalist (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Iranian poet and activist (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and activist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Iranian poet and activist (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American photographer and journalist (b. 1973)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American photographer and journalist (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American photographer and journalist (b. 1973)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Anglo-Irish journalist and author (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Candida_Lycett_Green\" title=\"Candida Lycett Green\"><PERSON><PERSON><PERSON>y<PERSON></a>, Anglo-Irish journalist and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Candida_Lycett_Green\" title=\"Candida Lycett Green\"><PERSON><PERSON><PERSON>y<PERSON></a>, Anglo-Irish journalist and author (b. 1942)", "links": [{"title": "Candida Lycett Green", "link": "https://wikipedia.org/wiki/Candida_Lycett_Green"}]}, {"year": "2015", "text": "<PERSON>, American minister and activist (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and activist (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian activist and politician (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mehta\" title=\"San<PERSON> Mehta\"><PERSON><PERSON></a>, Indian activist and politician (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mehta\" title=\"<PERSON><PERSON> Mehta\"><PERSON><PERSON></a>, Indian activist and politician (b. 1935)", "links": [{"title": "<PERSON><PERSON> Me<PERSON>", "link": "https://wikipedia.org/wiki/Sanat_<PERSON>hta"}]}, {"year": "2016", "text": "<PERSON>, American actor and voice artist (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and voice artist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and voice artist (b. 1935)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "2017", "text": "<PERSON>, American comedian, author and activist (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, author and activist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, author and activist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Danish businessman and billionaire, founder and owner of the Danish retail chain JYSK (b. 1948)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(entrepreneur)\" class=\"mw-redirect\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, Danish businessman and billionaire, founder and owner of the Danish retail chain <a href=\"https://wikipedia.org/wiki/JYSK\" class=\"mw-redirect\" title=\"JYSK\">JYSK</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(entrepreneur)\" class=\"mw-redirect\" title=\"<PERSON> (entrepreneur)\"><PERSON></a>, Danish businessman and billionaire, founder and owner of the Danish retail chain <a href=\"https://wikipedia.org/wiki/JYSK\" class=\"mw-redirect\" title=\"JYSK\">JYS<PERSON></a> (b. 1948)", "links": [{"title": "<PERSON> (entrepreneur)", "link": "https://wikipedia.org/wiki/<PERSON>(entrepreneur)"}, {"title": "JYSK", "link": "https://wikipedia.org/wiki/JYSK"}]}, {"year": "2021", "text": "<PERSON>, Japanese actor (b. 1939)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese actor (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Polish supercentenarian (b. 1906)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish supercentenarian (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish supercentenarian (b. 1906)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovak musician (b. 1954)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak musician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Slovak musician (b. 1954)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American-Spanish supercentenarian (b. 1907)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Maria_B<PERSON>yas\" title=\"<PERSON>\"><PERSON></a>, American-Spanish supercentenarian (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_B<PERSON>yas\" title=\"Maria Branyas\"><PERSON></a>, American-Spanish supercentenarian (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Branyas"}]}]}}