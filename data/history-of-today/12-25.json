{"date": "December 25", "url": "https://wikipedia.org/wiki/December_25", "data": {"Events": [{"year": "36", "text": "Forces of Emperor <PERSON><PERSON><PERSON> of the Eastern Han, under the command of <PERSON>, conquer the separatist Chengjia empire, reuniting China.", "html": "36 - Forces of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Eastern_Han\" class=\"mw-redirect\" title=\"Eastern Han\">Eastern Han</a>, under the command of <a href=\"https://wikipedia.org/wiki/Wu_Han_(Han_dynasty)\" title=\"<PERSON> Han (Han dynasty)\"><PERSON></a>, conquer the separatist <a href=\"https://wikipedia.org/wiki/Chengjia\" title=\"Chengji<PERSON>\">Cheng<PERSON>a</a> empire, reuniting China.", "no_year_html": "Forces of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Eastern_Han\" class=\"mw-redirect\" title=\"Eastern Han\">Eastern Han</a>, under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Han_dynasty)\" title=\"<PERSON> Han (Han dynasty)\"><PERSON></a>, conquer the separatist <a href=\"https://wikipedia.org/wiki/Chengjia\" title=\"Chengjia\">Chengjia</a> empire, reuniting China.", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>"}, {"title": "Eastern Han", "link": "https://wikipedia.org/wiki/Eastern_Han"}, {"title": "<PERSON> (Han dynasty)", "link": "https://wikipedia.org/wiki/<PERSON>_Han_(Han_dynasty)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cheng<PERSON>a"}]}, {"year": "274", "text": "A temple to <PERSON> is dedicated in Rome by Emperor <PERSON><PERSON><PERSON>.", "html": "274 - A <a href=\"https://wikipedia.org/wiki/Temple_of_the_Sun_(Rome)\" title=\"Temple of the Sun (Rome)\">temple</a> to <a href=\"https://wikipedia.org/wiki/Sol_Invictus\" title=\"Sol Invictus\"><PERSON> Invictus</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> by Emperor <a href=\"https://wikipedia.org/wiki/Aurelian\" title=\"Aurelian\">Aurelian</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Temple_of_the_Sun_(Rome)\" title=\"Temple of the Sun (Rome)\">temple</a> to <a href=\"https://wikipedia.org/wiki/Sol_Invictus\" title=\"Sol Invictus\"><PERSON> Invictus</a> is dedicated in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> by Emperor <a href=\"https://wikipedia.org/wiki/Aurelian\" title=\"Aurelian\">Aurelian</a>.", "links": [{"title": "Temple of the Sun (Rome)", "link": "https://wikipedia.org/wiki/Temple_of_the_Sun_(Rome)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sol_Invictus"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aurelian"}]}, {"year": "333", "text": "Roman Emperor <PERSON> the Great elevates his youngest son <PERSON><PERSON><PERSON> to the rank of Caesar.", "html": "333 - Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> elevates his youngest son <a href=\"https://wikipedia.org/wiki/Constans\" title=\"<PERSON>stan<PERSON>\"><PERSON><PERSON><PERSON></a> to the rank of <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a></i>.", "no_year_html": "Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> elevates his youngest son <a href=\"https://wikipedia.org/wiki/Constans\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> to the rank of <i><a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\"><PERSON></a></i>.", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Constans"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}]}, {"year": "336", "text": "First documentary sign of Christmas celebration in Rome.", "html": "336 - First documentary sign of <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a> celebration in Rome.", "no_year_html": "First documentary sign of <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a> celebration in Rome.", "links": [{"title": "Christmas", "link": "https://wikipedia.org/wiki/Christmas"}]}, {"year": "350", "text": "<PERSON><PERSON><PERSON><PERSON> meets <PERSON><PERSON><PERSON> at Naissus (Serbia) and is forced to abdicate his imperial title. <PERSON><PERSON><PERSON> allows him to live as a private citizen on a state pension.", "html": "350 - <a href=\"https://wikipedia.org/wiki/Vetranio\" title=\"Vetranio\">Vetrani<PERSON></a> meets <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a> at <a href=\"https://wikipedia.org/wiki/Ni%C5%A1\" title=\"Ni<PERSON>\">Naissus</a> (<a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>) and is forced to abdicate his imperial title. <PERSON><PERSON><PERSON> allows him to live as a private citizen on a state pension.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vetranio\" title=\"Vetranio\">Vetrani<PERSON></a> meets <a href=\"https://wikipedia.org/wiki/Constantius_II\" title=\"Constantius II\">Constantius II</a> at <a href=\"https://wikipedia.org/wiki/Ni%C5%A1\" title=\"Ni<PERSON>\">Naissus</a> (<a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>) and is forced to abdicate his imperial title. <PERSON><PERSON><PERSON> allows him to live as a private citizen on a state pension.", "links": [{"title": "Vetranio", "link": "https://wikipedia.org/wiki/Vetranio"}, {"title": "<PERSON><PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/Constantius_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni%C5%A1"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}]}, {"year": "508", "text": "<PERSON><PERSON><PERSON>, king of the Franks, is baptized into the Catholic faith at Reims, by <PERSON> <PERSON><PERSON><PERSON><PERSON>.", "html": "508 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> I\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"Franks\">Franks</a>, is <a href=\"https://wikipedia.org/wiki/Baptism\" title=\"Baptism\">baptized</a> into the <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholic</a> faith at <a href=\"https://wikipedia.org/wiki/Reims\" title=\"Reims\">Reims</a>, by <a href=\"https://wikipedia.org/wiki/Saint_Remigius\" title=\"Saint Remigius\">Saint Remigius</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Franks\" title=\"Franks\"><PERSON></a>, is <a href=\"https://wikipedia.org/wiki/Baptism\" title=\"Baptism\">baptized</a> into the <a href=\"https://wikipedia.org/wiki/Catholicism\" class=\"mw-redirect\" title=\"Catholicism\">Catholic</a> faith at <a href=\"https://wikipedia.org/wiki/Reims\" title=\"Reims\">Reims</a>, by <a href=\"https://wikipedia.org/wiki/Saint_Remigius\" title=\"Saint Remigius\">Saint Remigius</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Franks"}, {"title": "Baptism", "link": "https://wikipedia.org/wiki/Baptism"}, {"title": "Catholicism", "link": "https://wikipedia.org/wiki/Catholicism"}, {"title": "Reims", "link": "https://wikipedia.org/wiki/Reims"}, {"title": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saint_Remigius"}]}, {"year": "597", "text": "<PERSON> of Canterbury and his fellow-labourers baptise in Kent more than 10,000 Anglo-Saxons.", "html": "597 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury\" title=\"<PERSON> of Canterbury\"><PERSON> of Canterbury</a> and his fellow-labourers baptise in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kent\" title=\"Kingdom of Kent\">Kent</a> more than 10,000 <a href=\"https://wikipedia.org/wiki/Anglo-Saxons\" title=\"Anglo-Saxons\">Anglo-Saxons</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury\" title=\"<PERSON> of Canterbury\"><PERSON> of Canterbury</a> and his fellow-labourers baptise in <a href=\"https://wikipedia.org/wiki/Kingdom_of_Kent\" title=\"Kingdom of Kent\">Kent</a> more than 10,000 <a href=\"https://wikipedia.org/wiki/Anglo-Saxons\" title=\"Anglo-Saxons\">Anglo-Saxons</a>.", "links": [{"title": "<PERSON> of Canterbury", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Canterbury"}, {"title": "Kingdom of Kent", "link": "https://wikipedia.org/wiki/Kingdom_of_Kent"}, {"title": "Anglo-Saxons", "link": "https://wikipedia.org/wiki/Anglo-Saxons"}]}, {"year": "800", "text": "The coronation of <PERSON><PERSON><PERSON><PERSON> as Holy Roman Emperor, in Rome.", "html": "800 - The coronation of <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlemagne\"><PERSON><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, in Rome.", "no_year_html": "The coronation of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a>, in Rome.", "links": [{"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}]}, {"year": "820", "text": "Eastern Emperor <PERSON> is murdered in a church of the Great Palace of Constantinople by followers of <PERSON>.", "html": "820 - Eastern Emperor <a href=\"https://wikipedia.org/wiki/Leo_V_the_Armenian\" title=\"<PERSON> V the Armenian\"><PERSON></a> is murdered in a <a href=\"https://wikipedia.org/wiki/Church_of_the_Virgin_of_the_Pharos\" title=\"Church of the Virgin of the Pharos\">church</a> of the <a href=\"https://wikipedia.org/wiki/Great_Palace_of_Constantinople\" title=\"Great Palace of Constantinople\">Great Palace</a> of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> by followers of <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>.", "no_year_html": "Eastern Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>_V_the_Armenian\" title=\"<PERSON> V the Armenian\"><PERSON></a> is murdered in a <a href=\"https://wikipedia.org/wiki/Church_of_the_Virgin_of_the_Pharos\" title=\"Church of the Virgin of the Pharos\">church</a> of the <a href=\"https://wikipedia.org/wiki/Great_Palace_of_Constantinople\" title=\"Great Palace of Constantinople\">Great Palace</a> of <a href=\"https://wikipedia.org/wiki/Constantinople\" title=\"Constantinople\">Constantinople</a> by followers of <a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"<PERSON> II\"><PERSON> II</a>.", "links": [{"title": "<PERSON> the Armenian", "link": "https://wikipedia.org/wiki/Leo_V_the_Armenian"}, {"title": "Church of the Virgin of the Pharos", "link": "https://wikipedia.org/wiki/Church_of_the_Virgin_of_the_<PERSON><PERSON><PERSON>"}, {"title": "Great Palace of Constantinople", "link": "https://wikipedia.org/wiki/Great_Palace_of_Constantinople"}, {"title": "Constantinople", "link": "https://wikipedia.org/wiki/Constantinople"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1000", "text": "The foundation of the Kingdom of Hungary: Hungary is established as a Christian kingdom by <PERSON> of Hungary.", "html": "1000 - The foundation of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a>: Hungary is established as a Christian kingdom by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> I of Hungary\"><PERSON> of Hungary</a>.", "no_year_html": "The foundation of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Hungary\" title=\"Kingdom of Hungary\">Kingdom of Hungary</a>: Hungary is established as a Christian kingdom by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Hungary\" title=\"<PERSON> I of Hungary\"><PERSON> of Hungary</a>.", "links": [{"title": "Kingdom of Hungary", "link": "https://wikipedia.org/wiki/Kingdom_of_Hungary"}, {"title": "<PERSON> of Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Hungary"}]}, {"year": "1013", "text": "<PERSON><PERSON><PERSON> takes control of the Danelaw and is proclaimed king of England.", "html": "1013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Forkbeard\" title=\"<PERSON><PERSON><PERSON>beard\"><PERSON><PERSON><PERSON></a> takes control of the <a href=\"https://wikipedia.org/wiki/Danelaw\" title=\"Danelaw\"><PERSON><PERSON></a> and is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>beard\" title=\"<PERSON><PERSON><PERSON>ard\"><PERSON><PERSON><PERSON></a> takes control of the <a href=\"https://wikipedia.org/wiki/Danelaw\" title=\"Danelaw\">Dane<PERSON></a> and is proclaimed king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_England\" title=\"Kingdom of England\">England</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>beard"}, {"title": "Danelaw", "link": "https://wikipedia.org/wiki/Dane<PERSON>"}, {"title": "Kingdom of England", "link": "https://wikipedia.org/wiki/Kingdom_of_England"}]}, {"year": "1025", "text": "Coronation of <PERSON><PERSON><PERSON><PERSON> as king of Poland.", "html": "1025 - Coronation of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> as king of Poland.", "no_year_html": "Coronation of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> II Lambert\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a> as king of Poland.", "links": [{"title": "Mieszko II Lambert", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Lambert"}]}, {"year": "1046", "text": "<PERSON> is crowned Holy Roman Emperor by <PERSON> <PERSON>.", "html": "1046 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> is crowned <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> by <a href=\"https://wikipedia.org/wiki/Pope_Clement_II\" title=\"Pope Clement II\">Pope Clement II</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> III, Holy Roman Emperor\"><PERSON> III</a> is crowned <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> by <a href=\"https://wikipedia.org/wiki/Pope_Clement_II\" title=\"Pope Clement II\"><PERSON> Clement II</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1066", "text": "<PERSON> the Conqueror, Duke of Normandy is crowned king of England, at Westminster Abbey, London.", "html": "1066 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of <PERSON> I\">crowned king of England</a>, at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Conqueror\" title=\"<PERSON> the Conqueror\"><PERSON> the Conqueror</a>, <a href=\"https://wikipedia.org/wiki/Duke_of_Normandy\" title=\"Duke of Normandy\">Duke of Normandy</a> is <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Coronation of William I\">crowned king of England</a>, at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "<PERSON> the Conqueror", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Duke of Normandy", "link": "https://wikipedia.org/wiki/Duke_of_Normandy"}, {"title": "Coronation of <PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "1076", "text": "Coronation of <PERSON><PERSON><PERSON> II the Generous as king of Poland.", "html": "1076 - Coronation of <a href=\"https://wikipedia.org/wiki/Boles%C5%82aw_II_the_Generous\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II the Generous\"><PERSON><PERSON><PERSON> the Generous</a> as king of Poland.", "no_year_html": "Coronation of <a href=\"https://wikipedia.org/wiki/<PERSON>les%C5%82aw_II_the_Generous\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> II the Generous\"><PERSON><PERSON><PERSON> II the Generous</a> as king of Poland.", "links": [{"title": "<PERSON><PERSON><PERSON> the Generous", "link": "https://wikipedia.org/wiki/Boles%C5%82aw_II_the_Generous"}]}, {"year": "1100", "text": "<PERSON> of Boulogne is crowned the first King of Jerusalem in the Church of the Nativity in Bethlehem.", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> of Boulogne</a> is crowned the first <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a> in the <a href=\"https://wikipedia.org/wiki/Church_of_the_Nativity\" title=\"Church of the Nativity\">Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jerusalem\" title=\"<PERSON> I of Jerusalem\"><PERSON> of Boulogne</a> is crowned the first <a href=\"https://wikipedia.org/wiki/King_of_Jerusalem\" title=\"King of Jerusalem\">King of Jerusalem</a> in the <a href=\"https://wikipedia.org/wiki/Church_of_the_Nativity\" title=\"Church of the Nativity\">Church of the Nativity</a> in <a href=\"https://wikipedia.org/wiki/Bethlehem\" title=\"Bethlehem\">Bethlehem</a>.", "links": [{"title": "<PERSON> of Jerusalem", "link": "https://wikipedia.org/wiki/Baldwin_I_of_Jerusalem"}, {"title": "King of Jerusalem", "link": "https://wikipedia.org/wiki/King_of_Jerusalem"}, {"title": "Church of the Nativity", "link": "https://wikipedia.org/wiki/Church_of_the_Nativity"}, {"title": "Bethlehem", "link": "https://wikipedia.org/wiki/Bethlehem"}]}, {"year": "1130", "text": "Count <PERSON> of Sicily is crowned the first king of Sicily.", "html": "1130 - Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> II of Sicily\"><PERSON> of Sicily</a> is crowned the first king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily\" title=\"Kingdom of Sicily\">Sicily</a>.", "no_year_html": "Count <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily\" title=\"<PERSON> of Sicily\"><PERSON> of Sicily</a> is crowned the first king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Sicily\" title=\"Kingdom of Sicily\">Sicily</a>.", "links": [{"title": "<PERSON> of Sicily", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sicily"}, {"title": "Kingdom of Sicily", "link": "https://wikipedia.org/wiki/Kingdom_of_Sicily"}]}, {"year": "1261", "text": "Eleven-year-old <PERSON> of the restored Eastern Roman Empire is deposed and blinded by orders of his co-ruler <PERSON>.", "html": "1261 - Eleven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the restored <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a> is deposed and blinded by orders of his co-ruler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>gos\" title=\"<PERSON> VIII <PERSON>ologos\"><PERSON></a>.", "no_year_html": "Eleven-year-old <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the restored <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Eastern Roman Empire</a> is deposed and blinded by orders of his co-ruler <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>olo<PERSON>\" title=\"<PERSON> VIII Palaiologos\"><PERSON>ologos</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1492", "text": "The carrack Santa María, commanded by <PERSON>, runs onto a reef off Haiti due to an improper watch.", "html": "1492 - The <a href=\"https://wikipedia.org/wiki/Carrack\" title=\"Carrack\">carrack</a> <i><a href=\"https://wikipedia.org/wiki/Santa_Mar%C3%ADa_(ship)\" title=\"<PERSON> María (ship)\">Santa María</a></i>, commanded by <a href=\"https://wikipedia.org/wiki/Christopher_Columbus\" title=\"Christopher Columbus\"><PERSON></a>, runs onto a <a href=\"https://wikipedia.org/wiki/Reef\" title=\"Reef\">reef</a> off <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> due to an improper <a href=\"https://wikipedia.org/wiki/Watch_system\" class=\"mw-redirect\" title=\"Watch system\">watch</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Carrack\" title=\"Carrack\">carrack</a> <i><a href=\"https://wikipedia.org/wiki/Santa_Mar%C3%ADa_(ship)\" title=\"<PERSON> María (ship)\">Santa María</a></i>, commanded by <a href=\"https://wikipedia.org/wiki/Christopher_Columbus\" title=\"Christopher Columbus\"><PERSON></a>, runs onto a <a href=\"https://wikipedia.org/wiki/Reef\" title=\"Reef\">reef</a> off <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> due to an improper <a href=\"https://wikipedia.org/wiki/Watch_system\" class=\"mw-redirect\" title=\"Watch system\">watch</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ack"}, {"title": "Santa María (ship)", "link": "https://wikipedia.org/wiki/Santa_Mar%C3%ADa_(ship)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reef", "link": "https://wikipedia.org/wiki/Reef"}, {"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "Watch system", "link": "https://wikipedia.org/wiki/Watch_system"}]}, {"year": "1553", "text": "Battle of Tucapel: Mapuche rebels under <PERSON><PERSON><PERSON> defeat the Spanish conquistadors and executes the governor of Chile, <PERSON>.", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Battle_of_Tucapel\" title=\"Battle of Tucapel\">Battle of Tucapel</a>: <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> rebels under <a href=\"https://wikipedia.org/wiki/Lautaro\" title=\"Lautaro\"><PERSON><PERSON><PERSON></a> defeat the Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> and executes the governor of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, <a href=\"https://wikipedia.org/wiki/Pedro_de_Valdivia\" title=\"Pedro de Valdivia\">Pedro de Valdivia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Tucapel\" title=\"Battle of Tucapel\">Battle of Tucapel</a>: <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuche</a> rebels under <a href=\"https://wikipedia.org/wiki/Lautaro\" title=\"Lautaro\"><PERSON><PERSON><PERSON></a> defeat the Spanish <a href=\"https://wikipedia.org/wiki/Conquistador\" title=\"Conquistador\">conquistadors</a> and executes the governor of <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, <a href=\"https://wikipedia.org/wiki/Pedro_de_Valdivia\" title=\"Pedro de Valdivia\">Pedro de Valdivia</a>.", "links": [{"title": "Battle of Tucapel", "link": "https://wikipedia.org/wiki/Battle_of_Tucapel"}, {"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>o"}, {"title": "Conquistador", "link": "https://wikipedia.org/wiki/Conquistador"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}, {"title": "<PERSON> Valdi<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>"}]}, {"year": "1559", "text": "<PERSON> <PERSON> is elected, four months after his predecessor's death.", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> is <a href=\"https://wikipedia.org/wiki/1559_papal_conclave\" title=\"1559 papal conclave\">elected</a>, four months after his predecessor's death.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Pius_IV\" title=\"Pope Pius IV\">Pope <PERSON> IV</a> is <a href=\"https://wikipedia.org/wiki/1559_papal_conclave\" title=\"1559 papal conclave\">elected</a>, four months after his predecessor's death.", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1559 papal conclave", "link": "https://wikipedia.org/wiki/1559_papal_conclave"}]}, {"year": "1724", "text": "<PERSON><PERSON> <PERSON><PERSON> leads the first performance of <PERSON><PERSON><PERSON> seist <PERSON>, <PERSON><PERSON>, BWV 91, in Leipzig, based on <PERSON>'s 1524 Christmas hymn.", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_seist_du,_<PERSON><PERSON>_Christ,_BWV_91\" title=\"<PERSON><PERSON><PERSON> seist du, <PERSON><PERSON> Christ, BWV 91\"><i><PERSON><PERSON><PERSON> seist du, <PERSON><PERSON> Christ</i>, BWV 91</a>, in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, based on <PERSON>'s 1524 <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_seist_du,_Je<PERSON>_Christ\" title=\"Gelobet seist du, Jesu Christ\">Christmas hymn</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> leads the first performance of <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_seist_du,_<PERSON><PERSON>_Christ,_BWV_91\" title=\"<PERSON><PERSON><PERSON> seist du, <PERSON><PERSON> Christ, BWV 91\"><i><PERSON><PERSON><PERSON> seist du, <PERSON><PERSON> Christ</i>, BWV 91</a>, in <a href=\"https://wikipedia.org/wiki/Leipzig\" title=\"Leipzig\">Leipzig</a>, based on <PERSON>'s 1524 <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_seist_du,_Je<PERSON>_Christ\" title=\"Gelobet seist du, Jesu Christ\">Christmas hymn</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> seist du, <PERSON><PERSON>, BWV 91", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_seist_du,_<PERSON><PERSON>_<PERSON>,_<PERSON>_91"}, {"title": "Leipzig", "link": "https://wikipedia.org/wiki/Leipzig"}, {"title": "<PERSON><PERSON><PERSON> seist <PERSON>, <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_seist_du,_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON><PERSON>'s Comet is sighted by <PERSON>, confirming <PERSON>'s prediction of its passage.  This was the first passage of a comet predicted ahead of time.", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> is sighted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, confirming <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s prediction of its passage. This was the first passage of a comet predicted ahead of time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a> is sighted by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, confirming <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>'s prediction of its passage. This was the first passage of a comet predicted ahead of time.", "links": [{"title": "Halley's Comet", "link": "https://wikipedia.org/wiki/Halley%27s_Comet"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1766", "text": "Mapuches in Chile launch a series of surprise attacks against the Spanish starting the Mapuche uprising of 1766.", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> in <a href=\"https://wikipedia.org/wiki/Colonial_Chile\" title=\"Colonial Chile\">Chile</a> launch a series of surprise attacks against the Spanish starting the <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1766\" title=\"Mapuche uprising of 1766\">Mapuche uprising of 1766</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mapuche\" title=\"Mapuche\">Mapuches</a> in <a href=\"https://wikipedia.org/wiki/Colonial_Chile\" title=\"Colonial Chile\">Chile</a> launch a series of surprise attacks against the Spanish starting the <a href=\"https://wikipedia.org/wiki/Mapuche_uprising_of_1766\" title=\"Mapuche uprising of 1766\">Mapuche uprising of 1766</a>.", "links": [{"title": "Mapuche", "link": "https://wikipedia.org/wiki/Mapuche"}, {"title": "Colonial Chile", "link": "https://wikipedia.org/wiki/Colonial_Chile"}, {"title": "Mapuche uprising of 1766", "link": "https://wikipedia.org/wiki/Mapuche_uprising_of_1766"}]}, {"year": "1776", "text": "American Revolutionary War: General <PERSON> and the Continental Army cross the Delaware River at night to attack Hessian forces serving Great Britain at Trenton, New Jersey, the next day.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> <a href=\"https://wikipedia.org/wiki/George_Washington%27s_crossing_of_the_Delaware_River\" title=\"<PERSON> Washington's crossing of the Delaware River\">cross the Delaware River</a> at night to <a href=\"https://wikipedia.org/wiki/Battle_of_Trenton\" title=\"Battle of Trenton\">attack</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\"><PERSON>ian</a> forces serving <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> at <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a>, the next day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/Continental_Army\" title=\"Continental Army\">Continental Army</a> <a href=\"https://wikipedia.org/wiki/George_Washington%27s_crossing_of_the_Delaware_River\" title=\"<PERSON> Washington's crossing of the Delaware River\">cross the Delaware River</a> at night to <a href=\"https://wikipedia.org/wiki/Battle_of_Trenton\" title=\"Battle of Trenton\">attack</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)\" title=\"<PERSON><PERSON> (soldier)\">Hessian</a> forces serving <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">Great Britain</a> at <a href=\"https://wikipedia.org/wiki/Trenton,_New_Jersey\" title=\"Trenton, New Jersey\">Trenton, New Jersey</a>, the next day.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Continental Army", "link": "https://wikipedia.org/wiki/Continental_Army"}, {"title": "George <PERSON>'s crossing of the Delaware River", "link": "https://wikipedia.org/wiki/George_Washington%27s_crossing_of_the_Delaware_River"}, {"title": "Battle of Trenton", "link": "https://wikipedia.org/wiki/Battle_of_Trenton"}, {"title": "<PERSON><PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(soldier)"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Trenton, New Jersey", "link": "https://wikipedia.org/wiki/Trenton,_New_Jersey"}]}, {"year": "1793", "text": "Northwest Indian War: General \"<PERSON> and a 300 man detachment identify the site of St. Clair's 1791 defeat by the large number of unburied human remains at modern Fort Recovery, Ohio.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">\"<PERSON>\" Wayne</a> and a <a href=\"https://wikipedia.org/wiki/Legion_of_the_United_States\" title=\"Legion of the United States\">300 man detachment</a> identify the site of <a href=\"https://wikipedia.org/wiki/St._Clair%27s_defeat\" title=\"<PERSON>. Clair's defeat\"><PERSON><PERSON> Clair's 1791 defeat</a> by the large number of unburied human remains at modern <a href=\"https://wikipedia.org/wiki/Fort_Recovery,_Ohio\" title=\"Fort Recovery, Ohio\">Fort Recovery, Ohio</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Indian_War\" title=\"Northwest Indian War\">Northwest Indian War</a>: General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">\"<PERSON>\" Wayne</a> and a <a href=\"https://wikipedia.org/wiki/Legion_of_the_United_States\" title=\"Legion of the United States\">300 man detachment</a> identify the site of <a href=\"https://wikipedia.org/wiki/St._Clair%27s_defeat\" title=\"St. Clair's defeat\"><PERSON>. Clair's 1791 defeat</a> by the large number of unburied human remains at modern <a href=\"https://wikipedia.org/wiki/Fort_Recovery,_Ohio\" title=\"Fort Recovery, Ohio\">Fort Recovery, Ohio</a>.", "links": [{"title": "Northwest Indian War", "link": "https://wikipedia.org/wiki/Northwest_Indian_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Legion of the United States", "link": "https://wikipedia.org/wiki/Legion_of_the_United_States"}, {"title": "<PERSON><PERSON>'s defeat", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s_defeat"}, {"title": "Fort Recovery, Ohio", "link": "https://wikipedia.org/wiki/Fort_Recovery,_Ohio"}]}, {"year": "1809", "text": "Dr. <PERSON><PERSON><PERSON><PERSON> performs the first ovariotomy, removing a 22-pound tumor.", "html": "1809 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> performs the first <a href=\"https://wikipedia.org/wiki/Ovariotomy\" class=\"mw-redirect\" title=\"Ovariotomy\">ovariotomy</a>, removing a 22-pound <a href=\"https://wikipedia.org/wiki/Neoplasm\" title=\"Neoplasm\">tumor</a>.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> performs the first <a href=\"https://wikipedia.org/wiki/Ovariotomy\" class=\"mw-redirect\" title=\"Ovariotomy\">ovariotomy</a>, removing a 22-pound <a href=\"https://wikipedia.org/wiki/Neoplasm\" title=\"Neoplasm\">tumor</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ovariotomy", "link": "https://wikipedia.org/wiki/Ovariotomy"}, {"title": "Neoplasm", "link": "https://wikipedia.org/wiki/Neoplasm"}]}, {"year": "1814", "text": "Rev. <PERSON> holds the first Christian service on land in New Zealand at Rangihoua Bay.", "html": "1814 - Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> holds the first Christian service on land in New Zealand at <a href=\"https://wikipedia.org/wiki/Rangihoua_Bay\" title=\"Rangihoua Bay\">Rangihoua Bay</a>.", "no_year_html": "Rev. <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> holds the first Christian service on land in New Zealand at <a href=\"https://wikipedia.org/wiki/Rangihoua_Bay\" title=\"Rangihoua Bay\">Rangihoua Bay</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Rangihoua Bay", "link": "https://wikipedia.org/wiki/Rangihoua_Bay"}]}, {"year": "1815", "text": "The Handel and Haydn Society, oldest continually performing arts organization in the United States, gives its first performance.", "html": "1815 - The <a href=\"https://wikipedia.org/wiki/Handel_and_Haydn_Society\" title=\"Handel and Haydn Society\">Handel and Haydn Society</a>, oldest continually performing arts organization in the United States, gives its first performance.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Handel_and_Haydn_Society\" title=\"Handel and Haydn Society\">Handel and Haydn Society</a>, oldest continually performing arts organization in the United States, gives its first performance.", "links": [{"title": "Handel and Haydn Society", "link": "https://wikipedia.org/wiki/Handel_and_Haydn_Society"}]}, {"year": "1826", "text": "The Eggnog Riot at the United States Military Academy concludes after beginning the previous evening.", "html": "1826 - The <a href=\"https://wikipedia.org/wiki/Eggnog_Riot\" class=\"mw-redirect\" title=\"Eggnog Riot\">Eggnog Riot</a> at the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a> concludes after beginning the previous evening.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eggnog_Riot\" class=\"mw-redirect\" title=\"Eggnog Riot\">Eggnog Riot</a> at the <a href=\"https://wikipedia.org/wiki/United_States_Military_Academy\" title=\"United States Military Academy\">United States Military Academy</a> concludes after beginning the previous evening.", "links": [{"title": "Eggnog Riot", "link": "https://wikipedia.org/wiki/Eggnog_Riot"}, {"title": "United States Military Academy", "link": "https://wikipedia.org/wiki/United_States_Military_Academy"}]}, {"year": "1831", "text": "The Great Jamaican Slave Revolt begins; up to 20% of Jamaica's slaves mobilize in an ultimately unsuccessful fight for freedom.", "html": "1831 - The <a href=\"https://wikipedia.org/wiki/Baptist_War\" title=\"Baptist War\">Great Jamaican Slave Revolt</a> begins; up to 20% of <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>'s slaves mobilize in an ultimately unsuccessful fight for freedom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Baptist_War\" title=\"Baptist War\">Great Jamaican Slave Revolt</a> begins; up to 20% of <a href=\"https://wikipedia.org/wiki/Jamaica\" title=\"Jamaica\">Jamaica</a>'s slaves mobilize in an ultimately unsuccessful fight for freedom.", "links": [{"title": "Baptist War", "link": "https://wikipedia.org/wiki/Baptist_War"}, {"title": "Jamaica", "link": "https://wikipedia.org/wiki/Jamaica"}]}, {"year": "1837", "text": "Second Seminole War: American general <PERSON> leads 1,100 troops against the Seminoles at the Battle of Lake Okeechobee.", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Second_Seminole_War\" title=\"Second Seminole War\">Second Seminole War</a>: American general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads 1,100 troops against the <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminoles</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Okeechobee\" title=\"Battle of Lake Okeechobee\">Battle of Lake Okeechobee</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Seminole_War\" title=\"Second Seminole War\">Second Seminole War</a>: American general <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leads 1,100 troops against the <a href=\"https://wikipedia.org/wiki/Seminole\" title=\"Seminole\">Seminoles</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Okeechobee\" title=\"Battle of Lake Okeechobee\">Battle of Lake Okeechobee</a>.", "links": [{"title": "Second Seminole War", "link": "https://wikipedia.org/wiki/Second_Seminole_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seminole", "link": "https://wikipedia.org/wiki/Seminole"}, {"title": "Battle of Lake Okeechobee", "link": "https://wikipedia.org/wiki/Battle_of_Lake_Okeechobee"}]}, {"year": "1868", "text": "Pardons for ex-Confederates: United States President <PERSON> grants an unconditional pardon to all Confederate veterans.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Pardons_for_ex-Confederates\" title=\"Pardons for ex-Confederates\">Pardons for ex-Confederates</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> grants an unconditional <a href=\"https://wikipedia.org/wiki/Pardon\" title=\"Pardon\">pardon</a> to all <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> veterans.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pardons_for_ex-Confederates\" title=\"Pardons for ex-Confederates\">Pardons for ex-Confederates</a>: United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> grants an unconditional <a href=\"https://wikipedia.org/wiki/Pardon\" title=\"Pardon\">pardon</a> to all <a href=\"https://wikipedia.org/wiki/Confederate_States_Army\" title=\"Confederate States Army\">Confederate</a> veterans.", "links": [{"title": "Pardons for ex-Confederates", "link": "https://wikipedia.org/wiki/Pardons_for_ex-Confederates"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pardon"}, {"title": "Confederate States Army", "link": "https://wikipedia.org/wiki/Confederate_States_Army"}]}, {"year": "1870", "text": "Wagner's <PERSON><PERSON><PERSON> is first performed.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Wagner\" class=\"mw-redirect\" title=\"Wagner\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> is first performed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wagner\" class=\"mw-redirect\" title=\"Wagner\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> is first performed.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wagner"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1914", "text": "World War I: A series of unofficial truces occur across the Western Front to celebrate Christmas.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A <a href=\"https://wikipedia.org/wiki/Christmas_truce\" title=\"Christmas truce\">series of unofficial truces</a> occur across the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a> to celebrate <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: A <a href=\"https://wikipedia.org/wiki/Christmas_truce\" title=\"Christmas truce\">series of unofficial truces</a> occur across the <a href=\"https://wikipedia.org/wiki/Western_Front_(World_War_I)\" title=\"Western Front (World War I)\">Western Front</a> to celebrate <a href=\"https://wikipedia.org/wiki/Christmas\" title=\"Christmas\">Christmas</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Christmas truce", "link": "https://wikipedia.org/wiki/Christmas_truce"}, {"title": "Western Front (World War I)", "link": "https://wikipedia.org/wiki/Western_Front_(World_War_I)"}, {"title": "Christmas", "link": "https://wikipedia.org/wiki/Christmas"}]}, {"year": "1915", "text": "The National Protection War breaks out against the Empire of China, as military leaders <PERSON><PERSON> and <PERSON> proclaim the independence of Yunnan and begin a campaign to restore the Republic.", "html": "1915 - The <a href=\"https://wikipedia.org/wiki/National_Protection_War\" title=\"National Protection War\">National Protection War</a> breaks out against the <a href=\"https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)\" title=\"Empire of China (1915-1916)\">Empire of China</a>, as military leaders <a href=\"https://wikipedia.org/wiki/Cai_E\" title=\"Cai E\">Cai E</a> and <a href=\"https://wikipedia.org/wiki/Tang_Jiyao\" title=\"Tang Jiyao\"><PERSON> Ji<PERSON></a> proclaim the independence of <a href=\"https://wikipedia.org/wiki/Yunnan\" title=\"Yunnan\">Yunnan</a> and begin a campaign to restore the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Protection_War\" title=\"National Protection War\">National Protection War</a> breaks out against the <a href=\"https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)\" title=\"Empire of China (1915-1916)\">Empire of China</a>, as military leaders <a href=\"https://wikipedia.org/wiki/Cai_E\" title=\"Cai E\">Cai E</a> and <a href=\"https://wikipedia.org/wiki/Tang_Jiyao\" title=\"Tang Jiyao\"><PERSON></a> proclaim the independence of <a href=\"https://wikipedia.org/wiki/Yunnan\" title=\"Yunnan\">Yunnan</a> and begin a campaign to restore the <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)\" title=\"Republic of China (1912-1949)\">Republic</a>.", "links": [{"title": "National Protection War", "link": "https://wikipedia.org/wiki/National_Protection_War"}, {"title": "Empire of China (1915-1916)", "link": "https://wikipedia.org/wiki/Empire_of_China_(1915%E2%80%931916)"}, {"title": "Cai E", "link": "https://wikipedia.org/wiki/Cai_E"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tang_<PERSON>"}, {"title": "Yunnan", "link": "https://wikipedia.org/wiki/Yunnan"}, {"title": "Republic of China (1912-1949)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%931949)"}]}, {"year": "1927", "text": "<PERSON><PERSON> <PERSON><PERSON> and his followers burn copies of the Manusmriti in Mahad, Maharashtra, to protest its treatment of Dalit people.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Dr. <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and his followers burn copies of the <a href=\"https://wikipedia.org/wiki/Manusmriti\" title=\"Manusmriti\">Manusmriti</a> in <a href=\"https://wikipedia.org/wiki/Mahad\" title=\"Mahad\"><PERSON>had</a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, to protest its treatment of <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Dalit</a> people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr._<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"Dr. <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> and his followers burn copies of the <a href=\"https://wikipedia.org/wiki/Manusmriti\" title=\"Manusmriti\">Manusmriti</a> in <a href=\"https://wikipedia.org/wiki/Mahad\" title=\"Mahad\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Maharashtra\" title=\"Maharashtra\">Maharashtra</a>, to protest its treatment of <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Dalit</a> people.", "links": [{"title": "Dr. <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dr<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manusmriti"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Maharashtra", "link": "https://wikipedia.org/wiki/Maharashtra"}, {"title": "Dalit", "link": "https://wikipedia.org/wiki/Dalit"}]}, {"year": "1932", "text": "A magnitude 7.6 earthquake in Gansu, China kills 275 people.", "html": "1932 - A <a href=\"https://wikipedia.org/wiki/1932_Changma_earthquake\" title=\"1932 Changma earthquake\">magnitude 7.6 earthquake in Gansu, China</a> kills 275 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1932_Changma_earthquake\" title=\"1932 Changma earthquake\">magnitude 7.6 earthquake in Gansu, China</a> kills 275 people.", "links": [{"title": "1932 Changma earthquake", "link": "https://wikipedia.org/wiki/1932_Changma_earthquake"}]}, {"year": "1941", "text": "Admiral <PERSON>, appointed commander of the U.S. Pacific Fleet on December 17, arrives at Pearl Harbor.", "html": "1941 - Admiral <a href=\"https://wikipedia.org/wiki/Chester_W<PERSON>_<PERSON>\" title=\"Chester W<PERSON>\"><PERSON></a>, appointed commander of the <a href=\"https://wikipedia.org/wiki/United_States_Pacific_Fleet\" title=\"United States Pacific Fleet\">U.S. Pacific Fleet</a> on December 17, arrives at Pearl Harbor.", "no_year_html": "Admiral <a href=\"https://wikipedia.org/wiki/Chester_W._<PERSON>\" title=\"Chester W. Nimit<PERSON>\"><PERSON></a>, appointed commander of the <a href=\"https://wikipedia.org/wiki/United_States_Pacific_Fleet\" title=\"United States Pacific Fleet\">U.S. Pacific Fleet</a> on December 17, arrives at Pearl Harbor.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Pacific Fleet", "link": "https://wikipedia.org/wiki/United_States_Pacific_Fleet"}]}, {"year": "1941", "text": "World War II: Battle of Hong Kong ends, beginning the Japanese occupation of Hong Kong.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hong_Kong\" title=\"Battle of Hong Kong\">Battle of Hong Kong</a> ends, beginning the <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong\" title=\"Japanese occupation of Hong Kong\">Japanese occupation of Hong Kong</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Hong_Kong\" title=\"Battle of Hong Kong\">Battle of Hong Kong</a> ends, beginning the <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong\" title=\"Japanese occupation of Hong Kong\">Japanese occupation of Hong Kong</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Hong Kong", "link": "https://wikipedia.org/wiki/Battle_of_Hong_Kong"}, {"title": "Japanese occupation of Hong Kong", "link": "https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong"}]}, {"year": "1941", "text": "Admiral <PERSON><PERSON> seizes the archipelago of Saint Pierre and Miquelon, which become the first part of France to be liberated by the Free French Forces.", "html": "1941 - Admiral <a href=\"https://wikipedia.org/wiki/%C3%89mile_Muselier\" title=\"Émile Muselier\"><PERSON><PERSON></a> seizes the archipelago of <a href=\"https://wikipedia.org/wiki/Saint_Pierre_and_Miquelon\" title=\"Saint Pierre and Miquelon\">Saint Pierre and Miquelon</a>, which become the first part of France to be liberated by the <a href=\"https://wikipedia.org/wiki/Free_French_Forces\" class=\"mw-redirect\" title=\"Free French Forces\">Free French Forces</a>.", "no_year_html": "Admiral <a href=\"https://wikipedia.org/wiki/%C3%89mile_Muselier\" title=\"Émile Muselier\"><PERSON><PERSON></a> seizes the archipelago of <a href=\"https://wikipedia.org/wiki/Saint_Pierre_and_Miquelon\" title=\"Saint Pierre and Miquelon\">Saint Pierre and Miquelon</a>, which become the first part of France to be liberated by the <a href=\"https://wikipedia.org/wiki/Free_French_Forces\" class=\"mw-redirect\" title=\"Free French Forces\">Free French Forces</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89mile_Muselier"}, {"title": "Saint Pierre and Miquelon", "link": "https://wikipedia.org/wiki/Saint_Pierre_and_Miquelon"}, {"title": "Free French Forces", "link": "https://wikipedia.org/wiki/Free_French_Forces"}]}, {"year": "1946", "text": "The first European self-sustaining nuclear chain reaction is initiated within the Soviet Union's F-1 nuclear reactor.", "html": "1946 - The first European self-sustaining <a href=\"https://wikipedia.org/wiki/Nuclear_chain_reaction\" title=\"Nuclear chain reaction\">nuclear chain reaction</a> is initiated within the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/F-1_(nuclear_reactor)\" title=\"F-1 (nuclear reactor)\">F-1 nuclear reactor</a>.", "no_year_html": "The first European self-sustaining <a href=\"https://wikipedia.org/wiki/Nuclear_chain_reaction\" title=\"Nuclear chain reaction\">nuclear chain reaction</a> is initiated within the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>'s <a href=\"https://wikipedia.org/wiki/F-1_(nuclear_reactor)\" title=\"F-1 (nuclear reactor)\">F-1 nuclear reactor</a>.", "links": [{"title": "Nuclear chain reaction", "link": "https://wikipedia.org/wiki/Nuclear_chain_reaction"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "F-1 (nuclear reactor)", "link": "https://wikipedia.org/wiki/F-1_(nuclear_reactor)"}]}, {"year": "1950", "text": "The Stone of Scone, traditional coronation stone of British monarchs, is taken from Westminster Abbey by Scottish nationalist students. It later turns up in Scotland on April 11, 1951.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Stone_of_Scone\" title=\"Stone of Scone\">Stone of Scone</a>, traditional coronation stone of <a href=\"https://wikipedia.org/wiki/British_Monarch\" class=\"mw-redirect\" title=\"British Monarch\">British monarchs</a>, <a href=\"https://wikipedia.org/wiki/Removal_of_the_Stone_of_Scone_in_1950\" class=\"mw-redirect\" title=\"Removal of the Stone of Scone in 1950\">is taken from Westminster Abbey</a> by <a href=\"https://wikipedia.org/wiki/Scottish_nationalism\" title=\"Scottish nationalism\">Scottish nationalist</a> students. It later turns up in Scotland on <a href=\"https://wikipedia.org/wiki/April_11\" title=\"April 11\">April 11</a>, 1951.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Stone_of_Scone\" title=\"Stone of Scone\">Stone of Scone</a>, traditional coronation stone of <a href=\"https://wikipedia.org/wiki/British_Monarch\" class=\"mw-redirect\" title=\"British Monarch\">British monarchs</a>, <a href=\"https://wikipedia.org/wiki/Removal_of_the_Stone_of_Scone_in_1950\" class=\"mw-redirect\" title=\"Removal of the Stone of Scone in 1950\">is taken from Westminster Abbey</a> by <a href=\"https://wikipedia.org/wiki/Scottish_nationalism\" title=\"Scottish nationalism\">Scottish nationalist</a> students. It later turns up in Scotland on <a href=\"https://wikipedia.org/wiki/April_11\" title=\"April 11\">April 11</a>, 1951.", "links": [{"title": "Stone of Scone", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "British Monarch", "link": "https://wikipedia.org/wiki/British_Monarch"}, {"title": "Removal of the Stone of Scone in 1950", "link": "https://wikipedia.org/wiki/Removal_of_the_<PERSON>_of_Scone_in_1950"}, {"title": "Scottish nationalism", "link": "https://wikipedia.org/wiki/Scottish_nationalism"}, {"title": "April 11", "link": "https://wikipedia.org/wiki/April_11"}]}, {"year": "1951", "text": "A bomb explodes at the home of <PERSON> and <PERSON><PERSON>, early leaders of the Civil Rights Movement, killing <PERSON> instantly and fatally wounding <PERSON><PERSON>.", "html": "1951 - A bomb <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_and_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Murder of <PERSON> and <PERSON><PERSON>\">explodes</a> at the home of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, early leaders of the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a>, killing <PERSON> instantly and fatally wounding <PERSON><PERSON>.", "no_year_html": "A bomb <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_and_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Murder of <PERSON> and <PERSON><PERSON>\">explodes</a> at the home of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, early leaders of the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a>, killing <PERSON> instantly and fatally wounding <PERSON><PERSON>.", "links": [{"title": "Murder of <PERSON> and <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_and_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Civil Rights Movement", "link": "https://wikipedia.org/wiki/Civil_Rights_Movement"}]}, {"year": "1962", "text": "The Soviet Union conducts its final above-ground nuclear weapon test, in anticipation of the 1963 Partial Nuclear Test Ban Treaty.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> conducts its final above-ground <a href=\"https://wikipedia.org/wiki/1962_Soviet_nuclear_tests\" title=\"1962 Soviet nuclear tests\">nuclear weapon test</a>, in anticipation of the 1963 <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> conducts its final above-ground <a href=\"https://wikipedia.org/wiki/1962_Soviet_nuclear_tests\" title=\"1962 Soviet nuclear tests\">nuclear weapon test</a>, in anticipation of the 1963 <a href=\"https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty\" title=\"Partial Nuclear Test Ban Treaty\">Partial Nuclear Test Ban Treaty</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "1962 Soviet nuclear tests", "link": "https://wikipedia.org/wiki/1962_Soviet_nuclear_tests"}, {"title": "Partial Nuclear Test Ban Treaty", "link": "https://wikipedia.org/wiki/Partial_Nuclear_Test_Ban_Treaty"}]}, {"year": "1963", "text": "Turkish Cypriot Bayrak Radio begins transmitting in Cyprus after Turkish Cypriots are forcibly excluded from Cyprus Broadcasting Corporation.", "html": "1963 - Turkish Cypriot <a href=\"https://wikipedia.org/wiki/Bayrak\" title=\"Bayrak\">Bayrak</a> Radio begins transmitting in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> after <a href=\"https://wikipedia.org/wiki/Turkish_Cypriots\" title=\"Turkish Cypriots\">Turkish Cypriots</a> are forcibly excluded from <a href=\"https://wikipedia.org/wiki/Cyprus_Broadcasting_Corporation\" title=\"Cyprus Broadcasting Corporation\">Cyprus Broadcasting Corporation</a>.", "no_year_html": "Turkish Cypriot <a href=\"https://wikipedia.org/wiki/Bayrak\" title=\"Bayrak\">Bayrak</a> Radio begins transmitting in <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> after <a href=\"https://wikipedia.org/wiki/Turkish_Cypriots\" title=\"Turkish Cypriots\">Turkish Cypriots</a> are forcibly excluded from <a href=\"https://wikipedia.org/wiki/Cyprus_Broadcasting_Corporation\" title=\"Cyprus Broadcasting Corporation\">Cyprus Broadcasting Corporation</a>.", "links": [{"title": "Bayrak", "link": "https://wikipedia.org/wiki/Bayrak"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "Turkish Cypriots", "link": "https://wikipedia.org/wiki/Turkish_Cypriots"}, {"title": "Cyprus Broadcasting Corporation", "link": "https://wikipedia.org/wiki/Cyprus_Broadcasting_Corporation"}]}, {"year": "1968", "text": "Apollo program: Apollo 8 performs the first successful Trans-Earth injection (TEI) maneuver, sending the crew and spacecraft on a trajectory back to Earth from Lunar orbit.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <i><a href=\"https://wikipedia.org/wiki/Apollo_8\" title=\"Apollo 8\">Apollo 8</a></i> performs the first successful <a href=\"https://wikipedia.org/wiki/Trans-Earth_injection\" title=\"Trans-Earth injection\">Trans-Earth injection</a> (TEI) maneuver, sending the crew and spacecraft on a trajectory back to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> from <a href=\"https://wikipedia.org/wiki/Lunar_orbit\" title=\"Lunar orbit\">Lunar orbit</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: <i><a href=\"https://wikipedia.org/wiki/Apollo_8\" title=\"Apollo 8\">Apollo 8</a></i> performs the first successful <a href=\"https://wikipedia.org/wiki/Trans-Earth_injection\" title=\"Trans-Earth injection\">Trans-Earth injection</a> (TEI) maneuver, sending the crew and spacecraft on a trajectory back to <a href=\"https://wikipedia.org/wiki/Earth\" title=\"Earth\">Earth</a> from <a href=\"https://wikipedia.org/wiki/Lunar_orbit\" title=\"Lunar orbit\">Lunar orbit</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Apollo 8", "link": "https://wikipedia.org/wiki/Apollo_8"}, {"title": "Trans-Earth injection", "link": "https://wikipedia.org/wiki/Trans-Earth_injection"}, {"title": "Earth", "link": "https://wikipedia.org/wiki/Earth"}, {"title": "Lunar orbit", "link": "https://wikipedia.org/wiki/Lunar_orbit"}]}, {"year": "1968", "text": "Kilvenmani massacre: Forty-four Dalits (untouchables) are burnt to death in Kizhavenmani village, Tamil Nadu, a retaliation for a campaign for higher wages by Dalit laborers.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Kilvenmani_massacre\" title=\"Kilvenmani massacre\">Kilvenmani massacre</a>: Forty-four <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Dalits</a> (untouchables) are burnt to death in <a href=\"https://wikipedia.org/wiki/Kizhavenmani\" title=\"Kizhavenmani\">Kizhavenmani</a> village, Tamil Nadu, a retaliation for a campaign for higher wages by Dalit laborers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kilvenmani_massacre\" title=\"Kilvenmani massacre\">Kilvenmani massacre</a>: Forty-four <a href=\"https://wikipedia.org/wiki/Dalit\" title=\"Dalit\">Dalits</a> (untouchables) are burnt to death in <a href=\"https://wikipedia.org/wiki/Kizhavenmani\" title=\"Kizhavenmani\">Kizhavenmani</a> village, Tamil Nadu, a retaliation for a campaign for higher wages by Dalit laborers.", "links": [{"title": "Kilvenmani massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_massacre"}, {"title": "Dalit", "link": "https://wikipedia.org/wiki/Dalit"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>mani"}]}, {"year": "1976", "text": "EgyptAir Flight 864, a Boeing 707-366C, crashes on approach to Don Mueang International Airport, killing 71 people.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_864\" title=\"EgyptAir Flight 864\">EgyptAir Flight 864</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707-366C</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Don_Mueang_International_Airport\" title=\"Don Mueang International Airport\">Don Mueang International Airport</a>, killing 71 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/EgyptAir_Flight_864\" title=\"EgyptAir Flight 864\">EgyptAir Flight 864</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707-366C</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Don_Mueang_International_Airport\" title=\"Don Mueang International Airport\">Don Mueang International Airport</a>, killing 71 people.", "links": [{"title": "EgyptAir Flight 864", "link": "https://wikipedia.org/wiki/EgyptAir_Flight_864"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "Don Mueang International Airport", "link": "https://wikipedia.org/wiki/Don_<PERSON>g_International_Airport"}]}, {"year": "1977", "text": "Israeli Prime Minister <PERSON><PERSON><PERSON> meets in Egypt with its president <PERSON><PERSON>.", "html": "1977 - Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Begin\"><PERSON><PERSON><PERSON></a> meets in Egypt with its president <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON></a>.", "no_year_html": "Israeli Prime Minister <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>ache<PERSON> Begin\"><PERSON><PERSON><PERSON></a> meets in Egypt with its president <a href=\"https://wikipedia.org/wiki/Anwar_Sadat\" title=\"Anwar Sadat\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}]}, {"year": "1986", "text": "Iraqi Airways Flight 163, a Boeing 737-270C, is hijacked and crashes in Arar, Saudi Arabia, killing 63 people.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Iraqi_Airways_Flight_163\" title=\"Iraqi Airways Flight 163\">Iraqi Airways Flight 163</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-270C</a>, is hijacked and crashes in <a href=\"https://wikipedia.org/wiki/Arar,_Saudi_Arabia\" title=\"Arar, Saudi Arabia\">Arar, Saudi Arabia</a>, killing 63 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iraqi_Airways_Flight_163\" title=\"Iraqi Airways Flight 163\">Iraqi Airways Flight 163</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-270C</a>, is hijacked and crashes in <a href=\"https://wikipedia.org/wiki/Arar,_Saudi_Arabia\" title=\"Arar, Saudi Arabia\">Arar, Saudi Arabia</a>, killing 63 people.", "links": [{"title": "Iraqi Airways Flight 163", "link": "https://wikipedia.org/wiki/Iraqi_Airways_Flight_163"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "Arar, Saudi Arabia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_Saudi_Arabia"}]}, {"year": "1989", "text": "Romanian Revolution: Deposed President of Romania <PERSON><PERSON> and his wife, <PERSON>, are condemned to death and executed after a summary trial.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: Deposed <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and his wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON>\">Elena</a>, are condemned to death and executed after a <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON><PERSON>_and_<PERSON>_<PERSON>%C8%99<PERSON>\" class=\"mw-redirect\" title=\"Trial of <PERSON><PERSON> and <PERSON>\">summary trial</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Romanian_Revolution\" class=\"mw-redirect\" title=\"Romanian Revolution\">Romanian Revolution</a>: Deposed <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and his wife, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON>\">Elena</a>, are condemned to death and executed after a <a href=\"https://wikipedia.org/wiki/Trial_of_<PERSON><PERSON>_and_<PERSON>_<PERSON>%C8%99<PERSON>\" class=\"mw-redirect\" title=\"Trial of <PERSON><PERSON> and <PERSON>\">summary trial</a>.", "links": [{"title": "Romanian Revolution", "link": "https://wikipedia.org/wiki/Romanian_Revolution"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99escu"}, {"title": "Trial of <PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Trial_of_<PERSON><PERSON>_and_<PERSON>_<PERSON>%C8%99escu"}]}, {"year": "1991", "text": "<PERSON> resigns as President of the Soviet Union (the union itself is dissolved the next day). Ukraine's referendum is finalized and Ukraine officially leaves the Soviet Union.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">President of the Soviet Union</a> (the union itself is <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">dissolved</a> the next day). Ukraine's <a href=\"https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum\" title=\"1991 Ukrainian independence referendum\">referendum</a> is finalized and Ukraine officially leaves the Soviet Union.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/President_of_the_Soviet_Union\" title=\"President of the Soviet Union\">President of the Soviet Union</a> (the union itself is <a href=\"https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union\" title=\"Dissolution of the Soviet Union\">dissolved</a> the next day). Ukraine's <a href=\"https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum\" title=\"1991 Ukrainian independence referendum\">referendum</a> is finalized and Ukraine officially leaves the Soviet Union.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Soviet Union", "link": "https://wikipedia.org/wiki/President_of_the_Soviet_Union"}, {"title": "Dissolution of the Soviet Union", "link": "https://wikipedia.org/wiki/Dissolution_of_the_Soviet_Union"}, {"title": "1991 Ukrainian independence referendum", "link": "https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum"}]}, {"year": "1996", "text": "The body of American child beauty queen <PERSON><PERSON><PERSON><PERSON><PERSON> was found in her family's Boulder, Colorado, home. Her murder remains unsolved.", "html": "1996 - The body of American child beauty queen <a href=\"https://wikipedia.org/wiki/Killing_of_Jon<PERSON>en%C3%A9t_<PERSON>\" title=\"Killing of <PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> was found</a> in her family's Boulder, Colorado, home. Her murder remains unsolved.", "no_year_html": "The body of American child beauty queen <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON>%C3%A9t_<PERSON>\" title=\"Killing of <PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON> was found</a> in her family's Boulder, Colorado, home. Her murder remains unsolved.", "links": [{"title": "Killing of <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_of_JonBen%C3%A9t_<PERSON>"}]}, {"year": "1999", "text": "Cubana de Aviación Flight 310, a Yakovlev Yak-42, crashes near Bejuma, Carabobo State, Venezuela, killing 22 people.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_310\" title=\"Cubana de Aviación Flight 310\">Cubana de Aviación Flight 310</a>, a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42\" title=\"<PERSON><PERSON><PERSON> Yak-42\"><PERSON><PERSON><PERSON> Yak-42</a>, crashes near <a href=\"https://wikipedia.org/wiki/Bejuma\" title=\"Beju<PERSON>\">Bejuma</a>, <a href=\"https://wikipedia.org/wiki/Carabobo_State\" class=\"mw-redirect\" title=\"Carabobo State\">Carabobo State</a>, Venezuela, killing 22 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_310\" title=\"Cubana de Aviación Flight 310\">Cubana de Aviación Flight 310</a>, a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42\" title=\"<PERSON><PERSON><PERSON> Yak-42\"><PERSON><PERSON><PERSON> Yak-42</a>, crashes near <a href=\"https://wikipedia.org/wiki/Bejuma\" title=\"Beju<PERSON>\">Beju<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Carabobo_State\" class=\"mw-redirect\" title=\"Carabobo State\">Carabobo State</a>, Venezuela, killing 22 people.", "links": [{"title": "Cubana de Aviación Flight 310", "link": "https://wikipedia.org/wiki/Cubana_de_Aviaci%C3%B3n_Flight_310"}, {"title": "<PERSON><PERSON><PERSON>-42", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-42"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>juma"}, {"title": "Carabobo State", "link": "https://wikipedia.org/wiki/Carabobo_State"}]}, {"year": "2003", "text": "UTA Flight 141, a Boeing 727-223, crashes at the Cotonou Airport in Benin, killing 141 people.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/UTA_Flight_141\" title=\"UTA Flight 141\">UTA Flight 141</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727-223</a>, crashes at the <a href=\"https://wikipedia.org/wiki/Cotonou_Airport\" class=\"mw-redirect\" title=\"Cotonou Airport\">Cotonou Airport</a> in <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>, killing 141 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/UTA_Flight_141\" title=\"UTA Flight 141\">UTA Flight 141</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727-223</a>, crashes at the <a href=\"https://wikipedia.org/wiki/Cotonou_Airport\" class=\"mw-redirect\" title=\"Cotonou Airport\">Cotonou Airport</a> in <a href=\"https://wikipedia.org/wiki/Benin\" title=\"Benin\">Benin</a>, killing 141 people.", "links": [{"title": "UTA Flight 141", "link": "https://wikipedia.org/wiki/UTA_Flight_141"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Cotonou Airport", "link": "https://wikipedia.org/wiki/Cotonou_Airport"}, {"title": "Benin", "link": "https://wikipedia.org/wiki/Benin"}]}, {"year": "2003", "text": "The ill-fated Beagle 2 probe, released from the Mars Express spacecraft on December 19, stops transmitting shortly before its scheduled landing.", "html": "2003 - The ill-fated <i><a href=\"https://wikipedia.org/wiki/Beagle_2\" title=\"Beagle 2\">Beagle 2</a></i> probe, released from the <a href=\"https://wikipedia.org/wiki/Mars_Express\" title=\"Mars Express\">Mars Express</a> spacecraft on <a href=\"https://wikipedia.org/wiki/December_19\" title=\"December 19\">December 19</a>, stops transmitting shortly before its scheduled landing.", "no_year_html": "The ill-fated <i><a href=\"https://wikipedia.org/wiki/Beagle_2\" title=\"Beagle 2\">Beagle 2</a></i> probe, released from the <a href=\"https://wikipedia.org/wiki/Mars_Express\" title=\"Mars Express\">Mars Express</a> spacecraft on <a href=\"https://wikipedia.org/wiki/December_19\" title=\"December 19\">December 19</a>, stops transmitting shortly before its scheduled landing.", "links": [{"title": "Beagle 2", "link": "https://wikipedia.org/wiki/Beagle_2"}, {"title": "Mars Express", "link": "https://wikipedia.org/wiki/Mars_Express"}, {"title": "December 19", "link": "https://wikipedia.org/wiki/December_19"}]}, {"year": "2004", "text": "The Cassini orbiter releases <PERSON><PERSON><PERSON> probe which successfully landed on Saturn's moon Titan on January 14, 2005.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Cassini_Orbiter\" class=\"mw-redirect\" title=\"Cassini Orbiter\"><i><PERSON><PERSON></i> orbiter</a> releases <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON>y<PERSON> (spacecraft)\"><i>Huygens</i> probe</a> which successfully landed on <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>'s moon <a href=\"https://wikipedia.org/wiki/Titan_(moon)\" title=\"Titan (moon)\">Titan</a> on January 14, 2005.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cassini_Orbiter\" class=\"mw-redirect\" title=\"Cassini Orbiter\"><i><PERSON><PERSON></i> orbiter</a> releases <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON>y<PERSON> (spacecraft)\"><i>Huygens</i> probe</a> which successfully landed on <a href=\"https://wikipedia.org/wiki/Saturn\" title=\"Saturn\">Saturn</a>'s moon <a href=\"https://wikipedia.org/wiki/Titan_(moon)\" title=\"Titan (moon)\">Titan</a> on January 14, 2005.", "links": [{"title": "<PERSON>ini <PERSON>", "link": "https://wikipedia.org/wiki/Cassini_Orbiter"}, {"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}, {"title": "Saturn", "link": "https://wikipedia.org/wiki/Saturn"}, {"title": "Titan (moon)", "link": "https://wikipedia.org/wiki/Titan_(moon)"}]}, {"year": "2012", "text": "An Antonov An-72 plane crashes close to the city of Shymkent, killing 27 people.", "html": "2012 - An <a href=\"https://wikipedia.org/wiki/Antonov_An-72\" title=\"Antonov An-72\">Antonov An-72</a> plane <a href=\"https://wikipedia.org/wiki/2012_Kazakhstan_Antonov_An-72_crash\" title=\"2012 Kazakhstan Antonov An-72 crash\">crashes</a> close to the city of <a href=\"https://wikipedia.org/wiki/Shymkent\" title=\"Shymkent\">Shymkent</a>, killing 27 people.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Antonov_An-72\" title=\"Antonov An-72\">Antonov An-72</a> plane <a href=\"https://wikipedia.org/wiki/2012_Kazakhstan_Antonov_An-72_crash\" title=\"2012 Kazakhstan Antonov An-72 crash\">crashes</a> close to the city of <a href=\"https://wikipedia.org/wiki/Shymkent\" title=\"Shymkent\">Shymkent</a>, killing 27 people.", "links": [{"title": "Antonov An-72", "link": "https://wikipedia.org/wiki/Antonov_An-72"}, {"title": "2012 Kazakhstan Antonov An-72 crash", "link": "https://wikipedia.org/wiki/2012_Kazakhstan_Antonov_An-72_crash"}, {"title": "Shymkent", "link": "https://wikipedia.org/wiki/Shymkent"}]}, {"year": "2012", "text": "Air Bagan Flight 011, a Fokker 100, crashes on approach to Heho Airport in Heho, Myanmar, killing two people.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Air_Bagan_Flight_011\" title=\"Air Bagan Flight 011\">Air Bagan Flight 011</a>, a <a href=\"https://wikipedia.org/wiki/Fokker_100\" title=\"Fokker 100\">Fokker 100</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Heho_Airport\" title=\"Heho Airport\">Heho Airport</a> in <a href=\"https://wikipedia.org/wiki/Heho\" title=\"Heho\">Heho</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing two people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Bagan_Flight_011\" title=\"Air Bagan Flight 011\">Air Bagan Flight 011</a>, a <a href=\"https://wikipedia.org/wiki/Fokker_100\" title=\"Fokker 100\">Fokker 100</a>, crashes on approach to <a href=\"https://wikipedia.org/wiki/Heho_Airport\" title=\"Heho Airport\">Heho Airport</a> in <a href=\"https://wikipedia.org/wiki/Heho\" title=\"Heho\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>, killing two people.", "links": [{"title": "Air Bagan Flight 011", "link": "https://wikipedia.org/wiki/Air_Bagan_Flight_011"}, {"title": "Fokker 100", "link": "https://wikipedia.org/wiki/Fokker_100"}, {"title": "Heho Airport", "link": "https://wikipedia.org/wiki/Heho_Airport"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Heho"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "2016", "text": "A Russian Defence Ministry Tupolev Tu-154 carrying members of the Alexandrov Ensemble crashes into the Black Sea shortly after takeoff, killing all 92 people on board.", "html": "2016 - A <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Defence Ministry</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> carrying members of the <a href=\"https://wikipedia.org/wiki/Alexandrov_Ensemble\" title=\"Alexandrov Ensemble\">Alexandrov Ensemble</a> <a href=\"https://wikipedia.org/wiki/2016_Russian_Defence_Ministry_Tupolev_Tu-154_crash\" title=\"2016 Russian Defence Ministry Tupolev Tu-154 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> shortly after takeoff, killing all 92 people on board.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Defence Ministry</a> <a href=\"https://wikipedia.org/wiki/Tupolev_Tu-154\" title=\"Tupolev Tu-154\">Tupolev Tu-154</a> carrying members of the <a href=\"https://wikipedia.org/wiki/Alexandrov_Ensemble\" title=\"Alexandrov Ensemble\">Alexandrov Ensemble</a> <a href=\"https://wikipedia.org/wiki/2016_Russian_Defence_Ministry_Tupolev_Tu-154_crash\" title=\"2016 Russian Defence Ministry Tupolev Tu-154 crash\">crashes</a> into the <a href=\"https://wikipedia.org/wiki/Black_Sea\" title=\"Black Sea\">Black Sea</a> shortly after takeoff, killing all 92 people on board.", "links": [{"title": "Ministry of Defence (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)"}, {"title": "Tupolev Tu-154", "link": "https://wikipedia.org/wiki/Tupolev_Tu-154"}, {"title": "Alexandrov Ensemble", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>rov_Ensemble"}, {"title": "2016 Russian Defence Ministry Tupolev Tu-154 crash", "link": "https://wikipedia.org/wiki/2016_Russian_Defence_Ministry_Tupolev_Tu-154_crash"}, {"title": "Black Sea", "link": "https://wikipedia.org/wiki/Black_Sea"}]}, {"year": "2019", "text": "Twenty people are killed and thousands are left homeless by Typhoon Phanfone in the Philippines.", "html": "2019 - Twenty people are killed and thousands are left homeless by <a href=\"https://wikipedia.org/wiki/Typhoon_Phanfone_(2019)\" class=\"mw-redirect\" title=\"Typhoon Phanfone (2019)\">Typhoon Phanfone</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "Twenty people are killed and thousands are left homeless by <a href=\"https://wikipedia.org/wiki/Typhoon_Phanfone_(2019)\" class=\"mw-redirect\" title=\"Typhoon Phanfone (2019)\">Typhoon Phanfone</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Typhoon Phanfone (2019)", "link": "https://wikipedia.org/wiki/Typhoon_Phanfone_(2019)"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "2020", "text": "An explosion in Nashville, Tennessee, occurs, leaving three civilians in the hospital.", "html": "2020 - An <a href=\"https://wikipedia.org/wiki/2020_Nashville_bombing\" title=\"2020 Nashville bombing\">explosion in Nashville, Tennessee</a>, occurs, leaving three civilians in the hospital.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2020_Nashville_bombing\" title=\"2020 Nashville bombing\">explosion in Nashville, Tennessee</a>, occurs, leaving three civilians in the hospital.", "links": [{"title": "2020 Nashville bombing", "link": "https://wikipedia.org/wiki/2020_Nashville_bombing"}]}, {"year": "2021", "text": "The James Webb Space Telescope is launched.", "html": "2021 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Space_Telescope\" title=\"James Webb Space Telescope\"><PERSON> Space Telescope</a> is <a href=\"https://wikipedia.org/wiki/Ariane_flight_VA256\" title=\"Ariane flight VA256\">launched</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Space_Telescope\" title=\"James Webb Space Telescope\"><PERSON> Space Telescope</a> is <a href=\"https://wikipedia.org/wiki/Ariane_flight_VA256\" title=\"Ariane flight VA256\">launched</a>.", "links": [{"title": "<PERSON> Space Telescope", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Space_Telescope"}, {"title": "Ariane flight VA256", "link": "https://wikipedia.org/wiki/Ariane_flight_VA256"}]}, {"year": "2024", "text": "Azerbaijan Airlines Flight 8243 crashes in Aktau, Kazakhstan, killing 38 of its occupants.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_8243\" title=\"Azerbaijan Airlines Flight 8243\">Azerbaijan Airlines Flight 8243</a> crashes in <a href=\"https://wikipedia.org/wiki/Aktau\" title=\"Aktau\">Akt<PERSON></a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, killing 38 of its occupants.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_8243\" title=\"Azerbaijan Airlines Flight 8243\">Azerbaijan Airlines Flight 8243</a> crashes in <a href=\"https://wikipedia.org/wiki/Aktau\" title=\"Aktau\">Aktau</a>, <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>, killing 38 of its occupants.", "links": [{"title": "Azerbaijan Airlines Flight 8243", "link": "https://wikipedia.org/wiki/Azerbaijan_Airlines_Flight_8243"}, {"title": "Aktau", "link": "https://wikipedia.org/wiki/Aktau"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}], "Births": [{"year": "1250", "text": "<PERSON>, Byzantine emperor (d. 1305)", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (d. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (d. 1305)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1281", "text": "<PERSON>, 4th Countess of Lincoln (d. 1348)", "html": "1281 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Countess_of_Lincoln\" class=\"mw-redirect\" title=\"<PERSON>, 4th Countess of Lincoln\"><PERSON>, 4th Countess of Lincoln</a> (d. 1348)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Countess_of_Lincoln\" class=\"mw-redirect\" title=\"<PERSON>, 4th Countess of Lincoln\"><PERSON>, 4th Countess of Lincoln</a> (d. 1348)", "links": [{"title": "<PERSON>, 4th Countess of Lincoln", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_4th_Countess_of_Lincoln"}]}, {"year": "1400", "text": "<PERSON>, 1st Baron <PERSON>, Lord Lieutenant of Ireland (d. 1487)", "html": "1400 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Lord Lieutenant of Ireland (d. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\"><PERSON>, 1st Baron <PERSON></a>, Lord Lieutenant of Ireland (d. 1487)", "links": [{"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1424", "text": "<PERSON>, Daup<PERSON> of France (d. 1445)", "html": "1424 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France\"><PERSON>, <PERSON><PERSON><PERSON> of France</a> (d. 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France\"><PERSON>, <PERSON><PERSON><PERSON> of France</a> (d. 1445)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_of_France"}]}, {"year": "1461", "text": "<PERSON> of Saxony, Queen consort of Denmark (d. 1521)", "html": "1461 - <a href=\"https://wikipedia.org/wiki/Christina_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, Queen consort of Denmark (d. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christina_of_Saxony\" title=\"<PERSON> of Saxony\"><PERSON> of Saxony</a>, Queen consort of Denmark (d. 1521)", "links": [{"title": "Christina of Saxony", "link": "https://wikipedia.org/wiki/Christina_of_Saxony"}]}, {"year": "1490", "text": "<PERSON>, Italian Roman Catholic priest (d. 1562)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic priest (d. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Roman Catholic priest (d. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1493", "text": "<PERSON><PERSON>, French noblewoman (d. 1583)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French noblewoman (d. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French noblewoman (d. 1583)", "links": [{"title": "<PERSON><PERSON> Bourbon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1505", "text": "<PERSON> of Saxony, German noblewoman (d. 1549)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony\" title=\"Christine of Saxony\">Christine of Saxony</a>, German noblewoman (d. 1549)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Saxony\" title=\"Christine of Saxony\">Christine of Saxony</a>, German noblewoman (d. 1549)", "links": [{"title": "<PERSON> of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony"}]}, {"year": "1564", "text": "<PERSON>, German Calvinist theologian (d. 1629)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Calvinist theologian (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Calvinist theologian (d. 1629)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>torf"}]}, {"year": "1583", "text": "<PERSON>, English organist and composer (d. 1625)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Gibbons\" title=\"<PERSON> Gibbons\"><PERSON></a>, English organist and composer (d. 1625)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Gibbons\" title=\"<PERSON> Gibbons\"><PERSON></a>, English organist and composer (d. 1625)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Gibbons"}]}, {"year": "1584", "text": "<PERSON> of Austria, Queen of Spain (d. 1611)", "html": "1584 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Spain\" title=\"<PERSON> of Austria, Queen of Spain\"><PERSON> of Austria, Queen of Spain</a> (d. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Spain\" title=\"<PERSON> of Austria, Queen of Spain\"><PERSON> of Austria, Queen of Spain</a> (d. 1611)", "links": [{"title": "<PERSON> of Austria, Queen of Spain", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Queen_of_Spain"}]}, {"year": "1601", "text": "<PERSON>, Duke of Saxe-Gotha (d. 1675)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Gotha\" title=\"<PERSON>, Duke of Saxe-Gotha\"><PERSON>, Duke of Saxe-Gotha</a> (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Gotha\" title=\"<PERSON>, Duke of Saxe-Gotha\"><PERSON>, Duke of Saxe-Gotha</a> (d. 1675)", "links": [{"title": "<PERSON>, Duke of Saxe-Gotha", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Gotha"}]}, {"year": "1628", "text": "<PERSON><PERSON>, French painter and educator (d. 1707)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl_Coypel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and educator (d. 1707)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_Coypel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French painter and educator (d. 1707)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Coypel"}]}, {"year": "1642", "text": "<PERSON>, English physicist and mathematician (d. 1727)", "html": "1642 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, English physicist and mathematician (d. 1727)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Newton\"><PERSON></a>, English physicist and mathematician (d. 1727)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1652", "text": "<PERSON>, Scottish physician, anatomist, and scholar (d. 1713)", "html": "1652 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician, anatomist, and scholar (d. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician, anatomist, and scholar (d. 1713)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Archibald_<PERSON>cairne"}]}, {"year": "1665", "text": "<PERSON> <PERSON><PERSON><PERSON>, Scottish-English poet and songwriter (d. 1746)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/Lady_<PERSON><PERSON>_<PERSON>\" title=\"Lady <PERSON><PERSON>\">Lady <PERSON><PERSON><PERSON></a>, Scottish-English poet and songwriter (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lady_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Lady G<PERSON><PERSON>\">Lady <PERSON><PERSON><PERSON></a>, Scottish-English poet and songwriter (d. 1746)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>llie"}]}, {"year": "1674", "text": "<PERSON>, Scottish minister and theologian (d. 1712)", "html": "1674 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and theologian (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish minister and theologian (d. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1686", "text": "<PERSON>, Italian violinist and composer (d. 1763)", "html": "1686 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian violinist and composer (d. 1763)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1700", "text": "<PERSON>, Prince of Anhalt-Dessau (d. 1758)", "html": "1700 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau\" title=\"<PERSON>, Prince of Anhalt-Dessau\"><PERSON>, Prince of Anhalt-Dessau</a> (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau\" title=\"<PERSON>, Prince of Anhalt-Dessau\"><PERSON>, Prince of Anhalt-Dessau</a> (d. 1758)", "links": [{"title": "<PERSON>, Prince of Anhalt-Dessau", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Anhalt-Dessau"}]}, {"year": "1711", "text": "<PERSON><PERSON><PERSON>, French violinist and composer (d. 1772)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (d. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French violinist and composer (d. 1772)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, German physician and scholar (d. 1774)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and scholar (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and scholar (d. 1774)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON> (d. 1799)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_VI\" title=\"Pope Pius VI\">Pope <PERSON> VI</a> (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VI\" title=\"Pope Pius VI\"><PERSON> <PERSON> VI</a> (d. 1799)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1728", "text": "<PERSON>, German composer and conductor (d. 1804)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and conductor (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON><PERSON><PERSON>, Italian-American physician and philosopher (d. 1816)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American physician and philosopher (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American physician and philosopher (d. 1816)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, Caribbean-French violinist, composer, and conductor (d. 1799)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Chevalier <PERSON>Georges\"><PERSON></a>, Caribbean-French violinist, composer, and conductor (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chevalier_<PERSON>_<PERSON>\" title=\"Chevalier <PERSON>Georges\">Chevalier <PERSON></a>, Caribbean-French violinist, composer, and conductor (d. 1799)", "links": [{"title": "<PERSON>Georges", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, American general and politician, 17th Governor of New Hampshire (d. 1839)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1839)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American general and politician, 17th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Hampshire\" title=\"Governor of New Hampshire\">Governor of New Hampshire</a> (d. 1839)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of New Hampshire", "link": "https://wikipedia.org/wiki/Governor_of_New_Hampshire"}]}, {"year": "1766", "text": "<PERSON>, Welsh Nonconformist preacher (d. 1838)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/Christmas_<PERSON>\" title=\"Christmas <PERSON>\"><PERSON></a>, Welsh Nonconformist preacher (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christmas_<PERSON>\" title=\"Christmas Evans\"><PERSON></a>, Welsh Nonconformist preacher (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christmas_<PERSON>"}]}, {"year": "1771", "text": "<PERSON>, English diarist and poet (d. 1855)", "html": "1771 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist and poet (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English diarist and poet (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1776", "text": "<PERSON>, <PERSON>, Irish author and poet (d. 1859)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Sydney,_<PERSON>_<PERSON>\" title=\"Sydney, <PERSON>\"><PERSON>, <PERSON></a>, Irish author and poet (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sydney,_<PERSON>_<PERSON>\" title=\"Sydney, <PERSON>\"><PERSON>, <PERSON></a>, Irish author and poet (d. 1859)", "links": [{"title": "Sydney, <PERSON>", "link": "https://wikipedia.org/wiki/Sydney,_<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON><PERSON> <PERSON><PERSON>, American apiarist, clergyman and teacher (d. 1895)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American apiarist, clergyman and teacher (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American apiarist, clergyman and teacher (d. 1895)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1821", "text": "<PERSON>, American nurse and humanitarian, founder of the American Red Cross (d. 1912)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and humanitarian, founder of the <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and humanitarian, founder of the <a href=\"https://wikipedia.org/wiki/American_Red_Cross\" title=\"American Red Cross\">American Red Cross</a> (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Red Cross", "link": "https://wikipedia.org/wiki/American_Red_Cross"}]}, {"year": "1825", "text": "<PERSON>, American lawyer and politician, 5th Governor of Oregon (d. 1895)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1829", "text": "<PERSON>, Irish-American composer and bandleader (d. 1892)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American composer and bandleader (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American composer and bandleader (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1902)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>vin\" title=\"<PERSON><PERSON> G<PERSON>vin\"><PERSON><PERSON></a>, American baseball player and manager (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vin\" title=\"<PERSON><PERSON> G<PERSON>vin\"><PERSON><PERSON></a>, American baseball player and manager (d. 1902)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1861", "text": "<PERSON>, American hunter, explorer and army scout famous for writing <PERSON><PERSON><PERSON>'s Masterpiece (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American hunter, explorer and army scout famous for writing <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Masterpiece\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Masterpiece\"><PERSON><PERSON><PERSON>'s Masterpiece</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American hunter, explorer and army scout famous for writing <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%27s_Masterpiece\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Masterpiece\"><PERSON><PERSON><PERSON>'s Masterpiece</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>'s Masterpiece", "link": "https://wikipedia.org/wiki/Buzzacott%27s_Masterpiece"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Indian educator, lawyer, and politician, President of the Indian National Congress (d. 1946)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian educator, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian educator, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (d. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Indian National Congress", "link": "https://wikipedia.org/wiki/President_of_the_Indian_National_Congress"}]}, {"year": "1865", "text": "<PERSON><PERSON><PERSON>, English 4th General of The Salvation Army (d. 1950)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English 4th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English 4th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1869", "text": "<PERSON>, English-American journalist and author (d. 1941)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Polish-American businesswoman and philanthropist (d. 1965)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American businesswoman and philanthropist (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American businesswoman and philanthropist (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Swiss-American agriculturalist and educator (d. 1959)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American agriculturalist and educator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American agriculturalist and educator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON><PERSON>, Italian soprano and actress (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian soprano and actress (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Canadian psychologist and priest (d. 1941)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist and priest (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian psychologist and priest (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, Austrian cardinal (d. 1955)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, Indian-Pakistani lawyer and politician, 1st Governor-General of Pakistan (d. 1948)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Pakistani lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor-General_of_Pakistan\" title=\"Governor-General of Pakistan\">Governor-General of Pakistan</a> (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor-General of Pakistan", "link": "https://wikipedia.org/wiki/Governor-General_of_Pakistan"}]}, {"year": "1876", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1959)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"Adolf <PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1878", "text": "<PERSON>, American race car driver and businessman, co-founded Chevrolet (d. 1941)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Louis_Chevrolet\" title=\"Louis Chevrolet\"><PERSON></a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_Chevrolet\" title=\"Louis Chevrolet\"><PERSON></a>, American race car driver and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Chevrolet\" title=\"Chevrolet\">Chevrolet</a> (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chevrolet", "link": "https://wikipedia.org/wiki/Chevrolet"}]}, {"year": "1878", "text": "<PERSON><PERSON>, Countess of Rothes, British philanthropist, social leader and heroine of Titanic disaster (d. 1956)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/No%C3%ABl,_Countess_of_Rothes\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Countess of Rothes\"><PERSON><PERSON>, Countess of Rothes</a>, British philanthropist, social leader and heroine of Titanic disaster (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl,_Countess_of_Rothes\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Countess of Rothes\"><PERSON><PERSON>, Countess of Rothes</a>, British philanthropist, social leader and heroine of Titanic disaster (d. 1956)", "links": [{"title": "<PERSON><PERSON>, Countess of Rothes", "link": "https://wikipedia.org/wiki/No%C3%ABl,_Countess_<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, Russian-American film producer (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American film producer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American film producer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Czech-Israeli philosopher and academic (d. 1975)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Israeli philosopher and academic (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-Israeli philosopher and academic (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON>, Belarusian-Israeli agronomist and politician (d. 1972)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli agronomist and politician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belarusian-Israeli agronomist and politician (d. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, American boxer (d. 1925)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (d. 1925)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_(boxer)"}]}, {"year": "1884", "text": "<PERSON>, American model and actress (d. 1967)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Egyptian poet and activist (d. 1918)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Malak_Hifni_Nasif\" title=\"Malak Hifni Nasif\"><PERSON><PERSON> Hif<PERSON></a>, Egyptian poet and activist (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malak_Hifni_Nasif\" title=\"Malak Hifni Nasif\"><PERSON><PERSON> Hifni Nasif</a>, Egyptian poet and activist (d. 1918)", "links": [{"title": "Malak Hifni <PERSON>f", "link": "https://wikipedia.org/wiki/<PERSON>ak_<PERSON><PERSON><PERSON>_Nasif"}]}, {"year": "1886", "text": "<PERSON>, American trombonist and bandleader (d. 1973)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and bandleader (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist and bandleader (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American entrepreneur (d. 1979)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a>, American entrepreneur (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hilton\"><PERSON></a>, American entrepreneur (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American publisher and philanthropist, co-founded Reader's Digest (d. 1984)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher and philanthropist, co-founded <i><a href=\"https://wikipedia.org/wiki/Reader%27s_Digest\" title=\"Reader's Digest\">Reader's Digest</a></i> (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American publisher and philanthropist, co-founded <i><a href=\"https://wikipedia.org/wiki/Reader%27s_Digest\" title=\"Reader's Digest\">Reader's Digest</a></i> (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Reader's Digest", "link": "https://wikipedia.org/wiki/Reader%27s_Digest"}]}, {"year": "1890", "text": "<PERSON>, English geologist and mountaineer (d. 1987)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and mountaineer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English geologist and mountaineer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Indian-English general and politician, Governor of Gibraltar (d. 1959)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Indian-English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Indian-English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Gibraltar\" title=\"Governor of Gibraltar\">Governor of Gibraltar</a> (d. 1959)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Army_officer)"}, {"title": "Governor of Gibraltar", "link": "https://wikipedia.org/wiki/Governor_of_Gibraltar"}]}, {"year": "1891", "text": "<PERSON><PERSON><PERSON>, New Zealand-Australian cricketer (d. 1980)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American actor (d. 1957)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "Princess <PERSON>, Duchess of Gloucester (d. 2004)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester\" title=\"Princess <PERSON>, Duchess of Gloucester\">Princess <PERSON>, Duchess of Gloucester</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester\" title=\"Princess <PERSON>, Duchess of Gloucester\">Princess <PERSON>, Duchess of Gloucester</a> (d. 2004)", "links": [{"title": "Princess <PERSON>, Duchess of Gloucester", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Duchess_of_Gloucester"}]}, {"year": "1902", "text": "<PERSON>, American tuba player and educator (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tuba_player)\" class=\"mw-redirect\" title=\"<PERSON> (tuba player)\"><PERSON></a>, American tuba player and educator (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tuba_player)\" class=\"mw-redirect\" title=\"<PERSON> (tuba player)\"><PERSON></a>, American tuba player and educator (d. 1971)", "links": [{"title": "<PERSON> (tuba player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tuba_player)"}]}, {"year": "1902", "text": "<PERSON>, American actor, playwright, and screenwriter (d. 1969)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and screenwriter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, playwright, and screenwriter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Greek composer and conductor (d. 1981)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Evangelatos\" title=\"<PERSON><PERSON> Evangelato<PERSON>\"><PERSON><PERSON></a>, Greek composer and conductor (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gelatos\" title=\"<PERSON><PERSON> Evangelato<PERSON>\"><PERSON><PERSON></a>, Greek composer and conductor (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antiochos_Evangelatos"}]}, {"year": "1904", "text": "<PERSON>, German-Canadian physicist and chemist, Nobel Prize laureate (d. 1999)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian physicist and chemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1904", "text": "<PERSON>, Filipino-American labor leader and farmworker (d. 1994)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American labor leader and farmworker (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American labor leader and farmworker (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, <PERSON>, Ukrainian-English film producer (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Lew_Grade,_Baron_Grade\" class=\"mw-redirect\" title=\"Lew Grade, Baron Grade\"><PERSON><PERSON>, Baron Grade</a>, Ukrainian-English film producer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lew_Grade,_Baron_Grade\" class=\"mw-redirect\" title=\"Lew Grade, Baron Grade\"><PERSON><PERSON>, Baron <PERSON></a>, Ukrainian-English film producer (d. 1998)", "links": [{"title": "Lew Grade, Baron Grade", "link": "https://wikipedia.org/wiki/Lew_Grade,_<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1988)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American singer-songwriter and bandleader (d. 1994)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Calloway\" title=\"<PERSON><PERSON> Calloway\"><PERSON><PERSON></a>, American singer-songwriter and bandleader (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Calloway\" title=\"<PERSON><PERSON> Calloway\"><PERSON><PERSON></a>, American singer-songwriter and bandleader (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cab_Calloway"}]}, {"year": "1907", "text": "<PERSON>, Ukrainian-American wrestler and actor (d. 1990)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American wrestler and actor (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American wrestler and actor (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American businessman, founded the Shamrock Hotel (d. 1988)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Shamrock_Hotel\" title=\"Shamrock Hotel\">Shamrock Hotel</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Shamrock_Hotel\" title=\"Shamrock Hotel\">Shamrock Hotel</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shamrock Hotel", "link": "https://wikipedia.org/wiki/Shamrock_Hotel"}]}, {"year": "1908", "text": "<PERSON>, English author and illustrator (d. 1999)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American general (d. 1993)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON><PERSON>, American baseball player (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belgian-American engineer (d. 1996)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>nto<PERSON>\" title=\"<PERSON><PERSON>-Duntov\"><PERSON><PERSON></a>, Belgian-American engineer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>nto<PERSON>\" title=\"<PERSON><PERSON>-Duntov\"><PERSON><PERSON></a>, Belgian-American engineer (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>v"}]}, {"year": "1911", "text": "<PERSON>, French-American sculptor and painter (d. 2010)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American sculptor and painter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American sculptor and painter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American singer, bass player, and voice actor (d. 1999)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Candy_Candido\" title=\"Candy Candido\"><PERSON></a>, American singer, bass player, and voice actor (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Candy_Candido\" title=\"Candy Candido\"><PERSON></a>, American singer, bass player, and voice actor (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American singer (d. 2012)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" title=\"<PERSON> (American singer)\"><PERSON></a>, American singer (d. 2012)", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)"}]}, {"year": "1914", "text": "<PERSON>, New Zealand businessman (d. 2007)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, New Zealand businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jnr\" title=\"<PERSON> Jnr\"><PERSON> Jnr</a>, New Zealand businessman (d. 2007)", "links": [{"title": "<PERSON> Jnr", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jnr"}]}, {"year": "1914", "text": "<PERSON>, American anthropologist of Latin America (d. 1970)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist of Latin America (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthropologist of Latin America (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Italian-American composer and producer (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American composer and producer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Algerian soldier and politician, 1st President of Algeria (d. 2012)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Algeria\" title=\"President of Algeria\">President of Algeria</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Algeria", "link": "https://wikipedia.org/wiki/President_of_Algeria"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Russian scientist, engineer, pilot, oldest active researcher in aircraft aerodynamics and flight testing (d. 2019)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian scientist, engineer, pilot, oldest active researcher in <a href=\"https://wikipedia.org/wiki/Aircraft\" title=\"Aircraft\">aircraft</a> <a href=\"https://wikipedia.org/wiki/Aerodynamics\" title=\"Aerodynamics\">aerodynamics</a> and <a href=\"https://wikipedia.org/wiki/Flight_test\" title=\"Flight test\">flight testing</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian scientist, engineer, pilot, oldest active researcher in <a href=\"https://wikipedia.org/wiki/Aircraft\" title=\"Aircraft\">aircraft</a> <a href=\"https://wikipedia.org/wiki/Aerodynamics\" title=\"Aerodynamics\">aerodynamics</a> and <a href=\"https://wikipedia.org/wiki/Flight_test\" title=\"Flight test\">flight testing</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}, {"title": "Aircraft", "link": "https://wikipedia.org/wiki/Aircraft"}, {"title": "Aerodynamics", "link": "https://wikipedia.org/wiki/Aerodynamics"}, {"title": "Flight test", "link": "https://wikipedia.org/wiki/Flight_test"}]}, {"year": "1917", "text": "<PERSON>, Ecuadorian journalist and politician (d. 2009)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Lincoln_Verduga_Loor\" title=\"Lincoln Verduga Loor\"><PERSON> Verd<PERSON></a>, Ecuadorian journalist and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lincoln_Verduga_Loor\" title=\"Lincoln Verduga Loor\"><PERSON> Verduga Loor</a>, Ecuadorian journalist and politician (d. 2009)", "links": [{"title": "Lincoln Verduga Loor", "link": "https://wikipedia.org/wiki/Lincoln_Verduga_Loor"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Egyptian lieutenant and politician, 3rd President of Egypt, Nobel Prize laureate (d. 1981)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>war <PERSON>\"><PERSON><PERSON></a>, Egyptian lieutenant and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian lieutenant and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1981)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>at"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Indian composer and director (d. 2006)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer and director (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian composer and director (d. 2006)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Canadian cardiologist and politician, founded the Montreal Heart Institute (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cardiologist)\" title=\"<PERSON> (cardiologist)\"><PERSON></a>, Canadian cardiologist and politician, founded the <a href=\"https://wikipedia.org/wiki/Montreal_Heart_Institute\" title=\"Montreal Heart Institute\">Montreal Heart Institute</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cardiologist)\" title=\"<PERSON> (cardiologist)\"><PERSON></a>, Canadian cardiologist and politician, founded the <a href=\"https://wikipedia.org/wiki/Montreal_Heart_Institute\" title=\"Montreal Heart Institute\">Montreal Heart Institute</a> (d. 1999)", "links": [{"title": "<PERSON> (cardiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cardiologist)"}, {"title": "Montreal Heart Institute", "link": "https://wikipedia.org/wiki/Montreal_Heart_Institute"}]}, {"year": "1919", "text": "<PERSON><PERSON>, English actress (d. 1985)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani journalist and author (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Indian-Pakistani journalist and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>-un-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON></a>, Indian-Pakistani journalist and author (d. 2000)", "links": [{"title": "<PERSON>ai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Polish-Canadian lawyer and politician (d. 1989)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian lawyer and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Canadian lawyer and politician (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American author (d. 2013)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French-American historian, philosopher, and critic (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American historian, philosopher, and critic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American historian, philosopher, and critic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>rd"}]}, {"year": "1923", "text": "<PERSON>, American conductor and educator (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Lane\"><PERSON></a>, American conductor and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"Louis Lane\"><PERSON></a>, American conductor and educator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American screenwriter and producer, created The Twilight Zone (d. 1975)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer, created <i><a href=\"https://wikipedia.org/wiki/The_Twilight_Zone\" title=\"The Twilight Zone\">The Twilight Zone</a></i> (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer, created <i><a href=\"https://wikipedia.org/wiki/The_Twilight_Zone\" title=\"The Twilight Zone\">The Twilight Zone</a></i> (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Twilight Zone", "link": "https://wikipedia.org/wiki/The_Twilight_Zone"}]}, {"year": "1924", "text": "<PERSON><PERSON> <PERSON>, Indian poet and politician, 10th Prime Minister of India (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Atal_Bihari_Vajpayee\" title=\"Atal Bihari Vajpayee\">Atal Bihar<PERSON></a>, Indian poet and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atal_Bihari_Vajpayee\" title=\"Atal Bihari Vajpayee\">Atal Bihar<PERSON>e</a>, Indian poet and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (d. 2018)", "links": [{"title": "Atal Bihari Vajpayee", "link": "https://wikipedia.org/wiki/Atal_Bihari_Vajpayee"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "1925", "text": "<PERSON>, Peruvian-American anthropologist and author (d. 1998)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American anthropologist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian-American anthropologist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American baseball player (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian businessman (d. 2007)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Cuban violinist and composer (d. 1987)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Cuban violinist and composer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON>\"><PERSON></a>, Cuban violinist and composer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn"}]}, {"year": "1927", "text": "<PERSON><PERSON>, American baseball player and coach (d. 1975)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Indian sarangi player (d. 2024)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sarangi\" title=\"<PERSON><PERSON><PERSON>\">sarangi</a> player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sarangi\" title=\"<PERSON><PERSON><PERSON>\">sarangi</a> player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sarangi"}]}, {"year": "1928", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, American actress and model (d. 2002)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Irish_McCalla\" title=\"Irish McCalla\"><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress and model (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_McCalla\" title=\"Irish McCalla\"><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress and model (d. 2002)", "links": [{"title": "Irish McCalla", "link": "https://wikipedia.org/wiki/Irish_McCalla"}]}, {"year": "1928", "text": "<PERSON>, American actor, director, and screenwriter (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American educator and politician (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chinese-born Portuguese-American fashion model, editor and television producer (d. 2016)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/China_Machado\" title=\"China Machado\">China Machado</a>, Chinese-born Portuguese-American fashion model, editor and television producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Machado\" title=\"China Machado\">China Machado</a>, Chinese-born Portuguese-American fashion model, editor and television producer (d. 2016)", "links": [{"title": "China Machado", "link": "https://wikipedia.org/wiki/China_Machado"}]}, {"year": "1929", "text": "<PERSON>, American singer and songwriter (d. 1976)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and songwriter (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Iranian-American boxer and coach (d. 2021)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iranian-American boxer and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Iranian-American boxer and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Armenian basketball player and coach (d. 2017)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>k_<PERSON>an\" title=\"Armenak Alachachian\"><PERSON><PERSON><PERSON></a>, Armenian basketball player and coach (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Armenak Alachachian\"><PERSON><PERSON><PERSON></a>, Armenian basketball player and coach (d. 2017)", "links": [{"title": "<PERSON>enak <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>enak_Alach<PERSON>an"}]}, {"year": "1930", "text": "<PERSON>, Montserrat politician (d. 2005)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Montserrat politician (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Montserrat politician (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actress and singer (d. 1999)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American actress and singer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a>, American actress and singer (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English runner (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Sudanese politician, Prime Minister of Sudan (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Sudan\" title=\"List of heads of government of Sudan\">Prime Minister of Sudan</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sudanese politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_government_of_Sudan\" title=\"List of heads of government of Sudan\">Prime Minister of Sudan</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of government of Sudan", "link": "https://wikipedia.org/wiki/List_of_heads_of_government_of_Sudan"}]}, {"year": "1935", "text": "<PERSON>, American scholar and academic (d. 2009)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and academic (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and academic (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American educator and politician (d. 2007)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "Princess <PERSON>, The Honourable Lady <PERSON>", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>\" title=\"Princess <PERSON>, The Honourable Lady <PERSON>\">Princess <PERSON>, The Honourable Lady <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>\" title=\"Princess <PERSON>, The Honourable Lady <PERSON>\">Princess <PERSON>, The Honourable Lady <PERSON></a>", "links": [{"title": "Princess <PERSON>, The Honourable Lady <PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_The_Honourable_Lady_<PERSON><PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Indian-English director and producer (d. 2005)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ismail Merchant\"><PERSON></a>, Indian-English director and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ismail Merchant\"><PERSON></a>, Indian-English director and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON>, Burmese military officer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Maung_<PERSON>\" title=\"Maung Aye\"><PERSON><PERSON></a>, Burmese military officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ung_<PERSON>\" title=\"Maung Aye\"><PERSON><PERSON></a>, Burmese military officer", "links": [{"title": "Maung Aye", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, American R&B/soul singer-songwriter (d. 1986)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/O%27K<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON>.\"><PERSON><PERSON><PERSON> Jr.</a>, American R&amp;B/soul singer-songwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O%27K<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON><PERSON> Jr.\"><PERSON><PERSON><PERSON>.</a>, American R&amp;B/soul singer-songwriter (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/O%27<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>."}]}, {"year": "1938", "text": "<PERSON><PERSON>, American painter", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian ice hockey player (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Pakistani businessman and politician", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani businessman and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American keyboard player, songwriter, and producer", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American keyboard player, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Tibetan-Chinese spiritual leader (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Akong_Rinpoche\" title=\"Akong Rinpoche\">Akong Rinpoche</a>, Tibetan-Chinese spiritual leader (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Akong_Rinpoche\" title=\"Akong Rinpoche\">Akong Rinpoche</a>, Tibetan-Chinese spiritual leader (d. 2013)", "links": [{"title": "Akong Rinpoche", "link": "https://wikipedia.org/wiki/Akong_Rinpoche"}]}, {"year": "1940", "text": "<PERSON>, English journalist and author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Scottish physician and academic", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, French tennis player and coach", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_D%C3%BCrr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7oise_D%C3%BCrr\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7oise_D%C3%BCrr"}]}, {"year": "1942", "text": "<PERSON>, English politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1942", "text": "<PERSON>, American keyboard player, songwriter, and producer (d. 2025)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American keyboard player, songwriter, and producer (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Spanish singer-songwriter (d. 2010)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Brazilian race car driver and businessman (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_J%C3%BAnior\" title=\"<PERSON>ior\"><PERSON></a>, Brazilian race car driver and businessman (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_J%C3%BAnior\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver and businessman (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wilson_Fittipaldi_J%C3%BAnior"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Indian pilot and military officer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian pilot and military officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian pilot and military officer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>hot<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English journalist and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, English singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, British comedian and broadcaster (d. 1995)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comedian and broadcaster (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British comedian and broadcaster (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON>, New Zealand rugby player (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American screenwriter and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Zambian-Scottish lawyer and politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Zambian-Scottish lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Zambian-Scottish lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and bass player (d. 2003)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American football player and sportscaster (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor (d. 2023)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Buffett\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American sociologist and writer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Brazilian footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Brazilian singer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Pakistani politician, 12th Prime Minister of Pakistan", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Pakistan\" class=\"mw-redirect\" title=\"List of Prime Ministers of Pakistan\">Prime Minister of Pakistan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician, 12th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Pakistan\" class=\"mw-redirect\" title=\"List of Prime Ministers of Pakistan\">Prime Minister of Pakistan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Pakistan", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Pakistan"}]}, {"year": "1949", "text": "<PERSON><PERSON>, American actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Sissy Spacek\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>k\" title=\"<PERSON><PERSON> Spacek\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON> Spacek", "link": "https://wikipedia.org/wiki/Sissy_Spacek"}]}, {"year": "1950", "text": "<PERSON>, English mountaineer and author (d. 1982)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/1950\" title=\"1950\">1950</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1950\" title=\"1950\">1950</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mountaineer and author (d. 1982)", "links": [{"title": "1950", "link": "https://wikipedia.org/wiki/1950"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American political strategist and activist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political strategist and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political strategist and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Venezuelan baseball player and manager", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American video game designer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/1951\" title=\"1951\">1951</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1951\" title=\"1951\">1951</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American video game designer", "links": [{"title": "1951", "link": "https://wikipedia.org/wiki/1951"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"To<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian runner and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Guyanese-American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/CCH_Pounder\" title=\"CCH Pounder\"><PERSON><PERSON> Pounder</a>, Guyanese-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CCH_Pounder\" title=\"<PERSON>H Pounder\"><PERSON><PERSON> Pounder</a>, Guyanese-American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Pounder"}]}, {"year": "1952", "text": "Desire<PERSON>, French singer and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Desireless\" title=\"Desireless\"><PERSON><PERSON></a>, French singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Desireless\" title=\"Desireless\"><PERSON><PERSON></a>, French singer and songwriter", "links": [{"title": "Desireless", "link": "https://wikipedia.org/wiki/Desireless"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Finnish runner", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish singer-songwriter and pianist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Mansoor_<PERSON>\" title=\"Mansoor Akhtar\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mansoor_<PERSON>\" title=\"Mansoor Akhtar\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English footballer and sportscaster", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English-Irish singer-songwriter (d. 2023)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish singer-songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American voice actress and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American voice actress and singer", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American baseball player and coach (d. 2024)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian singer-songwriter and guitarist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, Canadian singer-songwriter and actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 2003)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Indian poet and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian poet and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ale"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Colombian political scientist and politician", "html": "1961 - <a href=\"https://wikipedia.org/wiki/%C3%8Dngrid_Betancourt\" title=\"<PERSON><PERSON><PERSON>ncourt\"><PERSON><PERSON><PERSON></a>, Colombian political scientist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%8Dngrid_Betancourt\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Colombian political scientist and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%8Dngrid_Betancourt"}]}, {"year": "1962", "text": "<PERSON>, English musician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English tenor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tenor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Scottish footballer and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, British musician and writer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, British musician and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, British musician and writer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1965", "text": "<PERSON>, English politician, Leader of the Liberal Democrats", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Liberal_Democrats\" title=\"Leader of the Liberal Democrats\">Leader of the Liberal Democrats</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Leader_of_the_Liberal_Democrats\" title=\"Leader of the Liberal Democrats\">Leader of the Liberal Democrats</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leader of the Liberal Democrats", "link": "https://wikipedia.org/wiki/Leader_of_the_Liberal_Democrats"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Russian ice hockey player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Czech physician and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech physician and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Japanese race car driver", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"To<PERSON> Ara<PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toshi_Arai"}]}, {"year": "1967", "text": "<PERSON>, Austrian politician", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American bass player (d. 1996)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Danish model and actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1969", "text": "<PERSON>, French musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Kenyan runner", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Nigerian footballer and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, English singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1971", "text": "<PERSON>, Irish musician and songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian educator and politician, 23rd Prime Minister of Canada", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 23rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Chinese runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>u_<PERSON>\" title=\"Qu Yunxia\"><PERSON><PERSON></a>, Chinese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>u_<PERSON>xia\" title=\"Qu Yunxia\"><PERSON><PERSON></a>, Chinese runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Qu_<PERSON>xia"}]}, {"year": "1973", "text": "<PERSON>, English footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American wrestler", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Japanese baseball player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian journalist and director", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON>, South Korean footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yong\" title=\"<PERSON>-yong\"><PERSON></a>, South Korean footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-yong\" title=\"<PERSON>-yong\"><PERSON>-<PERSON></a>, South Korean footballer and manager", "links": [{"title": "<PERSON>yong", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-yong"}]}, {"year": "1975", "text": "<PERSON>, English cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Finnish keyboard player, songwriter, and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish keyboard player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish keyboard player, songwriter, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Atko_V%C3%A4ikmeri\" title=\"Atko Väikmeri\"><PERSON><PERSON>äikmeri</a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atko_V%C3%A4ikmeri\" title=\"Atko Väikmeri\"><PERSON><PERSON>äikmeri</a>, Estonian footballer", "links": [{"title": "Atko Väikmeri", "link": "https://wikipedia.org/wiki/Atko_V%C3%A4ikmeri"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Dutch DJ and record producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch DJ and record producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Turkish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Fan\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%9Fan\" title=\"<PERSON>\"><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Tando%C4%9Fan"}]}, {"year": "1977", "text": "<PERSON>, Mexican boxer (d. 2024)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Israel_V%C3%<PERSON><PERSON><PERSON>\" title=\"Israel Vázquez\">Israel <PERSON></a>, Mexican boxer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel_V%C3%<PERSON><PERSON><PERSON>\" title=\"Israel Vázquez\">Israel <PERSON></a>, Mexican boxer (d. 2024)", "links": [{"title": "Israel Vá<PERSON>quez", "link": "https://wikipedia.org/wiki/Israel_V%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Welsh cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Welsh cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Welsh cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Turkish singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Akg%C3%BCl\" title=\"<PERSON><PERSON>g<PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Akg%C3%BCl\" title=\"<PERSON><PERSON>kgül\"><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferman_Akg%C3%BCl"}]}, {"year": "1979", "text": "<PERSON>, French footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, South Korean footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-min\" title=\"<PERSON>yun Young-min\"><PERSON><PERSON>-<PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-min\" title=\"<PERSON><PERSON> Young-min\"><PERSON><PERSON>-<PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON><PERSON>-min", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-min"}]}, {"year": "1980", "text": "<PERSON>, English actress (d. 2003)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, American wrestler and model", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Trene<PERSON>_Biggers\" class=\"mw-redirect\" title=\"Trenesha Biggers\">T<PERSON><PERSON></a>, American wrestler and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trene<PERSON>_Biggers\" class=\"mw-redirect\" title=\"Trenesha Biggers\">T<PERSON><PERSON></a>, American wrestler and model", "links": [{"title": "<PERSON><PERSON><PERSON>gers", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_Biggers"}]}, {"year": "1981", "text": "<PERSON>, American ultramarathon runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ultramarathon runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ultramarathon runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Danish-Faroese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Faroese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-Faroese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dominican baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Welsh footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)"}]}, {"year": "1982", "text": "<PERSON>, Canadian keyboard player, songwriter and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian keyboard player, songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian keyboard player, songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American singer-songwriter and producer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Samoan footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Samoan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, English cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian singer, actress, and fashion designer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actress, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actress, and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian singer, actress, and fashion designer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actress, and fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer, actress, and fashion designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1985", "text": "<PERSON>, Kenyan runner", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Bulgarian-American professional wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Ruse<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON></a>, Bulgarian-American professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruse<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (wrestler)\"><PERSON><PERSON><PERSON></a>, Bulgarian-American professional wrestler", "links": [{"title": "<PERSON><PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/Ruse<PERSON>_(wrestler)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Welsh actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Perdita_Weeks\" title=\"Perdit<PERSON> Weeks\"><PERSON><PERSON><PERSON></a>, Welsh actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Perdita_<PERSON>\" title=\"Perdit<PERSON> Weeks\"><PERSON><PERSON><PERSON></a>, Welsh actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Perdita_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Turkish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Ceyhun_G%C3%BClselam\" title=\"<PERSON>y<PERSON> Gülselam\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ceyhun_G%C3%BClselam\" title=\"<PERSON>y<PERSON> Gülselam\"><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ceyhun_G%C3%BClselam"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON><PERSON>, American football player (d. 2021)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>-<PERSON>, German rugby player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nds-<PERSON>\" title=\"<PERSON><PERSON> Hinds-Johnson\"><PERSON><PERSON>-<PERSON></a>, German rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nds-<PERSON>\" title=\"<PERSON><PERSON> Hinds-Johnson\"><PERSON><PERSON>-<PERSON></a>, German rugby player", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hinds-<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A<PERSON><PERSON><PERSON><PERSON>_(footballer,_born_1988)\" title=\"<PERSON><PERSON><PERSON> (footballer, born 1988)\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer, born 1988)", "link": "https://wikipedia.org/wiki/Jo%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>_(footballer,_born_1988)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese musician, songwriter, actor, model and producer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Avu-chan\" title=\"Avu-chan\"><PERSON><PERSON>-chan</a>, Japanese musician, songwriter, actor, model and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avu-chan\" title=\"Avu-chan\"><PERSON><PERSON>-chan</a>, Japanese musician, songwriter, actor, model and producer", "links": [{"title": "<PERSON><PERSON><PERSON>chan", "link": "https://wikipedia.org/wiki/Avu-chan"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ashi"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese actress, fashion model and singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Takei\"><PERSON><PERSON></a>, Japanese actress, fashion model and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Takei\"><PERSON><PERSON></a>, Japanese actress, fashion model and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Argentine footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Emiliano_Buend%C3%ADa\" title=\"Emiliano Buendía\"><PERSON><PERSON> Buend<PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emiliano_Buend%C3%ADa\" title=\"Emiliano Buendía\"><PERSON><PERSON> Buendía</a>, Argentine footballer", "links": [{"title": "Emiliano Buendía", "link": "https://wikipedia.org/wiki/Emiliano_Buend%C3%ADa"}]}, {"year": "1999", "text": "<PERSON><PERSON>, South Sudanese-Australian fashion model", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Sudanese-Australian fashion model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Sudanese-Australian fashion model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ad<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Ivorian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"Wil<PERSON>\">W<PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "304", "text": "Saint Anastasia", "html": "304 - <a href=\"https://wikipedia.org/wiki/Anastasia_of_Sirmium\" title=\"Anastasia of Sirmium\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anastasia_of_Sirmium\" title=\"Anastasia of Sirmium\"><PERSON></a>", "links": [{"title": "Anastasia of Sirmium", "link": "https://wikipedia.org/wiki/Anastasia_of_Sirmium"}]}, {"year": "795", "text": "<PERSON>", "html": "795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Adrian <PERSON>\"><PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "820", "text": "Emperor <PERSON>", "html": "820 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_V\" class=\"mw-redirect\" title=\"Emperor Leo V\">Emperor <PERSON> V</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_V\" class=\"mw-redirect\" title=\"Emperor Leo V\">Emperor <PERSON> V</a>", "links": [{"title": "Emperor <PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>"}]}, {"year": "936", "text": "<PERSON>, general of Later Tang", "html": "936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Zhang <PERSON>\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Zhang Jing<PERSON>\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Later_Tang\" title=\"Later Tang\">Later Tang</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Later Tang", "link": "https://wikipedia.org/wiki/Later_Tang"}]}, {"year": "940", "text": "<PERSON><PERSON>, Iranian general", "html": "940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Iranian general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_ibn_<PERSON>\" title=\"<PERSON><PERSON> ibn <PERSON>\"><PERSON><PERSON> ibn <PERSON></a>, Iranian general", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1147", "text": "<PERSON>, Count of Ponthieu (b. c. 1120)", "html": "1147 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Ponthieu\" title=\"<PERSON> of Ponthieu\"><PERSON>, Count of Ponthieu</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1120</span>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Ponthieu\" title=\"<PERSON> of Ponthieu\"><PERSON>, Count of Ponthieu</a> (b. <abbr title=\"circa\">c.</abbr><span style=\"white-space:nowrap;\"> 1120</span>)", "links": [{"title": "<PERSON> of Ponthieu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1156", "text": "<PERSON> the Venerable, French abbot and saint (b. 1092)", "html": "1156 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Venerable\" title=\"<PERSON> the Venerable\"><PERSON> the Venerable</a>, French abbot and saint (b. 1092)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Venerable\" title=\"<PERSON> the Venerable\"><PERSON> the Venerable</a>, French abbot and saint (b. 1092)", "links": [{"title": "<PERSON> the Venerable", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Venerable"}]}, {"year": "1156", "text": "<PERSON><PERSON><PERSON> the Elder, king of Sweden", "html": "1156 - <a href=\"https://wikipedia.org/wiki/Sverker_I_of_Sweden\" class=\"mw-redirect\" title=\"Sverker I of Sweden\"><PERSON><PERSON><PERSON> the Elder</a>, king of Sweden", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Sweden\" class=\"mw-redirect\" title=\"Sver<PERSON> I of Sweden\"><PERSON><PERSON><PERSON> the Elder</a>, king of Sweden", "links": [{"title": "Sverker I of Sweden", "link": "https://wikipedia.org/wiki/Sverker_I_of_Sweden"}]}, {"year": "1294", "text": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania", "html": "1294 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II,_Duke_of_Pomerania\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_II,_Duke_of_Pomerania\" title=\"<PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania\"><PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania</a>", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1395", "text": "<PERSON>, Countess of Neuchâtel, Swiss ruler", "html": "1395 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Neuch%C3%A2tel\" title=\"<PERSON>, Countess of Neuchâtel\"><PERSON>, Countess of Neuchâtel</a>, Swiss ruler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Neuch%C3%A2tel\" title=\"<PERSON>, Countess of Neuchâtel\"><PERSON>, Countess of Neuchâtel</a>, Swiss ruler", "links": [{"title": "<PERSON>, Countess of Neuchâtel", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_Neuch%C3%A2tel"}]}, {"year": "1406", "text": "<PERSON> of Castile (b. 1379)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> of Castile</a> (b. 1379)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> III of Castile\"><PERSON> of Castile</a> (b. 1379)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}]}, {"year": "1505", "text": "<PERSON>, 2nd Earl of Kent, English politician (b. 1454)", "html": "1505 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Kent\" title=\"<PERSON>, 2nd Earl of Kent\"><PERSON>, 2nd Earl of Kent</a>, English politician (b. 1454)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Kent\" title=\"<PERSON>, 2nd Earl of Kent\"><PERSON>, 2nd Earl of Kent</a>, English politician (b. 1454)", "links": [{"title": "<PERSON>, 2nd Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Kent"}]}, {"year": "1553", "text": "<PERSON>, Spanish explorer and politician, 1st Royal Governor of Chile (b. 1500)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_Valdivia\" title=\"<PERSON> Valdivia\"><PERSON></a>, Spanish explorer and politician, 1st <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Chile\" title=\"Royal Governor of Chile\">Royal Governor of Chile</a> (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro<PERSON>Valdi<PERSON>\" title=\"<PERSON> Valdivia\"><PERSON></a>, Spanish explorer and politician, 1st <a href=\"https://wikipedia.org/wiki/Royal_Governor_of_Chile\" title=\"Royal Governor of Chile\">Royal Governor of Chile</a> (b. 1500)", "links": [{"title": "<PERSON> Valdi<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>"}, {"title": "Royal Governor of Chile", "link": "https://wikipedia.org/wiki/Royal_Governor_of_Chile"}]}, {"year": "1634", "text": "<PERSON><PERSON><PERSON>, English noblewoman (b. 1543)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman (b. 1543)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Let<PERSON><PERSON>_<PERSON>\" title=\"Let<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English noblewoman (b. 1543)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lettice_<PERSON><PERSON>s"}]}, {"year": "1635", "text": "<PERSON>, French soldier, geographer, and explorer (b. 1567)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, geographer, and explorer (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier, geographer, and explorer (b. 1567)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1676", "text": "<PERSON>, 1st Duke of Newcastle, English soldier and politician, Lord Lieutenant of Nottinghamshire (b. 1592)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Nottinghamshire\" title=\"Lord Lieutenant of Nottinghamshire\">Lord Lieutenant of Nottinghamshire</a> (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Nottinghamshire\" title=\"Lord Lieutenant of Nottinghamshire\">Lord Lieutenant of Nottinghamshire</a> (b. 1592)", "links": [{"title": "<PERSON>, 1st Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle"}, {"title": "Lord Lieutenant of Nottinghamshire", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Nottinghamshire"}]}, {"year": "1676", "text": "<PERSON>, English lawyer and jurist, Lord Chief Justice of England and Wales (b. 1609)", "html": "1676 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, English lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (b. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(jurist)\" title=\"<PERSON> (jurist)\"><PERSON></a>, English lawyer and jurist, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a> (b. 1609)", "links": [{"title": "<PERSON> (jurist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(jurist)"}, {"title": "Lord Chief Justice of England and Wales", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales"}]}, {"year": "1683", "text": "<PERSON>, Ottoman general and politician, 111th Grand Vizier of the Ottoman Empire (b. 1634)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 111th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman general and politician, 111th <a href=\"https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers\" class=\"mw-redirect\" title=\"List of Ottoman Grand Viziers\">Grand Vizier of the Ottoman Empire</a> (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Ottoman Grand Viziers", "link": "https://wikipedia.org/wiki/List_of_Ottoman_Grand_Viziers"}]}, {"year": "1708", "text": "<PERSON><PERSON><PERSON>, German-Norwegian merchant (b. c.1640)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_<PERSON><PERSON>%C3%B8hlen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Norwegian merchant (b. c.1640)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B8rgen_<PERSON><PERSON>%C3%B8hlen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Norwegian merchant (b. c.1640)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B8rgen_Thorm%C3%B8hlen"}]}, {"year": "1730", "text": "<PERSON>, 1st Earl of Deloraine, Scottish peer and general (b. 1676)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Deloraine\" title=\"<PERSON>, 1st Earl of Deloraine\"><PERSON>, 1st Earl of Deloraine</a>, Scottish peer and general (b. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Deloraine\" title=\"<PERSON>, 1st Earl of Deloraine\"><PERSON>, 1st Earl of Deloraine</a>, Scottish peer and general (b. 1676)", "links": [{"title": "<PERSON>, 1st Earl of Deloraine", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Deloraine"}]}, {"year": "1758", "text": "<PERSON>, English priest and author (b. 1714)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and author (b. 1714)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1784", "text": "<PERSON><PERSON>, Japanese poet and painter (b. 1716)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese poet and painter (b. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese poet and painter (b. 1716)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, Queen of Sivagangai (b. 1730)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/Velu_Nachiyar\" title=\"<PERSON>el<PERSON>\"><PERSON><PERSON><PERSON></a>, Queen of Sivagangai (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Velu_Na<PERSON>yar\" title=\"Vel<PERSON> Nachiyar\"><PERSON><PERSON><PERSON></a>, Queen of Sivagangai (b. 1730)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Velu_<PERSON>chiyar"}]}, {"year": "1824", "text": "<PERSON>, German mystic and author (b. 1764)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCdener\" title=\"<PERSON>\"><PERSON></a>, German mystic and author (b. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCdener\" title=\"<PERSON>\"><PERSON></a>, German mystic and author (b. 1764)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BCdener"}]}, {"year": "1824", "text": "<PERSON>, Irish revolutionary, later French Army general (b. 1772)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish revolutionary, later French Army general (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish revolutionary, later French Army general (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Jr., American engineer and businessman (b. 1821)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American engineer and businessman (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Jr.\"><PERSON><PERSON>, Jr.</a>, American engineer and businessman (b. 1821)", "links": [{"title": "<PERSON><PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1875", "text": "<PERSON>, Scottish golfer (b. 1851)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Young Tom Morris\"><PERSON></a>, Scottish golfer (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Young Tom Morris\"><PERSON></a>, Scottish golfer (b. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, Swiss lawyer and politician, President of the Swiss National Council (b. 1828)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ander<PERSON>t\" title=\"<PERSON><PERSON><PERSON>er<PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (b. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_And<PERSON>\" title=\"<PERSON><PERSON><PERSON> Ander<PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland\" class=\"mw-redirect\" title=\"List of Presidents of the National Council of Switzerland\">President of the Swiss National Council</a> (b. 1828)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}, {"title": "List of Presidents of the National Council of Switzerland", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_National_Council_of_Switzerland"}]}, {"year": "1916", "text": "<PERSON>, Polish saint, founded the Albertine Brothers (b. 1845)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish saint, founded the <a href=\"https://wikipedia.org/wiki/Albertine_Brothers\" title=\"Albertine Brothers\">Albertine Brothers</a> (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish saint, founded the <a href=\"https://wikipedia.org/wiki/Albertine_Brothers\" title=\"Albertine Brothers\">Albertine Brothers</a> (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Albertine Brothers", "link": "https://wikipedia.org/wiki/Albertine_Brothers"}]}, {"year": "1921", "text": "<PERSON>, Russian journalist, author, and activist (b. 1853)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist, author, and activist (b. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist, author, and activist (b. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, German psychoanalyst and author (b. 1877)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychoanalyst and author (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German psychoanalyst and author (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (b. 1879)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (b. 1879)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C5%8D"}]}, {"year": "1928", "text": "<PERSON>, American boxer (b. 1885)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Estonian journalist and author (b. 1871)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ndmets\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1871)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4ndmets\" title=\"<PERSON>\"><PERSON></a>, Estonian journalist and author (b. 1871)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jakob_M%C3%A4ndmets"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Catalan colonel and politician, 122nd President of Catalonia (b. 1859)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Frances<PERSON>_Maci%C3%A0\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan colonel and politician, 122nd <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C3%A0\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Catalan colonel and politician, 122nd <a href=\"https://wikipedia.org/wiki/President_of_Catalonia\" class=\"mw-redirect\" title=\"President of Catalonia\">President of Catalonia</a> (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Francesc_Maci%C3%A0"}, {"title": "President of Catalonia", "link": "https://wikipedia.org/wiki/President_of_Catalonia"}]}, {"year": "1935", "text": "<PERSON>, French author and critic (b. 1852)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and critic (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Czech author and playwright (b. 1890)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%8Capek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech author and playwright (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_%C4%8Capek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech author and playwright (b. 1890)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>_%C4%8Capek"}]}, {"year": "1940", "text": "<PERSON>, American actress (b. 1898)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American lawyer and politician (b. 1884)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, South African-English journalist and author (b. 1909)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English journalist and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English journalist and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor, comedian, juggler, and screenwriter (b. 1880)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, comedian, juggler, and screenwriter (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor, comedian, juggler, and screenwriter (b. 1880)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._C._Fields"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, American lawyer and politician, 51st Lieutenant Governor of Massachusetts (b. 1886)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Gaspar G<PERSON>\"><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Gaspar <PERSON>\"><PERSON><PERSON> <PERSON></a>, American lawyer and politician, 51st <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts\" title=\"Lieutenant Governor of Massachusetts\">Lieutenant Governor of Massachusetts</a> (b. 1886)", "links": [{"title": "Gaspar G<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Massachusetts"}]}, {"year": "1949", "text": "<PERSON>, American animator and producer, founded Warner Bros. Cartoons (b. 1884)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer, founded <a href=\"https://wikipedia.org/wiki/Warner_Bros._Cartoons\" title=\"Warner Bros. Cartoons\">Warner Bros. Cartoons</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer, founded <a href=\"https://wikipedia.org/wiki/Warner_Bros._Cartoons\" title=\"Warner Bros. Cartoons\">Warner Bros. Cartoons</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Warner Bros. Cartoons", "link": "https://wikipedia.org/wiki/Warner_Bros._Cartoons"}]}, {"year": "1950", "text": "<PERSON>, English politician (b. 1903)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, American photographer (b. 1886)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>er\"><PERSON><PERSON><PERSON><PERSON></a>, American photographer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>er\"><PERSON><PERSON><PERSON><PERSON></a>, American photographer (b. 1886)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Irish-American baseball player and manager (b. 1865)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American baseball player and manager (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-American baseball player and manager (b. 1865)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, British cartoonist (b. 1872)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cartoonist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British cartoonist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Swiss author and playwright (b. 1878)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss author and playwright (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, French record producer, founded Pathé Records (b. 1863)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Charles_Path%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Path%C3%A9_Records\" title=\"Pathé Records\">Pathé Records</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_Path%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French record producer, founded <a href=\"https://wikipedia.org/wiki/Path%C3%A9_Records\" title=\"Pathé Records\">Pathé Records</a> (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_Path%C3%A9"}, {"title": "Pathé Records", "link": "https://wikipedia.org/wiki/Path%C3%A9_Records"}]}, {"year": "1961", "text": "<PERSON>, American captain, lawyer, and politician, 54th Governor of Maine (b. 1888)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, and politician, 54th <a href=\"https://wikipedia.org/wiki/Governor_of_Maine\" title=\"Governor of Maine\">Governor of Maine</a> (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Maine", "link": "https://wikipedia.org/wiki/Governor_of_Maine"}]}, {"year": "1961", "text": "<PERSON>, German-American pharmacologist and academic, Nobel Prize laureate (b. 1873)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American pharmacologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1963", "text": "<PERSON>, Romanian-French poet, playwright, painter, and critic (b. 1896)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French poet, playwright, painter, and critic (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French poet, playwright, painter, and critic (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Hungarian-English photographer and journalist (b. 1908)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English photographer and journalist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English photographer and journalist (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish general and politician, 2nd President of Turkey (b. 1884)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON>smet İnönü\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC\" title=\"<PERSON>smet İnönü\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1884)", "links": [{"title": "İsmet İnönü", "link": "https://wikipedia.org/wiki/%C4%B0smet_%C4%B0n%C3%B6n%C3%BC"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1973", "text": "<PERSON>, French pilot and engineer (b. 1880)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French pilot and engineer (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French publisher, founded Éditions Gallimard (b. 1881)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French publisher, founded <a href=\"https://wikipedia.org/wiki/%C3%89ditions_Gallimard\" title=\"Éditions Gallimard\">Éditions <PERSON></a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French publisher, founded <a href=\"https://wikipedia.org/wiki/%C3%89ditions_Gallimard\" title=\"Éditions Gallimard\">Éditions Gallimard</a> (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "É<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89ditions_<PERSON>allimard"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Estonian mathematician and author (b. 1913)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian mathematician and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian mathematician and author (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ro"}]}, {"year": "1977", "text": "<PERSON>, English actor and director (b. 1889)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Chaplin\"><PERSON></a>, English actor and director (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress and singer (b. 1906)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Canadian painter and sculptor (b. 1932)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and sculptor (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian painter and sculptor (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English actor and comedian (b. 1900)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and comedian (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Spanish painter and sculptor (b. 1893)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3\" title=\"<PERSON>\"><PERSON></a>, Spanish painter and sculptor (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese author and critic (b. 1909)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Sh%C5%8Dhei_%C5%8Coka\" title=\"Shō<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and critic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%8Dhei_%C5%8Coka\" title=\"Shōhe<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese author and critic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Dhei_%C5%8Coka"}]}, {"year": "1988", "text": "<PERSON>, 10th Duke of Newcastle, English entomologist and lepidopterist (b. 1920)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Duke_of_Newcastle\" title=\"<PERSON>, 10th Duke of Newcastle\"><PERSON>, 10th Duke of Newcastle</a>, English entomologist and lepidopterist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Duke_of_Newcastle\" title=\"<PERSON>, 10th Duke of Newcastle\"><PERSON>, 10th Duke of Newcastle</a>, English entomologist and lepidopterist (b. 1920)", "links": [{"title": "<PERSON>, 10th Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_10th_Duke_of_Newcastle"}]}, {"year": "1989", "text": "<PERSON>, American poker player and businessman (b. 1904)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Benny Binion\"><PERSON></a>, American poker player and businessman (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Bin<PERSON>\" title=\"Benny Binion\"><PERSON></a>, American poker player and businessman (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Binion"}]}, {"year": "1989", "text": "<PERSON>, Romanian politician, First Lady of Romania (b. 1916)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian politician, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Romania\" title=\"First Lady of Romania\">First Lady of Romania</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian politician, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Romania\" title=\"First Lady of Romania\">First Lady of Romania</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C8%99escu"}, {"title": "First Lady of Romania", "link": "https://wikipedia.org/wiki/First_Lady_of_Romania"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Romanian general and politician, 1st President of Romania (b. 1918)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Romania\" title=\"President of Romania\">President of Romania</a> (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C8%99escu"}, {"title": "President of Romania", "link": "https://wikipedia.org/wiki/President_of_Romania"}]}, {"year": "1989", "text": "<PERSON>, American actress (b. 1905)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American judge and politician, 34th Lieutenant Governor of California (b. 1905)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge and politician, 34th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_California\" title=\"Lieutenant Governor of California\">Lieutenant Governor of California</a> (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>r"}, {"title": "Lieutenant Governor of California", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_California"}]}, {"year": "1989", "text": "<PERSON>, American baseball player and manager (b. 1928)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American director and screenwriter (b. 1910)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and screenwriter (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American football player and wrestler (b. 1929)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and wrestler (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player and wrestler (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, British-American nurse and author (b. 1915)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American nurse and author (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American nurse and author (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French physicist and academic (b. 1899)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Indian politician, 7th President of India (b. 1916)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, 7th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1995", "text": "<PERSON>, Lithuanian-French philosopher and academic (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-French philosopher and academic (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-French philosopher and academic (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer and actor (b. 1917)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Korean surgeon (b. 1914)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yo\" title=\"<PERSON>-ryo\"><PERSON></a>, Korean surgeon (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yo\" title=\"<PERSON>-ryo\"><PERSON></a>, Korean surgeon (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ryo"}]}, {"year": "1996", "text": "<PERSON>, Canadian sportscaster (b. 1928)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sportscaster (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)\" title=\"<PERSON> (sportscaster)\"><PERSON></a>, Canadian sportscaster (b. 1928)", "links": [{"title": "<PERSON> (sportscaster)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportscaster)"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Kazakh mountaineer and explorer (b. 1958)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ree<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh mountaineer and explorer (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh mountaineer and explorer (b. 1958)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>v"}]}, {"year": "1997", "text": "<PERSON>, American actor (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Denver_Pyle\" title=\"Denver Pyle\"><PERSON></a>, American actor (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denver_Pyle\" title=\"Denver Pyle\"><PERSON></a>, American actor (b. 1920)", "links": [{"title": "Denver Pyle", "link": "https://wikipedia.org/wiki/Denver_Pyle"}]}, {"year": "1998", "text": "<PERSON>, English snooker player (b. 1923)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian cricketer and footballer (b. 1939)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and footballer (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American philosopher and academic (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and academic (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French otolaryngologist and academic (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French otolaryngologist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French otolaryngologist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American politician (b. 1929)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Russian engineer and astronaut (b. 1940)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Gennadi_Strekalov\" class=\"mw-redirect\" title=\"Gennadi Strekalov\"><PERSON><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gennadi_Strekalov\" class=\"mw-redirect\" title=\"Gennadi Strekalov\"><PERSON><PERSON><PERSON></a>, Russian engineer and astronaut (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Genna<PERSON>_<PERSON>alov"}]}, {"year": "2005", "text": "<PERSON>, English guitarist (b. 1930)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(guitarist)\" title=\"<PERSON> (guitarist)\"><PERSON></a>, English guitarist (b. 1930)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>(guitarist)"}]}, {"year": "2005", "text": "<PERSON>, Filipino police officer, lawyer, and politician, 15th Secretary of the Interior and Local Government (b. 1944)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino police officer, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino police officer, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government\" title=\"Secretary of the Interior and Local Government\">Secretary of the Interior and Local Government</a> (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of the Interior and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_the_Interior_and_Local_Government"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Swedish operatic soprano (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish operatic soprano (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish operatic soprano (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Sri Lankan journalist, businessman, and politician (b. 1934)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan journalist, businessman, and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan journalist, businessman, and politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American singer-songwriter (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, English cricketer (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_Barrick"}]}, {"year": "2007", "text": "<PERSON>, American baseball player and coach (b. 1939)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON>, American singer and actress (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1964)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Venezuelan politician, 66th President of Venezuela (b. 1922)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician, 66th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9s_P%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, Venezuelan politician, 66th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela\" class=\"mw-redirect\" title=\"List of Presidents of Venezuela\">President of Venezuela</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Andr%C3%A9s_P%C3%A9rez"}, {"title": "List of Presidents of Venezuela", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Venezuela"}]}, {"year": "2011", "text": "<PERSON>, Italian journalist (b. 1920)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American saxophonist (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, American author and illustrator (b. 1932)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Si<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>back\"><PERSON><PERSON><PERSON></a>, American author and illustrator (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Si<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and illustrator (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Filipino journalist, lawyer, and politician (b. 1940)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, lawyer, and politician (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino journalist, lawyer, and politician (b. 1940)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eric<PERSON>_Aumentado"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Norwegian radio host and politician (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun\" title=\"<PERSON>dan Hegtun\"><PERSON><PERSON></a>, Norwegian radio host and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun\" title=\"<PERSON><PERSON> Hegtun\"><PERSON><PERSON></a>, Norwegian radio host and politician (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Hegtun"}]}, {"year": "2012", "text": "<PERSON>, American football player and coach (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian politician (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (b. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish lawyer, politician, government minister (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/%C5%9Eerafettin_El%C3%A7i\" title=\"Şerafettin Elçi\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer, politician, government minister (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9Eerafettin_El%C3%A7i\" title=\"Şerafettin Elçi\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish lawyer, politician, government minister (b. 1938)", "links": [{"title": "Şerafettin Elçi", "link": "https://wikipedia.org/wiki/%C5%9Eerafettin_El%C3%A7i"}]}, {"year": "2013", "text": "<PERSON>, American historian and author (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English geographer, anthropologist, archaeologist and academic (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English geographer, anthropologist, archaeologist and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(geographer)\" title=\"<PERSON> (geographer)\"><PERSON></a>, English geographer, anthropologist, archaeologist and academic (b. 1930)", "links": [{"title": "<PERSON> (geographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_(geographer)"}]}, {"year": "2013", "text": "<PERSON>, English footballer (b. 1967)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1967)\" title=\"<PERSON> (footballer, born 1967)\"><PERSON></a>, English footballer (b. 1967)", "links": [{"title": "<PERSON> (footballer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1967)"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and sportscaster (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Canadian rural feminist and appointee to the Royal Commission on the Status of Women (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rural feminist and appointee to the <a href=\"https://wikipedia.org/wiki/Royal_Commission_on_the_Status_of_Women\" title=\"Royal Commission on the Status of Women\">Royal Commission on the Status of Women</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian rural feminist and appointee to the <a href=\"https://wikipedia.org/wiki/Royal_Commission_on_the_Status_of_Women\" title=\"Royal Commission on the Status of Women\">Royal Commission on the Status of Women</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Royal Commission on the Status of Women", "link": "https://wikipedia.org/wiki/Royal_Commission_on_the_Status_of_Women"}]}, {"year": "2013", "text": "<PERSON>, Filipino politician, 8th Mayor of Quezon City (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quezon_City\" title=\"Mayor of Quezon City\">Mayor of Quezon City</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino politician, 8th <a href=\"https://wikipedia.org/wiki/Mayor_of_Quezon_City\" title=\"Mayor of Quezon City\">Mayor of Quezon City</a> (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mayor of Quezon City", "link": "https://wikipedia.org/wiki/Mayor_of_Quezon_City"}]}, {"year": "2014", "text": "<PERSON>, Cuban-French architect (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-French architect (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-French architect (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English cricketer (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English actor (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American author and screenwriter (b. 1929)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American author and historian (b. 1961)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>\" title=\"Acharya <PERSON>\"><PERSON></a>, American author and historian (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON>\" title=\"Acharya <PERSON>\"><PERSON></a>, American author and historian (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Russian military musician and composer (b. 1952)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian military musician and composer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian military musician and composer (b. 1952)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, British singer and songwriter (b. 1963)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer and songwriter (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British singer and songwriter (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American astronomer (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON>, American magazine publisher (Philadelphia, Boston) (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American magazine publisher (<i><a href=\"https://wikipedia.org/wiki/Philadelphia_(magazine)\" title=\"Philadelphia (magazine)\">Philadelphia</a></i>, <i><a href=\"https://wikipedia.org/wiki/Boston_(magazine)\" title=\"Boston (magazine)\">Boston</a></i>) (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American magazine publisher (<i><a href=\"https://wikipedia.org/wiki/Philadelphia_(magazine)\" title=\"Philadelphia (magazine)\">Philadelphia</a></i>, <i><a href=\"https://wikipedia.org/wiki/Boston_(magazine)\" title=\"Boston (magazine)\">Boston</a></i>) (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Philadelphia (magazine)", "link": "https://wikipedia.org/wiki/Philadelphia_(magazine)"}, {"title": "Boston (magazine)", "link": "https://wikipedia.org/wiki/Boston_(magazine)"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, Indian midwife (b. 1920)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Sul<PERSON><PERSON>_Narasamma\" title=\"Sulagi<PERSON> Narasamma\"><PERSON><PERSON><PERSON></a>, Indian midwife (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sul<PERSON><PERSON>_Naras<PERSON>ma\" title=\"<PERSON><PERSON><PERSON> Narasamma\"><PERSON><PERSON><PERSON></a>, Indian midwife (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ma"}]}, {"year": "2019", "text": "<PERSON>, Norwegian writer (b. 1972)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian writer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian writer (b. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player and coach (b. 1932)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (b. 1932)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American artist (b. 1920)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American artist (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer (b. 1973)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_O%27Neill\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fabi%C3%A1n_O%27Neill\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer (b. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fabi%C3%A1n_O%27Neill"}]}, {"year": "2023", "text": "<PERSON>, British wrestler (b. 1940)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim Breaks\"><PERSON></a>, British wrestler (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim Breaks\"><PERSON></a>, British wrestler (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, English writer (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English writer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English writer (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>croft"}]}, {"year": "2024", "text": "<PERSON>, American football player (b. 1945)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American professional wrestler (b. 1976)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestler (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dane\"><PERSON></a>, American professional wrestler (b. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author and screenwriter (b. 1933)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and screenwriter (b. 1933)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, Japanese businessman (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)\" title=\"<PERSON><PERSON><PERSON> (businessman)\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)\" title=\"<PERSON><PERSON><PERSON> (businessman)\"><PERSON><PERSON><PERSON></a>, Japanese businessman (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(businessman)"}]}]}}