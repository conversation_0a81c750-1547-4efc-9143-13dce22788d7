{"date": "May 22", "url": "https://wikipedia.org/wiki/May_22", "data": {"Events": [{"year": "192", "text": "<PERSON> is assassinated by his adopted son <PERSON><PERSON>.", "html": "192 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated by his adopted son <a href=\"https://wikipedia.org/wiki/L%C3%BC_Bu\" title=\"<PERSON>ü Bu\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is assassinated by his adopted son <a href=\"https://wikipedia.org/wiki/L%C3%BC_Bu\" title=\"<PERSON>ü Bu\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%BC_Bu"}]}, {"year": "760", "text": "Fourteenth recorded perihelion passage of <PERSON><PERSON>'s <PERSON>.", "html": "760 - Fourteenth recorded perihelion passage of <a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a>.", "no_year_html": "Fourteenth recorded perihelion passage of <a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a>.", "links": [{"title": "Halley's Comet", "link": "https://wikipedia.org/wiki/Halley%27s_Comet"}]}, {"year": "853", "text": "A Byzantine fleet sacks and destroys undefended Damietta in Egypt.", "html": "853 - A Byzantine fleet <a href=\"https://wikipedia.org/wiki/Sack_of_Damietta_(853)\" title=\"Sack of Damietta (853)\">sacks and destroys</a> undefended <a href=\"https://wikipedia.org/wiki/Damietta\" title=\"Damietta\">Damietta</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "no_year_html": "A Byzantine fleet <a href=\"https://wikipedia.org/wiki/Sack_of_Damietta_(853)\" title=\"Sack of Damietta (853)\">sacks and destroys</a> undefended <a href=\"https://wikipedia.org/wiki/Damietta\" title=\"Damietta\">Damietta</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>.", "links": [{"title": "Sack of Damietta (853)", "link": "https://wikipedia.org/wiki/Sack_of_Damietta_(853)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Damietta"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1176", "text": "The <PERSON><PERSON><PERSON>n (Assassins) attempt to assassinate <PERSON><PERSON><PERSON> near Aleppo.", "html": "1176 - The Hashshashin (<a href=\"https://wikipedia.org/wiki/Order_of_Assassins\" title=\"Order of Assassins\">Assassins</a>) attempt to assassinate <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> near <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>.", "no_year_html": "The Hashshashin (<a href=\"https://wikipedia.org/wiki/Order_of_Assassins\" title=\"Order of Assassins\">Assassins</a>) attempt to assassinate <a href=\"https://wikipedia.org/wiki/Saladin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> near <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>.", "links": [{"title": "Order of Assassins", "link": "https://wikipedia.org/wiki/Order_of_Assassins"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n"}, {"title": "Aleppo", "link": "https://wikipedia.org/wiki/Aleppo"}]}, {"year": "1200", "text": "King <PERSON> of England and King <PERSON> of France sign the Treaty of Le Goulet.", "html": "1200 - King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> and King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Le_Goulet\" title=\"Treaty of Le Goulet\">Treaty of Le Goulet</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> and King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Le_Goulet\" title=\"Treaty of Le Goulet\">Treaty of Le Goulet</a>.", "links": [{"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France"}, {"title": "Treaty of Le Goulet", "link": "https://wikipedia.org/wiki/Treaty_of_Le_Goulet"}]}, {"year": "1246", "text": "<PERSON> is elected anti-king of the Kingdom of Germany in opposition to <PERSON>.", "html": "1246 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Thuringia\" class=\"mw-redirect\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Anti-king\" title=\"Anti-king\">anti-king</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Germany\" title=\"Kingdom of Germany\">Kingdom of Germany</a> in opposition to <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"<PERSON> IV of Germany\"><PERSON> IV</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Landgrave_of_Thuringia\" class=\"mw-redirect\" title=\"<PERSON>, Landgrave of Thuringia\"><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/Anti-king\" title=\"Anti-king\">anti-king</a> of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Germany\" title=\"Kingdom of Germany\">Kingdom of Germany</a> in opposition to <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_Germany\" title=\"<PERSON> IV of Germany\"><PERSON> IV</a>.", "links": [{"title": "<PERSON>, Landgrave of Thuringia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_of_Thuringia"}, {"title": "Anti-king", "link": "https://wikipedia.org/wiki/Anti-king"}, {"title": "Kingdom of Germany", "link": "https://wikipedia.org/wiki/Kingdom_of_Germany"}, {"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/Conrad_IV_of_Germany"}]}, {"year": "1254", "text": "Serbian King <PERSON> and the Republic of Venice sign a peace treaty.", "html": "1254 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">Serbian King</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1_I\" title=\"<PERSON>\"><PERSON> I</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> sign a peace treaty.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)\" class=\"mw-redirect\" title=\"Kingdom of Serbia (medieval)\">Serbian King</a> <a href=\"https://wikipedia.org/wiki/Stefan_Uro%C5%A1_I\" title=\"<PERSON>\"><PERSON> I</a> and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> sign a peace treaty.", "links": [{"title": "Kingdom of Serbia (medieval)", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia_(medieval)"}, {"title": "<PERSON>š <PERSON>", "link": "https://wikipedia.org/wiki/Stefan_Uro%C5%A1_I"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1370", "text": "Brussels massacre: An estimated 13 Jews are murdered and the rest of the Jewish community is banished from Brussels, Belgium, for allegedly desecrating consecrated Host.", "html": "1370 - <a href=\"https://wikipedia.org/wiki/Brussels_massacre\" title=\"Brussels massacre\">Brussels massacre</a>: An estimated 13 Jews are murdered and the rest of the Jewish community is banished from <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, for allegedly desecrating consecrated <a href=\"https://wikipedia.org/wiki/Sacramental_bread\" title=\"Sacramental bread\">Host</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brussels_massacre\" title=\"Brussels massacre\">Brussels massacre</a>: An estimated 13 Jews are murdered and the rest of the Jewish community is banished from <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, <a href=\"https://wikipedia.org/wiki/Belgium\" title=\"Belgium\">Belgium</a>, for allegedly desecrating consecrated <a href=\"https://wikipedia.org/wiki/Sacramental_bread\" title=\"Sacramental bread\">Host</a>.", "links": [{"title": "Brussels massacre", "link": "https://wikipedia.org/wiki/Brussels_massacre"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}, {"title": "Belgium", "link": "https://wikipedia.org/wiki/Belgium"}, {"title": "Sacramental bread", "link": "https://wikipedia.org/wiki/Sacramental_bread"}]}, {"year": "1377", "text": "<PERSON> <PERSON> issues five papal bulls to denounce the doctrines of English theologian <PERSON>.", "html": "1377 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XI\">Pope Gregory <PERSON></a> issues five <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bulls</a> to denounce the <a href=\"https://wikipedia.org/wiki/Doctrine\" title=\"Doctrine\">doctrines</a> of English <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theologian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory <PERSON>\">Pope Gregory <PERSON></a> issues five <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bulls</a> to denounce the <a href=\"https://wikipedia.org/wiki/Doctrine\" title=\"Doctrine\">doctrines</a> of English <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theologian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Doctrine", "link": "https://wikipedia.org/wiki/Doctrine"}, {"title": "Theology", "link": "https://wikipedia.org/wiki/Theology"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1455", "text": "Start of the Wars of the Roses: At the First Battle of St Albans, <PERSON>, Duke of York, defeats and captures King <PERSON> of England.", "html": "1455 - Start of the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>: At the <a href=\"https://wikipedia.org/wiki/First_Battle_of_St_Albans\" title=\"First Battle of St Albans\">First Battle of St Albans</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_York\" title=\"<PERSON> of York, 3rd Duke of York\"><PERSON>, Duke of York</a>, defeats and captures <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Henry VI of England\"><PERSON> of England</a>.", "no_year_html": "Start of the <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>: At the <a href=\"https://wikipedia.org/wiki/First_Battle_of_St_Albans\" title=\"First Battle of St Albans\">First Battle of St Albans</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_York\" title=\"<PERSON> of York, 3rd Duke of York\"><PERSON>, Duke of York</a>, defeats and captures <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> of England</a>.", "links": [{"title": "Wars of the Roses", "link": "https://wikipedia.org/wiki/Wars_of_the_Roses"}, {"title": "First Battle of St Albans", "link": "https://wikipedia.org/wiki/First_Battle_of_St_Albans"}, {"title": "<PERSON> York, 3rd Duke of York", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_York"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1520", "text": "The massacre at the festival of Tóxcatl takes place during the Fall of Tenochtitlan, resulting in turning the Aztecs against the Spanish.", "html": "1520 - The <a href=\"https://wikipedia.org/wiki/Massacre_in_the_Great_Temple_of_Tenochtitlan\" title=\"Massacre in the Great Temple of Tenochtitlan\">massacre at the festival of Tóxcatl</a> takes place during the <a href=\"https://wikipedia.org/wiki/Fall_of_Tenochtitlan\" title=\"Fall of Tenochtitlan\">Fall of Tenochtitlan</a>, resulting in turning the Aztecs against the Spanish.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Massacre_in_the_Great_Temple_of_Tenochtitlan\" title=\"Massacre in the Great Temple of Tenochtitlan\">massacre at the festival of Tóxcatl</a> takes place during the <a href=\"https://wikipedia.org/wiki/Fall_of_Tenochtitlan\" title=\"Fall of Tenochtitlan\">Fall of Tenochtitlan</a>, resulting in turning the Aztecs against the Spanish.", "links": [{"title": "Massacre in the Great Temple of Tenochtitlan", "link": "https://wikipedia.org/wiki/Massacre_in_the_Great_Temple_of_Tenochtitlan"}, {"title": "Fall of Tenochtitlan", "link": "https://wikipedia.org/wiki/Fall_of_Tenochtitlan"}]}, {"year": "1629", "text": "Holy Roman Emperor <PERSON> and Danish King <PERSON> sign the Treaty of Lübeck ending Danish intervention in the Thirty Years' War.", "html": "1629 - <a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/List_of_Danish_monarchs\" title=\"List of Danish monarchs\">Danish King</a> <a href=\"https://wikipedia.org/wiki/Christian_IV_of_Denmark\" title=\"Christian IV of Denmark\">Christian IV</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_L%C3%BCbeck\" title=\"Treaty of Lübeck\">Treaty of Lübeck</a> ending Danish intervention in the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Holy_Roman_Emperor\" title=\"Holy Roman Emperor\">Holy Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/List_of_Danish_monarchs\" title=\"List of Danish monarchs\">Danish King</a> <a href=\"https://wikipedia.org/wiki/Christian_IV_of_Denmark\" title=\"Christian IV of Denmark\">Christian IV</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_L%C3%BCbeck\" title=\"Treaty of Lübeck\">Treaty of Lübeck</a> ending Danish intervention in the <a href=\"https://wikipedia.org/wiki/Thirty_Years%27_War\" title=\"Thirty Years' War\">Thirty Years' War</a>.", "links": [{"title": "Holy Roman Emperor", "link": "https://wikipedia.org/wiki/Holy_Roman_Emperor"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "List of Danish monarchs", "link": "https://wikipedia.org/wiki/List_of_Danish_monarchs"}, {"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Christian_IV_of_Denmark"}, {"title": "Treaty of Lübeck", "link": "https://wikipedia.org/wiki/Treaty_of_L%C3%BCbeck"}, {"title": "Thirty Years' War", "link": "https://wikipedia.org/wiki/Thirty_Years%27_War"}]}, {"year": "1762", "text": "Sweden and Prussia sign the Treaty of Hamburg.", "html": "1762 - Sweden and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Hamburg_(1762)\" title=\"Treaty of Hamburg (1762)\">Treaty of Hamburg</a>.", "no_year_html": "Sweden and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Hamburg_(1762)\" title=\"Treaty of Hamburg (1762)\">Treaty of Hamburg</a>.", "links": [{"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Treaty of Hamburg (1762)", "link": "https://wikipedia.org/wiki/Treaty_of_Hamburg_(1762)"}]}, {"year": "1762", "text": "Trevi Fountain is officially completed and inaugurated in Rome.", "html": "1762 - <a href=\"https://wikipedia.org/wiki/Trevi_Fountain\" title=\"Trevi Fountain\">Trevi Fountain</a> is officially completed and inaugurated in Rome.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trevi_Fountain\" title=\"Trevi Fountain\">Trevi Fountain</a> is officially completed and inaugurated in Rome.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Trevi_Fountain"}]}, {"year": "1766", "text": "A large earthquake causes heavy damage and loss of life in Istanbul and the Marmara region.", "html": "1766 - A <a href=\"https://wikipedia.org/wiki/1766_Istanbul_earthquake\" title=\"1766 Istanbul earthquake\">large earthquake</a> causes heavy damage and loss of life in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a> and the Marmara region.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1766_Istanbul_earthquake\" title=\"1766 Istanbul earthquake\">large earthquake</a> causes heavy damage and loss of life in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a> and the Marmara region.", "links": [{"title": "1766 Istanbul earthquake", "link": "https://wikipedia.org/wiki/1766_Istanbul_earthquake"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}]}, {"year": "1804", "text": "The Lewis and Clark Expedition officially begins as the Corps of Discovery departs from St. Charles, Missouri.", "html": "1804 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_Expedition\" title=\"Lewis and Clark Expedition\"><PERSON> and Clark Expedition</a> officially begins as the <a href=\"https://wikipedia.org/wiki/Corps_of_Discovery\" title=\"Corps of Discovery\">Corps of Discovery</a> departs from St. Charles, Missouri.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_and_Clark_Expedition\" title=\"Lewis and Clark Expedition\"><PERSON> and <PERSON> Expedition</a> officially begins as the <a href=\"https://wikipedia.org/wiki/Corps_of_Discovery\" title=\"Corps of Discovery\">Corps of Discovery</a> departs from St. Charles, Missouri.", "links": [{"title": "<PERSON> and Clark Expedition", "link": "https://wikipedia.org/wiki/<PERSON>_and_Clark_Expedition"}, {"title": "Corps of Discovery", "link": "https://wikipedia.org/wiki/Corps_of_Discovery"}]}, {"year": "1807", "text": "A grand jury indicts former Vice President of the United States <PERSON> on a charge of treason.", "html": "1807 - A <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> indicts former <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on a charge of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> indicts former <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> on a charge of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "Grand jury", "link": "https://wikipedia.org/wiki/Grand_jury"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1809", "text": "On the second and last day of the Battle of Aspern-Essling (near Vienna, Austria), <PERSON> is defeated in a major battle for the first time in his career, and repelled by an enemy army for the first time in a decade.", "html": "1809 - On the second and last day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Aspern-Essling\" title=\"Battle of Aspern-Essling\">Battle of Aspern-Essling</a> (near <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria), <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a> I is defeated in a major battle for the first time in his career, and repelled by an enemy army for the first time in a decade.", "no_year_html": "On the second and last day of the <a href=\"https://wikipedia.org/wiki/Battle_of_Aspern-Essling\" title=\"Battle of Aspern-Essling\">Battle of Aspern-Essling</a> (near <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>, Austria), <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"Napoleon\"><PERSON></a> I is defeated in a major battle for the first time in his career, and repelled by an enemy army for the first time in a decade.", "links": [{"title": "Battle of Aspern-Essling", "link": "https://wikipedia.org/wiki/Battle_of_Aspern-Essling"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}]}, {"year": "1816", "text": "A mob in Littleport, Cambridgeshire, England, riots over high unemployment and rising grain costs, and the riots spread to Ely the next day.", "html": "1816 - A mob in <a href=\"https://wikipedia.org/wiki/Littleport,_Cambridgeshire\" class=\"mw-redirect\" title=\"Littleport, Cambridgeshire\">Littleport, Cambridgeshire</a>, England, <a href=\"https://wikipedia.org/wiki/Ely_and_Littleport_riots_of_1816\" title=\"Ely and Littleport riots of 1816\">riots</a> over high unemployment and rising grain costs, and the riots spread to <a href=\"https://wikipedia.org/wiki/Ely,_Cambridgeshire\" title=\"Ely, Cambridgeshire\">Ely</a> the next day.", "no_year_html": "A mob in <a href=\"https://wikipedia.org/wiki/Littleport,_Cambridgeshire\" class=\"mw-redirect\" title=\"Littleport, Cambridgeshire\">Littleport, Cambridgeshire</a>, England, <a href=\"https://wikipedia.org/wiki/Ely_and_Littleport_riots_of_1816\" title=\"Ely and Littleport riots of 1816\">riots</a> over high unemployment and rising grain costs, and the riots spread to <a href=\"https://wikipedia.org/wiki/Ely,_Cambridgeshire\" title=\"Ely, Cambridgeshire\">Ely</a> the next day.", "links": [{"title": "Littleport, Cambridgeshire", "link": "https://wikipedia.org/wiki/Littleport,_Cambridgeshire"}, {"title": "Ely and Littleport riots of 1816", "link": "https://wikipedia.org/wiki/Ely_and_Littleport_riots_of_1816"}, {"title": "Ely, Cambridgeshire", "link": "https://wikipedia.org/wiki/Ely,_Cambridgeshire"}]}, {"year": "1819", "text": "SS Savannah leaves port at Savannah, Georgia, United States, on a voyage to become the first steamship to cross the Atlantic Ocean.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/SS_Savannah\" title=\"SS Savannah\">SS <i>Savannah</i></a> leaves port at <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, United States, on a voyage to become the first <a href=\"https://wikipedia.org/wiki/Steamship\" title=\"Steamship\">steamship</a> to cross the Atlantic Ocean.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SS_Savannah\" title=\"SS Savannah\">SS <i>Savannah</i></a> leaves port at <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, United States, on a voyage to become the first <a href=\"https://wikipedia.org/wiki/Steamship\" title=\"Steamship\">steamship</a> to cross the Atlantic Ocean.", "links": [{"title": "SS Savannah", "link": "https://wikipedia.org/wiki/SS_Savannah"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}, {"title": "Steamship", "link": "https://wikipedia.org/wiki/Steamship"}]}, {"year": "1826", "text": "HMS Beagle departs on its first voyage.", "html": "1826 - <a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON><PERSON><PERSON></i></a> departs on its first voyage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Beagle\" title=\"HMS Beagle\">HMS <i><PERSON><PERSON>le</i></a> departs on its first voyage.", "links": [{"title": "HMS Beagle", "link": "https://wikipedia.org/wiki/HMS_Beagle"}]}, {"year": "1840", "text": "The penal transportation of British convicts to the New South Wales colony is abolished.", "html": "1840 - The <a href=\"https://wikipedia.org/wiki/Penal_transportation\" title=\"Penal transportation\">penal transportation</a> of British <a href=\"https://wikipedia.org/wiki/Convict\" title=\"Convict\">convicts</a> to the <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a> <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> is abolished.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Penal_transportation\" title=\"Penal transportation\">penal transportation</a> of British <a href=\"https://wikipedia.org/wiki/Convict\" title=\"Convict\">convicts</a> to the <a href=\"https://wikipedia.org/wiki/New_South_Wales\" title=\"New South Wales\">New South Wales</a> <a href=\"https://wikipedia.org/wiki/Colony\" title=\"Colony\">colony</a> is abolished.", "links": [{"title": "Penal transportation", "link": "https://wikipedia.org/wiki/Penal_transportation"}, {"title": "Convict", "link": "https://wikipedia.org/wiki/Convict"}, {"title": "New South Wales", "link": "https://wikipedia.org/wiki/New_South_Wales"}, {"title": "Colony", "link": "https://wikipedia.org/wiki/Colony"}]}, {"year": "1846", "text": "The Associated Press is formed in New York City as a non-profit news cooperative.", "html": "1846 - The <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> is formed in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> as a non-profit news cooperative.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Associated_Press\" title=\"Associated Press\">Associated Press</a> is formed in <a href=\"https://wikipedia.org/wiki/New_York_City\" title=\"New York City\">New York City</a> as a non-profit news cooperative.", "links": [{"title": "Associated Press", "link": "https://wikipedia.org/wiki/Associated_Press"}, {"title": "New York City", "link": "https://wikipedia.org/wiki/New_York_City"}]}, {"year": "1848", "text": "Slavery is abolished in Martinique.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/History_of_Martinique#1815-1899\" title=\"History of Martinique\">Slavery is abolished</a> in <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\">Martinique</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/History_of_Martinique#1815-1899\" title=\"History of Martinique\">Slavery is abolished</a> in <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\">Martinique</a>.", "links": [{"title": "History of Martinique", "link": "https://wikipedia.org/wiki/History_of_Martinique#1815-1899"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1849", "text": "Future U.S. President <PERSON> is issued a patent for an invention to lift boats, making him the only U.S. president to ever hold a patent.", "html": "1849 - Future <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is issued a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_patent\" title=\"<PERSON>'s patent\">invention to lift boats</a>, making him the only U.S. president to ever hold a patent.", "no_year_html": "Future <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is issued a <a href=\"https://wikipedia.org/wiki/Patent\" title=\"Patent\">patent</a> for an <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_patent\" title=\"<PERSON>'s patent\">invention to lift boats</a>, making him the only U.S. president to ever hold a patent.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Patent", "link": "https://wikipedia.org/wiki/Patent"}, {"title": "<PERSON>'s patent", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_patent"}]}, {"year": "1856", "text": "Congressman <PERSON> of South Carolina severely beats Senator <PERSON> of Massachusetts with a cane in the hall of the United States Senate for a speech <PERSON> had made regarding  Southerners and slavery.", "html": "1856 - <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">Congressman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> <a href=\"https://wikipedia.org/wiki/Caning_of_<PERSON>_<PERSON>\" title=\"Caning of <PERSON>\">severely beats</a> <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> with a cane in the hall of the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> for a speech <PERSON> had made regarding <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southerners</a> and <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">Congressman</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/South_Carolina\" title=\"South Carolina\">South Carolina</a> <a href=\"https://wikipedia.org/wiki/Caning_of_<PERSON>_<PERSON>\" title=\"Caning of <PERSON>\">severely beats</a> <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">Senator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Massachusetts\" title=\"Massachusetts\">Massachusetts</a> with a cane in the hall of the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> for a speech <PERSON> had made regarding <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">Southerners</a> and <a href=\"https://wikipedia.org/wiki/Slavery_in_the_United_States\" title=\"Slavery in the United States\">slavery</a>.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "South Carolina", "link": "https://wikipedia.org/wiki/South_Carolina"}, {"title": "Caning of <PERSON>", "link": "https://wikipedia.org/wiki/Caning_of_<PERSON>_<PERSON>"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Massachusetts", "link": "https://wikipedia.org/wiki/Massachusetts"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}, {"title": "Slavery in the United States", "link": "https://wikipedia.org/wiki/Slavery_in_the_United_States"}]}, {"year": "1863", "text": "American Civil War: Union forces begin the Siege of Port Hudson which lasts 48 days, the longest siege in U.S. military history.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> which lasts 48 days, the longest siege in U.S. military history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Port_Hudson\" title=\"Siege of Port Hudson\">Siege of Port Hudson</a> which lasts 48 days, the longest siege in U.S. military history.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Siege of Port Hudson", "link": "https://wikipedia.org/wiki/Siege_of_Port_Hudson"}]}, {"year": "1864", "text": "American Civil War: After ten weeks, the Union Army's Red River Campaign ends in failure.", "html": "1864 - American Civil War: After ten weeks, the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>'s <a href=\"https://wikipedia.org/wiki/Red_River_Campaign\" class=\"mw-redirect\" title=\"Red River Campaign\">Red River Campaign</a> ends in failure.", "no_year_html": "American Civil War: After ten weeks, the <a href=\"https://wikipedia.org/wiki/Union_Army\" class=\"mw-redirect\" title=\"Union Army\">Union Army</a>'s <a href=\"https://wikipedia.org/wiki/Red_River_Campaign\" class=\"mw-redirect\" title=\"Red River Campaign\">Red River Campaign</a> ends in failure.", "links": [{"title": "Union Army", "link": "https://wikipedia.org/wiki/Union_Army"}, {"title": "Red River Campaign", "link": "https://wikipedia.org/wiki/Red_River_Campaign"}]}, {"year": "1866", "text": "<PERSON> founded the Winchester Repeating Arms", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Oliver_<PERSON>\" title=\"Oliver Winchester\"><PERSON></a> founded the Winchester Repeating Arms", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oliver_Winchester\" title=\"Oliver Winchester\"><PERSON></a> founded the Winchester Repeating Arms", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "Reconstruction Era: President <PERSON> signs the Amnesty Act into law, restoring full civil and political rights to all but about 500 Confederate sympathizers.", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Amnesty_Act\" title=\"Amnesty Act\">Amnesty Act</a> into law, restoring full <a href=\"https://wikipedia.org/wiki/Civil_and_political_rights\" title=\"Civil and political rights\">civil and political rights</a> to all but about 500 Confederate sympathizers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconstruction_Era\" class=\"mw-redirect\" title=\"Reconstruction Era\">Reconstruction Era</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Amnesty_Act\" title=\"Amnesty Act\">Amnesty Act</a> into law, restoring full <a href=\"https://wikipedia.org/wiki/Civil_and_political_rights\" title=\"Civil and political rights\">civil and political rights</a> to all but about 500 Confederate sympathizers.", "links": [{"title": "Reconstruction Era", "link": "https://wikipedia.org/wiki/Reconstruction_Era"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Amnesty Act", "link": "https://wikipedia.org/wiki/Amnesty_Act"}, {"title": "Civil and political rights", "link": "https://wikipedia.org/wiki/Civil_and_political_rights"}]}, {"year": "1874", "text": "<PERSON>'s Requiem was first performed at San Marco in Milan on the first anniversary of <PERSON><PERSON><PERSON>'s death.", "html": "1874 - <PERSON>'s <a href=\"https://wikipedia.org/wiki/Requiem_(<PERSON>)\" title=\"Requiem (<PERSON>)\">Requiem</a> was first performed at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Milan\" title=\"San Marco, Milan\"><PERSON></a> in Milan on the first anniversary of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>'s death.", "no_year_html": "<PERSON>'s <a href=\"https://wikipedia.org/wiki/Requiem_(<PERSON>)\" title=\"Requiem (<PERSON>)\">Requiem</a> was first performed at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Milan\" title=\"San Marco, Milan\"><PERSON></a> in Milan on the first anniversary of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>'s death.", "links": [{"title": "Requiem (Verdi)", "link": "https://wikipedia.org/wiki/Requiem_(Verdi)"}, {"title": "San Marco, Milan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Milan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "The Sultan of the Ottoman Empire <PERSON> establishes the Ullah millet for the Aromanians of the empire. For this reason, the Aromanian National Day is sometimes celebrated on this day, although most do so on May 23 instead, which is when this event was publicly announced.", "html": "1905 - The <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> II</a> establishes the <a href=\"https://wikipedia.org/wiki/Ullah_millet\" title=\"Ullah millet\">Ullah millet</a> for the <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> of the empire. For this reason, the <a href=\"https://wikipedia.org/wiki/Aromanian_National_Day\" title=\"Aromanian National Day\">Aromanian National Day</a> is sometimes celebrated on this day, although most do so on <a href=\"https://wikipedia.org/wiki/May_23\" title=\"May 23\">May 23</a> instead, which is when this event was publicly announced.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire\" class=\"mw-redirect\" title=\"Sultan of the Ottoman Empire\">Sultan of the Ottoman Empire</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON> II</a> establishes the <a href=\"https://wikipedia.org/wiki/Ullah_millet\" title=\"Ullah millet\">Ullah millet</a> for the <a href=\"https://wikipedia.org/wiki/Aromanians\" title=\"Aromanians\">Aromanians</a> of the empire. For this reason, the <a href=\"https://wikipedia.org/wiki/Aromanian_National_Day\" title=\"Aromanian National Day\">Aromanian National Day</a> is sometimes celebrated on this day, although most do so on <a href=\"https://wikipedia.org/wiki/May_23\" title=\"May 23\">May 23</a> instead, which is when this event was publicly announced.", "links": [{"title": "Sultan of the Ottoman Empire", "link": "https://wikipedia.org/wiki/Sultan_of_the_Ottoman_Empire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> millet", "link": "https://wikipedia.org/wiki/U<PERSON>_millet"}, {"title": "Aromanians", "link": "https://wikipedia.org/wiki/Aromanians"}, {"title": "Aromanian National Day", "link": "https://wikipedia.org/wiki/Aromanian_National_Day"}, {"title": "May 23", "link": "https://wikipedia.org/wiki/May_23"}]}, {"year": "1906", "text": "The <PERSON> brothers are granted U.S. patent number 821,393 for their \"Flying-Machine\".", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\">Wright brothers</a> are granted U.S. patent number 821,393 for their \"<a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">Flying-Machine</a>\".", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_brothers\" title=\"<PERSON> brothers\">Wright brothers</a> are granted U.S. patent number 821,393 for their \"<a href=\"https://wikipedia.org/wiki/Airplane\" title=\"Airplane\">Flying-Machine</a>\".", "links": [{"title": "<PERSON> brothers", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Airplane", "link": "https://wikipedia.org/wiki/Airplane"}]}, {"year": "1915", "text": "Lassen Peak erupts with a powerful force, the only volcano besides Mount St. Helens to erupt in the contiguous U.S. during the 20th century.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Lassen_Peak\" title=\"Lassen Peak\">Lassen Peak</a> erupts with a powerful force, the only volcano besides <a href=\"https://wikipedia.org/wiki/Mount_St._Helens\" title=\"Mount St. Helens\">Mount St. Helens</a> to erupt in the contiguous U.S. during the 20th century.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lassen_Peak\" title=\"Lassen Peak\">Lassen Peak</a> erupts with a powerful force, the only volcano besides <a href=\"https://wikipedia.org/wiki/Mount_St._Helens\" title=\"Mount St. Helens\">Mount St. Helens</a> to erupt in the contiguous U.S. during the 20th century.", "links": [{"title": "Lassen Peak", "link": "https://wikipedia.org/wiki/Lassen_Peak"}, {"title": "Mount St. Helens", "link": "https://wikipedia.org/wiki/Mount_St._Helens"}]}, {"year": "1915", "text": "Three trains collide in the Quintinshill rail disaster near Gretna Green, Scotland, killing 227 people and injuring 246.", "html": "1915 - Three trains collide in the <a href=\"https://wikipedia.org/wiki/Quintinshill_rail_disaster\" title=\"Quintinshill rail disaster\">Quintinshill rail disaster</a> near <a href=\"https://wikipedia.org/wiki/Gretna_Green\" title=\"Gretna Green\">Gretna Green</a>, Scotland, killing 227 people and injuring 246.", "no_year_html": "Three trains collide in the <a href=\"https://wikipedia.org/wiki/Quintinshill_rail_disaster\" title=\"Quintinshill rail disaster\">Quintinshill rail disaster</a> near <a href=\"https://wikipedia.org/wiki/Gretna_Green\" title=\"Gretna Green\">Gretna Green</a>, Scotland, killing 227 people and injuring 246.", "links": [{"title": "Quintinshill rail disaster", "link": "https://wikipedia.org/wiki/Quintinshill_rail_disaster"}, {"title": "<PERSON><PERSON><PERSON> Green", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_Green"}]}, {"year": "1926", "text": "<PERSON> replaces the communists in Kuomintang China.[vague]", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> replaces the communists in <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a> China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> replaces the communists in <a href=\"https://wikipedia.org/wiki/Kuomintang\" title=\"Kuomintang\">Kuomintang</a> China.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Kuomintang", "link": "https://wikipedia.org/wiki/Kuomintang"}]}, {"year": "1927", "text": "Near Xining, China, an 8.3 magnitude earthquake causes 200,000 deaths in one of the world's most destructive earthquakes.", "html": "1927 - Near <a href=\"https://wikipedia.org/wiki/Xining\" title=\"Xining\">Xining</a>, China, an 8.3 magnitude <a href=\"https://wikipedia.org/wiki/1927_Gulang_earthquake\" title=\"1927 Gulang earthquake\">earthquake</a> causes 200,000 deaths in one of the world's most destructive earthquakes.", "no_year_html": "Near <a href=\"https://wikipedia.org/wiki/Xining\" title=\"Xining\">Xining</a>, China, an 8.3 magnitude <a href=\"https://wikipedia.org/wiki/1927_Gulang_earthquake\" title=\"1927 Gulang earthquake\">earthquake</a> causes 200,000 deaths in one of the world's most destructive earthquakes.", "links": [{"title": "Xining", "link": "https://wikipedia.org/wiki/Xining"}, {"title": "1927 Gulang earthquake", "link": "https://wikipedia.org/wiki/1927_Gulang_earthquake"}]}, {"year": "1939", "text": "World War II: Germany and Italy sign the Pact of Steel.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany and Italy sign the <a href=\"https://wikipedia.org/wiki/Pact_of_Steel\" title=\"Pact of Steel\">Pact of Steel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Germany and Italy sign the <a href=\"https://wikipedia.org/wiki/Pact_of_Steel\" title=\"Pact of Steel\">Pact of Steel</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Pact of Steel", "link": "https://wikipedia.org/wiki/Pact_of_Steel"}]}, {"year": "1941", "text": "During the Anglo-Iraqi War, British troops take Fallujah.", "html": "1941 - During the <a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a>, British troops take <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a>, British troops take <a href=\"https://wikipedia.org/wiki/Fallujah\" title=\"Fallujah\">Fallujah</a>.", "links": [{"title": "Anglo-Iraqi War", "link": "https://wikipedia.org/wiki/Anglo-Iraqi_War"}, {"title": "Fallujah", "link": "https://wikipedia.org/wiki/Fallujah"}]}, {"year": "1942", "text": "Mexico enters the Second World War on the side of the Allies.", "html": "1942 - <a href='https://wikipedia.org/wiki/History_of_Mexico#\"Revolution_to_evolution\",_1940-1970' title=\"History of Mexico\">Mexico enters the Second World War</a> on the side of the Allies.", "no_year_html": "<a href='https://wikipedia.org/wiki/History_of_Mexico#\"Revolution_to_evolution\",_1940-1970' title=\"History of Mexico\">Mexico enters the Second World War</a> on the side of the Allies.", "links": [{"title": "History of Mexico", "link": "https://wikipedia.org/wiki/History_of_Mexico#\"Revolution_to_evolution\",_1940-1970"}]}, {"year": "1943", "text": "<PERSON> disbands the Comintern.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> disbands the <a href=\"https://wikipedia.org/wiki/Comintern\" class=\"mw-redirect\" title=\"Comintern\">Comintern</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> disbands the <a href=\"https://wikipedia.org/wiki/Comintern\" class=\"mw-redirect\" title=\"Comintern\">Comintern</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Comintern", "link": "https://wikipedia.org/wiki/Comintern"}]}, {"year": "1947", "text": "Cold War: The Truman Doctrine goes into effect, aiding Turkey and Greece.", "html": "1947 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Truman_Doctrine\" title=\"Truman Doctrine\">Truman Doctrine</a> goes into effect, aiding Turkey and Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: The <a href=\"https://wikipedia.org/wiki/Truman_Doctrine\" title=\"Truman Doctrine\"><PERSON> Doctrine</a> goes into effect, aiding Turkey and Greece.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Truman Doctrine", "link": "https://wikipedia.org/wiki/Truman_Doctrine"}]}, {"year": "1948", "text": "Finnish President <PERSON><PERSON> <PERSON><PERSON> releases <PERSON><PERSON><PERSON><PERSON> from his duties as interior minister in 1948 after the Finnish parliament adopted a motion of censure of <PERSON><PERSON> with connection to his illegal handing over of nineteen people to the Soviet Union in 1945.", "html": "1948 - Finnish President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> releases <a href=\"https://wikipedia.org/wiki/Yrj%C3%B<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> from his duties as <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)\" title=\"Minister of the Interior (Finland)\">interior minister</a> in 1948 after the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish parliament</a> adopted a motion of censure of <PERSON><PERSON> with connection to his illegal handing over of nineteen people to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in 1945.", "no_year_html": "Finnish President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> releases <a href=\"https://wikipedia.org/wiki/Yrj%C3%B<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> from his duties as <a href=\"https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)\" title=\"Minister of the Interior (Finland)\">interior minister</a> in 1948 after the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish parliament</a> adopted a motion of censure of <PERSON><PERSON> with connection to his illegal handing over of nineteen people to the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> in 1945.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yrj%C3%B6_<PERSON><PERSON>"}, {"title": "Minister of the Interior (Finland)", "link": "https://wikipedia.org/wiki/Minister_of_the_Interior_(Finland)"}, {"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1957", "text": "South Africa's government approves of racial separation in universities.", "html": "1957 - South Africa's government approves of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">racial separation</a> in universities.", "no_year_html": "South Africa's government approves of <a href=\"https://wikipedia.org/wiki/Apartheid\" title=\"Apartheid\">racial separation</a> in universities.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>heid"}]}, {"year": "1958", "text": "The 1958 riots in Ceylon become a watershed in the race relations of various ethnic communities of Sri Lanka. The total deaths are estimated at 300, mostly Tamils.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/1958_riots_in_Ceylon\" class=\"mw-redirect\" title=\"1958 riots in Ceylon\">1958 riots in Ceylon</a> become a watershed in the race relations of various ethnic communities of Sri Lanka. The total deaths are estimated at 300, mostly <a href=\"https://wikipedia.org/wiki/Tamils\" title=\"Tamils\">Tamils</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1958_riots_in_Ceylon\" class=\"mw-redirect\" title=\"1958 riots in Ceylon\">1958 riots in Ceylon</a> become a watershed in the race relations of various ethnic communities of Sri Lanka. The total deaths are estimated at 300, mostly <a href=\"https://wikipedia.org/wiki/Tamils\" title=\"Tamils\">Tamils</a>.", "links": [{"title": "1958 riots in Ceylon", "link": "https://wikipedia.org/wiki/1958_riots_in_Ceylon"}, {"title": "Tamils", "link": "https://wikipedia.org/wiki/Tamils"}]}, {"year": "1960", "text": "The Great Chilean earthquake, measuring 9.5 on the moment magnitude scale, hits southern Chile, becoming the most powerful earthquake ever recorded.", "html": "1960 - The <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">Great Chilean earthquake</a>, measuring 9.5 on the <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">moment magnitude scale</a>, hits southern <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, becoming the most powerful earthquake ever recorded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1960_Valdivia_earthquake\" title=\"1960 Valdivia earthquake\">Great Chilean earthquake</a>, measuring 9.5 on the <a href=\"https://wikipedia.org/wiki/Moment_magnitude_scale\" title=\"Moment magnitude scale\">moment magnitude scale</a>, hits southern <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, becoming the most powerful earthquake ever recorded.", "links": [{"title": "1960 Valdivia earthquake", "link": "https://wikipedia.org/wiki/1960_Valdivia_earthquake"}, {"title": "Moment magnitude scale", "link": "https://wikipedia.org/wiki/Moment_magnitude_scale"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "1962", "text": "Continental Airlines Flight 11 crashes in Unionville, Missouri after bombs explode on board, killing 45.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Continental_Airlines_Flight_11\" title=\"Continental Airlines Flight 11\">Continental Airlines Flight 11</a> crashes in <a href=\"https://wikipedia.org/wiki/Unionville,_Missouri\" title=\"Unionville, Missouri\">Unionville, Missouri</a> after bombs explode on board, killing 45.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Continental_Airlines_Flight_11\" title=\"Continental Airlines Flight 11\">Continental Airlines Flight 11</a> crashes in <a href=\"https://wikipedia.org/wiki/Unionville,_Missouri\" title=\"Unionville, Missouri\">Unionville, Missouri</a> after bombs explode on board, killing 45.", "links": [{"title": "Continental Airlines Flight 11", "link": "https://wikipedia.org/wiki/Continental_Airlines_Flight_11"}, {"title": "Unionville, Missouri", "link": "https://wikipedia.org/wiki/Unionville,_Missouri"}]}, {"year": "1963", "text": "Greek left-wing politician <PERSON><PERSON><PERSON> is clubbed over the head, causing his death five days later.", "html": "1963 - Greek left-wing politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is clubbed over the head, causing his death five days later.", "no_year_html": "Greek left-wing politician <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is clubbed over the head, causing his death five days later.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "U.S. President <PERSON> launches his Great Society program.", "html": "1964 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches his <a href=\"https://wikipedia.org/wiki/Great_Society\" title=\"Great Society\">Great Society</a> program.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> launches his <a href=\"https://wikipedia.org/wiki/Great_Society\" title=\"Great Society\">Great Society</a> program.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Great Society", "link": "https://wikipedia.org/wiki/Great_Society"}]}, {"year": "1967", "text": "Egypt closes the Straits of Tiran to Israeli shipping.", "html": "1967 - Egypt closes the <a href=\"https://wikipedia.org/wiki/Straits_of_Tiran\" title=\"Straits of Tiran\">Straits of Tiran</a> to Israeli shipping.", "no_year_html": "Egypt closes the <a href=\"https://wikipedia.org/wiki/Straits_of_Tiran\" title=\"Straits of Tiran\">Straits of Tiran</a> to Israeli shipping.", "links": [{"title": "Straits of Tiran", "link": "https://wikipedia.org/wiki/Straits_of_Tiran"}]}, {"year": "1967", "text": "L'Innovation department store in Brussels, Belgium, burns down, resulting in 323 dead or missing and 150 injured, the most devastating fire in Belgian history.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/L%27Innovation_Department_Store_fire\" class=\"mw-redirect\" title=\"L'Innovation Department Store fire\">L'Innovation</a> department store in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, Belgium, burns down, resulting in 323 dead or missing and 150 injured, the most devastating fire in Belgian history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%27Innovation_Department_Store_fire\" class=\"mw-redirect\" title=\"L'Innovation Department Store fire\">L'Innovation</a> department store in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a>, Belgium, burns down, resulting in 323 dead or missing and 150 injured, the most devastating fire in Belgian history.", "links": [{"title": "L'Innovation Department Store fire", "link": "https://wikipedia.org/wiki/L%27Innovation_Department_Store_fire"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}]}, {"year": "1968", "text": "The nuclear-powered submarine USS Scorpion sinks with 99 men aboard, 400 miles southwest of the Azores.", "html": "1968 - The nuclear-powered <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Scorpion_(SSN-589)\" title=\"USS Scorpion (SSN-589)\">USS <i>Scorpion</i></a> sinks with 99 men aboard, 400 miles southwest of the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "no_year_html": "The nuclear-powered <a href=\"https://wikipedia.org/wiki/Submarine\" title=\"Submarine\">submarine</a> <a href=\"https://wikipedia.org/wiki/USS_Scorpion_(SSN-589)\" title=\"USS Scorpion (SSN-589)\">USS <i>Scorpion</i></a> sinks with 99 men aboard, 400 miles southwest of the <a href=\"https://wikipedia.org/wiki/Azores\" title=\"Azores\">Azores</a>.", "links": [{"title": "Submarine", "link": "https://wikipedia.org/wiki/Submarine"}, {"title": "USS Scorpion (SSN-589)", "link": "https://wikipedia.org/wiki/USS_Scorpion_(SSN-589)"}, {"title": "Azores", "link": "https://wikipedia.org/wiki/Azores"}]}, {"year": "1969", "text": "Apollo 10's <PERSON><PERSON> flies within 8.4 nautical miles (16 km) of the Moon's surface.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_10\" title=\"Apollo 10\">Apollo 10</a>'s <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Lunar Module</a> flies within 8.4 <a href=\"https://wikipedia.org/wiki/Nautical_mile\" title=\"Nautical mile\">nautical miles</a> (16 km) of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>'s surface.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_10\" title=\"Apollo 10\">Apollo 10</a>'s <a href=\"https://wikipedia.org/wiki/Apollo_Lunar_Module\" title=\"Apollo Lunar Module\">Lunar Module</a> flies within 8.4 <a href=\"https://wikipedia.org/wiki/Nautical_mile\" title=\"Nautical mile\">nautical miles</a> (16 km) of the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>'s surface.", "links": [{"title": "Apollo 10", "link": "https://wikipedia.org/wiki/Apollo_10"}, {"title": "Apollo Lunar Module", "link": "https://wikipedia.org/wiki/Apollo_Lunar_Module"}, {"title": "Nautical mile", "link": "https://wikipedia.org/wiki/Nautical_mile"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1972", "text": "Ceylon adopts a new constitution, becoming a republic and changing its name to Sri Lanka.", "html": "1972 - Ceylon adopts a <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Constitution_of_1972\" title=\"Sri Lankan Constitution of 1972\">new constitution</a>, becoming a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> and changing its name to <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "no_year_html": "Ceylon adopts a <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Constitution_of_1972\" title=\"Sri Lankan Constitution of 1972\">new constitution</a>, becoming a <a href=\"https://wikipedia.org/wiki/Republic\" title=\"Republic\">republic</a> and changing its name to <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>.", "links": [{"title": "Sri Lankan Constitution of 1972", "link": "https://wikipedia.org/wiki/Sri_Lankan_Constitution_of_1972"}, {"title": "Republic", "link": "https://wikipedia.org/wiki/Republic"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1972", "text": "Over 400 women in Derry, Northern Ireland attack the offices of Sinn Féin following the shooting by the Irish Republican Army of a young British soldier on leave.", "html": "1972 - Over 400 women in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> attack the offices of <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a> following the shooting by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> of a young British soldier on leave.", "no_year_html": "Over 400 women in <a href=\"https://wikipedia.org/wiki/Derry\" title=\"Derry\">Derry</a>, <a href=\"https://wikipedia.org/wiki/Northern_Ireland\" title=\"Northern Ireland\">Northern Ireland</a> attack the offices of <a href=\"https://wikipedia.org/wiki/Sinn_F%C3%A9in\" title=\"Sinn Féin\">Sinn Féin</a> following the shooting by the <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> of a young British soldier on leave.", "links": [{"title": "Derry", "link": "https://wikipedia.org/wiki/Derry"}, {"title": "Northern Ireland", "link": "https://wikipedia.org/wiki/Northern_Ireland"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sinn_F%C3%A9in"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}]}, {"year": "1987", "text": "Hashimpura massacre occurs in Meerut, India.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Hashimpura_massacre\" title=\"Hashimpura massacre\">Hashimpura massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Meerut\" title=\"Meerut\">Meerut</a>, India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hashimpura_massacre\" title=\"Hashimpura massacre\">Hashimpura massacre</a> occurs in <a href=\"https://wikipedia.org/wiki/Meerut\" title=\"Meerut\">Meerut</a>, India.", "links": [{"title": "Hashimpura massacre", "link": "https://wikipedia.org/wiki/Hashim<PERSON>_massacre"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Meerut"}]}, {"year": "1987", "text": "First ever Rugby World Cup kicks off with New Zealand playing Italy at Eden Park in Auckland, New Zealand.", "html": "1987 - First ever <a href=\"https://wikipedia.org/wiki/1987_Rugby_World_Cup\" title=\"1987 Rugby World Cup\">Rugby World Cup</a> kicks off with <a href=\"https://wikipedia.org/wiki/New_Zealand_Rugby_Union\" class=\"mw-redirect\" title=\"New Zealand Rugby Union\">New Zealand</a> playing <a href=\"https://wikipedia.org/wiki/Italy_national_rugby_union_team\" title=\"Italy national rugby union team\">Italy</a> at <a href=\"https://wikipedia.org/wiki/Eden_Park\" title=\"Eden Park\">Eden Park</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand.", "no_year_html": "First ever <a href=\"https://wikipedia.org/wiki/1987_Rugby_World_Cup\" title=\"1987 Rugby World Cup\">Rugby World Cup</a> kicks off with <a href=\"https://wikipedia.org/wiki/New_Zealand_Rugby_Union\" class=\"mw-redirect\" title=\"New Zealand Rugby Union\">New Zealand</a> playing <a href=\"https://wikipedia.org/wiki/Italy_national_rugby_union_team\" title=\"Italy national rugby union team\">Italy</a> at <a href=\"https://wikipedia.org/wiki/Eden_Park\" title=\"Eden Park\">Eden Park</a> in <a href=\"https://wikipedia.org/wiki/Auckland\" title=\"Auckland\">Auckland</a>, New Zealand.", "links": [{"title": "1987 Rugby World Cup", "link": "https://wikipedia.org/wiki/1987_Rugby_World_Cup"}, {"title": "New Zealand Rugby Union", "link": "https://wikipedia.org/wiki/New_Zealand_Rugby_Union"}, {"title": "Italy national rugby union team", "link": "https://wikipedia.org/wiki/Italy_national_rugby_union_team"}, {"title": "Eden Park", "link": "https://wikipedia.org/wiki/Eden_Park"}, {"title": "Auckland", "link": "https://wikipedia.org/wiki/Auckland"}]}, {"year": "1990", "text": "North and South Yemen are unified to create the Republic of Yemen.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Yemen_Arab_Republic\" title=\"Yemen Arab Republic\">North</a> and <a href=\"https://wikipedia.org/wiki/South_Yemen\" title=\"South Yemen\">South Yemen</a> are <a href=\"https://wikipedia.org/wiki/Yemeni_unification\" title=\"Yemeni unification\">unified</a> to create the Republic of <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yemen_Arab_Republic\" title=\"Yemen Arab Republic\">North</a> and <a href=\"https://wikipedia.org/wiki/South_Yemen\" title=\"South Yemen\">South Yemen</a> are <a href=\"https://wikipedia.org/wiki/Yemeni_unification\" title=\"Yemeni unification\">unified</a> to create the Republic of <a href=\"https://wikipedia.org/wiki/Yemen\" title=\"Yemen\">Yemen</a>.", "links": [{"title": "Yemen Arab Republic", "link": "https://wikipedia.org/wiki/Yemen_Arab_Republic"}, {"title": "South Yemen", "link": "https://wikipedia.org/wiki/South_Yemen"}, {"title": "Yemeni unification", "link": "https://wikipedia.org/wiki/Yemeni_unification"}, {"title": "Yemen", "link": "https://wikipedia.org/wiki/Yemen"}]}, {"year": "1992", "text": "Bosnia and Herzegovina, Croatia and Slovenia join the United Nations.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a>, <a href=\"https://wikipedia.org/wiki/Croatia\" title=\"Croatia\">Croatia</a> and <a href=\"https://wikipedia.org/wiki/Slovenia\" title=\"Slovenia\">Slovenia</a> join the <a href=\"https://wikipedia.org/wiki/United_Nations\" title=\"United Nations\">United Nations</a>.", "links": [{"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "Croatia", "link": "https://wikipedia.org/wiki/Croatia"}, {"title": "Slovenia", "link": "https://wikipedia.org/wiki/Slovenia"}, {"title": "United Nations", "link": "https://wikipedia.org/wiki/United_Nations"}]}, {"year": "1994", "text": "A worldwide trade embargo against Haiti goes into effect to punish its military rulers for not reinstating the country's ousted elected leader, <PERSON><PERSON><PERSON>.", "html": "1994 - A worldwide trade embargo against <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> goes into effect to punish its military rulers for not reinstating the country's ousted elected leader, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "A worldwide trade embargo against <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a> goes into effect to punish its military rulers for not reinstating the country's ousted elected leader, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "The Burmese military regime jails 71 supporters of <PERSON>ng <PERSON> in a bid to block a pro-democracy meeting.", "html": "1996 - The Burmese military regime jails 71 supporters of <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Su<PERSON> K<PERSON></a> in a bid to block a pro-democracy meeting.", "no_year_html": "The Burmese military regime jails 71 supporters of <a href=\"https://wikipedia.org/wiki/Aung_San_Suu_Kyi\" title=\"Aung San Suu Kyi\">Aung San Su<PERSON></a> in a bid to block a pro-democracy meeting.", "links": [{"title": "Aung San Suu Kyi", "link": "https://wikipedia.org/wiki/Aung_San_Suu_K<PERSON>"}]}, {"year": "1998", "text": "A U.S. federal judge rules that U.S. Secret Service agents can be compelled to testify before a grand jury concerning the <PERSON><PERSON><PERSON> scandal involving President <PERSON>.", "html": "1998 - A U.S. federal judge rules that <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">U.S. Secret Service</a> agents can be compelled to testify before a <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> concerning the <a href=\"https://wikipedia.org/wiki/Lewinsky_scandal\" class=\"mw-redirect\" title=\"Lewinsky scandal\"><PERSON><PERSON>sky scandal</a> involving President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A U.S. federal judge rules that <a href=\"https://wikipedia.org/wiki/United_States_Secret_Service\" title=\"United States Secret Service\">U.S. Secret Service</a> agents can be compelled to testify before a <a href=\"https://wikipedia.org/wiki/Grand_jury\" title=\"Grand jury\">grand jury</a> concerning the <a href=\"https://wikipedia.org/wiki/Lewinsky_scandal\" class=\"mw-redirect\" title=\"Lewinsky scandal\"><PERSON><PERSON>sky scandal</a> involving President <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "United States Secret Service", "link": "https://wikipedia.org/wiki/United_States_Secret_Service"}, {"title": "Grand jury", "link": "https://wikipedia.org/wiki/Grand_jury"}, {"title": "<PERSON><PERSON><PERSON> scandal", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_scandal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "In Sri Lanka, over 150 Tamil rebels are killed over two days of fighting for control in Jaffna.", "html": "2000 - In <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, over 150 <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil_people\" class=\"mw-redirect\" title=\"Sri Lankan Tamil people\">Tamil</a> rebels are killed over two days of fighting for control in <a href=\"https://wikipedia.org/wiki/Jaffna\" title=\"Jaffna\">Jaffna</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, over 150 <a href=\"https://wikipedia.org/wiki/Sri_Lankan_Tamil_people\" class=\"mw-redirect\" title=\"Sri Lankan Tamil people\">Tamil</a> rebels are killed over two days of fighting for control in <a href=\"https://wikipedia.org/wiki/Jaffna\" title=\"Jaffna\">Jaffna</a>.", "links": [{"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}, {"title": "Sri Lankan Tamil people", "link": "https://wikipedia.org/wiki/Sri_Lankan_Tamil_people"}, {"title": "Jaffna", "link": "https://wikipedia.org/wiki/Jaffna"}]}, {"year": "2002", "text": "Civil rights movement: A jury in Birmingham, Alabama, convicts former Ku Klux Klan member <PERSON> of the 1963 murder of four girls in the 16th Street Baptist Church bombing.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">Civil rights movement</a>: A jury in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, convicts former <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/1963\" title=\"1963\">1963</a> murder of four girls in the <a href=\"https://wikipedia.org/wiki/16th_Street_Baptist_Church_bombing\" title=\"16th Street Baptist Church bombing\">16th Street Baptist Church bombing</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">Civil rights movement</a>: A jury in <a href=\"https://wikipedia.org/wiki/Birmingham,_Alabama\" title=\"Birmingham, Alabama\">Birmingham, Alabama</a>, convicts former <a href=\"https://wikipedia.org/wiki/Ku_Klux_Klan\" title=\"Ku Klux Klan\">Ku Klux Klan</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/1963\" title=\"1963\">1963</a> murder of four girls in the <a href=\"https://wikipedia.org/wiki/16th_Street_Baptist_Church_bombing\" title=\"16th Street Baptist Church bombing\">16th Street Baptist Church bombing</a>.", "links": [{"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}, {"title": "Birmingham, Alabama", "link": "https://wikipedia.org/wiki/Birmingham,_Alabama"}, {"title": "Ku Klux Klan", "link": "https://wikipedia.org/wiki/Ku_Klux_Klan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1963", "link": "https://wikipedia.org/wiki/1963"}, {"title": "16th Street Baptist Church bombing", "link": "https://wikipedia.org/wiki/16th_Street_Baptist_Church_bombing"}]}, {"year": "2010", "text": "Air India Express Flight 812, a Boeing 737 crashes over a cliff upon landing at Mangalore, India, killing 158 of 166 people on board, becoming the deadliest crash involving a Boeing 737 until the crash of Lion Air Flight 610.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Air_India_Express_Flight_812\" title=\"Air India Express Flight 812\">Air India Express Flight 812</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> crashes over a cliff upon landing at <a href=\"https://wikipedia.org/wiki/Mangalore\" title=\"Mangalore\">Mangalore</a>, India, killing 158 of 166 people on board, becoming the deadliest crash involving a Boeing 737 until the crash of <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">Lion Air Flight 610</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_India_Express_Flight_812\" title=\"Air India Express Flight 812\">Air India Express Flight 812</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737</a> crashes over a cliff upon landing at <a href=\"https://wikipedia.org/wiki/Mangalore\" title=\"Mangalore\">Mangalore</a>, India, killing 158 of 166 people on board, becoming the deadliest crash involving a Boeing 737 until the crash of <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">Lion Air Flight 610</a>.", "links": [{"title": "Air India Express Flight 812", "link": "https://wikipedia.org/wiki/Air_India_Express_Flight_812"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "Mangalore", "link": "https://wikipedia.org/wiki/Mangalore"}, {"title": "Lion Air Flight 610", "link": "https://wikipedia.org/wiki/Lion_Air_Flight_610"}]}, {"year": "2010", "text": "Inter Milan beat Bayern Munich 2-0 in the UEFA Champions League final in Madrid, Spain to become the first, and so far only, Italian team to win the historic treble (Serie A, Coppa Italia, Champions League).", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Inter_Milan\" title=\"Inter Milan\">Inter Milan</a> beat <a href=\"https://wikipedia.org/wiki/Bayern_Munich\" class=\"mw-redirect\" title=\"Bayern Munich\">Bayern Munich</a> 2-0 in the <a href=\"https://wikipedia.org/wiki/UEFA_Champions_League\" title=\"UEFA Champions League\">UEFA Champions League</a> final in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> to become the first, and so far only, Italian team to win the historic treble (Serie A, Coppa Italia, Champions League).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inter_Milan\" title=\"Inter Milan\">Inter Milan</a> beat <a href=\"https://wikipedia.org/wiki/Bayern_Munich\" class=\"mw-redirect\" title=\"Bayern Munich\">Bayern Munich</a> 2-0 in the <a href=\"https://wikipedia.org/wiki/UEFA_Champions_League\" title=\"UEFA Champions League\">UEFA Champions League</a> final in <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a>, <a href=\"https://wikipedia.org/wiki/Spain\" title=\"Spain\">Spain</a> to become the first, and so far only, Italian team to win the historic treble (Serie A, Coppa Italia, Champions League).", "links": [{"title": "Inter Milan", "link": "https://wikipedia.org/wiki/Inter_Milan"}, {"title": "Bayern Munich", "link": "https://wikipedia.org/wiki/Bayern_Munich"}, {"title": "UEFA Champions League", "link": "https://wikipedia.org/wiki/UEFA_Champions_League"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Spain", "link": "https://wikipedia.org/wiki/Spain"}]}, {"year": "2011", "text": "An EF5 tornado strikes Joplin, Missouri, killing 158 people and wreaking $2.8 billion in damages, the costliest and seventh-deadliest single tornado in U.S. history.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/2011_Jo<PERSON><PERSON>_tornado\" title=\"2011 Joplin tornado\">An EF5 tornado</a> strikes <a href=\"https://wikipedia.org/wiki/Joplin,_Missouri\" title=\"Joplin, Missouri\">Joplin, Missouri</a>, killing 158 people and wreaking $2.8 billion in damages, the costliest and seventh-deadliest single tornado in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2011_Jo<PERSON><PERSON>_tornado\" title=\"2011 Joplin tornado\">An EF5 tornado</a> strikes <a href=\"https://wikipedia.org/wiki/Jop<PERSON>,_Missouri\" title=\"Joplin, Missouri\">Joplin, Missouri</a>, killing 158 people and wreaking $2.8 billion in damages, the costliest and seventh-deadliest single tornado in U.S. history.", "links": [{"title": "2011 Joplin tornado", "link": "https://wikipedia.org/wiki/2011_<PERSON><PERSON><PERSON>_tornado"}, {"title": "Joplin, Missouri", "link": "https://wikipedia.org/wiki/Joplin,_Missouri"}]}, {"year": "2012", "text": "Tokyo Skytree opens to the public. It is the tallest tower in the world (634 m), and the second tallest man-made structure on Earth after <PERSON><PERSON><PERSON> (829.8 m).", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Tokyo_Skytree\" title=\"Tokyo Skytree\">Tokyo Skytree</a> opens to the public. It is the <a href=\"https://wikipedia.org/wiki/List_of_tallest_towers\" title=\"List of tallest towers\">tallest tower in the world</a> (634 m), and the second tallest man-made structure on Earth after <a href=\"https://wikipedia.org/wiki/Burj_<PERSON>fa\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (829.8 m).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokyo_Skytree\" title=\"Tokyo Skytree\">Tokyo Skytree</a> opens to the public. It is the <a href=\"https://wikipedia.org/wiki/List_of_tallest_towers\" title=\"List of tallest towers\">tallest tower in the world</a> (634 m), and the second tallest man-made structure on Earth after <a href=\"https://wikipedia.org/wiki/Burj_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (829.8 m).", "links": [{"title": "Tokyo Skytree", "link": "https://wikipedia.org/wiki/Tokyo_Skytree"}, {"title": "List of tallest towers", "link": "https://wikipedia.org/wiki/List_of_tallest_towers"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "SpaceX COTS Demo Flight 2 launches a Dragon capsule on a Falcon 9 rocket in the first commercial flight to the International Space Station.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/SpaceX_COTS_Demo_Flight_2\" title=\"SpaceX COTS Demo Flight 2\">SpaceX COTS Demo Flight 2</a> launches a <a href=\"https://wikipedia.org/wiki/Dragon_capsule\" class=\"mw-redirect\" title=\"Dragon capsule\">Dragon capsule</a> on a <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a> rocket in the first commercial flight to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/SpaceX_COTS_Demo_Flight_2\" title=\"SpaceX COTS Demo Flight 2\">SpaceX COTS Demo Flight 2</a> launches a <a href=\"https://wikipedia.org/wiki/Dragon_capsule\" class=\"mw-redirect\" title=\"Dragon capsule\">Dragon capsule</a> on a <a href=\"https://wikipedia.org/wiki/Falcon_9\" title=\"Falcon 9\">Falcon 9</a> rocket in the first commercial flight to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "SpaceX COTS Demo Flight 2", "link": "https://wikipedia.org/wiki/SpaceX_COTS_Demo_Flight_2"}, {"title": "Dragon capsule", "link": "https://wikipedia.org/wiki/Dragon_capsule"}, {"title": "Falcon 9", "link": "https://wikipedia.org/wiki/Falcon_9"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2013", "text": "Fusilier <PERSON> is murdered by 2 Islamic extremists in Woolwich, Southeast London", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Fusilier\" title=\"Fusilier\">Fusilier</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is murdered by 2 <a href=\"https://wikipedia.org/wiki/Islamic_extremism\" title=\"Islamic extremism\">Islamic extremists</a> in <a href=\"https://wikipedia.org/wiki/Woolwich\" title=\"Woolwich\">Woolwich</a>, <a href=\"https://wikipedia.org/wiki/South_London\" title=\"South London\">Southeast London</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fusilier\" title=\"Fusilier\">Fusilier</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_Rig<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> is murdered by 2 <a href=\"https://wikipedia.org/wiki/Islamic_extremism\" title=\"Islamic extremism\">Islamic extremists</a> in <a href=\"https://wikipedia.org/wiki/Woolwich\" title=\"Woolwich\">Woolwich</a>, <a href=\"https://wikipedia.org/wiki/South_London\" title=\"South London\">Southeast London</a>", "links": [{"title": "Fusilier", "link": "https://wikipedia.org/wiki/Fusilier"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Islamic extremism", "link": "https://wikipedia.org/wiki/Islamic_extremism"}, {"title": "Woolwich", "link": "https://wikipedia.org/wiki/Woolwich"}, {"title": "South London", "link": "https://wikipedia.org/wiki/South_London"}]}, {"year": "2014", "text": "General <PERSON><PERSON><PERSON> becomes interim leader of Thailand in a military coup d'état, following six months of political turmoil.", "html": "2014 - General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>-cha\" title=\"<PERSON><PERSON><PERSON>-<PERSON>-cha\"><PERSON><PERSON><PERSON>-<PERSON>a</a> becomes interim leader of Thailand in a military <a href=\"https://wikipedia.org/wiki/2014_Thai_coup_d%27%C3%A9tat\" title=\"2014 Thai coup d'état\">coup d'état</a>, following six months of political turmoil.", "no_year_html": "General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-o-cha\" title=\"<PERSON><PERSON><PERSON>-<PERSON>-cha\"><PERSON><PERSON><PERSON>-<PERSON>-cha</a> becomes interim leader of Thailand in a military <a href=\"https://wikipedia.org/wiki/2014_Thai_coup_d%27%C3%A9tat\" title=\"2014 Thai coup d'état\">coup d'état</a>, following six months of political turmoil.", "links": [{"title": "<PERSON><PERSON><PERSON>a", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-o-cha"}, {"title": "2014 Thai coup d'état", "link": "https://wikipedia.org/wiki/2014_Thai_coup_d%27%C3%A9tat"}]}, {"year": "2014", "text": "An explosion occurs in Ürümqi, capital of China's far-western Xinjiang region, resulting in at least 43 deaths and 91 injuries.", "html": "2014 - An <a href=\"https://wikipedia.org/wiki/May_2014_%C3%9Cr%C3%BCmqi_attack\" title=\"May 2014 Ürümqi attack\">explosion</a> occurs in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\">Ürümqi</a>, capital of China's far-western <a href=\"https://wikipedia.org/wiki/Xinjiang_Uyghur_Autonomous_Region\" class=\"mw-redirect\" title=\"Xinjiang Uyghur Autonomous Region\">Xinjiang</a> region, resulting in at least 43 deaths and 91 injuries.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/May_2014_%C3%9Cr%C3%BCmqi_attack\" title=\"May 2014 Ürümqi attack\">explosion</a> occurs in <a href=\"https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi\" title=\"Ürümqi\">Ürümqi</a>, capital of China's far-western <a href=\"https://wikipedia.org/wiki/Xinjiang_Uyghur_Autonomous_Region\" class=\"mw-redirect\" title=\"Xinjiang Uyghur Autonomous Region\">Xinjiang</a> region, resulting in at least 43 deaths and 91 injuries.", "links": [{"title": "May 2014 Ürümqi attack", "link": "https://wikipedia.org/wiki/May_2014_%C3%9Cr%C3%BCmqi_attack"}, {"title": "Ürümqi", "link": "https://wikipedia.org/wiki/%C3%9Cr%C3%BCmqi"}, {"title": "Xinjiang Uyghur Autonomous Region", "link": "https://wikipedia.org/wiki/Xinjiang_Uyghur_Autonomous_Region"}]}, {"year": "2015", "text": "The Republic of Ireland becomes the first nation in the world to utilise a public referendum to legalise gay marriage.", "html": "2015 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a> becomes the first nation in the world to utilise a public referendum to <a href=\"https://wikipedia.org/wiki/2015_Irish_constitutional_referendums\" title=\"2015 Irish constitutional referendums\">legalise gay marriage</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a> becomes the first nation in the world to utilise a public referendum to <a href=\"https://wikipedia.org/wiki/2015_Irish_constitutional_referendums\" title=\"2015 Irish constitutional referendums\">legalise gay marriage</a>.", "links": [{"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "2015 Irish constitutional referendums", "link": "https://wikipedia.org/wiki/2015_Irish_constitutional_referendums"}]}, {"year": "2017", "text": "Twenty-two people are killed at an Ariana Grande concert in the 2017 Manchester Arena bombing.", "html": "2017 - Twenty-two people are killed at an <a href=\"https://wikipedia.org/wiki/Ariana_Grande\" title=\"Ariana Grande\">Ariana Grande</a> concert in the <a href=\"https://wikipedia.org/wiki/Manchester_Arena_bombing\" title=\"Manchester Arena bombing\">2017 Manchester Arena bombing</a>.", "no_year_html": "Twenty-two people are killed at an <a href=\"https://wikipedia.org/wiki/Ariana_Grande\" title=\"Ariana Grande\">Ariana Grande</a> concert in the <a href=\"https://wikipedia.org/wiki/Manchester_Arena_bombing\" title=\"Manchester Arena bombing\">2017 Manchester Arena bombing</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Manchester Arena bombing", "link": "https://wikipedia.org/wiki/Manchester_Arena_bombing"}]}, {"year": "2017", "text": "United States President <PERSON> visits the Church of the Holy Sepulchre in Jerusalem and becomes the first sitting U.S. president to visit the Western Wall.", "html": "2017 - United States <a href=\"https://wikipedia.org/wiki/President_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> visits the <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> and becomes the first sitting U.S. president to visit the <a href=\"https://wikipedia.org/wiki/Western_Wall\" title=\"Western Wall\">Western Wall</a>.", "no_year_html": "United States <a href=\"https://wikipedia.org/wiki/President_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"President <PERSON>\">President <PERSON></a> visits the <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> and becomes the first sitting U.S. president to visit the <a href=\"https://wikipedia.org/wiki/Western_Wall\" title=\"Western Wall\">Western Wall</a>.", "links": [{"title": "President <PERSON>", "link": "https://wikipedia.org/wiki/President_<PERSON>_<PERSON>"}, {"title": "Church of the Holy Sepulchre", "link": "https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Western Wall", "link": "https://wikipedia.org/wiki/Western_Wall"}]}, {"year": "2020", "text": "Pakistan International Airlines Flight 8303 crashes in Model Colony near Jinnah International Airport in Karachi, Pakistan, killing 98 people.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_8303\" title=\"Pakistan International Airlines Flight 8303\">Pakistan International Airlines Flight 8303</a> crashes in <a href=\"https://wikipedia.org/wiki/Model_Colony,_Karachi\" title=\"Model Colony, Karachi\">Model Colony</a> near <a href=\"https://wikipedia.org/wiki/Jinnah_International_Airport\" title=\"Jinnah International Airport\">Jinnah International Airport</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing 98 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_8303\" title=\"Pakistan International Airlines Flight 8303\">Pakistan International Airlines Flight 8303</a> crashes in <a href=\"https://wikipedia.org/wiki/Model_Colony,_Karachi\" title=\"Model Colony, Karachi\">Model Colony</a> near <a href=\"https://wikipedia.org/wiki/Jinnah_International_Airport\" title=\"Jinnah International Airport\">Jinnah International Airport</a> in <a href=\"https://wikipedia.org/wiki/Karachi\" title=\"Karachi\">Karachi</a>, <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>, killing 98 people.", "links": [{"title": "Pakistan International Airlines Flight 8303", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_8303"}, {"title": "Model Colony, Karachi", "link": "https://wikipedia.org/wiki/Model_Colony,_Karachi"}, {"title": "Jinnah International Airport", "link": "https://wikipedia.org/wiki/Jinnah_International_Airport"}, {"title": "Karachi", "link": "https://wikipedia.org/wiki/Karachi"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}]}, {"year": "2021", "text": "Hypothermia kills 21 runners in the 100 km (60-mile) Gansu ultramarathon disaster in China.", "html": "2021 - Hypothermia kills 21 runners in the 100 km (60-mile) <a href=\"https://wikipedia.org/wiki/Gansu_ultramarathon_disaster\" title=\"Gansu ultramarathon disaster\">Gansu ultramarathon disaster</a> in China.", "no_year_html": "Hypothermia kills 21 runners in the 100 km (60-mile) <a href=\"https://wikipedia.org/wiki/Gansu_ultramarathon_disaster\" title=\"Gansu ultramarathon disaster\">Gansu ultramarathon disaster</a> in China.", "links": [{"title": "Gansu ultramarathon disaster", "link": "https://wikipedia.org/wiki/Gansu_ultramarathon_disaster"}]}], "Births": [{"year": "626", "text": "<PERSON><PERSON>, Mayan king (d. 686)", "html": "626 - <a href=\"https://wikipedia.org/wiki/Itzam_K%27an_Ahk_I\" class=\"mw-redirect\" title=\"Itzam K'an Ahk I\"><PERSON><PERSON>an <PERSON></a>, Mayan king (d. 686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Itzam_K%27an_Ahk_I\" class=\"mw-redirect\" title=\"Itzam K'an Ahk I\"><PERSON><PERSON>an Ah<PERSON></a>, Mayan king (d. 686)", "links": [{"title": "<PERSON>zam K'an Ahk I", "link": "https://wikipedia.org/wiki/Itzam_K%27an_<PERSON>k_I"}]}, {"year": "1009", "text": "<PERSON>, Chinese writer (d. 1066)", "html": "1009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer (d. 1066)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese writer (d. 1066)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1408", "text": "<PERSON><PERSON><PERSON>, Hindu saint (d. 1503)", "html": "1408 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>charya\"><PERSON><PERSON><PERSON></a>, Hindu saint (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON>charya\"><PERSON><PERSON><PERSON></a>, Hindu saint (d. 1503)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1539", "text": "<PERSON>, 1st Earl of Hertford (d. 1621)", "html": "1539 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford\" title=\"<PERSON>, 1st Earl of Hertford\"><PERSON>, 1st Earl of Hertford</a> (d. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford\" title=\"<PERSON>, 1st Earl of Hertford\"><PERSON>, 1st Earl of Hertford</a> (d. 1621)", "links": [{"title": "<PERSON>, 1st Earl of Hertford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Hertford"}]}, {"year": "1622", "text": "<PERSON>, French soldier and governor (d. 1698)", "html": "1622 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_de_Frontenac\" title=\"<PERSON> de Frontenac\"><PERSON></a>, French soldier and governor (d. 1698)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Frontenac\" title=\"<PERSON> Frontenac\"><PERSON></a>, French soldier and governor (d. 1698)", "links": [{"title": "<PERSON> Front<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_Frontena<PERSON>"}]}, {"year": "1644", "text": "<PERSON><PERSON><PERSON><PERSON>, Flemish Baroque sculptor (d. 1730)", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Gabri%C3%ABl_Grupello\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Flemish Baroque sculptor (d. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gabri%C3%ABl_Grupello\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Flemish Baroque sculptor (d. 1730)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabri%C3%ABl_Grupello"}]}, {"year": "1650", "text": "<PERSON>, Dutch Golden Age painter (d. 1702)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Golden Age painter (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch Golden Age painter (d. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1665", "text": "<PERSON>, Swedish field marshal and Royal Councillor (d. 1717)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal and Royal Councillor (d. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish field marshal and Royal Councillor (d. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1694", "text": "<PERSON>, Austrian painter (d. 1757)", "html": "1694 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1757)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON><PERSON><PERSON>, French cardinal and diplomat (d. 1794)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal and diplomat (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cardinal and diplomat (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, French painter (d. 1808)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, French butcher and politician (d. 1797)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French butcher and politician (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French butcher and politician (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1762", "text": "<PERSON>, 3rd Earl <PERSON>, English politician (d. 1834)", "html": "1762 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_<PERSON>\" title=\"<PERSON>, 3rd Earl <PERSON>\"><PERSON>, 3rd Earl <PERSON></a>, English politician (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_<PERSON>_<PERSON>\" title=\"<PERSON>, 3rd Earl <PERSON>\"><PERSON>, 3rd Earl <PERSON></a>, English politician (d. 1834)", "links": [{"title": "<PERSON>, 3rd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "Princess <PERSON> of the United Kingdom (d. 1840)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/Princess_Elizabeth_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_Elizabeth_of_the_United_Kingdom\" title=\"Princess <PERSON> of the United Kingdom\">Princess <PERSON> of the United Kingdom</a> (d. 1840)", "links": [{"title": "Princess <PERSON> of the United Kingdom", "link": "https://wikipedia.org/wiki/Princess_Elizabeth_of_the_United_Kingdom"}]}, {"year": "1772", "text": "<PERSON>, Indian philosopher and reformer (d. 1833)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and reformer (d. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Indian philosopher and reformer (d. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, Austrian painter (d. 1853)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>h%C3%B6<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter (d. 1853)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>omuk_Sch%C3%B6dlberger"}]}, {"year": "1782", "text": "<PERSON><PERSON>, Japanese neo-Confucian scholar, teacher, writer (d. 1856)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/Hirose_Tans%C5%8D\" title=\"Hirose Tansō\"><PERSON><PERSON></a>, Japanese neo-Confucian scholar, teacher, writer (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hirose_Tans%C5%8D\" title=\"Hirose Tansō\"><PERSON><PERSON></a>, Japanese neo-Confucian scholar, teacher, writer (d. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hirose_Tans%C5%8D"}]}, {"year": "1783", "text": "<PERSON>, English physicist and inventor, invented the electromagnet and electric motor (d. 1850)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and inventor, invented the <a href=\"https://wikipedia.org/wiki/Electromagnet\" title=\"Electromagnet\">electromagnet</a> and <a href=\"https://wikipedia.org/wiki/Electric_motor\" title=\"Electric motor\">electric motor</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and inventor, invented the <a href=\"https://wikipedia.org/wiki/Electromagnet\" title=\"Electromagnet\">electromagnet</a> and <a href=\"https://wikipedia.org/wiki/Electric_motor\" title=\"Electric motor\">electric motor</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Electromagnet", "link": "https://wikipedia.org/wiki/Electromagnet"}, {"title": "Electric motor", "link": "https://wikipedia.org/wiki/Electric_motor"}]}, {"year": "1808", "text": "<PERSON><PERSON>, French poet and translator (d. 1855)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>_<PERSON>val\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and translator (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rar<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French poet and translator (d. 1855)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_de_<PERSON>val"}]}, {"year": "1811", "text": "<PERSON><PERSON><PERSON>, Italian soprano (d. 1869)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Giulia_Grisi\" title=\"Giulia Grisi\"><PERSON><PERSON><PERSON></a>, Italian soprano (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Giulia_Grisi\" title=\"Giulia Grisi\"><PERSON><PERSON><PERSON></a>, Italian soprano (d. 1869)", "links": [{"title": "Giulia G<PERSON>i", "link": "https://wikipedia.org/wiki/Giulia_Grisi"}]}, {"year": "1811", "text": "<PERSON><PERSON>, 5th Duke of Newcastle, English politician (d. 1864)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Duke_of_Newcastle\" title=\"<PERSON>, 5th Duke of Newcastle\"><PERSON>, 5th Duke of Newcastle</a>, English politician (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Duke_of_Newcastle\" title=\"<PERSON>, 5th Duke of Newcastle\"><PERSON>, 5th Duke of Newcastle</a>, English politician (d. 1864)", "links": [{"title": "<PERSON>, 5th Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>,_5th_Duke_of_Newcastle"}]}, {"year": "1813", "text": "<PERSON>, German composer (d. 1883)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON>, Swedish painter (d. 1891)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish painter (d. 1891)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON>, American painter (d. 1910)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Worthington_Whittredge\" title=\"Worthington Whittredge\">Worthington W<PERSON><PERSON></a>, American painter (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Worthington_Whittredge\" title=\"Worthington Whittredge\">Worth<PERSON><PERSON></a>, American painter (d. 1910)", "links": [{"title": "Worthington Whittredge", "link": "https://wikipedia.org/wiki/Worthington_Whittredge"}]}, {"year": "1828", "text": "<PERSON><PERSON>, German ophthalmologist and academic (d. 1870)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(ophthalmologist)\" title=\"<PERSON><PERSON> (ophthalmologist)\"><PERSON><PERSON></a>, German ophthalmologist and academic (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(ophthalmologist)\" title=\"<PERSON><PERSON> (ophthalmologist)\"><PERSON><PERSON></a>, German ophthalmologist and academic (d. 1870)", "links": [{"title": "<PERSON><PERSON> (ophthalmologist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_(ophthalmologist)"}]}, {"year": "1831", "text": "<PERSON>, English anatomist and surgeon (d. 1897)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anatomist and surgeon (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English anatomist and surgeon (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1833", "text": "<PERSON>, French painter and etcher (d. 1914)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_B<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_B<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and etcher (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Bracquemond"}]}, {"year": "1833", "text": "<PERSON>, Spanish politician, Prime Minister of Spain (d. 1895)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1841", "text": "<PERSON><PERSON><PERSON>, French poet, author, and playwright (d. 1909)", "html": "1841 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>e_Mend%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet, author, and playwright (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mend%C3%A8s\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French poet, author, and playwright (d. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Catulle_Mend%C3%A8s"}]}, {"year": "1844", "text": "<PERSON>, American painter and educator (d. 1926)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, Mexican poet, educator, and activist (d. 1908)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Mexican poet, educator, and activist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C3%A9rrez\" title=\"<PERSON>\"><PERSON></a>, Mexican poet, educator, and activist (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%C3%A9rrez"}]}, {"year": "1848", "text": "<PERSON>, German painter and educator (d. 1911)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and educator (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and educator (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, English architect and academic (d. 1930)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Aston_Webb\" title=\"Aston Webb\"><PERSON></a>, English architect and academic (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aston_Webb\" title=\"Aston Webb\"><PERSON></a>, English architect and academic (d. 1930)", "links": [{"title": "Aston Webb", "link": "https://wikipedia.org/wiki/Aston_Webb"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, Brazilian painter, illustrator, sculptor (d. 1935)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Bel<PERSON><PERSON>_de_Almeida\" title=\"<PERSON><PERSON><PERSON> de Almeida\"><PERSON><PERSON><PERSON></a>, Brazilian painter, illustrator, sculptor (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Almeida\" title=\"<PERSON><PERSON><PERSON> de Almeida\"><PERSON><PERSON><PERSON></a>, Brazilian painter, illustrator, sculptor (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Belmiro_de_Almeida"}]}, {"year": "1859", "text": "<PERSON>, British writer (d. 1930)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British writer (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese author, playwright, and educator (d. 1935)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Tsubouchi_Sh%C5%8Dy%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author, playwright, and educator (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsubouchi_Sh%C5%8Dy%C5%8D\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese author, playwright, and educator (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsubouchi_Sh%C5%8Dy%C5%8D"}]}, {"year": "1864", "text": "<PERSON>, German author and illustrator (d. 1931)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6wer\" title=\"<PERSON>\"><PERSON></a>, German author and illustrator (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6wer\" title=\"<PERSON>\"><PERSON></a>, German author and illustrator (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Willy_St%C3%B6wer"}]}, {"year": "1868", "text": "<PERSON><PERSON>, Brazilian engineer and politician (d. 1934)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Brazilian engineer and politician (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(politician)\" title=\"<PERSON><PERSON> (politician)\"><PERSON><PERSON></a>, Brazilian engineer and politician (d. 1934)", "links": [{"title": "<PERSON><PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(politician)"}]}, {"year": "1874", "text": "<PERSON>, South African clergyman and politician, 5th Prime Minister of South Africa (d. 1959)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African clergyman and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African clergyman and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_South_Africa\" title=\"Prime Minister of South Africa\">Prime Minister of South Africa</a> (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A7<PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of South Africa", "link": "https://wikipedia.org/wiki/Prime_Minister_of_South_Africa"}]}, {"year": "1876", "text": "<PERSON>, Austrian painter and illustrator (d. 1942)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and illustrator (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and illustrator (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Australian cricketer and journalist (d. 1947)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and journalist (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, French admiral and composer (d. 1932)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral and composer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French admiral and composer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Ukrainian statesman and independence leader (d. 1926)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian statesman and independence leader (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian statesman and independence leader (d. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, French author and translator (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and translator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and translator (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Scottish suffragist and feminist (d. 1957)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish suffragist and feminist (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish suffragist and feminist (d. 1957)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Italian lawyer and politician (d. 1924)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian lawyer and politician (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Japanese admiral (d. 1957)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese admiral (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>oda"}]}, {"year": "1887", "text": "<PERSON><PERSON> <PERSON><PERSON>, Danish film director and screenwriter (d. 1938)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Danish film director and screenwriter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Danish film director and screenwriter (d. 1938)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German politician, novelist, and poet (d. 1958)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, novelist, and poet (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, novelist, and poet (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, German sociologist and philosopher (d. 1970)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sociologist and philosopher (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German and English-speaking author (d. 1975)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, German and English-speaking author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, German and English-speaking author (d. 1975)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>(writer)"}]}, {"year": "1900", "text": "<PERSON>, Mexican lyric opera tenor and bolero vocalist (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lyric opera tenor and bolero vocalist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican lyric opera tenor and bolero vocalist (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American politician, 6th United States Secretary of Labor (d. 1953)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Labor\" title=\"United States Secretary of Labor\">United States Secretary of Labor</a> (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Labor", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Labor"}]}, {"year": "1902", "text": "<PERSON>, English footballer and manager (d. 1940) ", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1902)\" title=\"<PERSON> (footballer, born 1902)\"><PERSON></a>, English footballer and manager (d. 1940) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1902)\" title=\"<PERSON> (footballer, born 1902)\"><PERSON></a>, English footballer and manager (d. 1940) ", "links": [{"title": "<PERSON> (footballer, born 1902)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1902)"}]}, {"year": "1902", "text": "<PERSON>, American baseball player and coach (d. 1956)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Swedish electrical engineer and inventor (d. 1989)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Uno_Lamm\" title=\"Uno Lamm\"><PERSON><PERSON></a>, Swedish electrical engineer and inventor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uno_Lamm\" title=\"Uno Lamm\"><PERSON><PERSON></a>, Swedish electrical engineer and inventor (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uno_Lamm"}]}, {"year": "1905", "text": "<PERSON><PERSON>, German physicist and academic, co-invented the electron microscope (d. 1956)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, co-invented the <a href=\"https://wikipedia.org/wiki/Electron_microscope\" title=\"Electron microscope\">electron microscope</a> (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and academic, co-invented the <a href=\"https://wikipedia.org/wiki/Electron_microscope\" title=\"Electron microscope\">electron microscope</a> (d. 1956)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Electron microscope", "link": "https://wikipedia.org/wiki/Electron_microscope"}]}, {"year": "1905", "text": "<PERSON>, British politician (d. 1976)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Belgian author and illustrator (d. 1983)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Herg%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian author and illustrator (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Herg%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian author and illustrator (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Herg%C3%A9"}]}, {"year": "1907", "text": "<PERSON>, English actor, director, and producer (d. 1989)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and producer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American golfer and captain (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and captain (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and captain (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American-Australian radio and television host (d. 1984)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian radio and television host (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian radio and television host (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English illustrator and educator (d. 1988)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and educator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator and educator (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English-American chemist and academic, Nobel Prize laureate (d. 2004)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1913", "text": "<PERSON>, Spanish director and screenwriter (d. 1986)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish director and screenwriter (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, Belgian author (d. 2012)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Dutch historian and diplomat (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and diplomat (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch historian and diplomat (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American pianist, composer, bandleader, poet (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Sun_Ra\" title=\"Sun Ra\"><PERSON></a>, American pianist, composer, bandleader, poet (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sun_Ra\" title=\"Sun Ra\"><PERSON></a>, American pianist, composer, bandleader, poet (d. 1993)", "links": [{"title": "Sun Ra", "link": "https://wikipedia.org/wiki/Sun_Ra"}]}, {"year": "1917", "text": "<PERSON>, American businessman and philanthropist (d. 2013)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, French author (d. 1995)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Belgian businessman and politician, 55th Prime Minister of Belgium (d. 2001)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman and politician, 55th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Boeynant<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "1920", "text": "<PERSON>, Austrian-American astrophysicist and academic (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Gold\"><PERSON></a>, Austrian-American astrophysicist and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Thomas Gold\"><PERSON></a>, Austrian-American astrophysicist and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American scientist (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American screenwriter and producer (d. 1987)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, French-Armenian singer-songwriter and actor (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian singer-songwriter and actor (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Armenian singer-songwriter and actor (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Swiss painter and sculptor (d. 1991)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss painter and sculptor (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American novelist, short story writer, editor, co-founded The Paris Review (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, editor, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Paris_Review\" title=\"The Paris Review\">The Paris Review</a></i> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, editor, co-founded <i><a href=\"https://wikipedia.org/wiki/The_Paris_Review\" title=\"The Paris Review\">The Paris Review</a></i> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "The Paris Review", "link": "https://wikipedia.org/wiki/The_Paris_Review"}]}, {"year": "1927", "text": "<PERSON>, Hungarian-American chemist and academic, Nobel Prize laureate (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1928", "text": "<PERSON>, French theorist and author (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French theorist and author (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Scottish director and producer (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Scottish director and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)\" title=\"<PERSON> (film director)\"><PERSON></a>, Scottish director and producer (d. 2011)", "links": [{"title": "<PERSON> (film director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(film_director)"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American businessman (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American businessman (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese novelist (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese novelist (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1929", "text": "<PERSON>, Egyptian poet (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian poet (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian poet (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, English jazz trumpet player, vocalist, and bandleader (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ball\"><PERSON></a>, English jazz trumpet player, vocalist, and bandleader (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ball\"><PERSON></a>, English jazz trumpet player, vocalist, and bandleader (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, French-American sculptor (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Escobar\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-American sculptor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Escobar\" title=\"<PERSON><PERSON> Escobar\"><PERSON><PERSON></a>, French-American sculptor (d. 2016)", "links": [{"title": "Marisol Escobar", "link": "https://wikipedia.org/wiki/Marisol_Escobar"}]}, {"year": "1930", "text": "<PERSON>, American lieutenant and politician (d. 1978)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>, American lieutenant and politician (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Harvey Milk\"><PERSON></a>, American lieutenant and politician (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American psychiatrist and academic (d. 2015)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychiatrist)\" title=\"<PERSON> (psychiatrist)\"><PERSON></a>, American psychiatrist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychiatrist)\" title=\"<PERSON> (psychiatrist)\"><PERSON></a>, American psychiatrist and academic (d. 2015)", "links": [{"title": "<PERSON> (psychiatrist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(psychiatrist)"}]}, {"year": "1933", "text": "<PERSON>, Australian-South African rugby league player (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian-South African rugby league player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian-South African rugby league player (d. 2012)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1933", "text": "<PERSON>, Chinese mathematician and academic (d. 1996)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese mathematician and academic (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese mathematician and academic (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American pianist and conductor (d. 2023)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and conductor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian rugby league player (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American engineer (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Argentinian singer-songwriter (d. 2011)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Facundo_Cabral\" title=\"Facundo Cabral\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian singer-songwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Facundo_Cabral\" title=\"Facundo Cabral\"><PERSON><PERSON><PERSON><PERSON></a>, Argentinian singer-songwriter (d. 2011)", "links": [{"title": "Facundo Cabral", "link": "https://wikipedia.org/wiki/Facundo_Cabral"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Slovak writer (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak writer (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actor and director", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American actress (d. 1999)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actor (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American filmmaker", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American filmmaker", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian cricketer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/E._A._S._Pras<PERSON>\" title=\"E. A. S. Prasanna\">E. A<PERSON> S<PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._A._S._<PERSON><PERSON><PERSON>\" title=\"E. A. S. P<PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "E. A. S. Prasanna", "link": "https://wikipedia.org/wiki/E._<PERSON>._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian actor (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American journalist (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)\" title=\"<PERSON> (journalist)\"><PERSON></a>, American journalist (d. 2022)", "links": [{"title": "<PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(journalist)"}]}, {"year": "1940", "text": "<PERSON>, American football player (d. 2021)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Scottish sprinter and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish sprinter and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish sprinter and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American basketball player (d. 1997)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)\" title=\"<PERSON> (basketball, born 1942)\"><PERSON></a>, American basketball player (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)\" title=\"<PERSON> (basketball, born 1942)\"><PERSON></a>, American basketball player (d. 1997)", "links": [{"title": "<PERSON> (basketball, born 1942)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1942)"}]}, {"year": "1942", "text": "<PERSON>, American academic and mathematician turned anarchist and serial murderer (Unabomber) (d. 2023)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and mathematician turned anarchist and serial murderer (<a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a>) (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and mathematician turned anarchist and serial murderer (<a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a>) (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Unabomber", "link": "https://wikipedia.org/wiki/Unabomber"}]}, {"year": "1942", "text": "<PERSON>, Canadian actress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Native American civil rights activist (d. 1972)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Native American civil rights activist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)\" title=\"<PERSON> (activist)\"><PERSON></a>, Native American civil rights activist (d. 1972)", "links": [{"title": "<PERSON> (activist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(activist)"}]}, {"year": "1943", "text": "<PERSON>, Northern Irish peace activist, Nobel Prize laureate (d. 2020)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nobel_laureate)\" class=\"mw-redirect\" title=\"<PERSON> (Nobel laureate)\"><PERSON></a>, Northern Irish peace activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nobel_laureate)\" class=\"mw-redirect\" title=\"<PERSON> (Nobel laureate)\"><PERSON></a>, Northern Irish peace activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON> (Nobel laureate)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Nobel_laureate)"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1943", "text": "<PERSON>, American baseball player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian fantasy author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian fantasy author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian fantasy author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1945", "text": "<PERSON>, Australian politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Northern Irish footballer and manager (d. 2005)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George Best\"><PERSON></a>, Northern Irish footballer and manager (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Best\" title=\"George Best\"><PERSON></a>, Northern Irish footballer and manager (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English physicist and academic", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(physicist)\" title=\"<PERSON> (physicist)\"><PERSON></a>, English physicist and academic", "links": [{"title": "<PERSON> (physicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(physicist)"}]}, {"year": "1946", "text": "<PERSON>, English footballer and manager (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Romanian philosopher, political scientist, politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian philosopher, political scientist, politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian philosopher, political scientist, politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian-Ukrainian astronomer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian-Ukrainian astronomer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian-Ukrainian astronomer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, Cuban painter and engraver", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban painter and engraver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_S%C3%A1nchez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban painter and engraver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_S%C3%A1nchez"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Indian actor and screenwriter (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Nedumudi_Venu\" title=\"Nedumudi Venu\"><PERSON><PERSON><PERSON></a>, Indian actor and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nedumudi_Venu\" title=\"Nedumudi Venu\"><PERSON><PERSON><PERSON></a>, Indian actor and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nedumudi_Venu"}]}, {"year": "1949", "text": "<PERSON>, English actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, Austrian diplomat", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian diplomat", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian diplomat", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English singer-songwriter and poet", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>pin\"><PERSON></a>, English singer-songwriter and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American serial killer and rapist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and rapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer and rapist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French writer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_Bon"}]}, {"year": "1953", "text": "<PERSON>, South Korean footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-kun\" title=\"<PERSON> Bum-kun\"><PERSON>-k<PERSON></a>, South Korean footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-kun\" title=\"<PERSON> Bum-kun\"><PERSON>-k<PERSON></a>, South Korean footballer and manager", "links": [{"title": "<PERSON>kun", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-kun"}]}, {"year": "1953", "text": "<PERSON>, English footballer, coach, and manager (d. 2021)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and manager (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Native American human rights activist (d. 2002)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American human rights activist (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Native American human rights activist (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Japanese-American physicist and engineer, Nobel Prize laureate", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American physicist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Australian singer-songwriter and guitarist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American poet (d. 2018)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet (d. 2018)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Israeli-American basketball player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American golfer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English singer-songwriter and performer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and performer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and performer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Morrissey"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, South Korean director and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-yong\" title=\"<PERSON><PERSON><PERSON>-yong\"><PERSON><PERSON><PERSON>yo<PERSON></a>, South Korean director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-yong\" title=\"<PERSON><PERSON><PERSON>-yong\"><PERSON><PERSON><PERSON>yo<PERSON></a>, South Korean director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>ng", "link": "https://wikipedia.org/wiki/<PERSON>wa<PERSON>_<PERSON><PERSON>-yong"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Meh<PERSON><PERSON>_Mufti\" title=\"Mehbooba Mufti\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON><PERSON><PERSON>_Mufti\" title=\"Mehbooba Mufti\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician", "links": [{"title": "Me<PERSON>booba Mufti", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Mufti"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Japanese animator, director, and screenwriter", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Hidea<PERSON>_<PERSON>o\" title=\"Hideaki Anno\"><PERSON><PERSON><PERSON></a>, Japanese animator, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hidea<PERSON>_<PERSON>\" title=\"Hideaki Anno\"><PERSON><PERSON><PERSON></a>, Japanese animator, director, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hidea<PERSON>_<PERSON>o"}]}, {"year": "1962", "text": "<PERSON>, French-American golfer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American football player and wrestler (d. 1997)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, French contemporary artist", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French contemporary artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French contemporary artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American journalist, 29th White House Press Secretary", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 29th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, 29th <a href=\"https://wikipedia.org/wiki/White_House_Press_Secretary\" title=\"White House Press Secretary\">White House Press Secretary</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "White House Press Secretary", "link": "https://wikipedia.org/wiki/White_House_Press_Secretary"}]}, {"year": "1966", "text": "<PERSON>, American singer-songwriter and producer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Chinese director and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Irish comedy writer and activist", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish comedy writer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish comedy writer and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1969", "text": "<PERSON>, American lawyer and politician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English model", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian and actor (d. 2019)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American author and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Danish actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American actor", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Nigerian footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Garba_Lawal\" title=\"Garba Lawal\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON>_Lawal\" title=\"Garba Lawal\"><PERSON><PERSON><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Garba_Lawal"}]}, {"year": "1974", "text": "<PERSON>, Hungarian Olympic gymnast", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Henrietta_%C3%93nodi\" title=\"<PERSON>\"><PERSON></a>, Hungarian Olympic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henrietta_%C3%93nodi\" title=\"<PERSON>\"><PERSON></a>, Hungarian Olympic gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henrietta_%C3%93nodi"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Ukrainian politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Arsen<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ukrainian politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arseni<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Cuban author and dissident (d. 2015)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Canek_S%C3%<PERSON><PERSON><PERSON>_Guevara\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and dissident (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canek_S%C3%<PERSON><PERSON><PERSON>_<PERSON>ra\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban author and dissident (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Canek_S%C3%<PERSON><PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1974", "text": "<PERSON>, Norwegian politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Spanish footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Salva_Ballesta\" title=\"Salva Ballesta\"><PERSON><PERSON></a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salva_Ballesta\" title=\"Salva Ballesta\"><PERSON><PERSON></a>, Spanish footballer and manager", "links": [{"title": "Salva Ballesta", "link": "https://wikipedia.org/wiki/Salva_Ballesta"}]}, {"year": "1976", "text": "<PERSON>, American cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Irish jockey (d. 2020)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish jockey (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English television personality and glamour model", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television personality and glamour model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television personality and glamour model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Iranian-American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Bulgarian Chess boxer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Tih<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian <a href=\"https://wikipedia.org/wiki/Chess_boxing\" title=\"Chess boxing\">Chess boxer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tih<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bulgarian <a href=\"https://wikipedia.org/wiki/Chess_boxing\" title=\"Chess boxing\">Chess boxer</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Chess boxing", "link": "https://wikipedia.org/wiki/Chess_boxing"}]}, {"year": "1979", "text": "<PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rin_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, American politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> David<PERSON>\"><PERSON><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, British actress and model (d. 2009)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British actress and model (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, British actress and model (d. 2009)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1981", "text": "<PERSON>, American wrestler", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Syrian computer programmer and engineer (d. 2015)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khartabil\" title=\"<PERSON><PERSON> Khartabil\"><PERSON><PERSON></a>, Syrian computer programmer and engineer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Khartabil\" title=\"<PERSON><PERSON> Khartabil\"><PERSON><PERSON></a>, Syrian computer programmer and engineer (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Khartabil"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Austrian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meley\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Meley\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mark_O%27Meley"}]}, {"year": "1982", "text": "<PERSON>, Australian model and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American speed skater", "html": "1982 - <a href=\"https://wikipedia.org/wiki/A<PERSON>o_<PERSON>\" title=\"Apol<PERSON> Ohno\"><PERSON><PERSON><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"Apol<PERSON> Oh<PERSON>\"><PERSON><PERSON><PERSON></a>, American speed skater", "links": [{"title": "Apolo Ohno", "link": "https://wikipedia.org/wiki/A<PERSON>o_<PERSON>no"}]}, {"year": "1982", "text": "<PERSON>, North Korean footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>jo\"><PERSON></a>, North Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>jo\"><PERSON></a>, North Korean footballer", "links": [{"title": "<PERSON>o", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-jo"}]}, {"year": "1983", "text": "<PERSON>, American soccer player and Olympic medalist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and Olympic medalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player and Olympic medalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English television and radio presenter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Amfo\"><PERSON></a>, English television and radio presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clara Amfo\"><PERSON></a>, English television and radio presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clara_Amfo"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, German actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Ivorian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Did<PERSON>\"><PERSON><PERSON></a>, Ivorian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American entrepreneur, co-founder of Facebook", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, co-founder of <a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American entrepreneur, co-founder of <a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Facebook", "link": "https://wikipedia.org/wiki/Facebook"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Swiss footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tranqui<PERSON>_<PERSON>\" title=\"Tranqui<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tranqui<PERSON>_<PERSON>\" title=\"Tranqui<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tranquillo_<PERSON>a"}]}, {"year": "1985", "text": "<PERSON>, Japanese model and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Tao_Okamoto\" title=\"Tao Okamoto\"><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Okamoto\" title=\"Tao Okamoto\"><PERSON></a>, Japanese model and actress", "links": [{"title": "Tao <PERSON>", "link": "https://wikipedia.org/wiki/Tao_Okamoto"}]}, {"year": "1986", "text": "<PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian figure skater", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tatiana_Volosozhar"}]}, {"year": "1987", "text": "<PERSON>, Serbian tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Serbian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Chilean footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Icelandic-British actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic-British actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Icelandic-British actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Australian politician", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Nigerian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South Korean singer and actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Suho"}]}, {"year": "1992", "text": "<PERSON>, American actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Austrian male model", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian male model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian male model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Greek-Armenian singer and songwriter", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Man<PERSON>\"><PERSON></a>, Greek-Armenian singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Man<PERSON>\"><PERSON></a>, Greek-Armenian singer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Finnish basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Brazilian fashion model", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian fashion model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian fashion model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Dutch model", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian sumo wrestler", "html": "1999 - <a href=\"https://wikipedia.org/wiki/H%C5%8Dsh%C5%8Dry%C5%AB_Tomokatsu\" title=\"Hōsh<PERSON>ry<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C5%8Dsh%C5%8Dry%C5%AB_Tomokatsu\" title=\"Hōsh<PERSON>ry<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C5%8Dsh%C5%8Dry%C5%AB_Tomokatsu"}]}, {"year": "2001", "text": "<PERSON>, American internet personality", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American football player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2004", "text": "<PERSON>, American actress", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "192", "text": "<PERSON>, Chinese warlord and politician (b. 138)", "html": "192 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord and politician (b. 138)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese warlord and politician (b. 138)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "337", "text": "<PERSON> the Great, Roman emperor (b. 272)", "html": "337 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Roman emperor (b. 272)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, Roman emperor (b. 272)", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}]}, {"year": "748", "text": "Empress <PERSON><PERSON><PERSON> of Japan (b. 683)", "html": "748 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a> of Japan (b. 683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON><PERSON>%C5%8D\" title=\"Empress <PERSON><PERSON><PERSON>\">Empress <PERSON><PERSON><PERSON></a> of Japan (b. 683)", "links": [{"title": "Empress <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gensh%C5%8D"}]}, {"year": "1068", "text": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON> of Japan (b. 1025)", "html": "1068 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Reizei\" title=\"Emperor Go-Reizei\">Emperor <PERSON><PERSON>Re<PERSON><PERSON></a> of Japan (b. 1025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Reizei\" title=\"Emperor Go-Reizei\">Emperor <PERSON><PERSON>Re<PERSON><PERSON></a> of Japan (b. 1025)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Re<PERSON>i"}]}, {"year": "1310", "text": "<PERSON> <PERSON><PERSON><PERSON>, founder of the Vallumbrosan religious order of nuns (b. c.1226)", "html": "1310 - <a href=\"https://wikipedia.org/wiki/Saint_Humility\" title=\"Saint Humility\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Vallombrosians\" title=\"Vallombrosians\">Vallumbrosan religious order</a> of nuns (b. c.1226)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Humility\" title=\"Saint Hu<PERSON>\"><PERSON></a>, founder of the <a href=\"https://wikipedia.org/wiki/Vallombrosians\" title=\"Vallombrosians\">Vallumbrosan religious order</a> of nuns (b. c.1226)", "links": [{"title": "Saint Humility", "link": "https://wikipedia.org/wiki/Saint_Humility"}, {"title": "Vallombrosians", "link": "https://wikipedia.org/wiki/Vallombrosians"}]}, {"year": "1409", "text": "<PERSON> of England, sister of King <PERSON> (b. 1392)", "html": "1409 - <a href=\"https://wikipedia.org/wiki/Blanche_of_England\" title=\"Blanche of England\"><PERSON> of England</a>, sister of King <PERSON> (b. 1392)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Blanche_of_England\" title=\"Blanche of England\"><PERSON> of England</a>, sister of King <PERSON> (b. 1392)", "links": [{"title": "Blanche of England", "link": "https://wikipedia.org/wiki/Blanche_of_England"}]}, {"year": "1455", "text": "<PERSON>, 2nd Duke of Somerset, English commander (b. 1406)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Somerset\" title=\"<PERSON>, 2nd Duke of Somerset\"><PERSON>, 2nd Duke of Somerset</a>, English commander (b. 1406)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Duke_of_Somerset\" title=\"<PERSON>, 2nd Duke of Somerset\"><PERSON>, 2nd Duke of Somerset</a>, English commander (b. 1406)", "links": [{"title": "<PERSON>, 2nd Duke of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Somerset"}]}, {"year": "1455", "text": "<PERSON>, 8th Baron <PERSON>, Lancastrian commander (b. 1414)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, Lancastrian commander (b. 1414)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 8th Baron <PERSON>\"><PERSON>, 8th Baron <PERSON></a>, Lancastrian commander (b. 1414)", "links": [{"title": "<PERSON>, 8th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_8th_Baron_<PERSON>_<PERSON>"}]}, {"year": "1455", "text": "<PERSON>, 2nd Earl of Northumberland, English commander (b. 1393)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland\" title=\"<PERSON>, 2nd Earl of Northumberland\"><PERSON>, 2nd Earl of Northumberland</a>, English commander (b. 1393)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland\" title=\"<PERSON>, 2nd Earl of Northumberland\"><PERSON>, 2nd Earl of Northumberland</a>, English commander (b. 1393)", "links": [{"title": "<PERSON>, 2nd Earl of Northumberland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Northumberland"}]}, {"year": "1457", "text": "<PERSON> of Cascia, Italian nun and saint (b. 1381)", "html": "1457 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Cascia\" title=\"<PERSON> of Cascia\"><PERSON> of Cascia</a>, Italian nun and saint (b. 1381)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Cascia\" title=\"<PERSON> of Cascia\"><PERSON> of Cascia</a>, Italian nun and saint (b. 1381)", "links": [{"title": "Rita of Cascia", "link": "https://wikipedia.org/wiki/Rita_of_Cascia"}]}, {"year": "1490", "text": "<PERSON>, 1st Earl of Kent, English administrator, nobleman and magnate (b. 1416)", "html": "1490 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Kent\" title=\"<PERSON>, 1st Earl of Kent\"><PERSON>, 1st Earl of Kent</a>, English administrator, nobleman and magnate (b. 1416)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Kent\" title=\"<PERSON>, 1st Earl of Kent\"><PERSON>, 1st Earl of Kent</a>, English administrator, nobleman and magnate (b. 1416)", "links": [{"title": "<PERSON>, 1st Earl of Kent", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Kent"}]}, {"year": "1538", "text": "<PERSON>, English friar and martyr (b. 1471)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John Forest\"><PERSON></a>, English friar and martyr (b. 1471)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_<PERSON>\" title=\"John Forest\"><PERSON></a>, English friar and martyr (b. 1471)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1540", "text": "<PERSON>, Italian historian and politician (b. 1483)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian historian and politician (b. 1483)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian historian and politician (b. 1483)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON><PERSON>, Indian ruler (b. 1486)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian ruler (b. 1486)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian ruler (b. 1486)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1553", "text": "<PERSON>, Italian sculptor and engraver (b. 1495)", "html": "1553 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and engraver (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian sculptor and engraver (b. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1602", "text": "<PERSON><PERSON> <PERSON> Lorraine (b. 1544)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/Renata_of_Lorraine\" title=\"Renata of Lorraine\">Renata of Lorraine</a> (b. 1544)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Renata_of_Lorraine\" title=\"Renata of Lorraine\">Renata of Lorraine</a> (b. 1544)", "links": [{"title": "Renata of Lorraine", "link": "https://wikipedia.org/wiki/Renata_of_Lorraine"}]}, {"year": "1609", "text": "<PERSON>, Dutch captain (b. 1573)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch captain (b. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch captain (b. 1573)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON><PERSON>, German physicist and mathematician (b. 1608)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and mathematician (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physicist and mathematician (b. 1608)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1667", "text": "<PERSON> (b. 1599)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Alexander VII\">Pope <PERSON> VII</a> (b. 1599)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Alexander VII\"><PERSON> <PERSON> VII</a> (b. 1599)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1745", "text": "<PERSON><PERSON><PERSON>, 1st duc <PERSON>, French general (b. 1671)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_1st_duc_de_Broglie\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st duc de Broglie\"><PERSON><PERSON><PERSON>, 1st duc de Broglie</a>, French general (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>,_1st_duc_de_Brog<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, 1st duc de Broglie\"><PERSON><PERSON><PERSON>, 1st duc de Broglie</a>, French general (b. 1671)", "links": [{"title": "<PERSON><PERSON><PERSON>, 1st duc de B<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>-<PERSON>,_1st_duc_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1760", "text": "<PERSON><PERSON>, Polish rabbi and author (b. 1700)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Shem_Tov\" title=\"Baal Shem Tov\"><PERSON><PERSON><PERSON></a>, Polish rabbi and author (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Tov\" title=\"Baal Shem Tov\"><PERSON><PERSON> <PERSON><PERSON></a>, Polish rabbi and author (b. 1700)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>v"}]}, {"year": "1772", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian historian and academic (b. 1687)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and academic (b. 1687)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian historian and academic (b. 1687)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1795", "text": "<PERSON><PERSON>, Prussian politician, Foreign Minister of Prussia (b. 1725)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Prussia\" title=\"List of foreign ministers of Prussia\">Foreign Minister of Prussia</a> (b. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Prussian politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Prussia\" title=\"List of foreign ministers of Prussia\">Foreign Minister of Prussia</a> (b. 1725)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of foreign ministers of Prussia", "link": "https://wikipedia.org/wiki/List_of_foreign_ministers_of_Prussia"}]}, {"year": "1802", "text": "<PERSON>, First, First Lady of the United States (b. 1731)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/Martha_Washington\" title=\"Martha Washington\"><PERSON></a>, First, First Lady of the United States (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Martha_Washington\" title=\"Martha Washington\"><PERSON></a>, First, First Lady of the United States (b. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Martha_Washington"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON><PERSON>, American journalist and diplomat (b. 1755)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Morde<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Mordecai Manuel <PERSON>\">Morde<PERSON><PERSON> <PERSON></a>, American journalist and diplomat (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Mordeca<PERSON> Manuel <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, American journalist and diplomat (b. 1755)", "links": [{"title": "Morde<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON> of the Two Sicilies (b. 1810)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> II of the Two Sicilies</a> (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies\" title=\"<PERSON> II of the Two Sicilies\"><PERSON> II of the Two Sicilies</a> (b. 1810)", "links": [{"title": "<PERSON> of the Two Sicilies", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_the_Two_Sicilies"}]}, {"year": "1861", "text": "<PERSON><PERSON>, American soldier  (b. 1829)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Thornsbury_<PERSON>_<PERSON>\" title=\"Thornsbury Bailey Brown\"><PERSON><PERSON></a>, American soldier (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thornsbury_Bailey_Brown\" title=\"Thornsbury Bailey Brown\"><PERSON><PERSON></a>, American soldier (b. 1829)", "links": [{"title": "Thornsbury Bailey Brown", "link": "https://wikipedia.org/wiki/Thorn<PERSON>_Bailey_Brown"}]}, {"year": "1868", "text": "<PERSON>, German mathematician and physicist (b. 1801)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%BCcker\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%BCcker\" title=\"<PERSON>\"><PERSON></a>, German mathematician and physicist (b. 1801)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Pl%C3%BCcker"}]}, {"year": "1885", "text": "<PERSON>, French novelist, poet, and playwright (b. 1802)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French novelist, poet, and playwright (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON><PERSON>, Italian-American anarchist, assassin of <PERSON><PERSON> of Italy (b. 1869)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American anarchist, assassin of <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ae<PERSON>_<PERSON>\" title=\"<PERSON>ae<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-American anarchist, assassin of <a href=\"https://wikipedia.org/wiki/Umberto_I_of_Italy\" title=\"Umberto I of Italy\"><PERSON><PERSON> of Italy</a> (b. 1869)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gae<PERSON>_Bres<PERSON>"}, {"title": "<PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/Umberto_I_of_Italy"}]}, {"year": "1910", "text": "<PERSON>, French author and playwright (b. 1864)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and playwright (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, <PERSON>, Anglo-Irish activist, landlord, and playwright, co-founded the Abbey Theatre (b. 1852)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, Anglo-Irish activist, landlord, and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/Abbey_Theatre\" title=\"Abbey Theatre\">Abbey Theatre</a> (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON>\"><PERSON>, Lady <PERSON></a>, Anglo-Irish activist, landlord, and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/Abbey_Theatre\" title=\"Abbey Theatre\">Abbey Theatre</a> (b. 1852)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/Augusta,_<PERSON>_<PERSON>"}, {"title": "Abbey Theatre", "link": "https://wikipedia.org/wiki/Abbey_Theatre"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Mongolian politician, 10th Prime Minister of Mongolia (b. 1894)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Tsengel<PERSON><PERSON>_Jig<PERSON>jav\" title=\"Tsengelti<PERSON> Ji<PERSON>jav\">Tsen<PERSON><PERSON><PERSON></a>, Mongolian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mongolia\" class=\"mw-redirect\" title=\"List of Prime Ministers of Mongolia\">Prime Minister of Mongolia</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tsengel<PERSON><PERSON>_Jig<PERSON>jav\" title=\"Tsengeltiin Jigjidjav\">Tsen<PERSON><PERSON><PERSON></a>, Mongolian politician, 10th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mongolia\" class=\"mw-redirect\" title=\"List of Prime Ministers of Mongolia\">Prime Minister of Mongolia</a> (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsengel<PERSON><PERSON>_<PERSON>v"}, {"title": "List of Prime Ministers of Mongolia", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Mongolia"}]}, {"year": "1938", "text": "<PERSON>, American painter and illustrator (b. 1870)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, German playwright and author (b. 1893)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German playwright and author (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German playwright and author (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Czech author and playwright (b. 1882)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech author and playwright (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ji%C5%99%C3%AD_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech author and playwright (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ji%C5%99%C3%AD_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Jamaican writer and poet (b. 1889)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican writer and poet (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican writer and poet (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "Chief <PERSON>, American baseball player, coach, and manager (b. 1884)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON>er\">Chief <PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chief_<PERSON><PERSON>\" title=\"Chief <PERSON>er\">Chief <PERSON><PERSON></a>, American baseball player, coach, and manager (b. 1884)", "links": [{"title": "Chief <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chief_<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English radio host (b. 1882)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(broadcaster)\" title=\"<PERSON> (broadcaster)\"><PERSON></a>, English radio host (b. 1882)", "links": [{"title": "<PERSON> (broadcaster)", "link": "https://wikipedia.org/wiki/<PERSON>_(broadcaster)"}]}, {"year": "1966", "text": "<PERSON>, English cricketer (b. 1900)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American poet, social activist, novelist, and playwright (b. 1902)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hughes\"><PERSON><PERSON></a>, American poet, social activist, novelist, and playwright (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American poet, social activist, novelist, and playwright (b. 1902)", "links": [{"title": "<PERSON><PERSON> Hughes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American Librarian of the Manhattan Project's Los Alamos site (b. 1911)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Librarian of the Manhattan Project's Los Alamos site (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Librarian of the Manhattan Project's Los Alamos site (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1972", "text": "<PERSON>-<PERSON>, Anglo-Irish poet and author (b. 1904)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish poet and author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish poet and author (b. 1904)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English actress (b. 1892)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, German-American mathematician and aerospace engineer (b. 1903)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz\" title=\"<PERSON>rm<PERSON> Flügge-Lotz\"><PERSON><PERSON><PERSON></a>, German-American mathematician and aerospace engineer (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz\" title=\"<PERSON>rm<PERSON> Flügge-Lotz\"><PERSON><PERSON><PERSON></a>, German-American mathematician and aerospace engineer (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>ügge-Lotz", "link": "https://wikipedia.org/wiki/Irmgard_Fl%C3%BCgge-Lotz"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American baseball player (b. 1900)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Lefty_Grove\" title=\"Lefty Grove\"><PERSON><PERSON></a>, American baseball player (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lefty_Grove\" title=\"Lefty Grove\"><PERSON><PERSON></a>, American baseball player (b. 1900)", "links": [{"title": "Lefty <PERSON>", "link": "https://wikipedia.org/wiki/Lefty_Grove"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Turkish general and politician, 5th President of Turkey (b. 1899)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish general and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_Turkey\" title=\"President of Turkey\">President of Turkey</a> (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cevdet_Sunay"}, {"title": "President of Turkey", "link": "https://wikipedia.org/wiki/President_of_Turkey"}]}, {"year": "1983", "text": "<PERSON>, Belgian biologist and academic, Nobel Prize laureate (b. 1899)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German lawyer and justice of the Federal Constitutional Court (b. 1893)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German lawyer and <a href=\"https://wikipedia.org/wiki/List_of_justices_of_the_Federal_Constitutional_Court\" title=\"List of justices of the Federal Constitutional Court\">justice of the Federal Constitutional Court</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German lawyer and <a href=\"https://wikipedia.org/wiki/List_of_justices_of_the_Federal_Constitutional_Court\" title=\"List of justices of the Federal Constitutional Court\">justice of the Federal Constitutional Court</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "List of justices of the Federal Constitutional Court", "link": "https://wikipedia.org/wiki/List_of_justices_of_the_Federal_Constitutional_Court"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Finnish politician, val<PERSON><PERSON><PERSON>, the Speaker of the Parliament and the Prime Minister of Finland (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Finnish politician, <i><a href=\"https://wikipedia.org/wiki/Valtioneuvos\" title=\"Valtioneuvos\">valtioneuvos</a></i>, the <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a> and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish politician, <i><a href=\"https://wikipedia.org/wiki/Valtioneuvos\" title=\"Valtioneuvos\">valtioneuvos</a></i>, the <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland\" title=\"Speaker of the Parliament of Finland\">Speaker of the Parliament</a> and the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Finland\" title=\"Prime Minister of Finland\">Prime Minister of Finland</a> (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Valtioneuvos", "link": "https://wikipedia.org/wiki/Valtioneuvos"}, {"title": "Speaker of the Parliament of Finland", "link": "https://wikipedia.org/wiki/Speaker_of_the_Parliament_of_Finland"}, {"title": "Prime Minister of Finland", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Finland"}]}, {"year": "1985", "text": "<PERSON>, German-American animator, director, and producer (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American animator, director, and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American animator, director, and producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Italian journalist and politician (b. 1914)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, South African pianist and educator (b. 1953)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African pianist and educator (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African pianist and educator (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American boxer (b. 1922)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rocky_Graziano"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Filipino director and screenwriter (b. 1939)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino director and screenwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino director and screenwriter (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Indian lawyer and politician (b. 1899)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Shri<PERSON>_Amrit_Dange\" title=\"Shripad Amrit Dange\"><PERSON><PERSON> <PERSON></a>, Indian lawyer and politician (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shri<PERSON>_Amrit_Dange\" title=\"Shripad Amrit Dange\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician (b. 1899)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rit_Dange"}]}, {"year": "1991", "text": "<PERSON>, English footballer and manager (b. 1921)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American linguist and academic (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American linguist and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American linguist and academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish-American pianist and composer (b. 1892)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-American pianist and composer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mieczys%C5%82aw_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish-American pianist and composer (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mieczys%C5%82aw_<PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Italian architect and painter (b. 1906)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and painter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect and painter (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>zo"}]}, {"year": "1997", "text": "<PERSON>, American biochemist and geneticist, Nobel Prize laureate (b. 1908)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and geneticist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1998", "text": "<PERSON>, American actor, director, and photographer (b. 1926)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and photographer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and photographer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Israeli physicist and engineer (b. 1910)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli physicist and engineer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli physicist and engineer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Canadian lawyer, judge, and politician (b. 1916)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American actor (b. 1960)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Russian gymnast (b. 1945)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian gymnast (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek politician (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek politician (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, American voice actor and singer (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Thur<PERSON>_<PERSON>\" title=\"Thur<PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actor and singer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thur<PERSON>_<PERSON>\" title=\"Thur<PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actor and singer (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thurl_<PERSON>croft"}]}, {"year": "2006", "text": "<PERSON>, South Korean physician and diplomat (b. 1945)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook\" title=\"<PERSON>wook\"><PERSON>w<PERSON></a>, South Korean physician and diplomat (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook\" title=\"<PERSON>wook\"><PERSON>w<PERSON></a>, South Korean physician and diplomat (b. 1945)", "links": [{"title": "<PERSON>wook", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-wook"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Nepalese mountaineer (b. 1970)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/P<PERSON><PERSON>_Doma_She<PERSON>\" title=\"Pemba Doma Sherpa\"><PERSON><PERSON><PERSON></a>, Nepalese mountaineer (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Doma_She<PERSON>\" title=\"Pemba Doma Sherpa\"><PERSON><PERSON><PERSON></a>, Nepalese mountaineer (b. 1970)", "links": [{"title": "Pemba Doma Sherpa", "link": "https://wikipedia.org/wiki/<PERSON>emba_<PERSON><PERSON>_<PERSON>a"}]}, {"year": "2008", "text": "<PERSON>, American soldier and author (b. 1946)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American mathematician, cryptographer, and author (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, cryptographer, and author (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician, cryptographer, and author (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American director, producer, screenwriter, and composer (b. 1938)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American director, producer, screenwriter, and composer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American director, producer, screenwriter, and composer (b. 1938)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(songwriter)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Pakistani politician (b. 1970)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician (b. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani politician (b. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American lieutenant and engineer (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and engineer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and engineer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Russian historian and ethnographer (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>gu<PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and ethnographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian historian and ethnographer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American basketball player and coach (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player and coach (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Ukrainian-Canadian SS officer (b. 1921)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Canadian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Canadian <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>, Serbian actor and politician (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, Serbian actor and politician (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, Serbian actor and politician (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bata_%C5%BDivojinovi%C4%87"}]}, {"year": "2017", "text": "<PERSON>, American motorcycle racer (b. 1981)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American motorcycle racer (b. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, German-born British writer and illustrator (b. 1923)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born British writer and illustrator (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-born British writer and illustrator (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Canadian costume designer (b. 1938)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian costume designer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian costume designer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Irish touring cyclist and author (b. 1931)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Irish touring cyclist and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Irish touring cyclist and author (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Scottish swimmer (b. 1954)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer (b. 1954)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}]}}