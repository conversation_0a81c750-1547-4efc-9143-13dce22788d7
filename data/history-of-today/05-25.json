{"date": "May 25", "url": "https://wikipedia.org/wiki/May_25", "data": {"Events": [{"year": "567 BC", "text": "<PERSON><PERSON><PERSON>, the king of Rome, celebrates a triumph for his victory over the Etruscans.", "html": "567 BC - 567 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>lli<PERSON>\"><PERSON><PERSON><PERSON></a>, the king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for his victory over the <a href=\"https://wikipedia.org/wiki/Etruscans\" class=\"mw-redirect\" title=\"Etruscans\">Etruscans</a>.", "no_year_html": "567 BC - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>llius\"><PERSON><PERSON><PERSON></a>, the king of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a>, celebrates a <a href=\"https://wikipedia.org/wiki/Roman_triumph\" title=\"Roman triumph\">triumph</a> for his victory over the <a href=\"https://wikipedia.org/wiki/Etruscans\" class=\"mw-redirect\" title=\"Etruscans\">Etruscans</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Roman triumph", "link": "https://wikipedia.org/wiki/Roman_triumph"}, {"title": "Etruscans", "link": "https://wikipedia.org/wiki/Etruscans"}]}, {"year": "240 BC", "text": "First recorded perihelion passage of <PERSON><PERSON>'s <PERSON>.", "html": "240 BC - 240 BC - First recorded <a href=\"https://wikipedia.org/wiki/Perihelion\" class=\"mw-redirect\" title=\"Perihelion\">perihelion</a> passage of <a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a>.", "no_year_html": "240 BC - First recorded <a href=\"https://wikipedia.org/wiki/Perihelion\" class=\"mw-redirect\" title=\"Perihelion\">perihelion</a> passage of <a href=\"https://wikipedia.org/wiki/Halley%27s_Comet\" title=\"<PERSON><PERSON>'s Comet\"><PERSON><PERSON>'s Comet</a>.", "links": [{"title": "Perih<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Perihelion"}, {"title": "Halley's Comet", "link": "https://wikipedia.org/wiki/Halley%27s_Comet"}]}, {"year": "1085", "text": "<PERSON> of Castile takes Toledo, Spain, back from the Moors.", "html": "1085 - <a href=\"https://wikipedia.org/wiki/Alfonso_VI_of_Castile\" class=\"mw-redirect\" title=\"<PERSON> VI of Castile\"><PERSON> of Castile</a> takes <a href=\"https://wikipedia.org/wiki/Toledo,_Spain\" title=\"Toledo, Spain\">Toledo, Spain</a>, back from the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moors</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfonso_VI_of_Castile\" class=\"mw-redirect\" title=\"<PERSON> VI of Castile\"><PERSON> of Castile</a> takes <a href=\"https://wikipedia.org/wiki/Toledo,_Spain\" title=\"Toledo, Spain\">Toledo, Spain</a>, back from the <a href=\"https://wikipedia.org/wiki/Moors\" title=\"Moors\">Moors</a>.", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/Alfonso_VI_of_Castile"}, {"title": "Toledo, Spain", "link": "https://wikipedia.org/wiki/Toledo,_Spain"}, {"title": "Moors", "link": "https://wikipedia.org/wiki/Moors"}]}, {"year": "1420", "text": "<PERSON> the Navigator is appointed governor of the Order of Christ.", "html": "1420 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Navigator\" class=\"mw-redirect\" title=\"<PERSON> the Navigator\"><PERSON> the Navigator</a> is appointed governor of the <a href=\"https://wikipedia.org/wiki/Order_of_Christ_(Portugal)\" class=\"mw-redirect\" title=\"Order of Christ (Portugal)\">Order of Christ</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Navigator\" class=\"mw-redirect\" title=\"<PERSON> the Navigator\"><PERSON> the Navigator</a> is appointed governor of the <a href=\"https://wikipedia.org/wiki/Order_of_Christ_(Portugal)\" class=\"mw-redirect\" title=\"Order of Christ (Portugal)\">Order of Christ</a>.", "links": [{"title": "<PERSON> the Navigator", "link": "https://wikipedia.org/wiki/<PERSON>_the_Navigator"}, {"title": "Order of Christ (Portugal)", "link": "https://wikipedia.org/wiki/Order_of_Christ_(Portugal)"}]}, {"year": "1521", "text": "The Diet of Worms ends when <PERSON>, Holy Roman Emperor, issues the Edict of Worms, declaring <PERSON> an outlaw.", "html": "1521 - The <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a> ends when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> V, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Worms\" class=\"mw-redirect\" title=\"Edict of Worms\">Edict of Worms</a>, declaring <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> an <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Diet_of_Worms\" title=\"Diet of Worms\">Diet of Worms</a> ends when <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>, issues the <a href=\"https://wikipedia.org/wiki/Edict_of_Worms\" class=\"mw-redirect\" title=\"Edict of Worms\">Edict of Worms</a>, declaring <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> an <a href=\"https://wikipedia.org/wiki/Outlaw\" title=\"Outlaw\">outlaw</a>.", "links": [{"title": "Diet of Worms", "link": "https://wikipedia.org/wiki/Diet_of_Worms"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Edict of Worms", "link": "https://wikipedia.org/wiki/Edict_of_Worms"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Outlaw", "link": "https://wikipedia.org/wiki/Outlaw"}]}, {"year": "1644", "text": "Ming general <PERSON> forms an alliance with the invading Manchus and opens the gates of the Great Wall of China at Shanhaiguan pass, letting the Manchus through towards the capital Beijing.", "html": "1644 - <a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> general <a href=\"https://wikipedia.org/wiki/Wu_Sangui\" title=\"Wu Sang<PERSON>\"><PERSON></a> forms an alliance with the invading <a href=\"https://wikipedia.org/wiki/Manchus\" class=\"mw-redirect\" title=\"Manchus\">Manchus</a> and opens the gates of the <a href=\"https://wikipedia.org/wiki/Great_Wall_of_China\" title=\"Great Wall of China\">Great Wall of China</a> at <a href=\"https://wikipedia.org/wiki/Shanhai_Pass\" title=\"Shanhai Pass\">Shanhaiguan</a> pass, letting the Manchus through towards the capital Beijing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ming_dynasty\" title=\"Ming dynasty\">Ming</a> general <a href=\"https://wikipedia.org/wiki/Wu_Sangui\" title=\"Wu Sang<PERSON>\"><PERSON></a> forms an alliance with the invading <a href=\"https://wikipedia.org/wiki/Manchus\" class=\"mw-redirect\" title=\"Manchus\">Manchus</a> and opens the gates of the <a href=\"https://wikipedia.org/wiki/Great_Wall_of_China\" title=\"Great Wall of China\">Great Wall of China</a> at <a href=\"https://wikipedia.org/wiki/Shanhai_Pass\" title=\"Shanhai Pass\">Shanhaiguan</a> pass, letting the Manchus through towards the capital Beijing.", "links": [{"title": "Ming dynasty", "link": "https://wikipedia.org/wiki/Ming_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Manchus", "link": "https://wikipedia.org/wiki/Manchus"}, {"title": "Great Wall of China", "link": "https://wikipedia.org/wiki/Great_Wall_of_China"}, {"title": "Shanhai Pass", "link": "https://wikipedia.org/wiki/Shanhai_Pass"}]}, {"year": "1659", "text": "<PERSON> resigns as Lord Protector of England following the restoration of the Long Parliament, beginning a second brief period of the republican government called the Commonwealth of England.", "html": "1659 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of England following the restoration of the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>, beginning a second brief period of the <a href=\"https://wikipedia.org/wiki/Republicanism\" title=\"Republicanism\">republican</a> government called the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of England following the restoration of the <a href=\"https://wikipedia.org/wiki/Long_Parliament\" title=\"Long Parliament\">Long Parliament</a>, beginning a second brief period of the <a href=\"https://wikipedia.org/wiki/Republicanism\" title=\"Republicanism\">republican</a> government called the <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England\" title=\"Commonwealth of England\">Commonwealth of England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Pro<PERSON>tor", "link": "https://wikipedia.org/wiki/<PERSON>_Protector"}, {"title": "Long Parliament", "link": "https://wikipedia.org/wiki/Long_Parliament"}, {"title": "Republicanism", "link": "https://wikipedia.org/wiki/Republicanism"}, {"title": "Commonwealth of England", "link": "https://wikipedia.org/wiki/Commonwealth_of_England"}]}, {"year": "1660", "text": "<PERSON> lands at Dover at the invitation of the Convention Parliament, which marks the end of the Cromwell-proclaimed Commonwealth of England, Scotland and Ireland and begins the Restoration of the British monarchy.", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> II</a> lands at Dover at the invitation of the <a href=\"https://wikipedia.org/wiki/Convention_Parliament_(England)\" title=\"Convention Parliament (England)\">Convention Parliament</a>, which marks the end of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>-proclaimed <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England,_Scotland_and_Ireland\" class=\"mw-redirect\" title=\"Commonwealth of England, Scotland and Ireland\">Commonwealth of England, Scotland and Ireland</a> and begins the <a href=\"https://wikipedia.org/wiki/Stuart_Restoration\" title=\"Stuart Restoration\">Restoration of the British monarchy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England\" title=\"<PERSON> II of England\"><PERSON> II</a> lands at Dover at the invitation of the <a href=\"https://wikipedia.org/wiki/Convention_Parliament_(England)\" title=\"Convention Parliament (England)\">Convention Parliament</a>, which marks the end of the <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"Cromwell\"><PERSON></a>-proclaimed <a href=\"https://wikipedia.org/wiki/Commonwealth_of_England,_Scotland_and_Ireland\" class=\"mw-redirect\" title=\"Commonwealth of England, Scotland and Ireland\">Commonwealth of England, Scotland and Ireland</a> and begins the <a href=\"https://wikipedia.org/wiki/Stuart_Restoration\" title=\"Stuart Restoration\">Restoration of the British monarchy</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Convention Parliament (England)", "link": "https://wikipedia.org/wiki/Convention_Parliament_(England)"}, {"title": "Cromwell", "link": "https://wikipedia.org/wiki/Cromwell"}, {"title": "Commonwealth of England, Scotland and Ireland", "link": "https://wikipedia.org/wiki/Commonwealth_of_England,_Scotland_and_Ireland"}, {"title": "Stuart Restoration", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1738", "text": "A treaty between Pennsylvania and Maryland ends the Conojocular War with settlement of a boundary dispute and exchange of prisoners.", "html": "1738 - A treaty between <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> and <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a> ends the <a href=\"https://wikipedia.org/wiki/Cresap%27s_War\" title=\"Cresap's War\">Conojocular War</a> with settlement of a <a href=\"https://wikipedia.org/wiki/Territorial_dispute\" title=\"Territorial dispute\">boundary dispute</a> and exchange of <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">prisoners</a>.", "no_year_html": "A treaty between <a href=\"https://wikipedia.org/wiki/Pennsylvania\" title=\"Pennsylvania\">Pennsylvania</a> and <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a> ends the <a href=\"https://wikipedia.org/wiki/Cresap%27s_War\" title=\"Cresap's War\">Conojocular War</a> with settlement of a <a href=\"https://wikipedia.org/wiki/Territorial_dispute\" title=\"Territorial dispute\">boundary dispute</a> and exchange of <a href=\"https://wikipedia.org/wiki/Prisoner_of_war\" title=\"Prisoner of war\">prisoners</a>.", "links": [{"title": "Pennsylvania", "link": "https://wikipedia.org/wiki/Pennsylvania"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}, {"title": "Cresap's War", "link": "https://wikipedia.org/wiki/Cresap%27s_War"}, {"title": "Territorial dispute", "link": "https://wikipedia.org/wiki/Territorial_dispute"}, {"title": "Prisoner of war", "link": "https://wikipedia.org/wiki/Prisoner_of_war"}]}, {"year": "1763", "text": "First issue of Norske Intelligenz-Seddeler, the first regular Norwegian newspaper (1763-1920).", "html": "1763 - First issue of <i><a href=\"https://wikipedia.org/wiki/Norske_Intelligenz-Seddeler\" title=\"Norske Intelligenz-Seddeler\">Norske Intelligenz-Seddeler</a></i>, the first regular Norwegian newspaper (1763-1920).", "no_year_html": "First issue of <i><a href=\"https://wikipedia.org/wiki/Norske_Intelligenz-Seddeler\" title=\"Norske Intelligenz-Seddeler\">Norske Intelligenz-Seddeler</a></i>, the first regular Norwegian newspaper (1763-1920).", "links": [{"title": "Norske Intelligenz-Seddeler", "link": "https://wikipedia.org/wiki/Norske_Intelligenz-Seddeler"}]}, {"year": "1787", "text": "After a delay of 11 days, the United States Constitutional Convention formally convenes in Philadelphia after a quorum of seven states is secured.", "html": "1787 - After a delay of 11 days, the <a href=\"https://wikipedia.org/wiki/Constitutional_Convention_(United_States)\" title=\"Constitutional Convention (United States)\">United States Constitutional Convention</a> formally convenes in Philadelphia after a <a href=\"https://wikipedia.org/wiki/Quorum\" title=\"Quorum\">quorum</a> of seven states is secured.", "no_year_html": "After a delay of 11 days, the <a href=\"https://wikipedia.org/wiki/Constitutional_Convention_(United_States)\" title=\"Constitutional Convention (United States)\">United States Constitutional Convention</a> formally convenes in Philadelphia after a <a href=\"https://wikipedia.org/wiki/Quorum\" title=\"Quorum\">quorum</a> of seven states is secured.", "links": [{"title": "Constitutional Convention (United States)", "link": "https://wikipedia.org/wiki/Constitutional_Convention_(United_States)"}, {"title": "Quorum", "link": "https://wikipedia.org/wiki/Quorum"}]}, {"year": "1798", "text": "United Irishmen Rebellion: Battle of Carlow begins; executions of suspected rebels at Carnew and at Dunlavin Green take place.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irishmen Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Carlow\" title=\"Battle of Carlow\">Battle of Carlow</a> begins; <a href=\"https://wikipedia.org/wiki/Carnew_executions\" title=\"Carnew executions\">executions of suspected rebels at Carnew</a> and <a href=\"https://wikipedia.org/wiki/Dunlavin_Green_executions\" title=\"Dunlavin Green executions\">at Dunlavin Green</a> take place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">United Irishmen Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Carlow\" title=\"Battle of Carlow\">Battle of Carlow</a> begins; <a href=\"https://wikipedia.org/wiki/Carnew_executions\" title=\"Carnew executions\">executions of suspected rebels at Carnew</a> and <a href=\"https://wikipedia.org/wiki/Dunlavin_Green_executions\" title=\"Dunlavin Green executions\">at Dunlavin Green</a> take place.", "links": [{"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "Battle of Carlow", "link": "https://wikipedia.org/wiki/Battle_of_Carlow"}, {"title": "Carnew executions", "link": "https://wikipedia.org/wiki/Carnew_executions"}, {"title": "<PERSON><PERSON><PERSON><PERSON> executions", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Green_executions"}]}, {"year": "1809", "text": "Chuquisaca Revolution: Patriot revolt in Chuquisaca (modern-day Sucre) against the Spanish Empire, sparking the Latin American wars of independence.", "html": "1809 - <a href=\"https://wikipedia.org/wiki/Chuquisaca_Revolution\" title=\"Chuquisaca Revolution\">Chuquisaca Revolution</a>: Patriot revolt in Chuquisaca (modern-day <a href=\"https://wikipedia.org/wiki/Sucre\" title=\"Sucre\">Sucre</a>) against the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>, sparking the <a href=\"https://wikipedia.org/wiki/Spanish_American_wars_of_independence\" title=\"Spanish American wars of independence\">Latin American wars of independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chuquisaca_Revolution\" title=\"Chuquisaca Revolution\">Chuquisaca Revolution</a>: Patriot revolt in Chuquisaca (modern-day <a href=\"https://wikipedia.org/wiki/Sucre\" title=\"Sucre\">Sucre</a>) against the <a href=\"https://wikipedia.org/wiki/Spanish_Empire\" title=\"Spanish Empire\">Spanish Empire</a>, sparking the <a href=\"https://wikipedia.org/wiki/Spanish_American_wars_of_independence\" title=\"Spanish American wars of independence\">Latin American wars of independence</a>.", "links": [{"title": "Chuquisaca Revolution", "link": "https://wikipedia.org/wiki/Chuquisaca_Revolution"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sucre"}, {"title": "Spanish Empire", "link": "https://wikipedia.org/wiki/Spanish_Empire"}, {"title": "Spanish American wars of independence", "link": "https://wikipedia.org/wiki/Spanish_American_wars_of_independence"}]}, {"year": "1810", "text": "May Revolution: Citizens of Buenos Aires expel Viceroy <PERSON><PERSON><PERSON> during the \"May Week\", starting the Argentine War of Independence.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/May_Revolution\" title=\"May Revolution\">May Revolution</a>: Citizens of <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a> expel <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroy</a> <a href=\"https://wikipedia.org/wiki/Baltasar_Hidalgo_de_Cisneros\" title=\"Baltasar Hidalgo de Cisneros\">Baltasar Hidalgo de Cisneros</a> during the <a href=\"https://wikipedia.org/wiki/May_Revolution#May_Week\" title=\"May Revolution\">\"May Week\"</a>, starting the <a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Revolution\" title=\"May Revolution\">May Revolution</a>: Citizens of <a href=\"https://wikipedia.org/wiki/Buenos_Aires\" title=\"Buenos Aires\">Buenos Aires</a> expel <a href=\"https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata\" title=\"Viceroyalty of the Río de la Plata\">Viceroy</a> <a href=\"https://wikipedia.org/wiki/Baltasar_Hidalgo_de_Cisneros\" title=\"Baltasar Hidalgo de Cisneros\">Baltasar Hidalgo de Cisneros</a> during the <a href=\"https://wikipedia.org/wiki/May_Revolution#May_Week\" title=\"May Revolution\">\"May Week\"</a>, starting the <a href=\"https://wikipedia.org/wiki/Argentine_War_of_Independence\" title=\"Argentine War of Independence\">Argentine War of Independence</a>.", "links": [{"title": "May Revolution", "link": "https://wikipedia.org/wiki/May_Revolution"}, {"title": "Buenos Aires", "link": "https://wikipedia.org/wiki/Buenos_Aires"}, {"title": "Viceroyalty of the Río de la Plata", "link": "https://wikipedia.org/wiki/Viceroyalty_of_the_R%C3%ADo_de_la_Plata"}, {"title": "Baltasar <PERSON> de Cisneros", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "May Revolution", "link": "https://wikipedia.org/wiki/May_Revolution#May_Week"}, {"title": "Argentine War of Independence", "link": "https://wikipedia.org/wiki/Argentine_War_of_Independence"}]}, {"year": "1819", "text": "The Argentine Constitution of 1819 is promulgated.", "html": "1819 - The <a href=\"https://wikipedia.org/wiki/Argentine_Constitution_of_1819\" title=\"Argentine Constitution of 1819\">Argentine Constitution of 1819</a> is <a href=\"https://wikipedia.org/wiki/Promulgation\" title=\"Promulgation\">promulgated</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Argentine_Constitution_of_1819\" title=\"Argentine Constitution of 1819\">Argentine Constitution of 1819</a> is <a href=\"https://wikipedia.org/wiki/Promulgation\" title=\"Promulgation\">promulgated</a>.", "links": [{"title": "Argentine Constitution of 1819", "link": "https://wikipedia.org/wiki/Argentine_Constitution_of_1819"}, {"title": "Promulgation", "link": "https://wikipedia.org/wiki/Promulgation"}]}, {"year": "1833", "text": "The Chilean Constitution of 1833 is promulgated.", "html": "1833 - The <a href=\"https://wikipedia.org/wiki/Chilean_Constitution_of_1833\" title=\"Chilean Constitution of 1833\">Chilean Constitution of 1833</a> is promulgated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Chilean_Constitution_of_1833\" title=\"Chilean Constitution of 1833\">Chilean Constitution of 1833</a> is promulgated.", "links": [{"title": "Chilean Constitution of 1833", "link": "https://wikipedia.org/wiki/Chilean_Constitution_of_1833"}]}, {"year": "1865", "text": "In Mobile, Alabama, around 300 people are killed when an ordnance depot explodes.", "html": "1865 - In <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, around 300 people are killed when an <a href=\"https://wikipedia.org/wiki/Mobile_magazine_explosion\" title=\"Mobile magazine explosion\">ordnance depot explodes</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Mobile,_Alabama\" title=\"Mobile, Alabama\">Mobile, Alabama</a>, around 300 people are killed when an <a href=\"https://wikipedia.org/wiki/Mobile_magazine_explosion\" title=\"Mobile magazine explosion\">ordnance depot explodes</a>.", "links": [{"title": "Mobile, Alabama", "link": "https://wikipedia.org/wiki/Mobile,_Alabama"}, {"title": "Mobile magazine explosion", "link": "https://wikipedia.org/wiki/Mobile_magazine_explosion"}]}, {"year": "1878", "text": "<PERSON> and <PERSON>'s comic opera H.M.S. Pina<PERSON>re opens at the Opera Comique in London.", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Comic_opera\" title=\"Comic opera\">comic opera</a> <i><a href=\"https://wikipedia.org/wiki/H.M.S._Pinafore\" title=\"H.M.S. Pinafore\">H.M.S. Pinafore</a></i> opens at the <a href=\"https://wikipedia.org/wiki/Opera_Comique\" title=\"Opera Comique\">Opera Comique</a> in London.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON> and <PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Comic_opera\" title=\"Comic opera\">comic opera</a> <i><a href=\"https://wikipedia.org/wiki/H.M.S._Pinafore\" title=\"H.M.S. Pinafore\">H.M.S. Pinafore</a></i> opens at the <a href=\"https://wikipedia.org/wiki/Opera_Comique\" title=\"Opera Comique\">Opera Comique</a> in London.", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>"}, {"title": "Comic opera", "link": "https://wikipedia.org/wiki/Comic_opera"}, {"title": "H.M.S. Pinafore", "link": "https://wikipedia.org/wiki/H.M.S._Pinafore"}, {"title": "Opera Comique", "link": "https://wikipedia.org/wiki/Opera_Comique"}]}, {"year": "1895", "text": "Playwright, poet and novelist <PERSON> is convicted of \"committing acts of gross indecency with other male persons\" and sentenced to serve two years in prison.", "html": "1895 - Playwright, poet and novelist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON> Wilde\"><PERSON></a> is convicted of \"committing acts of <a href=\"https://wikipedia.org/wiki/Labouchere_Amendment\" title=\"Labouchere Amendment\">gross indecency</a> with <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">other male persons</a>\" and sentenced to serve two years in prison.", "no_year_html": "Playwright, poet and novelist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>Wilde\" title=\"<PERSON> Wilde\"><PERSON></a> is convicted of \"committing acts of <a href=\"https://wikipedia.org/wiki/Labouchere_Amendment\" title=\"Labouchere Amendment\">gross indecency</a> with <a href=\"https://wikipedia.org/wiki/Homosexuality\" title=\"Homosexuality\">other male persons</a>\" and sentenced to serve two years in prison.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Labouchere Amendment", "link": "https://wikipedia.org/wiki/Labouchere_Amendment"}, {"title": "Homosexuality", "link": "https://wikipedia.org/wiki/Homosexuality"}]}, {"year": "1895", "text": "The Republic of Formosa is formed, with <PERSON> as its president.", "html": "1895 - The <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Republic of Formosa</a> is formed, with <a href=\"https://wikipedia.org/wiki/Tang_Jingsong\" title=\"Tang Jingsong\"><PERSON></a> as its <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Taiwan#Republic_of_Formosa_.281895.29\" title=\"List of rulers of Taiwan\">president</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Republic of Formosa</a> is formed, with <a href=\"https://wikipedia.org/wiki/Tang_Jingsong\" title=\"Tang Jingsong\"><PERSON>son<PERSON></a> as its <a href=\"https://wikipedia.org/wiki/List_of_rulers_of_Taiwan#Republic_of_Formosa_.281895.29\" title=\"List of rulers of Taiwan\">president</a>.", "links": [{"title": "Republic of Formosa", "link": "https://wikipedia.org/wiki/Republic_of_Formosa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tang_<PERSON>song"}, {"title": "List of rulers of Taiwan", "link": "https://wikipedia.org/wiki/List_of_rulers_of_Taiwan#Republic_of_Formosa_.281895.29"}]}, {"year": "1914", "text": "The House of Commons of the United Kingdom passes the Home Rule Bill for devolution in Ireland.", "html": "1914 - The <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons of the United Kingdom</a> passes <a href=\"https://wikipedia.org/wiki/Home_Rule_Act_1914\" class=\"mw-redirect\" title=\"Home Rule Act 1914\">the Home Rule Bill</a> for <a href=\"https://wikipedia.org/wiki/Devolution\" title=\"Devolution\">devolution</a> in Ireland.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons of the United Kingdom</a> passes <a href=\"https://wikipedia.org/wiki/Home_Rule_Act_1914\" class=\"mw-redirect\" title=\"Home Rule Act 1914\">the Home Rule Bill</a> for <a href=\"https://wikipedia.org/wiki/Devolution\" title=\"Devolution\">devolution</a> in Ireland.", "links": [{"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}, {"title": "Home Rule Act 1914", "link": "https://wikipedia.org/wiki/Home_Rule_Act_1914"}, {"title": "Devolution", "link": "https://wikipedia.org/wiki/Devolution"}]}, {"year": "1925", "text": "Scopes Trial: <PERSON> is indicted for teaching human evolution in Tennessee.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Scopes_Trial\" class=\"mw-redirect\" title=\"Scopes Trial\">Scopes Trial</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Scope<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted for teaching <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in Tennessee.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scopes_Trial\" class=\"mw-redirect\" title=\"Scopes Trial\">Scopes Trial</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Scope<PERSON>\" title=\"<PERSON>\"><PERSON></a> is indicted for teaching <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in Tennessee.", "links": [{"title": "Scopes Trial", "link": "https://wikipedia.org/wiki/Scopes_Trial"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Human evolution", "link": "https://wikipedia.org/wiki/Human_evolution"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON> assassinates <PERSON><PERSON><PERSON>, the head of the government of the Ukrainian People's Republic, which is in government-in-exile in Paris.", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Shol<PERSON>\"><PERSON><PERSON><PERSON></a> assassinates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the head of the government of the <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic\" title=\"Ukrainian People's Republic\">Ukrainian People's Republic</a>, which is in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">government-in-exile</a> in Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>hol<PERSON>\"><PERSON><PERSON><PERSON></a> assassinates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the head of the government of the <a href=\"https://wikipedia.org/wiki/Ukrainian_People%27s_Republic\" title=\"Ukrainian People's Republic\">Ukrainian People's Republic</a>, which is in <a href=\"https://wikipedia.org/wiki/Exile\" title=\"Exile\">government-in-exile</a> in Paris.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hol<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ukrainian People's Republic", "link": "https://wikipedia.org/wiki/Ukrainian_People%27s_Republic"}, {"title": "Exile", "link": "https://wikipedia.org/wiki/Exile"}]}, {"year": "1933", "text": "The Walt Disney Company cartoon Three Little Pigs premieres at Radio City Music Hall, featuring the hit song \"Who's Afraid of the Big Bad Wolf?\"", "html": "1933 - <a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> <i><a href=\"https://wikipedia.org/wiki/Three_Little_Pigs_(film)\" title=\"Three Little Pigs (film)\">Three Little Pigs</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a>, featuring the hit song \"<a href=\"https://wikipedia.org/wiki/Who%27s_Afraid_of_the_Big_Bad_Wolf%3F\" title=\"Who's Afraid of the Big Bad Wolf?\">Who's Afraid of the Big Bad Wolf?</a>\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Walt_Disney_Company\" title=\"The Walt Disney Company\">The Walt Disney Company</a> <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a> <i><a href=\"https://wikipedia.org/wiki/Three_Little_Pigs_(film)\" title=\"Three Little Pigs (film)\">Three Little Pigs</a></i> premieres at <a href=\"https://wikipedia.org/wiki/Radio_City_Music_Hall\" title=\"Radio City Music Hall\">Radio City Music Hall</a>, featuring the hit song \"<a href=\"https://wikipedia.org/wiki/Who%27s_Afraid_of_the_Big_Bad_Wolf%3F\" title=\"Who's Afraid of the Big Bad Wolf?\">Who's Afraid of the Big Bad Wolf?</a>\"", "links": [{"title": "The Walt Disney Company", "link": "https://wikipedia.org/wiki/The_Walt_Disney_Company"}, {"title": "Cartoon", "link": "https://wikipedia.org/wiki/Cartoon"}, {"title": "Three Little Pigs (film)", "link": "https://wikipedia.org/wiki/Three_Little_Pigs_(film)"}, {"title": "Radio City Music Hall", "link": "https://wikipedia.org/wiki/Radio_City_Music_Hall"}, {"title": "Who's <PERSON><PERSON><PERSON> of the Big Bad Wolf?", "link": "https://wikipedia.org/wiki/Who%27s_Afraid_of_the_Big_Bad_Wolf%3F"}]}, {"year": "1935", "text": "<PERSON> of Ohio State University breaks three world records and ties a fourth at the Big Ten Conference Track and Field Championships in Ann Arbor, Michigan.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ohio_State_University\" title=\"Ohio State University\">Ohio State University</a> breaks three world records and ties a fourth at the <a href=\"https://wikipedia.org/wiki/Big_Ten_Conference\" title=\"Big Ten Conference\">Big Ten Conference</a> Track and Field Championships in <a href=\"https://wikipedia.org/wiki/Ann_Arbor,_Michigan\" title=\"Ann Arbor, Michigan\">Ann Arbor, Michigan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Ohio_State_University\" title=\"Ohio State University\">Ohio State University</a> breaks three world records and ties a fourth at the <a href=\"https://wikipedia.org/wiki/Big_Ten_Conference\" title=\"Big Ten Conference\">Big Ten Conference</a> Track and Field Championships in <a href=\"https://wikipedia.org/wiki/Ann_Arbor,_Michigan\" title=\"Ann Arbor, Michigan\">Ann Arbor, Michigan</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Ohio State University", "link": "https://wikipedia.org/wiki/Ohio_State_University"}, {"title": "Big Ten Conference", "link": "https://wikipedia.org/wiki/Big_Ten_Conference"}, {"title": "Ann Arbor, Michigan", "link": "https://wikipedia.org/wiki/Ann_Arbor,_Michigan"}]}, {"year": "1938", "text": "Spanish Civil War: The bombing of Alicante kills 313 people.", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The bombing of <a href=\"https://wikipedia.org/wiki/Alicante\" title=\"Alicante\">Alicante</a> kills 313 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The bombing of <a href=\"https://wikipedia.org/wiki/Alicante\" title=\"Alicante\">Alicante</a> kills 313 people.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Alicante", "link": "https://wikipedia.org/wiki/Alicante"}]}, {"year": "1940", "text": "World War II: The German 2nd Panzer Division captures the port of Boulogne-sur-Mer; the surrender of the last French and British troops marks the end of the Battle of Boulogne.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <a href=\"https://wikipedia.org/wiki/2nd_Panzer_Division_(Wehrmacht)\" title=\"2nd Panzer Division (Wehrmacht)\">2nd Panzer Division</a> captures the port of <a href=\"https://wikipedia.org/wiki/Boulogne-sur-Mer\" title=\"Boulogne-sur-Mer\">Boulogne-sur-Mer</a>; the surrender of the last French and British troops marks the end of the <a href=\"https://wikipedia.org/wiki/Battle_of_Boulogne_(1940)\" class=\"mw-redirect\" title=\"Battle of Boulogne (1940)\">Battle of Boulogne</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The German <a href=\"https://wikipedia.org/wiki/2nd_Panzer_Division_(Wehrmacht)\" title=\"2nd Panzer Division (Wehrmacht)\">2nd Panzer Division</a> captures the port of <a href=\"https://wikipedia.org/wiki/Boulogne-sur-Mer\" title=\"Boulogne-sur-Mer\">Boulogne-sur-Mer</a>; the surrender of the last French and British troops marks the end of the <a href=\"https://wikipedia.org/wiki/Battle_of_Boulogne_(1940)\" class=\"mw-redirect\" title=\"Battle of Boulogne (1940)\">Battle of Boulogne</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "2nd Panzer Division (Wehrmacht)", "link": "https://wikipedia.org/wiki/2nd_Panzer_Division_(Wehrmacht)"}, {"title": "Boulogne-sur-Mer", "link": "https://wikipedia.org/wiki/Boulogne-sur-Mer"}, {"title": "Battle of Boulogne (1940)", "link": "https://wikipedia.org/wiki/Battle_of_Boulogne_(1940)"}]}, {"year": "1946", "text": "The parliament of Transjordan makes <PERSON> of Jordan their Emir.", "html": "1946 - The parliament of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a> makes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> their <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\"><PERSON>ir</a>.", "no_year_html": "The parliament of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Transjordan</a> makes <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan\" title=\"<PERSON> of Jordan\"><PERSON> of Jordan</a> their <a href=\"https://wikipedia.org/wiki/Emir\" title=\"Emir\"><PERSON>ir</a>.", "links": [{"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Jordan"}, {"title": "<PERSON>ir", "link": "https://wikipedia.org/wiki/Emir"}]}, {"year": "1953", "text": "Nuclear weapons testing: At the Nevada Test Site, the United States conducts its first and only nuclear artillery test.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: At the <a href=\"https://wikipedia.org/wiki/Nevada_Test_Site\" title=\"Nevada Test Site\">Nevada Test Site</a>, the United States <a href=\"https://wikipedia.org/wiki/Upshot-Knothole_Grable\" title=\"Upshot-Knothole Grable\">conducts its first and only</a> <a href=\"https://wikipedia.org/wiki/Nuclear_artillery\" title=\"Nuclear artillery\">nuclear artillery</a> test.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuclear_weapons_testing\" title=\"Nuclear weapons testing\">Nuclear weapons testing</a>: At the <a href=\"https://wikipedia.org/wiki/Nevada_Test_Site\" title=\"Nevada Test Site\">Nevada Test Site</a>, the United States <a href=\"https://wikipedia.org/wiki/Upshot-Knothole_Grable\" title=\"Upshot-Knothole Grable\">conducts its first and only</a> <a href=\"https://wikipedia.org/wiki/Nuclear_artillery\" title=\"Nuclear artillery\">nuclear artillery</a> test.", "links": [{"title": "Nuclear weapons testing", "link": "https://wikipedia.org/wiki/Nuclear_weapons_testing"}, {"title": "Nevada Test Site", "link": "https://wikipedia.org/wiki/Nevada_Test_Site"}, {"title": "Upshot-Knothole Grable", "link": "https://wikipedia.org/wiki/Upshot-Knothole_Grable"}, {"title": "Nuclear artillery", "link": "https://wikipedia.org/wiki/Nuclear_artillery"}]}, {"year": "1953", "text": "The first public television station in the United States officially begins broadcasting as KUHT from the campus of the University of Houston.", "html": "1953 - The first <a href=\"https://wikipedia.org/wiki/Public_broadcasting\" title=\"Public broadcasting\">public television station</a> in the United States officially begins broadcasting as <a href=\"https://wikipedia.org/wiki/KUHT\" title=\"KUHT\">KUHT</a> from the campus of the <a href=\"https://wikipedia.org/wiki/University_of_Houston\" title=\"University of Houston\">University of Houston</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Public_broadcasting\" title=\"Public broadcasting\">public television station</a> in the United States officially begins broadcasting as <a href=\"https://wikipedia.org/wiki/KUHT\" title=\"KUHT\">KUHT</a> from the campus of the <a href=\"https://wikipedia.org/wiki/University_of_Houston\" title=\"University of Houston\">University of Houston</a>.", "links": [{"title": "Public broadcasting", "link": "https://wikipedia.org/wiki/Public_broadcasting"}, {"title": "KUHT", "link": "https://wikipedia.org/wiki/KUHT"}, {"title": "University of Houston", "link": "https://wikipedia.org/wiki/University_of_Houston"}]}, {"year": "1955", "text": "In the United States, a night-time F5 tornado strikes the small city of Udall, Kansas as part of a larger outbreak across the Great Plains, killing 80 and injuring 273. It is the deadliest tornado to ever occur in the state and the 23rd deadliest in the U.S.", "html": "1955 - In the United States, a night-time <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> strikes the small city of <a href=\"https://wikipedia.org/wiki/Udall,_Kansas\" title=\"Udall, Kansas\">Udall, Kansas</a> as part of <a href=\"https://wikipedia.org/wiki/1955_Great_Plains_tornado_outbreak\" title=\"1955 Great Plains tornado outbreak\">a larger outbreak across the Great Plains</a>, killing 80 and injuring 273. It is the deadliest tornado to ever occur in the state and the 23rd deadliest in the U.S.", "no_year_html": "In the United States, a night-time <a href=\"https://wikipedia.org/wiki/Fujita_scale\" title=\"Fujita scale\">F5</a> <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a> strikes the small city of <a href=\"https://wikipedia.org/wiki/Udall,_Kansas\" title=\"Udall, Kansas\">Udall, Kansas</a> as part of <a href=\"https://wikipedia.org/wiki/1955_Great_Plains_tornado_outbreak\" title=\"1955 Great Plains tornado outbreak\">a larger outbreak across the Great Plains</a>, killing 80 and injuring 273. It is the deadliest tornado to ever occur in the state and the 23rd deadliest in the U.S.", "links": [{"title": "Fujita scale", "link": "https://wikipedia.org/wiki/Fujita_scale"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Udall, Kansas", "link": "https://wikipedia.org/wiki/Udall,_Kansas"}, {"title": "1955 Great Plains tornado outbreak", "link": "https://wikipedia.org/wiki/1955_Great_Plains_tornado_outbreak"}]}, {"year": "1955", "text": "First ascent of Mount Kangchenjunga: On the British Kangchenjunga expedition led by <PERSON>, <PERSON> and <PERSON> reach the summit of the third-highest mountain in the world (8,586 meters); <PERSON> and <PERSON> join them the following day.", "html": "1955 - First ascent of Mount <a href=\"https://wikipedia.org/wiki/Kangchenjunga\" title=\"Kangchenjunga\">Kangchenjunga</a>: On the <a href=\"https://wikipedia.org/wiki/1955_British_Kangchenjunga_expedition\" title=\"1955 British Kangchenjunga expedition\">British Kangchenjunga expedition</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)\" title=\"<PERSON> (climber)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/George_Band\" title=\"George Band\"><PERSON></a> reach the summit of the third-highest mountain in the world (8,586 meters); <PERSON> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> join them the following day.", "no_year_html": "First ascent of Mount <a href=\"https://wikipedia.org/wiki/Kangchenjunga\" title=\"Kangchenjunga\">Kangchenjunga</a>: On the <a href=\"https://wikipedia.org/wiki/1955_British_Kangchenjunga_expedition\" title=\"1955 British Kangchenjunga expedition\">British Kangchenjunga expedition</a> led by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mountaineer)\" title=\"<PERSON> (mountaineer)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)\" title=\"<PERSON> (climber)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Band\"><PERSON></a> reach the summit of the third-highest mountain in the world (8,586 meters); <PERSON> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> join them the following day.", "links": [{"title": "Kangchenjunga", "link": "https://wikipedia.org/wiki/Kangchenjunga"}, {"title": "1955 British Kangchenjunga expedition", "link": "https://wikipedia.org/wiki/1955_British_Kangchenjunga_expedition"}, {"title": "<PERSON> (mountaineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mountaineer)"}, {"title": "<PERSON> (climber)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(climber)"}, {"title": "George Band", "link": "https://wikipedia.org/wiki/<PERSON>_Band"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "Apollo program: U.S. President <PERSON> announces, before a special joint session of the U.S. Congress, his goal to initiate a project to put a \"man on the Moon\" before the end of the decade.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces, before a special joint session of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>, his goal to initiate a project to put a \"man on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>\" before the end of the decade.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces, before a special joint session of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a>, his goal to initiate a project to put a \"man on the <a href=\"https://wikipedia.org/wiki/Moon\" title=\"Moon\">Moon</a>\" before the end of the decade.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Moon", "link": "https://wikipedia.org/wiki/Moon"}]}, {"year": "1963", "text": "The Organisation of African Unity is established in Addis Ababa, Ethiopia.", "html": "1963 - The <a href=\"https://wikipedia.org/wiki/Organisation_of_African_Unity\" title=\"Organisation of African Unity\">Organisation of African Unity</a> is established in <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a>, Ethiopia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Organisation_of_African_Unity\" title=\"Organisation of African Unity\">Organisation of African Unity</a> is established in <a href=\"https://wikipedia.org/wiki/Addis_Ababa\" title=\"Addis Ababa\">Addis Ababa</a>, Ethiopia.", "links": [{"title": "Organisation of African Unity", "link": "https://wikipedia.org/wiki/Organisation_of_African_Unity"}, {"title": "Addis A<PERSON>ba", "link": "https://wikipedia.org/wiki/Addis_Ababa"}]}, {"year": "1966", "text": "Explorer program: Explorer 32 launches.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_32\" title=\"Explorer 32\">Explorer 32</a></i> launches.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Explorer_program\" class=\"mw-redirect\" title=\"Explorer program\">Explorer program</a>: <i><a href=\"https://wikipedia.org/wiki/Explorer_32\" title=\"Explorer 32\">Explorer 32</a></i> launches.", "links": [{"title": "Explorer program", "link": "https://wikipedia.org/wiki/Explorer_program"}, {"title": "Explorer 32", "link": "https://wikipedia.org/wiki/Explorer_32"}]}, {"year": "1968", "text": "The Gateway Arch in St. Louis, Missouri, is dedicated.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Gateway_Arch\" title=\"Gateway Arch\">Gateway Arch</a> in <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a>, Missouri, is dedicated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Gateway_Arch\" title=\"Gateway Arch\">Gateway Arch</a> in <a href=\"https://wikipedia.org/wiki/St._Louis\" title=\"St. Louis\">St. Louis</a>, Missouri, is dedicated.", "links": [{"title": "Gateway Arch", "link": "https://wikipedia.org/wiki/Gateway_Arch"}, {"title": "St. Louis", "link": "https://wikipedia.org/wiki/St._Louis"}]}, {"year": "1973", "text": "In protest against the dictatorship in Greece, the captain and crew on Greek naval destroyer <PERSON><PERSON><PERSON> mutiny and refuse to return to Greece, instead anchoring at Fiumicino, Italy.", "html": "1973 - In protest against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">dictatorship in Greece</a>, the captain and crew on Greek naval destroyer <a href=\"https://wikipedia.org/wiki/USS_Charrette\" title=\"USS Charrette\"><i><PERSON><PERSON><PERSON></i></a> mutiny and refuse to return to Greece, instead anchoring at <a href=\"https://wikipedia.org/wiki/Fiumicino\" title=\"Fiumicino\">Fiumicino</a>, Italy.", "no_year_html": "In protest against the <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">dictatorship in Greece</a>, the captain and crew on Greek naval destroyer <a href=\"https://wikipedia.org/wiki/USS_Charrette\" title=\"USS Charrette\"><i><PERSON><PERSON><PERSON></i></a> mutiny and refuse to return to Greece, instead anchoring at <a href=\"https://wikipedia.org/wiki/Fiumicino\" title=\"Fiumicino\">Fiumicino</a>, Italy.", "links": [{"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}, {"title": "USS Charrette", "link": "https://wikipedia.org/wiki/USS_Charrette"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fiumicino"}]}, {"year": "1977", "text": "Star Wars (retroactively titled Star Wars: Episode IV - A New Hope) is released in US theaters.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Star_Wars_(film)\" title=\"Star Wars (film)\"><i>Star Wars</i></a> (retroactively titled <i>Star Wars: Episode IV - A New Hope</i>) is released in US theaters.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Star_Wars_(film)\" title=\"Star Wars (film)\"><i>Star Wars</i></a> (retroactively titled <i>Star Wars: Episode IV - A New Hope</i>) is released in US theaters.", "links": [{"title": "Star Wars (film)", "link": "https://wikipedia.org/wiki/Star_Wars_(film)"}]}, {"year": "1977", "text": "The Chinese government removes a decade-old ban on <PERSON>'s work, effectively ending the Cultural Revolution started in 1966.", "html": "1977 - The Chinese government removes a decade-old ban on <a href=\"https://wikipedia.org/wiki/William_Shakespeare\" title=\"William Shakespeare\"><PERSON></a>'s work, effectively ending the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a> started in 1966.", "no_year_html": "The Chinese government removes a decade-old ban on <a href=\"https://wikipedia.org/wiki/William_Shakespeare\" title=\"William Shakespeare\"><PERSON></a>'s work, effectively ending the <a href=\"https://wikipedia.org/wiki/Cultural_Revolution\" title=\"Cultural Revolution\">Cultural Revolution</a> started in 1966.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cultural Revolution", "link": "https://wikipedia.org/wiki/Cultural_Revolution"}]}, {"year": "1978", "text": "The first of a series of bombings orchestrated by the <PERSON><PERSON><PERSON> detonates at Northwestern University resulting in minor injuries.", "html": "1978 - The first of a series of bombings orchestrated by the <a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a> detonates at <a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a> resulting in minor injuries.", "no_year_html": "The first of a series of bombings orchestrated by the <a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a> detonates at <a href=\"https://wikipedia.org/wiki/Northwestern_University\" title=\"Northwestern University\">Northwestern University</a> resulting in minor injuries.", "links": [{"title": "Unabomber", "link": "https://wikipedia.org/wiki/Unabomber"}, {"title": "Northwestern University", "link": "https://wikipedia.org/wiki/Northwestern_University"}]}, {"year": "1979", "text": "<PERSON>, a convicted murderer, is executed in Florida; he is the first person to be executed in the state after the reintroduction of capital punishment in 1976.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a convicted murderer, is executed in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>; he is the first person to be executed in the state after the reintroduction of capital punishment in 1976.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a convicted murderer, is executed in <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>; he is the first person to be executed in the state after the reintroduction of capital punishment in 1976.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}]}, {"year": "1979", "text": "American Airlines Flight 191: A McDonnell Douglas DC-10 crashes during takeoff at O'Hare International Airport, Chicago, killing all 271 on board and two people on the ground.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_191\" title=\"American Airlines Flight 191\">American Airlines Flight 191</a>: A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-10\" title=\"McDonnell Douglas DC-10\">McDonnell Douglas DC-10</a> crashes during takeoff at <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a>, Chicago, killing all 271 on board and two people on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_191\" title=\"American Airlines Flight 191\">American Airlines Flight 191</a>: A <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_DC-10\" title=\"McDonnell Douglas DC-10\">McDonnell Douglas DC-10</a> crashes during takeoff at <a href=\"https://wikipedia.org/wiki/O%27Hare_International_Airport\" title=\"O'Hare International Airport\">O'Hare International Airport</a>, Chicago, killing all 271 on board and two people on the ground.", "links": [{"title": "American Airlines Flight 191", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_191"}, {"title": "McDonnell Douglas DC-10", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_DC-10"}, {"title": "O'Hare International Airport", "link": "https://wikipedia.org/wiki/O%27Hare_International_Airport"}]}, {"year": "1981", "text": "In Riyadh, the Gulf Cooperation Council is created between Bahrain, Kuwait, Oman, Qatar, Saudi Arabia and the United Arab Emirates.", "html": "1981 - In <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, the <a href=\"https://wikipedia.org/wiki/Gulf_Cooperation_Council\" title=\"Gulf Cooperation Council\">Gulf Cooperation Council</a> is created between <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>, <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>, <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> and the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Riyadh\" title=\"Riyadh\">Riyadh</a>, the <a href=\"https://wikipedia.org/wiki/Gulf_Cooperation_Council\" title=\"Gulf Cooperation Council\">Gulf Cooperation Council</a> is created between <a href=\"https://wikipedia.org/wiki/Bahrain\" title=\"Bahrain\">Bahrain</a>, <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a>, <a href=\"https://wikipedia.org/wiki/Oman\" title=\"Oman\">Oman</a>, <a href=\"https://wikipedia.org/wiki/Qatar\" title=\"Qatar\">Qatar</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a> and the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a>.", "links": [{"title": "Riyadh", "link": "https://wikipedia.org/wiki/Riyadh"}, {"title": "Gulf Cooperation Council", "link": "https://wikipedia.org/wiki/Gulf_Cooperation_Council"}, {"title": "Bahrain", "link": "https://wikipedia.org/wiki/Bahrain"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}, {"title": "Oman", "link": "https://wikipedia.org/wiki/Oman"}, {"title": "Qatar", "link": "https://wikipedia.org/wiki/Qatar"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}]}, {"year": "1982", "text": "Falklands War: HMS Coventry is sunk by Argentine Air Force A-4 Skyhawks.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/HMS_Coventry_(D118)\" title=\"HMS Coventry (D118)\">HMS <i>Coventry</i></a> is sunk by <a href=\"https://wikipedia.org/wiki/Argentine_Air_Force\" title=\"Argentine Air Force\">Argentine Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_A-4_Skyhawk\" title=\"Douglas A-4 Skyhawk\">A-4 Skyhawks</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: <a href=\"https://wikipedia.org/wiki/HMS_Coventry_(D118)\" title=\"HMS Coventry (D118)\">HMS <i>Coventry</i></a> is sunk by <a href=\"https://wikipedia.org/wiki/Argentine_Air_Force\" title=\"Argentine Air Force\">Argentine Air Force</a> <a href=\"https://wikipedia.org/wiki/Douglas_A-4_Skyhawk\" title=\"Douglas A-4 Skyhawk\">A-4 Skyhawks</a>.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "HMS Coventry (D118)", "link": "https://wikipedia.org/wiki/HMS_Coventry_(D118)"}, {"title": "Argentine Air Force", "link": "https://wikipedia.org/wiki/Argentine_Air_Force"}, {"title": "Douglas A-4 Skyhawk", "link": "https://wikipedia.org/wiki/Douglas_A-4_Skyhawk"}]}, {"year": "1985", "text": "Bangladesh is hit by a tropical cyclone and storm surge, which kills approximately 10,000 people.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> is hit by a <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical cyclone</a> and <a href=\"https://wikipedia.org/wiki/Storm_surge\" title=\"Storm surge\">storm surge</a>, which kills approximately 10,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bangladesh\" title=\"Bangladesh\">Bangladesh</a> is hit by a <a href=\"https://wikipedia.org/wiki/Tropical_cyclone\" title=\"Tropical cyclone\">tropical cyclone</a> and <a href=\"https://wikipedia.org/wiki/Storm_surge\" title=\"Storm surge\">storm surge</a>, which kills approximately 10,000 people.", "links": [{"title": "Bangladesh", "link": "https://wikipedia.org/wiki/Bangladesh"}, {"title": "Tropical cyclone", "link": "https://wikipedia.org/wiki/Tropical_cyclone"}, {"title": "Storm surge", "link": "https://wikipedia.org/wiki/Storm_surge"}]}, {"year": "1986", "text": "The Hands Across America event takes place.", "html": "1986 - The <a href=\"https://wikipedia.org/wiki/Hands_Across_America\" title=\"Hands Across America\">Hands Across America</a> event takes place.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hands_Across_America\" title=\"Hands Across America\">Hands Across America</a> event takes place.", "links": [{"title": "Hands Across America", "link": "https://wikipedia.org/wiki/Hands_Across_America"}]}, {"year": "1997", "text": "A military coup in Sierra Leone replaces President <PERSON> with Major <PERSON>.", "html": "1997 - A <a href=\"https://wikipedia.org/wiki/Military_coup\" class=\"mw-redirect\" title=\"Military coup\">military coup</a> in <a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> replaces President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with Major <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Military_coup\" class=\"mw-redirect\" title=\"Military coup\">military coup</a> in <a href=\"https://wikipedia.org/wiki/Sierra_Leone\" title=\"Sierra Leone\">Sierra Leone</a> replaces President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> with Major <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Military coup", "link": "https://wikipedia.org/wiki/Military_coup"}, {"title": "Sierra Leone", "link": "https://wikipedia.org/wiki/Sierra_Leone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "The United States House of Representatives releases the Cox Report which details China's nuclear espionage against the U.S. over the prior two decades.", "html": "1999 - The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> releases the <i><a href=\"https://wikipedia.org/wiki/Cox_Report\" title=\"Cox Report\">Cox Report</a></i> which details <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>'s nuclear espionage against the U.S. over the prior two decades.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> releases the <i><a href=\"https://wikipedia.org/wiki/Cox_Report\" title=\"Cox Report\">Cox Report</a></i> which details <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a>'s nuclear espionage against the U.S. over the prior two decades.", "links": [{"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Cox Report", "link": "https://wikipedia.org/wiki/Cox_Report"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}]}, {"year": "2000", "text": "Liberation Day of Lebanon: Israel withdraws its army from Lebanese territory (with the exception of the disputed Shebaa farms zone) 18 years after the invasion of 1982.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Liberation_Day_(Lebanon)\" class=\"mw-redirect\" title=\"Liberation Day (Lebanon)\">Liberation Day of Lebanon</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> withdraws its army from Lebanese territory (with the exception of the disputed <a href=\"https://wikipedia.org/wiki/Shebaa_farms\" class=\"mw-redirect\" title=\"Shebaa farms\">Shebaa farms</a> zone) 18 years after the <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">invasion of 1982</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liberation_Day_(Lebanon)\" class=\"mw-redirect\" title=\"Liberation Day (Lebanon)\">Liberation Day of Lebanon</a>: <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> withdraws its army from Lebanese territory (with the exception of the disputed <a href=\"https://wikipedia.org/wiki/Shebaa_farms\" class=\"mw-redirect\" title=\"Shebaa farms\">Shebaa farms</a> zone) 18 years after the <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">invasion of 1982</a>.", "links": [{"title": "Liberation Day (Lebanon)", "link": "https://wikipedia.org/wiki/Liberation_Day_(Lebanon)"}, {"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "Shebaa farms", "link": "https://wikipedia.org/wiki/She<PERSON>a_farms"}, {"title": "1982 Lebanon War", "link": "https://wikipedia.org/wiki/1982_Lebanon_War"}]}, {"year": "2001", "text": "<PERSON> becomes the first blind person to reach the summit of Mount Everest, in the Himalayas, with Dr. <PERSON>.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first blind person to reach the summit of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, in the <a href=\"https://wikipedia.org/wiki/Himalayas\" title=\"Himalayas\">Himalayas</a>, with Dr. <PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first blind person to reach the summit of <a href=\"https://wikipedia.org/wiki/Mount_Everest\" title=\"Mount Everest\">Mount Everest</a>, in the <a href=\"https://wikipedia.org/wiki/Himalayas\" title=\"Himalayas\">Himalayas</a>, with Dr. <PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Mount Everest", "link": "https://wikipedia.org/wiki/Mount_Everest"}, {"title": "Himalayas", "link": "https://wikipedia.org/wiki/Himalayas"}]}, {"year": "2002", "text": "China Airlines Flight 611 disintegrates in mid-air and crashes into the Taiwan Strait, with the loss of all 225 people on board.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_611\" title=\"China Airlines Flight 611\">China Airlines Flight 611</a> disintegrates in mid-air and crashes into the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Taiwan Strait</a>, with the loss of all 225 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/China_Airlines_Flight_611\" title=\"China Airlines Flight 611\">China Airlines Flight 611</a> disintegrates in mid-air and crashes into the <a href=\"https://wikipedia.org/wiki/Taiwan_Strait\" title=\"Taiwan Strait\">Taiwan Strait</a>, with the loss of all 225 people on board.", "links": [{"title": "China Airlines Flight 611", "link": "https://wikipedia.org/wiki/China_Airlines_Flight_611"}, {"title": "Taiwan Strait", "link": "https://wikipedia.org/wiki/Taiwan_Strait"}]}, {"year": "2008", "text": "NASA's Phoenix lander touches down in the Green Valley region of Mars to search for environments suitable for water and microbial life.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <i><a href=\"https://wikipedia.org/wiki/Phoenix_(spacecraft)\" title=\"<PERSON> (spacecraft)\">Phoenix</a></i> lander touches down in the <a href=\"https://wikipedia.org/wiki/Green_Valley_(Mars)\" title=\"Green Valley (Mars)\">Green Valley</a> region of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> to search for environments suitable for water and <a href=\"https://wikipedia.org/wiki/Life_on_Mars\" title=\"Life on Mars\">microbial life</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <i><a href=\"https://wikipedia.org/wiki/Phoenix_(spacecraft)\" title=\"Phoenix (spacecraft)\">Phoenix</a></i> lander touches down in the <a href=\"https://wikipedia.org/wiki/Green_Valley_(Mars)\" title=\"Green Valley (Mars)\">Green Valley</a> region of <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> to search for environments suitable for water and <a href=\"https://wikipedia.org/wiki/Life_on_Mars\" title=\"Life on Mars\">microbial life</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Phoenix (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "Green Valley (Mars)", "link": "https://wikipedia.org/wiki/Green_Valley_(Mars)"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}, {"title": "Life on Mars", "link": "https://wikipedia.org/wiki/Life_on_Mars"}]}, {"year": "2009", "text": "North Korea allegedly tests its second nuclear device, after which Pyongyang also conducts several missile tests, building tensions in the international community.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> allegedly tests <a href=\"https://wikipedia.org/wiki/2009_North_Korean_nuclear_test\" title=\"2009 North Korean nuclear test\">its second nuclear device</a>, after which Pyongyang also conducts several missile tests, building tensions in the international community.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> allegedly tests <a href=\"https://wikipedia.org/wiki/2009_North_Korean_nuclear_test\" title=\"2009 North Korean nuclear test\">its second nuclear device</a>, after which Pyongyang also conducts several missile tests, building tensions in the international community.", "links": [{"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "2009 North Korean nuclear test", "link": "https://wikipedia.org/wiki/2009_North_Korean_nuclear_test"}]}, {"year": "2011", "text": "<PERSON><PERSON> Winfrey airs her last show, ending her 25-year run of The Oprah Winfrey Show.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Oprah_Winfrey\" title=\"Oprah Winfrey\"><PERSON><PERSON> Winfrey</a> airs her last show, ending her 25-year run of <i><a href=\"https://wikipedia.org/wiki/The_Oprah_Winfrey_Show\" title=\"The Oprah Winfrey Show\">The Oprah Winfrey Show</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oprah_Winfrey\" title=\"Oprah Winfrey\"><PERSON><PERSON> Winfrey</a> airs her last show, ending her 25-year run of <i><a href=\"https://wikipedia.org/wiki/The_Oprah_Winfrey_Show\" title=\"The Oprah Winfrey Show\">The Oprah Winfrey Show</a></i>.", "links": [{"title": "<PERSON>rah Winfrey", "link": "https://wikipedia.org/wiki/Oprah_Winfrey"}, {"title": "The Oprah Winfrey Show", "link": "https://wikipedia.org/wiki/The_Oprah_Winfrey_Show"}]}, {"year": "2012", "text": "The SpaceX Dragon 1 becomes the first commercial spacecraft to successfully rendezvous and berth with the International Space Station.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/SpaceX_Dragon_1\" title=\"SpaceX Dragon 1\">SpaceX Dragon 1</a> becomes the <a href=\"https://wikipedia.org/wiki/COTS_Demo_Flight_2\" class=\"mw-redirect\" title=\"COTS Demo Flight 2\">first commercial spacecraft</a> to successfully rendezvous and berth with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SpaceX_Dragon_1\" title=\"SpaceX Dragon 1\">SpaceX Dragon 1</a> becomes the <a href=\"https://wikipedia.org/wiki/COTS_Demo_Flight_2\" class=\"mw-redirect\" title=\"COTS Demo Flight 2\">first commercial spacecraft</a> to successfully rendezvous and berth with the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>.", "links": [{"title": "SpaceX Dragon 1", "link": "https://wikipedia.org/wiki/SpaceX_Dragon_1"}, {"title": "COTS Demo Flight 2", "link": "https://wikipedia.org/wiki/COTS_Demo_Flight_2"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}]}, {"year": "2013", "text": "Suspected Maoist rebels kill at least 28 people and injure 32 others in an attack on a convoy of Indian National Congress politicians in Chhattisgarh, India.", "html": "2013 - Suspected <a href=\"https://wikipedia.org/wiki/Naxalite\" class=\"mw-redirect\" title=\"Naxalite\">Maoist rebels</a> kill at least 28 people and injure 32 others in <a href=\"https://wikipedia.org/wiki/2013_Naxal_attack_in_Darbha_valley\" title=\"2013 Naxal attack in Darbha valley\">an attack</a> on a convoy of <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> politicians in <a href=\"https://wikipedia.org/wiki/Chhattisgarh\" title=\"Chhattisgarh\">Chhattisgarh</a>, India.", "no_year_html": "Suspected <a href=\"https://wikipedia.org/wiki/Naxalite\" class=\"mw-redirect\" title=\"Naxalite\">Maoist rebels</a> kill at least 28 people and injure 32 others in <a href=\"https://wikipedia.org/wiki/2013_Naxal_attack_in_Darbha_valley\" title=\"2013 Naxal attack in Darbha valley\">an attack</a> on a convoy of <a href=\"https://wikipedia.org/wiki/Indian_National_Congress\" title=\"Indian National Congress\">Indian National Congress</a> politicians in <a href=\"https://wikipedia.org/wiki/Chhattisgarh\" title=\"Chhattisgarh\">Chhattisgarh</a>, India.", "links": [{"title": "Naxalite", "link": "https://wikipedia.org/wiki/Naxalite"}, {"title": "2013 Naxal attack in Darbha valley", "link": "https://wikipedia.org/wiki/2013_Naxal_attack_in_Darbha_valley"}, {"title": "Indian National Congress", "link": "https://wikipedia.org/wiki/Indian_National_Congress"}, {"title": "Chhattisgarh", "link": "https://wikipedia.org/wiki/Chhattisgarh"}]}, {"year": "2013", "text": "A gas cylinder explodes on a school bus in the Pakistani city of Gujrat, killing at least 18 people.", "html": "2013 - A <a href=\"https://wikipedia.org/wiki/Gas_cylinder\" title=\"Gas cylinder\">gas cylinder</a> <a href=\"https://wikipedia.org/wiki/2013_Pakistan_gas_bus_explosion\" title=\"2013 Pakistan gas bus explosion\">explodes</a> on a school bus in the Pakistani city of <a href=\"https://wikipedia.org/wiki/Gujrat_City\" class=\"mw-redirect\" title=\"Gujrat City\">Gujrat</a>, killing at least 18 people.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Gas_cylinder\" title=\"Gas cylinder\">gas cylinder</a> <a href=\"https://wikipedia.org/wiki/2013_Pakistan_gas_bus_explosion\" title=\"2013 Pakistan gas bus explosion\">explodes</a> on a school bus in the Pakistani city of <a href=\"https://wikipedia.org/wiki/Gujrat_City\" class=\"mw-redirect\" title=\"Gujrat City\">Gujrat</a>, killing at least 18 people.", "links": [{"title": "Gas cylinder", "link": "https://wikipedia.org/wiki/Gas_cylinder"}, {"title": "2013 Pakistan gas bus explosion", "link": "https://wikipedia.org/wiki/2013_Pakistan_gas_bus_explosion"}, {"title": "Gujrat City", "link": "https://wikipedia.org/wiki/Gujrat_City"}]}, {"year": "2018", "text": "The General Data Protection Regulation (GDPR) becomes enforceable in the European Union.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/General_Data_Protection_Regulation\" title=\"General Data Protection Regulation\">General Data Protection Regulation</a> (GDPR) becomes enforceable in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Data_Protection_Regulation\" title=\"General Data Protection Regulation\">General Data Protection Regulation</a> (GDPR) becomes enforceable in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "General Data Protection Regulation", "link": "https://wikipedia.org/wiki/General_Data_Protection_Regulation"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2018", "text": "Ireland votes to repeal the Eighth Amendment of their constitution that prohibits abortion in all but a few cases, choosing to replace it with the Thirty-sixth Amendment of the Constitution of Ireland.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> votes to repeal the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_of_the_Constitution_of_Ireland\" title=\"Eighth Amendment of the Constitution of Ireland\">Eighth Amendment of their constitution</a> that prohibits <a href=\"https://wikipedia.org/wiki/Abortion_in_the_Republic_of_Ireland\" title=\"Abortion in the Republic of Ireland\">abortion</a> in all but a few cases, choosing to replace it with the <a href=\"https://wikipedia.org/wiki/Thirty-sixth_Amendment_of_the_Constitution_of_Ireland\" title=\"Thirty-sixth Amendment of the Constitution of Ireland\">Thirty-sixth Amendment of the Constitution of Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Ireland</a> votes to repeal the <a href=\"https://wikipedia.org/wiki/Eighth_Amendment_of_the_Constitution_of_Ireland\" title=\"Eighth Amendment of the Constitution of Ireland\">Eighth Amendment of their constitution</a> that prohibits <a href=\"https://wikipedia.org/wiki/Abortion_in_the_Republic_of_Ireland\" title=\"Abortion in the Republic of Ireland\">abortion</a> in all but a few cases, choosing to replace it with the <a href=\"https://wikipedia.org/wiki/Thirty-sixth_Amendment_of_the_Constitution_of_Ireland\" title=\"Thirty-sixth Amendment of the Constitution of Ireland\">Thirty-sixth Amendment of the Constitution of Ireland</a>.", "links": [{"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}, {"title": "Eighth Amendment of the Constitution of Ireland", "link": "https://wikipedia.org/wiki/Eighth_Amendment_of_the_Constitution_of_Ireland"}, {"title": "Abortion in the Republic of Ireland", "link": "https://wikipedia.org/wiki/Abortion_in_the_Republic_of_Ireland"}, {"title": "Thirty-sixth Amendment of the Constitution of Ireland", "link": "https://wikipedia.org/wiki/Thirty-sixth_Amendment_of_the_Constitution_of_Ireland"}]}], "Births": [{"year": "1048", "text": "Emperor <PERSON><PERSON> of Song (d. 1085)", "html": "1048 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON><PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1085)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song\" title=\"Emperor <PERSON> of Song\">Emperor <PERSON><PERSON> of Song</a> (d. 1085)", "links": [{"title": "Emperor <PERSON><PERSON> of Song", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Song"}]}, {"year": "1320", "text": "<PERSON><PERSON><PERSON>, Mongolian emperor (d. 1370)", "html": "1320 - <a href=\"https://wikipedia.org/wiki/Toghon_Tem%C3%BCr\" title=\"Toghon Temür\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toghon_Tem%C3%BCr\" title=\"Toghon Temür\"><PERSON><PERSON><PERSON></a>, Mongolian emperor (d. 1370)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toghon_Tem%C3%BCr"}]}, {"year": "1334", "text": "Emperor <PERSON><PERSON><PERSON> of Japan (d. 1398)", "html": "1334 - <a href=\"https://wikipedia.org/wiki/Emperor_Suk%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1398)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>k%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON></a> of Japan (d. 1398)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Suk%C5%8D"}]}, {"year": "1416", "text": "<PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg (d. 1480)", "html": "1416 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(%22James%22),_Count_of_Lichtenburg\" class=\"mw-redirect\" title='<PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg'><PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg</a> (d. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(%22James%22),_Count_of_Lichtenburg\" class=\"mw-redirect\" title='<PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg'><PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg</a> (d. 1480)", "links": [{"title": "<PERSON><PERSON> (\"<PERSON>\"), Count of Lichtenburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(%22James%22),_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1417", "text": "<PERSON> Cleves, Duchess consort regent of Guelders (d. 1479)", "html": "1417 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Clev<PERSON>_(1417%E2%80%931479)\" title=\"<PERSON> of Cleves (1417-1479)\"><PERSON> of Cleves</a>, Duchess consort regent of Guelders (d. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lev<PERSON>_(1417%E2%80%931479)\" title=\"<PERSON> of Cleves (1417-1479)\"><PERSON> of Cleves</a>, Duchess consort regent of Guelders (d. 1479)", "links": [{"title": "<PERSON> Cleves (1417-1479)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1417%E2%80%931479)"}]}, {"year": "1550", "text": "<PERSON><PERSON><PERSON>, Italian saint and nurse (d. 1614)", "html": "1550 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian saint and nurse (d. 1614)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Italian saint and nurse (d. 1614)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON>, French missionary and saint (d. 1649)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, French missionary and saint (d. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)\" title=\"<PERSON> (missionary)\"><PERSON></a>, French missionary and saint (d. 1649)", "links": [{"title": "<PERSON> (missionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(missionary)"}]}, {"year": "1661", "text": "<PERSON>, Polish-French historian and philosopher (d. 1737)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-French historian and philosopher (d. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-French historian and philosopher (d. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, 3rd Earl of Bute, Scottish politician, Prime Minister of Great Britain (d. 1792)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bute\" title=\"<PERSON>, 3rd Earl of Bute\"><PERSON>, 3rd Earl of Bute</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bute\" title=\"<PERSON>, 3rd Earl of Bute\"><PERSON>, 3rd Earl of Bute</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1792)", "links": [{"title": "<PERSON>, 3rd Earl of Bute", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Bute"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1725", "text": "<PERSON>, American politician, 31st and 33rd Governor of the Colony of Rhode Island and Providence Plantations (d. 1776)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American politician, 31st and 33rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island\" title=\"List of colonial governors of Rhode Island\">Governor of the Colony of Rhode Island and Providence Plantations</a> (d. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Rhode_Island_politician)\" title=\"<PERSON> (Rhode Island politician)\"><PERSON></a>, American politician, 31st and 33rd <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island\" title=\"List of colonial governors of Rhode Island\">Governor of the Colony of Rhode Island and Providence Plantations</a> (d. 1776)", "links": [{"title": "<PERSON> (Rhode Island politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Rhode_Island_politician)"}, {"title": "List of colonial governors of Rhode Island", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_Rhode_Island"}]}, {"year": "1783", "text": "<PERSON>, American farmer and politician, 12th Speaker of the United States House of Representatives (d. 1841)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 12th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American farmer and politician, 12th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1791", "text": "<PERSON>, Vietnamese emperor (d. 1841)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/Minh_M%E1%BA%A1ng\" title=\"<PERSON>\"><PERSON></a>, Vietnamese emperor (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Minh_M%E1%BA%A1ng\" title=\"<PERSON>\"><PERSON></a>, Vietnamese emperor (d. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Minh_M%E1%BA%A1ng"}]}, {"year": "1803", "text": "<PERSON><PERSON><PERSON><PERSON>, English author, playwright, and politician, Secretary of State for the Colonies (d. 1873)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, playwright, and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1803", "text": "<PERSON>, American poet and philosopher (d. 1882)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and philosopher (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and philosopher (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Swiss historian and academic (d. 1897)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and academic (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss historian and academic (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, Countess <PERSON>, French essayist and biographer (d. 1882)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_d%27Haussonville\" title=\"<PERSON>, Countess d'Ha<PERSON>on<PERSON>\"><PERSON>, Countess d<PERSON>Ha<PERSON></a>, French essayist and biographer (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_d%27Haussonville\" title=\"<PERSON>, Countess d'Ha<PERSON>onville\"><PERSON>, Countess d<PERSON>Ha<PERSON></a>, French essayist and biographer (d. 1882)", "links": [{"title": "<PERSON>, Countess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Countess_d%27Haussonville"}]}, {"year": "1830", "text": "<PERSON><PERSON><PERSON> (n<PERSON> <PERSON>), Welsh poet (d. 1877)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (<PERSON><PERSON><PERSON>)\"><PERSON><PERSON><PERSON> (n<PERSON> <PERSON>)</a>, Welsh poet (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)\" title=\"<PERSON> (<PERSON><PERSON><PERSON> Mai)\"><PERSON><PERSON><PERSON> (n<PERSON> <PERSON>)</a>, Welsh poet (d. 1877)", "links": [{"title": "<PERSON> (<PERSON><PERSON>bor <PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(<PERSON><PERSON><PERSON>_<PERSON>)"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Albanian-Turkish poet and translator (d. 1900)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Na<PERSON>_<PERSON>%C3%ABri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian-Turkish poet and translator (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ABri\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Albanian-Turkish poet and translator (d. 1900)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Na<PERSON>_<PERSON>%C3%ABri"}]}, {"year": "1848", "text": "<PERSON>, Swiss composer, educator, and publisher (d. 1924)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss composer, educator, and publisher (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss composer, educator, and publisher (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, American wrestler and trainer (d. 1933)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Algerian-French general (d. 1942)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Esp%C3%A8rey\" title=\"<PERSON>E<PERSON>èrey\"><PERSON></a>, Algerian-French general (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Esp%C3%A8rey\" title=\"<PERSON>E<PERSON>èrey\"><PERSON></a>, Algerian-French general (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Franchet_d%27Esp%C3%A8rey"}]}, {"year": "1860", "text": "<PERSON>, American psychologist and academic (d. 1944)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and academic (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>l"}]}, {"year": "1865", "text": "<PERSON>, American evangelist and saint, Nobel Prize laureate (d. 1955)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and saint, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American evangelist and saint, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1865", "text": "<PERSON>, Dutch physicist and academic, Nobel Prize laureate (d. 1943)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1865", "text": "<PERSON><PERSON>, English pianist and educator (d. 1936)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and educator (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English pianist and educator (d. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, Danish target shooter (d. 1950)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish target shooter (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish target shooter (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, Canadian journalist and art critic (d. 1918)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and art critic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and art critic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American actor and dancer (d. 1949)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and dancer (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, Lord <PERSON>, Canadian-English businessman and politician, Chancellor of the Duchy of Lancaster (d. 1964)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Canadian-English businessman and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Lord_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Lord <PERSON>\"><PERSON>, Lord <PERSON></a>, Canadian-English businessman and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 1964)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Lord_<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1879", "text": "<PERSON><PERSON> <PERSON><PERSON>, English Jesuit priest (d. 1963)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/C._C._<PERSON>\" title=\"C. C. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English Jesuit priest (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._C._<PERSON>\" title=\"C. C. <PERSON>dale\"><PERSON><PERSON> <PERSON><PERSON></a>, English Jesuit priest (d. 1963)", "links": [{"title": "C. C. <PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON>, American golfer (d. 1944)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, American golfer (d. 1944)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_(golfer)"}]}, {"year": "1880", "text": "<PERSON>, French neurologist and academic (d. 1967)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French neurologist and academic (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1882", "text": "<PERSON>, American actress (d. 1956)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Swedish hammer thrower (d. 1965)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish hammer thrower (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish hammer thrower (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, Indian soldier and activist (d. 1945)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian soldier and activist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian soldier and activist (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Scottish-American miner and labor leader (d. 1952)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American miner and labor leader (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American miner and labor leader (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON>, Italian priest and saint (d. 1968)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian priest and saint (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian priest and saint (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1888", "text": "<PERSON>, English actor and screenwriter (d. 1969)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>eson\"><PERSON></a>, English actor and screenwriter (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eson"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON>, German admiral (d. 1941)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German admiral (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German admiral (d. 1941)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%BCnther_L%C3%BCtjens"}]}, {"year": "1889", "text": "<PERSON>, Russian-American aircraft designer, founded Sikorsky Aircraft (d. 1972)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American aircraft designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>sky_Aircraft\" title=\"Sikorsky Aircraft\">Sikorsky Aircraft</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American aircraft designer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>sky_Aircraft\" title=\"Sikorsky Aircraft\">Sikorsky Aircraft</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sikorsky Aircraft", "link": "https://wikipedia.org/wiki/Sikorsky_Aircraft"}]}, {"year": "1893", "text": "<PERSON><PERSON>\" <PERSON>, American country musician (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>\" <PERSON></a>, American country musician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>\" <PERSON></a>, American country musician (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Australian cricketer (d. 1972)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American boxer and soldier (d. 1978)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and soldier (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American publisher and television game show panelist; co-founded Random House (d. 1971)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and television game show panelist; co-founded <a href=\"https://wikipedia.org/wiki/Random_House\" title=\"Random House\">Random House</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and television game show panelist; co-founded <a href=\"https://wikipedia.org/wiki/Random_House\" title=\"Random House\">Random House</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Random House", "link": "https://wikipedia.org/wiki/Random_House"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Bengali poet, author, and flute player (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Kazi_Nazrul_Islam\" title=\"Kazi Nazrul Islam\"><PERSON><PERSON><PERSON><PERSON></a>, Bengali poet, author, and flute player (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>zrul_<PERSON>\" title=\"Kazi Nazrul Islam\"><PERSON><PERSON><PERSON></a>, Bengali poet, author, and flute player (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Canadian poet and author (d. 1975)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian poet and author (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Irish Republican Army leader during the Irish War of Independence and the Irish Civil War (d. 1991)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> leader during the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> and the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican_Army\" title=\"Irish Republican Army\">Irish Republican Army</a> leader during the <a href=\"https://wikipedia.org/wiki/Irish_War_of_Independence\" title=\"Irish War of Independence\">Irish War of Independence</a> and the <a href=\"https://wikipedia.org/wiki/Irish_Civil_War\" title=\"Irish Civil War\">Irish Civil War</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish Republican Army", "link": "https://wikipedia.org/wiki/Irish_Republican_Army"}, {"title": "Irish War of Independence", "link": "https://wikipedia.org/wiki/Irish_War_of_Independence"}, {"title": "Irish Civil War", "link": "https://wikipedia.org/wiki/Irish_Civil_War"}]}, {"year": "1907", "text": "<PERSON>, Burmese politician, 1st Prime Minister of Burma (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/U_Nu\" title=\"U Nu\"><PERSON></a>, Burmese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U_Nu\" title=\"U Nu\"><PERSON></a>, Burmese politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Burma\" class=\"mw-redirect\" title=\"Prime Minister of Burma\">Prime Minister of Burma</a> (d. 1995)", "links": [{"title": "U Nu", "link": "https://wikipedia.org/wiki/U_Nu"}, {"title": "Prime Minister of Burma", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Burma"}]}, {"year": "1908", "text": "<PERSON>, American poet (d. 1963)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, German politician, 5th Prime Minister of Lower Saxony (d. 1999)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony\" class=\"mw-redirect\" title=\"Prime Minister of Lower Saxony\">Prime Minister of Lower Saxony</a> (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony\" class=\"mw-redirect\" title=\"Prime Minister of Lower Saxony\">Prime Minister of Lower Saxony</a> (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Lower Saxony", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Lower_Saxony"}]}, {"year": "1912", "text": "<PERSON>, American commander, wrestler, and coach (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, wrestler, and coach (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander, wrestler, and coach (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German colonel and pilot (d. 1957)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4r\" title=\"<PERSON>\"><PERSON></a>, German colonel and pilot (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_B%C3%A4r"}]}, {"year": "1913", "text": "<PERSON>, English journalist and producer (d. 1965)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and producer (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Canadian captain, lawyer, and politician, 15th Chief Justice of Canada (d. 1998)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian captain, lawyer, and politician, 15th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1916", "text": "<PERSON>, Italian discus thrower (d. 1981)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian discus thrower (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian discus thrower (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American film, television and stage actor (d. 1965)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film, television and stage actor (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film, television and stage actor (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American priest, theologian, and academic (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, theologian, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American priest, theologian, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Jamaican runner and diplomat (d. 1992)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican runner and diplomat (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican runner and diplomat (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American songwriter and composer (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and composer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American singer (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, German-Swiss physicist and academic, Nobel Prize laureate (d. 2020)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1922", "text": "<PERSON>, Italian politician (d. 1984)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, French-Hungarian footballer (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Nyers\" title=\"<PERSON><PERSON><PERSON> Nyers\"><PERSON><PERSON><PERSON></a>, French-Hungarian footballer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Nyers\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Hungarian footballer (d. 2005)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Nyers"}]}, {"year": "1925", "text": "<PERSON>, Mexican poet and author (d. 1974)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nos\" title=\"<PERSON>\"><PERSON></a>, Mexican poet and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>nos\" title=\"<PERSON>nos\"><PERSON></a>, Mexican poet and author (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rosario_Castellanos"}]}, {"year": "1925", "text": "<PERSON>, American actress (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, English journalist and politician (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American baseball player (d. 2000)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French film director and screenwriter (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French film director and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French film director and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 1994)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English painter and academic (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and academic (d. 2015)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1926", "text": "<PERSON>, Canadian author and poet (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and poet (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American basketball player and coach (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, English sculptor and painter (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and painter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and painter (d. 2014)", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "1927", "text": "<PERSON>, American soldier and author (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer-songwriter, pianist, and producer (d. 1984)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Petty\"><PERSON></a>, American singer-songwriter, pianist, and producer (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American soprano and actress (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and actress (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, French fashion designer (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Canadian lawyer and politician, 7th Deputy Prime Minister of Canada (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada\" title=\"Deputy Prime Minister of Canada\">Deputy Prime Minister of Canada</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Canada"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Russian engineer and astronaut (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and astronaut (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American director and producer", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Indian historian, dengue (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian, dengue (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian historian, dengue (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American novelist, screenwriter, and critic (d. 2003)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, screenwriter, and critic (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON> <PERSON><PERSON>, American basketball player and coach (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American basketball player and coach (d. 2020)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English-American actress (d. 2014)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)\" title=\"<PERSON> (British actress)\"><PERSON></a>, English-American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)\" title=\"<PERSON> (British actress)\"><PERSON></a>, English-American actress (d. 2014)", "links": [{"title": "<PERSON> (British actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_actress)"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Trinidadian lawyer and politician, 5th Prime Minister of Trinidad and Tobago (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Basde<PERSON>_Panday\" title=\"Bas<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Trinidadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Basde<PERSON>_<PERSON>\" title=\"<PERSON>s<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Trinidadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"Prime Minister of Trinidad and Tobago\">Prime Minister of Trinidad and Tobago</a> (d. 2024)", "links": [{"title": "Basdeo <PERSON>", "link": "https://wikipedia.org/wiki/Basdeo_Panday"}, {"title": "Prime Minister of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Trinidad_and_Tobago"}]}, {"year": "1933", "text": "<PERSON>, English footballer (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON><PERSON>, Faroese accountant and politician, 7th Prime Minister of the Faroe Islands (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/J%C3%B3g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Faroese accountant and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3g<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Faroese accountant and politician, 7th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3g<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1935", "text": "<PERSON>, Welsh engineer and academic (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a>, Welsh engineer and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh engineer and academic (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American football player (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON> <PERSON><PERSON>,  Canadian novelist and short story writer (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian novelist and short story writer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian novelist and short story writer (d. 2016)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Australian actress (d. 1988)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Australian actress (d. 1988)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2021)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Tom <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Tom_<PERSON>._<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 2013)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English painter and academic (d. 2022)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, English painter and academic (d. 2022)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1938", "text": "<PERSON>, American short story writer and poet (d. 1988)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and poet (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English historian, author, and critic (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and critic (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English businessman and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English businessman and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(politician)"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Russian economist and politician (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian economist and politician (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian economist and politician (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American actress and singer (d. 2010)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Carter\"><PERSON></a>, American actress and singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actor", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, Japanese photographer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese photographer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Czech filmmaker: 88 ", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, German developmental psychologist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/U<PERSON>_Frith\" title=\"Uta Frith\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Developmental_psychology\" title=\"Developmental psychology\">developmental psychologist</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON>_Frith\" title=\"Uta Frith\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Developmental_psychology\" title=\"Developmental psychology\">developmental psychologist</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Uta_Frith"}, {"title": "Developmental psychology", "link": "https://wikipedia.org/wiki/Developmental_psychology"}]}, {"year": "1941", "text": "<PERSON>, Moldovan economist and politician, 3rd President of Moldova", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan economist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Moldova\" title=\"President of Moldova\">President of Moldova</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan economist and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Moldova\" title=\"President of Moldova\">President of Moldova</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Moldova", "link": "https://wikipedia.org/wiki/President_of_Moldova"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American singer-songwriter and pianist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English keyboard player", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English keyboard player", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1943", "text": "<PERSON>, American actress and singer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, English journalist and philosopher", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and philosopher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and philosopher", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, French singer-songwriter (d. 2005)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and producer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1944", "text": "<PERSON>, American mathematician and academic", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, American mathematician and academic", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_(mathematician)"}]}, {"year": "1944", "text": "<PERSON>, English-born American puppeteer, filmmaker, and actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Oz\"><PERSON></a>, English-born American puppeteer, filmmaker, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Frank Oz\"><PERSON></a>, English-born American puppeteer, filmmaker, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English rugby player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Scottish-Canadian racing driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Canadian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American game designer, created <PERSON><PERSON><PERSON> (d. 1988)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Arduin\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a></i> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Arduin\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a></i> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arduin"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American psychologist and computer scientist (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and computer scientist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and computer scientist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Turkish lawyer and politician, Deputy Prime Minister of Turkey", "html": "1948 - <a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ar%C4%B1n%C3%A7\" title=\"Bülent Arınç\"><PERSON><PERSON><PERSON> Arınç</a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%BClent_Ar%C4%B1n%C3%A7\" title=\"Bülent Arınç\"><PERSON><PERSON><PERSON> Arınç</a>, Turkish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey\" title=\"Deputy Prime Minister of Turkey\">Deputy Prime Minister of Turkey</a>", "links": [{"title": "Bülent Arınç", "link": "https://wikipedia.org/wiki/B%C3%BClent_Ar%C4%B1n%C3%A7"}, {"title": "Deputy Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_Turkey"}]}, {"year": "1948", "text": "<PERSON>, Northern Irish historian, author, and academic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Northern Irish historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Northern Irish historian, author, and academic", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)"}]}, {"year": "1948", "text": "<PERSON>, German rock singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rock singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German rock singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Antiguan-American novelist, short story writer, and essayist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Jamaica_Kincaid\" title=\"Jamaica Kincaid\"><PERSON></a>, Antiguan-American novelist, short story writer, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jamaica_Kincaid\" title=\"Jamaica Kincaid\"><PERSON></a>, Antiguan-American novelist, short story writer, and essayist", "links": [{"title": "Jamaica Kincaid", "link": "https://wikipedia.org/wiki/Jamaica_Kincaid"}]}, {"year": "1949", "text": "<PERSON>-<PERSON>, English painter and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American rock violinist and singer (d. 2021)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rock violinist and singer (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rock violinist and singer (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Gale\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American businessman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American ice hockey player and coach", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Trinidadian-Scottish runner", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Trinidadian-Scottish runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" class=\"mw-redirect\" title=\"<PERSON> (athlete)\"><PERSON></a>, Trinidadian-Scottish runner", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>_(athlete)"}]}, {"year": "1952", "text": "<PERSON>, American author and publisher", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Al_Sarrantonio\" title=\"Al Sarrantonio\"><PERSON></a>, American author and publisher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Sarrantonio\" title=\"Al Sarrantonio\"><PERSON></a>, American author and publisher", "links": [{"title": "Al Sarrantonio", "link": "https://wikipedia.org/wiki/Al_Sarrantonio"}]}, {"year": "1952", "text": "<PERSON>, American businessman and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American playwright and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Argentinian footballer, coach, and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Japanese-American author and illustrator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese-American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stan_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Italian footballer (d. 1989)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Gaetano_Sc<PERSON>a\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gaetano_Sc<PERSON>\" title=\"Gaetano <PERSON>\"><PERSON><PERSON><PERSON></a>, Italian footballer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gaetano_Scirea"}]}, {"year": "1954", "text": "<PERSON>, English footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Indian actor, producer, and politician (d. 2009)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Malayalam_actor)\" title=\"<PERSON><PERSON><PERSON> (Malayalam actor)\"><PERSON><PERSON><PERSON></a>, Indian actor, producer, and politician (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Malayalam_actor)\" title=\"<PERSON><PERSON><PERSON> (Malayalam actor)\"><PERSON><PERSON><PERSON></a>, Indian actor, producer, and politician (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON> (Malayalam actor)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(Malayalam_actor)"}]}, {"year": "1955", "text": "<PERSON>, English lawyer and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Greek politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American politician, 62nd Governor of Maryland", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1956", "text": "<PERSON>, Irish Republican (d. 1981)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (d. 1981)", "links": [{"title": "<PERSON> (hunger striker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1956", "text": "<PERSON>, American composer and conductor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, English journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(writer)"}]}, {"year": "1957", "text": "<PERSON>, Canadian ice hockey player", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American children's author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Straight\"><PERSON></a>, American children's author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dorothy Straight\"><PERSON></a>, American children's author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English singer, songwriter and musician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer, songwriter and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English comedian, actor, and author", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor, and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Greek politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Manolis_Kefalogiannis\" title=\"Man<PERSON> Kefalogiannis\"><PERSON><PERSON></a>, Greek politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Manolis_Kefalogiannis\" title=\"Man<PERSON> Kefalogiannis\"><PERSON><PERSON></a>, Greek politician", "links": [{"title": "Manolis Kefalogiannis", "link": "https://wikipedia.org/wiki/Manolis_Kefalogiannis"}]}, {"year": "1959", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American lawyer and politician", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, English journalist and television host", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and television host", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Canadian ice hockey player, coach, and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American director and producer (d. 2010)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Canadian-American actor, singer, producer, and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, singer, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, singer, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Romanian engineer and politician, 68th Prime Minister of Romania", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">68th</a> <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian engineer and politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania\" class=\"mw-redirect\" title=\"List of Prime Ministers of Romania\">68th</a> <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Romania\" title=\"Prime Minister of Romania\">Prime Minister of Romania</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Romania", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Romania"}, {"title": "Prime Minister of Romania", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Romania"}]}, {"year": "1964", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Gambian colonel and politician, President of the Gambia", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Gambia\" title=\"President of the Gambia\">President of the Gambia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Gambian colonel and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Gambia\" title=\"President of the Gambia\">President of the Gambia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Gambia", "link": "https://wikipedia.org/wiki/President_of_the_Gambia"}]}, {"year": "1967", "text": "<PERSON>, Belgian footballer and manager", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, head designer of Magic: the Gathering", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head designer of <i><a href=\"https://wikipedia.org/wiki/Magic:_the_Gathering\" class=\"mw-redirect\" title=\"Magic: the Gathering\">Magic: the Gathering</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, head designer of <i><a href=\"https://wikipedia.org/wiki/Magic:_the_Gathering\" class=\"mw-redirect\" title=\"Magic: the Gathering\">Magic: the Gathering</a></i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Magic: the Gathering", "link": "https://wikipedia.org/wiki/Magic:_the_Gathering"}]}, {"year": "1967", "text": "<PERSON>, Canadian tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player, boxer, and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, boxer, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player, boxer, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian guitarist and songwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>over\" title=\"<PERSON> Drover\"><PERSON></a>, Canadian guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Glen_Drover\" title=\"<PERSON> Drover\"><PERSON></a>, Canadian guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Glen_Drover"}]}, {"year": "1969", "text": "<PERSON>, American actress (d. 2022)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian voice actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American journalist and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Stacy_<PERSON>\" title=\"Stacy London\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stacy_<PERSON>\" title=\"Stacy London\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stacy_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Welsh-English cricketer and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American actress and author[a]", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and author", "links": [{"title": "Oct<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian runner", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Slovak politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, Indian actor, director, producer, and screenwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, American rapper and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American model and actress", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Scottish footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American drummer and composer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Dominican-American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Congolese-Swiss footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese-Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese-Swiss footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish high jumper", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Estonian cyclist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Erki_P%C3%BCtsep\" title=\"<PERSON><PERSON><PERSON>ütsep\"><PERSON><PERSON><PERSON></a>, Estonian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erki_P%C3%BCtsep\" title=\"<PERSON><PERSON><PERSON>ütsep\"><PERSON><PERSON><PERSON></a>, Estonian cyclist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erki_P%C3%BCtsep"}]}, {"year": "1976", "text": "<PERSON>, American actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Irish actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Irish actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Mexican footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Estonian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Mexican-American mixed martial artist and wrestler", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American mixed martial artist and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American mixed martial artist and wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alberto_<PERSON>_Rio"}]}, {"year": "1978", "text": "<PERSON>, Canadian singer-songwriter and guitarist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American footballer and executive", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Egyptian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>d"}]}, {"year": "1979", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, English-Nigerian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, English rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball pitcher", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(pitcher)\" title=\"<PERSON> (pitcher)\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON> (pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(pitcher)"}]}, {"year": "1980", "text": "<PERSON>, Spanish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>kan<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, New Zealand rugby league player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Norwegian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American skateboarder", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skateboarder", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Polish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian rugby league player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON><PERSON>, Kenyan runner", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Ezekiel_Kemboi\" title=\"Ezekiel Kemboi\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ezekiel_Kemboi\" title=\"Ezekiel Kemboi\"><PERSON><PERSON><PERSON><PERSON></a>, Kenyan runner", "links": [{"title": "Ezekiel <PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>kiel_<PERSON>mboi"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American figure skater and meteorologist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and meteorologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and meteorologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Australian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luke Ball\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Luke Ball\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON> <PERSON><PERSON>, American race car driver", "html": "1984 - <a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_IV\" title=\"<PERSON><PERSON> <PERSON><PERSON> Foyt IV\"><PERSON><PERSON> <PERSON><PERSON>oyt IV</a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>t_IV\" title=\"<PERSON><PERSON> <PERSON><PERSON> Foyt IV\"><PERSON><PERSON> <PERSON><PERSON> IV</a>, American race car driver", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Portuguese singer and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Portuguese singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, French footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/De<PERSON>_Ba\" title=\"Demba Ba\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON>_Ba\" title=\"Demba Ba\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "Demba Ba", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ba"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player and wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Roman_Reigns\" title=\"Roman Reigns\">Roman Reigns</a>, American football player and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Reigns\" title=\"Roman Reigns\">Roman Reigns</a>, American football player and wrestler", "links": [{"title": "Roman Reigns", "link": "https://wikipedia.org/wiki/Roman_Reigns"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Japanese actor and musician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_H%C5%8Dj%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%8Dj%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese actor and musician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takahiro_H%C5%8Dj%C5%8D"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, Welsh cyclist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Geraint <PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Geraint <PERSON>\"><PERSON><PERSON><PERSON></a>, Welsh cyclist", "links": [{"title": "Geraint <PERSON>", "link": "https://wikipedia.org/wiki/Geraint_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, German footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Polish ski jumper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish ski jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish ski jumper", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Slovak footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/D%C3%A1vid_%C5%A0kutka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A1vid_%C5%A0kutka\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A1vid_%C5%A0kutka"}]}, {"year": "1988", "text": "<PERSON>, South African swimmer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American wrestler", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Bo_Dallas\" title=\"Bo Dallas\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo_Dallas\" title=\"Bo Dallas\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bo_Dallas"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Russian ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1993)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1993)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1993)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1993)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer, born 1993)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1993)"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1994)\" title=\"<PERSON> (ice hockey, born 1994)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1994)\" title=\"<PERSON> (ice hockey, born 1994)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey, born 1994)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1994)"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American gymnast", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Czech ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%88%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%88%C3%A1k\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%88%C3%A1k"}]}, {"year": "1999", "text": "<PERSON><PERSON>, French footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ibrahima_Konat%C3%A9"}]}, {"year": "2000", "text": "<PERSON>, American tennis player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "675", "text": "<PERSON>, Chinese prince (b. 652)", "html": "675 - <a href=\"https://wikipedia.org/wiki/Li_<PERSON>\" title=\"Li Hong\"><PERSON></a>, Chinese prince (b. 652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Li_Hong\" title=\"Li Hong\"><PERSON></a>, Chinese prince (b. 652)", "links": [{"title": "Li Hong", "link": "https://wikipedia.org/wiki/Li_Hong"}]}, {"year": "709", "text": "<PERSON><PERSON><PERSON><PERSON>, English-Latin bishop, poet, and scholar (b. 639)", "html": "709 - <a href=\"https://wikipedia.org/wiki/Aldhelm\" title=\"Aldhelm\"><PERSON><PERSON><PERSON><PERSON></a>, English-Latin bishop, poet, and scholar (b. 639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aldhelm\" title=\"Aldhel<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English-Latin bishop, poet, and scholar (b. 639)", "links": [{"title": "Aldhelm", "link": "https://wikipedia.org/wiki/Aldhelm"}]}, {"year": "803", "text": "<PERSON><PERSON><PERSON><PERSON> of Lindisfarne, English bishop", "html": "803 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON><PERSON>_of_Lindisfarne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON><PERSON><PERSON> of Lindisfarne</a>, English bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON><PERSON>_of_Lindisfarne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON><PERSON><PERSON> of Lindisfarne</a>, English bishop", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Lindisfarne", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON><PERSON>_of_Lindisfarne"}]}, {"year": "912", "text": "<PERSON><PERSON>, chancellor of Later Liang", "html": "912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"<PERSON> Liang (Five Dynasties)\"><PERSON> Liang</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, chancellor of <a href=\"https://wikipedia.org/wiki/Later_Liang_(Five_Dynasties)\" title=\"<PERSON> Liang (Five Dynasties)\"><PERSON> Liang</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> Liang (Five Dynasties)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Five_Dynasties)"}]}, {"year": "916", "text": "<PERSON><PERSON><PERSON>, king of Meath", "html": "916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Meath\" title=\"Kingdom of Meath\"><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, king of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Meath\" title=\"Kingdom of Meath\"><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>na"}, {"title": "Kingdom of Meath", "link": "https://wikipedia.org/wiki/Kingdom_of_Meath"}]}, {"year": "939", "text": "<PERSON>, general of Chu", "html": "939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"<PERSON> (Ten Kingdoms)\">Chu</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of <a href=\"https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)\" class=\"mw-redirect\" title=\"<PERSON> (Ten Kingdoms)\">Chu</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chu (Ten Kingdoms)", "link": "https://wikipedia.org/wiki/Chu_(Ten_Kingdoms)"}]}, {"year": "986", "text": "<PERSON> <PERSON><PERSON>, Muslim astronomer (b. 903)", "html": "986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> al<PERSON>\"><PERSON></a>, Muslim astronomer (b. 903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON> al<PERSON>\"><PERSON></a>, Muslim astronomer (b. 903)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "992", "text": "<PERSON><PERSON><PERSON><PERSON> of Poland (b. 935)", "html": "992 - <a href=\"https://wikipedia.org/wiki/Mieszko_I_of_Poland\" class=\"mw-redirect\" title=\"Mieszko I of Poland\">Mieszko I of Poland</a> (b. 935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miesz<PERSON>_I_of_Poland\" class=\"mw-redirect\" title=\"Mieszko I of Poland\"><PERSON>esz<PERSON> I of Poland</a> (b. 935)", "links": [{"title": "Mieszko I of Poland", "link": "https://wikipedia.org/wiki/Mieszko_I_of_Poland"}]}, {"year": "1085", "text": "<PERSON> (b. 1020)", "html": "1085 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_VII\" title=\"<PERSON> Gregory VII\"><PERSON> <PERSON> VII</a> (b. 1020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory VII\"><PERSON> <PERSON> VII</a> (b. 1020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1261", "text": "<PERSON> (b. 1185)", "html": "1261 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Alexander IV\">Pope <PERSON> IV</a> (b. 1185)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_IV\" title=\"Pope Alexander IV\">Pope <PERSON> IV</a> (b. 1185)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1452", "text": "<PERSON>, English archbishop and politician", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English archbishop and politician", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1555", "text": "<PERSON>, Dutch physician, mathematician, and cartographer (b. 1508)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>isi<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician, mathematician, and cartographer (b. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physician, mathematician, and cartographer (b. 1508)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>isius"}]}, {"year": "1555", "text": "<PERSON> of Navarre (b. 1503)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Navarre\" title=\"<PERSON> II of Navarre\"><PERSON> of Navarre</a> (b. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> II of Navarre\"><PERSON> of Navarre</a> (b. 1503)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_II_of_Navarre"}]}, {"year": "1558", "text": "<PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen (b. 1510)", "html": "1558 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen\" title=\"Elisabeth of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen\"><PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen</a> (b. 1510)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen\" title=\"Elisabeth of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen\"><PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen</a> (b. 1510)", "links": [{"title": "<PERSON> of Brandenburg, Duchess of Brunswick-Calenberg-Göttingen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Brandenburg,_Duchess_of_Brunswick-Calenberg-G%C3%B6ttingen"}]}, {"year": "1595", "text": "<PERSON><PERSON>, German poet and critic (b. 1567)", "html": "1595 - <a href=\"https://wikipedia.org/wiki/Valens_Acidalius\" title=\"Valens Acidalius\"><PERSON><PERSON></a>, German poet and critic (b. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Valens_Acidalius\" title=\"Valens Acidalius\"><PERSON><PERSON></a>, German poet and critic (b. 1567)", "links": [{"title": "Valens <PERSON>alius", "link": "https://wikipedia.org/wiki/Valens_Acidalius"}]}, {"year": "1607", "text": "<PERSON>, Italian Carmelite nun and mystic (b. 1566)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON>dale<PERSON>\"><PERSON></a>, Italian Carmelite nun and mystic (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Carmelite nun and mystic (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON><PERSON>"}]}, {"year": "1632", "text": "<PERSON>, Austrian mathematician and philosopher (b. 1572)", "html": "1632 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" class=\"mw-redirect\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Austrian mathematician and philosopher (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)\" class=\"mw-redirect\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Austrian mathematician and philosopher (b. 1572)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(mathematician)"}]}, {"year": "1667", "text": "<PERSON><PERSON><PERSON>, Finnish-Swedish politician, 5th Lord High Treasurer of Sweden (b. 1620)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1620%E2%80%931667)\" title=\"<PERSON><PERSON><PERSON> (1620-1667)\"><PERSON><PERSON><PERSON></a>, Finnish-Swedish politician, 5th <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer_of_Sweden\" title=\"Lord High Treasurer of Sweden\">Lord High Treasurer of Sweden</a> (b. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1620%E2%80%931667)\" title=\"<PERSON><PERSON><PERSON> (1620-1667)\"><PERSON><PERSON><PERSON></a>, Finnish-Swedish politician, 5th <a href=\"https://wikipedia.org/wiki/Lord_High_Treasurer_of_Sweden\" title=\"Lord High Treasurer of Sweden\">Lord High Treasurer of Sweden</a> (b. 1620)", "links": [{"title": "<PERSON><PERSON><PERSON> (1620-1667)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(1620%E2%80%931667)"}, {"title": "Lord High Treasurer of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Treasurer_of_Sweden"}]}, {"year": "1681", "text": "<PERSON>, Spanish poet and playwright (b. 1600)", "html": "1681 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>%C3%B3n_de_la_Barca\" title=\"<PERSON> Barca\"><PERSON></a>, Spanish poet and playwright (b. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n_de_la_Barca\" title=\"<PERSON> la Barca\"><PERSON></a>, Spanish poet and playwright (b. 1600)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Calder%C3%B3n_de_la_Barca"}]}, {"year": "1741", "text": "<PERSON>, German bishop and theologian (b. 1660)", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and theologian (b. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German bishop and theologian (b. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1786", "text": "<PERSON> of Portugal (b. 1717)", "html": "1786 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (b. 1717)", "links": [{"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Portugal"}]}, {"year": "1789", "text": "<PERSON>, Swedish botanist and physician (b. 1751)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and physician (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish botanist and physician (b. 1751)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, 4th Baron <PERSON>, English field marshal and politician, Lord Lieutenant of Essex (b. 1719)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 4th Baron <PERSON>\"><PERSON>, 4th Baron <PERSON></a>, English field marshal and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex\" title=\"Lord Lieutenant of Essex\">Lord Lieutenant of Essex</a> (b. 1719)", "links": [{"title": "<PERSON>, 4th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Baron_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Essex", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Essex"}]}, {"year": "1805", "text": "<PERSON>, English priest and philosopher (b. 1743)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and philosopher (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest and philosopher (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1849", "text": "<PERSON>, English general and politician, Governor of British Guiana (b. 1777)", "html": "1849 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Urban\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_British_Guiana\" class=\"mw-redirect\" title=\"Governor of British Guiana\">Governor of British Guiana</a> (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Urban\" title=\"<PERSON>\"><PERSON></a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_British_Guiana\" class=\"mw-redirect\" title=\"Governor of British Guiana\">Governor of British Guiana</a> (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benjamin_D%27Urban"}, {"title": "Governor of British Guiana", "link": "https://wikipedia.org/wiki/Governor_of_British_Guiana"}]}, {"year": "1895", "text": "<PERSON>, Ottoman sociologist, historian, and jurist (b. 1822)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sociologist, historian, and jurist (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ottoman sociologist, historian, and jurist (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French painter and sculptor (b. 1822)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American educator and politician, 46th Governor of Maryland (b. 1860)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/Austin_Lane_Crothers\" title=\"Austin Lane Crothers\"><PERSON></a>, American educator and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Lane_Crothers\" title=\"Austin Lane Crothers\"><PERSON>roth<PERSON></a>, American educator and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Maryland\" title=\"Governor of Maryland\">Governor of Maryland</a> (b. 1860)", "links": [{"title": "<PERSON> Crothers", "link": "https://wikipedia.org/wiki/Austin_Lane_Crothers"}, {"title": "Governor of Maryland", "link": "https://wikipedia.org/wiki/Governor_of_Maryland"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Belarusian poet and critic (b. 1891)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and critic (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian poet and critic (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>%C4%8D"}]}, {"year": "1919", "text": "<PERSON>, American archer (b. 1840)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archer (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businesswoman and philanthropist, founded the Madame C.J. Walker Manufacturing Company (b. 1867)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Madame_C.J<PERSON>_Walker_Manufacturing_Company\" class=\"mw-redirect\" title=\"Madame C.J. Walker Manufacturing Company\">Madame C.J. Walker Manufacturing Company</a> (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON>am <PERSON>\"><PERSON><PERSON><PERSON></a>, American businesswoman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Madame_C.J<PERSON>_Walker_Manufacturing_Company\" class=\"mw-redirect\" title=\"Madame C.J. Walker Manufacturing Company\">Madame C.J. Walker Manufacturing Company</a> (b. 1867)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Madame C.J. Walker Manufacturing Company", "link": "https://wikipedia.org/wiki/Madame_C.J._Walker_Manufacturing_Company"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian painter and illustrator (b. 1889)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Popova\" title=\"Lyubov Popova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian painter and illustrator (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Popova\" title=\"Lyubov Popova\"><PERSON><PERSON><PERSON><PERSON></a>, Russian painter and illustrator (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lyubov_Popova"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Ukrainian journalist and politician (b. 1879)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian journalist and politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian journalist and politician (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American businessman and philanthropist (b. 1876)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Scottish-English archbishop (b. 1848)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English archbishop (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English archbishop (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English trombonist, composer, and educator (b. 1874)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trombonist, composer, and educator (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English trombonist, composer, and educator (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American-French painter and illustrator (b. 1859)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter and illustrator (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-French painter and illustrator (b. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English astronomer and academic (b. 1868)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and academic (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Ukrainian-American cellist and educator (b. 1902)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American cellist and educator (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-American cellist and educator (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Swedish painter (b. 1888)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Swedish painter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> von <PERSON>\"><PERSON><PERSON> <PERSON></a>, Swedish painter (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Polish officer and Resistance leader (b. 1901)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish officer and Resistance leader (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish officer and Resistance leader (b. 1901)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wito<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Croatian poet and author (b. 1887)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet and author (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian poet and author (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C4%87"}]}, {"year": "1954", "text": "<PERSON>, Hungarian photographer and journalist (b. 1913)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian photographer and journalist (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian photographer and journalist (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American swimmer, diver, and water polo player (b. 1883)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer, diver, and water polo player (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer, diver, and water polo player (b. 1883)", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1968", "text": "<PERSON>, German field marshal (b. 1881)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German field marshal (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Dutch-American psychoanalyst (b. 1909)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American psychoanalyst (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American psychoanalyst (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Scottish mountaineer and author (b. 1932)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mountaineer and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish mountaineer and author (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian author (b. 1904)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Yevgenia_Ginzburg\" title=\"Yevgenia <PERSON>\">Ye<PERSON><PERSON><PERSON></a>, Russian author (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yevgenia_Ginzburg\" title=\"Yevgenia <PERSON>\">Ye<PERSON><PERSON><PERSON></a>, Russian author (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yevgenia_Ginzburg"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Czech-Israeli engineer, mystic, and author (b. 1923)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Israeli engineer, mystic, and author (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-Israeli engineer, mystic, and author (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian-born French racing driver and sports car manufacturer (b. 1899)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Am%C3%A9d%C3%A9e_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-born French racing driver and sports car manufacturer (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am%C3%A9d%C3%A9<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian-born French racing driver and sports car manufacturer (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Am%C3%A9d%C3%A9e_<PERSON><PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American murderer (b. 1949)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>-<PERSON>, Australian physicist and astronomer (b. 1912)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and astronomer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and astronomer (b. 1912)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, English author and publisher (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and publisher (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author and publisher (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ric_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Turkish author, poet, and playwright (b. 1904)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek\" title=\"Necip <PERSON>azıl Kısakürek\"><PERSON><PERSON><PERSON></a>, Turkish author, poet, and playwright (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek\" title=\"Necip <PERSON>azıl Kısakürek\"><PERSON><PERSON><PERSON></a>, Turkish author, poet, and playwright (b. 1904)", "links": [{"title": "Necip <PERSON>", "link": "https://wikipedia.org/wiki/Necip_Faz%C4%B1l_K%C4%B1sak%C3%BCrek"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON> of Libya (b. 1889)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Idris_of_Libya\" title=\"Idris of Libya\"><PERSON>dris of Libya</a> (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Idris_of_Libya\" title=\"Idris of Libya\"><PERSON>dris of Libya</a> (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON> of Libya", "link": "https://wikipedia.org/wiki/Idris_of_Libya"}]}, {"year": "1983", "text": "<PERSON>, Canadian-American ice hockey player (b. 1917)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian-American ice hockey player (b. 1917)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1986", "text": "<PERSON>, American journalist and politician, 22nd Under Secretary of State (b. 1901)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chester_Bowles\" class=\"mw-redirect\" title=\"Chester Bowles\"><PERSON></a>, American journalist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chester_Bowles\" class=\"mw-redirect\" title=\"Chester Bowles\"><PERSON></a>, American journalist and politician, 22nd <a href=\"https://wikipedia.org/wiki/Under_Secretary_of_State\" class=\"mw-redirect\" title=\"Under Secretary of State\">Under Secretary of State</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chester_Bowles"}, {"title": "Under Secretary of State", "link": "https://wikipedia.org/wiki/Under_Secretary_of_State"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1930)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, French racing driver (b. 1914)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_Bayol\" title=\"<PERSON><PERSON>ol\"><PERSON><PERSON></a>, French racing driver (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89lie_Bayol\" title=\"<PERSON><PERSON>ol\"><PERSON><PERSON></a>, French racing driver (b. 1914)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_Bayol"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Croatian basketball player and coach, Naismith Basketball Hall of Famer 1996 (b. 1948)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Kre%C5%A1imir_%C4%86osi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Croatian basketball player and coach, Naismith Basketball Hall of Famer 1996 (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kre%C5%A1imir_%C4%86osi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Croatian basketball player and coach, Naismith Basketball Hall of Famer 1996 (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kre%C5%A1imir_%C4%86osi%C4%87"}]}, {"year": "1995", "text": "<PERSON><PERSON>, French actress (b. 1927)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French actress (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Italian historian and author (b. 1929)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian historian and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian historian and author (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American author and poet (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and poet (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Jr., American publisher, co-founded Farrar, Straus and Giroux Publishing Company (b. 1917)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>\" title=\"Farrar, <PERSON>raus and <PERSON>ux\">Farrar, Straus and Giroux Publishing Company</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>\" title=\"Farrar, <PERSON><PERSON><PERSON> and <PERSON>ux\">Farrar, Straus and Giroux Publishing Company</a> (b. 1917)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "Farrar, Straus and Giroux", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Indian actor, director, producer, and politician (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and politician (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian actor, director, producer, and politician (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English businessman, founded Panther Westwinds (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Panther_Westwinds\" title=\"Panther Westwinds\">Panther Westwinds</a> (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded <a href=\"https://wikipedia.org/wiki/Panther_Westwinds\" title=\"Panther Westwinds\">Panther Westwinds</a> (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Panther Westwinds", "link": "https://wikipedia.org/wiki/Panther_Westwinds"}]}, {"year": "2005", "text": "<PERSON>, Australian television host and actor (b. 1934)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Indian-born film producer and director (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ismail Merchant\"><PERSON></a>, Indian-born film producer and director (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ismail Merchant\"><PERSON></a>, Indian-born film producer and director (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Slovene painter and illustrator (b. 1909)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>oran_Mu%C5%A1i%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene painter and illustrator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>oran_Mu%C5%A1i%C4%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovene painter and illustrator (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zoran_Mu%C5%A1i%C4%8D"}]}, {"year": "2007", "text": "<PERSON>, American actor, comedian, and director (b. 1931)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and director (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and director (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Belarusian blogger, web designer and website creator (b. 1976)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/U%C5%82adzimir_Katko%C5%ADski\" title=\"Uładzi<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian blogger, web designer and website creator (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U%C5%82adzimir_Katko%C5%ADski\" title=\"Uład<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Belarusian blogger, web designer and website creator (b. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U%C5%82adzimir_Katko%C5%ADski"}]}, {"year": "2008", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businessman, founded <PERSON><PERSON><PERSON> (b. 1909)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/J._<PERSON>._Simplot\" title=\"<PERSON><PERSON> <PERSON><PERSON> Simplot\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Simplot\" title=\"Simplot\">Simplot</a> (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._<PERSON>._Simplot\" title=\"<PERSON><PERSON> <PERSON><PERSON> Simplot\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Simplot\" title=\"Simplot\">Simplot</a> (b. 1909)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Simplot"}, {"title": "Simplot", "link": "https://wikipedia.org/wiki/Simplot"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish actor and theater councilor (b. 1921)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Veikko_Uusim%C3%A4ki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and theater councilor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>k<PERSON>_Uusim%C3%A4ki\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor and theater councilor (b. 1921)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veikko_Uusim%C3%A4ki"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Norwegian politician (b. 1905)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Haak<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Haak<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian politician (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Ukrainian basketball player (b. 1959)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian basketball player (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian basketball player (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ny"}]}, {"year": "2010", "text": "<PERSON>, American businessman (b. 1936)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Australian footballer and coach (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Mexican painter and illustrator (b. 1915)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Mexican painter and illustrator (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Mexican painter and illustrator (b. 1915)", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "2010", "text": "<PERSON>, American football player and coach (b. 1965)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_back)\" title=\"<PERSON> (defensive back)\"><PERSON></a>, American football player and coach (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_back)\" title=\"<PERSON> (defensive back)\"><PERSON></a>, American football player and coach (b. 1965)", "links": [{"title": "<PERSON> (defensive back)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(defensive_back)"}]}, {"year": "2011", "text": "<PERSON>, Australian cricketer and coach (b. 1944)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American author and screenwriter (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American author and critic (b. 1958)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and critic (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player and coach (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian politician (b. 1950)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician (b. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian politician (b. 1953)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician (b. 1953)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English cricketer (b. 1935)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (b. 1935)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "2014", "text": "<PERSON>, Canadian economist and politician (b. 1942)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Marcel_<PERSON>%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marcel_C%C3%B4t%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian economist and politician (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marcel_C%C3%B4t%C3%A9"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Polish general and politician, 1st President of Poland (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>zel<PERSON>\" title=\"Wojcie<PERSON>aruzel<PERSON>\">Woj<PERSON><PERSON></a>, Polish general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Woj<PERSON><PERSON>_<PERSON>\" title=\"Wojcie<PERSON>aru<PERSON>\">Wo<PERSON><PERSON><PERSON></a>, Polish general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Poland\" title=\"President of Poland\">President of Poland</a> (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wojcie<PERSON>_<PERSON>"}, {"title": "President of Poland", "link": "https://wikipedia.org/wiki/President_of_Poland"}]}, {"year": "2014", "text": "<PERSON>, American singer and actor (b. 1913)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Tuvaluan educator and politician, 1st Prime Minister of Tuvalu (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tuvaluan educator and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Tuvaluan educator and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu\" title=\"Prime Minister of Tuvalu\">Prime Minister of Tuvalu</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ti"}, {"title": "Prime Minister of Tuvalu", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Tuvalu"}]}, {"year": "2014", "text": "<PERSON>, American boxer and trainer (b. 1954)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and trainer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian lawyer and politician, 2nd Premier of the Northwest Territories (b. 1949)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_the_Northwest_Territories\" title=\"Premier of the Northwest Territories\">Premier of the Northwest Territories</a> (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/Premier_of_the_Northwest_Territories\" title=\"Premier of the Northwest Territories\">Premier of the Northwest Territories</a> (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the Northwest Territories", "link": "https://wikipedia.org/wiki/Premier_of_the_Northwest_Territories"}]}, {"year": "2015", "text": "<PERSON>, Canadian bishop (b. 1924)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Canadian bishop (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, Canadian bishop (b. 1924)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician and <PERSON><PERSON> caste leader (b. 1961)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Guru\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> caste leader (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Guru\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician and <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON></a> caste leader (b. 1961)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Danish-British socialite (b. 1926)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-British socialite (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-British socialite (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BClow"}]}, {"year": "2020", "text": "<PERSON>, African American man murdered by Minneapolis police officer <PERSON> (b. 1973)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African American man <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murdered</a> by <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis</a> police officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African American man <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">murdered</a> by <a href=\"https://wikipedia.org/wiki/Minneapolis\" title=\"Minneapolis\">Minneapolis</a> police officer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "Minneapolis", "link": "https://wikipedia.org/wiki/Minneapolis"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American attorney and politician (b. 1927)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American author and illustrator (b. 1934)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American literary agent (b. 1930)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary agent (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary agent (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American professional golfer (b. 1993)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional golfer (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional golfer (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Canadian film producer (b. 1930)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film producer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian film producer (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American songwriter (b. 1928)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American actor", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>actor\" title=\"<PERSON> Wactor\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>actor\" title=\"<PERSON> Wactor\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>actor"}]}]}}