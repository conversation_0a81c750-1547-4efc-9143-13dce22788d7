{"date": "December 1", "url": "https://wikipedia.org/wiki/December_1", "data": {"Events": [{"year": "800", "text": "A council is convened in the Vatican, at which <PERSON><PERSON><PERSON><PERSON> is to judge the accusations against <PERSON> <PERSON>.", "html": "800 - A council is convened in the <a href=\"https://wikipedia.org/wiki/Apostolic_Palace\" title=\"Apostolic Palace\">Vatican</a>, at which <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is to judge the accusations against <a href=\"https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>\" title=\"<PERSON> Leo <PERSON>\">Pope <PERSON></a>.", "no_year_html": "A council is convened in the <a href=\"https://wikipedia.org/wiki/Apostolic_Palace\" title=\"Apostolic Palace\">Vatican</a>, at which <a href=\"https://wikipedia.org/wiki/Cha<PERSON>ma<PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> is to judge the accusations against <a href=\"https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>\" title=\"Pope Leo <PERSON>\">Pope <PERSON></a>.", "links": [{"title": "Apostolic Palace", "link": "https://wikipedia.org/wiki/Apostolic_Palace"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1420", "text": "<PERSON> of England enters Paris alongside his father-in-law King <PERSON> of France.", "html": "1420 - <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a> enters <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> alongside his father-in-law King <a href=\"https://wikipedia.org/wiki/Charles_VI_of_France\" title=\"Charles VI of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a> enters <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> alongside his father-in-law King <a href=\"https://wikipedia.org/wiki/Charles_VI_of_France\" title=\"Charles VI of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_V_of_England"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_VI_of_France"}]}, {"year": "1577", "text": "Courtiers <PERSON> and <PERSON> are knighted by Queen <PERSON> of England.", "html": "1577 - Courtiers <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Knight\" title=\"Knight\">knighted</a> by Queen <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "no_year_html": "Courtiers <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are <a href=\"https://wikipedia.org/wiki/Knight\" title=\"Knight\">knighted</a> by Queen <a href=\"https://wikipedia.org/wiki/Elizabeth_I_of_England\" class=\"mw-redirect\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Knight"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1640", "text": "End of the Iberian Union: Portugal acclaims as King <PERSON> of Portugal, ending 59 years of personal union of the crowns of Portugal and Spain and the end of the rule of the Philippine Dynasty.", "html": "1640 - End of the <a href=\"https://wikipedia.org/wiki/Iberian_Union\" title=\"Iberian Union\">Iberian Union</a>: Portugal acclaims as King <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_IV_of_Portugal\" class=\"mw-redirect\" title=\"João IV of Portugal\"><PERSON> of Portugal</a>, ending 59 years of <a href=\"https://wikipedia.org/wiki/Personal_union\" title=\"Personal union\">personal union</a> of the crowns of Portugal and Spain and the end of the rule of the <a href=\"https://wikipedia.org/wiki/Philippine_Dynasty\" class=\"mw-redirect\" title=\"Philippine Dynasty\">Philippine Dynasty</a>.", "no_year_html": "End of the <a href=\"https://wikipedia.org/wiki/Iberian_Union\" title=\"Iberian Union\">Iberian Union</a>: Portugal acclaims as King <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_IV_of_Portugal\" class=\"mw-redirect\" title=\"João IV of Portugal\"><PERSON> of Portugal</a>, ending 59 years of <a href=\"https://wikipedia.org/wiki/Personal_union\" title=\"Personal union\">personal union</a> of the crowns of Portugal and Spain and the end of the rule of the <a href=\"https://wikipedia.org/wiki/Philippine_Dynasty\" class=\"mw-redirect\" title=\"Philippine Dynasty\">Philippine Dynasty</a>.", "links": [{"title": "Iberian Union", "link": "https://wikipedia.org/wiki/Iberian_Union"}, {"title": "<PERSON> of Portugal", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_IV_of_Portugal"}, {"title": "Personal union", "link": "https://wikipedia.org/wiki/Personal_union"}, {"title": "Philippine Dynasty", "link": "https://wikipedia.org/wiki/Philippine_Dynasty"}]}, {"year": "1662", "text": "Diarist <PERSON> records skating on the frozen lake in St James's Park, London, watched by <PERSON> and <PERSON>.", "html": "1662 - <PERSON><PERSON>t <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> records skating on the frozen lake in <a href=\"https://wikipedia.org/wiki/St_James%27s_Park\" title=\"St James's Park\">St James's Park</a>, London, watched by <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"Charles II of England\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/Catherine_of_Braganza\" title=\"Catherine of Braganza\">Queen <PERSON></a>.", "no_year_html": "Diarist <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> records skating on the frozen lake in <a href=\"https://wikipedia.org/wiki/St_James%27s_Park\" title=\"St James's Park\">St James's Park</a>, London, watched by <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"Charles II of England\"><PERSON> II</a> and <a href=\"https://wikipedia.org/wiki/Catherine_of_Braganza\" title=\"Catherine of Braganza\">Queen <PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St James's Park", "link": "https://wikipedia.org/wiki/St_James%27s_Park"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Catherine of Braganza", "link": "https://wikipedia.org/wiki/Catherine_of_Braganza"}]}, {"year": "1768", "text": "The former slave ship <PERSON><PERSON><PERSON> sinks off Tromøya in Norway.", "html": "1768 - The former <a href=\"https://wikipedia.org/wiki/Slave_ship\" title=\"Slave ship\">slave ship</a> <i><a href=\"https://wikipedia.org/wiki/Fredensborg_(slave_ship)\" title=\"Fredensborg (slave ship)\">Fredensborg</a></i> sinks off <a href=\"https://wikipedia.org/wiki/Trom%C3%B8ya\" title=\"Tromøya\">Tromøya</a> in Norway.", "no_year_html": "The former <a href=\"https://wikipedia.org/wiki/Slave_ship\" title=\"Slave ship\">slave ship</a> <i><a href=\"https://wikipedia.org/wiki/Fredensborg_(slave_ship)\" title=\"Fredensborg (slave ship)\">Fredensborg</a></i> sinks off <a href=\"https://wikipedia.org/wiki/Trom%C3%B8ya\" title=\"Tromøya\">Tromøya</a> in Norway.", "links": [{"title": "Slave ship", "link": "https://wikipedia.org/wiki/Slave_ship"}, {"title": "<PERSON><PERSON><PERSON> (slave ship)", "link": "https://wikipedia.org/wiki/Fredensborg_(slave_ship)"}, {"title": "Tromøya", "link": "https://wikipedia.org/wiki/Trom%C3%B8ya"}]}, {"year": "1821", "text": "<PERSON> wins the independence of the Dominican Republic from Spain and names the new territory the Republic of Spanish Haiti.", "html": "1821 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a> wins the independence of the Dominican Republic from Spain and names the new territory the <a href=\"https://wikipedia.org/wiki/Republic_of_Spanish_Haiti\" title=\"Republic of Spanish Haiti\">Republic of Spanish Haiti</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres\" title=\"<PERSON>\"><PERSON></a> wins the independence of the Dominican Republic from Spain and names the new territory the <a href=\"https://wikipedia.org/wiki/Republic_of_Spanish_Haiti\" title=\"Republic of Spanish Haiti\">Republic of Spanish Haiti</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_N%C3%BA%C3%B1ez_de_C%C3%A1ceres"}, {"title": "Republic of Spanish Haiti", "link": "https://wikipedia.org/wiki/Republic_of_Spanish_Haiti"}]}, {"year": "1822", "text": "<PERSON> is crowned Emperor of Brazil.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Pedro_<PERSON>_of_Brazil\" title=\"Pedro I of Brazil\"><PERSON></a> is crowned <a href=\"https://wikipedia.org/wiki/Emperor_of_Brazil\" class=\"mw-redirect\" title=\"Emperor of Brazil\">Emperor of Brazil</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brazil\" title=\"Pedro I of Brazil\"><PERSON> I</a> is crowned <a href=\"https://wikipedia.org/wiki/Emperor_of_Brazil\" class=\"mw-redirect\" title=\"Emperor of Brazil\">Emperor of Brazil</a>.", "links": [{"title": "<PERSON> of Brazil", "link": "https://wikipedia.org/wiki/Pedro_I_of_Brazil"}, {"title": "Emperor of Brazil", "link": "https://wikipedia.org/wiki/Emperor_of_Brazil"}]}, {"year": "1824", "text": "United States presidential election: Since no candidate received a majority of the total electoral college votes in the election, the United States House of Representatives is given the task of deciding the winner in accordance with the Twelfth Amendment to the United States Constitution.", "html": "1824 - <a href=\"https://wikipedia.org/wiki/1824_United_States_presidential_election\" title=\"1824 United States presidential election\">United States presidential election</a>: Since no candidate received a majority of the total <a href=\"https://wikipedia.org/wiki/Electoral_College_(United_States)\" class=\"mw-redirect\" title=\"Electoral College (United States)\">electoral college</a> votes in the election, the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> is given the task of deciding the winner in accordance with the <a href=\"https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution\" title=\"Twelfth Amendment to the United States Constitution\">Twelfth Amendment to the United States Constitution</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1824_United_States_presidential_election\" title=\"1824 United States presidential election\">United States presidential election</a>: Since no candidate received a majority of the total <a href=\"https://wikipedia.org/wiki/Electoral_College_(United_States)\" class=\"mw-redirect\" title=\"Electoral College (United States)\">electoral college</a> votes in the election, the <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">United States House of Representatives</a> is given the task of deciding the winner in accordance with the <a href=\"https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution\" title=\"Twelfth Amendment to the United States Constitution\">Twelfth Amendment to the United States Constitution</a>.", "links": [{"title": "1824 United States presidential election", "link": "https://wikipedia.org/wiki/1824_United_States_presidential_election"}, {"title": "Electoral College (United States)", "link": "https://wikipedia.org/wiki/Electoral_College_(United_States)"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Twelfth Amendment to the United States Constitution", "link": "https://wikipedia.org/wiki/Twelfth_Amendment_to_the_United_States_Constitution"}]}, {"year": "1828", "text": "Argentine general <PERSON> makes a coup against governor <PERSON>, beginning the Decembrist revolution.", "html": "1828 - Argentine general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes a coup against governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/Decembrist_revolution_(Argentina)\" title=\"Decembrist revolution (Argentina)\">Decembrist revolution</a>.", "no_year_html": "Argentine general <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> makes a coup against governor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, beginning the <a href=\"https://wikipedia.org/wiki/Decembrist_revolution_(Argentina)\" title=\"Decembrist revolution (Argentina)\">Decembrist revolution</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Decembrist revolution (Argentina)", "link": "https://wikipedia.org/wiki/Decembrist_revolution_(Argentina)"}]}, {"year": "1834", "text": "Slavery is abolished in the Cape Colony in accordance with the Slavery Abolition Act 1833.", "html": "1834 - Slavery is abolished in the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a> in accordance with the <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a>.", "no_year_html": "Slavery is abolished in the <a href=\"https://wikipedia.org/wiki/Cape_Colony\" title=\"Cape Colony\">Cape Colony</a> in accordance with the <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a>.", "links": [{"title": "Cape Colony", "link": "https://wikipedia.org/wiki/Cape_Colony"}, {"title": "Slavery Abolition Act 1833", "link": "https://wikipedia.org/wiki/Slavery_Abolition_Act_1833"}]}, {"year": "1862", "text": "American Civil War: In his second State of the Union Address, President <PERSON> reaffirms the necessity of ending slavery as ordered ten weeks earlier in the Emancipation Proclamation.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In his second <a href=\"https://wikipedia.org/wiki/1862_State_of_the_Union_Address\" title=\"1862 State of the Union Address\">State of the Union Address</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> reaffirms the necessity of ending slavery as ordered ten weeks earlier in the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: In his second <a href=\"https://wikipedia.org/wiki/1862_State_of_the_Union_Address\" title=\"1862 State of the Union Address\">State of the Union Address</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> reaffirms the necessity of ending slavery as ordered ten weeks earlier in the <a href=\"https://wikipedia.org/wiki/Emancipation_Proclamation\" title=\"Emancipation Proclamation\">Emancipation Proclamation</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "1862 State of the Union Address", "link": "https://wikipedia.org/wiki/1862_State_of_the_Union_Address"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emancipation Proclamation", "link": "https://wikipedia.org/wiki/Emancipation_Proclamation"}]}, {"year": "1865", "text": "Shaw University, the first historically black university in the southern United States, is founded in Raleigh, North Carolina.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Shaw_University\" title=\"Shaw University\">Shaw University</a>, the first <a href=\"https://wikipedia.org/wiki/Historically_black_university\" class=\"mw-redirect\" title=\"Historically black university\">historically black university</a> in the southern United States, is founded in <a href=\"https://wikipedia.org/wiki/Raleigh,_North_Carolina\" title=\"Raleigh, North Carolina\">Raleigh, North Carolina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shaw_University\" title=\"Shaw University\">Shaw University</a>, the first <a href=\"https://wikipedia.org/wiki/Historically_black_university\" class=\"mw-redirect\" title=\"Historically black university\">historically black university</a> in the southern United States, is founded in <a href=\"https://wikipedia.org/wiki/Raleigh,_North_Carolina\" title=\"Raleigh, North Carolina\">Raleigh, North Carolina</a>.", "links": [{"title": "Shaw University", "link": "https://wikipedia.org/wiki/Shaw_University"}, {"title": "Historically black university", "link": "https://wikipedia.org/wiki/Historically_black_university"}, {"title": "Raleigh, North Carolina", "link": "https://wikipedia.org/wiki/Raleigh,_North_Carolina"}]}, {"year": "1878", "text": "President <PERSON> gets the first telephone installed in the White House.", "html": "1878 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rutherford B. Hayes\"><PERSON></a> gets the first telephone installed in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Rutherford B. Hayes\"><PERSON> B<PERSON> Hayes</a> gets the first telephone installed in the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a>.", "links": [{"title": "<PERSON> B<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1900", "text": "Nicaragua sells canal rights to U.S. for $5 million. The canal agreement fails in March 1901. Great Britain rejects amended treaty", "html": "1900 - Nicaragua sells canal rights to U.S. for $5 million. The canal agreement fails in March 1901. Great Britain rejects amended treaty", "no_year_html": "Nicaragua sells canal rights to U.S. for $5 million. The canal agreement fails in March 1901. Great Britain rejects amended treaty", "links": []}, {"year": "1913", "text": "The Buenos Aires Metro, the first underground railway system in the Southern Hemisphere and in Latin America, begins operation.", "html": "1913 - The <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Underground\" title=\"Buenos Aires Underground\">Buenos Aires Metro</a>, the first underground railway system in the <a href=\"https://wikipedia.org/wiki/Southern_Hemisphere\" title=\"Southern Hemisphere\">Southern Hemisphere</a> and in Latin America, begins operation.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Buenos_Aires_Underground\" title=\"Buenos Aires Underground\">Buenos Aires Metro</a>, the first underground railway system in the <a href=\"https://wikipedia.org/wiki/Southern_Hemisphere\" title=\"Southern Hemisphere\">Southern Hemisphere</a> and in Latin America, begins operation.", "links": [{"title": "Buenos Aires Underground", "link": "https://wikipedia.org/wiki/Buenos_Aires_Underground"}, {"title": "Southern Hemisphere", "link": "https://wikipedia.org/wiki/Southern_Hemisphere"}]}, {"year": "1913", "text": "Crete, having obtained self rule from Turkey after the First Balkan War, is annexed by Greece.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>, having obtained <a href=\"https://wikipedia.org/wiki/Self_rule\" class=\"mw-redirect\" title=\"Self rule\">self rule</a> from <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> after the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>, is annexed by Greece.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>, having obtained <a href=\"https://wikipedia.org/wiki/Self_rule\" class=\"mw-redirect\" title=\"Self rule\">self rule</a> from <a href=\"https://wikipedia.org/wiki/Turkey\" title=\"Turkey\">Turkey</a> after the <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>, is annexed by Greece.", "links": [{"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}, {"title": "Self rule", "link": "https://wikipedia.org/wiki/Self_rule"}, {"title": "Turkey", "link": "https://wikipedia.org/wiki/Turkey"}, {"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}]}, {"year": "1918", "text": "Transylvania unites with Romania, following the incorporation of Bessarabia (March 27) and Bukovina (November 28) and thus concluding the Great Union.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Transylvania\" title=\"Transylvania\">Transylvania</a> <a href=\"https://wikipedia.org/wiki/Union_of_Transylvania_with_Romania\" title=\"Union of Transylvania with Romania\">unites</a> with <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>, following the incorporation of <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> (<a href=\"https://wikipedia.org/wiki/March_27\" title=\"March 27\">March 27</a>) and <a href=\"https://wikipedia.org/wiki/Bukovina\" title=\"Bukovina\">Bukovina</a> (<a href=\"https://wikipedia.org/wiki/November_28\" title=\"November 28\">November 28</a>) and thus concluding the <a href=\"https://wikipedia.org/wiki/Great_Union\" title=\"Great Union\">Great Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Transylvania\" title=\"Transylvania\">Transylvania</a> <a href=\"https://wikipedia.org/wiki/Union_of_Transylvania_with_Romania\" title=\"Union of Transylvania with Romania\">unites</a> with <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>, following the incorporation of <a href=\"https://wikipedia.org/wiki/Bessarabia\" title=\"Bessarabia\">Bessarabia</a> (<a href=\"https://wikipedia.org/wiki/March_27\" title=\"March 27\">March 27</a>) and <a href=\"https://wikipedia.org/wiki/Bukovina\" title=\"Bukovina\">Bukovina</a> (<a href=\"https://wikipedia.org/wiki/November_28\" title=\"November 28\">November 28</a>) and thus concluding the <a href=\"https://wikipedia.org/wiki/Great_Union\" title=\"Great Union\">Great Union</a>.", "links": [{"title": "Transylvania", "link": "https://wikipedia.org/wiki/Transylvania"}, {"title": "Union of Transylvania with Romania", "link": "https://wikipedia.org/wiki/Union_of_Transylvania_with_Romania"}, {"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Bessarabia", "link": "https://wikipedia.org/wiki/Bessarabia"}, {"title": "March 27", "link": "https://wikipedia.org/wiki/March_27"}, {"title": "Bukovina", "link": "https://wikipedia.org/wiki/Bukovina"}, {"title": "November 28", "link": "https://wikipedia.org/wiki/November_28"}, {"title": "Great Union", "link": "https://wikipedia.org/wiki/Great_Union"}]}, {"year": "1918", "text": "Iceland becomes a sovereign state, yet remains a part of the Danish kingdom.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Iceland\" title=\"Kingdom of Iceland\">Iceland</a> becomes a <a href=\"https://wikipedia.org/wiki/Danish%E2%80%93Icelandic_Act_of_Union\" title=\"Danish-Icelandic Act of Union\">sovereign state</a>, yet remains a part of the Danish kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Iceland\" title=\"Kingdom of Iceland\">Iceland</a> becomes a <a href=\"https://wikipedia.org/wiki/Danish%E2%80%93Icelandic_Act_of_Union\" title=\"Danish-Icelandic Act of Union\">sovereign state</a>, yet remains a part of the Danish kingdom.", "links": [{"title": "Kingdom of Iceland", "link": "https://wikipedia.org/wiki/Kingdom_of_Iceland"}, {"title": "Danish-Icelandic Act of Union", "link": "https://wikipedia.org/wiki/Danish%E2%80%93Icelandic_Act_of_Union"}]}, {"year": "1918", "text": "The Kingdom of Serbs, Croats and Slovenes (later known as the Kingdom of Yugoslavia) is proclaimed.", "html": "1918 - The Kingdom of Serbs, Croats and Slovenes (later known as the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Yugoslavia</a>) is proclaimed.", "no_year_html": "The Kingdom of Serbs, Croats and Slovenes (later known as the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Yugoslavia\" title=\"Kingdom of Yugoslavia\">Kingdom of Yugoslavia</a>) is proclaimed.", "links": [{"title": "Kingdom of Yugoslavia", "link": "https://wikipedia.org/wiki/Kingdom_of_Yugoslavia"}]}, {"year": "1919", "text": "Lady <PERSON><PERSON> becomes the first female Member of Parliament (MP) to take her seat in the House of Commons of the United Kingdom. (She had been elected to that position on November 28.)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\">Lady <PERSON><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) to take her seat in the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons of the United Kingdom</a>. (She had been elected to that position on November 28.)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\">Lady <PERSON>tor</a> becomes the first female <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP) to take her seat in the <a href=\"https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom\" title=\"House of Commons of the United Kingdom\">House of Commons of the United Kingdom</a>. (She had been elected to that position on November 28.)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}, {"title": "House of Commons of the United Kingdom", "link": "https://wikipedia.org/wiki/House_of_Commons_of_the_United_Kingdom"}]}, {"year": "1924", "text": "The National Hockey League's first United States-based franchise, the Boston Bruins, plays their first game in league play at home, at the still-extant Boston Arena indoor hockey facility.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a>'s first United States-based franchise, the <a href=\"https://wikipedia.org/wiki/Boston_Bruins\" title=\"Boston Bruins\">Boston Bruins</a>, plays their first game in league play at home, at the still-extant <a href=\"https://wikipedia.org/wiki/Matthews_Arena\" title=\"Matthews Arena\">Boston Arena</a> indoor hockey facility.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a>'s first United States-based franchise, the <a href=\"https://wikipedia.org/wiki/Boston_Bruins\" title=\"Boston Bruins\">Boston Bruins</a>, plays their first game in league play at home, at the still-extant <a href=\"https://wikipedia.org/wiki/Matthews_Arena\" title=\"Matthews Arena\">Boston Arena</a> indoor hockey facility.", "links": [{"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "Boston Bruins", "link": "https://wikipedia.org/wiki/Boston_Bruins"}, {"title": "Matthews Arena", "link": "https://wikipedia.org/wiki/Matthews_Arena"}]}, {"year": "1924", "text": "A Soviet-backed communist 1924 Estonian coup d'état attempt fails in Estonia.", "html": "1924 - A Soviet-backed communist <a href=\"https://wikipedia.org/wiki/1924_Estonian_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"1924 Estonian coup d'état attempt\">1924 Estonian coup d'état attempt</a> fails in <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "no_year_html": "A Soviet-backed communist <a href=\"https://wikipedia.org/wiki/1924_Estonian_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"1924 Estonian coup d'état attempt\">1924 Estonian coup d'état attempt</a> fails in <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>.", "links": [{"title": "1924 Estonian coup d'état attempt", "link": "https://wikipedia.org/wiki/1924_Estonian_coup_d%27%C3%A9tat_attempt"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}]}, {"year": "1934", "text": "<PERSON> is assassinated, paving way for the repressive Great Purge, and Vinnytsia massacre by General Secretary of the Communist Party of the Soviet Union, <PERSON>.", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Assassination of <PERSON>\">assassinated</a>, paving way for the repressive <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a>, and <a href=\"https://wikipedia.org/wiki/Vinnytsia_massacre\" title=\"Vinnytsia massacre\">Vinnytsia massacre</a> by <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Assassination of <PERSON>\">assassinated</a>, paving way for the repressive <a href=\"https://wikipedia.org/wiki/Great_Purge\" title=\"Great Purge\">Great Purge</a>, and <a href=\"https://wikipedia.org/wiki/Vinnytsia_massacre\" title=\"Vinnytsia massacre\">Vinnytsia massacre</a> by <a href=\"https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union\" title=\"General Secretary of the Communist Party of the Soviet Union\">General Secretary of the Communist Party of the Soviet Union</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Assassination of <PERSON>", "link": "https://wikipedia.org/wiki/Assassination_of_<PERSON>_<PERSON>"}, {"title": "Great Purge", "link": "https://wikipedia.org/wiki/Great_Purge"}, {"title": "Vinnytsia massacre", "link": "https://wikipedia.org/wiki/Vinnytsia_massacre"}, {"title": "General Secretary of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/General_Secretary_of_the_Communist_Party_of_the_Soviet_Union"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "World War II: A day after the beginning of the Winter War in Finland, the Cajander III Cabinet resigns and is replaced by the Ryti I Cabinet, while the Finnish Parliament move from Helsinki to Kauhajoki to escape the Soviet airstrikes.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: A day after the beginning of the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> in <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, the <a href=\"https://wikipedia.org/wiki/Cajander_III_Cabinet\" class=\"mw-redirect\" title=\"Cajander III Cabinet\">Cajander III Cabinet</a> resigns and is replaced by the <a href=\"https://wikipedia.org/wiki/Ryti_I_Cabinet\" class=\"mw-redirect\" title=\"Ryti I Cabinet\">Ryti I Cabinet</a>, while the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a> move from <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> to escape the <a href=\"https://wikipedia.org/wiki/Bombing_of_Helsinki_in_World_War_II\" title=\"Bombing of Helsinki in World War II\">Soviet airstrikes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: A day after the beginning of the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> in <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>, the <a href=\"https://wikipedia.org/wiki/Cajander_III_Cabinet\" class=\"mw-redirect\" title=\"Cajander III Cabinet\">Cajander III Cabinet</a> resigns and is replaced by the <a href=\"https://wikipedia.org/wiki/Ryti_I_Cabinet\" class=\"mw-redirect\" title=\"Ryti I Cabinet\">Ryti I Cabinet</a>, while the <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a> move from <a href=\"https://wikipedia.org/wiki/Helsinki\" title=\"Helsinki\">Helsinki</a> to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> to escape the <a href=\"https://wikipedia.org/wiki/Bombing_of_Helsinki_in_World_War_II\" title=\"Bombing of Helsinki in World War II\">Soviet airstrikes</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}, {"title": "<PERSON><PERSON>nder III Cabinet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>nder_III_Cabinet"}, {"title": "Ryti I Cabinet", "link": "https://wikipedia.org/wiki/Ryti_I_Cabinet"}, {"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "Helsinki", "link": "https://wikipedia.org/wiki/Helsinki"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}, {"title": "Bombing of Helsinki in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Helsinki_in_World_War_II"}]}, {"year": "1939", "text": "The Soviet Union establishes the Finnish Democratic Republic puppet state in Terijoki.", "html": "1939 - The Soviet Union establishes the <a href=\"https://wikipedia.org/wiki/Finnish_Democratic_Republic\" title=\"Finnish Democratic Republic\">Finnish Democratic Republic</a> puppet state in <a href=\"https://wikipedia.org/wiki/Zelenogorsk,_Saint_Petersburg\" title=\"Zelenogorsk, Saint Petersburg\">Te<PERSON><PERSON><PERSON></a>.", "no_year_html": "The Soviet Union establishes the <a href=\"https://wikipedia.org/wiki/Finnish_Democratic_Republic\" title=\"Finnish Democratic Republic\">Finnish Democratic Republic</a> puppet state in <a href=\"https://wikipedia.org/wiki/Zelenogorsk,_Saint_Petersburg\" title=\"Zelenogorsk, Saint Petersburg\">Te<PERSON><PERSON><PERSON></a>.", "links": [{"title": "Finnish Democratic Republic", "link": "https://wikipedia.org/wiki/Finnish_Democratic_Republic"}, {"title": "Zelenogorsk, Saint Petersburg", "link": "https://wikipedia.org/wiki/Zelenogorsk,_Saint_Petersburg"}]}, {"year": "1941", "text": "World War II: Emperor <PERSON><PERSON><PERSON><PERSON> of Japan gives his tacit approval to the decision of the imperial council to initiate war against the United States.", "html": "1941 - World War II: Emperor <a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Japan gives his tacit approval to the decision of the imperial council to initiate war against the United States.", "no_year_html": "World War II: Emperor <a href=\"https://wikipedia.org/wiki/Hi<PERSON><PERSON>o\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> of Japan gives his tacit approval to the decision of the imperial council to initiate war against the United States.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hi<PERSON><PERSON>o"}]}, {"year": "1941", "text": "World War II: <PERSON><PERSON><PERSON>, Mayor of New York City and Director of the Office of Civilian Defense, signs Administrative Order 9, creating the Civil Air Patrol.", "html": "1941 - World War II: <a href=\"https://wikipedia.org/wiki/Fiorello_La_Guardia\" title=\"Fiorello La Guardia\"><PERSON>orello La Guardia</a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> and Director of the <a href=\"https://wikipedia.org/wiki/Office_of_Civilian_Defense\" title=\"Office of Civilian Defense\">Office of Civilian Defense</a>, signs <a href=\"https://wikipedia.orghttps://en.wikisource.org/wiki/Administrative_Order_9\" class=\"extiw\" title=\"s:Administrative Order 9\">Administrative Order 9</a>, creating the <a href=\"https://wikipedia.org/wiki/Civil_Air_Patrol\" title=\"Civil Air Patrol\">Civil Air Patrol</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Fiorello_La_Guardia\" title=\"Fiorello La Guardia\"><PERSON>orello La Guardia</a>, <a href=\"https://wikipedia.org/wiki/Mayor_of_New_York_City\" title=\"Mayor of New York City\">Mayor of New York City</a> and Director of the <a href=\"https://wikipedia.org/wiki/Office_of_Civilian_Defense\" title=\"Office of Civilian Defense\">Office of Civilian Defense</a>, signs <a href=\"https://wikipedia.orghttps://en.wikisource.org/wiki/Administrative_Order_9\" class=\"extiw\" title=\"s:Administrative Order 9\">Administrative Order 9</a>, creating the <a href=\"https://wikipedia.org/wiki/Civil_Air_Patrol\" title=\"Civil Air Patrol\">Civil Air Patrol</a>.", "links": [{"title": "<PERSON><PERSON>llo La Guardia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_La_Guardia"}, {"title": "Mayor of New York City", "link": "https://wikipedia.org/wiki/Mayor_of_New_York_City"}, {"title": "Office of Civilian Defense", "link": "https://wikipedia.org/wiki/Office_of_Civilian_Defense"}, {"title": "s:Administrative Order 9", "link": "https://wikipedia.orghttps://en.wikisource.org/wiki/Administrative_Order_9"}, {"title": "Civil Air Patrol", "link": "https://wikipedia.org/wiki/Civil_Air_Patrol"}]}, {"year": "1952", "text": "The New York Daily News reports the news of <PERSON>, the first notable case of sex reassignment surgery.", "html": "1952 - The <i><a href=\"https://wikipedia.org/wiki/New_York_Daily_News\" title=\"New York Daily News\">New York Daily News</a></i> reports the news of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first notable case of <a href=\"https://wikipedia.org/wiki/Gender-affirming_surgery\" title=\"Gender-affirming surgery\">sex reassignment surgery</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/New_York_Daily_News\" title=\"New York Daily News\">New York Daily News</a></i> reports the news of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the first notable case of <a href=\"https://wikipedia.org/wiki/Gender-affirming_surgery\" title=\"Gender-affirming surgery\">sex reassignment surgery</a>.", "links": [{"title": "New York Daily News", "link": "https://wikipedia.org/wiki/New_York_Daily_News"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gender-affirming surgery", "link": "https://wikipedia.org/wiki/Gender-affirming_surgery"}]}, {"year": "1955", "text": "American Civil Rights Movement: In Montgomery, Alabama, seamstress <PERSON> refuses to give up her bus seat to a white man and is arrested for violating the city's racial segregation laws, an incident which leads to that city's bus boycott.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">American Civil Rights Movement</a>: In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, seamstress <a href=\"https://wikipedia.org/wiki/Rosa_Parks\" title=\"Rosa Parks\"><PERSON></a> refuses to give up her bus seat to a white man and is arrested for violating the city's <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> laws, an incident which leads to that city's <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">bus boycott</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">American Civil Rights Movement</a>: In <a href=\"https://wikipedia.org/wiki/Montgomery,_Alabama\" title=\"Montgomery, Alabama\">Montgomery, Alabama</a>, seamstress <a href=\"https://wikipedia.org/wiki/<PERSON>_Parks\" title=\"Rosa Parks\"><PERSON></a> refuses to give up her bus seat to a white man and is arrested for violating the city's <a href=\"https://wikipedia.org/wiki/Racial_segregation\" title=\"Racial segregation\">racial segregation</a> laws, an incident which leads to that city's <a href=\"https://wikipedia.org/wiki/Montgomery_bus_boycott\" title=\"Montgomery bus boycott\">bus boycott</a>.", "links": [{"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}, {"title": "Montgomery, Alabama", "link": "https://wikipedia.org/wiki/Montgomery,_Alabama"}, {"title": "Rosa <PERSON>", "link": "https://wikipedia.org/wiki/Rosa_Parks"}, {"title": "Racial segregation", "link": "https://wikipedia.org/wiki/Racial_segregation"}, {"title": "Montgomery bus boycott", "link": "https://wikipedia.org/wiki/Montgomery_bus_boycott"}]}, {"year": "1958", "text": "The Central African Republic attains self-rule within the French Union.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> attains self-rule within the <a href=\"https://wikipedia.org/wiki/French_Union\" title=\"French Union\">French Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Central_African_Republic\" title=\"Central African Republic\">Central African Republic</a> attains self-rule within the <a href=\"https://wikipedia.org/wiki/French_Union\" title=\"French Union\">French Union</a>.", "links": [{"title": "Central African Republic", "link": "https://wikipedia.org/wiki/Central_African_Republic"}, {"title": "French Union", "link": "https://wikipedia.org/wiki/French_Union"}]}, {"year": "1958", "text": "The Our Lady of the Angels School fire in Chicago kills 92 children and three nuns.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/Our_Lady_of_the_Angels_School_fire\" title=\"Our Lady of the Angels School fire\">Our Lady of the Angels School fire</a> in Chicago kills 92 children and three nuns.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Our_Lady_of_the_Angels_School_fire\" title=\"Our Lady of the Angels School fire\">Our Lady of the Angels School fire</a> in Chicago kills 92 children and three nuns.", "links": [{"title": "Our Lady of the Angels School fire", "link": "https://wikipedia.org/wiki/Our_Lady_of_the_Angels_School_fire"}]}, {"year": "1959", "text": "Cold War: Opening date for signature of the Antarctic Treaty, which sets aside Antarctica as a scientific preserve and bans military activity on the continent.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Opening date for signature of the <a href=\"https://wikipedia.org/wiki/Antarctic_Treaty_System\" title=\"Antarctic Treaty System\">Antarctic Treaty</a>, which sets aside Antarctica as a scientific preserve and bans military activity on the continent.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Opening date for signature of the <a href=\"https://wikipedia.org/wiki/Antarctic_Treaty_System\" title=\"Antarctic Treaty System\">Antarctic Treaty</a>, which sets aside Antarctica as a scientific preserve and bans military activity on the continent.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Antarctic Treaty System", "link": "https://wikipedia.org/wiki/Antarctic_Treaty_System"}]}, {"year": "1960", "text": "Patrice <PERSON> is arrested by <PERSON><PERSON><PERSON>'s men on the banks of the Sankuru River, for inciting the army to rebellion.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s men on the banks of the <a href=\"https://wikipedia.org/wiki/Sankuru_River\" title=\"Sankuru River\">Sankuru River</a>, for inciting the army to rebellion.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is arrested by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>'s men on the banks of the <a href=\"https://wikipedia.org/wiki/Sankuru_River\" title=\"Sankuru River\">Sankuru River</a>, for inciting the army to rebellion.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Sankuru River", "link": "https://wikipedia.org/wiki/Sankuru_River"}]}, {"year": "1963", "text": "Nagaland, became the 16th state of India.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Nagaland\" title=\"Nagaland\">Nagaland</a>, became the 16th state of India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nagaland\" title=\"Nagaland\">Nagaland</a>, became the 16th state of India.", "links": [{"title": "Nagaland", "link": "https://wikipedia.org/wiki/Nagaland"}]}, {"year": "1964", "text": "Vietnam War: U.S. President <PERSON> and his top-ranking advisers meet to discuss plans to bomb North Vietnam.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his top-ranking advisers meet to discuss plans to bomb <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and his top-ranking advisers meet to discuss plans to bomb <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}]}, {"year": "1969", "text": "Vietnam War: The first draft lottery in the United States is held since World War II.", "html": "1969 - Vietnam War: The first <a href=\"https://wikipedia.org/wiki/Draft_lottery_(1969)\" class=\"mw-redirect\" title=\"Draft lottery (1969)\">draft lottery</a> in the United States is held since <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "no_year_html": "Vietnam War: The first <a href=\"https://wikipedia.org/wiki/Draft_lottery_(1969)\" class=\"mw-redirect\" title=\"Draft lottery (1969)\">draft lottery</a> in the United States is held since <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>.", "links": [{"title": "Draft lottery (1969)", "link": "https://wikipedia.org/wiki/Draft_lottery_(1969)"}, {"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}]}, {"year": "1971", "text": "Cambodian Civil War: Khmer Rouge rebels intensify assaults on Cambodian government positions, forcing their retreat from Kompong Thmar and nearby Ba Ray.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> rebels intensify assaults on <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodian</a> government positions, forcing their retreat from Kompong Thmar and nearby Ba Ray.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cambodian_Civil_War\" title=\"Cambodian Civil War\">Cambodian Civil War</a>: <a href=\"https://wikipedia.org/wiki/Khmer_Rouge\" title=\"Khmer Rouge\">Khmer Rouge</a> rebels intensify assaults on <a href=\"https://wikipedia.org/wiki/Cambodia\" title=\"Cambodia\">Cambodian</a> government positions, forcing their retreat from Kompong Thmar and nearby Ba Ray.", "links": [{"title": "Cambodian Civil War", "link": "https://wikipedia.org/wiki/Cambodian_Civil_War"}, {"title": "Khmer Rouge", "link": "https://wikipedia.org/wiki/Khmer_Rouge"}, {"title": "Cambodia", "link": "https://wikipedia.org/wiki/Cambodia"}]}, {"year": "1971", "text": "Purge of Croatian Spring leaders starts in Yugoslavia at the meeting of the League of Communists at the Karađorđevo estate.", "html": "1971 - Purge of <a href=\"https://wikipedia.org/wiki/Croatian_Spring\" title=\"Croatian Spring\">Croatian Spring</a> leaders starts in <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> at the meeting of the <a href=\"https://wikipedia.org/wiki/League_of_Communists_of_Yugoslavia\" title=\"League of Communists of Yugoslavia\">League of Communists</a> at the <a href=\"https://wikipedia.org/wiki/Kara%C4%91or%C4%91evo_estate\" title=\"Karađorđevo estate\">Karađorđevo estate</a>.", "no_year_html": "Purge of <a href=\"https://wikipedia.org/wiki/Croatian_Spring\" title=\"Croatian Spring\">Croatian Spring</a> leaders starts in <a href=\"https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia\" title=\"Socialist Federal Republic of Yugoslavia\">Yugoslavia</a> at the meeting of the <a href=\"https://wikipedia.org/wiki/League_of_Communists_of_Yugoslavia\" title=\"League of Communists of Yugoslavia\">League of Communists</a> at the <a href=\"https://wikipedia.org/wiki/Kara%C4%91or%C4%91evo_estate\" title=\"Karađorđevo estate\">Karađorđevo estate</a>.", "links": [{"title": "Croatian Spring", "link": "https://wikipedia.org/wiki/Croatian_Spring"}, {"title": "Socialist Federal Republic of Yugoslavia", "link": "https://wikipedia.org/wiki/Socialist_Federal_Republic_of_Yugoslavia"}, {"title": "League of Communists of Yugoslavia", "link": "https://wikipedia.org/wiki/League_of_Communists_of_Yugoslavia"}, {"title": "Karađorđevo estate", "link": "https://wikipedia.org/wiki/Kara%C4%91or%C4%91evo_estate"}]}, {"year": "1973", "text": "Papua New Guinea gains self-government from Australia.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a> gains self-government from Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a> gains self-government from Australia.", "links": [{"title": "Papua New Guinea", "link": "https://wikipedia.org/wiki/Papua_New_Guinea"}]}, {"year": "1974", "text": "TWA Flight 514, a Boeing 727, crashes northwest of Dulles International Airport, killing all 92 people on board.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/TWA_Flight_514\" title=\"TWA Flight 514\">TWA Flight 514</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>, crashes northwest of <a href=\"https://wikipedia.org/wiki/Dulles_International_Airport\" title=\"Dulles International Airport\">Dulles International Airport</a>, killing all 92 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TWA_Flight_514\" title=\"TWA Flight 514\">TWA Flight 514</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_727\" title=\"Boeing 727\">Boeing 727</a>, crashes northwest of <a href=\"https://wikipedia.org/wiki/Dulles_International_Airport\" title=\"Dulles International Airport\">Dulles International Airport</a>, killing all 92 people on board.", "links": [{"title": "TWA Flight 514", "link": "https://wikipedia.org/wiki/TWA_Flight_514"}, {"title": "Boeing 727", "link": "https://wikipedia.org/wiki/Boeing_727"}, {"title": "Dulles International Airport", "link": "https://wikipedia.org/wiki/Dulles_International_Airport"}]}, {"year": "1974", "text": "Northwest Orient Airlines Flight 6231, another Boeing 727, crashes northwest of John F. Kennedy International Airport.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_6231\" title=\"Northwest Orient Airlines Flight 6231\">Northwest Orient Airlines Flight 6231</a>, another Boeing 727, crashes northwest of <a href=\"https://wikipedia.org/wiki/John_F._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_6231\" title=\"Northwest Orient Airlines Flight 6231\">Northwest Orient Airlines Flight 6231</a>, another Boeing 727, crashes northwest of <a href=\"https://wikipedia.org/wiki/John_F._Kennedy_International_Airport\" title=\"John F. Kennedy International Airport\">John F. Kennedy International Airport</a>.", "links": [{"title": "Northwest Orient Airlines Flight 6231", "link": "https://wikipedia.org/wiki/Northwest_Orient_Airlines_Flight_6231"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_Kennedy_International_Airport"}]}, {"year": "1981", "text": "Inex-Adria Aviopromet Flight 1308, a McDonnell Douglas MD-80, crashes in Corsica, killing all 180 people on board.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_1308\" title=\"Inex-Adria Aviopromet Flight 1308\">Inex-Adria Aviopromet Flight 1308</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-80</a>, crashes in <a href=\"https://wikipedia.org/wiki/Corsica\" title=\"Corsica\">Corsica</a>, killing all 180 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_1308\" title=\"Inex-Adria Aviopromet Flight 1308\">Inex-Adria Aviopromet Flight 1308</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-80</a>, crashes in <a href=\"https://wikipedia.org/wiki/Corsica\" title=\"Corsica\">Corsica</a>, killing all 180 people on board.", "links": [{"title": "Inex-Adria Aviopromet Flight 1308", "link": "https://wikipedia.org/wiki/Inex-Adria_Aviopromet_Flight_1308"}, {"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-80"}, {"title": "Corsica", "link": "https://wikipedia.org/wiki/Corsica"}]}, {"year": "1984", "text": "NASA conducts the Controlled Impact Demonstration, wherein an airliner is deliberately crashed in order to test technologies and gather data to help improve survivability of crashes.", "html": "1984 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> conducts the <a href=\"https://wikipedia.org/wiki/Controlled_Impact_Demonstration\" title=\"Controlled Impact Demonstration\">Controlled Impact Demonstration</a>, wherein an airliner is deliberately crashed in order to test technologies and gather data to help improve survivability of crashes.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a> conducts the <a href=\"https://wikipedia.org/wiki/Controlled_Impact_Demonstration\" title=\"Controlled Impact Demonstration\">Controlled Impact Demonstration</a>, wherein an airliner is deliberately crashed in order to test technologies and gather data to help improve survivability of crashes.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Controlled Impact Demonstration", "link": "https://wikipedia.org/wiki/Controlled_Impact_Demonstration"}]}, {"year": "1988", "text": "World AIDS Day is proclaimed worldwide by the UN member states.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/World_AIDS_Day\" title=\"World AIDS Day\">World AIDS Day</a> is proclaimed worldwide by the <a href=\"https://wikipedia.org/wiki/Member_states_of_the_United_Nations\" title=\"Member states of the United Nations\">UN member states</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_AIDS_Day\" title=\"World AIDS Day\">World AIDS Day</a> is proclaimed worldwide by the <a href=\"https://wikipedia.org/wiki/Member_states_of_the_United_Nations\" title=\"Member states of the United Nations\">UN member states</a>.", "links": [{"title": "World AIDS Day", "link": "https://wikipedia.org/wiki/World_AIDS_Day"}, {"title": "Member states of the United Nations", "link": "https://wikipedia.org/wiki/Member_states_of_the_United_Nations"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, is named as the Prime Minister of Pakistan, becoming the first female leader to lead a Muslim nation.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is named as the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>, becoming the first female leader to lead a Muslim nation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, is named as the <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Pakistan\" title=\"Prime Minister of Pakistan\">Prime Minister of Pakistan</a>, becoming the first female leader to lead a Muslim nation.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Pakistan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Pakistan"}]}, {"year": "1989", "text": "Philippine coup attempt: The right-wing military rebel Reform the Armed Forces Movement attempts to oust Philippine President <PERSON><PERSON> in a failed bloody coup d'état.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/1989_Philippine_coup_attempt\" title=\"1989 Philippine coup attempt\">Philippine coup attempt</a>: The right-wing military rebel <a href=\"https://wikipedia.org/wiki/Reform_the_Armed_Forces_Movement\" title=\"Reform the Armed Forces Movement\">Reform the Armed Forces Movement</a> attempts to oust <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">Philippine President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in a failed bloody <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1989_Philippine_coup_attempt\" title=\"1989 Philippine coup attempt\">Philippine coup attempt</a>: The right-wing military rebel <a href=\"https://wikipedia.org/wiki/Reform_the_Armed_Forces_Movement\" title=\"Reform the Armed Forces Movement\">Reform the Armed Forces Movement</a> attempts to oust <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">Philippine President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> in a failed bloody <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>.", "links": [{"title": "1989 Philippine coup attempt", "link": "https://wikipedia.org/wiki/1989_Philippine_coup_attempt"}, {"title": "Reform the Armed Forces Movement", "link": "https://wikipedia.org/wiki/Reform_the_Armed_Forces_Movement"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}]}, {"year": "1989", "text": "Cold War: East Germany's parliament abolishes the constitutional provision granting the Communist Party the leading role in the state.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>'s parliament abolishes the constitutional provision granting the Communist Party the leading role in the state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germany</a>'s parliament abolishes the constitutional provision granting the Communist Party the leading role in the state.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1990", "text": "Channel Tunnel sections started from the United Kingdom and France meet beneath the seabed.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a> sections started from the United Kingdom and France meet beneath the seabed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a> sections started from the United Kingdom and France meet beneath the seabed.", "links": [{"title": "Channel Tunnel", "link": "https://wikipedia.org/wiki/Channel_Tunnel"}]}, {"year": "1991", "text": "Cold War: Ukrainian voters overwhelmingly approve a referendum for independence from the Soviet Union.", "html": "1991 - Cold War: <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> voters overwhelmingly approve a <a href=\"https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum\" title=\"1991 Ukrainian independence referendum\">referendum for independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "Cold War: <a href=\"https://wikipedia.org/wiki/Ukraine\" title=\"Ukraine\">Ukrainian</a> voters overwhelmingly approve a <a href=\"https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum\" title=\"1991 Ukrainian independence referendum\">referendum for independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Ukraine", "link": "https://wikipedia.org/wiki/Ukraine"}, {"title": "1991 Ukrainian independence referendum", "link": "https://wikipedia.org/wiki/1991_Ukrainian_independence_referendum"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1997", "text": "In the Indian state of Bihar, <PERSON><PERSON><PERSON> attacks the CPI (ML) Party Unity stronghold Lakshmanpur-Bathe, killing 63 lower caste people.", "html": "1997 - In the Indian state of <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>n<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> attacks the <a href=\"https://wikipedia.org/wiki/Central_Organising_Committee,_Communist_Party_of_India_(Marxist%E2%80%93Leninist)_Party_Unity\" title=\"Central Organising Committee, Communist Party of India (Marxist-Leninist) Party Unity\">CPI (ML) Party Unity</a> stronghold Lakshmanpur-Bathe, killing 63 lower caste people.", "no_year_html": "In the Indian state of <a href=\"https://wikipedia.org/wiki/Bihar\" title=\"Bihar\">Bihar</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>n<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> attacks the <a href=\"https://wikipedia.org/wiki/Central_Organising_Committee,_Communist_Party_of_India_(Marxist%E2%80%93Leninist)_Party_Unity\" title=\"Central Organising Committee, Communist Party of India (Marxist-Leninist) Party Unity\">CPI (ML) Party Unity</a> stronghold Lakshmanpur-Bathe, killing 63 lower caste people.", "links": [{"title": "Bihar", "link": "https://wikipedia.org/wiki/Bihar"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>n<PERSON>_<PERSON>a"}, {"title": "Central Organising Committee, Communist Party of India (Marxist-Leninist) Party Unity", "link": "https://wikipedia.org/wiki/Central_Organising_Committee,_Communist_Party_of_India_(Marxist%E2%80%93Leninist)_Party_Unity"}]}, {"year": "1997", "text": "Fourteen-year-old <PERSON> opens fire at a group of students in Heath High School in West Paducah, Kentucky, killing three and injuring five.", "html": "1997 - Fourteen-year-old <PERSON> <a href=\"https://wikipedia.org/wiki/1997_Heath_High_School_shooting\" title=\"1997 Heath High School shooting\">opens fire</a> at a group of students in Heath High School in <a href=\"https://wikipedia.org/wiki/West_Paducah,_Kentucky\" title=\"West Paducah, Kentucky\">West Paducah, Kentucky</a>, killing three and injuring five.", "no_year_html": "Fourteen-year-old <PERSON> <a href=\"https://wikipedia.org/wiki/1997_Heath_High_School_shooting\" title=\"1997 Heath High School shooting\">opens fire</a> at a group of students in Heath High School in <a href=\"https://wikipedia.org/wiki/West_Paducah,_Kentucky\" title=\"West Paducah, Kentucky\">West Paducah, Kentucky</a>, killing three and injuring five.", "links": [{"title": "1997 Heath High School shooting", "link": "https://wikipedia.org/wiki/1997_Heath_High_School_shooting"}, {"title": "West Paducah, Kentucky", "link": "https://wikipedia.org/wiki/West_Paducah,_Kentucky"}]}, {"year": "2000", "text": "<PERSON> is inaugurated as the president of Mexico, marking the first peaceful transfer of executive federal power to an opposing political party following a free and democratic election in Mexico's history.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">president of Mexico</a>, marking the first peaceful transfer of executive federal power to an opposing political party following a free and democratic election in Mexico's history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is inaugurated as the <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">president of Mexico</a>, marking the first peaceful transfer of executive federal power to an opposing political party following a free and democratic election in Mexico's history.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Fox"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "2001", "text": "The United Russia political party was founded.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/United_Russia\" title=\"United Russia\">United Russia</a> political party was founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Russia\" title=\"United Russia\">United Russia</a> political party was founded.", "links": [{"title": "United Russia", "link": "https://wikipedia.org/wiki/United_Russia"}]}, {"year": "2005", "text": "As a result of the merger of the Perm Oblast and the Komi-Permyak Autonomous Okrug, a new subject of the Russian Federation, the Perm Krai, was created.", "html": "2005 - As a result of the merger of the <a href=\"https://wikipedia.org/wiki/Perm_Oblast\" title=\"Perm Oblast\">Perm Oblast</a> and the <a href=\"https://wikipedia.org/wiki/Komi-Permyak_Autonomous_Okrug\" title=\"Komi-Permyak Autonomous Okrug\">Komi-Permyak Autonomous Okrug</a>, a new <a href=\"https://wikipedia.org/wiki/Federal_subjects_of_Russia\" title=\"Federal subjects of Russia\">subject</a> of the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a>, the <a href=\"https://wikipedia.org/wiki/Perm_Krai\" title=\"Perm Krai\">Perm Krai</a>, was created.", "no_year_html": "As a result of the merger of the <a href=\"https://wikipedia.org/wiki/Perm_Oblast\" title=\"Perm Oblast\">Perm Oblast</a> and the <a href=\"https://wikipedia.org/wiki/Komi-Permyak_Autonomous_Okrug\" title=\"Komi-Permyak Autonomous Okrug\">Komi-Permyak Autonomous Okrug</a>, a new <a href=\"https://wikipedia.org/wiki/Federal_subjects_of_Russia\" title=\"Federal subjects of Russia\">subject</a> of the <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russian Federation</a>, the <a href=\"https://wikipedia.org/wiki/Perm_Krai\" title=\"Perm Krai\">Perm Krai</a>, was created.", "links": [{"title": "Perm Oblast", "link": "https://wikipedia.org/wiki/Perm_Oblast"}, {"title": "Komi-Permyak Autonomous Okrug", "link": "https://wikipedia.org/wiki/Komi-Permyak_Autonomous_Okrug"}, {"title": "Federal subjects of Russia", "link": "https://wikipedia.org/wiki/Federal_subjects_of_Russia"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}, {"title": "Perm Krai", "link": "https://wikipedia.org/wiki/Perm_Krai"}]}, {"year": "2006", "text": "The law on same-sex marriage comes into force in South Africa, legalizing same-sex marriage for the first time on the African continent.", "html": "2006 - The <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_South_Africa\" title=\"Same-sex marriage in South Africa\">law on same-sex marriage</a> comes into force in <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, legalizing same-sex marriage for the first time on the <a href=\"https://wikipedia.org/wiki/Africa\" title=\"Africa\">African continent</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Same-sex_marriage_in_South_Africa\" title=\"Same-sex marriage in South Africa\">law on same-sex marriage</a> comes into force in <a href=\"https://wikipedia.org/wiki/South_Africa\" title=\"South Africa\">South Africa</a>, legalizing same-sex marriage for the first time on the <a href=\"https://wikipedia.org/wiki/Africa\" title=\"Africa\">African continent</a>.", "links": [{"title": "Same-sex marriage in South Africa", "link": "https://wikipedia.org/wiki/Same-sex_marriage_in_South_Africa"}, {"title": "South Africa", "link": "https://wikipedia.org/wiki/South_Africa"}, {"title": "Africa", "link": "https://wikipedia.org/wiki/Africa"}]}, {"year": "2009", "text": "The Treaty of Lisbon entered into force in the European Union.", "html": "2009 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lisbon\" title=\"Treaty of Lisbon\">Treaty of Lisbon</a> entered into force in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Lisbon\" title=\"Treaty of Lisbon\">Treaty of Lisbon</a> entered into force in the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>.", "links": [{"title": "Treaty of Lisbon", "link": "https://wikipedia.org/wiki/Treaty_of_Lisbon"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "2011", "text": "The Alma-Ata Metro was opened.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Almaty_Metro\" title=\"Almaty Metro\">Alma-Ata Metro</a> was opened.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Almaty_Metro\" title=\"Almaty Metro\">Alma-Ata Metro</a> was opened.", "links": [{"title": "Almaty Metro", "link": "https://wikipedia.org/wiki/Almaty_Metro"}]}, {"year": "2018", "text": "The Oulu Police informed the public about the first offence of the much larger child sexual exploitation in Oulu, Finland.", "html": "2018 - The Oulu Police informed the public about the first offence of the <a href=\"https://wikipedia.org/wiki/Oulu_child_sexual_exploitation_scandal\" title=\"Oulu child sexual exploitation scandal\">much larger child sexual exploitation</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "no_year_html": "The Oulu Police informed the public about the first offence of the <a href=\"https://wikipedia.org/wiki/Oulu_child_sexual_exploitation_scandal\" title=\"Oulu child sexual exploitation scandal\">much larger child sexual exploitation</a> in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Finland\" title=\"Finland\">Finland</a>.", "links": [{"title": "Oulu child sexual exploitation scandal", "link": "https://wikipedia.org/wiki/Oulu_child_sexual_exploitation_scandal"}, {"title": "Oulu", "link": "https://wikipedia.org/wiki/Oulu"}, {"title": "Finland", "link": "https://wikipedia.org/wiki/Finland"}]}, {"year": "2019", "text": "Arsenal Women 11-1 Bristol City Women breaks the record for most goals scored in a FA Women's Super League match, with <PERSON><PERSON> involved in ten of the eleven Arsenal goals.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Arsenal_Women_11%E2%80%931_Bristol_City_Women\" title=\"Arsenal Women 11-1 Bristol City Women\">Arsenal Women 11-1 Bristol City Women</a> breaks the record for most goals scored in a <a href=\"https://wikipedia.org/wiki/FA_Women%27s_Super_League\" class=\"mw-redirect\" title=\"FA Women's Super League\">FA Women's Super League</a> match, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Miedema\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> involved in ten of the eleven Arsenal goals.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arsenal_Women_11%E2%80%931_Bristol_City_Women\" title=\"Arsenal Women 11-1 Bristol City Women\">Arsenal Women 11-1 Bristol City Women</a> breaks the record for most goals scored in a <a href=\"https://wikipedia.org/wiki/FA_Women%27s_Super_League\" class=\"mw-redirect\" title=\"FA Women's Super League\">FA Women's Super League</a> match, with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>edema\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> involved in ten of the eleven Arsenal goals.", "links": [{"title": "Arsenal Women 11-1 Bristol City Women", "link": "https://wikipedia.org/wiki/Arsenal_Women_11%E2%80%931_Bristol_City_Women"}, {"title": "FA Women's Super League", "link": "https://wikipedia.org/wiki/FA_Women%27s_Super_League"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "The outbreak of coronavirus infection began in Wuhan.", "html": "2019 - The <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">outbreak of coronavirus infection</a> began in <a href=\"https://wikipedia.org/wiki/Wuhan\" title=\"Wuhan\"><PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">outbreak of coronavirus infection</a> began in <a href=\"https://wikipedia.org/wiki/<PERSON>han\" title=\"Wuhan\"><PERSON><PERSON></a>.", "links": [{"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>han"}]}, {"year": "2020", "text": "The Arecibo Telescope collapsed.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/Arecibo_Telescope\" title=\"Arecibo Telescope\">Arecibo Telescope</a> collapsed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arecibo_Telescope\" title=\"Arecibo Telescope\">Arecibo Telescope</a> collapsed.", "links": [{"title": "Arecibo Telescope", "link": "https://wikipedia.org/wiki/Arecibo_Telescope"}]}], "Births": [{"year": "624", "text": "<PERSON> ibn <PERSON>, the second Shia Imam (d. 670)", "html": "624 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, the second Shia Imam (d. 670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> ibn <PERSON>\"><PERSON> ibn <PERSON></a>, the second Shia Imam (d. 670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1081", "text": "<PERSON>, French king (d. 1137)", "html": "1081 - <a href=\"https://wikipedia.org/wiki/Louis_VI_of_France\" title=\"Louis VI of France\"><PERSON> VI</a>, French king (d. 1137)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_VI_of_France\" title=\"<PERSON> of France\"><PERSON> VI</a>, French king (d. 1137)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_VI_of_France"}]}, {"year": "1083", "text": "<PERSON>, Byzantine physician and scholar (d. 1153)", "html": "1083 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine physician and scholar (d. 1153)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine physician and scholar (d. 1153)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1415", "text": "<PERSON>, Polish historian (d. 1480)", "html": "1415 - <a href=\"https://wikipedia.org/wiki/Jan_D%C5%82ugosz\" title=\"<PERSON>\"><PERSON></a>, Polish historian (d. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jan_D%C5%82ugosz\" title=\"<PERSON>\"><PERSON></a>, Polish historian (d. 1480)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_D%C5%82ugosz"}]}, {"year": "1438", "text": "<PERSON>, Duke of Bourbon, son of <PERSON> (d. 1503)", "html": "1438 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a>, son of <PERSON> (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a>, son of <PERSON> (d. 1503)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Bourbon"}]}, {"year": "1443", "text": "<PERSON> of France, French princess (d. 1495)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/Magdalena_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a>, French princess (d. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Magdalena_of_France\" class=\"mw-redirect\" title=\"<PERSON> of France\"><PERSON> of France</a>, French princess (d. 1495)", "links": [{"title": "Magdalena of France", "link": "https://wikipedia.org/wiki/Magdalena_of_France"}]}, {"year": "1521", "text": "<PERSON><PERSON>, Japanese daimyō (d. 1573)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Takeda_Shingen\" title=\"Takeda Shingen\"><PERSON><PERSON></a>, Japanese daimyō (d. 1573)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takeda_Shingen\" title=\"Takeda Shingen\"><PERSON><PERSON></a>, Japanese daimyō (d. 1573)", "links": [{"title": "Takeda Shingen", "link": "https://wikipedia.org/wiki/Takeda_Shingen"}]}, {"year": "1525", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech physician and astronomer (d. 1600)", "html": "1525 - <a href=\"https://wikipedia.org/wiki/Tade%C3%A1%C5%A1_H%C3%A1jek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech physician and astronomer (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tade%C3%A1%C5%A1_H%C3%A1jek\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech physician and astronomer (d. 1600)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tade%C3%A1%C5%A1_H%C3%A1jek"}]}, {"year": "1530", "text": "<PERSON>, Italian Jesuit (d. 1616)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Jesuit (d. 1616)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Real<PERSON>\"><PERSON></a>, Italian Jesuit (d. 1616)", "links": [{"title": "Bernardino <PERSON>", "link": "https://wikipedia.org/wiki/Bernardino_Realino"}]}, {"year": "1561", "text": "<PERSON> of Brunswick-Wolfe<PERSON><PERSON><PERSON><PERSON>, Duchess consort of Pomerania-Wolgas<PERSON> (d. 1631)", "html": "1561 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brunswick-Wolfenb%C3%<PERSON>ttel_(1561%E2%80%931631)\" title=\"<PERSON> of Brunswick-Wolfenbüttel (1561-1631)\"><PERSON> of Brunswick-Wolfenb<PERSON></a>, Duchess consort of Pomerania-Wolgast (d. 1631)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel_(1561%E2%80%931631)\" title=\"<PERSON> of Brunswick-Wolfenbüttel (1561-1631)\"><PERSON> of Brunswick-Wolfenbü<PERSON>l</a>, Duchess consort of Pomerania-Wolgast (d. 1631)", "links": [{"title": "<PERSON> of Brunswick-Wolfenbüttel (1561-1631)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_of_Brunswick-Wolfenb%C3%BCttel_(1561%E2%80%931631)"}]}, {"year": "1580", "text": "<PERSON><PERSON><PERSON>, French astronomer and historian (d. 1637)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and historian (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French astronomer and historian (d. 1637)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1690", "text": "<PERSON>, 1st Earl of Hardwicke, English lawyer and politician, Lord Chancellor of the United Kingdom (d. 1764)", "html": "1690 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Hardwicke\" title=\"<PERSON>, 1st Earl of Hardwicke\"><PERSON>, 1st Earl of Hardwicke</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (d. 1764)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Hardwicke\" title=\"<PERSON>, 1st Earl of Hardwicke\"><PERSON>, 1st Earl of Hardwicke</a>, English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord Chancellor of the United Kingdom</a> (d. 1764)", "links": [{"title": "<PERSON>, 1st Earl of Hardwicke", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Hardwicke"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1709", "text": "<PERSON>, Czech composer, violinist, and conductor (d. 1789)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer, violinist, and conductor (d. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech composer, violinist, and conductor (d. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, French sculptor (d. 1791)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON>, German chemist and academic (d. 1817)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (d. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1761", "text": "<PERSON>, French-English sculptor, founded Madame Tussauds Wax Museum (d. 1850)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English sculptor, founded <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame Tussauds\">Madame Tussauds Wax Museum</a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English sculptor, founded <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>\" title=\"Madame Tussauds\">Madame Tussauds Wax Museum</a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON> Tu<PERSON>uds", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, Russian mathematician and geometer (d. 1856)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and geometer (d. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and geometer (d. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1800", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian poet (d. 1855)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Mih%C3%A1ly_V%C3%B6r%C3%B6smarty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian poet (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mih%C3%A1ly_V%C3%B6r%C3%B6smarty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian poet (d. 1855)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mih%C3%A1ly_V%C3%B6r%C3%B6smarty"}]}, {"year": "1805", "text": "9th Dalai Lama, Tibetan Buddhist spiritual leader (d. 1815)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/9th_<PERSON>ai_Lama\" title=\"9th Dalai Lama\">9th Dalai Lama</a>, Tibetan Buddhist spiritual leader (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/9th_Dalai_Lama\" title=\"9th Dalai Lama\">9th Dalai Lama</a>, Tibetan Buddhist spiritual leader (d. 1815)", "links": [{"title": "9th <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/9th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON> Denmark (d. 1925)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/Alexandra_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alexandra_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (d. 1925)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/Alexandra_of_Denmark"}]}, {"year": "1846", "text": "<PERSON><PERSON>, Burmese monk and philosopher (d. 1923)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/Ledi_Sayadaw\" title=\"Ledi Sayadaw\"><PERSON><PERSON></a>, Burmese monk and philosopher (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Led<PERSON>_Sayadaw\" title=\"<PERSON><PERSON> Sayadaw\"><PERSON><PERSON></a>, Burmese monk and philosopher (d. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ledi_Sayadaw"}]}, {"year": "1847", "text": "<PERSON>, American poet (d. 1920)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English-Australian politician, 21st Premier of Tasmania (d. 1943)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, English-Australian politician, 21st <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1943)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Australian_politician)"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish painter and critic (d. 1923)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Eligi<PERSON><PERSON>_<PERSON>domski\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter and critic (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish painter and critic (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eligi<PERSON><PERSON>_<PERSON>ewiadomski"}]}, {"year": "1871", "text": "<PERSON>, English cricketer (d. 1944)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, American historian, scholar, and academic (d. 1974)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, scholar, and academic (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, scholar, and academic (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON><PERSON><PERSON><PERSON>, German painter and etcher (d. 1976)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and etcher (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and etcher (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American detective novelist (d. 1975)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American detective novelist (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American detective novelist (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Chinese general and politician, 1st Vice Chairman of the People's Republic of China (d. 1976)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Chairman_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Chairman of the People's Republic of China\">Vice Chairman of the People's Republic of China</a> (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician, 1st <a href=\"https://wikipedia.org/wiki/Vice_Chairman_of_the_People%27s_Republic_of_China\" class=\"mw-redirect\" title=\"Vice Chairman of the People's Republic of China\">Vice Chairman of the People's Republic of China</a> (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice Chairman of the People's Republic of China", "link": "https://wikipedia.org/wiki/Vice_Chairman_of_the_People%27s_Republic_of_China"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Brazilian herpetologist (d. 1982)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Afr%C3%A2nio_Pomp%C3%ADlio_Gastos_do_Amaral\" class=\"mw-redirect\" title=\"Afrânio Pompílio Gastos do Amaral\">A<PERSON><PERSON><PERSON><PERSON> Pompílio <PERSON></a>, Brazilian herpetologist (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afr%C3%A2nio_Pomp%C3%ADlio_Gastos_do_Amaral\" class=\"mw-redirect\" title=\"Afrânio Pompílio Gastos do Amaral\">A<PERSON><PERSON><PERSON>io Pompílio <PERSON></a>, Brazilian herpetologist (d. 1982)", "links": [{"title": "Afrânio Pompílio <PERSON> Amaral", "link": "https://wikipedia.org/wiki/Afr%C3%A2nio_Pomp%C3%ADlio_Gastos_do_Amaral"}]}, {"year": "1895", "text": "<PERSON>, English farmer, soldier, and author (d. 1977)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer, soldier, and author (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English farmer, soldier, and author (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Russian general and politician, 2nd Minister of Defence for the Soviet Union (d. 1974)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general and politician, 2nd <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)\" title=\"Minister of Defence (Soviet Union)\">Minister of Defence for the Soviet Union</a> (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Defence (Soviet Union)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Soviet_Union)"}]}, {"year": "1898", "text": "<PERSON>, Canadian lawyer and politician, 12th Premier of Manitoba (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Manitoba\" title=\"Premier of Manitoba\">Premier of Manitoba</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Manitoba", "link": "https://wikipedia.org/wiki/Premier_of_Manitoba"}]}, {"year": "1898", "text": "<PERSON>, Australian-American actor and singer (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and singer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American actor and singer (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON>, Australian artist, illustrator and print maker (d. 1987)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>rna_<PERSON>_<PERSON>\" title=\"Karna <PERSON>\"><PERSON><PERSON></a>, Australian artist, illustrator and print maker (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Karna <PERSON>\"><PERSON><PERSON></a>, Australian artist, illustrator and print maker (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karna_Maria_Birmingham"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Hungarian-Israeli violinist and educator (d. 1988)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Ilona_Feh%C3%A9r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli violinist and educator (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilona_Feh%C3%A9r\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian-Israeli violinist and educator (d. 1988)", "links": [{"title": "Ilona Fehér", "link": "https://wikipedia.org/wiki/Ilona_Feh%C3%A9r"}]}, {"year": "1903", "text": "<PERSON>, Soviet economic planner, member of the Politburo of the Central Committee of the Communist Party of the Soviet Union (d. 1950)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet <a href=\"https://wikipedia.org/wiki/Planned_economy\" title=\"Planned economy\">economic planner</a>, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet <a href=\"https://wikipedia.org/wiki/Planned_economy\" title=\"Planned economy\">economic planner</a>, member of the <a href=\"https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union\" class=\"mw-redirect\" title=\"Politburo of the Central Committee of the Communist Party of the Soviet Union\">Politburo of the Central Committee of the Communist Party of the Soviet Union</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Planned economy", "link": "https://wikipedia.org/wiki/Planned_economy"}, {"title": "Politburo of the Central Committee of the Communist Party of the Soviet Union", "link": "https://wikipedia.org/wiki/Politburo_of_the_Central_Committee_of_the_Communist_Party_of_the_Soviet_Union"}]}, {"year": "1905", "text": "<PERSON>, Canadian sprinter and coach (d. 1994)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_sprinter)\" title=\"<PERSON> (Canadian sprinter)\"><PERSON></a>, Canadian sprinter and coach (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_sprinter)\" title=\"<PERSON> (Canadian sprinter)\"><PERSON></a>, Canadian sprinter and coach (d. 1994)", "links": [{"title": "<PERSON> (Canadian sprinter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Canadian_sprinter)"}]}, {"year": "1910", "text": "<PERSON>, English ballerina and choreographer (d. 2004)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and choreographer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English ballerina and choreographer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American baseball player and manager (d. 1984)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Canadian-American businessman (d. 1999)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American businessman (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American baseball player (d. 2010)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American architect, designed the World Trade Center (d. 1986)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)\" title=\"World Trade Center (1973-2001)\">World Trade Center</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "World Trade Center (1973-2001)", "link": "https://wikipedia.org/wiki/World_Trade_Center_(1973%E2%80%932001)"}]}, {"year": "1913", "text": "<PERSON>, American actress and singer (d. 1990)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Chinese educator and politician, 4th Vice Premier of the People's Republic of China (d. 2015)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese educator and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of vice premiers of the People's Republic of China", "link": "https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China"}]}, {"year": "1917", "text": "<PERSON>, American tenor and actor (d. 1995)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 1995)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1917", "text": "<PERSON>, American baseball player and manager (d. 2011)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Japanese priest, 1st Bishop of Naha (d. 2014)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Peter <PERSON>\">Peter <PERSON></a>, Japanese priest, 1st <a href=\"https://wikipedia.org/wiki/Bishop_of_Naha\" class=\"mw-redirect\" title=\"Bishop of Naha\">Bishop of Naha</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Peter <PERSON>\">Peter <PERSON></a>, Japanese priest, 1st <a href=\"https://wikipedia.org/wiki/Bishop_of_Naha\" class=\"mw-redirect\" title=\"Bishop of Naha\">Bishop of Naha</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bishop of Naha", "link": "https://wikipedia.org/wiki/<PERSON>_of_Naha"}]}, {"year": "1921", "text": "<PERSON>, American sergeant, Medal of Honor recipient (d. 2013)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian ice hockey player, footballer, and manager (d. 1979)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>se<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player, footballer, and manager (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Russian ice hockey player, footballer, and manager (d. 1979)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vsevolod_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 1987)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, American admiral and academic, 12th Director of Central Intelligence (d. 2018)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Turner\"><PERSON><PERSON></a>, American admiral and academic, 12th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Turner\"><PERSON><PERSON></a>, American admiral and academic, 12th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (d. 2018)", "links": [{"title": "Stan<PERSON> Turner", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Turner"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Japanese businessman, founded <PERSON><PERSON><PERSON> (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Horiba", "link": "https://wikipedia.org/wiki/Ho<PERSON>ba"}]}, {"year": "1925", "text": "<PERSON>, American biochemist and endocrinologist, Nobel Prize laureate (d. 1998)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and endocrinologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1926", "text": "<PERSON>, American-Mexican nun and activist (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Mexican nun and activist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American-Mexican nun and activist (d. 2013)", "links": [{"title": "<PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mother_Antonia"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Canadian-American actress, singer, and dancer (d. 2018)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress, singer, and dancer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American actress, singer, and dancer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian actor (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, 3rd Baron <PERSON>, Scottish businessman (d. 2010)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Scottish businessman (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, Scottish businessman (d. 2010)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON>, French dancer and model", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French dancer and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French dancer and model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actress (d. 1991)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American actor (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Malachi_Throne\" title=\"Malachi Throne\"><PERSON><PERSON></a>, American actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malachi_Throne\" title=\"Malachi Throne\"><PERSON><PERSON></a>, American actor (d. 2013)", "links": [{"title": "Malachi Throne", "link": "https://wikipedia.org/wiki/Malachi_Throne"}]}, {"year": "1929", "text": "<PERSON>, American actor (d. 1997)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 1997)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1930", "text": "<PERSON>, Australian psychiatrist, academic, and politician, 37th Governor of New South Wales", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychiatrist, academic, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian psychiatrist, academic, and politician, 37th <a href=\"https://wikipedia.org/wiki/Governor_of_New_South_Wales\" title=\"Governor of New South Wales\">Governor of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New South Wales", "link": "https://wikipedia.org/wiki/Governor_of_New_South_Wales"}]}, {"year": "1930", "text": "<PERSON>, German historian and author (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German historian and author (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American saxophonist (d. 1986)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Trinidadian politician, 4th President of Trinidad and Tobago (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"List of Presidents of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_Trinidad_and_Tobago\" class=\"mw-redirect\" title=\"List of Presidents of Trinidad and Tobago\">President of Trinidad and Tobago</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Presidents of Trinidad and Tobago", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_Trinidad_and_Tobago"}]}, {"year": "1933", "text": "<PERSON>, American singer-songwriter, producer, and actor (d. 2006)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, French ballerina (d. 2016)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Verd<PERSON>\"><PERSON><PERSON></a>, French ballerina (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Verdy\"><PERSON><PERSON></a>, French ballerina (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Violette_Verdy"}]}, {"year": "1934", "text": "<PERSON>, American soul singer (d. 2016)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Chilean human rights activist (d. 1999)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Sola_Sierra\" title=\"Sola Sierra\"><PERSON><PERSON> Sierra</a>, Chilean human rights activist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sola_Sierra\" title=\"Sola Sierra\"><PERSON><PERSON> Sierra</a>, Chilean human rights activist (d. 1999)", "links": [{"title": "Sola Sierra", "link": "https://wikipedia.org/wiki/Sola_Sierra"}]}, {"year": "1936", "text": "<PERSON>, Russian general and politician, 3rd Russian Minister of Defence (d. 2014)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician, 3rd <a href=\"https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)\" title=\"Ministry of Defence (Russia)\">Russian Minister of Defence</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Defence (Russia)", "link": "https://wikipedia.org/wiki/Ministry_of_Defence_(Russia)"}]}, {"year": "1937", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, American soprano and actress (d. 2005)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soprano and actress (d. 2005)", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>pon"}]}, {"year": "1937", "text": "<PERSON>, English composer and academic (d. 2021)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>, Latvian psychologist and politician, President of Latvia", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Vaira_V%C4%AB%C4%B7e-<PERSON><PERSON><PERSON><PERSON>\" title=\"Vaira Vīķe-Freiberga\"><PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON><PERSON><PERSON></a>, Latvian psychologist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vaira_V%C4%AB%C4%B7e-<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>air<PERSON> Vīķe-Freiberga\"><PERSON><PERSON><PERSON> Vīķe-<PERSON><PERSON><PERSON><PERSON></a>, Latvian psychologist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Latvia\" title=\"President of Latvia\">President of Latvia</a>", "links": [{"title": "Vaira Vīķe-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vaira_V%C4%AB%C4%B7e-Freiberga"}, {"title": "President of Latvia", "link": "https://wikipedia.org/wiki/President_of_Latvia"}]}, {"year": "1938", "text": "<PERSON>, American rock and roll drummer (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock and roll drummer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rock and roll drummer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American golfer and sportscaster", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Scottish cricketer and referee (d. 2013)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cricketer and referee (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish cricketer and referee (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American electronic engineer and inventor (d. 2011)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, American electronic engineer and inventor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)\" title=\"<PERSON> (engineer)\"><PERSON></a>, American electronic engineer and inventor (d. 2011)", "links": [{"title": "<PERSON> (engineer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(engineer)"}]}, {"year": "1940", "text": "<PERSON>, American comedian, actor, producer, and screenwriter (d. 2005)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, producer, and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, German footballer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wild\" title=\"Tasso Wild\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wild\" title=\"Tasso Wild\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "Tasso Wild", "link": "https://wikipedia.org/wiki/Tasso_Wild"}]}, {"year": "1942", "text": "<PERSON>, Egyptian politician, Egyptian Minister of Foreign Affairs", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Egypt)\" title=\"Minister of Foreign Affairs (Egypt)\">Egyptian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Egypt)\" title=\"Minister of Foreign Affairs (Egypt)\">Egyptian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Egypt)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Egypt)"}]}, {"year": "1942", "text": "<PERSON>, American author and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1942", "text": "<PERSON>, Australian cricketer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1943", "text": "<PERSON>, American runner and journalist (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner and journalist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)\" title=\"<PERSON> (runner)\"><PERSON></a>, American runner and journalist (d. 2022)", "links": [{"title": "<PERSON> (runner)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(runner)"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American drummer and songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American general", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Moroccan author and poet", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Moroccan author and poet", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON> <PERSON>, Icelandic politician", "html": "1945 - <a href=\"https://wikipedia.org/wiki/%C3%81sta_B._%C3%9Eorsteinsd%C3%B3ttir\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Icelandic politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81sta_B._%C3%9Eorsteinsd%C3%B3ttir\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, Icelandic politician", "links": [{"title": "Ásta B<PERSON>", "link": "https://wikipedia.org/wiki/%C3%81sta_B._%C3%9Eorsteinsd%C3%B3ttir"}]}, {"year": "1945", "text": "<PERSON>, American vice admiral in the United States Navy", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vice admiral in the United States Navy", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American vice admiral in the United States Navy", "links": [{"title": "Lyle <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American singer-songwriter, actress and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, actress and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American comedian and actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Bosnian journalist and author (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian journalist and author (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian journalist and author (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ku<PERSON>pahi%C4%87"}]}, {"year": "1946", "text": "<PERSON>, Irish singer-songwriter and pianist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_O%27Sullivan"}]}, {"year": "1947", "text": "<PERSON>, French singer-songwriter and actor (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French singer-songwriter and actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English-Australian rugby league player, coach, and sportscaster (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and sportscaster (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian rugby league player, coach, and sportscaster (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and radio host", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and radio host", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer and politician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Sarfraz_Nawaz\" title=\"Sarfraz Nawaz\">Sarfraz Nawaz</a>, Pakistani cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sarfraz_Nawaz\" title=\"Sarfraz Nawaz\">Sarfraz <PERSON></a>, Pakistani cricketer and politician", "links": [{"title": "Sarfraz Nawaz", "link": "https://wikipedia.org/wiki/Sarfraz_Nawaz"}]}, {"year": "1948", "text": "<PERSON>, American mountaineer and author", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mountaineer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English footballer and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON> <PERSON><PERSON>, English bishop and scholar", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English bishop and scholar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English bishop and scholar", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Nigerian civil servant and politician, Governor of Kaduna State (d. 2012)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Nigerian civil servant and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Kaduna_State\" title=\"Governor of Kaduna State\">Governor of Kaduna State</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Nigerian civil servant and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Kaduna_State\" title=\"Governor of Kaduna State\">Governor of Kaduna State</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Kaduna State", "link": "https://wikipedia.org/wiki/Governor_of_Kaduna_State"}]}, {"year": "1949", "text": "<PERSON>, American author and illustrator", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jan_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Colombian drug lord and narcoterrorist (d. 1993)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian drug lord and narcoterrorist (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian drug lord and narcoterrorist (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pablo_Escobar"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean businessman and politician, 35th President of Chile (d. 2024)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Pi%C3%B1era\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Pi%C3%B1era\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean businessman and politician, 35th <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Pi%C3%B1era"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Indian biologist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Indian biologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Man<PERSON>\"><PERSON><PERSON></a>, Indian biologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manju_Bansal"}]}, {"year": "1950", "text": "<PERSON>, Australian singer-songwriter and guitarist (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor and drummer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and drummer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "1950", "text": "<PERSON>, American illustrator and painter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek lawyer and politician, Greek Minister of Justice (d. 2020)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights\">Greek Minister of Justice</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights\" class=\"mw-redirect\" title=\"Ministry of Justice, Transparency and Human Rights\">Greek Minister of Justice</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Justice, Transparency and Human Rights", "link": "https://wikipedia.org/wiki/Ministry_of_Justice,_Transparency_and_Human_Rights"}]}, {"year": "1951", "text": "<PERSON>, Bulgarian cosmonaut", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian cosmonaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bulgarian cosmonaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American actor, director, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/O<PERSON>_<PERSON>tund%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/O<PERSON>_<PERSON>tund%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Obba_Babatund%C3%A9"}]}, {"year": "1951", "text": "<PERSON>, Australian radio and television host (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio and television host (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American bass player, songwriter, and producer (d. 1987)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, South African lawn bowler", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Nozi<PERSON><PERSON>_<PERSON>der\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African lawn bowler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nozi<PERSON><PERSON>_<PERSON>der\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African lawn bowler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ziph<PERSON>_<PERSON>hroeder"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American actor (d. 2023)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>rea<PERSON>_<PERSON>\" title=\"Trea<PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rea<PERSON>_<PERSON>\" title=\"Treat <PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treat_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English director, producer, and playwright", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English journalist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alan_De<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English chemist and engineer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Belgian footballer (d. 2017)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish actor, director, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>k<PERSON>", "link": "https://wikipedia.org/wiki/Veik<PERSON>_<PERSON>n"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish actress (d. 2021)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Ver%C3%B3nica_Forqu%C3%A9\" title=\"<PERSON>er<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish actress (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ver%C3%B3nica_Forqu%C3%A9\" title=\"<PERSON>er<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish actress (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ver%C3%B3nica_Forqu%C3%A9"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indian playback singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian playback singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/U<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Irish footballer and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American journalist", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, American singer-songwriter, musician, and actress (d. 2022)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Julee_<PERSON>\" title=\"<PERSON>ee <PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, musician, and actress (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julee_<PERSON>\" title=\"<PERSON>ee <PERSON>\"><PERSON><PERSON> <PERSON></a>, American singer-songwriter, musician, and actress (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ee_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American guitarist and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/Chris_<PERSON>\" title=\"Chris <PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chris_<PERSON>\" title=\"Chris <PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Chris_Poland"}]}, {"year": "1957", "text": "<PERSON>, Kenyan-British actor", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Roy\" title=\"<PERSON> Roy\"><PERSON></a>, Kenyan-British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Roy\" title=\"<PERSON> Roy\"><PERSON></a>, Kenyan-British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Roy"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American singer-songwriter and actress (d. 2011)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>esta_Williams\" title=\"Vesta Williams\"><PERSON><PERSON></a>, American singer-songwriter and actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>esta_Williams\" title=\"Vesta Williams\"><PERSON><PERSON></a>, American singer-songwriter and actress (d. 2011)", "links": [{"title": "Vesta Williams", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Williams"}]}, {"year": "1958", "text": "<PERSON>, Mexican footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American journalist and author", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Italian runner", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" class=\"mw-redirect\" title=\"<PERSON> (politician)\"><PERSON></a>, American politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American actress and singer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter, guitarist, and painter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and painter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ish"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American model and actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Alt\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Indian-English political scientist and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian-English political scientist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian actress and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Israeli-American businesswoman and boss of Oracle", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American businesswoman and boss of Oracle", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-American businesswoman and boss of Oracle", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}]}, {"year": "1961", "text": "<PERSON>, American biophysicist and academic", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biophysicist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biophysicist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American basketball player and coach", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Brazilian race car driver", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Canadian speed skater", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian speed skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Sri Lankan cricketer and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Italian footballer (d. 2024)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Welsh-Canadian author and poet", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Canadian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-Canadian author and poet", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, South African rugby player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Slovenian singer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON></a>, Slovenian singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/Magnific<PERSON>_(musician)"}]}, {"year": "1966", "text": "<PERSON>, New Zealand director, producer, and screenwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American actress, ballet dancer, and choreographer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, ballet dancer, and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, ballet dancer, and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian baseball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Nestor_Carbonell\" class=\"mw-redirect\" title=\"Nestor <PERSON>ell\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nestor_Carbonell\" class=\"mw-redirect\" title=\"Nestor Carbonell\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_Carbonell"}]}, {"year": "1967", "text": "<PERSON>, American baseball player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, English actor and director", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Australian squash player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian squash player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian squash player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Swedish swimmer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American author and blogger", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and blogger", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and blogger", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actress", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brooks\" title=\"<PERSON> Brooks\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Brooks\" title=\"<PERSON> Brooks\"><PERSON></a>, American actress", "links": [{"title": "<PERSON> Brooks", "link": "https://wikipedia.org/wiki/<PERSON>_Brooks"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American baseball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American comedian, actress, and singer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actress, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, American high jumper and educator", "html": "1970 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\">T<PERSON><PERSON></a>, American high jumper and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"Tish<PERSON>\">T<PERSON><PERSON></a>, American high jumper and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tish<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian race car driver", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_P<PERSON>ori"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Finnish-American pianist and composer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish-American pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American author and educator", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American race car driver and stuntman", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and stuntman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and stuntman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Portuguese footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Costin<PERSON>\" title=\"Cost<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>st<PERSON><PERSON>\" title=\"Cost<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Costinha"}]}, {"year": "1975", "text": "<PERSON>, American author", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>action"}]}, {"year": "1975", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American keyboard player and producer (d. 2014)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Ikey%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American keyboard player and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Ikey%22_<PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON>'><PERSON> \"<PERSON><PERSON>\" <PERSON></a>, American keyboard player and producer (d. 2014)", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Ikey%22_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Norwegian racing driver and sportscaster", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian racing driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian racing driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Pakistani actress and host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani actress and host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani actress and host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Danish swimmer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sophia_S<PERSON>u"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Polish boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish boxer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American journalist and author", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, New Zealand actor, artist, and photographer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Gorman\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor, artist, and photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Gorman\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor, artist, and photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Gorman"}]}, {"year": "1976", "text": "<PERSON>, American hate crime victim (d. 1998)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Hate_crime\" title=\"Hate crime\">hate crime</a> victim (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Hate_crime\" title=\"Hate crime\">hate crime</a> victim (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Hate crime", "link": "https://wikipedia.org/wiki/Hate_crime"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Evangelos_Sklavos\" class=\"mw-redirect\" title=\"Evangelos Sklavos\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Sklavos\" class=\"mw-redirect\" title=\"Evangelos Sklavos\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Evangel<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American guitarist and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, French actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Scottish journalist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor and comedian", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American discus thrower", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Trafton\"><PERSON></a>, American discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Trafton\"><PERSON></a>, American discus thrower", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American ice hockey player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Jamaican sprinter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter,_born_1979)\" title=\"<PERSON> (sprinter, born 1979)\"><PERSON></a>, Jamaican sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sprinter,_born_1979)\" title=\"<PERSON> (sprinter, born 1979)\"><PERSON></a>, Jamaican sprinter", "links": [{"title": "<PERSON> (sprinter, born 1979)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sprinter,_born_1979)"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Indian cricketer and politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian cricketer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Kenyan-Qatari runner", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-Qatari runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan-Qatari runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Greek singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1981", "text": "<PERSON>hin, South Korean singer-songwriter and actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-shin\" title=\"<PERSON> Hyo-shin\"><PERSON>hin</a>, South Korean singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-shin\" title=\"<PERSON> Hyo-shin\"><PERSON>hin</a>, South Korean singer-songwriter and actor", "links": [{"title": "<PERSON>hin", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-shin"}]}, {"year": "1981", "text": "<PERSON>, Australian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON> <PERSON>, Indonesian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"I Made W<PERSON>wan\">I <PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"I Made <PERSON>wan\">I <PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, English actor and rapper", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actor and rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Greek footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Greek footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, South African rapper and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Yolandi_Visser\" title=\"Yolandi Visser\"><PERSON><PERSON><PERSON></a>, South African rapper and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yolandi_Visser\" title=\"Yolandi Visser\"><PERSON><PERSON><PERSON></a>, South African rapper and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Visser"}]}, {"year": "1985", "text": "<PERSON>, American media host and YouTube personality", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media host and YouTube personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American media host and YouTube personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1e\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A1e"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiliano_<PERSON>o"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Virgin Islander sprinter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Henry\"><PERSON><PERSON><PERSON></a>, Virgin Islander sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Virgin Islander sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Australian singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Joy\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)\" title=\"<PERSON> (footballer, born 1987)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1987)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1987)"}]}, {"year": "1988", "text": "<PERSON>, American musician and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American actress, singer, and model", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Kravitz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, singer, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zo%C3%AB_Kra<PERSON>tz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress, singer, and model", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zo%C3%AB_Kravitz"}]}, {"year": "1988", "text": "<PERSON>, Greek-American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Mavrai<PERSON>\"><PERSON></a>, Greek-American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Austrian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican trumpet player, composer, and producer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Sotel%C3%BAm\" title=\"Sotelúm\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican trumpet player, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sotel%C3%BAm\" title=\"Sotelúm\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican trumpet player, composer, and producer", "links": [{"title": "Sotelúm", "link": "https://wikipedia.org/wiki/Sotel%C3%BAm"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Slovak ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Tatar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Tatar\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1%C5%A1_Tatar"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON>_Christmas\" title=\"Rakeem Christmas\"><PERSON><PERSON><PERSON> Christmas</a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON>_Christmas\" title=\"Rakeem Christmas\">Ra<PERSON><PERSON> Christmas</a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra<PERSON><PERSON>_Christmas"}]}, {"year": "1991", "text": "<PERSON>, Swedish tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Chinese swimmer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Yang\"><PERSON></a>, Chinese swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Yang"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, Ghanaian footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Puerto Rican baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Javier_B%C3%A1ez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Javier_B%C3%A1ez"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Dutch footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Estonian archer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C3%A4rnat\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C3%A4rnat\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian archer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reena_P%C3%A4rnat"}]}, {"year": "1993", "text": "<PERSON>, Australian cricketer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/See<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/See<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/See<PERSON>_<PERSON>e"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, Lithuanian tennis player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Agn%C4%97_%C4%8Cepelyt%C4%97\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agn%C4%97_%C4%8Cepelyt%C4%97\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agn%C4%97_%C4%8Cepelyt%C4%97"}]}, {"year": "1995", "text": "<PERSON>, Scottish footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)\" title=\"<PERSON> (footballer, born 1995)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, born 1995)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1995)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Barbadian sprinter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Barbadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Barbadian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, German footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, French tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "217", "text": "<PERSON><PERSON><PERSON>, '<PERSON><PERSON>', Rabbi and editor of the Mishnah (b. 135)", "html": "217 - <a href=\"https://wikipedia.org/wiki/<PERSON>_ha-Nasi\" title=\"<PERSON> ha-Nasi\"><PERSON><PERSON><PERSON></a>, '<a href=\"https://wikipedia.org/wiki/Nasi_(Hebrew_title)\" title=\"<PERSON><PERSON> (Hebrew title)\">Nasi</a>', <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">Rabbi</a> and editor of the <a href=\"https://wikipedia.org/wiki/Mishnah\" title=\"Mishnah\">Mishnah</a> (b. 135)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_ha-Nasi\" title=\"<PERSON> ha-Nasi\"><PERSON><PERSON><PERSON></a>, '<a href=\"https://wikipedia.org/wiki/Nasi_(Hebrew_title)\" title=\"<PERSON><PERSON> (Hebrew title)\">Na<PERSON></a>', <a href=\"https://wikipedia.org/wiki/Rabbi\" title=\"Rabbi\">Rabbi</a> and editor of the <a href=\"https://wikipedia.org/wiki/Mishnah\" title=\"Mishnah\">Mishnah</a> (b. 135)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON><PERSON>"}, {"title": "<PERSON><PERSON> (Hebrew title)", "link": "https://wikipedia.org/wiki/Nasi_(Hebrew_title)"}, {"title": "Rabbi", "link": "https://wikipedia.org/wiki/Rabbi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>shnah"}]}, {"year": "660", "text": "<PERSON><PERSON><PERSON>, Frankish bishop and saint (b. 588)", "html": "660 - <a href=\"https://wikipedia.org/wiki/Saint_Eligius\" title=\"Saint <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish bishop and saint (b. 588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Eli<PERSON>us\" title=\"Saint <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Frankish bishop and saint (b. 588)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saint_Eligius"}]}, {"year": "948", "text": "<PERSON>, Chinese governor and prince (b. 891)", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese governor and prince (b. 891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese governor and prince (b. 891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "969", "text": "<PERSON><PERSON>, Japanese statesman (b. 920)", "html": "969 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Morotada\" title=\"Fujiwara no Morotada\"><PERSON><PERSON> no Morotada</a>, Japanese statesman (b. 920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Morotada\" title=\"Fujiwara no Morotada\"><PERSON><PERSON> no Mo<PERSON>ada</a>, Japanese statesman (b. 920)", "links": [{"title": "<PERSON><PERSON> no Morotada", "link": "https://wikipedia.org/wiki/Fujiwara_no_<PERSON>ada"}]}, {"year": "1018", "text": "<PERSON><PERSON><PERSON><PERSON> of Merseburg, German bishop (b. 975)", "html": "1018 - <a href=\"https://wikipedia.org/wiki/Thiet<PERSON>_of_Merseburg\" title=\"Thiet<PERSON> of Merseburg\"><PERSON><PERSON><PERSON><PERSON> of Merseburg</a>, German bishop (b. 975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thiet<PERSON>_of_Merseburg\" title=\"Thiet<PERSON> of Merseburg\"><PERSON><PERSON><PERSON><PERSON> of Merseburg</a>, German bishop (b. 975)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> of Merseburg", "link": "https://wikipedia.org/wiki/Thietmar_of_Merseburg"}]}, {"year": "1135", "text": "<PERSON>, king of England (b. 1068)", "html": "1135 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON></a>, king of England (b. 1068)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON></a>, king of England (b. 1068)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1241", "text": "<PERSON> of England, Holy Roman Empress (b. 1214)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/Isabella_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>, Holy Roman Empress (b. 1214)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isabella_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>, Holy Roman Empress (b. 1214)", "links": [{"title": "Isabella of England", "link": "https://wikipedia.org/wiki/Isabella_of_England"}]}, {"year": "1255", "text": "<PERSON> of Alamut, <PERSON><PERSON><PERSON>", "html": "1255 - <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Alamut\" title=\"<PERSON> III of Alamut\"><PERSON> of Alamut</a>, <PERSON><PERSON><PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_Alamut\" title=\"<PERSON> III of Alamut\"><PERSON> of Alamut</a>, <PERSON><PERSON><PERSON>", "links": [{"title": "<PERSON> of Alamut", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Alamut"}]}, {"year": "1335", "text": "<PERSON>, Mongol ruler of the Ilkhanate (b. 1305)", "html": "1335 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sa%27id_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mongol ruler of the Ilkhanate (b. 1305)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sa%27id_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mongol ruler of the Ilkhanate (b. 1305)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Sa%27id_<PERSON>_<PERSON>"}]}, {"year": "1374", "text": "<PERSON>, king of Sweden (b. 1316)", "html": "1374 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of Sweden (b. 1316)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, king of Sweden (b. 1316)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1433", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, emperor of Japan (b. 1377)", "html": "1433 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ko<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1377)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON><PERSON>\" title=\"Emperor <PERSON><PERSON>Ko<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 1377)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1455", "text": "<PERSON>, Italian goldsmith and sculptor (b. 1378)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian goldsmith and sculptor (b. 1378)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian goldsmith and sculptor (b. 1378)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1521", "text": "<PERSON>, pope of the Catholic Church (b. 1475)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON></a>, pope of the Catholic Church (b. 1475)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1530", "text": "<PERSON> of Austria, duchess of Savoy (b. 1480)", "html": "1530 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Duchess_of_Savoy\" title=\"<PERSON> of Austria, Duchess of Savoy\"><PERSON> of Austria</a>, duchess of Savoy (b. 1480)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Duchess_of_Savoy\" title=\"<PERSON> of Austria, Duchess of Savoy\"><PERSON> of Austria</a>, duchess of Savoy (b. 1480)", "links": [{"title": "<PERSON> of Austria, Duchess of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Duchess_of_Savoy"}]}, {"year": "1580", "text": "<PERSON>, Italian cardinal (b. 1509)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1509)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, English Roman Catholic priest, martyr and saint (b. 1556)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr and saint (b. 1556)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr and saint (b. 1556)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, English Roman Catholic priest, martyr, and saint (b. 1540)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr, and saint (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr, and saint (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1581", "text": "<PERSON>, English Roman Catholic priest, martyr, and saint (b. 1550)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr, and saint (b. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English Roman Catholic priest, martyr, and saint (b. 1550)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1633", "text": "<PERSON>, infanta of Spain (b. 1566)", "html": "1633 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, infanta of Spain (b. 1566)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, infanta of Spain (b. 1566)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1640", "text": "<PERSON>, Portuguese politician, Prime Minister of Portugal (b. 1590)", "html": "1640 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Portugal\">Prime Minister of Portugal</a> (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese politician, <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal\" class=\"mw-redirect\" title=\"List of Prime Ministers of Portugal\">Prime Minister of Portugal</a> (b. 1590)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Portugal", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Portugal"}]}, {"year": "1660", "text": "<PERSON>, French genealogist and historian (b. 1592)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier\" title=\"<PERSON>\"><PERSON></a>, French genealogist and historian (b. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier\" title=\"<PERSON>\"><PERSON></a>, French genealogist and historian (b. 1592)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hozier"}]}, {"year": "1729", "text": "<PERSON>, French-Italian astronomer and mathematician (b. 1665)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian astronomer and mathematician (b. 1665)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Italian astronomer and mathematician (b. 1665)", "links": [{"title": "Giacomo <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1750", "text": "<PERSON>, German mathematician, astronomer, and cartographer (b. 1671)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and cartographer (b. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, astronomer, and cartographer (b. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, English organist and composer (b. 1696)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (b. 1696)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_(composer)"}]}, {"year": "1767", "text": "<PERSON>, 10th Earl of Buchan, Scottish politician (b. 1710)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Buchan\" title=\"<PERSON>, 10th Earl of Buchan\"><PERSON>, 10th Earl of Buchan</a>, Scottish politician (b. 1710)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Buchan\" title=\"<PERSON>, 10th Earl of Buchan\"><PERSON>, 10th Earl of Buchan</a>, Scottish politician (b. 1710)", "links": [{"title": "<PERSON>, 10th Earl of Buchan", "link": "https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Buch<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, emperor and autocrat of Russia (b. 1777)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, emperor and autocrat of Russia (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> of Russia\"><PERSON></a>, emperor and autocrat of Russia (b. 1777)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1865", "text": "<PERSON>, Swiss pastor, poet, and educator (b. 1796)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor, poet, and educator (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor, poet, and educator (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6hlich"}]}, {"year": "1866", "text": "<PERSON>, Welsh geographer and surveyor (b. 1790)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George Everest\"><PERSON></a>, Welsh geographer and surveyor (b. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Everest\" title=\"George Everest\"><PERSON></a>, Welsh geographer and surveyor (b. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, English lawyer and politician (b. 1797)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (b. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English-New Zealand lawyer and politician, Attorney-General of the Crown Colony of New Zealand (b. 1809)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English-New Zealand lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney-General_(New_Zealand)\" title=\"Attorney-General (New Zealand)\">Attorney-General of the Crown Colony of New Zealand</a> (b. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, English-New Zealand lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney-General_(New_Zealand)\" title=\"Attorney-General (New Zealand)\">Attorney-General of the Crown Colony of New Zealand</a> (b. 1809)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}, {"title": "Attorney-General (New Zealand)", "link": "https://wikipedia.org/wiki/Attorney-General_(New_Zealand)"}]}, {"year": "1913", "text": "<PERSON><PERSON>, Estonian poet and author (b. 1864)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian poet and author (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>iv"}]}, {"year": "1914", "text": "<PERSON>, American captain and historian (b. 1840)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and historian (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and historian (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, French priest and martyr (b. 1858)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and martyr (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French priest and martyr (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON>, Belgian author and poet (b. 1836)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian author and poet (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian author and poet (b. 1836)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Colombian-American lawyer and poet (b. 1888)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian-American lawyer and poet (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian-American lawyer and poet (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Finnish painter (b. 1865)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish painter (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish painter (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ek<PERSON>_<PERSON>n"}]}, {"year": "1934", "text": "<PERSON>, Russian engineer and politician (b. 1886)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Estonian-German optician, invented the <PERSON> camera (b. 1879)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German optician, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_camera\" title=\"<PERSON> camera\"><PERSON> camera</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-German optician, invented the <a href=\"https://wikipedia.org/wiki/<PERSON>_camera\" title=\"<PERSON> camera\"><PERSON> camera</a> (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Schmidt camera", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Polish scientist and medical examiner (b. 1867)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish scientist and medical examiner (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish scientist and medical examiner (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Thai historian and educator (b. 1862)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Damrong_Rajanubhab\" title=\"Damrong Rajanubhab\"><PERSON><PERSON></a>, Thai historian and educator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Damrong_Rajanubhab\" title=\"Damrong Rajanubhab\"><PERSON><PERSON></a>, Thai historian and educator (b. 1862)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Damrong_<PERSON>b"}]}, {"year": "1944", "text": "<PERSON>, Irish Republican executed by hanging (b. 1918)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> executed by hanging (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> executed by hanging (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, English magician, poet, and mountaineer (b. 1875)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English magician, poet, and mountaineer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English magician, poet, and mountaineer (b. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON> <PERSON><PERSON>, English mathematician and theorist (b. 1877)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English mathematician and theorist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English mathematician and theorist (b. 1877)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English pianist and composer (b. 1894)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and composer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American pianist, composer, and publisher (b. 1898)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist, composer, and publisher (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American pianist, composer, and publisher (b. 1898)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1958", "text": "<PERSON>, American civil rights activist (b. 1911)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, English-Indian geneticist and biologist (b. 1892)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/J._B._<PERSON><PERSON>_Haldane\" title=\"J. B. S. Haldane\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Indian geneticist and biologist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J._B._<PERSON><PERSON>_<PERSON>dane\" title=\"J. B. S. Haldane\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, English-Indian geneticist and biologist (b. 1892)", "links": [{"title": "J. B. S<PERSON>", "link": "https://wikipedia.org/wiki/J._<PERSON><PERSON>_<PERSON><PERSON>_Hal<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek runner (b. 1877)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek runner (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek runner (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Romanian opera singer, composer, and conductor (b. 1887)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Nicola<PERSON>_<PERSON>\" title=\"Nicolae B<PERSON>n\"><PERSON><PERSON></a>, Romanian opera singer, composer, and conductor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nicolae_<PERSON>\" title=\"<PERSON>e B<PERSON>\"><PERSON><PERSON></a>, Romanian opera singer, composer, and conductor (b. 1887)", "links": [{"title": "Nicolae <PERSON>", "link": "https://wikipedia.org/wiki/Nicolae_Bretan"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish singer-songwriter, guitarist, and actor (b. 1921)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Dar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, guitarist, and actor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer-songwriter, guitarist, and actor (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dar%C3%<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Israeli politician, 1st Prime Minister of Israel (b. 1886)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a> (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American baseball player and coach (b. 1927)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Italian race car driver and engineer (b. 1898)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver and engineer (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian race car driver and engineer (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American journalist (b. 1906)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American author and illustrator (b. 1929)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Dutch painter and photographer (b. 1911)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and photographer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch painter and photographer (b. 1911)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American general and film producer (b. 1912)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American general and film producer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(producer)\" title=\"<PERSON> (producer)\"><PERSON></a>, American general and film producer (b. 1912)", "links": [{"title": "<PERSON> (producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(producer)"}]}, {"year": "1987", "text": "<PERSON>, American novelist, poet, and critic (b. 1924)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and critic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and critic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (b. 1918)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ch"}]}, {"year": "1988", "text": "<PERSON><PERSON> <PERSON>, American pastor and theologian (b. 1904)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pastor and theologian (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pastor and theologian (b. 1904)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American dancer and choreographer (b. 1931)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian-English actress (b. 1917)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-English actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Irish athlete (b. 1906)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Pat_<PERSON>%27C<PERSON>agh<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish athlete (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pat_<PERSON>%27<PERSON><PERSON>agh<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish athlete (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pat_O%27<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American economist and academic, Nobel Prize laureate (b. 1911)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1993", "text": "<PERSON>, American singer-songwriter (b. 1959)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English cricketer (b. 1908)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, New Zealand-English actor (b. 1907)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English actor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-English actor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American general (b. 1931)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Canadian businessman (b. 1928)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Canadian banker and businessman (b. 1929)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian banker and businessman (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9langer\" title=\"<PERSON>\"><PERSON></a>, Canadian banker and businessman (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Michel_B%C3%A9langer"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, French violinist (b. 1908)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>li\" title=\"<PERSON><PERSON><PERSON><PERSON>pelli\"><PERSON><PERSON><PERSON><PERSON></a>, French violinist (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>li\" title=\"<PERSON><PERSON><PERSON><PERSON> Grappelli\"><PERSON><PERSON><PERSON><PERSON></a>, French violinist (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/St%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American lieutenant, lawyer, and politician, 62nd Governor of Massachusetts (b. 1920)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Peabody\" title=\"Endicott Peabody\"><PERSON><PERSON><PERSON></a>, American lieutenant, lawyer, and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Endicott_Peabody\" title=\"Endico<PERSON> Peabody\"><PERSON><PERSON><PERSON></a>, American lieutenant, lawyer, and politician, 62nd <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (b. 1920)", "links": [{"title": "Endicott Peabody", "link": "https://wikipedia.org/wiki/Endicott_Peabody"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1998", "text": "<PERSON>, American poet and novelist (b. 1899)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and novelist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American director and producer (b. 1909)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American captain and author (b. 1918)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American captain and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American captain and author (b. 1918)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "2002", "text": "<PERSON>, American baseball player (b. 1942)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American economist and academic (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and academic (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON>, Italian bobsledder (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bobsledder (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian bobsledder (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON> of Lippe-Biesterfeld (b. 1911)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Lippe-Biesterfeld\" title=\"Prince <PERSON> of Lippe-Biesterfeld\">Prince <PERSON> of Lippe-Biesterfeld</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Lippe-Biesterfeld\" title=\"Prince <PERSON> of Lippe-Biesterfeld\">Prince <PERSON> of Lippe-Biesterfeld</a> (b. 1911)", "links": [{"title": "<PERSON> of Lippe-Biesterfeld", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Lippe-Biesterfeld"}]}, {"year": "2004", "text": "<PERSON>, Scottish footballer (b. 1931)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper)\" class=\"mw-redirect\" title=\"<PERSON> (goalkeeper)\"><PERSON></a>, Scottish footballer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper)\" class=\"mw-redirect\" title=\"<PERSON> (goalkeeper)\"><PERSON></a>, Scottish footballer (b. 1931)", "links": [{"title": "<PERSON> (goalkeeper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(goalkeeper)"}]}, {"year": "2005", "text": "<PERSON><PERSON>, American CIA officer  (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Gust_Avrakotos\" title=\"Gust Avrakotos\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gust_Avrakotos\" title=\"Gust Avrakotos\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gust_Avrak<PERSON>s"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "2005", "text": "<PERSON>, English actress and playwright (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and playwright (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and playwright (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American soldier, Medal of Honor recipient (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2006", "text": "<PERSON>, French actress (b. 1948)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Canadian archaeologist, anthropologist, and historian (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archaeologist, anthropologist, and historian (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archaeologist, anthropologist, and historian (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Australian tennis player and footballer (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player and footballer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, British actor (b. 1933)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Croatian-Argentine war crimes suspect, businessman, diplomat, and intelligence agent (b. 1915)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Ivo_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian-Argentine war crimes suspect, businessman, diplomat, and intelligence agent (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian-Argentine war crimes suspect, businessman, diplomat, and intelligence agent (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American businessman and religious leader (b. 1917)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and religious leader (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and religious leader (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Dutch astronomer and academic (b. 1914)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch astronomer and academic (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, American actor and producer (b. 1929)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and producer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, German author and critic (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and critic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author and critic (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American football player (b. 1987)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (b. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, South African lawyer and judge, 18th Chief Justice of South Africa (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and judge, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African lawyer and judge, 18th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_South_Africa\" title=\"Chief Justice of South Africa\">Chief Justice of South Africa</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of South Africa", "link": "https://wikipedia.org/wiki/Chief_Justice_of_South_Africa"}]}, {"year": "2012", "text": "<PERSON>, American basketball player and coach (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American soldier, pilot, and politician (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Florida_politician)\" title=\"<PERSON> (Florida politician)\"><PERSON></a>, American soldier, pilot, and politician (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Florida_politician)\" title=\"<PERSON> (Florida politician)\"><PERSON></a>, American soldier, pilot, and politician (b. 1918)", "links": [{"title": "<PERSON> (Florida politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Florida_politician)"}]}, {"year": "2013", "text": "<PERSON>, English drummer (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English drummer (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physicist and academic (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Stirling_Colgate\" title=\"Stirling Colgate\"><PERSON></a>, American physicist and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stirling_Colgate\" title=\"Stirling Colgate\"><PERSON></a>, American physicist and academic (b. 1925)", "links": [{"title": "Stirling Colgate", "link": "https://wikipedia.org/wiki/Stirling_Colgate"}]}, {"year": "2013", "text": "<PERSON>, American soldier (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian cartoonist and songwriter (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cartoonist and songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Argentinian violinist and composer (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian violinist and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian violinist and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Greek epidemiologist, oncologist, and academic (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek epidemiologist, oncologist, and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek epidemiologist, oncologist, and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, New Zealand-Australian author (b. 1959)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian author (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wood\"><PERSON></a>, New Zealand-Australian author (b. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Dutch physicist and computer scientist (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and computer scientist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and computer scientist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "2015", "text": "<PERSON>, American physicist and engineer (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American neurologist and academic (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neurologist and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Australian footballer and coach (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, New Zealand artist (b. 1931)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand artist (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand artist (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American actor, dancer, and singer (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and singer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, English actress (b. 1930)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, American athlete (b. 1948)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American athlete (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American athlete (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, American baseball player and coach (b. 1938)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, first female U.S. Supreme Court Justice (1981-2006) (b. 1930)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, first female U.S. Supreme Court Justice (1981-2006) (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, first female U.S. Supreme Court Justice (1981-2006) (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Connor"}]}, {"year": "2024", "text": "<PERSON>, Welsh snooker player and coach (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and coach (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh snooker player and coach (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian cricketer and coach (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}