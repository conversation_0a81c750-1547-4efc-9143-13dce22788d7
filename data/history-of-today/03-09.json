{"date": "March 9", "url": "https://wikipedia.org/wiki/March_9", "data": {"Events": [{"year": "141 BC", "text": "<PERSON>, posthumously known as Emperor <PERSON> of Han, assumes the throne over the Han dynasty of China.", "html": "141 BC - 141 BC - <PERSON>, <a href=\"https://wikipedia.org/wiki/Posthumous_name\" title=\"Posthumous name\">posthumously</a> known as <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han\" title=\"Emperor <PERSON> of Han\">Emperor <PERSON> of Han</a>, assumes the throne over the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> of China.", "no_year_html": "141 BC - <PERSON>, <a href=\"https://wikipedia.org/wiki/Posthumous_name\" title=\"Posthumous name\">posthumously</a> known as <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Han\" title=\"Emperor <PERSON> of Han\">Emperor <PERSON> of Han</a>, assumes the throne over the <a href=\"https://wikipedia.org/wiki/Han_dynasty\" title=\"Han dynasty\">Han dynasty</a> of China.", "links": [{"title": "Posthumous name", "link": "https://wikipedia.org/wiki/Posthumous_name"}, {"title": "Emperor <PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Han"}, {"title": "Han dynasty", "link": "https://wikipedia.org/wiki/Han_dynasty"}]}, {"year": "1009", "text": "First known mention of Lithuania, in the annals of the monastery of Quedlinburg.", "html": "1009 - First known mention of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, in the <a href=\"https://wikipedia.org/wiki/Annals_of_Quedlinburg\" title=\"Annals of Quedlinburg\">annals</a> of the monastery of <a href=\"https://wikipedia.org/wiki/Quedlinburg\" title=\"Quedlinburg\">Quedlinburg</a>.", "no_year_html": "First known mention of <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>, in the <a href=\"https://wikipedia.org/wiki/Annals_of_Quedlinburg\" title=\"Annals of Quedlinburg\">annals</a> of the monastery of <a href=\"https://wikipedia.org/wiki/Quedlinburg\" title=\"Quedlinburg\">Quedlinburg</a>.", "links": [{"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}, {"title": "Annals of Quedlinburg", "link": "https://wikipedia.org/wiki/Annals_of_Quedlinburg"}, {"title": "Quedlinburg", "link": "https://wikipedia.org/wiki/Quedlinburg"}]}, {"year": "1226", "text": "Khwarazmian sultan <PERSON><PERSON><PERSON> conquers the Georgian capital of Tbilisi.", "html": "1226 - <a href=\"https://wikipedia.org/wiki/Khwarazmian_dynasty\" class=\"mw-redirect\" title=\"Khwarazmian dynasty\">Khwarazmian</a> <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">sultan</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a> conquers the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Georgian</a> capital of <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Khwarazmian_dynasty\" class=\"mw-redirect\" title=\"Khwarazmian dynasty\">Khwarazmian</a> <a href=\"https://wikipedia.org/wiki/Sultan\" title=\"Sultan\">sultan</a> <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON></a> conquers the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Georgia\" title=\"Kingdom of Georgia\">Georgian</a> capital of <a href=\"https://wikipedia.org/wiki/Tbilisi\" title=\"Tbilisi\">Tbilisi</a>.", "links": [{"title": "Khwarazmian dynasty", "link": "https://wikipedia.org/wiki/Khwarazmian_dynasty"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sultan"}, {"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>_<PERSON>"}, {"title": "Kingdom of Georgia", "link": "https://wikipedia.org/wiki/Kingdom_of_Georgia"}, {"title": "Tbilisi", "link": "https://wikipedia.org/wiki/Tbilisi"}]}, {"year": "1230", "text": "Bulgarian Tsar <PERSON> defeats <PERSON> of Epirus in the Battle of Klokotnitsa.", "html": "1230 - <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II_of_Bulgaria\" class=\"mw-redirect\" title=\"<PERSON> II of Bulgaria\">Tsar <PERSON> II</a> defeats <a href=\"https://wikipedia.org/wiki/Despotate_of_Epirus\" title=\"Despotate of Epirus\"><PERSON> of Epirus</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Klokotnitsa\" title=\"Battle of Klokotnitsa\">Battle of Klokotnitsa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgarian</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_II_of_Bulgaria\" class=\"mw-redirect\" title=\"<PERSON> II of Bulgaria\">Tsar <PERSON> II</a> defeats <a href=\"https://wikipedia.org/wiki/Despotate_of_Epirus\" title=\"Despotate of Epirus\"><PERSON> of Epirus</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Klokotnitsa\" title=\"Battle of Klokotnitsa\">Battle of Klokotnitsa</a>.", "links": [{"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II_of_Bulgaria"}, {"title": "Despotate of Epirus", "link": "https://wikipedia.org/wiki/Despotate_of_Epirus"}, {"title": "Battle of Klokotnitsa", "link": "https://wikipedia.org/wiki/Battle_of_Klokotnitsa"}]}, {"year": "1500", "text": "The fleet of <PERSON> leaves Lisbon for the Indies. The fleet will discover Brazil which lies within boundaries granted to Portugal in the Treaty of Tordesillas in 1494.", "html": "1500 - The fleet of <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> for the <a href=\"https://wikipedia.org/wiki/Indies\" class=\"mw-redirect\" title=\"Indies\">Indies</a>. The fleet will discover <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> which lies within boundaries granted to Portugal in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tordesillas\" title=\"Treaty of Tordesillas\">Treaty of Tordesillas</a> in 1494.", "no_year_html": "The fleet of <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> leaves <a href=\"https://wikipedia.org/wiki/Lisbon\" title=\"Lisbon\">Lisbon</a> for the <a href=\"https://wikipedia.org/wiki/Indies\" class=\"mw-redirect\" title=\"Indies\">Indies</a>. The fleet will discover <a href=\"https://wikipedia.org/wiki/Brazil\" title=\"Brazil\">Brazil</a> which lies within boundaries granted to Portugal in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Tordesillas\" title=\"Treaty of Tordesillas\">Treaty of Tordesillas</a> in 1494.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%81l<PERSON><PERSON>_<PERSON>"}, {"title": "Lisbon", "link": "https://wikipedia.org/wiki/Lisbon"}, {"title": "Indies", "link": "https://wikipedia.org/wiki/Indies"}, {"title": "Brazil", "link": "https://wikipedia.org/wiki/Brazil"}, {"title": "Treaty of Tordesillas", "link": "https://wikipedia.org/wiki/Treaty_of_Tordesillas"}]}, {"year": "1701", "text": "Safavid troops retreat from Basra, ending a three-year occupation.", "html": "1701 - <a href=\"https://wikipedia.org/wiki/Safavid_Iran\" title=\"Safavid Iran\">Safavid</a> troops retreat from <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a>, <a href=\"https://wikipedia.org/wiki/Safavid_occupation_of_Basra_(1697%E2%80%931701)\" class=\"mw-redirect\" title=\"Safavid occupation of Basra (1697-1701)\">ending a three-year occupation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Safavid_Iran\" title=\"Safavid Iran\">Safavid</a> troops retreat from <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a>, <a href=\"https://wikipedia.org/wiki/Safavid_occupation_of_Basra_(1697%E2%80%931701)\" class=\"mw-redirect\" title=\"Safavid occupation of Basra (1697-1701)\">ending a three-year occupation</a>.", "links": [{"title": "Safavid Iran", "link": "https://wikipedia.org/wiki/Safavid_Iran"}, {"title": "Basra", "link": "https://wikipedia.org/wiki/Basra"}, {"title": "Safavid occupation of Basra (1697-1701)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_occupation_of_Basra_(1697%E2%80%931701)"}]}, {"year": "1765", "text": "After a campaign by the writer <PERSON><PERSON>, judges in Paris posthumously exonerate <PERSON> of murdering his son. <PERSON><PERSON> had been tortured and executed in 1762 on the charge, though his son may have actually died by suicide.", "html": "1765 - After a campaign by the writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, judges in Paris posthumously exonerate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of murdering his son. <PERSON><PERSON> had been tortured and executed in <a href=\"https://wikipedia.org/wiki/1762\" title=\"1762\">1762</a> on the charge, though his son may have actually died by suicide.", "no_year_html": "After a campaign by the writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, judges in Paris posthumously exonerate <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of murdering his son. <PERSON><PERSON> had been tortured and executed in <a href=\"https://wikipedia.org/wiki/1762\" title=\"1762\">1762</a> on the charge, though his son may have actually died by suicide.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Voltaire"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1762", "link": "https://wikipedia.org/wiki/1762"}]}, {"year": "1776", "text": "Scottish philosopher <PERSON> publishes The Wealth of Nations, ushering in the classical period of political economy.", "html": "1776 - Scottish philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/The_Wealth_of_Nations\" title=\"The Wealth of Nations\">The Wealth of Nations</a></i>, ushering in the <a href=\"https://wikipedia.org/wiki/Classical_economics\" title=\"Classical economics\">classical period</a> of <a href=\"https://wikipedia.org/wiki/Political_economy\" title=\"Political economy\">political economy</a>.", "no_year_html": "Scottish philosopher <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> publishes <i><a href=\"https://wikipedia.org/wiki/The_Wealth_of_Nations\" title=\"The Wealth of Nations\">The Wealth of Nations</a></i>, ushering in the <a href=\"https://wikipedia.org/wiki/Classical_economics\" title=\"Classical economics\">classical period</a> of <a href=\"https://wikipedia.org/wiki/Political_economy\" title=\"Political economy\">political economy</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "The Wealth of Nations", "link": "https://wikipedia.org/wiki/The_Wealth_of_Nations"}, {"title": "Classical economics", "link": "https://wikipedia.org/wiki/Classical_economics"}, {"title": "Political economy", "link": "https://wikipedia.org/wiki/Political_economy"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON><PERSON> marries his first wife, <PERSON><PERSON>.", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> marries his first wife, <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> marries his first wife, <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9<PERSON>_<PERSON>_<PERSON>s"}]}, {"year": "1811", "text": "Paraguayan forces defeat <PERSON> at the Battle of Tacuarí.", "html": "1811 - Paraguayan forces defeat <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tacuar%C3%AD\" title=\"Battle of Tacuarí\">Battle of Tacuarí</a>.", "no_year_html": "Paraguayan forces defeat <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Tacuar%C3%AD\" title=\"Battle of Tacuarí\">Battle of Tacuarí</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Tacuarí", "link": "https://wikipedia.org/wiki/Battle_of_Tacuar%C3%AD"}]}, {"year": "1815", "text": "<PERSON> describes the first battery-operated clock in the Philosophical Magazine.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> describes the first battery-operated clock in the <i><a href=\"https://wikipedia.org/wiki/Philosophical_Magazine\" title=\"Philosophical Magazine\">Philosophical Magazine</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> describes the first battery-operated clock in the <i><a href=\"https://wikipedia.org/wiki/Philosophical_Magazine\" title=\"Philosophical Magazine\">Philosophical Magazine</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Philosophical Magazine", "link": "https://wikipedia.org/wiki/Philosophical_Magazine"}]}, {"year": "1841", "text": "The U.S. Supreme Court rules in the United States v. The Amistad case that captive Africans who had seized control of the ship carrying them had been taken into slavery illegally.", "html": "1841 - The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in the <i><a href=\"https://wikipedia.org/wiki/United_States_v._The_Amistad\" title=\"United States v. The Amistad\">United States v. The Amistad</a></i> case that captive Africans who had seized control of the ship carrying them had been taken into <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> illegally.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_the_United_States\" title=\"Supreme Court of the United States\">U.S. Supreme Court</a> rules in the <i><a href=\"https://wikipedia.org/wiki/United_States_v._The_Amistad\" title=\"United States v. The Amistad\">United States v. The Amistad</a></i> case that captive Africans who had seized control of the ship carrying them had been taken into <a href=\"https://wikipedia.org/wiki/Slavery\" title=\"Slavery\">slavery</a> illegally.", "links": [{"title": "Supreme Court of the United States", "link": "https://wikipedia.org/wiki/Supreme_Court_of_the_United_States"}, {"title": "United States v. The Amistad", "link": "https://wikipedia.org/wiki/United_States_v._The_Amistad"}, {"title": "Slavery", "link": "https://wikipedia.org/wiki/Slavery"}]}, {"year": "1842", "text": "<PERSON>'s third opera, <PERSON><PERSON><PERSON>, receives its première performance in Milan; its success establishes <PERSON> as one of Italy's foremost opera composers.", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s third opera, <i><a href=\"https://wikipedia.org/wiki/Nabucco\" title=\"Nabucco\">Nabucco</a></i>, receives its première performance in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; its success establishes <PERSON> as one of Italy's foremost opera composers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Verdi\" title=\"Giuseppe Verdi\"><PERSON></a>'s third opera, <i><a href=\"https://wikipedia.org/wiki/Nabucco\" title=\"Nabucco\">Nabucco</a></i>, receives its première performance in <a href=\"https://wikipedia.org/wiki/Milan\" title=\"Milan\">Milan</a>; its success establishes <PERSON> as one of Italy's foremost opera composers.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nabucco", "link": "https://wikipedia.org/wiki/Nabucco"}, {"title": "Milan", "link": "https://wikipedia.org/wiki/Milan"}]}, {"year": "1842", "text": "The first documented discovery of gold in California occurs at Rancho San Francisco, six years before the California Gold Rush.", "html": "1842 - The first documented discovery of <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> occurs at <a href=\"https://wikipedia.org/wiki/Rancho_San_Francisco\" title=\"Rancho San Francisco\">Rancho San Francisco</a>, six years before the <a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>.", "no_year_html": "The first documented discovery of <a href=\"https://wikipedia.org/wiki/Gold\" title=\"Gold\">gold</a> in <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a> occurs at <a href=\"https://wikipedia.org/wiki/Rancho_San_Francisco\" title=\"Rancho San Francisco\">Rancho San Francisco</a>, six years before the <a href=\"https://wikipedia.org/wiki/California_Gold_Rush\" class=\"mw-redirect\" title=\"California Gold Rush\">California Gold Rush</a>.", "links": [{"title": "Gold", "link": "https://wikipedia.org/wiki/Gold"}, {"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "Rancho San Francisco", "link": "https://wikipedia.org/wiki/Rancho_San_Francisco"}, {"title": "California Gold Rush", "link": "https://wikipedia.org/wiki/California_Gold_Rush"}]}, {"year": "1847", "text": "Mexican-American War: The first large-scale amphibious assault in U.S. history is launched in the Siege of Veracruz.", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The first large-scale <a href=\"https://wikipedia.org/wiki/Amphibious_assault\" class=\"mw-redirect\" title=\"Amphibious assault\">amphibious assault</a> in U.S. history is launched in the <a href=\"https://wikipedia.org/wiki/Siege_of_Veracruz\" title=\"Siege of Veracruz\">Siege of Veracruz</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>: The first large-scale <a href=\"https://wikipedia.org/wiki/Amphibious_assault\" class=\"mw-redirect\" title=\"Amphibious assault\">amphibious assault</a> in U.S. history is launched in the <a href=\"https://wikipedia.org/wiki/Siege_of_Veracruz\" title=\"Siege of Veracruz\">Siege of Veracruz</a>.", "links": [{"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}, {"title": "Amphibious assault", "link": "https://wikipedia.org/wiki/Amphibious_assault"}, {"title": "Siege of Veracruz", "link": "https://wikipedia.org/wiki/Siege_of_Veracruz"}]}, {"year": "1862", "text": "American Civil War: USS Monitor and CSS Virginia (rebuilt from the engines and lower hull of the USS Merrimack) fight to a draw in the Battle of Hampton Roads, the first battle between two ironclad warships.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/USS_Monitor\" title=\"USS Monitor\">USS <i>Monitor</i></a> and <a href=\"https://wikipedia.org/wiki/CSS_Virginia\" title=\"CSS Virginia\">CSS <i>Virginia</i></a> (rebuilt from the engines and lower hull of the <a href=\"https://wikipedia.org/wiki/USS_Merrimack_(1855)\" title=\"USS Merrimack (1855)\">USS <i>Merrimack</i></a>) fight to a draw in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hampton_Roads\" title=\"Battle of Hampton Roads\">Battle of Hampton Roads</a>, the first battle between two <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad warships</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/USS_Monitor\" title=\"USS Monitor\">USS <i>Monitor</i></a> and <a href=\"https://wikipedia.org/wiki/CSS_Virginia\" title=\"CSS Virginia\">CSS <i>Virginia</i></a> (rebuilt from the engines and lower hull of the <a href=\"https://wikipedia.org/wiki/USS_Merrimack_(1855)\" title=\"USS Merrimack (1855)\">USS <i>Merrimack</i></a>) fight to a draw in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hampton_Roads\" title=\"Battle of Hampton Roads\">Battle of Hampton Roads</a>, the first battle between two <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad warships</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "USS Monitor", "link": "https://wikipedia.org/wiki/USS_Monitor"}, {"title": "CSS Virginia", "link": "https://wikipedia.org/wiki/CSS_Virginia"}, {"title": "USS Merrimack (1855)", "link": "https://wikipedia.org/wiki/USS_Merrimack_(1855)"}, {"title": "Battle of Hampton Roads", "link": "https://wikipedia.org/wiki/Battle_of_Hampton_Roads"}, {"title": "Ironclad warship", "link": "https://wikipedia.org/wiki/Ironclad_warship"}]}, {"year": "1908", "text": "Inter Milan was founded on Football Club Internazionale, following a schism from A.C. Milan.", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Inter_Milan\" title=\"Inter Milan\">Inter Milan</a> was founded on <i>Football Club Internazionale</i>, following a schism from <a href=\"https://wikipedia.org/wiki/A.C._Milan\" class=\"mw-redirect\" title=\"A.C. Milan\">A.C. Milan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inter_Milan\" title=\"Inter Milan\">Inter Milan</a> was founded on <i>Football Club Internazionale</i>, following a schism from <a href=\"https://wikipedia.org/wiki/A.C._Milan\" class=\"mw-redirect\" title=\"A.C. Milan\">A.C. Milan</a>.", "links": [{"title": "Inter Milan", "link": "https://wikipedia.org/wiki/Inter_Milan"}, {"title": "A.C. Milan", "link": "https://wikipedia.org/wiki/A.C._Milan"}]}, {"year": "1916", "text": "Mexican Revolution: Pancho Villa leads nearly 500 Mexican raiders in an attack against the border town of Columbus, New Mexico.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a> leads nearly 500 Mexican raiders in <a href=\"https://wikipedia.org/wiki/Battle_of_Columbus_(1916)\" title=\"Battle of Columbus (1916)\">an attack</a> against the border town of <a href=\"https://wikipedia.org/wiki/Columbus,_New_Mexico\" title=\"Columbus, New Mexico\">Columbus, New Mexico</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Pancho_Villa\" title=\"Pancho Villa\">Pancho Villa</a> leads nearly 500 Mexican raiders in <a href=\"https://wikipedia.org/wiki/Battle_of_Columbus_(1916)\" title=\"Battle of Columbus (1916)\">an attack</a> against the border town of <a href=\"https://wikipedia.org/wiki/Columbus,_New_Mexico\" title=\"Columbus, New Mexico\">Columbus, New Mexico</a>.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "Pancho <PERSON>", "link": "https://wikipedia.org/wiki/Pancho_Villa"}, {"title": "Battle of Columbus (1916)", "link": "https://wikipedia.org/wiki/Battle_of_Columbus_(1916)"}, {"title": "Columbus, New Mexico", "link": "https://wikipedia.org/wiki/Columbus,_New_Mexico"}]}, {"year": "1933", "text": "Great Depression: President <PERSON> submits the Emergency Banking Act to Congress, the first of his New Deal policies.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> submits the <a href=\"https://wikipedia.org/wiki/Emergency_Banking_Act\" class=\"mw-redirect\" title=\"Emergency Banking Act\">Emergency Banking Act</a> to <PERSON>, the first of his <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a> policies.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>: President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> submits the <a href=\"https://wikipedia.org/wiki/Emergency_Banking_Act\" class=\"mw-redirect\" title=\"Emergency Banking Act\">Emergency Banking Act</a> to <PERSON>, the first of his <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a> policies.", "links": [{"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Emergency Banking Act", "link": "https://wikipedia.org/wiki/Emergency_Banking_Act"}, {"title": "New Deal", "link": "https://wikipedia.org/wiki/New_Deal"}]}, {"year": "1942", "text": "World War II: Dutch East Indies unconditionally surrendered to the Japanese forces in Kalijati, Subang, West Java, and the Japanese completed their Dutch East Indies campaign.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a> unconditionally surrendered to the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces in Kalijati, <a href=\"https://wikipedia.org/wiki/Subang_Regency\" title=\"Subang Regency\">Subang</a>, <a href=\"https://wikipedia.org/wiki/West_Java\" title=\"West Java\">West Java</a>, and the Japanese completed their <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies_campaign\" title=\"Dutch East Indies campaign\">Dutch East Indies campaign</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a> unconditionally surrendered to the <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces in Kalijati, <a href=\"https://wikipedia.org/wiki/Subang_Regency\" title=\"Subang Regency\">Subang</a>, <a href=\"https://wikipedia.org/wiki/West_Java\" title=\"West Java\">West Java</a>, and the Japanese completed their <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies_campaign\" title=\"Dutch East Indies campaign\">Dutch East Indies campaign</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Subang Regency", "link": "https://wikipedia.org/wiki/Subang_Regency"}, {"title": "West Java", "link": "https://wikipedia.org/wiki/West_Java"}, {"title": "Dutch East Indies campaign", "link": "https://wikipedia.org/wiki/Dutch_East_Indies_campaign"}]}, {"year": "1944", "text": "World War II: Soviet Army planes attack Tallinn, Estonia.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_Tallinn_in_World_War_II\" title=\"Bombing of Tallinn in World War II\">Soviet Army planes attack Tallinn, Estonia</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Bombing_of_Tallinn_in_World_War_II\" title=\"Bombing of Tallinn in World War II\">Soviet Army planes attack Tallinn, Estonia</a>.", "links": [{"title": "Bombing of Tallinn in World War II", "link": "https://wikipedia.org/wiki/Bombing_of_Tallinn_in_World_War_II"}]}, {"year": "1945", "text": "World War II: A coup d'état by Japanese forces in French Indochina removes the French from power.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Japanese_coup_d%27%C3%A9tat_in_French_Indochina\" title=\"Japanese coup d'état in French Indochina\">A coup d'état</a> by Japanese forces in <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a> removes the French from power.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Japanese_coup_d%27%C3%A9tat_in_French_Indochina\" title=\"Japanese coup d'état in French Indochina\">A coup d'état</a> by Japanese forces in <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a> removes the French from power.", "links": [{"title": "Japanese coup d'état in French Indochina", "link": "https://wikipedia.org/wiki/Japanese_coup_d%27%C3%A9tat_in_French_Indochina"}, {"title": "French Indochina", "link": "https://wikipedia.org/wiki/French_Indochina"}]}, {"year": "1945", "text": "World War II: Allied forces carry out firebombing over Tokyo, destroying most of the capital and killing over 100,000 civilians.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Allied_forces_(World_War_II)\" class=\"mw-redirect\" title=\"Allied forces (World War II)\">Allied forces</a> carry out <a href=\"https://wikipedia.org/wiki/Bombing_of_Tokyo\" title=\"Bombing of Tokyo\">firebombing over Tokyo</a>, destroying most of the capital and killing over 100,000 civilians.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Allied_forces_(World_War_II)\" class=\"mw-redirect\" title=\"Allied forces (World War II)\">Allied forces</a> carry out <a href=\"https://wikipedia.org/wiki/Bombing_of_Tokyo\" title=\"Bombing of Tokyo\">firebombing over Tokyo</a>, destroying most of the capital and killing over 100,000 civilians.", "links": [{"title": "Allied forces (World War II)", "link": "https://wikipedia.org/wiki/Allied_forces_(World_War_II)"}, {"title": "Bombing of Tokyo", "link": "https://wikipedia.org/wiki/Bombing_of_Tokyo"}]}, {"year": "1946", "text": "Bolton Wanderers stadium disaster at Burnden Park, Bolton, England, kills 33 and injures hundreds more.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Bolton_Wanderers_F.C.\" title=\"Bolton Wanderers F.C.\">Bolton Wanderers</a> <a href=\"https://wikipedia.org/wiki/Burnden_Park_disaster\" title=\"Burnden Park disaster\">stadium disaster</a> at <a href=\"https://wikipedia.org/wiki/Burnden_Park\" title=\"Burnden Park\">Burnden Park</a>, <a href=\"https://wikipedia.org/wiki/Bolton\" title=\"Bolton\">Bolton</a>, England, kills 33 and injures hundreds more.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bolton_Wanderers_F.C.\" title=\"Bolton Wanderers F.C.\">Bolton Wanderers</a> <a href=\"https://wikipedia.org/wiki/Burnden_Park_disaster\" title=\"Burnden Park disaster\">stadium disaster</a> at <a href=\"https://wikipedia.org/wiki/Burnden_Park\" title=\"Burnden Park\">Burnden Park</a>, <a href=\"https://wikipedia.org/wiki/Bolton\" title=\"Bolton\">Bolton</a>, England, kills 33 and injures hundreds more.", "links": [{"title": "Bolton Wanderers F.C.", "link": "https://wikipedia.org/wiki/Bolton_Wanderers_F.C."}, {"title": "Burnden Park disaster", "link": "https://wikipedia.org/wiki/Burnden_Park_disaster"}, {"title": "Burnden Park", "link": "https://wikipedia.org/wiki/Burnden_Park"}, {"title": "Bolton", "link": "https://wikipedia.org/wiki/Bolton"}]}, {"year": "1954", "text": "McCarthyism: CBS television broadcasts the See It Now episode, \"A Report on Senator <PERSON>\", produced by <PERSON>.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/McCarthyism\" title=\"McCarthyism\">McCarthyism</a>: <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> television broadcasts the <i><a href=\"https://wikipedia.org/wiki/See_It_Now\" title=\"See It Now\">See It Now</a></i> episode, \"A Report on Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>\", produced by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Fred Friendly\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/McCarthyism\" title=\"McCarthyism\">McCarthyism</a>: <a href=\"https://wikipedia.org/wiki/CBS\" title=\"CBS\">CBS</a> television broadcasts the <i><a href=\"https://wikipedia.org/wiki/See_It_Now\" title=\"See It Now\">See It Now</a></i> episode, \"A Report on Senator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>\", produced by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Friendly\"><PERSON></a>.", "links": [{"title": "McCarthyism", "link": "https://wikipedia.org/wiki/McCarthyism"}, {"title": "CBS", "link": "https://wikipedia.org/wiki/CBS"}, {"title": "See It Now", "link": "https://wikipedia.org/wiki/See_It_Now"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "Soviet forces suppress mass demonstrations in the Georgian SSR, reacting to <PERSON><PERSON>'s de-Stalinization policy.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet forces</a> suppress <a href=\"https://wikipedia.org/wiki/1956_Georgian_demonstrations\" title=\"1956 Georgian demonstrations\">mass demonstrations</a> in the <a href=\"https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic\" title=\"Georgian Soviet Socialist Republic\">Georgian SSR</a>, reacting to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/De-Stalinization\" title=\"De-Stalinization\">de-Stalinization</a> policy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Soviet forces</a> suppress <a href=\"https://wikipedia.org/wiki/1956_Georgian_demonstrations\" title=\"1956 Georgian demonstrations\">mass demonstrations</a> in the <a href=\"https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic\" title=\"Georgian Soviet Socialist Republic\">Georgian SSR</a>, reacting to <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/De-Stalinization\" title=\"De-Stalinization\">de-Stalinization</a> policy.", "links": [{"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "1956 Georgian demonstrations", "link": "https://wikipedia.org/wiki/1956_Georgian_demonstrations"}, {"title": "Georgian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "De-Stalinization", "link": "https://wikipedia.org/wiki/De-Stalinization"}]}, {"year": "1957", "text": "The 8.6 Mw  Andreanof Islands earthquake shakes the Aleutian Islands, causing over $5 million in damage from ground movement and a destructive tsunami.", "html": "1957 - The 8.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1957_Andreanof_Islands_earthquake\" title=\"1957 Andreanof Islands earthquake\">Andreanof Islands earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Aleutian_Islands\" title=\"Aleutian Islands\">Aleutian Islands</a>, causing over $5 million in damage from ground movement and a destructive tsunami.", "no_year_html": "The 8.6 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1957_Andreanof_Islands_earthquake\" title=\"1957 Andreanof Islands earthquake\">Andreanof Islands earthquake</a> shakes the <a href=\"https://wikipedia.org/wiki/Aleutian_Islands\" title=\"Aleutian Islands\">Aleutian Islands</a>, causing over $5 million in damage from ground movement and a destructive tsunami.", "links": [{"title": "1957 Andreanof Islands earthquake", "link": "https://wikipedia.org/wiki/1957_Andreanof_Islands_earthquake"}, {"title": "Aleutian Islands", "link": "https://wikipedia.org/wiki/Aleutian_Islands"}]}, {"year": "1959", "text": "The Barbie doll makes its debut at the American International Toy Fair in New York.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie</a> doll makes its debut at the <a href=\"https://wikipedia.org/wiki/American_International_Toy_Fair\" class=\"mw-redirect\" title=\"American International Toy Fair\">American International Toy Fair</a> in New York.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Barbie\" title=\"Barbie\">Barbie</a> doll makes its debut at the <a href=\"https://wikipedia.org/wiki/American_International_Toy_Fair\" class=\"mw-redirect\" title=\"American International Toy Fair\">American International Toy Fair</a> in New York.", "links": [{"title": "Barbie", "link": "https://wikipedia.org/wiki/Barbie"}, {"title": "American International Toy Fair", "link": "https://wikipedia.org/wiki/American_International_Toy_Fair"}]}, {"year": "1960", "text": "Dr. <PERSON><PERSON> implants for the first time a shunt he invented into a patient, which allows the patient to receive hemodialysis on a regular basis.", "html": "1960 - Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>ding_Hibbard_Scribner\" title=\"Belding Hibbard Scribner\">Belding Hibbard Scribner</a> implants for the first time a <a href=\"https://wikipedia.org/wiki/Shunt_(medical)\" title=\"Shunt (medical)\">shunt</a> he invented into a patient, which allows the patient to receive <a href=\"https://wikipedia.org/wiki/Hemodialysis\" title=\"Hemodialysis\">hemodialysis</a> on a regular basis.", "no_year_html": "Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>ding_Hibbard_Scribner\" title=\"Belding Hibbard Scribner\">Belding Hibbard Scribner</a> implants for the first time a <a href=\"https://wikipedia.org/wiki/Shunt_(medical)\" title=\"Shunt (medical)\">shunt</a> he invented into a patient, which allows the patient to receive <a href=\"https://wikipedia.org/wiki/Hemodialysis\" title=\"Hemodialysis\">hemodialysis</a> on a regular basis.", "links": [{"title": "Belding Hibbard <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> (medical)", "link": "https://wikipedia.org/wiki/Shunt_(medical)"}, {"title": "Hemodialysis", "link": "https://wikipedia.org/wiki/Hemodialysis"}]}, {"year": "1961", "text": "Sputnik 9 successfully launches, carrying a dog and a human dummy, and demonstrating that the Soviet Union was ready to begin human spaceflight.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Sputnik_9\" class=\"mw-redirect\" title=\"Sputnik 9\">Sputnik 9</a> successfully launches, carrying a dog and a human dummy, and demonstrating that the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> was ready to begin <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">human spaceflight</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sputnik_9\" class=\"mw-redirect\" title=\"Sputnik 9\">Sputnik 9</a> successfully launches, carrying a dog and a human dummy, and demonstrating that the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> was ready to begin <a href=\"https://wikipedia.org/wiki/Human_spaceflight\" title=\"Human spaceflight\">human spaceflight</a>.", "links": [{"title": "Sputnik 9", "link": "https://wikipedia.org/wiki/Sputnik_9"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Human spaceflight", "link": "https://wikipedia.org/wiki/Human_spaceflight"}]}, {"year": "1967", "text": "Trans World Airlines Flight 553 crashes in a field in Concord Township, Ohio, following a mid-air collision with a Beechcraft Baron, killing 26 people.", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">Trans World Airlines</a> <a href=\"https://wikipedia.org/wiki/TWA_Flight_553\" title=\"TWA Flight 553\">Flight 553</a> crashes in a field in <a href=\"https://wikipedia.org/wiki/Concord_Township,_Champaign_County,_Ohio\" title=\"Concord Township, Champaign County, Ohio\">Concord Township, Ohio</a>, following a <a href=\"https://wikipedia.org/wiki/Mid-air_collision\" title=\"Mid-air collision\">mid-air collision</a> with a <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Baron\" title=\"<PERSON><PERSON><PERSON> Baron\"><PERSON><PERSON><PERSON> Baron</a>, killing 26 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trans_World_Airlines\" title=\"Trans World Airlines\">Trans World Airlines</a> <a href=\"https://wikipedia.org/wiki/TWA_Flight_553\" title=\"TWA Flight 553\">Flight 553</a> crashes in a field in <a href=\"https://wikipedia.org/wiki/Concord_Township,_Champaign_County,_Ohio\" title=\"Concord Township, Champaign County, Ohio\">Concord Township, Ohio</a>, following a <a href=\"https://wikipedia.org/wiki/Mid-air_collision\" title=\"Mid-air collision\">mid-air collision</a> with a <a href=\"https://wikipedia.org/wiki/Bee<PERSON><PERSON>_Baron\" title=\"<PERSON><PERSON><PERSON> Baron\"><PERSON><PERSON><PERSON> Baron</a>, killing 26 people.", "links": [{"title": "Trans World Airlines", "link": "https://wikipedia.org/wiki/Trans_World_Airlines"}, {"title": "TWA Flight 553", "link": "https://wikipedia.org/wiki/TWA_Flight_553"}, {"title": "Concord Township, Champaign County, Ohio", "link": "https://wikipedia.org/wiki/Concord_Township,_Champaign_County,_Ohio"}, {"title": "Mid-air collision", "link": "https://wikipedia.org/wiki/Mid-air_collision"}, {"title": "Beechcraft Baron", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Baron"}]}, {"year": "1974", "text": "The Mars 7 Flyby bus releases the descent module too early, missing Mars.", "html": "1974 - The <a href=\"https://wikipedia.org/wiki/Mars_7\" title=\"Mars 7\">Mars 7</a> Flyby bus releases the descent module too early, missing Mars.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mars_7\" title=\"Mars 7\">Mars 7</a> Flyby bus releases the descent module too early, missing Mars.", "links": [{"title": "Mars 7", "link": "https://wikipedia.org/wiki/Mars_7"}]}, {"year": "1976", "text": "Forty-two people die in the Cavalese cable car disaster, the deadliest cable car accident in history.", "html": "1976 - Forty-two people die in the <a href=\"https://wikipedia.org/wiki/1976_Cavalese_cable_car_crash\" title=\"1976 Cavalese cable car crash\">Cavalese cable car disaster</a>, the deadliest cable car accident in history.", "no_year_html": "Forty-two people die in the <a href=\"https://wikipedia.org/wiki/1976_Cavalese_cable_car_crash\" title=\"1976 Cavalese cable car crash\">Cavalese cable car disaster</a>, the deadliest cable car accident in history.", "links": [{"title": "1976 Cavalese cable car crash", "link": "https://wikipedia.org/wiki/1976_Cavalese_cable_car_crash"}]}, {"year": "1977", "text": "The Hanafi Siege: In a 39-hour standoff, armed Hanafi Muslims seize three Washington, D.C., buildings.", "html": "1977 - The <a href=\"https://wikipedia.org/wiki/1977_Hanafi_Siege\" class=\"mw-redirect\" title=\"1977 Hanafi Siege\">Hanafi Siege</a>: In a 39-hour standoff, armed <a href=\"https://wikipedia.org/wiki/Hanafi\" class=\"mw-redirect\" title=\"Han<PERSON>i\"><PERSON><PERSON><PERSON></a> Muslims seize three <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, buildings.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1977_Hanafi_Siege\" class=\"mw-redirect\" title=\"1977 Hanafi Siege\">Hanafi Siege</a>: In a 39-hour standoff, armed <a href=\"https://wikipedia.org/wiki/Hanafi\" class=\"mw-redirect\" title=\"Hanafi\"><PERSON><PERSON>i</a> Muslims seize three <a href=\"https://wikipedia.org/wiki/Washington,_D.C.\" title=\"Washington, D.C.\">Washington, D.C.</a>, buildings.", "links": [{"title": "1977 Hanafi Siege", "link": "https://wikipedia.org/wiki/1977_Han<PERSON>i_Siege"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>i"}, {"title": "Washington, D.C.", "link": "https://wikipedia.org/wiki/Washington,_D.C."}]}, {"year": "1978", "text": "President <PERSON><PERSON><PERSON><PERSON> inaugurated Jagorawi Toll Road, the first toll highway in Indonesia, connecting Jakarta, Bogor and Ciawi, West Java.", "html": "1978 - President <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suharto\"><PERSON><PERSON><PERSON><PERSON></a> inaugurated <a href=\"https://wikipedia.org/wiki/Jagorawi_Toll_Road\" title=\"Jagorawi Toll Road\">Jagorawi Toll Road</a>, the first <a href=\"https://wikipedia.org/wiki/Toll_highway\" class=\"mw-redirect\" title=\"Toll highway\">toll highway</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, connecting <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, <a href=\"https://wikipedia.org/wiki/Bogor\" title=\"Bogor\">Bogor</a> and Ciawi, <a href=\"https://wikipedia.org/wiki/West_Java\" title=\"West Java\">West Java</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Suharto\" title=\"Suharto\"><PERSON><PERSON><PERSON><PERSON></a> inaugurated <a href=\"https://wikipedia.org/wiki/Jagorawi_Toll_Road\" title=\"Jagorawi Toll Road\">Jagorawi Toll Road</a>, the first <a href=\"https://wikipedia.org/wiki/Toll_highway\" class=\"mw-redirect\" title=\"Toll highway\">toll highway</a> in <a href=\"https://wikipedia.org/wiki/Indonesia\" title=\"Indonesia\">Indonesia</a>, connecting <a href=\"https://wikipedia.org/wiki/Jakarta\" title=\"Jakarta\">Jakarta</a>, <a href=\"https://wikipedia.org/wiki/Bogor\" title=\"Bogor\">Bogor</a> and Ciawi, <a href=\"https://wikipedia.org/wiki/West_Java\" title=\"West Java\">West Java</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Su<PERSON>o"}, {"title": "Jagorawi Toll Road", "link": "https://wikipedia.org/wiki/Jagorawi_Toll_Road"}, {"title": "Toll highway", "link": "https://wikipedia.org/wiki/Toll_highway"}, {"title": "Indonesia", "link": "https://wikipedia.org/wiki/Indonesia"}, {"title": "Jakarta", "link": "https://wikipedia.org/wiki/Jakarta"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>gor"}, {"title": "West Java", "link": "https://wikipedia.org/wiki/West_Java"}]}, {"year": "1987", "text": "Chrysler announces its acquisition of American Motors Corporation.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> announces its acquisition of <a href=\"https://wikipedia.org/wiki/American_Motors_Corporation\" title=\"American Motors Corporation\">American Motors Corporation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chrysler\" title=\"Chrysler\">Chrysler</a> announces its acquisition of <a href=\"https://wikipedia.org/wiki/American_Motors_Corporation\" title=\"American Motors Corporation\">American Motors Corporation</a>.", "links": [{"title": "Chrysler", "link": "https://wikipedia.org/wiki/Chrysler"}, {"title": "American Motors Corporation", "link": "https://wikipedia.org/wiki/American_Motors_Corporation"}]}, {"year": "1997", "text": "Comet Hale-Bopp: Observers in China, Mongolia and eastern Siberia are treated to a rare double feature as an eclipse permits <PERSON><PERSON><PERSON><PERSON> to be seen during the day.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\">Comet Hale-Bopp</a>: Observers in China, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and eastern <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a> are treated to a rare double feature as an <a href=\"https://wikipedia.org/wiki/Eclipse\" title=\"Eclipse\">eclipse</a> permits <PERSON><PERSON><PERSON><PERSON> to be seen during the day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp\" title=\"Comet Hale-Bopp\"><PERSON> Hale-Bopp</a>: Observers in China, <a href=\"https://wikipedia.org/wiki/Mongolia\" title=\"Mongolia\">Mongolia</a> and eastern <a href=\"https://wikipedia.org/wiki/Siberia\" title=\"Siberia\">Siberia</a> are treated to a rare double feature as an <a href=\"https://wikipedia.org/wiki/Eclipse\" title=\"Eclipse\">eclipse</a> permits <PERSON><PERSON><PERSON><PERSON> to be seen during the day.", "links": [{"title": "Comet Hale-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Comet_Hale%E2%80%93Bopp"}, {"title": "Mongolia", "link": "https://wikipedia.org/wiki/Mongolia"}, {"title": "Siberia", "link": "https://wikipedia.org/wiki/Siberia"}, {"title": "Eclipse", "link": "https://wikipedia.org/wiki/Eclipse"}]}, {"year": "2000", "text": "Nupedia, a multi-language online encyclopedia, is launched.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Nupedia\" title=\"Nupedia\">Nupedia</a>, a multi-language <a href=\"https://wikipedia.org/wiki/Online_encyclopedia\" title=\"Online encyclopedia\">online encyclopedia</a>, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nupedia\" title=\"Nupedia\">Nupedia</a>, a multi-language <a href=\"https://wikipedia.org/wiki/Online_encyclopedia\" title=\"Online encyclopedia\">online encyclopedia</a>, is launched.", "links": [{"title": "Nupedia", "link": "https://wikipedia.org/wiki/Nupedia"}, {"title": "Online encyclopedia", "link": "https://wikipedia.org/wiki/Online_encyclopedia"}]}, {"year": "2011", "text": "Space Shuttle Discovery makes its final landing after 39 flights.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> makes its final landing after 39 flights.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> makes its final landing after 39 flights.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}]}, {"year": "2012", "text": "A truce between the Salvadoran government and gangs in the country goes into effect when 30 gang leaders are transferred to lower security prisons.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/2012%E2%80%932014_Salvadoran_gang_truce\" title=\"2012-2014 Salvadoran gang truce\">truce</a> between the Salvadoran government and gangs in the country goes into effect when 30 gang leaders are transferred to lower security prisons.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2012%E2%80%932014_Salvadoran_gang_truce\" title=\"2012-2014 Salvadoran gang truce\">truce</a> between the Salvadoran government and gangs in the country goes into effect when 30 gang leaders are transferred to lower security prisons.", "links": [{"title": "2012-2014 Salvadoran gang truce", "link": "https://wikipedia.org/wiki/2012%E2%80%932014_Salvadoran_gang_truce"}]}, {"year": "2015", "text": "Two Eurocopter AS350 Écureuil helicopters collide in mid-air over Villa Castelli, Argentina, killing all 10 people on board both aircraft, including French athletes <PERSON>, <PERSON> and <PERSON>, as well as producers and guests for the French TV show Dropped.", "html": "2015 - Two <a href=\"https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil\" title=\"Eurocopter AS350 Écureuil\">Eurocopter AS350 Écureuil</a> helicopters <a href=\"https://wikipedia.org/wiki/2015_<PERSON>_Castelli_mid-air_collision\" title=\"2015 <PERSON> Castelli mid-air collision\">collide</a> in mid-air over <a href=\"https://wikipedia.org/wiki/Villa_Castelli,_Argentina\" title=\"Villa Castelli, Argentina\">Villa Castelli, Argentina</a>, killing all 10 people on board both aircraft, including French athletes <a href=\"https://wikipedia.org/wiki/Florence_Arthaud\" title=\"Florence Arthaud\"><PERSON>ha<PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexis Vastine\"><PERSON></a>, as well as producers and guests for the French TV show <i><a href=\"https://wikipedia.org/wiki/Dropped_(TV_series)\" title=\"Dropped (TV series)\">Dropped</a></i>.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil\" title=\"Eurocopter AS350 Écureuil\">Eurocopter AS350 Écureuil</a> helicopters <a href=\"https://wikipedia.org/wiki/2015_<PERSON>_Castelli_mid-air_collision\" title=\"2015 <PERSON> Castelli mid-air collision\">collide</a> in mid-air over <a href=\"https://wikipedia.org/wiki/Villa_Castelli,_Argentina\" title=\"Villa Castelli, Argentina\">Villa Castelli, Argentina</a>, killing all 10 people on board both aircraft, including French athletes <a href=\"https://wikipedia.org/wiki/Florence_Arthaud\" title=\"Florence Arthaud\"><PERSON>haud</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Alexis Vastine\"><PERSON></a>, as well as producers and guests for the French TV show <i><a href=\"https://wikipedia.org/wiki/Dropped_(TV_series)\" title=\"Dropped (TV series)\">Dropped</a></i>.", "links": [{"title": "Eurocopter AS350 Écureuil", "link": "https://wikipedia.org/wiki/Eurocopter_AS350_%C3%89cureuil"}, {"title": "2015 <PERSON> Castelli mid-air collision", "link": "https://wikipedia.org/wiki/2015_<PERSON>_Castelli_mid-air_collision"}, {"title": "Villa <PERSON>, Argentina", "link": "https://wikipedia.org/wiki/Villa_Castelli,_Argentina"}, {"title": "<PERSON>ha<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Arthaud"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dropped (TV series)", "link": "https://wikipedia.org/wiki/Dropped_(TV_series)"}]}, {"year": "2020", "text": "<PERSON>, Prime Minister of Italy, announces in a televised address and signs the decree imposing the first nationwide COVID-19 lockdown in the world.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>, announces in a televised address and signs the decree imposing the <a href=\"https://wikipedia.org/wiki/COVID-19_lockdowns_in_Italy\" title=\"COVID-19 lockdowns in Italy\">first nationwide</a> <a href=\"https://wikipedia.org/wiki/COVID-19_lockdowns\" title=\"COVID-19 lockdowns\">COVID-19 lockdown</a> in the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a>, announces in a televised address and signs the decree imposing the <a href=\"https://wikipedia.org/wiki/COVID-19_lockdowns_in_Italy\" title=\"COVID-19 lockdowns in Italy\">first nationwide</a> <a href=\"https://wikipedia.org/wiki/COVID-19_lockdowns\" title=\"COVID-19 lockdowns\">COVID-19 lockdown</a> in the world.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}, {"title": "COVID-19 lockdowns in Italy", "link": "https://wikipedia.org/wiki/COVID-19_lockdowns_in_Italy"}, {"title": "COVID-19 lockdowns", "link": "https://wikipedia.org/wiki/COVID-19_lockdowns"}]}, {"year": "2023", "text": "A shooting in the Alsterdorf quarter of Hamburg, Germany, kills eight people and injures another eight.", "html": "2023 - A <a href=\"https://wikipedia.org/wiki/2023_Hamburg_shooting\" title=\"2023 Hamburg shooting\">shooting</a> in the <a href=\"https://wikipedia.org/wiki/Alsterdorf\" title=\"Alsterdorf\">Alsterdorf</a> quarter of <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany, kills eight people and injures another eight.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2023_Hamburg_shooting\" title=\"2023 Hamburg shooting\">shooting</a> in the <a href=\"https://wikipedia.org/wiki/Alsterdorf\" title=\"Alsterdorf\">Alsterdorf</a> quarter of <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a>, Germany, kills eight people and injures another eight.", "links": [{"title": "2023 Hamburg shooting", "link": "https://wikipedia.org/wiki/2023_Hamburg_shooting"}, {"title": "Alsterdorf", "link": "https://wikipedia.org/wiki/Alsterdorf"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}]}], "Births": [{"year": "1451", "text": "<PERSON><PERSON><PERSON>, Italian cartographer and explorer, namesake of the Americas (d. 1512)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/Amerigo_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cartographer and explorer, namesake of the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a> (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ameri<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cartographer and explorer, namesake of the <a href=\"https://wikipedia.org/wiki/Americas\" title=\"Americas\">Americas</a> (d. 1512)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amerigo_<PERSON>"}, {"title": "Americas", "link": "https://wikipedia.org/wiki/Americas"}]}, {"year": "1534", "text": "<PERSON> of Anchieta, Spanish Jesuit saint and missionary (d. 1597)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anchieta\" title=\"<PERSON> of Anchieta\"><PERSON> of Anchieta</a>, Spanish Jesuit saint and missionary (d. 1597)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Anchieta\" title=\"<PERSON> of Anchieta\"><PERSON> of Anchieta</a>, Spanish Jesuit saint and missionary (d. 1597)", "links": [{"title": "<PERSON> of Anchieta", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1564", "text": "<PERSON>, German theologian, cartographer and astronomer (d. 1617)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian, cartographer and astronomer (d. 1617)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian, cartographer and astronomer (d. 1617)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1568", "text": "<PERSON><PERSON><PERSON>, Italian saint, namesake of Gonzaga University (d. 1591)", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint, namesake of <a href=\"https://wikipedia.org/wiki/Gonzaga_University\" title=\"Gonzaga University\">Gonzaga University</a> (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian saint, namesake of <a href=\"https://wikipedia.org/wiki/Gonzaga_University\" title=\"Gonzaga University\">Gonzaga University</a> (d. 1591)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Gonzaga University", "link": "https://wikipedia.org/wiki/Gonzaga_University"}]}, {"year": "1662", "text": "<PERSON>, German noble (d. 1738)", "html": "1662 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German noble (d. 1738)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German noble (d. 1738)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1697", "text": "<PERSON><PERSON><PERSON><PERSON>, German actress (d. 1760)", "html": "1697 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German actress (d. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German actress (d. 1760)", "links": [{"title": "Friederike <PERSON>", "link": "https://wikipedia.org/wiki/Friederi<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, Czech violinist and composer (d. 1781)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech violinist and composer (d. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Josef_Myslive%C4%8Dek"}]}, {"year": "1749", "text": "<PERSON><PERSON>, comte <PERSON>, French journalist and politician (d. 1791)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>,_comte_<PERSON>_Mira<PERSON>au\" title=\"<PERSON><PERSON>, comte de Mirabeau\"><PERSON><PERSON>, comte <PERSON></a>, French journalist and politician (d. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Honor%C3%A9_<PERSON>,_comte_<PERSON>_Mira<PERSON>au\" title=\"<PERSON><PERSON>, comte de Mirabeau\"><PERSON><PERSON>, comte <PERSON>au</a>, French journalist and politician (d. 1791)", "links": [{"title": "<PERSON><PERSON>, comte <PERSON>au", "link": "https://wikipedia.org/wiki/Honor%C3%A<PERSON>_<PERSON>_<PERSON>,_comte_<PERSON>_<PERSON>"}]}, {"year": "1753", "text": "<PERSON><PERSON><PERSON>, French general (d. 1800)", "html": "1753 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ber\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>l%C3%A9ber\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (d. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9ber"}]}, {"year": "1758", "text": "<PERSON>, German neuroanatomist and physiologist (d. 1828)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neuroanatomist and physiologist (d. 1828)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German neuroanatomist and physiologist (d. 1828)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1763", "text": "<PERSON>, English journalist and author (d. 1835)", "html": "1763 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author (d. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, American actor and philanthropist (d. 1872)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and philanthropist (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and philanthropist (d. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON><PERSON>, Ukrainian poet and playwright (d. 1861)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian poet and playwright (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian poet and playwright (d. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, American jurist and politician (d. 1886)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Supreme_Court_justice)\" title=\"<PERSON> (Supreme Court justice)\"><PERSON></a>, American jurist and politician (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Supreme_Court_justice)\" title=\"<PERSON> (Supreme Court justice)\"><PERSON></a>, American jurist and politician (d. 1886)", "links": [{"title": "<PERSON> (Supreme Court justice)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Supreme_Court_justice)"}]}, {"year": "1820", "text": "<PERSON>, American lawyer and jurist (d. 1893)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and jurist (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON><PERSON>, American businessman and politician, founded Stanford University (d. 1893)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Am<PERSON>_<PERSON>_Stanford\" class=\"mw-redirect\" title=\"Amasa Leland Stanford\"><PERSON><PERSON></a>, American businessman and politician, founded <a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford University</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Am<PERSON>_<PERSON>_Stanford\" class=\"mw-redirect\" title=\"Amasa Leland Stanford\"><PERSON><PERSON></a>, American businessman and politician, founded <a href=\"https://wikipedia.org/wiki/Stanford_University\" title=\"Stanford University\">Stanford University</a> (d. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amasa_Le<PERSON>_Stanford"}, {"title": "Stanford University", "link": "https://wikipedia.org/wiki/Stanford_University"}]}, {"year": "1847", "text": "<PERSON>, Belgian violinist, composer, and educator (d. 1924)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and educator (d. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian violinist, composer, and educator (d. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON><PERSON>, English sculptor and academic (d. 1925)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sculptor and academic (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English sculptor and academic (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Sr., American actor and dancer (d. 1928)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>, Sr.</a>, American actor and dancer (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON>, Sr.</a>, American actor and dancer (d. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, American suffragist (d. 1950)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American suffragist (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German geneticist and physician (d. 1976)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geneticist and physician (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geneticist and physician (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Australian footballer and lieutenant (d. 1915)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and lieutenant (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and lieutenant (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian politician and diplomat, Soviet Minister of Foreign Affairs (d. 1986)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Russian politician and diplomat, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)\" title=\"Ministry of Foreign Affairs (Soviet Union)\">Soviet Minister of Foreign Affairs</a> (d. 1986)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs (Soviet Union)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_(Soviet_Union)"}]}, {"year": "1891", "text": "<PERSON>, Filipino lawyer, politician and President of the Philippines (d. 1959)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_the_Philippines\" title=\"President of the Philippines\">President of the Philippines</a> (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Philippines"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian politician (d. 1971)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/M%C3%A1ty%C3%A1s_R%C3%A1kosi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1ty%C3%A1s_R%C3%A1kosi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian politician (d. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1ty%C3%A1s_R%C3%A1kosi"}]}, {"year": "1892", "text": "<PERSON>-West, English author, poet, and gardener (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/Vita_Sackville-West\" title=\"Vita Sackville-West\">Vita Sackville-West</a>, English author, poet, and gardener (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vita_Sackville-West\" title=\"Vita Sackville-West\">Vita Sackville-West</a>, English author, poet, and gardener (d. 1962)", "links": [{"title": "Vita <PERSON>ville-West", "link": "https://wikipedia.org/wiki/Vita_Sackville-West"}]}, {"year": "1902", "text": "<PERSON>, American actor (d. 1978)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American soldier and engineer, founded Klipsch Audio Technologies (d. 2002)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer, founded <a href=\"https://wikipedia.org/wiki/Klipsch_Audio_Technologies\" title=\"Klipsch Audio Technologies\">Klipsch Audio Technologies</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer, founded <a href=\"https://wikipedia.org/wiki/Klipsch_Audio_Technologies\" title=\"Klipsch Audio Technologies\">Klipsch Audio Technologies</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Klipsch Audio Technologies", "link": "https://wikipedia.org/wiki/Klipsch_Audio_Technologies"}]}, {"year": "1910", "text": "<PERSON>, American pianist and composer (d. 1981)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Barber\"><PERSON></a>, American pianist and composer (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American classical violin prodigy and theremin player (d. 1998)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clara Rockmore\"><PERSON></a>, American classical violin prodigy and <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">theremin</a> player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Clara Rockmore\"><PERSON></a>, American classical violin prodigy and <a href=\"https://wikipedia.org/wiki/Theremin\" title=\"Theremin\">theremin</a> player (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clara_<PERSON>more"}, {"title": "Theremin", "link": "https://wikipedia.org/wiki/Theremin"}]}, {"year": "1915", "text": "<PERSON><PERSON>, English air marshal and pilot (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON><PERSON> (RAF officer)\"><PERSON><PERSON></a>, English air marshal and pilot (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)\" title=\"<PERSON><PERSON> (RAF officer)\"><PERSON><PERSON></a>, English air marshal and pilot (d. 2001)", "links": [{"title": "<PERSON><PERSON> (RAF officer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(RAF_officer)"}]}, {"year": "1918", "text": "<PERSON>, American sailor and politician, founded the American Nazi Party (d. 1967)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and politician, founded the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sailor and politician, founded the <a href=\"https://wikipedia.org/wiki/American_Nazi_Party\" title=\"American Nazi Party\">American Nazi Party</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>well"}, {"title": "American Nazi Party", "link": "https://wikipedia.org/wiki/American_Nazi_Party"}]}, {"year": "1918", "text": "<PERSON>, American crime novelist (d. 2006)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American crime novelist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Croatian-Serbian runner and coach (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian runner and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian-Serbian runner and coach (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87"}]}, {"year": "1921", "text": "<PERSON>, American actor (d. 1978)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, New Zealand-Australian former diplomat and university administrator (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian former diplomat and university administrator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand-Australian former diplomat and university administrator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American lawyer, judge, and politician (d. 2023)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, judge, and politician (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, French fashion designer (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Courr%C3%A8ges\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Courr%C3%A8ges\" title=\"<PERSON>\"><PERSON></a>, French fashion designer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Courr%C3%A8ges"}]}, {"year": "1923", "text": "<PERSON>, Austrian-American physicist and academic, Nobel Prize laureate (d. 2016)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1926", "text": "<PERSON>, American radio and television host (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio and television host (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American baseball player (d. 1982)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian-American engineer and academic (d. 1990)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Gerald Bull\"><PERSON></a>, Canadian-American engineer and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American singer and actress (d. 2017)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Guyanese lawyer, politician and President of Guyana (d. 2002)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guyanese lawyer, politician and <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician, 19th President of Bangladesh (d. 2013)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_Bangladesh\" title=\"President of Bangladesh\">President of Bangladesh</a> (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bangladesh", "link": "https://wikipedia.org/wiki/President_of_Bangladesh"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, American saxophonist, violinist, trumpet player, and composer (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist, violinist, trumpet player, and composer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American saxophonist, violinist, trumpet player, and composer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>-<PERSON>, Irish politician (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish politician (d. 2014)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Bangladeshi painter and academic (d. 2014)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>yu<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi painter and academic (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Bangladeshi painter and academic (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>yu<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Puerto Rican astrologer and actor (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican astrologer and actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican astrologer and actor (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American R&B singer-songwriter (d. 2021)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Price"}]}, {"year": "1933", "text": "<PERSON>, English physician, geneticist, and academic (d. 2018)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, geneticist, and academic (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician, geneticist, and academic (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, American jazz drummer and biographer (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Art<PERSON>_Frank\" title=\"Art<PERSON> Frank\"><PERSON><PERSON></a>, American jazz drummer and biographer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art<PERSON>_Frank\" title=\"Artt Frank\"><PERSON><PERSON></a>, American jazz drummer and biographer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Art<PERSON>_Frank"}]}, {"year": "1934", "text": "<PERSON>, Russian colonel, pilot, and cosmonaut, first human in space (d. 1968)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and cosmonaut, first human in space (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian colonel, pilot, and cosmonaut, first human in space (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American engineer and businessman, co-founded Qualcomm Inc.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qualcomm\" title=\"Qualcomm\">Qualcomm Inc.</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Qualcomm\" title=\"Qualcomm\">Qualcomm Inc.</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Qualcomm", "link": "https://wikipedia.org/wiki/Qualcomm"}]}, {"year": "1936", "text": "<PERSON>, American singer-songwriter and pianist (d. 2022)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actor and comedian (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian lawyer, politician and Premier of Quebec (d. 2018)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, politician and <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer, politician and <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1937", "text": "<PERSON>, Canadian ice hockey player, coach, and sportscaster", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, English race car driver", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American businessman, founded Bricklin and Yugo", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Bricklin_SV-1\" title=\"Bricklin SV-1\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Yugo\" title=\"Yugo\">Yugo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Bricklin_SV-1\" title=\"Bricklin SV-1\"><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Yugo\" title=\"Yugo\">Yugo</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Bricklin SV-1", "link": "https://wikipedia.org/wiki/Bricklin_SV-1"}, {"title": "Yugo", "link": "https://wikipedia.org/wiki/Yugo"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Puerto Rican actor (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican actor (d. 1994)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American golfer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American criminal (d. 1976)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American actress", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian-American motorcycle racer (d. 2024)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, Canadian-American motorcycle racer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, Canadian-American motorcycle racer (d. 2024)", "links": [{"title": "<PERSON> (motorcyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_(motorcyclist)"}]}, {"year": "1942", "text": "<PERSON>, Welsh musician, composer, singer, songwriter and record producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh musician, composer, singer, songwriter and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh musician, composer, singer, songwriter and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Romanian actor and artistic director (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian actor and artistic director (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian actor and artistic director (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ion_Caramitru"}]}, {"year": "1942", "text": "<PERSON>, American singer-songwriter, saxophonist, and producer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, saxophonist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, saxophonist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American chess player and author (d. 2008)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and author (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chess player and author (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American journalist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, South African cricketer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter and playwright (d. 1988)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and playwright (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and playwright (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American serial killer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American serial killer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English guitarist and vocalist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Trower\"><PERSON></a>, English guitarist and vocalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Trower\"><PERSON></a>, English guitarist and vocalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rower"}]}, {"year": "1946", "text": "<PERSON>, English actress (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, German footballer and scout (d. 2024)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Bernd_H%C3%B6lzenbein\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and scout (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bernd_H%C3%B6<PERSON><PERSON>bein\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and scout (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bernd_H%C3%B<PERSON><PERSON>zenbein"}]}, {"year": "1946", "text": "<PERSON>, American screenwriter and producer (d. 1990)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, New Zealand author and poet (d. 2021)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author and poet (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author and poet (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1948", "text": "<PERSON>, Italian politician, Italian Minister of Foreign Affairs", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Italy)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)"}]}, {"year": "1948", "text": "<PERSON>, American painter and sculptor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer and drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Welsh lawyer and politician", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Welsh lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Welsh lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1950", "text": "<PERSON>, American baseball player and manager (d. 2004)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American golfer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andy <PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Andy North\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English pianist and conductor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, South African journalist, politician and Premier of the Western Cape", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist, politician and <a href=\"https://wikipedia.org/wiki/Premier_of_the_Western_Cape\" title=\"Premier of the Western Cape\">Premier of the Western Cape</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African journalist, politician and <a href=\"https://wikipedia.org/wiki/Premier_of_the_Western_Cape\" title=\"Premier of the Western Cape\">Premier of the Western Cape</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of the Western Cape", "link": "https://wikipedia.org/wiki/Premier_of_the_Western_Cape"}]}, {"year": "1952", "text": "<PERSON>, English rugby player and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Brazilian-Lebanese-French business executive", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Lebanese-French business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian-Lebanese-French business executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, PIRA volunteer, Irish republican politician, and hunger striker (d. 1981)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA</a> volunteer, Irish republican politician, and hunger striker (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">PIRA</a> volunteer, Irish republican politician, and hunger striker (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Scottish motorcycle racer (d. 1982)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish motorcycle racer (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Scottish motorcycle racer (d. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Italian race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>abi\" title=\"Teo Fabi\"><PERSON><PERSON></a>, Italian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>abi\" title=\"Teo Fabi\"><PERSON><PERSON></a>, Italian race car driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>abi"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Polish academic and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3zef_Pinior"}]}, {"year": "1956", "text": "<PERSON>, American football player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian politician, Indian Minister of External Affairs", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)\" title=\"Minister of External Affairs (India)\">Indian Minister of External Affairs</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Minister of External Affairs (India)", "link": "https://wikipedia.org/wiki/Minister_of_External_Affairs_(India)"}]}, {"year": "1956", "text": "<PERSON>, English academic and politician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1959", "text": "<PERSON>, American actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actor, director, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Lonny <PERSON>\"><PERSON><PERSON></a>, American actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor, director, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Obradovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDeljko_Obradovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%B<PERSON>eljko_Obradovi%C4%87"}]}, {"year": "1961", "text": "<PERSON>, American wrestler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American basketball player and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Polish football player and manager (d. 2024)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish football player and manager (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish football player and manager (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Croatian-Australian rugby league player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian-Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American baseball player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Canadian director and screenwriter (d. 2021)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9e\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter (d. 2021)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%C3%A9e"}]}, {"year": "1964", "text": "<PERSON>, French actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American ice hockey player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player and actor", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Puerto Rican baseball player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Benito_<PERSON>\" title=\"Benito Santiago\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Benito_<PERSON>\" title=\"Benito Santiago\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Benito_Santiago"}]}, {"year": "1966", "text": "<PERSON>, American drummer and songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Australian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, French footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American lawyer and journalist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Indian businessman and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Naveen_<PERSON>\" title=\"Nave<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naveen_<PERSON>\" title=\"Nave<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian businessman and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naveen_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English rugby player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1970", "text": "<PERSON>, American musician and songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American politician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American baseball player and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English race car driver", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1974", "text": "<PERSON>, Australian cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Vincentian-American basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ad<PERSON>_Foyle\" title=\"Ad<PERSON> Foyle\"><PERSON><PERSON></a>, Vincentian-American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ad<PERSON>_<PERSON>oyle\" title=\"Ad<PERSON> Foyle\"><PERSON><PERSON></a>, Vincentian-American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>le"}]}, {"year": "1975", "text": "<PERSON>, Argentine footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Ver%C3%B3n\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, Argentine footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Ver%C3%B3n\" title=\"<PERSON> Verón\"><PERSON></a>, Argentine footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>%C3%A1n_Ver%C3%B3n"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dvo%C5%99%C3%A1k\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Radek_Dvo%C5%99%C3%A1k"}]}, {"year": "1977", "text": "<PERSON>, Australian rugby league player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Canadian ice hockey player and businessman", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Guatemalan-American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Isaac\"><PERSON></a>, Guatemalan-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Isaac\"><PERSON></a>, Guatemalan-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American rapper", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chingy"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American musician, songwriter, and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American baseball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Clay_Rapada\" title=\"Clay Rapada\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Clay_Rapada\" title=\"Clay Rapada\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Clay_Rapada"}]}, {"year": "1982", "text": "<PERSON>, Australian cyclist", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Brazilian basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/%C3%89rika_de_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>-<PERSON><PERSON>, Croatian tennis player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%87-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Croatian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Di%C4%87-<PERSON><PERSON>\" title=\"<PERSON><PERSON>-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, Croatian tennis player", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirjana_Lu%C4%8Di%C4%87-<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American soccer player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Canadian ice hockey player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Venezuelan race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Pastor_<PERSON>\" title=\"Pastor <PERSON>\">Pastor <PERSON></a>, Venezuelan race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pastor_<PERSON>\" title=\"Pastor <PERSON>\">Pastor <PERSON></a>, Venezuelan race car driver", "links": [{"title": "Pastor <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>hi<PERSON>_<PERSON>\" title=\"<PERSON>hi<PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hi<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hiv_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Swiss ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>runner\" title=\"Damien Brunner\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>runner\" title=\"Damien Brunner\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>runner"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actress and producer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Snow\" title=\"<PERSON> Snow\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Snow\" title=\"<PERSON> Snow\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Brittany_Snow"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON> <PERSON>, American rapper and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> <PERSON> (rapper)\"><PERSON> <PERSON></a>, American rapper and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)\" title=\"<PERSON> <PERSON> (rapper)\"><PERSON> <PERSON></a>, American rapper and actor", "links": [{"title": "<PERSON> <PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rapper)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, South Korean singer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Taeyeon\" title=\"Taeyeon\"><PERSON><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taeyeon\" title=\"Taeyeon\"><PERSON><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON>eyeon", "link": "https://wikipedia.org/wiki/Taeyeon"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, American rapper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean singer-songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>ng\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>ng\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo<PERSON><PERSON>ng"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A4ki\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON><PERSON>_Salom%C3%A4ki"}]}, {"year": "1993", "text": "<PERSON><PERSON>, South Korean rapper, songwriter, record producer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper, songwriter, record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, South Korean rapper, songwriter, record producer", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Indonesian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Nade<PERSON>_<PERSON>\" title=\"Na<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nade<PERSON>_<PERSON>\" title=\"Na<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nade<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American rapper", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American football running back", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football running back", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football running back", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Ukko-Pek<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Uk<PERSON>-P<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "Ukko-Pek<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>k<PERSON>-<PERSON><PERSON><PERSON>_<PERSON>en"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Senegalese-Italian social media personality", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese-Italian social media personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese-Italian social media personality", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Spanish basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Usman_Garuba\" title=\"Usman Garuba\"><PERSON><PERSON></a>, Spanish basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Usman_Garuba\" title=\"Usman Garuba\"><PERSON><PERSON></a>, Spanish basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Usman_Garuba"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American gymnast", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "886", "text": "<PERSON>, Muslim scholar and astrologer (b. 787)", "html": "886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON>_al-Balkhi\" title=\"<PERSON> Ma<PERSON>\"><PERSON></a>, Muslim scholar and <a href=\"https://wikipedia.org/wiki/Astrology_in_medieval_Islam\" class=\"mw-redirect\" title=\"Astrology in medieval Islam\">astrologer</a> (b. 787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Muslim scholar and <a href=\"https://wikipedia.org/wiki/Astrology_in_medieval_Islam\" class=\"mw-redirect\" title=\"Astrology in medieval Islam\">astrologer</a> (b. 787)", "links": [{"title": "Abu <PERSON>", "link": "https://wikipedia.org/wiki/Abu_Ma%27sha<PERSON>_<PERSON>-<PERSON>"}, {"title": "Astrology in medieval Islam", "link": "https://wikipedia.org/wiki/Astrology_in_medieval_Islam"}]}, {"year": "1202", "text": "<PERSON><PERSON><PERSON> of Norway, King of Norway and founder of the House of Sverre", "html": "1202 - <a href=\"https://wikipedia.org/wiki/Sverre_of_Norway\" title=\"Sverre of Norway\">Sverre of Norway</a>, <a href=\"https://wikipedia.org/wiki/Monarchy_of_Norway\" title=\"Monarchy of Norway\">King of Norway</a> and founder of the <a href=\"https://wikipedia.org/wiki/House_of_Sverre\" title=\"House of Sverre\">House of Sverre</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sverre_of_Norway\" title=\"Sverre of Norway\">Sverre of Norway</a>, <a href=\"https://wikipedia.org/wiki/Monarchy_of_Norway\" title=\"Monarchy of Norway\">King of Norway</a> and founder of the <a href=\"https://wikipedia.org/wiki/House_of_Sverre\" title=\"House of Sverre\">House of Sverre</a>", "links": [{"title": "Sverre of Norway", "link": "https://wikipedia.org/wiki/Sverre_of_Norway"}, {"title": "Monarchy of Norway", "link": "https://wikipedia.org/wiki/Monarchy_of_Norway"}, {"title": "House of Sverre", "link": "https://wikipedia.org/wiki/House_of_Sverre"}]}, {"year": "1440", "text": "<PERSON> of Rome, Italian nun and saint (b. 1384)", "html": "1440 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rome\" title=\"<PERSON> of Rome\"><PERSON> of Rome</a>, Italian nun and saint (b. 1384)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rome\" title=\"<PERSON> of Rome\"><PERSON> of Rome</a>, Italian nun and saint (b. 1384)", "links": [{"title": "Frances of Rome", "link": "https://wikipedia.org/wiki/Frances_of_Rome"}]}, {"year": "1444", "text": "<PERSON>, Italian humanist (b. c. 1370)", "html": "1444 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian humanist (b. c. 1370)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian humanist (b. c. 1370)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1463", "text": "<PERSON> of Bologna, Italian nun and saint (b. 1463)", "html": "1463 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna\" title=\"<PERSON> of Bologna\"><PERSON> of Bologna</a>, Italian nun and saint (b. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna\" title=\"<PERSON> of Bologna\"><PERSON> of Bologna</a>, Italian nun and saint (b. 1463)", "links": [{"title": "Catherine of Bologna", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bologna"}]}, {"year": "1566", "text": "<PERSON>, Italian-Scottish courtier and politician (b. 1533)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish courtier and politician (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish courtier and politician (b. 1533)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, 1st Duke of Hamilton, Scottish soldier and politician (b. 1606)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Hamilton\" title=\"<PERSON>, 1st Duke of Hamilton\"><PERSON>, 1st Duke of Hamilton</a>, Scottish soldier and politician (b. 1606)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_Hamilton\" title=\"<PERSON>, 1st Duke of Hamilton\"><PERSON>, 1st Duke of Hamilton</a>, Scottish soldier and politician (b. 1606)", "links": [{"title": "<PERSON>, 1st Duke of Hamilton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON>, 1st Earl of Holland, English soldier and politician (b. 1590)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Holland\" title=\"<PERSON>, 1st Earl of Holland\"><PERSON>, 1st Earl of Holland</a>, English soldier and politician (b. 1590)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Holland\" title=\"<PERSON>, 1st Earl of Holland\"><PERSON>, 1st Earl of Holland</a>, English soldier and politician (b. 1590)", "links": [{"title": "<PERSON>, 1st Earl of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Holland"}]}, {"year": "1661", "text": "<PERSON>, Italian-French academic and politician, Prime Minister of France (b. 1602)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Cardinal <PERSON>\">Cardinal <PERSON></a>, Italian-French academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\">Cardinal <PERSON></a>, Italian-French academic and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1602)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1709", "text": "<PERSON>, 1st Duke of Montagu, English courtier and politician (b. 1638)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Montagu\" title=\"<PERSON>, 1st Duke of Montagu\"><PERSON>, 1st Duke of Montagu</a>, English courtier and politician (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Montagu\" title=\"<PERSON>, 1st Duke of Montagu\"><PERSON>, 1st Duke of Montagu</a>, English courtier and politician (b. 1638)", "links": [{"title": "<PERSON>, 1st Duke of Montagu", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON> the Elder, Italian architect (b. 1739)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Italian architect (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder\" title=\"<PERSON> the Elder\"><PERSON> the Elder</a>, Italian architect (b. 1739)", "links": [{"title": "<PERSON> the Elder", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Elder"}]}, {"year": "1810", "text": "<PERSON><PERSON>, English painter and academic (b. 1742)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y\" title=\"<PERSON><PERSON> Humphry\"><PERSON><PERSON></a>, English painter and academic (b. 1742)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y\" title=\"<PERSON><PERSON> Humphry\"><PERSON><PERSON></a>, English painter and academic (b. 1742)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1825", "text": "<PERSON>, English poet, author, and critic (b. 1743)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and critic (b. 1743)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and critic (b. 1743)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>uld"}]}, {"year": "1831", "text": "<PERSON>, German author and playwright (b. 1752)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (b. 1752)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON>, English paleontologist (b. 1799)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Paleontology\" title=\"Paleontology\">paleontologist</a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/Paleontology\" title=\"Paleontology\">paleontologist</a> (b. 1799)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Paleontology", "link": "https://wikipedia.org/wiki/Paleontology"}]}, {"year": "1851", "text": "<PERSON>, Danish physicist and chemist, discovered electromagnetism and the element aluminium (b. 1777)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%98rsted\" title=\"<PERSON>\"><PERSON></a>, Danish physicist and chemist, discovered <a href=\"https://wikipedia.org/wiki/Electromagnetism\" title=\"Electromagnetism\">electromagnetism</a> and the element <a href=\"https://wikipedia.org/wiki/Aluminium\" title=\"Aluminium\">aluminium</a> (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%C3%98rsted\" title=\"<PERSON>\"><PERSON></a>, Danish physicist and chemist, discovered <a href=\"https://wikipedia.org/wiki/Electromagnetism\" title=\"Electromagnetism\">electromagnetism</a> and the element <a href=\"https://wikipedia.org/wiki/Aluminium\" title=\"Aluminium\">aluminium</a> (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Christian_%C3%98rsted"}, {"title": "Electromagnetism", "link": "https://wikipedia.org/wiki/Electromagnetism"}, {"title": "Aluminium", "link": "https://wikipedia.org/wiki/Aluminium"}]}, {"year": "1876", "text": "<PERSON>, French poet (b. 1810)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French poet (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, German Emperor (b. 1797)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"<PERSON>, German Emperor\"><PERSON>, German Emperor</a> (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor\" title=\"<PERSON>, German Emperor\"><PERSON>, German Emperor</a> (b. 1797)", "links": [{"title": "<PERSON>, German Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_German_Emperor"}]}, {"year": "1895", "text": "<PERSON>, Austrian journalist and author (b. 1836)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and author (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian journalist and author (b. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Norwegian-American skier (b. 1825)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Sondre_<PERSON>heim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American skier (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sondre_<PERSON>heim\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian-American skier (b. 1825)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sondre_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, German author and playwright (b. 1864)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and playwright (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American painter and academic (b. 1858)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Japanese spiritual leader, founded <PERSON><PERSON> (b. 1865)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese spiritual leader, founded <a href=\"https://wikipedia.org/wiki/Reiki\" title=\"Reiki\">Re<PERSON></a> (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese spiritual leader, founded <a href=\"https://wikipedia.org/wiki/Reiki\" title=\"Reiki\">Reiki</a> (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ui"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Reiki"}]}, {"year": "1937", "text": "<PERSON>, American journalist and critic (b. 1864)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and critic (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German painter and sculptor (b. 1878)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and sculptor (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish oceanographer and academic (b. 1874)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vag<PERSON> <PERSON><PERSON><PERSON><PERSON>\">V<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Swedish oceanographer and academic (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"Vag<PERSON> <PERSON><PERSON><PERSON><PERSON>\">Vag<PERSON> <PERSON><PERSON><PERSON><PERSON></a>, Swedish oceanographer and academic (b. 1874)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vagn_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON> (Miroslava), Czech-Mexican actress (b. 1925)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Mir<PERSON><PERSON>_Stern\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (Miroslava)</a>, Czech-Mexican actress (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mir<PERSON><PERSON>_Stern\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> (Miroslava)</a>, Czech-Mexican actress (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Miroslava_Stern"}]}, {"year": "1964", "text": "<PERSON>, German general (b. 1870)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Egyptian general (b. 1919)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian general (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian general (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Riad"}]}, {"year": "1971", "text": "<PERSON> of Alexandria, Coptic Orthodox Pope (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Alexandria\" title=\"Pope <PERSON> VI of Alexandria\">Pope <PERSON> of Alexandria</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Coptic_Orthodox_Church\" title=\"Pope of the Coptic Orthodox Church\">Coptic Orthodox Pope</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Alexandria\" title=\"Pope <PERSON> VI of Alexandria\">Pope <PERSON> of Alexandria</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_of_the_Coptic_Orthodox_Church\" title=\"Pope of the Coptic Orthodox Church\">Coptic Orthodox Pope</a> (b. 1902)", "links": [{"title": "<PERSON> <PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Alexandria"}, {"title": "<PERSON> of the Coptic Orthodox Church", "link": "https://wikipedia.org/wiki/<PERSON>_of_the_Coptic_Orthodox_Church"}]}, {"year": "1974", "text": "<PERSON>, Jr., American pharmacologist and biochemist, Nobel Prize laureate (b. 1915)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON><PERSON> Jr.\"><PERSON>, Jr.</a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>, Jr.</a>, American pharmacologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1974", "text": "<PERSON>, American singer (b. 1945)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress (b. 1917)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Swedish physiologist and pharmacologist, Nobel Prize laureate (b. 1905)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish physiologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Swedish physiologist and pharmacologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1988", "text": "<PERSON>, German lawyer, politician and Chancellor of Germany (b. 1904)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, politician and <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer, politician and <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1989", "text": "<PERSON>, American photographer (b. 1946)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player (b. 1943)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Belarusian-Israeli soldier, politician and Prime Minister of Israel, Nobel Prize laureate (b. 1913)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin\" title=\"<PERSON><PERSON><PERSON> Begin\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli soldier, politician and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli soldier, politician and <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1993", "text": "<PERSON><PERSON>, English historian and author (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._Northcote_Parkinson"}]}, {"year": "1994", "text": "<PERSON>, American poet, novelist, and short story writer (b. 1920)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet, novelist, and short story writer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Canadian wrestler, referee, and manager (b. 1928)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler, referee, and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler, referee, and manager (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Spanish actor (b. 1917)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Fernando <PERSON>\"><PERSON></a>, Spanish actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Austrian-American propagandist (b. 1891)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American propagandist (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American propagandist (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American comedian, actor, and writer (b. 1896)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and writer (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, French journalist and author (b. 1952)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and author (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and author (b. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Welsh author and screenwriter (b. 1930)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Terry_<PERSON>\" title=\"Terry Nation\"><PERSON></a>, Welsh author and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Terry_Nation\" title=\"Terry Nation\"><PERSON></a>, Welsh author and screenwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Terry_Nation"}]}, {"year": "1997", "text": "The Notorious B.I.G., American rapper, songwriter, and actor (b. 1972)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/The_Notorious_B.I.G.\" title=\"The Notorious B.I.G.\">The Notorious B.I.G.</a>, American rapper, songwriter, and actor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Notorious_B.I.G.\" title=\"The Notorious B.I.G.\">The Notorious B.I.G.</a>, American rapper, songwriter, and actor (b. 1972)", "links": [{"title": "The Notorious B.I.G.", "link": "https://wikipedia.org/wiki/The_Notorious_B.I.G."}]}, {"year": "1999", "text": "<PERSON>, Canadian pianist and composer (b. 1925)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian pianist and composer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Belizean jurist and Chief Justice of Belize (b. 1937)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean jurist and <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Belize\" title=\"Chief Justice of Belize\">Chief Justice of Belize</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belizean jurist and <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Belize\" title=\"Chief Justice of Belize\">Chief Justice of Belize</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Belize", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Belize"}]}, {"year": "2000", "text": "<PERSON>, Canadian composer and educator (b. 1908)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian composer and educator (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American director and cinematographer (b. 1933)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and cinematographer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Nauruan politician, President of Nauru (b. 1946)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nauruan politician, <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bernard_<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "2004", "text": "<PERSON>, Indian composer (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Indian composer (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, Indian composer (b. 1930)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "2006", "text": "<PERSON>, American activist (b. 1951)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)\" title=\"<PERSON> (Quaker)\"><PERSON></a>, American activist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)\" title=\"<PERSON> (Quaker)\"><PERSON></a>, American activist (b. 1951)", "links": [{"title": "<PERSON> (Quaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Quaker)"}]}, {"year": "2006", "text": "<PERSON>, American soprano (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, English soldier and politician, Secretary of State for War (b. 1915)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_War\" title=\"Secretary of State for War\">Secretary of State for War</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for War", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_War"}]}, {"year": "2007", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1951)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Canadian ice hockey player (b. 1921)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American baseball player and manager (b. 1940)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and manager (b. 1940)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2010", "text": "<PERSON>, American activist and politician (b. 1910)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American activist and politician (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON><PERSON>, Indian singer (b. 1942)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/Wilfy_Rebimbus\" title=\"Wilfy Rebimbus\">Wil<PERSON></a>, Indian singer (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilfy_Rebimbus\" title=\"Wilfy Rebimbus\">Wil<PERSON></a>, Indian singer (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wilfy_Rebimbus"}]}, {"year": "2010", "text": "<PERSON>, American wrestler (b. 1918)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American journalist and academic (b. 1929)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Finnish journalist and diplomat (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish journalist and diplomat (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish journalist and diplomat (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American painter and art collector (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and art collector (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and art collector (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Baron <PERSON> of Killead, Northern Irish soldier and politician (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead\" title=\"<PERSON>, Baron <PERSON> of Killead\"><PERSON>, Baron <PERSON> of Killead</a>, Northern Irish soldier and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead\" title=\"<PERSON>, Baron <PERSON> of Killead\"><PERSON>, Baron <PERSON> of Killead</a>, Northern Irish soldier and politician (b. 1920)", "links": [{"title": "<PERSON>, Baron <PERSON> of Killead", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1924)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1924)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2016", "text": "<PERSON>, American basketball player and coach (b. 1929)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, British painter (b. 1932)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British painter (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Korean actor (b. 1965)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>i\" title=\"<PERSON>\"><PERSON></a>, Korean actor (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Korean actor (b. 1965)", "links": [{"title": "<PERSON>i", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ki"}]}, {"year": "2020", "text": "<PERSON>, Australian Catholic bishop (b. 1936)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Catholic bishop (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian Catholic bishop (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American conductor and pianist (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and pianist (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conductor and pianist (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American journalist (b. 1928)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON><PERSON>, Israeli actor (b. 1935)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli actor (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}