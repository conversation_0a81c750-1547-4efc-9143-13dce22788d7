{"date": "August 10", "url": "https://wikipedia.org/wiki/August_10", "data": {"Events": [{"year": "654", "text": "<PERSON> elected to succeed <PERSON><PERSON>.", "html": "654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> I</a> elected to succeed <PERSON><PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> I</a> elected to succeed <PERSON><PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "955", "text": "Battle of Lechfeld: <PERSON>, Holy Roman Emperor defeats the Magyars, ending 50 years of Magyar invasion of the West.", "html": "955 - <a href=\"https://wikipedia.org/wiki/Battle_of_Lechfeld_(955)\" class=\"mw-redirect\" title=\"Battle of Lechfeld (955)\">Battle of Lechfeld</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> defeats the <a href=\"https://wikipedia.org/wiki/Magyars\" class=\"mw-redirect\" title=\"Magyars\">Magyars</a>, ending 50 years of Magyar invasion of the West.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Lechfeld_(955)\" class=\"mw-redirect\" title=\"Battle of Lechfeld (955)\">Battle of Lechfeld</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" class=\"mw-redirect\" title=\"<PERSON> I, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> defeats the <a href=\"https://wikipedia.org/wiki/Magyars\" class=\"mw-redirect\" title=\"Magyars\">Magyars</a>, ending 50 years of Magyar invasion of the West.", "links": [{"title": "Battle of Lechfeld (955)", "link": "https://wikipedia.org/wiki/Battle_of_Lechfeld_(955)"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>gyars"}]}, {"year": "991", "text": "Battle of Maldon: The English, led by <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> of Essex, are defeated by a band of inland-raiding Vikings near Maldon, Essex.", "html": "991 - <a href=\"https://wikipedia.org/wiki/Battle_of_Maldon\" title=\"Battle of Maldon\">Battle of Maldon</a>: The English, led by <a href=\"https://wikipedia.org/wiki/Byrhtnoth\" title=\"Byrhtnoth\">Byrhtnoth</a>, <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"Ealdorman\">Ealdorman</a> of <a href=\"https://wikipedia.org/wiki/Essex\" title=\"Essex\">Essex</a>, are defeated by a band of inland-raiding <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a> near <a href=\"https://wikipedia.org/wiki/Maldon,_Essex\" class=\"mw-redirect\" title=\"Maldon, Essex\">Maldon, Essex</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Maldon\" title=\"Battle of Maldon\">Battle of Maldon</a>: The English, led by <a href=\"https://wikipedia.org/wiki/Byrhtnoth\" title=\"Byrhtnoth\">Byrhtnoth</a>, <a href=\"https://wikipedia.org/wiki/Ealdorman\" title=\"Ealdorman\"><PERSON>aldo<PERSON></a> of <a href=\"https://wikipedia.org/wiki/Essex\" title=\"Essex\">Essex</a>, are defeated by a band of inland-raiding <a href=\"https://wikipedia.org/wiki/Vikings\" title=\"Vikings\">Vikings</a> near <a href=\"https://wikipedia.org/wiki/Maldon,_Essex\" class=\"mw-redirect\" title=\"Maldon, Essex\">Maldon, Essex</a>.", "links": [{"title": "Battle of Maldon", "link": "https://wikipedia.org/wiki/Battle_of_Maldon"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Byrhtnoth"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Essex", "link": "https://wikipedia.org/wiki/Essex"}, {"title": "Vikings", "link": "https://wikipedia.org/wiki/Vikings"}, {"title": "Maldon, Essex", "link": "https://wikipedia.org/wiki/Maldon,_Essex"}]}, {"year": "1030", "text": "The Battle of Azaz ends with a humiliating retreat of the Byzantine emperor, <PERSON><PERSON>, against the Mirdasid rulers of Aleppo. The retreat degenerates into a rout, in which <PERSON><PERSON> himself barely escapes capture.", "html": "1030 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Azaz_(1030)\" title=\"Battle of Azaz (1030)\">Battle of Azaz</a> ends with a humiliating retreat of the Byzantine emperor, <a href=\"https://wikipedia.org/wiki/<PERSON>s_III_Argyros\" title=\"Romanos III Argyros\"><PERSON><PERSON> III <PERSON>rgyros</a>, against the <a href=\"https://wikipedia.org/wiki/Mirdasid\" class=\"mw-redirect\" title=\"Mirdasid\">Mir<PERSON><PERSON></a> rulers of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>. The retreat degenerates into a rout, in which <PERSON><PERSON> himself barely escapes capture.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Azaz_(1030)\" title=\"Battle of Azaz (1030)\">Battle of Azaz</a> ends with a humiliating retreat of the Byzantine emperor, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_III_<PERSON>rgy<PERSON>\" title=\"Romanos III Argyros\"><PERSON><PERSON></a>, against the <a href=\"https://wikipedia.org/wiki/Mirdasid\" class=\"mw-redirect\" title=\"Mirdasid\">Mirdasid</a> rulers of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a>. The retreat degenerates into a rout, in which <PERSON><PERSON> himself barely escapes capture.", "links": [{"title": "Battle of Azaz (1030)", "link": "https://wikipedia.org/wiki/Battle_of_Azaz_(1030)"}, {"title": "Romanos III Argyros", "link": "https://wikipedia.org/wiki/Romanos_III_Argyros"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mirdasid"}, {"title": "Aleppo", "link": "https://wikipedia.org/wiki/Aleppo"}]}, {"year": "1270", "text": "<PERSON><PERSON><PERSON> takes the imperial throne of Ethiopia, restoring the Solomonic dynasty to power after a 100-year Zagwe interregnum.", "html": "1270 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> takes the <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">imperial throne</a> of <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopia</a>, restoring the <a href=\"https://wikipedia.org/wiki/Solomonic_dynasty\" title=\"Solomonic dynasty\">Solomonic dynasty</a> to power after a 100-year <a href=\"https://wikipedia.org/wiki/Zagwe_dynasty\" title=\"Zagwe dynasty\">Zagwe</a> <a href=\"https://wikipedia.org/wiki/Interregnum\" title=\"Interregnum\">interregnum</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> takes the <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">imperial throne</a> of <a href=\"https://wikipedia.org/wiki/Ethiopian_Empire\" title=\"Ethiopian Empire\">Ethiopia</a>, restoring the <a href=\"https://wikipedia.org/wiki/Solomonic_dynasty\" title=\"Solomonic dynasty\">Solomonic dynasty</a> to power after a 100-year <a href=\"https://wikipedia.org/wiki/Zagwe_dynasty\" title=\"Zagwe dynasty\">Zagwe</a> <a href=\"https://wikipedia.org/wiki/Interregnum\" title=\"Interregnum\">interregnum</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}, {"title": "Ethiopian Empire", "link": "https://wikipedia.org/wiki/Ethiopian_Empire"}, {"title": "Solomonic dynasty", "link": "https://wikipedia.org/wiki/Solomonic_dynasty"}, {"title": "Zagwe dynasty", "link": "https://wikipedia.org/wiki/Zagwe_dynasty"}, {"title": "Interregnum", "link": "https://wikipedia.org/wiki/Interregnum"}]}, {"year": "1316", "text": "The Second Battle of Athenry takes place near Athenry during the Bruce campaign in Ireland.", "html": "1316 - The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Athenry\" title=\"Second Battle of Athenry\">Second Battle of Athenry</a> takes place near <a href=\"https://wikipedia.org/wiki/Athenry\" title=\"Athenry\">Athenry</a> during the <a href=\"https://wikipedia.org/wiki/Bruce_campaign_in_Ireland\" title=\"Bruce campaign in Ireland\">Bruce campaign in Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Athenry\" title=\"Second Battle of Athenry\">Second Battle of Athenry</a> takes place near <a href=\"https://wikipedia.org/wiki/Athenry\" title=\"Athenry\">Athenry</a> during the <a href=\"https://wikipedia.org/wiki/Bruce_campaign_in_Ireland\" title=\"Bruce campaign in Ireland\">Bruce campaign in Ireland</a>.", "links": [{"title": "Second Battle of Athenry", "link": "https://wikipedia.org/wiki/Second_Battle_of_Athenry"}, {"title": "Athenry", "link": "https://wikipedia.org/wiki/Athenry"}, {"title": "<PERSON> campaign in Ireland", "link": "https://wikipedia.org/wiki/<PERSON>_campaign_in_Ireland"}]}, {"year": "1346", "text": "<PERSON><PERSON><PERSON> sets out from Majorca for the \"River of Gold\", the Senegal River.", "html": "1346 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sets out from <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a> for the \"River of Gold\", the <a href=\"https://wikipedia.org/wiki/Senegal_River\" title=\"Senegal River\">Senegal River</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sets out from <a href=\"https://wikipedia.org/wiki/Majorca\" class=\"mw-redirect\" title=\"Majorca\">Majorca</a> for the \"River of Gold\", the <a href=\"https://wikipedia.org/wiki/Senegal_River\" title=\"Senegal River\">Senegal River</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Majorca", "link": "https://wikipedia.org/wiki/Majorca"}, {"title": "Senegal River", "link": "https://wikipedia.org/wiki/Senegal_River"}]}, {"year": "1512", "text": "The naval Battle of Saint-Mathieu, during the War of the League of Cambrai, sees the simultaneous destruction of the Breton ship La Cordelière and the English ship The Regent.", "html": "1512 - The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Saint-Mathieu\" title=\"Battle of Saint-Mathieu\">Battle of Saint-Mathieu</a>, during the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">War of the League of Cambrai</a>, sees the simultaneous destruction of the Breton ship <i>La Cordelière</i> and the English ship <i>The Regent</i>.", "no_year_html": "The naval <a href=\"https://wikipedia.org/wiki/Battle_of_Saint-Mathieu\" title=\"Battle of Saint-Mathieu\">Battle of Saint-Mathieu</a>, during the <a href=\"https://wikipedia.org/wiki/War_of_the_League_of_Cambrai\" title=\"War of the League of Cambrai\">War of the League of Cambrai</a>, sees the simultaneous destruction of the Breton ship <i>La Cordelière</i> and the English ship <i>The Regent</i>.", "links": [{"title": "Battle of Saint-Mathieu", "link": "https://wikipedia.org/wiki/Battle_of_Saint-<PERSON>ieu"}, {"title": "War of the League of Cambrai", "link": "https://wikipedia.org/wiki/War_of_the_League_of_Cambrai"}]}, {"year": "1519", "text": "<PERSON>'s five ships set sail from Seville to circumnavigate the globe. The Basque second-in-command <PERSON> will complete the expedition after <PERSON><PERSON><PERSON>'s death in the Philippines.", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s five ships set sail from <a href=\"https://wikipedia.org/wiki/Seville\" title=\"Seville\">Seville</a> to circumnavigate the globe. The <a href=\"https://wikipedia.org/wiki/Basques\" title=\"Basques\">Basque</a> second-in-command <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Elcano\" title=\"Juan Sebasti<PERSON> El<PERSON>\"><PERSON></a> will complete the expedition after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Death_in_the_Philippines\" title=\"<PERSON>\"><PERSON><PERSON>'s death</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s five ships set sail from <a href=\"https://wikipedia.org/wiki/Seville\" title=\"Seville\">Seville</a> to circumnavigate the globe. The <a href=\"https://wikipedia.org/wiki/Basques\" title=\"Basques\">Basque</a> second-in-command <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Elcano\" title=\"Juan Se<PERSON> El<PERSON>\"><PERSON></a> will complete the expedition after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>#Death_in_the_Philippines\" title=\"<PERSON>\"><PERSON>'s death</a> in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Seville", "link": "https://wikipedia.org/wiki/Seville"}, {"title": "Basques", "link": "https://wikipedia.org/wiki/Basques"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_Elcano"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>#Death_in_the_Philippines"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1557", "text": "Battle of St. Quentin: Spanish victory over the French in the Italian War of 1551-59.", "html": "1557 - <a href=\"https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)\" title=\"Battle of St. Quentin (1557)\">Battle of St. Quentin</a>: Spanish victory over the French in the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1551%E2%80%9359\" class=\"mw-redirect\" title=\"Italian War of 1551-59\">Italian War of 1551-59</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)\" title=\"Battle of St. Quentin (1557)\">Battle of St. Quentin</a>: Spanish victory over the French in the <a href=\"https://wikipedia.org/wiki/Italian_War_of_1551%E2%80%9359\" class=\"mw-redirect\" title=\"Italian War of 1551-59\">Italian War of 1551-59</a>.", "links": [{"title": "Battle of St. Quentin (1557)", "link": "https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)"}, {"title": "Italian War of 1551-59", "link": "https://wikipedia.org/wiki/Italian_War_of_1551%E2%80%9359"}]}, {"year": "1585", "text": "The Treaty of Nonsuch signed by <PERSON> of England and the Dutch Rebels.", "html": "1585 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nonsuch\" title=\"Treaty of Nonsuch\">Treaty of Nonsuch</a> signed by <a href=\"https://wikipedia.org/wiki/<PERSON>_I\" title=\"<PERSON> I\"><PERSON> I</a> of England and the <a href=\"https://wikipedia.org/wiki/Dutch_Revolt\" class=\"mw-redirect\" title=\"Dutch Revolt\">Dutch Rebels</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nonsuch\" title=\"Treaty of Nonsuch\">Treaty of Nonsuch</a> signed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth I\"><PERSON> I</a> of England and the <a href=\"https://wikipedia.org/wiki/Dutch_Revolt\" class=\"mw-redirect\" title=\"Dutch Revolt\">Dutch Rebels</a>.", "links": [{"title": "Treaty of Nonsuch", "link": "https://wikipedia.org/wiki/Treaty_of_Nonsuch"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dutch Revolt", "link": "https://wikipedia.org/wiki/Dutch_Revolt"}]}, {"year": "1628", "text": "The Swedish warship Vasa sinks on her maiden voyage off Stockholm.", "html": "1628 - The Swedish warship <i><a href=\"https://wikipedia.org/wiki/Vasa_(ship)\" title=\"Vasa (ship)\">Vasa</a></i> sinks on her maiden voyage off <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>.", "no_year_html": "The Swedish warship <i><a href=\"https://wikipedia.org/wiki/Vasa_(ship)\" title=\"Vasa (ship)\">Vasa</a></i> sinks on her maiden voyage off <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a>.", "links": [{"title": "Vasa (ship)", "link": "https://wikipedia.org/wiki/Vasa_(ship)"}, {"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}]}, {"year": "1641", "text": "The Treaty of London between England and Scotland, ending the Bishops' Wars, is signed.", "html": "1641 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1641)\" title=\"Treaty of London (1641)\">Treaty of London</a> between England and Scotland, ending the <a href=\"https://wikipedia.org/wiki/Bishops%27_Wars\" title=\"Bishops' Wars\">Bishops' Wars</a>, is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_London_(1641)\" title=\"Treaty of London (1641)\">Treaty of London</a> between England and Scotland, ending the <a href=\"https://wikipedia.org/wiki/Bishops%27_Wars\" title=\"Bishops' Wars\">Bishops' Wars</a>, is signed.", "links": [{"title": "Treaty of London (1641)", "link": "https://wikipedia.org/wiki/Treaty_of_London_(1641)"}, {"title": "Bishops' Wars", "link": "https://wikipedia.org/wiki/Bishops%27_Wars"}]}, {"year": "1680", "text": "The Pueblo Revolt begins in New Mexico.", "html": "1680 - The <a href=\"https://wikipedia.org/wiki/Pueblo_Revolt\" title=\"Pueblo Revolt\">Pueblo Revolt</a> begins in <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Pueblo_Revolt\" title=\"Pueblo Revolt\">Pueblo Revolt</a> begins in <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>.", "links": [{"title": "Pueblo Revolt", "link": "https://wikipedia.org/wiki/Pueblo_Revolt"}, {"title": "New Mexico", "link": "https://wikipedia.org/wiki/New_Mexico"}]}, {"year": "1741", "text": "King <PERSON><PERSON> of Travancore defeats the Dutch East India Company at the Battle of Colachel, effectively bringing about the end of the Dutch colonial rule in India.", "html": "1741 - King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Varma\" title=\"Marthanda Varma\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Travancore\" title=\"Travancore\">Travancore</a> defeats the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Colachel\" title=\"Battle of Colachel\">Battle of Colachel</a>, effectively bringing about the end of the Dutch colonial rule in India.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Varma\" title=\"Martha<PERSON> Varma\"><PERSON><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Travancore\" title=\"Travancore\">Travancore</a> defeats the <a href=\"https://wikipedia.org/wiki/Dutch_East_India_Company\" title=\"Dutch East India Company\">Dutch East India Company</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Colachel\" title=\"Battle of Colachel\">Battle of Colachel</a>, effectively bringing about the end of the Dutch colonial rule in India.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Martha<PERSON>_Varma"}, {"title": "Travancore", "link": "https://wikipedia.org/wiki/Travancore"}, {"title": "Dutch East India Company", "link": "https://wikipedia.org/wiki/Dutch_East_India_Company"}, {"title": "Battle of Colachel", "link": "https://wikipedia.org/wiki/Battle_of_Colachel"}]}, {"year": "1755", "text": "Under the direction of <PERSON>, the British begin to forcibly deport the Acadians from Nova Scotia to the Thirteen Colonies and France.", "html": "1755 - Under the direction of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, the British begin to <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">forcibly deport</a> the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a> from <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> to the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "no_year_html": "Under the direction of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, the British begin to <a href=\"https://wikipedia.org/wiki/Expulsion_of_the_Acadians\" title=\"Expulsion of the Acadians\">forcibly deport</a> the <a href=\"https://wikipedia.org/wiki/Acadians\" title=\"Acadians\">Acadians</a> from <a href=\"https://wikipedia.org/wiki/Nova_Scotia\" title=\"Nova Scotia\">Nova Scotia</a> to the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">France</a>.", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}, {"title": "Expulsion of the Acadians", "link": "https://wikipedia.org/wiki/Expulsion_of_the_Acadians"}, {"title": "Acadians", "link": "https://wikipedia.org/wiki/Acadians"}, {"title": "Nova Scotia", "link": "https://wikipedia.org/wiki/Nova_Scotia"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1792", "text": "French Revolution: Storming of the Tuileries Palace: <PERSON> is arrested and taken into custody as his Swiss Guards are massacred by the Parisian mob.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/10_August_(French_Revolution)\" class=\"mw-redirect\" title=\"10 August (French Revolution)\">Storming of the Tuileries Palace</a>: <a href=\"https://wikipedia.org/wiki/Louis_XVI\" title=\"Louis XVI\"><PERSON></a> is arrested and taken into custody as his <a href=\"https://wikipedia.org/wiki/Swiss_Guard\" title=\"Swiss Guard\">Swiss Guards</a> are massacred by the Parisian mob.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/10_August_(French_Revolution)\" class=\"mw-redirect\" title=\"10 August (French Revolution)\">Storming of the Tuileries Palace</a>: <a href=\"https://wikipedia.org/wiki/Louis_XVI\" title=\"Louis XVI\"><PERSON></a> is arrested and taken into custody as his <a href=\"https://wikipedia.org/wiki/Swiss_Guard\" title=\"Swiss Guard\">Swiss Guards</a> are massacred by the Parisian mob.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "10 August (French Revolution)", "link": "https://wikipedia.org/wiki/10_August_(French_Revolution)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Swiss Guard", "link": "https://wikipedia.org/wiki/Swiss_Guard"}]}, {"year": "1808", "text": "Finnish War: Swedish forces led by General <PERSON> defeat Russian forces led by General <PERSON><PERSON><PERSON><PERSON> in the Battle of Kauhajoki.", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Swedish forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\">von <PERSON></a> defeat Russian forces led by General <PERSON><PERSON><PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kauhajoki\" title=\"Battle of Kauhajoki\">Battle of Kauhajoki</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Finnish_War\" title=\"Finnish War\">Finnish War</a>: Swedish forces led by General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6beln\" title=\"<PERSON>\">von <PERSON></a> defeat Russian forces led by General <PERSON><PERSON><PERSON> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Kauhajoki\" title=\"Battle of Kauhajoki\">Battle of Kauhajoki</a>.", "links": [{"title": "Finnish War", "link": "https://wikipedia.org/wiki/Finnish_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%B6beln"}, {"title": "Battle of Kauhajoki", "link": "https://wikipedia.org/wiki/Battle_of_Ka<PERSON><PERSON>oki"}]}, {"year": "1835", "text": "<PERSON><PERSON> <PERSON><PERSON> begins his career as a showman and circus entrepreneur by exhibiting <PERSON><PERSON>, an octogenerian African slave whom he claims was <PERSON>'s nursemaid.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_Bar<PERSON>\" title=\"P. T. Barnum\"><PERSON><PERSON> <PERSON><PERSON></a> begins his career as a showman and circus entrepreneur by exhibiting <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an octogenerian African slave whom he claims was <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Nursemaid\" title=\"Nursemaid\">nursemaid</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>._<PERSON>._Barnum\" title=\"P. T. Barnum\"><PERSON><PERSON> <PERSON><PERSON></a> begins his career as a showman and circus entrepreneur by exhibiting <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, an octogenerian African slave whom he claims was <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Nursemaid\" title=\"Nursemaid\">nursemaid</a>.", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>th"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>maid"}]}, {"year": "1856", "text": "The Last Island hurricane strikes Louisiana, resulting in over 200 deaths.", "html": "1856 - The <a href=\"https://wikipedia.org/wiki/1856_Last_Island_hurricane\" title=\"1856 Last Island hurricane\">Last Island hurricane</a> strikes <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, resulting in over 200 deaths.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1856_Last_Island_hurricane\" title=\"1856 Last Island hurricane\">Last Island hurricane</a> strikes <a href=\"https://wikipedia.org/wiki/Louisiana\" title=\"Louisiana\">Louisiana</a>, resulting in over 200 deaths.", "links": [{"title": "1856 Last Island hurricane", "link": "https://wikipedia.org/wiki/1856_Last_Island_hurricane"}, {"title": "Louisiana", "link": "https://wikipedia.org/wiki/Louisiana"}]}, {"year": "1861", "text": "American Civil War: Battle of Wilson's Creek: A mixed force of Confederate, Missouri State Guard, and Arkansas State troops defeat outnumbered attacking Union forces in the southwestern part of the state.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wilson%27s_Creek\" title=\"Battle of Wilson's Creek\">Battle of Wilson's Creek</a>: A mixed force of <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a>, <a href=\"https://wikipedia.org/wiki/Missouri_State_Guard\" title=\"Missouri State Guard\">Missouri State Guard</a>, and Arkansas State troops defeat outnumbered attacking <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces in the southwestern part of the state.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wilson%27s_Creek\" title=\"Battle of Wilson's Creek\">Battle of Wilson's Creek</a>: A mixed force of <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a>, <a href=\"https://wikipedia.org/wiki/Missouri_State_Guard\" title=\"Missouri State Guard\">Missouri State Guard</a>, and Arkansas State troops defeat outnumbered attacking <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces in the southwestern part of the state.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Battle of Wilson's Creek", "link": "https://wikipedia.org/wiki/Battle_of_Wilson%27s_Creek"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Missouri State Guard", "link": "https://wikipedia.org/wiki/Missouri_State_Guard"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1864", "text": "After Uruguay's governing Blanco Party refuses Brazil's demands, <PERSON> announces that the Brazilian military will begin reprisals, beginning the Uruguayan War.", "html": "1864 - After Uruguay's governing <a href=\"https://wikipedia.org/wiki/National_Party_(Uruguay)\" title=\"National Party (Uruguay)\">Blanco Party</a> refuses Brazil's demands, <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ant%C3%B4nio_Saraiva\" title=\"<PERSON>\"><PERSON></a> announces that the Brazilian military will begin reprisals, beginning the <a href=\"https://wikipedia.org/wiki/Uruguayan_War\" title=\"Uruguayan War\">Uruguayan War</a>.", "no_year_html": "After Uruguay's governing <a href=\"https://wikipedia.org/wiki/National_Party_(Uruguay)\" title=\"National Party (Uruguay)\">Blanco Party</a> refuses Brazil's demands, <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Ant%C3%B4nio_Saraiva\" title=\"<PERSON>\"><PERSON></a> announces that the Brazilian military will begin reprisals, beginning the <a href=\"https://wikipedia.org/wiki/Uruguayan_War\" title=\"Uruguayan War\">Uruguayan War</a>.", "links": [{"title": "National Party (Uruguay)", "link": "https://wikipedia.org/wiki/National_Party_(Uruguay)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Ant%C3%B4nio_Saraiva"}, {"title": "Uruguayan War", "link": "https://wikipedia.org/wiki/Uruguayan_War"}]}, {"year": "1901", "text": "The U.S. Steel recognition strike by the Amalgamated Association of Iron and Steel Workers begins.", "html": "1901 - The <a href=\"https://wikipedia.org/wiki/U.S._Steel_recognition_strike_of_1901\" title=\"U.S. Steel recognition strike of 1901\">U.S. Steel recognition strike</a> by the <a href=\"https://wikipedia.org/wiki/Amalgamated_Association_of_Iron_and_Steel_Workers\" title=\"Amalgamated Association of Iron and Steel Workers\">Amalgamated Association of Iron and Steel Workers</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/U.S._Steel_recognition_strike_of_1901\" title=\"U.S. Steel recognition strike of 1901\">U.S. Steel recognition strike</a> by the <a href=\"https://wikipedia.org/wiki/Amalgamated_Association_of_Iron_and_Steel_Workers\" title=\"Amalgamated Association of Iron and Steel Workers\">Amalgamated Association of Iron and Steel Workers</a> begins.", "links": [{"title": "U.S. Steel recognition strike of 1901", "link": "https://wikipedia.org/wiki/U.S._Steel_recognition_strike_of_1901"}, {"title": "Amalgamated Association of Iron and Steel Workers", "link": "https://wikipedia.org/wiki/Amalgamated_Association_of_Iron_and_Steel_Workers"}]}, {"year": "1904", "text": "Russo-Japanese War: The Battle of the Yellow Sea between the Russian and Japanese battleship fleets takes place.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Yellow_Sea\" title=\"Battle of the Yellow Sea\">Battle of the Yellow Sea</a> between the Russian and Japanese battleship fleets takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Japanese_War\" title=\"Russo-Japanese War\">Russo-Japanese War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Yellow_Sea\" title=\"Battle of the Yellow Sea\">Battle of the Yellow Sea</a> between the Russian and Japanese battleship fleets takes place.", "links": [{"title": "Russo-Japanese War", "link": "https://wikipedia.org/wiki/Russo-Japanese_War"}, {"title": "Battle of the Yellow Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Yellow_Sea"}]}, {"year": "1905", "text": "Russo-Japanese War: Peace negotiations begin in Portsmouth, New Hampshire.", "html": "1905 - Russo-Japanese War: Peace negotiations begin in <a href=\"https://wikipedia.org/wiki/Portsmouth,_New_Hampshire\" title=\"Portsmouth, New Hampshire\">Portsmouth, New Hampshire</a>.", "no_year_html": "Russo-Japanese War: Peace negotiations begin in <a href=\"https://wikipedia.org/wiki/Portsmouth,_New_Hampshire\" title=\"Portsmouth, New Hampshire\">Portsmouth, New Hampshire</a>.", "links": [{"title": "Portsmouth, New Hampshire", "link": "https://wikipedia.org/wiki/Portsmouth,_New_Hampshire"}]}, {"year": "1913", "text": "Second Balkan War: Delegates from Bulgaria, Romania, Serbia, Montenegro, and Greece sign the Treaty of Bucharest, ending the war.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>: Delegates from <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, and Greece sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1913)\" title=\"Treaty of Bucharest (1913)\">Treaty of Bucharest</a>, ending the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Balkan_War\" title=\"Second Balkan War\">Second Balkan War</a>: Delegates from <a href=\"https://wikipedia.org/wiki/Bulgaria\" title=\"Bulgaria\">Bulgaria</a>, <a href=\"https://wikipedia.org/wiki/Romania\" title=\"Romania\">Romania</a>, <a href=\"https://wikipedia.org/wiki/Serbia\" title=\"Serbia\">Serbia</a>, <a href=\"https://wikipedia.org/wiki/Montenegro\" title=\"Montenegro\">Montenegro</a>, and Greece sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bucharest_(1913)\" title=\"Treaty of Bucharest (1913)\">Treaty of Bucharest</a>, ending the war.", "links": [{"title": "Second Balkan War", "link": "https://wikipedia.org/wiki/Second_Balkan_War"}, {"title": "Bulgaria", "link": "https://wikipedia.org/wiki/Bulgaria"}, {"title": "Romania", "link": "https://wikipedia.org/wiki/Romania"}, {"title": "Serbia", "link": "https://wikipedia.org/wiki/Serbia"}, {"title": "Montenegro", "link": "https://wikipedia.org/wiki/Montenegro"}, {"title": "Treaty of Bucharest (1913)", "link": "https://wikipedia.org/wiki/Treaty_of_Bucharest_(1913)"}]}, {"year": "1920", "text": "World War I: Ottoman sultan <PERSON><PERSON><PERSON>'s representatives sign the Treaty of Sèvres that divides up the Ottoman Empire between the Allies.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/List_of_sultans_of_the_Ottoman_Empire\" title=\"List of sultans of the Ottoman Empire\">Ottoman sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehmed VI</a>'s representatives sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_S%C3%A8vres\" title=\"Treaty of Sèvres\">Treaty of Sèvres</a> that divides up the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> between the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/List_of_sultans_of_the_Ottoman_Empire\" title=\"List of sultans of the Ottoman Empire\">Ottoman sultan</a> <a href=\"https://wikipedia.org/wiki/Mehmed_VI\" title=\"Mehmed VI\">Mehmed VI</a>'s representatives sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_S%C3%A8vres\" title=\"Treaty of Sèvres\">Treaty of Sèvres</a> that divides up the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a> between the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allies</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "List of sultans of the Ottoman Empire", "link": "https://wikipedia.org/wiki/List_of_sultans_of_the_Ottoman_Empire"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Treaty of Sèvres", "link": "https://wikipedia.org/wiki/Treaty_of_S%C3%A8vres"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1937", "text": "Spanish Civil War: The Regional Defence Council of Aragon is dissolved by the Second Spanish Republic.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon\" title=\"Regional Defence Council of Aragon\">Regional Defence Council of Aragon</a> is dissolved by the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon\" title=\"Regional Defence Council of Aragon\">Regional Defence Council of Aragon</a> is dissolved by the <a href=\"https://wikipedia.org/wiki/Second_Spanish_Republic\" title=\"Second Spanish Republic\">Second Spanish Republic</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Regional Defence Council of Aragon", "link": "https://wikipedia.org/wiki/Regional_Defence_Council_of_Aragon"}, {"title": "Second Spanish Republic", "link": "https://wikipedia.org/wiki/Second_Spanish_Republic"}]}, {"year": "1944", "text": "World War II: The Battle of Guam comes to an effective end.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Guam_(1944)\" title=\"Battle of Guam (1944)\">Battle of Guam</a> comes to an effective end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Guam_(1944)\" title=\"Battle of Guam (1944)\">Battle of Guam</a> comes to an effective end.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Guam (1944)", "link": "https://wikipedia.org/wiki/Battle_of_Guam_(1944)"}]}, {"year": "1944", "text": "World War II: The Battle of Narva ends with a defensive German victory.", "html": "1944 - World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Narva_(1944)\" title=\"Battle of Narva (1944)\">Battle of Narva</a> ends with a defensive German victory.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Battle_of_Narva_(1944)\" title=\"Battle of Narva (1944)\">Battle of Narva</a> ends with a defensive German victory.", "links": [{"title": "Battle of Narva (1944)", "link": "https://wikipedia.org/wiki/Battle_of_Narva_(1944)"}]}, {"year": "1945", "text": "The Japanese government announced that a message had been sent to the Allies accepting the terms of the Potsdam Declaration provided that it \"does not comprise any demand that prejudices the prerogatives of the Emperor as sovereign ruler.\"", "html": "1945 - The Japanese government announced that a message had been sent to the Allies accepting the terms of the <a href=\"https://wikipedia.org/wiki/Potsdam_Declaration\" title=\"Potsdam Declaration\">Potsdam Declaration</a> provided that it \"does not comprise any demand that prejudices the prerogatives of the Emperor as sovereign ruler.\"", "no_year_html": "The Japanese government announced that a message had been sent to the Allies accepting the terms of the <a href=\"https://wikipedia.org/wiki/Potsdam_Declaration\" title=\"Potsdam Declaration\">Potsdam Declaration</a> provided that it \"does not comprise any demand that prejudices the prerogatives of the Emperor as sovereign ruler.\"", "links": [{"title": "Potsdam Declaration", "link": "https://wikipedia.org/wiki/Potsdam_Declaration"}]}, {"year": "1948", "text": "Candid Camera makes its television debut after being on radio for a year as The Candid Microphone.", "html": "1948 - <i><a href=\"https://wikipedia.org/wiki/Candid_Camera\" title=\"Candid Camera\">Candid Camera</a></i> makes its television debut after being on radio for a year as <i><a href=\"https://wikipedia.org/wiki/The_Candid_Microphone\" title=\"The Candid Microphone\">The Candid Microphone</a></i>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Candid_Camera\" title=\"Candid Camera\">Candid Camera</a></i> makes its television debut after being on radio for a year as <i><a href=\"https://wikipedia.org/wiki/The_Candid_Microphone\" title=\"The Candid Microphone\">The Candid Microphone</a></i>.", "links": [{"title": "Candid Camera", "link": "https://wikipedia.org/wiki/Candid_Camera"}, {"title": "The Candid Microphone", "link": "https://wikipedia.org/wiki/The_Candid_Microphone"}]}, {"year": "1949", "text": "An amendment to the National Security Act of 1947 enhances the authority of the United States Secretary of Defense over the Army, Navy and Air Force, and replaces the National Military Establishment with the Department of Defense.", "html": "1949 - An amendment to the <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act of 1947</a> enhances the authority of the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> over the Army, Navy and Air Force, and replaces the National Military Establishment with the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">Department of Defense</a>.", "no_year_html": "An amendment to the <a href=\"https://wikipedia.org/wiki/National_Security_Act_of_1947\" title=\"National Security Act of 1947\">National Security Act of 1947</a> enhances the authority of the <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> over the Army, Navy and Air Force, and replaces the National Military Establishment with the <a href=\"https://wikipedia.org/wiki/United_States_Department_of_Defense\" title=\"United States Department of Defense\">Department of Defense</a>.", "links": [{"title": "National Security Act of 1947", "link": "https://wikipedia.org/wiki/National_Security_Act_of_1947"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}, {"title": "United States Department of Defense", "link": "https://wikipedia.org/wiki/United_States_Department_of_Defense"}]}, {"year": "1953", "text": "First Indochina War: The French Union withdraws its forces from Operation Camargue against the Viet Minh in central Vietnam.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/French_Union\" title=\"French Union\">French Union</a> withdraws its forces from <a href=\"https://wikipedia.org/wiki/Operation_Camargue\" title=\"Operation Camargue\">Operation Camargue</a> against the <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">Viet Minh</a> in central <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/French_Union\" title=\"French Union\">French Union</a> withdraws its forces from <a href=\"https://wikipedia.org/wiki/Operation_Camargue\" title=\"Operation Camargue\">Operation Camargue</a> against the <a href=\"https://wikipedia.org/wiki/Viet_Minh\" title=\"Viet Minh\">Viet Minh</a> in central <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a>.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "French Union", "link": "https://wikipedia.org/wiki/French_Union"}, {"title": "Operation Camargue", "link": "https://wikipedia.org/wiki/Operation_Camargue"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viet_Minh"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}]}, {"year": "1954", "text": "At Massena, New York, the groundbreaking ceremony for the Saint Lawrence Seaway is held.", "html": "1954 - At <a href=\"https://wikipedia.org/wiki/Massena,_New_York\" title=\"Massena, New York\">Massena, New York</a>, the groundbreaking ceremony for the <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a> is held.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Massena,_New_York\" title=\"Massena, New York\">Massena, New York</a>, the groundbreaking ceremony for the <a href=\"https://wikipedia.org/wiki/Saint_Lawrence_Seaway\" class=\"mw-redirect\" title=\"Saint Lawrence Seaway\">Saint Lawrence Seaway</a> is held.", "links": [{"title": "Massena, New York", "link": "https://wikipedia.org/wiki/Massena,_New_York"}, {"title": "Saint Lawrence Seaway", "link": "https://wikipedia.org/wiki/Saint_Lawrence_Seaway"}]}, {"year": "1961", "text": "Vietnam War: The U.S. Army begins Operation Ranch Hand, spraying an estimated 20 million US gallons (76,000 m3) of defoliants and herbicides over rural areas of South Vietnam in an attempt to deprive the Viet Cong of food and vegetation cover.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The U.S. Army begins <a href=\"https://wikipedia.org/wiki/Operation_Ranch_Hand\" title=\"Operation Ranch Hand\">Operation Ranch Hand</a>, spraying an estimated 20 million US gallons (76,000 m) of defoliants and herbicides over rural areas of South Vietnam in an attempt to deprive the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> of food and vegetation cover.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The U.S. Army begins <a href=\"https://wikipedia.org/wiki/Operation_Ranch_Hand\" title=\"Operation Ranch Hand\">Operation Ranch Hand</a>, spraying an estimated 20 million US gallons (76,000 m) of defoliants and herbicides over rural areas of South Vietnam in an attempt to deprive the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> of food and vegetation cover.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Ranch Hand", "link": "https://wikipedia.org/wiki/Operation_Ranch_Hand"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}]}, {"year": "1966", "text": "The Heron Road Bridge collapses while being built, killing nine workers in the deadliest construction accident in both Ottawa and Ontario.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Heron_Road_Bridge\" class=\"mw-redirect\" title=\"Heron Road Bridge\">Heron Road Bridge</a> collapses while being built, killing nine workers in the deadliest construction accident in both <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> and <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Heron_Road_Bridge\" class=\"mw-redirect\" title=\"Heron Road Bridge\">Heron Road Bridge</a> collapses while being built, killing nine workers in the deadliest construction accident in both <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a> and <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>.", "links": [{"title": "Heron Road Bridge", "link": "https://wikipedia.org/wiki/Heron_Road_Bridge"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}]}, {"year": "1969", "text": "A day after murdering <PERSON> and four others, members of <PERSON>'s cult kill <PERSON><PERSON> and <PERSON>.", "html": "1969 - A day after murdering <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and four others, members of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s cult kill <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a>.", "no_year_html": "A day after murdering <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and four others, members of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s cult kill <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "The Society for American Baseball Research is founded in Cooperstown, New York.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/Society_for_American_Baseball_Research\" title=\"Society for American Baseball Research\">Society for American Baseball Research</a> is founded in <a href=\"https://wikipedia.org/wiki/Cooperstown,_New_York\" title=\"Cooperstown, New York\">Cooperstown, New York</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Society_for_American_Baseball_Research\" title=\"Society for American Baseball Research\">Society for American Baseball Research</a> is founded in <a href=\"https://wikipedia.org/wiki/Cooperstown,_New_York\" title=\"Cooperstown, New York\">Cooperstown, New York</a>.", "links": [{"title": "Society for American Baseball Research", "link": "https://wikipedia.org/wiki/Society_for_American_Baseball_Research"}, {"title": "Cooperstown, New York", "link": "https://wikipedia.org/wiki/Cooperstown,_New_York"}]}, {"year": "1977", "text": "In Yonkers, New York, 24-year-old postal employee <PERSON> (\"Son of Sam\") is arrested for a series of killings in the New York City area over the period of one year.", "html": "1977 - In <a href=\"https://wikipedia.org/wiki/Yonkers,_New_York\" title=\"Yonkers, New York\">Yonkers, New York</a>, 24-year-old postal employee <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"Son of Sam\") is arrested for a <a href=\"https://wikipedia.org/wiki/Serial_killer\" title=\"Serial killer\">series of killings</a> in the New York City area over the period of one year.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Yonkers,_New_York\" title=\"Yonkers, New York\">Yonkers, New York</a>, 24-year-old postal employee <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (\"Son of Sam\") is arrested for a <a href=\"https://wikipedia.org/wiki/Serial_killer\" title=\"Serial killer\">series of killings</a> in the New York City area over the period of one year.", "links": [{"title": "Yonkers, New York", "link": "https://wikipedia.org/wiki/Yonkers,_New_York"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Serial killer", "link": "https://wikipedia.org/wiki/Serial_killer"}]}, {"year": "1978", "text": "Three members of the <PERSON> family are killed in an accident. This leads to the Ford Pinto litigation.", "html": "1978 - Three members of the <PERSON> family are killed in an accident. This leads to the <a href=\"https://wikipedia.org/wiki/Ford_Pinto\" title=\"Ford Pinto\">Ford Pinto</a> <a href=\"https://wikipedia.org/wiki/Ford_Pinto#Recall\" title=\"Ford Pinto\">litigation</a>.", "no_year_html": "Three members of the <PERSON> family are killed in an accident. This leads to the <a href=\"https://wikipedia.org/wiki/Ford_Pinto\" title=\"Ford Pinto\">Ford Pinto</a> <a href=\"https://wikipedia.org/wiki/Ford_Pinto#Recall\" title=\"Ford Pinto\">litigation</a>.", "links": [{"title": "Ford Pinto", "link": "https://wikipedia.org/wiki/Ford_Pinto"}, {"title": "Ford Pinto", "link": "https://wikipedia.org/wiki/Ford_Pinto#Recall"}]}, {"year": "1981", "text": "Murder of <PERSON>: The head of <PERSON>'s son is found. This inspires the creation of the television series America's Most Wanted and the National Center for Missing & Exploited Children.", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">Murder of <PERSON></a>: The head of <a href=\"https://wikipedia.org/wiki/<PERSON>(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>'s son is found. This inspires the creation of the television series <i><a href=\"https://wikipedia.org/wiki/America%27s_Most_Wanted\" title=\"America's Most Wanted\">America's Most Wanted</a></i> and the <a href=\"https://wikipedia.org/wiki/National_Center_for_Missing_%26_Exploited_Children\" title=\"National Center for Missing &amp; Exploited Children\">National Center for Missing &amp; Exploited Children</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>\" title=\"Murder of <PERSON>\">Murder of <PERSON></a>: The head of <a href=\"https://wikipedia.org/wiki/<PERSON>(television_host)\" title=\"<PERSON> (television host)\"><PERSON></a>'s son is found. This inspires the creation of the television series <i><a href=\"https://wikipedia.org/wiki/America%27s_Most_Wanted\" title=\"America's Most Wanted\">America's Most Wanted</a></i> and the <a href=\"https://wikipedia.org/wiki/National_Center_for_Missing_%26_Exploited_Children\" title=\"National Center for Missing &amp; Exploited Children\">National Center for Missing &amp; Exploited Children</a>.", "links": [{"title": "Murder of <PERSON>", "link": "https://wikipedia.org/wiki/Murder_of_<PERSON>_<PERSON>"}, {"title": "<PERSON> (television host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(television_host)"}, {"title": "America's Most Wanted", "link": "https://wikipedia.org/wiki/America%27s_Most_Wanted"}, {"title": "National Center for Missing & Exploited Children", "link": "https://wikipedia.org/wiki/National_Center_for_Missing_%26_Exploited_Children"}]}, {"year": "1988", "text": "Japanese American internment: U.S. President <PERSON> signs the Civil Liberties Act of 1988, providing $20,000 payments to Japanese Americans who were either interned in or relocated by the United States during World War II.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Japanese_American_internment\" class=\"mw-redirect\" title=\"Japanese American internment\">Japanese American internment</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Civil_Liberties_Act_of_1988\" title=\"Civil Liberties Act of 1988\">Civil Liberties Act of 1988</a>, providing $20,000 payments to <a href=\"https://wikipedia.org/wiki/Japanese_Americans\" title=\"Japanese Americans\">Japanese Americans</a> who were either interned in or relocated by the United States during World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_American_internment\" class=\"mw-redirect\" title=\"Japanese American internment\">Japanese American internment</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Civil_Liberties_Act_of_1988\" title=\"Civil Liberties Act of 1988\">Civil Liberties Act of 1988</a>, providing $20,000 payments to <a href=\"https://wikipedia.org/wiki/Japanese_Americans\" title=\"Japanese Americans\">Japanese Americans</a> who were either interned in or relocated by the United States during World War II.", "links": [{"title": "Japanese American internment", "link": "https://wikipedia.org/wiki/Japanese_American_internment"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Civil Liberties Act of 1988", "link": "https://wikipedia.org/wiki/Civil_Liberties_Act_of_1988"}, {"title": "Japanese Americans", "link": "https://wikipedia.org/wiki/Japanese_Americans"}]}, {"year": "1990", "text": "The Magellan space probe reaches Venus.", "html": "1990 - The <a href=\"https://wikipedia.org/wiki/Magel<PERSON>_(spacecraft)\" title=\"Magel<PERSON> (spacecraft)\"><PERSON>gel<PERSON> space probe</a> reaches <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Magel<PERSON>_(spacecraft)\" title=\"<PERSON>gel<PERSON> (spacecraft)\"><PERSON><PERSON><PERSON> space probe</a> reaches <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>.", "links": [{"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}]}, {"year": "1993", "text": "Two earthquakes affect New Zealand. A 7.0 Mw  shock (intensity VI (Strong)) in the South Island was followed nine hours later by a 6.4 Mw  event (intensity VII (Very strong)) in the North Island.", "html": "1993 - Two <a href=\"https://wikipedia.org/wiki/List_of_earthquakes_in_New_Zealand\" title=\"List of earthquakes in New Zealand\">earthquakes affect New Zealand</a>. A 7.0 M<sub>w</sub>  shock (intensity VI (<i>Strong</i>)) in the <a href=\"https://wikipedia.org/wiki/South_Island\" title=\"South Island\">South Island</a> was followed nine hours later by a 6.4 M<sub>w</sub>  event (intensity VII (<i>Very strong</i>)) in the North Island.", "no_year_html": "Two <a href=\"https://wikipedia.org/wiki/List_of_earthquakes_in_New_Zealand\" title=\"List of earthquakes in New Zealand\">earthquakes affect New Zealand</a>. A 7.0 M<sub>w</sub>  shock (intensity VI (<i>Strong</i>)) in the <a href=\"https://wikipedia.org/wiki/South_Island\" title=\"South Island\">South Island</a> was followed nine hours later by a 6.4 M<sub>w</sub>  event (intensity VII (<i>Very strong</i>)) in the North Island.", "links": [{"title": "List of earthquakes in New Zealand", "link": "https://wikipedia.org/wiki/List_of_earthquakes_in_New_Zealand"}, {"title": "South Island", "link": "https://wikipedia.org/wiki/South_Island"}]}, {"year": "1995", "text": "Oklahoma City bombing: <PERSON> and <PERSON> are indicted for the bombing. <PERSON> pleads guilty in a plea-bargain for his testimony.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are indicted for the bombing. <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>\"><PERSON></a> pleads guilty in a plea-bargain for his testimony.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oklahoma_City_bombing\" title=\"Oklahoma City bombing\">Oklahoma City bombing</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> are indicted for the bombing. <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> and <PERSON>\"><PERSON></a> pleads guilty in a plea-bargain for his testimony.", "links": [{"title": "Oklahoma City bombing", "link": "https://wikipedia.org/wiki/Oklahoma_City_bombing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "Sixteen people are killed when Formosa Airlines Flight 7601 crashes near Beigan Airport in the Matsu Islands of Taiwan.", "html": "1997 - Sixteen people are killed when <a href=\"https://wikipedia.org/wiki/Formosa_Airlines_Flight_7601\" title=\"Formosa Airlines Flight 7601\">Formosa Airlines Flight 7601</a> crashes near <a href=\"https://wikipedia.org/wiki/Beigan_Airport\" title=\"Beigan Airport\">Beigan Airport</a> in the <a href=\"https://wikipedia.org/wiki/Matsu_Islands\" title=\"Matsu Islands\">Matsu Islands</a> of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "no_year_html": "Sixteen people are killed when <a href=\"https://wikipedia.org/wiki/Formosa_Airlines_Flight_7601\" title=\"Formosa Airlines Flight 7601\">Formosa Airlines Flight 7601</a> crashes near <a href=\"https://wikipedia.org/wiki/Beigan_Airport\" title=\"Beigan Airport\">Beigan Airport</a> in the <a href=\"https://wikipedia.org/wiki/Matsu_Islands\" title=\"Matsu Islands\">Matsu Islands</a> of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "links": [{"title": "Formosa Airlines Flight 7601", "link": "https://wikipedia.org/wiki/Formosa_Airlines_Flight_7601"}, {"title": "Beigan Airport", "link": "https://wikipedia.org/wiki/Beigan_Airport"}, {"title": "Matsu Islands", "link": "https://wikipedia.org/wiki/Matsu_Islands"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "1998", "text": "HRH Prince <PERSON> is proclaimed the crown prince of Brunei with a Royal Proclamation.", "html": "1998 - HRH Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is proclaimed the <a href=\"https://wikipedia.org/wiki/Crown_prince\" title=\"Crown prince\">crown prince</a> of <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> with a Royal Proclamation.", "no_year_html": "HRH Prince <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a> is proclaimed the <a href=\"https://wikipedia.org/wiki/Crown_prince\" title=\"Crown prince\">crown prince</a> of <a href=\"https://wikipedia.org/wiki/Brunei\" title=\"Brunei\">Brunei</a> with a Royal Proclamation.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Crown prince", "link": "https://wikipedia.org/wiki/Crown_prince"}, {"title": "Brunei", "link": "https://wikipedia.org/wiki/Brunei"}]}, {"year": "1999", "text": "Los Angeles Jewish Community Center shooting.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Los_Angeles_Jewish_Community_Center_shooting\" title=\"Los Angeles Jewish Community Center shooting\">Los Angeles Jewish Community Center shooting</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Los_Angeles_Jewish_Community_Center_shooting\" title=\"Los Angeles Jewish Community Center shooting\">Los Angeles Jewish Community Center shooting</a>.", "links": [{"title": "Los Angeles Jewish Community Center shooting", "link": "https://wikipedia.org/wiki/Los_Angeles_Jewish_Community_Center_shooting"}]}, {"year": "2001", "text": "The 2001 Angola train attack occurred, causing 252 deaths.", "html": "2001 - The <a href=\"https://wikipedia.org/wiki/2001_Angola_train_attack\" title=\"2001 Angola train attack\">2001 Angola train attack</a> occurred, causing 252 deaths.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/2001_Angola_train_attack\" title=\"2001 Angola train attack\">2001 Angola train attack</a> occurred, causing 252 deaths.", "links": [{"title": "2001 Angola train attack", "link": "https://wikipedia.org/wiki/2001_Angola_train_attack"}]}, {"year": "2001", "text": "Space Shuttle program: The Space Shuttle Discovery is launched on STS-105 to the International Space Station, carrying the astronauts of Expedition 3 to replace the crew of Expedition 2.", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-105\" title=\"STS-105\">STS-105</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, carrying the astronauts of <a href=\"https://wikipedia.org/wiki/Expedition_3\" title=\"Expedition 3\">Expedition 3</a> to replace the crew of <a href=\"https://wikipedia.org/wiki/Expedition_2\" title=\"Expedition 2\">Expedition 2</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: The <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-105\" title=\"STS-105\">STS-105</a> to the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>, carrying the astronauts of <a href=\"https://wikipedia.org/wiki/Expedition_3\" title=\"Expedition 3\">Expedition 3</a> to replace the crew of <a href=\"https://wikipedia.org/wiki/Expedition_2\" title=\"Expedition 2\">Expedition 2</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-105", "link": "https://wikipedia.org/wiki/STS-105"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "Expedition 3", "link": "https://wikipedia.org/wiki/Expedition_3"}, {"title": "Expedition 2", "link": "https://wikipedia.org/wiki/Expedition_2"}]}, {"year": "2003", "text": "The Okinawa Urban Monorail is opened in Naha, Okinawa.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Okinawa_Urban_Monorail\" title=\"Okinawa Urban Monorail\">Okinawa Urban Monorail</a> is opened in <a href=\"https://wikipedia.org/wiki/Naha\" title=\"Naha\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Okinawa_Urban_Monorail\" title=\"Okinawa Urban Monorail\">Okinawa Urban Monorail</a> is opened in <a href=\"https://wikipedia.org/wiki/Naha\" title=\"Naha\"><PERSON>a</a>, <a href=\"https://wikipedia.org/wiki/Okinawa_Prefecture\" title=\"Okinawa Prefecture\">Okinawa</a>.", "links": [{"title": "Okinawa Urban Monorail", "link": "https://wikipedia.org/wiki/Okinawa_Urban_Monorail"}, {"title": "Naha", "link": "https://wikipedia.org/wiki/Naha"}, {"title": "Okinawa Prefecture", "link": "https://wikipedia.org/wiki/Okinawa_Prefecture"}]}, {"year": "2009", "text": "Twenty people are killed in Handlová, Trenčín Region, in the deadliest mining disaster in Slovakia's history.", "html": "2009 - Twenty people are killed in <a href=\"https://wikipedia.org/wiki/Handlov%C3%A1\" title=\"Handlová\">Handlová</a>, <a href=\"https://wikipedia.org/wiki/Tren%C4%8D%C3%ADn_Region\" title=\"Trenčín Region\">Trenčín Region</a>, in the deadliest <a href=\"https://wikipedia.org/wiki/2009_Handlov%C3%A1_mine_blast\" title=\"2009 Handlová mine blast\">mining disaster</a> in <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>'s history.", "no_year_html": "Twenty people are killed in <a href=\"https://wikipedia.org/wiki/Handlov%C3%A1\" title=\"Handlová\">Handlová</a>, <a href=\"https://wikipedia.org/wiki/Tren%C4%8D%C3%ADn_Region\" title=\"Trenčín Region\">Trenčín Region</a>, in the deadliest <a href=\"https://wikipedia.org/wiki/2009_Handlov%C3%A1_mine_blast\" title=\"2009 Handlová mine blast\">mining disaster</a> in <a href=\"https://wikipedia.org/wiki/Slovakia\" title=\"Slovakia\">Slovakia</a>'s history.", "links": [{"title": "Handlová", "link": "https://wikipedia.org/wiki/Handlov%C3%A1"}, {"title": "Trenčín Region", "link": "https://wikipedia.org/wiki/Tren%C4%8D%C3%ADn_Region"}, {"title": "2009 Handlová mine blast", "link": "https://wikipedia.org/wiki/2009_Handlov%C3%A1_mine_blast"}, {"title": "Slovakia", "link": "https://wikipedia.org/wiki/Slovakia"}]}, {"year": "2012", "text": "The Marikana massacre begins near Rustenburg, South Africa, resulting in the deaths of 47 people.", "html": "2012 - The <a href=\"https://wikipedia.org/wiki/Marikana_killings\" class=\"mw-redirect\" title=\"Marikana killings\">Marikana massacre</a> begins near <a href=\"https://wikipedia.org/wiki/Rustenburg\" title=\"Rustenburg\">Rustenburg</a>, South Africa, resulting in the deaths of 47 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Marikana_killings\" class=\"mw-redirect\" title=\"Marikana killings\">Marikana massacre</a> begins near <a href=\"https://wikipedia.org/wiki/Rustenburg\" title=\"Rustenburg\">Rustenburg</a>, South Africa, resulting in the deaths of 47 people.", "links": [{"title": "Marikana killings", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_killings"}, {"title": "Rustenburg", "link": "https://wikipedia.org/wiki/Rustenburg"}]}, {"year": "2014", "text": "Forty people are killed when Sepahan Airlines Flight 5915 crashes at Tehran's Mehrabad International Airport.", "html": "2014 - Forty people are killed when <a href=\"https://wikipedia.org/wiki/Sepahan_Airlines_Flight_5915\" title=\"Sepahan Airlines Flight 5915\">Sepahan Airlines Flight 5915</a> crashes at Tehran's <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a>.", "no_year_html": "Forty people are killed when <a href=\"https://wikipedia.org/wiki/Sepahan_Airlines_Flight_5915\" title=\"Sepahan Airlines Flight 5915\">Sepahan Airlines Flight 5915</a> crashes at Tehran's <a href=\"https://wikipedia.org/wiki/Mehrabad_International_Airport\" title=\"Mehrabad International Airport\">Mehrabad International Airport</a>.", "links": [{"title": "Sepahan Airlines Flight 5915", "link": "https://wikipedia.org/wiki/Sepahan_Airlines_Flight_5915"}, {"title": "Mehrabad International Airport", "link": "https://wikipedia.org/wiki/Mehrabad_International_Airport"}]}, {"year": "2018", "text": "Horizon Air employee <PERSON> hijacks and performs an unauthorized takeoff on a Horizon Air Bombardier Dash 8 Q400 plane at Seattle-Tacoma International Airport in Washington, flying it for more than an hour before crashing the plane and killing himself on Ketron Island in Puget Sound.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Horizon_Air\" title=\"Horizon Air\">Horizon Air</a> employee <PERSON> <a href=\"https://wikipedia.org/wiki/2018_Horizon_Air_Q400_incident\" class=\"mw-redirect\" title=\"2018 Horizon Air Q400 incident\">hijacks and performs an unauthorized takeoff</a> on a <a href=\"https://wikipedia.org/wiki/Horizon_Air\" title=\"Horizon Air\">Horizon Air</a> <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_Dash_8\" title=\"De Havilland Canada Dash 8\">Bombardier Dash 8 Q400</a> plane at <a href=\"https://wikipedia.org/wiki/Seattle%E2%80%93Tacoma_International_Airport\" title=\"Seattle-Tacoma International Airport\">Seattle-Tacoma International Airport</a> in Washington, flying it for more than an hour before crashing the plane and killing himself on <a href=\"https://wikipedia.org/wiki/Ketron_Island,_Washington\" title=\"Ketron Island, Washington\">Ketron Island</a> in <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Horizon_Air\" title=\"Horizon Air\">Horizon Air</a> employee <PERSON> <a href=\"https://wikipedia.org/wiki/2018_Horizon_Air_Q400_incident\" class=\"mw-redirect\" title=\"2018 Horizon Air Q400 incident\">hijacks and performs an unauthorized takeoff</a> on a <a href=\"https://wikipedia.org/wiki/Horizon_Air\" title=\"Horizon Air\">Horizon Air</a> <a href=\"https://wikipedia.org/wiki/De_Havilland_Canada_Dash_8\" title=\"De Havilland Canada Dash 8\">Bombardier Dash 8 Q400</a> plane at <a href=\"https://wikipedia.org/wiki/Seattle%E2%80%93Tacoma_International_Airport\" title=\"Seattle-Tacoma International Airport\">Seattle-Tacoma International Airport</a> in Washington, flying it for more than an hour before crashing the plane and killing himself on <a href=\"https://wikipedia.org/wiki/Ketron_Island,_Washington\" title=\"Ketron Island, Washington\">Ketron Island</a> in <a href=\"https://wikipedia.org/wiki/Puget_Sound\" title=\"Puget Sound\">Puget Sound</a>.", "links": [{"title": "Horizon Air", "link": "https://wikipedia.org/wiki/Horizon_Air"}, {"title": "2018 Horizon Air Q400 incident", "link": "https://wikipedia.org/wiki/2018_Horizon_Air_Q400_incident"}, {"title": "Horizon Air", "link": "https://wikipedia.org/wiki/Horizon_Air"}, {"title": "De Havilland Canada Dash 8", "link": "https://wikipedia.org/wiki/<PERSON>_Havilland_Canada_Dash_8"}, {"title": "Seattle-Tacoma International Airport", "link": "https://wikipedia.org/wiki/Seattle%E2%80%93Tacoma_International_Airport"}, {"title": "Ketron Island, Washington", "link": "https://wikipedia.org/wiki/Ketron_Island,_Washington"}, {"title": "Puget Sound", "link": "https://wikipedia.org/wiki/Puget_Sound"}]}, {"year": "2018", "text": "An anti-government rally turns into a riot when members of the Romanian Gendarmerie attack the 100,000 people protesting in front of the Victoria Palace, leading to 452 recorded injuries. The authorities alleged that the crowd was infiltrated by hooligans who began attacking law enforcement agents.", "html": "2018 - An <a href=\"https://wikipedia.org/wiki/2017%E2%80%932019_Romanian_protests#August_2018\" title=\"2017-2019 Romanian protests\">anti-government rally turns into a riot</a> when members of the <a href=\"https://wikipedia.org/wiki/Romanian_Gendarmerie\" class=\"mw-redirect\" title=\"Romanian Gendarmerie\">Romanian Gendarmerie</a> attack the 100,000 people protesting in front of the <a href=\"https://wikipedia.org/wiki/Victoria_Palace\" title=\"Victoria Palace\">Victoria Palace</a>, leading to 452 recorded injuries. The authorities alleged that the crowd was infiltrated by hooligans who began attacking law enforcement agents.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2017%E2%80%932019_Romanian_protests#August_2018\" title=\"2017-2019 Romanian protests\">anti-government rally turns into a riot</a> when members of the <a href=\"https://wikipedia.org/wiki/Romanian_Gendarmerie\" class=\"mw-redirect\" title=\"Romanian Gendarmerie\">Romanian Gendarmerie</a> attack the 100,000 people protesting in front of the <a href=\"https://wikipedia.org/wiki/Victoria_Palace\" title=\"Victoria Palace\">Victoria Palace</a>, leading to 452 recorded injuries. The authorities alleged that the crowd was infiltrated by hooligans who began attacking law enforcement agents.", "links": [{"title": "2017-2019 Romanian protests", "link": "https://wikipedia.org/wiki/2017%E2%80%932019_Romanian_protests#August_2018"}, {"title": "Romanian Gendarmerie", "link": "https://wikipedia.org/wiki/Romanian_Gendarmerie"}, {"title": "Victoria Palace", "link": "https://wikipedia.org/wiki/Victoria_Palace"}]}, {"year": "2019", "text": "Thirty-two are killed and one million are evacuated as Typhoon <PERSON><PERSON><PERSON> makes landfall in Zhejiang, China. Earlier it had caused flooding in the Philippines.", "html": "2019 - Thirty-two are killed and one million are evacuated as <a href=\"https://wikipedia.org/wiki/Typhoon_Lekima_(2019)\" class=\"mw-redirect\" title=\"Typhoon Lekima (2019)\">Typhoon Lekim<PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Zhejiang\" title=\"Zhejiang\">Zhejiang</a>, China. Earlier it had caused flooding in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "no_year_html": "Thirty-two are killed and one million are evacuated as <a href=\"https://wikipedia.org/wiki/Typhoon_Lekima_(2019)\" class=\"mw-redirect\" title=\"Typhoon Lekima (2019)\">Typhoon Lekim<PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Zhejiang\" title=\"Zhejiang\">Zhejiang</a>, China. Earlier it had caused flooding in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>.", "links": [{"title": "Typhoon Lekima (2019)", "link": "https://wikipedia.org/wiki/Typhoon_Lekima_(2019)"}, {"title": "Zhejiang", "link": "https://wikipedia.org/wiki/Zhejiang"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "2019", "text": "<PERSON> shoots his stepsister and attacks a mosque in the Bærum mosque shooting.", "html": "2019 - <PERSON> shoots his stepsister and attacks a mosque in the <a href=\"https://wikipedia.org/wiki/B%C3%A6rum_mosque_shooting\" class=\"mw-redirect\" title=\"Bærum mosque shooting\">Bærum mosque shooting</a>.", "no_year_html": "<PERSON> shoots his stepsister and attacks a mosque in the <a href=\"https://wikipedia.org/wiki/B%C3%A6rum_mosque_shooting\" class=\"mw-redirect\" title=\"Bærum mosque shooting\">Bærum mosque shooting</a>.", "links": [{"title": "Bærum mosque shooting", "link": "https://wikipedia.org/wiki/B%C3%A6rum_mosque_shooting"}]}, {"year": "2020", "text": "Derecho in Iowa becomes the most costly thunderstorm disaster in U.S. history.", "html": "2020 - <a href=\"https://wikipedia.org/wiki/August_2020_Midwest_derecho\" class=\"mw-redirect\" title=\"August 2020 Midwest derecho\">Derecho</a> in <a href=\"https://wikipedia.org/wiki/Iowa\" title=\"Iowa\">Iowa</a> becomes the most costly thunderstorm disaster in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_2020_Midwest_derecho\" class=\"mw-redirect\" title=\"August 2020 Midwest derecho\">Derecho</a> in <a href=\"https://wikipedia.org/wiki/Iowa\" title=\"Iowa\">Iowa</a> becomes the most costly thunderstorm disaster in U.S. history.", "links": [{"title": "August 2020 Midwest derecho", "link": "https://wikipedia.org/wiki/August_2020_Midwest_derecho"}, {"title": "Iowa", "link": "https://wikipedia.org/wiki/Iowa"}]}, {"year": "2024", "text": " Israel strikes Al-Tabaeen school in eastern Gaza City, killing at least 80 Palestinians. ", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel</a> strikes <a href=\"https://wikipedia.org/wiki/Al-Tabaeen_school_attack\" title=\"Al-Tabaeen school attack\">Al-Tabaeen school</a> in eastern Gaza City, killing at least 80 Palestinians. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024\" title=\"2024\">2024</a> - <a href=\"https://wikipedia.org/wiki/Israel_Defense_Forces\" title=\"Israel Defense Forces\">Israel</a> strikes <a href=\"https://wikipedia.org/wiki/Al-Tabaeen_school_attack\" title=\"Al-Tabaeen school attack\">Al-Tabaeen school</a> in eastern Gaza City, killing at least 80 Palestinians. ", "links": [{"title": "2024", "link": "https://wikipedia.org/wiki/2024"}, {"title": "Israel Defense Forces", "link": "https://wikipedia.org/wiki/Israel_Defense_Forces"}, {"title": "Al-Tabaeen school attack", "link": "https://wikipedia.org/wiki/Al-Tabaeen_school_attack"}]}], "Births": [{"year": "941", "text": "<PERSON><PERSON>, Vietnamese emperor (d. 1005)", "html": "941 - <a href=\"https://wikipedia.org/wiki/L%C3%AA_Ho%C3%A0n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese emperor (d. 1005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%AA_Ho%C3%A0n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese emperor (d. 1005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%AA_Ho%C3%A0n"}]}, {"year": "1267", "text": "<PERSON> of Aragon (d. 1327)", "html": "1267 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> Aragon</a> (d. 1327)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> (d. 1327)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}]}, {"year": "1296", "text": "<PERSON> of Bohemia (d. 1346)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (d. 1346)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_of_Bohemia\" title=\"<PERSON> of Bohemia\"><PERSON> of Bohemia</a> (d. 1346)", "links": [{"title": "<PERSON> of Bohemia", "link": "https://wikipedia.org/wiki/<PERSON>_of_Bohemia"}]}, {"year": "1360", "text": "<PERSON>, Italian cardinal (d. 1417)", "html": "1360 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1417)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 1417)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1397", "text": "<PERSON> of Germany (d. 1439)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"Albert II of Germany\"><PERSON> of Germany</a> (d. 1439)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"Albert II of Germany\"><PERSON> of Germany</a> (d. 1439)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}]}, {"year": "1439", "text": "<PERSON>, Duchess of Exeter, Duchess of York (d. 1476)", "html": "1439 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Exeter\" class=\"mw-redirect\" title=\"<PERSON> York, Duchess of Exeter\"><PERSON>, Duchess of Exeter</a>, Duchess of York (d. 1476)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Exeter\" class=\"mw-redirect\" title=\"<PERSON> York, Duchess of Exeter\"><PERSON>, Duchess of Exeter</a>, Duchess of York (d. 1476)", "links": [{"title": "<PERSON>, Duchess of Exeter", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duchess_of_Exeter"}]}, {"year": "1449", "text": "<PERSON><PERSON> of Savoy, Duchess of Savoy (d. 1503)", "html": "1449 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy\" title=\"<PERSON><PERSON> of Savoy\"><PERSON><PERSON> of Savoy</a>, Duchess of Savoy (d. 1503)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy\" title=\"<PERSON><PERSON> of Savoy\"><PERSON><PERSON> of Savoy</a>, Duchess of Savoy (d. 1503)", "links": [{"title": "Bona of Savoy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savoy"}]}, {"year": "1466", "text": "<PERSON>, Marquess of Mantua (d. 1519)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Marquess_of_Mantua\" class=\"mw-redirect\" title=\"<PERSON>, Marquess of Mantua\"><PERSON>, Marquess of Mantua</a> (d. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Marquess_of_Mantua\" class=\"mw-redirect\" title=\"<PERSON>, Marquess of Mantua\"><PERSON>, Marquess of Mantua</a> (d. 1519)", "links": [{"title": "<PERSON>, Marquess of Mantua", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Marquess_of_Mantua"}]}, {"year": "1489", "text": "<PERSON>, German lawyer and politician (d. 1553)", "html": "1489 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1553)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician (d. 1553)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1520", "text": "<PERSON> Valois (d. 1537)", "html": "1520 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a> (d. 1537)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a> (d. 1537)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>"}]}, {"year": "1528", "text": "<PERSON>, Duke of Brunswick-Lüneburg (d. 1584)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg\" title=\"<PERSON>, Duke of Brunswick-Lüneburg\"><PERSON>, Duke of Brunswick-Lüneburg</a> (d. 1584)", "links": [{"title": "<PERSON>, Duke of Brunswick-Lüneburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-L%C3%BCneburg"}]}, {"year": "1547", "text": "<PERSON>, Duke of Saxe-Lauenburg (d. 1619)", "html": "1547 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (d. 1619)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg\" title=\"<PERSON>, Duke of Saxe-Lauenburg\"><PERSON>, Duke of Saxe-Lauenburg</a> (d. 1619)", "links": [{"title": "<PERSON>, Duke of Saxe-Lauenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Saxe-Lauenburg"}]}, {"year": "1560", "text": "<PERSON><PERSON><PERSON><PERSON>, German organist and composer (d. 1629)", "html": "1560 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>tor<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German organist and composer (d. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>torius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German organist and composer (d. 1629)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>torius"}]}, {"year": "1602", "text": "<PERSON>, French mathematician and academic (d. 1675)", "html": "1602 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON><PERSON><PERSON>, Italian priest and missionary (d. 1711)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Eusebio_Kino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and missionary (d. 1711)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>usebio_Kino\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian priest and missionary (d. 1711)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eusebio_Kino"}]}, {"year": "1734", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Burmese king (d. 1763)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/Naungdawgyi\" title=\"Naungdawgyi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1763)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Naungdawgyi\" title=\"Naungdawgyi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Burmese king (d. 1763)", "links": [{"title": "<PERSON>ungdaw<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Naungdawgyi"}]}, {"year": "1737", "text": "<PERSON>, Russian painter and academic (d. 1773)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter and academic (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1740", "text": "<PERSON>, English organist and composer (d. 1802)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English organist and composer (d. 1802)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>(composer)"}]}, {"year": "1744", "text": "<PERSON><PERSON><PERSON>, daughter of <PERSON> (d. 1754)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27%C3%89tiolles\" title=\"<PERSON><PERSON><PERSON>Étioll<PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame de Pompadour\">Madame <PERSON></a> (d. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>%27%C3%89tiolles\" title=\"<PERSON><PERSON><PERSON>Étioll<PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/Madame_<PERSON>_<PERSON>\" title=\"Madame de Pompadour\">Madame <PERSON>ado<PERSON></a> (d. 1754)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>%27%C3%89tiolles"}, {"title": "<PERSON> de Pompadour", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1755", "text": "<PERSON>, fifth Pesh<PERSON> of the Maratha Empire (d. 1773)", "html": "1755 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/Peshwa\" title=\"Peshwa\">Peshwa</a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fifth <a href=\"https://wikipedia.org/wiki/Peshwa\" title=\"Peshwa\">Peshwa</a> of the <a href=\"https://wikipedia.org/wiki/Maratha_Confederacy\" title=\"Maratha Confederacy\">Maratha Empire</a> (d. 1773)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peshwa", "link": "https://wikipedia.org/wiki/Peshwa"}, {"title": "Maratha Confederacy", "link": "https://wikipedia.org/wiki/Maratha_Confederacy"}]}, {"year": "1782", "text": "<PERSON>, Mexican insurgent leader and President of Mexico (d. 1831)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican insurgent leader and <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican insurgent leader and <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1805", "text": "<PERSON><PERSON><PERSON>, German-Hungarian historian and critic (d. 1875)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Hungarian historian and critic (d. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-Hungarian historian and critic (d. 1875)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>renc_<PERSON>y"}]}, {"year": "1809", "text": "<PERSON>, American ornithologist and explorer (d. 1851)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and explorer (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and explorer (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON><PERSON>, Count of Cavour, Italian soldier and politician, 1st Prime Minister of Italy (d. 1861)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, Italian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_Cavour\" title=\"<PERSON><PERSON>, Count of Cavour\"><PERSON><PERSON>, Count of Cavour</a>, Italian soldier and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (d. 1861)", "links": [{"title": "<PERSON><PERSON>, Count of Cavour", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Count_of_<PERSON><PERSON><PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "1814", "text": "<PERSON>, German businessman, founded Nestlé (d. 1890)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Nestl%C3%A9\" title=\"Nestlé\">Nestlé</a> (d. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, German businessman, founded <a href=\"https://wikipedia.org/wiki/Nestl%C3%A9\" title=\"Nestlé\">Nestlé</a> (d. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Henri_Nestl%C3%A9"}, {"title": "Nestlé", "link": "https://wikipedia.org/wiki/Nestl%C3%A9"}]}, {"year": "1814", "text": "<PERSON>, United States soldier and Confederate general (d. 1881)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States</a> soldier and <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate</a> general (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States</a> soldier and <a href=\"https://wikipedia.org/wiki/Confederate_Army\" class=\"mw-redirect\" title=\"Confederate Army\">Confederate</a> general (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Confederate Army", "link": "https://wikipedia.org/wiki/Confederate_Army"}]}, {"year": "1821", "text": "<PERSON>, American financier, founded Jay Cooke & Company (d. 1905)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Jay Cooke &amp; Company\">Jay Cooke &amp; Company</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company\" title=\"Jay Cooke &amp; Company\">Jay Cooke &amp; Company</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Jay Cooke & Company", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_%26_Company"}]}, {"year": "1823", "text": "<PERSON>, English minister and reformer (d. 1886)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and reformer (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and reformer (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON><PERSON><PERSON>, Hungarian soldier, architect, and engineer, co-designed the Corinth Canal (d. 1908)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_T%C3%BCrr\" title=\"<PERSON><PERSON><PERSON> T<PERSON>rr\"><PERSON><PERSON><PERSON></a>, Hungarian soldier, architect, and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Corinth_Canal\" title=\"Corinth Canal\">Corinth Canal</a> (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_T%C3%BCrr\" title=\"<PERSON><PERSON><PERSON> Türr\"><PERSON><PERSON><PERSON></a>, Hungarian soldier, architect, and engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Corinth_Canal\" title=\"Corinth Canal\">Corinth Canal</a> (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_T%C3%BCrr"}, {"title": "Corinth Canal", "link": "https://wikipedia.org/wiki/Corinth_Canal"}]}, {"year": "1827", "text": "<PERSON><PERSON><PERSON>, Slovenian lawyer and politician (d. 1870)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian lawyer and politician (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian lawyer and politician (d. 1870)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>v<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, Russian physicist and academic (d. 1896)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON>, Kazakh poet, composer, and philosopher (d. 1904)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/A<PERSON>i_Qunanbaiuli\" class=\"mw-redirect\" title=\"A<PERSON>i Qunanbaiuli\"><PERSON><PERSON><PERSON></a>, Kazakh poet, composer, and philosopher (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON>i_Qunan<PERSON>li\" class=\"mw-redirect\" title=\"A<PERSON><PERSON> Qunanba<PERSON>\"><PERSON><PERSON><PERSON></a>, Kazakh poet, composer, and philosopher (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON>i_Qunan<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, Irish-American painter and educator (d. 1892)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American painter and educator (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American painter and educator (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, English inventor, founded British Summer Time (d. 1915)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor, founded <a href=\"https://wikipedia.org/wiki/British_Summer_Time\" title=\"British Summer Time\">British Summer Time</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English inventor, founded <a href=\"https://wikipedia.org/wiki/British_Summer_Time\" title=\"British Summer Time\">British Summer Time</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "British Summer Time", "link": "https://wikipedia.org/wiki/British_Summer_Time"}]}, {"year": "1860", "text": "<PERSON>, Indian singer and musicologist (d. 1936)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer and musicologist (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer and musicologist (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Russian composer, conductor, and educator (d. 1936)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer, conductor, and educator (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian composer, conductor, and educator (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, German pilot and businessman (d. 1954)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and businessman (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and businessman (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON>, English poet, playwright, and scholar (d. 1943)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and scholar (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, playwright, and scholar (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese poet and satirist (d. 1907)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_T%E1%BA%BF_X%C6%B0%C6%A1ng\" title=\"Trần Tế Xương\">Tr<PERSON><PERSON></a>, Vietnamese poet and satirist (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_T%E1%BA%BF_X%C6%B0%C6%A1ng\" title=\"Trần Tế Xương\">Tr<PERSON><PERSON></a>, Vietnamese poet and satirist (d. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_T%E1%BA%BF_X%C6%B0%C6%A1ng"}]}, {"year": "1872", "text": "<PERSON>, American bassist (d. 1972)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, American engineer and politician, 31st President of the United States (d. 1964)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1874", "text": "<PERSON><PERSON><PERSON>, Lithuanian jurist and politician, President of Lithuania (d. 1944)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/Antanas_Smetona\" title=\"An<PERSON>s <PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antanas_Smet<PERSON>\" title=\"An<PERSON>s <PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian jurist and politician, <a href=\"https://wikipedia.org/wiki/President_of_Lithuania\" title=\"President of Lithuania\">President of Lithuania</a> (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antanas_Smetona"}, {"title": "President of Lithuania", "link": "https://wikipedia.org/wiki/President_of_Lithuania"}]}, {"year": "1877", "text": "<PERSON>, American chess player and author (d. 1944)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and author (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(chess_player)\" title=\"<PERSON> (chess player)\"><PERSON></a>, American chess player and author (d. 1944)", "links": [{"title": "<PERSON> (chess player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(chess_player)"}]}, {"year": "1878", "text": "<PERSON>, Polish-German physician and author (d. 1957)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Alfred_D%C3%B6blin\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician and author (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alfred_D%C3%B6blin\" title=\"<PERSON>\"><PERSON></a>, Polish-German physician and author (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alfred_D%C3%B6blin"}]}, {"year": "1880", "text": "<PERSON>, American businessman and politician, Mayor of Dallas (d. 1964)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Dallas\" title=\"Mayor of Dallas\">Mayor of Dallas</a> (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mayor of Dallas", "link": "https://wikipedia.org/wiki/Mayor_of_Dallas"}]}, {"year": "1884", "text": "<PERSON><PERSON>, Romanian journalist and author (d. 1935)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Panait Is<PERSON>ti\"><PERSON><PERSON></a>, Romanian journalist and author (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Panait Is<PERSON>ti\"><PERSON><PERSON></a>, Romanian journalist and author (d. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Panait_Istrati"}]}, {"year": "1888", "text": "<PERSON> of Greece and Denmark (d. 1940)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark\" title=\"Prince <PERSON> of Greece and Denmark\">Prince <PERSON> of Greece and Denmark</a> (d. 1940)", "links": [{"title": "<PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1889", "text": "<PERSON>, American game designer, created <PERSON><PERSON><PERSON> (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Monopoly_(game)\" title=\"Monopoly (game)\">Monopoly</a></i> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer, created <i><a href=\"https://wikipedia.org/wiki/Monopoly_(game)\" title=\"Monopoly (game)\">Monopoly</a></i> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Monopoly (game)", "link": "https://wikipedia.org/wiki/Monopoly_(game)"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Polish writer and member of the WW II Polish Resistance (d. 1968)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Zofia_Kossak-Szczucka\" title=\"Zofia Kossak-Szczucka\"><PERSON><PERSON><PERSON>k-Szczucka</a>, Polish writer and member of the WW II Polish Resistance (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ofia_Kossak-Szczucka\" title=\"<PERSON>ofia Kossak-Szczucka\"><PERSON><PERSON><PERSON>-Sz<PERSON></a>, Polish writer and member of the WW II Polish Resistance (d. 1968)", "links": [{"title": "Zofia Kossak-Szczucka", "link": "https://wikipedia.org/wiki/Zofia_Kossak-Szczucka"}]}, {"year": "1890", "text": "<PERSON>, Canadian lawyer and politician, 12th Premier of Nova Scotia (d. 1954)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 12th <a href=\"https://wikipedia.org/wiki/Premier_of_Nova_Scotia\" title=\"Premier of Nova Scotia\">Premier of Nova Scotia</a> (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Nova Scotia", "link": "https://wikipedia.org/wiki/Premier_of_Nova_Scotia"}]}, {"year": "1894", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, 4th President of India (d. 1980)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/V._V._Giri\" title=\"V. V. Giri\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V._V._Giri\" title=\"V. V. Giri\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_India\" title=\"President of India\">President of India</a> (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V._V._Giri"}, {"title": "President of India", "link": "https://wikipedia.org/wiki/President_of_India"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Australian cricketer (d. 1969)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Love\" title=\"<PERSON><PERSON> Love\"><PERSON><PERSON></a>, Australian cricketer (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Love\" title=\"Ham<PERSON> Love\"><PERSON><PERSON></a>, Australian cricketer (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Love"}]}, {"year": "1897", "text": "<PERSON>, American businessman and philanthropist, founded Darby Dan Farm (d. 1988)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Darby_Dan_Farm\" title=\"Darby Dan Farm\">Darby Dan Farm</a> (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Darby_Dan_Farm\" title=\"Darby Dan Farm\">Darby Dan Farm</a> (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Darby Dan Farm", "link": "https://wikipedia.org/wiki/Darby_Dan_Farm"}]}, {"year": "1897", "text": "<PERSON>, American actor and singer (d. 1979)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, <PERSON>, New Zealand physician and politician, 11th Governor-General of New Zealand (d. 1994)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, New Zealand physician and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, New Zealand physician and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General of New Zealand</a> (d. 1994)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}]}, {"year": "1902", "text": "<PERSON>, Canadian-American actress (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, German-English author and screenwriter (d. 2000)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English author and screenwriter (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-English author and screenwriter (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, Swedish biochemist and academic, Nobel Prize laureate (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1903", "text": "<PERSON>, American author (d. 1978)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American journalist and author (d. 1986)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bell_Thompson"}]}, {"year": "1907", "text": "<PERSON>, Chinese general and politician (d. 1984)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general and politician (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian botanist, historian, and author (d. 2009)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Rica_Erickson\" title=\"Rica Erickson\"><PERSON></a>, Australian botanist, historian, and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rica_Erickson\" title=\"Rica Erickson\"><PERSON></a>, Australian botanist, historian, and author (d. 2009)", "links": [{"title": "<PERSON> Erickson", "link": "https://wikipedia.org/wiki/Rica_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American soccer player (d. 1977)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>alves"}]}, {"year": "1909", "text": "<PERSON>, American businessman, founded Fender Musical Instruments Corporation (d. 1991)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Leo_Fender\" title=\"Leo Fender\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation\" class=\"mw-redirect\" title=\"Fender Musical Instruments Corporation\">Fender Musical Instruments Corporation</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_Fender\" title=\"Leo Fender\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation\" class=\"mw-redirect\" title=\"Fender Musical Instruments Corporation\">Fender Musical Instruments Corporation</a> (d. 1991)", "links": [{"title": "Leo Fender", "link": "https://wikipedia.org/wiki/<PERSON>_Fender"}, {"title": "Fender Musical Instruments Corporation", "link": "https://wikipedia.org/wiki/Fender_Musical_Instruments_Corporation"}]}, {"year": "1909", "text": "<PERSON>, American politician, 45th Governor of New Jersey, and Chief Justice of the New Jersey Supreme Court (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, and Chief Justice of the New Jersey Supreme Court (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 45th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, and Chief Justice of the New Jersey Supreme Court (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}]}, {"year": "1910", "text": "<PERSON>, French racing driver (d. 1954)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French racing driver (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON>, Greek footballer (d. 2011)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON> <PERSON><PERSON>-<PERSON>, English historian and author (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English historian and author (d. 1993)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>-<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Brazilian novelist and poet (d. 2001)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian novelist and poet (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian novelist and poet (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American actor (d. 1994)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Jr.\" title=\"<PERSON> Beery Jr.\"><PERSON>.</a>, American actor (d. 1994)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Estonian-Finnish high jumper and discus thrower (d. 1983)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Finnish high jumper and discus thrower (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-Finnish high jumper and discus thrower (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1914", "text": "<PERSON>, American actor and director (d. 2002)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Argentinian racing driver and polo player (d. 1973)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver and polo player (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian racing driver and polo player (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, English cricketer (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer (d. 1996)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1918", "text": "<PERSON>, American admiral (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American basketball player and coach (d. 1998)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American pop singer and composer (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer and composer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pop singer and composer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American football player and coach (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, American actress (d. 2020)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English cricketer and footballer (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and footballer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Bangladeshi painter and illustrator (d. 1994)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Sultan\"><PERSON></a>, Bangladeshi painter and illustrator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sultan\" title=\"<PERSON> Sultan\"><PERSON></a>, Bangladeshi painter and illustrator (d. 1994)", "links": [{"title": "SM Sultan", "link": "https://wikipedia.org/wiki/SM_Sultan"}]}, {"year": "1924", "text": "<PERSON>, English author (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>,  French philosopher, sociologist, and literary theorist (d. 1998)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>%C3%A7ois_Lyotard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher, sociologist, and literary theorist (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A7ois_Lyotard\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French philosopher, sociologist, and literary theorist (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A7ois_Lyotard"}]}, {"year": "1925", "text": "<PERSON>, English general (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (d. 2020)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, French organist and educator (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French organist and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American mathematician (d. 1972)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American singer and guitarist (d. 2005)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 1988)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Vernon_<PERSON>\" title=\"Vernon Washington\"><PERSON></a>, American actor (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vernon_Washington\" title=\"Vernon Washington\"><PERSON></a>, American actor (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vernon_Washington"}]}, {"year": "1928", "text": "<PERSON>, American singer, actor, and businessman, founded the Jimmy Dean Food Company  (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" title=\"<PERSON> (brand)\">Jimmy Dean Food Company</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, actor, and businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)\" title=\"<PERSON> (brand)\">Jimmy Dean Food Company</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (brand)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brand)"}]}, {"year": "1928", "text": "<PERSON>, American singer and actor (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Italian racing driver (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Italian racing driver (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_(racing_driver)"}]}, {"year": "1928", "text": "<PERSON>, American-Australian actor (d. 2010)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Me<PERSON>urio\"><PERSON></a>, American-Australian actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Me<PERSON>urio\"><PERSON></a>, American-Australian actor (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gus_Mercurio"}]}, {"year": "1930", "text": "<PERSON>, English-Italian author and academic (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian author and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Italian author and academic (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American journalist and activist (d. 2008)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and activist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, English composer and academic", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino cardinal", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Gaud<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aud<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>aud<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aud<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino cardinal", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>auden<PERSON>_<PERSON><PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American poker player (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poker player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Baroness <PERSON>, English lawyer and judge", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>-<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>-<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON><PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1933", "text": "<PERSON>,  American baseball player and sportscaster (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Colavito"}]}, {"year": "1933", "text": "<PERSON>, English engineer, founded <PERSON>sworth (d. 2005)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cosworth"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish wrestler and trainer (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Tevfik_K%C4%B1%C5%9F\" title=\"Tev<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish wrestler and trainer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tevfik_K%C4%B1%C5%9F\" title=\"Tev<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish wrestler and trainer (d. 2019)", "links": [{"title": "Tevfik <PERSON>", "link": "https://wikipedia.org/wiki/Tevfik_K%C4%B1%C5%9F"}]}, {"year": "1935", "text": "<PERSON>, Baron <PERSON>, English politician, Minister of State for the Armed Forces (d. 2018)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces\" title=\"Minister of State for the Armed Forces\">Minister of State for the Armed Forces</a> (d. 2018)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Minister of State for the Armed Forces", "link": "https://wikipedia.org/wiki/Minister_of_State_for_the_Armed_Forces"}]}, {"year": "1935", "text": "<PERSON>, Dutch bishop", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch bishop", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Danish actress", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Russian scholar and politician, Mayor of Saint Petersburg (d. 2000)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian scholar and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Saint_Petersburg\" class=\"mw-redirect\" title=\"Mayor of Saint Petersburg\">Mayor of Saint Petersburg</a> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian scholar and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Saint_Petersburg\" class=\"mw-redirect\" title=\"Mayor of Saint Petersburg\">Mayor of Saint Petersburg</a> (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Mayor of Saint Petersburg", "link": "https://wikipedia.org/wiki/Mayor_of_Saint_Petersburg"}]}, {"year": "1938", "text": "<PERSON>, English author and illustrator", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ross\"><PERSON></a>, English author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English actress (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Mara\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Mara\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kate_O%27Mara"}]}, {"year": "1939", "text": "<PERSON>, American lawyer and politician (d. 2012)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" class=\"mw-redirect\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)\" class=\"mw-redirect\" title=\"<PERSON> (congressman)\"><PERSON></a>, American lawyer and politician (d. 2012)", "links": [{"title": "<PERSON> (congressman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(congressman)"}]}, {"year": "1940", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English sportscaster (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sportscaster (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English swimmer and journalist", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English swimmer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Australian painter and sculptor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American football player (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Duncan"}]}, {"year": "1942", "text": "<PERSON><PERSON>, American fashion designer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English physicist and engineer", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Canadian singer-songwriter and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2005)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American trumpet player and composer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian-Pakistani cricketer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian-Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter (d. 2022)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Scottish-English singer-songwriter and guitarist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Malaysian academic and politician, 10th Prime Minister of Malaysia", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian academic and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian academic and politician, 10th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}]}, {"year": "1947", "text": "<PERSON>, English rugby player and manager", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1947)\" title=\"<PERSON> (rugby union, born 1947)\"><PERSON></a>, English rugby player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1947)\" title=\"<PERSON> (rugby union, born 1947)\"><PERSON></a>, English rugby player and manager", "links": [{"title": "<PERSON> (rugby union, born 1947)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union,_born_1947)"}]}, {"year": "1947", "text": "<PERSON>, English cricketer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1948", "text": "<PERSON>, English actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Austin\" title=\"<PERSON> Austin\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patti_Austin\" title=\"Patti Austin\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patti_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Colombian businessman and politician, 59th President of Colombia", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian businessman and politician, 59th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian businessman and politician, 59th <a href=\"https://wikipedia.org/wiki/President_of_Colombia\" title=\"President of Colombia\">President of Colombia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Colombia", "link": "https://wikipedia.org/wiki/President_of_Colombia"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, German footballer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American screenwriter, actor and comedian", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter, actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American illustrator", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)\" title=\"<PERSON> (illustrator)\"><PERSON></a>, American illustrator", "links": [{"title": "<PERSON> (illustrator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illustrator)"}]}, {"year": "1955", "text": "<PERSON>, American set designer (d. 2013)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American set designer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American set designer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Filipino journalist and talk show host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and talk show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino journalist and talk show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mel_<PERSON>co"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Austrian politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Australian tennis player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Spanish footballer and manager (d. 2013)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON>%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American wrestler", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter, pianist, and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Indian-English businesswoman", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Perween_Warsi\" title=\"Perween Warsi\"><PERSON><PERSON><PERSON></a>, Indian-English businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Perween_Warsi\" title=\"Perween Warsi\"><PERSON><PERSON><PERSON></a>, Indian-English businesswoman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Perween_Warsi"}]}, {"year": "1957", "text": "<PERSON>, American saxophonist, composer, and playwright (d. 2014)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, composer, and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Ho\"><PERSON></a>, American saxophonist, composer, and playwright (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Estonian architect", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_P%C3%B5ime\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B5ime\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andres_P%C3%B5ime"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Pakistani writer, poet, architect and chief editor Urdu Dictionary Board", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani writer, poet, architect and chief editor Urdu Dictionary Board", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani writer, poet, architect and chief editor Urdu Dictionary Board", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American boxer (d. 2012)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English cricketer, coach, and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1958)\" title=\"<PERSON> (cricketer, born 1958)\"><PERSON></a>, English cricketer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1958)\" title=\"<PERSON> (cricketer, born 1958)\"><PERSON></a>, English cricketer, coach, and manager", "links": [{"title": "<PERSON> (cricketer, born 1958)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1958)"}]}, {"year": "1958", "text": "<PERSON>, English nurse and politician, Shadow Leader of the House of Commons", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons\" title=\"Shadow Leader of the House of Commons\">Shadow Leader of the House of Commons</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Shadow Leader of the House of Commons", "link": "https://wikipedia.org/wiki/Shadow_Leader_of_the_House_of_Commons"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American actress, director, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Arquette\" title=\"<PERSON><PERSON> Arquette\"><PERSON><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>tte\" title=\"<PERSON><PERSON> Arquette\"><PERSON><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rosa<PERSON>_A<PERSON>tte"}]}, {"year": "1959", "text": "<PERSON>, Welsh sailor and politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sailor and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh sailor and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English drummer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English drummer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Canadian singer-songwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Florent_Vollant\" title=\"Florent Vollant\">Florent Vollant</a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florent_Vollant\" title=\"Florent Vollant\">Florent Vollant</a>, Canadian singer-songwriter", "links": [{"title": "Florent Vollant", "link": "https://wikipedia.org/wiki/Florent_Vollant"}]}, {"year": "1960", "text": "<PERSON>, Spanish actor and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Estonian sprinter and long jumper", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sprinter and long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian sprinter and long jumper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American golfer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Australian drummer, songwriter, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian drummer, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American author and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician (d. 2001)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Dutch footballer and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English-American journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Danish automotive designer and businessman", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish automotive designer and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish automotive designer and businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Norwegian saxophonist and composer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/K%C3%A5re_Kolve\" title=\"<PERSON><PERSON><PERSON> Kolve\"><PERSON><PERSON><PERSON></a>, Norwegian saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A5re_Kolve\" title=\"<PERSON><PERSON><PERSON> Kolve\"><PERSON><PERSON><PERSON></a>, Norwegian saxophonist and composer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A5re_Kolve"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Japanese singer-songwriter and guitarist (d. 2005)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter and guitarist (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress, singer, writer, and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, writer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, writer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American university leader and sport shooter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American university leader and sport shooter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American university leader and sport shooter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American jockey and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jockey and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American basketball player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English gardener and television host", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, German singer-songwriter and bass player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Hansi_K%C3%BCrsch\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hansi_K%C3%BCrsch\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German singer-songwriter and bass player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hansi_K%C3%BCrsch"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Egyptian footballer and manager", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Belgian footballer and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, American boxer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Dutch cricketer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Scholte\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Scholte\"><PERSON><PERSON><PERSON></a>, Dutch cricketer", "links": [{"title": "<PERSON><PERSON>ut <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>inout_Scholte"}]}, {"year": "1968", "text": "<PERSON>, American singer and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian voice actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American tennis player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, American ice hockey player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_He<PERSON>an\" title=\"<PERSON><PERSON> Hedican\"><PERSON><PERSON></a>, American ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_He<PERSON>an\" title=\"<PERSON><PERSON> Hedican\"><PERSON><PERSON></a>, American ice hockey player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, New Zealand-Australian cricketer and journalist", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian footballer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sal_<PERSON>no"}]}, {"year": "1971", "text": "<PERSON>, Danish singer-songwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Irish footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Cuban boxer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Cuban boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Cuban boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Kindel%C3%A1n"}]}, {"year": "1971", "text": "<PERSON>, English rugby player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American mixed martial artist and wrestler (d. 2016)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mixed martial artist and wrestler (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, South African singer-songwriter and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English rugby player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American model and actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter, guitarist, and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American tennis player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Saudi Arabian director and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saudi Arabian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Saudi Arabian director and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Costa Rican footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Murillo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADn_Murillo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Costa Rican footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Mar%C3%ADn_Murillo"}]}, {"year": "1974", "text": "<PERSON>, American scholar and author", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, French footballer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Turkish footballer and figure skater", "html": "1975 - <a href=\"https://wikipedia.org/wiki/%C4%B0lhan_Mans%C4%B1z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer and figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0lhan_Mans%C4%B1z\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish footballer and figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%B0lhan_Mans%C4%B1z"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Roadkill_(wrestler)\" title=\"Roadkill (wrestler)\">Road<PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roadkill_(wrestler)\" title=\"Roadkill (wrestler)\">Roadkill</a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/Roadkill_(wrestler)"}]}, {"year": "1976", "text": "<PERSON>, Scottish businessman and politician, Shadow Secretary of State for Scotland", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish businessman and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)\" class=\"mw-redirect\" title=\"<PERSON> (British politician)\"><PERSON></a>, Scottish businessman and politician, <a href=\"https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland\" title=\"Shadow Secretary of State for Scotland\">Shadow Secretary of State for Scotland</a>", "links": [{"title": "<PERSON> (British politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_politician)"}, {"title": "Shadow Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Shadow_Secretary_of_State_for_Scotland"}]}, {"year": "1977", "text": "<PERSON>, Irish footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1977", "text": "<PERSON>, English comedian, actor, and radio host", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian, actor, and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, English comedian, actor, and radio host", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)"}]}, {"year": "1978", "text": "<PERSON>, Australian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English cricketer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Sri Lankan cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>wisher\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>wisher\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American author, screenwriter, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, screenwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, French rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON><PERSON><PERSON> (rugby union)\"><PERSON><PERSON><PERSON></a>, French rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A<PERSON><PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON><PERSON><PERSON> (rugby union)\"><PERSON><PERSON><PERSON></a>, French rugby player", "links": [{"title": "<PERSON><PERSON><PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/R%C3%A9<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Slovene physicist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Matja%C5%BE_Perc\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matja%C5%BE_Perc\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovene physicist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matja%C5%BE_Perc"}]}, {"year": "1979", "text": "<PERSON><PERSON>, French racing driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>der\"><PERSON><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>der\"><PERSON><PERSON></a>, French racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>der"}]}, {"year": "1980", "text": "<PERSON>, English boxer, wrestler, and actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, wrestler, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English boxer, wrestler, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Indonesian badminton player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tau<PERSON><PERSON>_Hidayat\" title=\"Taufi<PERSON> Hidayat\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hidayat\" title=\"Taufi<PERSON> Hidayat\"><PERSON><PERSON><PERSON></a>, Indonesian badminton player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Hidayat"}]}, {"year": "1982", "text": "<PERSON>, Swedish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>vb%C3%A5ge\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>b%C3%A5ge\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_Alvb%C3%A5ge"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1982", "text": "<PERSON>, Brazilian actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English snooker player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American soccer player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1983", "text": "<PERSON><PERSON> <PERSON><PERSON>, American mixed martial artist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_Dollaway\" title=\"<PERSON><PERSON> <PERSON><PERSON> Dollaway\"><PERSON><PERSON> <PERSON><PERSON></a>, American mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_Dollaway\" title=\"<PERSON>. B<PERSON> Dollaway\"><PERSON><PERSON> <PERSON><PERSON></a>, American mixed martial artist", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Spanish motorcycle racer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>aubel\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9ctor_<PERSON>aubel\" title=\"<PERSON><PERSON><PERSON>aubel\"><PERSON><PERSON><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9ctor_F<PERSON>bel"}]}, {"year": "1983", "text": "<PERSON>, Russian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1983)\" title=\"<PERSON><PERSON> (ice hockey, born 1983)\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1983)\" title=\"<PERSON><PERSON> (ice hockey, born 1983)\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON> (ice hockey, born 1983)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(ice_hockey,_born_1983)"}]}, {"year": "1984", "text": "<PERSON>, American actor and composer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese model and actor", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese model and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mo<PERSON><PERSON><PERSON>_<PERSON>ami"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English cricketer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ik\" title=\"<PERSON><PERSON> Naik\"><PERSON><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ik\" title=\"<PERSON><PERSON> Na<PERSON>\"><PERSON><PERSON></a>, English cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Naik"}]}, {"year": "1984", "text": "<PERSON>, American football player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Italian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, Irish footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donovan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Donovan\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Donovan"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON>, Mongolian sumo wrestler", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Kakury%C5%AB_Rikisabur%C5%8D\" title=\"Ka<PERSON><PERSON><PERSON> Rikisaburō\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ka<PERSON>ry%C5%AB_Rikisabur%C5%8D\" title=\"Ka<PERSON><PERSON><PERSON> Rikisaburō\"><PERSON><PERSON><PERSON><PERSON></a>, Mongolian sumo wrestler", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON>ry%C5%AB_Rikisabur%C5%8D"}]}, {"year": "1985", "text": "<PERSON>, Estonian tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julia_<PERSON>nik"}]}, {"year": "1986", "text": "<PERSON>, Czech tennis player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1%C4%8Dkov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1%C4%8Dkov%C3%A1\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andrea_Hlav%C3%A1%C4%8Dkov%C3%A1"}]}, {"year": "1987", "text": "<PERSON>, Dutch singer and actor", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch singer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, New Zealand actor and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Boy<PERSON>\" title=\"Ari Boyland\"><PERSON></a>, New Zealand actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Boyland\" title=\"Ari Boyland\"><PERSON></a>, New Zealand actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Boyland"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Israeli footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Australian actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Brenton_Thwaites\" title=\"Brenton Thwaites\"><PERSON><PERSON></a>, Australian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brenton_Thwaites\" title=\"Brenton Thwaites\"><PERSON><PERSON></a>, Australian actor", "links": [{"title": "Brenton Thwaites", "link": "https://wikipedia.org/wiki/Brenton_Thwaites"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Australian rugby player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>Nau\"><PERSON><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Ah-Nau\"><PERSON><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>u"}]}, {"year": "1990", "text": "<PERSON>, South Korean model, actress, and singer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yung\" title=\"<PERSON>yung\"><PERSON></a>, South Korean model, actress, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>yung\"><PERSON></a>, South Korean model, actress, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Icelandic footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Dagn%C3%BD_Brynjarsd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dagn%C3%BD_Brynjarsd%C3%B3ttir\" title=\"<PERSON><PERSON><PERSON>tti<PERSON>\"><PERSON><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dagn%C3%BD_Brynjarsd%C3%B3ttir"}]}, {"year": "1991", "text": "<PERSON>, American-Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Greek footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian cricketer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1992", "text": "<PERSON>, American baseball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1992", "text": "<PERSON>, American YouTuber and television host", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American YouTuber and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English racing driver", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American basketball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Portuguese footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON>, American football player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Scottish netball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish netball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American television personality and businesswoman", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television personality and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jen<PERSON>\"><PERSON></a>, American television personality and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Italian motorcycle rider", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle rider", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle rider", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American basketball player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Japanese racing driver", "html": "1999 - <a href=\"https://wikipedia.org/wiki/R<PERSON><PERSON>_Miyata\" title=\"Ritomo Miyata\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Mi<PERSON>\" title=\"Rito<PERSON> Miyata\"><PERSON><PERSON><PERSON></a>, Japanese racing driver", "links": [{"title": "Rito<PERSON>", "link": "https://wikipedia.org/wiki/Ritomo_Mi<PERSON>ta"}]}, {"year": "1999", "text": "<PERSON>, Canadian ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American soccer player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer,_born_2000)\" class=\"mw-redirect\" title=\"<PERSON> (soccer, born 2000)\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer,_born_2000)\" class=\"mw-redirect\" title=\"<PERSON> (soccer, born 2000)\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON> (soccer, born 2000)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer,_born_2000)"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Estonian racing driver", "html": "2000 - <a href=\"https://wikipedia.org/wiki/J%C3%BCri_Vips\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCri_Vips\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCri_Vips"}]}], "Deaths": [{"year": "258", "text": "<PERSON> of Rome, Spanish-Italian deacon and saint (b. 225)", "html": "258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rome\" class=\"mw-redirect\" title=\"<PERSON> of Rome\"><PERSON> of Rome</a>, Spanish-Italian deacon and saint (b. 225)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rome\" class=\"mw-redirect\" title=\"<PERSON> of Rome\"><PERSON> of Rome</a>, Spanish-Italian deacon and saint (b. 225)", "links": [{"title": "Lawrence of Rome", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Rome"}]}, {"year": "794", "text": "<PERSON><PERSON>, Frankish noblewoman (b. 765)", "html": "794 - <a href=\"https://wikipedia.org/wiki/Fastrada\" title=\"Fastrada\"><PERSON><PERSON></a>, Frankish noblewoman (b. 765)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fastrada\" title=\"Fastrada\"><PERSON><PERSON></a>, Frankish noblewoman (b. 765)", "links": [{"title": "Fastrada", "link": "https://wikipedia.org/wiki/Fastrada"}]}, {"year": "796", "text": "<PERSON><PERSON><PERSON><PERSON>, archbishop of York", "html": "796 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(died_796)\" title=\"<PERSON><PERSON><PERSON><PERSON> (died 796)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_York\" title=\"Diocese of York\">York</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(died_796)\" title=\"<PERSON><PERSON><PERSON><PERSON> (died 796)\"><PERSON><PERSON><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Diocese_of_York\" title=\"Diocese of York\">York</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (died 796)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(died_796)"}, {"title": "Diocese of York", "link": "https://wikipedia.org/wiki/Diocese_of_York"}]}, {"year": "847", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> caliph (b. 816)", "html": "847 - <a href=\"https://wikipedia.org/wiki/Al-Wathiq\" title=\"Al-Wathiq\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Wathiq\" title=\"Al-Wathiq\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> caliph (b. 816)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Al-Wathiq"}]}, {"year": "955", "text": "<PERSON> ('the <PERSON>'), duke of Lorraine", "html": "955 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON></a> ('the <PERSON>'), duke of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lorraine</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON></a> ('the <PERSON>'), duke of <a href=\"https://wikipedia.org/wiki/Lotharingia\" title=\"Lotharingia\">Lorraine</a>", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Lorraine"}, {"title": "Lotharingia", "link": "https://wikipedia.org/wiki/Lotharingia"}]}, {"year": "1241", "text": "<PERSON>, Fair Maid of Brittany (b. 1184)", "html": "1241 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Fair_Maid_of_Brittany\" title=\"<PERSON>, Fair Maid of Brittany\"><PERSON>, Fair Maid of Brittany</a> (b. 1184)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_Maid_of_Brittany\" title=\"<PERSON>, Fair Maid of Brittany\"><PERSON>, Fair Maid of Brittany</a> (b. 1184)", "links": [{"title": "<PERSON>, Fair Maid of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_Maid_of_Brittany"}]}, {"year": "1250", "text": "<PERSON> of Denmark (b. 1216)", "html": "1250 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1216)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a> (b. 1216)", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1284", "text": "<PERSON><PERSON><PERSON>, Khan of the Mongol Ilkhanate", "html": "1284 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Khan of the <a href=\"https://wikipedia.org/wiki/Mongol\" class=\"mw-redirect\" title=\"Mongol\">Mongol</a> <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Ilkhanate</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Khan of the <a href=\"https://wikipedia.org/wiki/Mongol\" class=\"mw-redirect\" title=\"Mongol\">Mongol</a> <a href=\"https://wikipedia.org/wiki/Ilkhanate\" title=\"Ilkhanate\">Ilkhanate</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Mongol", "link": "https://wikipedia.org/wiki/Mongol"}, {"title": "Ilkhanate", "link": "https://wikipedia.org/wiki/Ilkhanate"}]}, {"year": "1316", "text": "<PERSON><PERSON> mac <PERSON>, King of Connacht", "html": "1316 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>%27Connor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of Connacht", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>%27Connor\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, King of Connacht", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_O%27Connor"}]}, {"year": "1322", "text": "<PERSON> of La Verna, Italian ascetic (b. 1259)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_La_Verna\" title=\"<PERSON> of La Verna\"><PERSON> of La Verna</a>, Italian ascetic (b. 1259)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_La_Verna\" title=\"<PERSON> of La Verna\"><PERSON> of La Verna</a>, Italian ascetic (b. 1259)", "links": [{"title": "<PERSON> of La Verna", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>a"}]}, {"year": "1410", "text": "<PERSON>, Duke of Bourbon (b. 1337)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1337)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a> (b. 1337)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1535", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian cardinal (b. 1509)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1509)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%27_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Italian cardinal (b. 1509)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ippolito_de%27_Medici"}]}, {"year": "1536", "text": "<PERSON>, Duke of Brittany, <PERSON><PERSON><PERSON> of France, Brother of <PERSON> (b. 1518)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a>, <PERSON><PERSON><PERSON> of France, Brother of <PERSON> (b. 1518)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a>, <PERSON><PERSON><PERSON> of France, Brother of <PERSON> (b. 1518)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1653", "text": "<PERSON><PERSON><PERSON>, Dutch admiral (b. 1598)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/Maarten_Tromp\" title=\"Maarten Tromp\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maarten_Tromp\" title=\"Maarten Tromp\"><PERSON><PERSON><PERSON></a>, Dutch admiral (b. 1598)", "links": [{"title": "Ma<PERSON>n <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>n_Tromp"}]}, {"year": "1655", "text": "<PERSON>, 1st Marquis of Bedmar, Spanish cardinal and diplomat (b. 1572)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Bedmar\" class=\"mw-redirect\" title=\"<PERSON>, 1st Marquis of Bedmar\"><PERSON>, 1st Marquis of Bedmar</a>, Spanish cardinal and diplomat (b. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Bedmar\" class=\"mw-redirect\" title=\"<PERSON>, 1st Marquis of Bedmar\"><PERSON>, 1st Marquis of Bedmar</a>, Spanish cardinal and diplomat (b. 1572)", "links": [{"title": "<PERSON>, 1st Marquis of Bedmar", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Marquis_of_Bedmar"}]}, {"year": "1660", "text": "<PERSON><PERSON><PERSON>, 2nd Duke of Richmond (b. 1649)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_2nd_Duke_of_Richmond\" title=\"<PERSON><PERSON><PERSON>, 2nd Duke of Richmond\"><PERSON><PERSON><PERSON>, 2nd Duke of Richmond</a> (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_2nd_Duke_of_Richmond\" title=\"<PERSON><PERSON><PERSON>, 2nd Duke of Richmond\"><PERSON><PERSON><PERSON>, 2nd Duke of Richmond</a> (b. 1649)", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Duke of Richmond", "link": "https://wikipedia.org/wiki/Esm%C3%A<PERSON>_<PERSON>,_2nd_Duke_<PERSON>_Richmond"}]}, {"year": "1723", "text": "<PERSON>, French cardinal and politician, French Secretary of State for Foreign Affairs (b. 1656)", "html": "1723 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Secretary of State for Foreign Affairs</a> (b. 1656)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)\" class=\"mw-redirect\" title=\"Ministry of Foreign Affairs and International Development (France)\">French Secretary of State for Foreign Affairs</a> (b. 1656)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ministry of Foreign Affairs and International Development (France)", "link": "https://wikipedia.org/wiki/Ministry_of_Foreign_Affairs_and_International_Development_(France)"}]}, {"year": "1759", "text": "<PERSON> of Spain (b. 1713)", "html": "1759 - <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_VI_of_Spain\" class=\"mw-redirect\" title=\"Ferdinand VI of Spain\"><PERSON> VI of Spain</a> (b. 1713)", "links": [{"title": "<PERSON> of Spain", "link": "https://wikipedia.org/wiki/Ferdinand_VI_of_Spain"}]}, {"year": "1784", "text": "<PERSON>, Scottish-English painter (b. 1713)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish-English painter (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, Scottish-English painter (b. 1713)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1796", "text": "<PERSON><PERSON><PERSON>, Austrian nobleman and government official (b. 1759)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Austrian nobleman and government official (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a>, Austrian nobleman and government official (b. 1759)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, German-Russian philosopher and academic (b. 1724)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Russian philosopher and academic (b. 1724)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Russian philosopher and academic (b. 1724)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, Austrian composer and educator (b. 1737)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and educator (b. 1737)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and educator (b. 1737)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1839", "text": "Sir <PERSON>, 5th Baronet, English lawyer and politician (b. 1758)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_5th_Baronet\" title=\"Sir <PERSON>, 5th Baronet\">Sir <PERSON>, 5th Baronet</a>, English lawyer and politician (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_5th_Baronet\" title=\"Sir <PERSON>, 5th Baronet\">Sir <PERSON>, 5th Baronet</a>, English lawyer and politician (b. 1758)", "links": [{"title": "Sir <PERSON>, 5th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_5th_Baronet"}]}, {"year": "1862", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese Go player (b. 1829)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/Hon%27inb%C5%8D_Sh%C5%ABsaku\" title=\"Hon'inbō Shūsaku\">Hon'inbō Shūsaku</a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hon%27inb%C5%8D_Sh%C5%ABsaku\" title=\"Hon'inbō Shūsaku\">Hon'inbō Shūsaku</a>, Japanese <a href=\"https://wikipedia.org/wiki/Go_(game)\" title=\"Go (game)\">Go</a> player (b. 1829)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hon%27inb%C5%8D_Sh%C5%ABsaku"}, {"title": "Go (game)", "link": "https://wikipedia.org/wiki/Go_(game)"}]}, {"year": "1875", "text": "<PERSON>, German geographer and journalist (b. 1808)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and journalist (b. 1808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and journalist (b. 1808)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, German pathologist and anatomist (b. 1831)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6ttcher\" title=\"<PERSON>\"><PERSON></a>, German pathologist and anatomist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%B6ttcher\" title=\"<PERSON>\"><PERSON></a>, German pathologist and anatomist (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arthur_B%C3%B6ttcher"}]}, {"year": "1890", "text": "<PERSON>, Irish-born poet, journalist and fiction writer (b. 1844)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Irish-born poet, journalist and fiction writer (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Reilly\" title=\"<PERSON>\"><PERSON></a>, Irish-born poet, journalist and fiction writer (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Reilly"}]}, {"year": "1896", "text": "<PERSON>, German pilot and engineer (b. 1848)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pilot and engineer (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, French lawyer and politician, 68th Prime Minister of France (b. 1846)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 68th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1913", "text": "<PERSON>, Finnish author (b. 1869)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish author (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish author (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English physicist and engineer (b. 1887)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and engineer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American inventor (b. 1844)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, German lieutenant and pilot (b. 1897)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant and pilot (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_L%C3%B<PERSON><PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-Austrian physician and academic (b. 1835)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and academic (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%81d%C3%A1<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian-Austrian physician and academic (b. 1835)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%81d%C3%A1m_Politzer"}]}, {"year": "1922", "text": "<PERSON>, Irish Republican, executed for the killing of Sir <PERSON>", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, executed for the killing of <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, executed for the killing of <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}, {"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1922", "text": "<PERSON>, Irish Republican, executed for the killing of Sir <PERSON>", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Sullivan\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, executed for the killing of <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>27Sullivan\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a>, executed for the killing of <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Sullivan"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}, {"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet"}]}, {"year": "1929", "text": "<PERSON>, French mathematician and astronomer (b. 1878)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and astronomer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON>, Dutch physician (b. 1854)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch physician (b. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, American acting dog (b. 1918)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>_<PERSON>\" title=\"Rin Tin Tin\"><PERSON><PERSON> <PERSON></a>, American acting dog (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>_<PERSON>_<PERSON>\" title=\"Rin Tin Tin\"><PERSON><PERSON> <PERSON></a>, American acting dog (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rin_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, Welsh-Australian politician, 4th Premier of Western Australia (b. 1850)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1850)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alf_Morgans"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1945", "text": "<PERSON>, American physicist and engineer (b. 1882)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and engineer (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Japanese-American historian, author, and academic (b. 1873)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Kan%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American historian, author, and academic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%27<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>'<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Japanese-American historian, author, and academic (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%27ichi_<PERSON><PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Scottish footballer and coach (b. 1870)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Scottish footballer and coach (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)\" title=\"<PERSON> (soccer)\"><PERSON></a>, Scottish footballer and coach (b. 1870)", "links": [{"title": "<PERSON> (soccer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soccer)"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English clergyman and author (b. 1880)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Mont<PERSON>_Summers\" title=\"Montague Summers\"><PERSON><PERSON></a>, English clergyman and author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mont<PERSON>_Summers\" title=\"Montague Summers\"><PERSON><PERSON></a>, English clergyman and author (b. 1880)", "links": [{"title": "Montague Summers", "link": "https://wikipedia.org/wiki/Montague_Summers"}]}, {"year": "1949", "text": "<PERSON>, American chemist (b. 1892)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American-born British actor (b. 1900)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American-born British actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American-born British actor (b. 1900)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and manager (b. 1910)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON>, Ottoman princess (b. 1887)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Hamide_Ay%C5%9Fe_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman princess (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ham<PERSON>_Ay%C5%9Fe_Sultan\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ottoman princess (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hamide_Ay%C5%9Fe_Sultan"}]}, {"year": "1961", "text": "<PERSON>, American author (b. 1880)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician (b. 1903)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>uver"}]}, {"year": "1963", "text": "<PERSON>, Swiss lawyer and jurist (b. 1877)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and jurist (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and jurist (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Hungarian author (b. 1899)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Kodol%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Kodol%C3%A1nyi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Kodol%C3%A1nyi"}]}, {"year": "1976", "text": "<PERSON>, Australian cricketer (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor and singer (b. 1910)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German physicist and academic (b. 1889)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Pakistani general and politician, 3rd President of Pakistan (b. 1917)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani general and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Pakistan\" title=\"President of Pakistan\">President of Pakistan</a> (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Pakistan", "link": "https://wikipedia.org/wiki/President_of_Pakistan"}]}, {"year": "1982", "text": "<PERSON>, Brazilian author and poet (b. 1962)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian author and poet (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Herzer\"><PERSON></a>, Brazilian author and poet (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Bigode_Herzer"}]}, {"year": "1985", "text": "<PERSON>, American football player and sergeant (b. 1906)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sergeant (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sergeant (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>-<PERSON>, Greek lawyer and politician, 163rd Prime Minister of Greece (b. 1893)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 163rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek lawyer and politician, 163rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Georgios_Athanasiadis-Novas"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Vietnamese poet and playwright (b. 1912)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/L%C6%B0u_Tr%E1%BB%8Dng_L%C6%B0\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese poet and playwright (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C6%B0u_Tr%E1%BB%8Dng_L%C6%B0\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Vietnamese poet and playwright (b. 1912)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C6%B0u_Tr%E1%BB%8Dng_L%C6%B0"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Norwegian singer, guitarist, and producer (b. 1968)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Euronymous\" title=\"Euronymous\">Eurony<PERSON></a>, Norwegian singer, guitarist, and producer (b. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Euronymous\" title=\"Euronymous\">Euronymous</a>, Norwegian singer, guitarist, and producer (b. 1968)", "links": [{"title": "Euronymous", "link": "https://wikipedia.org/wiki/Euronymous"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Canadian director and screenwriter (b. 1953)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian director and screenwriter (b. 1953)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American-Mexican pianist and composer (b. 1912)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Mexican pianist and composer (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Mexican pianist and composer (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, English chef and television presenter (b. 1928)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television presenter (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chef and television presenter (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Indian historian, scholar, and critic (b. 1899)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Upadhyaya\" title=\"<PERSON><PERSON><PERSON> Upadhyaya\"><PERSON><PERSON><PERSON></a>, Indian historian, scholar, and critic (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Upadhyaya\" title=\"<PERSON><PERSON><PERSON> Upadhyaya\"><PERSON><PERSON><PERSON></a>, Indian historian, scholar, and critic (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>ya"}]}, {"year": "2000", "text": "<PERSON>, Welsh cricketer and rugby player (b. 1925)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>house\"><PERSON></a>, Welsh cricketer and rugby player (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Gilbert <PERSON>house\"><PERSON></a>, Welsh cricketer and rugby player (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gilbert_Parkhouse"}]}, {"year": "2001", "text": "<PERSON>, American baseball player and manager (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1962)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Norwegian computer scientist and politician (b. 1926)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer scientist and politician (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer scientist and politician (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, American lieutenant and pilot (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>hler\"><PERSON></a>, American lieutenant and pilot (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Bohler\"><PERSON></a>, American lieutenant and pilot (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>hler"}]}, {"year": "2007", "text": "<PERSON>, American lawyer and religious leader (b. 1920)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and religious leader (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and religious leader (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, French race car driver and pilot, founded Alpine (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9d%C3%A9l%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver and pilot, founded <a href=\"https://wikipedia.org/wiki/Alpine_(automobile)\" class=\"mw-redirect\" title=\"Alpine (automobile)\">Alpine</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9d%C3%A9l%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French race car driver and pilot, founded <a href=\"https://wikipedia.org/wiki/Alpine_(automobile)\" class=\"mw-redirect\" title=\"Alpine (automobile)\">Alpine</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9d%C3%A9l%C3%A9"}, {"title": "Alpine (automobile)", "link": "https://wikipedia.org/wiki/Alpine_(automobile)"}]}, {"year": "2007", "text": "<PERSON>, English journalist, producer, and manager, co-founded Factory Records (b. 1950)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, producer, and manager, co-founded <a href=\"https://wikipedia.org/wiki/Factory_Records\" title=\"Factory Records\">Factory Records</a> (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist, producer, and manager, co-founded <a href=\"https://wikipedia.org/wiki/Factory_Records\" title=\"Factory Records\">Factory Records</a> (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Factory Records", "link": "https://wikipedia.org/wiki/Factory_Records"}]}, {"year": "2008", "text": "<PERSON>, American singer-songwriter, pianist, producer, and actor (b. 1942)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, producer, and actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, pianist, producer, and actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, German-Swiss businessman (b. 1948)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss businessman (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English footballer (b. 1978)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American director and producer (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1925)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French race car driver (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Romanian general and pilot (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian general and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian general and pilot (b. 1916)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American producer and manager (b. 1911)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer and manager (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American general and pilot (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Italian special effects artist (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian special effects artist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian special effects artist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American judge and politician, 12th United States National Security Advisor (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American judge and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jr.\"><PERSON> Jr.</a>, American judge and politician, 12th <a href=\"https://wikipedia.org/wiki/United_States_National_Security_Advisor\" class=\"mw-redirect\" title=\"United States National Security Advisor\">United States National Security Advisor</a> (b. 1931)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>."}, {"title": "United States National Security Advisor", "link": "https://wikipedia.org/wiki/United_States_National_Security_Advisor"}]}, {"year": "2013", "text": "<PERSON>, Australian historian and academic (b. 1941)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, Australian historian and academic (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, Australian historian and academic (b. 1941)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American singer and actress (b. 1928)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>rm%C3%A9"}]}, {"year": "2013", "text": "<PERSON>, American general (b. 1921)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American singer and guitarist (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American author (b. 1955)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and scout (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Command\" title=\"Jim Command\"><PERSON></a>, American baseball player and scout (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jim_Command\" title=\"Jim Command\"><PERSON></a>, American baseball player and scout (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Command"}]}, {"year": "2014", "text": "<PERSON><PERSON>, American journalist and academic (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and academic (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and academic (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English mathematician, astronomer, and politician, Lord Mayor of Manchester (b. 1912)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, astronomer, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Manchester\" class=\"mw-redirect\" title=\"Lord Mayor of Manchester\">Lord Mayor of Manchester</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician, astronomer, and politician, <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_Manchester\" class=\"mw-redirect\" title=\"Lord Mayor of Manchester\">Lord Mayor of Manchester</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Mayor of Manchester", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_Manchester"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American race car driver and sportscaster (b. 1941)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Hungarian physician, geneticist, and academic (b. 1935)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian physician, geneticist, and academic (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Hungarian physician, geneticist, and academic (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Norwegian footballer and coach (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian footballer and coach (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Belgian author and poet (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Eriek_Verpale\" title=\"<PERSON><PERSON> Verpale\"><PERSON><PERSON></a>, Belgian author and poet (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erie<PERSON>_Verpale\" title=\"<PERSON><PERSON> Verpale\"><PERSON><PERSON></a>, Belgian author and poet (b. 1952)", "links": [{"title": "Eriek Verpale", "link": "https://wikipedia.org/wiki/Eriek_Verpale"}]}, {"year": "2017", "text": "<PERSON>, German-Pakistani doctor and nun (b. 1929)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Pakistani doctor and nun (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Pakistani doctor and nun (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American financier (b. 1953)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American financier (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Canadian-American ice hockey player (b. 1943)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish actor, musician and comedian (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/Ves<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Vesa-<PERSON><PERSON>\">V<PERSON><PERSON>-<PERSON><PERSON></a>, Finnish actor, musician and comedian (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ves<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"Vesa-<PERSON><PERSON>\">V<PERSON><PERSON>-<PERSON><PERSON></a>, Finnish actor, musician and comedian (b. 1945)", "links": [{"title": "Vesa-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vesa-<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON>, American voice actress and scriptwriter (b. 1978)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actress and scriptwriter (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American voice actress and scriptwriter (b. 1978)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American model and actress (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actress (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}