{"date": "August 27", "url": "https://wikipedia.org/wiki/August_27", "data": {"Events": [{"year": "410", "text": "The sacking of Rome by the Visigoths ends after three days.", "html": "410 - The <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(410)\" title=\"Sack of Rome (410)\">sacking</a> of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> by the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> ends after three days.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(410)\" title=\"Sack of Rome (410)\">sacking</a> of <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">Rome</a> by the <a href=\"https://wikipedia.org/wiki/Visigoths\" title=\"Visigoths\">Visigoths</a> ends after three days.", "links": [{"title": "Sack of Rome (410)", "link": "https://wikipedia.org/wiki/Sack_of_Rome_(410)"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}, {"title": "Visigoths", "link": "https://wikipedia.org/wiki/Visigoths"}]}, {"year": "1172", "text": "<PERSON> the Young King and <PERSON> of France are crowned junior king and queen of England.", "html": "1172 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Young_King\" title=\"<PERSON> the Young King\"><PERSON> the Young King</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_of_France,_Queen_of_England_and_Hungary\" title=\"<PERSON> of France, Queen of England and Hungary\"><PERSON> of France</a> are crowned junior king and queen of England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Young_King\" title=\"<PERSON> the Young King\"><PERSON> the Young King</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Queen_of_England_and_Hungary\" title=\"<PERSON> of France, Queen of England and Hungary\"><PERSON> of France</a> are crowned junior king and queen of England.", "links": [{"title": "<PERSON> the Young King", "link": "https://wikipedia.org/wiki/<PERSON>_the_Young_King"}, {"title": "<PERSON> of France, Queen of England and Hungary", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_France,_Queen_of_England_and_Hungary"}]}, {"year": "1232", "text": "<PERSON><PERSON><PERSON> of the Kamakura shogunate promulgates the Goseibai Shikimoku, the first Japanese legal code governing the samurai class.", "html": "1232 - <a href=\"https://wikipedia.org/wiki/Shikken\" title=\"Shikken\">Shikken</a> <a href=\"https://wikipedia.org/wiki/Hojo_Yasutoki\" class=\"mw-redirect\" title=\"<PERSON><PERSON>toki\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a> promulgates the <a href=\"https://wikipedia.org/wiki/Goseibai_Shikimoku\" title=\"Goseibai Shikimoku\">Goseibai Shikimoku</a>, the first <a href=\"https://wikipedia.org/wiki/Law_of_Japan\" title=\"Law of Japan\">Japanese legal code</a> governing the <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> class.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shikken\" title=\"Shikken\">Shikken</a> <a href=\"https://wikipedia.org/wiki/Hojo_Yasutoki\" class=\"mw-redirect\" title=\"<PERSON><PERSON>tok<PERSON>\"><PERSON><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Kamakura_shogunate\" title=\"Kamakura shogunate\">Kamakura shogunate</a> promulgates the <a href=\"https://wikipedia.org/wiki/Goseibai_Shikimoku\" title=\"Goseibai Shikimoku\">Goseibai Shikimoku</a>, the first <a href=\"https://wikipedia.org/wiki/Law_of_Japan\" title=\"Law of Japan\">Japanese legal code</a> governing the <i><a href=\"https://wikipedia.org/wiki/Samurai\" title=\"Samurai\">samurai</a></i> class.", "links": [{"title": "Shikken", "link": "https://wikipedia.org/wiki/Shikken"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>i"}, {"title": "Kamakura shogunate", "link": "https://wikipedia.org/wiki/Kamakura_shogunate"}, {"title": "Gosei<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Goseibai_Shi<PERSON>oku"}, {"title": "Law of Japan", "link": "https://wikipedia.org/wiki/Law_of_Japan"}, {"title": "Samurai", "link": "https://wikipedia.org/wiki/Samurai"}]}, {"year": "1353", "text": "War of the Straits and Sardinian-Aragonese war: The Battle of Alghero results in a crushing victory of the allied Aragonese and Venetian fleet over the Genoese fleet, most of which is captured.", "html": "1353 - <a href=\"https://wikipedia.org/wiki/War_of_the_Straits\" title=\"War of the Straits\">War of the Straits</a> and <a href=\"https://wikipedia.org/wiki/Sardinian%E2%80%93Aragonese_war\" title=\"Sardinian-Aragonese war\">Sardinian-Aragonese war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alghero\" title=\"Battle of Alghero\">Battle of Alghero</a> results in a crushing victory of the allied Aragonese and Venetian fleet over the Genoese fleet, most of which is captured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_the_Straits\" title=\"War of the Straits\">War of the Straits</a> and <a href=\"https://wikipedia.org/wiki/Sardinian%E2%80%93Aragonese_war\" title=\"Sardinian-Aragonese war\">Sardinian-Aragonese war</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Alghero\" title=\"Battle of Alghero\">Battle of Alghero</a> results in a crushing victory of the allied Aragonese and Venetian fleet over the Genoese fleet, most of which is captured.", "links": [{"title": "War of the Straits", "link": "https://wikipedia.org/wiki/War_of_the_Straits"}, {"title": "Sardinian-Aragonese war", "link": "https://wikipedia.org/wiki/Sardinian%E2%80%93Aragonese_war"}, {"title": "Battle of Alghero", "link": "https://wikipedia.org/wiki/Battle_of_Alghero"}]}, {"year": "1557", "text": "The Battle of St. Quentin results in <PERSON> becoming Duke of Savoy.", "html": "1557 - The <a href=\"https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)\" title=\"Battle of St. Quentin (1557)\">Battle of St. Quentin</a> results in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a> becoming Duke of Savoy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)\" title=\"Battle of St. Quentin (1557)\">Battle of St. Quentin</a> results in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON></a> becoming Duke of Savoy.", "links": [{"title": "Battle of St. Quentin (1557)", "link": "https://wikipedia.org/wiki/Battle_of_St._<PERSON>_(1557)"}, {"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Savoy"}]}, {"year": "1593", "text": "<PERSON> failed an attempt to assassinate <PERSON> of France.", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a> failed an attempt to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"Henry IV of France\"><PERSON> of France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a> failed an attempt to assassinate <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_France\" title=\"Henry IV of France\"><PERSON> of France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_Barri%C3%A8re"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_France"}]}, {"year": "1597", "text": "Jeongyu War: Battle of Chilcheollyang: A Japanese fleet of 500 ships destroys Joseon commander <PERSON>'s fleet of 200 ships at Chilcheollyang.", "html": "1597 - <a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%931598)#Chongyu_War:_Japanese_second_invasion_(1597-1598)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-1598)\">Jeongyu War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chilcheollyang\" title=\"Battle of Chilcheollyang\">Battle of Chilcheollyang</a>: A Japanese fleet of 500 ships destroys <a href=\"https://wikipedia.org/wiki/Joseon_dynasty\" class=\"mw-redirect\" title=\"Joseon dynasty\">Joseon</a> commander <a href=\"https://wikipedia.org/wiki/Won_Gyun\" class=\"mw-redirect\" title=\"Won Gyun\">Won Gyun</a>'s fleet of 200 ships at Chilcheollyang.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%931598)#Chongyu_War:_Japanese_second_invasion_(1597-1598)\" class=\"mw-redirect\" title=\"Japanese invasions of Korea (1592-1598)\">Jeongyu War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Chilcheollyang\" title=\"Battle of Chilcheollyang\">Battle of Chilcheollyang</a>: A Japanese fleet of 500 ships destroys <a href=\"https://wikipedia.org/wiki/Joseon_dynasty\" class=\"mw-redirect\" title=\"Joseon dynasty\">Joseon</a> commander <a href=\"https://wikipedia.org/wiki/Won_Gyun\" class=\"mw-redirect\" title=\"Won Gyun\">Won Gyun</a>'s fleet of 200 ships at Chilcheollyang.", "links": [{"title": "Japanese invasions of Korea (1592-1598)", "link": "https://wikipedia.org/wiki/Japanese_invasions_of_Korea_(1592%E2%80%931598)#Chongyu_War:_Japanese_second_invasion_(1597-1598)"}, {"title": "Battle of Chilcheollyang", "link": "https://wikipedia.org/wiki/Battle_of_Chilcheollyang"}, {"title": "Joseon dynasty", "link": "https://wikipedia.org/wiki/Joseon_dynasty"}, {"title": "<PERSON>yun", "link": "https://wikipedia.org/wiki/Won_Gyun"}]}, {"year": "1600", "text": "<PERSON><PERSON><PERSON>'s Western Army commences the Siege of Fushimi Castle, which is lightly defended by a much smaller Tokugawa garrison led by <PERSON><PERSON>.", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>tsunari\" title=\"<PERSON><PERSON><PERSON>tsunar<PERSON>\"><PERSON><PERSON><PERSON></a>'s Western Army commences the <a href=\"https://wikipedia.org/wiki/Siege_of_Fushimi_Castle\" title=\"Siege of Fushimi Castle\">Siege of Fushimi Castle</a>, which is lightly defended by a much smaller <a href=\"https://wikipedia.org/wiki/Tokugawa_clan\" title=\"Tokugawa clan\">Tokugawa</a> garrison led by <a href=\"https://wikipedia.org/wiki/Torii_<PERSON>totada\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>tsunar<PERSON>\"><PERSON><PERSON><PERSON></a>'s Western Army commences the <a href=\"https://wikipedia.org/wiki/Siege_of_Fushimi_Castle\" title=\"Siege of Fushimi Castle\">Siege of Fushimi Castle</a>, which is lightly defended by a much smaller <a href=\"https://wikipedia.org/wiki/Tokugawa_clan\" title=\"Tokugawa clan\">Tokugawa</a> garrison led by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>i"}, {"title": "Siege of Fushimi Castle", "link": "https://wikipedia.org/wiki/Siege_of_Fushimi_Castle"}, {"title": "Tokugawa clan", "link": "https://wikipedia.org/wiki/Tokugawa_clan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1689", "text": "The Treaty of Nerchinsk is signed by Russia and the Qing Empire (Julian calendar).", "html": "1689 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nerchinsk\" title=\"Treaty of Nerchinsk\">Treaty of Nerchinsk</a> is signed by <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> and the <a href=\"https://wikipedia.org/wiki/Qing_Empire\" class=\"mw-redirect\" title=\"Qing Empire\">Qing Empire</a> (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Nerchinsk\" title=\"Treaty of Nerchinsk\">Treaty of Nerchinsk</a> is signed by <a href=\"https://wikipedia.org/wiki/Tsardom_of_Russia\" title=\"Tsardom of Russia\">Russia</a> and the <a href=\"https://wikipedia.org/wiki/Qing_Empire\" class=\"mw-redirect\" title=\"Qing Empire\">Qing Empire</a> (<a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "links": [{"title": "Treaty of Nerchinsk", "link": "https://wikipedia.org/wiki/Treaty_of_Nerchinsk"}, {"title": "Tsardom of Russia", "link": "https://wikipedia.org/wiki/Tsardom_of_Russia"}, {"title": "Qing Empire", "link": "https://wikipedia.org/wiki/Qing_Empire"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1776", "text": "American Revolutionary War: Members of the 1st Maryland Regiment repeatedly charged a numerically superior British force during the Battle of Long Island, allowing General <PERSON> and the rest of the American troops to escape.", "html": "1776 - American Revolutionary War: Members of the <a href=\"https://wikipedia.org/wiki/1st_Maryland_Regiment\" title=\"1st Maryland Regiment\">1st Maryland Regiment</a> repeatedly charged a numerically superior British force during the <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Island\" title=\"Battle of Long Island\">Battle of Long Island</a>, allowing <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George <PERSON>\">General <PERSON></a> and the rest of the American troops to escape.", "no_year_html": "American Revolutionary War: Members of the <a href=\"https://wikipedia.org/wiki/1st_Maryland_Regiment\" title=\"1st Maryland Regiment\">1st Maryland Regiment</a> repeatedly charged a numerically superior British force during the <a href=\"https://wikipedia.org/wiki/Battle_of_Long_Island\" title=\"Battle of Long Island\">Battle of Long Island</a>, allowing <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George Washington\">General <PERSON></a> and the rest of the American troops to escape.", "links": [{"title": "1st Maryland Regiment", "link": "https://wikipedia.org/wiki/1st_Maryland_Regiment"}, {"title": "Battle of Long Island", "link": "https://wikipedia.org/wiki/Battle_of_Long_Island"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}]}, {"year": "1791", "text": "French Revolution: <PERSON> of Prussia and <PERSON>, Holy Roman Emperor, issue the Declaration of Pillnitz, declaring the joint support of the Holy Roman Empire and Prussia for the French monarchy, agitating the French revolutionaries and contributing to the outbreak of the War of the First Coalition.", "html": "1791 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> of Prussia</a> and <a href=\"https://wikipedia.org/wiki/Leopold_II,_Holy_Roman_Emperor\" title=\"Leopold II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, issue the <a href=\"https://wikipedia.org/wiki/Declaration_of_Pillnitz\" title=\"Declaration of Pillnitz\">Declaration of Pillnitz</a>, declaring the joint support of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French monarchy</a>, agitating the French revolutionaries and contributing to the outbreak of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia\" title=\"<PERSON> of Prussia\"><PERSON> II of Prussia</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_II,_Holy_Roman_Emperor\" title=\"Leopold II, Holy Roman Emperor\"><PERSON> II, Holy Roman Emperor</a>, issue the <a href=\"https://wikipedia.org/wiki/Declaration_of_Pillnitz\" title=\"Declaration of Pillnitz\">Declaration of Pillnitz</a>, declaring the joint support of the <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empire</a> and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> for the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">French monarchy</a>, agitating the French revolutionaries and contributing to the outbreak of the <a href=\"https://wikipedia.org/wiki/War_of_the_First_Coalition\" title=\"War of the First Coalition\">War of the First Coalition</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON> of Prussia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_of_Prussia"}, {"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}, {"title": "Declaration of Pillnitz", "link": "https://wikipedia.org/wiki/Declaration_of_Pillnitz"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}, {"title": "War of the First Coalition", "link": "https://wikipedia.org/wiki/War_of_the_First_Coalition"}]}, {"year": "1793", "text": "French Revolutionary Wars: The city of Toulon revolts against the French Republic and admits the British and Spanish fleets to seize its port, leading to the Siege of Toulon by French Revolutionary forces.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: The city of <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a> revolts against the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French Republic</a> and admits the British and Spanish fleets to seize <a href=\"https://wikipedia.org/wiki/Military_port_of_Toulon\" class=\"mw-redirect\" title=\"Military port of Toulon\">its port</a>, leading to the <a href=\"https://wikipedia.org/wiki/Siege_of_Toulon\" class=\"mw-redirect\" title=\"Siege of Toulon\">Siege of Toulon</a> by <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Army\" title=\"French Revolutionary Army\">French Revolutionary forces</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolutionary_Wars\" title=\"French Revolutionary Wars\">French Revolutionary Wars</a>: The city of <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a> revolts against the <a href=\"https://wikipedia.org/wiki/French_First_Republic\" title=\"French First Republic\">French Republic</a> and admits the British and Spanish fleets to seize <a href=\"https://wikipedia.org/wiki/Military_port_of_Toulon\" class=\"mw-redirect\" title=\"Military port of Toulon\">its port</a>, leading to the <a href=\"https://wikipedia.org/wiki/Siege_of_Toulon\" class=\"mw-redirect\" title=\"Siege of Toulon\">Siege of Toulon</a> by <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Army\" title=\"French Revolutionary Army\">French Revolutionary forces</a>.", "links": [{"title": "French Revolutionary Wars", "link": "https://wikipedia.org/wiki/French_Revolutionary_Wars"}, {"title": "Toulon", "link": "https://wikipedia.org/wiki/Toulon"}, {"title": "French First Republic", "link": "https://wikipedia.org/wiki/French_First_Republic"}, {"title": "Military port of Toulon", "link": "https://wikipedia.org/wiki/Military_port_of_Toulon"}, {"title": "Siege of Toulon", "link": "https://wikipedia.org/wiki/Siege_of_Toulon"}, {"title": "French Revolutionary Army", "link": "https://wikipedia.org/wiki/French_Revolutionary_Army"}]}, {"year": "1798", "text": "<PERSON>'s United Irish and French forces clash with the British Army in the Battle of Castlebar, part of the Irish Rebellion of 1798, resulting in the creation of the French puppet Republic of Connacht.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolfe Tone\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irish</a> and <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Army\" title=\"French Revolutionary Army\">French forces</a> clash with the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Castlebar\" title=\"Battle of Castlebar\">Battle of Castlebar</a>, part of the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>, resulting in the creation of the <a href=\"https://wikipedia.org/wiki/French_client_republic\" class=\"mw-redirect\" title=\"French client republic\">French puppet</a> <a href=\"https://wikipedia.org/wiki/Republic_of_Connacht\" class=\"mw-redirect\" title=\"Republic of Connacht\">Republic of Connacht</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Wolfe Tone\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Society_of_United_Irishmen\" title=\"Society of United Irishmen\">United Irish</a> and <a href=\"https://wikipedia.org/wiki/French_Revolutionary_Army\" title=\"French Revolutionary Army\">French forces</a> clash with the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Castlebar\" title=\"Battle of Castlebar\">Battle of Castlebar</a>, part of the <a href=\"https://wikipedia.org/wiki/Irish_Rebellion_of_1798\" title=\"Irish Rebellion of 1798\">Irish Rebellion of 1798</a>, resulting in the creation of the <a href=\"https://wikipedia.org/wiki/French_client_republic\" class=\"mw-redirect\" title=\"French client republic\">French puppet</a> <a href=\"https://wikipedia.org/wiki/Republic_of_Connacht\" class=\"mw-redirect\" title=\"Republic of Connacht\">Republic of Connacht</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Society of United Irishmen", "link": "https://wikipedia.org/wiki/Society_of_United_Irishmen"}, {"title": "French Revolutionary Army", "link": "https://wikipedia.org/wiki/French_Revolutionary_Army"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "Battle of Castlebar", "link": "https://wikipedia.org/wiki/Battle_of_Castlebar"}, {"title": "Irish Rebellion of 1798", "link": "https://wikipedia.org/wiki/Irish_Rebellion_of_1798"}, {"title": "French client republic", "link": "https://wikipedia.org/wiki/French_client_republic"}, {"title": "Republic of Connacht", "link": "https://wikipedia.org/wiki/Republic_of_Connacht"}]}, {"year": "1810", "text": "Napoleonic Wars: The French Navy defeats the British Royal Navy, preventing them from taking the harbour of Grand Port on Île de France.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The French Navy <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Port\" title=\"Battle of Grand Port\">defeats</a> the British Royal Navy, preventing them from taking the harbour of <a href=\"https://wikipedia.org/wiki/Grand_Port_District\" title=\"Grand Port District\">Grand Port</a> on <a href=\"https://wikipedia.org/wiki/Mauritius\" title=\"Mauritius\">Île de France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The French Navy <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Port\" title=\"Battle of Grand Port\">defeats</a> the British Royal Navy, preventing them from taking the harbour of <a href=\"https://wikipedia.org/wiki/Grand_Port_District\" title=\"Grand Port District\">Grand Port</a> on <a href=\"https://wikipedia.org/wiki/Mauritius\" title=\"Mauritius\">Île de France</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "Battle of Grand Port", "link": "https://wikipedia.org/wiki/Battle_of_Grand_Port"}, {"title": "Grand Port District", "link": "https://wikipedia.org/wiki/Grand_Port_District"}, {"title": "Mauritius", "link": "https://wikipedia.org/wiki/Mauritius"}]}, {"year": "1813", "text": "French Emperor <PERSON> defeats a larger force of Austrians, Russians, and Prussians at the Battle of Dresden.", "html": "1813 - French Emperor <a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\">Napoleon I</a> defeats a larger force of <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrians</a>, <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a>, and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussians</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dresden\" title=\"Battle of Dresden\">Battle of Dresden</a>.", "no_year_html": "French Emperor <a href=\"https://wikipedia.org/wiki/Napoleon_I\" class=\"mw-redirect\" title=\"Napoleon I\">Napoleon I</a> defeats a larger force of <a href=\"https://wikipedia.org/wiki/Austrian_Empire\" title=\"Austrian Empire\">Austrians</a>, <a href=\"https://wikipedia.org/wiki/Russian_Empire\" title=\"Russian Empire\">Russians</a>, and <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussians</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Dresden\" title=\"Battle of Dresden\">Battle of Dresden</a>.", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_I"}, {"title": "Austrian Empire", "link": "https://wikipedia.org/wiki/Austrian_Empire"}, {"title": "Russian Empire", "link": "https://wikipedia.org/wiki/Russian_Empire"}, {"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}, {"title": "Battle of Dresden", "link": "https://wikipedia.org/wiki/Battle_of_Dresden"}]}, {"year": "1828", "text": "Brazil and Argentina recognize the sovereignty of Uruguay in the Treaty of Montevideo", "html": "1828 - Brazil and Argentina recognize the sovereignty of Uruguay in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Montevideo_(1828)\" class=\"mw-redirect\" title=\"Treaty of Montevideo (1828)\">Treaty of Montevideo</a>", "no_year_html": "Brazil and Argentina recognize the sovereignty of Uruguay in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Montevideo_(1828)\" class=\"mw-redirect\" title=\"Treaty of Montevideo (1828)\">Treaty of Montevideo</a>", "links": [{"title": "Treaty of Montevideo (1828)", "link": "https://wikipedia.org/wiki/Treaty_of_Montevideo_(1828)"}]}, {"year": "1832", "text": "<PERSON> Hawk, leader of the Sauk tribe of Native Americans, surrenders to U.S. authorities, ending the Black Hawk War.", "html": "1832 - <a href=\"https://wikipedia.org/wiki/Black_Hawk_(Sauk_leader)\" title=\"Black Hawk (Sauk leader)\">Black Hawk</a>, leader of the <a href=\"https://wikipedia.org/wiki/Sauk_people\" title=\"Sauk people\">Sauk</a> tribe of Native Americans, surrenders to U.S. authorities, ending the <a href=\"https://wikipedia.org/wiki/Black_Hawk_War\" title=\"Black Hawk War\">Black Hawk War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Hawk_(Sauk_leader)\" title=\"Black Hawk (Sauk leader)\">Black Hawk</a>, leader of the <a href=\"https://wikipedia.org/wiki/Sauk_people\" title=\"Sauk people\">Sauk</a> tribe of Native Americans, surrenders to U.S. authorities, ending the <a href=\"https://wikipedia.org/wiki/Black_Hawk_War\" title=\"Black Hawk War\">Black Hawk War</a>.", "links": [{"title": "<PERSON> (Sauk leader)", "link": "https://wikipedia.org/wiki/<PERSON>_Hawk_(<PERSON><PERSON>_leader)"}, {"title": "Sauk people", "link": "https://wikipedia.org/wiki/Sauk_people"}, {"title": "Black Hawk War", "link": "https://wikipedia.org/wiki/Black_Hawk_War"}]}, {"year": "1859", "text": "Petroleum is discovered in Titusville, Pennsylvania, leading to the world's first commercially successful oil well.", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Petroleum\" title=\"Petroleum\">Petroleum</a> is discovered in <a href=\"https://wikipedia.org/wiki/Titusville,_Pennsylvania\" title=\"Titusville, Pennsylvania\">Titusville, Pennsylvania</a>, leading to the world's first commercially successful <a href=\"https://wikipedia.org/wiki/Oil_well\" title=\"Oil well\">oil well</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Petroleum\" title=\"Petroleum\">Petroleum</a> is discovered in <a href=\"https://wikipedia.org/wiki/Titusville,_Pennsylvania\" title=\"Titusville, Pennsylvania\">Titusville, Pennsylvania</a>, leading to the world's first commercially successful <a href=\"https://wikipedia.org/wiki/Oil_well\" title=\"Oil well\">oil well</a>.", "links": [{"title": "Petroleum", "link": "https://wikipedia.org/wiki/Petroleum"}, {"title": "Titusville, Pennsylvania", "link": "https://wikipedia.org/wiki/Titusville,_Pennsylvania"}, {"title": "Oil well", "link": "https://wikipedia.org/wiki/Oil_well"}]}, {"year": "1881", "text": "The Georgia hurricane makes landfall near Savannah, Georgia, resulting in an estimated 700 deaths.", "html": "1881 - The <a href=\"https://wikipedia.org/wiki/1881_Atlantic_hurricane_season#Hurricane_Five\" title=\"1881 Atlantic hurricane season\">Georgia hurricane</a> makes landfall near <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, resulting in an estimated 700 deaths.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1881_Atlantic_hurricane_season#Hurricane_Five\" title=\"1881 Atlantic hurricane season\">Georgia hurricane</a> makes landfall near <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, resulting in an estimated 700 deaths.", "links": [{"title": "1881 Atlantic hurricane season", "link": "https://wikipedia.org/wiki/1881_Atlantic_hurricane_season#Hurricane_Five"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}]}, {"year": "1883", "text": "Eruption of Krakatoa: Four enormous explosions almost completely destroy the island of Krakatoa and cause years of climate change.", "html": "1883 - <a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">Eruption of Krakatoa</a>: Four enormous explosions almost completely destroy the island of Krakatoa and cause years of climate change.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1883_eruption_of_Krakatoa\" title=\"1883 eruption of Krakatoa\">Eruption of Krakatoa</a>: Four enormous explosions almost completely destroy the island of Krakatoa and cause years of climate change.", "links": [{"title": "1883 eruption of Krakatoa", "link": "https://wikipedia.org/wiki/1883_eruption_of_Krakatoa"}]}, {"year": "1893", "text": "The Sea Islands hurricane strikes the United States near Savannah, Georgia, killing between 1,000 and 2,000 people.", "html": "1893 - The <a href=\"https://wikipedia.org/wiki/1893_Sea_Islands_hurricane\" title=\"1893 Sea Islands hurricane\">Sea Islands hurricane</a> strikes the United States near <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, killing between 1,000 and 2,000 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1893_Sea_Islands_hurricane\" title=\"1893 Sea Islands hurricane\">Sea Islands hurricane</a> strikes the United States near <a href=\"https://wikipedia.org/wiki/Savannah,_Georgia\" title=\"Savannah, Georgia\">Savannah, Georgia</a>, killing between 1,000 and 2,000 people.", "links": [{"title": "1893 Sea Islands hurricane", "link": "https://wikipedia.org/wiki/1893_Sea_Islands_hurricane"}, {"title": "Savannah, Georgia", "link": "https://wikipedia.org/wiki/Savannah,_Georgia"}]}, {"year": "1895", "text": "Japanese invasion of Taiwan: Battle of Baguashan: The Empire of Japan decisively defeats a smaller Formosan army at Changhua, crippling the short-lived Republic of Formosa and leading to its surrender two months later.", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Taiwan_(1895)\" title=\"Japanese invasion of Taiwan (1895)\">Japanese invasion of Taiwan</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Baguashan\" title=\"Battle of Baguashan\">Battle of Baguashan</a>: The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> decisively defeats a smaller <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Formosan</a> army at <a href=\"https://wikipedia.org/wiki/Changhua\" title=\"Changhua\">Changhua</a>, crippling the short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Republic of Formosa</a> and leading to its surrender two months later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Japanese_invasion_of_Taiwan_(1895)\" title=\"Japanese invasion of Taiwan (1895)\">Japanese invasion of Taiwan</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Baguashan\" title=\"Battle of Baguashan\">Battle of Baguashan</a>: The <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Empire of Japan</a> decisively defeats a smaller <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Formosan</a> army at <a href=\"https://wikipedia.org/wiki/Changhua\" title=\"Changhua\">Changhua</a>, crippling the short-lived <a href=\"https://wikipedia.org/wiki/Republic_of_Formosa\" title=\"Republic of Formosa\">Republic of Formosa</a> and leading to its surrender two months later.", "links": [{"title": "Japanese invasion of Taiwan (1895)", "link": "https://wikipedia.org/wiki/Japanese_invasion_of_Taiwan_(1895)"}, {"title": "Battle of Baguashan", "link": "https://wikipedia.org/wiki/Battle_of_Baguashan"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Republic of Formosa", "link": "https://wikipedia.org/wiki/Republic_of_Formosa"}, {"title": "Changhua", "link": "https://wikipedia.org/wiki/Changhua"}, {"title": "Republic of Formosa", "link": "https://wikipedia.org/wiki/Republic_of_Formosa"}]}, {"year": "1896", "text": "Anglo-Zanzibar War: The shortest war in world history (09:02 to 09:40), between the United Kingdom and Zanzibar.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Anglo-Zanzibar_War\" title=\"Anglo-Zanzibar War\">Anglo-Zanzibar War</a>: The shortest war in world history (09:02 to 09:40), between the United Kingdom and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Zanzibar_War\" title=\"Anglo-Zanzibar War\">Anglo-Zanzibar War</a>: The shortest war in world history (09:02 to 09:40), between the United Kingdom and <a href=\"https://wikipedia.org/wiki/Zanzibar\" title=\"Zanzibar\">Zanzibar</a>.", "links": [{"title": "Anglo-Zanzibar War", "link": "https://wikipedia.org/wiki/Anglo-Zanzibar_War"}, {"title": "Zanzibar", "link": "https://wikipedia.org/wiki/Zanzibar"}]}, {"year": "1908", "text": "The Qing dynasty promulgates the Qinding Xianfa Dagang, the first constitutional document in the history of China, transforming the Qing empire into a constitutional monarchy.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> promulgates the <a href=\"https://wikipedia.org/wiki/Principles_of_the_Constitution_(1908)\" class=\"mw-redirect\" title=\"Principles of the Constitution (1908)\">Qinding <PERSON></a>, the first constitutional document in the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">history of China</a>, transforming the Qing empire into a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> promulgates the <a href=\"https://wikipedia.org/wiki/Principles_of_the_Constitution_(1908)\" class=\"mw-redirect\" title=\"Principles of the Constitution (1908)\">Qinding Xianfa Dagang</a>, the first constitutional document in the <a href=\"https://wikipedia.org/wiki/History_of_China\" title=\"History of China\">history of China</a>, transforming the Qing empire into a <a href=\"https://wikipedia.org/wiki/Constitutional_monarchy\" title=\"Constitutional monarchy\">constitutional monarchy</a>.", "links": [{"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Principles of the Constitution (1908)", "link": "https://wikipedia.org/wiki/Principles_of_the_Constitution_(1908)"}, {"title": "History of China", "link": "https://wikipedia.org/wiki/History_of_China"}, {"title": "Constitutional monarchy", "link": "https://wikipedia.org/wiki/Constitutional_monarchy"}]}, {"year": "1914", "text": "World War I: Battle of Étreux: A British rearguard action by the Royal Munster Fusiliers during the Great Retreat.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_%C3%89treux\" class=\"mw-redirect\" title=\"Battle of Étreux\">Battle of Étreux</a>: A British rearguard action by the <a href=\"https://wikipedia.org/wiki/Royal_Munster_Fusiliers\" title=\"Royal Munster Fusiliers\">Royal Munster Fusiliers</a> during the <a href=\"https://wikipedia.org/wiki/Great_Retreat\" title=\"Great Retreat\">Great Retreat</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_%C3%89treux\" class=\"mw-redirect\" title=\"Battle of Étreux\">Battle of Étreux</a>: A British rearguard action by the <a href=\"https://wikipedia.org/wiki/Royal_Munster_Fusiliers\" title=\"Royal Munster Fusiliers\">Royal Munster Fusiliers</a> during the <a href=\"https://wikipedia.org/wiki/Great_Retreat\" title=\"Great Retreat\">Great Retreat</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Battle of Étreux", "link": "https://wikipedia.org/wiki/Battle_of_%C3%89treux"}, {"title": "Royal Munster Fusiliers", "link": "https://wikipedia.org/wiki/Royal_Munster_Fusiliers"}, {"title": "Great Retreat", "link": "https://wikipedia.org/wiki/Great_Retreat"}]}, {"year": "1914", "text": "World War I: Siege of Tsingtao: A Japanese fleet commanded by Vice Admiral <PERSON><PERSON><PERSON> imposes a blockade along the whole coastline of German Tsingtao, initiating the Siege of Tsingtao.", "html": "1914 - World War I: <a href=\"https://wikipedia.org/wiki/Siege_of_Tsingtao\" title=\"Siege of Tsingtao\">Siege of Tsingtao</a>: A Japanese fleet commanded by Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> imposes a blockade along the whole coastline of <a href=\"https://wikipedia.org/wiki/Qingdao#German_and_Japanese_occupations\" title=\"Qingdao\">German Tsingtao</a>, initiating the <a href=\"https://wikipedia.org/wiki/Siege_of_Tsingtao\" title=\"Siege of Tsingtao\">Siege of Tsingtao</a>.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Siege_of_Tsingtao\" title=\"Siege of Tsingtao\">Siege of Tsingtao</a>: A Japanese fleet commanded by Vice Admiral <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Kat<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> imposes a blockade along the whole coastline of <a href=\"https://wikipedia.org/wiki/Qingdao#German_and_Japanese_occupations\" title=\"Qingdao\">German Tsingtao</a>, initiating the <a href=\"https://wikipedia.org/wiki/Siege_of_Tsingtao\" title=\"Siege of Tsingtao\">Siege of Tsingtao</a>.", "links": [{"title": "Siege of Tsingtao", "link": "https://wikipedia.org/wiki/Siege_of_Tsingtao"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}, {"title": "Qingdao", "link": "https://wikipedia.org/wiki/Qingdao#German_and_Japanese_occupations"}, {"title": "Siege of Tsingtao", "link": "https://wikipedia.org/wiki/Siege_of_Tsingtao"}]}, {"year": "1915", "text": "Attempted assassination of Bishop <PERSON>, bishop of the Diocese of Winona, by Rev. <PERSON>.", "html": "1915 - Attempted <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassination</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Bishop <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Winona%E2%80%93Rochester\" title=\"Roman Catholic Diocese of Winona-Rochester\">Diocese of Winona</a>, by <a href=\"https://wikipedia.org/wiki/The_Reverend#Roman_Catholic\" title=\"The Reverend\">Rev.</a> <PERSON>.", "no_year_html": "Attempted <a href=\"https://wikipedia.org/wiki/Assassination\" title=\"Assassination\">assassination</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Bishop <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a> of the <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Winona%E2%80%93Rochester\" title=\"Roman Catholic Diocese of Winona-Rochester\">Diocese of Winona</a>, by <a href=\"https://wikipedia.org/wiki/The_Reverend#Roman_Catholic\" title=\"The Reverend\">Rev.</a> <PERSON>.", "links": [{"title": "Assassination", "link": "https://wikipedia.org/wiki/Assassination"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}, {"title": "Roman Catholic Diocese of Winona-Rochester", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Winona%E2%80%93Rochester"}, {"title": "The Reverend", "link": "https://wikipedia.org/wiki/The_Reverend#Roman_Catholic"}]}, {"year": "1916", "text": "World War I: The Kingdom of Romania declares war on Austria-Hungary, entering the war as one of the Allied nations.", "html": "1916 - World War I: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Kingdom of Romania</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>, entering the war as one of the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied nations</a>.", "no_year_html": "World War I: The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Kingdom of Romania</a> declares war on <a href=\"https://wikipedia.org/wiki/Austria-Hungary\" title=\"Austria-Hungary\">Austria-Hungary</a>, entering the war as one of the <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_I\" title=\"Allies of World War I\">Allied nations</a>.", "links": [{"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}, {"title": "Austria-Hungary", "link": "https://wikipedia.org/wiki/Austria-Hungary"}, {"title": "Allies of World War I", "link": "https://wikipedia.org/wiki/Allies_of_World_War_I"}]}, {"year": "1918", "text": "Mexican Revolution: Battle of Ambos Nogales: U.S. Army forces skirmish against Mexican Carrancistas in the only battle of World War I fought on American soil.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ambos_Nogales\" title=\"Battle of Ambos Nogales\">Battle of Ambos Nogales</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> forces skirmish against Mexican <a href=\"https://wikipedia.org/wiki/Carrancistas\" class=\"mw-redirect\" title=\"Carrancistas\">Carrancistas</a> in the only battle of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> fought on American soil.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mexican_Revolution\" title=\"Mexican Revolution\">Mexican Revolution</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Ambos_Nogales\" title=\"Battle of Ambos Nogales\">Battle of Ambos Nogales</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">U.S. Army</a> forces skirmish against Mexican <a href=\"https://wikipedia.org/wiki/Carrancistas\" class=\"mw-redirect\" title=\"Carrancistas\">Carrancistas</a> in the only battle of <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a> fought on American soil.", "links": [{"title": "Mexican Revolution", "link": "https://wikipedia.org/wiki/Mexican_Revolution"}, {"title": "Battle of Ambos Nogales", "link": "https://wikipedia.org/wiki/Battle_of_Ambos_Nogales"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Carrancistas", "link": "https://wikipedia.org/wiki/Carrancistas"}, {"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}]}, {"year": "1922", "text": "Greco-Turkish War: The Turkish army takes the Aegean city of Afyonkarahisar from the Kingdom of Greece.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%931922)\" title=\"Greco-Turkish War (1919-1922)\">Greco-Turkish War</a>: The Turkish army takes the <a href=\"https://wikipedia.org/wiki/Aegean_Region\" class=\"mw-redirect\" title=\"Aegean Region\">Aegean</a> city of <a href=\"https://wikipedia.org/wiki/Afyonkarahisar\" title=\"Afyonkarahisar\">Afyonkarahisar</a> from the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">Kingdom of Greece</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%931922)\" title=\"Greco-Turkish War (1919-1922)\">Greco-Turkish War</a>: The Turkish army takes the <a href=\"https://wikipedia.org/wiki/Aegean_Region\" class=\"mw-redirect\" title=\"Aegean Region\">Aegean</a> city of <a href=\"https://wikipedia.org/wiki/Afyonkarahisar\" title=\"Afyonkarahisar\">Afyonkarahisar</a> from the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)\" class=\"mw-redirect\" title=\"Kingdom of Greece (Glücksburg)\">Kingdom of Greece</a>.", "links": [{"title": "Greco-Turkish War (1919-1922)", "link": "https://wikipedia.org/wiki/Greco-Turkish_War_(1919%E2%80%931922)"}, {"title": "Aegean Region", "link": "https://wikipedia.org/wiki/Aegean_Region"}, {"title": "Afyonkarahisar", "link": "https://wikipedia.org/wiki/Afyonkarahisar"}, {"title": "Kingdom of Greece (Glücksburg)", "link": "https://wikipedia.org/wiki/Kingdom_of_Greece_(Gl%C3%BCcksburg)"}]}, {"year": "1927", "text": "Five Canadian women file a petition to the Supreme Court of Canada, asking: \"Does the word 'Persons' in Section 24 of the British North America Act, 1867, include female persons?\"", "html": "1927 - <a href=\"https://wikipedia.org/wiki/The_Famous_Five_(Canada)\" title=\"The Famous Five (Canada)\">Five Canadian women</a> file a petition to the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Canada\" title=\"Supreme Court of Canada\">Supreme Court of Canada</a>, asking: \"Does the word 'Persons' in Section 24 of the <a href=\"https://wikipedia.org/wiki/British_North_America_Act,_1867\" class=\"mw-redirect\" title=\"British North America Act, 1867\">British North America Act, 1867</a>, include female persons?\"", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Famous_Five_(Canada)\" title=\"The Famous Five (Canada)\">Five Canadian women</a> file a petition to the <a href=\"https://wikipedia.org/wiki/Supreme_Court_of_Canada\" title=\"Supreme Court of Canada\">Supreme Court of Canada</a>, asking: \"Does the word 'Persons' in Section 24 of the <a href=\"https://wikipedia.org/wiki/British_North_America_Act,_1867\" class=\"mw-redirect\" title=\"British North America Act, 1867\">British North America Act, 1867</a>, include female persons?\"", "links": [{"title": "The Famous Five (Canada)", "link": "https://wikipedia.org/wiki/The_Famous_Five_(Canada)"}, {"title": "Supreme Court of Canada", "link": "https://wikipedia.org/wiki/Supreme_Court_of_Canada"}, {"title": "British North America Act, 1867", "link": "https://wikipedia.org/wiki/British_North_America_Act,_1867"}]}, {"year": "1928", "text": "The Kellogg-Briand Pact outlawing war is signed by fifteen nations. Ultimately sixty-one nations will sign it.", "html": "1928 - The <a href=\"https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact\" title=\"Kellogg-Briand Pact\">Kellogg-Briand Pact</a> outlawing war is signed by fifteen nations. Ultimately sixty-one nations will sign it.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact\" title=\"Kellogg-Briand Pact\">Kellogg-Briand Pact</a> outlawing war is signed by fifteen nations. Ultimately sixty-one nations will sign it.", "links": [{"title": "Kellogg-Briand Pact", "link": "https://wikipedia.org/wiki/Kellogg%E2%80%93Briand_Pact"}]}, {"year": "1933", "text": "The first Afrikaans Bible is introduced during a Bible Festival in Bloemfontein.", "html": "1933 - The first <a href=\"https://wikipedia.org/wiki/Bible_translations_into_Afrikaans\" title=\"Bible translations into Afrikaans\">Afrikaans Bible</a> is introduced during a Bible Festival in <a href=\"https://wikipedia.org/wiki/Bloemfontein\" title=\"Bloemfontein\">Bloemfontein</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Bible_translations_into_Afrikaans\" title=\"Bible translations into Afrikaans\">Afrikaans Bible</a> is introduced during a Bible Festival in <a href=\"https://wikipedia.org/wiki/Bloemfontein\" title=\"Bloemfontein\">Bloemfontein</a>.", "links": [{"title": "Bible translations into Afrikaans", "link": "https://wikipedia.org/wiki/Bible_translations_into_Afrikaans"}, {"title": "Bloemfontein", "link": "https://wikipedia.org/wiki/Bloemfontein"}]}, {"year": "1939", "text": "First flight of the turbojet-powered Heinkel He 178, the world's first jet aircraft.", "html": "1939 - First flight of the <a href=\"https://wikipedia.org/wiki/Turbojet\" title=\"Turbojet\">turbojet</a>-powered <a href=\"https://wikipedia.org/wiki/Heinkel_He_178\" title=\"Heinkel He 178\">Heinkel He 178</a>, the world's first <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>.", "no_year_html": "First flight of the <a href=\"https://wikipedia.org/wiki/Turbojet\" title=\"Turbojet\">turbojet</a>-powered <a href=\"https://wikipedia.org/wiki/Heinkel_He_178\" title=\"Heinkel He 178\">Heinkel He 178</a>, the world's first <a href=\"https://wikipedia.org/wiki/Jet_aircraft\" title=\"Jet aircraft\">jet aircraft</a>.", "links": [{"title": "Turbojet", "link": "https://wikipedia.org/wiki/Turbojet"}, {"title": "<PERSON><PERSON><PERSON> He 178", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_178"}, {"title": "Jet aircraft", "link": "https://wikipedia.org/wiki/Jet_aircraft"}]}, {"year": "1942", "text": "First day of the Sarny Massacre, perpetrated by Germans and Ukrainians.", "html": "1942 - First day of the <a href=\"https://wikipedia.org/wiki/Sarny_Massacre\" class=\"mw-redirect\" title=\"Sarny Massacre\">Sarny Massacre</a>, perpetrated by Germans and Ukrainians.", "no_year_html": "First day of the <a href=\"https://wikipedia.org/wiki/Sarny_Massacre\" class=\"mw-redirect\" title=\"Sarny Massacre\">Sarny Massacre</a>, perpetrated by Germans and Ukrainians.", "links": [{"title": "Sarny Massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "World War II: Japanese forces evacuate New Georgia Island in the Pacific Theater of Operations during World War II.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces evacuate <a href=\"https://wikipedia.org/wiki/New_Georgia\" title=\"New Georgia\">New Georgia Island</a> in the <a href=\"https://wikipedia.org/wiki/Asiatic-Pacific_Theater\" class=\"mw-redirect\" title=\"Asiatic-Pacific Theater\">Pacific Theater of Operations</a> during World War II.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> forces evacuate <a href=\"https://wikipedia.org/wiki/New_Georgia\" title=\"New Georgia\">New Georgia Island</a> in the <a href=\"https://wikipedia.org/wiki/Asiatic-Pacific_Theater\" class=\"mw-redirect\" title=\"Asiatic-Pacific Theater\">Pacific Theater of Operations</a> during World War II.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "New Georgia", "link": "https://wikipedia.org/wiki/New_Georgia"}, {"title": "Asiatic-Pacific Theater", "link": "https://wikipedia.org/wiki/Asiatic-Pacific_Theater"}]}, {"year": "1943", "text": "World War II: Aerial bombardment by the Luftwaffe razes to the ground the village of Vorizia in Crete.", "html": "1943 - World War II: Aerial bombardment by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> <a href=\"https://wikipedia.org/wiki/Razing_of_Vorizia\" title=\"Razing of Vorizia\">razes</a> to the ground the village of Vorizia in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>.", "no_year_html": "World War II: Aerial bombardment by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> <a href=\"https://wikipedia.org/wiki/Razing_of_Vorizia\" title=\"Razing of Vorizia\">razes</a> to the ground the village of Vorizia in <a href=\"https://wikipedia.org/wiki/Crete\" title=\"Crete\">Crete</a>.", "links": [{"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "Razing of Vorizia", "link": "https://wikipedia.org/wiki/Razing_of_Vorizia"}, {"title": "Crete", "link": "https://wikipedia.org/wiki/Crete"}]}, {"year": "1955", "text": "The first edition of the Guinness Book of Records is published in Great Britain.", "html": "1955 - The first edition of the <i><a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness Book of Records</a></i> is published in Great Britain.", "no_year_html": "The first edition of the <i><a href=\"https://wikipedia.org/wiki/Guinness_World_Records\" title=\"Guinness World Records\">Guinness Book of Records</a></i> is published in Great Britain.", "links": [{"title": "Guinness World Records", "link": "https://wikipedia.org/wiki/Guinness_World_Records"}]}, {"year": "1956", "text": "The nuclear power station at Calder Hall in the United Kingdom was connected to the national power grid becoming the world's first commercial nuclear power station to generate electricity on an industrial scale.", "html": "1956 - The nuclear power station at <a href=\"https://wikipedia.org/wiki/Calder_Hall\" class=\"mw-redirect\" title=\"Calder Hall\">Calder Hall</a> in the United Kingdom was connected to the national power grid becoming the world's first commercial nuclear power station to generate electricity on an industrial scale.", "no_year_html": "The nuclear power station at <a href=\"https://wikipedia.org/wiki/Calder_Hall\" class=\"mw-redirect\" title=\"Calder Hall\">Calder Hall</a> in the United Kingdom was connected to the national power grid becoming the world's first commercial nuclear power station to generate electricity on an industrial scale.", "links": [{"title": "Calder Hall", "link": "https://wikipedia.org/wiki/Calder_Hall"}]}, {"year": "1962", "text": "The Mariner 2 unmanned space mission is launched to Venus by NASA.", "html": "1962 - The <a href=\"https://wikipedia.org/wiki/Mariner_2\" title=\"Mariner 2\">Mariner 2</a> unmanned space mission is launched to <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> by <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mariner_2\" title=\"Mariner 2\">Mariner 2</a> unmanned space mission is launched to <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a> by <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>.", "links": [{"title": "Mariner 2", "link": "https://wikipedia.org/wiki/Mariner_2"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}]}, {"year": "1963", "text": "An explosion at the Cane Creek potash mine near Moab, Utah kills 18 miners.", "html": "1963 - An explosion at the <a href=\"https://wikipedia.org/wiki/Intrepid_Potash#Moab\" title=\"Intrepid Potash\">Cane Creek potash mine</a> near <a href=\"https://wikipedia.org/wiki/Moab,_Utah\" title=\"Moab, Utah\">Moab, Utah</a> kills 18 miners.", "no_year_html": "An explosion at the <a href=\"https://wikipedia.org/wiki/Intrepid_Potash#Moab\" title=\"Intrepid Potash\">Cane Creek potash mine</a> near <a href=\"https://wikipedia.org/wiki/Moab,_Utah\" title=\"Moab, Utah\">Moab, Utah</a> kills 18 miners.", "links": [{"title": "Intrepid Potash", "link": "https://wikipedia.org/wiki/Intrepid_Potash#Moab"}, {"title": "Moab, Utah", "link": "https://wikipedia.org/wiki/Moab,_Utah"}]}, {"year": "1964", "text": "South Vietnamese junta leader <PERSON><PERSON><PERSON><PERSON> enters into a triumvirate power-sharing arrangement with rival generals <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, who had both been involved in plots to unseat <PERSON><PERSON><PERSON><PERSON>.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese junta</a> leader <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> enters into a <a href=\"https://wikipedia.org/wiki/Triumvirate\" title=\"Triumvirate\">triumvirate</a> power-sharing arrangement with rival generals <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thiện Khiêm\">Trần <PERSON></a> and <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"<PERSON>ương <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, who had both been involved in plots to unseat <PERSON><PERSON>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam\" title=\"Army of the Republic of Vietnam\">South Vietnamese junta</a> leader <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> enters into a <a href=\"https://wikipedia.org/wiki/Triumvirate\" title=\"Triumvirate\">triumvirate</a> power-sharing arrangement with rival generals <a href=\"https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm\" title=\"Trần Thiện Khiêm\">Trần Thiện <PERSON>hiêm</a> and <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"<PERSON>ương <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, who had both been involved in plots to unseat <PERSON><PERSON><PERSON><PERSON>.", "links": [{"title": "Army of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "Triumvirate", "link": "https://wikipedia.org/wiki/Triumvirate"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tr%E1%BA%A7n_Thi%E1%BB%87n_Khi%C3%AAm"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh"}]}, {"year": "1971", "text": "An attempted coup d'état fails in the African nation of Chad. The Government of Chad accuses Egypt of playing a role in the attempt and breaks off diplomatic relations.", "html": "1971 - An attempted <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> fails in the African nation of <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a>. The <a href=\"https://wikipedia.org/wiki/Government_of_Chad\" title=\"Government of Chad\">Government of Chad</a> accuses <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> of playing a role in the attempt and breaks off <a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">diplomatic relations</a>.", "no_year_html": "An attempted <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> fails in the African nation of <a href=\"https://wikipedia.org/wiki/Chad\" title=\"Chad\">Chad</a>. The <a href=\"https://wikipedia.org/wiki/Government_of_Chad\" title=\"Government of Chad\">Government of Chad</a> accuses <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> of playing a role in the attempt and breaks off <a href=\"https://wikipedia.org/wiki/Diplomatic_relations\" class=\"mw-redirect\" title=\"Diplomatic relations\">diplomatic relations</a>.", "links": [{"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Chad", "link": "https://wikipedia.org/wiki/Chad"}, {"title": "Government of Chad", "link": "https://wikipedia.org/wiki/Government_of_Chad"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "Diplomatic relations", "link": "https://wikipedia.org/wiki/Diplomatic_relations"}]}, {"year": "1975", "text": "The Governor of Portuguese Timor abandons its capital, Dili, and flees to Atauro Island, leaving control to a rebel group.", "html": "1975 - The Governor of <a href=\"https://wikipedia.org/wiki/Portuguese_Timor\" title=\"Portuguese Timor\">Portuguese Timor</a> abandons its capital, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and flees to <a href=\"https://wikipedia.org/wiki/Atauro_Island\" class=\"mw-redirect\" title=\"Atauro Island\">Atauro Island</a>, leaving control to a rebel group.", "no_year_html": "The Governor of <a href=\"https://wikipedia.org/wiki/Portuguese_Timor\" title=\"Portuguese Timor\">Portuguese Timor</a> abandons its capital, <a href=\"https://wikipedia.org/wiki/Di<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, and flees to <a href=\"https://wikipedia.org/wiki/Atauro_Island\" class=\"mw-redirect\" title=\"Atauro Island\">Atauro Island</a>, leaving control to a rebel group.", "links": [{"title": "Portuguese Timor", "link": "https://wikipedia.org/wiki/Portuguese_Timor"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dili"}, {"title": "Atauro Island", "link": "https://wikipedia.org/wiki/Atauro_Island"}]}, {"year": "1979", "text": "The Troubles: Eighteen British soldiers are killed in an ambush by the Provisional Irish Republican Army near Warrenpoint, Northern Ireland, in the deadliest attack on British forces during Operation Banner. An IRA bomb also kills British royal family member <PERSON> and three others on his boat at Mullaghmore, Republic of Ireland.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Eighteen British soldiers are <a href=\"https://wikipedia.org/wiki/Warrenpoint_ambush\" title=\"Warrenpoint ambush\">killed in an ambush</a> by the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> near <a href=\"https://wikipedia.org/wiki/Warrenpoint\" title=\"Warrenpoint\">Warrenpoint</a>, Northern Ireland, in the deadliest attack on British forces during <a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>. An IRA bomb also kills <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">British royal family</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\">Lord <PERSON></a> and three others on his boat at <a href=\"https://wikipedia.org/wiki/Mullaghmore,_County_Sligo\" title=\"Mullaghmore, County Sligo\">Mullaghmore</a>, Republic of Ireland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: Eighteen British soldiers are <a href=\"https://wikipedia.org/wiki/Warrenpoint_ambush\" title=\"Warrenpoint ambush\">killed in an ambush</a> by the <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional Irish Republican Army</a> near <a href=\"https://wikipedia.org/wiki/Warrenpoint\" title=\"Warrenpoint\">Warrenpoint</a>, Northern Ireland, in the deadliest attack on British forces during <a href=\"https://wikipedia.org/wiki/Operation_Banner\" title=\"Operation Banner\">Operation Banner</a>. An IRA bomb also kills <a href=\"https://wikipedia.org/wiki/British_royal_family\" title=\"British royal family\">British royal family</a> member <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\">Lord <PERSON></a> and three others on his boat at <a href=\"https://wikipedia.org/wiki/Mullaghmore,_County_Sligo\" title=\"Mullaghmore, County Sligo\">Mullaghmore</a>, Republic of Ireland.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Warrenpoint ambush", "link": "https://wikipedia.org/wiki/Warrenpoint_ambush"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Warrenpoint", "link": "https://wikipedia.org/wiki/Warrenpoint"}, {"title": "Operation Banner", "link": "https://wikipedia.org/wiki/Operation_Banner"}, {"title": "British royal family", "link": "https://wikipedia.org/wiki/British_royal_family"}, {"title": "<PERSON>, 1st Earl <PERSON> of Burma", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma"}, {"title": "Mullaghmore, County Sligo", "link": "https://wikipedia.org/wiki/Mullaghmore,_County_Sligo"}]}, {"year": "1980", "text": "South Korean presidential election: After successfully staging the Coup d'état of May Seventeenth, General <PERSON>, running unopposed, has the National Conference for Unification elect him President of the Fourth Republic of Korea.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/1980_South_Korean_presidential_election\" title=\"1980 South Korean presidential election\">South Korean presidential election</a>: After successfully staging the <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_May_Seventeenth\" title=\"Coup d'état of May Seventeenth\">Coup d'état of May Seventeenth</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, running unopposed, has the National Conference for Unification elect him <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of the Fourth Republic of Korea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1980_South_Korean_presidential_election\" title=\"1980 South Korean presidential election\">South Korean presidential election</a>: After successfully staging the <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_May_Seventeenth\" title=\"Coup d'état of May Seventeenth\">Coup d'état of May Seventeenth</a>, General <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a>, running unopposed, has the National Conference for Unification elect him <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President of the Fourth Republic of Korea</a>.", "links": [{"title": "1980 South Korean presidential election", "link": "https://wikipedia.org/wiki/1980_South_Korean_presidential_election"}, {"title": "Coup d'état of May Seventeenth", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat_of_May_Seventeenth"}, {"title": "<PERSON>wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}]}, {"year": "1982", "text": "Turkish military diplomat Colonel <PERSON><PERSON> is shot and killed in Ottawa. Justice Commandos of the Armenian Genocide claim to be avenging the massacre of .mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}1+1⁄2 million Armenians in the 1915 Armenian genocide.", "html": "1982 - Turkish <a href=\"https://wikipedia.org/wiki/Military_attach%C3%A9\" title=\"Military attaché\">military diplomat</a> Colonel <a href=\"https://wikipedia.org/wiki/Atilla_Alt%C4%B1kat\" title=\"Atilla Altıkat\">Atilla Altıkat</a> is shot and killed in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>. <a href=\"https://wikipedia.org/wiki/Justice_Commandos_of_the_Armenian_Genocide\" title=\"Justice Commandos of the Armenian Genocide\">Justice Commandos of the Armenian Genocide</a> claim to be avenging the massacre of <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">1<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million Armenians in the 1915 <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "no_year_html": "Turkish <a href=\"https://wikipedia.org/wiki/Military_attach%C3%A9\" title=\"Military attaché\">military diplomat</a> Colonel <a href=\"https://wikipedia.org/wiki/Atilla_Alt%C4%B1kat\" title=\"Atilla Altıkat\">Atilla Altıkat</a> is shot and killed in <a href=\"https://wikipedia.org/wiki/Ottawa\" title=\"Ottawa\">Ottawa</a>. <a href=\"https://wikipedia.org/wiki/Justice_Commandos_of_the_Armenian_Genocide\" title=\"Justice Commandos of the Armenian Genocide\">Justice Commandos of the Armenian Genocide</a> claim to be avenging the massacre of <style data-mw-deduplicate=\"TemplateStyles:r1154941027\">.mw-parser-output .frac{white-space:nowrap}.mw-parser-output .frac .num,.mw-parser-output .frac .den{font-size:80%;line-height:0;vertical-align:super}.mw-parser-output .frac .den{vertical-align:sub}.mw-parser-output .sr-only{border:0;clip:rect(0,0,0,0);clip-path:polygon(0px 0px,0px 0px,0px 0px);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}</style>\n<span class=\"frac\">1<span class=\"sr-only\">+</span><span class=\"num\">1</span>⁄<span class=\"den\">2</span></span> million Armenians in the 1915 <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "links": [{"title": "Military attaché", "link": "https://wikipedia.org/wiki/Military_attach%C3%A9"}, {"title": "Atilla Altıkat", "link": "https://wikipedia.org/wiki/Atilla_Alt%C4%B1kat"}, {"title": "Ottawa", "link": "https://wikipedia.org/wiki/Ottawa"}, {"title": "Justice Commandos of the Armenian Genocide", "link": "https://wikipedia.org/wiki/Justice_Commandos_of_the_Armenian_Genocide"}, {"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}]}, {"year": "1985", "text": "Major General <PERSON><PERSON>, Chairman of the Supreme Military Council of Nigeria, is ousted from power in a coup d'état led by Major General <PERSON>.", "html": "1985 - Major General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chairman of the <a href=\"https://wikipedia.org/wiki/Supreme_Military_Council_of_Nigeria_(1983%E2%80%931985)\" title=\"Supreme Military Council of Nigeria (1983-1985)\">Supreme Military Council of Nigeria</a>, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1985_Nigerian_coup_d%27%C3%A9tat\" title=\"1985 Nigerian coup d'état\">coup d'état</a> led by Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "Major General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chairman of the <a href=\"https://wikipedia.org/wiki/Supreme_Military_Council_of_Nigeria_(1983%E2%80%931985)\" title=\"Supreme Military Council of Nigeria (1983-1985)\">Supreme Military Council of Nigeria</a>, is ousted from power in a <a href=\"https://wikipedia.org/wiki/1985_Nigerian_coup_d%27%C3%A9tat\" title=\"1985 Nigerian coup d'état\">coup d'état</a> led by Major General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Supreme Military Council of Nigeria (1983-1985)", "link": "https://wikipedia.org/wiki/Supreme_Military_Council_of_Nigeria_(1983%E2%80%931985)"}, {"title": "1985 Nigerian coup d'état", "link": "https://wikipedia.org/wiki/1985_Nigerian_coup_d%27%C3%A9tat"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "Space Shuttle Discovery is launched on STS-51-I to deploy three communication satellites and repair a fourth malfunctioning one.", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-51-I\" title=\"STS-51-I\">STS-51-I</a> to deploy three <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communication satellites</a> and repair a fourth malfunctioning one.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-51-I\" title=\"STS-51-I\">STS-51-I</a> to deploy three <a href=\"https://wikipedia.org/wiki/Communications_satellite\" title=\"Communications satellite\">communication satellites</a> and repair a fourth malfunctioning one.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-51-I", "link": "https://wikipedia.org/wiki/STS-51-I"}, {"title": "Communications satellite", "link": "https://wikipedia.org/wiki/Communications_satellite"}]}, {"year": "1991", "text": "The European Community recognizes the independence of the Baltic states of Estonia, Latvia and Lithuania.", "html": "1991 - The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Community\" class=\"mw-redirect\" title=\"European Community\">European Community</a> recognizes the independence of the <a href=\"https://wikipedia.org/wiki/Baltic_states\" title=\"Baltic states\">Baltic states</a> of <a href=\"https://wikipedia.org/wiki/Estonia\" title=\"Estonia\">Estonia</a>, <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> and <a href=\"https://wikipedia.org/wiki/Lithuania\" title=\"Lithuania\">Lithuania</a>.", "links": [{"title": "European Community", "link": "https://wikipedia.org/wiki/European_Community"}, {"title": "Baltic states", "link": "https://wikipedia.org/wiki/Baltic_states"}, {"title": "Estonia", "link": "https://wikipedia.org/wiki/Estonia"}, {"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "Lithuania", "link": "https://wikipedia.org/wiki/Lithuania"}]}, {"year": "1991", "text": "Moldova declares independence from the USSR.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moldova\" title=\"Moldova\">Moldova</a> declares independence from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">USSR</a>.", "links": [{"title": "Moldova", "link": "https://wikipedia.org/wiki/Moldova"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1992", "text": "Aeroflot Flight 2808 crashes on approach to Ivanovo Yuzhny Airport, killing all 84 aboard.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_2808\" title=\"Aeroflot Flight 2808\">Aeroflot Flight 2808</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ivanovo_Yuzhny_Airport\" title=\"Ivanovo Yuzhny Airport\">Ivanovo Yuzhny Airport</a>, killing all 84 aboard.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_2808\" title=\"Aeroflot Flight 2808\">Aeroflot Flight 2808</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Ivanovo_Yuzhny_Airport\" title=\"Ivanovo Yuzhny Airport\">Ivanovo Yuzhny Airport</a>, killing all 84 aboard.", "links": [{"title": "Aeroflot Flight 2808", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_2808"}, {"title": "Ivanovo Yuzhny Airport", "link": "https://wikipedia.org/wiki/Ivanovo_Yuzhny_Airport"}]}, {"year": "2003", "text": "Mars makes its closest approach to Earth in nearly 60,000 years, passing 34,646,418 miles (55,758,005 km) distant.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> makes its closest approach to Earth in nearly 60,000 years, passing 34,646,418 miles (55,758,005 km) distant.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a> makes its closest approach to Earth in nearly 60,000 years, passing 34,646,418 miles (55,758,005 km) distant.", "links": [{"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "2003", "text": "The first six-party talks, involving South and North Korea, the United States, China, Japan and Russia, convene to find a peaceful resolution to the security concerns of the North Korean nuclear weapons program.", "html": "2003 - The first <a href=\"https://wikipedia.org/wiki/Six-party_talks\" title=\"Six-party talks\">six-party talks</a>, involving South and North Korea, the United States, China, Japan and Russia, convene to find a peaceful resolution to the security concerns of the <a href=\"https://wikipedia.org/wiki/North_Korea_and_weapons_of_mass_destruction\" title=\"North Korea and weapons of mass destruction\">North Korean nuclear weapons program</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Six-party_talks\" title=\"Six-party talks\">six-party talks</a>, involving South and North Korea, the United States, China, Japan and Russia, convene to find a peaceful resolution to the security concerns of the <a href=\"https://wikipedia.org/wiki/North_Korea_and_weapons_of_mass_destruction\" title=\"North Korea and weapons of mass destruction\">North Korean nuclear weapons program</a>.", "links": [{"title": "Six-party talks", "link": "https://wikipedia.org/wiki/Six-party_talks"}, {"title": "North Korea and weapons of mass destruction", "link": "https://wikipedia.org/wiki/North_Korea_and_weapons_of_mass_destruction"}]}, {"year": "2006", "text": "Comair Flight 5191 crashes on takeoff from Blue Grass Airport in Lexington, Kentucky, bound for Hartsfield-Jackson Atlanta International Airport in Atlanta. Of the passengers and crew, 49 of 50 are confirmed dead in the hours following the crash.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Comair_Flight_5191\" title=\"Comair Flight 5191\">Comair Flight 5191</a> crashes on <a href=\"https://wikipedia.org/wiki/Takeoff\" title=\"Takeoff\">takeoff</a> from <a href=\"https://wikipedia.org/wiki/Blue_Grass_Airport\" title=\"Blue Grass Airport\">Blue Grass Airport</a> in <a href=\"https://wikipedia.org/wiki/Lexington,_Kentucky\" title=\"Lexington, Kentucky\">Lexington, Kentucky</a>, bound for <a href=\"https://wikipedia.org/wiki/Hartsfield%E2%80%93Jackson_Atlanta_International_Airport\" title=\"Hartsfield-Jackson Atlanta International Airport\">Hartsfield-Jackson Atlanta International Airport</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>. Of the passengers and crew, 49 of 50 are confirmed dead in the hours following the crash.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Comair_Flight_5191\" title=\"Comair Flight 5191\">Comair Flight 5191</a> crashes on <a href=\"https://wikipedia.org/wiki/Takeoff\" title=\"Takeoff\">takeoff</a> from <a href=\"https://wikipedia.org/wiki/Blue_Grass_Airport\" title=\"Blue Grass Airport\">Blue Grass Airport</a> in <a href=\"https://wikipedia.org/wiki/Lexington,_Kentucky\" title=\"Lexington, Kentucky\">Lexington, Kentucky</a>, bound for <a href=\"https://wikipedia.org/wiki/Hartsfield%E2%80%93J<PERSON>son_Atlanta_International_Airport\" title=\"Hartsfield-Jackson Atlanta International Airport\">Hartsfield-Jackson Atlanta International Airport</a> in <a href=\"https://wikipedia.org/wiki/Atlanta\" title=\"Atlanta\">Atlanta</a>. Of the passengers and crew, 49 of 50 are confirmed dead in the hours following the crash.", "links": [{"title": "Comair Flight 5191", "link": "https://wikipedia.org/wiki/Comair_Flight_5191"}, {"title": "Takeoff", "link": "https://wikipedia.org/wiki/Takeoff"}, {"title": "Blue Grass Airport", "link": "https://wikipedia.org/wiki/Blue_Grass_Airport"}, {"title": "Lexington, Kentucky", "link": "https://wikipedia.org/wiki/Lexington,_Kentucky"}, {"title": "Hartsfield-Jackson Atlanta International Airport", "link": "https://wikipedia.org/wiki/Hartsfield%E2%80%93J<PERSON>son_Atlanta_International_Airport"}, {"title": "Atlanta", "link": "https://wikipedia.org/wiki/Atlanta"}]}, {"year": "2009", "text": "Internal conflict in Myanmar: The Burmese military junta and ethnic armies begin three days of violent clashes in the Kokang Special Region.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Myanmar_conflict\" title=\"Myanmar conflict\">Internal conflict in Myanmar</a>: The <a href=\"https://wikipedia.org/wiki/State_Peace_and_Development_Council\" title=\"State Peace and Development Council\">Burmese military junta</a> and ethnic armies begin <a href=\"https://wikipedia.org/wiki/2009_Kokang_incident\" title=\"2009 Kokang incident\">three days of violent clashes</a> in the <a href=\"https://wikipedia.org/wiki/Kokang_Special_Region\" class=\"mw-redirect\" title=\"Kokang Special Region\">Kokang Special Region</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Myanmar_conflict\" title=\"Myanmar conflict\">Internal conflict in Myanmar</a>: The <a href=\"https://wikipedia.org/wiki/State_Peace_and_Development_Council\" title=\"State Peace and Development Council\">Burmese military junta</a> and ethnic armies begin <a href=\"https://wikipedia.org/wiki/2009_Kokang_incident\" title=\"2009 Kokang incident\">three days of violent clashes</a> in the <a href=\"https://wikipedia.org/wiki/Kokang_Special_Region\" class=\"mw-redirect\" title=\"Kokang Special Region\">Kokang Special Region</a>.", "links": [{"title": "Myanmar conflict", "link": "https://wikipedia.org/wiki/Myanmar_conflict"}, {"title": "State Peace and Development Council", "link": "https://wikipedia.org/wiki/State_Peace_and_Development_Council"}, {"title": "2009 Kokang incident", "link": "https://wikipedia.org/wiki/2009_Kokang_incident"}, {"title": "Kokang Special Region", "link": "https://wikipedia.org/wiki/Kokang_Special_Region"}]}, {"year": "2011", "text": "Hurricane Irene strikes the United States east coast, killing 47 and causing an estimated $15.6 billion in damage.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Hurricane_Irene\" title=\"Hurricane Irene\">Hurricane <PERSON></a> strikes the United States east coast, killing 47 and causing an estimated $15.6 billion in damage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Irene\" title=\"Hurricane Irene\">Hurricane <PERSON></a> strikes the United States east coast, killing 47 and causing an estimated $15.6 billion in damage.", "links": [{"title": "Hurricane Irene", "link": "https://wikipedia.org/wiki/Hurricane_Irene"}]}], "Births": [{"year": "865", "text": "<PERSON><PERSON><PERSON>, Persian polymath (d. 925)", "html": "865 - <a href=\"https://wikipedia.org/wiki/Rhazes\" class=\"mw-redirect\" title=\"Rhazes\"><PERSON><PERSON><PERSON></a>, Persian polymath (d. 925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rhazes\" class=\"mw-redirect\" title=\"Rhazes\"><PERSON><PERSON><PERSON></a>, Persian polymath (d. 925)", "links": [{"title": "R<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rhazes"}]}, {"year": "1407", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (d. 1425)", "html": "1407 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshikazu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Yoshi<PERSON>zu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (d. 1425)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>zu"}]}, {"year": "1471", "text": "<PERSON>, Duke of Saxony (d. 1539)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (d. 1539)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony\" title=\"<PERSON>, Duke of Saxony\"><PERSON>, Duke of Saxony</a> (d. 1539)", "links": [{"title": "<PERSON>, Duke of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Saxony"}]}, {"year": "1487", "text": "<PERSON> of Brandenburg (d. 1514)", "html": "1487 - <a href=\"https://wikipedia.org/wiki/Anna_of_Brandenburg\" title=\"Anna of Brandenburg\"><PERSON> of Brandenburg</a> (d. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anna_of_Brandenburg\" title=\"Anna of Brandenburg\"><PERSON> of Brandenburg</a> (d. 1514)", "links": [{"title": "Anna of Brandenburg", "link": "https://wikipedia.org/wiki/Anna_of_Brandenburg"}]}, {"year": "1512", "text": "<PERSON>, German theologian (d. 1564)", "html": "1512 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>yl<PERSON>\"><PERSON></a>, German theologian (d. 1564)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>aphylus"}]}, {"year": "1542", "text": "<PERSON>, Duke of Pomerania and Protestant Bishop of Cammin (d. 1600)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> and Protestant Bishop of Cammin (d. 1600)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania\" title=\"<PERSON>, Duke of Pomerania\"><PERSON>, Duke of Pomerania</a> and Protestant Bishop of Cammin (d. 1600)", "links": [{"title": "<PERSON>, Duke of Pomerania", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Pomerania"}]}, {"year": "1545", "text": "<PERSON>, Duke of Parma (d. 1592)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1592)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma\" title=\"<PERSON>, Duke of Parma\"><PERSON>, Duke of Parma</a> (d. 1592)", "links": [{"title": "<PERSON>, Duke of Parma", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Parma"}]}, {"year": "1624", "text": "<PERSON><PERSON><PERSON>, Chinese-Japanese Ming loyalist (d. 1662)", "html": "1624 - <a href=\"https://wikipedia.org/wiki/Koxinga\" title=\"Koxing<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese Ming loyalist (d. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Koxinga\" title=\"<PERSON>xing<PERSON>\"><PERSON><PERSON><PERSON></a>, Chinese-Japanese Ming loyalist (d. 1662)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}]}, {"year": "1637", "text": "<PERSON>, 3rd Baron <PERSON>, English politician, 2nd Proprietor of Maryland (d. 1715)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Proprietors_of_Maryland\" class=\"mw-redirect\" title=\"List of Proprietors of Maryland\">Proprietor of Maryland</a> (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron Baltimore\"><PERSON>, 3rd Baron <PERSON></a>, English politician, 2nd <a href=\"https://wikipedia.org/wiki/List_of_Proprietors_of_Maryland\" class=\"mw-redirect\" title=\"List of Proprietors of Maryland\">Proprietor of Maryland</a> (d. 1715)", "links": [{"title": "<PERSON>, 3rd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Baron_<PERSON>"}, {"title": "List of Proprietors of Maryland", "link": "https://wikipedia.org/wiki/List_of_Proprietors_of_Maryland"}]}, {"year": "1665", "text": "<PERSON>, 1st Earl of Bristol, English politician (d. 1751)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bristol\" title=\"<PERSON>, 1st Earl of Bristol\"><PERSON>, 1st Earl of Bristol</a>, English politician (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bristol\" title=\"<PERSON>, 1st Earl of Bristol\"><PERSON>, 1st Earl of Bristol</a>, English politician (d. 1751)", "links": [{"title": "<PERSON>, 1st Earl of Bristol", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Bristol"}]}, {"year": "1669", "text": "<PERSON>, queen of Sardinia (d. 1728)", "html": "1669 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, queen of Sardinia (d. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans\" title=\"<PERSON>\"><PERSON></a>, queen of Sardinia (d. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Orl%C3%A9ans"}]}, {"year": "1677", "text": "<PERSON>, Austrian general (d. 1748)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_und_Traun\" class=\"mw-redirect\" title=\"<PERSON> von <PERSON> und Traun\"><PERSON> und Traun</a>, Austrian general (d. 1748)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_und_Traun\" class=\"mw-redirect\" title=\"<PERSON> von <PERSON> und Traun\"><PERSON> und Traun</a>, Austrian general (d. 1748)", "links": [{"title": "<PERSON> und T<PERSON>un", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>un"}]}, {"year": "1724", "text": "<PERSON>, Swiss-American pastor, planter, and politician (d. 1781)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American pastor, planter, and politician (d. 1781)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American pastor, planter, and politician (d. 1781)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1730", "text": "<PERSON>, German philosopher and author (d. 1788)", "html": "1730 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1770", "text": "<PERSON>, German philosopher and academic (d. 1831)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German philosopher and academic (d. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Georg <PERSON>\"><PERSON></a>, German philosopher and academic (d. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON><PERSON><PERSON><PERSON>, Peruvian general and politician, 10th and 14th President of Peru (d. 1841)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian general and politician, 10th and 14th President of Peru (d. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Peruvian general and politician, 10th and 14th President of Peru (d. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agust%C3%ADn_Gamarra"}]}, {"year": "1795", "text": "<PERSON>, Maltese politician (d. 1885)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician (d. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese politician (d. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, American minister and theologian (d. 1895)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American minister and theologian (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1809", "text": "<PERSON>, American publisher and politician, 15th Vice President of the United States (d. 1891)", "html": "1809 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 15th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1812", "text": "<PERSON><PERSON>, Hungarian poet and politician, 3rd Prime Minister of Hungary (d. 1869)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/Bertalan_S<PERSON>mere\" title=\"Bertalan Szemere\"><PERSON><PERSON></a>, Hungarian poet and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (d. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_S<PERSON>\" title=\"Bertalan Szemere\"><PERSON><PERSON></a>, Hungarian poet and politician, 3rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Hungary\" title=\"Prime Minister of Hungary\">Prime Minister of Hungary</a> (d. 1869)", "links": [{"title": "Bertalan Szemere", "link": "https://wikipedia.org/wiki/Bertalan_Szemere"}, {"title": "Prime Minister of Hungary", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Hungary"}]}, {"year": "1822", "text": "<PERSON>, American politician, U.S. Representative from Indiana and Democratic vice-presidential nominee (d. 1896)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">U.S. Representative</a> from <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> and Democratic vice-presidential nominee (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, <a href=\"https://wikipedia.org/wiki/United_States_House_of_Representatives\" title=\"United States House of Representatives\">U.S. Representative</a> from <a href=\"https://wikipedia.org/wiki/Indiana\" title=\"Indiana\">Indiana</a> and Democratic vice-presidential nominee (d. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States House of Representatives", "link": "https://wikipedia.org/wiki/United_States_House_of_Representatives"}, {"title": "Indiana", "link": "https://wikipedia.org/wiki/Indiana"}]}, {"year": "1827", "text": "<PERSON>, English-Australian politician, 4th Premier of Queensland (d. 1897)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 4th <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1845", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian architect, designed the Museum of Applied Arts and the Church of St Elisabeth (d. 1914)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect, designed the <a href=\"https://wikipedia.org/wiki/Museum_of_Applied_Arts_(Budapest)\" title=\"Museum of Applied Arts (Budapest)\">Museum of Applied Arts</a> and the <a href=\"https://wikipedia.org/wiki/Church_of_St._Elisabeth_(Bratislava)\" class=\"mw-redirect\" title=\"Church of St. Elisabeth (Bratislava)\">Church of St Elisabeth</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian architect, designed the <a href=\"https://wikipedia.org/wiki/Museum_of_Applied_Arts_(Budapest)\" title=\"Museum of Applied Arts (Budapest)\">Museum of Applied Arts</a> and the <a href=\"https://wikipedia.org/wiki/Church_of_St._Elisabeth_(Bratislava)\" class=\"mw-redirect\" title=\"Church of St. Elisabeth (Bratislava)\">Church of St Elisabeth</a> (d. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96d%C3%B6n_<PERSON><PERSON><PERSON>"}, {"title": "Museum of Applied Arts (Budapest)", "link": "https://wikipedia.org/wiki/Museum_of_Applied_Arts_(Budapest)"}, {"title": "Church of St. Elisabeth (Bratislava)", "link": "https://wikipedia.org/wiki/Church_of_St._Elisabeth_(Bratislava)"}]}, {"year": "1845", "text": "<PERSON>, Estonian-Russian historian, lawyer, and diplomat (d. 1909)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian historian, lawyer, and diplomat (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Russian historian, lawyer, and diplomat (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, Ukrainian author and poet (d. 1916)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian author and poet (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian author and poet (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Italian mathematician and philosopher (d. 1932)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and philosopher (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German gymnast (d. 1919)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rtner\" title=\"<PERSON>\"><PERSON></a>, German gymnast (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rt<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German gymnast (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rtner"}]}, {"year": "1865", "text": "<PERSON>, American archaeologist and historian (d. 1935)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and historian (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American general and politician, 30th Vice President of the United States, Nobel Prize laureate (d. 1951)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general and politician, 30th <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1868", "text": "<PERSON>, Korean general and activist (d. 1943)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Korean general and activist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Korean general and activist (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1870", "text": "<PERSON><PERSON>, Mexican journalist, poet, and diplomat (d. 1919)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Amado_Nervo\" title=\"Amado Nervo\"><PERSON><PERSON> Nervo</a>, Mexican journalist, poet, and diplomat (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amado_Nervo\" title=\"Amado Nervo\"><PERSON><PERSON> Nervo</a>, Mexican journalist, poet, and diplomat (d. 1919)", "links": [{"title": "Amado Nervo", "link": "https://wikipedia.org/wiki/Amado_Nervo"}]}, {"year": "1871", "text": "<PERSON>, American novelist and journalist (d. 1945)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist and journalist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1874", "text": "<PERSON>, German chemist and engineer, Nobel Prize laureate (d. 1940)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1875", "text": "<PERSON><PERSON>, American biologist, philanthropist, and activist (d. 1967)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist, philanthropist, and activist (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American biologist, philanthropist, and activist (d. 1967)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, English engineer and businessman, co-founded Rolls-Royce Limited (d. 1910)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Rolls\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a> (d. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charles Rolls\"><PERSON></a>, English engineer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Rolls-Royce_Limited\" title=\"Rolls-Royce Limited\">Rolls-Royce Limited</a> (d. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Rolls-Royce Limited", "link": "https://wikipedia.org/wiki/Rolls-Royce_Limited"}]}, {"year": "1877", "text": "<PERSON>, Swiss lawyer and politician, 48th President of the Swiss Confederation (d. 1963)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss lawyer and politician, 48th <a href=\"https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation\" class=\"mw-redirect\" title=\"List of Presidents of the Swiss Confederation\">President of the Swiss Confederation</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Presidents of the Swiss Confederation", "link": "https://wikipedia.org/wiki/List_of_Presidents_of_the_Swiss_Confederation"}]}, {"year": "1878", "text": "<PERSON><PERSON><PERSON>, Russian general (d. 1928)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>rangel\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>rangel\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian general (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>el"}]}, {"year": "1884", "text": "<PERSON>, French lawyer and politician, President of the French Republic (d. 1966)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_French_Republic\" class=\"mw-redirect\" title=\"President of the French Republic\">President of the French Republic</a> (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the French Republic", "link": "https://wikipedia.org/wiki/President_of_the_French_Republic"}]}, {"year": "1884", "text": "<PERSON>, British biologist, member of the 1910-1913 Terra Nova Expedition (d. 1963)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biologist, member of the 1910-1913 <a href=\"https://wikipedia.org/wiki/Terra_Nova_Expedition\" title=\"Terra Nova Expedition\"><i>Terra Nova</i> Expedition</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British biologist, member of the 1910-1913 <a href=\"https://wikipedia.org/wiki/Terra_Nova_Expedition\" title=\"Terra Nova Expedition\"><i>Terra Nova</i> Expedition</a> (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Terra Nova Expedition", "link": "https://wikipedia.org/wiki/Terra_Nova_Expedition"}]}, {"year": "1886", "text": "<PERSON>, English viola player and composer (d. 1979)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English viola player and composer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, English viola player and composer (d. 1979)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1890", "text": "<PERSON>, American-French photographer and painter (d. 1976)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ray\" title=\"Man Ray\"><PERSON></a>, American-French photographer and painter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ray\" title=\"Man Ray\"><PERSON></a>, American-French photographer and painter (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Hungarian archaeologist and historian (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ldi\" title=\"<PERSON>\"><PERSON></a>, Hungarian archaeologist and historian (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6ldi\" title=\"<PERSON>\"><PERSON></a>, Hungarian archaeologist and historian (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andreas_Alf%C3%B6ldi"}]}, {"year": "1896", "text": "<PERSON><PERSON>, Japanese author and poet (d. 1933)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and poet (d. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and poet (d. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON>, Canadian businessman and politician, 19th Lieutenant Governor of Quebec (d. 1963)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician, 19th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian businessman and politician, 19th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec\" title=\"Lieutenant Governor of Quebec\">Lieutenant Governor of Quebec</a> (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Fauteux"}, {"title": "Lieutenant Governor of Quebec", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_Quebec"}]}, {"year": "1899", "text": "<PERSON><PERSON> <PERSON><PERSON>, English novelist (d. 1966)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_<PERSON>er\" title=\"C. S. Forester\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._<PERSON><PERSON>_Forester\" title=\"C. S. Forester\"><PERSON><PERSON> <PERSON><PERSON></a>, English novelist (d. 1966)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Estonian architect (d. 1963)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian architect (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "1904", "text": "<PERSON><PERSON>, English author (d. 1983)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lofts\"><PERSON><PERSON></a>, English author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lofts\"><PERSON><PERSON></a>, English author (d. 1983)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, American businessman, publisher, and diplomat, founded J.H. Whitney & Company (d. 1982)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, publisher, and diplomat, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Whitney_%26_Company\" class=\"mw-redirect\" title=\"J.H. Whitney &amp; Company\">J.H. Whitney &amp; Company</a> (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, publisher, and diplomat, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_%26_Company\" class=\"mw-redirect\" title=\"J.H. Whitney &amp; Company\">J.H. Whitney &amp; Company</a> (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "J.H. Whitney & Company", "link": "https://wikipedia.org/wiki/<PERSON>.<PERSON><PERSON>_Whitney_%26_Company"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Greek soldier (d. 1945)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>uchi<PERSON>\"><PERSON><PERSON></a>, Greek soldier (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek soldier (d. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Velouchiotis"}]}, {"year": "1906", "text": "<PERSON>, American murderer and body snatcher, The Butcher of Plainfield (d. 1982)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer and body snatcher, <PERSON> of Plainfield (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murderer and body snatcher, The Butcher of Plainfield (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Australian cricketer and manager (d. 2001)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and manager (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American commander and politician, 36th President of the United States (d. 1973)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American commander and politician, 36th <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President of the United States</a> (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}]}, {"year": "1909", "text": "<PERSON><PERSON><PERSON><PERSON>, Belgian cyclist (d. 1966)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Sylv%C3%A8re_<PERSON>\" title=\"<PERSON>yl<PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sylv%C3%A8re_<PERSON>\" title=\"<PERSON>ylv<PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Belgian cyclist (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sylv%C3%A8re_Maes"}]}, {"year": "1909", "text": "<PERSON>, French race car driver (d. 2001)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American saxophonist and clarinet player (d. 1959)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and clarinet player (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, English actress and dancer (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and dancer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Mexican journalist (d. 1980)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Guinness\"><PERSON></a>, Mexican journalist (d. 1980)", "links": [{"title": "Gloria Guinness", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2011)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" class=\"mw-redirect\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2011)", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1916", "text": "<PERSON>, English engineer, co-designed the Range Rover (d. 1991)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Range_Rover_Classic\" title=\"Range Rover Classic\">Range Rover</a> (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English engineer, co-designed the <a href=\"https://wikipedia.org/wiki/Range_Rover_Classic\" title=\"Range Rover Classic\">Range Rover</a> (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Range Rover Classic", "link": "https://wikipedia.org/wiki/Range_Rover_Classic"}]}, {"year": "1916", "text": "<PERSON>, South African cricketer and rugby player (d. 1993)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and rugby player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer and rugby player (d. 1993)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1916", "text": "<PERSON>, American actress and comedian (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, American baseball player, coach, and manager (d. 1986)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/Peanuts_Lowrey\" title=\"Peanuts Lowrey\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peanuts_Lowrey\" title=\"Peanuts Lowrey\"><PERSON><PERSON><PERSON></a>, American baseball player, coach, and manager (d. 1986)", "links": [{"title": "Peanuts Lowrey", "link": "https://wikipedia.org/wiki/Peanuts_Lowrey"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Dutch economist and politician, Prime Minister of the Netherlands (d. 2001)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American baseball player and coach (d. 1972)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Pee_Wee_Butts\" title=\"Pee Wee Butts\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and coach (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pee_We<PERSON>_Butts\" title=\"Pee Wee Butts\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player and coach (d. 1972)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Pee_<PERSON><PERSON>_<PERSON>ts"}]}, {"year": "1919", "text": "<PERSON>, American singer-songwriter and pianist (d. 2007)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Murray_Grand\" title=\"Murray Grand\"><PERSON></a>, American singer-songwriter and pianist (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Murray_Grand\" title=\"Murray Grand\"><PERSON></a>, American singer-songwriter and pianist (d. 2007)", "links": [{"title": "Murray Grand", "link": "https://wikipedia.org/wiki/Murray_Grand"}]}, {"year": "1920", "text": "<PERSON>, American football player (d. 2008)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Baron <PERSON> of Killead, Northern Irish soldier and politician (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead\" title=\"<PERSON>, Baron <PERSON> of Killead\"><PERSON>, Baron <PERSON> of Killead</a>, Northern Irish soldier and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead\" title=\"<PERSON>, Baron <PERSON> of Killead\"><PERSON>, Baron <PERSON> of Killead</a>, Northern Irish soldier and politician (d. 2015)", "links": [{"title": "<PERSON>, Baron <PERSON> of Killead", "link": "https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Killead"}]}, {"year": "1921", "text": "<PERSON>, Duke of Mecklenburg (d. 1996)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg\" title=\"<PERSON>, Duke of Mecklenburg\"><PERSON>, Duke of Mecklenburg</a> (d. 1996)", "links": [{"title": "<PERSON>, Duke of Mecklenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Mecklenburg"}]}, {"year": "1921", "text": "<PERSON>, American actor, director, and screenwriter (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Leo Penn\"><PERSON></a>, American actor, director, and screenwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Penn\" title=\"Leo Penn\"><PERSON></a>, American actor, director, and screenwriter (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Leo_Penn"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Dutch physician and politician, Minister of Defence for The Netherlands (d. 2012)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Defence of the Netherlands\">Minister of Defence for The Netherlands</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physician and politician, <a href=\"https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands\" class=\"mw-redirect\" title=\"List of Ministers of Defence of the Netherlands\">Minister of Defence for The Netherlands</a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Ministers of Defence of the Netherlands", "link": "https://wikipedia.org/wiki/List_of_Ministers_of_Defence_of_the_Netherlands"}]}, {"year": "1923", "text": "<PERSON>, English footballer and manager (d. 2013)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Australian journalist and poet (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and poet (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and poet (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American lawyer and jurist (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and jurist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and jurist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian cardinal (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>nza_di_Montezemolo\" title=\"Andrea Cordero Lanza di Montezemolo\"><PERSON> Montezemolo</a>, Italian cardinal (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_di_Montezemolo\" title=\"<PERSON> Cordero Lanza di Montezemolo\"><PERSON> Monteze<PERSON>lo</a>, Italian cardinal (d. 2017)", "links": [{"title": "<PERSON> Lanza di Montezemolo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English footballer and manager (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Nat_Lo<PERSON>house\" title=\"Nat Lofthouse\"><PERSON></a>, English footballer and manager (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Nat Lofthouse\"><PERSON></a>, English footballer and manager (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nat_Lofthouse"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Japanese author and critic (d. 2012)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese author and critic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mar<PERSON>\"><PERSON><PERSON></a>, Japanese author and critic (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>a"}]}, {"year": "1925", "text": "<PERSON>, Australian politician, 34th Premier of Tasmania (d. 1989)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 34th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian poet and academic (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian poet and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American bluegrass singer-songwriter and guitarist (d. 1966)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer-songwriter and guitarist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bluegrass singer-songwriter and guitarist (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American-German chemist and composer (d. 2008)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German chemist and composer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-German chemist and composer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Norwegian computer scientist and academic (d. 2002)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer scientist and academic (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian computer scientist and academic (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Hungarian lawyer and politician, 54th Prime Minister of Hungary", "html": "1928 - <a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian lawyer and politician, 54th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary\" class=\"mw-redirect\" title=\"List of Prime Ministers of Hungary\">Prime Minister of Hungary</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_<PERSON>ross"}, {"title": "List of Prime Ministers of Hungary", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Hungary"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, South African politician, Chief Minister of KwaZulu (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_KwaZulu\" class=\"mw-redirect\" title=\"Chief Minister of KwaZulu\">Chief Minister of KwaZulu</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South African politician, <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_KwaZulu\" class=\"mw-redirect\" title=\"Chief Minister of KwaZulu\">Chief Minister of KwaZulu</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "Chief Minister of KwaZulu", "link": "https://wikipedia.org/wiki/Chief_Minister_of_KwaZulu"}]}, {"year": "1928", "text": "<PERSON>, American philanthropist (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American novelist, playwright, and songwriter (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and songwriter (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, playwright, and songwriter (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American wrestler and promoter (d. 2014)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian-American wrestler and promoter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian-American wrestler and promoter (d. 2014)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Norwegian writer (d. 2023)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian writer (d. 2023)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Iranian wrestler and politician (d. 1968)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian wrestler and politician (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian wrestler and politician (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Indian-American guru and poet (d. 2007)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/Sri_Chinmoy\" title=\"Sri Chinmoy\"><PERSON></a>, Indian-American guru and poet (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Chinmoy\" title=\"Sri Chinmoy\"><PERSON></a>, Indian-American guru and poet (d. 2007)", "links": [{"title": "Sri Chinmoy", "link": "https://wikipedia.org/wiki/Sri_Chinmoy"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and coach (d. 2021)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player and coach (d. 2021)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1932", "text": "<PERSON><PERSON>, Dutch footballer and manager (d. 2008)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>r_<PERSON>rom\" title=\"Cor <PERSON>rom\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Cor <PERSON>rom\"><PERSON><PERSON></a>, Dutch footballer and manager (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cor_<PERSON>rom"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English historian and author", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English historian and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Anton<PERSON>_Fraser"}]}, {"year": "1935", "text": "<PERSON>, American baseball player (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, English author", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American screenwriter and producer (d. 2014)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American scholar and author (d. 2018)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Taiwanese politician, Vice President of the Republic of China", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China\" title=\"Vice President of the Republic of China\">Vice President of the Republic of China</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Taiwanese politician, <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China\" title=\"Vice President of the Republic of China\">Vice President of the Republic of China</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vice President of the Republic of China", "link": "https://wikipedia.org/wiki/Vice_President_of_the_Republic_of_China"}]}, {"year": "1937", "text": "<PERSON>, American pianist and composer (d. 2007)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American pop singer and actor", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" class=\"mw-redirect\" title=\"<PERSON> (American singer)\"><PERSON></a>, American pop singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)\" class=\"mw-redirect\" title=\"<PERSON> (American singer)\"><PERSON></a>, American pop singer and actor", "links": [{"title": "<PERSON> (American singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_singer)"}]}, {"year": "1939", "text": "<PERSON>, American travel writer and historian", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Heat-Moon\" title=\"<PERSON> Heat-Moon\"><PERSON> Heat-Moon</a>, American travel writer and historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Heat-Moon\" title=\"<PERSON> Heat-Moon\"><PERSON>Moon</a>, American travel writer and historian", "links": [{"title": "<PERSON> Heat-Moon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Heat-Moon"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and producer (d. 2005)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Yugoslav tennis player and coach", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Yugoslav tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Yugoslav tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Pili%C4%87"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American singer and accordion player (d. 2008)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and accordion player (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and accordion player (d. 2008)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rne<PERSON>_<PERSON>aux"}]}, {"year": "1940", "text": "<PERSON>, American guitarist (d. 1994)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON><PERSON>, Cape Verdean singer (d. 2011)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Ces%C3%A1ria_%C3%89vora\" title=\"Cesária Évora\"><PERSON><PERSON><PERSON><PERSON></a>, Cape Verdean singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ces%C3%A1ria_%C3%89vora\" title=\"Cesária Évora\"><PERSON><PERSON><PERSON><PERSON></a>, Cape Verdean singer (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ces%C3%A1ria_%C3%89vora"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Hungarian water polo player and swimmer (d. 2014)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Konr%C3%A1d\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian water polo player and swimmer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A1nos_Konr%C3%A1d\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian water polo player and swimmer (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A1nos_Konr%C3%A1d"}]}, {"year": "1941", "text": "<PERSON>, American actor", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American keyboard player and songwriter (d. 2019)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Daryl Dragon\"><PERSON></a>, American keyboard player and songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Daryl Dragon\"><PERSON></a>, American keyboard player and songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Canadian educator and politician, 3rd Premier of Newfoundland and Labrador", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland_and_Labrador\" title=\"Premier of Newfoundland and Labrador\">Premier of Newfoundland and Labrador</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Newfoundland_and_Labrador\" title=\"Premier of Newfoundland and Labrador\">Premier of Newfoundland and Labrador</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/Premier_of_Newfoundland_and_Labrador"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter and pianist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American lieutenant and politician, Medal of Honor recipient, 35th Governor of Nebraska", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Nebraska\" class=\"mw-redirect\" title=\"Governor of Nebraska\">Governor of Nebraska</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}, {"title": "Governor of Nebraska", "link": "https://wikipedia.org/wiki/Governor_of_Nebraska"}]}, {"year": "1943", "text": "Tuesday <PERSON><PERSON>, American model and actress", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Tuesday_Weld\" title=\"Tuesday Weld\">Tuesday Weld</a>, American model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tuesday_Weld\" title=\"Tuesday Weld\">Tuesday Weld</a>, American model and actress", "links": [{"title": "Tuesday Weld", "link": "https://wikipedia.org/wiki/Tuesday_Weld"}]}, {"year": "1944", "text": "<PERSON><PERSON> <PERSON><PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer and bass player (d. 2021)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and bass player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Canadian lawyer and judge", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German actress", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A4ge<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_S%C3%A4<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marianne_S%C3%A4gebrecht"}]}, {"year": "1946", "text": "<PERSON>, Barbadian cricketer and manager", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American actress and model", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, Turkish historian and academic", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ta<PERSON>\"><PERSON><PERSON></a>, Turkish historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish historian and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Halil_Berktay"}]}, {"year": "1947", "text": "<PERSON>, American engineer and producer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German director, producer, and screenwriter (d. 2009)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German director, producer, and screenwriter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, New Zealand cricketer and politician", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and politician", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1947", "text": "<PERSON>, South African cricketer and sportscaster (d. 2002)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and sportscaster (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer and sportscaster (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "Sgt<PERSON>, American wrestler", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Sgt._Slaughter\" title=\"Sgt. Slaughter\">Sgt. <PERSON>laughter</a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sgt._Slaughter\" title=\"Sgt. Slaughter\">Sgt. Slaughter</a>, American wrestler", "links": [{"title": "Sgt<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>._<PERSON>laughter"}]}, {"year": "1948", "text": "<PERSON>, English historian and curator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ow"}]}, {"year": "1948", "text": "<PERSON>, French director and screenwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2022) ", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2022) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American computer scientist, engineer, and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, engineer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist, engineer, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Irish soprano", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish soprano", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American comedian and actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Scottish bass player and songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, Scottish bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)\" title=\"<PERSON> (British musician)\"><PERSON></a>, Scottish bass player and songwriter", "links": [{"title": "<PERSON> (British musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_musician)"}]}, {"year": "1950", "text": "<PERSON>, English lexicographer and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lexicographer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lexicographer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American baseball player and manager", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Buddy Bell\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American football player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American-Canadian criminologist and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian criminologist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian criminologist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor and comedian (d. 2023)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American businessman and politician (d. 2020)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian singer-songwriter, guitarist, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>son"}]}, {"year": "1953", "text": "<PERSON>, English journalist and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Swedish actor, director, and playwright", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actor, director, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English tennis player and sportscaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(tennis)\" title=\"<PERSON> (tennis)\"><PERSON></a>, English tennis player and sportscaster", "links": [{"title": "<PERSON> (tennis)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tennis)"}]}, {"year": "1954", "text": "<PERSON><PERSON>, English physician and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English physician and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English race car driver", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American cinematographer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, American cinematographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cinematographer)\" title=\"<PERSON> (cinematographer)\"><PERSON></a>, American cinematographer", "links": [{"title": "<PERSON> (cinematographer)", "link": "https://wikipedia.org/wiki/<PERSON>(cinematographer)"}]}, {"year": "1955", "text": "<PERSON>, American actress", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and bass player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American game designer and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American game designer and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, German golfer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Russian engineer and astronaut", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian engineer and astronaut", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Belgian author, poet, and playwright", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author, poet, and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author, poet, and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, British police officer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British police officer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British police officer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Mexican singer, actress and TV hostess", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Romo\"><PERSON><PERSON></a>, Mexican singer, actress and TV hostess", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Romo\"><PERSON><PERSON></a>, Mexican singer, actress and TV hostess", "links": [{"title": "Daniela Romo", "link": "https://wikipedia.org/wiki/Daniel<PERSON>_Romo"}]}, {"year": "1959", "text": "<PERSON>, Austrian race car driver", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Colombian painter and sculptor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American engineer and academic (d. 2006)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer and academic (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American engineer and academic (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Den<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Norwegian pianist and composer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Fr<PERSON>_<PERSON>\" title=\"Frode Fjellheim\"><PERSON><PERSON></a>, Norwegian pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frode_<PERSON>\" title=\"Frode Fjellheim\"><PERSON><PERSON></a>, Norwegian pianist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frode_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Hungarian author and poet", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Pet%C5%91cz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author and poet", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A1s_Pet%C5%91cz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian author and poet", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A1s_Pet%C5%91cz"}]}, {"year": "1959", "text": "<PERSON><PERSON>, English journalist and novelist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and novelist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English journalist and novelist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, American singer, producer, and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer, producer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English television host and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" class=\"mw-redirect\" title=\"<PERSON> (television presenter)\"><PERSON></a>, English television host and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_presenter)\" class=\"mw-redirect\" title=\"<PERSON> (television presenter)\"><PERSON></a>, English television host and actor", "links": [{"title": "<PERSON> (television presenter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(television_presenter)"}]}, {"year": "1961", "text": "<PERSON>, American fashion designer and film director", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Tom Ford\"><PERSON></a>, American fashion designer and film director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Tom Ford\"><PERSON></a>, American fashion designer and film director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, New Zealand rugby player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, German footballer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Canadian ice hockey player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian actor, director, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Canadian serial rapist and murderer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial rapist and murderer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian serial rapist and murderer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American lawyer and politician", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}]}, {"year": "1965", "text": "<PERSON>, Zimbabwean cricketer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Zimbabwean cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Greek-Australian footballer and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Australian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-Australian footballer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>lou"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Dutch rower", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch rower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch rower", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Colombian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Estonian lawyer and politician, 14th Prime Minister of Estonia", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Juhan_Parts\" title=\"Juhan Parts\"><PERSON><PERSON></a>, Estonian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juhan_Parts\" title=\"Juhan Parts\"><PERSON><PERSON></a>, Estonian lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Estonia\" title=\"Prime Minister of Estonia\">Prime Minister of Estonia</a>", "links": [{"title": "Juhan Parts", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Estonia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Estonia"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Filipino singer-songwriter, producer, and actor", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Ogie_Alcasid\" title=\"Ogie Alcasid\"><PERSON><PERSON></a>, Filipino singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ogie_Alcasid\" title=\"Ogie Alcasid\"><PERSON><PERSON></a>, Filipino singer-songwriter, producer, and actor", "links": [{"title": "Ogie Alcasid", "link": "https://wikipedia.org/wiki/Ogie_Alcasid"}]}, {"year": "1967", "text": "<PERSON>, American football player and sportscaster", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1968", "text": "<PERSON> \"<PERSON><PERSON>\" <PERSON>, American musician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Bob<PERSON>%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON><PERSON>\" <PERSON><PERSON><PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22Bob<PERSON>%22_<PERSON><PERSON><PERSON>\" title='<PERSON> \"<PERSON>\" <PERSON>'><PERSON> \"<PERSON>\" <PERSON></a>, American musician", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Bobo%22_<PERSON><PERSON>a"}]}, {"year": "1968", "text": "<PERSON>, Israeli-American computer scientist and academic", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand golfer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, New Zealand golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, New Zealand golfer", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1968", "text": "<PERSON>, New Zealand rugby player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Matthew_<PERSON>\" title=\"Matthew Ridge\"><PERSON></a>, New Zealand rugby player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Matthew_Ridge\" title=\"Matthew Ridge\"><PERSON></a>, New Zealand rugby player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matthew_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Mexican-American dog trainer, television personality, and author", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American dog trainer, television personality, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American dog trainer, television personality, and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English actor, comedian and writer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, comedian and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American actress and director", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian cricketer and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English cricketer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, British-American bass player. songwriter, and record producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American bass player. songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-American bass player. songwriter, and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American baseball player and manager", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Italian mountaineer (d. 2008)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mountaineer (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Dutch footballer and manager", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, South Korean-American journalist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean-American journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Japanese runner", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese runner", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, German lawyer and politician", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ayg%C3%BCl_%C3%96zkan\" title=\"Aygül Özkan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayg%C3%BCl_%C3%96zkan\" title=\"Aygül Özkan\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, German lawyer and politician", "links": [{"title": "Aygül Özkan", "link": "https://wikipedia.org/wiki/Ayg%C3%BCl_%C3%96zkan"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dutch field hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jaa<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aa<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON><PERSON></a>, Dutch field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jaa<PERSON>-<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>aa<PERSON>-<PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON><PERSON></a>, Dutch field hockey player", "links": [{"title": "Jaap-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jaa<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "The <PERSON>, Indian professional wrestler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_<PERSON>_<PERSON>\" title=\"The Great Khali\">The <PERSON></a>, Indian professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Great_<PERSON><PERSON>\" title=\"The Great Khali\">The <PERSON></a>, Indian professional wrestler", "links": [{"title": "The Great Khali", "link": "https://wikipedia.org/wiki/The_<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, English heptathlete", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English heptathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jimmy_Pop\" title=\"Jimmy Pop\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jimmy_Pop\" title=\"Jimmy Pop\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jimmy_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Filipino comedian, actress, television host and singer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino comedian, actress, television host and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>k<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino comedian, actress, television host and singer", "links": [{"title": "Pokwang", "link": "https://wikipedia.org/wiki/<PERSON>kwang"}]}, {"year": "1973", "text": "<PERSON>, Welsh footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Turkish singer-songwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>t"}]}, {"year": "1973", "text": "<PERSON>, Swedish historian and author", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1974", "text": "<PERSON>, New Zealand cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1974", "text": "<PERSON>, Puerto Rican-American baseball player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Vidro\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_Vidro\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_Vidro"}]}, {"year": "1974", "text": "<PERSON>, Pakistani cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1974)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1974)\"><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer,_born_1974)\" class=\"mw-redirect\" title=\"<PERSON> (cricketer, born 1974)\"><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON> (cricketer, born 1974)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(cricketer,_born_1974)"}]}, {"year": "1975", "text": "<PERSON>, American golfer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American rapper, songwriter and pastor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Ma<PERSON>\" title=\"Ma<PERSON>\"><PERSON><PERSON></a>, American rapper, songwriter and pastor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ma<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American rapper, songwriter and pastor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mase"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Puerto Rican-American skier and television host", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American skier and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Puerto Rican-American skier and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Canadian actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, French astronomer and biologist", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and biologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and biologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1976", "text": "Milano Collection A.T., Japanese wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Milano_Collection_A.T.\" class=\"mw-redirect\" title=\"Milano Collection A.T.\">Milano Collection A.T.</a>, Japanese wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milano_Collection_A.T.\" class=\"mw-redirect\" title=\"Milano Collection A.T.\">Milano Collection A.T.</a>, Japanese wrestler", "links": [{"title": "Milano Collection A.T.", "link": "https://wikipedia.org/wiki/Milano_Collection_A.T."}]}, {"year": "1976", "text": "<PERSON>, Spanish-Swiss tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish-Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1"}]}, {"year": "1976", "text": "<PERSON>, Australian race car driver", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Brazilian-Portuguese footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Deco\" title=\"<PERSON>o\"><PERSON><PERSON></a>, Brazilian-Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deco\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian-Portuguese footballer", "links": [{"title": "Deco", "link": "https://wikipedia.org/wiki/Deco"}]}, {"year": "1977", "text": "<PERSON>, American baseball player (d. 2013)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)\" title=\"<PERSON> (baseball, born 1977)\"><PERSON></a>, American baseball player (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)\" title=\"<PERSON> (baseball, born 1977)\"><PERSON></a>, American baseball player (d. 2013)", "links": [{"title": "<PERSON> (baseball, born 1977)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball,_born_1977)"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, American actress and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/Demet<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/De<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Demetria <PERSON>", "link": "https://wikipedia.org/wiki/Demetria_<PERSON><PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian violinist", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actor and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Czech ice hockey player (d. 2011)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%AFnek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%AFnek\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C5%AFnek"}]}, {"year": "1979", "text": "<PERSON>, American speed skater", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON> (speed skater)\"><PERSON></a>, American speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speed_skater)\" title=\"<PERSON> (speed skater)\"><PERSON></a>, American speed skater", "links": [{"title": "<PERSON> (speed skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speed_skater)"}]}, {"year": "1981", "text": "<PERSON>, Canadian actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Brazilian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Beninese-Central African nurse and politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese-Central African nurse and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Beninese-Central African nurse and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Italian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English rugby player and physiotherapist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and physiotherapist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby player and physiotherapist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Ghanaian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Mu<PERSON>ri\" title=\"<PERSON><PERSON> Muntari\"><PERSON><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Muntari\" title=\"<PERSON><PERSON> Muntari\"><PERSON><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, English footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Croatian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nikica_Jelavi%C4%87"}]}, {"year": "1985", "text": "<PERSON>, Romanian-American painter and sculptor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American painter and sculptor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-American painter and sculptor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Serbian-Bosnian author and translator", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian-Bosnian author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serbian-Bosnian author and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lana_Basta%C5%A1i%C4%87"}]}, {"year": "1986", "text": "<PERSON>, Austrian politician, 25th Chancellor of Austria", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 25th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 25th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1986", "text": "<PERSON>, American singer and actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1987", "text": "<PERSON>, English-Jamaican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Jamaican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Jamaican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actress and singer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aVega\" title=\"<PERSON><PERSON> PenaVega\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>aVega\" title=\"<PERSON><PERSON> PenaVega\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Alexa PenaVega", "link": "https://wikipedia.org/wiki/Alexa_PenaVega"}]}, {"year": "1989", "text": "<PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American figure skater and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American athlete (d. 2023)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American athlete (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, South Korean actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>ol"}]}, {"year": "1992", "text": "<PERSON>, American actor and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1992", "text": "<PERSON>, German singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Japanese actress and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_Goriki\" title=\"Ayame Goriki\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ayam<PERSON>_Goriki\" title=\"<PERSON>yame Goriki\"><PERSON><PERSON><PERSON></a>, Japanese actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ayame_Go<PERSON>i"}]}, {"year": "1993", "text": "<PERSON>, German figure skater", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French cyclist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, American actor", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, English actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian race car driver", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Russian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Russian race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1997", "text": "<PERSON>, Brazilian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1"}]}, {"year": "1998", "text": "<PERSON>, American basketball player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Portuguese footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Math<PERSON> Nunes\"><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Math<PERSON> Nunes\"><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Matheus_Nunes"}]}, {"year": "1998", "text": "<PERSON>, American rapper, singer, and songwriter", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Rod_Wave\" title=\"Rod Wave\"><PERSON></a>, American rapper, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rod_Wave\" title=\"Rod Wave\"><PERSON></a>, American rapper, singer, and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rod_Wave"}]}, {"year": "2001", "text": "<PERSON>, German basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, German basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, German basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "2006", "text": "<PERSON>, South Korean footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyeok\" title=\"<PERSON>hyeok\"><PERSON></a>, South Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyeok\" title=\"<PERSON>hyeok\"><PERSON></a>, South Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-hyeok"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American actress", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ariana_Greenblatt"}]}], "Deaths": [{"year": "542", "text": "<PERSON><PERSON> of Arles, French bishop and saint (b. 470)", "html": "542 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Arles\" title=\"<PERSON><PERSON> of Arles\"><PERSON><PERSON> of Arles</a>, French bishop and saint (b. 470)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Arles\" title=\"<PERSON><PERSON> of Arles\"><PERSON><PERSON> of Arles</a>, French bishop and saint (b. 470)", "links": [{"title": "<PERSON><PERSON> of Arles", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_of_Arles"}]}, {"year": "749", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>, Persian general", "html": "749 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%27i\" title=\"<PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON></a>, Persian general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_ibn_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%27i\" title=\"<PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON> ibn <PERSON><PERSON><PERSON></a>, Persian general", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>%27i"}]}, {"year": "827", "text": "<PERSON>", "html": "827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON> II</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Eugene II\"><PERSON> II</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "923", "text": "<PERSON><PERSON><PERSON><PERSON>, queen of Italy and Holy Roman Empress", "html": "923 - <a href=\"https://wikipedia.org/wiki/Ageltrude\" title=\"Ageltrude\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy_(Holy_Roman_Empire)\" title=\"Kingdom of Italy (Holy Roman Empire)\">Italy</a> and <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empress</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ageltrude\" title=\"Ageltrude\"><PERSON><PERSON><PERSON><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy_(Holy_Roman_Empire)\" title=\"Kingdom of Italy (Holy Roman Empire)\">Italy</a> and <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Holy Roman Empress</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ltrude"}, {"title": "Kingdom of Italy (Holy Roman Empire)", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy_(Holy_Roman_Empire)"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}]}, {"year": "1146", "text": "King <PERSON> III of Denmark", "html": "1146 - <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Denmark\" title=\"<PERSON> of Denmark\"><PERSON> of Denmark</a>", "links": [{"title": "<PERSON> of Denmark", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Denmark"}]}, {"year": "1255", "text": "<PERSON> Saint <PERSON> of Lincoln (b. 1247)", "html": "1255 - <a href=\"https://wikipedia.org/wiki/Little_Saint_<PERSON>_<PERSON>_Lincoln\" title=\"Little Saint Hugh of Lincoln\">Little Saint <PERSON> of Lincoln</a> (b. 1247)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Little_Saint_<PERSON>_<PERSON>_Lincoln\" title=\"Little Saint Hugh of Lincoln\">Little Saint <PERSON> of Lincoln</a> (b. 1247)", "links": [{"title": "Little Saint Hugh of Lincoln", "link": "https://wikipedia.org/wiki/Little_Saint_Hugh_<PERSON>_Lincoln"}]}, {"year": "1312", "text": "<PERSON>, Duke of Brittany (b. 1261)", "html": "1312 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1261)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1261)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1394", "text": "Emperor <PERSON><PERSON><PERSON><PERSON> of Japan (b. 1343)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%8Dkei\" title=\"Emperor Ch<PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1343)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Ch%C5%8Dkei\" title=\"Emperor Ch<PERSON><PERSON><PERSON>\">Emperor <PERSON><PERSON><PERSON><PERSON></a> of Japan (b. 1343)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Ch%C5%8Dkei"}]}, {"year": "1450", "text": "<PERSON>, 6th Baron <PERSON>, English politician (b. 1395)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician (b. 1395)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>\" title=\"<PERSON>, 6th Baron <PERSON>\"><PERSON>, 6th Baron <PERSON></a>, English politician (b. 1395)", "links": [{"title": "<PERSON>, 6th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_6th_Baron_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1521", "text": "<PERSON><PERSON><PERSON>, Flemish composer (b. 1450)", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish composer (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish composer (b. 1450)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_des_Prez"}]}, {"year": "1545", "text": "<PERSON><PERSON><PERSON>, Polish archbishop (b. 1487)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/Piotr_Gamrat\" title=\"<PERSON><PERSON><PERSON> Gamrat\"><PERSON><PERSON><PERSON></a>, Polish archbishop (b. 1487)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>otr_Gamrat\" title=\"<PERSON>otr Gamrat\"><PERSON><PERSON><PERSON></a>, Polish archbishop (b. 1487)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>otr_<PERSON>"}]}, {"year": "1576", "text": "<PERSON><PERSON><PERSON>, Italian painter and educator (b. 1488)", "html": "1576 - <a href=\"https://wikipedia.org/wiki/Titian\" title=\"Titian\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1488)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Titian\" title=\"Titian\"><PERSON><PERSON><PERSON></a>, Italian painter and educator (b. 1488)", "links": [{"title": "Titian", "link": "https://wikipedia.org/wiki/Titian"}]}, {"year": "1590", "text": "<PERSON> <PERSON><PERSON> (b. 1521)", "html": "1590 - <a href=\"https://wikipedia.org/wiki/Pope_Sixtus_V\" title=\"Pope Sixtus V\"><PERSON> <PERSON><PERSON> V</a> (b. 1521)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Sixtus_V\" title=\"Pope Sixtus V\"><PERSON> <PERSON><PERSON> V</a> (b. 1521)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/Pope_Six<PERSON>_V"}]}, {"year": "1611", "text": "<PERSON><PERSON>, Spanish composer (b. c. 1548)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Luis_de_Victoria\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish composer (b. c. 1548)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Luis_de_Victoria\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish composer (b. c. 1548)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1635", "text": "<PERSON><PERSON>, Spanish poet and playwright (b. 1562)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and playwright (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish poet and playwright (b. 1562)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, Spanish painter and educator (b. 1598)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n\" title=\"Francisco de Zurbarán\"><PERSON></a>, Spanish painter and educator (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n\" title=\"<PERSON> de Zurbarán\"><PERSON></a>, Spanish painter and educator (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_de_Zurbar%C3%A1n"}]}, {"year": "1748", "text": "<PERSON>, Scottish poet and playwright (b. 1700)", "html": "1748 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)\" title=\"<PERSON> (poet, born 1700)\"><PERSON></a>, Scottish poet and playwright (b. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)\" title=\"<PERSON> (poet, born 1700)\"><PERSON></a>, Scottish poet and playwright (b. 1700)", "links": [{"title": "<PERSON> (poet, born 1700)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet,_born_1700)"}]}, {"year": "1782", "text": "<PERSON>, American Revolutionary and abolitionist (b. 1754)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Revolutionary and abolitionist (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Revolutionary and abolitionist (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON>, Dutch astronomer and academic, built the Eisinga Planetarium (b. 1744)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Eise_Eisinga\" title=\"Eise Eisinga\"><PERSON><PERSON></a>, Dutch astronomer and academic, built the <a href=\"https://wikipedia.org/wiki/Eisinga_Planetarium\" class=\"mw-redirect\" title=\"Eisinga Planetarium\">Eisinga Planetarium</a> (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eise_Eisinga\" title=\"Eise Eisinga\"><PERSON><PERSON></a>, Dutch astronomer and academic, built the <a href=\"https://wikipedia.org/wiki/Eisinga_Planetarium\" class=\"mw-redirect\" title=\"Eisinga Planetarium\">Eisinga Planetarium</a> (b. 1744)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>_E<PERSON>a"}, {"title": "Eisinga Planetarium", "link": "https://wikipedia.org/wiki/Eisinga_Planetarium"}]}, {"year": "1857", "text": "<PERSON>, American anthologist, poet, and critic (b. 1815)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist, poet, and critic (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American anthologist, poet, and critic (b. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Canadian judge and politician (b. 1796)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian judge and politician (b. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian judge and politician (b. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, American lawyer and politician (b. 1794)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Boardman\"><PERSON></a>, American lawyer and politician (b. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> Boardman\"><PERSON></a>, American lawyer and politician (b. 1794)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>man"}]}, {"year": "1875", "text": "<PERSON>, American businessman and financier, founded the Bank of California (b. 1826)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, founded the <a href=\"https://wikipedia.org/wiki/Bank_of_California\" title=\"Bank of California\">Bank of California</a> (b. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, founded the <a href=\"https://wikipedia.org/wiki/Bank_of_California\" title=\"Bank of California\">Bank of California</a> (b. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bank of California", "link": "https://wikipedia.org/wiki/Bank_of_California"}]}, {"year": "1891", "text": "<PERSON>, American businessman and politician (b. 1816)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (b. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, first Japanese female doctor of Western medicine (b. 1827)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine\" title=\"<PERSON><PERSON><PERSON> Ine\"><PERSON><PERSON><PERSON></a>, first Japanese female doctor of Western medicine (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine\" title=\"<PERSON><PERSON><PERSON> Ine\"><PERSON><PERSON><PERSON></a>, first Japanese female doctor of Western medicine (b. 1827)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ine"}]}, {"year": "1909", "text": "<PERSON>, Danish physiologist and mycologist (b. 1842)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physiologist and mycologist (b. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish physiologist and mycologist (b. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, Turkish colonel (b. 1879)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_%C3%87i%C4%9Filtepe\" title=\"Reşat Çiğiltepe\"><PERSON><PERSON><PERSON> Çiğilt<PERSON></a>, Turkish colonel (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Re%C5%9Fat_%C3%87i%C4%9Filtepe\" title=\"Reşat Çiğiltepe\"><PERSON><PERSON><PERSON> Ç<PERSON>ğilt<PERSON></a>, Turkish colonel (b. 1879)", "links": [{"title": "Reşat Çiğiltepe", "link": "https://wikipedia.org/wiki/Re%C5%9Fat_%C3%87i%C4%9Filtepe"}]}, {"year": "1929", "text": "<PERSON>, Croatian-Austrian engineer (b. 1892)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik\" title=\"<PERSON>\"><PERSON></a>, Croatian-Austrian engineer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik\" title=\"<PERSON>\"><PERSON></a>, Croatian-Austrian engineer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dnik"}]}, {"year": "1931", "text": "<PERSON>, Irish-American journalist and author (b. 1856)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American journalist and author (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American journalist and author (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Dutch priest and politician (b. 1860)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch priest and politician (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Dutch priest and politician (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American miner and businessman (b. 1846)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner and businessman (b. 1846)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American miner and businessman (b. 1846)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American painter and academic (b. 1859)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and academic (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American painter and academic (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}]}, {"year": "1944", "text": "<PERSON>, German soldier (b. 1915)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Hungarian engineer, designed the Petőfi Bridge (b. 1894)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1l_%C3%81lgyay\" title=\"<PERSON>\"><PERSON></a>, Hungarian engineer, designed the <a href=\"https://wikipedia.org/wiki/Pet%C5%91fi_Bridge\" title=\"Petőfi Bridge\">Petőfi Bridge</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1l_%C3%81lgyay\" title=\"<PERSON>\"><PERSON></a>, Hungarian engineer, designed the <a href=\"https://wikipedia.org/wiki/Pet%C5%91fi_Bridge\" title=\"Petőfi Bridge\">Petőfi Bridge</a> (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hubert_P%C3%A1l_%C3%81lgyay"}, {"title": "Petőfi Bridge", "link": "https://wikipedia.org/wiki/Pet%C5%91fi_Bridge"}]}, {"year": "1948", "text": "<PERSON>, American lawyer and politician, 11th Chief Justice of the United States (b. 1862)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 11th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_the_United_States\" title=\"Chief Justice of the United States\">Chief Justice of the United States</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of the United States", "link": "https://wikipedia.org/wiki/Chief_Justice_of_the_United_States"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Italian author, poet, and critic (b. 1908)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author, poet, and critic (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian author, poet, and critic (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian astronomer and academic (b. 1894)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Pelageya_Shajn\" title=\"Pelageya Shajn\"><PERSON><PERSON><PERSON><PERSON></a>, Russian astronomer and academic (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pela<PERSON><PERSON>_Shajn\" title=\"Pelageya Shajn\"><PERSON><PERSON><PERSON><PERSON></a>, Russian astronomer and academic (b. 1894)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pelageya_<PERSON>jn"}]}, {"year": "1958", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1901)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1963", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, American sociologist, historian, and activist (b. 1868)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American sociologist, historian, and activist (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, American sociologist, historian, and activist (b. 1868)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Pakistani mathematician and scholar (b. 1888)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani mathematician and scholar (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani mathematician and scholar (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress and comedian (b. 1895)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American actress and comedian (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Allen\" title=\"<PERSON> Allen\"><PERSON></a>, American actress and comedian (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Swiss-French architect and urban planner, designed the Philips Pavilion (b. 1887)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Le_Corbusier\" title=\"Le Corbusier\"><PERSON></a>, Swiss-French architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Philips_Pavilion\" title=\"Philips Pavilion\">Philips Pavilion</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Le_Corbusier\" title=\"Le Corbusier\"><PERSON></a>, Swiss-French architect and urban planner, designed the <a href=\"https://wikipedia.org/wiki/Philips_Pavilion\" title=\"Philips Pavilion\">Philips Pavilion</a> (b. 1887)", "links": [{"title": "Le Corbusier", "link": "https://wikipedia.org/wiki/Le_Corbusier"}, {"title": "Philips Pavilion", "link": "https://wikipedia.org/wiki/Philips_Pavilion"}]}, {"year": "1967", "text": "<PERSON>, English businessman and manager (b. 1934)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and manager (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "Princess <PERSON> of Greece and Denmark (b. 1906)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (b. 1906)", "links": [{"title": "Princess <PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark"}]}, {"year": "1969", "text": "<PERSON>, English author (b. 1884)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German actress and author (b. 1905)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress and author (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German actress and author (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American publisher, co-founded Random House (b. 1898)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/Random_House\" title=\"Random House\">Random House</a> (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher, co-founded <a href=\"https://wikipedia.org/wiki/Random_House\" title=\"Random House\">Random House</a> (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Random House", "link": "https://wikipedia.org/wiki/Random_House"}]}, {"year": "1971", "text": "<PERSON>-<PERSON>, American photographer and journalist (b. 1906)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1906)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Ethiopian emperor (b. 1892)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a>, Ethiopian emperor (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Se<PERSON>ie\" title=\"<PERSON>le Selassie\"><PERSON><PERSON></a>, Ethiopian emperor (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>-<PERSON>, American painter and illustrator (b. 1943)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Lithuanian author and poet (b. 1897)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/I<PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian author and poet (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%97\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian author and poet (b. 1897)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ieva_Simonaityt%C4%97"}]}, {"year": "1979", "text": "<PERSON>, 1st Earl <PERSON> of Burma, English admiral and politician, 44th Governor-General of India (b. 1900)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\"><PERSON>, 1st <PERSON> of Burma</a>, English admiral and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma\" class=\"mw-redirect\" title=\"<PERSON>, 1st <PERSON> of Burma\"><PERSON>, 1st <PERSON> of Burma</a>, English admiral and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor-General_of_India\" title=\"Governor-General of India\">Governor-General of India</a> (b. 1900)", "links": [{"title": "<PERSON>, 1st Earl <PERSON> of Burma", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_<PERSON>_of_Burma"}, {"title": "Governor-General of India", "link": "https://wikipedia.org/wiki/Governor-General_of_India"}]}, {"year": "1980", "text": "<PERSON>, American actor, producer, and screenwriter (b. 1947)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Russian ice hockey player (b. 1948)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ice hockey player (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Estonian soldier and diplomat (b. 1899)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian soldier and diplomat (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian soldier and diplomat (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Av<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1954)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Danish folklorist (b. 1933)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish folklorist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Danish folklorist (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, German footballer (b. 1960)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American actor (b. 1933)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, New Zealand author (b. 1912)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand author (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Brazilian archbishop and theologian (b. 1909)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian archbishop and theologian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian archbishop and theologian (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9lder_C%C3%A2mara"}]}, {"year": "2001", "text": "<PERSON>, Greek-American computer scientist and academic (b. 1936)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American computer scientist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-American computer scientist and academic (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, Palestinian politician (b. 1938)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Palestinian politician (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American religious leader and author (b. 1922)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French soldier and politician (b. 1920)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and politician (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American baseball player (b. 1946)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Greek trumpet player and composer (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek trumpet player and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek trumpet player and composer (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Irish footballer (b. 1929)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_Purcell\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_Purcell\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish footballer (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_Purcell"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Mexican-American guitarist (b. 1969)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American guitarist (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American guitarist (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Spanish actress (b. 1930)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Russian author and poet (b. 1913)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Dutch martial artist (b. 1934)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch martial artist (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian-American wrestler and manager (b. 1962)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Vachon\" title=\"Luna Vachon\"><PERSON></a>, Canadian-American wrestler and manager (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Vachon\" title=\"Luna Vachon\"><PERSON></a>, Canadian-American wrestler and manager (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Vachon"}]}, {"year": "2012", "text": "<PERSON>, South African linguist and activist (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African linguist and activist (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African linguist and activist (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American journalist and photographer (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and photographer (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and photographer (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Croatian footballer and manager (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian footballer and manager (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON><PERSON>_<PERSON>t"}]}, {"year": "2012", "text": "<PERSON>, Australian captain and pilot (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian captain and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Russian painter (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian painter (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Chinese director and playwright (b. 1910)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and playwright (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese director and playwright (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Australian journalist (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Welsh golfer and architect (b. 1934)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Welsh golfer and architect (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Welsh golfer and architect (b. 1934)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(golfer)"}]}, {"year": "2014", "text": "<PERSON>, French physicist and academic (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physicist and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Bulgarian poet, playwright, and screenwriter (b. 1920)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet, playwright, and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian poet, playwright, and screenwriter (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, German author (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German author (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dra"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Bangladeshi politician, 8th Prime Minister of Bangladesh (b. 1939)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi politician, 8th <a href=\"https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh\" class=\"mw-redirect\" title=\"List of Prime Ministers of Bangladesh\">Prime Minister of Bangladesh</a> (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Prime Ministers of Bangladesh", "link": "https://wikipedia.org/wiki/List_of_Prime_Ministers_of_Bangladesh"}]}, {"year": "2015", "text": "<PERSON>, French director and screenwriter (b. 1961)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1957)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Australian Major <PERSON>'s cockatoo, oldest recorded parrot (b. 1933)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)\" title=\"<PERSON><PERSON> (cockatoo)\"><PERSON><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo\" class=\"mw-redirect\" title=\"<PERSON>'s cockatoo\"><PERSON>'s cockatoo</a>, oldest recorded parrot (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)\" title=\"<PERSON><PERSON> (cockatoo)\"><PERSON><PERSON></a>, Australian <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo\" class=\"mw-redirect\" title=\"<PERSON>'s cockatoo\"><PERSON>'s cockatoo</a>, oldest recorded parrot (b. 1933)", "links": [{"title": "<PERSON><PERSON> (cockatoo)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(cockatoo)"}, {"title": "<PERSON>'s cockatoo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s_cockatoo"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1943)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Michigan_politician)\" title=\"<PERSON> (Michigan politician)\"><PERSON></a>, American politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Michigan_politician)\" title=\"<PERSON> (Michigan politician)\"><PERSON></a>, American politician (b. 1943)", "links": [{"title": "<PERSON> (Michigan politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Michigan_politician)"}]}, {"year": "2024", "text": "<PERSON>, Uruguayan footballer (b. 1997)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer (b. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer (b. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, German supercentenarian (b. 1909)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German supercentenarian (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German supercentenarian (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American businessman (b. 1941)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}