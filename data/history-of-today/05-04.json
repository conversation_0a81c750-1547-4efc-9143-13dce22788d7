{"date": "May 4", "url": "https://wikipedia.org/wiki/May_4", "data": {"Events": [{"year": "1256", "text": "The Augustinian monastic order is constituted at the Lecceto Monastery when Pope <PERSON> issues a papal bull Licet ecclesiae catholicae.", "html": "1256 - The <a href=\"https://wikipedia.org/wiki/Augustinians\" title=\"Augustinians\">Augustinian</a> <a href=\"https://wikipedia.org/wiki/Monastic_order\" class=\"mw-redirect\" title=\"Monastic order\">monastic order</a> is constituted at the <a href=\"https://wikipedia.org/wiki/Monastery_of_the_Holy_Saviour\" title=\"Monastery of the Holy Saviour\">Lecceto Monastery</a> when <a href=\"https://wikipedia.org/wiki/Pope_Alexander_IV\" title=\"Pope Alexander IV\">Pope Alexander IV</a> issues a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i>Licet ecclesiae catholicae</i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Augustinians\" title=\"Augustinians\">Augustinian</a> <a href=\"https://wikipedia.org/wiki/Monastic_order\" class=\"mw-redirect\" title=\"Monastic order\">monastic order</a> is constituted at the <a href=\"https://wikipedia.org/wiki/Monastery_of_the_Holy_Saviour\" title=\"Monastery of the Holy Saviour\">Lecceto Monastery</a> when <a href=\"https://wikipedia.org/wiki/Pope_Alexander_IV\" title=\"Pope Alexander IV\">Pope Alexander IV</a> issues a <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i>Licet ecclesiae catholicae</i>.", "links": [{"title": "Augustinians", "link": "https://wikipedia.org/wiki/Augustinians"}, {"title": "Monastic order", "link": "https://wikipedia.org/wiki/Monastic_order"}, {"title": "Monastery of the Holy Saviour", "link": "https://wikipedia.org/wiki/Monastery_of_the_Holy_Saviour"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}]}, {"year": "1415", "text": "Religious reformer <PERSON> is condemned as a heretic at the Council of Constance.", "html": "1415 - Religious reformer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> is condemned as a <a href=\"https://wikipedia.org/wiki/Christian_heresy\" class=\"mw-redirect\" title=\"Christian heresy\">heretic</a> at the <a href=\"https://wikipedia.org/wiki/Council_of_Constance\" title=\"Council of Constance\">Council of Constance</a>.", "no_year_html": "Religious reformer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is condemned as a <a href=\"https://wikipedia.org/wiki/Christian_heresy\" class=\"mw-redirect\" title=\"Christian heresy\">heretic</a> at the <a href=\"https://wikipedia.org/wiki/Council_of_Constance\" title=\"Council of Constance\">Council of Constance</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Christian heresy", "link": "https://wikipedia.org/wiki/Christian_heresy"}, {"title": "Council of Constance", "link": "https://wikipedia.org/wiki/Council_of_Constance"}]}, {"year": "1436", "text": "Assassination of the Swedish rebel (later national hero) <PERSON><PERSON><PERSON><PERSON> Engelbre<PERSON> (27 April O.S.).", "html": "1436 - Assassination of the Swedish rebel (later national hero) <a href=\"https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson\" title=\"Engelbrekt Engelbrektsson\">Engelbrekt Engelbrektsson</a> (27 April <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "no_year_html": "Assassination of the Swedish rebel (later national hero) <a href=\"https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson\" title=\"Engelbrekt Engelbrektsson\">Engelbrekt Engelbrektsson</a> (27 April <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "links": [{"title": "Engelbrekt Engelbrektsson", "link": "https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1471", "text": "Wars of the Roses: The Battle of Tewkesbury: <PERSON> defeats a Lancastrian Army and kills <PERSON> of <PERSON>, Prince of Wales.", "html": "1471 - <a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tewkesbury\" title=\"Battle of Tewkesbury\">Battle of Tewkesbury</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"Edward IV of England\"><PERSON> IV</a> defeats a <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lancastrian</a> Army and kills <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westminster,_Prince_of_Wales\" title=\"<PERSON>, Prince of Wales\"><PERSON>, Prince of Wales</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wars_of_the_Roses\" title=\"Wars of the Roses\">Wars of the Roses</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Tewkesbury\" title=\"Battle of Tewkesbury\">Battle of Tewkesbury</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" class=\"mw-redirect\" title=\"Edward IV of England\"><PERSON> IV</a> defeats a <a href=\"https://wikipedia.org/wiki/House_of_Lancaster\" title=\"House of Lancaster\">Lancastrian</a> Army and kills <a href=\"https://wikipedia.org/wiki/<PERSON>_Westminster,_Prince_of_Wales\" title=\"<PERSON>, <PERSON> of Wales\"><PERSON>, Prince of Wales</a>.", "links": [{"title": "Wars of the Roses", "link": "https://wikipedia.org/wiki/Wars_of_the_Roses"}, {"title": "Battle of Tewkesbury", "link": "https://wikipedia.org/wiki/Battle_of_Tewkesbury"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "House of Lancaster", "link": "https://wikipedia.org/wiki/House_of_Lancaster"}, {"title": "<PERSON> Westminster, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westminster,_Prince_of_Wales"}]}, {"year": "1493", "text": "In the papal bull Inter caetera, Pope <PERSON> divides the New World between Spain and Portugal along the Line of Demarcation.", "html": "1493 - In the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Inter_caetera\" title=\"Inter caetera\">Inter caetera</a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_VI\" title=\"Pope Alexander VI\">Pope Alexander VI</a> divides the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> between Spain and Portugal along the <a href=\"https://wikipedia.org/wiki/Line_of_Demarcation\" class=\"mw-redirect\" title=\"Line of Demarcation\">Line of Demarcation</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Inter_caetera\" title=\"Inter caetera\">Inter caetera</a></i>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Alexander_VI\" title=\"Pope Alexander VI\"><PERSON> <PERSON> VI</a> divides the <a href=\"https://wikipedia.org/wiki/New_World\" title=\"New World\">New World</a> between Spain and Portugal along the <a href=\"https://wikipedia.org/wiki/Line_of_Demarcation\" class=\"mw-redirect\" title=\"Line of Demarcation\">Line of Demarcation</a>.", "links": [{"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Inter caetera", "link": "https://wikipedia.org/wiki/Inter_caetera"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "New World", "link": "https://wikipedia.org/wiki/New_World"}, {"title": "Line of Demarcation", "link": "https://wikipedia.org/wiki/Line_of_Demarcation"}]}, {"year": "1626", "text": "Dutch explorer <PERSON> arrives in New Netherland (present day Manhattan Island) aboard the See Meeuw.", "html": "1626 - Dutch explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> (present day <a href=\"https://wikipedia.org/wiki/Manhattan_Island\" class=\"mw-redirect\" title=\"Manhattan Island\">Manhattan Island</a>) aboard the <i>See Meeuw</i>.", "no_year_html": "Dutch explorer <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives in <a href=\"https://wikipedia.org/wiki/New_Netherland\" title=\"New Netherland\">New Netherland</a> (present day <a href=\"https://wikipedia.org/wiki/Manhattan_Island\" class=\"mw-redirect\" title=\"Manhattan Island\">Manhattan Island</a>) aboard the <i>See Meeuw</i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New Netherland", "link": "https://wikipedia.org/wiki/New_Netherland"}, {"title": "Manhattan Island", "link": "https://wikipedia.org/wiki/Manhattan_Island"}]}, {"year": "1738", "text": "The Imperial Theatrical School, the first ballet school in Russia, is founded.", "html": "1738 - The <a href=\"https://wikipedia.org/wiki/Vaganova_Academy_of_Russian_Ballet\" title=\"Vaganova Academy of Russian Ballet\">Imperial Theatrical School</a>, the first ballet school in Russia, is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Vaganova_Academy_of_Russian_Ballet\" title=\"Vaganova Academy of Russian Ballet\">Imperial Theatrical School</a>, the first ballet school in Russia, is founded.", "links": [{"title": "Vaganova Academy of Russian Ballet", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ova_Academy_of_Russian_Ballet"}]}, {"year": "1776", "text": "Rhode Island becomes the first American colony to renounce allegiance to King <PERSON>.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/Colony_of_Rhode_Island_and_Providence_Plantations\" title=\"Colony of Rhode Island and Providence Plantations\">Rhode Island</a> becomes the first American colony to renounce allegiance to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Colony_of_Rhode_Island_and_Providence_Plantations\" title=\"Colony of Rhode Island and Providence Plantations\">Rhode Island</a> becomes the first American colony to renounce allegiance to King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> III\"><PERSON></a>.", "links": [{"title": "Colony of Rhode Island and Providence Plantations", "link": "https://wikipedia.org/wiki/Colony_of_Rhode_Island_and_Providence_Plantations"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "Fourth Anglo-Mysore War: The Battle of Seringapatam: The siege of Seringapatam ends when the city is invaded and <PERSON><PERSON><PERSON> Sultan killed by the besieging British army, under the command of General <PERSON>.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Fourth_Anglo-Mysore_War\" title=\"Fourth Anglo-Mysore War\">Fourth Anglo-Mysore War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Seringapatam\" class=\"mw-redirect\" title=\"Battle of Seringapatam\">Battle of Seringapatam</a>: The siege of <a href=\"https://wikipedia.org/wiki/Seringapatam\" class=\"mw-redirect\" title=\"Seringapatam\">Seringapatam</a> ends when the city is invaded and <a href=\"https://wikipedia.org/wiki/Tipu_Sultan\" title=\"Tipu Sultan\">Tipu Sultan</a> killed by the besieging British army, under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\">General <PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fourth_Anglo-Mysore_War\" title=\"Fourth Anglo-Mysore War\">Fourth Anglo-Mysore War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Seringapatam\" class=\"mw-redirect\" title=\"Battle of Seringapatam\">Battle of Seringapatam</a>: The siege of <a href=\"https://wikipedia.org/wiki/Seringapatam\" class=\"mw-redirect\" title=\"Seringapatam\">Seringapatam</a> ends when the city is invaded and <a href=\"https://wikipedia.org/wiki/Tipu_Sultan\" title=\"Tipu Sultan\">T<PERSON><PERSON> <PERSON></a> killed by the besieging British army, under the command of <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_<PERSON>_<PERSON>\" title=\"<PERSON>, 1st Baron <PERSON>\">General <PERSON></a>.", "links": [{"title": "Fourth Anglo-Mysore War", "link": "https://wikipedia.org/wiki/Fourth_Anglo-Mysore_War"}, {"title": "Battle of Seringapatam", "link": "https://wikipedia.org/wiki/Battle_of_Seringapatam"}, {"title": "Seringapatam", "link": "https://wikipedia.org/wiki/Seringapatam"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tipu_<PERSON>"}, {"title": "<PERSON>, 1st Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON>"}]}, {"year": "1814", "text": "Emperor <PERSON> arrives at Portoferraio on the island of Elba to begin his exile.", "html": "1814 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Portoferraio\" title=\"Portoferraio\">Portoferraio</a> on the island of <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a> to begin his exile.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> arrives at <a href=\"https://wikipedia.org/wiki/Portoferraio\" title=\"Portoferraio\">Portoferraio</a> on the island of <a href=\"https://wikipedia.org/wiki/Elba\" title=\"Elba\">Elba</a> to begin his exile.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Portoferraio", "link": "https://wikipedia.org/wiki/Portoferraio"}, {"title": "Elba", "link": "https://wikipedia.org/wiki/Elba"}]}, {"year": "1814", "text": "King <PERSON> abolishes the Spanish Constitution of 1812, returning Spain to absolutism.", "html": "1814 - King <PERSON> VII abolishes the <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1812\" title=\"Spanish Constitution of 1812\">Spanish Constitution of 1812</a>, returning Spain to <a href=\"https://wikipedia.org/wiki/Absolute_monarchy\" title=\"Absolute monarchy\">absolutism</a>.", "no_year_html": "King <PERSON> VII abolishes the <a href=\"https://wikipedia.org/wiki/Spanish_Constitution_of_1812\" title=\"Spanish Constitution of 1812\">Spanish Constitution of 1812</a>, returning Spain to <a href=\"https://wikipedia.org/wiki/Absolute_monarchy\" title=\"Absolute monarchy\">absolutism</a>.", "links": [{"title": "Spanish Constitution of 1812", "link": "https://wikipedia.org/wiki/Spanish_Constitution_of_1812"}, {"title": "Absolute monarchy", "link": "https://wikipedia.org/wiki/Absolute_monarchy"}]}, {"year": "1836", "text": "Formation of Ancient Order of Hibernians.", "html": "1836 - Formation of <a href=\"https://wikipedia.org/wiki/Ancient_Order_of_Hibernians\" title=\"Ancient Order of Hibernians\">Ancient Order of Hibernians</a>.", "no_year_html": "Formation of <a href=\"https://wikipedia.org/wiki/Ancient_Order_of_Hibernians\" title=\"Ancient Order of Hibernians\">Ancient Order of Hibernians</a>.", "links": [{"title": "Ancient Order of Hibernians", "link": "https://wikipedia.org/wiki/Ancient_Order_of_Hibernians"}]}, {"year": "1859", "text": "The Cornwall Railway opens across the Royal Albert Bridge linking Devon and Cornwall in England.", "html": "1859 - The <a href=\"https://wikipedia.org/wiki/Cornwall_Railway\" title=\"Cornwall Railway\">Cornwall Railway</a> opens across the <a href=\"https://wikipedia.org/wiki/Royal_Albert_Bridge\" title=\"Royal Albert Bridge\">Royal Albert Bridge</a> linking <a href=\"https://wikipedia.org/wiki/Devon\" title=\"Devon\">Devon</a> and <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> in England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cornwall_Railway\" title=\"Cornwall Railway\">Cornwall Railway</a> opens across the <a href=\"https://wikipedia.org/wiki/Royal_Albert_Bridge\" title=\"Royal Albert Bridge\">Royal Albert Bridge</a> linking <a href=\"https://wikipedia.org/wiki/Devon\" title=\"Devon\">Devon</a> and <a href=\"https://wikipedia.org/wiki/Cornwall\" title=\"Cornwall\">Cornwall</a> in England.", "links": [{"title": "Cornwall Railway", "link": "https://wikipedia.org/wiki/Cornwall_Railway"}, {"title": "Royal Albert Bridge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Devon", "link": "https://wikipedia.org/wiki/Devon"}, {"title": "Cornwall", "link": "https://wikipedia.org/wiki/Cornwall"}]}, {"year": "1869", "text": "The four-day Naval Battle of Hakodate begins. The newly formed Imperial Japanese Navy defeats the remnants of the Tokugawa shogunate navy in the Sea of Japan off the city of Hakodate, leading to the surrender of the Ezo Republic on May 17.", "html": "1869 - The four-day <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Hakodate\" title=\"Naval Battle of Hakodate\">Naval Battle of Hakodate</a> begins. The newly formed <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> defeats the remnants of the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> navy in the <a href=\"https://wikipedia.org/wiki/Sea_of_Japan\" title=\"Sea of Japan\">Sea of Japan</a> off the city of <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a>, leading to the surrender of the <a href=\"https://wikipedia.org/wiki/Ezo_Republic\" class=\"mw-redirect\" title=\"Ezo Republic\">Ezo Republic</a> on May 17.", "no_year_html": "The four-day <a href=\"https://wikipedia.org/wiki/Naval_Battle_of_Hakodate\" title=\"Naval Battle of Hakodate\">Naval Battle of Hakodate</a> begins. The newly formed <a href=\"https://wikipedia.org/wiki/Imperial_Japanese_Navy\" title=\"Imperial Japanese Navy\">Imperial Japanese Navy</a> defeats the remnants of the <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> navy in the <a href=\"https://wikipedia.org/wiki/Sea_of_Japan\" title=\"Sea of Japan\">Sea of Japan</a> off the city of <a href=\"https://wikipedia.org/wiki/Hakodate\" title=\"Hakodate\">Hakodate</a>, leading to the surrender of the <a href=\"https://wikipedia.org/wiki/Ezo_Republic\" class=\"mw-redirect\" title=\"Ezo Republic\">Ezo Republic</a> on May 17.", "links": [{"title": "Naval Battle of Hakodate", "link": "https://wikipedia.org/wiki/Naval_Battle_of_Hakodate"}, {"title": "Imperial Japanese Navy", "link": "https://wikipedia.org/wiki/Imperial_Japanese_Navy"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}, {"title": "Sea of Japan", "link": "https://wikipedia.org/wiki/Sea_of_Japan"}, {"title": "Hakodate", "link": "https://wikipedia.org/wiki/Hakodate"}, {"title": "Ezo Republic", "link": "https://wikipedia.org/wiki/Ezo_Republic"}]}, {"year": "1871", "text": "The National Association, the first professional baseball league, opens its first season in Fort Wayne, Indiana.", "html": "1871 - The <a href=\"https://wikipedia.org/wiki/National_Association_of_Professional_Base_Ball_Players\" title=\"National Association of Professional Base Ball Players\">National Association</a>, the first professional baseball league, opens its first season in <a href=\"https://wikipedia.org/wiki/Fort_Wayne,_Indiana\" title=\"Fort Wayne, Indiana\">Fort Wayne, Indiana</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Association_of_Professional_Base_Ball_Players\" title=\"National Association of Professional Base Ball Players\">National Association</a>, the first professional baseball league, opens its first season in <a href=\"https://wikipedia.org/wiki/Fort_Wayne,_Indiana\" title=\"Fort Wayne, Indiana\">Fort Wayne, Indiana</a>.", "links": [{"title": "National Association of Professional Base Ball Players", "link": "https://wikipedia.org/wiki/National_Association_of_Professional_Base_Ball_Players"}, {"title": "Fort Wayne, Indiana", "link": "https://wikipedia.org/wiki/Fort_Wayne,_Indiana"}]}, {"year": "1886", "text": "Haymarket affair: In Chicago, United States, a homemade bomb is thrown at police officers trying to break up a labor rally, killing one officer. Ensuing gunfire leads to the deaths of a further seven officers and four civilians.", "html": "1886 - <a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a>: In Chicago, United States, a <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">homemade bomb</a> is thrown at <a href=\"https://wikipedia.org/wiki/Chicago_Police_Department\" title=\"Chicago Police Department\">police</a> officers trying to break up a labor rally, killing one officer. Ensuing gunfire leads to the deaths of a further seven officers and four civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Haymarket_affair\" title=\"Haymarket affair\">Haymarket affair</a>: In Chicago, United States, a <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">homemade bomb</a> is thrown at <a href=\"https://wikipedia.org/wiki/Chicago_Police_Department\" title=\"Chicago Police Department\">police</a> officers trying to break up a labor rally, killing one officer. Ensuing gunfire leads to the deaths of a further seven officers and four civilians.", "links": [{"title": "Haymarket affair", "link": "https://wikipedia.org/wiki/Haymarket_affair"}, {"title": "Improvised explosive device", "link": "https://wikipedia.org/wiki/Improvised_explosive_device"}, {"title": "Chicago Police Department", "link": "https://wikipedia.org/wiki/Chicago_Police_Department"}]}, {"year": "1904", "text": "The United States begins construction of the Panama Canal.", "html": "1904 - The United States begins construction of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "no_year_html": "The United States begins construction of the <a href=\"https://wikipedia.org/wiki/Panama_Canal\" title=\"Panama Canal\">Panama Canal</a>.", "links": [{"title": "Panama Canal", "link": "https://wikipedia.org/wiki/Panama_Canal"}]}, {"year": "1910", "text": "The Royal Canadian Navy is created.", "html": "1910 - The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a> is created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Navy\" title=\"Royal Canadian Navy\">Royal Canadian Navy</a> is created.", "links": [{"title": "Royal Canadian Navy", "link": "https://wikipedia.org/wiki/Royal_Canadian_Navy"}]}, {"year": "1912", "text": "Italy occupies the Ottoman island of Rhodes.", "html": "1912 - Italy occupies the Ottoman island of <a href=\"https://wikipedia.org/wiki/Rhodes\" title=\"Rhodes\">Rhodes</a>.", "no_year_html": "Italy occupies the Ottoman island of <a href=\"https://wikipedia.org/wiki/Rhodes\" title=\"Rhodes\">Rhodes</a>.", "links": [{"title": "Rhodes", "link": "https://wikipedia.org/wiki/Rhodes"}]}, {"year": "1919", "text": "May Fourth Movement: Student demonstrations take place in Tiananmen Square in Beijing, China, protesting the Treaty of Versailles, which transferred Chinese territory to Japan.", "html": "1919 - <a href=\"https://wikipedia.org/wiki/May_Fourth_Movement\" title=\"May Fourth Movement\">May Fourth Movement</a>: Student demonstrations take place in <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> in Beijing, China, protesting the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>, which transferred <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Chinese</a> territory to Japan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/May_Fourth_Movement\" title=\"May Fourth Movement\">May Fourth Movement</a>: Student demonstrations take place in <a href=\"https://wikipedia.org/wiki/Tiananmen_Square\" title=\"Tiananmen Square\">Tiananmen Square</a> in Beijing, China, protesting the <a href=\"https://wikipedia.org/wiki/Treaty_of_Versailles\" title=\"Treaty of Versailles\">Treaty of Versailles</a>, which transferred <a href=\"https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)\" class=\"mw-redirect\" title=\"Republic of China (1912-49)\">Chinese</a> territory to Japan.", "links": [{"title": "May Fourth Movement", "link": "https://wikipedia.org/wiki/May_Fourth_Movement"}, {"title": "Tiananmen Square", "link": "https://wikipedia.org/wiki/Tiananmen_Square"}, {"title": "Treaty of Versailles", "link": "https://wikipedia.org/wiki/Treaty_of_Versailles"}, {"title": "Republic of China (1912-49)", "link": "https://wikipedia.org/wiki/Republic_of_China_(1912%E2%80%9349)"}]}, {"year": "1926", "text": "The United Kingdom general strike begins.", "html": "1926 - The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> <a href=\"https://wikipedia.org/wiki/1926_United_Kingdom_general_strike\" title=\"1926 United Kingdom general strike\">general strike</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> <a href=\"https://wikipedia.org/wiki/1926_United_Kingdom_general_strike\" title=\"1926 United Kingdom general strike\">general strike</a> begins.", "links": [{"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "1926 United Kingdom general strike", "link": "https://wikipedia.org/wiki/1926_United_Kingdom_general_strike"}]}, {"year": "1927", "text": "The Academy of Motion Picture Arts and Sciences is incorporated.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences\" title=\"Academy of Motion Picture Arts and Sciences\">Academy of Motion Picture Arts and Sciences</a> is incorporated.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences\" title=\"Academy of Motion Picture Arts and Sciences\">Academy of Motion Picture Arts and Sciences</a> is incorporated.", "links": [{"title": "Academy of Motion Picture Arts and Sciences", "link": "https://wikipedia.org/wiki/Academy_of_Motion_Picture_Arts_and_Sciences"}]}, {"year": "1932", "text": "Having been incarcerated at the Cook County Jail since his sentencing on October 24, 1931, mobster <PERSON> is transferred to the federal penitentiary in Atlanta after the U.S. Supreme Court denies his appeal for conviction of tax evasion.", "html": "1932 - Having been incarcerated at the <a href=\"https://wikipedia.org/wiki/Cook_County_Jail\" title=\"Cook County Jail\">Cook County Jail</a> since his sentencing on October 24, 1931, <a href=\"https://wikipedia.org/wiki/Mo<PERSON>ter\" class=\"mw-redirect\" title=\"Mobster\">mobster</a> <a href=\"https://wikipedia.org/wiki/Al_Capone\" title=\"Al Capone\">Al <PERSON></a> is transferred to the <a href=\"https://wikipedia.org/wiki/United_States_Penitentiary,_Atlanta\" class=\"mw-redirect\" title=\"United States Penitentiary, Atlanta\">federal penitentiary in Atlanta</a> after the <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> denies his appeal for conviction of <a href=\"https://wikipedia.org/wiki/Tax_evasion\" title=\"Tax evasion\">tax evasion</a>.", "no_year_html": "Having been incarcerated at the <a href=\"https://wikipedia.org/wiki/Cook_County_Jail\" title=\"Cook County Jail\">Cook County Jail</a> since his sentencing on October 24, 1931, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ter\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ter\">mobster</a> <a href=\"https://wikipedia.org/wiki/Al_Capone\" title=\"Al Capone\">Al <PERSON></a> is transferred to the <a href=\"https://wikipedia.org/wiki/United_States_Penitentiary,_Atlanta\" class=\"mw-redirect\" title=\"United States Penitentiary, Atlanta\">federal penitentiary in Atlanta</a> after the <a href=\"https://wikipedia.org/wiki/U.S._Supreme_Court\" class=\"mw-redirect\" title=\"U.S. Supreme Court\">U.S. Supreme Court</a> denies his appeal for conviction of <a href=\"https://wikipedia.org/wiki/Tax_evasion\" title=\"Tax evasion\">tax evasion</a>.", "links": [{"title": "Cook County Jail", "link": "https://wikipedia.org/wiki/Cook_County_Jail"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Al Capone", "link": "https://wikipedia.org/wiki/Al_Capone"}, {"title": "United States Penitentiary, Atlanta", "link": "https://wikipedia.org/wiki/United_States_Penitentiary,_Atlanta"}, {"title": "U.S. Supreme Court", "link": "https://wikipedia.org/wiki/U.S._Supreme_Court"}, {"title": "Tax evasion", "link": "https://wikipedia.org/wiki/Tax_evasion"}]}, {"year": "1942", "text": "World War II: The Battle of the Coral Sea begins with an attack by aircraft from the United States aircraft carrier USS Yorktown on Japanese naval forces at Tulagi Island in the Solomon Islands. The Japanese forces had invaded Tulagi the day before.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a> begins with an attack by aircraft from the United States <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Yorktown_(CV-5)\" title=\"USS Yorktown (CV-5)\">USS <i>Yorktown</i></a> on <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval forces at <a href=\"https://wikipedia.org/wiki/Tulagi\" title=\"Tulagi\">Tulagi</a> Island in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>. The Japanese forces had <a href=\"https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)\" title=\"Invasion of Tulagi (May 1942)\">invaded Tulagi</a> the day before.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_the_Coral_Sea\" title=\"Battle of the Coral Sea\">Battle of the Coral Sea</a> begins with an attack by aircraft from the United States <a href=\"https://wikipedia.org/wiki/Aircraft_carrier\" title=\"Aircraft carrier\">aircraft carrier</a> <a href=\"https://wikipedia.org/wiki/USS_Yorktown_(CV-5)\" title=\"USS Yorktown (CV-5)\">USS <i>Yorktown</i></a> on <a href=\"https://wikipedia.org/wiki/Empire_of_Japan\" title=\"Empire of Japan\">Japanese</a> naval forces at <a href=\"https://wikipedia.org/wiki/Tulagi\" title=\"Tulagi\">Tulagi</a> Island in the <a href=\"https://wikipedia.org/wiki/Solomon_Islands\" title=\"Solomon Islands\">Solomon Islands</a>. The Japanese forces had <a href=\"https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)\" title=\"Invasion of Tulagi (May 1942)\">invaded Tulagi</a> the day before.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of the Coral Sea", "link": "https://wikipedia.org/wiki/Battle_of_the_Coral_Sea"}, {"title": "Aircraft carrier", "link": "https://wikipedia.org/wiki/Aircraft_carrier"}, {"title": "USS Yorktown (CV-5)", "link": "https://wikipedia.org/wiki/USS_Yorktown_(CV-5)"}, {"title": "Empire of Japan", "link": "https://wikipedia.org/wiki/Empire_of_Japan"}, {"title": "Tulagi", "link": "https://wikipedia.org/wiki/Tulagi"}, {"title": "Solomon Islands", "link": "https://wikipedia.org/wiki/Solomon_Islands"}, {"title": "Invasion of Tulagi (May 1942)", "link": "https://wikipedia.org/wiki/Invasion_of_Tulagi_(May_1942)"}]}, {"year": "1945", "text": "World War II: Neuengamme concentration camp near Hamburg is liberated by the British Army.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/Neuengamme_concentration_camp\" title=\"Neuengamme concentration camp\">Neuengamme concentration camp</a> near <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a> is liberated by the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Neuengamme_concentration_camp\" title=\"Neuengamme concentration camp\">Neuengamme concentration camp</a> near <a href=\"https://wikipedia.org/wiki/Hamburg\" title=\"Hamburg\">Hamburg</a> is liberated by the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a>.", "links": [{"title": "Neuengamme concentration camp", "link": "https://wikipedia.org/wiki/Neuengamme_concentration_camp"}, {"title": "Hamburg", "link": "https://wikipedia.org/wiki/Hamburg"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}]}, {"year": "1945", "text": "World War II: The German surrender at Lüneburg Heath is signed, coming into effect the following day. It encompasses all Wehrmacht units in the Netherlands, Denmark and northwest Germany.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/German_surrender_at_L%C3%BCneburg_Heath\" title=\"German surrender at Lüneburg Heath\">German surrender at Lüneburg Heath</a> is signed, coming into effect the following day. It encompasses all <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> units in the Netherlands, Denmark and northwest Germany.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/German_surrender_at_L%C3%BCneburg_Heath\" title=\"German surrender at Lüneburg Heath\">German surrender at Lüneburg Heath</a> is signed, coming into effect the following day. It encompasses all <a href=\"https://wikipedia.org/wiki/Wehrmacht\" title=\"Wehrmacht\">Wehrmacht</a> units in the Netherlands, Denmark and northwest Germany.", "links": [{"title": "German surrender at Lüneburg Heath", "link": "https://wikipedia.org/wiki/German_surrender_at_L%C3%BCneburg_Heath"}, {"title": "Wehrmacht", "link": "https://wikipedia.org/wiki/Wehrmacht"}]}, {"year": "1946", "text": "In San Francisco Bay, U.S. Marines from the nearby Treasure Island Naval Base stop a two-day riot at Alcatraz Federal Penitentiary.  Five people are killed in the riot.", "html": "1946 - In <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> from the nearby <a href=\"https://wikipedia.org/wiki/Treasure_Island_Naval_Base\" class=\"mw-redirect\" title=\"Treasure Island Naval Base\">Treasure Island Naval Base</a> stop a <a href=\"https://wikipedia.org/wiki/Battle_of_Alcatraz\" title=\"Battle of Alcatraz\">two-day riot</a> at <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a>. Five people are killed in the riot.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/San_Francisco_Bay\" title=\"San Francisco Bay\">San Francisco Bay</a>, <a href=\"https://wikipedia.org/wiki/United_States_Marine_Corps\" title=\"United States Marine Corps\">U.S. Marines</a> from the nearby <a href=\"https://wikipedia.org/wiki/Treasure_Island_Naval_Base\" class=\"mw-redirect\" title=\"Treasure Island Naval Base\">Treasure Island Naval Base</a> stop a <a href=\"https://wikipedia.org/wiki/Battle_of_Alcatraz\" title=\"Battle of Alcatraz\">two-day riot</a> at <a href=\"https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary\" title=\"Alcatraz Federal Penitentiary\">Alcatraz Federal Penitentiary</a>. Five people are killed in the riot.", "links": [{"title": "San Francisco Bay", "link": "https://wikipedia.org/wiki/San_Francisco_Bay"}, {"title": "United States Marine Corps", "link": "https://wikipedia.org/wiki/United_States_Marine_Corps"}, {"title": "Treasure Island Naval Base", "link": "https://wikipedia.org/wiki/Treasure_Island_Naval_Base"}, {"title": "Battle of Alcatraz", "link": "https://wikipedia.org/wiki/Battle_of_Alcatraz"}, {"title": "Alcatraz Federal Penitentiary", "link": "https://wikipedia.org/wiki/Alcatraz_Federal_Penitentiary"}]}, {"year": "1949", "text": "The entire Torino football team (except for two players who did not take the trip: <PERSON><PERSON>, due to an injury and <PERSON><PERSON>, because of coach request) is killed in a plane crash.", "html": "1949 - The entire <a href=\"https://wikipedia.org/wiki/Torino_F.C.\" class=\"mw-redirect\" title=\"Torino F.C.\">Torino</a> football team (except for two players who did not take the trip: <PERSON><PERSON>, due to an injury and <PERSON><PERSON>, because of coach request) is killed in <a href=\"https://wikipedia.org/wiki/Superga_air_disaster\" title=\"Superga air disaster\">a plane crash</a>.", "no_year_html": "The entire <a href=\"https://wikipedia.org/wiki/Torino_F.C.\" class=\"mw-redirect\" title=\"Torino F.C.\">Torino</a> football team (except for two players who did not take the trip: <PERSON><PERSON>, due to an injury and <PERSON><PERSON>, because of coach request) is killed in <a href=\"https://wikipedia.org/wiki/Superga_air_disaster\" title=\"Superga air disaster\">a plane crash</a>.", "links": [{"title": "Torino F.C.", "link": "https://wikipedia.org/wiki/Torino_F.C."}, {"title": "Superga air disaster", "link": "https://wikipedia.org/wiki/Superga_air_disaster"}]}, {"year": "1953", "text": "<PERSON> wins the Pulitzer Prize for The Old Man and the Sea.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize\" title=\"Pulitzer Prize\">Pulitzer Prize</a> for <i><a href=\"https://wikipedia.org/wiki/The_Old_Man_and_the_Sea\" title=\"The Old Man and the Sea\">The Old Man and the Sea</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> wins the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize\" title=\"Pulitzer Prize\">Pulitzer Prize</a> for <i><a href=\"https://wikipedia.org/wiki/The_Old_Man_and_the_Sea\" title=\"The Old Man and the Sea\">The Old Man and the Sea</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Pulitzer Prize", "link": "https://wikipedia.org/wiki/Pulitzer_Prize"}, {"title": "The Old Man and the Sea", "link": "https://wikipedia.org/wiki/The_Old_Man_and_the_Sea"}]}, {"year": "1959", "text": "The 1st Annual Grammy Awards are held.", "html": "1959 - The <a href=\"https://wikipedia.org/wiki/1st_Annual_Grammy_Awards\" title=\"1st Annual Grammy Awards\">1st Annual Grammy Awards</a> are held.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1st_Annual_Grammy_Awards\" title=\"1st Annual Grammy Awards\">1st Annual Grammy Awards</a> are held.", "links": [{"title": "1st Annual Grammy Awards", "link": "https://wikipedia.org/wiki/1st_Annual_Grammy_Awards"}]}, {"year": "1961", "text": "American civil rights movement: The \"Freedom Riders\" begin a bus trip through the South.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: The \"<a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Riders</a>\" begin a bus trip through the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">South</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_civil_rights_movement\" class=\"mw-redirect\" title=\"American civil rights movement\">American civil rights movement</a>: The \"<a href=\"https://wikipedia.org/wiki/Freedom_Riders\" title=\"Freedom Riders\">Freedom Riders</a>\" begin a bus trip through the <a href=\"https://wikipedia.org/wiki/Southern_United_States\" title=\"Southern United States\">South</a>.", "links": [{"title": "American civil rights movement", "link": "https://wikipedia.org/wiki/American_civil_rights_movement"}, {"title": "Freedom Riders", "link": "https://wikipedia.org/wiki/Freedom_Riders"}, {"title": "Southern United States", "link": "https://wikipedia.org/wiki/Southern_United_States"}]}, {"year": "1961", "text": "<PERSON> and <PERSON> attain a new altitude record for manned balloon flight ascending in the Strato-Lab V open gondola to 113,740 feet (34.67 km).", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attain a new altitude record for manned balloon flight ascending in the <a href=\"https://wikipedia.org/wiki/Project_Strato-Lab\" title=\"Project Strato-Lab\">Strato-Lab</a> V open gondola to 113,740 feet (34.67 km).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(balloonist)\" title=\"<PERSON> (balloonist)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> attain a new altitude record for manned balloon flight ascending in the <a href=\"https://wikipedia.org/wiki/Project_Strato-Lab\" title=\"Project Strato-Lab\">Strato-Lab</a> V open gondola to 113,740 feet (34.67 km).", "links": [{"title": "<PERSON> (balloonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(balloonist)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Project Strato-Lab", "link": "https://wikipedia.org/wiki/Project_Strato-Lab"}]}, {"year": "1970", "text": "Vietnam War: Kent State shootings: The Ohio National Guard, sent to Kent State University after disturbances in the city of Kent the weekend before, opens fire killing four unarmed students and wounding nine others. The students were protesting the Cambodian Campaign of the United States and South Vietnam.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Kent_State_shootings\" title=\"Kent State shootings\">Kent State shootings</a>: The <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a>, sent to <a href=\"https://wikipedia.org/wiki/Kent_State_University\" title=\"Kent State University\">Kent State University</a> after disturbances in the city of Kent the weekend before, opens fire killing four unarmed students and wounding nine others. The students were protesting the <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">Cambodian Campaign</a> of the United States and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Kent_State_shootings\" title=\"Kent State shootings\">Kent State shootings</a>: The <a href=\"https://wikipedia.org/wiki/Ohio_National_Guard\" title=\"Ohio National Guard\">Ohio National Guard</a>, sent to <a href=\"https://wikipedia.org/wiki/Kent_State_University\" title=\"Kent State University\">Kent State University</a> after disturbances in the city of Kent the weekend before, opens fire killing four unarmed students and wounding nine others. The students were protesting the <a href=\"https://wikipedia.org/wiki/Cambodian_Campaign\" class=\"mw-redirect\" title=\"Cambodian Campaign\">Cambodian Campaign</a> of the United States and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Kent State shootings", "link": "https://wikipedia.org/wiki/Kent_State_shootings"}, {"title": "Ohio National Guard", "link": "https://wikipedia.org/wiki/Ohio_National_Guard"}, {"title": "Kent State University", "link": "https://wikipedia.org/wiki/Kent_State_University"}, {"title": "Cambodian Campaign", "link": "https://wikipedia.org/wiki/Cambodian_Campaign"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1972", "text": "The Don't Make A Wave Committee, a fledgling environmental organization founded in Canada in 1971, officially changes its name to \"Greenpeace Foundation\".", "html": "1972 - The <i><a href=\"https://wikipedia.org/wiki/Don%27t_Make_A_Wave_Committee\" class=\"mw-redirect\" title=\"Don't Make A Wave Committee\">Don't Make A Wave Committee</a></i>, a fledgling <a href=\"https://wikipedia.org/wiki/Environmental_organization\" class=\"mw-redirect\" title=\"Environmental organization\">environmental organization</a> founded in Canada in 1971, officially changes its name to \"<a href=\"https://wikipedia.org/wiki/Greenpeace\" title=\"Greenpeace\">Greenpeace Foundation</a>\".", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Don%27t_Make_A_Wave_Committee\" class=\"mw-redirect\" title=\"Don't Make A Wave Committee\">Don't Make A Wave Committee</a></i>, a fledgling <a href=\"https://wikipedia.org/wiki/Environmental_organization\" class=\"mw-redirect\" title=\"Environmental organization\">environmental organization</a> founded in Canada in 1971, officially changes its name to \"<a href=\"https://wikipedia.org/wiki/Greenpeace\" title=\"Greenpeace\">Greenpeace Foundation</a>\".", "links": [{"title": "Don't Make A Wave Committee", "link": "https://wikipedia.org/wiki/Don%27t_Make_A_Wave_Committee"}, {"title": "Environmental organization", "link": "https://wikipedia.org/wiki/Environmental_organization"}, {"title": "Greenpeace", "link": "https://wikipedia.org/wiki/Greenpeace"}]}, {"year": "1973", "text": "The 108-story Sears Tower in Chicago is topped out at 1,451 feet (442 m) as the world's tallest building.", "html": "1973 - The 108-story <a href=\"https://wikipedia.org/wiki/Sears_Tower\" class=\"mw-redirect\" title=\"Sears Tower\">Sears Tower</a> in Chicago is topped out at 1,451 feet (442 m) as the <a href=\"https://wikipedia.org/wiki/World%27s_tallest_building\" class=\"mw-redirect\" title=\"World's tallest building\">world's tallest building</a>.", "no_year_html": "The 108-story <a href=\"https://wikipedia.org/wiki/Sears_Tower\" class=\"mw-redirect\" title=\"Sears Tower\">Sears Tower</a> in Chicago is topped out at 1,451 feet (442 m) as the <a href=\"https://wikipedia.org/wiki/World%27s_tallest_building\" class=\"mw-redirect\" title=\"World's tallest building\">world's tallest building</a>.", "links": [{"title": "Sears Tower", "link": "https://wikipedia.org/wiki/Sears_Tower"}, {"title": "World's tallest building", "link": "https://wikipedia.org/wiki/World%27s_tallest_building"}]}, {"year": "1978", "text": "The South African Defence Force attacks a SWAPO base at Cassinga in southern Angola, killing about 600 people.", "html": "1978 - The <a href=\"https://wikipedia.org/wiki/South_African_Defence_Force\" title=\"South African Defence Force\">South African Defence Force</a> attacks a <a href=\"https://wikipedia.org/wiki/South_West_Africa_People%27s_Organization\" class=\"mw-redirect\" title=\"South West Africa People's Organization\">SWAPO</a> base at <a href=\"https://wikipedia.org/wiki/Cassinga\" title=\"Cassinga\">Cassinga</a> in southern <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing about 600 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/South_African_Defence_Force\" title=\"South African Defence Force\">South African Defence Force</a> attacks a <a href=\"https://wikipedia.org/wiki/South_West_Africa_People%27s_Organization\" class=\"mw-redirect\" title=\"South West Africa People's Organization\">SWAPO</a> base at <a href=\"https://wikipedia.org/wiki/Cassinga\" title=\"Cassinga\">Cassinga</a> in southern <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angola</a>, killing about 600 people.", "links": [{"title": "South African Defence Force", "link": "https://wikipedia.org/wiki/South_African_Defence_Force"}, {"title": "South West Africa People's Organization", "link": "https://wikipedia.org/wiki/South_West_Africa_People%27s_Organization"}, {"title": "Cassinga", "link": "https://wikipedia.org/wiki/Cassinga"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}]}, {"year": "1979", "text": "<PERSON> becomes the first female Prime Minister of the United Kingdom.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first female <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1982", "text": "Twenty sailors are killed when the British Type 42 destroyer HMS Sheffield is hit by an Argentinian Exocet missile during the Falklands War.", "html": "1982 - Twenty sailors are killed when the British <a href=\"https://wikipedia.org/wiki/Type_42_destroyer\" title=\"Type 42 destroyer\">Type 42 destroyer</a> <a href=\"https://wikipedia.org/wiki/HMS_Sheffield_(D80)\" title=\"HMS Sheffield (D80)\">HMS <i>Sheffield</i></a> is hit by an <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentinian</a> <a href=\"https://wikipedia.org/wiki/Exocet\" title=\"Exocet\">Exocet</a> missile during the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "no_year_html": "Twenty sailors are killed when the British <a href=\"https://wikipedia.org/wiki/Type_42_destroyer\" title=\"Type 42 destroyer\">Type 42 destroyer</a> <a href=\"https://wikipedia.org/wiki/HMS_Sheffield_(D80)\" title=\"HMS Sheffield (D80)\">HMS <i>Sheffield</i></a> is hit by an <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentinian</a> <a href=\"https://wikipedia.org/wiki/Exocet\" title=\"Exocet\">Exocet</a> missile during the <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>.", "links": [{"title": "Type 42 destroyer", "link": "https://wikipedia.org/wiki/Type_42_destroyer"}, {"title": "HMS Sheffield (D80)", "link": "https://wikipedia.org/wiki/HMS_Sheffield_(D80)"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "Exocet", "link": "https://wikipedia.org/wiki/Exocet"}, {"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}]}, {"year": "1988", "text": "The PEPCON disaster rocks Henderson, Nevada, as tons of Space Shuttle fuel detonate during a fire.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/PEPCON_disaster\" title=\"PEPCON disaster\">PEPCON disaster</a> rocks <a href=\"https://wikipedia.org/wiki/Henderson,_Nevada\" title=\"Henderson, Nevada\">Henderson, Nevada</a>, as tons of <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> fuel detonate during a fire.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/PEPCON_disaster\" title=\"PEPCON disaster\">PEPCON disaster</a> rocks <a href=\"https://wikipedia.org/wiki/Henderson,_Nevada\" title=\"Henderson, Nevada\">Henderson, Nevada</a>, as tons of <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> fuel detonate during a fire.", "links": [{"title": "PEPCON disaster", "link": "https://wikipedia.org/wiki/PEPCON_disaster"}, {"title": "Henderson, Nevada", "link": "https://wikipedia.org/wiki/Henderson,_Nevada"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}]}, {"year": "1989", "text": "Iran-Contra affair: Former White House aide <PERSON> is convicted of three crimes and acquitted of nine other charges; the convictions are later overturned on appeal.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> aide <a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a> is convicted of three crimes and acquitted of nine other charges; the convictions are later overturned on appeal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: Former <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> aide <a href=\"https://wikipedia.org/wiki/Oliver_North\" title=\"Oliver North\"><PERSON></a> is convicted of three crimes and acquitted of nine other charges; the convictions are later overturned on appeal.", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Oliver_North"}]}, {"year": "1989", "text": "Space Shuttle Atlantis launches on mission STS-30 to deploy the Venus-bound Magellan space probe.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on mission <a href=\"https://wikipedia.org/wiki/STS-30\" title=\"STS-30\">STS-30</a> to deploy the <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>-bound <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\"><PERSON><PERSON><PERSON></a></i> space probe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> launches on mission <a href=\"https://wikipedia.org/wiki/STS-30\" title=\"STS-30\">STS-30</a> to deploy the <a href=\"https://wikipedia.org/wiki/Venus\" title=\"Venus\">Venus</a>-bound <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)\" title=\"<PERSON><PERSON><PERSON> (spacecraft)\"><PERSON><PERSON><PERSON></a></i> space probe.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-30", "link": "https://wikipedia.org/wiki/STS-30"}, {"title": "Venus", "link": "https://wikipedia.org/wiki/Venus"}, {"title": "<PERSON><PERSON><PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(spacecraft)"}]}, {"year": "1990", "text": "Latvia declares independence from the Soviet Union.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> <a href=\"https://wikipedia.org/wiki/On_the_Restoration_of_Independence_of_the_Republic_of_Latvia\" title=\"On the Restoration of Independence of the Republic of Latvia\">declares independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Latvia\" title=\"Latvia\">Latvia</a> <a href=\"https://wikipedia.org/wiki/On_the_Restoration_of_Independence_of_the_Republic_of_Latvia\" title=\"On the Restoration of Independence of the Republic of Latvia\">declares independence</a> from the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Latvia", "link": "https://wikipedia.org/wiki/Latvia"}, {"title": "On the Restoration of Independence of the Republic of Latvia", "link": "https://wikipedia.org/wiki/On_the_Restoration_of_Independence_of_the_Republic_of_Latvia"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1994", "text": "Israeli Prime Minister <PERSON><PERSON><PERSON> and PLO leader <PERSON><PERSON> sign a peace accord, granting self-rule in the Gaza Strip and Jericho.", "html": "1994 - Israeli <a href=\"https://wikipedia.org/wiki/Prime_Minister\" class=\"mw-redirect\" title=\"Prime Minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">PLO</a> leader <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sign <a href=\"https://wikipedia.org/wiki/Oslo_I_Accord\" title=\"Oslo I Accord\">a peace accord</a>, granting self-rule in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a>.", "no_year_html": "Israeli <a href=\"https://wikipedia.org/wiki/Prime_Minister\" class=\"mw-redirect\" title=\"Prime Minister\">Prime Minister</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">PLO</a> leader <a href=\"https://wikipedia.org/wiki/Ya<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> sign <a href=\"https://wikipedia.org/wiki/Oslo_I_Accord\" title=\"Oslo I Accord\">a peace accord</a>, granting self-rule in the <a href=\"https://wikipedia.org/wiki/Gaza_Strip\" title=\"Gaza Strip\">Gaza Strip</a> and <a href=\"https://wikipedia.org/wiki/Jericho\" title=\"Jericho\">Jericho</a>.", "links": [{"title": "Prime Minister", "link": "https://wikipedia.org/wiki/Prime_Minister"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Oslo I Accord", "link": "https://wikipedia.org/wiki/Oslo_I_Accord"}, {"title": "Gaza Strip", "link": "https://wikipedia.org/wiki/Gaza_Strip"}, {"title": "Jericho", "link": "https://wikipedia.org/wiki/Jericho"}]}, {"year": "1998", "text": "A federal judge in Sacramento, California, gives \"Unabomber\" <PERSON> four life sentences plus 30 years after <PERSON><PERSON><PERSON><PERSON> accepts a plea agreement sparing him from the death penalty.", "html": "1998 - A federal judge in <a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>, gives \"<a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> four life sentences plus 30 years after <PERSON><PERSON><PERSON><PERSON> accepts a plea agreement sparing him from the <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">death penalty</a>.", "no_year_html": "A federal judge in <a href=\"https://wikipedia.org/wiki/Sacramento,_California\" title=\"Sacramento, California\">Sacramento, California</a>, gives \"<a href=\"https://wikipedia.org/wiki/Unabomber\" class=\"mw-redirect\" title=\"Unabomber\">Unabomber</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> four life sentences plus 30 years after <PERSON><PERSON><PERSON><PERSON> accepts a plea agreement sparing him from the <a href=\"https://wikipedia.org/wiki/Capital_punishment\" title=\"Capital punishment\">death penalty</a>.", "links": [{"title": "Sacramento, California", "link": "https://wikipedia.org/wiki/Sacramento,_California"}, {"title": "Unabomber", "link": "https://wikipedia.org/wiki/Unabomber"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Capital punishment", "link": "https://wikipedia.org/wiki/Capital_punishment"}]}, {"year": "2000", "text": "<PERSON> becomes the first Mayor of London (an office separate from that of the Lord Mayor of London).", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/2000_London_mayoral_election\" title=\"2000 London mayoral election\">first</a> <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a> (an office separate from that of the <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/2000_London_mayoral_election\" title=\"2000 London mayoral election\">first</a> <a href=\"https://wikipedia.org/wiki/Mayor_of_London\" title=\"Mayor of London\">Mayor of London</a> (an office separate from that of the <a href=\"https://wikipedia.org/wiki/Lord_Mayor_of_London\" title=\"Lord Mayor of London\">Lord Mayor of London</a>).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2000 London mayoral election", "link": "https://wikipedia.org/wiki/2000_London_mayoral_election"}, {"title": "Mayor of London", "link": "https://wikipedia.org/wiki/Mayor_of_London"}, {"title": "Lord Mayor of London", "link": "https://wikipedia.org/wiki/Lord_Mayor_of_London"}]}, {"year": "2002", "text": "One hundred three people are killed and 51 are injured in a plane crash near Mallam Aminu Kano International Airport in Kano, Nigeria.", "html": "2002 - One hundred three people are killed and 51 are injured in a <a href=\"https://wikipedia.org/wiki/EAS_Airlines_Flight_4226\" title=\"EAS Airlines Flight 4226\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Mallam_Aminu_Kano_International_Airport\" title=\"Mallam Aminu Kano International Airport\">Mallam Aminu Kano International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kano_(city)\" title=\"Kano (city)\">Kano</a>, Nigeria.", "no_year_html": "One hundred three people are killed and 51 are injured in a <a href=\"https://wikipedia.org/wiki/EAS_Airlines_Flight_4226\" title=\"EAS Airlines Flight 4226\">plane crash</a> near <a href=\"https://wikipedia.org/wiki/Mallam_Aminu_Kano_International_Airport\" title=\"Mallam Aminu Kano International Airport\">Mallam Aminu Kano International Airport</a> in <a href=\"https://wikipedia.org/wiki/Kano_(city)\" title=\"Kano (city)\">Kano</a>, Nigeria.", "links": [{"title": "EAS Airlines Flight 4226", "link": "https://wikipedia.org/wiki/EAS_Airlines_Flight_4226"}, {"title": "Mallam Aminu Kano International Airport", "link": "https://wikipedia.org/wiki/Mallam_Aminu_Kano_International_Airport"}, {"title": "Kano (city)", "link": "https://wikipedia.org/wiki/Kano_(city)"}]}, {"year": "2007", "text": "Greensburg, Kansas is almost completely destroyed by the 2007 Greensburg tornado, a 1.7-mile wide EF5 tornado. It was the first-ever tornado to be rated as such with the new Enhanced Fujita scale.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Greensburg,_Kansas\" title=\"Greensburg, Kansas\">Greensburg, Kansas</a> is almost completely destroyed by the <a href=\"https://wikipedia.org/wiki/2007_Greensburg_tornado\" class=\"mw-redirect\" title=\"2007 Greensburg tornado\">2007 Greensburg tornado</a>, a 1.7-mile wide <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">EF5</a> <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a>. It was the first-ever tornado to be rated as such with the new <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">Enhanced Fujita scale</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greensburg,_Kansas\" title=\"Greensburg, Kansas\">Greensburg, Kansas</a> is almost completely destroyed by the <a href=\"https://wikipedia.org/wiki/2007_Greensburg_tornado\" class=\"mw-redirect\" title=\"2007 Greensburg tornado\">2007 Greensburg tornado</a>, a 1.7-mile wide <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">EF5</a> <a href=\"https://wikipedia.org/wiki/Tornado\" title=\"Tornado\">tornado</a>. It was the first-ever tornado to be rated as such with the new <a href=\"https://wikipedia.org/wiki/Enhanced_Fujita_scale\" title=\"Enhanced Fujita scale\">Enhanced Fujita scale</a>.", "links": [{"title": "Greensburg, Kansas", "link": "https://wikipedia.org/wiki/Greensburg,_Kansas"}, {"title": "2007 Greensburg tornado", "link": "https://wikipedia.org/wiki/2007_Greensburg_tornado"}, {"title": "Enhanced Fujita scale", "link": "https://wikipedia.org/wiki/Enhanced_Fujita_scale"}, {"title": "Tornado", "link": "https://wikipedia.org/wiki/Tornado"}, {"title": "Enhanced Fujita scale", "link": "https://wikipedia.org/wiki/Enhanced_Fujita_scale"}]}, {"year": "2014", "text": "Three people are killed and 62 injured in a pair of bombings on buses in Nairobi, Kenya.", "html": "2014 - Three people are killed and 62 injured in a <a href=\"https://wikipedia.org/wiki/2014_Nairobi_bus_bombings\" title=\"2014 Nairobi bus bombings\">pair of bombings</a> on buses in <a href=\"https://wikipedia.org/wiki/Nairobi,_Kenya\" class=\"mw-redirect\" title=\"Nairobi, Kenya\">Nairobi, Kenya</a>.", "no_year_html": "Three people are killed and 62 injured in a <a href=\"https://wikipedia.org/wiki/2014_Nairobi_bus_bombings\" title=\"2014 Nairobi bus bombings\">pair of bombings</a> on buses in <a href=\"https://wikipedia.org/wiki/Nairobi,_Kenya\" class=\"mw-redirect\" title=\"Nairobi, Kenya\">Nairobi, Kenya</a>.", "links": [{"title": "2014 Nairobi bus bombings", "link": "https://wikipedia.org/wiki/2014_Nairobi_bus_bombings"}, {"title": "Nairobi, Kenya", "link": "https://wikipedia.org/wiki/Nairobi,_Kenya"}]}, {"year": "2019", "text": "The inaugural all-female motorsport series, W Series, takes place at Hockenheimring. The race was won by <PERSON>, who would go on to become the inaugural season's champion.", "html": "2019 - The inaugural all-female motorsport series, <a href=\"https://wikipedia.org/wiki/W_Series_(championship)\" title=\"W Series (championship)\">W Series</a>, takes place at <a href=\"https://wikipedia.org/wiki/Hockenheimring\" title=\"Hockenheimring\">Hockenheimring</a>. The race was won by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would go on to become <a href=\"https://wikipedia.org/wiki/2019_W_Series\" title=\"2019 W Series\">the inaugural season's</a> champion.", "no_year_html": "The inaugural all-female motorsport series, <a href=\"https://wikipedia.org/wiki/W_Series_(championship)\" title=\"W Series (championship)\">W Series</a>, takes place at <a href=\"https://wikipedia.org/wiki/Hockenheimring\" title=\"Hockenheimring\">Hockenheimring</a>. The race was won by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who would go on to become <a href=\"https://wikipedia.org/wiki/2019_W_Series\" title=\"2019 W Series\">the inaugural season's</a> champion.", "links": [{"title": "W Series (championship)", "link": "https://wikipedia.org/wiki/W_Series_(championship)"}, {"title": "Hockenheimring", "link": "https://wikipedia.org/wiki/Hockenheimring"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "2019 W Series", "link": "https://wikipedia.org/wiki/2019_W_Series"}]}, {"year": "2023", "text": "Nine people are killed and thirteen injured in a spree shooting in Mladenovac and Smederevo, Serbia. It is the second mass shooting in the country in two days.", "html": "2023 - Nine people are killed and thirteen injured in <a href=\"https://wikipedia.org/wiki/Mladenovac_and_Smederevo_shootings\" title=\"Mladenovac and Smederevo shootings\">a spree shooting</a> in <a href=\"https://wikipedia.org/wiki/Mladenovac\" title=\"Mladenovac\">Mladenovac</a> and <a href=\"https://wikipedia.org/wiki/Smederevo\" title=\"Smederevo\">Smederevo</a>, Serbia. It is the second mass shooting in the country in two days.", "no_year_html": "Nine people are killed and thirteen injured in <a href=\"https://wikipedia.org/wiki/Mladenovac_and_Smederevo_shootings\" title=\"Mladenovac and Smederevo shootings\">a spree shooting</a> in <a href=\"https://wikipedia.org/wiki/Mladenovac\" title=\"Mladenovac\">Mladenovac</a> and <a href=\"https://wikipedia.org/wiki/Smederevo\" title=\"Smederevo\">Smederevo</a>, Serbia. It is the second mass shooting in the country in two days.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> and S<PERSON>ere<PERSON> shootings", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_and_<PERSON><PERSON><PERSON><PERSON>_shootings"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>c"}, {"title": "Smederevo", "link": "https://wikipedia.org/wiki/Smederevo"}]}], "Births": [{"year": "1006", "text": "<PERSON><PERSON><PERSON>, Persian mystic and poet (d. 1088)", "html": "1006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Persian mystic and poet (d. 1088)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Persian mystic and poet (d. 1088)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1559", "text": "<PERSON>, English noblewoman (d. 1637)", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby,_Baroness_<PERSON>_and_Viscountess_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Derby, Baroness <PERSON> and Viscountess <PERSON><PERSON>\"><PERSON></a>, English noblewoman (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby,_Baroness_<PERSON>_and_Viscountess_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Countess of Derby, Baroness <PERSON> and Viscountess <PERSON><PERSON>\"><PERSON></a>, English noblewoman (d. 1637)", "links": [{"title": "<PERSON>, Countess of Derby, Baroness <PERSON> and Viscountess <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Countess_of_Derby,_Baroness_<PERSON><PERSON><PERSON>_and_Viscountess_<PERSON><PERSON>"}]}, {"year": "1634", "text": "<PERSON>, English aristocrat and heiress (d. 1660)", "html": "1634 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English aristocrat and heiress (d. 1660)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English aristocrat and heiress (d. 1660)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1649", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian ruler (d. 1731)", "html": "1649 - <a href=\"https://wikipedia.org/wiki/Chhatrasal\" title=\"<PERSON><PERSON>ras<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>hatrasal\" title=\"<PERSON><PERSON>ras<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1731)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chhatrasal"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian instrument maker, invented the piano (d. 1731)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian instrument maker, invented the <a href=\"https://wikipedia.org/wiki/Piano\" title=\"Piano\">piano</a> (d. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian instrument maker, invented the <a href=\"https://wikipedia.org/wiki/Piano\" title=\"Piano\">piano</a> (d. 1731)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Piano", "link": "https://wikipedia.org/wiki/Piano"}]}, {"year": "1677", "text": "<PERSON><PERSON><PERSON><PERSON>, French noblewoman (d. 1749)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French noblewoman (d. 1749)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French noblewoman (d. 1749)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>-<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1715", "text": "<PERSON>, English minister and author (d. 1804)", "html": "1715 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and author (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1733", "text": "<PERSON><PERSON><PERSON>, French mathematician, physicist, and sailor (d. 1799)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and sailor (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician, physicist, and sailor (d. 1799)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1752", "text": "<PERSON>, American soldier and politician, 11th Governor of Massachusetts (d. 1825)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American soldier and politician, 11th <a href=\"https://wikipedia.org/wiki/Governor_of_Massachusetts\" title=\"Governor of Massachusetts\">Governor of Massachusetts</a> (d. 1825)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(governor)"}, {"title": "Governor of Massachusetts", "link": "https://wikipedia.org/wiki/Governor_of_Massachusetts"}]}, {"year": "1757", "text": "<PERSON>, Spanish sculptor and first director of the Academy of San Carlos in Mexico City (d. 1816)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and first director of the <a href=\"https://wikipedia.org/wiki/Academy_of_San_Carlos\" title=\"Academy of San Carlos\">Academy of San Carlos</a> in Mexico City (d. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Spanish sculptor and first director of the <a href=\"https://wikipedia.org/wiki/Academy_of_San_Carlos\" title=\"Academy of San Carlos\">Academy of San Carlos</a> in Mexico City (d. 1816)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_To<PERSON>%C3%A1"}, {"title": "Academy of San Carlos", "link": "https://wikipedia.org/wiki/Academy_of_San_Carlos"}]}, {"year": "1767", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian composer (d. 1847)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/Tyagaraja\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian composer (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ty<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian composer (d. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tyagaraja"}]}, {"year": "1770", "text": "<PERSON>, French painter (d. 1837)", "html": "1770 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rard\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rard\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_G%C3%A9rard"}]}, {"year": "1772", "text": "<PERSON>, German publisher (d. 1823)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (d. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German publisher (d. 1823)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American educator and politician (d. 1859)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American educator and politician (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American lawyer and politician, 13th Governor of New Jersey, 23rd Speaker of the United States House of Representatives (d. 1862)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, 23rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_New_Jersey\" title=\"Governor of New Jersey\">Governor of New Jersey</a>, 23rd <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (d. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of New Jersey", "link": "https://wikipedia.org/wiki/Governor_of_New_Jersey"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "1796", "text": "<PERSON>, American historian and scholar (d. 1859)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (d. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and scholar (d. 1859)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American wife of <PERSON>, 11th First Lady of the United States (d. 1889)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wife of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, 11th <a href=\"https://wikipedia.org/wiki/First_Lady_of_the_United_States\" title=\"First Lady of the United States\">First Lady of the United States</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Lady of the United States", "link": "https://wikipedia.org/wiki/First_Lady_of_the_United_States"}]}, {"year": "1820", "text": "<PERSON>, American soldier, judge, and politician, 1st Governor of Oregon (d. 1902)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, judge, and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1822", "text": "<PERSON>, Canadian physician and politician, 3rd Premier of Quebec (d. 1915)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>Bouche<PERSON>\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Canadian physician and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>Bo<PERSON>\" title=\"<PERSON> Bo<PERSON>\"><PERSON></a>, Canadian physician and politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1825", "text": "<PERSON>, English biologist, anatomist, and academic (d. 1895)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and academic (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, anatomist, and academic (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1825", "text": "<PERSON>, English-American historian, photographer, and academic (d. 1908)", "html": "1825 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian, photographer, and academic (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian, photographer, and academic (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1826", "text": "<PERSON>, American painter (d. 1900)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, English soldier and explorer (d. 1864)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and explorer (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and explorer (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON>, German opera singer (d. 1896)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Bian<PERSON> Blu<PERSON>\"><PERSON><PERSON><PERSON></a>, German opera singer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Blu<PERSON>\"><PERSON><PERSON><PERSON></a>, German opera singer (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>me"}]}, {"year": "1851", "text": "<PERSON>, American painter (d. 1938)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, English model (d. 1934)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Chinese politician (d. 1944)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, English army officer and cricketer (d. 1975)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English army officer and cricketer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English army officer and cricketer (d. 1975)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1887", "text": "<PERSON>, French-American painter (d. 1979)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American painter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American cardinal (d. 1967)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cardinal (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Canadian painter (d. 1945)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English cricketer and administrator (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and administrator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and administrator (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American actor (d. 1984)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American country singer-songwriter and guitarist (d. 1984)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter and guitarist (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, American soldier and playwright, co-founded the New York City Ballet (d. 1996)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Lincoln_Kirstein\" title=\"<PERSON> Kirstein\"><PERSON></a>, American soldier and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kirstein\" title=\"<PERSON> Kirstein\"><PERSON></a>, American soldier and playwright, co-founded the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lincoln_<PERSON>ein"}, {"title": "New York City Ballet", "link": "https://wikipedia.org/wiki/New_York_City_Ballet"}]}, {"year": "1907", "text": "<PERSON>, American target shooter and FBI agent (d. 2014)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American target shooter and <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a> agent (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}]}, {"year": "1913", "text": "Princess <PERSON> of Greece and Denmark (d. 2007)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Greece_and_Denmark\" title=\"Princess <PERSON> of Greece and Denmark\">Princess <PERSON> of Greece and Denmark</a> (d. 2007)", "links": [{"title": "Princess <PERSON> of Greece and Denmark", "link": "https://wikipedia.org/wiki/Princess_Katherine_of_Greece_and_Denmark"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 39th <PERSON><PERSON><PERSON><PERSON> (d. 1971)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D\" title=\"Maedayama Eigorō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 39th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yo<PERSON><PERSON>na\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D\" title=\"Maedayama Eigorō\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 39th <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 1971)", "links": [{"title": "Maedayama Eigorō", "link": "https://wikipedia.org/wiki/Maedayama_Eigor%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1916", "text": "<PERSON>, American-Canadian journalist, author, and activist (d. 2006)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist, author, and activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian journalist, author, and activist (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American soldier, carpenter, and meteorologist (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, carpenter, and meteorologist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, carpenter, and meteorologist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Filipino writer, journalist and historian (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino writer, journalist and historian (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino writer, journalist and historian (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Japanese soldier and politician, 64th Prime Minister of Japan (d. 1993)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese soldier and politician, 64th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (d. 1993)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1921", "text": "<PERSON>, Croatian painter, sculptor, and illustrator (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Edo_Mu<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter, sculptor, and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edo_<PERSON>i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian painter, sculptor, and illustrator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edo_Murti%C4%87"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, American biologist and academic (d. 2015)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American biologist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American biologist and academic (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, British actor and comedian (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and comedian (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor and comedian (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American businessman and philanthropist", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian trumpet player and bandleader (d. 2006)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trumpet player and bandleader (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trumpet player and bandleader (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Egyptian air marshal and politician, 4th President of Egypt (d. 2020)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian air marshal and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Egyptian air marshal and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>k"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}]}, {"year": "1928", "text": "<PERSON>, American golfer (d. 2023)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Chilean general (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean general (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Belgian-British actress and humanitarian (d. 1993)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-British actress and humanitarian (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-British actress and humanitarian (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, matriarch of the <PERSON> family", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, matriarch of the <PERSON> family", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, matriarch of the <PERSON> family", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, American football player and coach (d. 2013)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Harlon_Hill\" title=\"Harlon Hill\"><PERSON><PERSON><PERSON></a>, American football player and coach (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Harlon_<PERSON>\" title=\"Harlon Hill\"><PERSON><PERSON><PERSON></a>, American football player and coach (d. 2013)", "links": [{"title": "Harlon Hill", "link": "https://wikipedia.org/wiki/Harlon_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American bassist and educator", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bassist and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American surf-rock guitarist, singer, and songwriter (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surf-rock guitarist, singer, and songwriter (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surf-rock guitarist, singer, and songwriter (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Mexican journalist, author, and critic (d. 2010)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1is\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist, author, and critic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1is\" title=\"<PERSON>\"><PERSON></a>, Mexican journalist, author, and critic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carlos_Monsiv%C3%A1is"}]}, {"year": "1939", "text": "<PERSON>, Israeli journalist and author (d. 2018)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amos Oz\"><PERSON></a>, Israeli journalist and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Amos Oz\"><PERSON></a>, Israeli journalist and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American physician and author", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_novelist)\" title=\"<PERSON> (American novelist)\"><PERSON></a>, American physician and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_novelist)\" title=\"<PERSON> (American novelist)\"><PERSON></a>, American physician and author", "links": [{"title": "<PERSON> (American novelist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_novelist)"}]}, {"year": "1941", "text": "<PERSON>, American journalist and author", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Bulgarian footballer (d. 1971)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bulgarian footballer (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American voice actress (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American voice actress (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor (d. 2025)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2025)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1946", "text": "<PERSON>, English car designer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American political activist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, British race car driver", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_(racing_driver)"}]}, {"year": "1948", "text": "King <PERSON> Tonga, (d. 2012)", "html": "1948 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON> V\"><PERSON></a> of Tonga, (d. 2012)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON> V\"><PERSON></a> of Tonga, (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English bass player, songwriter, and producer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, American lawyer and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and dancer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian beauty queen and 1972 Miss World", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian beauty queen and 1972 <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian beauty queen and 1972 <a href=\"https://wikipedia.org/wiki/Miss_World\" title=\"Miss World\">Miss World</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss World", "link": "https://wikipedia.org/wiki/Miss_World"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American actress and singer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Zador<PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "Pia Z<PERSON>", "link": "https://wikipedia.org/wiki/Pia_Zadora"}]}, {"year": "1954", "text": "<PERSON>, Filipino pianist, composer, and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American baseball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Canadian skier", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American painter (d. 1990)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English politician, Secretary of State for Environment, Food and Rural Affairs", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Environment,_Food_and_Rural_Affairs\" title=\"Secretary of State for Environment, Food and Rural Affairs\">Secretary of State for Environment, Food and Rural Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Environment,_Food_and_Rural_Affairs\" title=\"Secretary of State for Environment, Food and Rural Affairs\">Secretary of State for Environment, Food and Rural Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Environment, Food and Rural Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Environment,_Food_and_Rural_Affairs"}]}, {"year": "1959", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Austrian politician, 28th Chancellor of Austria", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 28th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian politician, 28th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Austria\" title=\"Chancellor of Austria\">Chancellor of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chancellor of Austria", "link": "https://wikipedia.org/wiki/Chancellor_of_Austria"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and dancer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English journalist", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kate_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actress and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ana_Gasteyer"}]}, {"year": "1970", "text": "<PERSON>, Canadian actor and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American basketball player", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>aley\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dawn_Staley"}]}, {"year": "1972", "text": "<PERSON>, American bass player and songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer-songwriter", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Argentinian footballer and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1974", "text": "<PERSON>, Northern Irish jockey and sportscaster", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish jockey and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish jockey and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American model", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American sportscaster and journalist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Croatian footballer and manager", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Igor_<PERSON>can"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON>, American singer, dancer, and producer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, dancer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American singer, dancer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Tongan rugby player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Tongan rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>-<PERSON><PERSON><PERSON>, Cameroon footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroon footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroon footballer", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Ethiopian-Irish actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ethiopian-Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ethiopian-Irish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American singer-songwriter and musician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Weekes\" title=\"<PERSON><PERSON> Weekes\"><PERSON><PERSON></a>, American singer-songwriter and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Weekes\" title=\"<PERSON><PERSON> Weekes\"><PERSON><PERSON></a>, American singer-songwriter and musician", "links": [{"title": "Dallon Weekes", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Weekes"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American wrestler and referee", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, English cricketer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born May 1985)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1985)\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born May 1985)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born May 1985)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_(footballer,_born_May_1985)"}]}, {"year": "1985", "text": "<PERSON>, English MC and rapper", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English MC and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English MC and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Spanish footballer and manager", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Cesc_F%C3%A0bregas\" title=\"Cesc Fàbregas\"><PERSON><PERSON><PERSON> Fàbregas</a>, Spanish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cesc_F%C3%A0bregas\" title=\"Cesc Fàbregas\"><PERSON><PERSON><PERSON> Fàbregas</a>, Spanish footballer and manager", "links": [{"title": "Cesc Fàbregas", "link": "https://wikipedia.org/wiki/Cesc_F%C3%A0bregas"}]}, {"year": "1987", "text": "<PERSON>, Spanish motorcycle racer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish motorcycle racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Belgian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>an"}]}, {"year": "1989", "text": "<PERSON>, Northern Irish golfer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Canadian women's ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian women's ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian women's ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Japanese sumo wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bi Masatora\"><PERSON><PERSON></a>, Japanese sumo wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>bi Masatora\"><PERSON><PERSON></a>, Japanese sumo wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1994", "text": "<PERSON>, New Zealand rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, English musician", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Rex_Orange_County\" title=\"Rex Orange County\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rex_Orange_County\" title=\"Rex Orange County\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rex_Orange_County"}]}], "Deaths": [{"year": "408", "text": "<PERSON><PERSON><PERSON>, archbishop of Milan", "html": "408 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (bishop of Milan)\"><PERSON><PERSON><PERSON></a>, archbishop of Milan", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)\" title=\"<PERSON><PERSON><PERSON> (bishop of Milan)\"><PERSON><PERSON><PERSON></a>, archbishop of Milan", "links": [{"title": "<PERSON><PERSON><PERSON> (bishop of Milan)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(bishop_of_Milan)"}]}, {"year": "784", "text": "<PERSON><PERSON><PERSON>, bishop of Freising", "html": "784 - <a href=\"https://wikipedia.org/wiki/Arb<PERSON>_of_Freising\" title=\"<PERSON><PERSON><PERSON> of Freising\"><PERSON><PERSON><PERSON></a>, bishop of Freising", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Arb<PERSON>_of_Freising\" title=\"<PERSON><PERSON><PERSON> of Freising\"><PERSON><PERSON><PERSON></a>, bishop of Freising", "links": [{"title": "<PERSON><PERSON><PERSON> of Freising", "link": "https://wikipedia.org/wiki/Arbeo_of_<PERSON><PERSON><PERSON>"}]}, {"year": "1003", "text": "<PERSON>, duke of Swabia", "html": "1003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON> II</a>, duke of Swabia", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia\" title=\"<PERSON>, Duke of Swabia\"><PERSON> II</a>, duke of Swabia", "links": [{"title": "<PERSON>, Duke of Swabia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Swabia"}]}, {"year": "1038", "text": "<PERSON><PERSON><PERSON> of Hildesheim, German bishop (b. 960)", "html": "1038 - <a href=\"https://wikipedia.org/wiki/Gotthard_of_Hildesheim\" title=\"<PERSON><PERSON><PERSON> of Hildesheim\"><PERSON><PERSON><PERSON> of Hildesheim</a>, German bishop (b. 960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>tha<PERSON>_of_Hildesheim\" title=\"<PERSON><PERSON><PERSON> of Hildesheim\"><PERSON><PERSON><PERSON> of Hildesheim</a>, German bishop (b. 960)", "links": [{"title": "<PERSON><PERSON><PERSON> of Hildesheim", "link": "https://wikipedia.org/wiki/<PERSON>tha<PERSON>_of_Hi<PERSON>sheim"}]}, {"year": "1406", "text": "<PERSON><PERSON><PERSON>, chancellor of Florence (b. 1331)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, chancellor of Florence (b. 1331)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, chancellor of Florence (b. 1331)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1436", "text": "<PERSON><PERSON><PERSON><PERSON> Engelbre<PERSON>, Swedish rebel leader (27 April O.S.).", "html": "1436 - <a href=\"https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson\" title=\"Engelbrekt Engelbrektsson\">Engelbrekt Engelbrektsson</a>, Swedish rebel leader (27 April <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson\" title=\"Engelbrekt Engelbrektsson\">Engelbrekt Engelbrektsson</a>, Swedish rebel leader (27 April <a href=\"https://wikipedia.org/wiki/Old_Style_and_New_Style_dates\" title=\"Old Style and New Style dates\">O.S.</a>).", "links": [{"title": "Engelbrekt Engelbrektsson", "link": "https://wikipedia.org/wiki/Engelbrekt_Engelbrektsson"}, {"title": "Old Style and New Style dates", "link": "https://wikipedia.org/wiki/Old_Style_and_New_Style_dates"}]}, {"year": "1471", "text": "<PERSON>, Prince of Wales, son and heir of <PERSON> of England (b. 1453)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westminster,_Prince_of_Wales\" title=\"<PERSON> of Westminster, Prince of Wales\"><PERSON> of Westminster, Prince of Wales</a>, son and heir of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI of England</a> (b. 1453)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westminster,_Prince_of_Wales\" title=\"<PERSON> of Westminster, Prince of Wales\"><PERSON> Westminster, Prince of Wales</a>, son and heir of <a href=\"https://wikipedia.org/wiki/<PERSON>_VI_of_England\" title=\"<PERSON> VI of England\"><PERSON> VI of England</a> (b. 1453)", "links": [{"title": "<PERSON> Westminster, Prince of Wales", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Westminster,_Prince_of_Wales"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VI_of_England"}]}, {"year": "1471", "text": "<PERSON>, 4th Duke of Somerset (b. 1438)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Somerset\"><PERSON>, 4th Duke of Somerset</a> (b. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Somerset\" class=\"mw-redirect\" title=\"<PERSON>, 4th Duke of Somerset\"><PERSON>, 4th Duke of Somerset</a> (b. 1438)", "links": [{"title": "<PERSON>, 4th Duke of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Somerset"}]}, {"year": "1483", "text": "<PERSON>, Duke of Bedford (b. 1457)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bedford\" title=\"<PERSON>, Duke of Bedford\"><PERSON>, Duke of Bedford</a> (b. 1457)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bedford\" title=\"<PERSON>, Duke of Bedford\"><PERSON>, Duke of Bedford</a> (b. 1457)", "links": [{"title": "<PERSON>, Duke of Bedford", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_of_Bedford"}]}, {"year": "1506", "text": "<PERSON><PERSON><PERSON>, Timurid ruler of Herat (b. 1438)", "html": "1506 - <a href=\"https://wikipedia.org/wiki/Sultan_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sultan <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Timurid ruler of Herat (b. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sultan <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Timurid ruler of Herat (b. 1438)", "links": [{"title": "Sultan <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1519", "text": "<PERSON>, duke of Urbino (b. 1492)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Urbino\" title=\"<PERSON>, Duke of Urbino\"><PERSON></a>, duke of Urbino (b. 1492)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Urbino\" title=\"<PERSON>, Duke of Urbino\"><PERSON></a>, duke of Urbino (b. 1492)", "links": [{"title": "<PERSON>, Duke of Urbino", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Duke_of_Urbino"}]}, {"year": "1535", "text": "<PERSON>, Carthusian monk and saint", "html": "1535 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, Carthusian monk and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(martyr)\" title=\"<PERSON> (martyr)\"><PERSON></a>, Carthusian monk and saint", "links": [{"title": "<PERSON> (martyr)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(martyr)"}]}, {"year": "1562", "text": "<PERSON><PERSON>, Italian Protestant theologian (b. 1525)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Protestant theologian (b. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian Protestant theologian (b. 1525)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1566", "text": "<PERSON>, Italian physician and botanist (b. 1490)", "html": "1566 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and botanist (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian physician and botanist (b. 1490)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1571", "text": "<PERSON>, Swiss theologian and reformer (b. 1511)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1511)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss theologian and reformer (b. 1511)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1604", "text": "<PERSON>, Italian organist and composer (b. 1533)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian organist and composer (b. 1533)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1605", "text": "<PERSON><PERSON><PERSON>, Italian naturalist (b. 1522)", "html": "1605 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian naturalist (b. 1522)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian naturalist (b. 1522)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ndi"}]}, {"year": "1615", "text": "<PERSON><PERSON><PERSON>, Flemish priest and mathematician (b. 1561)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish priest and mathematician (b. 1561)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1626", "text": "<PERSON>, English bishop and scholar (b. 1569)", "html": "1626 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and scholar (b. 1569)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)\" title=\"<PERSON> (bishop)\"><PERSON></a>, English bishop and scholar (b. 1569)", "links": [{"title": "<PERSON> (bishop)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bishop)"}]}, {"year": "1677", "text": "<PERSON>, English mathematician and theologian (b. 1630)", "html": "1677 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theologian (b. 1630)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and theologian (b. 1630)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1684", "text": "<PERSON>, English criminal (b. 1639)", "html": "1684 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English criminal (b. 1639)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, French cardinal (b. 1651)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1651)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal (b. 1651)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1734", "text": "<PERSON>, English painter and politician (b. 1675)", "html": "1734 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and politician (b. 1675)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and politician (b. 1675)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON><PERSON><PERSON>, English journalist and politician (b. 1686)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and politician (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English journalist and politician (b. 1686)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>l"}]}, {"year": "1774", "text": "<PERSON> of Brunswick, Prussian nobleman (b. 1714)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_of_Brunswick\" title=\"Duke <PERSON> of Brunswick\"><PERSON> of Brunswick</a>, Prussian nobleman (b. 1714)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_of_Brunswick\" title=\"Duke <PERSON> of Brunswick\"><PERSON> of Brunswick</a>, Prussian nobleman (b. 1714)", "links": [{"title": "Duke <PERSON> of Brunswick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Brunswick"}]}, {"year": "1776", "text": "<PERSON>, French painter and sculptor (b. 1717)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1717)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter and sculptor (b. 1717)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1790", "text": "<PERSON>, American politician (b. 1718)", "html": "1790 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON><PERSON><PERSON>, ruler of Mysore (b. 1750)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/Tipu_Sultan\" title=\"Tipu Sultan\"><PERSON><PERSON><PERSON></a>, ruler of Mysore (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tipu_Sultan\" title=\"Tipu Sultan\"><PERSON><PERSON><PERSON></a>, ruler of Mysore (b. 1750)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tipu_<PERSON>"}]}, {"year": "1811", "text": "<PERSON><PERSON>, Russian general (b. 1776)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian general (b. 1776)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, American lawyer and politician, 4th United States Secretary of War, 3rd United States Secretary of the Treasury (b. 1761)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a>, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1761)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 4th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_War\" title=\"United States Secretary of War\">United States Secretary of War</a>, 3rd <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a> (b. 1761)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of War", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_War"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1824", "text": "<PERSON>, French author (b. 1754)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON><PERSON>, colonial governor of East Florida, Santo Domingo and Cuba (b. 1757)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Kindel%C3%A1n_y_O%27Regan\" title=\"<PERSON><PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON><PERSON> y <PERSON></a>, colonial governor of <a href=\"https://wikipedia.org/wiki/East_Florida\" title=\"East Florida\">East Florida</a>, <a href=\"https://wikipedia.org/wiki/Captaincy_General_of_Santo_Domingo\" title=\"Captaincy General of Santo Domingo\"><PERSON></a> and Cuba (b. 1757)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sebasti%C3%A1n_Kindel%C3%A1n_y_O%27Regan\" title=\"<PERSON><PERSON><PERSON><PERSON> y <PERSON>\"><PERSON><PERSON><PERSON><PERSON> y <PERSON></a>, colonial governor of <a href=\"https://wikipedia.org/wiki/East_Florida\" title=\"East Florida\">East Florida</a>, <a href=\"https://wikipedia.org/wiki/Captaincy_General_of_Santo_Domingo\" title=\"Captaincy General of Santo Domingo\"><PERSON></a> and Cuba (b. 1757)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/Sebasti%C3%A1n_Kindel%C3%A1n_y_O%27Regan"}, {"title": "East Florida", "link": "https://wikipedia.org/wiki/East_Florida"}, {"title": "Captaincy General of Santo Domingo", "link": "https://wikipedia.org/wiki/Captaincy_General_of_Santo_Domingo"}]}, {"year": "1839", "text": "<PERSON>, Russian general and poet (b. 1784)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and poet (b. 1784)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and poet (b. 1784)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French mathematician and philosopher (b. 1771)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (b. 1771)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American lawyer and politician, 8th Governor of Texas (b. 1815)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Texas\" title=\"Governor of Texas\">Governor of Texas</a> (b. 1815)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_(governor)"}, {"title": "Governor of Texas", "link": "https://wikipedia.org/wiki/Governor_of_Texas"}]}, {"year": "1901", "text": "<PERSON>, Canadian lawyer and politician, 7th Premier of Quebec (b. 1831)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Macedonian Bulgarian revolutionary I<PERSON><PERSON> (b. 1872)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian Bulgarian revolutionary <a href=\"https://wikipedia.org/wiki/IMRO\" class=\"mw-redirect\" title=\"IMRO\">IMR<PERSON></a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Macedonian Bulgarian revolutionary <a href=\"https://wikipedia.org/wiki/IMRO\" class=\"mw-redirect\" title=\"IMR<PERSON>\">IMR<PERSON></a> (b. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "IMRO", "link": "https://wikipedia.org/wiki/IMRO"}]}, {"year": "1912", "text": "<PERSON><PERSON>, American geneticist credited with discovering sex chromosomes (b. 1861)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist credited with discovering <a href=\"https://wikipedia.org/wiki/Sex_chromosomes\" class=\"mw-redirect\" title=\"Sex chromosomes\">sex chromosomes</a> (b. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American geneticist credited with discovering <a href=\"https://wikipedia.org/wiki/Sex_chromosomes\" class=\"mw-redirect\" title=\"Sex chromosomes\">sex chromosomes</a> (b. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sex chromosomes", "link": "https://wikipedia.org/wiki/Sex_chromosomes"}]}, {"year": "1916", "text": "<PERSON>, Irish rebel commander (Easter Rising) (b. 1891)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_revolutionary)\" title=\"<PERSON> (Irish revolutionary)\"><PERSON></a>, Irish rebel commander (<a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>) (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_revolutionary)\" title=\"<PERSON> (Irish revolutionary)\"><PERSON></a>, Irish rebel commander (<a href=\"https://wikipedia.org/wiki/Easter_Rising\" title=\"Easter Rising\">Easter Rising</a>) (b. 1891)", "links": [{"title": "<PERSON> (Irish revolutionary)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_revolutionary)"}, {"title": "Easter Rising", "link": "https://wikipedia.org/wiki/Easter_Rising"}]}, {"year": "1916", "text": "<PERSON>, Australian politician, 23rd Premier of Victoria (b. 1851)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1851)", "links": [{"title": "<PERSON> (Victorian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Victorian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1916", "text": "<PERSON>, Irish rebel (b. 1881)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Irish rebel and writer (b. 1887)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel and writer (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel and writer (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Slovak general and politician (b. 1880)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/Milan_Ra<PERSON><PERSON>_%C5%A0tef%C3%A1nik\" title=\"Milan Rast<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak general and politician (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Ra<PERSON>islav_%C5%A0tef%C3%A1nik\" title=\"Milan <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak general and politician (b. 1880)", "links": [{"title": "Milan Ra<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milan_<PERSON><PERSON><PERSON>_%C5%A0tef%C3%A1nik"}]}, {"year": "1922", "text": "<PERSON>, Estonian politician (b. 1888)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian politician (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American golfer and tennis player (b. 1877)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and tennis player (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, English author and poet (b. 1858)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/E._Nesbit\" title=\"E. Nesbit\"><PERSON><PERSON></a>, English author and poet (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._Nesbit\" title=\"E. Nesbit\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, English author and poet (b. 1858)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E._Nesbit"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Japanese founder of judo (b. 1860)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Jigor%C5%8D\" title=\"Kanō <PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese founder of <a href=\"https://wikipedia.org/wiki/Judo\" title=\"Judo\">judo</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Jigor%C5%8D\" title=\"Kanō Jigor<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese founder of <a href=\"https://wikipedia.org/wiki/Judo\" title=\"Judo\">judo</a> (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON>gor%C5%8D"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judo"}]}, {"year": "1938", "text": "<PERSON>, German journalist and activist, Nobel Prize laureate (b. 1889)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1941", "text": "<PERSON>, Australian rugby player and coach (b. 1880)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, German field marshal (b. 1880)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German field marshal (b. 1880)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, French rugby player (b. 1876)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Romanian pianist, composer, and conductor (b. 1881)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian pianist, composer, and conductor (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian pianist, composer, and conductor (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Estonian politician, 4th Estonian Minister of Foreign Affairs (b. 1883)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Estonian politician, 4th <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)\" title=\"Minister of Foreign Affairs (Estonia)\">Estonian Minister of Foreign Affairs</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Foreign Affairs (Estonia)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Estonia)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, English-Italian author and poet (b. 1892)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian author and poet (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-Italian author and poet (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, writer and theatrical producer (b. 1902)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1902%E2%80%931971)\" title=\"<PERSON> (1902-1971)\"><PERSON></a>, writer and theatrical producer (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1902%E2%80%931971)\" title=\"<PERSON> (1902-1971)\"><PERSON></a>, writer and theatrical producer (b. 1902)", "links": [{"title": "<PERSON> (1902-1971)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(1902%E2%80%931971)"}]}, {"year": "1972", "text": "<PERSON>, Dutch arachnologist (b. 1905)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Father_<PERSON><PERSON><PERSON><PERSON>\" title=\"Father <PERSON><PERSON><PERSON>\">Father <PERSON><PERSON></a>, Dutch arachnologist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Father_<PERSON><PERSON><PERSON><PERSON>\" title=\"Father <PERSON><PERSON><PERSON>\">Father <PERSON><PERSON><PERSON><PERSON></a>, Dutch arachnologist (b. 1905)", "links": [{"title": "Father <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1886)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1973", "text": "<PERSON>, American author and playwright (b. 1917)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jane_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor, singer, and screenwriter (b. 1897)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and screenwriter (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Australian public servant (b. 1886)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian public servant (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, Yugoslav field marshal and politician, 1st President of Yugoslavia (b. 1892)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Yugoslavia\" title=\"President of Yugoslavia\">President of Yugoslavia</a> (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav field marshal and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Yugoslavia\" title=\"President of Yugoslavia\">President of Yugoslavia</a> (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Yugoslavia", "link": "https://wikipedia.org/wiki/President_of_Yugoslavia"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Sri Lankan banker (b. 1913)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan banker (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan banker (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Italian conductor and composer (b. 1911)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor and composer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian conductor and composer (b. 1911)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English actress (b. 1931)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Turkish tailor and politician (b. 1938)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Fikri_S%C3%B6nmez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish tailor and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fikri_S%C3%B6nmez\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish tailor and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fikri_S%C3%B6nmez"}]}, {"year": "1985", "text": "<PERSON>, English-Canadian 10th General of The Salvation Army (b. 1907)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian 10th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian 10th <a href=\"https://wikipedia.org/wiki/General_of_The_Salvation_Army\" title=\"General of The Salvation Army\">General of The Salvation Army</a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of The Salvation Army", "link": "https://wikipedia.org/wiki/General_of_The_Salvation_Army"}]}, {"year": "1987", "text": "<PERSON>, American singer and harmonica player (b. 1942)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and harmonica player (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1930)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American historian of Spanish America (b. 1891)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of Spanish America (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian of Spanish America (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American guitarist (b. 1957)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Egyptian singer-songwriter and mandolin player (b. 1902)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian singer-songwriter and mandolin player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian singer-songwriter and mandolin player (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Scottish politician (b. 1927)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish politician (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Slovenian film director and screenwriter (b. 1919)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/France_%C5%A0tiglic\" title=\"France Štiglic\">France Štiglic</a>, Slovenian film director and screenwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_%C5%A0tiglic\" title=\"France Štiglic\"><PERSON> Štigli<PERSON></a>, Slovenian film director and screenwriter (b. 1919)", "links": [{"title": "France Štiglic", "link": "https://wikipedia.org/wiki/France_%C5%A0tiglic"}]}, {"year": "1995", "text": "<PERSON>, American baseball player (b. 1922)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Dutch physicist and academic (b. 1909)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch physicist and academic (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American model, wife of <PERSON> (b. 1956)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American model, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a> (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American model, wife of <a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a> (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "2004", "text": "<PERSON>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision  (b. 1965)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision (b. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian man, born male but reassigned female and raised as a girl after a botched circumcision (b. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American colonel and journalist (b. 1930)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and journalist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel and journalist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American chemist and founder of P<PERSON>les (b. 1918)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>ring<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and founder of <a href=\"https://wikipedia.org/wiki/<PERSON>ring<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>les"}]}, {"year": "2009", "text": "<PERSON>, American actor, director, and producer (b. 1933)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> De<PERSON>uise\"><PERSON></a>, American actor, director, and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON> De<PERSON>uise\"><PERSON></a>, American actor, director, and producer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, Northern Irish footballer (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American pianist, composer, and conductor (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist, composer, and conductor (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American television producer, founded Stewart Tele Enterprises (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_producer)\" title=\"<PERSON> (television producer)\"><PERSON></a>, American television producer, founded <a href=\"https://wikipedia.org/wiki/Stewart_Tele_Enterprises\" class=\"mw-redirect\" title=\"Stewart Tele Enterprises\">Stewart Tele Enterprises</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_producer)\" title=\"<PERSON> (television producer)\"><PERSON></a>, American television producer, founded <a href=\"https://wikipedia.org/wiki/Stewart_Tele_Enterprises\" class=\"mw-redirect\" title=\"Stewart Tele Enterprises\">Stewart Tele Enterprises</a> (b. 1920)", "links": [{"title": "<PERSON> (television producer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(television_producer)"}, {"title": "Stewart Tele Enterprises", "link": "https://wikipedia.org/wiki/Stewart_Tele_Enterprises"}]}, {"year": "2012", "text": "<PERSON>, American rapper and director (b. 1964)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and director (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rapper and director (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Nigerian footballer (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Nigerian footballer (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American physician and politician, 44th Governor of Indiana (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician, 44th <a href=\"https://wikipedia.org/wiki/Governor_of_Indiana\" title=\"Governor of Indiana\">Governor of Indiana</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Indiana", "link": "https://wikipedia.org/wiki/Governor_of_Indiana"}]}, {"year": "2013", "text": "<PERSON>, English-Belgian cytologist and biochemist, Nobel Prize laureate (b. 1917)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian cytologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Belgian cytologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2013", "text": "<PERSON>, Peruvian sociologist and politician (b. 1948)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian sociologist and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian sociologist and politician (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Chinese-American journalist and actor (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American journalist and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese-American journalist and actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English admiral and politician (b. 1914)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English admiral and politician (b. 1914)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON> Luz, Cuban guitarist and composer (b. 1922)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Portillo_de_la_Luz\" title=\"<PERSON> la Luz\"><PERSON></a>, Cuban guitarist and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Portillo_de_la_Luz\" title=\"<PERSON> la Luz\"><PERSON></a>, Cuban guitarist and composer (b. 1922)", "links": [{"title": "<PERSON> la Luz", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Portillo_de_la_Luz"}]}, {"year": "2014", "text": "<PERSON>, American author and illustrator (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Aye<PERSON>\"><PERSON></a>, American author and illustrator (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Ukrainian-Scottish tennis player (b. 1983)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Scottish tennis player (b. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-Scottish tennis player (b. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American scientist and engineer (b. 1923)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scientist and engineer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German physicist and author (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Helga_K%C3%B6nigsdorf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physicist and author (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hel<PERSON>_K%C3%B6nigsdorf\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physicist and author (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Helga_K%C3%B6nigsdorf"}]}, {"year": "2014", "text": "<PERSON>, Canadian-American ice hockey player (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Central African politician, Prime Minister of the Central African Republic (b. 1948)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Central African politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Central African politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic\" class=\"mw-redirect\" title=\"Prime Minister of the Central African Republic\">Prime Minister of the Central African Republic</a> (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}, {"title": "Prime Minister of the Central African Republic", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Central_African_Republic"}]}, {"year": "2015", "text": "<PERSON>, American screenwriter and author (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and author (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American actress (b. 1913)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, American football player (b. 1946)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>v_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American football player (b. 1946)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Marv_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Burundian politician (b. 1946)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian politician (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Burundian politician (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American football player and coach (b. 1930)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, American carpenter and activist (b. 1950)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and activist (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American carpenter and activist (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English model, songwriter (b. 1962)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, songwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English model, songwriter (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Irish singer, songwriter, guitarist and band leader (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, songwriter, guitarist and band leader (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer, songwriter, guitarist and band leader (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American painter (b. 1936)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}