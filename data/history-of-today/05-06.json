{"date": "May 6", "url": "https://wikipedia.org/wiki/May_6", "data": {"Events": [{"year": "1527", "text": "Spanish and German troops sack Rome; many scholars consider this the end of the Renaissance.", "html": "1527 - Spanish and German troops <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(1527)\" title=\"Sack of Rome (1527)\">sack Rome</a>; many scholars consider this the end of the <a href=\"https://wikipedia.org/wiki/Renaissance\" title=\"Renaissance\">Renaissance</a>.", "no_year_html": "Spanish and German troops <a href=\"https://wikipedia.org/wiki/Sack_of_Rome_(1527)\" title=\"Sack of Rome (1527)\">sack Rome</a>; many scholars consider this the end of the <a href=\"https://wikipedia.org/wiki/Renaissance\" title=\"Renaissance\">Renaissance</a>.", "links": [{"title": "Sack of Rome (1527)", "link": "https://wikipedia.org/wiki/Sack_of_Rome_(1527)"}, {"title": "Renaissance", "link": "https://wikipedia.org/wiki/Renaissance"}]}, {"year": "1536", "text": "The Siege of Cuzco commences, in which Incan forces attempt to retake the city of Cuzco from the Spanish.", "html": "1536 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Cuzco\" class=\"mw-redirect\" title=\"Siege of Cuzco\">Siege of Cuzco</a> commences, in which <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Incan</a> forces attempt to retake the city of <a href=\"https://wikipedia.org/wiki/Cuzco\" class=\"mw-redirect\" title=\"Cuzco\">Cuzco</a> from the Spanish.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Cuzco\" class=\"mw-redirect\" title=\"Siege of Cuzco\">Siege of Cuzco</a> commences, in which <a href=\"https://wikipedia.org/wiki/Inca_Empire\" title=\"Inca Empire\">Incan</a> forces attempt to retake the city of <a href=\"https://wikipedia.org/wiki/Cuzco\" class=\"mw-redirect\" title=\"Cuzco\">Cuzco</a> from the Spanish.", "links": [{"title": "Siege of Cuzco", "link": "https://wikipedia.org/wiki/Siege_of_Cuzco"}, {"title": "Inca Empire", "link": "https://wikipedia.org/wiki/Inca_Empire"}, {"title": "Cuzco", "link": "https://wikipedia.org/wiki/Cuzco"}]}, {"year": "1541", "text": "King <PERSON> orders English-language Bibles be placed in every church. In 1539 the Great Bible would be provided for this purpose.", "html": "1541 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII</a> orders <a href=\"https://wikipedia.org/wiki/Bible_translations_into_English\" title=\"Bible translations into English\">English-language Bibles</a> be placed in every church. In 1539 the <a href=\"https://wikipedia.org/wiki/Great_Bible\" title=\"Great Bible\">Great Bible</a> would be provided for this purpose.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> VIII</a> orders <a href=\"https://wikipedia.org/wiki/Bible_translations_into_English\" title=\"Bible translations into English\">English-language Bibles</a> be placed in every church. In 1539 the <a href=\"https://wikipedia.org/wiki/Great_Bible\" title=\"Great Bible\">Great Bible</a> would be provided for this purpose.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "Bible translations into English", "link": "https://wikipedia.org/wiki/Bible_translations_into_English"}, {"title": "Great Bible", "link": "https://wikipedia.org/wiki/Great_Bible"}]}, {"year": "1542", "text": "<PERSON> reaches Old Goa, the capital of Portuguese India at the time.", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches <a href=\"https://wikipedia.org/wiki/Old_Goa\" title=\"Old Goa\">Old Goa</a>, the capital of <a href=\"https://wikipedia.org/wiki/Portuguese_India\" title=\"Portuguese India\">Portuguese India</a> at the time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> reaches <a href=\"https://wikipedia.org/wiki/Old_Goa\" title=\"Old Goa\">Old Goa</a>, the capital of <a href=\"https://wikipedia.org/wiki/Portuguese_India\" title=\"Portuguese India\">Portuguese India</a> at the time.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Old Goa", "link": "https://wikipedia.org/wiki/Old_Goa"}, {"title": "Portuguese India", "link": "https://wikipedia.org/wiki/Portuguese_India"}]}, {"year": "1594", "text": "The Dutch city of Coevorden held by the Spanish, falls to a Dutch and English force.", "html": "1594 - The Dutch city of <a href=\"https://wikipedia.org/wiki/Coevorden\" title=\"Coevorden\">Coevorden</a> held by the Spanish, <a href=\"https://wikipedia.org/wiki/Siege_of_Coevorden_(1593)\" title=\"Siege of Coevorden (1593)\">falls to a Dutch and English force</a>.", "no_year_html": "The Dutch city of <a href=\"https://wikipedia.org/wiki/Coevorden\" title=\"Coevorden\">Coevorden</a> held by the Spanish, <a href=\"https://wikipedia.org/wiki/Siege_of_Coevorden_(1593)\" title=\"Siege of Coevorden (1593)\">falls to a Dutch and English force</a>.", "links": [{"title": "Coevorden", "link": "https://wikipedia.org/wiki/Coevorden"}, {"title": "Siege of Coevorden (1593)", "link": "https://wikipedia.org/wiki/Siege_of_Coevorden_(1593)"}]}, {"year": "1659", "text": "English Restoration: A faction of the British Army removes <PERSON> as Lord Protector of the Commonwealth and reinstalls the Rump Parliament.", "html": "1659 - <a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: A <a href=\"https://wikipedia.org/wiki/Wallingford_House_party\" title=\"Wallingford House party\">faction</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> removes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of the Commonwealth and reinstalls the <a href=\"https://wikipedia.org/wiki/Rump_Parliament\" title=\"Rump Parliament\">Rump Parliament</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Restoration_(England)\" class=\"mw-redirect\" title=\"Restoration (England)\">English Restoration</a>: A <a href=\"https://wikipedia.org/wiki/Wallingford_House_party\" title=\"Wallingford House party\">faction</a> of the <a href=\"https://wikipedia.org/wiki/British_Army\" title=\"British Army\">British Army</a> removes <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Lord_Protector\" title=\"Lord Protector\">Lord Protector</a> of the Commonwealth and reinstalls the <a href=\"https://wikipedia.org/wiki/Rump_Parliament\" title=\"Rump Parliament\">Rump Parliament</a>.", "links": [{"title": "Restoration (England)", "link": "https://wikipedia.org/wiki/Restoration_(England)"}, {"title": "Wallingford House party", "link": "https://wikipedia.org/wiki/Wallingford_House_party"}, {"title": "British Army", "link": "https://wikipedia.org/wiki/British_Army"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Pro<PERSON>tor", "link": "https://wikipedia.org/wiki/<PERSON>_Protector"}, {"title": "Rump Parliament", "link": "https://wikipedia.org/wiki/Rump_Parliament"}]}, {"year": "1682", "text": "<PERSON> of <PERSON> moves his court to the Palace of Versailles.", "html": "1682 - <a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a> moves his court to the <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XIV_of_France\" class=\"mw-redirect\" title=\"Louis XIV of France\"><PERSON> of France</a> moves his court to the <a href=\"https://wikipedia.org/wiki/Palace_of_Versailles\" title=\"Palace of Versailles\">Palace of Versailles</a>.", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XIV_of_France"}, {"title": "Palace of Versailles", "link": "https://wikipedia.org/wiki/Palace_of_Versailles"}]}, {"year": "1757", "text": "Battle of Prague: A Prussian army fights an Austrian army in Prague during the Seven Years' War.", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Battle_of_Prague_(1757)\" title=\"Battle of Prague (1757)\">Battle of Prague</a>: A <a href=\"https://wikipedia.org/wiki/Prussian_army\" class=\"mw-redirect\" title=\"Prussian army\">Prussian army</a> fights an Austrian army in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Prague_(1757)\" title=\"Battle of Prague (1757)\">Battle of Prague</a>: A <a href=\"https://wikipedia.org/wiki/Prussian_army\" class=\"mw-redirect\" title=\"Prussian army\">Prussian army</a> fights an Austrian army in <a href=\"https://wikipedia.org/wiki/Prague\" title=\"Prague\">Prague</a> during the <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>.", "links": [{"title": "Battle of Prague (1757)", "link": "https://wikipedia.org/wiki/Battle_of_Prague_(1757)"}, {"title": "Prussian army", "link": "https://wikipedia.org/wiki/Prussian_army"}, {"title": "Prague", "link": "https://wikipedia.org/wiki/Prague"}, {"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}]}, {"year": "1757", "text": "The end of Konbaung-Hanthawaddy War, and the end of Burmese Civil War (1740-1757).", "html": "1757 - The end of <a href=\"https://wikipedia.org/wiki/Konbaung%E2%80%93Hanthawaddy_War\" title=\"Konbaung-Hanthawaddy War\">Konbaung-Hanthawaddy War</a>, and the end of Burmese Civil War (1740-1757).", "no_year_html": "The end of <a href=\"https://wikipedia.org/wiki/Konbaung%E2%80%93Hanthawaddy_War\" title=\"Konbaung-Hanthawaddy War\">Konbaung-Hanthawaddy War</a>, and the end of Burmese Civil War (1740-1757).", "links": [{"title": "Konbaung-Hanthawaddy War", "link": "https://wikipedia.org/wiki/Konbaung%E2%80%93Hanthawaddy_War"}]}, {"year": "1757", "text": "English poet <PERSON> is admitted into St Luke's Hospital for Lunatics in London, beginning his six-year confinement to mental asylums.", "html": "1757 - English poet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is admitted into <a href=\"https://wikipedia.org/wiki/St_Luke%27s_Hospital_for_Lunatics\" title=\"St Luke's Hospital for Lunatics\">St Luke's Hospital for Lunatics</a> in London, beginning <a href=\"https://wikipedia.org/wiki/Asylum_confinement_of_<PERSON>_<PERSON>\" title=\"Asylum confinement of <PERSON>\">his six-year confinement</a> to <a href=\"https://wikipedia.org/wiki/Psychiatric_hospital\" title=\"Psychiatric hospital\">mental asylums</a>.", "no_year_html": "English poet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is admitted into <a href=\"https://wikipedia.org/wiki/St_Luke%27s_Hospital_for_Lunatics\" title=\"St Luke's Hospital for Lunatics\">St Luke's Hospital for Lunatics</a> in London, beginning <a href=\"https://wikipedia.org/wiki/Asylum_confinement_of_<PERSON>_<PERSON>\" title=\"Asylum confinement of <PERSON>\">his six-year confinement</a> to <a href=\"https://wikipedia.org/wiki/Psychiatric_hospital\" title=\"Psychiatric hospital\">mental asylums</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "St Luke's Hospital for Lunatics", "link": "https://wikipedia.org/wiki/St_Luke%27s_Hospital_for_Lunatics"}, {"title": "Asylum confinement of <PERSON>", "link": "https://wikipedia.org/wiki/Asylum_confinement_of_<PERSON>_<PERSON>"}, {"title": "Psychiatric hospital", "link": "https://wikipedia.org/wiki/Psychiatric_hospital"}]}, {"year": "1782", "text": "Construction begins on the Grand Palace, the royal residence of the King of Siam in Bangkok, at the command of King <PERSON>.", "html": "1782 - Construction begins on the <a href=\"https://wikipedia.org/wiki/Grand_Palace\" title=\"Grand Palace\">Grand Palace</a>, the royal residence of the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Thailand\" title=\"Monarchy of Thailand\">King of Siam</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, at the command of King <a href=\"https://wikipedia.org/wiki/Buddha_Yodfa_Chulaloke\" class=\"mw-redirect\" title=\"Buddha Yod<PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON></a>.", "no_year_html": "Construction begins on the <a href=\"https://wikipedia.org/wiki/Grand_Palace\" title=\"Grand Palace\">Grand Palace</a>, the royal residence of the <a href=\"https://wikipedia.org/wiki/Monarchy_of_Thailand\" title=\"Monarchy of Thailand\">King of Siam</a> in <a href=\"https://wikipedia.org/wiki/Bangkok\" title=\"Bangkok\">Bangkok</a>, at the command of King <a href=\"https://wikipedia.org/wiki/Buddha_Yod<PERSON>_Chulaloke\" class=\"mw-redirect\" title=\"Buddha Yod<PERSON> Chulaloke\">Buddha <PERSON><PERSON><PERSON>lal<PERSON></a>.", "links": [{"title": "Grand Palace", "link": "https://wikipedia.org/wiki/Grand_Palace"}, {"title": "Monarchy of Thailand", "link": "https://wikipedia.org/wiki/Monarchy_of_Thailand"}, {"title": "Bangkok", "link": "https://wikipedia.org/wiki/Bangkok"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Chulaloke"}]}, {"year": "1801", "text": "Captain <PERSON> in the 14-gun HMS Speedy captures the 32-gun Spanish frigate El Gamo.", "html": "1801 - Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON></a> in the 14-gun <a href=\"https://wikipedia.org/wiki/HMS_Speedy_(1782)\" title=\"HMS Speedy (1782)\">HMS <i>Speedy</i></a> <a href=\"https://wikipedia.org/wiki/Action_of_6_May_1801\" title=\"Action of 6 May 1801\">captures</a> the 32-gun <a href=\"https://wikipedia.org/wiki/Spanish_frigate_El_Gamo\" title=\"Spanish frigate El Gamo\">Spanish frigate <i>El Gamo</i></a>.", "no_year_html": "Captain <a href=\"https://wikipedia.org/wiki/<PERSON>,_10th_Earl_of_Dundonald\" title=\"<PERSON>, 10th Earl of Dundonald\"><PERSON></a> in the 14-gun <a href=\"https://wikipedia.org/wiki/HMS_Speedy_(1782)\" title=\"HMS Speedy (1782)\">HMS <i>Speedy</i></a> <a href=\"https://wikipedia.org/wiki/Action_of_6_May_1801\" title=\"Action of 6 May 1801\">captures</a> the 32-gun <a href=\"https://wikipedia.org/wiki/Spanish_frigate_El_Gamo\" title=\"Spanish frigate El Gamo\">Spanish frigate <i>El Gamo</i></a>.", "links": [{"title": "<PERSON>, 10th Earl of Dundonald", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_10th_Earl_of_Dundonald"}, {"title": "HMS Speedy (1782)", "link": "https://wikipedia.org/wiki/HMS_Speedy_(1782)"}, {"title": "Action of 6 May 1801", "link": "https://wikipedia.org/wiki/Action_of_6_May_1801"}, {"title": "Spanish frigate El Gamo", "link": "https://wikipedia.org/wiki/Spanish_frigate_El_Gamo"}]}, {"year": "1835", "text": "<PERSON>, Sr. publishes the first issue of the New York Herald.", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a> publishes the first issue of the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Sr.\" class=\"mw-redirect\" title=\"<PERSON>, Sr.\"><PERSON>, Sr.</a> publishes the first issue of the <i><a href=\"https://wikipedia.org/wiki/New_York_Herald\" title=\"New York Herald\">New York Herald</a></i>.", "links": [{"title": "<PERSON>, Sr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Sr."}, {"title": "New York Herald", "link": "https://wikipedia.org/wiki/New_York_Herald"}]}, {"year": "1840", "text": "The Penny Black postage stamp becomes valid for use in the United Kingdom of Great Britain and Ireland.", "html": "1840 - The <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Postage_stamp\" title=\"Postage stamp\">postage stamp</a> becomes valid for use in the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON>_Black\" title=\"<PERSON> Black\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Postage_stamp\" title=\"Postage stamp\">postage stamp</a> becomes valid for use in the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">United Kingdom of Great Britain and Ireland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Postage stamp", "link": "https://wikipedia.org/wiki/Postage_stamp"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1857", "text": "The East India Company disbands the 34th Regiment of Bengal Native Infantry whose sepoy Mangal Pandey had earlier revolted against the British in the lead up to the War of Indian Independence.", "html": "1857 - The <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> disbands the 34th Regiment of <a href=\"https://wikipedia.org/wiki/Bengal_Native_Infantry\" title=\"Bengal Native Infantry\">Bengal Native Infantry</a> whose <a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">sepoy</a> <a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\">Mangal Pandey</a> had earlier revolted against the British in the lead up to the <a href=\"https://wikipedia.org/wiki/War_of_Independence_of_1857\" class=\"mw-redirect\" title=\"War of Independence of 1857\">War of Indian Independence</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/East_India_Company\" title=\"East India Company\">East India Company</a> disbands the 34th Regiment of <a href=\"https://wikipedia.org/wiki/Bengal_Native_Infantry\" title=\"Bengal Native Infantry\">Bengal Native Infantry</a> whose <a href=\"https://wikipedia.org/wiki/Sepoy\" title=\"Sepoy\">sepoy</a> <a href=\"https://wikipedia.org/wiki/Mangal_Pandey\" title=\"Mangal Pandey\">Mangal Pandey</a> had earlier revolted against the British in the lead up to the <a href=\"https://wikipedia.org/wiki/War_of_Independence_of_1857\" class=\"mw-redirect\" title=\"War of Independence of 1857\">War of Indian Independence</a>.", "links": [{"title": "East India Company", "link": "https://wikipedia.org/wiki/East_India_Company"}, {"title": "Bengal Native Infantry", "link": "https://wikipedia.org/wiki/Bengal_Native_Infantry"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>oy"}, {"title": "Mangal <PERSON>", "link": "https://wikipedia.org/wiki/Mangal_Pandey"}, {"title": "War of Independence of 1857", "link": "https://wikipedia.org/wiki/War_of_Independence_of_1857"}]}, {"year": "1861", "text": "American Civil War: Arkansas secedes from the Union.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Arkansas_in_the_American_Civil_War\" title=\"Arkansas in the American Civil War\">Arkansas</a> secedes from the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Arkansas_in_the_American_Civil_War\" title=\"Arkansas in the American Civil War\">Arkansas</a> secedes from the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Arkansas in the American Civil War", "link": "https://wikipedia.org/wiki/Arkansas_in_the_American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1863", "text": "American Civil War: The Battle of Chancellorsville ends with a major defeat of the Union's Army of the Potomac under <PERSON> by the Confederate Army of Northern Virginia under <PERSON>.", "html": "1863 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a> ends with a major defeat of the Union's <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a> ends with a major defeat of the Union's <a href=\"https://wikipedia.org/wiki/Army_of_the_Potomac\" title=\"Army of the Potomac\">Army of the Potomac</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> <a href=\"https://wikipedia.org/wiki/Army_of_Northern_Virginia\" title=\"Army of Northern Virginia\">Army of Northern Virginia</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Robert <PERSON>\"><PERSON></a>.", "links": [{"title": "Battle of Chancellorsville", "link": "https://wikipedia.org/wiki/Battle_of_Chancellorsville"}, {"title": "Army of the Potomac", "link": "https://wikipedia.org/wiki/Army_of_the_Potomac"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Army of Northern Virginia", "link": "https://wikipedia.org/wiki/Army_of_Northern_Virginia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1877", "text": "Chief <PERSON> of the Oglala Lakota surrenders to United States troops in Nebraska.", "html": "1877 - Chief <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> of the <a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala Lakota</a> surrenders to United States troops in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "no_year_html": "Chief <a href=\"https://wikipedia.org/wiki/Crazy_Horse\" title=\"Crazy Horse\">Crazy Horse</a> of the <a href=\"https://wikipedia.org/wiki/Oglala_Lakota\" class=\"mw-redirect\" title=\"Oglala Lakota\">Oglala Lakota</a> surrenders to United States troops in <a href=\"https://wikipedia.org/wiki/Nebraska\" title=\"Nebraska\">Nebraska</a>.", "links": [{"title": "Crazy Horse", "link": "https://wikipedia.org/wiki/Crazy_Horse"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oglala_Lakota"}, {"title": "Nebraska", "link": "https://wikipedia.org/wiki/Nebraska"}]}, {"year": "1882", "text": "<PERSON> and Lord <PERSON> are stabbed to death by Fenian assassins in Phoenix Park, Dublin.", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(civil_servant)\" title=\"<PERSON> (civil servant)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a> are stabbed to death by <a href=\"https://wikipedia.org/wiki/Fenian\" title=\"Fenian\"><PERSON><PERSON></a> assassins in <a href=\"https://wikipedia.org/wiki/Phoenix_Park_Murders\" title=\"Phoenix Park Murders\">Phoenix Park, Dublin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(civil_servant)\" title=\"<PERSON> (civil servant)\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a> are stabbed to death by <a href=\"https://wikipedia.org/wiki/Fenian\" title=\"<PERSON>ian\"><PERSON><PERSON></a> assassins in <a href=\"https://wikipedia.org/wiki/Phoenix_Park_Murders\" title=\"Phoenix Park Murders\">Phoenix Park, Dublin</a>.", "links": [{"title": "<PERSON> (civil servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(civil_servant)"}, {"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fenian"}, {"title": "Phoenix Park Murders", "link": "https://wikipedia.org/wiki/Phoenix_Park_Murders"}]}, {"year": "1882", "text": "The United States Congress passes the Chinese Exclusion Act.", "html": "1882 - The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Chinese_Exclusion_Act\" title=\"Chinese Exclusion Act\">Chinese Exclusion Act</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">United States Congress</a> passes the <a href=\"https://wikipedia.org/wiki/Chinese_Exclusion_Act\" title=\"Chinese Exclusion Act\">Chinese Exclusion Act</a>.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}, {"title": "Chinese Exclusion Act", "link": "https://wikipedia.org/wiki/Chinese_Exclusion_Act"}]}, {"year": "1889", "text": "The Eiffel Tower is officially opened to the public at the Universal Exposition in Paris.", "html": "1889 - The <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> is officially opened to the public at the <a href=\"https://wikipedia.org/wiki/Exposition_Universelle_(1889)\" title=\"Exposition Universelle (1889)\">Universal Exposition</a> in Paris.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a> is officially opened to the public at the <a href=\"https://wikipedia.org/wiki/Exposition_Universelle_(1889)\" title=\"Exposition Universelle (1889)\">Universal Exposition</a> in Paris.", "links": [{"title": "Eiffel Tower", "link": "https://wikipedia.org/wiki/Eiffel_Tower"}, {"title": "Exposition Universelle (1889)", "link": "https://wikipedia.org/wiki/Exposition_Universelle_(1889)"}]}, {"year": "1901", "text": "The first issue of Gorkhapatra, the oldest still running state-owned Nepali newspaper was published.", "html": "1901 - The first issue of <i><a href=\"https://wikipedia.org/wiki/Gorkhapatra\" title=\"Gorkhapatra\">Gorkhapatra</a>,</i> the oldest still running state-owned <a href=\"https://wikipedia.org/wiki/Nepali_language\" title=\"Nepali language\">Nepali</a> newspaper was published.", "no_year_html": "The first issue of <i><a href=\"https://wikipedia.org/wiki/Gorkhapatra\" title=\"Gorkhapatra\">Gorkhapatra</a>,</i> the oldest still running state-owned <a href=\"https://wikipedia.org/wiki/Nepali_language\" title=\"Nepali language\">Nepali</a> newspaper was published.", "links": [{"title": "Gorkhapatra", "link": "https://wikipedia.org/wiki/Gorkhapatra"}, {"title": "Nepali language", "link": "https://wikipedia.org/wiki/Nepali_language"}]}, {"year": "1906", "text": "The Russian Constitution of 1906 is adopted (on April 23 by the Julian calendar).", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/Russian_Constitution_of_1906\" title=\"Russian Constitution of 1906\">Russian Constitution of 1906</a> is adopted (on April 23 by the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Russian_Constitution_of_1906\" title=\"Russian Constitution of 1906\">Russian Constitution of 1906</a> is adopted (on April 23 by the <a href=\"https://wikipedia.org/wiki/Julian_calendar\" title=\"Julian calendar\">Julian calendar</a>).", "links": [{"title": "Russian Constitution of 1906", "link": "https://wikipedia.org/wiki/Russian_Constitution_of_1906"}, {"title": "Julian calendar", "link": "https://wikipedia.org/wiki/Julian_calendar"}]}, {"year": "1910", "text": "<PERSON> becomes King of Great Britain, Ireland, and many overseas territories, on the death of his father, <PERSON>.", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON></a> becomes King of Great Britain, Ireland, and many overseas territories, on the death of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> VII\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"George V\"><PERSON></a> becomes King of Great Britain, Ireland, and many overseas territories, on the death of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_VII\" title=\"Edward VII\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, then a pitcher for the Boston Red Sox, hits his first major league home run.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"Babe Ruth\"><PERSON></a>, then a pitcher for the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a>, hits his first major league home run.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ruth\" title=\"Babe Ruth\"><PERSON></a>, then a pitcher for the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a>, hits his first major league home run.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Boston Red Sox", "link": "https://wikipedia.org/wiki/Boston_Red_Sox"}]}, {"year": "1915", "text": "Imperial Trans-Antarctic Expedition: The SY Aurora broke loose from its anchorage during a gale, beginning a 312-day ordeal.", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a>: The <a href=\"https://wikipedia.org/wiki/SY_Aurora\" title=\"SY Aurora\">SY <i>Aurora</i></a> <a href=\"https://wikipedia.org/wiki/SY_Aurora%27s_drift\" title=\"SY Aurora's drift\">broke loose from its anchorage during a gale</a>, beginning a 312-day ordeal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition\" title=\"Imperial Trans-Antarctic Expedition\">Imperial Trans-Antarctic Expedition</a>: The <a href=\"https://wikipedia.org/wiki/SY_Aurora\" title=\"SY Aurora\">SY <i>Aurora</i></a> <a href=\"https://wikipedia.org/wiki/SY_Aurora%27s_drift\" title=\"SY Aurora's drift\">broke loose from its anchorage during a gale</a>, beginning a 312-day ordeal.", "links": [{"title": "Imperial Trans-Antarctic Expedition", "link": "https://wikipedia.org/wiki/Imperial_Trans-Antarctic_Expedition"}, {"title": "SY Aurora", "link": "https://wikipedia.org/wiki/SY_Aurora"}, {"title": "SY Aurora's drift", "link": "https://wikipedia.org/wiki/SY_Aurora%27s_drift"}]}, {"year": "1916", "text": "Twenty-one Lebanese nationalists are executed in Martyrs' Square, Beirut by <PERSON><PERSON><PERSON>.", "html": "1916 - Twenty-one Lebanese nationalists are executed in <a href=\"https://wikipedia.org/wiki/Martyrs%27_Square,_Beirut\" title=\"Martyrs' Square, Beirut\">Martyrs' Square, Beirut</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Twenty-one Lebanese nationalists are executed in <a href=\"https://wikipedia.org/wiki/Martyrs%27_Square,_Beirut\" title=\"Martyrs' Square, Beirut\">Martyrs' Square, Beirut</a> by <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Martyrs' Square, Beirut", "link": "https://wikipedia.org/wiki/Martyrs%27_Square,_Beirut"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "Vietnamese Emperor <PERSON><PERSON> is captured while calling upon the people to rise up against the French, and is later deposed and exiled to Réunion island.", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> Emperor <a href=\"https://wikipedia.org/wiki/Duy_T%C3%A2n\" title=\"<PERSON><PERSON>ân\"><PERSON><PERSON></a> is captured while calling upon the people to rise up against the French, and is later deposed and exiled to <a href=\"https://wikipedia.org/wiki/R%C3%A9union\" title=\"Réunion\">Réunion</a> island.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnamese</a> Emperor <a href=\"https://wikipedia.org/wiki/Duy_T%C3%A2n\" title=\"<PERSON><PERSON>ân\"><PERSON><PERSON></a> is captured while calling upon the people to rise up against the French, and is later deposed and exiled to <a href=\"https://wikipedia.org/wiki/R%C3%A9union\" title=\"Réunion\">Réunion</a> island.", "links": [{"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Duy_T%C3%A2n"}, {"title": "Réunion", "link": "https://wikipedia.org/wiki/R%C3%A9union"}]}, {"year": "1933", "text": "The Deutsche Studentenschaft attacked Magnus <PERSON>'s Institut für Sexualwissenschaft, later burning many of its books.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/Deutsche_Studentenschaft\" class=\"mw-redirect\" title=\"Deutsche Studentenschaft\">Deutsche Studentenschaft</a> attacked <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Institut_f%C3%BCr_Sexualwissenschaft\" title=\"Institut für Sexualwissenschaft\">Institut für Sexualwissenschaft</a>, later burning many of its books.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Deutsche_Studentenschaft\" class=\"mw-redirect\" title=\"Deutsche Studentenschaft\">Deutsche Studentenschaft</a> attacked <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Institut_f%C3%BCr_Sexualwissenschaft\" title=\"Institut für Sexualwissenschaft\">Institut für Sexualwissenschaft</a>, later burning many of its books.", "links": [{"title": "Deutsche Studentenschaft", "link": "https://wikipedia.org/wiki/Deutsche_Studentenschaft"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Institut für Sexualwissenschaft", "link": "https://wikipedia.org/wiki/Institut_f%C3%BCr_Sexualwissenschaft"}]}, {"year": "1935", "text": "New Deal: Under the authority of the newly-enacted Federal Emergency Relief Administration, President <PERSON> issues Executive Order 7034 to create the Works Progress Administration.", "html": "1935 - <a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: Under the authority of the newly-enacted <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration\" title=\"Federal Emergency Relief Administration\">Federal Emergency Relief Administration</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues Executive Order 7034 to create the <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/New_Deal\" title=\"New Deal\">New Deal</a>: Under the authority of the newly-enacted <a href=\"https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration\" title=\"Federal Emergency Relief Administration\">Federal Emergency Relief Administration</a>, President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> issues Executive Order 7034 to create the <a href=\"https://wikipedia.org/wiki/Works_Progress_Administration\" title=\"Works Progress Administration\">Works Progress Administration</a>.", "links": [{"title": "New Deal", "link": "https://wikipedia.org/wiki/New_Deal"}, {"title": "Federal Emergency Relief Administration", "link": "https://wikipedia.org/wiki/Federal_Emergency_Relief_Administration"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Works Progress Administration", "link": "https://wikipedia.org/wiki/Works_Progress_Administration"}]}, {"year": "1937", "text": "Hindenburg disaster: The German zeppelin Hindenburg catches fire and is destroyed within a minute while attempting to dock at Lakehurst, New Jersey. Thirty-six people are killed.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Hindenburg_disaster\" title=\"Hindenburg disaster\">Hindenburg disaster</a>: The <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Zeppelin\" title=\"Zeppelin\">zeppelin</a> <i>Hindenburg</i> catches fire and is destroyed within a minute while attempting to dock at <a href=\"https://wikipedia.org/wiki/Lakehurst,_New_Jersey\" title=\"Lakehurst, New Jersey\">Lakehurst, New Jersey</a>. Thirty-six people are killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hindenburg_disaster\" title=\"Hindenburg disaster\">Hindenburg disaster</a>: The <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">German</a> <a href=\"https://wikipedia.org/wiki/Zeppelin\" title=\"Zeppelin\">zeppelin</a> <i>Hindenburg</i> catches fire and is destroyed within a minute while attempting to dock at <a href=\"https://wikipedia.org/wiki/Lakehurst,_New_Jersey\" title=\"Lakehurst, New Jersey\">Lakehurst, New Jersey</a>. Thirty-six people are killed.", "links": [{"title": "Hindenburg disaster", "link": "https://wikipedia.org/wiki/Hindenburg_disaster"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Zeppelin", "link": "https://wikipedia.org/wiki/Zeppelin"}, {"title": "Lakehurst, New Jersey", "link": "https://wikipedia.org/wiki/Lakehurst,_New_Jersey"}]}, {"year": "1940", "text": "<PERSON> is awarded the Pulitzer Prize for his novel The Grapes of Wrath.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Pulitzer Prize</a> for his novel <i><a href=\"https://wikipedia.org/wiki/The_Grapes_of_Wrath\" title=\"The Grapes of Wrath\">The Grapes of Wrath</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is awarded the <a href=\"https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction\" title=\"Pulitzer Prize for Fiction\">Pulitzer Prize</a> for his novel <i><a href=\"https://wikipedia.org/wiki/The_Grapes_of_Wrath\" title=\"The Grapes of Wrath\">The Grapes of Wrath</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Pulitzer Prize for Fiction", "link": "https://wikipedia.org/wiki/Pulitzer_Prize_for_Fiction"}, {"title": "The Grapes of Wrath", "link": "https://wikipedia.org/wiki/The_Grapes_of_Wrath"}]}, {"year": "1941", "text": "At California's March Field, <PERSON> performs his first USO show.", "html": "1941 - At <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>'s <a href=\"https://wikipedia.org/wiki/March_Field\" class=\"mw-redirect\" title=\"March Field\">March Field</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs his first <a href=\"https://wikipedia.org/wiki/United_Service_Organizations\" title=\"United Service Organizations\">USO</a> show.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/California\" title=\"California\">California</a>'s <a href=\"https://wikipedia.org/wiki/March_Field\" class=\"mw-redirect\" title=\"March Field\">March Field</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> performs his first <a href=\"https://wikipedia.org/wiki/United_Service_Organizations\" title=\"United Service Organizations\">USO</a> show.", "links": [{"title": "California", "link": "https://wikipedia.org/wiki/California"}, {"title": "March Field", "link": "https://wikipedia.org/wiki/March_Field"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United Service Organizations", "link": "https://wikipedia.org/wiki/United_Service_Organizations"}]}, {"year": "1941", "text": "The first flight of the Republic P-47 Thunderbolt.", "html": "1941 - The first flight of the <a href=\"https://wikipedia.org/wiki/Republic_P-47_Thunderbolt\" title=\"Republic P-47 Thunderbolt\">Republic P-47 Thunderbolt</a>.", "no_year_html": "The first flight of the <a href=\"https://wikipedia.org/wiki/Republic_P-47_Thunderbolt\" title=\"Republic P-47 Thunderbolt\">Republic P-47 Thunderbolt</a>.", "links": [{"title": "Republic P-47 Thunderbolt", "link": "https://wikipedia.org/wiki/Republic_P-47_Thunderbolt"}]}, {"year": "1942", "text": "World War II: <PERSON> Corregidor, the last American forces in the Philippines surrender to the Japanese.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: On <a href=\"https://wikipedia.org/wiki/Corregidor\" title=\"Corregidor\">Corregidor</a>, the last American forces in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Corregidor\" title=\"Battle of Corregidor\">surrender</a> to the Japanese.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: On <a href=\"https://wikipedia.org/wiki/Corregidor\" title=\"Corregidor\">Corregidor</a>, the last American forces in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a> <a href=\"https://wikipedia.org/wiki/Battle_of_Corregidor\" title=\"Battle of Corregidor\">surrender</a> to the Japanese.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Corregidor", "link": "https://wikipedia.org/wiki/Corregidor"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Battle of Corregidor", "link": "https://wikipedia.org/wiki/Battle_of_Corregidor"}]}, {"year": "1945", "text": "World War II: Axis Sally delivers her last propaganda broadcast to Allied troops.", "html": "1945 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> Sally</a> delivers her last <a href=\"https://wikipedia.org/wiki/Propaganda\" title=\"Propaganda\">propaganda</a> broadcast to <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> troops.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a> delivers her last <a href=\"https://wikipedia.org/wiki/Propaganda\" title=\"Propaganda\">propaganda</a> broadcast to <a href=\"https://wikipedia.org/wiki/Allies_of_World_War_II\" title=\"Allies of World War II\">Allied</a> troops.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Propaganda", "link": "https://wikipedia.org/wiki/Propaganda"}, {"title": "Allies of World War II", "link": "https://wikipedia.org/wiki/Allies_of_World_War_II"}]}, {"year": "1945", "text": "World War II: The Prague Offensive, the last major battle of the Eastern Front, begins.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Prague_Offensive\" class=\"mw-redirect\" title=\"Prague Offensive\">Prague Offensive</a>, the last major battle of the <a href=\"https://wikipedia.org/wiki/Eastern_Front_(World_War_II)\" title=\"Eastern Front (World War II)\">Eastern Front</a>, begins.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Prague_Offensive\" class=\"mw-redirect\" title=\"Prague Offensive\">Prague Offensive</a>, the last major battle of the <a href=\"https://wikipedia.org/wiki/Eastern_Front_(World_War_II)\" title=\"Eastern Front (World War II)\">Eastern Front</a>, begins.", "links": [{"title": "Prague Offensive", "link": "https://wikipedia.org/wiki/Prague_Offensive"}, {"title": "Eastern Front (World War II)", "link": "https://wikipedia.org/wiki/Eastern_Front_(World_War_II)"}]}, {"year": "1949", "text": "EDSAC, the first practical electronic digital stored-program computer, runs its first operation.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Electronic_Delay_Storage_Automatic_Calculator\" class=\"mw-redirect\" title=\"Electronic Delay Storage Automatic Calculator\">EDSAC</a>, the first practical electronic digital <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_architecture\" title=\"Von Neumann architecture\">stored-program computer</a>, runs its first operation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Electronic_Delay_Storage_Automatic_Calculator\" class=\"mw-redirect\" title=\"Electronic Delay Storage Automatic Calculator\">EDSAC</a>, the first practical electronic digital <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_architecture\" title=\"Von Neumann architecture\">stored-program computer</a>, runs its first operation.", "links": [{"title": "Electronic Delay Storage Automatic Calculator", "link": "https://wikipedia.org/wiki/Electronic_Delay_Storage_Automatic_Calculator"}, {"title": "<PERSON> architecture", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_architecture"}]}, {"year": "1954", "text": "<PERSON> becomes the first person to run the mile in under four minutes.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to run the <a href=\"https://wikipedia.org/wiki/Middle_distance_track_event#Mile\" class=\"mw-redirect\" title=\"Middle distance track event\">mile</a> in <a href=\"https://wikipedia.org/wiki/Four-minute_mile\" title=\"Four-minute mile\">under four minutes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first person to run the <a href=\"https://wikipedia.org/wiki/Middle_distance_track_event#Mile\" class=\"mw-redirect\" title=\"Middle distance track event\">mile</a> in <a href=\"https://wikipedia.org/wiki/Four-minute_mile\" title=\"Four-minute mile\">under four minutes</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Middle distance track event", "link": "https://wikipedia.org/wiki/Middle_distance_track_event#Mile"}, {"title": "Four-minute mile", "link": "https://wikipedia.org/wiki/Four-minute_mile"}]}, {"year": "1960", "text": "More than 20 million viewers watch the first televised royal wedding when Princess <PERSON> marries <PERSON> at Westminster Abbey.", "html": "1960 - More than 20 million viewers watch the first televised <a href=\"https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON>_and_<PERSON>_<PERSON>\" title=\"Wedding of Princess <PERSON> and <PERSON>\">royal wedding</a> when <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Snowdon\" title=\"Princess <PERSON>, Countess of Snowdon\">Princess <PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_<PERSON>\" title=\"<PERSON>, 1st Earl of Snowdon\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "no_year_html": "More than 20 million viewers watch the first televised <a href=\"https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON>_and_<PERSON>\" title=\"Wedding of Princess <PERSON> and <PERSON>\">royal wedding</a> when <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Snowdon\" title=\"Princess <PERSON>, Countess of Snowdon\">Princess <PERSON></a> marries <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_<PERSON>\" title=\"<PERSON>, 1st Earl of Snowdon\"><PERSON></a> at <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>.", "links": [{"title": "Wedding of Princess <PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/Wedding_of_Princess_<PERSON>_and_<PERSON>_<PERSON>-<PERSON>"}, {"title": "Princess <PERSON>, Countess of Snowdon", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Snowdon"}, {"title": "<PERSON>, 1st Earl of Snowdon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Snowdon"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}]}, {"year": "1966", "text": "<PERSON> and <PERSON> are sentenced to life imprisonment for the Moors murders in England.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> are sentenced to <a href=\"https://wikipedia.org/wiki/Life_imprisonment_(England_and_Wales)\" class=\"mw-redirect\" title=\"Life imprisonment (England and Wales)\">life imprisonment</a> for the <a href=\"https://wikipedia.org/wiki/Moors_murders\" title=\"Moors murders\">Moors murders</a> in England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> are sentenced to <a href=\"https://wikipedia.org/wiki/Life_imprisonment_(England_and_Wales)\" class=\"mw-redirect\" title=\"Life imprisonment (England and Wales)\">life imprisonment</a> for the <a href=\"https://wikipedia.org/wiki/Moors_murders\" title=\"Moors murders\">Moors murders</a> in England.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Life imprisonment (England and Wales)", "link": "https://wikipedia.org/wiki/Life_imprisonment_(England_and_Wales)"}, {"title": "Moors murders", "link": "https://wikipedia.org/wiki/Moors_murders"}]}, {"year": "1972", "text": "<PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> are executed in Ankara after being convicted of attempting to overthrow the Constitutional order.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> are executed in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a> after being convicted of attempting to overthrow the Constitutional order.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%9F\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> are executed in <a href=\"https://wikipedia.org/wiki/Ankara\" title=\"Ankara\">Ankara</a> after being convicted of attempting to overthrow the Constitutional order.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deniz_Gezmi%C5%9F"}, {"title": "Ankara", "link": "https://wikipedia.org/wiki/Ankara"}]}, {"year": "1975", "text": "During a lull in fighting, 100,000 Armenians gather in Beirut for the 60th anniversary commemorations of the Armenian genocide.", "html": "1975 - During a lull in fighting, 100,000 Armenians gather in Beirut for the <a href=\"https://wikipedia.org/wiki/60th_anniversary_of_the_Armenian_Genocide_commemorations_in_Beirut\" class=\"mw-redirect\" title=\"60th anniversary of the Armenian Genocide commemorations in Beirut\">60th anniversary commemorations</a> of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "no_year_html": "During a lull in fighting, 100,000 Armenians gather in Beirut for the <a href=\"https://wikipedia.org/wiki/60th_anniversary_of_the_Armenian_Genocide_commemorations_in_Beirut\" class=\"mw-redirect\" title=\"60th anniversary of the Armenian Genocide commemorations in Beirut\">60th anniversary commemorations</a> of the <a href=\"https://wikipedia.org/wiki/Armenian_genocide\" title=\"Armenian genocide\">Armenian genocide</a>.", "links": [{"title": "60th anniversary of the Armenian Genocide commemorations in Beirut", "link": "https://wikipedia.org/wiki/60th_anniversary_of_the_Armenian_Genocide_commemorations_in_Beirut"}, {"title": "Armenian genocide", "link": "https://wikipedia.org/wiki/Armenian_genocide"}]}, {"year": "1976", "text": "The 6.5 Mw  Friuli earthquake affected Northern Italy with a maximum Mercalli intensity of X (Extreme), leaving 900-978 dead and 1,700-2,400 injured.", "html": "1976 - The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1976_Friuli_earthquake\" title=\"1976 Friuli earthquake\">Friuli earthquake</a> affected <a href=\"https://wikipedia.org/wiki/Northern_Italy\" title=\"Northern Italy\">Northern Italy</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), leaving 900-978 dead and 1,700-2,400 injured.", "no_year_html": "The 6.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1976_Friuli_earthquake\" title=\"1976 Friuli earthquake\">Friuli earthquake</a> affected <a href=\"https://wikipedia.org/wiki/Northern_Italy\" title=\"Northern Italy\">Northern Italy</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of X (<i>Extreme</i>), leaving 900-978 dead and 1,700-2,400 injured.", "links": [{"title": "1976 Friuli earthquake", "link": "https://wikipedia.org/wiki/1976_Friuli_earthquake"}, {"title": "Northern Italy", "link": "https://wikipedia.org/wiki/Northern_Italy"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1983", "text": "The Hitler Diaries are revealed as a hoax after being examined by new experts.", "html": "1983 - The <i><a href=\"https://wikipedia.org/wiki/Hitler_Diaries\" title=\"Hitler Diaries\">Hitler Diaries</a></i> are revealed as a hoax after being examined by new experts.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Hitler_Diaries\" title=\"Hitler Diaries\">Hitler Diaries</a></i> are revealed as a hoax after being examined by new experts.", "links": [{"title": "Hitler Diaries", "link": "https://wikipedia.org/wiki/Hitler_Diaries"}]}, {"year": "1984", "text": "One hundred and three Korean Martyrs are canonized by Pope <PERSON> in Seoul.", "html": "1984 - One hundred and three <a href=\"https://wikipedia.org/wiki/Korean_Martyrs\" title=\"Korean Martyrs\">Korean Martyrs</a> are <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope John <PERSON> II\">Pope <PERSON></a> in <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>.", "no_year_html": "One hundred and three <a href=\"https://wikipedia.org/wiki/Korean_Martyrs\" title=\"Korean Martyrs\">Korean Martyrs</a> are <a href=\"https://wikipedia.org/wiki/Canonization\" title=\"Canonization\">canonized</a> by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> in <a href=\"https://wikipedia.org/wiki/Seoul\" title=\"Seoul\">Seoul</a>.", "links": [{"title": "Korean Martyrs", "link": "https://wikipedia.org/wiki/Korean_Martyrs"}, {"title": "Canonization", "link": "https://wikipedia.org/wiki/Canonization"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Seoul", "link": "https://wikipedia.org/wiki/Seoul"}]}, {"year": "1988", "text": "All thirty-six passengers and crew were killed when Widerøe Flight 710 crashed into Mt. Torghatten in Brønnøy.", "html": "1988 - All thirty-six passengers and crew were killed when <a href=\"https://wikipedia.org/wiki/Wider%C3%B8e_Flight_710\" title=\"Widerøe Flight 710\">Widerøe Flight 710</a> crashed into <a href=\"https://wikipedia.org/wiki/Torghatten\" title=\"Torghatten\">Mt. Torghatten</a> in <a href=\"https://wikipedia.org/wiki/Br%C3%B8nn%C3%B8y_Municipality\" title=\"Brønnøy Municipality\">Brønnøy</a>.", "no_year_html": "All thirty-six passengers and crew were killed when <a href=\"https://wikipedia.org/wiki/Wider%C3%B8e_Flight_710\" title=\"Widerøe Flight 710\">Widerøe Flight 710</a> crashed into <a href=\"https://wikipedia.org/wiki/Torghatten\" title=\"Torghatten\">Mt. Torghatten</a> in <a href=\"https://wikipedia.org/wiki/Br%C3%B8nn%C3%B8y_Municipality\" title=\"Brønnøy Municipality\">Brønnøy</a>.", "links": [{"title": "Widerøe Flight 710", "link": "https://wikipedia.org/wiki/Wider%C3%B8e_Flight_710"}, {"title": "Torghatten", "link": "https://wikipedia.org/wiki/Torghatten"}, {"title": "Brønnøy Municipality", "link": "https://wikipedia.org/wiki/Br%C3%B8nn%C3%B8y_Municipality"}]}, {"year": "1994", "text": "<PERSON> of the United Kingdom and French President <PERSON> officiate at the opening of the Channel Tunnel.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom and French President <a href=\"https://wikipedia.org/wiki/Fran%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> officiate at the opening of the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II\" title=\"Elizabeth II\"><PERSON> II</a> of the United Kingdom and French President <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> officiate at the opening of the <a href=\"https://wikipedia.org/wiki/Channel_Tunnel\" title=\"Channel Tunnel\">Channel Tunnel</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_II"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Channel Tunnel", "link": "https://wikipedia.org/wiki/Channel_Tunnel"}]}, {"year": "1996", "text": "The body of former CIA director <PERSON> is found washed up on a riverbank in southern Maryland, eight days after he disappeared.", "html": "1996 - The body of former <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> director <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found washed up on a riverbank in southern <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, eight days after he disappeared.", "no_year_html": "The body of former <a href=\"https://wikipedia.org/wiki/Central_Intelligence_Agency\" title=\"Central Intelligence Agency\">CIA</a> director <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found washed up on a riverbank in southern <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a>, eight days after he disappeared.", "links": [{"title": "Central Intelligence Agency", "link": "https://wikipedia.org/wiki/Central_Intelligence_Agency"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}]}, {"year": "1997", "text": "The Bank of England is given independence from political control, the most significant change in the bank's 300-year history.", "html": "1997 - The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> is given independence from political control, the most significant change in the bank's 300-year history.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bank_of_England\" title=\"Bank of England\">Bank of England</a> is given independence from political control, the most significant change in the bank's 300-year history.", "links": [{"title": "Bank of England", "link": "https://wikipedia.org/wiki/Bank_of_England"}]}, {"year": "1998", "text": "<PERSON> strikes out 20 Houston Astros to tie the major league record held by <PERSON>. He threw a one-hitter and did not walk a batter in his fifth career start.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> strikes out 20 <a href=\"https://wikipedia.org/wiki/Houston_Astros\" title=\"Houston Astros\">Houston Astros</a> to tie the major league record held by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. He threw a one-hitter and did not walk a batter in his fifth career start.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> strikes out 20 <a href=\"https://wikipedia.org/wiki/Houston_Astros\" title=\"Houston Astros\">Houston Astros</a> to tie the major league record held by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>. He threw a one-hitter and did not walk a batter in his fifth career start.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Houston Astros", "link": "https://wikipedia.org/wiki/Houston_Astros"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON> of Apple Inc. unveils the first iMac.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jobs\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc</a>. unveils the first iMac.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Jobs\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/Apple_Inc.\" title=\"Apple Inc.\">Apple Inc</a>. unveils the first iMac.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apple Inc.", "link": "https://wikipedia.org/wiki/Apple_Inc."}]}, {"year": "1999", "text": "The first elections to the devolved Scottish Parliament and Welsh Assembly are held.", "html": "1999 - The first elections to the <a href=\"https://wikipedia.org/wiki/Devolution\" title=\"Devolution\">devolved</a> <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">Scottish Parliament</a> and <a href=\"https://wikipedia.org/wiki/Welsh_Assembly\" class=\"mw-redirect\" title=\"Welsh Assembly\">Welsh Assembly</a> are held.", "no_year_html": "The first elections to the <a href=\"https://wikipedia.org/wiki/Devolution\" title=\"Devolution\">devolved</a> <a href=\"https://wikipedia.org/wiki/Scottish_Parliament\" title=\"Scottish Parliament\">Scottish Parliament</a> and <a href=\"https://wikipedia.org/wiki/Welsh_Assembly\" class=\"mw-redirect\" title=\"Welsh Assembly\">Welsh Assembly</a> are held.", "links": [{"title": "Devolution", "link": "https://wikipedia.org/wiki/Devolution"}, {"title": "Scottish Parliament", "link": "https://wikipedia.org/wiki/Scottish_Parliament"}, {"title": "Welsh Assembly", "link": "https://wikipedia.org/wiki/Welsh_Assembly"}]}, {"year": "2001", "text": "During a trip to Syria, Pope <PERSON> becomes the first pope to enter a mosque.", "html": "2001 - During a trip to <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> becomes the first pope to enter a <a href=\"https://wikipedia.org/wiki/Mosque\" title=\"Mosque\">mosque</a>.", "no_year_html": "During a trip to <a href=\"https://wikipedia.org/wiki/Syria\" title=\"Syria\">Syria</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\">Pope <PERSON> II</a> becomes the first pope to enter a <a href=\"https://wikipedia.org/wiki/Mosque\" title=\"Mosque\">mosque</a>.", "links": [{"title": "Syria", "link": "https://wikipedia.org/wiki/Syria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Mosque", "link": "https://wikipedia.org/wiki/Mosque"}]}, {"year": "2002", "text": "Dutch politician <PERSON><PERSON> is assassinated following a radio-interview at the Mediapark in Hilversum.", "html": "2002 - Dutch politician <a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON> Fort<PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Pim_Fortuyn\" title=\"Assassination of Pim Fortuyn\">assassinated</a> following a radio-interview at the Mediapark in <a href=\"https://wikipedia.org/wiki/Hilversum\" title=\"Hilversum\">Hilversum</a>.", "no_year_html": "Dutch politician <a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON> Fort<PERSON></a> is <a href=\"https://wikipedia.org/wiki/Assassination_of_Pim_Fortuyn\" title=\"Assassination of Pim Fortuyn\">assassinated</a> following a radio-interview at the Mediapark in <a href=\"https://wikipedia.org/wiki/Hilversum\" title=\"Hilversum\">Hilversum</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pim_Fortuyn"}, {"title": "Assassination of Pim Fortuyn", "link": "https://wikipedia.org/wiki/Assassination_of_Pim_Fortuyn"}, {"title": "Hilversum", "link": "https://wikipedia.org/wiki/Hilversum"}]}, {"year": "2002", "text": "Founding of SpaceX.", "html": "2002 - Founding of <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a>.", "no_year_html": "Founding of <a href=\"https://wikipedia.org/wiki/SpaceX\" title=\"SpaceX\">SpaceX</a>.", "links": [{"title": "SpaceX", "link": "https://wikipedia.org/wiki/SpaceX"}]}, {"year": "2004", "text": "The final episode of the television sitcom Friends was aired.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/The_Last_One_(Friends)\" title=\"The Last One (Friends)\">final episode</a> of the television sitcom <a href=\"https://wikipedia.org/wiki/Friends\" title=\"Friends\">Friends</a> was aired.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/The_Last_One_(Friends)\" title=\"The Last One (Friends)\">final episode</a> of the television sitcom <a href=\"https://wikipedia.org/wiki/Friends\" title=\"Friends\">Friends</a> was aired.", "links": [{"title": "The Last One (Friends)", "link": "https://wikipedia.org/wiki/The_Last_One_(Friends)"}, {"title": "Friends", "link": "https://wikipedia.org/wiki/Friends"}]}, {"year": "2010", "text": "In just 36 minutes, the Dow-Jones average plunged nearly 1,000 points in what is known as the 2010 Flash Crash.", "html": "2010 - In just 36 minutes, the Dow-Jones average plunged nearly 1,000 points in what is known as the <a href=\"https://wikipedia.org/wiki/2010_flash_crash\" title=\"2010 flash crash\">2010 Flash Crash</a>.", "no_year_html": "In just 36 minutes, the Dow-Jones average plunged nearly 1,000 points in what is known as the <a href=\"https://wikipedia.org/wiki/2010_flash_crash\" title=\"2010 flash crash\">2010 Flash Crash</a>.", "links": [{"title": "2010 flash crash", "link": "https://wikipedia.org/wiki/2010_flash_crash"}]}, {"year": "2013", "text": "Three women, kidnapped and missing for more than a decade, are found alive in Cleveland, Ohio, in the United States.", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_kidnappings\" title=\"<PERSON> kidnappings\">Three women</a>, kidnapped and missing for more than a decade, are found alive in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_kidnappings\" title=\"<PERSON> kidnappings\">Three women</a>, kidnapped and missing for more than a decade, are found alive in <a href=\"https://wikipedia.org/wiki/Cleveland,_Ohio\" class=\"mw-redirect\" title=\"Cleveland, Ohio\">Cleveland, Ohio</a>, in the United States.", "links": [{"title": "<PERSON> kidnappings", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_kidnappings"}, {"title": "Cleveland, Ohio", "link": "https://wikipedia.org/wiki/Cleveland,_Ohio"}]}, {"year": "2023", "text": "The coronation of <PERSON> and <PERSON><PERSON> as King and Queen of the United Kingdom and the other Commonwealth realms is held in Westminster Abbey, London.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_III_and_Camilla\" title=\"Coronation of <PERSON> III and Camilla\">coronation of <PERSON> III and Cam<PERSON></a> as <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">King</a> and <a href=\"https://wikipedia.org/wiki/List_of_British_royal_consorts\" title=\"List of British royal consorts\">Queen</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and the other <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realms</a> is held in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Coronation_of_<PERSON>_III_and_Camilla\" title=\"Coronation of <PERSON> III and Camilla\">coronation of <PERSON> III and Camilla</a> as <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">King</a> and <a href=\"https://wikipedia.org/wiki/List_of_British_royal_consorts\" title=\"List of British royal consorts\">Queen</a> of the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a> and the other <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realms</a> is held in <a href=\"https://wikipedia.org/wiki/Westminster_Abbey\" title=\"Westminster Abbey\">Westminster Abbey</a>, <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>.", "links": [{"title": "Coronation of <PERSON> and Cam<PERSON>", "link": "https://wikipedia.org/wiki/Coronation_of_<PERSON>_<PERSON>_and_<PERSON><PERSON>"}, {"title": "Monarchy of the United Kingdom", "link": "https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom"}, {"title": "List of British royal consorts", "link": "https://wikipedia.org/wiki/List_of_British_royal_consorts"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}, {"title": "Commonwealth realm", "link": "https://wikipedia.org/wiki/Commonwealth_realm"}, {"title": "Westminster Abbey", "link": "https://wikipedia.org/wiki/Westminster_Abbey"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "2023", "text": "Eight people are killed and seven injured in a mass shooting in Allen, Texas. The perpetrator is killed by a police officer.", "html": "2023 - Eight people are killed and seven injured in <a href=\"https://wikipedia.org/wiki/Allen_Premium_Outlets_shooting\" class=\"mw-redirect\" title=\"Allen Premium Outlets shooting\">a mass shooting</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Texas\" title=\"Allen, Texas\">Allen, Texas</a>. The perpetrator is killed by a police officer.", "no_year_html": "Eight people are killed and seven injured in <a href=\"https://wikipedia.org/wiki/Allen_Premium_Outlets_shooting\" class=\"mw-redirect\" title=\"Allen Premium Outlets shooting\">a mass shooting</a> in <a href=\"https://wikipedia.org/wiki/<PERSON>,_Texas\" title=\"Allen, Texas\">Allen, Texas</a>. The perpetrator is killed by a police officer.", "links": [{"title": "Allen Premium Outlets shooting", "link": "https://wikipedia.org/wiki/Allen_Premium_Outlets_shooting"}, {"title": "Allen, Texas", "link": "https://wikipedia.org/wiki/Allen,_Texas"}]}], "Births": [{"year": "973", "text": "<PERSON>, Holy Roman Emperor (d. 1024)", "html": "973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1024)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1464", "text": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> of Brandenburg-Ansbach, Polish princess (d. 1512)", "html": "1464 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON><PERSON>_of_Brandenburg-Ansbach\" title=\"<PERSON>, <PERSON><PERSON><PERSON><PERSON> of Brandenburg-Ansbach\"><PERSON>, <PERSON><PERSON><PERSON><PERSON> of Brandenburg-Ansbach</a>, Polish princess (d. 1512)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON><PERSON>_of_Brandenburg-Ansbach\" title=\"<PERSON>, Mar<PERSON><PERSON><PERSON> of Brandenburg-Ansbach\"><PERSON>, <PERSON><PERSON><PERSON><PERSON> of Brandenburg-Ansbach</a>, Polish princess (d. 1512)", "links": [{"title": "<PERSON>, Margravine of Brandenburg-Ansbach", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON><PERSON>_of_Brandenburg-Ansbach"}]}, {"year": "1493", "text": "<PERSON><PERSON><PERSON>, Italian theologian and cardinal (d. 1563)", "html": "1493 - <a href=\"https://wikipedia.org/wiki/Giro<PERSON>o_Seripando\" title=\"Girolamo Seripando\"><PERSON><PERSON><PERSON></a>, Italian theologian and cardinal (d. 1563)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Giro<PERSON><PERSON> Seripand<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian theologian and cardinal (d. 1563)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>lamo_Seripando"}]}, {"year": "1501", "text": "<PERSON><PERSON>, pope of the Catholic Church (d. 1555)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lus_II\" title=\"<PERSON> <PERSON>lus II\"><PERSON><PERSON></a>, pope of the Catholic Church (d. 1555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lus_II\" title=\"Pope <PERSON>lus II\"><PERSON><PERSON></a>, pope of the Catholic Church (d. 1555)", "links": [{"title": "<PERSON> <PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_II"}]}, {"year": "1574", "text": "<PERSON>, pope of the Catholic Church (d. 1655)", "html": "1574 - <a href=\"https://wikipedia.org/wiki/Pope_Innocent_X\" title=\"Pope Innocent <PERSON>\"><PERSON></a>, pope of the Catholic Church (d. 1655)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Innocent_X\" title=\"Pope Innocent <PERSON>\"><PERSON></a>, pope of the Catholic Church (d. 1655)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Innocent_<PERSON>"}]}, {"year": "1580", "text": "<PERSON>, Duke of Mantua and Montferrat, French noble (d. 1637)", "html": "1580 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mantua_and_Montferrat\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Mantua and Montferrat\"><PERSON>, Duke of Mantua and Montferrat</a>, French noble (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Mantua_and_Montferrat\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Mantua and Montferrat\"><PERSON>, Duke of Mantua and Montferrat</a>, French noble (d. 1637)", "links": [{"title": "<PERSON>, Duke of Mantua and Montferrat", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_<PERSON>_Mantua_and_Montferrat"}]}, {"year": "1635", "text": "<PERSON>, German physician and alchemist (d. 1682)", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and alchemist (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician and alchemist (d. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1668", "text": "<PERSON><PERSON><PERSON>, French author and playwright (d. 1747)", "html": "1668 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A9_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and playwright (d. 1747)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A9_<PERSON>age"}]}, {"year": "1680", "text": "<PERSON><PERSON><PERSON>, Italian-French cellist and composer (d. 1755)", "html": "1680 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French cellist and composer (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-French cellist and composer (d. 1755)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1713", "text": "<PERSON>, French philosopher and academic (d. 1780)", "html": "1713 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philosopher and academic (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1714", "text": "<PERSON>, German tenor (d. 1797)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor (d. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German tenor (d. 1797)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1742", "text": "<PERSON>, Swiss pastor and physiologist (d. 1809)", "html": "1742 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and physiologist (d. 1809)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss pastor and physiologist (d. 1809)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1758", "text": "<PERSON>, French general (d. 1817)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na\" title=\"<PERSON>\"><PERSON></a>, French general (d. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Mass%C3%A9na"}]}, {"year": "1758", "text": "<PERSON><PERSON><PERSON>, French politician (d. 1794)", "html": "1758 - <a href=\"https://wikipedia.org/wiki/Maxim<PERSON>en_Robespierre\" title=\"<PERSON><PERSON>en Robespierre\"><PERSON><PERSON><PERSON></a>, French politician (d. 1794)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maxim<PERSON>en_Robespierre\" title=\"Maximilien Robespierre\"><PERSON><PERSON><PERSON></a>, French politician (d. 1794)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_Robespierre"}]}, {"year": "1769", "text": "<PERSON>, Grand Duke of Tuscany (d. 1824)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (d. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany\" title=\"<PERSON>, Grand Duke of Tuscany\"><PERSON>, Grand Duke of Tuscany</a> (d. 1824)", "links": [{"title": "<PERSON>, Grand Duke of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Tuscany"}]}, {"year": "1769", "text": "<PERSON>, French mathematician and academic (d. 1834)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1781", "text": "<PERSON>, German philosopher and author (d. 1832)", "html": "1781 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and author (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Karl <PERSON>\"><PERSON></a>, German philosopher and author (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, American religious leader and composer (d. 1882)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and composer (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and composer (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1800", "text": "<PERSON>, Polish general (d. 1881)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/Roman_Sangus<PERSON>ko\" title=\"Roman Sang<PERSON>ko\"><PERSON></a>, Polish general (d. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Sangus<PERSON>ko\" title=\"Roman Sanguszko\"><PERSON></a>, Polish general (d. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, German-American journalist and politician (d. 1891)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and politician (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American journalist and politician (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, German engineer and author (d. 1906)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and author (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer and author (d. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, American geologist and academic (d. 1918)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geologist and academic (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1848", "text": "<PERSON>, English chemist and academic (d. 1937)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist and academic (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, French singer and actor (d. 1925)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/Aristide_Bruant\" title=\"Arist<PERSON> Bruant\"><PERSON><PERSON><PERSON></a>, French singer and actor (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aristide_Bruant\" title=\"Arist<PERSON> Bruant\"><PERSON><PERSON><PERSON></a>, French singer and actor (d. 1925)", "links": [{"title": "<PERSON>st<PERSON>", "link": "https://wikipedia.org/wiki/Aristide_B<PERSON>ant"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON>, Austrian neurologist and psychoanalyst (d. 1939)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Freud\" title=\"Sigmund Freud\"><PERSON><PERSON><PERSON></a>, Austrian neurologist and psychoanalyst (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>g<PERSON>_Freud\" title=\"Sig<PERSON> Freud\"><PERSON><PERSON><PERSON></a>, Austrian neurologist and psychoanalyst (d. 1939)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>g<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American admiral and explorer (d. 1920)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and explorer (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and explorer (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician, President of the Indian National Congress (d. 1931)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a> (d. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Indian National Congress", "link": "https://wikipedia.org/wiki/President_of_the_Indian_National_Congress"}]}, {"year": "1868", "text": "<PERSON>, French journalist and author (d. 1927)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist and author (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1869", "text": "<PERSON><PERSON><PERSON>, Japanese businessman and central banker, 8th and 11th Governor of the Bank of Japan (d. 1932)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and central banker, 8th and 11th Governor of the <a href=\"https://wikipedia.org/wiki/Bank_of_Japan\" title=\"Bank of Japan\">Bank of Japan</a> (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and central banker, 8th and 11th Governor of the <a href=\"https://wikipedia.org/wiki/Bank_of_Japan\" title=\"Bank of Japan\">Bank of Japan</a> (d. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Bank of Japan", "link": "https://wikipedia.org/wiki/Bank_of_Japan"}]}, {"year": "1870", "text": "<PERSON>, Scottish golfer (d. 1936)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(golfer)\" title=\"<PERSON> (golfer)\"><PERSON></a>, Scottish golfer (d. 1936)", "links": [{"title": "<PERSON> (golfer)", "link": "https://wikipedia.org/wiki/<PERSON>(golfer)"}]}, {"year": "1871", "text": "<PERSON>, French chemist and academic, Nobel Prize laureate (d. 1935)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1871", "text": "<PERSON>, German author and poet (d. 1914)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German author and poet (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, German author and poet (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Christian_Morgenstern"}]}, {"year": "1872", "text": "<PERSON>, Dutch mathematician, physicist, and astronomer (d. 1934)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician, physicist, and astronomer (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch mathematician, physicist, and astronomer (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON><PERSON><PERSON>, Ottoman general (d. 1922)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman general (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ottoman general (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Czech orientalist and linguist (d. 1952)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Hrozn%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech orientalist and linguist (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bed%C5%99ich_Hrozn%C3%BD\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech orientalist and linguist (d. 1952)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bed%C5%99ich_Hrozn%C3%BD"}]}, {"year": "1879", "text": "<PERSON><PERSON><PERSON>, Dutch footballer (d. 1929)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/He<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch footballer (d. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, English-South African painter and illustrator (d. 1959)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-South African painter and illustrator (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-South African painter and illustrator (d. 1959)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, German-Swiss painter (d. 1938)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss painter (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Swiss painter (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Italian actor (d. 1955)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Brazilian mathematician and author (d. 1974)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_de_Mello_e_Souza\" title=\"<PERSON><PERSON><PERSON> Souza\"><PERSON><PERSON><PERSON></a>, Brazilian mathematician and author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_de_Mello_e_Souza\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian mathematician and author (d. 1974)", "links": [{"title": "<PERSON><PERSON><PERSON> Souza", "link": "https://wikipedia.org/wiki/J%C3%BAlio_C%C3%A9sar_de_Mello_e_Souza"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian soldier and politician, Hungarian Minister of Agriculture (d. 1946)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)\" title=\"Minister of Agriculture (Hungary)\">Hungarian Minister of Agriculture</a> (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy\" title=\"<PERSON>d<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)\" title=\"Minister of Agriculture (Hungary)\">Hungarian Minister of Agriculture</a> (d. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fid%C3%A9l_P%C3%A1lffy"}, {"title": "Minister of Agriculture (Hungary)", "link": "https://wikipedia.org/wiki/Minister_of_Agriculture_(Hungary)"}]}, {"year": "1895", "text": "<PERSON>, Italian actor (d. 1926)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, Swedish physicist and academic (d. 1966)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish physicist and academic (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, German author and poet (d. 1979)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author and poet (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, Czech soldier and politician (d. 1945)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soldier and politician (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soldier and politician (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, German-American director and screenwriter (d. 1957)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/Max_Oph%C3%BCls\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Oph%C3%BCls\" title=\"<PERSON>\"><PERSON></a>, German-American director and screenwriter (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Max_Oph%C3%BCls"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American businessman, founded <PERSON>ts Shor's Restaurant (d. 1977)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/Toots_Shor\" title=\"Toots Shor\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant\" title=\"Toots Shor's Restaurant\">Toots Shor's Restaurant</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toots_Shor\" title=\"Toots Shor\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant\" title=\"Toots Shor's Restaurant\">Toots Shor's Restaurant</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Toots_Shor"}, {"title": "Toots Shor's Restaurant", "link": "https://wikipedia.org/wiki/Toots_Shor%27s_Restaurant"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Israeli physicist and academic (d. 1984)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli physicist and academic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Israeli physicist and academic (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mosh%C3%A9_Feldenkrais"}]}, {"year": "1904", "text": "<PERSON>, English actress (d. 1979)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Swedish novelist, essayist, and poet Nobel Prize laureate (d. 1978)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish novelist, essayist, and poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish novelist, essayist, and poet <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1906", "text": "<PERSON>, French mathematician and academic (d. 1998)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Weil"}]}, {"year": "1907", "text": "<PERSON>, Executed Irish Republican (d. 1940)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Irish_republican)\" title=\"<PERSON> (Irish republican)\"><PERSON></a>, Executed <a href=\"https://wikipedia.org/wiki/Irish_Republican\" class=\"mw-redirect\" title=\"Irish Republican\">Irish Republican</a> (d. 1940)", "links": [{"title": "<PERSON> (Irish republican)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_republican)"}, {"title": "Irish Republican", "link": "https://wikipedia.org/wiki/Irish_Republican"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American football player and coach (d. 1998)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/Weeb_Ewbank\" title=\"Weeb Ewbank\"><PERSON><PERSON></a>, American football player and coach (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Weeb_Ewbank\" title=\"Weeb Ewbank\"><PERSON><PERSON></a>, American football player and coach (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Weeb_Ewbank"}]}, {"year": "1911", "text": "<PERSON>, French journalist and author (d. 1993)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guy <PERSON>\"><PERSON></a>, French journalist and author (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guy des <PERSON>\"><PERSON></a>, French journalist and author (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American pianist (d. 1989)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_<PERSON>o"}]}, {"year": "1913", "text": "<PERSON>, English-American actor (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American actor, director, producer, and screenwriter (d. 1985)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Welles\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Welles\"><PERSON><PERSON></a>, American actor, director, producer, and screenwriter (d. 1985)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American historian, journalist, and author (d. 1986)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, journalist, and author (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, journalist, and author (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American physicist and astronomer (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and astronomer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON> <PERSON>, emir of Abu Dhabi and first president of the United Arab Emirates (d. 2004)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>Nahyan\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, emir of <a href=\"https://wikipedia.org/wiki/Abu_Dhabi\" title=\"Abu Dhabi\">Abu Dhabi</a> and first president of the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>Nahyan\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, emir of <a href=\"https://wikipedia.org/wiki/Abu_Dhabi\" title=\"Abu Dhabi\">Abu Dhabi</a> and first president of the <a href=\"https://wikipedia.org/wiki/United_Arab_Emirates\" title=\"United Arab Emirates\">United Arab Emirates</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Abu Dhabi", "link": "https://wikipedia.org/wiki/Abu_Dhabi"}, {"title": "United Arab Emirates", "link": "https://wikipedia.org/wiki/United_Arab_Emirates"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Fijian politician, 1st Prime Minister of Fiji (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Fijian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Fijian politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Fiji\" title=\"Prime Minister of Fiji\">Prime Minister of Fiji</a> (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ka<PERSON><PERSON>_Mara"}, {"title": "Prime Minister of Fiji", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Fiji"}]}, {"year": "1923", "text": "<PERSON>, Canadian ice hockey player and coach (d. 2002)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1923)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey b. 1923)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1923)\" class=\"mw-redirect\" title=\"<PERSON> (ice hockey b. 1923)\"><PERSON></a>, Canadian ice hockey player and coach (d. 2002)", "links": [{"title": "<PERSON> (ice hockey b. 1923)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey_b._1923)"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Spanish painter and sculptor (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Nestor_Basterretxea\" title=\"Nestor Basterretxea\">Nest<PERSON></a>, Spanish painter and sculptor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nestor_Basterretxea\" title=\"Nestor Basterretxea\"><PERSON><PERSON><PERSON></a>, Spanish painter and sculptor (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nestor_Basterretxea"}]}, {"year": "1924", "text": "<PERSON>, American socialite, activist, and author (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, activist, and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American socialite, activist, and author (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English guitarist, composer, and producer (d. 1992)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist, composer, and producer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Wright\"><PERSON></a>, English guitarist, composer, and producer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, English archaeologist and academic (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American chemist and biophysicist, Nobel Prize laureate (d. 2007)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and biophysicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1931", "text": "<PERSON>, American baseball player and coach (d. 2024)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1932", "text": "<PERSON>, 7th Marquess of Bath, English lieutenant and politician (d. 2020)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Marquess_of_Bath\" title=\"<PERSON>, 7th Marquess of Bath\"><PERSON>, 7th Marquess <PERSON> Bath</a>, English lieutenant and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Marquess_of_Bath\" title=\"<PERSON>, 7th Marquess of Bath\"><PERSON>, 7th Marquess of Bath</a>, English lieutenant and politician (d. 2020)", "links": [{"title": "<PERSON>, 7th Marquess of Bath", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Marquess_of_Bath"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American-Canadian boxer (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian boxer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian boxer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Argentinian author, playwright, and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author, playwright, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian author, playwright, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, German terrorist, co-founded the Red Army Faction (d. 1977)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German terrorist, co-founded the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German terrorist, co-founded the <a href=\"https://wikipedia.org/wiki/Red_Army_Faction\" title=\"Red Army Faction\">Red Army Faction</a> (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Red Army Faction", "link": "https://wikipedia.org/wiki/Red_Army_Faction"}]}, {"year": "1943", "text": "<PERSON>, American conspiracy theorist and author (d. 2001)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conspiracy theorist and author (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conspiracy theorist and author (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American sculptor and illustrator", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American country singer-songwriter, guitarist, actor, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter, guitarist, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer-songwriter, guitarist, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, New Zealand actor", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American philosopher and author", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philosopher and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American journalist and author", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Deaver\"><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Deaver\"><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Liberian sergeant and politician, 21st President of Liberia (d. 1990)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian sergeant and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Liberian sergeant and politician, 21st <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">President of Liberia</a> (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1952", "text": "<PERSON><PERSON>, Japanese physician and astronaut", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese physician and astronaut", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese physician and astronaut", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Dutch economist and politician, Deputy Prime Minister of the Netherlands", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands\" title=\"Deputy Prime Minister of the Netherlands\">Deputy Prime Minister of the Netherlands</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G<PERSON><PERSON>_<PERSON><PERSON>m"}, {"title": "Deputy Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_Netherlands"}]}, {"year": "1953", "text": "<PERSON>, Ukrainian Chernobyl worker (d. 1986)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian <a href=\"https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant\" title=\"Chernobyl Nuclear Power Plant\">Chernobyl</a> worker (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Ukrainian <a href=\"https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant\" title=\"Chernobyl Nuclear Power Plant\">Chernobyl</a> worker (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Chernobyl Nuclear Power Plant", "link": "https://wikipedia.org/wiki/Chernobyl_Nuclear_Power_Plant"}]}, {"year": "1953", "text": "<PERSON>, British politician, Prime Minister of the United Kingdom", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1953", "text": "<PERSON>, Scottish international footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish international footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish international footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Greek politician, 120th Greek Minister for Foreign Affairs", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek politician, 120th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister for Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek politician, 120th <a href=\"https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)\" class=\"mw-redirect\" title=\"Minister for Foreign Affairs (Greece)\">Greek Minister for Foreign Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister for Foreign Affairs (Greece)", "link": "https://wikipedia.org/wiki/Minister_for_Foreign_Affairs_(Greece)"}]}, {"year": "1955", "text": "<PERSON>, American television host", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Baron <PERSON> of Furness, English academic and politician, Secretary of State for Defence", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Furness\" title=\"<PERSON>, Baron <PERSON> of Furness\"><PERSON>, Baron <PERSON> of Furness</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Furness\" title=\"<PERSON>, Baron <PERSON> of Furness\"><PERSON>, Baron <PERSON> of Furness</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Defence\" title=\"Secretary of State for Defence\">Secretary of State for Defence</a>", "links": [{"title": "<PERSON>, Baron <PERSON> of Furness", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Furness"}, {"title": "Secretary of State for Defence", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Defence"}]}, {"year": "1959", "text": "<PERSON>, English politician", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English political scientist, philosopher, and academic", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist, philosopher, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English political scientist, philosopher, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Irish-American actress and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish-American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Down<PERSON>\"><PERSON></a>, Irish-American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roma_Downey"}]}, {"year": "1960", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, French actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Scottish businessman and philanthropist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, Scottish businessman and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Dutch politician and diplomat, First Vice President of the European Commission", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/Vice-President_of_the_European_Commission\" title=\"Vice-President of the European Commission\">First Vice President of the European Commission</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch politician and diplomat, <a href=\"https://wikipedia.org/wiki/Vice-President_of_the_European_Commission\" title=\"Vice-President of the European Commission\">First Vice President of the European Commission</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>mans"}, {"title": "Vice-President of the European Commission", "link": "https://wikipedia.org/wiki/Vice-President_of_the_European_Commission"}]}, {"year": "1962", "text": "<PERSON>, English politician", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brak<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>e"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Italian ballerina", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ballerina", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian ballerina", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, Canadian actress, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, French singer and keyboard player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/L%C3%A6<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Sadier\"><PERSON><PERSON><PERSON><PERSON></a>, French singer and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A6<PERSON><PERSON>_Sadie<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Sadier\"><PERSON><PERSON><PERSON><PERSON></a>, French singer and keyboard player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A6titia_Sadier"}]}, {"year": "1969", "text": "<PERSON>, Northern Irish footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_de_la_Pe%C3%B1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_de_la_Pe%C3%B1a\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_de_la_Pe%C3%B1a"}]}, {"year": "1977", "text": "<PERSON>, American ice hockey player and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, American ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, Australian diver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian diver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian diver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>bery"}]}, {"year": "1978", "text": "<PERSON>, American football player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1978", "text": "<PERSON>, French slalom canoeist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French slalom canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French slalom canoeist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Swedish journalist and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ley\"><PERSON><PERSON></a>, Swedish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Federley\"><PERSON><PERSON></a>, Swedish journalist and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Estonian discus thrower", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian discus thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian discus thrower", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Canadian skeleton racer and television host", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skeleton racer and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian skeleton racer and television host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American swimmer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Greek professional basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek professional basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek professional basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>n"}]}, {"year": "1983", "text": "<PERSON>, Brazilian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Gabourey_Sidibe\" title=\"Gabourey Sidibe\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gab<PERSON><PERSON>_Sidibe\" title=\"Gabourey Sidibe\"><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabourey_Sidibe"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Tibetan religious leader, the 17th Karmapa Lama", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tri<PERSON><PERSON> Thaye Do<PERSON>je\"><PERSON><PERSON><PERSON></a>, Tibetan religious leader, the 17th <a href=\"https://wikipedia.org/wiki/Karmapa\" title=\"Karmapa\"><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Tri<PERSON><PERSON> Thaye Do<PERSON>je\"><PERSON><PERSON><PERSON></a>, Tibetan religious leader, the 17th <a href=\"https://wikipedia.org/wiki/Karmapa\" title=\"Karmapa\"><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ka<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Karmapa"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Slovenian basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Goran_Dragic\" class=\"mw-redirect\" title=\"Goran Dragic\"><PERSON><PERSON></a>, Slovenian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Goran_<PERSON>agic\" class=\"mw-redirect\" title=\"<PERSON>ran Dragic\"><PERSON><PERSON></a>, Slovenian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ran_<PERSON>agic"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Belgian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, American rapper", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Meek Mill\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Meek Mill\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "Meek Mill", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Venezuelan baseball player and coach", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1988)\" title=\"<PERSON> (basketball, born 1988)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1988)\" title=\"<PERSON> (basketball, born 1988)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball, born 1988)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball,_born_1988)"}]}, {"year": "1988", "text": "<PERSON>, New Zealand professional wrestler", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Dakota_Kai\" title=\"Dakota Kai\"><PERSON></a>, New Zealand professional wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dakota_Kai\" title=\"Dakota Kai\"><PERSON></a>, New Zealand professional wrestler", "links": [{"title": "Dakota Kai", "link": "https://wikipedia.org/wiki/Dakota_Kai"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Slovak tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Dominika_Cibulkov%C3%A1\" title=\"Dominika Cibulková\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dominika_Cibulkov%C3%A1\" title=\"Dom<PERSON><PERSON> Cibulková\"><PERSON><PERSON><PERSON></a>, Slovak tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dominika_Cibulkov%C3%A1"}]}, {"year": "1989", "text": "<PERSON>, American football player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Venezuelan baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venezuelan baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Gul%C3%A1csi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/P%C3%A9ter_Gul%C3%A1csi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P%C3%A9ter_Gul%C3%A1csi"}]}, {"year": "1992", "text": "<PERSON>, Canadian ice hockey player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, South Korean musician and actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean musician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, South Korean musician and actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Lithuanian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C5%ABnas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C5%ABnas\" title=\"<PERSON>\"><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C5%ABnas"}]}, {"year": "1993", "text": "<PERSON>, Paraguayan footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Paraguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustavo_G%C3%B3mez"}]}, {"year": "1993", "text": "<PERSON>, English actress", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Croatian international footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian international footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian international footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Filipino model, entertainer and singer-songwriter", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"May<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino model, entertainer and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino model, entertainer and singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maymay_Entrata"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Filipino social media personality and entertainer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino social media personality and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino social media personality and entertainer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Scottish swimmer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, Scottish swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)"}]}, {"year": "1998", "text": "<PERSON>, American murder suspect", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder suspect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American murder suspect", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luigi_<PERSON>e"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Mexican racing driver", "html": "1999 - <a href=\"https://wikipedia.org/wiki/Pato_O%27Ward\" title=\"Pato O'Ward\"><PERSON><PERSON></a>, Mexican racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pato_O%27Ward\" title=\"Pato O'Ward\"><PERSON><PERSON></a>, Mexican racing driver", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pato_O%27Ward"}]}, {"year": "2002", "text": "<PERSON>, English footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Reese\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Reese\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON> of Sussex", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Sussex\" title=\"Prince <PERSON> of Sussex\">Prince <PERSON> of Sussex</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Sussex\" title=\"Prince <PERSON> of Sussex\">Prince <PERSON> of Sussex</a>", "links": [{"title": "<PERSON> of Sussex", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Sussex"}]}], "Deaths": [{"year": "698", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Lindisfarne", "html": "698 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON><PERSON>_of_Lindisfarne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Lindisfarne\" title=\"Lindisfarne\">Lindisfarne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_of_Lindisfarne\" title=\"<PERSON><PERSON><PERSON><PERSON> of Lindisfarne\"><PERSON><PERSON><PERSON><PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Lindisfarne\" title=\"Lindisfarne\">Lindisfarne</a>", "links": [{"title": "Eadberht of Lindisfarne", "link": "https://wikipedia.org/wiki/Eadber<PERSON>_of_Lindisfarne"}, {"title": "Lindisfarne", "link": "https://wikipedia.org/wiki/Lindisfarne"}]}, {"year": "850", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (b. 808)", "html": "850 - <a href=\"https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 808)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (b. 808)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_Nin<PERSON>%C5%8D"}]}, {"year": "932", "text": "<PERSON><PERSON>, Chinese warlord and king (b. 852)", "html": "932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese warlord and king (b. 852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese warlord and king (b. 852)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "988", "text": "<PERSON>, count of Frisia and Holland", "html": "988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON> II</a>, count of <a href=\"https://wikipedia.org/wiki/Frisia\" title=\"Frisia\">Frisia</a> and <a href=\"https://wikipedia.org/wiki/County_of_Holland\" title=\"County of Holland\">Holland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland\" title=\"<PERSON>, Count of Holland\"><PERSON> II</a>, count of <a href=\"https://wikipedia.org/wiki/Frisia\" title=\"Frisia\">Frisia</a> and <a href=\"https://wikipedia.org/wiki/County_of_Holland\" title=\"County of Holland\">Holland</a>", "links": [{"title": "<PERSON>, Count of Holland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Holland"}, {"title": "Frisia", "link": "https://wikipedia.org/wiki/Frisia"}, {"title": "County of Holland", "link": "https://wikipedia.org/wiki/County_of_Holland"}]}, {"year": "1002", "text": "<PERSON><PERSON><PERSON><PERSON>, Archbishop of York, Abbot of Peterborough and Bishop of Worcester", "html": "1002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_York)\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop of York)\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_York\" title=\"Archbishop of York\">Archbishop of York</a>, <a href=\"https://wikipedia.org/wiki/Abbot_of_Peterborough\" title=\"Abbot of Peterborough\">Abbot of Peterborough</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Worcester\" title=\"Bishop of Worcester\">Bishop of Worcester</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_York)\" title=\"<PERSON><PERSON><PERSON><PERSON> (archbishop of York)\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Archbishop_of_York\" title=\"Archbishop of York\">Archbishop of York</a>, <a href=\"https://wikipedia.org/wiki/Abbot_of_Peterborough\" title=\"Abbot of Peterborough\">Abbot of Peterborough</a> and <a href=\"https://wikipedia.org/wiki/Bishop_of_Worcester\" title=\"Bishop of Worcester\">Bishop of Worcester</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (archbishop of York)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_(archbishop_of_York)"}, {"title": "Archbishop of York", "link": "https://wikipedia.org/wiki/Archbishop_of_York"}, {"title": "Abbot of Peterborough", "link": "https://wikipedia.org/wiki/<PERSON>_of_Peterborough"}, {"title": "Bishop of Worcester", "link": "https://wikipedia.org/wiki/<PERSON>_of_Worcester"}]}, {"year": "1187", "text": "<PERSON><PERSON>, Prince of Armenia (b. 1145)", "html": "1187 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Prince of Armenia\"><PERSON><PERSON>, Prince of Armenia</a> (b. 1145)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Armenia\" class=\"mw-redirect\" title=\"<PERSON><PERSON>, Prince of Armenia\"><PERSON><PERSON>, Prince of Armenia</a> (b. 1145)", "links": [{"title": "<PERSON><PERSON>, Prince of Armenia", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Prince_of_Armenia"}]}, {"year": "1236", "text": "<PERSON> of Wendover, Benedictine monk and chronicler", "html": "1236 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wen<PERSON>ver\" title=\"<PERSON> of Wendover\"><PERSON> of Wendover</a>, <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> monk and <a href=\"https://wikipedia.org/wiki/Chronicle\" title=\"Chronicle\">chronicler</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Wendover\"><PERSON> of Wendover</a>, <a href=\"https://wikipedia.org/wiki/Benedictine\" class=\"mw-redirect\" title=\"Benedictine\">Benedictine</a> monk and <a href=\"https://wikipedia.org/wiki/Chronicle\" title=\"Chronicle\">chronicler</a>", "links": [{"title": "<PERSON> of Wendover", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wendo<PERSON>"}, {"title": "Benedictine", "link": "https://wikipedia.org/wiki/Benedictine"}, {"title": "Chronicle", "link": "https://wikipedia.org/wiki/Chronicle"}]}, {"year": "1471", "text": "<PERSON>, English commander (b. 1438)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1471)\" title=\"<PERSON> (died 1471)\"><PERSON></a>, English commander (b. 1438)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1471)\" title=\"<PERSON> (died 1471)\"><PERSON></a>, English commander (b. 1438)", "links": [{"title": "<PERSON> (died 1471)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(died_1471)"}]}, {"year": "1471", "text": "<PERSON>, Speaker of the House of Commons", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speaker)\" title=\"<PERSON> (speaker)\"><PERSON></a>, Speaker of the House of Commons", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speaker)\" title=\"<PERSON> (speaker)\"><PERSON></a>, Speaker of the House of Commons", "links": [{"title": "<PERSON> (speaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(speaker)"}]}, {"year": "1475", "text": "<PERSON><PERSON>, Flemish painter (b. 1415)", "html": "1475 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (b. 1415)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Flemish painter (b. 1415)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>uts"}]}, {"year": "1483", "text": "Queen <PERSON><PERSON><PERSON>, Korean regent (b. 1418)", "html": "1483 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Korean regent (b. 1418)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON></a>, Korean regent (b. 1418)", "links": [{"title": "Queen <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1502", "text": "<PERSON>, English knight (b. 1450)", "html": "1502 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight (b. 1450)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English knight (b. 1450)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1527", "text": "<PERSON>, Duke of Bourbon, Count of Montpensier and <PERSON><PERSON><PERSON> of Auvergne (b. 1490)", "html": "1527 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a>, Count of Montpensier and <PERSON><PERSON><PERSON> of Auvergne (b. 1490)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon\" title=\"<PERSON>, Duke of Bourbon\"><PERSON>, Duke of Bourbon</a>, Count of Montpensier and <PERSON><PERSON><PERSON> of Auvergne (b. 1490)", "links": [{"title": "<PERSON>, Duke of Bourbon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bourbon"}]}, {"year": "1540", "text": "<PERSON>, Spanish scholar (b. 1492)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_Vives\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish scholar (b. 1492)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_Vives\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish scholar (b. 1492)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADs_Vives"}]}, {"year": "1596", "text": "<PERSON><PERSON><PERSON>, Flemish-Italian composer (b. 1535)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Wert\" title=\"<PERSON><PERSON><PERSON> de Wert\"><PERSON><PERSON><PERSON> Wert</a>, Flemish-Italian composer (b. 1535)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Wert\" title=\"<PERSON><PERSON><PERSON> de Wert\"><PERSON><PERSON><PERSON>rt</a>, Flemish-Italian composer (b. 1535)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1631", "text": "Sir <PERSON>, 1st Baronet, of Connington, English historian and politician, founded the Cotton library (b. 1570)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet,_of_Connington\" title=\"Sir <PERSON>, 1st Baronet, of Connington\">Sir <PERSON>, 1st Baronet, of Connington</a>, English historian and politician, founded the <a href=\"https://wikipedia.org/wiki/Cotton_library\" title=\"Cotton library\">Cotton library</a> (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet,_of_Connington\" title=\"Sir <PERSON>, 1st Baronet, of Connington\">Sir <PERSON>, 1st Baronet, of Connington</a>, English historian and politician, founded the <a href=\"https://wikipedia.org/wiki/Cotton_library\" title=\"Cotton library\">Cotton library</a> (b. 1570)", "links": [{"title": "Sir <PERSON>, 1st Baronet, of Connington", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet,_of_Connington"}, {"title": "Cotton library", "link": "https://wikipedia.org/wiki/Cotton_library"}]}, {"year": "1638", "text": "<PERSON>, Dutch-French bishop and theologian (b. 1585)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French bishop and theologian (b. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-French bishop and theologian (b. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1708", "text": "<PERSON>, French-Canadian bishop (b. 1623)", "html": "1708 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian bishop (b. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-Canadian bishop (b. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, 2nd Duke of Grafton, English politician, Lord Lieutenant of Ireland (b. 1683)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Grafton\" title=\"<PERSON>, 2nd Duke of Grafton\"><PERSON>, 2nd Duke of Grafton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1683)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Grafton\" title=\"<PERSON>, 2nd Duke of Grafton\"><PERSON>, 2nd Duke of Grafton</a>, English politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1683)", "links": [{"title": "<PERSON>, 2nd Duke of Grafton", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Duke_of_Grafton"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1757", "text": "<PERSON>, Prussian field marshal (b. 1684)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (b. 1684)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian field marshal (b. 1684)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON>, German astronomer and academic (b. 1696)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (b. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German astronomer and academic (b. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON>, Colombian general and politician, 4th President of the Republic of the New Granada (b. 1792)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco <PERSON>nder\"><PERSON></a>, Colombian general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada\" class=\"mw-redirect\" title=\"President of the Republic of the New Granada\">President of the Republic of the New Granada</a> (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>_<PERSON>\" title=\"Francisco de <PERSON>nder\"><PERSON></a>, Colombian general and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada\" class=\"mw-redirect\" title=\"President of the Republic of the New Granada\">President of the Republic of the New Granada</a> (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of the Republic of the New Granada", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_the_New_Granada"}]}, {"year": "1859", "text": "<PERSON>, German geographer and explorer (b. 1769)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German geographer and explorer (b. 1769)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American essayist, poet, and philosopher (b. 1817)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist, poet, and philosopher (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American essayist, poet, and philosopher (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, American businessman and politician (b. 1814)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates <PERSON>\"><PERSON><PERSON></a>, American businessman and politician (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Socrates_<PERSON>\" title=\"Socrates Nelson\"><PERSON><PERSON></a>, American businessman and politician (b. 1814)", "links": [{"title": "So<PERSON>", "link": "https://wikipedia.org/wiki/So<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, Swedish-Finnish poet and hymn-writer (b. 1804)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Finnish poet and hymn-writer (b. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish-Finnish poet and hymn-writer (b. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Irish civil servant (b. 1829)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(civil_servant)\" title=\"<PERSON> (civil servant)\"><PERSON></a>, Irish civil servant (b. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(civil_servant)\" title=\"<PERSON> (civil servant)\"><PERSON></a>, Irish civil servant (b. 1829)", "links": [{"title": "<PERSON> (civil servant)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(civil_servant)"}]}, {"year": "1882", "text": "Lord <PERSON>, British politician, Chief Secretary for Ireland (b. 1836)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, British politician, <a href=\"https://wikipedia.org/wiki/Chief_Secretary_for_Ireland\" title=\"Chief Secretary for Ireland\">Chief Secretary for Ireland</a> (b. 1836)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}, {"title": "Chief Secretary for Ireland", "link": "https://wikipedia.org/wiki/Chief_Secretary_for_Ireland"}]}, {"year": "1888", "text": "<PERSON>, American rabbi (b. c. 1813)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi (b. c. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rabbi (b. c. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, English-Australian politician, 1st Premier of Queensland (b. 1831)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1907", "text": "<PERSON><PERSON>, Maltese architect and civil engineer (b. 1830)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese architect and civil engineer (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Maltese architect and civil engineer (b. 1830)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON> of the United Kingdom (b. 1841)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward VII\"><PERSON></a> of the United Kingdom (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Edward VII\"><PERSON></a> of the United Kingdom (b. 1841)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, French aviator (b. 1880)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French aviator (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Vallon"}]}, {"year": "1919", "text": "<PERSON><PERSON>,  American novelist (b. 1856)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American novelist (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American novelist (b. 1856)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Russian-French painter and illustrator (b. 1869)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and illustrator (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-French painter and illustrator (b. 1869)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Belgian-French poet and playwright, Nobel Prize laureate (b. 1862)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French poet and playwright, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1951", "text": "<PERSON><PERSON>, French mathematician and physicist (b. 1869)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and physicist (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French mathematician and physicist (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_<PERSON>tan"}]}, {"year": "1952", "text": "<PERSON>, Italian-Dutch physician and educator (b. 1870)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Dutch physician and educator (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maria_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Dutch physician and educator (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Montessori"}]}, {"year": "1959", "text": "<PERSON>, Polish actress (b. 1881)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%99ba\" title=\"<PERSON>\"><PERSON></a>, Polish actress (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%99ba\" title=\"<PERSON>\"><PERSON></a>, Polish actress (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_Dul%C4%99ba"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Estonian-American economist and academic (b. 1907)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>ur<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American economist and academic (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian-American economist and academic (b. 1907)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1961", "text": "<PERSON>, Romanian poet, playwright, and philosopher (b. 1895)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, playwright, and philosopher (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian poet, playwright, and philosopher (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lucian_<PERSON>a"}]}, {"year": "1963", "text": "<PERSON>, Hungarian-American mathematician, physicist, and engineer (b. 1881)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician, physicist, and engineer (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American mathematician, physicist, and engineer (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A1rm%C3%A1n"}]}, {"year": "1963", "text": "<PERSON>, American violinist, trombonist, and bandleader (b. 1901)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, trombonist, and bandleader (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, trombonist, and bandleader (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American raconteur, actor, and  director (b. 1888)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American raconteur, actor, and director (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American raconteur, actor, and director (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Chinese author and translator (b. 1885)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and translator (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese author and translator (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Russian general (b. 1879)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian conductor and composer (b. 1893)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian conductor and composer (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian conductor and composer (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian cardinal (b. 1892)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian cardinal (b. 1892)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%B3<PERSON><PERSON>_<PERSON>szenty"}]}, {"year": "1980", "text": "<PERSON>, Chilean writer (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Luisa_Bombal\" title=\"María Luis<PERSON> Bombal\"><PERSON></a>, Chilean writer (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%ADa_Luisa_Bombal\" title=\"María Luis<PERSON> Bomb<PERSON>\"><PERSON></a>, Chilean writer (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON><PERSON>_Bombal"}]}, {"year": "1983", "text": "<PERSON>, American author and illustrator (b. 1916)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ezra <PERSON>\"><PERSON></a>, American author and illustrator (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Ezra <PERSON>\"><PERSON></a>, American author and illustrator (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Danish-American trombonist and composer (b. 1922)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American trombonist and composer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American trombonist and composer (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American journalist and politician (b. 1904)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist and politician (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)\" title=\"<PERSON> (editor)\"><PERSON></a>, American journalist and politician (b. 1904)", "links": [{"title": "<PERSON> (editor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(editor)"}]}, {"year": "1984", "text": "<PERSON><PERSON>, English politician (b. 1912)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>er_Pink\" title=\"Bonner Pink\"><PERSON><PERSON></a>, English politician (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>er_<PERSON>\" title=\"Bonner Pink\"><PERSON><PERSON></a>, English politician (b. 1912)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American politician, 13th Director of Central Intelligence (b. 1913)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, 13th <a href=\"https://wikipedia.org/wiki/Director_of_Central_Intelligence\" title=\"Director of Central Intelligence\">Director of Central Intelligence</a> (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Director of Central Intelligence", "link": "https://wikipedia.org/wiki/Director_of_Central_Intelligence"}]}, {"year": "1989", "text": "<PERSON>, American football player and coach (b. 1897)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1900)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, English actor (b. 1903)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English actor (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">W<PERSON><PERSON><PERSON></a>, English actor (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, German-American actress and singer (b. 1901)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and singer (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-American actress and singer (b. 1901)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English actress and producer (b. 1909)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and producer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Northern Irish footballer (b. 1956)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Australian ecologist and academic (b. 1920)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ecologist and academic (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian ecologist and academic (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Canadian violinist, composer, conductor, and educator (b. 1906)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, conductor, and educator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian violinist, composer, conductor, and educator (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American singer-songwriter and pianist (b. 1932)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and pianist (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, Dutch sociologist, academic, and politician (b. 1948)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON></a>, Dutch sociologist, academic, and politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pim_Fortuyn\" title=\"Pim Fortuyn\"><PERSON><PERSON></a>, Dutch sociologist, academic, and politician (b. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pim_Fortuyn"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Norwegian saxophonist (b. 1940)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian saxophonist (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON><PERSON></a>, Norwegian saxophonist (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/Bj%C3%B8<PERSON>_<PERSON>_(musician)"}]}, {"year": "2003", "text": "<PERSON>, American baseball player and journalist (b. 1927)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American actress and singer (b. 1925)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Virginia_Capers\" title=\"Virginia Capers\">Virginia Capers</a>, American actress and singer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Virginia_Capers\" title=\"Virginia Capers\">Virginia Capers</a>, American actress and singer (b. 1925)", "links": [{"title": "Virginia Capers", "link": "https://wikipedia.org/wiki/Virginia_Capers"}]}, {"year": "2004", "text": "<PERSON>, American monk and educator (b. 1912)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American monk and educator (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American monk and educator (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American guitarist and composer (b. 1923)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and composer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Australian singer-songwriter and guitarist (b. 1958)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and guitarist (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Canadian journalist (b. 1958)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian journalist (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Brazilian physician and politician (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/En%C3%A9as_Carneiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/En%C3%A9as_Carneiro\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian physician and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/En%C3%A9as_Carneiro"}]}, {"year": "2007", "text": "<PERSON>, American actor, director, and screenwriter (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American race car driver (b. 1978)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American baseball player, coach, and sportscaster (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player, coach, and sportscaster (b. 1926)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "2012", "text": "<PERSON>, American lieutenant, lawyer, and judge (b. 1918)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and judge (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant, lawyer, and judge (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American director and producer (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French psychoanalyst and author (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychoanalyst and author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French psychoanalyst and author (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Italian journalist and politician, 41st Prime Minister of Italy (b. 1919)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and politician, 41st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Peruvian bishop (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian bishop (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Peruvian bishop (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sever<PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Italian-Canadian lawyer and politician (b. 1949)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Canadian lawyer and politician (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-Canadian lawyer and politician (b. 1949)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Dutch economist and politician, Dutch Minister of Social Affairs (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Wil_<PERSON>\" title=\"Wil <PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Social_Affairs_and_Employment_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Social Affairs and Employment (Netherlands)\">Dutch Minister of Social Affairs</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil_<PERSON>\" title=\"Wil <PERSON>\"><PERSON><PERSON></a>, Dutch economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Social_Affairs_and_Employment_(Netherlands)\" class=\"mw-redirect\" title=\"Ministry of Social Affairs and Employment (Netherlands)\">Dutch Minister of Social Affairs</a> (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil_Albeda"}, {"title": "Ministry of Social Affairs and Employment (Netherlands)", "link": "https://wikipedia.org/wiki/Ministry_of_Social_Affairs_and_Employment_(Netherlands)"}]}, {"year": "2014", "text": "<PERSON>, American pilot, engineer, and astronaut (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot, engineer, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American boxer (b. 1940)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(boxer)\" title=\"<PERSON> (boxer)\"><PERSON></a>, American boxer (b. 1940)", "links": [{"title": "<PERSON> (boxer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(boxer)"}]}, {"year": "2014", "text": "<PERSON>, American baseball player and scout (b. 1928)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and scout (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, English pianist, composer, and conductor (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Austrian painter and academic (b. 1919)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and academic (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian painter and academic (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Canadian environmentalist and author (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian environmentalist and author (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian environmentalist and author (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Bangladeshi sculptor (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi sculptor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi sculptor (b. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American race car driver and journalist (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and journalist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American soldier, lawyer, and politician, 56th Speaker of the United States House of Representatives (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician, 56th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives\" title=\"Speaker of the United States House of Representatives\">Speaker of the United States House of Representatives</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Speaker of the United States House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_United_States_House_of_Representatives"}]}, {"year": "2016", "text": "<PERSON>, Cameroonian footballer (b. 1990)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (b. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer (b. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Australian businessman (b. 1923)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "2021", "text": "<PERSON><PERSON>, Japanese manga artist (b. 1966)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese manga artist (b. 1966)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American comic book artist and writer (b. 1954)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, American comic book artist and writer (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9rez\" title=\"<PERSON>\"><PERSON></a>, American comic book artist and writer (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_P%C3%A9rez"}]}, {"year": "2024", "text": "<PERSON>, French journalist, interviewer and host (b. 1935)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, interviewer and host (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French journalist, interviewer and host (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Australian actor (b. 1929)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}