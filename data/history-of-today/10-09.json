{"date": "October 9", "url": "https://wikipedia.org/wiki/October_9", "data": {"Events": [{"year": "768", "text": "<PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> are crowned kings of the Franks.", "html": "768 - <a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carloman I</a> and <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are crowned kings of the Franks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Carloman_I\" title=\"Carloman I\">Carloman I</a> and <a href=\"https://wikipedia.org/wiki/Cha<PERSON>magne\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> are crowned kings of the Franks.", "links": [{"title": "Carloman I", "link": "https://wikipedia.org/wiki/Carloman_I"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}]}, {"year": "1238", "text": "<PERSON> of Aragon founds the Kingdom of Valencia.", "html": "1238 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> founds the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">Kingdom of Valencia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon\" title=\"<PERSON> of Aragon\"><PERSON> of Aragon</a> founds the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Valencia\" title=\"Kingdom of Valencia\">Kingdom of Valencia</a>.", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Aragon"}, {"title": "Kingdom of Valencia", "link": "https://wikipedia.org/wiki/Kingdom_of_Valencia"}]}, {"year": "1410", "text": "The first known mention of the Prague astronomical clock.", "html": "1410 - The first known mention of the <a href=\"https://wikipedia.org/wiki/Prague_astronomical_clock\" title=\"Prague astronomical clock\">Prague astronomical clock</a>.", "no_year_html": "The first known mention of the <a href=\"https://wikipedia.org/wiki/Prague_astronomical_clock\" title=\"Prague astronomical clock\">Prague astronomical clock</a>.", "links": [{"title": "Prague astronomical clock", "link": "https://wikipedia.org/wiki/Prague_astronomical_clock"}]}, {"year": "1446", "text": "The Hangul alphabet is published in Korea.", "html": "1446 - The <a href=\"https://wikipedia.org/wiki/Hangul\" title=\"Hangul\">Hangul</a> alphabet is published in Korea.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hangul\" title=\"Hangul\">Hangul</a> alphabet is published in Korea.", "links": [{"title": "Hangul", "link": "https://wikipedia.org/wiki/Hangul"}]}, {"year": "1594", "text": "Troops of the Portuguese Empire are defeated on Sri Lanka, bringing an end to the Campaign of Danture.", "html": "1594 - Troops of the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese Empire</a> are defeated on Sri Lanka, bringing an end to the <a href=\"https://wikipedia.org/wiki/Campaign_of_Danture\" title=\"Campaign of Danture\">Campaign of Danture</a>.", "no_year_html": "Troops of the <a href=\"https://wikipedia.org/wiki/Portuguese_Empire\" title=\"Portuguese Empire\">Portuguese Empire</a> are defeated on Sri Lanka, bringing an end to the <a href=\"https://wikipedia.org/wiki/Campaign_of_Danture\" title=\"Campaign of Danture\">Campaign of Danture</a>.", "links": [{"title": "Portuguese Empire", "link": "https://wikipedia.org/wiki/Portuguese_Empire"}, {"title": "Campaign of Danture", "link": "https://wikipedia.org/wiki/Campaign_of_Danture"}]}, {"year": "1604", "text": "<PERSON><PERSON>'s Supernova is the most recent supernova to be observed within the Milky Way.", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%27s_Supernova\" title=\"<PERSON><PERSON>'s Supernova\"><PERSON><PERSON>'s Supernova</a> is the most recent supernova to be observed within the <a href=\"https://wikipedia.org/wiki/Milky_Way\" title=\"Milky Way\">Milky Way</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ke<PERSON>%27s_Supernova\" title=\"<PERSON><PERSON>'s Supernova\"><PERSON><PERSON>'s Supernova</a> is the most recent supernova to be observed within the <a href=\"https://wikipedia.org/wiki/Milky_Way\" title=\"Milky Way\">Milky Way</a>.", "links": [{"title": "<PERSON><PERSON>'s Supernova", "link": "https://wikipedia.org/wiki/Kepler%27s_Supernova"}, {"title": "Milky Way", "link": "https://wikipedia.org/wiki/Milky_Way"}]}, {"year": "1635", "text": "<PERSON> is banished from the Massachusetts Bay Colony after religious and policy disagreements.", "html": "1635 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is banished from the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> after religious and policy disagreements.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is banished from the <a href=\"https://wikipedia.org/wiki/Massachusetts_Bay_Colony\" title=\"Massachusetts Bay Colony\">Massachusetts Bay Colony</a> after religious and policy disagreements.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Massachusetts Bay Colony", "link": "https://wikipedia.org/wiki/Massachusetts_Bay_Colony"}]}, {"year": "1701", "text": "The Collegiate School of Connecticut (later renamed Yale University) is chartered in Old Saybrook.", "html": "1701 - The Collegiate School of Connecticut (later renamed <a href=\"https://wikipedia.org/wiki/Yale_University\" title=\"Yale University\">Yale University</a>) is chartered in <a href=\"https://wikipedia.org/wiki/Old_Saybrook,_Connecticut\" title=\"Old Saybrook, Connecticut\">Old Saybrook</a>.", "no_year_html": "The Collegiate School of Connecticut (later renamed <a href=\"https://wikipedia.org/wiki/Yale_University\" title=\"Yale University\">Yale University</a>) is chartered in <a href=\"https://wikipedia.org/wiki/Old_Saybrook,_Connecticut\" title=\"Old Saybrook, Connecticut\">Old Saybrook</a>.", "links": [{"title": "Yale University", "link": "https://wikipedia.org/wiki/Yale_University"}, {"title": "Old Saybrook, Connecticut", "link": "https://wikipedia.org/wiki/Old_Saybrook,_Connecticut"}]}, {"year": "1708", "text": "<PERSON> the Great defeats the Swedes at the Battle of Lesnaya.", "html": "1708 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats the Swedes at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lesnaya\" title=\"Battle of Lesnaya\">Battle of Lesnaya</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a> defeats the Swedes at the <a href=\"https://wikipedia.org/wiki/Battle_of_Lesnaya\" title=\"Battle of Lesnaya\">Battle of Lesnaya</a>.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "Battle of Lesnaya", "link": "https://wikipedia.org/wiki/Battle_of_Lesnaya"}]}, {"year": "1740", "text": "Dutch colonists and Javanese natives begin a massacre of the ethnic Chinese population in Batavia, eventually killing at least 10,000.", "html": "1740 - Dutch colonists and Javanese natives begin a <a href=\"https://wikipedia.org/wiki/1740_Batavia_massacre\" title=\"1740 Batavia massacre\">massacre</a> of the ethnic Chinese population in <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a>, eventually killing at least 10,000.", "no_year_html": "Dutch colonists and Javanese natives begin a <a href=\"https://wikipedia.org/wiki/1740_Batavia_massacre\" title=\"1740 Batavia massacre\">massacre</a> of the ethnic Chinese population in <a href=\"https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies\" title=\"Batavia, Dutch East Indies\">Batavia</a>, eventually killing at least 10,000.", "links": [{"title": "1740 Batavia massacre", "link": "https://wikipedia.org/wiki/1740_Batavia_massacre"}, {"title": "Batavia, Dutch East Indies", "link": "https://wikipedia.org/wiki/Batavia,_Dutch_East_Indies"}]}, {"year": "1760", "text": "Seven Years' War: Russian and Austrian troops briefly occupy Berlin.", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: Russian and Austrian troops <a href=\"https://wikipedia.org/wiki/Raid_on_Berlin\" title=\"Raid on Berlin\">briefly occupy Berlin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Seven_Years%27_War\" title=\"Seven Years' War\">Seven Years' War</a>: Russian and Austrian troops <a href=\"https://wikipedia.org/wiki/Raid_on_Berlin\" title=\"Raid on Berlin\">briefly occupy Berlin</a>.", "links": [{"title": "Seven Years' War", "link": "https://wikipedia.org/wiki/Seven_Years%27_War"}, {"title": "Raid on Berlin", "link": "https://wikipedia.org/wiki/Raid_on_Berlin"}]}, {"year": "1779", "text": "American Revolutionary War: A combined Franco-American assault on British defenses during the Siege of Savannah is repulsed with heavy casualties.", "html": "1779 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A combined <a href=\"https://wikipedia.org/wiki/Franco-American_alliance\" title=\"Franco-American alliance\">Franco-American</a> assault on British defenses during the <a href=\"https://wikipedia.org/wiki/Siege_of_Savannah\" title=\"Siege of Savannah\">Siege of Savannah</a> is repulsed with heavy casualties.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: A combined <a href=\"https://wikipedia.org/wiki/Franco-American_alliance\" title=\"Franco-American alliance\">Franco-American</a> assault on British defenses during the <a href=\"https://wikipedia.org/wiki/Siege_of_Savannah\" title=\"Siege of Savannah\">Siege of Savannah</a> is repulsed with heavy casualties.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Franco-American alliance", "link": "https://wikipedia.org/wiki/Franco-American_alliance"}, {"title": "Siege of Savannah", "link": "https://wikipedia.org/wiki/Siege_of_Savannah"}]}, {"year": "1790", "text": "A severe earthquake in northern Algeria causes severe damage and a tsunami in the Mediterranean Sea and kills three thousand.", "html": "1790 - A severe <a href=\"https://wikipedia.org/wiki/1790_Oran_earthquake\" title=\"1790 Oran earthquake\">earthquake</a> in northern Algeria causes severe damage and a tsunami in the Mediterranean Sea and kills three thousand.", "no_year_html": "A severe <a href=\"https://wikipedia.org/wiki/1790_Oran_earthquake\" title=\"1790 Oran earthquake\">earthquake</a> in northern Algeria causes severe damage and a tsunami in the Mediterranean Sea and kills three thousand.", "links": [{"title": "1790 Oran earthquake", "link": "https://wikipedia.org/wiki/1790_Oran_earthquake"}]}, {"year": "1799", "text": "HMS Lutine sinks with the loss of 240 men and a cargo worth £1,200,000.", "html": "1799 - <a href=\"https://wikipedia.org/wiki/HMS_<PERSON><PERSON>_(1779)\" title=\"HMS Lutine (1779)\">HMS <i><PERSON><PERSON></i></a> sinks with the loss of 240 men and a cargo worth £1,200,000.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_<PERSON><PERSON>_(1779)\" title=\"HMS Lutine (1779)\">HMS <i><PERSON><PERSON></i></a> sinks with the loss of 240 men and a cargo worth £1,200,000.", "links": [{"title": "HMS <PERSON>tine (1779)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1779)"}]}, {"year": "1804", "text": "Hobart, capital of Tasmania, is founded.", "html": "1804 - <a href=\"https://wikipedia.org/wiki/Hobart\" title=\"Hobart\">Hobart</a>, capital of Tasmania, is founded.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hobart\" title=\"Hobart\">Hobart</a>, capital of Tasmania, is founded.", "links": [{"title": "Hobart", "link": "https://wikipedia.org/wiki/Hobart"}]}, {"year": "1806", "text": "Prussia begins the War of the Fourth Coalition against France.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> begins the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a> against <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussia</a> begins the <a href=\"https://wikipedia.org/wiki/War_of_the_Fourth_Coalition\" title=\"War of the Fourth Coalition\">War of the Fourth Coalition</a> against <a href=\"https://wikipedia.org/wiki/First_French_Empire\" title=\"First French Empire\">France</a>.", "links": [{"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}, {"title": "War of the Fourth Coalition", "link": "https://wikipedia.org/wiki/War_of_the_Fourth_Coalition"}, {"title": "First French Empire", "link": "https://wikipedia.org/wiki/First_French_Empire"}]}, {"year": "1812", "text": "War of 1812: In a naval engagement on Lake Erie, American forces capture two British ships: HMS Detroit and HMS Caledonia.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: In a naval engagement on Lake Erie, American forces capture two British ships: <a href=\"https://wikipedia.org/wiki/HMS_Detroit_(1812)\" title=\"HMS Detroit (1812)\">HMS <i>Detroit</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Caledonia_(1807)\" title=\"HMS Caledonia (1807)\">HMS <i>Caledonia</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: In a naval engagement on Lake Erie, American forces capture two British ships: <a href=\"https://wikipedia.org/wiki/HMS_Detroit_(1812)\" title=\"HMS Detroit (1812)\">HMS <i>Detroit</i></a> and <a href=\"https://wikipedia.org/wiki/HMS_Caledonia_(1807)\" title=\"HMS Caledonia (1807)\">HMS <i>Caledonia</i></a>.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "HMS Detroit (1812)", "link": "https://wikipedia.org/wiki/HMS_Detroit_(1812)"}, {"title": "HMS Caledonia (1807)", "link": "https://wikipedia.org/wiki/HMS_Caledonia_(1807)"}]}, {"year": "1820", "text": "Guayaquil declares independence from Spain.", "html": "1820 - <a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a> declares independence from Spain.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Guayaquil\" title=\"Guayaquil\">Guayaquil</a> declares independence from Spain.", "links": [{"title": "Guayaquil", "link": "https://wikipedia.org/wiki/Guayaquil"}]}, {"year": "1825", "text": "Restauration arrives in New York Harbor from Norway, the first organized immigration from Norway to the United States.", "html": "1825 - <i><a href=\"https://wikipedia.org/wiki/Restauration_(ship)\" title=\"Restauration (ship)\">Restauration</a></i> arrives in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a> from Norway, the first organized immigration from Norway to the United States.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Restauration_(ship)\" title=\"Restauration (ship)\">Restauration</a></i> arrives in <a href=\"https://wikipedia.org/wiki/New_York_Harbor\" title=\"New York Harbor\">New York Harbor</a> from Norway, the first organized immigration from Norway to the United States.", "links": [{"title": "Restauration (ship)", "link": "https://wikipedia.org/wiki/Restauration_(ship)"}, {"title": "New York Harbor", "link": "https://wikipedia.org/wiki/New_York_Harbor"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, the first head of state of independent Greece, is assassinated.", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the first head of state of independent <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a>, is assassinated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, the first head of state of independent <a href=\"https://wikipedia.org/wiki/Greece\" title=\"Greece\">Greece</a>, is assassinated.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Greece", "link": "https://wikipedia.org/wiki/Greece"}]}, {"year": "1834", "text": "Opening of the Dublin and Kingstown Railway, the first public railway on the island of Ireland.", "html": "1834 - Opening of the <a href=\"https://wikipedia.org/wiki/Dublin_and_Kingstown_Railway\" title=\"Dublin and Kingstown Railway\">Dublin and Kingstown Railway</a>, the first public railway on the island of Ireland.", "no_year_html": "Opening of the <a href=\"https://wikipedia.org/wiki/Dublin_and_Kingstown_Railway\" title=\"Dublin and Kingstown Railway\">Dublin and Kingstown Railway</a>, the first public railway on the island of Ireland.", "links": [{"title": "Dublin and Kingstown Railway", "link": "https://wikipedia.org/wiki/Dublin_and_Kingstown_Railway"}]}, {"year": "1847", "text": "Slavery is abolished in the Swedish colony of Saint Barthélemy.", "html": "1847 - Slavery is abolished in the <a href=\"https://wikipedia.org/wiki/Swedish_colony_of_Saint_Barth%C3%A9lemy\" title=\"Swedish colony of Saint Barthélemy\">Swedish colony of Saint Barthélemy</a>.", "no_year_html": "Slavery is abolished in the <a href=\"https://wikipedia.org/wiki/Swedish_colony_of_Saint_Barth%C3%A9lemy\" title=\"Swedish colony of Saint Barthélemy\">Swedish colony of Saint Barthélemy</a>.", "links": [{"title": "Swedish colony of Saint Barthélemy", "link": "https://wikipedia.org/wiki/Swedish_colony_of_Saint_Barth%C3%A9lemy"}]}, {"year": "1861", "text": "American Civil War: Union troops repel a Confederate attempt to capture Fort Pickens in the Battle of Santa Rosa Island.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops repel a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> attempt to capture <a href=\"https://wikipedia.org/wiki/Fort_Pickens\" title=\"Fort Pickens\">Fort Pickens</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Santa_Rosa_Island\" title=\"Battle of Santa Rosa Island\">Battle of Santa Rosa Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> troops repel a <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> attempt to capture <a href=\"https://wikipedia.org/wiki/Fort_Pickens\" title=\"Fort Pickens\">Fort Pickens</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Santa_Rosa_Island\" title=\"Battle of Santa Rosa Island\">Battle of Santa Rosa Island</a>.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Fort Pickens", "link": "https://wikipedia.org/wiki/Fort_Pickens"}, {"title": "Battle of Santa Rosa Island", "link": "https://wikipedia.org/wiki/Battle_of_Santa_Rosa_Island"}]}, {"year": "1864", "text": "American Civil War: Union cavalrymen defeat Confederate forces at Toms Brook, Virginia during <PERSON>'s Shenandoah Valley campaign.", "html": "1864 - American Civil War: Union cavalrymen <a href=\"https://wikipedia.org/wiki/Battle_of_Tom%27s_<PERSON>\" title=\"Battle of Tom's Brook\">defeat</a> Confederate forces at <a href=\"https://wikipedia.org/wiki/Toms_Brook,_Virginia\" title=\"Toms Brook, Virginia\">Toms Brook, Virginia</a> during <a href=\"https://wikipedia.org/wiki/Valley_campaigns_of_1864#<PERSON>'s_Shenandoah_Valley_campaign_(August-October_1864)\" title=\"Valley campaigns of 1864\"><PERSON>'s Shenandoah Valley campaign</a>.", "no_year_html": "American Civil War: Union cavalrymen <a href=\"https://wikipedia.org/wiki/Battle_of_Tom%27s_<PERSON>\" title=\"Battle of Tom's Brook\">defeat</a> Confederate forces at <a href=\"https://wikipedia.org/wiki/Toms_Brook,_Virginia\" title=\"Toms Brook, Virginia\">Toms Brook, Virginia</a> during <a href=\"https://wikipedia.org/wiki/Valley_campaigns_of_1864#<PERSON>'s_Shenandoah_Valley_campaign_(August-October_1864)\" title=\"Valley campaigns of 1864\"><PERSON>'s Shenandoah Valley campaign</a>.", "links": [{"title": "Battle of Tom's Brook", "link": "https://wikipedia.org/wiki/Battle_of_Tom%27s_Brook"}, {"title": "Toms Brook, Virginia", "link": "https://wikipedia.org/wiki/Toms_Brook,_Virginia"}, {"title": "Valley campaigns of 1864", "link": "https://wikipedia.org/wiki/Valley_campaigns_of_1864#<PERSON>'s_Shenandoah_Valley_campaign_(August-October_1864)"}]}, {"year": "1873", "text": "A meeting at the U.S. Naval Academy establishes the U.S. Naval Institute.", "html": "1873 - A meeting at the U.S. Naval Academy establishes the <a href=\"https://wikipedia.org/wiki/U.S._Naval_Institute\" class=\"mw-redirect\" title=\"U.S. Naval Institute\">U.S. Naval Institute</a>.", "no_year_html": "A meeting at the U.S. Naval Academy establishes the <a href=\"https://wikipedia.org/wiki/U.S._Naval_Institute\" class=\"mw-redirect\" title=\"U.S. Naval Institute\">U.S. Naval Institute</a>.", "links": [{"title": "U.S. Naval Institute", "link": "https://wikipedia.org/wiki/U.S._Naval_Institute"}]}, {"year": "1874", "text": "The Universal Postal Union is created by the Treaty of Bern.", "html": "1874 - The <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a> is created by the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bern\" title=\"Treaty of Bern\">Treaty of Bern</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Universal_Postal_Union\" title=\"Universal Postal Union\">Universal Postal Union</a> is created by the <a href=\"https://wikipedia.org/wiki/Treaty_of_Bern\" title=\"Treaty of Bern\">Treaty of Bern</a>.", "links": [{"title": "Universal Postal Union", "link": "https://wikipedia.org/wiki/Universal_Postal_Union"}, {"title": "Treaty of Bern", "link": "https://wikipedia.org/wiki/Treaty_of_Bern"}]}, {"year": "1900", "text": "The Cook Islands become a territory of the United Kingdom.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Cook_Islands\" title=\"Cook Islands\">Cook Islands</a> become a territory of the United Kingdom.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cook_Islands\" title=\"Cook Islands\">Cook Islands</a> become a territory of the United Kingdom.", "links": [{"title": "Cook Islands", "link": "https://wikipedia.org/wiki/Cook_Islands"}]}, {"year": "1911", "text": "An accidental bomb explosion triggers the Wuchang Uprising against the Qing dynasty, beginning the Xinhai Revolution.", "html": "1911 - An accidental bomb explosion triggers the <a href=\"https://wikipedia.org/wiki/Wuchang_Uprising\" title=\"Wuchang Uprising\">Wuchang Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, beginning the <a href=\"https://wikipedia.org/wiki/1911_Revolution\" title=\"1911 Revolution\">Xinhai Revolution</a>.", "no_year_html": "An accidental bomb explosion triggers the <a href=\"https://wikipedia.org/wiki/Wuchang_Uprising\" title=\"Wuchang Uprising\">Wuchang Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a>, beginning the <a href=\"https://wikipedia.org/wiki/1911_Revolution\" title=\"1911 Revolution\">Xinhai Revolution</a>.", "links": [{"title": "Wuchang Uprising", "link": "https://wikipedia.org/wiki/Wuchang_Uprising"}, {"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "1911 Revolution", "link": "https://wikipedia.org/wiki/1911_Revolution"}]}, {"year": "1913", "text": "The steamship SS Volturno catches fire in the mid-Atlantic.", "html": "1913 - The steamship <a href=\"https://wikipedia.org/wiki/SS_Volturno_(1906)\" title=\"SS Volturno (1906)\">SS <i>Volturno</i></a> catches fire in the mid-Atlantic.", "no_year_html": "The steamship <a href=\"https://wikipedia.org/wiki/SS_Volturno_(1906)\" title=\"SS Volturno (1906)\">SS <i>Volturno</i></a> catches fire in the mid-Atlantic.", "links": [{"title": "SS Volturno (1906)", "link": "https://wikipedia.org/wiki/SS_Volturno_(1906)"}]}, {"year": "1914", "text": "World War I: The Siege of Antwerp comes to an end.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Antwerp_(1914)\" title=\"Siege of Antwerp (1914)\">Siege of Antwerp</a> comes to an end.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Antwerp_(1914)\" title=\"Siege of Antwerp (1914)\">Siege of Antwerp</a> comes to an end.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Siege of Antwerp (1914)", "link": "https://wikipedia.org/wiki/Siege_of_Antwerp_(1914)"}]}, {"year": "1918", "text": "The Finnish Parliament offers to Prince <PERSON> of <PERSON> the throne of a short-lived Kingdom of Finland.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a> offers to <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Hesse\" title=\"Prince <PERSON> of Hesse\">Prince <PERSON> of <PERSON></a> the throne of a short-lived <a href=\"https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)\" title=\"Kingdom of Finland (1918)\">Kingdom of Finland</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Parliament_of_Finland\" title=\"Parliament of Finland\">Finnish Parliament</a> offers to <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Hesse\" title=\"Prince <PERSON> of Hesse\">Prince <PERSON> of <PERSON></a> the throne of a short-lived <a href=\"https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)\" title=\"Kingdom of Finland (1918)\">Kingdom of Finland</a>.", "links": [{"title": "Parliament of Finland", "link": "https://wikipedia.org/wiki/Parliament_of_Finland"}, {"title": "Prince <PERSON> of Hesse", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_<PERSON>_Hesse"}, {"title": "Kingdom of Finland (1918)", "link": "https://wikipedia.org/wiki/Kingdom_of_Finland_(1918)"}]}, {"year": "1919", "text": "The Cincinnati Reds win the World Series, resulting in the Black Sox Scandal.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> win the <a href=\"https://wikipedia.org/wiki/1919_World_Series\" title=\"1919 World Series\">World Series</a>, resulting in the <a href=\"https://wikipedia.org/wiki/Black_Sox_Scandal\" title=\"Black Sox Scandal\">Black Sox Scandal</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Cincinnati_Reds\" title=\"Cincinnati Reds\">Cincinnati Reds</a> win the <a href=\"https://wikipedia.org/wiki/1919_World_Series\" title=\"1919 World Series\">World Series</a>, resulting in the <a href=\"https://wikipedia.org/wiki/Black_Sox_Scandal\" title=\"Black Sox Scandal\">Black Sox Scandal</a>.", "links": [{"title": "Cincinnati Reds", "link": "https://wikipedia.org/wiki/Cincinnati_Reds"}, {"title": "1919 World Series", "link": "https://wikipedia.org/wiki/1919_World_Series"}, {"title": "Black Sox Scandal", "link": "https://wikipedia.org/wiki/Black_Sox_Scandal"}]}, {"year": "1934", "text": "An Ustashe assassin kills King <PERSON> of Yugoslavia and <PERSON>, Foreign Minister of France, in Marseille.", "html": "1934 - An <a href=\"https://wikipedia.org/wiki/Ustashe\" class=\"mw-redirect\" title=\"Ustashe\">Ustashe</a> assassin kills King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Foreign Minister of France, in Marseille.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Ustashe\" class=\"mw-redirect\" title=\"Ustashe\">Ustashe</a> assassin kills King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Foreign Minister of France, in Marseille.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ustashe"}, {"title": "<PERSON> of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "Boulder Dam (later Hoover Dam) begins to generate electricity and transmit it to Los Angeles.", "html": "1936 - Boulder Dam (later <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>) begins to generate electricity and transmit it to <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "no_year_html": "Boulder Dam (later <a href=\"https://wikipedia.org/wiki/Hoover_Dam\" title=\"Hoover Dam\">Hoover Dam</a>) begins to generate electricity and transmit it to <a href=\"https://wikipedia.org/wiki/Los_Angeles\" title=\"Los Angeles\">Los Angeles</a>.", "links": [{"title": "Hoover Dam", "link": "https://wikipedia.org/wiki/Hoover_Dam"}, {"title": "Los Angeles", "link": "https://wikipedia.org/wiki/Los_Angeles"}]}, {"year": "1937", "text": "Murder of 9 Catholic priests in Zhengding, China, who protected the local population from the advancing Japanese army.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Zhengding_Missionary_Murder\" title=\"Zhengding Missionary Murder\">Murder of 9 Catholic priests</a> in <a href=\"https://wikipedia.org/wiki/Zhengding_Town\" title=\"Zhengding Town\">Zhengding</a>, China, who protected the local population from the advancing Japanese army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zhengding_Missionary_Murder\" title=\"Zhengding Missionary Murder\">Murder of 9 Catholic priests</a> in <a href=\"https://wikipedia.org/wiki/Zhengding_Town\" title=\"Zhengding Town\">Zhengding</a>, China, who protected the local population from the advancing Japanese army.", "links": [{"title": "Zhengding Missionary Murder", "link": "https://wikipedia.org/wiki/Zhengding_Missionary_Murder"}, {"title": "Zhengding Town", "link": "https://wikipedia.org/wiki/Zhengding_Town"}]}, {"year": "1941", "text": "A coup in Panama declares <PERSON> the new president.", "html": "1941 - A coup in <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> declares <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Guardia_Arango\" class=\"mw-redirect\" title=\"<PERSON>ngo\"><PERSON></a> the new president.", "no_year_html": "A coup in <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a> declares <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_la_Guardia_Arango\" class=\"mw-redirect\" title=\"<PERSON> Guard<PERSON>ngo\"><PERSON></a> the new president.", "links": [{"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "<PERSON> Guardia Arango", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Guardia_Arango"}]}, {"year": "1942", "text": "Australia's Statute of Westminster Adoption Act 1942 receives royal assent.", "html": "1942 - Australia's <a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_Adoption_Act_1942\" title=\"Statute of Westminster Adoption Act 1942\">Statute of Westminster Adoption Act 1942</a> receives royal assent.", "no_year_html": "Australia's <a href=\"https://wikipedia.org/wiki/Statute_of_Westminster_Adoption_Act_1942\" title=\"Statute of Westminster Adoption Act 1942\">Statute of Westminster Adoption Act 1942</a> receives royal assent.", "links": [{"title": "Statute of Westminster Adoption Act 1942", "link": "https://wikipedia.org/wiki/Statute_of_Westminster_Adoption_Act_1942"}]}, {"year": "1950", "text": "The Goyang Geumjeong Cave massacre in Korea begins.", "html": "1950 - The <a href=\"https://wikipedia.org/wiki/Goyang_Geumjeong_Cave_massacre\" title=\"Goyang Geumjeong Cave massacre\">Goyang Geumjeong Cave massacre</a> in Korea begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Goyang_Geumjeong_Cave_massacre\" title=\"Goyang Geumjeong Cave massacre\">Goyang Geumjeong Cave massacre</a> in Korea begins.", "links": [{"title": "Goyang Geumjeong Cave massacre", "link": "https://wikipedia.org/wiki/Goyang_Geumjeong_Cave_massacre"}]}, {"year": "1962", "text": "Uganda becomes an independent Commonwealth realm.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a> becomes an independent <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realm</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Uganda\" title=\"Uganda\">Uganda</a> becomes an independent <a href=\"https://wikipedia.org/wiki/Commonwealth_realm\" title=\"Commonwealth realm\">Commonwealth realm</a>.", "links": [{"title": "Uganda", "link": "https://wikipedia.org/wiki/Uganda"}, {"title": "Commonwealth realm", "link": "https://wikipedia.org/wiki/Commonwealth_realm"}]}, {"year": "1963", "text": "In Italy, a large landslide causes a giant wave to overtop the Vajont Dam, killing over 2,000.", "html": "1963 - In Italy, a large landslide causes a giant wave to overtop the <a href=\"https://wikipedia.org/wiki/Vajont_Dam\" title=\"Vajont Dam\">Vajont Dam</a>, killing over 2,000.", "no_year_html": "In Italy, a large landslide causes a giant wave to overtop the <a href=\"https://wikipedia.org/wiki/Vajont_Dam\" title=\"Vajont Dam\">Vajont Dam</a>, killing over 2,000.", "links": [{"title": "Vajont Dam", "link": "https://wikipedia.org/wiki/Vajont_Dam"}]}, {"year": "1966", "text": "Vietnam War: the Republic of Korea Army commits the Binh Tai Massacre.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: the <a href=\"https://wikipedia.org/wiki/Republic_of_Korea_Army\" title=\"Republic of Korea Army\">Republic of Korea Army</a> commits the <a href=\"https://wikipedia.org/wiki/Binh_Tai_Massacre\" class=\"mw-redirect\" title=\"Binh Tai Massacre\">Binh Tai Massacre</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: the <a href=\"https://wikipedia.org/wiki/Republic_of_Korea_Army\" title=\"Republic of Korea Army\">Republic of Korea Army</a> commits the <a href=\"https://wikipedia.org/wiki/Binh_Tai_Massacre\" class=\"mw-redirect\" title=\"Binh Tai Massacre\">Binh Tai Massacre</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Republic of Korea Army", "link": "https://wikipedia.org/wiki/Republic_of_Korea_Army"}, {"title": "Binh Tai Massacre", "link": "https://wikipedia.org/wiki/Binh_Tai_Massacre"}]}, {"year": "1967", "text": "A day after his capture, <PERSON> is executed for attempting to incite a revolution in Bolivia.", "html": "1967 - A day after his capture, <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Che%22_Guevara\" class=\"mw-redirect\" title='<PERSON> \"<PERSON>\" <PERSON>uevara'><PERSON> \"<PERSON>\" <PERSON></a> is executed for attempting to incite a revolution in Bolivia.", "no_year_html": "A day after his capture, <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Che%22_Guevara\" class=\"mw-redirect\" title='<PERSON> \"<PERSON>\" <PERSON>ra'><PERSON> \"<PERSON>\" <PERSON></a> is executed for attempting to incite a revolution in Bolivia.", "links": [{"title": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/Ernesto_%22Che%22_<PERSON><PERSON>ra"}]}, {"year": "1969", "text": "In Chicago, the National Guard is called in as demonstrations continue over the trial of the \"Chicago Eight\".", "html": "1969 - In <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, the <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)\" title=\"National Guard (United States)\">National Guard</a> is called in as demonstrations continue over the trial of the \"<a href=\"https://wikipedia.org/wiki/Chicago_Eight\" class=\"mw-redirect\" title=\"Chicago Eight\">Chicago Eight</a>\".", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, the <a href=\"https://wikipedia.org/wiki/National_Guard_(United_States)\" title=\"National Guard (United States)\">National Guard</a> is called in as demonstrations continue over the trial of the \"<a href=\"https://wikipedia.org/wiki/Chicago_Eight\" class=\"mw-redirect\" title=\"Chicago Eight\">Chicago Eight</a>\".", "links": [{"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "National Guard (United States)", "link": "https://wikipedia.org/wiki/National_Guard_(United_States)"}, {"title": "Chicago Eight", "link": "https://wikipedia.org/wiki/Chicago_Eight"}]}, {"year": "1970", "text": "The Khmer Republic is proclaimed in Cambodia.", "html": "1970 - The <a href=\"https://wikipedia.org/wiki/Khmer_Republic\" title=\"Khmer Republic\">Khmer Republic</a> is proclaimed in Cambodia.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Khmer_Republic\" title=\"Khmer Republic\">Khmer Republic</a> is proclaimed in Cambodia.", "links": [{"title": "Khmer Republic", "link": "https://wikipedia.org/wiki/Khmer_Republic"}]}, {"year": "1980", "text": "Pope <PERSON> greets the <PERSON><PERSON> during a private audience in Vatican City.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON> II\">Pope <PERSON> II</a> greets the <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\"><PERSON><PERSON></a> during a private audience in Vatican City.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON> II</a> greets the <a href=\"https://wikipedia.org/wiki/14th_<PERSON><PERSON>_Lama\" title=\"14th Dalai Lama\"><PERSON><PERSON></a> during a private audience in Vatican City.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "14th Dalai Lama", "link": "https://wikipedia.org/wiki/14th_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "President <PERSON> abolishes capital punishment in France.", "html": "1981 - President <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_France\" title=\"Capital punishment in France\">capital punishment in France</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> abolishes <a href=\"https://wikipedia.org/wiki/Capital_punishment_in_France\" title=\"Capital punishment in France\">capital punishment in France</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "Capital punishment in France", "link": "https://wikipedia.org/wiki/Capital_punishment_in_France"}]}, {"year": "1983", "text": "South Korean President <PERSON> survives an assassination attempt in Rangoon, Burma (present-day Yangon, Myanmar), but the blast kills 21 and injures 17 others.", "html": "1983 - South Korean President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a> survives an <a href=\"https://wikipedia.org/wiki/Rangoon_bombing\" title=\"Rangoon bombing\">assassination attempt</a> in Rangoon, Burma (present-day Yangon, Myanmar), but the blast kills 21 and injures 17 others.", "no_year_html": "South Korean President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan\" title=\"<PERSON>wan\"><PERSON></a> survives an <a href=\"https://wikipedia.org/wiki/Rangoon_bombing\" title=\"Rangoon bombing\">assassination attempt</a> in Rangoon, Burma (present-day Yangon, Myanmar), but the blast kills 21 and injures 17 others.", "links": [{"title": "<PERSON>wan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wan"}, {"title": "Rangoon bombing", "link": "https://wikipedia.org/wiki/Rangoon_bombing"}]}, {"year": "1984", "text": "The popular children's television show Thomas The Tank Engine & Friends, based on The Railway Series by the Reverend <PERSON><PERSON><PERSON>, premieres on ITV.", "html": "1984 - The popular children's television show <i><a href=\"https://wikipedia.org/wiki/<PERSON>_%26_Friends\" title=\"<PERSON> &amp; Friends\"><PERSON> Tank Engine &amp; Friends</a></i>, based on <i><a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a></i> by <a href=\"https://wikipedia.org/wiki/Wilbert_<PERSON>wdry\" title=\"Wil<PERSON> Awdry\">the Reverend <PERSON><PERSON><PERSON></a>, premieres on <a href=\"https://wikipedia.org/wiki/ITV_(TV_network)\" title=\"ITV (TV network)\">ITV</a>.", "no_year_html": "The popular children's television show <i><a href=\"https://wikipedia.org/wiki/<PERSON>_%26_Friends\" title=\"<PERSON> &amp; Friends\"><PERSON> Tank Engine &amp; Friends</a></i>, based on <i><a href=\"https://wikipedia.org/wiki/The_Railway_Series\" title=\"The Railway Series\">The Railway Series</a></i> by <a href=\"https://wikipedia.org/wiki/Wilbert_Awdry\" title=\"Wil<PERSON> Awdry\">the Reverend <PERSON><PERSON><PERSON></a>, premieres on <a href=\"https://wikipedia.org/wiki/ITV_(TV_network)\" title=\"ITV (TV network)\">ITV</a>.", "links": [{"title": "Thomas & Friends", "link": "https://wikipedia.org/wiki/<PERSON>_%26_Friends"}, {"title": "The Railway Series", "link": "https://wikipedia.org/wiki/The_Railway_Series"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>dry"}, {"title": "ITV (TV network)", "link": "https://wikipedia.org/wiki/ITV_(TV_network)"}]}, {"year": "1986", "text": "The Phantom of the Opera, eventually the second longest running musical in London, opens at Her Majesty's Theatre.", "html": "1986 - <i><a href=\"https://wikipedia.org/wiki/The_Phantom_of_the_Opera_(1986_musical)\" title=\"The Phantom of the Opera (1986 musical)\">The Phantom of the Opera</a></i>, eventually the second longest running musical in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, opens at <a href=\"https://wikipedia.org/wiki/Her_Majesty%27s_Theatre\" class=\"mw-redirect\" title=\"Her Majesty's Theatre\">Her Majesty's Theatre</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Phantom_of_the_Opera_(1986_musical)\" title=\"The Phantom of the Opera (1986 musical)\">The Phantom of the Opera</a></i>, eventually the second longest running musical in <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>, opens at <a href=\"https://wikipedia.org/wiki/Her_Majesty%27s_Theatre\" class=\"mw-redirect\" title=\"Her Majesty's Theatre\">Her Majesty's Theatre</a>.", "links": [{"title": "The Phantom of the Opera (1986 musical)", "link": "https://wikipedia.org/wiki/The_Phantom_of_the_Opera_(1986_musical)"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}, {"title": "Her Majesty's Theatre", "link": "https://wikipedia.org/wiki/Her_Majesty%27s_Theatre"}]}, {"year": "1986", "text": "Fox Broadcasting Company (FBC) launches as the fourth US television network.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Fox_Broadcasting_Company\" title=\"Fox Broadcasting Company\">Fox Broadcasting Company</a> (FBC) launches as the <a href=\"https://wikipedia.org/wiki/Fourth_television_network\" title=\"Fourth television network\">fourth US television network</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fox_Broadcasting_Company\" title=\"Fox Broadcasting Company\">Fox Broadcasting Company</a> (FBC) launches as the <a href=\"https://wikipedia.org/wiki/Fourth_television_network\" title=\"Fourth television network\">fourth US television network</a>.", "links": [{"title": "Fox Broadcasting Company", "link": "https://wikipedia.org/wiki/Fox_Broadcasting_Company"}, {"title": "Fourth television network", "link": "https://wikipedia.org/wiki/Fourth_television_network"}]}, {"year": "1992", "text": "The Peekskill meteorite, a 27.7 pounds (12.6 kg) meteorite crashed into a parked car in Peekskill, New York", "html": "1992 - The <a href=\"https://wikipedia.org/wiki/Peekskill_meteorite\" title=\"Peekskill meteorite\">Peekskill meteorite</a>, a 27.7 pounds (12.6 kg) <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> crashed into a parked car in <a href=\"https://wikipedia.org/wiki/Peekskill,_New_York\" title=\"Peekskill, New York\">Peekskill, New York</a>", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Peekskill_meteorite\" title=\"Peekskill meteorite\">Peekskill meteorite</a>, a 27.7 pounds (12.6 kg) <a href=\"https://wikipedia.org/wiki/Meteorite\" title=\"Meteorite\">meteorite</a> crashed into a parked car in <a href=\"https://wikipedia.org/wiki/Peekskill,_New_York\" title=\"Peekskill, New York\">Peekskill, New York</a>", "links": [{"title": "Peekskill meteorite", "link": "https://wikipedia.org/wiki/Peekskill_meteorite"}, {"title": "Meteorite", "link": "https://wikipedia.org/wiki/Meteorite"}, {"title": "Peekskill, New York", "link": "https://wikipedia.org/wiki/P<PERSON>skill,_New_York"}]}, {"year": "1995", "text": "An Amtrak Sunset Limited train is derailed by saboteurs near Palo Verde, Arizona.", "html": "1995 - An <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> <a href=\"https://wikipedia.org/wiki/Sunset_Limited\" title=\"Sunset Limited\">Sunset Limited</a> train is <a href=\"https://wikipedia.org/wiki/1995_Palo_Verde,_Arizona_derailment\" class=\"mw-redirect\" title=\"1995 Palo Verde, Arizona derailment\">derailed</a> by saboteurs near <a href=\"https://wikipedia.org/wiki/Palo_Verde,_Arizona\" title=\"Palo Verde, Arizona\">Palo Verde, Arizona</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/Amtrak\" title=\"Amtrak\">Amtrak</a> <a href=\"https://wikipedia.org/wiki/Sunset_Limited\" title=\"Sunset Limited\">Sunset Limited</a> train is <a href=\"https://wikipedia.org/wiki/1995_Palo_Verde,_Arizona_derailment\" class=\"mw-redirect\" title=\"1995 Palo Verde, Arizona derailment\">derailed</a> by saboteurs near <a href=\"https://wikipedia.org/wiki/Palo_Verde,_Arizona\" title=\"Palo Verde, Arizona\">Palo Verde, Arizona</a>.", "links": [{"title": "Amtrak", "link": "https://wikipedia.org/wiki/Amtrak"}, {"title": "Sunset Limited", "link": "https://wikipedia.org/wiki/Sunset_Limited"}, {"title": "1995 Palo Verde, Arizona derailment", "link": "https://wikipedia.org/wiki/1995_Palo_Verde,_Arizona_derailment"}, {"title": "Palo Verde, Arizona", "link": "https://wikipedia.org/wiki/Palo_Verde,_Arizona"}]}, {"year": "2006", "text": "North Korea conducts its first nuclear test.", "html": "2006 - North Korea conducts its <a href=\"https://wikipedia.org/wiki/2006_North_Korean_nuclear_test\" title=\"2006 North Korean nuclear test\">first nuclear test</a>.", "no_year_html": "North Korea conducts its <a href=\"https://wikipedia.org/wiki/2006_North_Korean_nuclear_test\" title=\"2006 North Korean nuclear test\">first nuclear test</a>.", "links": [{"title": "2006 North Korean nuclear test", "link": "https://wikipedia.org/wiki/2006_North_Korean_nuclear_test"}]}, {"year": "2007", "text": "The Dow Jones Industrial Average reaches its all-time high of 14,164 points before rapidly declining due to the 2007-2008 financial crises.", "html": "2007 - The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> reaches its all-time high of 14,164 points before rapidly declining due to the <a href=\"https://wikipedia.org/wiki/Financial_crisis_of_2007%E2%80%932008\" class=\"mw-redirect\" title=\"Financial crisis of 2007-2008\">2007-2008 financial crises</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> reaches its all-time high of 14,164 points before rapidly declining due to the <a href=\"https://wikipedia.org/wiki/Financial_crisis_of_2007%E2%80%932008\" class=\"mw-redirect\" title=\"Financial crisis of 2007-2008\">2007-2008 financial crises</a>.", "links": [{"title": "Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/<PERSON>_Jones_Industrial_Average"}, {"title": "Financial crisis of 2007-2008", "link": "https://wikipedia.org/wiki/Financial_crisis_of_2007%E2%80%932008"}]}, {"year": "2009", "text": "First lunar impact of NASA's Lunar Precursor Robotic Program.", "html": "2009 - First lunar impact of NASA's <a href=\"https://wikipedia.org/wiki/Lunar_Precursor_Robotic_Program\" title=\"Lunar Precursor Robotic Program\">Lunar Precursor Robotic Program</a>.", "no_year_html": "First lunar impact of NASA's <a href=\"https://wikipedia.org/wiki/Lunar_Precursor_Robotic_Program\" title=\"Lunar Precursor Robotic Program\">Lunar Precursor Robotic Program</a>.", "links": [{"title": "Lunar Precursor Robotic Program", "link": "https://wikipedia.org/wiki/Lunar_Precursor_Robotic_Program"}]}, {"year": "2012", "text": "Pakistani Taliban attempt to assassinate outspoken schoolgirl <PERSON><PERSON>.", "html": "2012 - Pakistani Taliban attempt to assassinate outspoken schoolgirl <a href=\"https://wikipedia.org/wiki/Malala_Yousafzai\" title=\"Malala Yousafzai\"><PERSON><PERSON>fzai</a>.", "no_year_html": "Pakistani Taliban attempt to assassinate outspoken schoolgirl <a href=\"https://wikipedia.org/wiki/Mal<PERSON>_Yousafzai\" title=\"Malala Yousafzai\"><PERSON><PERSON> Yousafzai</a>.", "links": [{"title": "Malala You<PERSON>fzai", "link": "https://wikipedia.org/wiki/Malala_You<PERSON>fzai"}]}, {"year": "2016", "text": "The Arakan Rohingya Salvation Army launches its first attack on Myanmar security forces along the Bangladesh-Myanmar border.", "html": "2016 - The <a href=\"https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army\" title=\"Arakan Rohingya Salvation Army\">Arakan Rohingya Salvation Army</a> launches its <a href=\"https://wikipedia.org/wiki/Northern_Rakhine_State_clashes#2016\" class=\"mw-redirect\" title=\"Northern Rakhine State clashes\">first attack</a> on <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> security forces along the <a href=\"https://wikipedia.org/wiki/Bangladesh%E2%80%93Myanmar_border\" title=\"Bangladesh-Myanmar border\">Bangladesh-Myanmar border</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army\" title=\"Arakan Rohingya Salvation Army\">Arakan Rohingya Salvation Army</a> launches its <a href=\"https://wikipedia.org/wiki/Northern_Rakhine_State_clashes#2016\" class=\"mw-redirect\" title=\"Northern Rakhine State clashes\">first attack</a> on <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a> security forces along the <a href=\"https://wikipedia.org/wiki/Bangladesh%E2%80%93Myanmar_border\" title=\"Bangladesh-Myanmar border\">Bangladesh-Myanmar border</a>.", "links": [{"title": "Arakan Rohingya Salvation Army", "link": "https://wikipedia.org/wiki/Arakan_Rohingya_Salvation_Army"}, {"title": "Northern Rakhine State clashes", "link": "https://wikipedia.org/wiki/Northern_Rakhine_State_clashes#2016"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}, {"title": "Bangladesh-Myanmar border", "link": "https://wikipedia.org/wiki/Bangladesh%E2%80%93Myanmar_border"}]}, {"year": "2019", "text": "Turkey begins its military offensive in north-eastern Syria.", "html": "2019 - Turkey begins its <a href=\"https://wikipedia.org/wiki/2019_Turkish_offensive_into_north-eastern_Syria\" title=\"2019 Turkish offensive into north-eastern Syria\">military offensive</a> in north-eastern Syria.", "no_year_html": "Turkey begins its <a href=\"https://wikipedia.org/wiki/2019_Turkish_offensive_into_north-eastern_Syria\" title=\"2019 Turkish offensive into north-eastern Syria\">military offensive</a> in north-eastern Syria.", "links": [{"title": "2019 Turkish offensive into north-eastern Syria", "link": "https://wikipedia.org/wiki/2019_Turkish_offensive_into_north-eastern_Syria"}]}, {"year": "2024", "text": "Hurricane <PERSON> makes landfall in Siesta Key, Florida, as a Category 3 hurricane, causing over $85 billion in damage only two weeks after Hurricane <PERSON><PERSON> impacted the state. ", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Hurricane_Milton\" title=\"Hurricane Milton\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Siesta_Key\" class=\"mw-redirect\" title=\"Siesta Key\">Siesta Key</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_3\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">Category 3 hurricane</a>, causing over $85 billion in damage only two weeks after <a href=\"https://wikipedia.org/wiki/Hurricane_Helene\" title=\"Hurricane Helene\">Hurricane Helene</a> impacted the state. ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Milton\" title=\"Hurricane Milton\">Hurricane <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Siesta_Key\" class=\"mw-redirect\" title=\"Siesta Key\">Siesta Key</a>, <a href=\"https://wikipedia.org/wiki/Florida\" title=\"Florida\">Florida</a>, as a <a href=\"https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_3\" class=\"mw-redirect\" title=\"Saffir-Simpson hurricane wind scale\">Category 3 hurricane</a>, causing over $85 billion in damage only two weeks after <a href=\"https://wikipedia.org/wiki/Hurricane_Helene\" title=\"Hurricane Helene\">Hurricane Helene</a> impacted the state. ", "links": [{"title": "Hurricane Milton", "link": "https://wikipedia.org/wiki/Hurricane_Milton"}, {"title": "Siesta Key", "link": "https://wikipedia.org/wiki/Siesta_Key"}, {"title": "Florida", "link": "https://wikipedia.org/wiki/Florida"}, {"title": "Saffir-Simpson hurricane wind scale", "link": "https://wikipedia.org/wiki/Saffir-Simpson_hurricane_wind_scale#Category_3"}, {"title": "Hurricane <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hurricane_<PERSON>e"}]}], "Births": [{"year": "1201", "text": "<PERSON>, French minister and theologian, founded the Collège de Sorbonne (d. 1274)", "html": "1201 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French minister and theologian, founded the <a href=\"https://wikipedia.org/wiki/Coll%C3%A8ge_de_Sorbonne\" class=\"mw-redirect\" title=\"Collège de Sorbonne\">Collège de Sorbonne</a> (d. 1274)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French minister and theologian, founded the <a href=\"https://wikipedia.org/wiki/Coll%C3%A8ge_de_Sorbonne\" class=\"mw-redirect\" title=\"Collège de Sorbonne\">Collège de Sorbonne</a> (d. 1274)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Collège de Sorbonne", "link": "https://wikipedia.org/wiki/Coll%C3%A8ge_de_Sorbonne"}]}, {"year": "1221", "text": "<PERSON><PERSON><PERSON><PERSON> <PERSON>, Italian historian and scholar (d. 1290)", "html": "1221 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> di <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Italian historian and scholar (d. 1290)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Salimben<PERSON> di <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Italian historian and scholar (d. 1290)", "links": [{"title": "Salimbene di Adam", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1261", "text": "<PERSON> of Portugal (d. 1325)", "html": "1261 - <a href=\"https://wikipedia.org/wiki/Denis_of_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1325)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denis_<PERSON>_Portugal\" title=\"<PERSON> of Portugal\"><PERSON> of Portugal</a> (d. 1325)", "links": [{"title": "Denis of Portugal", "link": "https://wikipedia.org/wiki/Denis_of_Portugal"}]}, {"year": "1328", "text": "<PERSON> of Cyprus (d. 1369)", "html": "1328 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (d. 1369)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus\" title=\"<PERSON> of Cyprus\"><PERSON> of Cyprus</a> (d. 1369)", "links": [{"title": "<PERSON> of Cyprus", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Cyprus"}]}, {"year": "1581", "text": "<PERSON>, French mathematician, poet, and scholar (d. 1638)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9ziriac\" class=\"mw-redirect\" title=\"<PERSON> Méziriac\"><PERSON></a>, French mathematician, poet, and scholar (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A9ziriac\" class=\"mw-redirect\" title=\"<PERSON> Méziriac\"><PERSON></a>, French mathematician, poet, and scholar (d. 1638)", "links": [{"title": "<PERSON>zi<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_M%C3%A9ziriac"}]}, {"year": "1586", "text": "<PERSON>, Archduke of Austria (d. 1632)", "html": "1586 - <a href=\"https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria\" title=\"<PERSON>, Archduke of Austria\"><PERSON>, Archduke of Austria</a> (d. 1632)", "links": [{"title": "<PERSON>, Archduke of Austria", "link": "https://wikipedia.org/wiki/Leopold_V,_Archduke_of_Austria"}]}, {"year": "1593", "text": "<PERSON><PERSON>, Dutch anatomist and politician (d. 1674)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch anatomist and politician (d. 1674)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch anatomist and politician (d. 1674)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1609", "text": "<PERSON>, 4th Earl of Portland, English noble (d. 1688)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Portland\" title=\"<PERSON>, 4th Earl of Portland\"><PERSON>, 4th Earl of Portland</a>, English noble (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Portland\" title=\"<PERSON>, 4th Earl of Portland\"><PERSON>, 4th Earl of Portland</a>, English noble (d. 1688)", "links": [{"title": "<PERSON>, 4th Earl of Portland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Earl_of_Portland"}]}, {"year": "1623", "text": "<PERSON>, Flemish Jesuit missionary in China (d. 1688)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit missionary in China (d. 1688)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish Jesuit missionary in China (d. 1688)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>t"}]}, {"year": "1704", "text": "<PERSON>, German mathematician, physicist, and physician (d. 1777)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, physicist, and physician (d. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician, physicist, and physician (d. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON> France (d. 1836)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"Charles <PERSON> of France\"><PERSON> of France</a> (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_<PERSON>_of_France\" title=\"Charles <PERSON> of France\"><PERSON> of France</a> (d. 1836)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_X_of_France"}]}, {"year": "1796", "text": "<PERSON> the Younger, British Egyptologist and sculptor (d. 1878)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the Younger</a>, British Egyptologist and sculptor (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_the_Younger\" title=\"<PERSON> the Younger\"><PERSON> the <PERSON></a>, British Egyptologist and sculptor (d. 1878)", "links": [{"title": "<PERSON> the Younger", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, American-Canadian abolitionist (d. 1893)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian <a href=\"https://wikipedia.org/wiki/Abolitionism\" title=\"Abolitionism\">abolitionist</a> (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian <a href=\"https://wikipedia.org/wiki/Abolitionism\" title=\"Abolitionism\">abolitionist</a> (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Abolitionism", "link": "https://wikipedia.org/wiki/Abolitionism"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON>, Finnish politician and journalist (d. 1909)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/Agat<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Me<PERSON>man\"><PERSON><PERSON><PERSON></a>, Finnish politician and journalist (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agat<PERSON>_<PERSON>\" title=\"A<PERSON><PERSON> Me<PERSON>man\"><PERSON><PERSON><PERSON></a>, Finnish politician and journalist (d. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agat<PERSON>_<PERSON><PERSON>"}]}, {"year": "1835", "text": "<PERSON>, French composer and conductor (d. 1921)", "html": "1835 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%ABns\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%ABns\" title=\"<PERSON>\"><PERSON></a>, French composer and conductor (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Camille_Saint-Sa%C3%ABns"}]}, {"year": "1837", "text": "<PERSON>, American theorist and academic (d. 1902)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and academic (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theorist and academic (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1840", "text": "<PERSON><PERSON><PERSON>, English painter (d. 1905)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Solomon\" title=\"Sime<PERSON> Solomon\"><PERSON><PERSON><PERSON></a>, English painter (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Solomon\" title=\"Sime<PERSON> Solomon\"><PERSON><PERSON><PERSON></a>, English painter (d. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>on_Solomon"}]}, {"year": "1845", "text": "<PERSON>, Swedish shipowner (d. 1918)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish shipowner (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish shipowner (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, German-Brazilian zoologist (d. 1930)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian zoologist (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian zoologist (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (d. 1919)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian-American physicist and chemist (d. 1935)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-American physicist and chemist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian-American physicist and chemist (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, French colonel (d. 1935)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French colonel (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French colonel (d. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1863", "text": "<PERSON>, Dutch-American journalist and author (d. 1930)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American journalist and author (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American journalist and author (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, British brigadier general (d. 1927)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British brigadier general (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British brigadier general (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1871", "text": "<PERSON>, Canadian archbishop (d. 1940)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian archbishop (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, Hungarian violinist and educator (d. 1944)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist and educator (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, German physicist and astronomer (d. 1916)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and astronomer (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and astronomer (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, American pharmacist and businessman, founded Walgreens (d. 1939)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Walgreens\" title=\"Walgreens\">Walgreens</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Walgreens\" title=\"Walgreen<PERSON>\">W<PERSON><PERSON>s</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Walgreens", "link": "https://wikipedia.org/wiki/Walgreens"}]}, {"year": "1874", "text": "<PERSON>, Russian archaeologist and painter (d. 1947)", "html": "1874 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian archaeologist and painter (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian archaeologist and painter (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist, poet, and activist (d. 1928)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and activist (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist, poet, and activist (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Das"}]}, {"year": "1879", "text": "<PERSON>, German physicist and academic, Nobel Prize laureate (d. 1960)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1880", "text": "<PERSON>, American baseball player (d. 1915)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, Greek-Romanian actress (d. 1956)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Romanian actress (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-Romanian actress (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1980)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Russian journalist and politician (d. 1938)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and politician (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American actor, director, producer, and screenwriter (d. 1959)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, Canadian-American evangelist, founded the International Church of the Foursquare Gospel (d. 1944)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American evangelist, founded the <a href=\"https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel\" class=\"mw-redirect\" title=\"International Church of the Foursquare Gospel\">International Church of the Foursquare Gospel</a> (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian-American evangelist, founded the <a href=\"https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel\" class=\"mw-redirect\" title=\"International Church of the Foursquare Gospel\">International Church of the Foursquare Gospel</a> (d. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "International Church of the Foursquare Gospel", "link": "https://wikipedia.org/wiki/International_Church_of_the_Foursquare_Gospel"}]}, {"year": "1892", "text": "<PERSON><PERSON>, Yugoslav novelist, poet, and short story writer, Nobel Prize laureate (d. 1975)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Andri%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav novelist, poet, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Yugoslav novelist, poet, and short story writer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ivo_Andri%C4%87"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, Brazilian author, poet, and photographer (d. 1945)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_de_Andrade\" title=\"<PERSON><PERSON><PERSON> And<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian author, poet, and photographer (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rio_de_Andrade\" title=\"<PERSON><PERSON><PERSON> And<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian author, poet, and photographer (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1<PERSON>_de_Andrade"}]}, {"year": "1895", "text": "<PERSON>, American pilot (d. 1961)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pilot (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ard"}]}, {"year": "1897", "text": "<PERSON><PERSON>, Indian lawyer and politician, 6th Chief Minister of Madras State (d. 1987)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"List of Chief Ministers of Tamil Nadu\">Chief Minister of Madras State</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, Indian lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu\" class=\"mw-redirect\" title=\"List of Chief Ministers of Tamil Nadu\">Chief Minister of Madras State</a> (d. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M._<PERSON><PERSON><PERSON>am"}, {"title": "List of Chief Ministers of Tamil Nadu", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Tamil_Nadu"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON>, Egyptian author and playwright (d. 1987)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Taw<PERSON><PERSON>_al-<PERSON>\" title=\"<PERSON>w<PERSON><PERSON> al-Ha<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Egyptian author and playwright (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taw<PERSON><PERSON>_al-<PERSON>\" title=\"<PERSON>w<PERSON><PERSON> al-Ha<PERSON>\"><PERSON><PERSON><PERSON><PERSON> al<PERSON></a>, Egyptian author and playwright (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>w<PERSON><PERSON>_al-<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American baseball player (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American historian and author (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American inventor (d. 1982)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American inventor (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Scottish-English actor and academic (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English actor and academic (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-English actor and academic (d. 1976)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alastair_Sim"}]}, {"year": "1900", "text": "<PERSON>, Lithuanian-American psychologist and academic (d. 1990)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American psychologist and academic (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Lithuanian-American psychologist and academic (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Seneca political activist and journalist (d. 1964)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Seneca political activist and journalist (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Seneca political activist and journalist (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, English cinematographer (d. 1998)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cinematographer (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American lawyer and businessman (d. 1979)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Malley\" title=\"<PERSON>\"><PERSON></a>, American lawyer and businessman (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Walter_O%27Malley"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, American photographer and journalist (d. 1985)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer and journalist (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American photographer and journalist (d. 1985)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON><PERSON><PERSON>, Senegalese poet and politician, 1st President of Senegal (d. 2001)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>or\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Senegalese poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9<PERSON>_<PERSON>\" title=\"Léopold <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Senegalese poet and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Senegal\" title=\"President of Senegal\">President of Senegal</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9opold_S%C3%A9dar_Senghor"}, {"title": "President of Senegal", "link": "https://wikipedia.org/wiki/President_of_Senegal"}]}, {"year": "1907", "text": "<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone, English academic and politician, Lord High Chancellor of Great Britain (d. 2001)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone\" title=\"<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone\"><PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone</a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Lord_Chancellor\" title=\"Lord Chancellor\">Lord High Chancellor of Great Britain</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON>, Baron <PERSON> of St Marylebone", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>,_Baron_<PERSON>_of_St_Marylebone"}, {"title": "Lord Chancellor", "link": "https://wikipedia.org/wiki/Lord_Chancellor"}]}, {"year": "1907", "text": "<PERSON>, French actor, director, and screenwriter (d. 1982)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, German SA officer (d. 1930)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a> officer (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Sturmabteilung\" title=\"Sturmabteilung\">SA</a> officer (d. 1930)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Sturmabteilung", "link": "https://wikipedia.org/wiki/Sturmabteilung"}]}, {"year": "1908", "text": "<PERSON>, Australian poet and critic (d. 1961)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and critic (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian poet and critic (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, German lieutenant (d. 1944)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (d. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American singer (d. 1975)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English archbishop (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archbishop (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American photographer (d. 2006)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American actor (d. 1985)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American academic and politician, 17th United States Secretary of Agriculture (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 17th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician, 17th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture\" title=\"United States Secretary of Agriculture\">United States Secretary of Agriculture</a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Agriculture", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Agriculture"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American author (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Belva_Plain\" title=\"Belva Plain\">Belva Plain</a>, American author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Belva_Plain\" title=\"Belva Plain\">Belva Plain</a>, American author (d. 2010)", "links": [{"title": "Belva Plain", "link": "https://wikipedia.org/wiki/Belva_Plain"}]}, {"year": "1918", "text": "<PERSON><PERSON>, American CIA officer and author (d. 2007)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/CIA\" class=\"mw-redirect\" title=\"CIA\">CIA</a> officer and author (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "CIA", "link": "https://wikipedia.org/wiki/CIA"}]}, {"year": "1918", "text": "<PERSON>, Australian air marshal (d. 2014)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)\" title=\"<PERSON> (RAAF officer)\"><PERSON></a>, Australian air marshal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)\" title=\"<PERSON> (RAAF officer)\"><PERSON></a>, Australian air marshal (d. 2014)", "links": [{"title": "<PERSON> (RAAF officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(RAAF_officer)"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Cuban-Swedish pianist, composer, and bandleader (d. 2013)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>bo_Vald%C3%A9s\" title=\"<PERSON><PERSON> Valdés\"><PERSON><PERSON></a>, Cuban-Swedish pianist, composer, and bandleader (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vald%C3%A9s\" title=\"<PERSON><PERSON> Valdés\"><PERSON><PERSON></a>, Cuban-Swedish pianist, composer, and bandleader (d. 2013)", "links": [{"title": "Bebo Valdés", "link": "https://wikipedia.org/wiki/Bebo_Vald%C3%A9s"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Norwegian author and educator (d. 1976)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Jen<PERSON>_Bj%C3%B8rne<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and educator (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jen<PERSON>_Bj%C3%B8rne<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian author and educator (d. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jens_Bj%C3%B8rne<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American saxophonist, composer, and educator (d. 2013)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and educator (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist, composer, and educator (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American actor and screenwriter (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, French director and screenwriter (d. 2002)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French director and screenwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, Polish poet and playwright (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BCewicz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and playwright (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BC<PERSON>icz\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish poet and playwright (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_R%C3%B3%C5%BCewicz"}]}, {"year": "1922", "text": "<PERSON>, Canadian political scientist and academic (d. 1997)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political scientist and academic (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian political scientist and academic (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9on_Dion"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON><PERSON>, American actor (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>yvu<PERSON>_<PERSON>\" title=\"<PERSON>yvu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yvu<PERSON>_<PERSON>\" title=\"<PERSON>yvu<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American actor (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>yvush_Finkel"}]}, {"year": "1922", "text": "<PERSON>, Cuban-American singer (d. 2010)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, English actor (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian soldier (d. 1957)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian soldier (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian soldier (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>nu<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2012)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>n"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, French actress and producer (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dani%C3%A8le_Delorme"}]}, {"year": "1927", "text": "<PERSON>, English scholar and diplomat, British Ambassador to the Netherlands (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_the_Netherlands\" class=\"mw-redirect\" title=\"List of diplomats of the United Kingdom to the Netherlands\">British Ambassador to the Netherlands</a> (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English scholar and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_the_Netherlands\" class=\"mw-redirect\" title=\"List of diplomats of the United Kingdom to the Netherlands\">British Ambassador to the Netherlands</a> (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of diplomats of the United Kingdom to the Netherlands", "link": "https://wikipedia.org/wiki/List_of_diplomats_of_the_United_Kingdom_to_the_Netherlands"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish composer and educator (d. 2016)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish composer and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish composer and educator (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American football player, lieutenant, and politician (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, lieutenant, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, lieutenant, and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, English actor (d. 2017)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2017)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1931", "text": "<PERSON>, American football player and coach (d. 2011)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and coach (d. 2011)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1933", "text": "<PERSON>, English physicist and academic, Nobel Prize laureate (d. 2017)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1933", "text": "<PERSON>, American fashion photographer (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion photographer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American fashion photographer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English soldier and cartoonist (d. 2023)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and cartoonist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and cartoonist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>idy"}]}, {"year": "1934", "text": "<PERSON>, Australian historian and author (d. 2018)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian historian and author (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, South African pianist and composer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African pianist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON> <PERSON>, Duke of Kent", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent\" title=\"Prince <PERSON>, Duke of Kent\">Prince <PERSON>, Duke of Kent</a>", "links": [{"title": "<PERSON> <PERSON>, Duke of Kent", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Kent"}]}, {"year": "1935", "text": "<PERSON>, English photographer and journalist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English actor", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, Australian politician (d. 1996)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Austrian academic and politician, 11th President of Austria", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian academic and politician, 11th <a href=\"https://wikipedia.org/wiki/President_of_Austria\" title=\"President of Austria\">President of Austria</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "President of Austria", "link": "https://wikipedia.org/wiki/President_of_Austria"}]}, {"year": "1938", "text": "<PERSON>, English journalist, author, and academic", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English journalist, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English journalist, author, and academic", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "1939", "text": "<PERSON>, English architect and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English architect and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Australian-English journalist, director, and producer (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist, director, and producer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English journalist, director, and producer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English lawyer and judge", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON> <PERSON><PERSON>, American singer-songwriter and producer (d. 1980)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American singer-songwriter and producer (d. 1980)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American soldier, pilot, and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, pilot, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English singer-songwriter, guitarist, and producer (d. 1980)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist, and producer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American baseball player and coach (d. 2023)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American broadcaster, founded C-SPAN", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/C-SPAN\" title=\"C-SPAN\">C-SPAN</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American broadcaster, founded <a href=\"https://wikipedia.org/wiki/C-SPAN\" title=\"C-SPAN\">C-SPAN</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "C-SPAN", "link": "https://wikipedia.org/wiki/C-SPAN"}]}, {"year": "1941", "text": "<PERSON>, American lawyer and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Lott\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trent_Lott\" title=\"Trent Lott\"><PERSON></a>, American lawyer and politician", "links": [{"title": "Trent Lott", "link": "https://wikipedia.org/wiki/Trent_Lott"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, political activist and founder of the Uhuru Movement", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Omali_Yeshitela\" title=\"Omali Yeshitela\"><PERSON><PERSON><PERSON></a>, political activist and founder of the <a href=\"https://wikipedia.org/wiki/Uhuru_Movement\" class=\"mw-redirect\" title=\"Uhuru Movement\">Uhuru Movement</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>mali_Yeshitela\" title=\"Omali Yeshitela\"><PERSON><PERSON><PERSON></a>, political activist and founder of the <a href=\"https://wikipedia.org/wiki/Uhuru_Movement\" class=\"mw-redirect\" title=\"Uhuru Movement\">Uhuru Movement</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yeshitela"}, {"title": "Uhuru Movement", "link": "https://wikipedia.org/wiki/Uhuru_Movement"}]}, {"year": "1942", "text": "<PERSON>, American physician and author (d. 2013)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American physician and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, American physician and author (d. 2013)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "1943", "text": "<PERSON>, American psychologist and author (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychologist and author (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English footballer and coach", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American cartoonist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cartoonist)"}]}, {"year": "1944", "text": "<PERSON>, Baroness <PERSON>, English academic and politician", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>, Baroness <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter, bass player, and producer (d. 2002)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, bass player, and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON>, American singer-songwriter, producer, and actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> He<PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Indian classical Sarod player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical <a href=\"https://wikipedia.org/wiki/Sarod\" title=\"<PERSON>rod\"><PERSON><PERSON></a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian classical <a href=\"https://wikipedia.org/wiki/Sarod\" title=\"<PERSON>rod\"><PERSON><PERSON></a> player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>rod"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, Uruguayan-Brazilian singer-songwriter (d. 1996)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Taiguara\" title=\"Taigua<PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-Brazilian singer-songwriter (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taiguara\" title=\"Taigua<PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan-Brazilian singer-songwriter (d. 1996)", "links": [{"title": "Tai<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taiguara"}]}, {"year": "1947", "text": "<PERSON>, English sculptor and painter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and painter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)\" title=\"<PERSON> (sculptor)\"><PERSON></a>, English sculptor and painter", "links": [{"title": "<PERSON> (sculptor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sculptor)"}]}, {"year": "1947", "text": "<PERSON>, French singer (d. 2018)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/France_Gall\" title=\"France Gall\">France Gall</a>, French singer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_Gall\" title=\"France Gall\">France Gall</a>, French singer (d. 2018)", "links": [{"title": "France Gall", "link": "https://wikipedia.org/wiki/France_Gall"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and judge (d. 2007)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lt<PERSON>_<PERSON>.\" title=\"<PERSON>nult<PERSON> Jr.\"><PERSON>.</a>, American lawyer and judge (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>lt<PERSON>_<PERSON>.\" title=\"<PERSON>lt<PERSON> Jr.\"><PERSON>.</a>, American lawyer and judge (d. 2007)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>."}]}, {"year": "1947", "text": "<PERSON>, American photographer and journalist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English cricketer and rugby player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" class=\"mw-redirect\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English cricketer and rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sportsman)\" class=\"mw-redirect\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English cricketer and rugby player", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sportsman)"}]}, {"year": "1949", "text": "<PERSON>, American mass murderer (d. 1992)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mass murderer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English keyboard player, songwriter, and producer (d. 2016)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English keyboard player, songwriter, and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American baseball player", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Japanese fashion designer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Japanese singer and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American academic and activist, Nobel Prize laureate", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American academic and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1951", "text": "<PERSON>, American actor, comedian, and writer", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English illustrator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English television host and manager", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English television host and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English businessman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(businessman)\" title=\"<PERSON> (businessman)\"><PERSON></a>, English businessman", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}]}, {"year": "1952", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, South African-English soprano and educator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English soprano and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English soprano and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American tennis player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English musician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, Argentine-Italian basketball coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_Ma<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine-Italian basketball coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rub%C3%A9n_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American actor and game show host", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hurley\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hurley\" title=\"<PERSON>\"><PERSON></a>, American actor and game show host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hurley"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Canadian actor, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Boomer\" title=\"Linwood Boomer\"><PERSON><PERSON> Boomer</a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Boomer\" title=\"Linwood Boomer\"><PERSON><PERSON> Boomer</a>, Canadian actor, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON> Boomer", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Boomer"}]}, {"year": "1955", "text": "<PERSON>, English runner and sportscaster", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English runner and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, English graphic designer and art director", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(graphic_designer)\" title=\"<PERSON> (graphic designer)\"><PERSON></a>, English graphic designer and art director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(graphic_designer)\" title=\"<PERSON> (graphic designer)\"><PERSON></a>, English graphic designer and art director", "links": [{"title": "<PERSON> (graphic designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(graphic_designer)"}]}, {"year": "1957", "text": "<PERSON>, American businessman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ber"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Jamaican singer-songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Jamaican singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Cuban-American singer-songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n\" title=\"<PERSON>\"><PERSON></a>, Cuban-American singer-songwriter and producer", "links": [{"title": "Al <PERSON>n", "link": "https://wikipedia.org/wiki/Al_Jourgensen"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician (d. 2015)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ee"}]}, {"year": "1958", "text": "<PERSON>, American actor", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9"}]}, {"year": "1958", "text": "<PERSON>, American football player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Russian academic and politician, First Deputy Prime Minister of Russia (d. 2015)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian academic and politician, <a href=\"https://wikipedia.org/wiki/First_Deputy_Prime_Minister_of_Russia\" title=\"First Deputy Prime Minister of Russia\">First Deputy Prime Minister of Russia</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian academic and politician, <a href=\"https://wikipedia.org/wiki/First_Deputy_Prime_Minister_of_Russia\" title=\"First Deputy Prime Minister of Russia\">First Deputy Prime Minister of Russia</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "First Deputy Prime Minister of Russia", "link": "https://wikipedia.org/wiki/First_Deputy_Prime_Minister_of_Russia"}]}, {"year": "1960", "text": "<PERSON>, American saxophonist and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English race car driver and sportscaster", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver and sportscaster", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>(musician)"}]}, {"year": "1961", "text": "<PERSON>, American actress, director, and producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Argentinian footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, New Zealand race car driver", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English soldier and politician, Minister for Sport and the Olympics", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics\" class=\"mw-redirect\" title=\"Minister for Sport and the Olympics\">Minister for Sport and the Olympics</a>", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>(politician)"}, {"title": "Minister for Sport and the Olympics", "link": "https://wikipedia.org/wiki/Minister_for_Sport_and_the_Olympics"}]}, {"year": "1962", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 62nd Yo<PERSON>zuna", "html": "1962 - <a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 62nd <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yokozuna (sumo)\"><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%8C<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 62nd <a href=\"https://wikipedia.org/wiki/Yokozuna_(sumo)\" class=\"mw-redirect\" title=\"Yoko<PERSON>na (sumo)\"><PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%8C<PERSON>ku<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (sumo)", "link": "https://wikipedia.org/wiki/Yokozuna_(sumo)"}]}, {"year": "1963", "text": "<PERSON>, English rugby league player", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Mexican-American director, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guillermo del Toro\"><PERSON></a>, Mexican-American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Guillermo del Toro\"><PERSON></a>, Mexican-American director, producer, and screenwriter", "links": [{"title": "Guillermo <PERSON> Toro", "link": "https://wikipedia.org/wiki/Guillermo_<PERSON>_Toro"}]}, {"year": "1964", "text": "<PERSON>, Argentine tennis player", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Jai<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Jaite"}]}, {"year": "1965", "text": "<PERSON><PERSON>, American football player and coach", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fisher\"><PERSON><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Fisher\"><PERSON><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON><PERSON> Fisher", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, English politician, Prime Minister of the United Kingdom", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1966", "text": "<PERSON>, Swedish publisher, founded Plaza Magazine", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96stlund\" title=\"<PERSON>\"><PERSON></a>, Swedish publisher, founded <i>Plaza Magazine</i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%96stlund\" title=\"<PERSON>\"><PERSON></a>, Swedish publisher, founded <i>Plaza Magazine</i>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%96stlund"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian tennis player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian tennis player", "links": [{"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American wrestler (d. 2005)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Romanian footballer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1967)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born 1967)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer,_born_1967)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born 1967)\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Romanian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> (footballer, born 1967)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer,_born_1967)"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Welsh businessman and politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh businessman and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indian politician", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ramadoss\" title=\"Anbu<PERSON> Ramadoss\"><PERSON><PERSON><PERSON></a>, Indian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ramadoss\" title=\"Anbu<PERSON> Ramadoss\"><PERSON><PERSON><PERSON></a>, Indian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1969", "text": "<PERSON>, Australian rugby league player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Australian archer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian archer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian archer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English musician, singer-songwriter, writer, poet, and composer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician, singer-songwriter, writer, poet, and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English musician, singer-songwriter, writer, poet, and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian figure skater and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English songwriter and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, English director, producer, and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(director)"}]}, {"year": "1970", "text": "<PERSON>, American basketball player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1970", "text": "<PERSON>, American composer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, Swedish golfer and architect", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Ann<PERSON>_S%C3%B6renstam\" title=\"<PERSON><PERSON>sta<PERSON>\"><PERSON><PERSON></a>, Swedish golfer and architect", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ann<PERSON>_S%C3%B6renstam\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish golfer and architect", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Annika_S%C3%B6renstam"}]}, {"year": "1971", "text": "<PERSON>, Australian rugby league player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Welsh singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Welsh singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor, television host and musician", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television host and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, television host and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Italian singer-songwriter and keyboard player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian singer-songwriter and keyboard player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1974", "text": "<PERSON>, American basketball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, American rabbi", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rabbi", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rabbi", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian violinist", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian violinist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian violinist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American singer-songwriter, guitarist, producer, and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American author and educator", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1976", "text": "<PERSON>, American football player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Scottish footballer and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Turkish journalist and politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/%C3%96zlem_T%C3%BCrk%C3%B6ne\" class=\"mw-redirect\" title=\"<PERSON>zlem Türköne\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96zlem_T%C3%BCrk%C3%B6ne\" class=\"mw-redirect\" title=\"<PERSON><PERSON>m Türköne\"><PERSON><PERSON><PERSON></a>, Turkish journalist and politician", "links": [{"title": "Özlem Türköne", "link": "https://wikipedia.org/wiki/%C3%96zlem_T%C3%BCrk%C3%B6ne"}]}, {"year": "1976", "text": "<PERSON>, American actor and comedian", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ardi"}]}, {"year": "1977", "text": "<PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1978", "text": "<PERSON>, Irish singer-songwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player and coach", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American singer-songwriter, producer, and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian race car driver", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, American rapper, singer, songwriter, record producer, and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Le<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, singer, songwriter, record producer, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American rapper, singer, songwriter, record producer, and actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>rae"}]}, {"year": "1979", "text": "<PERSON>, Irish actor, producer, and screenwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dowd\" title=\"<PERSON>\"><PERSON></a>, Irish actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dowd\" title=\"<PERSON>\"><PERSON></a>, Irish actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Dowd"}]}, {"year": "1979", "text": "<PERSON>, American model and actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Uruguayan footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Go<PERSON>lo_<PERSON>do\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Go<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>do"}]}, {"year": "1980", "text": "<PERSON>, Ugandan social worker and politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ugandan social worker and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Polish actor", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Thami_Tsolekile\" title=\"Thami Tsolekile\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thami_Tsolekile\" title=\"Tham<PERSON> Tsolekile\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thami_Tsolekile"}]}, {"year": "1980", "text": "<PERSON>, Swedish ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American basketball player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, South African cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, South Korean weightlifter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ran\" title=\"<PERSON>ran\"><PERSON></a>, South Korean weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-ran\" title=\"<PERSON>\"><PERSON></a>, South Korean weightlifter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-ran"}]}, {"year": "1983", "text": "<PERSON>, Austrian race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Japanese author and television presenter", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author and television presenter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese author and television presenter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American swimmer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French swimmer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON>, Norwegian businessman and politician", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, French footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American basketball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, British tennis player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n\" title=\"<PERSON>n\"><PERSON></a>, British tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n\" title=\"<PERSON>haran\"><PERSON></a>, British tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>n"}]}, {"year": "1987", "text": "<PERSON>, American basketball player ", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player ", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Dominican baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Starling_Marte\" title=\"Starling Marte\"><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Starling_Marte\" title=\"Starling Marte\"><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ling_Marte"}]}, {"year": "1988", "text": "<PERSON>, Australian rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_league)"}]}, {"year": "1989", "text": "<PERSON>, New Zealand rugby league player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German-Slovene footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Slovene footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Slovene footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American baseball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American soccer player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Armenian tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American tennis player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Australian rugby league player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American football player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, American singer and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, Filipino-American chess grandmaster", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American chess grandmaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American chess grandmaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Canadian actress", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo<PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Filipino-American actor", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American model", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hadid\"><PERSON></a>, American model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American singer-songwriter.", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Canadian swimmer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian swimmer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American tennis player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "680", "text": "<PERSON><PERSON><PERSON>, Frankish anchorite and saint", "html": "680 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>lain\" title=\"<PERSON>lain\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Anchorite\" title=\"Anchorite\">anchorite</a> and saint", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>, <PERSON><PERSON> <a href=\"https://wikipedia.org/wiki/Anchorite\" title=\"Anchorite\">anchorite</a> and saint", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}, {"title": "Anchorite", "link": "https://wikipedia.org/wiki/Anchorite"}]}, {"year": "892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Persian scholar and hadith compiler (b. 824)", "html": "892 - <a href=\"https://wikipedia.org/wiki/Al-Tirmidhi\" title=\"Al-Tirmidhi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian <a href=\"https://wikipedia.org/wiki/Ulama\" title=\"<PERSON>lam<PERSON>\">scholar</a> and <a href=\"https://wikipedia.org/wiki/Hadith\" title=\"Hadith\">hadith</a> compiler (b. 824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al-Tirmidhi\" title=\"Al-Tirmidhi\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Persian <a href=\"https://wikipedia.org/wiki/Ulama\" title=\"Ulam<PERSON>\">scholar</a> and <a href=\"https://wikipedia.org/wiki/Hadith\" title=\"Hadith\">hadith</a> compiler (b. 824)", "links": [{"title": "Al-Tirmidhi", "link": "https://wikipedia.org/wiki/Al-Tirmidhi"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ulama"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hadith"}]}, {"year": "1047", "text": "<PERSON>", "html": "1047 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_II\" title=\"<PERSON> Clement II\"><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Clement_II\" title=\"Pope Clement II\"><PERSON></a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1212", "text": "<PERSON> of Namur, Marquis of Namur (b. 1175)", "html": "1212 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Namur\" title=\"<PERSON> of Namur\"><PERSON> of Namur</a>, Marquis of Namur (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Namur\" title=\"<PERSON> of Namur\"><PERSON> of Namur</a>, Marquis of Namur (b. 1175)", "links": [{"title": "<PERSON> of Namur", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1253", "text": "<PERSON>, English bishop and philosopher (b. 1175)", "html": "1253 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1175)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bishop and philosopher (b. 1175)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1273", "text": "<PERSON> of Bavaria, Queen of Germany (b. 1227)", "html": "1273 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Queen_of_Germany\" title=\"<PERSON> of Bavaria, Queen of Germany\"><PERSON> of Bavaria, Queen of Germany</a> (b. 1227)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Bavaria,_Queen_of_Germany\" title=\"<PERSON> of Bavaria, Queen of Germany\"><PERSON> of Bavaria, Queen of Germany</a> (b. 1227)", "links": [{"title": "<PERSON> of Bavaria, Queen of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Bavaria,_Queen_of_Germany"}]}, {"year": "1296", "text": "<PERSON>, Duke of Bavaria (b. 1269)", "html": "1296 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1269)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria\" title=\"<PERSON>, Duke of Bavaria\"><PERSON>, Duke of Bavaria</a> (b. 1269)", "links": [{"title": "<PERSON>, Duke of Bavaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Bavaria"}]}, {"year": "1390", "text": "<PERSON> of Castile (b. 1358)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> of Castile</a> (b. 1358)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile\" title=\"<PERSON> of Castile\"><PERSON> of Castile</a> (b. 1358)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Castile"}]}, {"year": "1555", "text": "<PERSON><PERSON>, German academic and reformer (b. 1493)", "html": "1555 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German academic and reformer (b. 1493)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German academic and reformer (b. 1493)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1562", "text": "<PERSON><PERSON>, Italian anatomist and physician (b. 1523)", "html": "1562 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian anatomist and physician (b. 1523)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian anatomist and physician (b. 1523)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Falloppio"}]}, {"year": "1569", "text": "<PERSON> Staritsa (b. 1533)", "html": "1569 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Staritsa\" title=\"<PERSON> of Staritsa\"><PERSON> of Staritsa</a> (b. 1533)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Staritsa\" title=\"<PERSON> of Staritsa\"><PERSON> of Staritsa</a> (b. 1533)", "links": [{"title": "Vladimir of Staritsa", "link": "https://wikipedia.org/wiki/<PERSON>_of_Staritsa"}]}, {"year": "1581", "text": "<PERSON>, Spanish missionary and saint (b. 1526)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)\" title=\"<PERSON> (saint)\"><PERSON></a>, Spanish missionary and saint (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)\" title=\"<PERSON> (saint)\"><PERSON></a>, Spanish missionary and saint (b. 1526)", "links": [{"title": "<PERSON> (saint)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(saint)"}]}, {"year": "1613", "text": "<PERSON>, English poet (b. 1562)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1562)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1562)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1619", "text": "<PERSON>, Italian rabbi and merchant (b. 1561)", "html": "1619 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rabbi)\" title=\"<PERSON> (rabbi)\"><PERSON></a>, Italian rabbi and merchant (b. 1561)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rabbi)\" title=\"<PERSON> (rabbi)\"><PERSON></a>, Italian rabbi and merchant (b. 1561)", "links": [{"title": "<PERSON> (rabbi)", "link": "https://wikipedia.org/wiki/<PERSON>(rabbi)"}]}, {"year": "1691", "text": "<PERSON>, English politician (b. 1638)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician (b. 1638)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1729", "text": "<PERSON>, English physician and poet (b. 1654)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and poet (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physician and poet (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON>, French missionary and linguist (b. 1718)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and linguist (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French missionary and linguist (b. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON><PERSON><PERSON>, Lithuanian rabbi and scholar (b. 1720)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/Vilna_Gaon\" title=\"Vilna Gaon\"><PERSON><PERSON><PERSON></a>, Lithuanian rabbi and scholar (b. 1720)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vilna_Gaon\" title=\"Vilna Gaon\"><PERSON><PERSON><PERSON></a>, Lithuanian rabbi and scholar (b. 1720)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vilna_Gaon"}]}, {"year": "1806", "text": "<PERSON>, American astronomer and surveyor (b. 1731)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and surveyor (b. 1731)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American astronomer and surveyor (b. 1731)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, American lawyer and politician (b. 1777)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1777)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1777)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON>, Russian-Greek lawyer and politician, Governor of Greece (b. 1776)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">Governor of Greece</a> (b. 1776)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Greek lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece\" title=\"List of heads of state of Greece\">Governor of Greece</a> (b. 1776)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Greece", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Greece"}]}, {"year": "1873", "text": "<PERSON>, English historian and author (b. 1785)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, Dutch lawyer and politician, Prime Minister of the Netherlands (b. 1818)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands\" title=\"Prime Minister of the Netherlands\">Prime Minister of the Netherlands</a> (b. 1818)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Netherlands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Netherlands"}]}, {"year": "1900", "text": "<PERSON>, Austrian composer and conductor (b. 1843)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian composer and conductor (b. 1843)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON>, Norwegian school owner and writer (b. 1843)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian school owner and writer (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norwegian school owner and writer (b. 1843)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American businessman, founded <PERSON>'s (b. 1849)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s\" title=\"<PERSON>'s\"><PERSON>'s</a> (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s\" title=\"<PERSON>'s\"><PERSON>'s</a> (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>'s", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27s"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Russian author, poet, and critic (b. 1873)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author, poet, and critic (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian author, poet, and critic (b. 1873)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1934", "text": "<PERSON> of Yugoslavia, King of Yugoslavia also known as <PERSON> the Unifier  (b. 1888)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a>, King of Yugoslavia also known as <PERSON> the Unifier (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia\" title=\"<PERSON> of Yugoslavia\"><PERSON> of Yugoslavia</a>, King of Yugoslavia also known as <PERSON> the Unifier (b. 1888)", "links": [{"title": "<PERSON> of Yugoslavia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Yugoslavia"}]}, {"year": "1934", "text": "<PERSON>, French union leader and politician, 78th Prime Minister of France (b. 1862)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French union leader and politician, 78th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French union leader and politician, 78th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1937", "text": "<PERSON>, Grand Duke of Hesse (b. 1868)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Grand_Duke_of_Hesse\" title=\"<PERSON>, Grand Duke of Hesse\"><PERSON>, Grand Duke of Hesse</a> (b. 1868)", "links": [{"title": "<PERSON>, Grand Duke of Hesse", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duke_of_Hesse"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, English-American physician and missionary (b. 1865)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American physician and missionary (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-American physician and missionary (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer and actress (b. 1900)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actress (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actress (b. 1900)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)"}]}, {"year": "1943", "text": "<PERSON>, Dutch physicist and academic, Nobel Prize laureate (b. 1865)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Italian partisan (b. 1927)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Partisan_(military)\" title=\"Partisan (military)\">partisan</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian <a href=\"https://wikipedia.org/wiki/Partisan_(military)\" title=\"Partisan (military)\">partisan</a> (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Partisan (military)", "link": "https://wikipedia.org/wiki/Partisan_(military)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, German captain (b. 1887)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German captain (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Her<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German captain (b. 1887)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player, baseball player, and coach (b. 1877)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, baseball player, and coach (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, baseball player, and coach (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Japanese businessman and politician, 27th Japanese Minister of Finance (b. 1888)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Finance_(Japan)\" title=\"Minister of Finance (Japan)\">Japanese Minister of Finance</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Minister of Finance (Japan)", "link": "https://wikipedia.org/wiki/Minister_of_Finance_(Japan)"}]}, {"year": "1950", "text": "<PERSON>, Canadian ice hockey player and politician (b. 1895)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and politician (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Scottish-American actor (b. 1887)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-American actor (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish-American actor (b. 1887)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1955", "text": "<PERSON>, Austrian cardinal (b. 1875)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian cardinal (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American actress (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON> (b. 1876)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Pius <PERSON>\">Pope <PERSON></a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American politician (b. 1884)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American politician (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American politician (b. 1884)", "links": [{"title": "<PERSON> (South Dakota politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Japanese general and biologist (b. 1892)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Shir%C5%8D_<PERSON>hi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general and biologist (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shir%C5%8D_<PERSON>hi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese general and biologist (b. 1892)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shir%C5%8D_Ishii"}]}, {"year": "1962", "text": "<PERSON>, Slovenian chess player and engineer (b. 1885)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Milan_Vidmar\" title=\"Milan Vidmar\"><PERSON></a>, Slovenian chess player and engineer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Vidmar\" title=\"Milan Vidmar\"><PERSON></a>, Slovenian chess player and engineer (b. 1885)", "links": [{"title": "Milan Vidmar", "link": "https://wikipedia.org/wiki/Milan_Vidmar"}]}, {"year": "1967", "text": "<PERSON><PERSON>, Argentinian-Cuban physician, politician and guerrilla leader (b. 1928)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Che_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a>, Argentinian-Cuban physician, politician and guerrilla leader (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Che_Guevara\" title=\"Che Guevara\"><PERSON><PERSON></a>, Argentinian-Cuban physician, politician and guerrilla leader (b. 1928)", "links": [{"title": "<PERSON>e Guevara", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ra"}]}, {"year": "1967", "text": "<PERSON>, English chemist and academic, Nobel Prize laureate (b. 1897)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cyril <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Cyril <PERSON>\"><PERSON></a>, English chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1967", "text": "<PERSON>, French soldier and author (b. 1885)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier and author (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1967", "text": "<PERSON>, German-American fitness trainer, developed <PERSON><PERSON> (b. 1883)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American fitness trainer, developed <a href=\"https://wikipedia.org/wiki/Pilates\" title=\"Pilates\">Pilates</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American fitness trainer, developed <a href=\"https://wikipedia.org/wiki/Pilates\" title=\"Pilates\">Pilates</a> (b. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pilates", "link": "https://wikipedia.org/wiki/Pilates"}]}, {"year": "1969", "text": "<PERSON>, American baseball player (b. 1928)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actress (b. 1902)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Czech-German businessman (b. 1908)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-German businessman (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-German businessman (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Pakistani poet (b. 1910)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani poet (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German general (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general (b. 1894)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Belgian singer-songwriter and actor (b. 1929)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and actor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian singer-songwriter and actor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, German historian and physician (b. 1893)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BChlpfordt\" title=\"<PERSON>\"><PERSON></a>, German historian and physician (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%BChlpfordt\" title=\"<PERSON>\"><PERSON></a>, German historian and physician (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_M%C3%BChlpfordt"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Brazilian general and politician, 28th President of Brazil (b. 1905)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/Em%C3%AD<PERSON>_Garrastazu_M%C3%A9dici\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian general and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Em%C3%<PERSON><PERSON>_Garrastazu_M%C3%A9dici\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian general and politician, 28th <a href=\"https://wikipedia.org/wiki/President_of_Brazil\" title=\"President of Brazil\">President of Brazil</a> (b. 1905)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em%C3%ADlio_Garrastazu_M%C3%A9dici"}, {"title": "President of Brazil", "link": "https://wikipedia.org/wiki/President_of_Brazil"}]}, {"year": "1987", "text": "<PERSON>, American author, playwright, and diplomat, United States Ambassador to Italy (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, playwright, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Italy", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Italy"}]}, {"year": "1987", "text": "<PERSON>, American physician and academic, Nobel Prize laureate (b. 1892)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1988", "text": "<PERSON>, German engineer, invented the <PERSON><PERSON> engine (b. 1902)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Wankel_engine\" title=\"Wankel engine\">Wankel engine</a> (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German engineer, invented the <a href=\"https://wikipedia.org/wiki/Wankel_engine\" title=\"Wankel engine\">Wankel engine</a> (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Wankel engine", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_engine"}]}, {"year": "1989", "text": "<PERSON>, Turkish author and playwright (b. 1921)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_At%C4%B1lgan\" title=\"<PERSON>\"><PERSON></a>, Turkish author and playwright (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_At%C4%B1lgan\" title=\"<PERSON>\"><PERSON></a>, Turkish author and playwright (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_At%C4%B1lgan"}]}, {"year": "1989", "text": "<PERSON>, American journalist and author (b. 1940)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>-<PERSON>, British cricketer and politician, Prime Minister of the United Kingdom (b. 1903)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>Home\"><PERSON></a>, British cricketer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>Home\"><PERSON></a>, British cricketer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1903)", "links": [{"title": "<PERSON>-Home", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-Home"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1996", "text": "<PERSON>, American author, composer, and critic (b. 1913)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, composer, and critic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, composer, and critic (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, American vibraphone player and composer (b. 1923)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American vibraphone player and composer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American vibraphone player and composer (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Pakistani economist and scholar (b. 1914)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani economist and scholar (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani economist and scholar (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American actor (b. 1945)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, Indian-Scottish colonel, Victoria Cross recipient (b. 1918)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Scottish colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\">Victoria Cross</a> recipient (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-Scottish colonel, <a href=\"https://wikipedia.org/wiki/Victoria_Cross\" title=\"Victoria Cross\"><PERSON> Cross</a> recipient (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Victoria Cross", "link": "https://wikipedia.org/wiki/Victoria_Cross"}]}, {"year": "2001", "text": "<PERSON>, American director, producer, and choreographer (b. 1927)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and choreographer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and choreographer (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Kyrgyzstani economist and politician (b. 1931)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Sopu<PERSON>_Begaliev\" title=\"Sopubek Begaliev\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani economist and politician (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sopu<PERSON>_Begaliev\" title=\"Sopubek Begaliev\"><PERSON><PERSON><PERSON></a>, Kyrgyzstani economist and politician (b. 1931)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>galiev"}]}, {"year": "2002", "text": "<PERSON>, American director, producer, and screenwriter (b. 1924)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American author and academic (b. 1926)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>run\"><PERSON></a>, American author and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American jazz trombonist (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz trombonist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jazz trombonist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Algerian-French philosopher and academic (b. 1930)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American actor (b. 1913)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, French filmmaker (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French filmmaker (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dani%C3%A8<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French filmmaker (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dani%C3%A8le_<PERSON>t"}]}, {"year": "2006", "text": "<PERSON>, English snooker player (b. 1978)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (b. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player (b. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Indian lawyer and politician (b. 1934)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, Indian lawyer and politician (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ram"}]}, {"year": "2007", "text": "<PERSON>, American businessman, founded hungry i (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Hungry_i\" class=\"mw-redirect\" title=\"Hungry i\">hungry i</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Hungry_i\" class=\"mw-redirect\" title=\"Hungry i\">hungry i</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hungry i", "link": "https://wikipedia.org/wiki/Hungry_i"}]}, {"year": "2007", "text": "<PERSON>, American actress and singer (b. 1919)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American author and educator (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American Zen Buddhist monastic and teacher (b. 1931)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Zen\" title=\"Zen\">Zen</a> Buddhist monastic and teacher (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Zen\" title=\"Zen\">Zen</a> Buddhist monastic and teacher (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Zen", "link": "https://wikipedia.org/wiki/Zen"}]}, {"year": "2009", "text": "<PERSON><PERSON>, German footballer (b. 1934)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1934)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, French economist and physicist, Nobel Prize laureate (b. 1911)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French economist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "2011", "text": "<PERSON>, Russian ski jumper (b. 1989)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ski jumper (b. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ski jumper (b. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, American actress (b. 1992)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1992)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American basketball player (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American cinematographer (b. 1957)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cinematographer (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Nigerian educator and politician, 4th Governor of Plateau State (b. 1933)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Solomon_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Plateau_State\" class=\"mw-redirect\" title=\"Governor of Plateau State\">Governor of Plateau State</a> (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Solomon_La<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Governor_of_Plateau_State\" class=\"mw-redirect\" title=\"Governor of Plateau State\">Governor of Plateau State</a> (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Solomon_<PERSON>"}, {"title": "Governor of Plateau State", "link": "https://wikipedia.org/wiki/Governor_of_Plateau_State"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Indian actor (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Srihari\" title=\"Srihari\"><PERSON><PERSON></a>, Indian actor (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Srihari\" title=\"Srihari\"><PERSON><PERSON></a>, Indian actor (b. 1964)", "links": [{"title": "Srihari", "link": "https://wikipedia.org/wiki/Srihari"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Belgian lawyer and politician, 60th Prime Minister of Belgium (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Belgian lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\">W<PERSON><PERSON></a>, Belgian lawyer and politician, 60th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Belgium\" title=\"Prime Minister of Belgium\">Prime Minister of Belgium</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Belgium", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Belgium"}]}, {"year": "2013", "text": "<PERSON>, Polish sociologist, lawyer, and author (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish sociologist, lawyer, and author (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish sociologist, lawyer, and author (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Croatian actor and politician, 47th Mayor of Zagreb (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian actor and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Zagreb\" title=\"List of mayors of Zagreb\">Mayor of Zagreb</a> (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Di%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian actor and politician, 47th <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Zagreb\" title=\"List of mayors of Zagreb\">Mayor of Zagreb</a> (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_Buzan%C4%8Di%C4%87"}, {"title": "List of mayors of Zagreb", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Zagreb"}]}, {"year": "2014", "text": "<PERSON>, American actress and comedienne (b. 1957)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedienne (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedienne (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American poet and academic (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soprano and educator (b. 1936)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American businessman (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2015", "text": "<PERSON>, Welsh lawyer and politician, Deputy Prime Minister of the United Kingdom (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom\" title=\"Deputy Prime Minister of the United Kingdom\">Deputy Prime Minister of the United Kingdom</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Deputy Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Deputy_Prime_Minister_of_the_United_Kingdom"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Indian composer and director (b. 1944)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer and director (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian composer and director (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Polish film and theatre director (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish film and theatre director (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish film and theatre director (b. 1926)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, French actor (b. 1930)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, British-born Greek international footballer (b. 1993)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born Greek international footballer (b. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-born Greek international footballer (b. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, German footballer (b. 1950)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer (b. 1950)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Hungarian-born Holocaust survivor (b. 1923)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born Holocaust survivor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-born Holocaust survivor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Singaporean neurologist (b. 1955)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean neurologist (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean neurologist (b. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American politician (b. 1934)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Finnish conductor and composer (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish conductor and composer (b. 1944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Le<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, Indian businessman and philanthropist (b. 1937)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian businessman and philanthropist (b. 1937)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}]}}