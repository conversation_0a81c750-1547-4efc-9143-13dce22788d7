{"date": "March 10", "url": "https://wikipedia.org/wiki/March_10", "data": {"Events": [{"year": "241 BC", "text": "First Punic War: Battle of the Aegates: The Romans sink the Carthaginian fleet bringing the First Punic War to an end.", "html": "241 BC - 241 BC - <a href=\"https://wikipedia.org/wiki/First_Punic_War\" title=\"First Punic War\">First Punic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Aegates\" title=\"Battle of the Aegates\">Battle of the Aegates</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Romans</a> sink the <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthaginian</a> fleet bringing the First Punic War to an end.", "no_year_html": "241 BC - <a href=\"https://wikipedia.org/wiki/First_Punic_War\" title=\"First Punic War\">First Punic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_the_Aegates\" title=\"Battle of the Aegates\">Battle of the Aegates</a>: The <a href=\"https://wikipedia.org/wiki/Roman_Republic\" title=\"Roman Republic\">Romans</a> sink the <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthaginian</a> fleet bringing the First Punic War to an end.", "links": [{"title": "First Punic War", "link": "https://wikipedia.org/wiki/First_Punic_War"}, {"title": "Battle of the Aegates", "link": "https://wikipedia.org/wiki/Battle_of_the_Aegates"}, {"title": "Roman Republic", "link": "https://wikipedia.org/wiki/Roman_Republic"}, {"title": "Carthage", "link": "https://wikipedia.org/wiki/Carthage"}]}, {"year": "298", "text": "Roman Emperor <PERSON><PERSON> concludes his campaign in North Africa and makes a triumphal entry into Carthage.", "html": "298 - Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> concludes his campaign in <a href=\"https://wikipedia.org/wiki/Africa_(Roman_province)\" title=\"Africa (Roman province)\">North Africa</a> and makes a triumphal entry into <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>.", "no_year_html": "Roman Emperor <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> concludes his campaign in <a href=\"https://wikipedia.org/wiki/Africa_(Roman_province)\" title=\"Africa (Roman province)\">North Africa</a> and makes a triumphal entry into <a href=\"https://wikipedia.org/wiki/Carthage\" title=\"Carthage\">Carthage</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "Africa (Roman province)", "link": "https://wikipedia.org/wiki/Africa_(Roman_province)"}, {"title": "Carthage", "link": "https://wikipedia.org/wiki/Carthage"}]}, {"year": "947", "text": "The Later Han is founded by <PERSON>. He declares himself emperor.", "html": "947 - The <a href=\"https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)\" title=\"Later Han (Five Dynasties)\">Later Han</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>yuan\"><PERSON></a>. He declares himself emperor.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)\" title=\"Later Han (Five Dynasties)\">Later Han</a> is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>yuan\"><PERSON></a>. He declares himself emperor.", "links": [{"title": "Later Han (Five Dynasties)", "link": "https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liu_<PERSON>yuan"}]}, {"year": "1496", "text": "After establishing the city of Santo Domingo, <PERSON> departs for Spain, leaving his brother in command.", "html": "1496 - After establishing the city of <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> departs for Spain, leaving his brother in command.", "no_year_html": "After establishing the city of <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> departs for Spain, leaving his brother in command.", "links": [{"title": "Santo Domingo", "link": "https://wikipedia.org/wiki/Santo_Domingo"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1535", "text": "Spaniard <PERSON><PERSON>, the fourth Bishop of Panama, discovers the Galápagos Islands by chance on his way to Peru.", "html": "1535 - Spaniard <PERSON> <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Berlanga\" title=\"Tomás de Berlanga\"><PERSON><PERSON> Be<PERSON></a>, the fourth Bishop of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, discovers the <a href=\"https://wikipedia.org/wiki/Gal%C3%A1pagos_Islands\" title=\"Galápagos Islands\">Galápagos Islands</a> by chance on his way to <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "no_year_html": "Spaniard Fray <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Berlanga\" title=\"Tomás de Berlanga\"><PERSON><PERSON></a>, the fourth Bishop of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, discovers the <a href=\"https://wikipedia.org/wiki/Gal%C3%A1pagos_Islands\" title=\"Galápagos Islands\">Galápagos Islands</a> by chance on his way to <a href=\"https://wikipedia.org/wiki/Peru\" title=\"Peru\">Peru</a>.", "links": [{"title": "Tomás de Berlanga", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_de_Berlanga"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}, {"title": "Galápagos Islands", "link": "https://wikipedia.org/wiki/Gal%C3%A1pagos_Islands"}, {"title": "Peru", "link": "https://wikipedia.org/wiki/Peru"}]}, {"year": "1607", "text": "Susenyos I defeats the combined armies of <PERSON><PERSON><PERSON> and <PERSON>na Petros II at the Battle of Gol in Gojjam, making him Emperor of Ethiopia.", "html": "1607 - <a href=\"https://wikipedia.org/wiki/Susenyo<PERSON>_I\" title=\"Susenyos I\"><PERSON><PERSON>yo<PERSON> I</a> defeats the combined armies of <a href=\"https://wikipedia.org/wiki/Yaqob\" title=\"<PERSON>qob\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Abuna\" title=\"Abuna\">Abuna</a> Petros II at the Battle of Gol in <a href=\"https://wikipedia.org/wiki/Gojjam\" title=\"Gojjam\">Gojja<PERSON></a>, making him <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Susenyo<PERSON>_I\" title=\"Susenyos I\"><PERSON><PERSON>yo<PERSON> I</a> defeats the combined armies of <a href=\"https://wikipedia.org/wiki/<PERSON>qob\" title=\"<PERSON>qob\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/Abuna\" title=\"Abuna\">Abuna</a> <PERSON>ros II at the Battle of Gol in <a href=\"https://wikipedia.org/wiki/Gojjam\" title=\"Gojjam\">Gojja<PERSON></a>, making him <a href=\"https://wikipedia.org/wiki/Emperor_of_Ethiopia\" title=\"Emperor of Ethiopia\">Emperor of Ethiopia</a>.", "links": [{"title": "Susenyos I", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>s_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yaqob"}, {"title": "Abuna", "link": "https://wikipedia.org/wiki/Abuna"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gojjam"}, {"title": "Emperor of Ethiopia", "link": "https://wikipedia.org/wiki/Emperor_of_Ethiopia"}]}, {"year": "1629", "text": "<PERSON> dissolves the Parliament of England, beginning the eleven-year period known as the Personal Rule.", "html": "1629 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> I</a> dissolves the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a>, beginning the eleven-year period known as the <a href=\"https://wikipedia.org/wiki/Personal_Rule\" title=\"Personal Rule\">Personal Rule</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_England\" title=\"<PERSON> I of England\"><PERSON> I</a> dissolves the <a href=\"https://wikipedia.org/wiki/Parliament_of_England\" title=\"Parliament of England\">Parliament of England</a>, beginning the eleven-year period known as the <a href=\"https://wikipedia.org/wiki/Personal_Rule\" title=\"Personal Rule\">Personal Rule</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Parliament of England", "link": "https://wikipedia.org/wiki/Parliament_of_England"}, {"title": "Personal Rule", "link": "https://wikipedia.org/wiki/Personal_Rule"}]}, {"year": "1661", "text": "French \"Sun King\" <PERSON> begins his personal rule of France after the death of his premier, the <PERSON>.", "html": "1661 - French \"Sun King\" <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV\" title=\"Louis XIV\"><PERSON></a> begins his personal rule of <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> after the death of his premier, the <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "French \"Sun King\" <a href=\"https://wikipedia.org/wiki/<PERSON>_XIV\" title=\"Louis XIV\"><PERSON></a> begins his personal rule of <a href=\"https://wikipedia.org/wiki/France\" title=\"France\">France</a> after the death of his premier, the <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Louis XIV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "France", "link": "https://wikipedia.org/wiki/France"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1735", "text": "An agreement between <PERSON><PERSON> Shah and Russia is signed near Ganja, Azerbaijan and Russian troops are withdrawn from occupied territories.", "html": "1735 - An agreement between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and Russia is signed near <a href=\"https://wikipedia.org/wiki/Ganja,_Azerbaijan\" title=\"Ganja, Azerbaijan\">Ganja, Azerbaijan</a> and Russian troops are withdrawn from occupied territories.", "no_year_html": "An agreement between <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> and Russia is signed near <a href=\"https://wikipedia.org/wiki/Ganja,_Azerbaijan\" title=\"Ganja, Azerbaijan\">Ganja, Azerbaijan</a> and Russian troops are withdrawn from occupied territories.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Ganja, Azerbaijan", "link": "https://wikipedia.org/wiki/Ganja,_Azerbaijan"}]}, {"year": "1762", "text": "French Huguenot <PERSON>, who had been wrongly convicted of killing his son, dies after being tortured by authorities; the event inspired <PERSON><PERSON> to begin a campaign for religious tolerance and legal reform.", "html": "1762 - French <a href=\"https://wikipedia.org/wiki/Hugue<PERSON>\" class=\"mw-redirect\" title=\"Hugue<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had been wrongly convicted of killing his son, dies after being tortured by authorities; the event inspired <a href=\"https://wikipedia.org/wiki/Voltaire\" title=\"Voltaire\"><PERSON><PERSON></a> to begin a campaign for religious tolerance and legal reform.", "no_year_html": "French <a href=\"https://wikipedia.org/wiki/Huguenot\" class=\"mw-redirect\" title=\"Hugue<PERSON>\"><PERSON><PERSON><PERSON></a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, who had been wrongly convicted of killing his son, dies after being tortured by authorities; the event inspired <a href=\"https://wikipedia.org/wiki/Voltaire\" title=\"Voltaire\"><PERSON><PERSON></a> to begin a campaign for religious tolerance and legal reform.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>not"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Voltaire"}]}, {"year": "1814", "text": "Emperor <PERSON> is defeated at the Battle of Laon in France.", "html": "1814 - Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON> I</a> is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Laon\" title=\"Battle of Laon\">Battle of Laon</a> in France.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Napoleon\"><PERSON> I</a> is defeated at the <a href=\"https://wikipedia.org/wiki/Battle_of_Laon\" title=\"Battle of Laon\">Battle of Laon</a> in France.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Battle of Laon", "link": "https://wikipedia.org/wiki/Battle_of_Laon"}]}, {"year": "1830", "text": "The Royal Netherlands East Indies Army is created.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">Royal Netherlands East Indies Army</a> is created.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army\" title=\"Royal Netherlands East Indies Army\">Royal Netherlands East Indies Army</a> is created.", "links": [{"title": "Royal Netherlands East Indies Army", "link": "https://wikipedia.org/wiki/Royal_Netherlands_East_Indies_Army"}]}, {"year": "1831", "text": "The French Foreign Legion is created by <PERSON>, the King of France, from the foreign regiments of the Kingdom of France.", "html": "1831 - The <a href=\"https://wikipedia.org/wiki/French_Foreign_Legion\" title=\"French Foreign Legion\">French Foreign Legion</a> is created by <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the King of France, from the foreign regiments of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/French_Foreign_Legion\" title=\"French Foreign Legion\">French Foreign Legion</a> is created by <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, the King of France, from the foreign regiments of the <a href=\"https://wikipedia.org/wiki/Kingdom_of_France\" title=\"Kingdom of France\">Kingdom of France</a>.", "links": [{"title": "French Foreign Legion", "link": "https://wikipedia.org/wiki/French_Foreign_Legion"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Kingdom of France", "link": "https://wikipedia.org/wiki/Kingdom_of_France"}]}, {"year": "1848", "text": "The Treaty of Guadalupe Hidalgo is ratified by the United States Senate, ending the Mexican-American War.", "html": "1848 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo\" title=\"Treaty of Guadalupe Hidalgo\">Treaty of Guadalupe Hidalgo</a> is ratified by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>, ending the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo\" title=\"Treaty of Guadalupe Hidalgo\">Treaty of Guadalupe Hidalgo</a> is ratified by the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a>, ending the <a href=\"https://wikipedia.org/wiki/Mexican%E2%80%93American_War\" title=\"Mexican-American War\">Mexican-American War</a>.", "links": [{"title": "Treaty of Guadalupe Hidalgo", "link": "https://wikipedia.org/wiki/Treaty_of_Guadalupe_Hidalgo"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Mexican-American War", "link": "https://wikipedia.org/wiki/Mexican%E2%80%93American_War"}]}, {"year": "1861", "text": "<PERSON> Hadj <PERSON> Tall seizes the city of Ségou, destroying the Bamana Empire of Mali.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/El_Hadj_<PERSON>_Tall\" class=\"mw-redirect\" title=\"El Hadj Umar Tall\">El Hadj <PERSON>ar Tall</a> seizes the city of <a href=\"https://wikipedia.org/wiki/S%C3%A9gou\" title=\"Ségou\">Ségou</a>, destroying the <a href=\"https://wikipedia.org/wiki/Bamana_Empire\" title=\"Bamana Empire\">Bamana Empire</a> of <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El_Hadj_<PERSON>_Tall\" class=\"mw-redirect\" title=\"El Hadj Umar Tall\">El Hadj <PERSON> Tall</a> seizes the city of <a href=\"https://wikipedia.org/wiki/S%C3%A9gou\" title=\"Ségou\">Ségou</a>, destroying the <a href=\"https://wikipedia.org/wiki/Bamana_Empire\" title=\"Bamana Empire\">Bamana Empire</a> of <a href=\"https://wikipedia.org/wiki/Mali\" title=\"Mali\">Mali</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Ségou", "link": "https://wikipedia.org/wiki/S%C3%A9gou"}, {"title": "Bamana Empire", "link": "https://wikipedia.org/wiki/Bamana_Empire"}, {"title": "Mali", "link": "https://wikipedia.org/wiki/Mali"}]}, {"year": "1873", "text": "The first Azerbaijani play, The Adventures of the Vizier of the Khan of Lenkaran, prepared by <PERSON><PERSON><PERSON><PERSON>, is performed by <PERSON><PERSON><PERSON><PERSON> and dramatist and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "html": "1873 - The first Azerbaijani play, <i>The Adventures of the Vizier of the Khan of Lenkaran</i>, prepared by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, is performed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> and dramatist and <a href=\"https://wikipedia.org/wiki/Najaf_bey_<PERSON>azirov\" title=\"Naja<PERSON> bey <PERSON>\">Naja<PERSON>-<PERSON><PERSON></a>.", "no_year_html": "The first Azerbaijani play, <i>The Adventures of the Vizier of the Khan of Lenkaran</i>, prepared by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, is performed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON><PERSON></a> and dramatist and <a href=\"https://wikipedia.org/wiki/Najaf_bey_<PERSON>azirov\" title=\"Naja<PERSON> bey <PERSON>\">Naja<PERSON>-<PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON> be<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "The first successful test of a telephone is made by <PERSON>.", "html": "1876 - The first successful <a href=\"https://wikipedia.org/wiki/Invention_of_the_telephone\" title=\"Invention of the telephone\">test of a telephone</a> is made by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "The first successful <a href=\"https://wikipedia.org/wiki/Invention_of_the_telephone\" title=\"Invention of the telephone\">test of a telephone</a> is made by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Invention of the telephone", "link": "https://wikipedia.org/wiki/Invention_of_the_telephone"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON><PERSON> patents the <PERSON><PERSON><PERSON> switch, a device which led to the automation of telephone circuit switching.", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Strowger\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Strowger\"><PERSON><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Strowger_switch\" title=\"Strowger switch\">Strowger switch</a>, a device which led to the automation of telephone circuit switching.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Strowger\"><PERSON><PERSON></a> patents the <a href=\"https://wikipedia.org/wiki/Strowger_switch\" title=\"Strowger switch\">Strowger switch</a>, a device which led to the automation of telephone circuit switching.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rowger"}, {"title": "Strowger switch", "link": "https://wikipedia.org/wiki/Strowger_switch"}]}, {"year": "1906", "text": "The Courrières mine disaster, Europe's worst ever, kills 1099 miners in northern France.", "html": "1906 - The <a href=\"https://wikipedia.org/wiki/Courri%C3%A8res_mine_disaster\" title=\"Courrières mine disaster\">Courrières mine disaster</a>, Europe's worst ever, kills 1099 miners in northern France.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Courri%C3%A8res_mine_disaster\" title=\"Courrières mine disaster\">Courrières mine disaster</a>, Europe's worst ever, kills 1099 miners in northern France.", "links": [{"title": "Courrières mine disaster", "link": "https://wikipedia.org/wiki/Courri%C3%A8res_mine_disaster"}]}, {"year": "1909", "text": "By signing the Anglo-Siamese Treaty of 1909, Thailand relinquishes its sovereignty over the Malay states of Kedah, Kelantan, Perlis and Terengganu, which become British protectorates.", "html": "1909 - By signing the <a href=\"https://wikipedia.org/wiki/Anglo-Siamese_Treaty_of_1909\" title=\"Anglo-Siamese Treaty of 1909\">Anglo-Siamese Treaty of 1909</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> relinquishes its <a href=\"https://wikipedia.org/wiki/Sovereignty\" title=\"Sovereignty\">sovereignty</a> over the <a href=\"https://wikipedia.org/wiki/Monarchies_of_Malaysia\" title=\"Monarchies of Malaysia\">Malay states</a> of <a href=\"https://wikipedia.org/wiki/Kedah\" title=\"Kedah\">Kedah</a>, <a href=\"https://wikipedia.org/wiki/Kelantan\" title=\"Kelantan\">Kelantan</a>, <a href=\"https://wikipedia.org/wiki/Perlis\" title=\"Perlis\">Perlis</a> and <a href=\"https://wikipedia.org/wiki/Terengganu\" title=\"Terengganu\">Terengganu</a>, which become <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorates</a>.", "no_year_html": "By signing the <a href=\"https://wikipedia.org/wiki/Anglo-Siamese_Treaty_of_1909\" title=\"Anglo-Siamese Treaty of 1909\">Anglo-Siamese Treaty of 1909</a>, <a href=\"https://wikipedia.org/wiki/Thailand\" title=\"Thailand\">Thailand</a> relinquishes its <a href=\"https://wikipedia.org/wiki/Sovereignty\" title=\"Sovereignty\">sovereignty</a> over the <a href=\"https://wikipedia.org/wiki/Monarchies_of_Malaysia\" title=\"Monarchies of Malaysia\">Malay states</a> of <a href=\"https://wikipedia.org/wiki/Kedah\" title=\"Kedah\">Kedah</a>, <a href=\"https://wikipedia.org/wiki/Kelantan\" title=\"Kelantan\">Kelantan</a>, <a href=\"https://wikipedia.org/wiki/Perlis\" title=\"Perlis\">Perlis</a> and <a href=\"https://wikipedia.org/wiki/Terengganu\" title=\"Terengganu\">Terengganu</a>, which become <a href=\"https://wikipedia.org/wiki/British_protectorate\" title=\"British protectorate\">British protectorates</a>.", "links": [{"title": "Anglo-Siamese Treaty of 1909", "link": "https://wikipedia.org/wiki/Anglo-Siamese_Treaty_of_1909"}, {"title": "Thailand", "link": "https://wikipedia.org/wiki/Thailand"}, {"title": "Sovereignty", "link": "https://wikipedia.org/wiki/Sovereignty"}, {"title": "Monarchies of Malaysia", "link": "https://wikipedia.org/wiki/Monarchies_of_Malaysia"}, {"title": "Kedah", "link": "https://wikipedia.org/wiki/Kedah"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kelantan"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "Terengganu", "link": "https://wikipedia.org/wiki/Terengganu"}, {"title": "British protectorate", "link": "https://wikipedia.org/wiki/British_protectorate"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON> is arrested in India, tried for sedition, and sentenced to six years in prison, only to be released after nearly two years for an appendicitis operation.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested in India, tried for <a href=\"https://wikipedia.org/wiki/Sedition\" title=\"Sedition\">sedition</a>, and sentenced to six years in prison, only to be released after nearly two years for an <a href=\"https://wikipedia.org/wiki/Appendicitis\" title=\"Appendicitis\">appendicitis</a> operation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is arrested in India, tried for <a href=\"https://wikipedia.org/wiki/Sedition\" title=\"Sedition\">sedition</a>, and sentenced to six years in prison, only to be released after nearly two years for an <a href=\"https://wikipedia.org/wiki/Appendicitis\" title=\"Appendicitis\">appendicitis</a> operation.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Sedition", "link": "https://wikipedia.org/wiki/Sedition"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Appendicitis"}]}, {"year": "1933", "text": "The Long Beach earthquake affects the Greater Los Angeles Area, leaving around 108 people dead.", "html": "1933 - The <a href=\"https://wikipedia.org/wiki/1933_Long_Beach_earthquake\" title=\"1933 Long Beach earthquake\">Long Beach earthquake</a> affects the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles_Area\" class=\"mw-redirect\" title=\"Greater Los Angeles Area\">Greater Los Angeles Area</a>, leaving around 108 people dead.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1933_Long_Beach_earthquake\" title=\"1933 Long Beach earthquake\">Long Beach earthquake</a> affects the <a href=\"https://wikipedia.org/wiki/Greater_Los_Angeles_Area\" class=\"mw-redirect\" title=\"Greater Los Angeles Area\">Greater Los Angeles Area</a>, leaving around 108 people dead.", "links": [{"title": "1933 Long Beach earthquake", "link": "https://wikipedia.org/wiki/1933_Long_Beach_earthquake"}, {"title": "Greater Los Angeles Area", "link": "https://wikipedia.org/wiki/Greater_Los_Angeles_Area"}]}, {"year": "1944", "text": "Greek Civil War: The Political Committee of National Liberation is established in Greece by the National Liberation Front.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Political_Committee_of_National_Liberation\" title=\"Political Committee of National Liberation\">Political Committee of National Liberation</a> is established in Greece by the <a href=\"https://wikipedia.org/wiki/National_Liberation_Front_(Greece)\" title=\"National Liberation Front (Greece)\">National Liberation Front</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greek_Civil_War\" title=\"Greek Civil War\">Greek Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Political_Committee_of_National_Liberation\" title=\"Political Committee of National Liberation\">Political Committee of National Liberation</a> is established in Greece by the <a href=\"https://wikipedia.org/wiki/National_Liberation_Front_(Greece)\" title=\"National Liberation Front (Greece)\">National Liberation Front</a>.", "links": [{"title": "Greek Civil War", "link": "https://wikipedia.org/wiki/Greek_Civil_War"}, {"title": "Political Committee of National Liberation", "link": "https://wikipedia.org/wiki/Political_Committee_of_National_Liberation"}, {"title": "National Liberation Front (Greece)", "link": "https://wikipedia.org/wiki/National_Liberation_Front_(Greece)"}]}, {"year": "1945", "text": "World War II: The U.S. Army Air Force firebombs Tokyo, and the resulting conflagration kills more than 100,000 people, mostly civilians.", "html": "1945 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The U.S. Army Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Tokyo_(10_March_1945)\" title=\"Bombing of Tokyo (10 March 1945)\">firebombs Tokyo</a>, and the resulting conflagration kills more than 100,000 people, mostly civilians.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The U.S. Army Air Force <a href=\"https://wikipedia.org/wiki/Bombing_of_Tokyo_(10_March_1945)\" title=\"Bombing of Tokyo (10 March 1945)\">firebombs Tokyo</a>, and the resulting conflagration kills more than 100,000 people, mostly civilians.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Bombing of Tokyo (10 March 1945)", "link": "https://wikipedia.org/wiki/Bombing_of_Tokyo_(10_March_1945)"}]}, {"year": "1949", "text": "<PERSON><PERSON> (\"<PERSON> Sally\") is convicted of treason.", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (\"<a href=\"https://wikipedia.org/wiki/<PERSON>_Sally\" title=\"Axis Sally\"><PERSON></a>\") is convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> (\"<a href=\"https://wikipedia.org/wiki/Axis_Sally\" title=\"<PERSON> Sally\"><PERSON></a>\") is convicted of <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Axis Sally", "link": "https://wikipedia.org/wiki/<PERSON>_Sally"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON> leads a successful coup in Cuba.", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON>io Batista</a> leads a <a href=\"https://wikipedia.org/wiki/1952_Cuban_coup_d%27%C3%A9tat\" title=\"1952 Cuban coup d'état\">successful</a> <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup</a> in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fulgencio_Batista\" title=\"Fulgencio Batista\"><PERSON><PERSON><PERSON>io Batista</a> leads a <a href=\"https://wikipedia.org/wiki/1952_Cuban_coup_d%27%C3%A9tat\" title=\"1952 Cuban coup d'état\">successful</a> <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup</a> in <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fulgencio_Batista"}, {"title": "1952 Cuban coup d'état", "link": "https://wikipedia.org/wiki/1952_Cuban_coup_d%27%C3%A9tat"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1959", "text": "Tibetan uprising: Fearing an abduction attempt by China, thousands of Tibetans surround the <PERSON><PERSON>'s palace to prevent his removal.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/1959_Tibetan_uprising\" title=\"1959 Tibetan uprising\">Tibetan uprising</a>: Fearing an abduction attempt by China, thousands of <a href=\"https://wikipedia.org/wiki/Tibetan_people\" class=\"mw-redirect\" title=\"Tibetan people\">Tibetans</a> surround the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s palace to prevent his removal.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1959_Tibetan_uprising\" title=\"1959 Tibetan uprising\">Tibetan uprising</a>: Fearing an abduction attempt by China, thousands of <a href=\"https://wikipedia.org/wiki/Tibetan_people\" class=\"mw-redirect\" title=\"Tibetan people\">Tibetans</a> surround the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>'s palace to prevent his removal.", "links": [{"title": "1959 Tibetan uprising", "link": "https://wikipedia.org/wiki/1959_Tibetan_uprising"}, {"title": "Tibetan people", "link": "https://wikipedia.org/wiki/Tibetan_people"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "Military Prime Minister of South Vietnam <PERSON><PERSON><PERSON><PERSON> sacks rival General <PERSON><PERSON><PERSON><PERSON>, precipitating large-scale civil and military dissension in parts of the nation.", "html": "1966 - Military Prime Minister of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_<PERSON>_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\">Nguy<PERSON><PERSON></a> sacks rival General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%A1nh_Thi\" title=\"Nguyễ<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, precipitating <a href=\"https://wikipedia.org/wiki/Buddhist_Uprising\" title=\"Buddhist Uprising\">large-scale civil and military dissension</a> in parts of the nation.", "no_year_html": "Military Prime Minister of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a> <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_<PERSON>_K%E1%BB%B3\" title=\"Nguyễ<PERSON>\">Nguy<PERSON><PERSON></a> sacks rival General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%A1nh_Thi\" title=\"Nguyễn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, precipitating <a href=\"https://wikipedia.org/wiki/Buddhist_Uprising\" title=\"Buddhist Uprising\">large-scale civil and military dissension</a> in parts of the nation.", "links": [{"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Cao_K%E1%BB%B3"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Ch%C3%A1nh_Thi"}, {"title": "Buddhist Uprising", "link": "https://wikipedia.org/wiki/Buddhist_Uprising"}]}, {"year": "1969", "text": "In Memphis, Tennessee, <PERSON> pleads guilty to assassinating <PERSON>. He later unsuccessfully attempts to recant.", "html": "1969 - In <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to assassinating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> He later unsuccessfully attempts to recant.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Memphis,_Tennessee\" title=\"Memphis, Tennessee\">Memphis, Tennessee</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to assassinating <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> He later unsuccessfully attempts to recant.", "links": [{"title": "Memphis, Tennessee", "link": "https://wikipedia.org/wiki/Memphis,_Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1970", "text": "Vietnam War: Captain <PERSON> is charged by the U.S. military with My Lai war crimes.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged by the U.S. military with <a href=\"https://wikipedia.org/wiki/My_Lai_massacre\" title=\"My Lai massacre\">My Lai war crimes</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged by the U.S. military with <a href=\"https://wikipedia.org/wiki/My_Lai_massacre\" title=\"My Lai massacre\">My Lai war crimes</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "My Lai massacre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_massacre"}]}, {"year": "1971", "text": "<PERSON> resigns as Prime Minister of Australia and the leader of the Liberal Party of Australia after a secret ballot vote of confidence, being replaced in both positions by <PERSON>.", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> and the leader of the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Australia\" title=\"Liberal Party of Australia\">Liberal Party of Australia</a> after a secret ballot <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Australia_leadership_spill,_1971\" class=\"mw-redirect\" title=\"Liberal Party of Australia leadership spill, 1971\">vote of confidence</a>, being replaced in both positions by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> resigns as <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Australia\" title=\"Prime Minister of Australia\">Prime Minister of Australia</a> and the leader of the <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Australia\" title=\"Liberal Party of Australia\">Liberal Party of Australia</a> after a secret ballot <a href=\"https://wikipedia.org/wiki/Liberal_Party_of_Australia_leadership_spill,_1971\" class=\"mw-redirect\" title=\"Liberal Party of Australia leadership spill, 1971\">vote of confidence</a>, being replaced in both positions by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Australia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Australia"}, {"title": "Liberal Party of Australia", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Australia"}, {"title": "Liberal Party of Australia leadership spill, 1971", "link": "https://wikipedia.org/wiki/Liberal_Party_of_Australia_leadership_spill,_1971"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1974", "text": "1974 Belgian general election: Elections are held in Belgium for all 212 seats in the Chamber of Representatives, the Belgian Socialist Party taking the majority with 59.", "html": "1974 - <a href=\"https://wikipedia.org/wiki/1974_Belgian_general_election\" title=\"1974 Belgian general election\">1974 Belgian general election</a>: Elections are held in Belgium for all 212 seats in the <a href=\"https://wikipedia.org/wiki/Chamber_of_Representatives_(Belgium)\" title=\"Chamber of Representatives (Belgium)\">Chamber of Representatives</a>, the <a href=\"https://wikipedia.org/wiki/Belgian_Socialist_Party\" title=\"Belgian Socialist Party\">Belgian Socialist Party</a> taking the majority with 59.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1974_Belgian_general_election\" title=\"1974 Belgian general election\">1974 Belgian general election</a>: Elections are held in Belgium for all 212 seats in the <a href=\"https://wikipedia.org/wiki/Chamber_of_Representatives_(Belgium)\" title=\"Chamber of Representatives (Belgium)\">Chamber of Representatives</a>, the <a href=\"https://wikipedia.org/wiki/Belgian_Socialist_Party\" title=\"Belgian Socialist Party\">Belgian Socialist Party</a> taking the majority with 59.", "links": [{"title": "1974 Belgian general election", "link": "https://wikipedia.org/wiki/1974_Belgian_general_election"}, {"title": "Chamber of Representatives (Belgium)", "link": "https://wikipedia.org/wiki/Chamber_of_Representatives_(Belgium)"}, {"title": "Belgian Socialist Party", "link": "https://wikipedia.org/wiki/Belgian_Socialist_Party"}]}, {"year": "1975", "text": "Vietnam War: Ho <PERSON> Campaign: North Vietnamese troops attack Ban Mê <PERSON>t in the South on their way to capturing Saigon in the final push for victory over South Vietnam.", "html": "1975 - Vietnam War: <a href=\"https://wikipedia.org/wiki/1975_Spring_Offensive\" class=\"mw-redirect\" title=\"1975 Spring Offensive\">Ho <PERSON> Campaign</a>: <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> troops <a href=\"https://wikipedia.org/wiki/Battle_of_Ban_Me_Thuot\" title=\"Battle of Ban Me Thuot\">attack Ban Mê Thuột</a> in the South on their way to capturing <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> in the final push for victory over South Vietnam.", "no_year_html": "Vietnam War: <a href=\"https://wikipedia.org/wiki/1975_Spring_Offensive\" class=\"mw-redirect\" title=\"1975 Spring Offensive\">Ho <PERSON> Campaign</a>: <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnamese</a> troops <a href=\"https://wikipedia.org/wiki/Battle_of_Ban_Me_Thuot\" title=\"Battle of Ban Me Thuot\">attack Ban Mê Thuột</a> in the South on their way to capturing <a href=\"https://wikipedia.org/wiki/Saigon\" class=\"mw-redirect\" title=\"Saigon\">Saigon</a> in the final push for victory over South Vietnam.", "links": [{"title": "1975 Spring Offensive", "link": "https://wikipedia.org/wiki/1975_Spring_Offensive"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "Battle of Ban Me Thuot", "link": "https://wikipedia.org/wiki/Battle_of_Ban_Me_<PERSON>ot"}, {"title": "Saigon", "link": "https://wikipedia.org/wiki/Saigon"}]}, {"year": "1977", "text": "Astronomers discover the rings of Uranus.", "html": "1977 - Astronomers discover the <a href=\"https://wikipedia.org/wiki/Rings_of_Uranus\" title=\"Rings of Uranus\">rings of Uranus</a>.", "no_year_html": "Astronomers discover the <a href=\"https://wikipedia.org/wiki/Rings_of_Uranus\" title=\"Rings of Uranus\">rings of Uranus</a>.", "links": [{"title": "Rings of Uranus", "link": "https://wikipedia.org/wiki/Rings_of_Uranus"}]}, {"year": "1979", "text": "1979 International Women's Day protests in Tehran: Protestor involvement peaks with 15,000 Iranian women and girls performing a three‐hour-long sit‐in at the Courthouse of Tehran.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/1979_International_Women%27s_Day_protests_in_Tehran\" title=\"1979 International Women's Day protests in Tehran\">1979 International Women's Day protests in Tehran</a>: Protestor involvement peaks with 15,000 Iranian women and girls performing a three‐hour-long sit‐in at the <a href=\"https://wikipedia.org/wiki/Courthouse_of_Tehran\" title=\"Courthouse of Tehran\">Courthouse of Tehran</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1979_International_Women%27s_Day_protests_in_Tehran\" title=\"1979 International Women's Day protests in Tehran\">1979 International Women's Day protests in Tehran</a>: Protestor involvement peaks with 15,000 Iranian women and girls performing a three‐hour-long sit‐in at the <a href=\"https://wikipedia.org/wiki/Courthouse_of_Tehran\" title=\"Courthouse of Tehran\">Courthouse of Tehran</a>.", "links": [{"title": "1979 International Women's Day protests in Tehran", "link": "https://wikipedia.org/wiki/1979_International_Women%27s_Day_protests_in_Tehran"}, {"title": "Courthouse of Tehran", "link": "https://wikipedia.org/wiki/Courthouse_of_Tehran"}]}, {"year": "1982", "text": "Syzygy: All nine planets recognized at this time — Mercury to Pluto — align on the same side of the Sun.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Syzygy_(astronomy)\" title=\"Syzygy (astronomy)\">Syzygy</a>: All nine planets recognized at this time — <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a> to <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> — align on the same side of the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Syzygy_(astronomy)\" title=\"Syzygy (astronomy)\">Syzygy</a>: All nine planets recognized at this time — <a href=\"https://wikipedia.org/wiki/Mercury_(planet)\" title=\"Mercury (planet)\">Mercury</a> to <a href=\"https://wikipedia.org/wiki/Pluto\" title=\"Pluto\">Pluto</a> — align on the same side of the <a href=\"https://wikipedia.org/wiki/Sun\" title=\"Sun\">Sun</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (astronomy)", "link": "https://wikipedia.org/wiki/Syzygy_(astronomy)"}, {"title": "Mercury (planet)", "link": "https://wikipedia.org/wiki/Mercury_(planet)"}, {"title": "Pluto", "link": "https://wikipedia.org/wiki/Pluto"}, {"title": "Sun", "link": "https://wikipedia.org/wiki/Sun"}]}, {"year": "1989", "text": "Air Ontario Flight 1363, a Fokker F-28 Fellowship, crashes at Dryden Regional Airport in Dryden, Ontario, Canada, killing 24.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Air_Ontario_Flight_1363\" title=\"Air Ontario Flight 1363\">Air Ontario Flight 1363</a>, a <a href=\"https://wikipedia.org/wiki/Fokker_F28_Fellowship\" title=\"Fokker F28 Fellowship\">Fokker F-28 Fellowship</a>, crashes at <a href=\"https://wikipedia.org/wiki/Dryden_Regional_Airport\" title=\"Dryden Regional Airport\">Dryden Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Dryden,_Ontario\" title=\"Dryden, Ontario\">Dryden, Ontario</a>, Canada, killing 24.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Air_Ontario_Flight_1363\" title=\"Air Ontario Flight 1363\">Air Ontario Flight 1363</a>, a <a href=\"https://wikipedia.org/wiki/Fokker_F28_Fellowship\" title=\"Fokker F28 Fellowship\">Fokker F-28 Fellowship</a>, crashes at <a href=\"https://wikipedia.org/wiki/Dryden_Regional_Airport\" title=\"Dryden Regional Airport\">Dryden Regional Airport</a> in <a href=\"https://wikipedia.org/wiki/Dryden,_Ontario\" title=\"Dryden, Ontario\">Dryden, Ontario</a>, Canada, killing 24.", "links": [{"title": "Air Ontario Flight 1363", "link": "https://wikipedia.org/wiki/Air_Ontario_Flight_1363"}, {"title": "Fokker F28 Fellowship", "link": "https://wikipedia.org/wiki/Fokker_F28_Fellowship"}, {"title": "Dryden Regional Airport", "link": "https://wikipedia.org/wiki/Dryden_Regional_Airport"}, {"title": "Dryden, Ontario", "link": "https://wikipedia.org/wiki/Dryden,_Ontario"}]}, {"year": "1990", "text": "In Haiti, <PERSON><PERSON> is ousted eighteen months after seizing power in a coup d'état in September 1988.", "html": "1990 - In <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, <a href=\"https://wikipedia.org/wiki/Prosper_Avril\" title=\"Prosper Avril\">Prosper Avril</a> is ousted eighteen months after seizing power in a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat\" title=\"September 1988 Haitian coup d'état\">September 1988</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Haiti\" title=\"Haiti\">Haiti</a>, <a href=\"https://wikipedia.org/wiki/Prosper_Avril\" title=\"Prosper Avril\"><PERSON>sper Avril</a> is ousted eighteen months after seizing power in a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> in <a href=\"https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat\" title=\"September 1988 Haitian coup d'état\">September 1988</a>.", "links": [{"title": "Haiti", "link": "https://wikipedia.org/wiki/Haiti"}, {"title": "Prosper A<PERSON>ril", "link": "https://wikipedia.org/wiki/Prosper_Avril"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "September 1988 Haitian coup d'état", "link": "https://wikipedia.org/wiki/September_1988_Haitian_coup_d%27%C3%A9tat"}]}, {"year": "1991", "text": "1991 Salvadoran legislative election: The Nationalist Republican Alliance wins 39 of the 84 seats in the Legislative Assembly of El Salvador.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/1991_Salvadoran_legislative_election\" title=\"1991 Salvadoran legislative election\">1991 Salvadoran legislative election</a>: The <a href=\"https://wikipedia.org/wiki/Nationalist_Republican_Alliance\" title=\"Nationalist Republican Alliance\">Nationalist Republican Alliance</a> wins 39 of the 84 seats in the <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_El_Salvador\" title=\"Legislative Assembly of El Salvador\">Legislative Assembly of El Salvador</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1991_Salvadoran_legislative_election\" title=\"1991 Salvadoran legislative election\">1991 Salvadoran legislative election</a>: The <a href=\"https://wikipedia.org/wiki/Nationalist_Republican_Alliance\" title=\"Nationalist Republican Alliance\">Nationalist Republican Alliance</a> wins 39 of the 84 seats in the <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_of_El_Salvador\" title=\"Legislative Assembly of El Salvador\">Legislative Assembly of El Salvador</a>.", "links": [{"title": "1991 Salvadoran legislative election", "link": "https://wikipedia.org/wiki/1991_Salvadoran_legislative_election"}, {"title": "Nationalist Republican Alliance", "link": "https://wikipedia.org/wiki/Nationalist_Republican_Alliance"}, {"title": "Legislative Assembly of El Salvador", "link": "https://wikipedia.org/wiki/Legislative_Assembly_of_El_Salvador"}]}, {"year": "2000", "text": "The Dot-com bubble peaks with the NASDAQ Composite stock market index reaching 5,048.62.", "html": "2000 - The <a href=\"https://wikipedia.org/wiki/Dot-com_bubble\" title=\"Dot-com bubble\">Dot-com bubble</a> peaks with the <a href=\"https://wikipedia.org/wiki/Nasdaq_Composite\" title=\"Nasdaq Composite\">NASDAQ Composite</a> stock market index reaching 5,048.62.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dot-com_bubble\" title=\"Dot-com bubble\">Dot-com bubble</a> peaks with the <a href=\"https://wikipedia.org/wiki/Nasdaq_Composite\" title=\"Nasdaq Composite\">NASDAQ Composite</a> stock market index reaching 5,048.62.", "links": [{"title": "Dot-com bubble", "link": "https://wikipedia.org/wiki/Dot-com_bubble"}, {"title": "Nasdaq Composite", "link": "https://wikipedia.org/wiki/Nasdaq_Composite"}]}, {"year": "2006", "text": "The Mars Reconnaissance Orbiter arrives at Mars.", "html": "2006 - The <i><a href=\"https://wikipedia.org/wiki/Mars_Reconnaissance_Orbiter\" title=\"Mars Reconnaissance Orbiter\">Mars Reconnaissance Orbiter</a></i> arrives at <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Mars_Reconnaissance_Orbiter\" title=\"Mars Reconnaissance Orbiter\">Mars Reconnaissance Orbiter</a></i> arrives at <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Mars Reconnaissance Orbiter", "link": "https://wikipedia.org/wiki/Mars_Reconnaissance_Orbiter"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "2017", "text": "The impeachment of President <PERSON> of South Korea in response to a major political scandal is unanimously upheld by the country's Constitutional Court, ending her presidency.", "html": "2017 - The <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>-hye\" title=\"Impeachment of <PERSON>\">impeachment</a> of <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hye\" title=\"<PERSON>\"><PERSON>hye</a> of <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> in response to a <a href=\"https://wikipedia.org/wiki/2016_South_Korean_political_scandal\" title=\"2016 South Korean political scandal\">major political scandal</a> is unanimously upheld by the country's <a href=\"https://wikipedia.org/wiki/Constitutional_Court_of_Korea\" title=\"Constitutional Court of Korea\">Constitutional Court</a>, ending her presidency.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>-hye\" title=\"Impeachment of <PERSON>\">impeachment</a> of <a href=\"https://wikipedia.org/wiki/President_of_South_Korea\" title=\"President of South Korea\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-hye\" title=\"<PERSON>\"><PERSON>hye</a> of <a href=\"https://wikipedia.org/wiki/South_Korea\" title=\"South Korea\">South Korea</a> in response to a <a href=\"https://wikipedia.org/wiki/2016_South_Korean_political_scandal\" title=\"2016 South Korean political scandal\">major political scandal</a> is unanimously upheld by the country's <a href=\"https://wikipedia.org/wiki/Constitutional_Court_of_Korea\" title=\"Constitutional Court of Korea\">Constitutional Court</a>, ending her presidency.", "links": [{"title": "Impeachment of <PERSON>", "link": "https://wikipedia.org/wiki/Impeachment_of_<PERSON>_<PERSON>eu<PERSON>-hye"}, {"title": "President of South Korea", "link": "https://wikipedia.org/wiki/President_of_South_Korea"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eu<PERSON>-hye"}, {"title": "South Korea", "link": "https://wikipedia.org/wiki/South_Korea"}, {"title": "2016 South Korean political scandal", "link": "https://wikipedia.org/wiki/2016_South_Korean_political_scandal"}, {"title": "Constitutional Court of Korea", "link": "https://wikipedia.org/wiki/Constitutional_Court_of_Korea"}]}, {"year": "2019", "text": "Ethiopian Airlines Flight 302, a Boeing 737 MAX, crashes shortly after take off, killing all 157 passengers and crew. This and the prior Lion Air Flight 610 led to all 387 Boeing 737 MAX aircraft being grounded worldwide.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Ethiopian_Airlines_Flight_302\" title=\"Ethiopian Airlines Flight 302\">Ethiopian Airlines Flight 302</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">Boeing 737 MAX</a>, crashes shortly after take off, killing all 157 passengers and crew. This and the prior <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">Lion Air Flight 610</a> led to all 387 Boeing 737 MAX aircraft <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX_groundings\" title=\"Boeing 737 MAX groundings\">being grounded worldwide</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ethiopian_Airlines_Flight_302\" title=\"Ethiopian Airlines Flight 302\">Ethiopian Airlines Flight 302</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX\" title=\"Boeing 737 MAX\">Boeing 737 MAX</a>, crashes shortly after take off, killing all 157 passengers and crew. This and the prior <a href=\"https://wikipedia.org/wiki/Lion_Air_Flight_610\" title=\"Lion Air Flight 610\">Lion Air Flight 610</a> led to all 387 Boeing 737 MAX aircraft <a href=\"https://wikipedia.org/wiki/Boeing_737_MAX_groundings\" title=\"Boeing 737 MAX groundings\">being grounded worldwide</a>.", "links": [{"title": "Ethiopian Airlines Flight 302", "link": "https://wikipedia.org/wiki/Ethiopian_Airlines_Flight_302"}, {"title": "Boeing 737 MAX", "link": "https://wikipedia.org/wiki/Boeing_737_MAX"}, {"title": "Lion Air Flight 610", "link": "https://wikipedia.org/wiki/Lion_Air_Flight_610"}, {"title": "Boeing 737 MAX groundings", "link": "https://wikipedia.org/wiki/Boeing_737_MAX_groundings"}]}, {"year": "2022", "text": "2022 Hungarian presidential election: The National Assembly of Hungary elects former minister for Family Affairs, <PERSON><PERSON><PERSON>, as president of Hungary in a 137-51 vote, becoming the first female president in the country's history.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022_Hungarian_presidential_election\" title=\"2022 Hungarian presidential election\">2022 Hungarian presidential election</a>: The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Hungary)\" title=\"National Assembly (Hungary)\">National Assembly of Hungary</a> elects former minister for Family Affairs, <a href=\"https://wikipedia.org/wiki/Katalin_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, as <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">president of Hungary</a> in a 137-51 vote, becoming the first female president in the country's history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022_Hungarian_presidential_election\" title=\"2022 Hungarian presidential election\">2022 Hungarian presidential election</a>: The <a href=\"https://wikipedia.org/wiki/National_Assembly_(Hungary)\" title=\"National Assembly (Hungary)\">National Assembly of Hungary</a> elects former minister for Family Affairs, <a href=\"https://wikipedia.org/wiki/Katalin_Nov%C3%A1k\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, as <a href=\"https://wikipedia.org/wiki/President_of_Hungary\" title=\"President of Hungary\">president of Hungary</a> in a 137-51 vote, becoming the first female president in the country's history.", "links": [{"title": "2022 Hungarian presidential election", "link": "https://wikipedia.org/wiki/2022_Hungarian_presidential_election"}, {"title": "National Assembly (Hungary)", "link": "https://wikipedia.org/wiki/National_Assembly_(Hungary)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Katalin_Nov%C3%A1k"}, {"title": "President of Hungary", "link": "https://wikipedia.org/wiki/President_of_Hungary"}]}, {"year": "2023", "text": "Silicon Valley Bank collapses due to a run on its deposits, in the second largest bank failure in US history. Its operations are taken over by the FDIC.", "html": "2023 - <a href=\"https://wikipedia.org/wiki/Silicon_Valley_Bank\" title=\"Silicon Valley Bank\">Silicon Valley Bank</a> <a href=\"https://wikipedia.org/wiki/Collapse_of_Silicon_Valley_Bank\" title=\"Collapse of Silicon Valley Bank\">collapses</a> due to a <a href=\"https://wikipedia.org/wiki/Bank_run\" title=\"Bank run\">run on its deposits</a>, in the <a href=\"https://wikipedia.org/wiki/List_of_largest_bank_failures_in_the_United_States\" title=\"List of largest bank failures in the United States\">second largest</a> <a href=\"https://wikipedia.org/wiki/Bank_failure\" title=\"Bank failure\">bank failure</a> in US history. Its operations are taken over by the <a href=\"https://wikipedia.org/wiki/Federal_Deposit_Insurance_Corporation\" title=\"Federal Deposit Insurance Corporation\">FDIC</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Silicon_Valley_Bank\" title=\"Silicon Valley Bank\">Silicon Valley Bank</a> <a href=\"https://wikipedia.org/wiki/Collapse_of_Silicon_Valley_Bank\" title=\"Collapse of Silicon Valley Bank\">collapses</a> due to a <a href=\"https://wikipedia.org/wiki/Bank_run\" title=\"Bank run\">run on its deposits</a>, in the <a href=\"https://wikipedia.org/wiki/List_of_largest_bank_failures_in_the_United_States\" title=\"List of largest bank failures in the United States\">second largest</a> <a href=\"https://wikipedia.org/wiki/Bank_failure\" title=\"Bank failure\">bank failure</a> in US history. Its operations are taken over by the <a href=\"https://wikipedia.org/wiki/Federal_Deposit_Insurance_Corporation\" title=\"Federal Deposit Insurance Corporation\">FDIC</a>.", "links": [{"title": "Silicon Valley Bank", "link": "https://wikipedia.org/wiki/Silicon_Valley_Bank"}, {"title": "Collapse of Silicon Valley Bank", "link": "https://wikipedia.org/wiki/Collapse_of_Silicon_Valley_Bank"}, {"title": "Bank run", "link": "https://wikipedia.org/wiki/Bank_run"}, {"title": "List of largest bank failures in the United States", "link": "https://wikipedia.org/wiki/List_of_largest_bank_failures_in_the_United_States"}, {"title": "Bank failure", "link": "https://wikipedia.org/wiki/Bank_failure"}, {"title": "Federal Deposit Insurance Corporation", "link": "https://wikipedia.org/wiki/Federal_Deposit_Insurance_Corporation"}]}, {"year": "2024", "text": "2024 Portuguese legislative election: Elections are held in Portugal for all 230 seats in the Assembly of the Republic. The Partido Socialista loses its absolute majority to the Partido Social Democrata, winning 77 and 79 seats respectively.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/2024_Portuguese_legislative_election\" title=\"2024 Portuguese legislative election\">2024 Portuguese legislative election</a>: Elections are held in Portugal for all 230 seats in the <a href=\"https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)\" title=\"Assembly of the Republic (Portugal)\">Assembly of the Republic</a>. The <a href=\"https://wikipedia.org/wiki/Socialist_Party_(Portugal)\" title=\"Socialist Party (Portugal)\">Partido Socialista</a> loses its absolute majority to the <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_(Portugal)\" title=\"Social Democratic Party (Portugal)\">Partido Social Democrata</a>, winning 77 and 79 seats respectively.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2024_Portuguese_legislative_election\" title=\"2024 Portuguese legislative election\">2024 Portuguese legislative election</a>: Elections are held in Portugal for all 230 seats in the <a href=\"https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)\" title=\"Assembly of the Republic (Portugal)\">Assembly of the Republic</a>. The <a href=\"https://wikipedia.org/wiki/Socialist_Party_(Portugal)\" title=\"Socialist Party (Portugal)\">Partido Socialista</a> loses its absolute majority to the <a href=\"https://wikipedia.org/wiki/Social_Democratic_Party_(Portugal)\" title=\"Social Democratic Party (Portugal)\">Partido Social Democrata</a>, winning 77 and 79 seats respectively.", "links": [{"title": "2024 Portuguese legislative election", "link": "https://wikipedia.org/wiki/2024_Portuguese_legislative_election"}, {"title": "Assembly of the Republic (Portugal)", "link": "https://wikipedia.org/wiki/Assembly_of_the_Republic_(Portugal)"}, {"title": "Socialist Party (Portugal)", "link": "https://wikipedia.org/wiki/Socialist_Party_(Portugal)"}, {"title": "Social Democratic Party (Portugal)", "link": "https://wikipedia.org/wiki/Social_Democratic_Party_(Portugal)"}]}], "Births": [{"year": "1452", "text": "<PERSON>, King of Castile and León (d. 1516)", "html": "1452 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> II</a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (d. 1516)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Aragon\" title=\"<PERSON> II of Aragon\"><PERSON> II</a>, King of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Castile\" title=\"Kingdom of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (d. 1516)", "links": [{"title": "<PERSON> of Aragon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Aragon"}, {"title": "Kingdom of Castile", "link": "https://wikipedia.org/wiki/Kingdom_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1503", "text": "<PERSON>, Holy Roman Emperor (d. 1564)", "html": "1503 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON>, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a> (d. 1564)", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1536", "text": "<PERSON>, 4th Duke of Norfolk, English politician, Earl Marshal of the United Kingdom (d. 1572)", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Earl_Marshal\" title=\"Earl Marshal\">Earl Marshal of the United Kingdom</a> (d. 1572)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_4th_Duke_of_Norfolk\" title=\"<PERSON>, 4th Duke of Norfolk\"><PERSON>, 4th Duke of Norfolk</a>, English politician, <a href=\"https://wikipedia.org/wiki/Earl_Marshal\" title=\"Earl Marshal\">Earl Marshal of the United Kingdom</a> (d. 1572)", "links": [{"title": "<PERSON>, 4th Duke of Norfolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_4th_Duke_of_Norfolk"}, {"title": "Earl Marshal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1596", "text": "Princess <PERSON> of Sweden, daughter of King <PERSON> of Sweden (d. 1618)", "html": "1596 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a>, daughter of King <PERSON> of Sweden (d. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Sweden\" title=\"Princess <PERSON> of Sweden\">Princess <PERSON> of Sweden</a>, daughter of King <PERSON> of Sweden (d. 1618)", "links": [{"title": "Princess <PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Princess_<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1604", "text": "<PERSON>, German-Dutch alchemist and chemist (d. 1670)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch alchemist and chemist (d. 1670)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Dutch alchemist and chemist (d. 1670)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON>, French sculptor (d. 1715)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Girardon\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_Girardon\" title=\"<PERSON>\"><PERSON></a>, French sculptor (d. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fran%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1628", "text": "<PERSON><PERSON>, Italian physician and biologist (d. 1694)", "html": "1628 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physician and biologist (d. 1694)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian physician and biologist (d. 1694)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1653", "text": "<PERSON>, Royal Navy admiral (d. 1702)", "html": "1653 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Navy admiral (d. 1702)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Royal Navy admiral (d. 1702)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON>, Italian Rococo sculptor (d. 1732)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Rococo sculptor (d. 1732)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian Rococo sculptor (d. 1732)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1709", "text": "<PERSON>, German botanist, zoologist, physician, and explorer (d. 1746)", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist, zoologist, physician, and explorer (d. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German botanist, zoologist, physician, and explorer (d. 1746)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1749", "text": "<PERSON>, Italian-American priest and poet (d. 1838)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American priest and poet (d. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American priest and poet (d. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1769", "text": "<PERSON>, English businessman and philanthropist (d. 1840)", "html": "1769 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, English businessman and philanthropist (d. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(philanthropist)\" title=\"<PERSON> (philanthropist)\"><PERSON></a>, English businessman and philanthropist (d. 1840)", "links": [{"title": "<PERSON> (philanthropist)", "link": "https://wikipedia.org/wiki/<PERSON>(philanthropist)"}]}, {"year": "1772", "text": "<PERSON>, German poet and critic (d. 1829)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1829)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German poet and critic (d. 1829)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, French painter (d. 1860)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French painter (d. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1787", "text": "<PERSON>, Spanish playwright and politician, Prime Minister of Spain (d. 1862)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_de_la_Rosa_y_Berdejo\" class=\"mw-redirect\" title=\"<PERSON> y Berdejo\"><PERSON> y <PERSON></a>, Spanish playwright and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_de_la_Rosa_y_Berdejo\" class=\"mw-redirect\" title=\"<PERSON> y Berdejo\"><PERSON> y <PERSON></a>, Spanish playwright and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Spain\" title=\"Prime Minister of Spain\">Prime Minister of Spain</a> (d. 1862)", "links": [{"title": "<PERSON> de la Rosa y Berdejo", "link": "https://wikipedia.org/wiki/Francisco_de_Paula_Mart%C3%ADnez_<PERSON>_<PERSON>_<PERSON>_y_Be<PERSON>jo"}, {"title": "Prime Minister of Spain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Spain"}]}, {"year": "1787", "text": "<PERSON>, English painter and academic (d. 1849)", "html": "1787 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (d. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, German author, poet, playwright, and critic (d. 1857)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, playwright, and critic (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author, poet, playwright, and critic (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1788", "text": "<PERSON>, English sculptor (d. 1867)", "html": "1788 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sculptor (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON>, Mexican lawyer and 20th President (1847) (d. 1850)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Pe%C3%B1a_y_Pe%C3%B1a\" title=\"<PERSON> y Peña\"><PERSON></a>, Mexican lawyer and 20th President (1847) (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>e%C3%B1a_y_Pe%C3%B1a\" title=\"<PERSON> Peña y Peña\"><PERSON></a>, Mexican lawyer and 20th President (1847) (d. 1850)", "links": [{"title": "<PERSON> de la Peña y Peña", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Pe%C3%B1a_y_Pe%C3%B1a"}]}, {"year": "1795", "text": "<PERSON>, Canadian painter and glazier, artist, seigneur and political figure (d. 1855)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gar%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and glazier, artist, seigneur and political figure (d. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9gar%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Canadian painter and glazier, artist, seigneur and political figure (d. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_L%C3%A9gar%C3%A9"}]}, {"year": "1810", "text": "<PERSON>, Irish poet and lawyer (d. 1886)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and lawyer (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish poet and lawyer (d. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1843", "text": "<PERSON>, English classical scholar (d. 1901)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English classical scholar (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Spanish violinist and composer (d. 1908)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish violinist and composer (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish violinist and composer (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, British Pre-Raphaelite painter (d. 1927)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Pre-Raphaelite painter (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British Pre-Raphaelite painter (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON> of Russia (d. 1894)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> of Russia</a> (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia\" title=\"<PERSON> III of Russia\"><PERSON> of Russia</a> (d. 1894)", "links": [{"title": "<PERSON> of Russia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1846", "text": "<PERSON>, American son of <PERSON> (d. 1850)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American son of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Abraham Lincoln\"><PERSON></a> (d. 1850)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON>, English tennis player and cricketer (d. 1906)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English tennis player and cricketer (d. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(sportsman)\" title=\"<PERSON> (sportsman)\"><PERSON></a>, English tennis player and cricketer (d. 1906)", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(sportsman)"}]}, {"year": "1853", "text": "<PERSON>, Scottish-New Zealand cartographer and politician, 18th Prime Minister of New Zealand (d. 1930)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand cartographer and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand cartographer and politician, 18th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1867", "text": "<PERSON>, French-American architect (d. 1942)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American architect (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American architect (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON>, American nurse, humanitarian, and author, founded the Henry Street Settlement (d. 1940)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, humanitarian, and author, founded the <a href=\"https://wikipedia.org/wiki/Henry_Street_Settlement\" title=\"Henry Street Settlement\">Henry Street Settlement</a> (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse, humanitarian, and author, founded the <a href=\"https://wikipedia.org/wiki/Henry_Street_Settlement\" title=\"Henry Street Settlement\">Henry Street Settlement</a> (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Henry Street Settlement", "link": "https://wikipedia.org/wiki/Henry_Street_Settlement"}]}, {"year": "1870", "text": "<PERSON>, Russian theorist and politician (d. 1938)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian theorist and politician (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian theorist and politician (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1873", "text": "<PERSON>, German-Austrian soldier and author (d. 1934)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian soldier and author (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Austrian soldier and author (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American sculptor (d. 1973)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anna_H<PERSON>tt_Huntington"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON><PERSON>, Mexican diplomat and president (1930-1932) (d. 1963)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican diplomat and president (1930-1932) (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Mexican diplomat and president (1930-1932) (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English painter (d. 1956)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, Irish actor (d. 1961)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON>, Australian politician, 28th Premier of Tasmania (d. 1939)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Tasmania\" title=\"Premier of Tasmania\">Premier of Tasmania</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Tasmania", "link": "https://wikipedia.org/wiki/Premier_of_Tasmania"}]}, {"year": "1892", "text": "<PERSON>, French composer and educator (d. 1955)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American director, producer, and screenwriter (d. 1952)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, British cartoonist, painter, teacher and author (d. 1973)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>augh\" title=\"<PERSON><PERSON> Waugh\"><PERSON></a>, British cartoonist, painter, teacher and author (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Waugh\" title=\"<PERSON><PERSON> Waugh\"><PERSON></a>, British cartoonist, painter, teacher and author (d. 1973)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Jamaican supercentenarian, oldest Jamaican ever (d. 2017)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican supercentenarian, oldest Jamaican ever (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Brown\"><PERSON></a>, Jamaican supercentenarian, oldest Jamaican ever (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, Greek lawyer and politician (d. 1943)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek lawyer and politician (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Belgian painter (d. 1999)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian painter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, English artist and illustrator (d. 1989)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist and illustrator (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist and illustrator (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, American cornet player, pianist, and composer (d. 1931)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Beiderbecke\"><PERSON><PERSON></a>, American cornet player, pianist, and composer (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>beck<PERSON>\"><PERSON><PERSON></a>, American cornet player, pianist, and composer (d. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American playwright, journalist, and diplomat, United States Ambassador to Italy (d. 1987)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, journalist, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright, journalist, and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Italy\" class=\"mw-redirect\" title=\"United States Ambassador to Italy\">United States Ambassador to Italy</a> (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to Italy", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Italy"}]}, {"year": "1915", "text": "<PERSON>, Italian-American sculptor and furniture designer (d. 1978)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American sculptor and furniture designer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American sculptor and furniture designer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON><PERSON>, Croatian writer (d. 2012)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Jo%C5%BEa_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian writer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C5%BEa_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian writer (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo%C5%BEa_<PERSON>t"}]}, {"year": "1917", "text": "<PERSON>, American Surrealist artist, sculptor, photographer and painter (d. 1992)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American Surrealist artist, sculptor, photographer and painter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American Surrealist artist, sculptor, photographer and painter (d. 1992)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_(artist)"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, German general and pilot (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German general and pilot (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German general and pilot (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%<PERSON><PERSON><PERSON>_<PERSON>ll"}]}, {"year": "1919", "text": "<PERSON><PERSON>, Chilean socialite, First Lady of Chile from 1990 to 1994 (d. 2022)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z%C3%BAn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean socialite, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a> from 1990 to 1994 (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BAn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean socialite, <a href=\"https://wikipedia.org/wiki/First_Lady_of_Chile\" title=\"First Lady of Chile\">First Lady of Chile</a> from 1990 to 1994 (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leonor_Oyarz%C3%BAn"}, {"title": "First Lady of Chile", "link": "https://wikipedia.org/wiki/First_Lady_of_Chile"}]}, {"year": "1920", "text": "<PERSON>, Dutch-American businessman, founded Peet's Coffee & Tea (d. 2007)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American businessman, founded <a href=\"https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Coffee &amp; Tea\"><PERSON><PERSON><PERSON>'s Coffee &amp; Tea</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch-American businessman, founded <a href=\"https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>'s Coffee &amp; Tea\"><PERSON><PERSON><PERSON>'s Coffee &amp; Tea</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Peet's Coffee & Tea", "link": "https://wikipedia.org/wiki/Peet%27s_Coffee_%26_Tea"}]}, {"year": "1923", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>tch"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1924", "text": "<PERSON>, American literary and cookbook editor (d. 2017)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary and cookbook editor (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American literary and cookbook editor (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American lawyer, banker, and politician, Mayor of Houston (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer, banker, and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Houston\" title=\"List of mayors of Houston\">Mayor of Houston</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American lawyer, banker, and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Houston\" title=\"List of mayors of Houston\">Mayor of Houston</a> (d. 2014)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "List of mayors of Houston", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Houston"}]}, {"year": "1926", "text": "<PERSON><PERSON>, American basketball player (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, Belgian-French actor, producer, and screenwriter (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French actor, producer, and screenwriter (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian-French actor, producer, and screenwriter (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Spanish actress (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American criminal; assassin of <PERSON> (d. 1998)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal; assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American criminal; assassin of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1929", "text": "<PERSON>, American journalist and politician (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and politician (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Steiger"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Hungarian runner (d. 1996)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Iharos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A1ndor_Iharos\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian runner (d. 1996)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/S%C3%A1ndor_Iharos"}]}, {"year": "1931", "text": "<PERSON>, Canadian author, playwright, and composer (d. 2001)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, and composer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author, playwright, and composer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, <PERSON>, English politician (d. 2019)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician (d. 2019)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American country music disc jockey, radio and television host (d. 2022)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music disc jockey, radio and television host (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music disc jockey, radio and television host (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian javelin thrower (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/G<PERSON><PERSON><PERSON>_<PERSON>cs%C3%A1r\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian javelin thrower (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>%C3%A1r\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian javelin thrower (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gergely_Kulcs%C3%A1r"}]}, {"year": "1935", "text": "<PERSON>, Australian footballer and coach (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer and coach (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON>, Swiss businessman and eighth president of FIFA", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Sepp_<PERSON>latter\" title=\"Sepp Blatter\"><PERSON><PERSON></a>, Swiss businessman and <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_FIFA\" title=\"List of presidents of FIFA\">eighth president of FIFA</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sepp_<PERSON>latter\" title=\"Sepp Blatter\"><PERSON><PERSON></a>, Swiss businessman and <a href=\"https://wikipedia.org/wiki/List_of_presidents_of_FIFA\" title=\"List of presidents of FIFA\">eighth president of FIFA</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sepp_<PERSON><PERSON>"}, {"title": "List of presidents of FIFA", "link": "https://wikipedia.org/wiki/List_of_presidents_of_FIFA"}]}, {"year": "1937", "text": "<PERSON>, Argentine writer and translator (d. 2023)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer and translator (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentine writer and translator (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%AD<PERSON>_Kodama"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)\" title=\"<PERSON> (American musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (American musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_musician)"}]}, {"year": "1938", "text": "<PERSON>, American football player", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ron Mix\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ron Mix\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Indian activist and author (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian activist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Ukrainian-Russian hurdler and pentathlete (d. 2004)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Irina_Press\" title=\"Irina Press\">Irina Press</a>, Ukrainian-Russian hurdler and pentathlete (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Irina_Press\" title=\"Irina Press\">Irina Press</a>, Ukrainian-Russian hurdler and pentathlete (d. 2004)", "links": [{"title": "<PERSON>rina <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Press"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON>, American basketball player (d. 2012)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American actor, producer, and martial artist", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American playwright and screenwriter", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, English historian and author", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actor", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, American actress and playwright", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian politician, Indian Minister of Railways (d. 2001)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Scindia\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Scind<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Railways_(India)\" class=\"mw-redirect\" title=\"Minister of Railways (India)\">Indian Minister of Railways</a> (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>india\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Railways_(India)\" class=\"mw-redirect\" title=\"Minister of Railways (India)\">Indian Minister of Railways</a> (d. 2001)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hav<PERSON>o_Scindia"}, {"title": "Minister of Railways (India)", "link": "https://wikipedia.org/wiki/Minister_of_Railways_(India)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, American football player (d. 2021)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> Culp\"><PERSON><PERSON></a>, American football player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"C<PERSON> Culp\"><PERSON><PERSON></a>, American football player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, French contemporary artist", "html": "1946 - <a href=\"https://wikipedia.org/wiki/G%C3%A9rard_Garouste\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French contemporary artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9rard_Garouste\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French contemporary artist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9rard_Garouste"}]}, {"year": "1946", "text": "<PERSON>, American basketball player and coach (d. 1993)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Canadian lawyer and politician, 19th Prime Minister of Canada", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 19th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1947", "text": "<PERSON>, American musician and songwriter", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American basketball player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Austin_Carr\" title=\"Austin Carr\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Austin_Carr\" title=\"Austin Carr\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Austin_Carr"}]}, {"year": "1950", "text": "<PERSON><PERSON>, American actress", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON> Wright", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Filipino actress and beauty queen, Miss Universe 1969", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_1969\" title=\"Miss Universe 1969\">Miss Universe 1969</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino actress and beauty queen, <a href=\"https://wikipedia.org/wiki/Miss_Universe_1969\" title=\"Miss Universe 1969\">Miss Universe 1969</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Miss Universe 1969", "link": "https://wikipedia.org/wiki/Miss_Universe_1969"}]}, {"year": "1952", "text": "<PERSON>, Zimbabwean politician, Prime Minister of Zimbabwe (d. 2018)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>vangi<PERSON>\"><PERSON></a>, Zimbabwean politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe\" title=\"Prime Minister of Zimbabwe\">Prime Minister of Zimbabwe</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> T<PERSON>\"><PERSON></a>, Zimbabwean politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe\" title=\"Prime Minister of Zimbabwe\">Prime Minister of Zimbabwe</a> (d. 2018)", "links": [{"title": "Morgan <PERSON>", "link": "https://wikipedia.org/wiki/Morgan_Tsvangirai"}, {"title": "Prime Minister of Zimbabwe", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Zimbabwe"}]}, {"year": "1953", "text": "<PERSON>, Canadian director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Japanese race car driver", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON><PERSON><PERSON> (racing driver)\"><PERSON><PERSON><PERSON></a>, Japanese race car driver", "links": [{"title": "<PERSON><PERSON><PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1956", "text": "<PERSON>, English actor, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American long jumper and sprinter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American long jumper and sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, French gynecologist, advocate for women's right to pleasure", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French gynecologist, advocate for women's right to pleasure", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French gynecologist, advocate for women's right to pleasure", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian terrorist, founded al-Qaeda (d. 2011)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON></a>, Saudi Arabian terrorist, founded <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON></a>, Saudi Arabian terrorist, founded <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">al-Qaeda</a> (d. 2011)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>den"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "1957", "text": "<PERSON>, Canadian model and actress", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian model and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Shannon_Tweed"}]}, {"year": "1958", "text": "<PERSON>, English footballer and sportscaster", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American baseball player (d. 2006)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player (d. 2006)", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1958", "text": "<PERSON>, American actress and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American captain, physician, and astronaut (d. 2003)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, physician, and astronaut (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American gymnast and actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actress, singer, and director", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jasmine Guy\"><PERSON></a>, American actress, singer, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Guy\" title=\"Jasmine Guy\"><PERSON></a>, American actress, singer, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Japanese singer-songwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American bass player and songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American record producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian cricketer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>(cricketer)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Swedish singer-songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Duke of Edinburgh", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh\" title=\"Prince <PERSON>, Duke of Edinburgh\">Prince <PERSON>, Duke of Edinburgh</a>", "links": [{"title": "<PERSON>, Duke of Edinburgh", "link": "https://wikipedia.org/wiki/Prince_<PERSON>,_Duke_of_Edinburgh"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Canadian sprinter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American football player, coach, and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American singer-songwriter", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American baseball player", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Bosnian singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Alma_%C4%8Card%C5%BEi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alma_%C4%8Card%C5%BEi%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Alma_%C4%8Card%C5%BEi%C4%87"}]}, {"year": "1968", "text": "<PERSON>, Czech footballer and coach (d. 2015)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>n%C3%AD%C4%8Dek\" title=\"<PERSON>\"><PERSON></a>, Czech footballer and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pavel_Srn%C3%AD%C4%8Dek"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American actress", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actor and director", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian actress", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American NASCAR driver", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American NASCAR driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American NASCAR driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, American rapper and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Timbaland\" title=\"Timbaland\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Timbaland\" title=\"Timbaland\"><PERSON><PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "Timbaland", "link": "https://wikipedia.org/wiki/Timbaland"}]}, {"year": "1973", "text": "<PERSON>, Australian rugby league player", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Chinese entrepreneur, billionaire, founder of JD.com", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Liu_<PERSON>\" title=\"Liu <PERSON>\"><PERSON></a>, Chinese entrepreneur, billionaire, founder of <a href=\"https://wikipedia.org/wiki/JD.com\" title=\"JD.com\">JD.com</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Liu_<PERSON>\" title=\"Liu <PERSON>\"><PERSON></a>, Chinese entrepreneur, billionaire, founder of <a href=\"https://wikipedia.org/wiki/JD.com\" title=\"JD.com\">JD.com</a>", "links": [{"title": "Liu <PERSON>", "link": "https://wikipedia.org/wiki/Liu_Qiangdong"}, {"title": "JD.com", "link": "https://wikipedia.org/wiki/JD.com"}]}, {"year": "1973", "text": "<PERSON>, English footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Argentine footballer and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentine footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Chilean actor, model, producer, and television host", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Cristi%C3%A1n_de_la_Fuente\" title=\"<PERSON><PERSON><PERSON><PERSON> Fuente\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean actor, model, producer, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cristi%C3%A1n_de_la_Fuente\" title=\"<PERSON><PERSON><PERSON><PERSON> Fuente\"><PERSON><PERSON><PERSON><PERSON></a>, Chilean actor, model, producer, and television host", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>ente", "link": "https://wikipedia.org/wiki/Cristi%C3%A1n_de_la_Fuente"}]}, {"year": "1976", "text": "<PERSON>, Austrian tennis player", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American gymnast", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American singer, songwriter, and record producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, songwriter, and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American musician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, French singer-songwriter and actress", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(French_singer)\" title=\"<PERSON> (French singer)\"><PERSON></a>, French singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(French_singer)\" title=\"<PERSON> (French singer)\"><PERSON></a>, French singer-songwriter and actress", "links": [{"title": "<PERSON> (French singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(French_singer)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Kenyan-American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Edi_Gathegi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edi_Gatheg<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Kenyan-American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edi_Gathegi"}]}, {"year": "1979", "text": "<PERSON>, American actor", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Cameroonian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27o"}]}, {"year": "1981", "text": "<PERSON>, English-Irish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American live streamer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Dr_Disrespect\" title=\"Dr Disrespect\"><PERSON>respect</a>, American live streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dr_Disrespect\" title=\"Dr Disrespect\"><PERSON>respect</a>, American live streamer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dr_Disrespect"}]}, {"year": "1982", "text": "<PERSON>, American football player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Canadian-American comedian and actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American comedian and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/%C3%89tienne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89tien<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/%C3%89tienne_<PERSON>y"}]}, {"year": "1983", "text": "<PERSON>, American journalist, author, and activist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, English actor", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American singer-songwriter", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Canadian ice hockey player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_May_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON> May</a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1984", "text": "<PERSON>, American actress and director", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, New Zealand rugby league player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tuuk<PERSON> Rask\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Tuukka Rask\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "Tuukka Rask", "link": "https://wikipedia.org/wiki/Tuukka_Rask"}]}, {"year": "1987", "text": "<PERSON><PERSON>, British singer-songwriter", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, British singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emeli_Sand%C3%A9"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Latvian BMX racer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C5%A0trombergs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian BMX racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C4%81ris_%C5%A0trombergs\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian BMX racer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C4%81ris_%C5%A0trombergs"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Brazilian basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Australian-New Zealand rugby league player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ego_Nwodim\" title=\"Ego Nwodim\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ego_Nwodim\" title=\"Ego Nwodim\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ego_Nwodim"}]}, {"year": "1988", "text": "<PERSON>, American basketball player and coach", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Quincy_Pondexter\" title=\"Quincy Pondexter\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quincy_Pondexter\" title=\"Quincy Pondexter\"><PERSON></a>, American basketball player and coach", "links": [{"title": "Quincy Pondexter", "link": "https://wikipedia.org/wiki/Quincy_Pondexter"}]}, {"year": "1988", "text": "<PERSON>, Croatian football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Croatian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Ra<PERSON>ti%C4%87"}]}, {"year": "1989", "text": "<PERSON>, Swiss ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Cuban baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Day%C3%A1n_Vic<PERSON>o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Day%C3%A1n_Vic<PERSON>o\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Cuban baseball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Day%C3%A1n_Viciedo"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Swiss tennis player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6gele\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B6gele\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stefanie_V%C3%B6gele"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Congolese footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Neeskens_Kebano\" title=\"Neeskens Kebano\">Neesken<PERSON> Kebano</a>, French-Congolese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neeskens_Kebano\" title=\"Neeskens Kebano\"><PERSON>ees<PERSON><PERSON> Kebano</a>, French-Congolese footballer", "links": [{"title": "Neeskens Kebano", "link": "https://wikipedia.org/wiki/Neeskens_Kebano"}]}, {"year": "1992", "text": "<PERSON>, American actress and singer-songwriter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Congolese politician", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Aminata_Namasia\" title=\"Aminata Namasia\"><PERSON><PERSON><PERSON></a>, Congolese politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Amina<PERSON>_Namasi<PERSON>\" title=\"Aminata Namasia\"><PERSON><PERSON><PERSON></a>, Congolese politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aminata_Namasia"}]}, {"year": "1994", "text": "<PERSON>, Puerto Rican rapper, songwriter, producer, actor, and wrestler", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Bad_Bunny\" title=\"Bad Bunny\"><PERSON> Bunny</a>, Puerto Rican rapper, songwriter, producer, actor, and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bad_Bunny\" title=\"Bad Bunny\"><PERSON> Bunny</a>, Puerto Rican rapper, songwriter, producer, actor, and wrestler", "links": [{"title": "Bad Bunny", "link": "https://wikipedia.org/wiki/Bad_Bunny"}]}, {"year": "1994", "text": "<PERSON><PERSON>, English footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Russian ice dancer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Swiss tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American football player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American basketball player", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2002)\" title=\"<PERSON><PERSON> (basketball, born 2002)\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2002)\" title=\"<PERSON><PERSON> (basketball, born 2002)\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON> (basketball, born 2002)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(basketball,_born_2002)"}]}, {"year": "2004", "text": "<PERSON>, Canadian ice hockey player", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, Italian footballer", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francesco <PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Francesco Cam<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francesco_Camarda"}]}], "Deaths": [{"year": "483", "text": "<PERSON> <PERSON><PERSON><PERSON>", "html": "483 - <a href=\"https://wikipedia.org/wiki/Pope_Simplicius\" title=\"Pope Simplicius\">Pope <PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Simplicius\" title=\"Pope Simplicius\">Pope Si<PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Simplicius"}]}, {"year": "948", "text": "<PERSON>, Shatuo founder of the Later Han dynasty (b. 895)", "html": "948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Zhiyuan\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Shatuo\" title=\"Shat<PERSON>\"><PERSON><PERSON><PERSON></a> founder of the <a href=\"https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)\" title=\"Later Han (Five Dynasties)\">Later Han</a> dynasty (b. 895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Zhiyuan\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Shatuo\" title=\"Shat<PERSON>\"><PERSON><PERSON><PERSON></a> founder of the <a href=\"https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)\" title=\"Later Han (Five Dynasties)\">Later Han</a> dynasty (b. 895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Liu_<PERSON>yuan"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shatuo"}, {"title": "Later Han (Five Dynasties)", "link": "https://wikipedia.org/wiki/Later_Han_(Five_Dynasties)"}]}, {"year": "1291", "text": "<PERSON><PERSON><PERSON>, Mongol ruler in Persia (b. c. 1258)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler in <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a> (b. c. 1258)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mongol ruler in <a href=\"https://wikipedia.org/wiki/Persia\" class=\"mw-redirect\" title=\"Persia\">Persia</a> (b. c. 1258)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Arghun"}, {"title": "Persia", "link": "https://wikipedia.org/wiki/Persia"}]}, {"year": "1315", "text": "<PERSON>, Austrian mystic", "html": "1315 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mystic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian mystic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1513", "text": "<PERSON>, 13th Earl of Oxford, English commander and politician, Lord High Constable of England (b. 1442)", "html": "1513 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford\" title=\"<PERSON>, 13th Earl of Oxford\"><PERSON>, 13th Earl of Oxford</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1442)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford\" title=\"<PERSON>, 13th Earl of Oxford\"><PERSON>, 13th Earl of Oxford</a>, English commander and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Constable_of_England\" title=\"Lord High Constable of England\">Lord High Constable of England</a> (b. 1442)", "links": [{"title": "<PERSON>, 13th Earl of Oxford", "link": "https://wikipedia.org/wiki/<PERSON>,_13th_Earl_of_Oxford"}, {"title": "Lord High Constable of England", "link": "https://wikipedia.org/wiki/Lord_High_Constable_of_England"}]}, {"year": "1528", "text": "<PERSON><PERSON><PERSON><PERSON>, German/Moravian Anabaptist leader", "html": "1528 - <a href=\"https://wikipedia.org/wiki/Balthasar_H%C3%BCbmaier\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German/Moravian Anabaptist leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balthasar_H%C3%BCbmaier\" class=\"mw-redirect\" title=\"<PERSON>lt<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, German/Moravian Anabaptist leader", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Balthasar_H%C3%BCbmaier"}]}, {"year": "1572", "text": "<PERSON>, 1st Marquess of Winchester", "html": "1572 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Winchester\" title=\"<PERSON>, 1st Marquess of Winchester\"><PERSON>, 1st Marquess of Winchester</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Winchester\" title=\"<PERSON>, 1st Marquess of Winchester\"><PERSON>, 1st Marquess of Winchester</a>", "links": [{"title": "<PERSON>, 1st Marquess of Winchester", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Marquess_of_Winchester"}]}, {"year": "1585", "text": "<PERSON><PERSON><PERSON>, Flemish physician and botanist (b. 1517)", "html": "1585 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish physician and botanist (b. 1517)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Flemish physician and botanist (b. 1517)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1682", "text": "<PERSON>, Dutch painter and etcher (b. 1628)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and etcher (b. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch painter and etcher (b. 1628)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1724", "text": "<PERSON>, Swedish chemist, geologist, and physician (b. 1641)", "html": "1724 - <a href=\"https://wikipedia.org/wiki/Urban_Hj%C3%A4rne\" title=\"Urban Hjärne\"><PERSON></a>, Swedish chemist, geologist, and physician (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Urban_Hj%C3%A4rne\" title=\"Urban Hjärne\"><PERSON></a>, Swedish chemist, geologist, and physician (b. 1641)", "links": [{"title": "Urban Hjärne", "link": "https://wikipedia.org/wiki/Urban_Hj%C3%A4rne"}]}, {"year": "1776", "text": "<PERSON><PERSON>, French author and critic (b. 1718)", "html": "1776 - <a href=\"https://wikipedia.org/wiki/%C3%89lie_<PERSON>_<PERSON>%C3%A9ron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89<PERSON>_<PERSON>_<PERSON>%C3%A9ron\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French author and critic (b. 1718)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89lie_<PERSON>_<PERSON>%C3%A9ron"}]}, {"year": "1792", "text": "<PERSON>, 3rd Earl of Bute, Scottish politician, Prime Minister of the United Kingdom (b. 1713)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Bute\" title=\"<PERSON>, 3rd Earl of Bute\"><PERSON>, 3rd Earl of Bute</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Earl_of_Bute\" title=\"<PERSON>, 3rd Earl of Bute\"><PERSON>, 3rd Earl of Bute</a>, Scottish politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (b. 1713)", "links": [{"title": "<PERSON>, 3rd Earl of Bute", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_3rd_Earl_of_Bute"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1826", "text": "<PERSON>, Scottish antiquarian, cartographer, author, numismatist and historian (b. 1758)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish antiquarian, cartographer, author, numismatist and historian (b. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish antiquarian, cartographer, author, numismatist and historian (b. 1758)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON><PERSON>, Italian pianist, composer, and conductor (b. 1752)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian pianist, composer, and conductor (b. 1752)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1861", "text": "<PERSON><PERSON>, Ukrainian poet, playwright, and ethnographer (b. 1814)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian poet, playwright, and ethnographer (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian poet, playwright, and ethnographer (b. 1814)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Italian journalist and politician (b. 1805)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, English-French fashion designer (b. 1825)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French fashion designer (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French fashion designer (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian poet and activist (b. 1831)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/Savitribai_Phule\" title=\"Savitribai Phule\">Savitribai Phule</a>, Indian poet and activist (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Savitribai_Phule\" title=\"Savitribai Phule\">Savitribai Phule</a>, Indian poet and activist (b. 1831)", "links": [{"title": "Savitribai Phule", "link": "https://wikipedia.org/wiki/Savitribai_Phule"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French nun and saint, founded the Religious of the Assumption (b. 1817)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A9nie_de_J%C3%A9sus\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>us\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Assumption\" title=\"Religious of the Assumption\">Religious of the Assumption</a> (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>%C3%A9nie_de_J%C3%A9sus\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French nun and saint, founded the <a href=\"https://wikipedia.org/wiki/Religious_of_the_Assumption\" title=\"Religious of the Assumption\">Religious of the Assumption</a> (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>ug%C3%A9nie_de_J%C3%A9sus"}, {"title": "Religious of the Assumption", "link": "https://wikipedia.org/wiki/Religious_of_the_Assumption"}]}, {"year": "1910", "text": "<PERSON>, Austrian lawyer and politician Mayor of Vienna (b. 1844)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Vienna\" title=\"List of mayors of Vienna\">Mayor of Vienna</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian lawyer and politician <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Vienna\" title=\"List of mayors of Vienna\">Mayor of Vienna</a> (b. 1844)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of mayors of Vienna", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Vienna"}]}, {"year": "1910", "text": "<PERSON>, German pianist, composer, and conductor (b. 1824)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German pianist, composer, and conductor (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American nurse and activist (b. c. 1820)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (b. c. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse and activist (b. c. 1820)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Polish-American jumper (b. 1878)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American jumper (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish-American jumper (b. 1878)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Russian journalist and author (b. 1884)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and author (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yev<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian journalist and author (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Russian novelist and playwright (b. 1891)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and playwright (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian novelist and playwright (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, American pharmacist and chemist (b. 1865)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pharmacist and chemist (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>\" title=\"W<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pharmacist and chemist (b. 1865)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, American author, visual artist, and ballet dancer (b. 1900)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, visual artist, and ballet dancer (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author, visual artist, and ballet dancer (b. 1900)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Czech soldier and politician (b. 1886)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soldier and politician (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech soldier and politician (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese lawyer and politician, Prime Minister of Japan (b. 1872)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_Shi<PERSON>hara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_Shidehara\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese lawyer and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Japan\" title=\"Prime Minister of Japan\">Prime Minister of Japan</a> (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kij%C5%ABr%C5%8D_<PERSON><PERSON>hara"}, {"title": "Prime Minister of Japan", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Japan"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Dutch physicist and academic, Nobel Prize laureate (b. 1888)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Frits_Zernike\" title=\"Frits Zernike\"><PERSON><PERSON> Zernike</a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frits_Zernike\" title=\"Frits Zernike\"><PERSON><PERSON> Zernike</a>, Dutch physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "Frits Zernike", "link": "https://wikipedia.org/wiki/Frits_Zernike"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1966", "text": "<PERSON>, Irish short story writer, novelist, and poet (b. 1903)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Irish short story writer, novelist, and poet (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, Irish short story writer, novelist, and poet (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_O%27Connor"}]}, {"year": "1973", "text": "<PERSON>, 1st Baron <PERSON> of Glendale (1960 - 1973), Governor of Kenya (1952 - 1959), High Commissioner for Southern Africa (1944 - 1951), Governor of Southern Rhodesia (1942 - 1944) (b. 1903)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Baron_<PERSON><PERSON>_of_Glendale\" title=\"<PERSON>, 1st Baron <PERSON> of Glendale\"><PERSON></a>, 1st <a href=\"https://wikipedia.org/wiki/Baron_<PERSON>_of_Glendale\" title=\"Baron <PERSON> of Glendale\">Baron <PERSON> of Glendale</a> (1960 - 1973), <a href=\"https://wikipedia.org/wiki/Governor_of_Kenya\" class=\"mw-redirect\" title=\"Governor of Kenya\">Governor of Kenya</a> (1952 - 1959), <a href=\"https://wikipedia.org/wiki/High_Commissioner_for_Southern_Africa\" title=\"High Commissioner for Southern Africa\">High Commissioner for Southern Africa</a> (1944 - 1951), <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (1942 - 1944) (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>_of_Glendale\" title=\"<PERSON>, 1st Baron <PERSON> of Glendale\"><PERSON></a>, 1st <a href=\"https://wikipedia.org/wiki/Baron_<PERSON><PERSON>_of_Glendale\" title=\"Baron <PERSON> of Glendale\">Baron <PERSON> of Glendale</a> (1960 - 1973), <a href=\"https://wikipedia.org/wiki/Governor_of_Kenya\" class=\"mw-redirect\" title=\"Governor of Kenya\">Governor of Kenya</a> (1952 - 1959), <a href=\"https://wikipedia.org/wiki/High_Commissioner_for_Southern_Africa\" title=\"High Commissioner for Southern Africa\">High Commissioner for Southern Africa</a> (1944 - 1951), <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (1942 - 1944) (b. 1903)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Glendale", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON><PERSON>_of_Glendale"}, {"title": "Baron <PERSON> of Glendale", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_of_Glendale"}, {"title": "Governor of Kenya", "link": "https://wikipedia.org/wiki/Governor_of_Kenya"}, {"title": "High Commissioner for Southern Africa", "link": "https://wikipedia.org/wiki/High_Commissioner_for_Southern_Africa"}, {"title": "Governor of Southern Rhodesia", "link": "https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia"}]}, {"year": "1973", "text": "<PERSON>, Chinese lieutenant general and anti-communist, Taiwanese nationalist (b. 1902)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Republic_of_China_general)\" title=\"<PERSON> (Republic of China general)\"><PERSON></a>, Chinese lieutenant general and anti-communist, Taiwanese nationalist (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Republic_of_China_general)\" title=\"<PERSON> (Republic of China general)\"><PERSON></a>, Chinese lieutenant general and anti-communist, Taiwanese nationalist (b. 1902)", "links": [{"title": "<PERSON> (Republic of China general)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Republic_of_China_general)"}]}, {"year": "1973", "text": "<PERSON>, British politician, incumbent Governor of Bermuda (1972-1973) (b. 1916)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, incumbent <a href=\"https://wikipedia.org/wiki/Governor_of_Bermuda\" title=\"Governor of Bermuda\">Governor of Bermuda</a> (1972-1973) (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British politician, incumbent <a href=\"https://wikipedia.org/wiki/Governor_of_Bermuda\" title=\"Governor of Bermuda\">Governor of Bermuda</a> (1972-1973) (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Bermuda", "link": "https://wikipedia.org/wiki/Governor_of_Bermuda"}]}, {"year": "1977", "text": "<PERSON><PERSON>, English-American organist and composer (b. 1906)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/E._Power_Biggs\" title=\"E. Power Biggs\"><PERSON><PERSON></a>, English-American organist and composer (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E._Power_Biggs\" title=\"E. Power Biggs\"><PERSON><PERSON> <PERSON></a>, English-American organist and composer (b. 1906)", "links": [{"title": "E. Power Biggs", "link": "https://wikipedia.org/wiki/E._Power_Biggs"}]}, {"year": "1985", "text": "<PERSON>, Russian soldier and politician, Head of State of The Soviet Union (b. 1911)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of The Soviet Union</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian soldier and politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union\" title=\"List of heads of state of the Soviet Union\">Head of State of The Soviet Union</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of heads of state of the Soviet Union", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Soviet_Union"}]}, {"year": "1985", "text": "<PERSON>, American baseball player (b. 1927)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Welsh-American actor and director (b. 1907)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1988", "text": "<PERSON>, Australian singer-songwriter and actor (b. 1958)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actor (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter and actor (b. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Greek bouzouki player and composer (b. 1925)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player and composer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek <a href=\"https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">bouzouki</a> player and composer (b. 1925)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Giorgos_Z<PERSON>s"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uz<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American film producer (b. 1926)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, American film producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hunter\"><PERSON></a>, American film producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, American singer and actress (b. 1929)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer and actress (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and director (b. 1913)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Bridges\"><PERSON></a>, American actor and director (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON>, Ecuadorian painter and sculptor (b. 1919)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian painter and sculptor (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%ADn\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian painter and sculptor (b. 1919)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oswaldo_Guayasam%C3%ADn"}]}, {"year": "2005", "text": "<PERSON>, Irish-English comedian, actor, and screenwriter (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Irish-English comedian, actor, and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, Irish-English comedian, actor, and screenwriter (b. 1936)", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(comedian)"}]}, {"year": "2007", "text": "<PERSON>, American football player and wrestler (b. 1938)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and wrestler (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Egyptian scholar and academic (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian scholar and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Egyptian scholar and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian actor (b. 1971)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor (b. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, American author and illustrator (b. 1926)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, French author and illustrator (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and illustrator (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1927)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "2013", "text": "Princess <PERSON><PERSON>, Duchess of Halland, British born Swedish Princess (b.1915)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Duchess_of_Halland\" title=\"Princess <PERSON><PERSON>, Duchess of Halland\">Princess <PERSON><PERSON>, Duchess of Halland</a>, British born Swedish Princess (b.1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Duchess_of_Halland\" title=\"Princess <PERSON><PERSON>, Duchess of Halland\">Princess <PERSON><PERSON>, Duchess of Halland</a>, British born Swedish Princess (b.1915)", "links": [{"title": "Princess <PERSON><PERSON>, Duchess of Halland", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>,_Duchess_of_Halland"}]}, {"year": "2015", "text": "<PERSON>, American director, producer, and screenwriter (b. 1952)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, German-English production designer and art director (b. 1921)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English production designer and art director (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Adam\"><PERSON></a>, German-English production designer and art director (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Argentinian footballer and sportscaster (b. 1942)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and sportscaster (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and sportscaster (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, Filipino lawyer and politician, 14th President of the Senate of the Philippines (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician, 14th <a href=\"https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines\" title=\"President of the Senate of the Philippines\">President of the Senate of the Philippines</a> (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ga"}, {"title": "President of the Senate of the Philippines", "link": "https://wikipedia.org/wiki/President_of_the_Senate_of_the_Philippines"}]}, {"year": "2016", "text": "<PERSON>, English novelist and art historian (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and art historian (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and art historian (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, English historian and academic (b. 1930)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(historian)\" class=\"mw-redirect\" title=\"<PERSON> (historian)\"><PERSON></a>, English historian and academic (b. 1930)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>(historian)"}]}]}}