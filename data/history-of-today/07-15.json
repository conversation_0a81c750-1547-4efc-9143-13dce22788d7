{"date": "July 15", "url": "https://wikipedia.org/wiki/July_15", "data": {"Events": [{"year": "484 BC", "text": "Dedication of the Temple of Castor and Pollux in ancient Rome", "html": "484 BC - 484 BC - Dedication of the <a href=\"https://wikipedia.org/wiki/Temple_of_Castor_and_Pollux\" title=\"Temple of Castor and Pollux\">Temple of Castor and Pollux</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">ancient Rome</a>", "no_year_html": "484 BC - Dedication of the <a href=\"https://wikipedia.org/wiki/Temple_of_Castor_and_Pollux\" title=\"Temple of Castor and Pollux\">Temple of Castor and Pollux</a> in <a href=\"https://wikipedia.org/wiki/Ancient_Rome\" title=\"Ancient Rome\">ancient Rome</a>", "links": [{"title": "Temple of Castor and Pollux", "link": "https://wikipedia.org/wiki/Temple_of_Castor_and_Pollux"}, {"title": "Ancient Rome", "link": "https://wikipedia.org/wiki/Ancient_Rome"}]}, {"year": "70", "text": "First Jewish-Roman War: <PERSON> and his armies breach the walls of Jerusalem. (17th of Tammuz in the Hebrew calendar).", "html": "70 - <a href=\"https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War\" title=\"First Jewish-Roman War\">First Jewish-Roman War</a>: <a href=\"https://wikipedia.org/wiki/Titus\" title=\"<PERSON>\"><PERSON></a> and his armies <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">breach the walls of Jerusalem</a>. (<a href=\"https://wikipedia.org/wiki/17th_of_Tammuz\" class=\"mw-redirect\" title=\"17th of Tammuz\">17th of Tammuz</a> in the Hebrew calendar).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War\" title=\"First Jewish-Roman War\">First Jewish-Roman War</a>: <a href=\"https://wikipedia.org/wiki/Titus\" title=\"<PERSON>\"><PERSON></a> and his armies <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)\" class=\"mw-redirect\" title=\"Siege of Jerusalem (AD 70)\">breach the walls of Jerusalem</a>. (<a href=\"https://wikipedia.org/wiki/17th_of_Tammuz\" class=\"mw-redirect\" title=\"17th of Tammuz\">17th of Tammuz</a> in the Hebrew calendar).", "links": [{"title": "First Jewish-Roman War", "link": "https://wikipedia.org/wiki/First_Jewish%E2%80%93Roman_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Titus"}, {"title": "Siege of Jerusalem (AD 70)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(AD_70)"}, {"title": "17th of Tammuz", "link": "https://wikipedia.org/wiki/17th_of_Tammuz"}]}, {"year": "756", "text": "An Lushan Rebellion: Emperor <PERSON><PERSON><PERSON> of Tang is ordered by his Imperial Guards to execute chancellor <PERSON> by forcing him to commit suicide or face a mutiny. General <PERSON> has other members of the emperor's family killed.", "html": "756 - <a href=\"https://wikipedia.org/wiki/An_Lushan_Rebellion\" class=\"mw-redirect\" title=\"An Lushan Rebellion\">An Lushan Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON>zong of Tang</a> is ordered by his <a href=\"https://wikipedia.org/wiki/Imperial_Guards_(Tang_dynasty)\" title=\"Imperial Guards (Tang dynasty)\">Imperial Guards</a> to execute chancellor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by forcing him to commit suicide or face a mutiny. General <a href=\"https://wikipedia.org/wiki/An_Lushan\" title=\"An Lushan\"><PERSON></a> has other members of the emperor's family killed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An_Lushan_Rebellion\" class=\"mw-redirect\" title=\"An Lushan Rebellion\">An Lushan Rebellion</a>: <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\">Emperor <PERSON><PERSON>zong of Tang</a> is ordered by his <a href=\"https://wikipedia.org/wiki/Imperial_Guards_(Tang_dynasty)\" title=\"Imperial Guards (Tang dynasty)\">Imperial Guards</a> to execute chancellor <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> by forcing him to commit suicide or face a mutiny. General <a href=\"https://wikipedia.org/wiki/An_Lushan\" title=\"An Lushan\">An Lushan</a> has other members of the emperor's family killed.", "links": [{"title": "An Lushan Rebellion", "link": "https://wikipedia.org/wiki/An_Lushan_Rebellion"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang"}, {"title": "Imperial Guards (Tang dynasty)", "link": "https://wikipedia.org/wiki/Imperial_Guards_(Tang_dynasty)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>shan"}]}, {"year": "1099", "text": "First Crusade: Christian soldiers take the Church of the Holy Sepulchre in Jerusalem after the final assault of a difficult siege.", "html": "1099 - <a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: Christian soldiers take the <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> after the final assault of <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">a difficult siege</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Crusade\" title=\"First Crusade\">First Crusade</a>: Christian soldiers take the <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a> after the final assault of <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">a difficult siege</a>.", "links": [{"title": "First Crusade", "link": "https://wikipedia.org/wiki/First_Crusade"}, {"title": "Church of the Holy Sepulchre", "link": "https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}, {"title": "Siege of Jerusalem (1099)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)"}]}, {"year": "1149", "text": "The reconstructed Church of the Holy Sepulchre is consecrated in Jerusalem.", "html": "1149 - The reconstructed <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> is consecrated in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "The reconstructed <a href=\"https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre\" title=\"Church of the Holy Sepulchre\">Church of the Holy Sepulchre</a> is consecrated in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "Church of the Holy Sepulchre", "link": "https://wikipedia.org/wiki/Church_of_the_Holy_Sepulchre"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1207", "text": "King <PERSON> of England expels Canterbury monks for supporting Archbishop <PERSON>.", "html": "1207 - King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> expels <a href=\"https://wikipedia.org/wiki/Canterbury\" title=\"Canterbury\">Canterbury</a> monks for supporting <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>,_King_of_England\" title=\"<PERSON>, King of England\"><PERSON> of England</a> expels <a href=\"https://wikipedia.org/wiki/Canterbury\" title=\"Canterbury\">Canterbury</a> monks for supporting <a href=\"https://wikipedia.org/wiki/Archbishop_of_Canterbury\" title=\"Archbishop of Canterbury\">Archbishop</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>, King of England", "link": "https://wikipedia.org/wiki/<PERSON>,_King_of_England"}, {"title": "Canterbury", "link": "https://wikipedia.org/wiki/Canterbury"}, {"title": "Archbishop of Canterbury", "link": "https://wikipedia.org/wiki/Archbishop_of_Canterbury"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1240", "text": "Swedish-Novgorodian Wars: A Novgorodian army led by <PERSON> defeats the Swedes in the Battle of the Neva.", "html": "1240 - <a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Novgorodian_Wars\" title=\"Swedish-Novgorodian Wars\">Swedish-Novgorodian Wars</a>: A <a href=\"https://wikipedia.org/wiki/Veliky_Novgorod\" title=\"Veliky Novgorod\">Novgorodian</a> army led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Swedes</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Neva\" title=\"Battle of the Neva\">Battle of the Neva</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swedish%E2%80%93Novgorodian_Wars\" title=\"Swedish-Novgorodian Wars\">Swedish-Novgorodian Wars</a>: A <a href=\"https://wikipedia.org/wiki/Veliky_Novgorod\" title=\"Veliky Novgorod\">Novgorodian</a> army led by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> defeats the <a href=\"https://wikipedia.org/wiki/Sweden\" title=\"Sweden\">Swedes</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Neva\" title=\"Battle of the Neva\">Battle of the Neva</a>.", "links": [{"title": "Swedish-Novgorodian Wars", "link": "https://wikipedia.org/wiki/Swedish%E2%80%93Novgorodian_Wars"}, {"title": "Veliky Novgorod", "link": "https://wikipedia.org/wiki/Veliky_Novgorod"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Sweden", "link": "https://wikipedia.org/wiki/Sweden"}, {"title": "Battle of the Neva", "link": "https://wikipedia.org/wiki/Battle_of_the_Neva"}]}, {"year": "1381", "text": "<PERSON>, a leader in the Peasants' Revolt, is hanged, drawn and quartered in the presence of King <PERSON> of England.", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, a leader in the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>, is <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">hanged, drawn and quartered</a> in the presence of King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, a leader in the <a href=\"https://wikipedia.org/wiki/Peasants%27_Revolt\" title=\"Peasants' Revolt\">Peasants' Revolt</a>, is <a href=\"https://wikipedia.org/wiki/Hanged,_drawn_and_quartered\" title=\"Hanged, drawn and quartered\">hanged, drawn and quartered</a> in the presence of <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a>.", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}, {"title": "Peasants' Revolt", "link": "https://wikipedia.org/wiki/Peasants%27_<PERSON>olt"}, {"title": "Hanged, drawn and quartered", "link": "https://wikipedia.org/wiki/Hanged,_drawn_and_quartered"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1410", "text": "Polish-Lithuanian-Teutonic War: Battle of Grunwald: The allied forces of the Kingdom of Poland and the Grand Duchy of Lithuania defeat the army of the Teutonic Order.", "html": "1410 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian%E2%80%93Teutonic_War\" title=\"Polish-Lithuanian-Teutonic War\">Polish-Lithuanian-Teutonic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Grunwald\" title=\"Battle of Grunwald\">Battle of Grunwald</a>: The allied forces of the <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Jagiellonian_dynasty\" title=\"History of Poland during the Jagiellonian dynasty\">Kingdom of Poland</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeat the army of the <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Order</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian%E2%80%93Teutonic_War\" title=\"Polish-Lithuanian-Teutonic War\">Polish-Lithuanian-Teutonic War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Grunwald\" title=\"Battle of Grunwald\">Battle of Grunwald</a>: The allied forces of the <a href=\"https://wikipedia.org/wiki/History_of_Poland_during_the_Jagiellonian_dynasty\" title=\"History of Poland during the Jagiellonian dynasty\">Kingdom of Poland</a> and the <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania\" title=\"Grand Duchy of Lithuania\">Grand Duchy of Lithuania</a> defeat the army of the <a href=\"https://wikipedia.org/wiki/Teutonic_Knights\" class=\"mw-redirect\" title=\"Teutonic Knights\">Teutonic Order</a>.", "links": [{"title": "Polish-Lithuanian-Teutonic War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Lithuanian%E2%80%93Teutonic_War"}, {"title": "Battle of Grunwald", "link": "https://wikipedia.org/wiki/Battle_of_Grunwald"}, {"title": "History of Poland during the Jagiellonian dynasty", "link": "https://wikipedia.org/wiki/History_of_Poland_during_the_Jagiellonian_dynasty"}, {"title": "Grand Duchy of Lithuania", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Lithuania"}, {"title": "Teutonic Knights", "link": "https://wikipedia.org/wiki/Teutonic_Knights"}]}, {"year": "1482", "text": "<PERSON> is crowned the twenty-second and last Nasrid king of Granada.", "html": "1482 - <a href=\"https://wikipedia.org/wiki/<PERSON>_XII_of_Granada\" title=\"Muhammad XII of Granada\"><PERSON></a> is crowned the twenty-second and last <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Na<PERSON><PERSON></a> king of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_XII_of_Granada\" title=\"Muhammad XII of Granada\"><PERSON></a> is crowned the twenty-second and last <a href=\"https://wikipedia.org/wiki/Nasrid_dynasty\" title=\"Nasrid dynasty\">Na<PERSON><PERSON></a> king of <a href=\"https://wikipedia.org/wiki/Emirate_of_Granada\" title=\"Emirate of Granada\">Granada</a>.", "links": [{"title": "<PERSON> of Granada", "link": "https://wikipedia.org/wiki/Muhammad_XII_of_Granada"}, {"title": "Nasrid dynasty", "link": "https://wikipedia.org/wiki/Nasrid_dynasty"}, {"title": "Emirate of Granada", "link": "https://wikipedia.org/wiki/Emirate_of_Granada"}]}, {"year": "1640", "text": "The first university of Finland, the Royal Academy of Turku, is inaugurated in Turku.", "html": "1640 - The first university of Finland, the <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Turku\" title=\"Royal Academy of Turku\">Royal Academy of Turku</a>, is inaugurated in <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a>.", "no_year_html": "The first university of Finland, the <a href=\"https://wikipedia.org/wiki/Royal_Academy_of_Turku\" title=\"Royal Academy of Turku\">Royal Academy of Turku</a>, is inaugurated in <a href=\"https://wikipedia.org/wiki/Turku\" title=\"Turku\">Turku</a>.", "links": [{"title": "Royal Academy of Turku", "link": "https://wikipedia.org/wiki/Royal_Academy_of_Turku"}, {"title": "Turku", "link": "https://wikipedia.org/wiki/Turku"}]}, {"year": "1738", "text": "<PERSON><PERSON> and <PERSON> are burned alive in St. Petersburg, Russia. <PERSON><PERSON><PERSON> had converted to Judaism with <PERSON><PERSON><PERSON>'s help, with the consent of Empress <PERSON>.", "html": "1738 - <PERSON><PERSON> and <PERSON> are burned alive in <a href=\"https://wikipedia.org/wiki/St._Petersburg\" class=\"mw-redirect\" title=\"St. Petersburg\">St. Petersburg</a>, Russia. <PERSON><PERSON><PERSON> had converted to Judaism with <PERSON><PERSON><PERSON>'s help, with the consent of <a href=\"https://wikipedia.org/wiki/Emperor_of_All_Russia\" class=\"mw-redirect\" title=\"Emperor of All Russia\">Empress</a> <a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"Anna of Russia\"><PERSON></a>.", "no_year_html": "<PERSON><PERSON> and <PERSON> are burned alive in <a href=\"https://wikipedia.org/wiki/St._Petersburg\" class=\"mw-redirect\" title=\"St. Petersburg\">St. Petersburg</a>, Russia. <PERSON><PERSON><PERSON> had converted to Judaism with <PERSON><PERSON><PERSON>'s help, with the consent of <a href=\"https://wikipedia.org/wiki/Emperor_of_All_Russia\" class=\"mw-redirect\" title=\"Emperor of All Russia\">Empress</a> <a href=\"https://wikipedia.org/wiki/Anna_of_Russia\" title=\"Anna of Russia\"><PERSON></a>.", "links": [{"title": "St. Petersburg", "link": "https://wikipedia.org/wiki/St._Petersburg"}, {"title": "Emperor of All Russia", "link": "https://wikipedia.org/wiki/Emperor_of_All_Russia"}, {"title": "Anna of Russia", "link": "https://wikipedia.org/wiki/Anna_of_Russia"}]}, {"year": "1741", "text": "<PERSON><PERSON><PERSON> sights land in Southeast Alaska. He sends men ashore in a longboat, making them the first Europeans to visit Alaska.", "html": "1741 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sights land in <a href=\"https://wikipedia.org/wiki/Southeast_Alaska\" title=\"Southeast Alaska\">Southeast Alaska</a>. He sends men ashore in a longboat, making them the first Europeans to visit <a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> sights land in <a href=\"https://wikipedia.org/wiki/Southeast_Alaska\" title=\"Southeast Alaska\">Southeast Alaska</a>. He sends men ashore in a longboat, making them the first Europeans to visit <a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Southeast Alaska", "link": "https://wikipedia.org/wiki/Southeast_Alaska"}, {"title": "Alaska", "link": "https://wikipedia.org/wiki/Alaska"}]}, {"year": "1789", "text": "French Revolution: <PERSON>, Marquis <PERSON>, is named by acclamation Colonel General of the new National Guard of Paris.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, is named by acclamation <a href=\"https://wikipedia.org/wiki/Colonel_General\" class=\"mw-redirect\" title=\"Colonel General\">Colonel General</a> of the new <a href=\"https://wikipedia.org/wiki/National_Guard_(France)\" title=\"National Guard (France)\">National Guard</a> of Paris.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON>, <PERSON></a>, is named by acclamation <a href=\"https://wikipedia.org/wiki/Colonel_General\" class=\"mw-redirect\" title=\"Colonel General\">Colonel General</a> of the new <a href=\"https://wikipedia.org/wiki/National_Guard_(France)\" title=\"National Guard (France)\">National Guard</a> of Paris.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON>, Marquis <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Colonel General", "link": "https://wikipedia.org/wiki/Colonel_General"}, {"title": "National Guard (France)", "link": "https://wikipedia.org/wiki/National_Guard_(France)"}]}, {"year": "1799", "text": "The Rosetta Stone is found in the Egyptian village of Rosetta by French Captain <PERSON><PERSON><PERSON> during <PERSON>'s Egyptian Campaign.", "html": "1799 - The <a href=\"https://wikipedia.org/wiki/Rose<PERSON>_Stone\" title=\"Rosetta Stone\"><PERSON><PERSON></a> is found in the <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> village of <a href=\"https://wikipedia.org/wiki/Rosetta\" title=\"Rosetta\">Rosetta</a> by French <a href=\"https://wikipedia.org/wiki/Captain_(armed_forces)\" title=\"Captain (armed forces)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> during <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">Egyptian Campaign</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Rose<PERSON>_Stone\" title=\"Rosetta Stone\"><PERSON><PERSON></a> is found in the <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> village of <a href=\"https://wikipedia.org/wiki/Rosetta\" title=\"Rosetta\">Rosetta</a> by French <a href=\"https://wikipedia.org/wiki/Captain_(armed_forces)\" title=\"Captain (armed forces)\">Captain</a> <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A7<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> during <a href=\"https://wikipedia.org/wiki/Napoleon\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">Egyptian Campaign</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rose<PERSON>_Stone"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rose<PERSON>"}, {"title": "Captain (armed forces)", "link": "https://wikipedia.org/wiki/Captain_(armed_forces)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%A<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "French campaign in Egypt and Syria", "link": "https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria"}]}, {"year": "1806", "text": "Pike Expedition: United States Army Lieutenant <PERSON><PERSON><PERSON> begins an expedition from Fort Bellefontaine near St. Louis, Missouri,  to explore the west.", "html": "1806 - <a href=\"https://wikipedia.org/wiki/Pike_Expedition\" title=\"Pike Expedition\">Pike Expedition</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> <a href=\"https://wikipedia.org/wiki/Lieutenant\" title=\"Lieutenant\">Lieutenant</a> <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\">Zebulon Pike</a> begins an expedition from <a href=\"https://wikipedia.org/wiki/Fort_Bellefontaine\" class=\"mw-redirect\" title=\"Fort Bellefontaine\">Fort Bellefontaine</a> near <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>, to explore the west.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pike_Expedition\" title=\"Pike Expedition\">Pike Expedition</a>: <a href=\"https://wikipedia.org/wiki/United_States_Army\" title=\"United States Army\">United States Army</a> <a href=\"https://wikipedia.org/wiki/Lieutenant\" title=\"Lieutenant\">Lieutenant</a> <a href=\"https://wikipedia.org/wiki/Zebulon_Pike\" title=\"Zebulon Pike\">Zebulon Pike</a> begins an expedition from <a href=\"https://wikipedia.org/wiki/Fort_Bellefontaine\" class=\"mw-redirect\" title=\"Fort Bellefontaine\">Fort Bellefontaine</a> near <a href=\"https://wikipedia.org/wiki/St._Louis,_Missouri\" class=\"mw-redirect\" title=\"St. Louis, Missouri\">St. Louis, Missouri</a>, to explore the west.", "links": [{"title": "Pike Expedition", "link": "https://wikipedia.org/wiki/Pike_Expedition"}, {"title": "United States Army", "link": "https://wikipedia.org/wiki/United_States_Army"}, {"title": "Lieutenant", "link": "https://wikipedia.org/wiki/Lieutenant"}, {"title": "Zebulon Pike", "link": "https://wikipedia.org/wiki/Zebulon_Pike"}, {"title": "Fort Bellefontaine", "link": "https://wikipedia.org/wiki/Fort_Bellefontaine"}, {"title": "St. Louis, Missouri", "link": "https://wikipedia.org/wiki/St._Louis,_Missouri"}]}, {"year": "1815", "text": "Napoleonic Wars: <PERSON> surrenders aboard HMS Bellerophon.", "html": "1815 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/Napoleon_Bonaparte\" class=\"mw-redirect\" title=\"Napoleon Bonaparte\"><PERSON></a> surrenders aboard <a href=\"https://wikipedia.org/wiki/HMS_Bellerophon_(1786)\" title=\"HMS Bellerophon (1786)\">HMS <i>Bellerophon</i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_Bonaparte\" class=\"mw-redirect\" title=\"Napoleon Bonaparte\"><PERSON></a> surrenders aboard <a href=\"https://wikipedia.org/wiki/HMS_Bellerophon_(1786)\" title=\"HMS Bellerophon (1786)\">HMS <i>Bellerophon</i></a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "HMS Bellerophon (1786)", "link": "https://wikipedia.org/wiki/HMS_Bellerophon_(1786)"}]}, {"year": "1823", "text": "A fire destroys the ancient Basilica of Saint Paul Outside the Walls in Rome, Italy.", "html": "1823 - A fire destroys the ancient <a href=\"https://wikipedia.org/wiki/Basilica_of_Saint_Paul_Outside_the_Walls\" title=\"Basilica of Saint Paul Outside the Walls\">Basilica of Saint Paul Outside the Walls</a> in Rome, Italy.", "no_year_html": "A fire destroys the ancient <a href=\"https://wikipedia.org/wiki/Basilica_of_Saint_Paul_Outside_the_Walls\" title=\"Basilica of Saint Paul Outside the Walls\">Basilica of Saint Paul Outside the Walls</a> in Rome, Italy.", "links": [{"title": "Basilica of Saint Paul Outside the Walls", "link": "https://wikipedia.org/wiki/Basilica_of_Saint_Paul_Outside_the_Walls"}]}, {"year": "1834", "text": "The Spanish Inquisition is officially disbanded after nearly 356 years.", "html": "1834 - The <a href=\"https://wikipedia.org/wiki/Spanish_Inquisition\" title=\"Spanish Inquisition\">Spanish Inquisition</a> is officially disbanded after nearly 356 years.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Spanish_Inquisition\" title=\"Spanish Inquisition\">Spanish Inquisition</a> is officially disbanded after nearly 356 years.", "links": [{"title": "Spanish Inquisition", "link": "https://wikipedia.org/wiki/Spanish_Inquisition"}]}, {"year": "1838", "text": "<PERSON> delivers the Divinity School Address at Harvard Divinity School, discounting Biblical miracles and declaring <PERSON> a great man, but not <PERSON>. The Protestant community reacts with outrage.", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Divinity_School_Address\" title=\"Divinity School Address\">Divinity School Address</a> at <a href=\"https://wikipedia.org/wiki/Harvard_Divinity_School\" title=\"Harvard Divinity School\">Harvard Divinity School</a>, discounting <a href=\"https://wikipedia.org/wiki/Biblical_miracles\" class=\"mw-redirect\" title=\"Biblical miracles\">Biblical miracles</a> and declaring <a href=\"https://wikipedia.org/wiki/Jesus\" title=\"Jesus\">Jesus</a> a great man, but not God. The <a href=\"https://wikipedia.org/wiki/Protestantism_in_the_United_States\" title=\"Protestantism in the United States\">Protestant</a> community reacts with outrage.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers the <a href=\"https://wikipedia.org/wiki/Divinity_School_Address\" title=\"Divinity School Address\">Divinity School Address</a> at <a href=\"https://wikipedia.org/wiki/Harvard_Divinity_School\" title=\"Harvard Divinity School\">Harvard Divinity School</a>, discounting <a href=\"https://wikipedia.org/wiki/Biblical_miracles\" class=\"mw-redirect\" title=\"Biblical miracles\">Biblical miracles</a> and declaring <a href=\"https://wikipedia.org/wiki/Jesus\" title=\"Jesus\">Jesus</a> a great man, but not God. The <a href=\"https://wikipedia.org/wiki/Protestantism_in_the_United_States\" title=\"Protestantism in the United States\">Protestant</a> community reacts with outrage.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Divinity School Address", "link": "https://wikipedia.org/wiki/Divinity_School_Address"}, {"title": "Harvard Divinity School", "link": "https://wikipedia.org/wiki/Harvard_Divinity_School"}, {"title": "Biblical miracles", "link": "https://wikipedia.org/wiki/Biblical_miracles"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Protestantism in the United States", "link": "https://wikipedia.org/wiki/Protestantism_in_the_United_States"}]}, {"year": "1849", "text": "The first air raid in history occurs; Austria launches pilotless balloons against the city of Venice", "html": "1849 - The first <a href=\"https://wikipedia.org/wiki/Airstrike\" title=\"Airstrike\">air raid</a> in history occurs; Austria launches pilotless balloons against the city of <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Airstrike\" title=\"Airstrike\">air raid</a> in history occurs; Austria launches pilotless balloons against the city of <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>", "links": [{"title": "Airstrike", "link": "https://wikipedia.org/wiki/Airstrike"}, {"title": "Venice", "link": "https://wikipedia.org/wiki/Venice"}]}, {"year": "1862", "text": "American Civil War: The CSS Arkansas, the most effective ironclad on the Mississippi River, battles with Union Navy ships commanded by Admiral <PERSON>, severely damaging three ships and sustaining heavy damage herself.", "html": "1862 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/CSS_Arkansas\" title=\"CSS Arkansas\">CSS <i>Arkansas</i></a>, the most effective <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad</a> on the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, battles with <a href=\"https://wikipedia.org/wiki/Union_Navy\" title=\"Union Navy\">Union Navy</a> ships commanded by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, severely damaging three ships and sustaining heavy damage herself.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: The <a href=\"https://wikipedia.org/wiki/CSS_Arkansas\" title=\"CSS Arkansas\">CSS <i>Arkansas</i></a>, the most effective <a href=\"https://wikipedia.org/wiki/Ironclad_warship\" title=\"Ironclad warship\">ironclad</a> on the <a href=\"https://wikipedia.org/wiki/Mississippi_River\" title=\"Mississippi River\">Mississippi River</a>, battles with <a href=\"https://wikipedia.org/wiki/Union_Navy\" title=\"Union Navy\">Union Navy</a> ships commanded by Admiral <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, severely damaging three ships and sustaining heavy damage herself.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "CSS Arkansas", "link": "https://wikipedia.org/wiki/CSS_Arkansas"}, {"title": "Ironclad warship", "link": "https://wikipedia.org/wiki/Ironclad_warship"}, {"title": "Mississippi River", "link": "https://wikipedia.org/wiki/Mississippi_River"}, {"title": "Union Navy", "link": "https://wikipedia.org/wiki/Union_Navy"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1870", "text": "Reconstruction Era of the United States: Georgia becomes the last of the former Confederate states to be readmitted to the Union.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Reconstruction_Era_of_the_United_States\" class=\"mw-redirect\" title=\"Reconstruction Era of the United States\">Reconstruction Era of the United States</a>: <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> becomes the last of the former <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> states to be readmitted to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reconstruction_Era_of_the_United_States\" class=\"mw-redirect\" title=\"Reconstruction Era of the United States\">Reconstruction Era of the United States</a>: <a href=\"https://wikipedia.org/wiki/Georgia_(U.S._state)\" title=\"Georgia (U.S. state)\">Georgia</a> becomes the last of the former <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> states to be readmitted to the <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a>.", "links": [{"title": "Reconstruction Era of the United States", "link": "https://wikipedia.org/wiki/Reconstruction_Era_of_the_United_States"}, {"title": "Georgia (U.S. state)", "link": "https://wikipedia.org/wiki/Georgia_(U.S._state)"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}]}, {"year": "1870", "text": "Canadian Confederation: Rupert's Land and the North-Western Territory are transferred to Canada from the Hudson's Bay Company, and the province of Manitoba and the Northwest Territories are established from these vast territories.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>: <a href=\"https://wikipedia.org/wiki/Rupert%27s_Land\" title=\"Rupert's Land\">Rupert's Land</a> and the <a href=\"https://wikipedia.org/wiki/North-Western_Territory\" title=\"North-Western Territory\">North-Western Territory</a> are transferred to Canada from the <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a>, and the province of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> and the <a href=\"https://wikipedia.org/wiki/Northwest_Territories\" title=\"Northwest Territories\">Northwest Territories</a> are established from these vast territories.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canadian_Confederation\" title=\"Canadian Confederation\">Canadian Confederation</a>: <a href=\"https://wikipedia.org/wiki/Rupert%27s_Land\" title=\"Rupert's Land\">Rupert's Land</a> and the <a href=\"https://wikipedia.org/wiki/North-Western_Territory\" title=\"North-Western Territory\">North-Western Territory</a> are transferred to Canada from the <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a>, and the province of <a href=\"https://wikipedia.org/wiki/Manitoba\" title=\"Manitoba\">Manitoba</a> and the <a href=\"https://wikipedia.org/wiki/Northwest_Territories\" title=\"Northwest Territories\">Northwest Territories</a> are established from these vast territories.", "links": [{"title": "Canadian Confederation", "link": "https://wikipedia.org/wiki/Canadian_Confederation"}, {"title": "Rupert's Land", "link": "https://wikipedia.org/wiki/Rupert%27s_Land"}, {"title": "North-Western Territory", "link": "https://wikipedia.org/wiki/North-Western_Territory"}, {"title": "Hudson's Bay Company", "link": "https://wikipedia.org/wiki/Hudson%27s_Bay_Company"}, {"title": "Manitoba", "link": "https://wikipedia.org/wiki/Manitoba"}, {"title": "Northwest Territories", "link": "https://wikipedia.org/wiki/Northwest_Territories"}]}, {"year": "1888", "text": "The stratovolcano Mount Bandai erupts, killing approximately 500 people in Fukushima Prefecture, Japan.", "html": "1888 - The <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Mount_Bandai\" title=\"Mount Bandai\">Mount Bandai</a> <a href=\"https://wikipedia.org/wiki/1888_eruption_of_Mount_Bandai\" title=\"1888 eruption of Mount Bandai\">erupts</a>, killing approximately 500 people in <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima Prefecture</a>, Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Stratovolcano\" title=\"Stratovolcano\">stratovolcano</a> <a href=\"https://wikipedia.org/wiki/Mount_Bandai\" title=\"Mount Bandai\">Mount Bandai</a> <a href=\"https://wikipedia.org/wiki/1888_eruption_of_Mount_Bandai\" title=\"1888 eruption of Mount Bandai\">erupts</a>, killing approximately 500 people in <a href=\"https://wikipedia.org/wiki/Fukushima_Prefecture\" title=\"Fukushima Prefecture\">Fukushima Prefecture</a>, Japan.", "links": [{"title": "Stratovolcano", "link": "https://wikipedia.org/wiki/Stratovolcano"}, {"title": "Mount Bandai", "link": "https://wikipedia.org/wiki/Mount_Bandai"}, {"title": "1888 eruption of Mount Bandai", "link": "https://wikipedia.org/wiki/1888_eruption_of_Mount_Bandai"}, {"title": "Fukushima Prefecture", "link": "https://wikipedia.org/wiki/Fukushima_Prefecture"}]}, {"year": "1910", "text": "In his book Clinical Psychiatry, <PERSON> gives a name to Alzheimer's disease, naming it after his colleague <PERSON><PERSON>.", "html": "1910 - In his book <i>Clinical Psychiatry</i>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives a name to <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a>, naming it after his colleague <a href=\"https://wikipedia.org/wiki/Alois_Alzheimer\" title=\"Alois Alzheimer\"><PERSON>ois Alzheimer</a>.", "no_year_html": "In his book <i>Clinical Psychiatry</i>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives a name to <a href=\"https://wikipedia.org/wiki/Alzheimer%27s_disease\" title=\"Alzheimer's disease\">Alzheimer's disease</a>, naming it after his colleague <a href=\"https://wikipedia.org/wiki/Alois_Alzheimer\" title=\"Alois Alzheimer\"><PERSON>ois Alzheimer</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Alzheimer's disease", "link": "https://wikipedia.org/wiki/Alzheimer%27s_disease"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "In Seattle, Washington, William <PERSON> and <PERSON>erve<PERSON> incorporate Pacific Aero Products (later renamed Boeing).", "html": "1916 - In <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle, Washington</a>, <a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\">William <PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporate Pacific Aero Products (later renamed <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing</a>).", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Seattle\" title=\"Seattle\">Seattle, Washington</a>, <a href=\"https://wikipedia.org/wiki/William_Boeing\" class=\"mw-redirect\" title=\"William Boeing\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> incorporate Pacific Aero Products (later renamed <a href=\"https://wikipedia.org/wiki/Boeing\" title=\"Boeing\">Boeing</a>).", "links": [{"title": "Seattle", "link": "https://wikipedia.org/wiki/Seattle"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Boeing"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>erve<PERSON>"}, {"title": "Boeing", "link": "https://wikipedia.org/wiki/Boeing"}]}, {"year": "1918", "text": "World War I: The Second Battle of the Marne begins near the River Marne with a German attack.", "html": "1918 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_the_Marne\" title=\"Second Battle of the Marne\">Second Battle of the Marne</a> begins near the <a href=\"https://wikipedia.org/wiki/Marne_(river)\" title=\"Marne (river)\">River Marne</a> with a German attack.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_the_Marne\" title=\"Second Battle of the Marne\">Second Battle of the Marne</a> begins near the <a href=\"https://wikipedia.org/wiki/Marne_(river)\" title=\"Marne (river)\">River Marne</a> with a German attack.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Second Battle of the Marne", "link": "https://wikipedia.org/wiki/Second_Battle_of_the_Marne"}, {"title": "Marne (river)", "link": "https://wikipedia.org/wiki/Marne_(river)"}]}, {"year": "1920", "text": "Aftermath of World War I: The Parliament of Poland establishes Silesian Voivodeship before the Polish-German plebiscite.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Aftermath_of_World_War_I\" title=\"Aftermath of World War I\">Aftermath of World War I</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_Poland\" title=\"Parliament of Poland\">Parliament of Poland</a> establishes <a href=\"https://wikipedia.org/wiki/Silesian_Voivodeship_(1920%E2%80%9339)\" class=\"mw-redirect\" title=\"Silesian Voivodeship (1920-39)\">Silesian Voivodeship</a> before the <a href=\"https://wikipedia.org/wiki/Upper_Silesia_plebiscite\" class=\"mw-redirect\" title=\"Upper Silesia plebiscite\">Polish-German plebiscite</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aftermath_of_World_War_I\" title=\"Aftermath of World War I\">Aftermath of World War I</a>: The <a href=\"https://wikipedia.org/wiki/Parliament_of_Poland\" title=\"Parliament of Poland\">Parliament of Poland</a> establishes <a href=\"https://wikipedia.org/wiki/Silesian_Voivodeship_(1920%E2%80%9339)\" class=\"mw-redirect\" title=\"Silesian Voivodeship (1920-39)\">Silesian Voivodeship</a> before the <a href=\"https://wikipedia.org/wiki/Upper_Silesia_plebiscite\" class=\"mw-redirect\" title=\"Upper Silesia plebiscite\">Polish-German plebiscite</a>.", "links": [{"title": "Aftermath of World War I", "link": "https://wikipedia.org/wiki/Aftermath_of_World_War_I"}, {"title": "Parliament of Poland", "link": "https://wikipedia.org/wiki/Parliament_of_Poland"}, {"title": "Silesian Voivodeship (1920-39)", "link": "https://wikipedia.org/wiki/Silesian_Voivodeship_(1920%E2%80%9339)"}, {"title": "Upper Silesia plebiscite", "link": "https://wikipedia.org/wiki/Upper_Silesia_plebiscite"}]}, {"year": "1922", "text": "The Japanese Communist Party is established in Japan.", "html": "1922 - The <a href=\"https://wikipedia.org/wiki/Japanese_Communist_Party\" title=\"Japanese Communist Party\">Japanese Communist Party</a> is established in Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Japanese_Communist_Party\" title=\"Japanese Communist Party\">Japanese Communist Party</a> is established in Japan.", "links": [{"title": "Japanese Communist Party", "link": "https://wikipedia.org/wiki/Japanese_Communist_Party"}]}, {"year": "1927", "text": "Massacre of July 15, 1927: Eighty-nine protesters are killed by Austrian police in Vienna.", "html": "1927 - <a href=\"https://wikipedia.org/wiki/July_Revolt_of_1927\" title=\"July Revolt of 1927\">Massacre of July 15, 1927</a>: Eighty-nine protesters are killed by Austrian police in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/July_Revolt_of_1927\" title=\"July Revolt of 1927\">Massacre of July 15, 1927</a>: Eighty-nine protesters are killed by Austrian police in <a href=\"https://wikipedia.org/wiki/Vienna\" title=\"Vienna\">Vienna</a>.", "links": [{"title": "July Revolt of 1927", "link": "https://wikipedia.org/wiki/July_Revolt_of_1927"}, {"title": "Vienna", "link": "https://wikipedia.org/wiki/Vienna"}]}, {"year": "1941", "text": "The Holocaust: Nazi Germany begins the deportation of 100,000 Jews from the occupied Netherlands to extermination camps.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins the deportation of 100,000 Jews from the <a href=\"https://wikipedia.org/wiki/Netherlands_in_World_War_II\" title=\"Netherlands in World War II\">occupied Netherlands</a> to <a href=\"https://wikipedia.org/wiki/Extermination_camp\" title=\"Extermination camp\">extermination camps</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Holocaust\" title=\"The Holocaust\">The Holocaust</a>: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins the deportation of 100,000 Jews from the <a href=\"https://wikipedia.org/wiki/Netherlands_in_World_War_II\" title=\"Netherlands in World War II\">occupied Netherlands</a> to <a href=\"https://wikipedia.org/wiki/Extermination_camp\" title=\"Extermination camp\">extermination camps</a>.", "links": [{"title": "The Holocaust", "link": "https://wikipedia.org/wiki/The_Holocaust"}, {"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Netherlands in World War II", "link": "https://wikipedia.org/wiki/Netherlands_in_World_War_II"}, {"title": "Extermination camp", "link": "https://wikipedia.org/wiki/Extermination_camp"}]}, {"year": "1946", "text": "The State of North Borneo, now Sabah, Malaysia, is annexed by the United Kingdom.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/North_Borneo\" title=\"North Borneo\">State of North Borneo</a>, now <a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, is annexed by the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/North_Borneo\" title=\"North Borneo\">State of North Borneo</a>, now <a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>, <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a>, is annexed by the <a href=\"https://wikipedia.org/wiki/United_Kingdom\" title=\"United Kingdom\">United Kingdom</a>.", "links": [{"title": "North Borneo", "link": "https://wikipedia.org/wiki/North_Borneo"}, {"title": "Sabah", "link": "https://wikipedia.org/wiki/Sabah"}, {"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}, {"title": "United Kingdom", "link": "https://wikipedia.org/wiki/United_Kingdom"}]}, {"year": "1954", "text": "The Boeing 367-80, the prototype for both the Boeing 707 and C-135 series, takes its first flight.", "html": "1954 - The <a href=\"https://wikipedia.org/wiki/Boeing_367-80\" title=\"Boeing 367-80\">Boeing 367-80</a>, the prototype for both the <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> and <a href=\"https://wikipedia.org/wiki/Boeing_C-135_Stratolifter\" title=\"Boeing C-135 Stratolifter\">C-135</a> series, takes its first flight.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Boeing_367-80\" title=\"Boeing 367-80\">Boeing 367-80</a>, the prototype for both the <a href=\"https://wikipedia.org/wiki/Boeing_707\" title=\"Boeing 707\">Boeing 707</a> and <a href=\"https://wikipedia.org/wiki/Boeing_C-135_Stratolifter\" title=\"Boeing C-135 Stratolifter\">C-135</a> series, takes its first flight.", "links": [{"title": "Boeing 367-80", "link": "https://wikipedia.org/wiki/Boeing_367-80"}, {"title": "Boeing 707", "link": "https://wikipedia.org/wiki/Boeing_707"}, {"title": "Boeing C-135 Stratolifter", "link": "https://wikipedia.org/wiki/Boeing_C-135_Stratolifter"}]}, {"year": "1955", "text": "Eighteen Nobel laureates sign the Mainau Declaration against nuclear weapons, later co-signed by thirty-four others.", "html": "1955 - Eighteen <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel laureates</a> sign the <a href=\"https://wikipedia.org/wiki/Mainau_Declaration\" title=\"Mainau Declaration\">Mainau Declaration</a> against <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapons</a>, later co-signed by thirty-four others.", "no_year_html": "Eighteen <a href=\"https://wikipedia.org/wiki/Nobel_Prize\" title=\"Nobel Prize\">Nobel laureates</a> sign the <a href=\"https://wikipedia.org/wiki/Mainau_Declaration\" title=\"Mainau Declaration\">Mainau Declaration</a> against <a href=\"https://wikipedia.org/wiki/Nuclear_weapon\" title=\"Nuclear weapon\">nuclear weapons</a>, later co-signed by thirty-four others.", "links": [{"title": "Nobel Prize", "link": "https://wikipedia.org/wiki/Nobel_Prize"}, {"title": "Mainau Declaration", "link": "https://wikipedia.org/wiki/Mainau_Declaration"}, {"title": "Nuclear weapon", "link": "https://wikipedia.org/wiki/Nuclear_weapon"}]}, {"year": "1966", "text": "Vietnam War: The United States and South Vietnam begin Operation Hastings to push the North Vietnamese out of the Vietnamese Demilitarized Zone.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The United States and South Vietnam begin <a href=\"https://wikipedia.org/wiki/Operation_Hastings\" title=\"Operation Hastings\">Operation Hastings</a> to push the North Vietnamese out of the <a href=\"https://wikipedia.org/wiki/Vietnamese_Demilitarized_Zone\" title=\"Vietnamese Demilitarized Zone\">Vietnamese Demilitarized Zone</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: The United States and South Vietnam begin <a href=\"https://wikipedia.org/wiki/Operation_Hastings\" title=\"Operation Hastings\">Operation Hastings</a> to push the North Vietnamese out of the <a href=\"https://wikipedia.org/wiki/Vietnamese_Demilitarized_Zone\" title=\"Vietnamese Demilitarized Zone\">Vietnamese Demilitarized Zone</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Operation Hastings", "link": "https://wikipedia.org/wiki/Operation_Hastings"}, {"title": "Vietnamese Demilitarized Zone", "link": "https://wikipedia.org/wiki/Vietnamese_Demilitarized_Zone"}]}, {"year": "1971", "text": "The United Red Army is founded in Japan.", "html": "1971 - The <a href=\"https://wikipedia.org/wiki/United_Red_Army\" title=\"United Red Army\">United Red Army</a> is founded in Japan.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_Red_Army\" title=\"United Red Army\">United Red Army</a> is founded in Japan.", "links": [{"title": "United Red Army", "link": "https://wikipedia.org/wiki/United_Red_Army"}]}, {"year": "1974", "text": "In Nicosia, Cyprus, Greek junta-sponsored nationalists launch a coup d'état, deposing President <PERSON><PERSON><PERSON> and installing <PERSON><PERSON> as Cypriot president.", "html": "1974 - In <a href=\"https://wikipedia.org/wiki/Nicosia\" title=\"Nicosia\">Nicosia</a>, <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek junta</a>-sponsored nationalists launch a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, deposing President <a href=\"https://wikipedia.org/wiki/Makarios_III\" title=\"Makarios III\"><PERSON><PERSON><PERSON></a> and installing <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cypriot</a> <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">president</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Nicosia\" title=\"Nicosia\">Nicosia</a>, <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a>, <a href=\"https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374\" class=\"mw-redirect\" title=\"Greek military junta of 1967-74\">Greek junta</a>-sponsored nationalists launch a <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a>, deposing President <a href=\"https://wikipedia.org/wiki/Makarios_III\" title=\"Makarios III\"><PERSON><PERSON><PERSON></a> and installing <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cypriot</a> <a href=\"https://wikipedia.org/wiki/President_(government_title)\" title=\"President (government title)\">president</a>.", "links": [{"title": "Nicosia", "link": "https://wikipedia.org/wiki/Nicosia"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "Greek military junta of 1967-74", "link": "https://wikipedia.org/wiki/Greek_military_junta_of_1967%E2%80%9374"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>kari<PERSON>_III"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}, {"title": "President (government title)", "link": "https://wikipedia.org/wiki/President_(government_title)"}]}, {"year": "1975", "text": "Space Race: Apollo-Soyuz Test Project features the dual launch of an Apollo spacecraft and a Soyuz spacecraft on the first  Soviet-United States human-crewed flight. It was the last launch of both an Apollo spacecraft, and the Saturn family of rockets.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: <a href=\"https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project\" class=\"mw-redirect\" title=\"Apollo-Soyuz Test Project\">Apollo-Soyuz Test Project</a> features the dual launch of an <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo spacecraft</a> and a <a href=\"https://wikipedia.org/wiki/Soyuz_(spacecraft)\" title=\"Soyuz (spacecraft)\">Soyuz spacecraft</a> on the first Soviet-United States human-crewed flight. It was the last launch of both an Apollo spacecraft, and the <a href=\"https://wikipedia.org/wiki/Saturn_(rocket_family)\" title=\"Saturn (rocket family)\">Saturn family of rockets</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Race\" title=\"Space Race\">Space Race</a>: <a href=\"https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project\" class=\"mw-redirect\" title=\"Apollo-Soyuz Test Project\">Apollo-Soyuz Test Project</a> features the dual launch of an <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo spacecraft</a> and a <a href=\"https://wikipedia.org/wiki/Soyuz_(spacecraft)\" title=\"Soyuz (spacecraft)\">Soyuz spacecraft</a> on the first Soviet-United States human-crewed flight. It was the last launch of both an Apollo spacecraft, and the <a href=\"https://wikipedia.org/wiki/Saturn_(rocket_family)\" title=\"Saturn (rocket family)\">Saturn family of rockets</a>.", "links": [{"title": "Space Race", "link": "https://wikipedia.org/wiki/Space_Race"}, {"title": "Apollo-Soyuz Test Project", "link": "https://wikipedia.org/wiki/Apollo%E2%80%93Soyuz_Test_Project"}, {"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "Soyuz (spacecraft)", "link": "https://wikipedia.org/wiki/Soyuz_(spacecraft)"}, {"title": "Saturn (rocket family)", "link": "https://wikipedia.org/wiki/Saturn_(rocket_family)"}]}, {"year": "1979", "text": "U.S. President <PERSON> gives his \"malaise speech\".", "html": "1979 - U.S. <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives his \"<a href=\"https://wikipedia.org/wiki/Malaise_speech\" class=\"mw-redirect\" title=\"Malaise speech\">malaise speech</a>\".", "no_year_html": "U.S. <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> gives his \"<a href=\"https://wikipedia.org/wiki/Malaise_speech\" class=\"mw-redirect\" title=\"Malaise speech\">malaise speech</a>\".", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Malaise speech", "link": "https://wikipedia.org/wiki/Malaise_speech"}]}, {"year": "1983", "text": "An attack at Orly Airport in Paris is launched by Armenian militant organisation ASALA, leaving eight people dead and 55 injured.", "html": "1983 - An <a href=\"https://wikipedia.org/wiki/1983_Orly_Airport_attack\" title=\"1983 Orly Airport attack\">attack</a> at <a href=\"https://wikipedia.org/wiki/Orly_Airport\" title=\"Orly Airport\">Orly Airport</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> is launched by <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> militant organisation <a href=\"https://wikipedia.org/wiki/Armenian_Secret_Army_for_the_Liberation_of_Armenia\" title=\"Armenian Secret Army for the Liberation of Armenia\">ASALA</a>, leaving eight people dead and 55 injured.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/1983_Orly_Airport_attack\" title=\"1983 Orly Airport attack\">attack</a> at <a href=\"https://wikipedia.org/wiki/Orly_Airport\" title=\"Orly Airport\">Orly Airport</a> in <a href=\"https://wikipedia.org/wiki/Paris\" title=\"Paris\">Paris</a> is launched by <a href=\"https://wikipedia.org/wiki/Armenia\" title=\"Armenia\">Armenian</a> militant organisation <a href=\"https://wikipedia.org/wiki/Armenian_Secret_Army_for_the_Liberation_of_Armenia\" title=\"Armenian Secret Army for the Liberation of Armenia\">ASALA</a>, leaving eight people dead and 55 injured.", "links": [{"title": "1983 Orly Airport attack", "link": "https://wikipedia.org/wiki/1983_Orly_Airport_attack"}, {"title": "Orly Airport", "link": "https://wikipedia.org/wiki/Orly_Airport"}, {"title": "Paris", "link": "https://wikipedia.org/wiki/Paris"}, {"title": "Armenia", "link": "https://wikipedia.org/wiki/Armenia"}, {"title": "Armenian Secret Army for the Liberation of Armenia", "link": "https://wikipedia.org/wiki/Armenian_Secret_Army_for_the_Liberation_of_Armenia"}]}, {"year": "1983", "text": "Nintendo released the Famicom in Japan.", "html": "1983 - Nintendo released the <a href=\"https://wikipedia.org/wiki/Nintendo_Entertainment_System\" title=\"Nintendo Entertainment System\">Famicom</a> in Japan.", "no_year_html": "Nintendo released the <a href=\"https://wikipedia.org/wiki/Nintendo_Entertainment_System\" title=\"Nintendo Entertainment System\">Famicom</a> in Japan.", "links": [{"title": "Nintendo Entertainment System", "link": "https://wikipedia.org/wiki/Nintendo_Entertainment_System"}]}, {"year": "1996", "text": "A Belgian Air Force C-130 Hercules carrying the Royal Netherlands Army marching band crashes on landing at Eindhoven Airport.", "html": "1996 - A <a href=\"https://wikipedia.org/wiki/Belgian_Air_Component\" title=\"Belgian Air Component\">Belgian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130 Hercules</a> carrying the <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_Army\" title=\"Royal Netherlands Army\">Royal Netherlands Army</a> marching band <a href=\"https://wikipedia.org/wiki/1996_Belgian_Air_Force_Hercules_accident\" title=\"1996 Belgian Air Force Hercules accident\">crashes</a> on landing at <a href=\"https://wikipedia.org/wiki/Eindhoven_Airport\" title=\"Eindhoven Airport\">Eindhoven Airport</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Belgian_Air_Component\" title=\"Belgian Air Component\">Belgian Air Force</a> <a href=\"https://wikipedia.org/wiki/Lockheed_C-130_Hercules\" title=\"Lockheed C-130 Hercules\">C-130 Hercules</a> carrying the <a href=\"https://wikipedia.org/wiki/Royal_Netherlands_Army\" title=\"Royal Netherlands Army\">Royal Netherlands Army</a> marching band <a href=\"https://wikipedia.org/wiki/1996_Belgian_Air_Force_Hercules_accident\" title=\"1996 Belgian Air Force Hercules accident\">crashes</a> on landing at <a href=\"https://wikipedia.org/wiki/Eindhoven_Airport\" title=\"Eindhoven Airport\">Eindhoven Airport</a>.", "links": [{"title": "Belgian Air Component", "link": "https://wikipedia.org/wiki/Belgian_Air_Component"}, {"title": "Lockheed C-130 Hercules", "link": "https://wikipedia.org/wiki/Lockheed_C-130_Hercules"}, {"title": "Royal Netherlands Army", "link": "https://wikipedia.org/wiki/Royal_Netherlands_Army"}, {"title": "1996 Belgian Air Force Hercules accident", "link": "https://wikipedia.org/wiki/1996_Belgian_Air_Force_Hercules_accident"}, {"title": "Eindhoven Airport", "link": "https://wikipedia.org/wiki/Eindhoven_Airport"}]}, {"year": "1998", "text": "Sri Lankan Civil War: Sri Lankan Tamil MP <PERSON><PERSON> is killed by a claymore mine.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sri Lankan Tamil MP <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a> is killed by a <a href=\"https://wikipedia.org/wiki/Claymore_mine\" title=\"Claymore mine\">claymore mine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sri_Lankan_civil_war\" title=\"Sri Lankan civil war\">Sri Lankan Civil War</a>: Sri Lankan Tamil MP <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a> is killed by a <a href=\"https://wikipedia.org/wiki/Claymore_mine\" title=\"Claymore mine\">claymore mine</a>.", "links": [{"title": "Sri Lankan civil war", "link": "https://wikipedia.org/wiki/Sri_Lankan_civil_war"}, {"title": "<PERSON><PERSON> (Sri Lankan politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(Sri_Lankan_politician)"}, {"title": "Claymore mine", "link": "https://wikipedia.org/wiki/Claymore_mine"}]}, {"year": "2002", "text": "\"American Taliban\" <PERSON> pleads guilty to supplying aid to the enemy and possession of explosives during the commission of a felony.", "html": "2002 - \"American <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to supplying aid to the enemy and possession of explosives during the commission of a <a href=\"https://wikipedia.org/wiki/Felony\" title=\"Felony\">felony</a>.", "no_year_html": "\"American <a href=\"https://wikipedia.org/wiki/Taliban\" title=\"Taliban\">Taliban</a>\" <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> pleads guilty to supplying aid to the enemy and possession of explosives during the commission of a <a href=\"https://wikipedia.org/wiki/Felony\" title=\"Felony\">felony</a>.", "links": [{"title": "Taliban", "link": "https://wikipedia.org/wiki/Taliban"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Felony"}]}, {"year": "2002", "text": "The Anti-Terrorism Court of Pakistan sentences British born <PERSON> to death, and three others suspected of murdering The Wall Street Journal reporter <PERSON> to life.", "html": "2002 - The <a href=\"https://wikipedia.org/wiki/Anti_Terrorism_Court_of_Pakistan\" title=\"Anti Terrorism Court of Pakistan\">Anti-Terrorism Court</a> of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> sentences British born <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Ahmed <PERSON></a> to death, and three others suspected of murdering <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i> reporter <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to life.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Anti_Terrorism_Court_of_Pakistan\" title=\"Anti Terrorism Court of Pakistan\">Anti-Terrorism Court</a> of <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> sentences British born <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to death, and three others suspected of murdering <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i> reporter <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> to life.", "links": [{"title": "Anti Terrorism Court of Pakistan", "link": "https://wikipedia.org/wiki/Anti_Terrorism_Court_of_Pakistan"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "The Wall Street Journal", "link": "https://wikipedia.org/wiki/The_Wall_Street_Journal"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "AOL Time Warner disbands Netscape. The Mozilla Foundation is established on the same day.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Time_Warner\" class=\"mw-redirect\" title=\"Time Warner\">AOL Time Warner</a> disbands <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a>. The <a href=\"https://wikipedia.org/wiki/Mozilla_Foundation\" title=\"Mozilla Foundation\">Mozilla Foundation</a> is established on the same day.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Time_Warner\" class=\"mw-redirect\" title=\"Time Warner\">AOL Time Warner</a> disbands <a href=\"https://wikipedia.org/wiki/Netscape\" title=\"Netscape\">Netscape</a>. The <a href=\"https://wikipedia.org/wiki/Mozilla_Foundation\" title=\"Mozilla Foundation\">Mozilla Foundation</a> is established on the same day.", "links": [{"title": "Time Warner", "link": "https://wikipedia.org/wiki/Time_Warner"}, {"title": "Netscape", "link": "https://wikipedia.org/wiki/Netscape"}, {"title": "Mozilla Foundation", "link": "https://wikipedia.org/wiki/Mozilla_Foundation"}]}, {"year": "2006", "text": "Twitter, later one of the largest social media platforms in the world, is launched.", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a>, later one of the largest <a href=\"https://wikipedia.org/wiki/Social_media\" title=\"Social media\">social media</a> platforms in the world, is launched.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Twitter\" title=\"Twitter\">Twitter</a>, later one of the largest <a href=\"https://wikipedia.org/wiki/Social_media\" title=\"Social media\">social media</a> platforms in the world, is launched.", "links": [{"title": "Twitter", "link": "https://wikipedia.org/wiki/Twitter"}, {"title": "Social media", "link": "https://wikipedia.org/wiki/Social_media"}]}, {"year": "2009", "text": "Caspian Airlines Flight 7908 crashes near Jannatabad, Qazvin, Iran, killing 168.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Caspian_Airlines_Flight_7908\" title=\"Caspian Airlines Flight 7908\">Caspian Airlines Flight 7908</a> crashes near <a href=\"https://wikipedia.org/wiki/Jannatabad,_Qazvin\" title=\"Jannatabad, Qazvin\">Jannatabad, Qazvin</a>, Iran, killing 168.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Caspian_Airlines_Flight_7908\" title=\"Caspian Airlines Flight 7908\">Caspian Airlines Flight 7908</a> crashes near <a href=\"https://wikipedia.org/wiki/Jannatabad,_Qazvin\" title=\"Jannatabad, Qazvin\">Jannatabad, Qazvin</a>, Iran, killing 168.", "links": [{"title": "Caspian Airlines Flight 7908", "link": "https://wikipedia.org/wiki/Caspian_Airlines_Flight_7908"}, {"title": "Jannatabad, Qazvin", "link": "https://wikipedia.org/wiki/Jannatabad,_Qazvin"}]}, {"year": "2009", "text": "Space Shuttle program: Endeavour is launched on STS-127 to complete assembly of the International Space Station's Kibō module.", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-127\" title=\"STS-127\">STS-127</a> to complete assembly of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>'s <a href=\"https://wikipedia.org/wiki/Kib%C5%8D_(ISS_module)\" title=\"Kibō (ISS module)\">Kibō module</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a>: <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Endeavour\" title=\"Space Shuttle Endeavour\"><i>Endeavour</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-127\" title=\"STS-127\">STS-127</a> to complete assembly of the <a href=\"https://wikipedia.org/wiki/International_Space_Station\" title=\"International Space Station\">International Space Station</a>'s <a href=\"https://wikipedia.org/wiki/Kib%C5%8D_(ISS_module)\" title=\"Kib<PERSON> (ISS module)\">Kibō module</a>.", "links": [{"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Endeavour", "link": "https://wikipedia.org/wiki/Space_Shuttle_Endeavour"}, {"title": "STS-127", "link": "https://wikipedia.org/wiki/STS-127"}, {"title": "International Space Station", "link": "https://wikipedia.org/wiki/International_Space_Station"}, {"title": "<PERSON><PERSON><PERSON> (ISS module)", "link": "https://wikipedia.org/wiki/Kib%C5%8D_(ISS_module)"}]}, {"year": "2012", "text": "South Korean rapper <PERSON><PERSON> releases his hit single Gangnam Style.", "html": "2012 - South Korean rapper <a href=\"https://wikipedia.org/wiki/Psy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> releases his hit single <i><a href=\"https://wikipedia.org/wiki/Gangnam_Style\" title=\"Gangnam Style\">Gangnam Style</a></i>.", "no_year_html": "South Korean rapper <a href=\"https://wikipedia.org/wiki/Psy\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> releases his hit single <i><a href=\"https://wikipedia.org/wiki/Gangnam_Style\" title=\"Gangnam Style\">Gangnam Style</a></i>.", "links": [{"title": "Psy", "link": "https://wikipedia.org/wiki/Psy"}, {"title": "Gangnam Style", "link": "https://wikipedia.org/wiki/Gangnam_Style"}]}, {"year": "2014", "text": "A train derails on the Moscow Metro, killing at least 24 and injuring more than 160 others.", "html": "2014 - A <a href=\"https://wikipedia.org/wiki/2014_Moscow_Metro_derailment\" title=\"2014 Moscow Metro derailment\">train derails</a> on the <a href=\"https://wikipedia.org/wiki/Moscow_Metro\" title=\"Moscow Metro\">Moscow Metro</a>, killing at least 24 and injuring more than 160 others.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2014_Moscow_Metro_derailment\" title=\"2014 Moscow Metro derailment\">train derails</a> on the <a href=\"https://wikipedia.org/wiki/Moscow_Metro\" title=\"Moscow Metro\">Moscow Metro</a>, killing at least 24 and injuring more than 160 others.", "links": [{"title": "2014 Moscow Metro derailment", "link": "https://wikipedia.org/wiki/2014_Moscow_Metro_derailment"}, {"title": "Moscow Metro", "link": "https://wikipedia.org/wiki/Moscow_Metro"}]}, {"year": "2016", "text": "Factions of the Turkish Armed Forces attempt a coup.", "html": "2016 - Factions of the <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> attempt <a href=\"https://wikipedia.org/wiki/2016_Turkish_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2016 Turkish coup d'état attempt\">a coup</a>.", "no_year_html": "Factions of the <a href=\"https://wikipedia.org/wiki/Turkish_Armed_Forces\" title=\"Turkish Armed Forces\">Turkish Armed Forces</a> attempt <a href=\"https://wikipedia.org/wiki/2016_Turkish_coup_d%27%C3%A9tat_attempt\" class=\"mw-redirect\" title=\"2016 Turkish coup d'état attempt\">a coup</a>.", "links": [{"title": "Turkish Armed Forces", "link": "https://wikipedia.org/wiki/Turkish_Armed_Forces"}, {"title": "2016 Turkish coup d'état attempt", "link": "https://wikipedia.org/wiki/2016_Turkish_coup_d%27%C3%A9tat_attempt"}]}, {"year": "2018", "text": "France win their second World Cup title, defeating Croatia 4-2.", "html": "2018 - <a href=\"https://wikipedia.org/wiki/France_national_football_team\" title=\"France national football team\">France</a> win their second <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup\" title=\"FIFA World Cup\">World Cup</a> title, defeating <a href=\"https://wikipedia.org/wiki/Croatia_national_football_team\" title=\"Croatia national football team\">Croatia</a> <a href=\"https://wikipedia.org/wiki/2018_FIFA_World_Cup_final\" title=\"2018 FIFA World Cup final\">4-2</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/France_national_football_team\" title=\"France national football team\">France</a> win their second <a href=\"https://wikipedia.org/wiki/FIFA_World_Cup\" title=\"FIFA World Cup\">World Cup</a> title, defeating <a href=\"https://wikipedia.org/wiki/Croatia_national_football_team\" title=\"Croatia national football team\">Croatia</a> <a href=\"https://wikipedia.org/wiki/2018_FIFA_World_Cup_final\" title=\"2018 FIFA World Cup final\">4-2</a>.", "links": [{"title": "France national football team", "link": "https://wikipedia.org/wiki/France_national_football_team"}, {"title": "FIFA World Cup", "link": "https://wikipedia.org/wiki/FIFA_World_Cup"}, {"title": "Croatia national football team", "link": "https://wikipedia.org/wiki/Croatia_national_football_team"}, {"title": "2018 FIFA World Cup final", "link": "https://wikipedia.org/wiki/2018_FIFA_World_Cup_final"}]}], "Births": [{"year": "980", "text": "<PERSON><PERSON><PERSON>, Japanese emperor (d. 1011)", "html": "980 - <a href=\"https://wikipedia.org/wiki/Emperor_Ichij%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_Ichij%C5%8D\" title=\"Emperor <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1011)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>%C5%8D"}]}, {"year": "1273", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ethiopian monk and saint (d. 1352)", "html": "1273 - <a href=\"https://wikipedia.org/wiki/Ewostatewos\" title=\"Ewostatewos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian monk and saint (d. 1352)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ewostatewos\" title=\"Ewostatewos\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Ethiopian monk and saint (d. 1352)", "links": [{"title": "Ewostatewos", "link": "https://wikipedia.org/wiki/Ewostatewos"}]}, {"year": "1353", "text": "<PERSON> the Bold, Russian prince (d. 1410)", "html": "1353 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, Russian prince (d. 1410)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Bold\" title=\"<PERSON> the Bold\"><PERSON> the Bold</a>, Russian prince (d. 1410)", "links": [{"title": "<PERSON> the Bold", "link": "https://wikipedia.org/wiki/<PERSON>_the_Bold"}]}, {"year": "1359", "text": "<PERSON>, Italian cardinal (d. 1445)", "html": "1359 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Italian cardinal (d. 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)\" title=\"<PERSON> (cardinal)\"><PERSON></a>, Italian cardinal (d. 1445)", "links": [{"title": "<PERSON> (cardinal)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cardinal)"}]}, {"year": "1442", "text": "<PERSON><PERSON><PERSON> of Poděbrady, Bohemian nobleman (d. 1496)", "html": "1442 - <a href=\"https://wikipedia.org/wiki/Bo%C4%8Dek_IV_of_Pod%C4%9Bbrady\" title=\"Boček IV of Poděbrady\"><PERSON><PERSON><PERSON> IV of Poděbrady</a>, Bohemian nobleman (d. 1496)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bo%C4%8Dek_IV_of_Pod%C4%9Bbrady\" title=\"Boček IV of Poděbrady\"><PERSON><PERSON><PERSON> IV of Poděbrady</a>, Bohemian nobleman (d. 1496)", "links": [{"title": "Boček IV of Poděbrady", "link": "https://wikipedia.org/wiki/Bo%C4%8Dek_IV_of_Pod%C4%9Bbrady"}]}, {"year": "1455", "text": "<PERSON> <PERSON>, Korean queen (d. 1482)", "html": "1455 - <a href=\"https://wikipedia.org/wiki/Deposed_Queen_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Deposed Queen <PERSON>\">Queen <PERSON></a>, Korean queen (d. 1482)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Deposed_Queen_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Deposed Queen <PERSON>\">Queen <PERSON></a>, Korean queen (d. 1482)", "links": [{"title": "Deposed Queen <PERSON>", "link": "https://wikipedia.org/wiki/Deposed_Queen_Lady_<PERSON>"}]}, {"year": "1471", "text": "<PERSON><PERSON><PERSON>, Ethiopian emperor (d. 1494)", "html": "1471 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian emperor (d. 1494)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ethiopian emperor (d. 1494)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON>nder"}]}, {"year": "1478", "text": "<PERSON>, duchess consort of Saxony and <PERSON><PERSON><PERSON><PERSON> consort of Meissen (d. 1534)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, duchess consort of Saxony and <PERSON><PERSON><PERSON><PERSON> consort of Meissen (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, duchess consort of Saxony and <PERSON><PERSON><PERSON><PERSON> consort of Meissen (d. 1534)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1573", "text": "<PERSON><PERSON>, English architect, designed the Queen's House (d. 1652)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jones\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Queen%27s_House\" title=\"Queen's House\">Queen's House</a> (d. 1652)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Jones\" title=\"<PERSON><PERSON> Jones\"><PERSON><PERSON></a>, English architect, designed the <a href=\"https://wikipedia.org/wiki/Queen%27s_House\" title=\"Queen's House\">Queen's House</a> (d. 1652)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Queen's House", "link": "https://wikipedia.org/wiki/Queen%27s_House"}]}, {"year": "1600", "text": "<PERSON>, Flemish painter (d. 1671)", "html": "1600 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1671)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Flemish painter (d. 1671)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1606", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch painter and etcher (d. 1669)", "html": "1606 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch painter and etcher (d. 1669)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch painter and etcher (d. 1669)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>t"}]}, {"year": "1611", "text": "<PERSON>, maharaja of Jaipur (d. 1667)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, maharaja of Jaipur (d. 1667)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> I</a>, maharaja of Jaipur (d. 1667)", "links": [{"title": "<PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1613", "text": "<PERSON><PERSON>, Chinese philologist and geographer (d. 1682)", "html": "1613 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese philologist and geographer (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese philologist and geographer (d. 1682)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1631", "text": "<PERSON><PERSON>, Danish politician and diplomat, Governor-general of Norway (d. 1700)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON><PERSON> (diplomat)\"><PERSON><PERSON></a>, Danish politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor-general_of_Norway\" class=\"mw-redirect\" title=\"Governor-general of Norway\">Governor-general of Norway</a> (d. 1700)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(diplomat)\" title=\"<PERSON><PERSON> (diplomat)\"><PERSON><PERSON></a>, Danish politician and diplomat, <a href=\"https://wikipedia.org/wiki/Governor-general_of_Norway\" class=\"mw-redirect\" title=\"Governor-general of Norway\">Governor-general of Norway</a> (d. 1700)", "links": [{"title": "<PERSON><PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(diplomat)"}, {"title": "Governor-general of Norway", "link": "https://wikipedia.org/wiki/Governor-general_of_Norway"}]}, {"year": "1631", "text": "<PERSON>, English philosopher (d. 1718)", "html": "1631 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English philosopher (d. 1718)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)"}]}, {"year": "1638", "text": "<PERSON>, Italian violinist and composer (d. 1693)", "html": "1638 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Buo<PERSON>ventura_Viviani\" title=\"Giovanni Buonaventura Viviani\"><PERSON></a>, Italian violinist and composer (d. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Buo<PERSON>ventura_Viviani\" title=\"Giovanni Buonaventura Viviani\"><PERSON> Buo<PERSON></a>, Italian violinist and composer (d. 1693)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_<PERSON>uo<PERSON>vent<PERSON>_<PERSON>i"}]}, {"year": "1704", "text": "August <PERSON><PERSON><PERSON><PERSON>, German bishop and theologian (d. 1792)", "html": "1704 - <a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ie<PERSON>_<PERSON>ngenberg\" title=\"August <PERSON><PERSON>ie<PERSON>\">August <PERSON><PERSON><PERSON><PERSON></a>, German bishop and theologian (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON><PERSON>ie<PERSON>_<PERSON>\" title=\"August <PERSON><PERSON>ie<PERSON>\">August <PERSON><PERSON><PERSON><PERSON></a>, German bishop and theologian (d. 1792)", "links": [{"title": "August <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, American author, poet, and educator (d. 1863)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and educator (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, poet, and educator (d. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON>, American educator, author, editor (d. 1884)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator, author, editor (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American educator, author, editor (d. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1796", "text": "<PERSON>, American mythologist (d. 1867)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mythologist (d. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1799", "text": "<PERSON>, American lawyer and politician, 13th Governor of Alabama (d. 1882)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (d. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Alabama\" class=\"mw-redirect\" title=\"Governor of Alabama\">Governor of Alabama</a> (d. 1882)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Alabama", "link": "https://wikipedia.org/wiki/Governor_of_Alabama"}]}, {"year": "1800", "text": "<PERSON>, American jurist and politician (d. 1878)", "html": "1800 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American jurist and politician (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1808", "text": "<PERSON>, English cardinal (d. 1892)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cardinal (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1812", "text": "<PERSON>, English lawyer and academic (d. 1873)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and academic (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and academic (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1817", "text": "Sir <PERSON>, 1st Baronet, English engineer, designed the Forth Bridge (d. 1898)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Forth_Bridge\" title=\"Forth Bridge\">Forth Bridge</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_1st_Baronet\" title=\"Sir <PERSON>, 1st Baronet\">Sir <PERSON>, 1st Baronet</a>, English engineer, designed the <a href=\"https://wikipedia.org/wiki/Forth_Bridge\" title=\"Forth Bridge\">Forth Bridge</a> (d. 1898)", "links": [{"title": "Sir <PERSON>, 1st Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_1st_Baronet"}, {"title": "Forth Bridge", "link": "https://wikipedia.org/wiki/Forth_Bridge"}]}, {"year": "1827", "text": "<PERSON><PERSON> <PERSON><PERSON> American lawyer and politician, 6th Governor of Oregon (d. 1899)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/Governor_of_Oregon\" title=\"Governor of Oregon\">Governor of Oregon</a> (d. 1899)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Governor of Oregon", "link": "https://wikipedia.org/wiki/Governor_of_Oregon"}]}, {"year": "1848", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian economist and sociologist (d. 1923)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Vil<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian economist and sociologist (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian economist and sociologist (d. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1850", "text": "<PERSON>, Italian-American nun and saint (d. 1917)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American nun and saint (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American nun and saint (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1852", "text": "<PERSON>, Polish-born singer and actor (d. 1920)", "html": "1852 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born singer and actor (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-born singer and actor (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON><PERSON>, English political activist and suffragist (d. 1928)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English political activist and suffragist (d. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English political activist and suffragist (d. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English actress and singer (d. 1942)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress and singer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>mpest\"><PERSON></a>, English actress and singer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, 1st Viscount <PERSON>, Anglo-Irish businessman and publisher, founded the Amalgamated Press (d. 1922)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>cliffe\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Anglo-Irish businessman and publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Amalgamated_Press\" title=\"Amalgamated Press\">Amalgamated Press</a></i> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>cliffe\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Anglo-Irish businessman and publisher, founded the <i><a href=\"https://wikipedia.org/wiki/Amalgamated_Press\" title=\"Amalgamated Press\">Amalgamated Press</a></i> (d. 1922)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>"}, {"title": "Amalgamated Press", "link": "https://wikipedia.org/wiki/Amalgamated_Press"}]}, {"year": "1865", "text": "<PERSON>, Austrian-German mathematician and theorist (d. 1945)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German mathematician and theorist (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German mathematician and theorist (d. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, French physician and explorer (d. 1936)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON>, Japanese journalist, author, and poet (d. 1908)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kunik<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese journalist, author, and poet (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Kunik<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese journalist, author, and poet (d. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Ku<PERSON>ida"}]}, {"year": "1880", "text": "<PERSON>, Argentinian lawyer and politician (d. 1950)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian lawyer and politician (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON> died during the 1923 Irish Hunger Strikes (d. 1923)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> died during the <a href=\"https://wikipedia.org/wiki/1923_Irish_Hunger_Strikes\" class=\"mw-redirect\" title=\"1923 Irish Hunger Strikes\">1923 Irish Hunger Strikes</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Irish_republicanism\" title=\"Irish republicanism\">Irish Republican</a> died during the <a href=\"https://wikipedia.org/wiki/1923_Irish_Hunger_Strikes\" class=\"mw-redirect\" title=\"1923 Irish Hunger Strikes\">1923 Irish Hunger Strikes</a> (d. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Irish republicanism", "link": "https://wikipedia.org/wiki/Irish_republicanism"}, {"title": "1923 Irish Hunger Strikes", "link": "https://wikipedia.org/wiki/1923_Irish_Hunger_Strikes"}]}, {"year": "1887", "text": "<PERSON>, American sculptor (d. 1970)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Wharton_E<PERSON>ick"}]}, {"year": "1892", "text": "<PERSON>, German philosopher and critic (d. 1940)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and critic (d. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher and critic (d. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, Australian-American actress (d. 1969)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American actress (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian-American actress (d. 1969)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, American football player and coach (d. 1970)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Polish-American engineer (d. 1989)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/Tadeusz_Sendzimir\" title=\"Tadeusz Sendzimir\"><PERSON><PERSON><PERSON></a>, Polish-American engineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tadeusz_Sendzimir\" title=\"Tadeusz Send<PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-American engineer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tadeusz_Sendzimir"}]}, {"year": "1899", "text": "<PERSON><PERSON>, Irish soldier and politician, 4th Taoiseach of Ireland (d. 1971)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/Se%C3%A1n_Lemass\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%A1n_Lemass\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Se%C3%A1n_Lemass"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1902", "text": "<PERSON>, Belgian lawyer and politician, 2nd President of the European Commission (d. 1983)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, Belgian lawyer and politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_the_European_Commission\" title=\"President of the European Commission\">President of the European Commission</a> (d. 1983)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}, {"title": "President of the European Commission", "link": "https://wikipedia.org/wiki/President_of_the_European_Commission"}]}, {"year": "1903", "text": "<PERSON>, American journalist and author (d. 1998)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Indian journalist and politician (d. 1975)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and politician (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian journalist and politician (d. 1975)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, German-American psychologist and author (d. 2007)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American psychologist and author (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American songwriter (d. 1974)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Italian actress (d. 2008)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian poet and academic (d. 1993)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/R._<PERSON>._Mugali\" title=\"R. S. Mugali\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and academic (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R._S._Mugali\" title=\"R. S. Mugali\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet and academic (d. 1993)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>_<PERSON><PERSON>_Mugali"}]}, {"year": "1906", "text": "<PERSON>, English-German engineer (d. 1989)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German engineer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-German engineer (d. 1989)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, French physician and surgeon (d. 1992)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French physician and surgeon (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, <PERSON>, English geographer and politician, Secretary of State for Air (d. 1994)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English geographer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Air\" title=\"Secretary of State for Air\">Secretary of State for Air</a> (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English geographer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Air\" title=\"Secretary of State for Air\">Secretary of State for Air</a> (d. 1994)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Secretary of State for Air", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Air"}]}, {"year": "1913", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1963)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a>, American singer-songwriter and guitarist (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cowboy_Copas\" title=\"Cowboy Copas\">Cowboy Copas</a>, American singer-songwriter and guitarist (d. 1963)", "links": [{"title": "Cowboy Copas", "link": "https://wikipedia.org/wiki/Cowboy_Copas"}]}, {"year": "1913", "text": "<PERSON>, English journalist and author (d. 1998)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hammond Innes\"><PERSON></a>, English journalist and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hammond_<PERSON>es\" title=\"Hammond Innes\"><PERSON></a>, English journalist and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Hammond_Innes"}]}, {"year": "1913", "text": "<PERSON>, Russian poet and author (d. 2010)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian poet and author (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai racing driver and sailor, member of the Thai royal family (d. 1985)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Birabongs<PERSON>_<PERSON>han<PERSON>j\" title=\"Birabongse Bhanudej\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai racing driver and sailor, member of the <a href=\"https://wikipedia.org/wiki/Chakri_dynasty\" title=\"Chakri dynasty\">Thai royal family</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Birabongs<PERSON>_<PERSON>\" title=\"Birabongse Bhanudej\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai racing driver and sailor, member of the <a href=\"https://wikipedia.org/wiki/Chakri_dynasty\" title=\"Chakri dynasty\">Thai royal family</a> (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Birabongs<PERSON>_<PERSON><PERSON>j"}, {"title": "Chakri dynasty", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_dynasty"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Pakistani economist, scholar, and activist (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani economist, scholar, and activist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani economist, scholar, and activist (d. 1999)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, Swiss-French actor (d. 1996)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French actor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French actor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American chemist and academic (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, Indian army officer (d. 2007)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Kashmir_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian army officer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kashmir_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian army officer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kashmir_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American politician and diplomat (d. 2005)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English-American historian, poet, and academic (d. 2015)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American historian, poet, and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Robert Conquest\"><PERSON></a>, English-American historian, poet, and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American actress and singer (d. 2012)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON>, Afghan journalist and politician (d. 1979)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan journalist and politician (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Afghan journalist and politician (d. 1979)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON>, Canadian physicist and academic, Nobel Prize laureate (d. 2003)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brockhouse\"><PERSON><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brockhouse\"><PERSON><PERSON></a>, Canadian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Brockhouse"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1918", "text": "<PERSON>, English-Canadian neuropsychologist and academic", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian neuropsychologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Canadian neuropsychologist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, German lieutenant (d. 2012)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German lieutenant (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Anglo-Irish British novelist and philosopher (d. 1999)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish British novelist and philosopher (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Anglo-Irish British novelist and philosopher (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Murdoch"}]}, {"year": "1921", "text": "<PERSON>, Swiss-French director and screenwriter (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French director and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-French director and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American biochemist and academic, Nobel Prize laureate (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1922", "text": "<PERSON>, American physicist and mathematician, Nobel Prize laureate (d. 2018)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, French writer (d. 2019)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French writer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French writer (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American admiral and politician (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral and politician (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Swedish actress and philanthropist", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and philanthropist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish actress and philanthropist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marianne_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American architect, designed Haydon Burns Library and Friendship Fountain Park (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Hardwick\"><PERSON></a>, American architect, designed <a href=\"https://wikipedia.org/wiki/Haydon_Burns_Library\" class=\"mw-redirect\" title=\"Haydon Burns Library\">Haydon Burns Library</a> and <a href=\"https://wikipedia.org/wiki/Friendship_Fountain_Park\" class=\"mw-redirect\" title=\"Friendship Fountain Park\">Friendship Fountain Park</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>wick\"><PERSON></a>, American architect, designed <a href=\"https://wikipedia.org/wiki/Haydon_Burns_Library\" class=\"mw-redirect\" title=\"Haydon Burns Library\">Haydon Burns Library</a> and <a href=\"https://wikipedia.org/wiki/Friendship_Fountain_Park\" class=\"mw-redirect\" title=\"Friendship Fountain Park\">Friendship Fountain Park</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Haydon Burns Library", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Library"}, {"title": "Friendship Fountain Park", "link": "https://wikipedia.org/wiki/Friendship_Fountain_Park"}]}, {"year": "1925", "text": "<PERSON><PERSON> <PERSON><PERSON>, American documentary filmmaker (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_<PERSON>ebaker\" title=\"<PERSON><PERSON> <PERSON><PERSON>aker\"><PERSON><PERSON> <PERSON><PERSON></a>, American documentary filmmaker (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_<PERSON>aker\" title=\"<PERSON><PERSON> <PERSON><PERSON>aker\"><PERSON><PERSON> <PERSON><PERSON></a>, American documentary filmmaker (d. 2019)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Pennebaker"}]}, {"year": "1925", "text": "<PERSON>, American politician (d. 2025)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON><PERSON>, American football player (d. 2018)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Moroccan-French journalist and author (d. 2007)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Driss_Chra%C3%AFbi\" title=\"Dr<PERSON>ï<PERSON>\"><PERSON><PERSON></a>, Moroccan-French journalist and author (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Driss_Chra%C3%AFbi\" title=\"Driss Chraïbi\"><PERSON><PERSON></a>, Moroccan-French journalist and author (d. 2007)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Driss_Chra%C3%AFbi"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Argentinian general and politician, 44th President of Argentina (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Argentina\" title=\"List of heads of state of Argentina\">President of Argentina</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian general and politician, 44th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Argentina\" title=\"List of heads of state of Argentina\">President of Argentina</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}, {"title": "List of heads of state of Argentina", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Argentina"}]}, {"year": "1926", "text": "<PERSON>, English physicist and academic (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physicist and academic (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "Sir <PERSON>, 4th Baronet, English diplomat (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English diplomat (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet\" title=\"Sir <PERSON>, 4th Baronet\">Sir <PERSON>, 4th Baronet</a>, English diplomat (d. 2019)", "links": [{"title": "Sir <PERSON>, 4th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_4th_Baronet"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2010)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2014)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Carmen_Z<PERSON>ta"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Norwegian cross-country skier (d. 2021)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/H%C3%A5kon_Brusveen\" title=\"Håkon Brusveen\"><PERSON><PERSON><PERSON> Brusveen</a>, Norwegian cross-country skier (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A5kon_Brusveen\" title=\"Håkon Brusveen\"><PERSON><PERSON><PERSON> Brusveen</a>, Norwegian cross-country skier (d. 2021)", "links": [{"title": "Håkon Brusveen", "link": "https://wikipedia.org/wiki/H%C3%A5kon_Brusveen"}]}, {"year": "1928", "text": "<PERSON>, American microbiologist and biophysicist (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and biophysicist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American microbiologist and biophysicist (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian parliamentarian (d. 1967)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian parliamentarian (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> V<PERSON>la <PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian parliamentarian (d. 1967)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American tenor and actor (d. 2012)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 2012)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(tenor)"}]}, {"year": "1929", "text": "<PERSON>, Cameroonian-French guitarist (d. 2001)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian-French guitarist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian-French guitarist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Scottish racing driver (d. 2017)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (d. 2017)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1930", "text": "<PERSON>, Algerian-French philosopher and academic (d. 2004)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French philosopher and academic (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Canadian journalist and sportscaster (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and sportscaster (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist and sportscaster (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American mathematician and computer scientist", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American mathematician and computer scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON><PERSON>, Japanese Buddhist leader", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese Buddhist leader", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American archaeologist and author (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American archaeologist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actress and casting director (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and casting director (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian lawyer and politician, Deputy Premier of Quebec (d. 2023)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Premier_of_Quebec\" title=\"Deputy Premier of Quebec\">Deputy Premier of Quebec</a> (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Premier of Quebec", "link": "https://wikipedia.org/wiki/Deputy_Premier_of_Quebec"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player (d. 2010)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Italian author and illustrator (d. 2003)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian author and illustrator (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guido_<PERSON>x"}]}, {"year": "1933", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author and screenwriter (d. 2024)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and screenwriter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and screenwriter (d. 2024)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English composer and academic (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer and academic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Czech actress (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Eva_Kr%C3%AD%C5%BEikov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eva_Kr%C3%AD%C5%BEikov%C3%A1\" title=\"<PERSON>\"><PERSON></a>, Czech actress (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eva_Kr%C3%AD%C5%BEikov%C3%A1"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Finnish director and producer (d. 1977)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and producer (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish director and producer (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rist<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, American baseball player and lawyer (d. 2005)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Don<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and lawyer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and lawyer (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Donn_<PERSON>on"}]}, {"year": "1935", "text": "<PERSON>, American football player, wrestler, and actor (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, wrestler, and actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor and director (d. 2019)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American lawyer and politician, 65th Governor of Ohio (d. 2016)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 65th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 65th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian journalist (d. 2009)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Josh<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian journalist (d. 2009)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1938", "text": "<PERSON>, American racing driver (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American football player, actor, and painter (d. 2009)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and painter (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, actor, and painter (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Australian publisher, founded Virago Press (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian publisher, founded <a href=\"https://wikipedia.org/wiki/Virago_Press\" title=\"Virago Press\">Virago Press</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Call<PERSON>\"><PERSON></a>, Australian publisher, founded <a href=\"https://wikipedia.org/wiki/Virago_Press\" title=\"Virago Press\">Virago Press</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Virago Press", "link": "https://wikipedia.org/wiki/Virago_Press"}]}, {"year": "1938", "text": "<PERSON>, Jr., American lawyer and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American lawyer and politician", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, Portuguese economist and politician, 19th President of the Portuguese Republic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/An%C3%ADbal_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese economist and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_the_Portuguese_Republic\" class=\"mw-redirect\" title=\"President of the Portuguese Republic\">President of the Portuguese Republic</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/An%C3%ADbal_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese economist and politician, 19th <a href=\"https://wikipedia.org/wiki/President_of_the_Portuguese_Republic\" class=\"mw-redirect\" title=\"President of the Portuguese Republic\">President of the Portuguese Republic</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/An%C3%ADbal_Cava<PERSON>_Silva"}, {"title": "President of the Portuguese Republic", "link": "https://wikipedia.org/wiki/President_of_the_Portuguese_Republic"}]}, {"year": "1940", "text": "<PERSON>, American racing driver (d. 2022)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Canadian director and producer (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9roux\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9roux\" title=\"<PERSON>\"><PERSON></a>, Canadian director and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Denis_H%C3%A9roux"}]}, {"year": "1940", "text": "<PERSON>, American sergeant and convicted murderer (d. 1990)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and convicted murderer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sergeant and convicted murderer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English surgeon, academic, and politician", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English surgeon, academic, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English surgeon, academic, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Venezuelan communist (d.1961)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Livia_Gouverneur\" title=\"<PERSON>ia Gouverneur\"><PERSON><PERSON></a>, Venezuelan communist (d.1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Livia_Gouverneur\" title=\"<PERSON><PERSON> Gouverneur\"><PERSON><PERSON></a>, Venezuelan communist (d.1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Livia_Gouverneur"}]}, {"year": "1942", "text": "<PERSON>, American civil rights activist (d. 2005)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American civil rights activist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Northern Irish astrophysicist, astronomer, and academic", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish astrophysicist, astronomer, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish astrophysicist, astronomer, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American singer-songwriter", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "Jan<PERSON><PERSON>, American actor (d. 2019)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON>-<PERSON></a>, American actor (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jan-<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English conservator (d. 1992)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conservator)\" title=\"<PERSON> (conservator)\"><PERSON></a>, English conservator (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conservator)\" title=\"<PERSON> (conservator)\"><PERSON></a>, English conservator (d. 1992)", "links": [{"title": "<PERSON> (conservator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(conservator)"}]}, {"year": "1945", "text": "<PERSON>, Guyanese politician, 9th President of Guyana", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Guyanese politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Guyanese politician, 9th <a href=\"https://wikipedia.org/wiki/President_of_Guyana\" title=\"President of Guyana\">President of Guyana</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Guyana", "link": "https://wikipedia.org/wiki/President_of_Guyana"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON>, German soldier and politician, Vice-Chancellor of Germany (d. 2003)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_M%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%<PERSON>rgen_M%C3%B<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German soldier and politician, <a href=\"https://wikipedia.org/wiki/Vice-Chancellor_of_Germany\" title=\"Vice-Chancellor of Germany\">Vice-Chancellor of Germany</a> (d. 2003)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCrgen_M%C3%B6<PERSON><PERSON>"}, {"title": "Vice-Chancellor of Germany", "link": "https://wikipedia.org/wiki/Vice-Chancellor_of_Germany"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter, producer, and actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Sultan of Brunei", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bolkiah\"><PERSON><PERSON></a>, Sultan of Brunei", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Bolkiah\"><PERSON><PERSON></a>, Sultan of Brunei", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American short story writer, novelist, and essayist", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer, novelist, and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Thai economist and politician, Thai Minister of Finance", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_Dev<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Devakula\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Thailand)\" title=\"Ministry of Finance (Thailand)\">Thai Minister of Finance</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> Devakula\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Thai economist and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Finance_(Thailand)\" title=\"Ministry of Finance (Thailand)\">Thai Minister of Finance</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/P<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Ministry of Finance (Thailand)", "link": "https://wikipedia.org/wiki/Ministry_of_Finance_(Thailand)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and musician (d. 2019)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and musician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and musician (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English singer-songwriter (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, English singer-songwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek anthropologist and critic", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek anthropologist and critic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek anthropologist and critic", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American rock drummer and songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Artimus_Pyle\" title=\"Artimus Pyle\"><PERSON><PERSON></a>, American rock drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artimus_Pyle\" title=\"Artimus Pyle\"><PERSON><PERSON></a>, American rock drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artimus_Pyle"}]}, {"year": "1949", "text": "<PERSON>, Swedish politician and diplomat, Prime Minister of Sweden", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician and diplomat, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>il<PERSON>"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter, keyboard player, and producer", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American novelist, short story writer, and screenwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian economist and politician, 29th Premier of Western Australia", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian economist and politician, 29th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Greek-American journalist and publisher", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American journalist and publisher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American journalist and publisher", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Jamaican-English singer-songwriter (d. 2010)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English singer-songwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican-English singer-songwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American wrestler, actor, and politician, 38th Governor of Minnesota", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, actor, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler, actor, and politician, 38th <a href=\"https://wikipedia.org/wiki/Governor_of_Minnesota\" title=\"Governor of Minnesota\">Governor of Minnesota</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Minnesota", "link": "https://wikipedia.org/wiki/Governor_of_Minnesota"}]}, {"year": "1952", "text": "<PERSON>, British racing driver", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, British racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1952", "text": "<PERSON>, English actress", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Quinn\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Quinn\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_O%27Quinn"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Pack\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/David_<PERSON>\" title=\"David Pack\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "David <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON>, American drummer and songwriter", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American drummer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist (d. 1991)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Thunders\"><PERSON></a>, American singer-songwriter and guitarist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Thunder<PERSON>\" title=\"<PERSON> Thunders\"><PERSON></a>, American singer-songwriter and guitarist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Haitian priest and politician, 49th President of Haiti", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian priest and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Haitian priest and politician, 49th <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Malaysian royal consort", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian royal consort", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian royal consort", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sultanah_<PERSON>h"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Malaysian politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Malaysian politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian rugby league player", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON>, American-Greek lawyer and politician, 78th Mayor of Athens", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Greek lawyer and politician, 78th <a href=\"https://wikipedia.org/wiki/Mayor_of_Athens\" class=\"mw-redirect\" title=\"Mayor of Athens\">Mayor of Athens</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Greek lawyer and politician, 78th <a href=\"https://wikipedia.org/wiki/Mayor_of_Athens\" class=\"mw-redirect\" title=\"Mayor of Athens\">Mayor of Athens</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Mayor of Athens", "link": "https://wikipedia.org/wiki/Mayor_of_Athens"}]}, {"year": "1954", "text": "<PERSON>, Argentinian footballer and manager", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Indian theoretical physicist and string theorist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian theoretical physicist and string theorist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian theoretical physicist and string theorist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English singer-songwriter and guitarist (d. 1980)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, British botanist, educator, and academician", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist, educator, and academician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British botanist, educator, and academician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian ice hockey player, coach, and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Australian rugby league player, coach, and administrator", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and administrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and administrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, South African racing driver", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, American activist and former Planned Parenthood president (d. 2025)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and former <a href=\"https://wikipedia.org/wiki/Planned_Parenthood\" title=\"Planned Parenthood\">Planned Parenthood</a> president (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American activist and former <a href=\"https://wikipedia.org/wiki/Planned_Parenthood\" title=\"Planned Parenthood\">Planned Parenthood</a> president (d. 2025)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Planned Parenthood", "link": "https://wikipedia.org/wiki/Planned_Parenthood"}]}, {"year": "1958", "text": "<PERSON>, English footballer and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American lawyer and politician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, Canadian actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, French journalist and screenwriter", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French journalist and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9"}]}, {"year": "1961", "text": "<PERSON>, American actor", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Whitaker\" title=\"<PERSON> Whitaker\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Whitaker\" title=\"<PERSON> Whitaker\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Forest_Whitaker"}]}, {"year": "1962", "text": "<PERSON><PERSON>, Greek basketball player and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Australian swimmer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Ford\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Danish-Italian actress", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Italian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish-Italian actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English-Canadian ice hockey player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, English-Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, English-Canadian ice hockey player and coach", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "1965", "text": "<PERSON>, Scottish lawyer and politician, Secretary of State for Scotland", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Scotland\" title=\"Secretary of State for Scotland\">Secretary of State for Scotland</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Scotland", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Scotland"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Austrian neuroscientist and educator", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nb%C3%B6ck\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian neuroscientist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nb%C3%B6ck\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian neuroscientist and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gero_Miesenb%C3%B6ck"}]}, {"year": "1965", "text": "<PERSON>, English politician, Secretary of State for Foreign and Commonwealth Affairs", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs\" class=\"mw-redirect\" title=\"Secretary of State for Foreign and Commonwealth Affairs\">Secretary of State for Foreign and Commonwealth Affairs</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Foreign and Commonwealth Affairs", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Foreign_and_Commonwealth_Affairs"}]}, {"year": "1966", "text": "<PERSON>, English singer-songwriter and drummer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, French-Swiss actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Ir%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ir%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Swiss actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ir%C3%A8ne_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American actor and special effects designer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Adam_Savage\" title=\"Adam Savage\"><PERSON></a>, American actor and special effects designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adam_Savage\" title=\"Adam Savage\"><PERSON></a>, American actor and special effects designer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American singer-songwriter (d. 2015)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Elbert_West\" title=\"Elbert West\"><PERSON><PERSON></a>, American singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elbert_West\" title=\"Elbert West\"><PERSON><PERSON></a>, American singer-songwriter (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elbert_West"}]}, {"year": "1968", "text": "<PERSON>, American comedian, actor, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Estonian footballer and coach", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Ain_<PERSON>mus\" title=\"<PERSON> Tam<PERSON>\"><PERSON></a>, Estonian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ain_<PERSON>\" title=\"Ain Tam<PERSON>\"><PERSON></a>, Estonian footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ain_Tammus"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Turkish bass player and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Tarkan_G%C3%B6z%C3%BCb%C3%BCy%C3%BCk\" title=\"Tarkan Gözübüyük\"><PERSON><PERSON><PERSON>ö<PERSON>übüyü<PERSON></a>, Turkish bass player and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tarkan_G%C3%B6z%C3%BCb%C3%BCy%C3%BCk\" title=\"Tarkan Gözübüyük\"><PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON></a>, Turkish bass player and producer", "links": [{"title": "Tarkan Gözübüyük", "link": "https://wikipedia.org/wiki/Tarkan_G%C3%B6z%C3%BCb%C3%BCy%C3%BCk"}]}, {"year": "1972", "text": "<PERSON>, American actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Jamaican singer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Jamaican singer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>uju_<PERSON>ton"}]}, {"year": "1975", "text": "<PERSON>, American wrestler and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(American_wrestler)\" title=\"<PERSON> (American wrestler)\"><PERSON></a>, American wrestler and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(American_wrestler)\" title=\"<PERSON> (American wrestler)\"><PERSON></a>, American wrestler and manager", "links": [{"title": "<PERSON> (American wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(American_wrestler)"}]}, {"year": "1975", "text": "<PERSON>, English cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Law\"><PERSON></a>, English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Danny Law\"><PERSON></a>, English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian basketball player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American boxer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Italian footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, German actress and model", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German actress and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Mexican-American comedian and voice actor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American comedian and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American comedian and voice actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gabriel_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian racing driver", "html": "1977 - <a href=\"https://wikipedia.org/wiki/D<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian racing driver", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, South African cricketer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_Nel"}]}, {"year": "1977", "text": "<PERSON>, American actress", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/John_St._Clair\" title=\"John St. Clair\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_St._Clair\" title=\"John St. Clair\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_St._Clair"}]}, {"year": "1977", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Toro"}]}, {"year": "1978", "text": "<PERSON>, Dominican baseball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress and singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Swiss footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Icelandic footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Edda_Gar%C3%B0arsd%C3%B3ttir\" title=\"Edda Garðarsdóttir\"><PERSON><PERSON></a>, Icelandic footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edda_Gar%C3%B0arsd%C3%B3ttir\" title=\"Edda Garðarsdóttir\"><PERSON><PERSON></a>, Icelandic footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Edda_Gar%C3%B0arsd%C3%B3ttir"}]}, {"year": "1979", "text": "<PERSON><PERSON>, Czech tennis player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ku%C4%8Derov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Ku%C4%8Derov%C3%A1\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Renata_Ku%C4%8Derov%C3%A1"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Indonesian singer and actress (d. 2017)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian singer and actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian singer and actress (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, French footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Greek bassist and composer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek bassist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek bassist and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Lithuanian footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON>\"><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Stan<PERSON>vi%C4%8Dius"}]}, {"year": "1982", "text": "<PERSON>, French racing driver", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Julien Canal\"><PERSON></a>, French racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julien_Canal\" title=\"Julien Canal\"><PERSON></a>, French racing driver", "links": [{"title": "Julien Canal", "link": "https://wikipedia.org/wiki/Julien_Canal"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, New Zealand rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Nee<PERSON>_Tialata\" title=\"Neemia Tialata\"><PERSON><PERSON><PERSON> Tial<PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nee<PERSON>_Tialata\" title=\"Neemia Tialata\"><PERSON><PERSON><PERSON> T<PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Tialata"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Venezuelan model and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/A%C3%ADda_Y%C3%A9spica\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A%C3%ADda_Y%C3%A9spica\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan model and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A%C3%ADda_Y%C3%A9spica"}]}, {"year": "1983", "text": "<PERSON>, American racing driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Brazilian racing driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>o"}]}, {"year": "1983", "text": "<PERSON>, American wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Slovak skier", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Veronika_Velez-Zuzulov%C3%A1\" title=\"Veronika Velez-Zuzulová\"><PERSON><PERSON><PERSON> Velez-<PERSON>uzulov<PERSON></a>, Slovak skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veronika_Velez-Zuzulov%C3%A1\" title=\"Veronika Velez-Zuzulová\"><PERSON><PERSON><PERSON>-<PERSON>uzulov<PERSON></a>, Slovak skier", "links": [{"title": "<PERSON><PERSON><PERSON>ulov<PERSON>", "link": "https://wikipedia.org/wiki/Veronika_Velez-Zuzulov%C3%A1"}]}, {"year": "1986", "text": "<PERSON>, Canadian ice hockey player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actor ", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> II</a>, American actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Russian tennis player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American basketball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, English singer and actor ", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer and actor ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, English singer and actor ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/O<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American ice hockey player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American racing driver", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, American racing driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_July_1991)\" title=\"<PERSON><PERSON> (footballer, born July 1991)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_July_1991)\" title=\"<PERSON><PERSON> (footballer, born July 1991)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born July 1991)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_July_1991)"}]}, {"year": "1991", "text": "<PERSON>, American basketball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Fav<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>avors"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Russian boxer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian boxer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Spanish tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Nuria_P%C3%A1rrizas_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nuria_P%C3%A1rrizas_D%C3%ADaz\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nuria_P%C3%A1rrizas_D%C3%ADaz"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South African sprinter", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African sprinter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/H%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A5<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American racing driver", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Japanese baseball player", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, Dutch football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Swiss tennis player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American racing driver", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Egyptian footballer", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, Egyptian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)\" title=\"<PERSON> (footballer, born 1999)\"><PERSON></a>, Egyptian footballer", "links": [{"title": "<PERSON> (footballer, born 1999)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1999)"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, American basketball player", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American actor", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Iain_Armitage"}]}], "Deaths": [{"year": "756", "text": "<PERSON>, consort of <PERSON><PERSON> (b. 719)", "html": "756 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, consort of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON> of Tang\"><PERSON><PERSON></a> (b. 719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, consort of <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON>_of_Tang\" title=\"Emperor <PERSON><PERSON> of Tang\"><PERSON><PERSON></a> (b. 719)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emperor <PERSON><PERSON><PERSON> of Tang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Tang"}]}, {"year": "998", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, Persian mathematician and astronomer (b. 940)", "html": "998 - <a href=\"https://wikipedia.org/wiki/Ab%C5%AB_al-Waf%C4%81%27_B%C5%ABzj%C4%81n%C4%AB\" class=\"mw-redirect\" title=\"A<PERSON><PERSON> al-Waf<PERSON>' Būzj<PERSON>\"><PERSON><PERSON><PERSON><PERSON>' <PERSON></a>, Persian mathematician and astronomer (b. 940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ab%C5%AB_al-Waf%C4%81%27_B%C5%ABzj%C4%81n%C4%AB\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> al-Wafā' Būzj<PERSON>\"><PERSON><PERSON><PERSON> <PERSON><PERSON></a>, Persian mathematician and astronomer (b. 940)", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ab%C5%AB_al-Waf%C4%81%27_B%C5%ABzj%C4%81n%C4%AB"}]}, {"year": "1015", "text": "<PERSON> the Great, Grand prince of Kievan Rus' (b. c. 958)", "html": "1015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, <a href=\"https://wikipedia.org/wiki/Grand_prince\" title=\"Grand prince\">Grand prince</a> of <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan Rus'\"><PERSON><PERSON>'</a> (b. c. 958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the Great</a>, <a href=\"https://wikipedia.org/wiki/Grand_prince\" title=\"Grand prince\">Grand prince</a> of <a href=\"https://wikipedia.org/wiki/Kievan_Rus%27\" title=\"Kievan Rus'\"><PERSON>an <PERSON>'</a> (b. c. 958)", "links": [{"title": "<PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON>_the_Great"}, {"title": "Grand prince", "link": "https://wikipedia.org/wiki/Grand_prince"}, {"title": "<PERSON><PERSON>'", "link": "https://wikipedia.org/wiki/Kievan_Rus%27"}]}, {"year": "1274", "text": "<PERSON><PERSON><PERSON>, Italian bishop and saint (b. 1221)", "html": "1274 - <a href=\"https://wikipedia.org/wiki/Bonaventure\" title=\"Bonaventure\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (b. 1221)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonaventure\" title=\"Bonaventure\"><PERSON><PERSON><PERSON></a>, Italian bishop and saint (b. 1221)", "links": [{"title": "Bonaventure", "link": "https://wikipedia.org/wiki/Bonaventure"}]}, {"year": "1291", "text": "<PERSON> of Germany (b. 1218)", "html": "1291 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (b. 1218)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany\" title=\"<PERSON> of Germany\"><PERSON> of Germany</a> (b. 1218)", "links": [{"title": "<PERSON> of Germany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Germany"}]}, {"year": "1299", "text": "King <PERSON> of Norway (b. c. 1268)", "html": "1299 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" title=\"<PERSON> of Norway\"><PERSON> of Norway</a> (b. c. 1268)", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway\" title=\"<PERSON> of Norway\"><PERSON> of Norway</a> (b. c. 1268)", "links": [{"title": "<PERSON> of Norway", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Norway"}]}, {"year": "1381", "text": "<PERSON>, English Lollard priest", "html": "1381 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/<PERSON>llardy\" title=\"<PERSON>llar<PERSON>\"><PERSON><PERSON><PERSON></a> priest", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)\" title=\"<PERSON> (priest)\"><PERSON></a>, English <a href=\"https://wikipedia.org/wiki/<PERSON>llardy\" title=\"<PERSON>llar<PERSON>\"><PERSON><PERSON><PERSON></a> priest", "links": [{"title": "<PERSON> (priest)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(priest)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1388", "text": "<PERSON> of Durazzo, titular Latin empress consort of Constantinople (b. 1313)", "html": "1388 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Durazzo\" title=\"Agnes of Durazzo\"><PERSON> of Durazzo</a>, titular Latin empress consort of Constantinople (b. 1313)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Durazzo\" title=\"Agnes of Durazzo\"><PERSON> of Durazzo</a>, titular Latin empress consort of Constantinople (b. 1313)", "links": [{"title": "<PERSON> of Durazzo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1397", "text": "<PERSON> of Henneberg, German ruler (b. c. 1334)", "html": "1397 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Henneberg\" title=\"<PERSON> of Henneberg\"><PERSON> of Henneberg</a>, German ruler (b. c. 1334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Henne<PERSON>\" title=\"<PERSON> of Henneberg\"><PERSON> of Henneberg</a>, German ruler (b. c. 1334)", "links": [{"title": "<PERSON> of Henneberg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1406", "text": "<PERSON>, Duke of Austria", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a>", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria"}]}, {"year": "1410", "text": "<PERSON>, German Grand Master of the Teutonic Knights (b. 1360)", "html": "1410 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Grand Master of the Teutonic Knights (b. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German Grand Master of the Teutonic Knights (b. 1360)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1445", "text": "<PERSON>, Queen of Scotland", "html": "1445 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Scotland\" class=\"mw-redirect\" title=\"<PERSON>, Queen of Scotland\"><PERSON>, Queen of Scotland</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scotland\" class=\"mw-redirect\" title=\"<PERSON>, Queen of Scotland\"><PERSON>, Queen of Scotland</a>", "links": [{"title": "<PERSON>, Queen of Scotland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Queen_of_Scotland"}]}, {"year": "1542", "text": "<PERSON>, subject of <PERSON>'s painting <PERSON> (b. 1479)", "html": "1542 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>do\" title=\"<PERSON> del G<PERSON>condo\"><PERSON></a>, subject of <PERSON>'s painting <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lisa\"><PERSON></a></i> (b. 1479)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> del Giocondo\"><PERSON></a>, subject of <PERSON>'s painting <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lisa\"><PERSON></a></i> (b. 1479)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1544", "text": "<PERSON> Châlon (b. 1519)", "html": "1544 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Ch%C3%A2lon\" class=\"mw-redirect\" title=\"<PERSON> of Châlon\"><PERSON> of Châlon</a> (b. 1519)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_of_Ch%C3%A2lon\" class=\"mw-redirect\" title=\"<PERSON> of Châlon\"><PERSON> of Châlon</a> (b. 1519)", "links": [{"title": "<PERSON> of Châlon", "link": "https://wikipedia.org/wiki/Ren%C3%A9_of_Ch%C3%A2lon"}]}, {"year": "1571", "text": "<PERSON><PERSON><PERSON>, Japanese daimyō (b. 1514)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1514)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese daimyō (b. 1514)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>a"}]}, {"year": "1609", "text": "<PERSON><PERSON><PERSON>, Italian painter and illustrator (b. 1560)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and illustrator (b. 1560)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1614", "text": "<PERSON>, seigneur <PERSON>, French soldier, historian and author (b. 1540)", "html": "1614 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seigneur_<PERSON>_<PERSON><PERSON>%C3%B4me\" title=\"<PERSON>, seigneur de Brantôme\"><PERSON>, seigneur <PERSON></a>, French soldier, historian and author (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_seigneur_<PERSON>_<PERSON>%C3%B4me\" title=\"<PERSON>, seigneur de Brantôme\"><PERSON>, seigneur <PERSON></a>, French soldier, historian and author (b. 1540)", "links": [{"title": "<PERSON>, seigneur de Brantôme", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_seigneur_<PERSON>_<PERSON><PERSON>%C3%B4me"}]}, {"year": "1655", "text": "<PERSON><PERSON><PERSON>, Italian architect (b. 1570)", "html": "1655 - <a href=\"https://wikipedia.org/wiki/Giro<PERSON><PERSON>_Rainaldi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect (b. 1570)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rainaldi\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian architect (b. 1570)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Rainaldi"}]}, {"year": "1685", "text": "<PERSON>, 1st Duke of Monmouth, Dutch-born English general and claimant to the throne, executed (b. 1649)", "html": "1685 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>, Dutch-born English general and claimant to the throne, executed (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Monmouth\" title=\"<PERSON>, 1st Duke of Monmouth\"><PERSON>, 1st Duke of Monmouth</a>, Dutch-born English general and claimant to the throne, executed (b. 1649)", "links": [{"title": "<PERSON>, 1st Duke of Monmouth", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Monmouth"}]}, {"year": "1750", "text": "<PERSON><PERSON>, Russian ethnographer and politician (b. 1686)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ethnographer and politician (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian ethnographer and politician (b. 1686)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1765", "text": "<PERSON><PERSON><PERSON>, French painter (b. 1705)", "html": "1765 - <a href=\"https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>r%C3%A<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French painter (b. 1705)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-Andr%C3%A9_<PERSON>_<PERSON>o"}]}, {"year": "1767", "text": "<PERSON>, Scottish poet and composer (b. 1746)", "html": "1767 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and composer (b. 1746)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, Scottish poet and composer (b. 1746)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(poet)"}]}, {"year": "1789", "text": "<PERSON>, French harpsichord player and composer (b. 1715)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1715)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player and composer (b. 1715)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON>, French sculptor (b. 1741)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor (b. 1741)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French sculptor (b. 1741)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}, {"year": "1839", "text": "<PERSON><PERSON>, English poet and politician (b. 1802)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/Winthrop_Mack<PERSON>_Praed\" title=\"Winthrop Mackworth Praed\"><PERSON><PERSON>raed</a>, English poet and politician (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Winthrop_Mackworth_Praed\" title=\"Winthrop Mackworth Praed\"><PERSON><PERSON>raed</a>, English poet and politician (b. 1802)", "links": [{"title": "<PERSON><PERSON>raed", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_Praed"}]}, {"year": "1844", "text": "<PERSON>, French philologist and historian (b. 1772)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and historian (b. 1772)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French philologist and historian (b. 1772)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON>, French nun, founder of the Sisters of St Joseph of Cluny (b. 1779)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nun, founder of the Sisters of St Joseph of Cluny (b. 1779)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French nun, founder of the Sisters of St Joseph of Cluny (b. 1779)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Argentinian general and politician (b. 1787)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian general and politician (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, Austrian pianist and composer (b. 1791)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian pianist and composer (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, Russian painter (b. 1806)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (b. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "General <PERSON>, American circus performer (b. 1838)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/General_<PERSON>_<PERSON>\" title=\"General <PERSON>\">General <PERSON></a>, American circus performer (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/General_<PERSON>_<PERSON>\" title=\"General <PERSON>\">General <PERSON></a>, American circus performer (b. 1838)", "links": [{"title": "General <PERSON>", "link": "https://wikipedia.org/wiki/General_<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Spanish author and poet (b. 1837)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Rosal%C3%AD<PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON> de <PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and poet (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rosal%C3%AD<PERSON>_de_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish author and poet (b. 1837)", "links": [{"title": "Rosalía de Castro", "link": "https://wikipedia.org/wiki/Rosal%C3%<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON>, Swiss author, poet and playwright (b. 1819)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author, poet and playwright (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss author, poet and playwright (b. 1819)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON><PERSON>, French-American archbishop (b. 1825)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American archbishop (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-American archbishop (b. 1825)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Russian playwright and short story writer (b. 1860)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and short story writer (b. <a href=\"https://wikipedia.org/wiki/1860\" title=\"1860\">1860</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian playwright and short story writer (b. <a href=\"https://wikipedia.org/wiki/1860\" title=\"1860\">1860</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "1860", "link": "https://wikipedia.org/wiki/1860"}]}, {"year": "1919", "text": "<PERSON>, German chemist and academic, Nobel Prize laureate (b. 1852)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. <a href=\"https://wikipedia.org/wiki/1852\" title=\"1852\">1852</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. <a href=\"https://wikipedia.org/wiki/1852\" title=\"1852\">1852</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}, {"title": "1852", "link": "https://wikipedia.org/wiki/1852"}]}, {"year": "1929", "text": "<PERSON>, Austrian author, poet, and playwright (b. 1874)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright (b. <a href=\"https://wikipedia.org/wiki/1874\" title=\"1874\">1874</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian author, poet, and playwright (b. <a href=\"https://wikipedia.org/wiki/1874\" title=\"1874\">1874</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "1874", "link": "https://wikipedia.org/wiki/1874"}]}, {"year": "1930", "text": "<PERSON>, Hungarian violinist, composer, and conductor (b. 1845)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian violinist, composer, and conductor (b. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Irish-born American social activist (b. 1849)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American social activist (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish-born American social activist (b. 1849)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON><PERSON>, Russian-German economist and mathematician (b. 1868)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German economist and mathematician (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-German economist and mathematician (b. 1868)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Iranian writer and leader in the Baha'i faith (b. 1846) ", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Bah%C3%ADyyih_Kh%C3%A1num\" title=\"<PERSON>h<PERSON>y<PERSON><PERSON>ánum\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian writer and leader in the Baha'i faith (b. 1846) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bah%C3%ADyyih_Kh%C3%A1num\" title=\"Bahíyyih <PERSON>hánum\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Iranian writer and leader in the Baha'i faith (b. 1846) ", "links": [{"title": "<PERSON>h<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bah%C3%ADyyih_Kh%C3%A1num"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, South African poet and politician (b. 1873)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African poet and politician (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African poet and politician (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Langenhoven"}]}, {"year": "1933", "text": "<PERSON>, American scholar, critic, and academic (b. 1865)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, critic, and academic (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American scholar, critic, and academic (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American cornet player (b. 1890)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cornet player (b. 1890)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, Swiss psychiatrist and physician (b. 1857)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss psychiatrist and physician (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss psychiatrist and physician (b. 1857)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, American giant, 8\"11' 271 cm (b. 1918)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American giant, 8\"11' 271 cm (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American giant, 8\"11' 271 cm (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Filipino lawyer and politician (b. 1910)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/Wenceslao_Vinzons\" title=\"Wenceslao Vin<PERSON>s\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wenceslao_<PERSON>s\" title=\"Wenceslao Vin<PERSON>s\"><PERSON><PERSON><PERSON><PERSON></a>, Filipino lawyer and politician (b. 1910)", "links": [{"title": "Wenceslao <PERSON>s", "link": "https://wikipedia.org/wiki/Wenceslao_Vinzons"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian botanist and academic (b. 1885)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian botanist and academic (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian botanist and academic (b. 1885)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON>, English cricketer and coach (b. 1877)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and coach (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and coach (b. 1877)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American soldier and songwriter (b. 1893)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American soldier and songwriter (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American soldier and songwriter (b. 1893)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)"}]}, {"year": "1948", "text": "<PERSON>, American general (b. 1860)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian archbishop, founded the Order of the Imitation of Christ (b. 1882)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Geevarghese Mar Ivan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian archbishop, founded the <a href=\"https://wikipedia.org/wiki/Order_of_the_Imitation_of_Christ\" class=\"mw-redirect\" title=\"Order of the Imitation of Christ\">Order of the Imitation of Christ</a> (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Geevarghese Mar Ivan<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian archbishop, founded the <a href=\"https://wikipedia.org/wiki/Order_of_the_Imitation_of_Christ\" class=\"mw-redirect\" title=\"Order of the Imitation of Christ\">Order of the Imitation of Christ</a> (b. 1882)", "links": [{"title": "Geevarghese Mar <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Order of the Imitation of Christ", "link": "https://wikipedia.org/wiki/Order_of_the_Imitation_of_Christ"}]}, {"year": "1957", "text": "<PERSON>, American publisher and politician, 46th Governor of Ohio (b. 1870)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. <a href=\"https://wikipedia.org/wiki/1870\" title=\"1870\">1870</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and politician, 46th <a href=\"https://wikipedia.org/wiki/Governor_of_Ohio\" class=\"mw-redirect\" title=\"Governor of Ohio\">Governor of Ohio</a> (b. <a href=\"https://wikipedia.org/wiki/1870\" title=\"1870\">1870</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Governor of Ohio", "link": "https://wikipedia.org/wiki/Governor_of_Ohio"}, {"title": "1870", "link": "https://wikipedia.org/wiki/1870"}]}, {"year": "1957", "text": "<PERSON><PERSON>, a Russian lawyer and politician (b. 1869)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a Russian lawyer and politician (b. 1869)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, a Russian lawyer and politician (b. 1869)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Swiss-American composer and academic (b. 1880)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American composer and academic (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American composer and academic (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian author and critic (b. 1885)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. <a href=\"https://wikipedia.org/wiki/1885\" title=\"1885\">1885</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author and critic (b. <a href=\"https://wikipedia.org/wiki/1885\" title=\"1885\">1885</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1885", "link": "https://wikipedia.org/wiki/1885"}]}, {"year": "1960", "text": "<PERSON>, Swedish politician (b. 1897)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish politician (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American singer and actor (b. 1896)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actor (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Canadian lawyer and politician, 5th Premier of Alberta (b. 1884)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Alberta\" title=\"Premier of Alberta\">Premier of Alberta</a> (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Alberta", "link": "https://wikipedia.org/wiki/Premier_of_Alberta"}]}, {"year": "1961", "text": "<PERSON>, Russian mathematician (b. 1901)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nina_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American lawyer and politician, 35th Governor of Arkansas (b. 1908)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(governor)\" title=\"<PERSON> (governor)\"><PERSON></a>, American lawyer and politician, 35th <a href=\"https://wikipedia.org/wiki/Governor_of_Arkansas\" class=\"mw-redirect\" title=\"Governor of Arkansas\">Governor of Arkansas</a> (b. 1908)", "links": [{"title": "<PERSON> (governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(governor)"}, {"title": "Governor of Arkansas", "link": "https://wikipedia.org/wiki/Governor_of_Arkansas"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Turkish architect (b. 1903)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish architect (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish architect (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>an"}]}, {"year": "1969", "text": "<PERSON>, American labor reformer and researcher (b. 1885)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor reformer and researcher (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American labor reformer and researcher (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American journalist (b. 1944)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. <a href=\"https://wikipedia.org/wiki/1944\" title=\"1944\">1944</a>)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (b. <a href=\"https://wikipedia.org/wiki/1944\" title=\"1944\">1944</a>)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1944", "link": "https://wikipedia.org/wiki/1944"}]}, {"year": "1976", "text": "<PERSON>, American journalist and author (b. 1897)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Australian businessman and activist (b. 1933)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(anti-drugs_campaigner)\" title=\"<PERSON> (anti-drugs campaigner)\"><PERSON></a>, Australian businessman and activist (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(anti-drugs_campaigner)\" title=\"<PERSON> (anti-drugs campaigner)\"><PERSON></a>, Australian businessman and activist (b. 1933)", "links": [{"title": "<PERSON> (anti-drugs campaigner)", "link": "https://wikipedia.org/wiki/<PERSON>_(anti-drugs_campaigner)"}]}, {"year": "1979", "text": "<PERSON>, Mexican academic and politician, 29th President of Mexico (b. 1911)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Gustavo_D%C3%ADaz_Ordaz\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gustavo_D%C3%ADaz_Ordaz\" title=\"<PERSON>\"><PERSON></a>, Mexican academic and politician, 29th <a href=\"https://wikipedia.org/wiki/President_of_Mexico\" title=\"President of Mexico\">President of Mexico</a> (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Gustavo_D%C3%ADaz_Ordaz"}, {"title": "President of Mexico", "link": "https://wikipedia.org/wiki/President_of_Mexico"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Canadian lawyer, judge, and politician (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Canadian lawyer, judge, and politician (b. 1898)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A9d%C3%A9ric_Dorion"}]}, {"year": "1982", "text": "<PERSON>, American saxophonist, songwriter, and producer (b. 1926)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American saxophonist, songwriter, and producer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American harness racer and trainer (b. 1923)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American harness racer and trainer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American harness racer and trainer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American librarian, author, and illustrator (b. 1906)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian, author, and illustrator (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American librarian, author, and illustrator (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer (b. 1956)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Yugoslav and Bosnian writer (b. 1920)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Zaim_Top%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav and Bosnian writer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zaim_Top%C4%8Di%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Yugoslav and Bosnian writer (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zaim_Top%C4%8Di%C4%87"}]}, {"year": "1990", "text": "<PERSON>, English actress (b. 1916)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Syrian poet and diplomat, 4th Syrian Ambassador to the United States (b. 1910)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian poet and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Ambassador_of_Syria_to_the_United_States\" class=\"mw-redirect\" title=\"Ambassador of Syria to the United States\">Syrian Ambassador to the United States</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Syrian poet and diplomat, 4th <a href=\"https://wikipedia.org/wiki/Ambassador_of_Syria_to_the_United_States\" class=\"mw-redirect\" title=\"Ambassador of Syria to the United States\">Syrian Ambassador to the United States</a> (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Ambassador of Syria to the United States", "link": "https://wikipedia.org/wiki/Ambassador_of_Syria_to_the_United_States"}]}, {"year": "1991", "text": "<PERSON>, American actor, singer, and game show host (b. 1933)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and game show host (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and game show host (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Nauruan educator and politician, 1st President of Nauru (b. 1922)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hammer DeRoburt\"><PERSON></a>, Nauruan educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"Hammer DeRoburt\"><PERSON></a>, Nauruan educator and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Nauru\" title=\"President of Nauru\">President of Nauru</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_DeRob<PERSON>"}, {"title": "President of Nauru", "link": "https://wikipedia.org/wiki/President_of_Nauru"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Azerbaijani journalist and author (b. 1960)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)\" title=\"<PERSON><PERSON> (journalist)\"><PERSON><PERSON></a>, Azerbaijani journalist and author (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)\" title=\"<PERSON><PERSON> (journalist)\"><PERSON><PERSON></a>, Azerbaijani journalist and author (b. 1960)", "links": [{"title": "<PERSON><PERSON> (journalist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(journalist)"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Lithuanian basketball player (b. 1924)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%8Dius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian basketball player (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Justinas_Lagunavi%C4%8Dius"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Italian fashion designer, founded <PERSON><PERSON><PERSON> (b. 1946)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/Versace\" title=\"Versace\">Versace</a> (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian fashion designer, founded <a href=\"https://wikipedia.org/wiki/Versace\" title=\"Versace\">Versace</a> (b. 1946)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Versace", "link": "https://wikipedia.org/wiki/Versace"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Sri Lankan politician (b. 1960)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a>, Sri Lankan politician (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(Sri_Lankan_politician)\" title=\"<PERSON><PERSON> (Sri Lankan politician)\"><PERSON><PERSON></a>, Sri Lankan politician (b. 1960)", "links": [{"title": "<PERSON><PERSON> (Sri Lankan politician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_(Sri_Lankan_politician)"}]}, {"year": "2000", "text": "<PERSON>, Canadian opera singer and educator (b. 1925)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian opera singer and educator (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian opera singer and educator (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Sri Lankan lawyer and civil servant (b. 1917)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and civil servant (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan lawyer and civil servant (b. 1917)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Chilean novelist, short-story writer, poet, and essayist (b. 1953)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Chilean novelist, short-story writer, poet, and essayist (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B1o\" title=\"<PERSON>\"><PERSON></a>, Chilean novelist, short-story writer, poet, and essayist (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roberto_Bola%C3%B1o"}]}, {"year": "2003", "text": "<PERSON>, American actress and singer (b. 1904)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American businessman, founder of Hooters and Naturally Fresh, Inc. (b. 1937)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founder of <a href=\"https://wikipedia.org/wiki/Hooters\" title=\"Hooters\">Hooters</a> and <a href=\"https://wikipedia.org/wiki/Naturally_Fresh,_Inc.\" title=\"Naturally Fresh, Inc.\">Naturally Fresh, Inc.</a> (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founder of <a href=\"https://wikipedia.org/wiki/Hooters\" title=\"Hooters\">Hooters</a> and <a href=\"https://wikipedia.org/wiki/Naturally_Fresh,_Inc.\" title=\"Naturally Fresh, Inc.\">Naturally Fresh, Inc.</a> (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Hooters", "link": "https://wikipedia.org/wiki/Hooters"}, {"title": "Naturally Fresh, Inc.", "link": "https://wikipedia.org/wiki/Naturally_Fresh,_Inc."}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Iranian archaeologist and academic (b. 1942)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian archaeologist and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian archaeologist and academic (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian canoe racer (b. 1972)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Kolonics\" title=\"György Kolonics\">György Kolonics</a>, Hungarian canoe racer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gy%C3%B6rgy_Kolonics\" title=\"György Kolonics\">György Kolon<PERSON></a>, Hungarian canoe racer (b. 1972)", "links": [{"title": "György Kolonics", "link": "https://wikipedia.org/wiki/Gy%C3%B6rgy_Kolonics"}]}, {"year": "2010", "text": "<PERSON>, American politician and diplomat, United States Ambassador to Saudi Arabia (b. 1926)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia\" class=\"mw-redirect\" title=\"United States Ambassador to Saudi Arabia\">United States Ambassador to Saudi Arabia</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician and diplomat, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia\" class=\"mw-redirect\" title=\"United States Ambassador to Saudi Arabia\">United States Ambassador to Saudi Arabia</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Ambassador to Saudi Arabia", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_Saudi_Arabia"}]}, {"year": "2011", "text": "<PERSON>, German landowner and politician (b. 1928)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German landowner and politician (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German landowner and politician (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, British-Australian actress (b. 1917)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Googie_<PERSON>ers\" title=\"Goog<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, British-Australian actress (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Googie_<PERSON>\" title=\"Googie <PERSON>\"><PERSON><PERSON><PERSON></a>, British-Australian actress (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Googie_Withers"}]}, {"year": "2012", "text": "<PERSON>, Moldovan footballer (b. 1975)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan footballer (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan footballer (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Israeli-French actress (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-French actress (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli-French actress (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON><PERSON>ton"}]}, {"year": "2012", "text": "<PERSON>, American football player (b. 1960)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English general (b. 1920)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English general (b. 1920)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "2012", "text": "<PERSON>, American actress and singer (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Japanese director and screenwriter (b. 1931)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese director and screenwriter (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Syrian-American poet and activist (b. 1945)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian-American poet and activist (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Syrian-American poet and activist (b. 1945)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ninos_Aho"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and politician (b. 1944)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American lawyer and judge (b. 1956)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1940)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Chinese-American pianist and composer (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/No%C3%AB<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-American pianist and composer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/No%C3%ABl_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese-American pianist and composer (b. 1924)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/No%C3%ABl_Lee"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Ethiopian runner (b. 1986)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/Meskerem_Legesse\" title=\"Meskerem Legesse\">Me<PERSON><PERSON></a>, Ethiopian runner (b. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meskerem_Legesse\" title=\"Meskerem Legesse\"><PERSON><PERSON><PERSON></a>, Ethiopian runner (b. 1986)", "links": [{"title": "Meskerem Legesse", "link": "https://wikipedia.org/wiki/Meskerem_Legesse"}]}, {"year": "2013", "text": "<PERSON>, American computer scientist and academic (b. 1962)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic (b. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Honduran author, poet, and diplomat (b. 1933)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/%C3%93scar_Acosta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Honduran author, poet, and diplomat (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%93scar_Acosta\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Honduran author, poet, and diplomat (b. 1933)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%93scar_Acosta"}]}, {"year": "2014", "text": "<PERSON>, American historian, political scientist, and author (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, political scientist, and author (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian, political scientist, and author (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American neuroscientist and academic (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American neuroscientist and academic (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Japanese-American economist and academic (b. 1938)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American economist and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese-American economist and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Chinese politician, 4th Vice Premier of the People's Republic of China (b. 1916)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China\" title=\"List of vice premiers of the People's Republic of China\">Vice Premier of the People's Republic of China</a> (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of vice premiers of the People's Republic of China", "link": "https://wikipedia.org/wiki/List_of_vice_premiers_of_the_People%27s_Republic_of_China"}]}, {"year": "2015", "text": "<PERSON>, British actor (b. 1926)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian singer (b. 1933)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian singer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American film and television actor (b. 1928)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and television actor (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film and television actor (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, Dutch investigative journalist and crime reporter (b. 1956)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch investigative journalist and crime reporter (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch investigative journalist and crime reporter (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}]}}