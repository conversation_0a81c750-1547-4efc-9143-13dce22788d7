{"date": "July 8", "url": "https://wikipedia.org/wiki/July_8", "data": {"Events": [{"year": "1099", "text": "Some 15,000 starving Christian soldiers begin the siege of Jerusalem by marching in a religious procession around the city as its Muslim defenders watch.", "html": "1099 - Some 15,000 starving Christian soldiers begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">siege of Jerusalem</a> by marching in a religious procession around the city as its Muslim defenders watch.", "no_year_html": "Some 15,000 starving Christian soldiers begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)\" title=\"Siege of Jerusalem (1099)\">siege of Jerusalem</a> by marching in a religious procession around the city as its Muslim defenders watch.", "links": [{"title": "Siege of Jerusalem (1099)", "link": "https://wikipedia.org/wiki/Siege_of_Jerusalem_(1099)"}]}, {"year": "1167", "text": "The Byzantines defeat the Hungarian army decisively at Sirmium, forcing the Hungarians to sue for peace.", "html": "1167 - The Byzantines defeat the Hungarian army <a href=\"https://wikipedia.org/wiki/Battle_of_Sirmium\" title=\"Battle of Sirmium\">decisively at Sirmium</a>, forcing the Hungarians to sue for peace.", "no_year_html": "The Byzantines defeat the Hungarian army <a href=\"https://wikipedia.org/wiki/Battle_of_Sirmium\" title=\"Battle of Sirmium\">decisively at Sirmium</a>, forcing the Hungarians to sue for peace.", "links": [{"title": "Battle of Sirmium", "link": "https://wikipedia.org/wiki/Battle_of_Sirmium"}]}, {"year": "1283", "text": "<PERSON> of Lauria, commanding the Aragonese fleet, defeats an Angevin fleet sent to put down a rebellion on Malta.", "html": "1283 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> of Lauria\"><PERSON> of <PERSON>ria</a>, commanding the <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">Aragonese</a> fleet, <a href=\"https://wikipedia.org/wiki/Battle_of_Malta\" title=\"Battle of Malta\">defeats</a> an <a href=\"https://wikipedia.org/wiki/Capetian_House_of_Anjou\" title=\"Capetian House of Anjou\">Ang<PERSON><PERSON></a> fleet sent to put down a rebellion on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> of Lauria\"><PERSON> of <PERSON>ria</a>, commanding the <a href=\"https://wikipedia.org/wiki/Crown_of_Aragon\" title=\"Crown of Aragon\">Aragonese</a> fleet, <a href=\"https://wikipedia.org/wiki/Battle_of_Malta\" title=\"Battle of Malta\">defeats</a> an <a href=\"https://wikipedia.org/wiki/Capetian_House_of_Anjou\" title=\"Capetian House of Anjou\">Ang<PERSON><PERSON></a> fleet sent to put down a rebellion on <a href=\"https://wikipedia.org/wiki/Malta\" title=\"Malta\">Malta</a>.", "links": [{"title": "<PERSON> of Lauria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Crown of Aragon", "link": "https://wikipedia.org/wiki/Crown_of_Aragon"}, {"title": "Battle of Malta", "link": "https://wikipedia.org/wiki/Battle_of_Malta"}, {"title": "Capetian House of Anjou", "link": "https://wikipedia.org/wiki/Capetian_House_of_Anjou"}, {"title": "Malta", "link": "https://wikipedia.org/wiki/Malta"}]}, {"year": "1497", "text": "<PERSON><PERSON> da Gama sets sail on the first direct European voyage to India.", "html": "1497 - <a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\"><PERSON><PERSON> da Gama</a> sets sail on the first direct European voyage to India.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vasco_da_Gama\" title=\"Vasco da Gama\"><PERSON><PERSON> da Gama</a> sets sail on the first direct European voyage to India.", "links": [{"title": "Vasco da Gama", "link": "https://wikipedia.org/wiki/V<PERSON>_da_Gama"}]}, {"year": "1579", "text": "Our Lady of Kazan, a holy icon of the Russian Orthodox Church, is discovered underground in the city of Kazan, Tatarstan.", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Our_Lady_of_Kazan\" title=\"Our Lady of Kazan\">Our Lady of Kazan</a>, a holy <a href=\"https://wikipedia.org/wiki/Icon\" title=\"Icon\">icon</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a>, is discovered underground in the city of <a href=\"https://wikipedia.org/wiki/Kazan\" title=\"Kazan\">Kazan</a>, <a href=\"https://wikipedia.org/wiki/Tatarstan\" title=\"Tatarstan\">Tatarstan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Our_Lady_of_Kazan\" title=\"Our Lady of Kazan\">Our Lady of Kazan</a>, a holy <a href=\"https://wikipedia.org/wiki/Icon\" title=\"Icon\">icon</a> of the <a href=\"https://wikipedia.org/wiki/Russian_Orthodox_Church\" title=\"Russian Orthodox Church\">Russian Orthodox Church</a>, is discovered underground in the city of <a href=\"https://wikipedia.org/wiki/Kazan\" title=\"Kazan\">Kazan</a>, <a href=\"https://wikipedia.org/wiki/Tatarstan\" title=\"Tatarstan\">Tatarstan</a>.", "links": [{"title": "Our Lady of Kazan", "link": "https://wikipedia.org/wiki/Our_Lady_of_Kazan"}, {"title": "Icon", "link": "https://wikipedia.org/wiki/Icon"}, {"title": "Russian Orthodox Church", "link": "https://wikipedia.org/wiki/Russian_Orthodox_Church"}, {"title": "Kazan", "link": "https://wikipedia.org/wiki/Kazan"}, {"title": "Tatarstan", "link": "https://wikipedia.org/wiki/Tatarstan"}]}, {"year": "1663", "text": "<PERSON> II of England grants <PERSON> a Royal charter to Rhode Island.", "html": "1663 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> grants <a href=\"https://wikipedia.org/wiki/<PERSON>(Baptist_minister)\" title=\"<PERSON> (Baptist minister)\"><PERSON></a> a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> to <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> grants <a href=\"https://wikipedia.org/wiki/<PERSON>(Baptist_minister)\" title=\"<PERSON> (Baptist minister)\"><PERSON></a> a <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">Royal charter</a> to <a href=\"https://wikipedia.org/wiki/Rhode_Island\" title=\"Rhode Island\">Rhode Island</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> (Baptist minister)", "link": "https://wikipedia.org/wiki/<PERSON>_(Baptist_minister)"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}, {"title": "Rhode Island", "link": "https://wikipedia.org/wiki/Rhode_Island"}]}, {"year": "1709", "text": "<PERSON> of Russia defeats <PERSON> of Sweden at the Battle of Poltava, thus effectively ending Sweden's status as a major power in Europe.", "html": "1709 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> of Russia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Poltava\" title=\"Battle of Poltava\">Battle of Poltava</a>, thus effectively ending Sweden's status as a major power in Europe.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> of Russia</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Poltava\" title=\"Battle of Poltava\">Battle of Poltava</a>, thus effectively ending Sweden's status as a major power in Europe.", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}, {"title": "Battle of Poltava", "link": "https://wikipedia.org/wiki/Battle_of_Poltava"}]}, {"year": "1716", "text": "The Battle of Dynekilen forces Sweden to abandon its invasion of Norway.", "html": "1716 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Dynekilen\" title=\"Battle of Dynekilen\">Battle of Dynekilen</a> forces Sweden to abandon its invasion of Norway.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Dynekilen\" title=\"Battle of Dynekilen\">Battle of Dynekilen</a> forces Sweden to abandon its invasion of Norway.", "links": [{"title": "Battle of Dynekilen", "link": "https://wikipedia.org/wiki/Battle_of_Dynekilen"}]}, {"year": "1730", "text": "An estimated magnitude 8.7 earthquake causes a tsunami that damages more than 1,000 km (620 mi) of Chile's coastline.", "html": "1730 - An estimated <a href=\"https://wikipedia.org/wiki/1730_Valpara%C3%ADso_earthquake\" title=\"1730 Valparaíso earthquake\">magnitude 8.7 earthquake</a> causes a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that damages more than 1,000 km (620 mi) of Chile's coastline.", "no_year_html": "An estimated <a href=\"https://wikipedia.org/wiki/1730_Valpara%C3%ADso_earthquake\" title=\"1730 Valparaíso earthquake\">magnitude 8.7 earthquake</a> causes a <a href=\"https://wikipedia.org/wiki/Tsunami\" title=\"Tsunami\">tsunami</a> that damages more than 1,000 km (620 mi) of Chile's coastline.", "links": [{"title": "1730 Valparaíso earthquake", "link": "https://wikipedia.org/wiki/1730_Valpara%C3%ADso_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tsunami"}]}, {"year": "1741", "text": "Reverend <PERSON> preaches to his congregation in Enfield, Connecticut his most famous sermon, \"Sinners in the Hands of an Angry God\"; an influence for the First Great Awakening.", "html": "1741 - Reverend <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a> preaches to his congregation in <a href=\"https://wikipedia.org/wiki/Enfield,_Connecticut\" title=\"Enfield, Connecticut\">Enfield, Connecticut</a> his most famous sermon, \"<a href=\"https://wikipedia.org/wiki/Sinners_in_the_Hands_of_an_Angry_God\" title=\"Sinners in the Hands of an Angry God\">Sinners in the Hands of an Angry God</a>\"; an influence for the <a href=\"https://wikipedia.org/wiki/First_Great_Awakening\" title=\"First Great Awakening\">First Great Awakening</a>.", "no_year_html": "Reverend <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)\" title=\"<PERSON> (theologian)\"><PERSON></a> preaches to his congregation in <a href=\"https://wikipedia.org/wiki/Enfield,_Connecticut\" title=\"Enfield, Connecticut\">Enfield, Connecticut</a> his most famous sermon, \"<a href=\"https://wikipedia.org/wiki/Sinners_in_the_Hands_of_an_Angry_God\" title=\"Sinners in the Hands of an Angry God\">Sinners in the Hands of an Angry God</a>\"; an influence for the <a href=\"https://wikipedia.org/wiki/First_Great_Awakening\" title=\"First Great Awakening\">First Great Awakening</a>.", "links": [{"title": "<PERSON> (theologian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(theologian)"}, {"title": "Enfield, Connecticut", "link": "https://wikipedia.org/wiki/Enfield,_Connecticut"}, {"title": "Sinners in the Hands of an Angry God", "link": "https://wikipedia.org/wiki/Sinners_in_the_Hands_of_an_Angry_God"}, {"title": "First Great Awakening", "link": "https://wikipedia.org/wiki/First_Great_Awakening"}]}, {"year": "1758", "text": "French forces hold Fort Carillon against the British at Ticonderoga, New York.", "html": "1758 - French forces hold <a href=\"https://wikipedia.org/wiki/Fort_Carillon\" title=\"Fort Carillon\">Fort Carillon</a> against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> at <a href=\"https://wikipedia.org/wiki/Ticonderoga,_New_York\" title=\"Ticonderoga, New York\">Ticonderoga, New York</a>.", "no_year_html": "French forces hold <a href=\"https://wikipedia.org/wiki/Fort_Carillon\" title=\"Fort Carillon\">Fort Carillon</a> against the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Great_Britain\" title=\"Kingdom of Great Britain\">British</a> at <a href=\"https://wikipedia.org/wiki/Ticonderoga,_New_York\" title=\"Ticonderoga, New York\">Ticonderoga, New York</a>.", "links": [{"title": "Fort Carillon", "link": "https://wikipedia.org/wiki/Fort_Carillon"}, {"title": "Kingdom of Great Britain", "link": "https://wikipedia.org/wiki/Kingdom_of_Great_Britain"}, {"title": "Ticonderoga, New York", "link": "https://wikipedia.org/wiki/Ticonderoga,_New_York"}]}, {"year": "1760", "text": "British forces defeat French forces in the last naval battle in New France.", "html": "1760 - British forces defeat French forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Restigouche\" title=\"Battle of Restigouche\">last naval battle</a> in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>.", "no_year_html": "British forces defeat French forces in the <a href=\"https://wikipedia.org/wiki/Battle_of_Restigouche\" title=\"Battle of Restigouche\">last naval battle</a> in <a href=\"https://wikipedia.org/wiki/New_France\" title=\"New France\">New France</a>.", "links": [{"title": "Battle of Restigouche", "link": "https://wikipedia.org/wiki/Battle_of_Restigouche"}, {"title": "New France", "link": "https://wikipedia.org/wiki/New_France"}]}, {"year": "1775", "text": "The Olive Branch Petition is signed by the Continental Congress of the Thirteen Colonies of North America.", "html": "1775 - The <a href=\"https://wikipedia.org/wiki/Olive_Branch_Petition\" title=\"Olive Branch Petition\">Olive Branch Petition</a> is signed by the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> of the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> of North America.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Olive_Branch_Petition\" title=\"Olive Branch Petition\">Olive Branch Petition</a> is signed by the <a href=\"https://wikipedia.org/wiki/Continental_Congress\" title=\"Continental Congress\">Continental Congress</a> of the <a href=\"https://wikipedia.org/wiki/Thirteen_Colonies\" title=\"Thirteen Colonies\">Thirteen Colonies</a> of North America.", "links": [{"title": "Olive Branch Petition", "link": "https://wikipedia.org/wiki/Olive_Branch_Petition"}, {"title": "Continental Congress", "link": "https://wikipedia.org/wiki/Continental_Congress"}, {"title": "Thirteen Colonies", "link": "https://wikipedia.org/wiki/Thirteen_Colonies"}]}, {"year": "1776", "text": "Church bells (possibly including the Liberty Bell) are rung after <PERSON> delivers the first public reading of the Declaration of Independence of the United States.", "html": "1776 - Church bells (possibly including the <a href=\"https://wikipedia.org/wiki/Liberty_Bell\" title=\"Liberty Bell\">Liberty Bell</a>) are rung after <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> delivers the first public reading of the <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_the_United_States\" class=\"mw-redirect\" title=\"Declaration of Independence of the United States\">Declaration of Independence of the United States</a>.", "no_year_html": "Church bells (possibly including the <a href=\"https://wikipedia.org/wiki/Liberty_Bell\" title=\"Liberty Bell\">Liberty Bell</a>) are rung after <a href=\"https://wikipedia.org/wiki/<PERSON>_(financier)\" title=\"<PERSON> (financier)\"><PERSON></a> delivers the first public reading of the <a href=\"https://wikipedia.org/wiki/Declaration_of_Independence_of_the_United_States\" class=\"mw-redirect\" title=\"Declaration of Independence of the United States\">Declaration of Independence of the United States</a>.", "links": [{"title": "Liberty Bell", "link": "https://wikipedia.org/wiki/Liberty_Bell"}, {"title": "<PERSON> (financier)", "link": "https://wikipedia.org/wiki/<PERSON>_(financier)"}, {"title": "Declaration of Independence of the United States", "link": "https://wikipedia.org/wiki/Declaration_of_Independence_of_the_United_States"}]}, {"year": "1808", "text": "Promulgation of the Bayonne Statute, a royal charter <PERSON> intended as the basis for his rule as king of Spain.", "html": "1808 - Promulgation of the <a href=\"https://wikipedia.org/wiki/Bayonne_Statute\" title=\"Bayonne Statute\">Bayonne Statute</a>, a royal charter <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> intended as the basis for his rule as king of Spain.", "no_year_html": "Promulgation of the <a href=\"https://wikipedia.org/wiki/Bayonne_Statute\" title=\"Bayonne Statute\">Bayonne Statute</a>, a royal charter <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> intended as the basis for his rule as king of Spain.", "links": [{"title": "Bayonne Statute", "link": "https://wikipedia.org/wiki/Bayonne_Statute"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1822", "text": "Chippewas turn over a huge tract of land in Ontario to the United Kingdom.", "html": "1822 - <a href=\"https://wikipedia.org/wiki/Chippewas\" class=\"mw-redirect\" title=\"Chippewas\">Chippewas</a> turn over a huge tract of land in <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a> to the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chippewas\" class=\"mw-redirect\" title=\"Chippewas\">Chippewas</a> turn over a huge tract of land in <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a> to the United Kingdom.", "links": [{"title": "Chippewas", "link": "https://wikipedia.org/wiki/Chippewas"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}]}, {"year": "1853", "text": "The Perry Expedition arrives in Edo Bay with a treaty requesting trade.", "html": "1853 - The <a href=\"https://wikipedia.org/wiki/Perry_Expedition\" title=\"Perry Expedition\">Perry Expedition</a> arrives in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> Bay with a treaty requesting trade.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Perry_Expedition\" title=\"Perry Expedition\">Perry Expedition</a> arrives in <a href=\"https://wikipedia.org/wiki/Edo_(Tokyo)\" class=\"mw-redirect\" title=\"Edo (Tokyo)\">Edo</a> Bay with a treaty requesting trade.", "links": [{"title": "Perry Expedition", "link": "https://wikipedia.org/wiki/Perry_Expedition"}, {"title": "Edo (Tokyo)", "link": "https://wikipedia.org/wiki/Edo_(Tokyo)"}]}, {"year": "1859", "text": "King <PERSON> XV & IV accedes to the throne of Sweden-Norway.", "html": "1859 - King <a href=\"https://wikipedia.org/wiki/Charles_XV_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> XV &amp; IV</a> accedes to the throne of <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">Sweden-Norway</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" class=\"mw-redirect\" title=\"Charles XV of Sweden\"><PERSON> XV &amp; IV</a> accedes to the throne of <a href=\"https://wikipedia.org/wiki/Union_between_Sweden_and_Norway\" title=\"Union between Sweden and Norway\">Sweden-Norway</a>.", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/Charles_XV_of_Sweden"}, {"title": "Union between Sweden and Norway", "link": "https://wikipedia.org/wiki/Union_between_Sweden_and_Norway"}]}, {"year": "1864", "text": "Ikedaya Incident: The Choshu Han shishi's planned Shinsengumi sabotage on Kyoto, Japan at Ikedaya.", "html": "1864 - <a href=\"https://wikipedia.org/wiki/Ikedaya_Incident\" class=\"mw-redirect\" title=\"Ikedaya Incident\">Ikedaya Incident</a>: The <a href=\"https://wikipedia.org/wiki/Ch%C5%8Dsh%C5%AB_Domain\" title=\"Chōshū Domain\">Cho<PERSON> Han</a> <a href=\"https://wikipedia.org/wiki/Shishi_(organization)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (organization)\">shishi</a>'s planned <a href=\"https://wikipedia.org/wiki/Shinsengumi\" title=\"Shinsengumi\">Shinsengumi</a> sabotage on <a href=\"https://wikipedia.org/wiki/Kyoto\" title=\"Kyoto\">Kyoto</a>, Japan at Ikedaya.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ikedaya_Incident\" class=\"mw-redirect\" title=\"Ikedaya Incident\">Ikedaya Incident</a>: The <a href=\"https://wikipedia.org/wiki/Ch%C5%8Dsh%C5%AB_Domain\" title=\"Chōshū Domain\">Choshu Han</a> <a href=\"https://wikipedia.org/wiki/Shishi_(organization)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (organization)\">shishi</a>'s planned <a href=\"https://wikipedia.org/wiki/Shinsengumi\" title=\"Shinsengumi\">Shinsengumi</a> sabotage on <a href=\"https://wikipedia.org/wiki/Kyoto\" title=\"Kyoto\">Kyoto</a>, Japan at Ikedaya.", "links": [{"title": "Ikedaya Incident", "link": "https://wikipedia.org/wiki/Ikedaya_Incident"}, {"title": "Chōshū Domain", "link": "https://wikipedia.org/wiki/Ch%C5%8Dsh%C5%AB_Domain"}, {"title": "<PERSON><PERSON> (organization)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(organization)"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>sengumi"}, {"title": "Kyoto", "link": "https://wikipedia.org/wiki/Kyoto"}]}, {"year": "1874", "text": "The Mounties begin their March West.", "html": "1874 - The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Mounties</a> begin their <a href=\"https://wikipedia.org/wiki/March_West\" title=\"March West\">March West</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police\" title=\"Royal Canadian Mounted Police\">Mounties</a> begin their <a href=\"https://wikipedia.org/wiki/March_West\" title=\"March West\">March West</a>.", "links": [{"title": "Royal Canadian Mounted Police", "link": "https://wikipedia.org/wiki/Royal_Canadian_Mounted_Police"}, {"title": "March West", "link": "https://wikipedia.org/wiki/March_West"}]}, {"year": "1876", "text": "The Hamburg massacre prior to the 1876 United States presidential election results in the deaths of six African-Americans of the Republican Party, along with one white assailant.", "html": "1876 - The <a href=\"https://wikipedia.org/wiki/Hamburg_massacre\" title=\"Hamburg massacre\">Hamburg massacre</a> prior to the <a href=\"https://wikipedia.org/wiki/1876_United_States_presidential_election\" title=\"1876 United States presidential election\">1876 United States presidential election</a> results in the deaths of six African-Americans of the Republican Party, along with one white assailant.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hamburg_massacre\" title=\"Hamburg massacre\">Hamburg massacre</a> prior to the <a href=\"https://wikipedia.org/wiki/1876_United_States_presidential_election\" title=\"1876 United States presidential election\">1876 United States presidential election</a> results in the deaths of six African-Americans of the Republican Party, along with one white assailant.", "links": [{"title": "Hamburg massacre", "link": "https://wikipedia.org/wiki/Hamburg_massacre"}, {"title": "1876 United States presidential election", "link": "https://wikipedia.org/wiki/1876_United_States_presidential_election"}]}, {"year": "1879", "text": "Sailing ship USS Jeannette departs San Francisco carrying an ill-fated expedition to the North Pole.", "html": "1879 - Sailing ship <a href=\"https://wikipedia.org/wiki/USS_Jeannette_(1878)\" title=\"USS Jeannette (1878)\">USS <i><PERSON><PERSON></i></a> departs San Francisco carrying an ill-fated expedition to the North Pole.", "no_year_html": "Sailing ship <a href=\"https://wikipedia.org/wiki/USS_Jeannette_(1878)\" title=\"USS Jeannette (1878)\">USS <i><PERSON><PERSON></i></a> departs San Francisco carrying an ill-fated expedition to the North Pole.", "links": [{"title": "USS Jeannette (1878)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(1878)"}]}, {"year": "1889", "text": "The first issue of The Wall Street Journal is published.", "html": "1889 - The first issue of <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i> is published.", "no_year_html": "The first issue of <i><a href=\"https://wikipedia.org/wiki/The_Wall_Street_Journal\" title=\"The Wall Street Journal\">The Wall Street Journal</a></i> is published.", "links": [{"title": "The Wall Street Journal", "link": "https://wikipedia.org/wiki/The_Wall_Street_Journal"}]}, {"year": "1892", "text": "St. John's, Newfoundland is devastated in the Great Fire of 1892.", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, Newfoundland</a> is devastated in the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_1892\" title=\"Great Fire of 1892\">Great Fire of 1892</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/St._John%27s,_Newfoundland_and_Labrador\" title=\"St. John's, Newfoundland and Labrador\">St. John's, Newfoundland</a> is devastated in the <a href=\"https://wikipedia.org/wiki/Great_Fire_of_1892\" title=\"Great Fire of 1892\">Great Fire of 1892</a>.", "links": [{"title": "St. John's, Newfoundland and Labrador", "link": "https://wikipedia.org/wiki/St._<PERSON>%27s,_Newfoundland_and_Labrador"}, {"title": "Great Fire of 1892", "link": "https://wikipedia.org/wiki/Great_Fire_of_1892"}]}, {"year": "1898", "text": "The death of crime boss <PERSON><PERSON>, killed in the Shootout on Juneau Wharf, releases Skagway, Alaska from his iron grip.", "html": "1898 - The death of crime boss <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, killed in the <a href=\"https://wikipedia.org/wiki/Shootout_on_Juneau_Wharf\" title=\"Shootout on Juneau Wharf\">Shootout on Juneau Wharf</a>, releases <a href=\"https://wikipedia.org/wiki/Skagway,_Alaska\" title=\"Skagway, Alaska\">Skagway, Alaska</a> from his iron grip.", "no_year_html": "The death of crime boss <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, killed in the <a href=\"https://wikipedia.org/wiki/Shootout_on_Juneau_Wharf\" title=\"Shootout on Juneau Wharf\">Shootout on Juneau Wharf</a>, releases <a href=\"https://wikipedia.org/wiki/Skagway,_Alaska\" title=\"Skagway, Alaska\">Skagway, Alaska</a> from his iron grip.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Shootout on Juneau Wharf", "link": "https://wikipedia.org/wiki/Shootout_on_Juneau_Wharf"}, {"title": "Skagway, Alaska", "link": "https://wikipedia.org/wiki/Skagway,_Alaska"}]}, {"year": "1912", "text": "<PERSON><PERSON>uceiro leads an unsuccessful royalist attack against the First Portuguese Republic in Chaves.", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>iva_Couceiro\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Paiva Couceiro\"><PERSON><PERSON>iva Couceiro</a> leads <a href=\"https://wikipedia.org/wiki/Royalist_attack_on_Chaves\" title=\"Royalist attack on Chaves\">an unsuccessful royalist attack</a> against the <a href=\"https://wikipedia.org/wiki/First_Portuguese_Republic\" title=\"First Portuguese Republic\">First Portuguese Republic</a> in <a href=\"https://wikipedia.org/wiki/Chaves_Municipality,_Portugal\" class=\"mw-redirect\" title=\"Chaves Municipality, Portugal\">Chaves</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>iva_Couceiro\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Paiva Couceiro\"><PERSON><PERSON>iva Couceiro</a> leads <a href=\"https://wikipedia.org/wiki/Royalist_attack_on_Chaves\" title=\"Royalist attack on Chaves\">an unsuccessful royalist attack</a> against the <a href=\"https://wikipedia.org/wiki/First_Portuguese_Republic\" title=\"First Portuguese Republic\">First Portuguese Republic</a> in <a href=\"https://wikipedia.org/wiki/Chaves_Municipality,_Portugal\" class=\"mw-redirect\" title=\"Chaves Municipality, Portugal\">Chaves</a>.", "links": [{"title": "<PERSON><PERSON> Paiva Couceiro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Royalist attack on <PERSON>ves", "link": "https://wikipedia.org/wiki/Royalist_attack_on_Chaves"}, {"title": "First Portuguese Republic", "link": "https://wikipedia.org/wiki/First_Portuguese_Republic"}, {"title": "Chaves Municipality, Portugal", "link": "https://wikipedia.org/wiki/Chaves_Municipality,_Portugal"}]}, {"year": "1932", "text": "The Dow Jones Industrial Average reaches its lowest level of the Great Depression, closing at 41.22.", "html": "1932 - The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> reaches its lowest level of the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>, closing at 41.22.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dow_Jones_Industrial_Average\" title=\"Dow Jones Industrial Average\">Dow Jones Industrial Average</a> reaches its lowest level of the <a href=\"https://wikipedia.org/wiki/Great_Depression\" title=\"Great Depression\">Great Depression</a>, closing at 41.22.", "links": [{"title": "Dow Jones Industrial Average", "link": "https://wikipedia.org/wiki/<PERSON>_Jones_Industrial_Average"}, {"title": "Great Depression", "link": "https://wikipedia.org/wiki/Great_Depression"}]}, {"year": "1933", "text": "The first rugby union test match between the Wallabies of Australia and the Springboks of South Africa is played at Newlands Stadium in Cape Town.", "html": "1933 - The first <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> <a href=\"https://wikipedia.org/wiki/Test_match_(rugby_union)\" title=\"Test match (rugby union)\">test match</a> between the <a href=\"https://wikipedia.org/wiki/Australia_national_rugby_union_team\" title=\"Australia national rugby union team\">Wallabies of Australia</a> and the <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">Springboks of South Africa</a> is played at <a href=\"https://wikipedia.org/wiki/Newlands_Stadium\" title=\"Newlands Stadium\">Newlands Stadium</a> in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Rugby_union\" title=\"Rugby union\">rugby union</a> <a href=\"https://wikipedia.org/wiki/Test_match_(rugby_union)\" title=\"Test match (rugby union)\">test match</a> between the <a href=\"https://wikipedia.org/wiki/Australia_national_rugby_union_team\" title=\"Australia national rugby union team\">Wallabies of Australia</a> and the <a href=\"https://wikipedia.org/wiki/South_Africa_national_rugby_union_team\" title=\"South Africa national rugby union team\">Springboks of South Africa</a> is played at <a href=\"https://wikipedia.org/wiki/Newlands_Stadium\" title=\"Newlands Stadium\">Newlands Stadium</a> in <a href=\"https://wikipedia.org/wiki/Cape_Town\" title=\"Cape Town\">Cape Town</a>.", "links": [{"title": "Rugby union", "link": "https://wikipedia.org/wiki/Rugby_union"}, {"title": "Test match (rugby union)", "link": "https://wikipedia.org/wiki/Test_match_(rugby_union)"}, {"title": "Australia national rugby union team", "link": "https://wikipedia.org/wiki/Australia_national_rugby_union_team"}, {"title": "South Africa national rugby union team", "link": "https://wikipedia.org/wiki/South_Africa_national_rugby_union_team"}, {"title": "Newlands Stadium", "link": "https://wikipedia.org/wiki/Newlands_Stadium"}, {"title": "Cape Town", "link": "https://wikipedia.org/wiki/Cape_Town"}]}, {"year": "1937", "text": "Turkey, Iran, Iraq, and Afghanistan sign the Treaty of Saadabad.", "html": "1937 - Turkey, Iran, Iraq, and Afghanistan sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saadabad\" title=\"Treaty of Saadabad\">Treaty of Saadabad</a>.", "no_year_html": "Turkey, Iran, Iraq, and Afghanistan sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_Saadabad\" title=\"Treaty of Saadabad\">Treaty of Saadabad</a>.", "links": [{"title": "Treaty of Saadabad", "link": "https://wikipedia.org/wiki/Treaty_of_Saadabad"}]}, {"year": "1947", "text": "Reports are broadcast that a UFO crash-landed in Roswell, New Mexico in what became known as the Roswell UFO incident.", "html": "1947 - Reports are broadcast that a <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFO</a> crash-landed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_New_Mexico\" title=\"Roswell, New Mexico\">Roswell, New Mexico</a> in what became known as the <a href=\"https://wikipedia.org/wiki/Roswell_UFO_incident\" class=\"mw-redirect\" title=\"Roswell UFO incident\">Roswell UFO incident</a>.", "no_year_html": "Reports are broadcast that a <a href=\"https://wikipedia.org/wiki/Unidentified_flying_object\" title=\"Unidentified flying object\">UFO</a> crash-landed in <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_New_Mexico\" title=\"Roswell, New Mexico\">Roswell, New Mexico</a> in what became known as the <a href=\"https://wikipedia.org/wiki/Roswell_UFO_incident\" class=\"mw-redirect\" title=\"Roswell UFO incident\">Roswell UFO incident</a>.", "links": [{"title": "Unidentified flying object", "link": "https://wikipedia.org/wiki/Unidentified_flying_object"}, {"title": "Roswell, New Mexico", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_New_Mexico"}, {"title": "Roswell UFO incident", "link": "https://wikipedia.org/wiki/Roswell_UFO_incident"}]}, {"year": "1948", "text": "The United States Air Force accepts its first female recruits into a program called the Women's Air Force (WAF).", "html": "1948 - The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> accepts its first female recruits into a program called the <a href=\"https://wikipedia.org/wiki/Women%27s_Air_Force\" title=\"Women's Air Force\">Women's Air Force</a> (WAF).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> accepts its first female recruits into a program called the <a href=\"https://wikipedia.org/wiki/Women%27s_Air_Force\" title=\"Women's Air Force\">Women's Air Force</a> (WAF).", "links": [{"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "Women's Air Force", "link": "https://wikipedia.org/wiki/Women%27s_Air_Force"}]}, {"year": "1960", "text": "<PERSON> is charged with espionage resulting from his flight over the Soviet Union.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> resulting from his flight over the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is charged with <a href=\"https://wikipedia.org/wiki/Espionage\" title=\"Espionage\">espionage</a> resulting from his flight over the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Espionage", "link": "https://wikipedia.org/wiki/Espionage"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1962", "text": "Ne Win besieges and blows up the Rangoon University Student Union building to crush the Student Movement.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/Ne_Win\" title=\"Ne Win\">Ne Win</a> <a href=\"https://wikipedia.org/wiki/1962_Rangoon_University_protests\" title=\"1962 Rangoon University protests\">besieges and blows up</a> the <a href=\"https://wikipedia.org/wiki/University_of_Yangon\" title=\"University of Yangon\">Rangoon University</a> Student Union building to crush the <a href=\"https://wikipedia.org/wiki/Student_activism\" title=\"Student activism\">Student Movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ne_Win\" title=\"Ne Win\">Ne Win</a> <a href=\"https://wikipedia.org/wiki/1962_Rangoon_University_protests\" title=\"1962 Rangoon University protests\">besieges and blows up</a> the <a href=\"https://wikipedia.org/wiki/University_of_Yangon\" title=\"University of Yangon\">Rangoon University</a> Student Union building to crush the <a href=\"https://wikipedia.org/wiki/Student_activism\" title=\"Student activism\">Student Movement</a>.", "links": [{"title": "<PERSON>e Win", "link": "https://wikipedia.org/wiki/Ne_Win"}, {"title": "1962 Rangoon University protests", "link": "https://wikipedia.org/wiki/1962_Rangoon_University_protests"}, {"title": "University of Yangon", "link": "https://wikipedia.org/wiki/University_of_Yangon"}, {"title": "Student activism", "link": "https://wikipedia.org/wiki/Student_activism"}]}, {"year": "1965", "text": "Canadian Pacific Air Lines Flight 21 is destroyed by a bomb near 100 Mile House, Canada, killing 52.", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_21\" title=\"Canadian Pacific Air Lines Flight 21\">Canadian Pacific Air Lines Flight 21</a> is destroyed by a bomb near <a href=\"https://wikipedia.org/wiki/100_Mile_House\" title=\"100 Mile House\">100 Mile House</a>, Canada, killing 52.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_21\" title=\"Canadian Pacific Air Lines Flight 21\">Canadian Pacific Air Lines Flight 21</a> is destroyed by a bomb near <a href=\"https://wikipedia.org/wiki/100_Mile_House\" title=\"100 Mile House\">100 Mile House</a>, Canada, killing 52.", "links": [{"title": "Canadian Pacific Air Lines Flight 21", "link": "https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_21"}, {"title": "100 Mile House", "link": "https://wikipedia.org/wiki/100_Mile_House"}]}, {"year": "1966", "text": "King <PERSON><PERSON><PERSON><PERSON><PERSON> IV <PERSON> of Burundi is deposed by his son Prince <PERSON>.", "html": "1966 - <a href=\"https://wikipedia.org/wiki/List_of_Kings_of_Burundi\" class=\"mw-redirect\" title=\"List of Kings of Burundi\">King</a> <a href=\"https://wikipedia.org/wiki/Mwambutsa_IV_Bangiriceng_of_Burundi\" class=\"mw-redirect\" title=\"Mwambutsa IV Bangiriceng of Burundi\">Mwambutsa IV Bangiriceng of Burundi</a> is deposed by his son <PERSON> <a href=\"https://wikipedia.org/wiki/Ntare_V_of_Burundi\" title=\"Ntare V of Burundi\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/List_of_Kings_of_Burundi\" class=\"mw-redirect\" title=\"List of Kings of Burundi\">King</a> <a href=\"https://wikipedia.org/wiki/Mwambutsa_IV_Bangiriceng_of_Burundi\" class=\"mw-redirect\" title=\"Mwambutsa IV Bangiriceng of Burundi\">Mwambutsa IV Bangiriceng of Burundi</a> is deposed by his son <PERSON> <a href=\"https://wikipedia.org/wiki/Ntare_V_of_Burundi\" title=\"Ntare V of Burundi\"><PERSON></a>.", "links": [{"title": "List of Kings of Burundi", "link": "https://wikipedia.org/wiki/List_of_Kings_of_Burundi"}, {"title": "Mwambutsa IV Bangiriceng of Burundi", "link": "https://wikipedia.org/wiki/Mwambutsa_IV_Bangiriceng_of_Burundi"}, {"title": "Ntare V of Burundi", "link": "https://wikipedia.org/wiki/Ntare_V_of_Burundi"}]}, {"year": "1968", "text": "The Chrysler wildcat strike begins in Detroit, Michigan.", "html": "1968 - The <a href=\"https://wikipedia.org/wiki/Dodge_Revolutionary_Union_Movement\" title=\"Dodge Revolutionary Union Movement\">Chrysler wildcat strike</a> begins in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit, Michigan</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dodge_Revolutionary_Union_Movement\" title=\"Dodge Revolutionary Union Movement\">Chrysler wildcat strike</a> begins in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit, Michigan</a>.", "links": [{"title": "Dodge Revolutionary Union Movement", "link": "https://wikipedia.org/wiki/Dodge_Revolutionary_Union_Movement"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}]}, {"year": "1970", "text": "<PERSON> delivers a special congressional message enunciating Native American self-determination as official US Indian policy, leading to the Indian Self-Determination and Education Assistance Act of 1975.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a special congressional message enunciating <a href=\"https://wikipedia.org/wiki/Native_American_self-determination\" title=\"Native American self-determination\">Native American self-determination</a> as official US Indian policy, leading to the <a href=\"https://wikipedia.org/wiki/Indian_Self-Determination_and_Education_Assistance_Act_of_1975\" title=\"Indian Self-Determination and Education Assistance Act of 1975\">Indian Self-Determination and Education Assistance Act of 1975</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> delivers a special congressional message enunciating <a href=\"https://wikipedia.org/wiki/Native_American_self-determination\" title=\"Native American self-determination\">Native American self-determination</a> as official US Indian policy, leading to the <a href=\"https://wikipedia.org/wiki/Indian_Self-Determination_and_Education_Assistance_Act_of_1975\" title=\"Indian Self-Determination and Education Assistance Act of 1975\">Indian Self-Determination and Education Assistance Act of 1975</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Native American self-determination", "link": "https://wikipedia.org/wiki/Native_American_self-determination"}, {"title": "Indian Self-Determination and Education Assistance Act of 1975", "link": "https://wikipedia.org/wiki/Indian_Self-Determination_and_Education_Assistance_Act_of_1975"}]}, {"year": "1972", "text": "Israeli Mossad assassinate Palestinian writer <PERSON><PERSON><PERSON>.", "html": "1972 - Israeli <a href=\"https://wikipedia.org/wiki/Mossad\" title=\"Mossad\">Mossad</a> assassinate Palestinian writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "no_year_html": "Israeli <a href=\"https://wikipedia.org/wiki/Mossad\" title=\"Mossad\">Mossad</a> assassinate Palestinian writer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>.", "links": [{"title": "Mossad", "link": "https://wikipedia.org/wiki/Mossad"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1980", "text": "The inaugural 1980 State of Origin game is won by Queensland who defeat New South Wales 20-10 at Lang Park.", "html": "1980 - The inaugural <a href=\"https://wikipedia.org/wiki/1980_State_of_Origin_game\" title=\"1980 State of Origin game\">1980 State of Origin game</a> is won by <a href=\"https://wikipedia.org/wiki/Queensland_Rugby_League_team\" class=\"mw-redirect\" title=\"Queensland Rugby League team\">Queensland</a> who defeat <a href=\"https://wikipedia.org/wiki/New_South_Wales_Rugby_League_team\" class=\"mw-redirect\" title=\"New South Wales Rugby League team\">New South Wales</a> 20-10 at <a href=\"https://wikipedia.org/wiki/Lang_Park\" title=\"Lang Park\">Lang Park</a>.", "no_year_html": "The inaugural <a href=\"https://wikipedia.org/wiki/1980_State_of_Origin_game\" title=\"1980 State of Origin game\">1980 State of Origin game</a> is won by <a href=\"https://wikipedia.org/wiki/Queensland_Rugby_League_team\" class=\"mw-redirect\" title=\"Queensland Rugby League team\">Queensland</a> who defeat <a href=\"https://wikipedia.org/wiki/New_South_Wales_Rugby_League_team\" class=\"mw-redirect\" title=\"New South Wales Rugby League team\">New South Wales</a> 20-10 at <a href=\"https://wikipedia.org/wiki/Lang_Park\" title=\"Lang Park\">Lang Park</a>.", "links": [{"title": "1980 State of Origin game", "link": "https://wikipedia.org/wiki/1980_State_of_Origin_game"}, {"title": "Queensland Rugby League team", "link": "https://wikipedia.org/wiki/Queensland_Rugby_League_team"}, {"title": "New South Wales Rugby League team", "link": "https://wikipedia.org/wiki/New_South_Wales_Rugby_League_team"}, {"title": "Lang Park", "link": "https://wikipedia.org/wiki/Lang_Park"}]}, {"year": "1980", "text": "Aeroflot Flight 4225 crashes near Almaty International Airport in the then Kazakh Soviet Socialist Republic (present day Kazakhstan) killing all 166 people on board.", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_4225\" title=\"Aeroflot Flight 4225\">Aeroflot Flight 4225</a> crashes near <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Almaty International Airport</a> in the then <a href=\"https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic\" title=\"Kazakh Soviet Socialist Republic\">Kazakh Soviet Socialist Republic</a> (present day <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>) killing all 166 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aeroflot_Flight_4225\" title=\"Aeroflot Flight 4225\">Aeroflot Flight 4225</a> crashes near <a href=\"https://wikipedia.org/wiki/Almaty_International_Airport\" title=\"Almaty International Airport\">Almaty International Airport</a> in the then <a href=\"https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic\" title=\"Kazakh Soviet Socialist Republic\">Kazakh Soviet Socialist Republic</a> (present day <a href=\"https://wikipedia.org/wiki/Kazakhstan\" title=\"Kazakhstan\">Kazakhstan</a>) killing all 166 people on board.", "links": [{"title": "Aeroflot Flight 4225", "link": "https://wikipedia.org/wiki/Aeroflot_Flight_4225"}, {"title": "Almaty International Airport", "link": "https://wikipedia.org/wiki/Almaty_International_Airport"}, {"title": "Kazakh Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Kazakh_Soviet_Socialist_Republic"}, {"title": "Kazakhstan", "link": "https://wikipedia.org/wiki/Kazakhstan"}]}, {"year": "1982", "text": "A failed assassination attempt against Iraqi president <PERSON> results in the Dujail Massacre over the next several months.", "html": "1982 - A failed assassination attempt against Iraqi president <PERSON> results in the <a href=\"https://wikipedia.org/wiki/Dujail_Massacre\" class=\"mw-redirect\" title=\"Dujail Massacre\">Dujail Massacre</a> over the next several months.", "no_year_html": "A failed assassination attempt against Iraqi president <PERSON> results in the <a href=\"https://wikipedia.org/wiki/Dujail_Massacre\" class=\"mw-redirect\" title=\"Dujail Massacre\">Dujail Massacre</a> over the next several months.", "links": [{"title": "Dujail Massacre", "link": "https://wikipedia.org/wiki/Duja<PERSON>_Massacre"}]}, {"year": "1988", "text": "The Island Express train travelling from Bangalore to Kanyakumari derails on the Peruman bridge and falls into Ashtamudi Lake, Kerala in India killing 105 passengers and injuring over 200 more.", "html": "1988 - The <a href=\"https://wikipedia.org/wiki/Island_Express_(train)\" title=\"Island Express (train)\">Island Express</a> train travelling from <a href=\"https://wikipedia.org/wiki/Bangalore\" class=\"mw-redirect\" title=\"Bangalore\">Bangalore</a> to <a href=\"https://wikipedia.org/wiki/Kanyakumari\" title=\"Kanyakumari\">Kanyakumari</a> <a href=\"https://wikipedia.org/wiki/Peruman_railway_accident\" title=\"Peruman railway accident\">derails on the Peruman bridge</a> and falls into <a href=\"https://wikipedia.org/wiki/Ashtamudi_Lake\" title=\"Ashtamudi Lake\">Ashtamudi Lake</a>, <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a> in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> killing 105 passengers and injuring over 200 more.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Island_Express_(train)\" title=\"Island Express (train)\">Island Express</a> train travelling from <a href=\"https://wikipedia.org/wiki/Bangalore\" class=\"mw-redirect\" title=\"Bangalore\">Bangalore</a> to <a href=\"https://wikipedia.org/wiki/Kanyakumari\" title=\"Kanyakumari\">Kanyakumari</a> <a href=\"https://wikipedia.org/wiki/Peruman_railway_accident\" title=\"Peruman railway accident\">derails on the Peruman bridge</a> and falls into <a href=\"https://wikipedia.org/wiki/Ashtamudi_Lake\" title=\"Ashtamudi Lake\">Ashtamudi Lake</a>, <a href=\"https://wikipedia.org/wiki/Kerala\" title=\"Kerala\">Kerala</a> in <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> killing 105 passengers and injuring over 200 more.", "links": [{"title": "Island Express (train)", "link": "https://wikipedia.org/wiki/Island_Express_(train)"}, {"title": "Bangalore", "link": "https://wikipedia.org/wiki/Bangalore"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kanyakumari"}, {"title": "Peruman railway accident", "link": "https://wikipedia.org/wiki/Peruman_railway_accident"}, {"title": "Ashtamudi Lake", "link": "https://wikipedia.org/wiki/Ashtamudi_Lake"}, {"title": "Kerala", "link": "https://wikipedia.org/wiki/Kerala"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "1994", "text": "<PERSON> begins to assume supreme leadership of North Korea upon the death of his father, <PERSON>.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins to assume <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">supreme leadership</a> of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> upon the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON>\">death</a> of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> begins to assume <a href=\"https://wikipedia.org/wiki/List_of_leaders_of_North_Korea\" class=\"mw-redirect\" title=\"List of leaders of North Korea\">supreme leadership</a> of <a href=\"https://wikipedia.org/wiki/North_Korea\" title=\"North Korea\">North Korea</a> upon the <a href=\"https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>\" title=\"Death and state funeral of <PERSON>\">death</a> of his father, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "List of leaders of North Korea", "link": "https://wikipedia.org/wiki/List_of_leaders_of_North_Korea"}, {"title": "North Korea", "link": "https://wikipedia.org/wiki/North_Korea"}, {"title": "Death and state funeral of <PERSON>", "link": "https://wikipedia.org/wiki/Death_and_state_funeral_of_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "Space Shuttle Columbia is launched on an international science mission.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-65\" title=\"STS-65\">an international science mission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Columbia\" title=\"Space Shuttle Columbia\">Space Shuttle <i>Columbia</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-65\" title=\"STS-65\">an international science mission</a>.", "links": [{"title": "Space Shuttle Columbia", "link": "https://wikipedia.org/wiki/Space_Shuttle_Columbia"}, {"title": "STS-65", "link": "https://wikipedia.org/wiki/STS-65"}]}, {"year": "2003", "text": "Sudan Airways Flight 139 crashes near Port Sudan Airport during an emergency landing attempt, killing 116 of the 117 people on board.", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Sudan_Airways_Flight_139\" title=\"Sudan Airways Flight 139\">Sudan Airways Flight 139</a> crashes near <a href=\"https://wikipedia.org/wiki/Port_Sudan_New_International_Airport\" title=\"Port Sudan New International Airport\">Port Sudan Airport</a> during an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> attempt, killing 116 of the 117 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sudan_Airways_Flight_139\" title=\"Sudan Airways Flight 139\">Sudan Airways Flight 139</a> crashes near <a href=\"https://wikipedia.org/wiki/Port_Sudan_New_International_Airport\" title=\"Port Sudan New International Airport\">Port Sudan Airport</a> during an <a href=\"https://wikipedia.org/wiki/Emergency_landing\" title=\"Emergency landing\">emergency landing</a> attempt, killing 116 of the 117 people on board.", "links": [{"title": "Sudan Airways Flight 139", "link": "https://wikipedia.org/wiki/Sudan_Airways_Flight_139"}, {"title": "Port Sudan New International Airport", "link": "https://wikipedia.org/wiki/Port_Sudan_New_International_Airport"}, {"title": "Emergency landing", "link": "https://wikipedia.org/wiki/Emergency_landing"}]}, {"year": "2011", "text": "Space Shuttle Atlantis is launched in the final mission of the U.S. Space Shuttle program.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched in the <a href=\"https://wikipedia.org/wiki/STS-135\" title=\"STS-135\">final mission</a> of the U.S. <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> program.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched in the <a href=\"https://wikipedia.org/wiki/STS-135\" title=\"STS-135\">final mission</a> of the U.S. <a href=\"https://wikipedia.org/wiki/Space_Shuttle\" title=\"Space Shuttle\">Space Shuttle</a> program.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-135", "link": "https://wikipedia.org/wiki/STS-135"}, {"title": "Space Shuttle", "link": "https://wikipedia.org/wiki/Space_Shuttle"}]}, {"year": "2014", "text": "Israel launches an offensive on Gaza amid rising tensions following the kidnapping and murder of three Israeli teenagers.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches an <a href=\"https://wikipedia.org/wiki/2014_Gaza_War\" title=\"2014 Gaza War\">offensive</a> on Gaza amid rising tensions following the <a href=\"https://wikipedia.org/wiki/2014_kidnapping_and_murder_of_Israeli_teenagers\" class=\"mw-redirect\" title=\"2014 kidnapping and murder of Israeli teenagers\">kidnapping and murder of three Israeli teenagers</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israel\" title=\"Israel\">Israel</a> launches an <a href=\"https://wikipedia.org/wiki/2014_Gaza_War\" title=\"2014 Gaza War\">offensive</a> on Gaza amid rising tensions following the <a href=\"https://wikipedia.org/wiki/2014_kidnapping_and_murder_of_Israeli_teenagers\" class=\"mw-redirect\" title=\"2014 kidnapping and murder of Israeli teenagers\">kidnapping and murder of three Israeli teenagers</a>.", "links": [{"title": "Israel", "link": "https://wikipedia.org/wiki/Israel"}, {"title": "2014 Gaza War", "link": "https://wikipedia.org/wiki/2014_Gaza_War"}, {"title": "2014 kidnapping and murder of Israeli teenagers", "link": "https://wikipedia.org/wiki/2014_kidnapping_and_murder_of_Israeli_teenagers"}]}, {"year": "2014", "text": "The worst historical defeat of Brazil against the Germany with a result of 1-7 in the semi-finals of the 2014 FIFA World Cup that has been dubbed the Mineirazo.", "html": "2014 - The worst historical defeat of <a href=\"https://wikipedia.org/wiki/Brazil_national_football_team\" title=\"Brazil national football team\">Brazil</a> against the <a href=\"https://wikipedia.org/wiki/Germany_national_football_team\" title=\"Germany national football team\">Germany</a> with a result of 1-7 in the semi-finals of the <a href=\"https://wikipedia.org/wiki/2014_FIFA_World_Cup\" title=\"2014 FIFA World Cup\">2014 FIFA World Cup</a> that has been dubbed the <a href=\"https://wikipedia.org/wiki/Mineirazo\" class=\"mw-redirect\" title=\"Mineirazo\"><PERSON>irazo</a>.", "no_year_html": "The worst historical defeat of <a href=\"https://wikipedia.org/wiki/Brazil_national_football_team\" title=\"Brazil national football team\">Brazil</a> against the <a href=\"https://wikipedia.org/wiki/Germany_national_football_team\" title=\"Germany national football team\">Germany</a> with a result of 1-7 in the semi-finals of the <a href=\"https://wikipedia.org/wiki/2014_FIFA_World_Cup\" title=\"2014 FIFA World Cup\">2014 FIFA World Cup</a> that has been dubbed the <a href=\"https://wikipedia.org/wiki/Mineirazo\" class=\"mw-redirect\" title=\"Mineirazo\"><PERSON>irazo</a>.", "links": [{"title": "Brazil national football team", "link": "https://wikipedia.org/wiki/Brazil_national_football_team"}, {"title": "Germany national football team", "link": "https://wikipedia.org/wiki/Germany_national_football_team"}, {"title": "2014 FIFA World Cup", "link": "https://wikipedia.org/wiki/2014_FIFA_World_Cup"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}]}], "Births": [{"year": "1478", "text": "<PERSON><PERSON>, Italian linguist, poet, and playwright (d. 1550)", "html": "1478 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian linguist, poet, and playwright (d. 1550)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, Italian linguist, poet, and playwright (d. 1550)", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1528", "text": "<PERSON>, Duke of Savoy (d. 1580)", "html": "1528 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (d. 1580)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Savoy\" title=\"<PERSON>, Duke of Savoy\"><PERSON>, Duke of Savoy</a> (d. 1580)", "links": [{"title": "<PERSON>, Duke of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>,_Duke_of_Savoy"}]}, {"year": "1538", "text": "<PERSON>, Roman Catholic cardinal (d. 1585)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal (d. 1585)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman Catholic cardinal (d. 1585)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1545", "text": "<PERSON>, Prince of Asturias (d. 1568)", "html": "1545 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a> (d. 1568)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias\" title=\"<PERSON>, Prince of Asturias\"><PERSON>, Prince of Asturias</a> (d. 1568)", "links": [{"title": "<PERSON>, Prince of Asturias", "link": "https://wikipedia.org/wiki/<PERSON>,_Prince_of_Asturias"}]}, {"year": "1593", "text": "<PERSON><PERSON>, Italian painter (d. 1653)", "html": "1593 - <a href=\"https://wikipedia.org/wiki/Artemisia_Gentileschi\" title=\"<PERSON>ia <PERSON>tileschi\"><PERSON><PERSON></a>, Italian painter (d. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artemisia_Gentileschi\" title=\"Artemisia <PERSON>tileschi\"><PERSON><PERSON></a>, Italian painter (d. 1653)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artemisia_Gentileschi"}]}, {"year": "1621", "text": "<PERSON>, French author and poet (d. 1695)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1695)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French author and poet (d. 1695)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON>, French mathematician and academic (d. 1826)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French mathematician and academic (d. 1826)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian_<PERSON>\" title=\"Christian <PERSON>\"><PERSON></a>, French mathematician and academic (d. 1826)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "1766", "text": "<PERSON>, French surgeon (d. 1842)", "html": "1766 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon (d. 1842)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon (d. 1842)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1779", "text": "<PERSON>, Maltese painter and architect (d. 1851)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter and architect (d. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Maltese painter and architect (d. 1851)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Irish admiral and explorer (d. 1907)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish admiral and explorer (d. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish admiral and explorer (d. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1830", "text": "<PERSON>, American lawyer and politician, 6th United States Assistant Secretary of State (d. 1915)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 6th <a href=\"https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State\" title=\"United States Assistant Secretary of State\">United States Assistant Secretary of State</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Assistant Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Assistant_Secretary_of_State"}]}, {"year": "1831", "text": "<PERSON>, American chemist and pharmacist, invented Coca-Cola (d. 1888)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and pharmacist, invented <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a> (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American chemist and pharmacist, invented <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a> (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coca-Cola", "link": "https://wikipedia.org/wiki/Coca-Cola"}]}, {"year": "1836", "text": "<PERSON>, English businessman and politician, Secretary of State for the Colonies (d. 1914)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1838", "text": "<PERSON>, American soldier, chemist, and businessman, founded Eli Lilly and Company (d. 1898)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, chemist, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Lilly_and_Company\" title=\"Eli Lilly and Company\">Eli Lilly and Company</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lilly\"><PERSON></a>, American soldier, chemist, and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_Lilly_and_Company\" title=\"Eli Lilly and Company\">Eli Lilly and Company</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Lilly"}, {"title": "Eli Lilly and Company", "link": "https://wikipedia.org/wiki/Eli_Lilly_and_Company"}]}, {"year": "1838", "text": "<PERSON>, German general and businessman, founded the Zeppelin Airship Company (d. 1917)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/Ferdinand_von_Zeppelin\" title=\"Ferdinand von Zeppelin\"><PERSON> von <PERSON></a>, German general and businessman, founded the <a href=\"https://wikipedia.org/wiki/Zeppelin\" title=\"Zeppelin\">Zeppelin Airship Company</a> (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ferdinand_von_Zeppelin\" title=\"Ferdinand von Zeppelin\"><PERSON> von <PERSON></a>, German general and businessman, founded the <a href=\"https://wikipedia.org/wiki/Zeppelin\" title=\"Zeppelin\">Zeppelin Airship Company</a> (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Zeppelin", "link": "https://wikipedia.org/wiki/Zeppelin"}]}, {"year": "1839", "text": "<PERSON>, American businessman and philanthropist, founded the Standard Oil Company (d. 1937)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Standard_Oil_Company\" class=\"mw-redirect\" title=\"Standard Oil Company\">Standard Oil Company</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded the <a href=\"https://wikipedia.org/wiki/Standard_Oil_Company\" class=\"mw-redirect\" title=\"Standard Oil Company\">Standard Oil Company</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Standard Oil Company", "link": "https://wikipedia.org/wiki/Standard_Oil_Company"}]}, {"year": "1851", "text": "<PERSON>, English archaeologist and academic (d. 1941)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English archaeologist and academic (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON>, Australian politician, 23rd Premier of Victoria (d. 1916)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Victorian_politician)\" title=\"<PERSON> (Victorian politician)\"><PERSON></a>, Australian politician, 23rd <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1916)", "links": [{"title": "<PERSON> (Victorian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Victorian_politician)"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1857", "text": "<PERSON>, French psychologist and graphologist (d. 1911)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bin<PERSON>\"><PERSON></a>, French psychologist and graphologist (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Binet\"><PERSON></a>, French psychologist and graphologist (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Binet"}]}, {"year": "1867", "text": "<PERSON><PERSON><PERSON>, German painter and sculptor (d. 1945)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor (d. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German painter and sculptor (d. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C3%A<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON><PERSON><PERSON>, Greek sociologist and politician, Prime Minister of Greece (d. 1936)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek sociologist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek sociologist and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1882", "text": "<PERSON>, Australian-American pianist and composer (d. 1961)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American pianist and composer (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American pianist and composer (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ger"}]}, {"year": "1885", "text": "<PERSON>, German philosopher, author, and academic (d. 1977)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, author, and academic (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German philosopher, author, and academic (d. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German fashion designer, founded <PERSON> (d. 1948)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Hugo_Boss_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> Boss (fashion designer)\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/Hugo_Boss\" title=\"Hugo Boss\">Hugo Boss</a> (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hugo_Boss_(fashion_designer)\" class=\"mw-redirect\" title=\"<PERSON> Boss (fashion designer)\"><PERSON></a>, German fashion designer, founded <a href=\"https://wikipedia.org/wiki/Hugo_Boss\" title=\"Hugo Boss\">Hugo Boss</a> (d. 1948)", "links": [{"title": "<PERSON> (fashion designer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(fashion_designer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON>, American painter (d. 1973)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, English author and poet (d. 1962)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and poet (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Russian painter (d. 1967)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian painter (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON>, American historian and author (d. 1968)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Buley\"><PERSON><PERSON></a>, American historian and author (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Buley\"><PERSON><PERSON></a>, American historian and author (d. 1968)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON>, Russian physicist and academic, Nobel Prize laureate (d. 1984)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1984)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1895", "text": "<PERSON>, Russian physicist and academic, Nobel Prize laureate (d. 1971)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1898", "text": "<PERSON>, American actor (d. 1972)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>ck\"><PERSON></a>, American actor (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> R<PERSON>ck\"><PERSON></a>, American actor (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_R<PERSON>ck"}]}, {"year": "1900", "text": "<PERSON>, American pianist, composer, and author (d. 1959)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and author (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist, composer, and author (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, French mathematician and academic (d. 2008)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and academic (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1905", "text": "<PERSON><PERSON>, Russian animator and director (d. 1997)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian animator and director (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian animator and director (d. 1997)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, American architect, designed the IDS Center and PPG Place (d. 2005)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/IDS_Center\" title=\"IDS Center\">IDS Center</a> and <a href=\"https://wikipedia.org/wiki/PPG_Place\" title=\"PPG Place\">PPG Place</a> (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American architect, designed the <a href=\"https://wikipedia.org/wiki/IDS_Center\" title=\"IDS Center\">IDS Center</a> and <a href=\"https://wikipedia.org/wiki/PPG_Place\" title=\"PPG Place\">PPG Place</a> (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "IDS Center", "link": "https://wikipedia.org/wiki/IDS_Center"}, {"title": "PPG Place", "link": "https://wikipedia.org/wiki/PPG_Place"}]}, {"year": "1907", "text": "<PERSON>, American businessman and politician, 43rd Governor of Michigan (d. 1995)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 43rd <a href=\"https://wikipedia.org/wiki/Governor_of_Michigan\" title=\"Governor of Michigan\">Governor of Michigan</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Michigan", "link": "https://wikipedia.org/wiki/Governor_of_Michigan"}]}, {"year": "1908", "text": "<PERSON>, American singer-songwriter, saxophonist, and actor (d. 1975)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Jordan\"><PERSON></a>, American singer-songwriter, saxophonist, and actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Louis Jordan\"><PERSON></a>, American singer-songwriter, saxophonist, and actor (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_Jordan"}]}, {"year": "1908", "text": "<PERSON>, American businessman and politician, 41st Vice President of the United States (d. 1979)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/Vice_President_of_the_United_States\" title=\"Vice President of the United States\">Vice President of the United States</a> (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Vice President of the United States", "link": "https://wikipedia.org/wiki/Vice_President_of_the_United_States"}]}, {"year": "1908", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, Indian economist, politician, professor and educator (d. 1991)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian economist, politician, professor and educator (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON></a>, Indian economist, politician, professor and educator (d. 1991)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, English soldier (d. 1971)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, English soldier (d. 1971)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1909", "text": "<PERSON><PERSON>, American football player (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player (d. 1995)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, Puerto Rican general (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADrez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican general (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%ADrez\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican general (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nces_Ram%C3%ADrez"}]}, {"year": "1911", "text": "<PERSON>, English cricketer (d. 1941)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Spanish politician (d. 2017)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish politician (d. 2017)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>er"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON>, Indian politician, 6th Chief Minister of West Bengal (d. 2010)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian politician, 6th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal\" class=\"mw-redirect\" title=\"Chief Minister of West Bengal\">Chief Minister of West Bengal</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Chief Minister of West Bengal", "link": "https://wikipedia.org/wiki/Chief_Minister_of_West_Bengal"}]}, {"year": "1914", "text": "<PERSON>, American singer and trumpet player (d. 1993)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and trumpet player (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American Air Force major general (d. 2019)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force major general (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Air Force major general (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, United States Marine Corps general (d. 2005)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Lowell_English\" class=\"mw-redirect\" title=\"Lowell English\">Lowell English</a>, United States Marine Corps general (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lowell_English\" class=\"mw-redirect\" title=\"Lowell English\">Lowell English</a>, United States Marine Corps general (d. 2005)", "links": [{"title": "Lowell English", "link": "https://wikipedia.org/wiki/Lowell_English"}]}, {"year": "1916", "text": "<PERSON>, American author, actress and screenwriter (d. 2017)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, actress and screenwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author, actress and screenwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, English actress (d. 1975)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, English actress (d. 1975)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1917", "text": "<PERSON>, American actress (d. 1983)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON> <PERSON><PERSON>, American novelist and short story writer (d. 1999)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist and short story writer (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>. <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist and short story writer (d. 1999)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American businessman, soldier, and diplomat, 12th United States Secretary of the Navy (d. 2009)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, soldier, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, soldier, and diplomat, 12th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy\" title=\"United States Secretary of the Navy\">United States Secretary of the Navy</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Navy", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Navy"}]}, {"year": "1918", "text": "<PERSON>, American illustrator (d. 2015)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Norwegian resistance member and pilot (d. 2002)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian resistance member and pilot (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian resistance member and pilot (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, British spy working for MI5 (d. 2008)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British spy working for MI5 (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British spy working for MI5 (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American Major General (d. 2017)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Major General (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American Major General (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON>, American actor (d. 2000)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2000)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1919", "text": "<PERSON>, German soldier and politician, 4th President of West Germany (d. 2016)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of West Germany</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier and politician, 4th <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of West Germany</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, Danish businessman (d. 1995)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish businessman (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Danish businessman (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, New Zealand psychologist and sexologist, known for his research on gender identity, and responsible for controversial involuntary sex reassignment of <PERSON>  (d. 2006)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Money\"><PERSON></a>, New Zealand psychologist and sexologist, known for his research on <a href=\"https://wikipedia.org/wiki/Gender_identity\" title=\"Gender identity\">gender identity</a>, and responsible for controversial involuntary <a href=\"https://wikipedia.org/wiki/Sex_reassignment_surgery\" class=\"mw-redirect\" title=\"Sex reassignment surgery\">sex reassignment</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Money\"><PERSON></a>, New Zealand psychologist and sexologist, known for his research on <a href=\"https://wikipedia.org/wiki/Gender_identity\" title=\"Gender identity\">gender identity</a>, and responsible for controversial involuntary <a href=\"https://wikipedia.org/wiki/Sex_reassignment_surgery\" class=\"mw-redirect\" title=\"Sex reassignment surgery\">sex reassignment</a> of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gender identity", "link": "https://wikipedia.org/wiki/Gender_identity"}, {"title": "Sex reassignment surgery", "link": "https://wikipedia.org/wiki/Sex_reassignment_surgery"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American sprinter and hurdler (d. 2019)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and hurdler (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American actor (d. 2021)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American pianist and songwriter (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American pianist and songwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, American pianist and songwriter (d. 2005)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(musician)"}]}, {"year": "1924", "text": "<PERSON>, American politician", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Italian cardinal (d. 2014)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marco_C%C3%A9"}]}, {"year": "1925", "text": "<PERSON>, Italian-American businessman (d. 2020)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, Italian-American businessman (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Sr.\"><PERSON> Sr.</a>, Italian-American businessman (d. 2020)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1925", "text": "<PERSON>, American football quarterback (d. 2019)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football quarterback (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football quarterback (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, French actor, screenwriter and director (d. 2017)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, screenwriter and director (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, screenwriter and director (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Australian philosopher and author (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian philosopher and author (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American lieutenant and politician (d. 2019)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and politician (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Swiss professional ice hockey goaltender (d. 2003)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss professional ice hockey goaltender (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss professional ice hockey goaltender (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>-<PERSON>, Swiss-American psychiatrist and author (d. 2004)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American psychiatrist and author (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss-American psychiatrist and author (d. 2004)", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Elisabeth_K%C3%BCbler-Ross"}]}, {"year": "1927", "text": "<PERSON>, Irish educator and politician (d. 2017)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish educator and politician (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON><PERSON>, Tibetan religious leader", "html": "1927 - <a href=\"https://wikipedia.org/wiki/Khen<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tibetan religious leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>hen<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Tibetan religious leader", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American country singer (d. 2013)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, former prime minister of Pakistan (d. 2022)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lak<PERSON>\"><PERSON><PERSON><PERSON></a>, former prime minister of Pakistan (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lak<PERSON>\"><PERSON><PERSON><PERSON></a>, former prime minister of Pakistan (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American singer (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Vale\"><PERSON></a>, American singer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Canadian lawyer and politician, 16th Chief Justice of Canada (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Chilean journalist (d. 2012)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean journalist (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chilean journalist (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor and screenwriter (d. 1982)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, American politician", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American football player and coach (d. 2015)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor and singer (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Russian engineer and cosmonaut (d. 2010)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and cosmonaut (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian engineer and cosmonaut (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, English actress (d. 2013)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian businessman and politician, 8th Canadian Minister of Communications", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Communications_(Canada)\" title=\"Minister of Communications (Canada)\">Canadian Minister of Communications</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_of_Communications_(Canada)\" title=\"Minister of Communications (Canada)\">Canadian Minister of Communications</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Communications (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Communications_(Canada)"}]}, {"year": "1940", "text": "<PERSON>, American bass player and songwriter (d. 2015)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player and songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON> B<PERSON> Ma<PERSON>in\"><PERSON></a>, American bass player and songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Italian-English footballer, coach, and manager", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-English footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-English footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dar<PERSON>_<PERSON>radi"}]}, {"year": "1942", "text": "<PERSON>, American economist and politician", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, American drummer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American drummer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>e"}]}, {"year": "1944", "text": "<PERSON>, American actor and singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON>, Swiss politician, 91st President of the Swiss Confederation", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss politician, 91st <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss politician, 91st <a href=\"https://wikipedia.org/wiki/President_of_the_Swiss_Confederation\" title=\"President of the Swiss Confederation\">President of the Swiss Confederation</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Calmy-Rey"}, {"title": "President of the Swiss Confederation", "link": "https://wikipedia.org/wiki/President_of_the_Swiss_Confederation"}]}, {"year": "1947", "text": "<PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English author and screenwriter (d. 2016)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Peruvian religious leader, founded the Sodalitium Christianae Vitae", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Sodalitium_Christianae_Vitae\" title=\"Sodalitium Christianae Vitae\">Sodalitium Christianae Vitae</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Peruvian religious leader, founded the <a href=\"https://wikipedia.org/wiki/Sodalitium_Christianae_Vitae\" title=\"Sodalitium Christianae Vitae\">Sodalitium Christianae Vitae</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sodalitium Christianae Vitae", "link": "https://wikipedia.org/wiki/Sodalitium_Christianae_Vitae"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Egyptian-Canadian singer-songwriter", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Egyptian-Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (musician)\"><PERSON><PERSON><PERSON></a>, Egyptian-Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(musician)"}]}, {"year": "1948", "text": "<PERSON>, American civil-rights activist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Ruby_Sales\" title=\"Ruby Sales\">Ruby Sales</a>, American civil-rights activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruby_Sales\" title=\"Ruby Sales\">Ruby Sales</a>, American civil-rights activist", "links": [{"title": "Ruby Sales", "link": "https://wikipedia.org/wiki/Ruby_Sales"}]}, {"year": "1949", "text": "<PERSON>, Austrian-American chef, restaurateur and entrepreneur", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chef, restaurateur and entrepreneur", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American chef, restaurateur and entrepreneur", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, Indian politician, 14th Chief Minister of Andhra Pradesh (d. 2009)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Y._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"List of Chief Ministers of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Y._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 14th <a href=\"https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Andhra_Pradesh\" class=\"mw-redirect\" title=\"List of Chief Ministers of Andhra Pradesh\">Chief Minister of Andhra Pradesh</a> (d. 2009)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Y._<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of Chief Ministers of Andhra Pradesh", "link": "https://wikipedia.org/wiki/List_of_Chief_Ministers_of_Andhra_Pradesh"}]}, {"year": "1951", "text": "<PERSON>, American baseball player, manager, and sportscaster", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, American actress and director", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and director", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American football player and sportscaster", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1952", "text": "<PERSON>, American author and activist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian baseball player and coach", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Mexican-American guitarist and songwriter", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Russian journalist and author", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actor and musician", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Swedish educator and politician, 8th Swedish Minister for the Environment", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish educator and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)\" title=\"Minister for the Environment (Sweden)\">Swedish Minister for the Environment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish educator and politician, 8th <a href=\"https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)\" title=\"Minister for the Environment (Sweden)\">Swedish Minister for the Environment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister for the Environment (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_the_Environment_(Sweden)"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Israeli lawyer and politician, 18th Justice Minister of Israel", "html": "1958 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Justice_Minister_of_Israel\" class=\"mw-redirect\" title=\"Justice Minister of Israel\">Justice Minister of Israel</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Justice_Minister_of_Israel\" class=\"mw-redirect\" title=\"Justice Minister of Israel\">Justice Minister of Israel</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T<PERSON><PERSON>_<PERSON>"}, {"title": "Justice Minister of Israel", "link": "https://wikipedia.org/wiki/Justice_Minister_of_Israel"}]}, {"year": "1959", "text": "<PERSON>, English actress", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mal_<PERSON>inga"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Filipino journalist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ces <PERSON>\"><PERSON><PERSON></a>, Filipino journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ce<PERSON>\"><PERSON><PERSON></a>, Filipino journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ces_Drilon"}]}, {"year": "1961", "text": "<PERSON>, English keyboard player (d. 2022)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English keyboard player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English keyboard player (d. 2022)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter, producer, and actor (d. 2024)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and actor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, Norwegian saxophonist and record producer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian saxophonist and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian saxophonist and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American director and screenwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, American director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)\" class=\"mw-redirect\" title=\"<PERSON> (director)\"><PERSON></a>, American director and screenwriter", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(director)"}]}, {"year": "1964", "text": "<PERSON>, Russian ice hockey player and manager", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian ice hockey player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American clarinet player, saxophonist, and bandleader", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, saxophonist, and bandleader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American clarinet player, saxophonist, and bandleader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, German-Chinese virologist and academic", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Chinese virologist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Chinese virologist and academic", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Nauruan politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/Shadlog_Bernicke\" title=\"Shadlog Bernicke\">Shad<PERSON></a>, Nauruan politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shadlog_Bernicke\" title=\"Shadlog Bernicke\">Shad<PERSON></a>, Nauruan politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shadlog_<PERSON>icke"}]}, {"year": "1966", "text": "<PERSON>, American politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Hong Kong actor and singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Colombian singer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charlie <PERSON>\"><PERSON></a>, Colombian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Charlie <PERSON>\"><PERSON></a>, Colombian singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, New Zealand rugby player and coach", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter, guitarist and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Sugi<PERSON>\" title=\"<PERSON>gi<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>gi<PERSON>\" title=\"<PERSON>gi<PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sugi<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter and producer", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian educator and politician", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American tennis player and coach", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Welsh rugby player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sourav_<PERSON>y\" title=\"Soura<PERSON> Ganguly\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sourav_<PERSON>\" title=\"Sourav Gangul<PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sourav_<PERSON>y"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese actor", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Sh%C5%8<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sh%C5%8<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sh%C5%8Ds<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Canadian actress and writer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Chinese field hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese field hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese field hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Moroccan footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/Talal_El_Karkouri\" title=\"Talal El Karkouri\"><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Talal_El_Karkouri\" title=\"Talal El Karkouri\"><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Karkouri"}]}, {"year": "1976", "text": "<PERSON>, English sailor", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English sailor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian footballer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Paolo_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American actor, director, and producer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Milo_Ventimiglia"}]}, {"year": "1977", "text": "<PERSON>, Chinese basketball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Estonian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_Roo<PERSON>\" title=\"<PERSON><PERSON>s Rooba\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/U<PERSON><PERSON>_R<PERSON>\" title=\"<PERSON><PERSON>s Rooba\"><PERSON><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ooba"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Scottish-American singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American-Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Irish footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, German runner", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_M%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%BCller\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wolfram_M%C3%BCller"}]}, {"year": "1981", "text": "<PERSON>, Russian tennis player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>-<PERSON>, Barbadian netball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/S<PERSON>tte_Azore-Bruce\" title=\"Shonette Azore-Bruce\">Shonette Azore-Bruce</a>, Barbadian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shonette_Azore-Bruce\" title=\"Shonette Azore-Bruce\">Shonette Azore-Bruce</a>, Barbadian netball player", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/Shonette_Azore-Bruce"}]}, {"year": "1982", "text": "<PERSON>, American actress and director", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Sophia_Bush\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sophia_Bush\" title=\"Sophia <PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sophia_Bush"}]}, {"year": "1982", "text": "<PERSON><PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rick"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1983", "text": "<PERSON>, Canadian ice hockey player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ren<PERSON> Costa\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ren<PERSON> Costa\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "Ren<PERSON>", "link": "https://wikipedia.org/wiki/Renata_Costa"}]}, {"year": "1987", "text": "<PERSON>, American baseball player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Spanish footballer (d. 2012)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Roqu%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>oqu%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer (d. 2012)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_Roqu%C3%A9"}]}, {"year": "1988", "text": "<PERSON>, New Zealand cyclist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Israeli Judo champion", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Yard<PERSON>_G<PERSON>bi\" title=\"Yarden Gerbi\"><PERSON><PERSON></a>, Israeli Judo champion", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yard<PERSON>_G<PERSON>bi\" title=\"Yarden Gerbi\"><PERSON><PERSON></a>, Israeli Judo champion", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>erbi"}]}, {"year": "1989", "text": "<PERSON> <PERSON>, Norwegian footballer (d. 2012)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> <PERSON>\"><PERSON> <PERSON></a>, Norwegian footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a>, Norwegian footballer (d. 2012)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, German footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Mexican singer-songwriter (d. 2015)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ariel_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Korean footballer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Korean footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>-<PERSON>\"><PERSON>-<PERSON></a>, Korean footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-min"}]}, {"year": "1993", "text": "<PERSON>, American actor", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, American football player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American football player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actress", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, American actor and rapper", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and rapper", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, Turkish tennis player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/%C4%B0pek_%C3%96z\" title=\"İpek Öz\"><PERSON><PERSON><PERSON></a>, Turkish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%B0pek_%C3%96z\" title=\"İpek Öz\"><PERSON><PERSON><PERSON></a>, Turkish tennis player", "links": [{"title": "İpek Öz", "link": "https://wikipedia.org/wiki/%C4%B0pek_%C3%96z"}]}], "Deaths": [{"year": "689", "text": "<PERSON><PERSON>, Irish bishop", "html": "689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Kilian\" title=\"Saint Kilian\"><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Kilian\" title=\"Saint Kilian\"><PERSON><PERSON></a>, Irish <a href=\"https://wikipedia.org/wiki/Bishop\" title=\"Bishop\">bishop</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Saint_Kilian"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Bishop"}]}, {"year": "810", "text": "<PERSON><PERSON><PERSON> of Italy, son of <PERSON><PERSON><PERSON><PERSON> (b. 773)", "html": "810 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Italy\" title=\"<PERSON><PERSON><PERSON> of Italy\"><PERSON><PERSON><PERSON> of Italy</a>, son of <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charlema<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 773)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Italy\" title=\"<PERSON><PERSON><PERSON> of Italy\"><PERSON><PERSON><PERSON> of Italy</a>, son of <a href=\"https://wikipedia.org/wiki/Cha<PERSON><PERSON><PERSON>\" title=\"Charle<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 773)", "links": [{"title": "<PERSON><PERSON>in of Italy", "link": "https://wikipedia.org/wiki/Pepin_of_Italy"}, {"title": "Charlemagne", "link": "https://wikipedia.org/wiki/Charlemagne"}]}, {"year": "873", "text": "<PERSON><PERSON>, archbishop of Cologne", "html": "873 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON><PERSON> (archbishop of Cologne)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)\" title=\"<PERSON><PERSON> (archbishop of Cologne)\"><PERSON><PERSON></a>, archbishop of <a href=\"https://wikipedia.org/wiki/Cologne\" title=\"Cologne\">Cologne</a>", "links": [{"title": "<PERSON><PERSON> (archbishop of Cologne)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(archbishop_of_Cologne)"}, {"title": "Cologne", "link": "https://wikipedia.org/wiki/Cologne"}]}, {"year": "900", "text": "<PERSON><PERSON><PERSON>, wife of the Abbasid caliph <PERSON><PERSON><PERSON>", "html": "900 - <a href=\"https://wikipedia.org/wiki/Qat<PERSON>_al-Nada\" title=\"Qatr al-Nada\"><PERSON><PERSON><PERSON> <PERSON></a>, wife of the Abbasid caliph <PERSON><PERSON><PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qat<PERSON>_al-Nada\" title=\"Qatr al-Nada\"><PERSON><PERSON><PERSON> <PERSON></a>, wife of the Abbasid caliph <PERSON><PERSON>", "links": [{"title": "<PERSON><PERSON>r al-Na<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>at<PERSON>_al-Nada"}]}, {"year": "901", "text": "<PERSON><PERSON><PERSON><PERSON>, French-English monk and saint (b. 827)", "html": "901 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-English monk and saint (b. 827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-English monk and saint (b. 827)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "975", "text": "<PERSON> the <PERSON>, English king (b. 943)", "html": "975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Peaceful\" class=\"mw-redirect\" title=\"<PERSON> the Peaceful\"><PERSON> the Peaceful</a>, English king (b. 943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Peaceful\" class=\"mw-redirect\" title=\"<PERSON> the Peaceful\"><PERSON> the Peaceful</a>, English king (b. 943)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Peaceful"}]}, {"year": "1153", "text": "<PERSON> (b. 1087)", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON> III</a> (b. 1087)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> III</a> (b. 1087)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1253", "text": "<PERSON><PERSON><PERSON> of Navarre (b. 1201)", "html": "1253 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Navarre\" title=\"<PERSON>bal<PERSON> I of Navarre\"><PERSON><PERSON><PERSON> of Navarre</a> (b. 1201)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON><PERSON><PERSON> I of Navarre\"><PERSON><PERSON><PERSON> of Navarre</a> (b. 1201)", "links": [{"title": "<PERSON><PERSON><PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Navarre"}]}, {"year": "1261", "text": "<PERSON> of Holstein, Count of Schauenburg", "html": "1261 - <a href=\"https://wikipedia.org/wiki/Adolf_IV_of_Holstein\" title=\"<PERSON> IV of Holstein\"><PERSON> of Holstein</a>, Count of Schauenburg", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adolf_IV_of_Holstein\" title=\"<PERSON> IV of Holstein\"><PERSON> of Holstein</a>, Count of Schauenburg", "links": [{"title": "<PERSON> of Holstein", "link": "https://wikipedia.org/wiki/Adolf_<PERSON>_<PERSON>_Holstein"}]}, {"year": "1390", "text": "<PERSON> of Saxony, Bishop of Halberstadt and German philosopher (b. circa 1320)", "html": "1390 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony_(philosopher)\" title=\"<PERSON> of Saxony (philosopher)\"><PERSON> of Saxony</a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Halberstadt\" class=\"mw-redirect\" title=\"Bishop of Halberstadt\">Bishop of Halberstadt</a> and German <a href=\"https://wikipedia.org/wiki/Philosopher\" class=\"mw-redirect\" title=\"Philosopher\">philosopher</a> (b. circa 1320)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony_(philosopher)\" title=\"<PERSON> of Saxony (philosopher)\"><PERSON> of Saxony</a>, <a href=\"https://wikipedia.org/wiki/Bishop_of_Halberstadt\" class=\"mw-redirect\" title=\"Bishop of Halberstadt\">Bishop of Halberstadt</a> and German <a href=\"https://wikipedia.org/wiki/Philosopher\" class=\"mw-redirect\" title=\"Philosopher\">philosopher</a> (b. circa 1320)", "links": [{"title": "<PERSON> of Saxony (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Saxony_(philosopher)"}, {"title": "Bishop of Halberstadt", "link": "https://wikipedia.org/wiki/<PERSON>_of_Halberstadt"}, {"title": "Philosopher", "link": "https://wikipedia.org/wiki/Philosopher"}]}, {"year": "1538", "text": "<PERSON>, Spanish general and explorer (b. 1475)", "html": "1538 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Diego <PERSON> Almagro\"><PERSON></a>, Spanish general and explorer (b. 1475)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Diego de Almagro\"><PERSON></a>, Spanish general and explorer (b. 1475)", "links": [{"title": "Diego <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1623", "text": "<PERSON> (b. 1554)", "html": "1623 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON>_XV\" title=\"Pope Gregory XV\"><PERSON> <PERSON></a> (b. 1554)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XV\" title=\"Pope Gregory XV\"><PERSON></a> (b. 1554)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1689", "text": "<PERSON>, English-American settler (b. 1622)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American settler (b. 1622)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American settler (b. 1622)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON><PERSON>, Dutch mathematician, astronomer, and physicist (b. 1629)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician, astronomer, and physicist (b. 1629)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch mathematician, astronomer, and physicist (b. 1629)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1716", "text": "<PERSON>, English preacher and theologian (b. 1634)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/Robert_South\" title=\"Robert <PERSON>\"><PERSON></a>, English preacher and theologian (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_South\" title=\"Robert South\"><PERSON></a>, English preacher and theologian (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_South"}]}, {"year": "1721", "text": "<PERSON><PERSON>, American-English merchant and philanthropist (b. 1649)", "html": "1721 - <a href=\"https://wikipedia.org/wiki/Elihu_Yale\" title=\"Elihu Yale\"><PERSON><PERSON></a>, American-English merchant and philanthropist (b. 1649)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elihu_Yale\" title=\"Elihu Yale\"><PERSON><PERSON></a>, American-English merchant and philanthropist (b. 1649)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Elihu_Yale"}]}, {"year": "1784", "text": "<PERSON><PERSON><PERSON>, Swedish chemist and mineralogist (b. 1735)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish chemist and mineralogist (b. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish chemist and mineralogist (b. 1735)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1794", "text": "<PERSON>, French architect (b. 1728)", "html": "1794 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (b. 1728)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French architect (b. 1728)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1820", "text": "<PERSON><PERSON>, daughter of <PERSON> (b. 1816)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1816)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1816)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, Scottish portrait painter (b. 1756)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish portrait painter (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish portrait painter (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1822", "text": "<PERSON>, English poet and playwright (b. 1792)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and playwright (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1850", "text": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Cambridge (b. 1774)", "html": "1850 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Cambridge\" title=\"Prince <PERSON><PERSON><PERSON>, Duke of Cambridge\"><PERSON> <PERSON><PERSON><PERSON>, Duke of Cambridge</a> (b. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON><PERSON><PERSON>,_Duke_of_Cambridge\" title=\"<PERSON> <PERSON><PERSON><PERSON>, Duke of Cambridge\">Prince <PERSON><PERSON><PERSON>, Duke of Cambridge</a> (b. 1774)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>, Duke of Cambridge", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>,_Duke_of_Cambridge"}]}, {"year": "1859", "text": "<PERSON> of Sweden (b. 1799)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a> (b. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Oscar_I_of_Sweden\" title=\"Oscar I of Sweden\"><PERSON> of Sweden</a> (b. 1799)", "links": [{"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Sweden"}]}, {"year": "1873", "text": "<PERSON>, German painter and lithographer (b. 1805)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and lithographer (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German painter and lithographer (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1887", "text": "<PERSON>, American businessman (b. 1819)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Austrian chemist and physicist (b. 1821)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian chemist and physicist (b. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian chemist and physicist (b. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American violinist and composer (b. 1834)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist and composer (b. 1834)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, French-Canadian author (b. 1880)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Louis_H%C3%A9mon\" title=\"<PERSON>\"><PERSON></a>, French-Canadian author (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_H%C3%A9mon\" title=\"<PERSON>\"><PERSON></a>, French-Canadian author (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_H%C3%A9mon"}]}, {"year": "1917", "text": "<PERSON>, Canadian painter (b. 1877)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian painter (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON> (suffragette), British sufragette and purity activist (b. 1870)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)\" title=\"<PERSON> (suffragette)\"><PERSON> (suffragette)</a>, British sufragette and purity activist (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)\" title=\"<PERSON> (suffragette)\"><PERSON> (suffragette)</a>, British sufragette and purity activist (b. 1870)", "links": [{"title": "<PERSON> (suffragette)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(suffragette)"}]}, {"year": "1930", "text": "<PERSON>, Australian-New Zealand businessman and politician, 17th Prime Minister of New Zealand (b. 1856)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-New Zealand businessman and politician, 17th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1933", "text": "<PERSON>, English author and playwright (b. 1863)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (b. 1863)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, French astronomer and academic (b. 1848)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer and academic (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON>, English psychologist and author (b. 1859)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English psychologist and author (b. 1859)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English psychologist and author (b. 1859)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Polish rabbi, historian, and politician (b. 1874)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish rabbi, historian, and politician (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish rabbi, historian, and politician (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Algerian-French general (b. 1856)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Esp%C3%A8rey\" title=\"<PERSON>E<PERSON>èrey\"><PERSON></a>, Algerian-French general (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Esp%C3%A8rey\" title=\"<PERSON>E<PERSON>èrey\"><PERSON></a>, Algerian-French general (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Franchet_d%27Esp%C3%A8rey"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON>, Turkish physician and politician, 5th Prime Minister of Turkey (b. 1881)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish physician and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Turkey\" title=\"Prime Minister of Turkey\">Prime Minister of Turkey</a> (b. 1881)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Refi<PERSON>_<PERSON>dam"}, {"title": "Prime Minister of Turkey", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Turkey"}]}, {"year": "1943", "text": "<PERSON>, French soldier (b. 1899)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French soldier (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Austrian sociologist, economist, and philosopher (b. 1878)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian sociologist, economist, and philosopher (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Austrian sociologist, economist, and philosopher (b. 1878)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>nn"}]}, {"year": "1952", "text": "<PERSON>, Estonian lawyer, author, and poet (b. 1890)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/August_Alle\" title=\"August Alle\">August <PERSON></a>, Estonian lawyer, author, and poet (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_Alle\" title=\"August Alle\">August <PERSON></a>, Estonian lawyer, author, and poet (b. 1890)", "links": [{"title": "August Alle", "link": "https://wikipedia.org/wiki/August_Alle"}]}, {"year": "1956", "text": "<PERSON>, Italian journalist, author, and critic (b. 1881)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, author, and critic (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist, author, and critic (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American lawyer and author (b. 1881)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, British actress (b. 1913)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British actress (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, British actress (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON><PERSON>, French swimmer and water polo player (b. 1882)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French swimmer and water polo player (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French swimmer and water polo player (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez"}]}, {"year": "1971", "text": "<PERSON>, German mathematician connected to the Vienna Circle (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician connected to the Vienna Circle (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German mathematician connected to the Vienna Circle (b. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Palestinian writer and politician (b. 1936)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian writer and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Palestinian writer and politician (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1973", "text": "<PERSON>, American screenwriter and producer (b. 1924)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Russian-Israeli educator and politician, 4th Education Minister of Israel (b. 1884)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Education_Minister_of_Israel\" class=\"mw-redirect\" title=\"Education Minister of Israel\">Education Minister of Israel</a> (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian-Israeli educator and politician, 4th <a href=\"https://wikipedia.org/wiki/Education_Minister_of_Israel\" class=\"mw-redirect\" title=\"Education Minister of Israel\">Education Minister of Israel</a> (b. 1884)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}, {"title": "Education Minister of Israel", "link": "https://wikipedia.org/wiki/Education_Minister_of_Israel"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, English cricketer and coach (b. 1877)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English cricketer and coach (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">W<PERSON><PERSON></a>, English cricketer and coach (b. 1877)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese physicist and academic, Nobel Prize laureate (b. 1906)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Sin-Itiro_Tomonaga\" class=\"mw-redirect\" title=\"Sin-Itiro Tomonaga\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sin-Itiro_Tomonaga\" class=\"mw-redirect\" title=\"Sin-Itiro Tomonaga\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1906)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sin-<PERSON>iro_<PERSON>ga"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1979", "text": "<PERSON>, English actor (b. 1912)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American chemist and academic, Nobel Prize laureate (b. 1917)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1981", "text": "<PERSON> (hunger striker), Irish Republican Army member (b. 1951)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON> (hunger striker)</a>, Irish Republican Army member (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)\" title=\"<PERSON> (hunger striker)\"><PERSON> (hunger striker)</a>, Irish Republican Army member (b. 1951)", "links": [{"title": "<PERSON> (hunger striker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hunger_striker)"}]}, {"year": "1981", "text": "<PERSON>, American baseball player (b. 1902)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actor and screenwriter (b. 1913)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, French actor, director, and screenwriter (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actor, director, and screenwriter (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON>, American baseball player and manager (b. 1909)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American baseball player and manager (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian lawyer and politician, 27th Canadian Minister of Justice (b. 1903)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Spanish poet and author (b. 1896)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Gerard<PERSON>\"><PERSON><PERSON></a>, Spanish poet and author (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Gerardo <PERSON>\"><PERSON><PERSON></a>, Spanish poet and author (b. 1896)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American runner and football player (b. 1905)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and football player (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and football player (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ray_Barbuti"}]}, {"year": "1990", "text": "<PERSON>, American actor (b. 1913)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor (b. 1934)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Bangladeshi Islamic scholar and freedom fighter (b. 1918)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and freedom fighter (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bangladeshi Islamic scholar and freedom fighter (b. 1918)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, French director and screenwriter (b. 1904)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Christian-Jaque\" title=\"Christian-Jaque\"><PERSON><PERSON><PERSON><PERSON></a>, French director and screenwriter (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christian-Jaque\" title=\"Christian-Jaque\"><PERSON><PERSON><PERSON><PERSON></a>, French director and screenwriter (b. 1904)", "links": [{"title": "Christian-J<PERSON>", "link": "https://wikipedia.org/wiki/Christian-Jaque"}]}, {"year": "1994", "text": "<PERSON>, North Korean commander and politician, President of North Korea (b. 1912)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, <a href=\"https://wikipedia.org/wiki/Eternal_leaders_of_North_Korea#Presidency_of_North_Korea_before_1994\" title=\"Eternal leaders of North Korea\">President of North Korea</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, North Korean commander and politician, <a href=\"https://wikipedia.org/wiki/Eternal_leaders_of_North_Korea#Presidency_of_North_Korea_before_1994\" title=\"Eternal leaders of North Korea\">President of North Korea</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Eternal leaders of North Korea", "link": "https://wikipedia.org/wiki/Eternal_leaders_of_North_Korea#Presidency_of_North_Korea_before_1994"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Swedish-American businessman and explorer (b. 1927)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American businessman and explorer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish-American businessman and explorer (b. 1927)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, American actor (b. 1930)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, Austrian-born actress and writer (b. 1911)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born actress and writer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-born actress and writer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Spanish tennis player, author, and feminist (b. 1905)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Lil%C3%AD_%C3%81l<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player, author, and feminist (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lil%C3%AD_%C3%81lvar<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish tennis player, author, and feminist (b. 1905)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lil%C3%AD_%C3%<PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American captain, pilot, and astronaut (b. 1930)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, pilot, and astronaut (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, New Zealand director, producer, and screenwriter (b. 1920)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, New Zealand director, producer, and screenwriter (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON>_(director)\" title=\"<PERSON> (director)\"><PERSON></a>, New Zealand director, producer, and screenwriter (b. 1920)", "links": [{"title": "<PERSON> (director)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27S<PERSON>_(director)"}]}, {"year": "2002", "text": "<PERSON>, American animator and trombonist (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Kimball\"><PERSON></a>, American animator and trombonist (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>ball\" title=\"Ward Kimball\"><PERSON></a>, American animator and trombonist (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ward_Kimball"}]}, {"year": "2004", "text": "<PERSON>, American author and educator (b. 1944)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French actor and cellist (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and cellist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and cellist (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, American actress and singer (b. 1917)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American actress and singer (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/June_<PERSON><PERSON>\" title=\"June <PERSON>\">June <PERSON></a>, American actress and singer (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/June_<PERSON><PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Indian lawyer and politician, 9th Prime Minister of India (b. 1927)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian lawyer and politician, 9th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_India\" title=\"Prime Minister of India\">Prime Minister of India</a> (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of India", "link": "https://wikipedia.org/wiki/Prime_Minister_of_India"}]}, {"year": "2007", "text": "<PERSON>, American screenwriter and producer (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON> So<PERSON>\"><PERSON></a>, American screenwriter and producer (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American-born British businessman and philanthropist (b. 1912)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born British businessman and philanthropist (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born British businessman and philanthropist (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter (b. 1962)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter (b. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\">Midnight</a>, American singer-songwriter (b. 1962)", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "2011", "text": "<PERSON>, American actor and poet (b. 1924)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blossom\"><PERSON></a>, American actor and poet (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Blossom\"><PERSON></a>, American actor and poet (b. 1924)", "links": [{"title": "<PERSON> Blossom", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>lossom"}]}, {"year": "2011", "text": "<PERSON>, First Lady of the United States (b. 1918)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Betty Ford\"><PERSON></a>, First Lady of the United States (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Ford\" title=\"Betty Ford\"><PERSON></a>, First Lady of the United States (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON> <PERSON>, Saudi Arabian politician (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, Saudi Arabian politician (b. 1934)", "links": [{"title": "<PERSON><PERSON><PERSON> bin <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Nigerian physician and politician (b. 1959)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>tong\" title=\"Gyang <PERSON>op Datong\"><PERSON><PERSON></a>, Nigerian physician and politician (b. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>tong\" title=\"Gyang <PERSON>op Datong\"><PERSON><PERSON></a>, Nigerian physician and politician (b. 1959)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>tong"}]}, {"year": "2012", "text": "<PERSON>, American costume designer (b. 1953)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American costume designer (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American costume designer (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American historian and author (b. 1916)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, American historian and author (b. 1916)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer (b. 1980)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian footballer (b. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, American physicist and academic (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rr\" title=\"Rubby Sherr\"><PERSON><PERSON><PERSON></a>, American physicist and academic (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rr\" title=\"R<PERSON>by Sherr\"><PERSON><PERSON><PERSON></a>, American physicist and academic (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Indian author (b. 1924)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian author (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>i"}]}, {"year": "2013", "text": "<PERSON>, American songwriter and producer (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian lawyer and politician (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio\" title=\"Plínio de Arruda Sampaio\"><PERSON><PERSON><PERSON><PERSON> de Arruda Sampaio</a>, Brazilian lawyer and politician (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio\" title=\"Plínio de Arruda Sampaio\"><PERSON><PERSON><PERSON><PERSON> de Arruda Sampaio</a>, Brazilian lawyer and politician (b. 1930)", "links": [{"title": "Plínio de Arruda Sampaio", "link": "https://wikipedia.org/wiki/Pl%C3%ADnio_de_Arruda_Sampaio"}]}, {"year": "2014", "text": "<PERSON>, American soldier and politician, 27th Governor of Idaho (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Idaho_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Idaho governor)\"><PERSON></a>, American soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Idaho_governor)\" class=\"mw-redirect\" title=\"<PERSON> (Idaho governor)\"><PERSON></a>, American soldier and politician, 27th <a href=\"https://wikipedia.org/wiki/Governor_of_Idaho\" class=\"mw-redirect\" title=\"Governor of Idaho\">Governor of Idaho</a> (b. 1925)", "links": [{"title": "<PERSON> (Idaho governor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Idaho_governor)"}, {"title": "Governor of Idaho", "link": "https://wikipedia.org/wiki/Governor_of_Idaho"}]}, {"year": "2014", "text": "<PERSON>, Guamanian businessman and politician (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guamanian businessman and politician (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Guamanian businessman and politician (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American bobsledder and coach (b. 1945)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bobsledder and coach (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bobsledder and coach (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American baseball player (b. 1953)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American football player and sportscaster (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and sportscaster (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American poet (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American poet (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American poet (b. 1943)", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)"}]}, {"year": "2016", "text": "<PERSON>, Pakistani philanthropist (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani philanthropist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Pakistani philanthropist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON>, American actor, pop singer, film producer and author (b. 1931)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>b_<PERSON>\" title=\"Tab <PERSON>\"><PERSON><PERSON></a>, American actor, pop singer, film producer and author (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Tab <PERSON>\"><PERSON><PERSON></a>, American actor, pop singer, film producer and author (b. 1931)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tab_<PERSON>"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American actress, model and singer (b. 1987)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, model and singer (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress, model and singer (b. 1987)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Australian snowboarder (b. 1987)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snowboarder (b. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian snowboarder (b. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON>, Japanese politician (b. 1954)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese politician (b. 1954)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, American actor and comedian (b. 1923)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2022", "text": "<PERSON>, Mexican lawyer and politician (b. 1922)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Mexican lawyer and politician (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>cheverr%C3%ADa"}]}, {"year": "2022", "text": "<PERSON>, American actor (b. 1942)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}