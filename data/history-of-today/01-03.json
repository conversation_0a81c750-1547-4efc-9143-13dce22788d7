{"date": "January 3", "url": "https://wikipedia.org/wiki/January_3", "data": {"Events": [{"year": "69", "text": "The Roman legions on the Rhine refuse to declare their allegiance to G<PERSON><PERSON>, instead proclaiming their legate, <PERSON><PERSON>, as emperor.", "html": "69 - The Roman legions on the Rhine refuse to declare their allegiance to <a href=\"https://wikipedia.org/wiki/Galba\" title=\"Gal<PERSON>\">Gal<PERSON></a>, instead proclaiming their legate, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Vitellius\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Vitellius\"><PERSON><PERSON></a>, as emperor.", "no_year_html": "The Roman legions on the Rhine refuse to declare their allegiance to <a href=\"https://wikipedia.org/wiki/Galba\" title=\"<PERSON>al<PERSON>\"><PERSON><PERSON><PERSON></a>, instead proclaiming their legate, <a href=\"https://wikipedia.org/wiki/Au<PERSON>_Vitellius\" class=\"mw-redirect\" title=\"<PERSON><PERSON> Vitellius\"><PERSON><PERSON></a>, as emperor.", "links": [{"title": "Galba", "link": "https://wikipedia.org/wiki/Galba"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Vitellius"}]}, {"year": "250", "text": "Emperor <PERSON><PERSON> orders everyone in the Roman Empire (except Jews) to make sacrifices to the Roman gods.", "html": "250 - Emperor <a href=\"https://wikipedia.org/wiki/Decius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> orders everyone in the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> (except Jews) <a href=\"https://wikipedia.org/wiki/Decian_persecution\" title=\"Decian persecution\">to make sacrifices to the Roman gods</a>.", "no_year_html": "Emperor <a href=\"https://wikipedia.org/wiki/Decius\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> orders everyone in the <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman Empire</a> (except Jews) <a href=\"https://wikipedia.org/wiki/Decian_persecution\" title=\"Decian persecution\">to make sacrifices to the Roman gods</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Decius"}, {"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "Decian persecution", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_persecution"}]}, {"year": "1521", "text": "Pope <PERSON> excommunicates <PERSON> in the papal bull Decet Romanum Pontificem.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Leo <PERSON>\">Pope <PERSON></a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Decet_Romanum_Pontificem\" title=\"Decet Romanum Pontificem\">Decet Romanum Pontificem</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Leo_<PERSON>\" title=\"Pope <PERSON>\">Pope <PERSON></a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Papal_bull\" title=\"Papal bull\">papal bull</a> <i><a href=\"https://wikipedia.org/wiki/Decet_Romanum_Pontificem\" title=\"Decet Romanum Pontificem\">Decet Romanum Pontificem</a></i>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Papal bull", "link": "https://wikipedia.org/wiki/Papal_bull"}, {"title": "Decet Romanum Pontificem", "link": "https://wikipedia.org/wiki/Decet_Romanum_Pontificem"}]}, {"year": "1653", "text": "By the Coonan Cross Oath, the Eastern Church in India cuts itself off from colonial Portuguese tutelage.", "html": "1653 - By the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>n_Cross_Oath\" title=\"Coonan Cross Oath\"><PERSON><PERSON><PERSON> Oath</a>, the <a href=\"https://wikipedia.org/wiki/Eastern_Christianity\" title=\"Eastern Christianity\">Eastern Church</a> in India cuts itself off from colonial Portuguese tutelage.", "no_year_html": "By the <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Cross_Oath\" title=\"Coonan Cross Oath\">Coonan Cross Oath</a>, the <a href=\"https://wikipedia.org/wiki/Eastern_Christianity\" title=\"Eastern Christianity\">Eastern Church</a> in India cuts itself off from colonial Portuguese tutelage.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Eastern Christianity", "link": "https://wikipedia.org/wiki/Eastern_Christianity"}]}, {"year": "1749", "text": "Benning Wentworth issues the first of the New Hampshire Grants, leading to the establishment of Vermont.", "html": "1749 - <a href=\"https://wikipedia.org/wiki/Benning_<PERSON>\" title=\"Benning Wentworth\">Benning <PERSON></a> issues the first of the <a href=\"https://wikipedia.org/wiki/New_Hampshire_Grants\" title=\"New Hampshire Grants\">New Hampshire Grants</a>, leading to the establishment of <a href=\"https://wikipedia.org/wiki/Vermont\" title=\"Vermont\">Vermont</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ning_<PERSON>\" title=\"Benning Wentworth\">Benning <PERSON></a> issues the first of the <a href=\"https://wikipedia.org/wiki/New_Hampshire_Grants\" title=\"New Hampshire Grants\">New Hampshire Grants</a>, leading to the establishment of <a href=\"https://wikipedia.org/wiki/Vermont\" title=\"Vermont\">Vermont</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "New Hampshire Grants", "link": "https://wikipedia.org/wiki/New_Hampshire_Grants"}, {"title": "Vermont", "link": "https://wikipedia.org/wiki/Vermont"}]}, {"year": "1749", "text": "The first issue of Berlingske, Denmark's oldest continually operating newspaper, is published.", "html": "1749 - The first issue of <i><a href=\"https://wikipedia.org/wiki/Berlingske\" title=\"Berlingske\">Berlingske</a></i>, Denmark's oldest continually operating newspaper, is published.", "no_year_html": "The first issue of <i><a href=\"https://wikipedia.org/wiki/Berlingske\" title=\"Berlingske\">Berlingske</a></i>, Denmark's oldest continually operating newspaper, is published.", "links": [{"title": "Berlingske", "link": "https://wikipedia.org/wiki/Berlingske"}]}, {"year": "1777", "text": "American Revolutionary War: American forces under General <PERSON> defeat British forces at the Battle of Princeton, helping boost patriot morale.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: American forces under General <a href=\"https://wikipedia.org/wiki/George_<PERSON>\" title=\"George <PERSON>\"><PERSON></a> defeat British forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Princeton\" title=\"Battle of Princeton\">Battle of Princeton</a>, helping boost <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">patriot</a> morale.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: American forces under General <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"George Washington\"><PERSON></a> defeat British forces at the <a href=\"https://wikipedia.org/wiki/Battle_of_Princeton\" title=\"Battle of Princeton\">Battle of Princeton</a>, helping boost <a href=\"https://wikipedia.org/wiki/Patriot_(American_Revolution)\" title=\"Patriot (American Revolution)\">patriot</a> morale.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "Battle of Princeton", "link": "https://wikipedia.org/wiki/Battle_of_Princeton"}, {"title": "Patriot (American Revolution)", "link": "https://wikipedia.org/wiki/Patriot_(American_Revolution)"}]}, {"year": "1815", "text": "Austria, the United Kingdom, and France form a secret defensive alliance against Prussia and Russia.", "html": "1815 - Austria, the United Kingdom, and France form a secret defensive alliance against <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> and Russia.", "no_year_html": "Austria, the United Kingdom, and France form a secret defensive alliance against <a href=\"https://wikipedia.org/wiki/Prussia\" title=\"Prussia\">Prussia</a> and Russia.", "links": [{"title": "Prussia", "link": "https://wikipedia.org/wiki/Prussia"}]}, {"year": "1833", "text": "Captain <PERSON>, in the Clio, reasserts British sovereignty over the Falkland Islands.", "html": "1833 - Captain <PERSON>, in the <i>Clio</i>, <a href=\"https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)\" title=\"Reassertion of British sovereignty over the Falkland Islands (1833)\">reasserts British sovereignty over the Falkland Islands</a>.", "no_year_html": "Captain <PERSON>, in the <i>Clio</i>, <a href=\"https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)\" title=\"Reassertion of British sovereignty over the Falkland Islands (1833)\">reasserts British sovereignty over the Falkland Islands</a>.", "links": [{"title": "Reassertion of British sovereignty over the Falkland Islands (1833)", "link": "https://wikipedia.org/wiki/Reassertion_of_British_sovereignty_over_the_Falkland_Islands_(1833)"}]}, {"year": "1848", "text": "<PERSON> is sworn in as the first president of Liberia.", "html": "1848 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">president of Liberia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> is sworn in as the first <a href=\"https://wikipedia.org/wiki/President_of_Liberia\" title=\"President of Liberia\">president of Liberia</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Liberia", "link": "https://wikipedia.org/wiki/President_of_Liberia"}]}, {"year": "1861", "text": "American Civil War: Delaware votes not to secede from the United States.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> votes not to secede from the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Delaware\" title=\"Delaware\">Delaware</a> votes not to secede from the United States.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Delaware", "link": "https://wikipedia.org/wiki/Delaware"}]}, {"year": "1868", "text": "Meiji Restoration in Japan: The Tokugawa shogunate is abolished; agents of Satsuma and Chōshū seize power.", "html": "1868 - <a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a> in Japan: The <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> is abolished; agents of Satsuma and Chōshū seize power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Meiji_Restoration\" title=\"Meiji Restoration\">Meiji Restoration</a> in Japan: The <a href=\"https://wikipedia.org/wiki/Tokugawa_shogunate\" title=\"Tokugawa shogunate\">Tokugawa shogunate</a> is abolished; agents of Satsuma and Chōshū seize power.", "links": [{"title": "Meiji Restoration", "link": "https://wikipedia.org/wiki/Meiji_Restoration"}, {"title": "Tokugawa shogunate", "link": "https://wikipedia.org/wiki/Tokugawa_shogunate"}]}, {"year": "1870", "text": "Construction work begins on the Brooklyn Bridge in New York, United States.", "html": "1870 - Construction work begins on the <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> in New York, United States.", "no_year_html": "Construction work begins on the <a href=\"https://wikipedia.org/wiki/Brooklyn_Bridge\" title=\"Brooklyn Bridge\">Brooklyn Bridge</a> in New York, United States.", "links": [{"title": "Brooklyn Bridge", "link": "https://wikipedia.org/wiki/Brooklyn_Bridge"}]}, {"year": "1871", "text": "In the Battle of Bapaume, an engagement in the Franco-Prussian War, General <PERSON>'s forces bring about a Prussian retreat.", "html": "1871 - In the <a href=\"https://wikipedia.org/wiki/Battle_of_Bapaume_(1871)\" title=\"Battle of Bapaume (1871)\">Battle of Bapaume</a>, an engagement in the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>, General <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces bring about a Prussian retreat.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Battle_of_Bapaume_(1871)\" title=\"Battle of Bapaume (1871)\">Battle of Bapaume</a>, an engagement in the <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>, General <a href=\"https://wikipedia.org/wiki/Louis<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s forces bring about a Prussian retreat.", "links": [{"title": "Battle of Bapaume (1871)", "link": "https://wikipedia.org/wiki/Battle_of_Bapaume_(1871)"}, {"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>"}]}, {"year": "1885", "text": "Sino-French War: Beginning of the Battle of Núi Bop.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: Beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop\" title=\"Battle of Núi Bop\">Battle of Núi Bop</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sino-French_War\" title=\"Sino-French War\">Sino-French War</a>: Beginning of the <a href=\"https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop\" title=\"Battle of Núi Bop\">Battle of Núi Bop</a>.", "links": [{"title": "Sino-French War", "link": "https://wikipedia.org/wiki/Sino-French_War"}, {"title": "Battle of Núi Bop", "link": "https://wikipedia.org/wiki/Battle_of_N%C3%BAi_Bop"}]}, {"year": "1911", "text": "A magnitude 7.7 earthquake destroys the city of Almaty in Russian Turkestan.", "html": "1911 - A <a href=\"https://wikipedia.org/wiki/1911_Kebin_earthquake\" title=\"1911 Kebin earthquake\">magnitude 7.7 earthquake</a> destroys the city of <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a> in <a href=\"https://wikipedia.org/wiki/Russian_Turkestan\" title=\"Russian Turkestan\">Russian Turkestan</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/1911_Kebin_earthquake\" title=\"1911 Kebin earthquake\">magnitude 7.7 earthquake</a> destroys the city of <a href=\"https://wikipedia.org/wiki/Almaty\" title=\"Almaty\">Almaty</a> in <a href=\"https://wikipedia.org/wiki/Russian_Turkestan\" title=\"Russian Turkestan\">Russian Turkestan</a>.", "links": [{"title": "1911 Kebin earthquake", "link": "https://wikipedia.org/wiki/1911_Kebin_earthquake"}, {"title": "Almaty", "link": "https://wikipedia.org/wiki/Almaty"}, {"title": "Russian Turkestan", "link": "https://wikipedia.org/wiki/Russian_Turkestan"}]}, {"year": "1911", "text": "A gun battle in the East End of London leaves two dead. It sparked a political row over the involvement of then-Home Secretary <PERSON>.", "html": "1911 - A <a href=\"https://wikipedia.org/wiki/Siege_of_Sidney_Street\" title=\"Siege of Sidney Street\">gun battle</a> in the <a href=\"https://wikipedia.org/wiki/East_End_of_London\" title=\"East End of London\">East End of London</a> leaves two dead. It sparked a political row over the involvement of then-<a href=\"https://wikipedia.org/wiki/Home_Secretary\" title=\"Home Secretary\">Home Secretary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Siege_of_Sidney_Street\" title=\"Siege of Sidney Street\">gun battle</a> in the <a href=\"https://wikipedia.org/wiki/East_End_of_London\" title=\"East End of London\">East End of London</a> leaves two dead. It sparked a political row over the involvement of then-<a href=\"https://wikipedia.org/wiki/Home_Secretary\" title=\"Home Secretary\">Home Secretary</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>.", "links": [{"title": "Siege of Sidney Street", "link": "https://wikipedia.org/wiki/Siege_of_Sidney_Street"}, {"title": "East End of London", "link": "https://wikipedia.org/wiki/East_End_of_London"}, {"title": "Home Secretary", "link": "https://wikipedia.org/wiki/Home_Secretary"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1913", "text": "An Atlantic coast storm sets the lowest confirmed barometric pressure reading (955.0 mb (28.20 inHg)) for a non-tropical system in the continental United States.", "html": "1913 - An <a href=\"https://wikipedia.org/wiki/January_1913_Atlantic_coast_storm\" title=\"January 1913 Atlantic coast storm\">Atlantic coast storm</a> sets the lowest confirmed <a href=\"https://wikipedia.org/wiki/Barometric_pressure\" class=\"mw-redirect\" title=\"Barometric pressure\">barometric pressure</a> reading (955.0 mb (28.20 inHg)) for a <a href=\"https://wikipedia.org/wiki/Extratropical_cyclone\" title=\"Extratropical cyclone\">non-tropical system</a> in the <a href=\"https://wikipedia.org/wiki/Contiguous_United_States#CONUS_and_OCONUS\" title=\"Contiguous United States\">continental United States</a>.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/January_1913_Atlantic_coast_storm\" title=\"January 1913 Atlantic coast storm\">Atlantic coast storm</a> sets the lowest confirmed <a href=\"https://wikipedia.org/wiki/Barometric_pressure\" class=\"mw-redirect\" title=\"Barometric pressure\">barometric pressure</a> reading (955.0 mb (28.20 inHg)) for a <a href=\"https://wikipedia.org/wiki/Extratropical_cyclone\" title=\"Extratropical cyclone\">non-tropical system</a> in the <a href=\"https://wikipedia.org/wiki/Contiguous_United_States#CONUS_and_OCONUS\" title=\"Contiguous United States\">continental United States</a>.", "links": [{"title": "January 1913 Atlantic coast storm", "link": "https://wikipedia.org/wiki/January_1913_Atlantic_coast_storm"}, {"title": "Barometric pressure", "link": "https://wikipedia.org/wiki/Barometric_pressure"}, {"title": "Extratropical cyclone", "link": "https://wikipedia.org/wiki/Extratropical_cyclone"}, {"title": "Contiguous United States", "link": "https://wikipedia.org/wiki/Contiguous_United_States#CONUS_and_OCONUS"}]}, {"year": "1913", "text": "First Balkan War: Greece completes its capture of the eastern Aegean island of Chios, as the last Ottoman forces on the island surrender.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: Greece completes its <a href=\"https://wikipedia.org/wiki/Battle_of_Chios_(1912)\" title=\"Battle of Chios (1912)\">capture</a> of the eastern Aegean island of <a href=\"https://wikipedia.org/wiki/Chios\" title=\"Chios\">Chios</a>, as the last Ottoman forces on the island surrender.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Balkan_War\" title=\"First Balkan War\">First Balkan War</a>: Greece completes its <a href=\"https://wikipedia.org/wiki/Battle_of_Chios_(1912)\" title=\"Battle of Chios (1912)\">capture</a> of the eastern Aegean island of <a href=\"https://wikipedia.org/wiki/Chios\" title=\"Chios\">Chios</a>, as the last Ottoman forces on the island surrender.", "links": [{"title": "First Balkan War", "link": "https://wikipedia.org/wiki/First_Balkan_War"}, {"title": "Battle of Chios (1912)", "link": "https://wikipedia.org/wiki/Battle_of_Chios_(1912)"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Chios"}]}, {"year": "1920", "text": "Over 640 are killed after a magnitude 6.4 earthquake strikes the Mexican states Puebla and Veracruz.", "html": "1920 - Over 640 are killed after a <a href=\"https://wikipedia.org/wiki/1920_Xalapa_earthquake\" title=\"1920 Xalapa earthquake\">magnitude 6.4 earthquake</a> strikes the Mexican states Puebla and Veracruz.", "no_year_html": "Over 640 are killed after a <a href=\"https://wikipedia.org/wiki/1920_Xalapa_earthquake\" title=\"1920 Xalapa earthquake\">magnitude 6.4 earthquake</a> strikes the Mexican states Puebla and Veracruz.", "links": [{"title": "1920 Xalapa earthquake", "link": "https://wikipedia.org/wiki/1920_Xalapa_earthquake"}]}, {"year": "1933", "text": "<PERSON><PERSON> becomes the first woman elected as Speaker of the North Dakota House of Representatives, the first woman to hold a Speaker position anywhere in the United States.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman elected as <a href=\"https://wikipedia.org/wiki/Speaker_of_the_North_Dakota_House_of_Representatives\" class=\"mw-redirect\" title=\"Speaker of the North Dakota House of Representatives\">Speaker of the North Dakota House of Representatives</a>, the first woman to hold a <a href=\"https://wikipedia.org/wiki/Speaker_(politics)\" title=\"Speaker (politics)\">Speaker</a> position anywhere in the United States.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman elected as <a href=\"https://wikipedia.org/wiki/Speaker_of_the_North_Dakota_House_of_Representatives\" class=\"mw-redirect\" title=\"Speaker of the North Dakota House of Representatives\">Speaker of the North Dakota House of Representatives</a>, the first woman to hold a <a href=\"https://wikipedia.org/wiki/Speaker_(politics)\" title=\"Speaker (politics)\">Speaker</a> position anywhere in the United States.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the North Dakota House of Representatives", "link": "https://wikipedia.org/wiki/Speaker_of_the_North_Dakota_House_of_Representatives"}, {"title": "Speaker (politics)", "link": "https://wikipedia.org/wiki/Speaker_(politics)"}]}, {"year": "1944", "text": "World War II: US flying ace Major <PERSON> \"<PERSON>\" <PERSON> is shot down in his Vought F4U Corsair by Captain <PERSON><PERSON><PERSON><PERSON> flying a Mitsubishi A6M Zero.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: US flying ace Major <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> \"<PERSON>\" <PERSON></a> is shot down in his <a href=\"https://wikipedia.org/wiki/Vought_F4U_Corsair\" title=\"Vought F4U Corsair\">Vought F4U Corsair</a> by Captain <a href=\"https://wikipedia.org/wiki/List_of_World_War_II_aces_from_Japan\" title=\"List of World War II aces from Japan\"><PERSON><PERSON><PERSON><PERSON></a> flying a <a href=\"https://wikipedia.org/wiki/Mitsubishi_A6M_Zero\" title=\"Mitsubishi A6M Zero\">Mitsubishi A6M Zero</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: US flying ace Major <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON> \"<PERSON>\" <PERSON></a> is shot down in his <a href=\"https://wikipedia.org/wiki/Vought_F4U_Corsair\" title=\"Vought F4U Corsair\">Vought F4U Corsair</a> by Captain <a href=\"https://wikipedia.org/wiki/List_of_World_War_II_aces_from_Japan\" title=\"List of World War II aces from Japan\"><PERSON><PERSON><PERSON><PERSON></a> flying a <a href=\"https://wikipedia.org/wiki/Mitsubishi_A6M_Zero\" title=\"Mitsubishi A6M Zero\">Mitsubishi A6M Zero</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Vought F4U Corsair", "link": "https://wikipedia.org/wiki/Vought_F4U_Corsair"}, {"title": "List of World War II aces from Japan", "link": "https://wikipedia.org/wiki/List_of_World_War_II_aces_from_Japan"}, {"title": "Mitsubishi A6M Zero", "link": "https://wikipedia.org/wiki/Mitsubishi_A6M_Zero"}]}, {"year": "1946", "text": "Popular Canadian American jockey <PERSON> suffers a concussion during a freak racing accident; he dies from the injury the following day. The annual <PERSON> Memorial Jockey Award is created to honor him.", "html": "1946 - Popular <a href=\"https://wikipedia.org/wiki/Canadian_American\" class=\"mw-redirect\" title=\"Canadian American\">Canadian American</a> <a href=\"https://wikipedia.org/wiki/Jockey\" title=\"<PERSON><PERSON>\">jockey</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a concussion during a freak racing accident; he dies from the injury the following day. The annual <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Memorial_Jockey_Award\" title=\"<PERSON> Memorial Jockey Award\"><PERSON> Memorial Jockey Award</a> is created to honor him.", "no_year_html": "Popular <a href=\"https://wikipedia.org/wiki/Canadian_American\" class=\"mw-redirect\" title=\"Canadian American\">Canadian American</a> <a href=\"https://wikipedia.org/wiki/Jockey\" title=\"<PERSON><PERSON>\">jockey</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> suffers a concussion during a freak racing accident; he dies from the injury the following day. The annual <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Memorial_Jockey_Award\" title=\"George <PERSON> Memorial Jockey Award\"><PERSON> Memorial Jockey Award</a> is created to honor him.", "links": [{"title": "Canadian American", "link": "https://wikipedia.org/wiki/Canadian_American"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jo<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>f"}, {"title": "<PERSON> Memorial Jockey Award", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_Memorial_Jockey_Award"}]}, {"year": "1947", "text": "Proceedings of the U.S. Congress are televised for the first time.", "html": "1947 - Proceedings of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> are televised for the first time.", "no_year_html": "Proceedings of the <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">U.S. Congress</a> are televised for the first time.", "links": [{"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1949", "text": "The Bangko Sentral ng Pilipinas, the central bank of the Philippines, is established.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Bangko_Sentral_ng_Pilipinas\" title=\"Bangko Sentral ng Pilipinas\">Bangko Sentral ng Pilipinas</a>, the <a href=\"https://wikipedia.org/wiki/Central_bank\" title=\"Central bank\">central bank</a> of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, is established.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Bangko_Sentral_ng_Pilipinas\" title=\"Bangko Sentral ng Pilipinas\">Bangko Sentral ng Pilipinas</a>, the <a href=\"https://wikipedia.org/wiki/Central_bank\" title=\"Central bank\">central bank</a> of the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, is established.", "links": [{"title": "Bangko Sentral ng Pilipinas", "link": "https://wikipedia.org/wiki/Bangko_Sentral_ng_Pilipinas"}, {"title": "Central bank", "link": "https://wikipedia.org/wiki/Central_bank"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "1953", "text": "<PERSON> and her son, <PERSON> from Ohio, become the first mother and son to serve simultaneously in the U.S. Congress.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, become the first mother and son to serve simultaneously in the U.S. Congress.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and her son, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Ohio\" title=\"Ohio\">Ohio</a>, become the first mother and son to serve simultaneously in the U.S. Congress.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Ohio", "link": "https://wikipedia.org/wiki/Ohio"}]}, {"year": "1956", "text": "A fire damages the top part of the Eiffel Tower.", "html": "1956 - A fire damages the top part of the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a>.", "no_year_html": "A fire damages the top part of the <a href=\"https://wikipedia.org/wiki/Eiffel_Tower\" title=\"Eiffel Tower\">Eiffel Tower</a>.", "links": [{"title": "Eiffel Tower", "link": "https://wikipedia.org/wiki/Eiffel_Tower"}]}, {"year": "1957", "text": "The Hamilton Watch Company introduces the first electric watch.", "html": "1957 - The <a href=\"https://wikipedia.org/wiki/Hamilton_Watch_Company\" title=\"Hamilton Watch Company\">Hamilton Watch Company</a> introduces the first <a href=\"https://wikipedia.org/wiki/Electric_watch\" title=\"Electric watch\">electric watch</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Hamilton_Watch_Company\" title=\"Hamilton Watch Company\">Hamilton Watch Company</a> introduces the first <a href=\"https://wikipedia.org/wiki/Electric_watch\" title=\"Electric watch\">electric watch</a>.", "links": [{"title": "Hamilton Watch Company", "link": "https://wikipedia.org/wiki/Hamilton_Watch_Company"}, {"title": "Electric watch", "link": "https://wikipedia.org/wiki/Electric_watch"}]}, {"year": "1958", "text": "The West Indies Federation is formed.", "html": "1958 - The <a href=\"https://wikipedia.org/wiki/West_Indies_Federation\" title=\"West Indies Federation\">West Indies Federation</a> is formed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/West_Indies_Federation\" title=\"West Indies Federation\">West Indies Federation</a> is formed.", "links": [{"title": "West Indies Federation", "link": "https://wikipedia.org/wiki/West_Indies_Federation"}]}, {"year": "1959", "text": "Alaska is admitted as the 49th U.S. state.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a> is admitted as the 49th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a> is admitted as the 49th <a href=\"https://wikipedia.org/wiki/U.S._state\" title=\"U.S. state\">U.S. state</a>.", "links": [{"title": "Alaska", "link": "https://wikipedia.org/wiki/Alaska"}, {"title": "U.S. state", "link": "https://wikipedia.org/wiki/U.S._state"}]}, {"year": "1961", "text": "Cold War: After a series of economic retaliations against one another, the United States severs diplomatic relations with Cuba.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: After a series of economic retaliations against one another, the United States severs diplomatic relations with <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: After a series of economic retaliations against one another, the United States severs diplomatic relations with <a href=\"https://wikipedia.org/wiki/Cuba\" title=\"Cuba\">Cuba</a>.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Cuba", "link": "https://wikipedia.org/wiki/Cuba"}]}, {"year": "1961", "text": "The SL-1 nuclear reactor, near Idaho Falls, Idaho, is destroyed by a steam explosion in the only reactor incident in the United States to cause immediate fatalities.", "html": "1961 - The <a href=\"https://wikipedia.org/wiki/SL-1\" title=\"SL-1\">SL-1</a> nuclear reactor, near <a href=\"https://wikipedia.org/wiki/Idaho_Falls,_Idaho\" title=\"Idaho Falls, Idaho\">Idaho Falls, Idaho</a>, is destroyed by a <a href=\"https://wikipedia.org/wiki/Steam_explosion\" title=\"Steam explosion\">steam explosion</a> in the only reactor incident in the United States to cause immediate fatalities.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/SL-1\" title=\"SL-1\">SL-1</a> nuclear reactor, near <a href=\"https://wikipedia.org/wiki/Idaho_Falls,_Idaho\" title=\"Idaho Falls, Idaho\">Idaho Falls, Idaho</a>, is destroyed by a <a href=\"https://wikipedia.org/wiki/Steam_explosion\" title=\"Steam explosion\">steam explosion</a> in the only reactor incident in the United States to cause immediate fatalities.", "links": [{"title": "SL-1", "link": "https://wikipedia.org/wiki/SL-1"}, {"title": "Idaho Falls, Idaho", "link": "https://wikipedia.org/wiki/Idaho_Falls,_Idaho"}, {"title": "Steam explosion", "link": "https://wikipedia.org/wiki/Steam_explosion"}]}, {"year": "1961", "text": "A protest by agricultural workers in Baixa de Cassanje, Portuguese Angola, turns into a revolt, opening the Angolan War of Independence, the first of the Portuguese Colonial Wars.", "html": "1961 - A protest by agricultural workers in <a href=\"https://wikipedia.org/wiki/Baixa_de_Cassanje\" title=\"Baixa de Cassanje\">Baixa de Cassanje</a>, <a href=\"https://wikipedia.org/wiki/Portuguese_Angola\" title=\"Portuguese Angola\">Portuguese Angola</a>, <a href=\"https://wikipedia.org/wiki/Baixa_de_Cassanje_revolt\" class=\"mw-redirect\" title=\"Baixa de Cassanje revolt\">turns into a revolt</a>, opening the <a href=\"https://wikipedia.org/wiki/Angolan_War_of_Independence\" title=\"Angolan War of Independence\">Angolan War of Independence</a>, the first of the <a href=\"https://wikipedia.org/wiki/Portuguese_Colonial_War\" title=\"Portuguese Colonial War\">Portuguese Colonial Wars</a>.", "no_year_html": "A protest by agricultural workers in <a href=\"https://wikipedia.org/wiki/Baixa_de_Cassanje\" title=\"Baixa de Cassanje\">Baixa de Cassanje</a>, <a href=\"https://wikipedia.org/wiki/Portuguese_Angola\" title=\"Portuguese Angola\">Portuguese Angola</a>, <a href=\"https://wikipedia.org/wiki/Baixa_de_Cassanje_revolt\" class=\"mw-redirect\" title=\"Baixa de Cassanje revolt\">turns into a revolt</a>, opening the <a href=\"https://wikipedia.org/wiki/Angolan_War_of_Independence\" title=\"Angolan War of Independence\">Angolan War of Independence</a>, the first of the <a href=\"https://wikipedia.org/wiki/Portuguese_Colonial_War\" title=\"Portuguese Colonial War\">Portuguese Colonial Wars</a>.", "links": [{"title": "<PERSON><PERSON> de Cassanje", "link": "https://wikipedia.org/wiki/Baixa_de_Cassanje"}, {"title": "Portuguese Angola", "link": "https://wikipedia.org/wiki/Portuguese_Angola"}, {"title": "Baixa de Cassanje revolt", "link": "https://wikipedia.org/wiki/Baixa_de_Cassanje_revolt"}, {"title": "Angolan War of Independence", "link": "https://wikipedia.org/wiki/Angolan_War_of_Independence"}, {"title": "Portuguese Colonial War", "link": "https://wikipedia.org/wiki/Portuguese_Colonial_War"}]}, {"year": "1961", "text": "Aero Flight 311 crashes into the forest in Kvevlax, Finland, killing 25 people.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Aero_Flight_311\" title=\"Aero Flight 311\">Aero Flight 311</a> crashes into the forest in <a href=\"https://wikipedia.org/wiki/Kvevlax\" title=\"Kvevlax\">Kvevlax, Finland</a>, killing 25 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aero_Flight_311\" title=\"Aero Flight 311\">Aero Flight 311</a> crashes into the forest in <a href=\"https://wikipedia.org/wiki/Kvevlax\" title=\"Kvevlax\">Kvevlax, Finland</a>, killing 25 people.", "links": [{"title": "Aero Flight 311", "link": "https://wikipedia.org/wiki/Aero_Flight_311"}, {"title": "Kvevlax", "link": "https://wikipedia.org/wiki/Kvevlax"}]}, {"year": "1962", "text": "<PERSON> excommunicates <PERSON><PERSON>.", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\">Pope John XXIII</a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_XXIII\" title=\"Pope John XXIII\">Pope John XXIII</a> excommunicates <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>III"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "The International Covenant on Economic, Social and Cultural Rights, adopted by the United Nations General Assembly, comes into force.", "html": "1976 - The <a href=\"https://wikipedia.org/wiki/International_Covenant_on_Economic,_Social_and_Cultural_Rights\" title=\"International Covenant on Economic, Social and Cultural Rights\">International Covenant on Economic, Social and Cultural Rights</a>, adopted by the United Nations General Assembly, comes into force.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/International_Covenant_on_Economic,_Social_and_Cultural_Rights\" title=\"International Covenant on Economic, Social and Cultural Rights\">International Covenant on Economic, Social and Cultural Rights</a>, adopted by the United Nations General Assembly, comes into force.", "links": [{"title": "International Covenant on Economic, Social and Cultural Rights", "link": "https://wikipedia.org/wiki/International_Covenant_on_Economic,_Social_and_Cultural_Rights"}]}, {"year": "1977", "text": "Apple Computer is incorporated.", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple Computer</a> is incorporated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apple_Computer\" class=\"mw-redirect\" title=\"Apple Computer\">Apple Computer</a> is incorporated.", "links": [{"title": "Apple Computer", "link": "https://wikipedia.org/wiki/Apple_Computer"}]}, {"year": "1987", "text": "Varig Flight 797 crashes near Akouré in the Ivory Coast, resulting in 50 deaths.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Varig_Flight_797\" title=\"Varig Flight 797\">Varig Flight 797</a> crashes near <a href=\"https://wikipedia.org/wiki/Akour%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, resulting in 50 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Varig_Flight_797\" title=\"Varig Flight 797\">Varig Flight 797</a> crashes near <a href=\"https://wikipedia.org/wiki/Akour%C3%A9\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/Ivory_Coast\" title=\"Ivory Coast\">Ivory Coast</a>, resulting in 50 deaths.", "links": [{"title": "Varig Flight 797", "link": "https://wikipedia.org/wiki/Varig_Flight_797"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Akour%C3%A9"}, {"title": "Ivory Coast", "link": "https://wikipedia.org/wiki/Ivory_Coast"}]}, {"year": "1990", "text": "United States invasion of Panama: <PERSON>, former leader of Panama, surrenders to American forces.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Panama\" title=\"United States invasion of Panama\">United States invasion of Panama</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former leader of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, surrenders to American forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_invasion_of_Panama\" title=\"United States invasion of Panama\">United States invasion of Panama</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, former leader of <a href=\"https://wikipedia.org/wiki/Panama\" title=\"Panama\">Panama</a>, surrenders to American forces.", "links": [{"title": "United States invasion of Panama", "link": "https://wikipedia.org/wiki/United_States_invasion_of_Panama"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Panama", "link": "https://wikipedia.org/wiki/Panama"}]}, {"year": "1992", "text": "CommutAir Flight 4821 crashes on approach to Adirondack Regional Airport, in Saranac Lake, New York, killing two people.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/CommutAir_Flight_4821\" title=\"CommutAir Flight 4821\">CommutAir Flight 4821</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Adirondack_Regional_Airport\" title=\"Adirondack Regional Airport\">Adirondack Regional Airport</a>, in <a href=\"https://wikipedia.org/wiki/Saranac_Lake,_New_York\" title=\"Saranac Lake, New York\">Saranac Lake, New York</a>, killing two people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CommutAir_Flight_4821\" title=\"CommutAir Flight 4821\">CommutAir Flight 4821</a> crashes on approach to <a href=\"https://wikipedia.org/wiki/Adirondack_Regional_Airport\" title=\"Adirondack Regional Airport\">Adirondack Regional Airport</a>, in <a href=\"https://wikipedia.org/wiki/Saranac_Lake,_New_York\" title=\"Saranac Lake, New York\">Saranac Lake, New York</a>, killing two people.", "links": [{"title": "CommutAir Flight 4821", "link": "https://wikipedia.org/wiki/CommutAir_Flight_4821"}, {"title": "Adirondack Regional Airport", "link": "https://wikipedia.org/wiki/Adirondack_Regional_Airport"}, {"title": "Saranac Lake, New York", "link": "https://wikipedia.org/wiki/Saranac_Lake,_New_York"}]}, {"year": "1993", "text": "In Moscow, Russia, <PERSON> and <PERSON> sign the second Strategic Arms Reduction Treaty (START).", "html": "1993 - In Moscow, Russia, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>._<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the second <a href=\"https://wikipedia.org/wiki/START_II\" title=\"START II\">Strategic Arms Reduction Treaty</a> (START).", "no_year_html": "In Moscow, Russia, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> sign the second <a href=\"https://wikipedia.org/wiki/START_II\" title=\"START II\">Strategic Arms Reduction Treaty</a> (START).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "START II", "link": "https://wikipedia.org/wiki/START_II"}]}, {"year": "1994", "text": "Baikal Airlines Flight 130 crashes near Mamoney, Irkutsk, Russia, resulting in 125 deaths.", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Baikal_Airlines_Flight_130\" title=\"Baikal Airlines Flight 130\">Baikal Airlines Flight 130</a> crashes near <a href=\"https://wikipedia.org/wiki/Mamony_(Irkutsk_district)\" class=\"mw-redirect\" title=\"Mamony (Irkutsk district)\">Ma<PERSON>ey</a>, <a href=\"https://wikipedia.org/wiki/Irkutsk\" title=\"Irkutsk\">Irkutsk</a>, Russia, resulting in 125 deaths.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Baikal_Airlines_Flight_130\" title=\"Baikal Airlines Flight 130\">Baikal Airlines Flight 130</a> crashes near <a href=\"https://wikipedia.org/wiki/Ma<PERSON>_(Irkutsk_district)\" class=\"mw-redirect\" title=\"Mamony (Irkutsk district)\">Ma<PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irkutsk\" title=\"Irkutsk\">Irkutsk</a>, Russia, resulting in 125 deaths.", "links": [{"title": "Baikal Airlines Flight 130", "link": "https://wikipedia.org/wiki/Baikal_Airlines_Flight_130"}, {"title": "Mamony (Irkutsk district)", "link": "https://wikipedia.org/wiki/Ma<PERSON>_(Irkutsk_district)"}, {"title": "Irkutsk", "link": "https://wikipedia.org/wiki/Irkutsk"}]}, {"year": "1999", "text": "The Mars Polar Lander is launched by NASA.", "html": "1999 - The <i><a href=\"https://wikipedia.org/wiki/Mars_Polar_Lander\" title=\"Mars Polar Lander\">Mars Polar Lander</a></i> is launched by NASA.", "no_year_html": "The <i><a href=\"https://wikipedia.org/wiki/Mars_Polar_Lander\" title=\"Mars Polar Lander\">Mars Polar Lander</a></i> is launched by NASA.", "links": [{"title": "Mars Polar Lander", "link": "https://wikipedia.org/wiki/Mars_Polar_Lander"}]}, {"year": "2002", "text": "Israeli-Palestinian conflict: Israeli forces seize the Palestinian freighter Karine A in the Red Sea, finding 50 tons of weapons.", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: Israeli forces seize the Palestinian freighter <i><a href=\"https://wikipedia.org/wiki/Karin<PERSON>_A_Affair\" class=\"mw-redirect\" title=\"Karine A Affair\"><PERSON><PERSON></a></i> in the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, finding 50 tons of weapons.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict\" title=\"Israeli-Palestinian conflict\">Israeli-Palestinian conflict</a>: Israeli forces seize the Palestinian freighter <i><a href=\"https://wikipedia.org/wiki/Karine_A_Affair\" class=\"mw-redirect\" title=\"Karine A Affair\">Karin<PERSON></a></i> in the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, finding 50 tons of weapons.", "links": [{"title": "Israeli-Palestinian conflict", "link": "https://wikipedia.org/wiki/Israeli%E2%80%93Palestinian_conflict"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Red Sea", "link": "https://wikipedia.org/wiki/Red_Sea"}]}, {"year": "2004", "text": "Flash Airlines Flight 604 crashes into the Red Sea, resulting in 148 deaths, making it one of the deadliest aviation accidents in Egyptian history.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Flash_Airlines_Flight_604\" title=\"Flash Airlines Flight 604\">Flash Airlines Flight 604</a> crashes into the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, resulting in 148 deaths, making it one of the deadliest aviation accidents in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Flash_Airlines_Flight_604\" title=\"Flash Airlines Flight 604\">Flash Airlines Flight 604</a> crashes into the <a href=\"https://wikipedia.org/wiki/Red_Sea\" title=\"Red Sea\">Red Sea</a>, resulting in 148 deaths, making it one of the deadliest aviation accidents in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egyptian</a> history.", "links": [{"title": "Flash Airlines Flight 604", "link": "https://wikipedia.org/wiki/Flash_Airlines_Flight_604"}, {"title": "Red Sea", "link": "https://wikipedia.org/wiki/Red_Sea"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "2009", "text": "The first block of the blockchain of the decentralized payment system Bitcoin, called the Genesis block, is established by the creator of the system, <PERSON><PERSON>.", "html": "2009 - The first block of the blockchain of the decentralized <a href=\"https://wikipedia.org/wiki/Payment_system\" title=\"Payment system\">payment system</a> <a href=\"https://wikipedia.org/wiki/Bitcoin\" title=\"Bitcoin\">Bitcoin</a>, called the <i>Genesis block</i>, is established by the creator of the system, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "no_year_html": "The first block of the blockchain of the decentralized <a href=\"https://wikipedia.org/wiki/Payment_system\" title=\"Payment system\">payment system</a> <a href=\"https://wikipedia.org/wiki/Bitcoin\" title=\"Bitcoin\">Bitcoin</a>, called the <i>Genesis block</i>, is established by the creator of the system, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>.", "links": [{"title": "Payment system", "link": "https://wikipedia.org/wiki/Payment_system"}, {"title": "Bitcoin", "link": "https://wikipedia.org/wiki/Bitcoin"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "Boko Haram militants destroy the entire town of Baga in north-east Nigeria, starting the Baga massacre and killing as many as 2,000 people.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Boko_Haram\" title=\"Boko Haram\">Boko Haram</a> militants destroy the entire town of <a href=\"https://wikipedia.org/wiki/Baga,_Nigeria\" class=\"mw-redirect\" title=\"Baga, Nigeria\">Baga</a> in north-east <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, starting the <a href=\"https://wikipedia.org/wiki/2015_Baga_massacre\" title=\"2015 Baga massacre\">Baga massacre</a> and killing as many as 2,000 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Boko_Haram\" title=\"Boko Haram\">Boko Haram</a> militants destroy the entire town of <a href=\"https://wikipedia.org/wiki/Baga,_Nigeria\" class=\"mw-redirect\" title=\"Baga, Nigeria\">Baga</a> in north-east <a href=\"https://wikipedia.org/wiki/Nigeria\" title=\"Nigeria\">Nigeria</a>, starting the <a href=\"https://wikipedia.org/wiki/2015_Baga_massacre\" title=\"2015 Baga massacre\">Baga massacre</a> and killing as many as 2,000 people.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>m"}, {"title": "Baga, Nigeria", "link": "https://wikipedia.org/wiki/Baga,_Nigeria"}, {"title": "Nigeria", "link": "https://wikipedia.org/wiki/Nigeria"}, {"title": "2015 Baga massacre", "link": "https://wikipedia.org/wiki/2015_Baga_massacre"}]}, {"year": "2016", "text": "In response to the execution of <PERSON><PERSON><PERSON>, Iran ends its diplomatic relations with Saudi Arabia.", "html": "2016 - In response to the execution of <a href=\"https://wikipedia.org/wiki/Nimr_al-Nimr\" title=\"<PERSON>mr al-Nimr\">Nimr al-Nimr</a>, Iran ends its <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Saudi_Arabia_relations\" title=\"Iran-Saudi Arabia relations\">diplomatic relations</a> with Saudi Arabia.", "no_year_html": "In response to the execution of <a href=\"https://wikipedia.org/wiki/Nimr_al-Nimr\" title=\"<PERSON>mr al-Nimr\">Nimr al-Nimr</a>, Iran ends its <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Saudi_Arabia_relations\" title=\"Iran-Saudi Arabia relations\">diplomatic relations</a> with Saudi Arabia.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nim<PERSON>_al-Nimr"}, {"title": "Iran-Saudi Arabia relations", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Saudi_Arabia_relations"}]}, {"year": "2018", "text": "For the first time in history, all five major storm surge gates in the Netherlands are closed simultaneously in the wake of a storm.", "html": "2018 - For the first time in history, all five major storm surge gates in the Netherlands are closed simultaneously in the wake of a storm.", "no_year_html": "For the first time in history, all five major storm surge gates in the Netherlands are closed simultaneously in the wake of a storm.", "links": []}, {"year": "2019", "text": "Chang'e 4 makes the first soft landing on the far side of the Moon, deploying the Yutu-2 lunar rover.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/Chang%27e_4\" title=\"Chang'e 4\">Chang'e 4</a> makes the first soft landing on the <a href=\"https://wikipedia.org/wiki/Far_side_of_the_Moon\" title=\"Far side of the Moon\">far side of the Moon</a>, deploying the Yutu-2 <a href=\"https://wikipedia.org/wiki/Lunar_rover\" title=\"Lunar rover\">lunar rover</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chang%27e_4\" title=\"Chang'e 4\">Chang'e 4</a> makes the first soft landing on the <a href=\"https://wikipedia.org/wiki/Far_side_of_the_Moon\" title=\"Far side of the Moon\">far side of the Moon</a>, deploying the Yutu-2 <a href=\"https://wikipedia.org/wiki/Lunar_rover\" title=\"Lunar rover\">lunar rover</a>.", "links": [{"title": "Chang'e 4", "link": "https://wikipedia.org/wiki/Chang%27e_4"}, {"title": "Far side of the Moon", "link": "https://wikipedia.org/wiki/Far_side_of_the_Moon"}, {"title": "Lunar rover", "link": "https://wikipedia.org/wiki/Lunar_rover"}]}, {"year": "2020", "text": "Iranian General <PERSON><PERSON><PERSON> is killed by an American airstrike near Baghdad International Airport, igniting global concerns of a potential armed conflict.", "html": "2020 - Iranian General <a href=\"https://wikipedia.org/wiki/Qasem_Soleimani\" title=\"Qasem Soleimani\">Qasem Soleimani</a> is <a href=\"https://wikipedia.org/wiki/2020_Baghdad_International_Airport_airstrike\" class=\"mw-redirect\" title=\"2020 Baghdad International Airport airstrike\">killed by an American airstrike</a> near <a href=\"https://wikipedia.org/wiki/Baghdad_International_Airport\" title=\"Baghdad International Airport\">Baghdad International Airport</a>, igniting global concerns of a potential <a href=\"https://wikipedia.org/wiki/War\" title=\"War\">armed conflict</a>.", "no_year_html": "Iranian General <a href=\"https://wikipedia.org/wiki/Qasem_Soleimani\" title=\"Qasem Soleimani\">Qasem Soleimani</a> is <a href=\"https://wikipedia.org/wiki/2020_Baghdad_International_Airport_airstrike\" class=\"mw-redirect\" title=\"2020 Baghdad International Airport airstrike\">killed by an American airstrike</a> near <a href=\"https://wikipedia.org/wiki/Baghdad_International_Airport\" title=\"Baghdad International Airport\">Baghdad International Airport</a>, igniting global concerns of a potential <a href=\"https://wikipedia.org/wiki/War\" title=\"War\">armed conflict</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>asem_<PERSON>eimani"}, {"title": "2020 Baghdad International Airport airstrike", "link": "https://wikipedia.org/wiki/2020_Baghdad_International_Airport_airstrike"}, {"title": "Baghdad International Airport", "link": "https://wikipedia.org/wiki/Baghdad_International_Airport"}, {"title": "War", "link": "https://wikipedia.org/wiki/War"}]}, {"year": "2023", "text": "Singapore's Jurong Bird Park permanently closes.", "html": "2023 - Singapore's <a href=\"https://wikipedia.org/wiki/Jurong_Bird_Park\" title=\"Jurong Bird Park\">Jurong Bird Park</a> permanently closes.", "no_year_html": "Singapore's <a href=\"https://wikipedia.org/wiki/Jurong_Bird_Park\" title=\"Jurong Bird Park\">Jurong Bird Park</a> permanently closes.", "links": [{"title": "Jurong Bird Park", "link": "https://wikipedia.org/wiki/Jurong_Bird_Park"}]}, {"year": "2024", "text": "At least 91 people are killed in bombings in Kerman, Iran, during a ceremony commemorating the assassination of Iranian General <PERSON><PERSON><PERSON> four years earlier.", "html": "2024 - At least 91 people are killed in <a href=\"https://wikipedia.org/wiki/Kerman_bombings\" class=\"mw-redirect\" title=\"Kerman bombings\">bombings</a> in <a href=\"https://wikipedia.org/wiki/Kerman,_Iran\" class=\"mw-redirect\" title=\"Kerman, Iran\">Kerman, Iran</a>, during a ceremony commemorating the <a href=\"https://wikipedia.org/wiki/Assassination_of_Qasem_Soleimani\" title=\"Assassination of Qasem Soleimani\">assassination</a> of Iranian General <a href=\"https://wikipedia.org/wiki/Qasem_Soleimani\" title=\"Qasem Soleimani\">Qasem Soleimani</a> four years earlier.", "no_year_html": "At least 91 people are killed in <a href=\"https://wikipedia.org/wiki/Kerman_bombings\" class=\"mw-redirect\" title=\"Kerman bombings\">bombings</a> in <a href=\"https://wikipedia.org/wiki/Kerman,_Iran\" class=\"mw-redirect\" title=\"Kerman, Iran\">Kerman, Iran</a>, during a ceremony commemorating the <a href=\"https://wikipedia.org/wiki/Assassination_of_Qasem_Soleimani\" title=\"Assassination of Qasem Soleimani\">assassination</a> of Iranian General <a href=\"https://wikipedia.org/wiki/Qasem_Soleimani\" title=\"Qasem Soleimani\">Qasem Soleimani</a> four years earlier.", "links": [{"title": "Kerman bombings", "link": "https://wikipedia.org/wiki/Kerman_bombings"}, {"title": "Kerman, Iran", "link": "https://wikipedia.org/wiki/Kerman,_Iran"}, {"title": "Assassination of Qasem Soleimani", "link": "https://wikipedia.org/wiki/Assassination_of_Q<PERSON>m_Soleimani"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>asem_<PERSON>eimani"}]}], "Births": [{"year": "106 BC", "text": "<PERSON>, Roman philosopher, lawyer, and politician (d. 43 BC)", "html": "106 BC - 106 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\">Cicero</a>, Roman philosopher, lawyer, and politician (d. 43 BC)", "no_year_html": "106 BC - <a href=\"https://wikipedia.org/wiki/Cicero\" title=\"Cicero\">Cicero</a>, Roman philosopher, lawyer, and politician (d. 43 BC)", "links": [{"title": "Cicero", "link": "https://wikipedia.org/wiki/Cicero"}]}, {"year": "1509", "text": "<PERSON><PERSON>, Italian cardinal (d. 1591)", "html": "1509 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (d. 1591)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cardinal (d. 1591)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1611", "text": "<PERSON>, English political theorist (d. 1677)", "html": "1611 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English political theorist (d. 1677)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, English political theorist (d. 1677)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1698", "text": "<PERSON>, Italian poet and songwriter (d. 1782)", "html": "1698 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and songwriter (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian poet and songwriter (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>astasio"}]}, {"year": "1710", "text": "<PERSON>, American soldier and engineer (d. 1796)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and engineer (d. 1796)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON><PERSON>, Swedish biologist and explorer (d. 1752)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biologist and explorer (d. 1752)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish biologist and explorer (d. 1752)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1731", "text": "<PERSON>, Venetian admiral and statesman (d. 1792)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Venetian admiral and statesman (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1760", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian ruler (d. 1799)", "html": "1760 - <a href=\"https://wikipedia.org/wiki/Veerapandiya_Kattabomman\" title=\"Veerapandiya Kattabomman\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Veerapandiya_Kattabomman\" title=\"Veerapandiya Kattabomman\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian ruler (d. 1799)", "links": [{"title": "Veera<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Veerapandiya_Kattabomman"}]}, {"year": "1775", "text": "<PERSON>, 2nd Earl of Charlemont (d. 1863)", "html": "1775 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Charlemont\" title=\"<PERSON>, 2nd Earl of Charlemont\"><PERSON>, 2nd Earl of Charlemont</a> (d. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Charlemont\" title=\"<PERSON>, 2nd Earl of Charlemont\"><PERSON>, 2nd Earl of Charlemont</a> (d. 1863)", "links": [{"title": "<PERSON>, 2nd Earl of Charlemont", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Charlemont"}]}, {"year": "1778", "text": "<PERSON><PERSON>, Polish archbishop (d. 1861)", "html": "1778 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>or_Fija%C5%82kowski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish archbishop (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>or_Fija%C5%82kowski\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Polish archbishop (d. 1861)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antoni_Melchior_Fija%C5%82kowski"}]}, {"year": "1793", "text": "<PERSON><PERSON><PERSON>, American activist (d. 1880)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist (d. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American activist (d. 1880)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lucretia_Mott"}]}, {"year": "1802", "text": "<PERSON>, English lawyer and politician (d. 1898)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1803", "text": "<PERSON>, English journalist and playwright (d. 1857)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and playwright (d. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English journalist and playwright (d. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON><PERSON>, German soprano and actress (d. 1854)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress (d. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German soprano and actress (d. 1854)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1810", "text": "<PERSON>, French geographer, ethnologist, linguist, and astronomer (d. 1897)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Abbadie\" title=\"<PERSON>'Abbadie\"><PERSON></a>, French geographer, ethnologist, linguist, and astronomer (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Abbadie\" title=\"<PERSON>'Abbadie\"><PERSON></a>, French geographer, ethnologist, linguist, and astronomer (d. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27A<PERSON>die"}]}, {"year": "1816", "text": "<PERSON>, American businessman and politician (d. 1891)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and politician (d. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, Italian-Scottish astronomer and academic (d. 1900)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish astronomer and academic (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-Scottish astronomer and academic (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>th"}]}, {"year": "1831", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Indian poet, educator, and activist (d. 1897)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/Savitribai_Phule\" title=\"Savitribai Phule\">Savitribai Phule</a>, Indian poet, educator, and activist (d. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Savitribai_Phule\" title=\"Savitribai Phule\">Savitribai Phule</a>, Indian poet, educator, and activist (d. 1897)", "links": [{"title": "Savitribai Phule", "link": "https://wikipedia.org/wiki/Savitribai_Phule"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, Japanese samurai and rebel leader (d. 1867)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/Sakamoto_Ry%C5%8Dma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai and rebel leader (d. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sa<PERSON>moto_Ry%C5%8Dma\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese samurai and rebel leader (d. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>moto_Ry%C5%8Dma"}]}, {"year": "1840", "text": "<PERSON>, Flemish priest and missionary (d. 1889)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/Father_<PERSON>\" title=\"Father <PERSON>\">Father <PERSON></a>, Flemish priest and missionary (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Father_<PERSON>\" title=\"Father <PERSON>\">Father <PERSON></a>, Flemish priest and missionary (d. 1889)", "links": [{"title": "Father <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1847", "text": "<PERSON><PERSON><PERSON>, Italian physician (d. 1935)", "html": "1847 - <a href=\"https://wikipedia.org/wiki/Ettore_March<PERSON>\" title=\"Ettore March<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ettore_March<PERSON>\" title=\"Ettore March<PERSON>\"><PERSON><PERSON><PERSON></a>, Italian physician (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ettore_Marchiafava"}]}, {"year": "1853", "text": "<PERSON>, Swedish writer (d. 1921)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish writer (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish writer (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English businessman (d. 1914)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English tennis player (d. 1899)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1861", "text": "<PERSON>, English tennis player (d. 1904)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English tennis player (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, English soldier and politician, 13th Governor of Queensland (d. 1939)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, 13th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Queensland", "link": "https://wikipedia.org/wiki/Governor_of_Queensland"}]}, {"year": "1865", "text": "<PERSON>, English actor (d. 1936)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Australian-English author (d. 1946)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English author (d. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-English author (d. 1946)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese businessman and art collector, founded the Hankyu Hanshin Holdings (d. 1957)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Ichiz%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman and art collector, founded the <a href=\"https://wikipedia.org/wiki/Hankyu_Hanshin_Holdings\" title=\"Hankyu Hanshin Holdings\">Hankyu Hanshin Holdings</a> (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ichiz%C5%8D_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese businessman and art collector, founded the <a href=\"https://wikipedia.org/wiki/Hankyu_Hanshin_Holdings\" title=\"Hankyu Hanshin Holdings\">Hankyu Hanshin Holdings</a> (d. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ichiz%C5%8D_<PERSON>"}, {"title": "Hankyu Hanshin Holdings", "link": "https://wikipedia.org/wiki/Hankyu_Hanshin_Holdings"}]}, {"year": "1875", "text": "<PERSON><PERSON><PERSON>, Greek banker and politician, 145th Prime Minister of Greece (d. 1950)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 145th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek banker and politician, 145th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Greece\" title=\"Prime Minister of Greece\">Prime Minister of Greece</a> (d. 1950)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Greece", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Greece"}]}, {"year": "1876", "text": "<PERSON>, German carpenter and politician, 1st President of the German Democratic Republic (d. 1960)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and politician, 1st <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">President of the German Democratic Republic</a> (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German carpenter and politician, 1st <a href=\"https://wikipedia.org/wiki/Leadership_of_East_Germany\" class=\"mw-redirect\" title=\"Leadership of East Germany\">President of the German Democratic Republic</a> (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Leadership of East Germany", "link": "https://wikipedia.org/wiki/Leadership_of_East_Germany"}]}, {"year": "1877", "text": "<PERSON>, American actress (d. 1957)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, Irish Jesuit priest and photographer (d. 1960)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Jesuit priest and photographer (d. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish Jesuit priest and photographer (d. 1960)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON>, English soldier, lawyer, and politician, Prime Minister of the United Kingdom (d. 1967)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, lawyer, and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1883", "text": "<PERSON>, Canadian discus thrower and hammer thrower (d. 1963)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian discus thrower and hammer thrower (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian discus thrower and hammer thrower (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1884", "text": "<PERSON>, Polish pianist and composer (d. 1948)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and composer (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish pianist and composer (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, American businessman (d. 1912)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Widener\"><PERSON></a>, American businessman (d. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, American poet and author (d. 1950)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Australian cricketer (d. 1967)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, German-French painter (d. 1914)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German-French painter (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, German-French painter (d. 1914)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>"}]}, {"year": "1892", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, English writer, poet, and philologist (d. 1973)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English writer, poet, and philologist (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, English writer, poet, and philologist (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON><PERSON><PERSON>, American actress (d. 1963)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/ZaSu_Pitts\" title=\"ZaSu Pitts\"><PERSON><PERSON><PERSON><PERSON></a>, American actress (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ZaSu_Pitts\" title=\"ZaSu Pitts\"><PERSON><PERSON><PERSON><PERSON></a>, American actress (d. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/ZaSu_Pitts"}]}, {"year": "1897", "text": "<PERSON><PERSON><PERSON>, Irish republican revolutionary, (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> revolutionary, (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Irish_republican\" class=\"mw-redirect\" title=\"Irish republican\">Irish republican</a> revolutionary, (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>"}, {"title": "Irish republican", "link": "https://wikipedia.org/wiki/Irish_republican"}]}, {"year": "1897", "text": "<PERSON>, American actress and comedian (d. 1961)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and comedian (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American author and illustrator (d. 1990)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, American businessman (d. 1985)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON><PERSON>, Vietnamese lawyer and politician, 1st President of the Republic of Vietnam (d. 1963)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"President of the Republic of Vietnam\">President of the Republic of Vietnam</a> (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Vietnamese lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_Vietnam\" class=\"mw-redirect\" title=\"President of the Republic of Vietnam\">President of the Republic of Vietnam</a> (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ng%C3%B4_%C4%90%C3%ACnh_Di%E1%BB%87m"}, {"title": "President of the Republic of Vietnam", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_Vietnam"}]}, {"year": "1905", "text": "<PERSON>, Italian engineer (d. 1996)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian engineer (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dante_<PERSON>osa"}]}, {"year": "1905", "text": "<PERSON>, American actress (d. 1961)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON>, Welsh-American actor and director (d. 1986)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-American actor and director (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>and"}]}, {"year": "1909", "text": "<PERSON>, Danish-American pianist and conductor (d. 2000)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/1909\" title=\"1909\">1909</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American pianist and conductor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1909\" title=\"1909\">1909</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish-American pianist and conductor (d. 2000)", "links": [{"title": "1909", "link": "https://wikipedia.org/wiki/1909"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON>, American baseball player and manager (d. 2000)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Frenchy_Bordagaray\" title=\"Frenchy Bordagaray\"><PERSON><PERSON></a>, American baseball player and manager (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frenchy_Bordagaray\" title=\"Frenchy Bordagaray\"><PERSON><PERSON></a>, American baseball player and manager (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Frenchy_Bordagaray"}]}, {"year": "1910", "text": "<PERSON>, American director and producer (d. 1992)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, Spanish soldier (d. 1936)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Garc%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Garc%C3%ADa\" title=\"<PERSON>\"><PERSON></a>, Spanish soldier (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Garc%C3%ADa"}]}, {"year": "1912", "text": "<PERSON><PERSON><PERSON>, Canadian journalist and politician (d. 2002)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian journalist and politician (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American-Finnish actor, director, and screenwriter (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Finnish actor, director, and screenwriter (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Finnish actor, director, and screenwriter (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American painter and soldier (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and soldier (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and soldier (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American actress and television journalist (d. 1994)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television journalist (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and television journalist (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American golfer (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Dutch author and actor (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and actor (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch author and actor (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, Jr., American journalist and publisher, co-founded Farrar, Straus and <PERSON><PERSON> (d. 2004)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>\" title=\"Farrar, <PERSON><PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON></a> (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American journalist and publisher, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>\" title=\"Farrar, <PERSON><PERSON><PERSON> and <PERSON><PERSON>\"><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON></a> (d. 2004)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}, {"title": "Farrar, Straus and Giroux", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1919", "text": "<PERSON><PERSON>, American pianist and composer (d. 1963)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American pianist and composer (d. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON>, German lawyer and politician, Attorney General of Germany (d. 1977)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Germany\" class=\"mw-redirect\" title=\"Attorney General of Germany\">Attorney General of Germany</a> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Attorney_General_of_Germany\" class=\"mw-redirect\" title=\"Attorney General of Germany\">Attorney General of Germany</a> (d. 1977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Attorney General of Germany", "link": "https://wikipedia.org/wiki/Attorney_General_of_Germany"}]}, {"year": "1921", "text": "<PERSON>, Russian historian of mathematics (d. 2005)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian of mathematics (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian historian of mathematics (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English actor, director, and screenwriter (d. 1994)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and screenwriter (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, American football coach and sportscaster (d. 2005)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and sportscaster (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football coach and sportscaster (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, German businessman and philanthropist, founded Metro AG (d. 2013)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Metro_AG\" title=\"Metro AG\">Metro AG</a> (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Metro_AG\" title=\"Metro AG\">Metro AG</a> (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>eim"}, {"title": "Metro AG", "link": "https://wikipedia.org/wiki/Metro_AG"}]}, {"year": "1924", "text": "<PERSON><PERSON>, Italian football player (d. 1962)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football player (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian football player (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, Belgian author and illustrator (d. 1997)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American soprano and educator (d. 2005)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soprano and educator (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, English actress (d. 2009)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON> <PERSON>, American economist and politician, 64th United States Secretary of the Treasury", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\">W<PERSON> <PERSON></a>, American economist and politician, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\">W<PERSON> <PERSON></a>, American economist and politician, 64th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury\" title=\"United States Secretary of the Treasury\">United States Secretary of the Treasury</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of the Treasury", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_the_Treasury"}]}, {"year": "1926", "text": "<PERSON>, English composer, conductor, and producer (d. 2016)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English composer, conductor, and producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Malaysian lawyer and politician, 3rd Chief Minister of Sarawak (d. 2015)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27kub\" title=\"<PERSON>kub\"><PERSON></a>, Malaysian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sarawak\" class=\"mw-redirect\" title=\"Chief Minister of Sarawak\">Chief Minister of Sarawak</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27kub\" title=\"<PERSON>kub\"><PERSON></a>, Malaysian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Sarawak\" class=\"mw-redirect\" title=\"Chief Minister of Sarawak\">Chief Minister of Sarawak</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27kub"}, {"title": "Chief Minister of Sarawak", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Sarawak"}]}, {"year": "1929", "text": "<PERSON>, Italian director, producer, and screenwriter (d. 1989)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Sergio_Leone\" title=\"Sergio Leone\"><PERSON></a>, Italian director, producer, and screenwriter (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergio_Leone\" title=\"Sergio Leone\"><PERSON></a>, Italian director, producer, and screenwriter (d. 1989)", "links": [{"title": "Sergio <PERSON>", "link": "https://wikipedia.org/wiki/Sergio_Leone"}]}, {"year": "1929", "text": "<PERSON>, German-Brazilian composer and conductor", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Brazilian composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American businessman, co-founder of Intel Corporation (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Intel_Corporation\" class=\"mw-redirect\" title=\"Intel Corporation\">Intel Corporation</a> (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Intel Corporation", "link": "https://wikipedia.org/wiki/Intel_Corporation"}]}, {"year": "1930", "text": "<PERSON>, American illustrator.", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American illustrator.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor and director (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and director (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, American actor (d. 2024)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Finnish pole vaulter and politician (d. 2022)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/Eeles_Landstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish pole vaulter and politician (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Landstr%C3%B6m\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish pole vaulter and politician (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Eeles_Landstr%C3%B6m"}]}, {"year": "1933", "text": "<PERSON>, English lawyer", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American-English poet and author (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, American-French actress, singer, and dancer (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Marpessa_Dawn\" title=\"Mar<PERSON><PERSON> Dawn\"><PERSON><PERSON><PERSON></a>, American-French actress, singer, and dancer (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marpessa_Dawn\" title=\"Mar<PERSON>sa Dawn\"><PERSON><PERSON><PERSON></a>, American-French actress, singer, and dancer (d. 2008)", "links": [{"title": "Marpessa Dawn", "link": "https://wikipedia.org/wiki/Marpessa_Dawn"}]}, {"year": "1934", "text": "<PERSON>, American lawyer and politician, 5th United States Secretary of Housing and Urban Development", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Secretary of Housing and Urban Development", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development"}]}, {"year": "1935", "text": "<PERSON>, Canadian businessman and politician", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON> <PERSON>, American director, producer, and screenwriter, created Battlestar Galactica (d. 2014)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Battlestar_Galactica\" title=\"Battlestar Galactica\">Battlestar Galactica</a></i> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter, created <i><a href=\"https://wikipedia.org/wiki/Battlestar_Galactica\" title=\"Battlestar Galactica\">Battlestar Galactica</a></i> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Battlestar Galactica", "link": "https://wikipedia.org/wiki/Battlestar_Galactica"}]}, {"year": "1938", "text": "<PERSON>, Baron <PERSON> of Brockwell, English academic and politician", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Brockwell\" title=\"<PERSON>, Baron <PERSON> of Brockwell\"><PERSON>, Baron <PERSON> of Brockwell</a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>_of_Brockwell\" title=\"<PERSON>, Baron <PERSON> of Brockwell\"><PERSON>, Baron <PERSON> of Brockwell</a>, English academic and politician", "links": [{"title": "<PERSON>, Baron <PERSON> of Brockwell", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>_of_Brockwell"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Sri Lankan accountant and politician, Mayor of Colombo (d. 2006)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/K._<PERSON>\" title=\"<PERSON>. <PERSON>\"><PERSON><PERSON></a>, Sri Lankan accountant and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Colombo\" title=\"Mayor of Colombo\">Mayor of Colombo</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan accountant and politician, <a href=\"https://wikipedia.org/wiki/Mayor_of_Colombo\" title=\"Mayor of Colombo\">Mayor of Colombo</a> (d. 2006)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON><PERSON>am"}, {"title": "Mayor of Colombo", "link": "https://wikipedia.org/wiki/Mayor_of_Colombo"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Israeli singer-songwriter and actor (d. 2013)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter and actor (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Canadian ice hockey player (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Polish footballer and coach (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and coach (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish footballer and coach (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Italian actor and director (d. 2008)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian actor and director (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, New Zealand rugby player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby union player)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby union player)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union_player)"}]}, {"year": "1942", "text": "<PERSON>, Australian lawyer and activist (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Australian lawyer and activist (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(lawyer)\" title=\"<PERSON> (lawyer)\"><PERSON></a>, Australian lawyer and activist (d. 2006)", "links": [{"title": "<PERSON> (lawyer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(lawyer)"}]}, {"year": "1942", "text": "<PERSON>, English actor and producer, played Inspector <PERSON> (d. 2002)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer, played <a href=\"https://wikipedia.org/wiki/Inspector_Morse\" title=\"Inspector Morse\">Inspector <PERSON></a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer, played <a href=\"https://wikipedia.org/wiki/Inspector_<PERSON>\" title=\"Inspector <PERSON>\">Inspector <PERSON></a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Inspector <PERSON>", "link": "https://wikipedia.org/wiki/Inspector_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American singer-songwriter, musician, composer, author, and actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, composer, author, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, musician, composer, author, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Australian author", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Alpuget\" title=\"<PERSON>\"><PERSON></a>, Australian author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Alpuget\" title=\"<PERSON>Alpu<PERSON>\"><PERSON></a>, Australian author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_d%27Alpuget"}]}, {"year": "1944", "text": "<PERSON><PERSON>, English geographer and political activist (d. 2016)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(geographer)\" title=\"<PERSON><PERSON> (geographer)\"><PERSON><PERSON></a>, English geographer and political activist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(geographer)\" title=\"<PERSON><PERSON> (geographer)\"><PERSON><PERSON></a>, English geographer and political activist (d. 2016)", "links": [{"title": "<PERSON><PERSON> (geographer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(geographer)"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English bass player, songwriter, and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player, songwriter, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, English bass player, songwriter, and producer", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Greek footballer (d. 2002)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek footballer (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English rugby player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English rugby player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter (d. 2013)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ma"}]}, {"year": "1948", "text": "<PERSON>, Australian footballer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actress and businesswoman", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Victoria_Principal\" title=\"Victoria Principal\"><PERSON> Principal</a>, American actress and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victoria_Principal\" title=\"Victoria Principal\"><PERSON> Principal</a>, American actress and businesswoman", "links": [{"title": "<PERSON> Principal", "link": "https://wikipedia.org/wiki/Victoria_Principal"}]}, {"year": "1950", "text": "<PERSON>, American journalist and academic", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Serbian plane crash survivor and Guinness World Record holder (d. 2016)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian plane crash survivor and <a href=\"https://wikipedia.org/wiki/Guinness_World_Record\" class=\"mw-redirect\" title=\"Guinness World Record\">Guinness World Record</a> holder (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian plane crash survivor and <a href=\"https://wikipedia.org/wiki/Guinness_World_Record\" class=\"mw-redirect\" title=\"Guinness World Record\">Guinness World Record</a> holder (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vesna_Vulovi%C4%87"}, {"title": "Guinness World Record", "link": "https://wikipedia.org/wiki/Guinness_World_Record"}]}, {"year": "1951", "text": "<PERSON>, English lawyer and judge", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and judge", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Australian surveyor and politician, 14th Special Minister of State (d. 2024)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surveyor and politician, 14th <a href=\"https://wikipedia.org/wiki/Special_Minister_of_State\" title=\"Special Minister of State\">Special Minister of State</a> (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surveyor and politician, 14th <a href=\"https://wikipedia.org/wiki/Special_Minister_of_State\" title=\"Special Minister of State\">Special Minister of State</a> (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Special Minister of State", "link": "https://wikipedia.org/wiki/Special_Minister_of_State"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, Spanish civil servant and politician, 3rd President of the Community of Madrid", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Esperanza_Aguirre\" title=\"Esperanza Aguirre\"><PERSON><PERSON><PERSON> Aguirre</a>, Spanish civil servant and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Community_of_Madrid\" title=\"President of the Community of Madrid\">President of the Community of Madrid</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Esperanza_Aguirre\" title=\"Esperanza Aguirre\"><PERSON><PERSON><PERSON> Aguirre</a>, Spanish civil servant and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_the_Community_of_Madrid\" title=\"President of the Community of Madrid\">President of the Community of Madrid</a>", "links": [{"title": "Esperanza Aguirre", "link": "https://wikipedia.org/wiki/Esperanza_Aguirre"}, {"title": "President of the Community of Madrid", "link": "https://wikipedia.org/wiki/President_of_the_Community_of_Madrid"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian journalist and politician, Italian Minister of Foreign Affairs", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian journalist and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)\" title=\"Minister of Foreign Affairs (Italy)\">Italian Minister of Foreign Affairs</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "Minister of Foreign Affairs (Italy)", "link": "https://wikipedia.org/wiki/Minister_of_Foreign_Affairs_(Italy)"}]}, {"year": "1952", "text": "<PERSON>, American professional wrestling commentator", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American professional wrestling commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian playwright and author", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian playwright and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, Australian playwright and author", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)"}]}, {"year": "1953", "text": "<PERSON>, Maldivian educator and politician, 5th President of the Maldives", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON></a>, Maldivian educator and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Maldives\" title=\"President of the Maldives\">President of the Maldives</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON></a>, Maldivian educator and politician, 5th <a href=\"https://wikipedia.org/wiki/President_of_the_Maldives\" title=\"President of the Maldives\">President of the Maldives</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of the Maldives", "link": "https://wikipedia.org/wiki/President_of_the_Maldives"}]}, {"year": "1953", "text": "<PERSON>, English footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1953)\" title=\"<PERSON> (footballer, born 1953)\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON> (footballer, born 1953)", "link": "https://wikipedia.org/wiki/<PERSON>_(footballer,_born_1953)"}]}, {"year": "1955", "text": "<PERSON>, Australian radio host and singer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio host and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian radio host and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American-Australian actor, director, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Australian actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, New Zealand singer-songwriter and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English racing driver", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player (d. 2017)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, Scottish rugby player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Scottish businessman and politician", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON>, Pakistani cricketer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English author and playwright", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American wrestler (d. 2021)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" class=\"mw-redirect\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler (d. 2021)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1964", "text": "<PERSON>, Canadian director, producer, and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American basketball player and coach", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Indian cricketer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American musician", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American musician", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Finnish racing driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish racing driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German racing driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German racing driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON>, Italian luger and bobsledder", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian luger and bobsledder", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian luger and bobsledder", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, English actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Canadian ice hockey player and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, South Korean actress", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>wa"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Dutch golfer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Italian cyclist", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Canadian ice hockey player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, French DJ, musician, and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French DJ, musician, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French DJ, musician, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, American actress and mathematician", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and mathematician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and mathematician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Greek footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Angelo<PERSON>_Basinas"}]}, {"year": "1976", "text": "<PERSON>, American actor and producer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English footballer and coach", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON> <PERSON><PERSON>, American baseball player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American baseball player", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON><PERSON>, Greek basketball player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, American singer, songwriter, and television personality", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, and television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer, songwriter, and television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American ice hockey player", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American actress, singer, and dancer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American decathlete", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American decathlete", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, U.S. representative for Arizona's 2nd congressional district", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/U.S._representative\" class=\"mw-redirect\" title=\"U.S. representative\">U.S. representative</a> for <a href=\"https://wikipedia.org/wiki/Arizona%27s_2nd_congressional_district\" title=\"Arizona's 2nd congressional district\">Arizona's 2nd congressional district</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/U.S._representative\" class=\"mw-redirect\" title=\"U.S. representative\">U.S. representative</a> for <a href=\"https://wikipedia.org/wiki/Arizona%27s_2nd_congressional_district\" title=\"Arizona's 2nd congressional district\">Arizona's 2nd congressional district</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "U.S. representative", "link": "https://wikipedia.org/wiki/U.S._representative"}, {"title": "Arizona's 2nd congressional district", "link": "https://wikipedia.org/wiki/Arizona%27s_2nd_congressional_district"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American actor, director, singer and songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Leung\"><PERSON><PERSON></a>, American actor, director, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Leung\"><PERSON><PERSON></a>, American actor, director, singer and songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>y_<PERSON>ung"}]}, {"year": "1980", "text": "<PERSON>, American ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Kurt_Vile"}]}, {"year": "1980", "text": "<PERSON>, American sprinter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1982", "text": "<PERSON>, South Korean singer and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>on"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Swedish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Irish actress", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English-Irish footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Lithuanian basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Linas Kleiza\"><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Linas Kleiza\"><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American football player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American basketball player and coach", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Iraqi sprinter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iraqi sprinter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1986", "text": "<PERSON>, Indonesian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Montenegrin basketball player and executive", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin basketball player and executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Montenegrin basketball player and executive", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Nikola_Pekovi%C4%87"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American-Bulgarian basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Bulgarian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Bulgarian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Russian pole vaulter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian pole vaulter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Spanish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Adri%C3%A1<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Adri%C3%A1<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Adri%C3%<PERSON><PERSON>_(footballer)"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Swiss professional ice hockey goaltender", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss professional ice hockey goaltender", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss professional ice hockey goaltender", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>rra"}]}, {"year": "1987", "text": "<PERSON>, South Korean actress and singer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-vin\" title=\"<PERSON>-vin\"><PERSON>in</a>, South Korean actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-vin\" title=\"<PERSON>-vin\"><PERSON>-vin</a>, South Korean actress and singer", "links": [{"title": "<PERSON>in", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-vin"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, Scottish-Nigerian footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Scottish-Nigerian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "The Completionist, American YouTuber", "html": "1988 - <a href=\"https://wikipedia.org/wiki/The_Completionist\" title=\"The Completionist\">The Completionist</a>, American YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Completionist\" title=\"The Completionist\">The Completionist</a>, American YouTuber", "links": [{"title": "The Completionist", "link": "https://wikipedia.org/wiki/The_Completionist"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Northern Irish footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Northern Irish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian ice hockey player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Masip\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Masip\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>p"}]}, {"year": "1989", "text": "<PERSON>, American baseball player and YouTuber", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and YouTuber", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and YouTuber", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese artistic gymnast", "html": "1989 - <a href=\"https://wikipedia.org/wiki/K%C5%8Dhe<PERSON>_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese artistic gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%C5%8Dhe<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese artistic gymnast", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%C5%8Dhe<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Japanese footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/%C3%96zg%C3%BCr_%C3%87ek\" title=\"<PERSON>zg<PERSON><PERSON> Çek\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%96zg%C3%BCr_%C3%87ek\" title=\"<PERSON>zg<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Turkish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%96zg%C3%BCr_%C3%87ek"}]}, {"year": "1991", "text": "<PERSON>, Canadian ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON><PERSON>, French footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1991", "text": "<PERSON>, Australian rugby league player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South Korean singer and actress (d. 2019)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hara\"><PERSON><PERSON></a>, South Korean singer and actress (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Hara\"><PERSON><PERSON></a>, South Korean singer and actress (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American basketball player (d. 2024)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Finnish ice hockey player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>s_N%C3%A4ttinen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_N%C3%A4ttinen\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Finnish ice hockey player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Joonas_N%C3%A4ttinen"}]}, {"year": "1992", "text": "<PERSON>, American basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, New Zealand-Tongan rugby league player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Tongan rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, New Zealand-Tongan rugby league player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>o"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, Brazilian sprint canoeist", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>z\" title=\"<PERSON><PERSON><PERSON> Que<PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian sprint canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian sprint canoeist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isa<PERSON>as_Queiroz"}]}, {"year": "1995", "text": "<PERSON><PERSON>-<PERSON>, American-Jordanian basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>llis-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American-Jordanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>llis-<PERSON>\"><PERSON><PERSON>-<PERSON></a>, American-Jordanian basketball player", "links": [{"title": "<PERSON><PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1995", "text": "<PERSON><PERSON><PERSON>, South Korean singer and actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>so<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>so<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jisoo"}]}, {"year": "1995", "text": "<PERSON>, English mixed martial artist", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mixed martial artist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mixed martial artist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, South Korean singer and actress", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>yun"}]}, {"year": "1995", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/To<PERSON>_<PERSON>\" title=\"To<PERSON> V<PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/To<PERSON>_<PERSON>\" title=\"To<PERSON> V<PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tonny_Vilhena"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/L%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/L%C3%A9o_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Florence_Pugh\" title=\"<PERSON> Pugh\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Pugh\" title=\"Florence Pugh\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Florence_Pugh"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, French-Senegalese footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Fod%C3%A9_Ballo-Tour%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fod%C3%A9_Ballo-Tour%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French-Senegalese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fod%C3%A9_Ballo-Tour%C3%A9"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, French-Ivorian footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>ga\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Ivorian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%A9r%C3%A9<PERSON>_<PERSON>ga\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French-Ivorian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%A9r%C3%A9mie_Boga"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, British Virgin Islands hurdler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>yr<PERSON>_McMaster\" title=\"<PERSON>yr<PERSON> McMaster\"><PERSON><PERSON><PERSON>c<PERSON></a>, British Virgin Islands hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>yr<PERSON>_McMaster\" title=\"Kyr<PERSON> McMaster\"><PERSON><PERSON><PERSON>c<PERSON></a>, British Virgin Islands hurdler", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>c<PERSON>aster"}]}, {"year": "1998", "text": "<PERSON>, Italian footballer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON><PERSON>, American online streamer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Emiru\" title=\"Emiru\">Emiru</a>, American online streamer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emiru\" title=\"Emiru\">Emiru</a>, American online streamer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiru"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Luxembourgish  footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourgish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Luxembourgish footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Israeli-Serbian basketball player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Den<PERSON>_<PERSON>vdi<PERSON>\" title=\"Den<PERSON>di<PERSON>\"><PERSON><PERSON></a>, Israeli-Serbian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>vdi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli-Serbian basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Deni_<PERSON><PERSON>di<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Spanish footballer", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_2002)\" title=\"<PERSON> (footballer, born 2002)\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_(footballer,_born_2002)\" title=\"<PERSON> (footballer, born 2002)\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON> (footballer, born 2002)", "link": "https://wikipedia.org/wiki/<PERSON>_Gonz%C3%<PERSON><PERSON><PERSON>_(footballer,_born_2002)"}]}, {"year": "2003", "text": "<PERSON>, American conservative personality", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conservative personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American conservative personality", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Swedish environmental activist", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Thunberg\"><PERSON><PERSON></a>, Swedish environmental activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"G<PERSON> Thunberg\"><PERSON><PERSON></a>, Swedish environmental activist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>berg"}]}, {"year": "2003", "text": "<PERSON>, French footballer", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Cameroonian footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, English footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON>, Senegalese footballer", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>arra\"><PERSON><PERSON><PERSON></a>, Senegalese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "236", "text": "<PERSON><PERSON><PERSON>, pope of the Catholic Church", "html": "236 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>terus\" title=\"Pope Anterus\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>us\" title=\"Pope Anterus\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Anterus"}]}, {"year": "323", "text": "Emperor <PERSON> of Jin, Chinese emperor (b. 276)", "html": "323 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, Chinese emperor (b. 276)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Jin\" title=\"Emperor <PERSON> of Jin\">Emperor <PERSON> of Jin</a>, Chinese emperor (b. 276)", "links": [{"title": "Emperor <PERSON> of Jin", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_of_Jin"}]}, {"year": "1027", "text": "<PERSON><PERSON>, Japanese calligrapher (b. 972)", "html": "1027 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Yu<PERSON>ari\" title=\"Fujiwara no Yukinari\"><PERSON><PERSON> no <PERSON></a>, Japanese calligrapher (b. 972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Yukinari\" title=\"Fujiwara no Yukinari\"><PERSON><PERSON> no <PERSON></a>, Japanese calligrapher (b. 972)", "links": [{"title": "<PERSON><PERSON> no <PERSON>", "link": "https://wikipedia.org/wiki/Fuji<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1028", "text": "<PERSON><PERSON>, Japanese nobleman (b. 966)", "html": "1028 - <a href=\"https://wikipedia.org/wiki/Fujiwara_no_Michinaga\" title=\"Fujiwara no Michinaga\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fujiwara_no_Michinaga\" title=\"Fujiwara no Michinaga\"><PERSON><PERSON> no <PERSON></a>, Japanese nobleman (b. 966)", "links": [{"title": "<PERSON><PERSON> no <PERSON>chinaga", "link": "https://wikipedia.org/wiki/Fujiwara_no_Mi<PERSON>ga"}]}, {"year": "1098", "text": "<PERSON><PERSON>, Norman bishop of Winchester", "html": "1098 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norman bishop of Winchester", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Norman bishop of Winchester", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>elin"}]}, {"year": "1322", "text": "<PERSON>, king of France (b. 1292)", "html": "1322 - <a href=\"https://wikipedia.org/wiki/Philip_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, king of France (b. 1292)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON></a>, king of France (b. 1292)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Philip_V_of_France"}]}, {"year": "1437", "text": "<PERSON> of Valois, queen consort of <PERSON> (b. 1401)", "html": "1437 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Valois\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, queen consort of <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON></a> (b. 1401)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Val<PERSON>\" title=\"<PERSON> of Valois\"><PERSON> of Valois</a>, queen consort of <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON></a> (b. 1401)", "links": [{"title": "<PERSON> of Valois", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_V_of_England"}]}, {"year": "1501", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkic poet, linguist, and mystic (b. 1441)", "html": "1501 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27i\" title=\"<PERSON><PERSON><PERSON><PERSON>i\"><PERSON><PERSON><PERSON><PERSON></a>, Turkic poet, linguist, and mystic (b. 1441)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>_<PERSON>%27i\" title=\"<PERSON><PERSON><PERSON><PERSON>'i\"><PERSON><PERSON><PERSON><PERSON></a>, Turkic poet, linguist, and mystic (b. 1441)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>_<PERSON>va%27i"}]}, {"year": "1543", "text": "<PERSON>, Portuguese explorer and navigator (b. 1499)", "html": "1543 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_Cabrillo\" title=\"<PERSON>\"><PERSON></a>, Portuguese explorer and navigator (b. 1499)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_<PERSON>abrillo\" title=\"<PERSON>\"><PERSON></a>, Portuguese explorer and navigator (b. 1499)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADguez_<PERSON>abrillo"}]}, {"year": "1571", "text": "<PERSON>, Elector of Brandenburg (b. 1505)", "html": "1571 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON></a>, Elector of Brandenburg (b. 1505)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg\" class=\"mw-redirect\" title=\"<PERSON>, Elector of Brandenburg\"><PERSON></a>, Elector of Brandenburg (b. 1505)", "links": [{"title": "<PERSON>, Elector of Brandenburg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Elector_of_Brandenburg"}]}, {"year": "1641", "text": "<PERSON>, English astronomer and mathematician (b. 1618)", "html": "1641 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and mathematician (b. 1618)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English astronomer and mathematician (b. 1618)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1656", "text": "<PERSON><PERSON>, French politician (b. 1584)", "html": "1656 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician (b. 1584)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French politician (b. 1584)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mathieu_Mol%C3%A9"}]}, {"year": "1670", "text": "<PERSON>, 1st Duke of Albemarle, English general and politician, Lord Lieutenant of Ireland (b. 1608)", "html": "1670 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON>, 1st Duke of Albemarle</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle\" title=\"<PERSON>, 1st Duke of Albemarle\"><PERSON>, 1st Duke of Albemarle</a>, English general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1608)", "links": [{"title": "<PERSON>, 1st Duke of Albemarle", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Albemarle"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1701", "text": "<PERSON>, prince of Monaco (b. 1642)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON></a>, prince of Monaco (b. 1642)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco\" title=\"<PERSON>, Prince of Monaco\"><PERSON></a>, prince of Monaco (b. 1642)", "links": [{"title": "<PERSON>, Prince of Monaco", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Prince_of_Monaco"}]}, {"year": "1705", "text": "<PERSON>, Italian painter and illustrator (b. 1634)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (b. 1634)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian painter and illustrator (b. 1634)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1743", "text": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>, Italian painter and architect (b. 1657)", "html": "1743 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Bibiena\" title=\"<PERSON><PERSON>Bibi<PERSON>\"><PERSON><PERSON></a>, Italian painter and architect (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-Bibiena\" title=\"<PERSON><PERSON>B<PERSON>\"><PERSON><PERSON></a>, Italian painter and architect (b. 1657)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ferdinando_Galli-Bibiena"}]}, {"year": "1777", "text": "<PERSON>, Scottish captain (b. 1751)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish captain (b. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish captain (b. 1751)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(British_Army_officer)"}]}, {"year": "1779", "text": "<PERSON>, French surgeon and lawyer (b. 1712)", "html": "1779 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and lawyer (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and lawyer (b. 1712)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1785", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer (b. 1706)", "html": "1785 - <a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_G<PERSON>uppi\" title=\"Balda<PERSON><PERSON> Galuppi\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Balda<PERSON><PERSON>_Galuppi\" title=\"Balda<PERSON><PERSON> Galuppi\"><PERSON><PERSON><PERSON><PERSON></a>, Italian composer (b. 1706)", "links": [{"title": "Baldassare Galuppi", "link": "https://wikipedia.org/wiki/Balda<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1795", "text": "<PERSON>, English potter, founded the Wedgwood Company (b. 1730)", "html": "1795 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English potter, founded the <a href=\"https://wikipedia.org/wiki/Wedgwood\" title=\"Wedgwood\">Wedgwood Company</a> (b. 1730)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English potter, founded the <a href=\"https://wikipedia.org/wiki/Wedgwood\" title=\"Wedgwood\">Wedgwood Company</a> (b. 1730)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wedgwood"}]}, {"year": "1826", "text": "<PERSON><PERSON><PERSON>, French general (b. 1770)", "html": "1826 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French general (b. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "1871", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian priest and saint (b. 1805)", "html": "1871 - <a href=\"https://wikipedia.org/wiki/Ku<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Kuriakos<PERSON>\">Kuria<PERSON><PERSON></a>, Indian priest and saint (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kuria<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"Kuriakose <PERSON>\">Kuria<PERSON><PERSON></a>, Indian priest and saint (b. 1805)", "links": [{"title": "Kuriakose <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, French lexicographer and publisher (b. 1817)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lexicographer and publisher (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lexicographer and publisher (b. 1817)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, English author (b. 1805)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American lithographer and businessman, co-founded Currier and Ives (b. 1824)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lithographer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> (b. 1824)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lithographer and businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>\" title=\"<PERSON><PERSON> and <PERSON>\"><PERSON><PERSON> and <PERSON></a> (b. 1824)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_and_<PERSON>"}]}, {"year": "1903", "text": "<PERSON><PERSON>, Austrian civil servant (b. 1837)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian civil servant (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian civil servant (b. 1837)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Greek author and poet (b. 1851)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and poet (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek author and poet (b. 1851)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, English poet, author, and playwright (b. 1884)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, author, and playwright (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, American general and politician (b. 1831)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/Grenville_M._Dodge\" title=\"Grenville M. Dodge\"><PERSON><PERSON><PERSON></a>, American general and politician (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grenville_M._Dodge\" title=\"Grenville M. Dodge\"><PERSON><PERSON><PERSON> <PERSON></a>, American general and politician (b. 1831)", "links": [{"title": "Grenville M. Dodge", "link": "https://wikipedia.org/wiki/Grenville_M._Dodge"}]}, {"year": "1922", "text": "<PERSON>, German criminal (b. 1849)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON>gt\"><PERSON></a>, German criminal (b. 1849)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> V<PERSON>gt\"><PERSON></a>, German criminal (b. 1849)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>oigt"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Czech journalist and author (b. 1883)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech journalist and author (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1ek\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech journalist and author (b. 1883)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>aroslav_Ha%C5%A1ek"}]}, {"year": "1927", "text": "<PERSON>, German physicist and mathematician (b. 1856)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9_Runge\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9_Runge\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German physicist and mathematician (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9_Runge"}]}, {"year": "1931", "text": "<PERSON>, French general (b. 1852)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1852)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French general (b. 1852)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, German lawyer and politician, Chancellor of Germany (b. 1876)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1933", "text": "<PERSON>, Canadian-American actor, director, and producer (b. 1896)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, and producer (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American actor, director, and producer (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Australian lawyer and politician, 5th Premier of Western Australia (b. 1863)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician, 5th <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1863)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1944", "text": "<PERSON><PERSON><PERSON>, Lithuanian poet, critic, and translator (b. 1873)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Jurg<PERSON>_<PERSON>ltru%C5%A1aitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian poet, critic, and translator (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ru%C5%A1aitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian poet, critic, and translator (b. 1873)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jurgis_Baltru%C5%A1aitis"}]}, {"year": "1945", "text": "<PERSON>, American psychic and author (b. 1877)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychic and author (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American psychic and author (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish journalist and explorer (b. 1879)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and explorer (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish journalist and explorer (b. 1879)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American-British pro-Axis propaganda broadcaster (b. 1906)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British pro-Axis propaganda broadcaster (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-British pro-Axis propaganda broadcaster (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Russian-American pianist and composer (b. 1864)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American pianist and composer (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Greek wrestler, weightlifter, and shot putter (b. 1886)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek wrestler, weightlifter, and shot putter (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek wrestler, weightlifter, and shot putter (b. 1886)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dimitrios_Vergos"}]}, {"year": "1956", "text": "<PERSON>, German educator and politician, Chancellor of Germany (b. 1876)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German educator and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany\" title=\"Chancellor of Germany\">Chancellor of Germany</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>h"}, {"title": "Chancellor of Germany", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Turkish general (b. 1877)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Cafer_Tayyar_E%C4%9Filmez\" title=\"Cafer Tayyar Eğilmez\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish general (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cafer_Tayyar_E%C4%9Filmez\" title=\"Cafer Tayyar Eğilmez\"><PERSON><PERSON></a>, Turkish general (b. 1877)", "links": [{"title": "<PERSON>r <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cafer_Tayyar_E%C4%9Filmez"}]}, {"year": "1959", "text": "<PERSON>, Scottish poet, author, and translator (b. 1887)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, author, and translator (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet, author, and translator (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American journalist, author, and academic (b. 1884)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist, author, and academic (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, German footballer and manager (b. 1893)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer and manager (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, German footballer and manager (b. 1893)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1965", "text": "<PERSON>, American painter (b. 1885)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Milton Avery\"><PERSON></a>, American painter (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Milton Avery\"><PERSON></a>, American painter (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American civil rights activist (b. 1944)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American civil rights activist (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American civil rights activist (b. 1944)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1967", "text": "<PERSON>, Scottish-American soprano and actress (b. 1874)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/Mary_Garden\" title=\"Mary Garden\"><PERSON></a>, Scottish-American soprano and actress (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mary_Garden\" title=\"Mary Garden\"><PERSON></a>, Scottish-American soprano and actress (b. 1874)", "links": [{"title": "Mary <PERSON>", "link": "https://wikipedia.org/wiki/Mary_Garden"}]}, {"year": "1967", "text": "<PERSON>, British scientist (b. 1875)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British scientist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American businessman and murderer (b. 1911)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and murderer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and murderer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Greek-French astronomer (b. 1909)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-French astronomer (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek-French astronomer (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek-French actor (b. 1904)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/T<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-French actor (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek-French actor (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, English missionary and humanitarian (b. 1902)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and humanitarian (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and humanitarian (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Indian author and playwright (b. 1925)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian author and playwright (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Austrian philosopher from the Vienna Circle (b. 1880)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (b. 1880)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian philosopher from the Vienna Circle (b. 1880)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Victor_<PERSON>raft"}]}, {"year": "1975", "text": "<PERSON>, American general (b. 1910)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American lithographer, cartoonist, and painter (b. 1897)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lithographer, cartoonist, and painter (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lithographer, cartoonist, and painter (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American businessman, founded the Hilton Hotels & Resorts (b. 1887)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Conrad_Hilton\" title=\"Conrad Hilton\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Hilton_Hotels_%26_Resorts\" title=\"Hilton Hotels &amp; Resorts\">Hilton Hotels &amp; Resorts</a> (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Conrad_Hilton\" title=\"Conrad Hilton\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Hilton_Hotels_%26_Resorts\" title=\"Hilton Hotels &amp; Resorts\">Hilton Hotels &amp; Resorts</a> (b. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Hilton Hotels & Resorts", "link": "https://wikipedia.org/wiki/Hilton_Hotels_%26_Resorts"}]}, {"year": "1980", "text": "<PERSON>, Austrian-Kenyan painter and conservationist (b. 1910)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Kenyan painter and conservationist (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Kenyan painter and conservationist (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Scottish poet and academic (b. 1915)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and academic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and academic (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "Princess <PERSON>, Countess of Athlone (b. 1883)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Athlone\" title=\"Princess <PERSON>, Countess of Athlone\">Princess <PERSON>, Countess of Athlone</a> (b. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Athlone\" title=\"Princess <PERSON>, Countess of Athlone\">Princess <PERSON>, Countess of Athlone</a> (b. 1883)", "links": [{"title": "Princess <PERSON>, Countess of Athlone", "link": "https://wikipedia.org/wiki/Princess_<PERSON>,_Countess_of_Athlone"}]}, {"year": "1988", "text": "<PERSON>, Ukrainian-German poet and author (b. 1901)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%A4nder\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German poet and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>l%C3%A4nder\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German poet and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rose_Ausl%C3%A4nder"}]}, {"year": "1989", "text": "<PERSON>, Russian mathematician and academic (b. 1909)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian mathematician and academic (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian actress (b. 1897)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON>, Indian engineer (b. 1920)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian engineer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian engineer (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2003", "text": "<PERSON>, American football player and coach (b. 1911)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, Australian politician, 37th Premier of South Australia (b. 1928)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 37th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Des_Corcoran"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Taiwanese businessman and diplomat (b. 1917)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu\" title=\"<PERSON><PERSON>-fu\"><PERSON><PERSON></a>, Taiwanese businessman and diplomat (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu\" title=\"<PERSON><PERSON>-fu\"><PERSON><PERSON></a>, Taiwanese businessman and diplomat (b. 1917)", "links": [{"title": "<PERSON><PERSON>u", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-fu"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Maltese Roman Catholic priest, missionary, and educator (b. 1918)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Egi<PERSON>_<PERSON>\" title=\"Egidio <PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese Roman Catholic priest, missionary, and educator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Egi<PERSON>_<PERSON>\" title=\"Egidio <PERSON>\"><PERSON><PERSON><PERSON></a>, Maltese Roman Catholic priest, missionary, and educator (b. 1918)", "links": [{"title": "Egi<PERSON>", "link": "https://wikipedia.org/wiki/Egidio_Galea"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian diplomat, 2nd Indian National Security Adviser (b. 1936)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian diplomat, 2nd <a href=\"https://wikipedia.org/wiki/National_Security_Adviser_(India)\" class=\"mw-redirect\" title=\"National Security Adviser (India)\">Indian National Security Adviser</a> (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>t\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian diplomat, 2nd <a href=\"https://wikipedia.org/wiki/National_Security_Adviser_(India)\" class=\"mw-redirect\" title=\"National Security Adviser (India)\">Indian National Security Adviser</a> (b. 1936)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Di<PERSON>t"}, {"title": "National Security Adviser (India)", "link": "https://wikipedia.org/wiki/National_Security_Adviser_(India)"}]}, {"year": "2006", "text": "<PERSON>, Papua New Guinean politician, 5th Prime Minister of Papua New Guinea (b. 1954)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea\" title=\"Prime Minister of Papua New Guinea\">Prime Minister of Papua New Guinea</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Papua New Guinean politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea\" title=\"Prime Minister of Papua New Guinea\">Prime Minister of Papua New Guinea</a> (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Papua New Guinea", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Papua_New_Guinea"}]}, {"year": "2007", "text": "<PERSON>, Jr., American businessman and politician, 27th United States Secretary of Commerce (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American businessman and politician, 27th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Commerce\" title=\"United States Secretary of Commerce\">United States Secretary of Commerce</a> (b. 1917)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr."}, {"title": "United States Secretary of Commerce", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Commerce"}]}, {"year": "2008", "text": "<PERSON>, Scottish racing driver (b. 1931)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, Scottish racing driver (b. 1931)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "2008", "text": "<PERSON>, South Korean boxer (b. 1972)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON></a>, South Korean boxer (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON>sa<PERSON>\"><PERSON></a>, South Korean boxer (b. 1972)", "links": [{"title": "<PERSON>m", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>m"}]}, {"year": "2009", "text": "<PERSON>, American philanthropist and photographer (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and photographer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American philanthropist and photographer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American actor (b. 1923)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ngle"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Japanese politician (b. 1969)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gata\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese politician (b. 1969)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gata"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Chilean-German composer and academic (b. 1925)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-German composer and academic (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-German composer and academic (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American theologian and scholar (b. 1928)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and scholar (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American theologian and scholar (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "Vicar, Chilean cartoonist (b. 1934)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Vicar_(cartoonist)\" title=\"Vicar (cartoonist)\">Vicar</a>, Chilean cartoonist (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vicar_(cartoonist)\" title=\"Vicar (cartoonist)\">Vicar</a>, Chilean cartoonist (b. 1934)", "links": [{"title": "Vicar (cartoonist)", "link": "https://wikipedia.org/wiki/Vicar_(cartoonist)"}]}, {"year": "2012", "text": "<PERSON>, American lawyer and judge (b. 1917)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge (b. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, American author and illustrator (b. 1914)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and illustrator (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American author and illustrator (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Czech-Canadian author and publisher (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian author and publisher (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD\" title=\"<PERSON>\"><PERSON></a>, Czech-Canadian author and publisher (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%C5%A0kvoreck%C3%BD"}]}, {"year": "2013", "text": "<PERSON><PERSON>, English soldier and pilot (b. 1913)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and pilot (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English soldier and pilot (b. 1913)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Czech cryptozoologist, explorer, and author (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech cryptozoologist, explorer, and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech cryptozoologist, explorer, and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American general (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Romanian actor, director, and screenwriter (b. 1930)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian actor, director, and screenwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian actor, director, and screenwriter (b. 1930)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American singer and guitarist (b. 1939)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (b. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American economist and author (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist and author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American film producer (b. 1921)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film producer (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American economist and academic (b. 1936)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, American economist and academic (b. 1936)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_(economist)"}]}, {"year": "2015", "text": "<PERSON>, American captain and politician, 47th Massachusetts Attorney General (b. 1919)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 47th <a href=\"https://wikipedia.org/wiki/Massachusetts_Attorney_General\" title=\"Massachusetts Attorney General\">Massachusetts Attorney General</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 47th <a href=\"https://wikipedia.org/wiki/Massachusetts_Attorney_General\" title=\"Massachusetts Attorney General\">Massachusetts Attorney General</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Massachusetts Attorney General", "link": "https://wikipedia.org/wiki/Massachusetts_Attorney_General"}]}, {"year": "2016", "text": "<PERSON>, Canadian-American pianist and composer (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist and composer (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American pianist and composer (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Danish computer scientist, astronomer, and academic (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish computer scientist, astronomer, and academic (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish computer scientist, astronomer, and academic (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1945)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Russian general and diplomat (b. 1957)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and diplomat (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and diplomat (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian politician (b. 1958)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> Ma<PERSON>eva <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian politician (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H._<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>eva <PERSON>\"><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON></a>, Indian politician (b. 1958)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, Australian composer (b. 1933)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian composer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American businessman, co-founder of Southwest Airlines (b. 1931)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Southwest_Airlines\" title=\"Southwest Airlines\">Southwest Airlines</a> (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founder of <a href=\"https://wikipedia.org/wiki/Southwest_Airlines\" title=\"Southwest Airlines\">Southwest Airlines</a> (b. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Southwest Airlines", "link": "https://wikipedia.org/wiki/Southwest_Airlines"}]}, {"year": "2020", "text": "<PERSON><PERSON><PERSON>, Iranian major general, commander of the Iranian Quds Force (b. 1957)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Qasem_Sol<PERSON>ani\" title=\"Qasem Soleimani\"><PERSON><PERSON><PERSON></a>, Iranian major general, commander of the Iranian <a href=\"https://wikipedia.org/wiki/Quds_Force\" title=\"Quds Force\">Quds Force</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qasem_Sol<PERSON>\" title=\"Qasem Soleimani\"><PERSON><PERSON><PERSON></a>, Iranian major general, commander of the Iranian <a href=\"https://wikipedia.org/wiki/Quds_Force\" title=\"Quds Force\">Quds Force</a> (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>asem_<PERSON>eimani"}, {"title": "Quds Force", "link": "https://wikipedia.org/wiki/Quds_Force"}]}, {"year": "2021", "text": "<PERSON>, American author (b. 1961)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, Spanish cancer activist and influencer (b. 2002)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cancer activist and influencer (b. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish cancer activist and influencer (b. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON>, American filmmaker (b. 1977)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American filmmaker (b. 1977)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2025", "text": "<PERSON><PERSON>, American R&B singer-songwriter and keyboard player (b. 1941)", "html": "2025 - <a href=\"https://wikipedia.org/wiki/Brenton_Wood\" title=\"Brenton Wood\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and keyboard player (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brenton_Wood\" title=\"Brenton Wood\"><PERSON><PERSON></a>, American R&amp;B singer-songwriter and keyboard player (b. 1941)", "links": [{"title": "Brenton Wood", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wood"}]}]}}