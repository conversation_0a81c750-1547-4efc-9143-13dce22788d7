{"date": "July 21", "url": "https://wikipedia.org/wiki/July_21", "data": {"Events": [{"year": "356 BC", "text": "The Temple of Artemis in Ephesus, one of the Seven Wonders of the World, is destroyed by arson.", "html": "356 BC - 356 BC - The <a href=\"https://wikipedia.org/wiki/Temple_of_Artemis\" title=\"Temple of Artemis\">Temple of Artemis</a> in <a href=\"https://wikipedia.org/wiki/Ephesus\" title=\"Ephesus\"><PERSON><PERSON><PERSON></a>, one of the <a href=\"https://wikipedia.org/wiki/Wonders_of_the_World\" title=\"Wonders of the World\">Seven Wonders of the World</a>, is destroyed by <a href=\"https://wikipedia.org/wiki/Arson\" title=\"Arson\">arson</a>.", "no_year_html": "356 BC - The <a href=\"https://wikipedia.org/wiki/Temple_of_Artemis\" title=\"Temple of Artemis\">Temple of Artemis</a> in <a href=\"https://wikipedia.org/wiki/Ephesus\" title=\"Ephesus\"><PERSON><PERSON><PERSON></a>, one of the <a href=\"https://wikipedia.org/wiki/Wonders_of_the_World\" title=\"Wonders of the World\">Seven Wonders of the World</a>, is destroyed by <a href=\"https://wikipedia.org/wiki/Arson\" title=\"Arson\">arson</a>.", "links": [{"title": "Temple of Artemis", "link": "https://wikipedia.org/wiki/Temple_of_Artemis"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ephesus"}, {"title": "Wonders of the World", "link": "https://wikipedia.org/wiki/Wonders_of_the_World"}, {"title": "Arson", "link": "https://wikipedia.org/wiki/Arson"}]}, {"year": "230", "text": "Pope <PERSON><PERSON> succeeds <PERSON> as the eighteenth pope. After being exiled to Sardinia, he became the first pope to resign his office.", "html": "230 - <a href=\"https://wikipedia.org/wiki/Pope_Pontian\" title=\"Pope Pontian\">Pope <PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Urban_I\" title=\"Pope Urban I\">Urban I</a> as the eighteenth <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a>. After being exiled to Sardinia, he became the first pope to resign his office.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_Pontian\" title=\"Pope Pontian\">Pope <PERSON></a> succeeds <a href=\"https://wikipedia.org/wiki/Pope_Urban_I\" title=\"Pope Urban I\">Urban I</a> as the eighteenth <a href=\"https://wikipedia.org/wiki/Pope\" title=\"Pope\">pope</a>. After being exiled to Sardinia, he became the first pope to resign his office.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope_Pontian"}, {"title": "Pope Urban I", "link": "https://wikipedia.org/wiki/Pope_Urban_I"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pope"}]}, {"year": "285", "text": "<PERSON><PERSON><PERSON><PERSON> appoints <PERSON><PERSON> as Caesar and co-ruler.", "html": "285 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/<PERSON>ian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\"><PERSON></a> and co-ruler.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> appoints <a href=\"https://wikipedia.org/wiki/Maximian\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as <a href=\"https://wikipedia.org/wiki/Caesar_(title)\" title=\"Caesar (title)\">Caesar</a> and co-ruler.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ian"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ian"}, {"title": "<PERSON> (title)", "link": "https://wikipedia.org/wiki/<PERSON>_(title)"}]}, {"year": "365", "text": "The 365 Crete earthquake affected the Greek island of Crete with a maximum Mercalli intensity of XI (Extreme), causing a destructive tsunami that affects the coasts of Libya and Egypt, especially Alexandria. Many thousands are killed.", "html": "365 - The <a href=\"https://wikipedia.org/wiki/365_Crete_earthquake\" title=\"365 Crete earthquake\">365 Crete earthquake</a> affected the Greek island of Crete with a maximum Mercalli intensity of XI (<i>Extreme</i>), causing a destructive tsunami that affects the coasts of Libya and Egypt, especially <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>. Many thousands are killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/365_Crete_earthquake\" title=\"365 Crete earthquake\">365 Crete earthquake</a> affected the Greek island of Crete with a maximum Mercalli intensity of XI (<i>Extreme</i>), causing a destructive tsunami that affects the coasts of Libya and Egypt, especially <a href=\"https://wikipedia.org/wiki/Alexandria\" title=\"Alexandria\">Alexandria</a>. Many thousands are killed.", "links": [{"title": "365 Crete earthquake", "link": "https://wikipedia.org/wiki/365_Crete_earthquake"}, {"title": "Alexandria", "link": "https://wikipedia.org/wiki/Alexandria"}]}, {"year": "905", "text": "King <PERSON><PERSON><PERSON> I of Italy and a hired Hungarian army defeats the Frankish forces at Verona. King <PERSON> is captured and blinded for breaking his oath (see 902).", "html": "905 - King <a href=\"https://wikipedia.org/wiki/Berengar_I_of_Italy\" title=\"Berengar I of Italy\"><PERSON><PERSON><PERSON> of Italy</a> and a hired <a href=\"https://wikipedia.org/wiki/Principality_of_Hungary\" title=\"Principality of Hungary\">Hungarian</a> army defeats the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish</a> forces at <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>. King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is captured and blinded for breaking his oath (see <a href=\"https://wikipedia.org/wiki/902\" title=\"902\">902</a>).", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/Berengar_I_of_Italy\" title=\"Berengar I of Italy\"><PERSON><PERSON><PERSON> of Italy</a> and a hired <a href=\"https://wikipedia.org/wiki/Principality_of_Hungary\" title=\"Principality of Hungary\">Hungarian</a> army defeats the <a href=\"https://wikipedia.org/wiki/Francia\" title=\"Francia\">Frankish</a> forces at <a href=\"https://wikipedia.org/wiki/Verona\" title=\"Verona\">Verona</a>. King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> the Blind\"><PERSON></a> is captured and blinded for breaking his oath (see <a href=\"https://wikipedia.org/wiki/902\" title=\"902\">902</a>).", "links": [{"title": "<PERSON><PERSON><PERSON> of Italy", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_I_of_Italy"}, {"title": "Principality of Hungary", "link": "https://wikipedia.org/wiki/Principality_of_Hungary"}, {"title": "Francia", "link": "https://wikipedia.org/wiki/Francia"}, {"title": "Verona", "link": "https://wikipedia.org/wiki/Verona"}, {"title": "Louis the Blind", "link": "https://wikipedia.org/wiki/<PERSON>_the_Blind"}, {"title": "902", "link": "https://wikipedia.org/wiki/902"}]}, {"year": "1242", "text": "Battle of Taillebourg: <PERSON> of France puts an end to the revolt of his vassals <PERSON> of England and <PERSON> of Lusignan.", "html": "1242 - <a href=\"https://wikipedia.org/wiki/Battle_of_Taillebourg\" title=\"Battle of Taillebourg\">Battle of Taillebourg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> puts an end to the revolt of his vassals <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>sign<PERSON>\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Taillebourg\" title=\"Battle of Taillebourg\">Battle of Taillebourg</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_France\" title=\"<PERSON> of France\"><PERSON> of France</a> puts an end to the revolt of his vassals <a href=\"https://wikipedia.org/wiki/<PERSON>_III_of_England\" title=\"<PERSON> III of England\"><PERSON> of England</a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>.", "links": [{"title": "Battle of Taillebourg", "link": "https://wikipedia.org/wiki/Battle_of_Taillebourg"}, {"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_IX_of_France"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "<PERSON> of Lusignan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1403", "text": "Battle of Shrewsbury: King <PERSON> of England defeats rebels to the north of the county town of Shropshire, England.", "html": "1403 - <a href=\"https://wikipedia.org/wiki/Battle_of_Shrewsbury\" title=\"Battle of Shrewsbury\">Battle of Shrewsbury</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> IV of England</a> defeats rebels to the north of the county town of <a href=\"https://wikipedia.org/wiki/Shropshire\" title=\"Shropshire\">Shropshire</a>, England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Shrewsbury\" title=\"Battle of Shrewsbury\">Battle of Shrewsbury</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_IV_of_England\" title=\"<PERSON> IV of England\"><PERSON> of England</a> defeats rebels to the north of the county town of <a href=\"https://wikipedia.org/wiki/Shropshire\" title=\"Shropshire\">Shropshire</a>, England.", "links": [{"title": "Battle of Shrewsbury", "link": "https://wikipedia.org/wiki/Battle_of_Shrewsbury"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_IV_of_England"}, {"title": "Shropshire", "link": "https://wikipedia.org/wiki/Shropshire"}]}, {"year": "1545", "text": "The first landing of French troops on the coast of the Isle of Wight during the French invasion of the Isle of Wight.", "html": "1545 - The first landing of French troops on the coast of the <a href=\"https://wikipedia.org/wiki/Isle_of_Wight\" title=\"Isle of Wight\">Isle of Wight</a> during the <a href=\"https://wikipedia.org/wiki/French_invasion_of_the_Isle_of_Wight\" title=\"French invasion of the Isle of Wight\">French invasion of the Isle of Wight</a>.", "no_year_html": "The first landing of French troops on the coast of the <a href=\"https://wikipedia.org/wiki/Isle_of_Wight\" title=\"Isle of Wight\">Isle of Wight</a> during the <a href=\"https://wikipedia.org/wiki/French_invasion_of_the_Isle_of_Wight\" title=\"French invasion of the Isle of Wight\">French invasion of the Isle of Wight</a>.", "links": [{"title": "Isle of Wight", "link": "https://wikipedia.org/wiki/Isle_of_Wight"}, {"title": "French invasion of the Isle of Wight", "link": "https://wikipedia.org/wiki/French_invasion_of_the_Isle_of_Wight"}]}, {"year": "1568", "text": "Eighty Years' War: Battle of Jemmingen: <PERSON>, Duke of Alva defeats <PERSON> of Nassau.", "html": "1568 - <a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jemmingen\" title=\"Battle of Jemmingen\">Battle of Jemmingen</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>,_3rd_Duke_<PERSON>_Alba\" title=\"<PERSON>, 3rd Duke of Alba\"><PERSON>, Duke of Alva</a> defeats <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_Nassau\" title=\"<PERSON> Nassau\"><PERSON> Nassau</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eighty_Years%27_War\" title=\"Eighty Years' War\">Eighty Years' War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Jemmingen\" title=\"Battle of Jemmingen\">Battle of Jemmingen</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba\" title=\"<PERSON>, 3rd Duke of Alba\"><PERSON>, Duke of Alva</a> defeats <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>_Nassau\" title=\"<PERSON> Nassau\"><PERSON> Nassau</a>.", "links": [{"title": "Eighty Years' War", "link": "https://wikipedia.org/wiki/Eighty_Years%27_War"}, {"title": "Battle of Jemmingen", "link": "https://wikipedia.org/wiki/Battle_of_Jemmingen"}, {"title": "<PERSON>, 3rd Duke of Alba", "link": "https://wikipedia.org/wiki/<PERSON>_%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_3rd_Duke_of_Alba"}, {"title": "Louis of Nassau", "link": "https://wikipedia.org/wiki/Louis_of_Nassau"}]}, {"year": "1645", "text": "Qing dynasty regent <PERSON><PERSON> issues an edict ordering all Han Chinese men to shave their forehead and braid the rest of their hair into a queue identical to those of the Manchus.", "html": "1645 - <a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> regent <a href=\"https://wikipedia.org/wiki/Dorgon\" title=\"Dorgon\"><PERSON><PERSON></a> issues an <a href=\"https://wikipedia.org/wiki/Queue_(hairstyle)#Queue_order\" title=\"Queue (hairstyle)\">edict</a> ordering all <a href=\"https://wikipedia.org/wiki/Han_Chinese\" title=\"Han Chinese\">Han Chinese</a> men to shave their forehead and braid the rest of their hair into a queue identical to those of the <a href=\"https://wikipedia.org/wiki/Manchus\" class=\"mw-redirect\" title=\"Manchus\">Manchus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Qing_dynasty\" title=\"Qing dynasty\">Qing dynasty</a> regent <a href=\"https://wikipedia.org/wiki/Dorgon\" title=\"Dorgon\"><PERSON><PERSON></a> issues an <a href=\"https://wikipedia.org/wiki/Queue_(hairstyle)#Queue_order\" title=\"Queue (hairstyle)\">edict</a> ordering all <a href=\"https://wikipedia.org/wiki/Han_Chinese\" title=\"Han Chinese\">Han Chinese</a> men to shave their forehead and braid the rest of their hair into a queue identical to those of the <a href=\"https://wikipedia.org/wiki/Manchus\" class=\"mw-redirect\" title=\"Manchus\">Manchus</a>.", "links": [{"title": "Qing dynasty", "link": "https://wikipedia.org/wiki/Qing_dynasty"}, {"title": "Dorgon", "link": "https://wikipedia.org/wiki/Do<PERSON>"}, {"title": "Queue (hairstyle)", "link": "https://wikipedia.org/wiki/Queue_(hairstyle)#Queue_order"}, {"title": "Han Chinese", "link": "https://wikipedia.org/wiki/Han_Chinese"}, {"title": "Manchus", "link": "https://wikipedia.org/wiki/Manchus"}]}, {"year": "1656", "text": "The Raid on Málaga takes place during the Anglo-Spanish War.", "html": "1656 - The <a href=\"https://wikipedia.org/wiki/Raid_on_M%C3%A1laga_(1656)\" title=\"Raid on Málaga (1656)\">Raid on Málaga</a> takes place during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Raid_on_M%C3%A1laga_(1656)\" title=\"Raid on Málaga (1656)\">Raid on Málaga</a> takes place during the <a href=\"https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)\" class=\"mw-redirect\" title=\"Anglo-Spanish War (1654-60)\">Anglo-Spanish War</a>.", "links": [{"title": "Raid on Málaga (1656)", "link": "https://wikipedia.org/wiki/Raid_on_M%C3%A1laga_(1656)"}, {"title": "Anglo-Spanish War (1654-60)", "link": "https://wikipedia.org/wiki/Anglo-Spanish_War_(1654%E2%80%9360)"}]}, {"year": "1674", "text": "A Dutch assault on the French island of Martinique is repulsed against all odds.", "html": "1674 - A <a href=\"https://wikipedia.org/wiki/Invasion_of_Martinique_(1674)\" title=\"Invasion of Martinique (1674)\">Dutch assault</a> on the French island of <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martin<PERSON>\"><PERSON><PERSON></a> is repulsed against all odds.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Invasion_of_Martinique_(1674)\" title=\"Invasion of Martinique (1674)\">Dutch assault</a> on the French island of <a href=\"https://wikipedia.org/wiki/Martinique\" title=\"Martinique\"><PERSON><PERSON></a> is repulsed against all odds.", "links": [{"title": "Invasion of Martinique (1674)", "link": "https://wikipedia.org/wiki/Invasion_of_<PERSON><PERSON>_(1674)"}, {"title": "Martinique", "link": "https://wikipedia.org/wiki/<PERSON>ique"}]}, {"year": "1718", "text": "The Treaty of Passarowitz between the Ottoman Empire, Austria and the Republic of Venice is signed.", "html": "1718 - The <a href=\"https://wikipedia.org/wiki/Treaty_of_Passarowitz\" title=\"Treaty of Passarowitz\">Treaty of Passarowitz</a> between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, Austria and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Treaty_of_Passarowitz\" title=\"Treaty of Passarowitz\">Treaty of Passarowitz</a> between the <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman Empire</a>, Austria and the <a href=\"https://wikipedia.org/wiki/Republic_of_Venice\" title=\"Republic of Venice\">Republic of Venice</a> is signed.", "links": [{"title": "Treaty of Passarowitz", "link": "https://wikipedia.org/wiki/Treaty_of_Passarowitz"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Republic of Venice", "link": "https://wikipedia.org/wiki/Republic_of_Venice"}]}, {"year": "1774", "text": "Russo-Turkish War (1768-74): Russia and the Ottoman Empire sign the Treaty of Küçük Kaynarca ending the war.", "html": "1774 - <a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1768%E2%80%9374)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1768-74)\">Russo-Turkish War (1768-74)</a>: Russia and the Ottoman Empire sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_K%C3%BC%C3%A7%C3%BCk_Kaynarca\" title=\"Treaty of Küçük Kaynarca\">Treaty of Küçük Kaynarca</a> ending the war.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russo-Turkish_War_(1768%E2%80%9374)\" class=\"mw-redirect\" title=\"Russo-Turkish War (1768-74)\">Russo-Turkish War (1768-74)</a>: Russia and the Ottoman Empire sign the <a href=\"https://wikipedia.org/wiki/Treaty_of_K%C3%BC%C3%A7%C3%BCk_Kaynarca\" title=\"Treaty of Küçük Kaynarca\">Treaty of Küçük Kaynarca</a> ending the war.", "links": [{"title": "Russo-Turkish War (1768-74)", "link": "https://wikipedia.org/wiki/Russo-Turkish_War_(1768%E2%80%9374)"}, {"title": "Treaty of Küçük Kaynarca", "link": "https://wikipedia.org/wiki/Treaty_of_K%C3%BC%C3%A7%C3%BCk_Kaynarca"}]}, {"year": "1798", "text": "French campaign in Egypt and Syria: <PERSON>'s forces defeat an Ottoman-Mamluk army near Cairo in the Battle of the Pyramids.", "html": "1798 - <a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">French campaign in Egypt and Syria</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> forces defeat an <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a>-<a href=\"https://wikipedia.org/wiki/Mamluk\" title=\"Mamluk\">Mamluk</a> army near <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Pyramids\" title=\"Battle of the Pyramids\">Battle of the Pyramids</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria\" class=\"mw-redirect\" title=\"French campaign in Egypt and Syria\">French campaign in Egypt and Syria</a>: <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON>'s</a> forces defeat an <a href=\"https://wikipedia.org/wiki/Ottoman_Empire\" title=\"Ottoman Empire\">Ottoman</a>-<a href=\"https://wikipedia.org/wiki/Mamluk\" title=\"Mamluk\">Ma<PERSON><PERSON></a> army near <a href=\"https://wikipedia.org/wiki/Cairo\" title=\"Cairo\">Cairo</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_the_Pyramids\" title=\"Battle of the Pyramids\">Battle of the Pyramids</a>.", "links": [{"title": "French campaign in Egypt and Syria", "link": "https://wikipedia.org/wiki/French_campaign_in_Egypt_and_Syria"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Ottoman Empire", "link": "https://wikipedia.org/wiki/Ottoman_Empire"}, {"title": "Mamluk", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Cairo", "link": "https://wikipedia.org/wiki/Cairo"}, {"title": "Battle of the Pyramids", "link": "https://wikipedia.org/wiki/Battle_of_the_Pyramids"}]}, {"year": "1831", "text": "Inauguration of <PERSON> of Belgium, first king of the Belgians.", "html": "1831 - Inauguration of <a href=\"https://wikipedia.org/wiki/Leopold_I_of_Belgium\" title=\"Leopold I of Belgium\"><PERSON> of Belgium</a>, first king of the <a href=\"https://wikipedia.org/wiki/Belgians\" title=\"Belgians\">Belgians</a>.", "no_year_html": "Inauguration of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Belgium\" title=\"Leopold I of Belgium\"><PERSON> of Belgium</a>, first king of the <a href=\"https://wikipedia.org/wiki/Belgians\" title=\"Belgians\">Belgians</a>.", "links": [{"title": "<PERSON> of Belgium", "link": "https://wikipedia.org/wiki/Leopold_I_of_Belgium"}, {"title": "Belgians", "link": "https://wikipedia.org/wiki/Belgians"}]}, {"year": "1861", "text": "American Civil War: First Battle of Bull Run: At Manassas Junction, Virginia, the first major battle of the war begins and ends in a victory for the Confederate army.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>: At <a href=\"https://wikipedia.org/wiki/Manassas,_Virginia\" title=\"Manassas, Virginia\">Manassas Junction, Virginia</a>, the first major battle of the war begins and ends in a victory for the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/First_Battle_of_Bull_Run\" title=\"First Battle of Bull Run\">First Battle of Bull Run</a>: At <a href=\"https://wikipedia.org/wiki/Manassas,_Virginia\" title=\"Manassas, Virginia\">Manassas Junction, Virginia</a>, the first major battle of the war begins and ends in a victory for the <a href=\"https://wikipedia.org/wiki/Confederate_States_of_America\" title=\"Confederate States of America\">Confederate</a> army.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "First Battle of Bull Run", "link": "https://wikipedia.org/wiki/First_Battle_of_Bull_Run"}, {"title": "Manassas, Virginia", "link": "https://wikipedia.org/wiki/Manassas,_Virginia"}, {"title": "Confederate States of America", "link": "https://wikipedia.org/wiki/Confederate_States_of_America"}]}, {"year": "1865", "text": "In the market square of Springfield, Missouri, <PERSON> shoots and kills <PERSON> in what is regarded as the first western showdown.", "html": "1865 - In the market square of <a href=\"https://wikipedia.org/wiki/Springfield,_Missouri\" title=\"Springfield, Missouri\">Springfield, Missouri</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Bill_<PERSON>\" title=\"Wild Bill <PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wild_Bill_<PERSON>_%E2%80%93_<PERSON>_<PERSON>_shootout\" class=\"mw-redirect\" title=\"Wild Bill <PERSON> - Davis Tutt shootout\">shoots and kills</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in what is regarded as the first western showdown.", "no_year_html": "In the market square of <a href=\"https://wikipedia.org/wiki/Springfield,_Missouri\" title=\"Springfield, Missouri\">Springfield, Missouri</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_Bill_<PERSON>\" title=\"Wild <PERSON>\"><PERSON></a> <a href=\"https://wikipedia.org/wiki/Wild_Bill_<PERSON>_%E2%80%93_<PERSON>_<PERSON>_shootout\" class=\"mw-redirect\" title=\"Wild Bill <PERSON> - Davis Tutt shootout\">shoots and kills</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in what is regarded as the first western showdown.", "links": [{"title": "Springfield, Missouri", "link": "https://wikipedia.org/wiki/Springfield,_Missouri"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON> <PERSON> - <PERSON> shootout", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_%E2%80%93_<PERSON>_<PERSON>_shootout"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1873", "text": "At Adair, Iowa, <PERSON> and the James-Younger Gang pull off the first successful train robbery in the American Old West.", "html": "1873 - At <a href=\"https://wikipedia.org/wiki/Adair,_Iowa\" title=\"Adair, Iowa\">Adair, Iowa</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/James%E2%80%93Younger_Gang\" title=\"James-Younger Gang\"><PERSON>-Younger Gang</a> pull off the first successful <a href=\"https://wikipedia.org/wiki/Train_robbery\" title=\"Train robbery\">train robbery</a> in the <a href=\"https://wikipedia.org/wiki/American_Frontier\" class=\"mw-redirect\" title=\"American Frontier\">American Old West</a>.", "no_year_html": "At <a href=\"https://wikipedia.org/wiki/Adair,_Iowa\" title=\"Adair, Iowa\">Adair, Iowa</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and the <a href=\"https://wikipedia.org/wiki/James%E2%80%93Younger_Gang\" title=\"James-Younger Gang\"><PERSON>-Younger Gang</a> pull off the first successful <a href=\"https://wikipedia.org/wiki/Train_robbery\" title=\"Train robbery\">train robbery</a> in the <a href=\"https://wikipedia.org/wiki/American_Frontier\" class=\"mw-redirect\" title=\"American Frontier\">American Old West</a>.", "links": [{"title": "Adair, Iowa", "link": "https://wikipedia.org/wiki/Adair,_Iowa"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>-Younger Gang", "link": "https://wikipedia.org/wiki/James%E2%80%93You<PERSON>_<PERSON>"}, {"title": "Train robbery", "link": "https://wikipedia.org/wiki/Train_robbery"}, {"title": "American Frontier", "link": "https://wikipedia.org/wiki/American_Frontier"}]}, {"year": "1877", "text": "After rioting by Baltimore and Ohio Railroad workers and the deaths of nine rail workers at the hands of the Maryland militia, workers in Pittsburgh, Pennsylvania, stage a sympathy strike that is met with an assault by the state militia.", "html": "1877 - After rioting by <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a> workers and the <a href=\"https://wikipedia.org/wiki/Great_Railroad_Strike_of_1877\" title=\"Great Railroad Strike of 1877\">deaths of nine rail workers</a> at the hands of the <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a> militia, workers in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a>, stage a <a href=\"https://wikipedia.org/wiki/Sympathy_strike\" class=\"mw-redirect\" title=\"Sympathy strike\">sympathy strike</a> that is met with an assault by the state militia.", "no_year_html": "After rioting by <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a> workers and the <a href=\"https://wikipedia.org/wiki/Great_Railroad_Strike_of_1877\" title=\"Great Railroad Strike of 1877\">deaths of nine rail workers</a> at the hands of the <a href=\"https://wikipedia.org/wiki/Maryland\" title=\"Maryland\">Maryland</a> militia, workers in <a href=\"https://wikipedia.org/wiki/Pittsburgh\" title=\"Pittsburgh\">Pittsburgh, Pennsylvania</a>, stage a <a href=\"https://wikipedia.org/wiki/Sympathy_strike\" class=\"mw-redirect\" title=\"Sympathy strike\">sympathy strike</a> that is met with an assault by the state militia.", "links": [{"title": "Baltimore and Ohio Railroad", "link": "https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad"}, {"title": "Great Railroad Strike of 1877", "link": "https://wikipedia.org/wiki/Great_Railroad_Strike_of_1877"}, {"title": "Maryland", "link": "https://wikipedia.org/wiki/Maryland"}, {"title": "Pittsburgh", "link": "https://wikipedia.org/wiki/Pittsburgh"}, {"title": "Sympathy strike", "link": "https://wikipedia.org/wiki/Sympathy_strike"}]}, {"year": "1904", "text": "<PERSON>, a Frenchman, becomes the first man to break the 100 mph (161 km/h) barrier on land. He drove a 15-liter Gobron-Brillié in Ostend, Belgium.", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Frenchman, becomes the first man to break the 100 mph (161 km/h) barrier on land. He drove a 15-liter <a href=\"https://wikipedia.org/wiki/Gobron-Brilli%C3%A9\" title=\"Gobron-Brillié\">Gobron-Brillié</a> in <a href=\"https://wikipedia.org/wiki/Ostend\" title=\"Ostend\">Ostend</a>, Belgium.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a Frenchman, becomes the first man to break the 100 mph (161 km/h) barrier on land. He drove a 15-liter <a href=\"https://wikipedia.org/wiki/Gobron-Brilli%C3%A9\" title=\"Gobron-Brillié\">Gobron-Brillié</a> in <a href=\"https://wikipedia.org/wiki/Ostend\" title=\"Ostend\">Ostend</a>, Belgium.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Gobron-Brillié", "link": "https://wikipedia.org/wiki/Gobron-Brilli%C3%A9"}, {"title": "Ostend", "link": "https://wikipedia.org/wiki/Ostend"}]}, {"year": "1907", "text": "The passenger steamer SS Columbia sinks after colliding with the steam schooner San Pedro off Shelter Cove, California, killing 88 people.", "html": "1907 - The passenger steamer <a href=\"https://wikipedia.org/wiki/SS_Columbia_(1880)\" title=\"SS Columbia (1880)\">SS <i>Columbia</i></a> sinks after colliding with the steam schooner <i>San Pedro</i> off <a href=\"https://wikipedia.org/wiki/Shelter_Cove,_California\" title=\"Shelter Cove, California\">Shelter Cove, California</a>, killing 88 people.", "no_year_html": "The passenger steamer <a href=\"https://wikipedia.org/wiki/SS_Columbia_(1880)\" title=\"SS Columbia (1880)\">SS <i>Columbia</i></a> sinks after colliding with the steam schooner <i>San Pedro</i> off <a href=\"https://wikipedia.org/wiki/Shelter_Cove,_California\" title=\"Shelter Cove, California\">Shelter Cove, California</a>, killing 88 people.", "links": [{"title": "SS Columbia (1880)", "link": "https://wikipedia.org/wiki/SS_Columbia_(1880)"}, {"title": "Shelter Cove, California", "link": "https://wikipedia.org/wiki/Shelter_Cove,_California"}]}, {"year": "1919", "text": "The dirigible Wingfoot Air Express crashes into the Illinois Trust and Savings Building in Chicago, killing 12 people.", "html": "1919 - The <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">dirigible</a> <i><a href=\"https://wikipedia.org/wiki/Wingfoot_Air_Express_crash\" title=\"Wingfoot Air Express crash\">Wingfoot Air Express</a></i> crashes into the Illinois Trust and Savings Building in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, killing 12 people.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Airship\" title=\"Airship\">dirigible</a> <i><a href=\"https://wikipedia.org/wiki/Wingfoot_Air_Express_crash\" title=\"Wingfoot Air Express crash\">Wingfoot Air Express</a></i> crashes into the Illinois Trust and Savings Building in <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a>, killing 12 people.", "links": [{"title": "Airship", "link": "https://wikipedia.org/wiki/Airship"}, {"title": "Wingfoot Air Express crash", "link": "https://wikipedia.org/wiki/Wingfoot_Air_Express_crash"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}]}, {"year": "1920", "text": "The \"Belfast Pogrom\" begins two years of violence with the expulsion of thousands of Catholic shipyard, factory and linen mill workers from their jobs.", "html": "1920 - The \"<a href=\"https://wikipedia.org/wiki/Belfast_Pogrom\" class=\"mw-redirect\" title=\"Belfast Pogrom\">Belfast Pogrom</a>\" begins two years of violence with the expulsion of thousands of <a href=\"https://wikipedia.org/wiki/Catholic_Church_in_Ireland\" title=\"Catholic Church in Ireland\">Catholic</a> shipyard, factory and linen mill workers from their jobs.", "no_year_html": "The \"<a href=\"https://wikipedia.org/wiki/Belfast_Pogrom\" class=\"mw-redirect\" title=\"Belfast Pogrom\">Belfast Pogrom</a>\" begins two years of violence with the expulsion of thousands of <a href=\"https://wikipedia.org/wiki/Catholic_Church_in_Ireland\" title=\"Catholic Church in Ireland\">Catholic</a> shipyard, factory and linen mill workers from their jobs.", "links": [{"title": "Belfast Pogrom", "link": "https://wikipedia.org/wiki/Belfast_Pogrom"}, {"title": "Catholic Church in Ireland", "link": "https://wikipedia.org/wiki/Catholic_Church_in_Ireland"}]}, {"year": "1925", "text": "Scopes Trial: In Dayton, Tennessee, high school biology teacher <PERSON> is found guilty of teaching human evolution in class and fined $100.", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Scopes_Trial\" class=\"mw-redirect\" title=\"Scopes Trial\">Scopes Trial</a>: In <a href=\"https://wikipedia.org/wiki/Dayton,_Tennessee\" title=\"Dayton, Tennessee\">Dayton, Tennessee</a>, high school biology teacher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found guilty of teaching <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in class and fined $100.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Scopes_Trial\" class=\"mw-redirect\" title=\"Scopes Trial\">Scopes Trial</a>: In <a href=\"https://wikipedia.org/wiki/Dayton,_Tennessee\" title=\"Dayton, Tennessee\">Dayton, Tennessee</a>, high school biology teacher <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is found guilty of teaching <a href=\"https://wikipedia.org/wiki/Human_evolution\" title=\"Human evolution\">human evolution</a> in class and fined $100.", "links": [{"title": "Scopes Trial", "link": "https://wikipedia.org/wiki/Scopes_Trial"}, {"title": "Dayton, Tennessee", "link": "https://wikipedia.org/wiki/Dayton,_Tennessee"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Human evolution", "link": "https://wikipedia.org/wiki/Human_evolution"}]}, {"year": "1925", "text": "<PERSON> becomes the first man to exceed 150 mph (241 km/h) on land. At Pendine Sands in Wales, he drives Sunbeam 350HP built by Sunbeam at a two-way average speed of 150.33 mph (242 km/h).", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first man to exceed 150 mph (241 km/h) on land. At <a href=\"https://wikipedia.org/wiki/Pendine_Sands\" title=\"Pendine Sands\"><PERSON><PERSON></a> in Wales, he drives <a href=\"https://wikipedia.org/wiki/Sunbeam_350HP\" title=\"Sunbeam 350HP\">Sunbeam 350HP</a> built by <a href=\"https://wikipedia.org/wiki/Sunbeam_Motor_Car_Company\" title=\"Sunbeam Motor Car Company\">Sunbeam</a> at a two-way average speed of 150.33 mph (242 km/h).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first man to exceed 150 mph (241 km/h) on land. At <a href=\"https://wikipedia.org/wiki/Pendine_Sands\" title=\"Pendine Sands\"><PERSON><PERSON></a> in Wales, he drives <a href=\"https://wikipedia.org/wiki/Sunbeam_350HP\" title=\"Sunbeam 350HP\">Sunbeam 350HP</a> built by <a href=\"https://wikipedia.org/wiki/Sunbeam_Motor_Car_Company\" title=\"Sunbeam Motor Car Company\">Sunbeam</a> at a two-way average speed of 150.33 mph (242 km/h).", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pendine Sands", "link": "https://wikipedia.org/wiki/Pendine_Sands"}, {"title": "Sunbeam 350HP", "link": "https://wikipedia.org/wiki/Sunbeam_350HP"}, {"title": "Sunbeam Motor Car Company", "link": "https://wikipedia.org/wiki/Sunbeam_Motor_Car_Company"}]}, {"year": "1936", "text": "Spanish Civil War: The Central Committee of Antifascist Militias of Catalonia is constituted, establishing an anarcho-syndicalist economy in Catalonia.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia\" title=\"Central Committee of Antifascist Militias of Catalonia\">Central Committee of Antifascist Militias of Catalonia</a> is constituted, establishing an <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> economy in <a href=\"https://wikipedia.org/wiki/Revolutionary_Catalonia\" title=\"Revolutionary Catalonia\">Catalonia</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Spanish_Civil_War\" title=\"Spanish Civil War\">Spanish Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia\" title=\"Central Committee of Antifascist Militias of Catalonia\">Central Committee of Antifascist Militias of Catalonia</a> is constituted, establishing an <a href=\"https://wikipedia.org/wiki/Anarcho-syndicalist\" class=\"mw-redirect\" title=\"Anarcho-syndicalist\">anarcho-syndicalist</a> economy in <a href=\"https://wikipedia.org/wiki/Revolutionary_Catalonia\" title=\"Revolutionary Catalonia\">Catalonia</a>.", "links": [{"title": "Spanish Civil War", "link": "https://wikipedia.org/wiki/Spanish_Civil_War"}, {"title": "Central Committee of Antifascist Militias of Catalonia", "link": "https://wikipedia.org/wiki/Central_Committee_of_Antifascist_Militias_of_Catalonia"}, {"title": "Anarcho-syndicalist", "link": "https://wikipedia.org/wiki/Anarcho-syndicalist"}, {"title": "Revolutionary Catalonia", "link": "https://wikipedia.org/wiki/Revolutionary_Catalonia"}]}, {"year": "1944", "text": "World War II: Battle of Guam: American troops land on Guam, starting a battle that will end on August 10.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Guam_(1944)\" title=\"Battle of Guam (1944)\">Battle of Guam</a>: American troops land on <a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a>, starting a battle that will end on <a href=\"https://wikipedia.org/wiki/August_10\" title=\"August 10\">August 10</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Guam_(1944)\" title=\"Battle of Guam (1944)\">Battle of Guam</a>: American troops land on <a href=\"https://wikipedia.org/wiki/Guam\" title=\"Guam\">Guam</a>, starting a battle that will end on <a href=\"https://wikipedia.org/wiki/August_10\" title=\"August 10\">August 10</a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Battle of Guam (1944)", "link": "https://wikipedia.org/wiki/Battle_of_Guam_(1944)"}, {"title": "Guam", "link": "https://wikipedia.org/wiki/Guam"}, {"title": "August 10", "link": "https://wikipedia.org/wiki/August_10"}]}, {"year": "1944", "text": "World War II: <PERSON> and four fellow conspirators are executed for the July 20 plot to assassinate <PERSON>.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and four fellow conspirators are executed for the <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">July 20 plot</a> to assassinate <PERSON>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and four fellow conspirators are executed for the <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">July 20 plot</a> to assassinate <PERSON>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "20 July plot", "link": "https://wikipedia.org/wiki/20_July_plot"}]}, {"year": "1949", "text": "The United States Senate ratifies the North Atlantic Treaty.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> ratifies the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Treaty\" title=\"North Atlantic Treaty\">North Atlantic Treaty</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> ratifies the <a href=\"https://wikipedia.org/wiki/North_Atlantic_Treaty\" title=\"North Atlantic Treaty\">North Atlantic Treaty</a>.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "North Atlantic Treaty", "link": "https://wikipedia.org/wiki/North_Atlantic_Treaty"}]}, {"year": "1951", "text": "Canadian Pacific Air Lines Flight 3505 disappears while flying from Vancouver to Tokyo. The aircraft and its 37 occupants are never found.", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_3505\" title=\"Canadian Pacific Air Lines Flight 3505\">Canadian Pacific Air Lines Flight 3505</a> disappears while flying from <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a> to <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>. The aircraft and its 37 occupants are never found.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_3505\" title=\"Canadian Pacific Air Lines Flight 3505\">Canadian Pacific Air Lines Flight 3505</a> disappears while flying from <a href=\"https://wikipedia.org/wiki/Vancouver\" title=\"Vancouver\">Vancouver</a> to <a href=\"https://wikipedia.org/wiki/Tokyo\" title=\"Tokyo\">Tokyo</a>. The aircraft and its 37 occupants are never found.", "links": [{"title": "Canadian Pacific Air Lines Flight 3505", "link": "https://wikipedia.org/wiki/Canadian_Pacific_Air_Lines_Flight_3505"}, {"title": "Vancouver", "link": "https://wikipedia.org/wiki/Vancouver"}, {"title": "Tokyo", "link": "https://wikipedia.org/wiki/Tokyo"}]}, {"year": "1952", "text": "The 7.3 Mw  Kern County earthquake strikes Southern California with a maximum Mercalli intensity of XI (Extreme), killing 12 and injuring hundreds.", "html": "1952 - The 7.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1952_Kern_County_earthquake\" title=\"1952 Kern County earthquake\">Kern County earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Southern_California\" title=\"Southern California\">Southern California</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing 12 and injuring hundreds.", "no_year_html": "The 7.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1952_Kern_County_earthquake\" title=\"1952 Kern County earthquake\">Kern County earthquake</a> strikes <a href=\"https://wikipedia.org/wiki/Southern_California\" title=\"Southern California\">Southern California</a> with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of XI (<i>Extreme</i>), killing 12 and injuring hundreds.", "links": [{"title": "1952 Kern County earthquake", "link": "https://wikipedia.org/wiki/1952_Kern_County_earthquake"}, {"title": "Southern California", "link": "https://wikipedia.org/wiki/Southern_California"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1954", "text": "First Indochina War: The Geneva Conference partitions Vietnam into North Vietnam and South Vietnam.", "html": "1954 - <a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Geneva_Conference_(1954)\" class=\"mw-redirect\" title=\"Geneva Conference (1954)\">Geneva Conference</a> partitions <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> into <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_Indochina_War\" title=\"First Indochina War\">First Indochina War</a>: The <a href=\"https://wikipedia.org/wiki/Geneva_Conference_(1954)\" class=\"mw-redirect\" title=\"Geneva Conference (1954)\">Geneva Conference</a> partitions <a href=\"https://wikipedia.org/wiki/Vietnam\" title=\"Vietnam\">Vietnam</a> into <a href=\"https://wikipedia.org/wiki/North_Vietnam\" title=\"North Vietnam\">North Vietnam</a> and <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>.", "links": [{"title": "First Indochina War", "link": "https://wikipedia.org/wiki/First_Indochina_War"}, {"title": "Geneva Conference (1954)", "link": "https://wikipedia.org/wiki/Geneva_Conference_(1954)"}, {"title": "Vietnam", "link": "https://wikipedia.org/wiki/Vietnam"}, {"title": "North Vietnam", "link": "https://wikipedia.org/wiki/North_Vietnam"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}]}, {"year": "1959", "text": "NS Savannah, the first nuclear-powered cargo-passenger ship, is launched as a showcase for <PERSON>'s \"Atoms for Peace\" initiative.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/NS_Savannah\" title=\"NS Savannah\">NS <i>Savannah</i></a>, the first <a href=\"https://wikipedia.org/wiki/Nuclear_marine_propulsion\" title=\"Nuclear marine propulsion\">nuclear-powered</a> cargo-passenger ship, is <a href=\"https://wikipedia.org/wiki/Ship_naming_and_launching\" class=\"mw-redirect\" title=\"Ship naming and launching\">launched</a> as a showcase for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s \"<a href=\"https://wikipedia.org/wiki/Atoms_for_Peace\" title=\"Atoms for Peace\">Atoms for Peace</a>\" initiative.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NS_Savannah\" title=\"NS Savannah\">NS <i>Savannah</i></a>, the first <a href=\"https://wikipedia.org/wiki/Nuclear_marine_propulsion\" title=\"Nuclear marine propulsion\">nuclear-powered</a> cargo-passenger ship, is <a href=\"https://wikipedia.org/wiki/Ship_naming_and_launching\" class=\"mw-redirect\" title=\"Ship naming and launching\">launched</a> as a showcase for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s \"<a href=\"https://wikipedia.org/wiki/Atoms_for_Peace\" title=\"Atoms for Peace\">Atoms for Peace</a>\" initiative.", "links": [{"title": "NS Savannah", "link": "https://wikipedia.org/wiki/NS_Savannah"}, {"title": "Nuclear marine propulsion", "link": "https://wikipedia.org/wiki/Nuclear_marine_propulsion"}, {"title": "Ship naming and launching", "link": "https://wikipedia.org/wiki/Ship_naming_and_launching"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Atoms for Peace", "link": "https://wikipedia.org/wiki/Atoms_for_Peace"}]}, {"year": "1959", "text": "<PERSON> \"<PERSON><PERSON>sie\" <PERSON> becomes the first African-American to play for the Boston Red Sox, the last team to integrate. He came in as a pinch runner for <PERSON> and stayed in as shortstop in a 2-1 loss to the Chicago White Sox.", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/List_of_African-American_firsts\" class=\"mw-redirect\" title=\"List of African-American firsts\">African-American</a> to play for the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a>, the last team to integrate. He came in as a <a href=\"https://wikipedia.org/wiki/Pinch_runner\" title=\"Pinch runner\">pinch runner</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and stayed in as shortstop in a 2-1 loss to the <a href=\"https://wikipedia.org/wiki/Chicago_White_Sox\" title=\"Chicago White Sox\">Chicago White Sox</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON> \"<PERSON><PERSON><PERSON>\" Green</a> becomes the first <a href=\"https://wikipedia.org/wiki/List_of_African-American_firsts\" class=\"mw-redirect\" title=\"List of African-American firsts\">African-American</a> to play for the <a href=\"https://wikipedia.org/wiki/Boston_Red_Sox\" title=\"Boston Red Sox\">Boston Red Sox</a>, the last team to integrate. He came in as a <a href=\"https://wikipedia.org/wiki/Pinch_runner\" title=\"Pinch runner\">pinch runner</a> for <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> and stayed in as shortstop in a 2-1 loss to the <a href=\"https://wikipedia.org/wiki/Chicago_White_Sox\" title=\"Chicago White Sox\">Chicago White Sox</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of African-American firsts", "link": "https://wikipedia.org/wiki/List_of_African-American_firsts"}, {"title": "Boston Red Sox", "link": "https://wikipedia.org/wiki/Boston_Red_Sox"}, {"title": "Pinch runner", "link": "https://wikipedia.org/wiki/Pinch_runner"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vic_We<PERSON>z"}, {"title": "Chicago White Sox", "link": "https://wikipedia.org/wiki/Chicago_White_Sox"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON> is elected Prime Minister of Sri Lanka, becoming the world's first female head of government", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\"><PERSON><PERSON><PERSON> Bandaranai<PERSON></a> is elected Prime Minister of <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, becoming the world's first female head of government", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sirimavo_Bandaranaike\" title=\"Sirimavo Bandaranaike\"><PERSON><PERSON><PERSON> Bandaranai<PERSON></a> is elected Prime Minister of <a href=\"https://wikipedia.org/wiki/Sri_Lanka\" title=\"Sri Lanka\">Sri Lanka</a>, becoming the world's first female head of government", "links": [{"title": "Sirimavo Bandaranaike", "link": "https://wikipedia.org/wiki/Sirimavo_Bandaranaike"}, {"title": "Sri Lanka", "link": "https://wikipedia.org/wiki/Sri_Lanka"}]}, {"year": "1961", "text": "Mercury program: Mercury-Redstone 4 Mission: <PERSON> piloting Liberty Bell 7 becomes the second American to go into space (in a suborbital mission).", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Mercury program</a>: <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_4\" title=\"Mercury-Redstone 4\">Mercury-Redstone 4</a> Mission: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> piloting<i> <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_4\" title=\"Mercury-Redstone 4\">Liberty Bell 7</a></i> becomes the second American to go into space (in a suborbital mission).", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Project_Mercury\" title=\"Project Mercury\">Mercury program</a>: <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_4\" title=\"Mercury-Redstone 4\">Mercury-Redstone 4</a> Mission: <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> piloting<i> <a href=\"https://wikipedia.org/wiki/Mercury-Redstone_4\" title=\"Mercury-Redstone 4\">Liberty Bell 7</a></i> becomes the second American to go into space (in a suborbital mission).", "links": [{"title": "Project Mercury", "link": "https://wikipedia.org/wiki/Project_Mercury"}, {"title": "Mercury-Redstone 4", "link": "https://wikipedia.org/wiki/Mercury-Redstone_4"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mercury-Redstone 4", "link": "https://wikipedia.org/wiki/Mercury-Redstone_4"}]}, {"year": "1961", "text": "Alaska Airlines Flight 779 crashes near Shemya Air Force Base in Shemya, Alaska killing six.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_779\" title=\"Alaska Airlines Flight 779\">Alaska Airlines Flight 779</a> crashes near <a href=\"https://wikipedia.org/wiki/Eareckson_Air_Station\" title=\"Eareckson Air Station\">Shemya Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/Shemya\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a> killing six.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alaska_Airlines_Flight_779\" title=\"Alaska Airlines Flight 779\">Alaska Airlines Flight 779</a> crashes near <a href=\"https://wikipedia.org/wiki/Eareckson_Air_Station\" title=\"Eareckson Air Station\">Shemya Air Force Base</a> in <a href=\"https://wikipedia.org/wiki/Shemya\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Alaska\" title=\"Alaska\">Alaska</a> killing six.", "links": [{"title": "Alaska Airlines Flight 779", "link": "https://wikipedia.org/wiki/Alaska_Airlines_Flight_779"}, {"title": "Eareckson Air Station", "link": "https://wikipedia.org/wiki/Eareckson_Air_Station"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>a"}, {"title": "Alaska", "link": "https://wikipedia.org/wiki/Alaska"}]}, {"year": "1964", "text": "A series of racial riots break out in Singapore. In the next six weeks, 23 die with 454 others injured.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/1964_race_riots_in_Singapore\" title=\"1964 race riots in Singapore\">A series of racial riots</a> break out in Singapore. In the next six weeks, 23 die with 454 others injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1964_race_riots_in_Singapore\" title=\"1964 race riots in Singapore\">A series of racial riots</a> break out in Singapore. In the next six weeks, 23 die with 454 others injured.", "links": [{"title": "1964 race riots in Singapore", "link": "https://wikipedia.org/wiki/1964_race_riots_in_Singapore"}]}, {"year": "1969", "text": "Apollo program: At 02:56 UTC, astronaut <PERSON> becomes the first person to walk on the Moon, followed 19 minutes later by <PERSON> \"<PERSON>\" <PERSON>.", "html": "1969 - <a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: At 02:56 UTC, astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Apollo_11#Lunar_surface_operations\" title=\"Apollo 11\">first person to walk on the Moon</a>, followed 19 minutes later by <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Buzz%22_Aldrin\" class=\"mw-redirect\" title='<PERSON> \"Buzz\" Aldrin'><PERSON> \"Buzz\" Aldrin</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Apollo_program\" title=\"Apollo program\">Apollo program</a>: At 02:56 UTC, astronaut <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the <a href=\"https://wikipedia.org/wiki/Apollo_11#Lunar_surface_operations\" title=\"Apollo 11\">first person to walk on the Moon</a>, followed 19 minutes later by <a href=\"https://wikipedia.org/wiki/<PERSON>_%22Buzz%22_Aldrin\" class=\"mw-redirect\" title='<PERSON> \"Buzz\" Aldrin'><PERSON> \"Buzz\" Aldrin</a>.", "links": [{"title": "Apollo program", "link": "https://wikipedia.org/wiki/Apollo_program"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Apollo 11", "link": "https://wikipedia.org/wiki/Apollo_11#Lunar_surface_operations"}, {"title": "<PERSON> \"<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22Buzz%22_<PERSON><PERSON><PERSON>"}]}, {"year": "1970", "text": "After 11 years of construction, the Aswan High Dam in Egypt is completed.", "html": "1970 - After 11 years of construction, the <a href=\"https://wikipedia.org/wiki/Aswan_Dam\" title=\"Aswan Dam\">Aswan High Dam</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> is completed.", "no_year_html": "After 11 years of construction, the <a href=\"https://wikipedia.org/wiki/Aswan_Dam\" title=\"Aswan Dam\">Aswan High Dam</a> in <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a> is completed.", "links": [{"title": "Aswan Dam", "link": "https://wikipedia.org/wiki/Aswan_Dam"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "1972", "text": "The Troubles: Bloody Friday: The Provisional IRA detonate 22 bombs in central Belfast, Northern Ireland, United Kingdom in the space of 80 minutes, killing nine and injuring 130.", "html": "1972 - <a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Bloody_Friday_(1972)\" title=\"Bloody Friday (1972)\">Bloody Friday</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonate 22 bombs in central <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast, Northern Ireland, United Kingdom</a> in the space of 80 minutes, killing nine and injuring 130.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Troubles\" title=\"The Troubles\">The Troubles</a>: <a href=\"https://wikipedia.org/wiki/Bloody_Friday_(1972)\" title=\"Bloody Friday (1972)\">Bloody Friday</a>: The <a href=\"https://wikipedia.org/wiki/Provisional_Irish_Republican_Army\" title=\"Provisional Irish Republican Army\">Provisional IRA</a> detonate 22 bombs in central <a href=\"https://wikipedia.org/wiki/Belfast\" title=\"Belfast\">Belfast, Northern Ireland, United Kingdom</a> in the space of 80 minutes, killing nine and injuring 130.", "links": [{"title": "The Troubles", "link": "https://wikipedia.org/wiki/The_Troubles"}, {"title": "Bloody Friday (1972)", "link": "https://wikipedia.org/wiki/Bloody_Friday_(1972)"}, {"title": "Provisional Irish Republican Army", "link": "https://wikipedia.org/wiki/Provisional_Irish_Republican_Army"}, {"title": "Belfast", "link": "https://wikipedia.org/wiki/Belfast"}]}, {"year": "1973", "text": "In Lillehammer, Norway, Mossad agents kill a waiter whom they mistakenly thought was involved in the 1972 Munich Olympics Massacre.", "html": "1973 - In <a href=\"https://wikipedia.org/wiki/Lillehammer_affair\" title=\"Lillehammer affair\">Lillehammer, Norway</a>, <a href=\"https://wikipedia.org/wiki/Mossad\" title=\"Mossad\">Mossad</a> agents kill a waiter whom they mistakenly thought was involved in the 1972 <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich Olympics Massacre</a>.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Lillehammer_affair\" title=\"Lillehammer affair\">Lillehammer, Norway</a>, <a href=\"https://wikipedia.org/wiki/Mossad\" title=\"Mossad\">Mossad</a> agents kill a waiter whom they mistakenly thought was involved in the 1972 <a href=\"https://wikipedia.org/wiki/Munich_massacre\" title=\"Munich massacre\">Munich Olympics Massacre</a>.", "links": [{"title": "Lillehammer affair", "link": "https://wikipedia.org/wiki/Lillehammer_affair"}, {"title": "Mossad", "link": "https://wikipedia.org/wiki/Mossad"}, {"title": "Munich massacre", "link": "https://wikipedia.org/wiki/Munich_massacre"}]}, {"year": "1976", "text": "<PERSON>, the British ambassador to the Republic of Ireland, is assassinated by the Provisional IRA.", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the British ambassador to the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>, is assassinated by the Provisional IRA.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, the British ambassador to the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>, is assassinated by the Provisional IRA.", "links": [{"title": "<PERSON>-<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}]}, {"year": "1977", "text": "The start of the four-day-long Libyan-Egyptian War.", "html": "1977 - The start of the four-day-long <a href=\"https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War\" class=\"mw-redirect\" title=\"Libyan-Egyptian War\">Libyan-Egyptian War</a>.", "no_year_html": "The start of the four-day-long <a href=\"https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War\" class=\"mw-redirect\" title=\"Libyan-Egyptian War\">Libyan-Egyptian War</a>.", "links": [{"title": "Libyan-Egyptian War", "link": "https://wikipedia.org/wiki/Libyan%E2%80%93Egyptian_War"}]}, {"year": "1979", "text": "<PERSON>, a Mohawk actor, becomes the first Native American to have a star commemorated in the Hollywood Walk of Fame.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a> actor, becomes the first <a href=\"https://wikipedia.org/wiki/Native_American_People\" class=\"mw-redirect\" title=\"Native American People\">Native American</a> to have a star commemorated in the <a href=\"https://wikipedia.org/wiki/Hollywood_Walk_of_Fame\" title=\"Hollywood Walk of Fame\">Hollywood Walk of Fame</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, a <a href=\"https://wikipedia.org/wiki/Mohawk_people\" title=\"Mohawk people\">Mohawk</a> actor, becomes the first <a href=\"https://wikipedia.org/wiki/Native_American_People\" class=\"mw-redirect\" title=\"Native American People\">Native American</a> to have a star commemorated in the <a href=\"https://wikipedia.org/wiki/Hollywood_Walk_of_Fame\" title=\"Hollywood Walk of Fame\">Hollywood Walk of Fame</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mohawk people", "link": "https://wikipedia.org/wiki/Mohawk_people"}, {"title": "Native American People", "link": "https://wikipedia.org/wiki/Native_American_People"}, {"title": "Hollywood Walk of Fame", "link": "https://wikipedia.org/wiki/Hollywood_Walk_of_Fame"}]}, {"year": "1983", "text": "The world's lowest temperature in an inhabited location is recorded at Vostok Station, Antarctica at −89.2 °C (−128.6 °F).", "html": "1983 - The <a href=\"https://wikipedia.org/wiki/Lowest_temperature_recorded_on_Earth\" title=\"Lowest temperature recorded on Earth\">world's lowest temperature</a> in an inhabited location is recorded at <a href=\"https://wikipedia.org/wiki/Vostok_Station\" title=\"Vostok Station\">Vostok Station</a>, Antarctica at −89.2 °C (−128.6 °F).", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Lowest_temperature_recorded_on_Earth\" title=\"Lowest temperature recorded on Earth\">world's lowest temperature</a> in an inhabited location is recorded at <a href=\"https://wikipedia.org/wiki/Vostok_Station\" title=\"Vostok Station\">Vostok Station</a>, Antarctica at −89.2 °C (−128.6 °F).", "links": [{"title": "Lowest temperature recorded on Earth", "link": "https://wikipedia.org/wiki/Lowest_temperature_recorded_on_Earth"}, {"title": "Vostok Station", "link": "https://wikipedia.org/wiki/Vostok_Station"}]}, {"year": "1990", "text": "Taiwan's military police forces mainland Chinese illegal immigrants into sealed holds of a fishing boat Min Ping Yu No. 5540 for repatriation to Fujian, causing 25 people to die from suffocation.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Republic_of_China_Military_Police\" title=\"Republic of China Military Police\">Taiwan's military police</a> forces mainland Chinese illegal immigrants into sealed holds of a fishing boat <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_No._5540_incident\" title=\"Min Ping Yu No. 5540 incident\"><PERSON> Yu No. 5540</a></i> for repatriation to <a href=\"https://wikipedia.org/wiki/Fujian\" title=\"Fujian\">Fujian</a>, causing 25 people to die from suffocation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Republic_of_China_Military_Police\" title=\"Republic of China Military Police\">Taiwan's military police</a> forces mainland Chinese illegal immigrants into sealed holds of a fishing boat <i><a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_No._5540_incident\" title=\"Min Ping Yu No. 5540 incident\"><PERSON> Yu No. 5540</a></i> for repatriation to <a href=\"https://wikipedia.org/wiki/Fujian\" title=\"Fujian\">Fujian</a>, causing 25 people to die from suffocation.", "links": [{"title": "Republic of China Military Police", "link": "https://wikipedia.org/wiki/Republic_of_China_Military_Police"}, {"title": "<PERSON> Yu No. 5540 incident", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_No._5540_incident"}, {"title": "Fujian", "link": "https://wikipedia.org/wiki/Fujian"}]}, {"year": "1995", "text": "Third Taiwan Strait Crisis: The People's Liberation Army begins firing missiles into the waters north of Taiwan.", "html": "1995 - <a href=\"https://wikipedia.org/wiki/Third_Taiwan_Strait_Crisis\" title=\"Third Taiwan Strait Crisis\">Third Taiwan Strait Crisis</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> begins firing missiles into the waters north of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Third_Taiwan_Strait_Crisis\" title=\"Third Taiwan Strait Crisis\">Third Taiwan Strait Crisis</a>: The <a href=\"https://wikipedia.org/wiki/People%27s_Liberation_Army\" title=\"People's Liberation Army\">People's Liberation Army</a> begins firing missiles into the waters north of <a href=\"https://wikipedia.org/wiki/Taiwan\" title=\"Taiwan\">Taiwan</a>.", "links": [{"title": "Third Taiwan Strait Crisis", "link": "https://wikipedia.org/wiki/Third_Taiwan_Strait_Crisis"}, {"title": "People's Liberation Army", "link": "https://wikipedia.org/wiki/People%27s_Liberation_Army"}, {"title": "Taiwan", "link": "https://wikipedia.org/wiki/Taiwan"}]}, {"year": "2001", "text": "At the conclusion of a fireworks display on Okura Beach in Akashi, Hyōgo, Japan, 11 people are killed and more than 120 are injured when a pedestrian footbridge connecting the beach to JR Asagiri Station becomes overcrowded and people leaving the event fall down in a domino effect.", "html": "2001 - At the conclusion of a fireworks display on Okura Beach in <a href=\"https://wikipedia.org/wiki/Akashi,_Hy%C5%8Dgo\" title=\"Akashi, Hyōgo\">A<PERSON><PERSON>, Hyōgo</a>, Japan, <a href=\"https://wikipedia.org/wiki/Akashi_pedestrian_bridge_accident\" title=\"Akashi pedestrian bridge accident\">11 people are killed and more than 120 are injured</a> when a pedestrian footbridge connecting the beach to JR <a href=\"https://wikipedia.org/wiki/Asagiri_Station_(Hy%C5%8Dgo)\" title=\"Asagiri Station (Hyōgo)\">Asagiri Station</a> becomes overcrowded and people leaving the event fall down in a domino effect.", "no_year_html": "At the conclusion of a fireworks display on Okura Beach in <a href=\"https://wikipedia.org/wiki/Akas<PERSON>,_Hy%C5%8Dgo\" title=\"Akashi, Hyōgo\">Akashi, Hyōgo</a>, Japan, <a href=\"https://wikipedia.org/wiki/Akas<PERSON>_pedestrian_bridge_accident\" title=\"Akashi pedestrian bridge accident\">11 people are killed and more than 120 are injured</a> when a pedestrian footbridge connecting the beach to JR <a href=\"https://wikipedia.org/wiki/Asagiri_Station_(Hy%C5%8Dgo)\" title=\"Asagiri Station (Hyōgo)\">Asagiri Station</a> becomes overcrowded and people leaving the event fall down in a domino effect.", "links": [{"title": "Akashi, Hyōgo", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Hy%C5%8Dgo"}, {"title": "Akashi pedestrian bridge accident", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_pedestrian_bridge_accident"}, {"title": "Asagiri Station (Hyōgo)", "link": "https://wikipedia.org/wiki/Asagiri_Station_(Hy%C5%8Dgo)"}]}, {"year": "2005", "text": "Four attempted bomb attacks by Islamist extremists disrupt part of London's public transport system.", "html": "2005 - Four <a href=\"https://wikipedia.org/wiki/21_July_2005_London_Bombings\" class=\"mw-redirect\" title=\"21 July 2005 London Bombings\">attempted bomb attacks</a> by Islamist extremists disrupt part of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>'s public transport system.", "no_year_html": "Four <a href=\"https://wikipedia.org/wiki/21_July_2005_London_Bombings\" class=\"mw-redirect\" title=\"21 July 2005 London Bombings\">attempted bomb attacks</a> by Islamist extremists disrupt part of <a href=\"https://wikipedia.org/wiki/London\" title=\"London\">London</a>'s public transport system.", "links": [{"title": "21 July 2005 London Bombings", "link": "https://wikipedia.org/wiki/21_July_2005_London_Bombings"}, {"title": "London", "link": "https://wikipedia.org/wiki/London"}]}, {"year": "2008", "text": "<PERSON> is declared the first President of Nepal.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>dav\" title=\"<PERSON>dav\"><PERSON>v</a> is declared the first <a href=\"https://wikipedia.org/wiki/President_of_Nepal\" title=\"President of Nepal\">President of Nepal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>dav\" title=\"<PERSON>dav\"><PERSON>v</a> is declared the first <a href=\"https://wikipedia.org/wiki/President_of_Nepal\" title=\"President of Nepal\">President of Nepal</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>dav"}, {"title": "President of Nepal", "link": "https://wikipedia.org/wiki/President_of_Nepal"}]}, {"year": "2010", "text": "President <PERSON> signs the Dodd-Frank Wall Street Reform and Consumer Protection Act.", "html": "2010 - President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Dodd%E2%80%93Frank_Wall_Street_Reform_and_Consumer_Protection_Act\" title=\"Dodd-Frank Wall Street Reform and Consumer Protection Act\">Dodd-Frank Wall Street Reform and Consumer Protection Act</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/Barack_Obama\" title=\"Barack Obama\"><PERSON></a> signs the <a href=\"https://wikipedia.org/wiki/Dodd%E2%80%93Frank_Wall_Street_Reform_and_Consumer_Protection_Act\" title=\"Dodd-Frank Wall Street Reform and Consumer Protection Act\">Dodd-Frank Wall Street Reform and Consumer Protection Act</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Barack<PERSON>Obama"}, {"title": "Dodd-Frank Wall Street Reform and Consumer Protection Act", "link": "https://wikipedia.org/wiki/Dodd%E2%80%93Frank_Wall_Street_Reform_and_Consumer_Protection_Act"}]}, {"year": "2011", "text": "NASA's Space Shuttle program ends with the landing of Space Shuttle Atlantis on mission STS-135 at NASA's Kennedy Space Center.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a> ends with the landing of <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on mission <a href=\"https://wikipedia.org/wiki/STS-135\" title=\"STS-135\">STS-135</a> at <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Space_Shuttle_program\" title=\"Space Shuttle program\">Space Shuttle program</a> ends with the landing of <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> on mission <a href=\"https://wikipedia.org/wiki/STS-135\" title=\"STS-135\">STS-135</a> at <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/Kennedy_Space_Center\" title=\"Kennedy Space Center\">Kennedy Space Center</a>.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Space Shuttle program", "link": "https://wikipedia.org/wiki/Space_Shuttle_program"}, {"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-135", "link": "https://wikipedia.org/wiki/STS-135"}, {"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "Kennedy Space Center", "link": "https://wikipedia.org/wiki/Kennedy_Space_Center"}]}, {"year": "2012", "text": "<PERSON><PERSON> completes the first solo human-powered circumnavigation of the world.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>rden_Eru%C3%A7\" title=\"<PERSON><PERSON> Eruç\"><PERSON><PERSON></a> completes the first solo <a href=\"https://wikipedia.org/wiki/Human-powered_transport\" title=\"Human-powered transport\">human-powered</a> circumnavigation of the world.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>rden_Eru%C3%A7\" title=\"<PERSON><PERSON> Eruç\"><PERSON><PERSON></a> completes the first solo <a href=\"https://wikipedia.org/wiki/Human-powered_transport\" title=\"Human-powered transport\">human-powered</a> circumnavigation of the world.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erden_Eru%C3%A7"}, {"title": "Human-powered transport", "link": "https://wikipedia.org/wiki/Human-powered_transport"}]}, {"year": "2019", "text": "<PERSON><PERSON> attack or \"721 incident\" in Hong Kong. Triad members indiscriminately beat civilians returning from protests while police failed to take action.", "html": "2019 - <a href=\"https://wikipedia.org/wiki/2019_<PERSON><PERSON>_Long_attack\" title=\"2019 Yuen Long attack\"><PERSON><PERSON> Long attack</a> or \"721 incident\" in Hong Kong. <a href=\"https://wikipedia.org/wiki/Triad_(organized_crime)\" title=\"Triad (organized crime)\">Triad</a> members indiscriminately beat civilians returning from <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">protests</a> while police failed to take action.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2019_<PERSON><PERSON>_Long_attack\" title=\"2019 Yuen Long attack\"><PERSON><PERSON> Long attack</a> or \"721 incident\" in Hong Kong. <a href=\"https://wikipedia.org/wiki/Triad_(organized_crime)\" title=\"Triad (organized crime)\">Triad</a> members indiscriminately beat civilians returning from <a href=\"https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests\" class=\"mw-redirect\" title=\"2019-20 Hong Kong protests\">protests</a> while police failed to take action.", "links": [{"title": "2019 Yuen <PERSON> attack", "link": "https://wikipedia.org/wiki/2019_<PERSON><PERSON>_<PERSON>_attack"}, {"title": "Triad (organized crime)", "link": "https://wikipedia.org/wiki/Triad_(organized_crime)"}, {"title": "2019-20 Hong Kong protests", "link": "https://wikipedia.org/wiki/2019%E2%80%9320_Hong_Kong_protests"}]}, {"year": "2023", "text": "The Barbenheimer phenomenon begins as two major motion pictures, <PERSON><PERSON>'s fantasy comedy Barbie and <PERSON>'s epic biographical thriller <PERSON><PERSON><PERSON>, are released in theaters on the same day and audiences, instead of creating a rivalry between the extremely dissimilar films, instead attend and praise both as an informal, surreal double feature.", "html": "2023 - The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> phenomenon begins as two major motion pictures, <a href=\"https://wikipedia.org/wiki/G<PERSON>_Gerwig\" title=\"G<PERSON> Gerwig\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Fantasy_comedy\" title=\"Fantasy comedy\">fantasy comedy</a> <i><a href=\"https://wikipedia.org/wiki/Barbie_(film)\" title=\"Barbie (film)\">Barbie</a></i> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Epic_film\" title=\"Epic film\">epic</a> <a href=\"https://wikipedia.org/wiki/Biographical_film\" title=\"Biographical film\">biographical</a> <a href=\"https://wikipedia.org/wiki/Thriller_film\" title=\"Thriller film\">thriller</a> <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)\" title=\"Oppenheimer (film)\"><PERSON>penheimer</a></i>, are released in theaters on the same day and audiences, instead of creating a rivalry between the extremely dissimilar films, instead attend and praise both as an informal, surreal <a href=\"https://wikipedia.org/wiki/Double_feature\" title=\"Double feature\">double feature</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> phenomenon begins as two major motion pictures, <a href=\"https://wikipedia.org/wiki/G<PERSON>_Gerwig\" title=\"G<PERSON> Gerwig\"><PERSON><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Fantasy_comedy\" title=\"Fantasy comedy\">fantasy comedy</a> <i><a href=\"https://wikipedia.org/wiki/Barbie_(film)\" title=\"Barbie (film)\">Barbie</a></i> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Epic_film\" title=\"Epic film\">epic</a> <a href=\"https://wikipedia.org/wiki/Biographical_film\" title=\"Biographical film\">biographical</a> <a href=\"https://wikipedia.org/wiki/Thriller_film\" title=\"Thriller film\">thriller</a> <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)\" title=\"<PERSON><PERSON><PERSON> (film)\"><PERSON>penheimer</a></i>, are released in theaters on the same day and audiences, instead of creating a rivalry between the extremely dissimilar films, instead attend and praise both as an informal, surreal <a href=\"https://wikipedia.org/wiki/Double_feature\" title=\"Double feature\">double feature</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wig"}, {"title": "Fantasy comedy", "link": "https://wikipedia.org/wiki/Fantasy_comedy"}, {"title": "Barbie (film)", "link": "https://wikipedia.org/wiki/<PERSON>_(film)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Epic film", "link": "https://wikipedia.org/wiki/Epic_film"}, {"title": "Biographical film", "link": "https://wikipedia.org/wiki/Biographical_film"}, {"title": "Thriller film", "link": "https://wikipedia.org/wiki/Thriller_film"}, {"title": "<PERSON><PERSON><PERSON> (film)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(film)"}, {"title": "Double feature", "link": "https://wikipedia.org/wiki/Double_feature"}]}, {"year": "2024", "text": "US President <PERSON> announces he will no longer seek a second term and withdraws from the 2024 election.", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">US President <PERSON></a> announces he will no longer seek a second term and <a href=\"https://wikipedia.org/wiki/Withdrawal_of_<PERSON>_<PERSON>_from_the_2024_United_States_presidential_election\" title=\"With<PERSON><PERSON> of <PERSON> from the 2024 United States presidential election\">withdraws</a> from the <a href=\"https://wikipedia.org/wiki/2024_United_States_presidential_election\" title=\"2024 United States presidential election\">2024 election</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">US President <PERSON></a> announces he will no longer seek a second term and <a href=\"https://wikipedia.org/wiki/Withdrawal_of_<PERSON>_<PERSON>_from_the_2024_United_States_presidential_election\" title=\"With<PERSON><PERSON> of <PERSON> from the 2024 United States presidential election\">withdraws</a> from the <a href=\"https://wikipedia.org/wiki/2024_United_States_presidential_election\" title=\"2024 United States presidential election\">2024 election</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> of <PERSON> from the 2024 United States presidential election", "link": "https://wikipedia.org/wiki/Withdrawal_of_<PERSON>_<PERSON>_from_the_2024_United_States_presidential_election"}, {"title": "2024 United States presidential election", "link": "https://wikipedia.org/wiki/2024_United_States_presidential_election"}]}], "Births": [{"year": "541", "text": "Emperor <PERSON> of Sui, emperor of the Sui dynasty (d. 604)", "html": "541 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Sui\" title=\"Emperor <PERSON> of Sui\">Emperor <PERSON> of Sui</a>, emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> (d. 604)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Su<PERSON>\" title=\"Emperor <PERSON> of Sui\">Emperor <PERSON> of Sui</a>, emperor of the <a href=\"https://wikipedia.org/wiki/Sui_dynasty\" title=\"Sui dynasty\">Sui dynasty</a> (d. 604)", "links": [{"title": "Emperor <PERSON> of Sui", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Sui"}, {"title": "Sui dynasty", "link": "https://wikipedia.org/wiki/Sui_dynasty"}]}, {"year": "1030", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, King of Burma (d. 1112)", "html": "1030 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of Burma (d. 1112)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, King of Burma (d. 1112)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K<PERSON><PERSON>ttha"}]}, {"year": "1414", "text": "<PERSON> <PERSON><PERSON> (d. 1484)", "html": "1414 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"<PERSON> Sixtus IV\"><PERSON> <PERSON><PERSON> IV</a> (d. 1484)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Sixtus_IV\" title=\"Pope Sixtus IV\"><PERSON> <PERSON>tus IV</a> (d. 1484)", "links": [{"title": "<PERSON> <PERSON><PERSON> IV", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_IV"}]}, {"year": "1462", "text": "Queen <PERSON><PERSON><PERSON><PERSON>, Korean royal consort (d. 1530)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1530)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Queen_<PERSON><PERSON><PERSON>\" title=\"Queen <PERSON><PERSON><PERSON>\">Queen <PERSON><PERSON><PERSON><PERSON></a>, Korean royal consort (d. 1530)", "links": [{"title": "Queen <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1476", "text": "<PERSON>, Duke of Ferrara (d. 1534)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a> (d. 1534)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27<PERSON><PERSON>,_Duke_of_Ferrara\" class=\"mw-redirect\" title=\"<PERSON>, Duke of Ferrara\"><PERSON>, Duke of Ferrara</a> (d. 1534)", "links": [{"title": "<PERSON>, Duke of Ferrara", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_d%27Este,_<PERSON>_<PERSON>_Ferrara"}]}, {"year": "1476", "text": "<PERSON>, Italian noble (d. 1497)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (d. 1497)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian noble (d. 1497)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1535", "text": "<PERSON>, 5th Marquis of Cañete, Royal Governor of Chile (d. 1609)", "html": "1535 - <a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Hurt<PERSON>_de_Mendoza,_5th_Marquis_of_Ca%C3%B1ete\" title=\"<PERSON>, 5th Marquis of Cañete\"><PERSON>, 5th Marquis of Cañete</a>, Royal Governor of Chile (d. 1609)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Garc%C3%ADa_Hurt<PERSON>_de_Mendoza,_5th_Marquis_of_Ca%C3%B1ete\" title=\"<PERSON>, 5th Marquis of Cañete\"><PERSON>, 5th Marquis of Cañete</a>, Royal Governor of Chile (d. 1609)", "links": [{"title": "<PERSON>, 5th Marquis of Cañete", "link": "https://wikipedia.org/wiki/Garc%C3%<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>,_5th_Marquis_of_Ca%C3%B1ete"}]}, {"year": "1616", "text": "<PERSON>, Archduchess of Austria (d. 1676)", "html": "1616 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Archduchess_of_Austria\" title=\"<PERSON>, Archduchess of Austria\"><PERSON>, Archduchess of Austria</a> (d. 1676)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Archduchess_of_Austria\" title=\"<PERSON>, Archduchess of Austria\"><PERSON>, Archduchess of Austria</a> (d. 1676)", "links": [{"title": "<PERSON>, Archduchess of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27_<PERSON>,_Archduchess_of_Austria"}]}, {"year": "1620", "text": "<PERSON>, French astronomer (d. 1682)", "html": "1620 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (d. 1682)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French astronomer (d. 1682)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, 1st Viscount <PERSON>, Scottish general (d. 1689)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish general (d. 1689)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Viscount_<PERSON>\" title=\"<PERSON>, 1st Viscount <PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, Scottish general (d. 1689)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, Filipino catechist and sacristan; later canonized (d. 1672)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino catechist and sacristan; later canonized (d. 1672)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino catechist and sacristan; later canonized (d. 1672)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1664", "text": "<PERSON>, English poet and diplomat, British Ambassador to France (d. 1721)", "html": "1664 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France\" title=\"List of ambassadors of the United Kingdom to France\">British Ambassador to France</a> (d. 1721)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of ambassadors of the United Kingdom to France", "link": "https://wikipedia.org/wiki/List_of_ambassadors_of_the_United_Kingdom_to_France"}]}, {"year": "1693", "text": "<PERSON>, 1st Duke of Newcastle, English politician, Prime Minister of Great Britain (d. 1768)", "html": "1693 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1768)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Newcastle\" title=\"<PERSON>, 1st Duke of Newcastle\"><PERSON>, 1st Duke of Newcastle</a>, English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1768)", "links": [{"title": "<PERSON>, 1st Duke of Newcastle", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Newcastle"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1710", "text": "<PERSON>, German physician, botanist, and zoologist (d. 1792)", "html": "1710 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hring\" title=\"<PERSON>\"><PERSON></a>, German physician, botanist, and zoologist (d. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hring\" title=\"<PERSON>\"><PERSON></a>, German physician, botanist, and zoologist (d. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_M%C3%B6hring"}]}, {"year": "1783", "text": "<PERSON>, marquis de <PERSON>, French general (d. 1853)", "html": "1783 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marquis_de_Montholon\" title=\"<PERSON>, marquis de Montholon\"><PERSON>, marquis de Montholon</a>, French general (d. 1853)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marquis_de_Montholon\" title=\"<PERSON>, marquis de Montholon\"><PERSON>, marquis de Montholon</a>, French general (d. 1853)", "links": [{"title": "<PERSON>, marquis de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_marquis_<PERSON>_<PERSON>"}]}, {"year": "1789", "text": "<PERSON><PERSON><PERSON>, Bulgarian educator, merchant and writer (d. 1847)", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian educator, merchant and writer (d. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bulgarian educator, merchant and writer (d. 1847)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>ov"}]}, {"year": "1808", "text": "<PERSON><PERSON><PERSON>, Romanian historian, academic, and politician (d. 1864)", "html": "1808 - <a href=\"https://wikipedia.org/wiki/Simion_B%C4%83rnu%C8%9Biu\" title=\"<PERSON><PERSON>n Bărnuțiu\"><PERSON><PERSON><PERSON></a>, Romanian historian, academic, and politician (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Simion_B%C4%83rnu%C8%9Biu\" title=\"Simion Bărnuțiu\"><PERSON><PERSON><PERSON></a>, Romanian historian, academic, and politician (d. 1864)", "links": [{"title": "Simion Bărnuțiu", "link": "https://wikipedia.org/wiki/Simion_B%C4%83rnu%C8%9Biu"}]}, {"year": "1810", "text": "<PERSON>, French chemist and physicist (d. 1878)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (d. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French chemist and physicist (d. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1811", "text": "<PERSON>, Scottish-Australian politician, 3rd Premier of Queensland (d. 1873)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>,_10th_Baronet\" title=\"Sir <PERSON>, 10th Baronet\"><PERSON></a>, Scottish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>,_10th_Baronet\" title=\"Sir <PERSON>, 10th Baronet\"><PERSON></a>, Scottish-Australian politician, 3rd <a href=\"https://wikipedia.org/wiki/Premier_of_Queensland\" title=\"Premier of Queensland\">Premier of Queensland</a> (d. 1873)", "links": [{"title": "Sir <PERSON>, 10th Baronet", "link": "https://wikipedia.org/wiki/Sir_<PERSON>,_10th_Baronet"}, {"title": "Premier of Queensland", "link": "https://wikipedia.org/wiki/Premier_of_Queensland"}]}, {"year": "1816", "text": "<PERSON>, German-English journalist, founded Reuters (d. 1899)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English journalist, founded <a href=\"https://wikipedia.org/wiki/Reuters\" title=\"Reuters\">Reuters</a> (d. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English journalist, founded <a href=\"https://wikipedia.org/wiki/Reuters\" title=\"Reuters\">Reuters</a> (d. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Reuters", "link": "https://wikipedia.org/wiki/Reuters"}]}, {"year": "1858", "text": "<PERSON> of Austria (d. 1929)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Austria\" title=\"<PERSON> of Austria\"><PERSON> of Austria</a> (d. 1929)", "links": [{"title": "<PERSON> of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Austria"}]}, {"year": "1858", "text": "<PERSON><PERSON>, German painter (d. 1925)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German painter (d. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>th"}]}, {"year": "1858", "text": "<PERSON>, New Zealand painter and educator (d. 1941)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand painter and educator (d. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Kee<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand painter and educator (d. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Keeffe"}]}, {"year": "1863", "text": "<PERSON><PERSON>, English-American cricketer and actor (d. 1948)", "html": "1863 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English-American cricketer and actor (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English-American cricketer and actor (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Swiss Symbolist painter and printmaker (d. 1926)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss Symbolist painter and printmaker (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss Symbolist painter and printmaker (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1870", "text": "<PERSON>, Czech painter, etcher, and lithographer (d. 1932)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech painter, etcher, and lithographer (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk\" title=\"<PERSON>\"><PERSON></a>, Czech painter, etcher, and lithographer (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADk"}]}, {"year": "1875", "text": "<PERSON>, French rugby player and tug of war competitor (d. 1947)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French rugby player and <a href=\"https://wikipedia.org/wiki/Tug_of_war\" title=\"Tug of war\">tug of war</a> competitor (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tug of war", "link": "https://wikipedia.org/wiki/Tug_of_war"}]}, {"year": "1880", "text": "<PERSON> <PERSON><PERSON><PERSON>, Slovak astronomer, general, and politician (d. 1919)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/Milan_Ra<PERSON><PERSON>_%C5%A0tef%C3%A1nik\" title=\"Milan Rast<PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak astronomer, general, and politician (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Milan_Ra<PERSON>islav_%C5%A0tef%C3%A1nik\" title=\"Milan <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovak astronomer, general, and politician (d. 1919)", "links": [{"title": "Milan Ra<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Milan_<PERSON><PERSON><PERSON>_%C5%A0tef%C3%A1nik"}]}, {"year": "1882", "text": "<PERSON>, Ukrainian author and illustrator (d. 1967)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian author and illustrator (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian author and illustrator (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, Belgian actor, director, and screenwriter (d. 1948)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actor, director, and screenwriter (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian actor, director, and screenwriter (d. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, Finnish javelin thrower and soldier (d. 1969)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish javelin thrower and soldier (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish javelin thrower and soldier (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, German author (d. 1947)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Hans <PERSON>\"><PERSON></a>, German author (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ada"}]}, {"year": "1896", "text": "<PERSON>, American anthropologist, physician and nutritionist (d. 1996)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American anthropologist, physician and nutritionist (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American anthropologist, physician and nutritionist (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, American singer-songwriter (d. 1979)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American poet (d. 1932)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet (d. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, American novelist, short story writer, and journalist, Nobel Prize laureate (d. 1961)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, short story writer, and journalist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1900", "text": "<PERSON><PERSON><PERSON>, American theatre manager and modern dance publicity agent (d. 1980)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theatre manager and modern dance publicity agent (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American theatre manager and modern dance publicity agent (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American photographer and journalist (d. 1986)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (d. 1986)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)"}]}, {"year": "1903", "text": "<PERSON>, American businessman and financier, co-founded Neuberger Berman (d. 2010)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Berman\"><PERSON><PERSON><PERSON></a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and financier, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Berman\"><PERSON><PERSON><PERSON></a> (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> Berman", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American golfer and architect (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/Jug_<PERSON>c<PERSON>\" title=\"<PERSON>g <PERSON>c<PERSON>\"><PERSON><PERSON></a>, American golfer and architect (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jug_<PERSON>\" title=\"<PERSON>g <PERSON>\"><PERSON><PERSON></a>, American golfer and architect (d. 1996)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jug_<PERSON>c<PERSON>en"}]}, {"year": "1911", "text": "<PERSON>, Canadian author and theorist (d. 1980)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and theorist (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian author and theorist (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian author, poet, and scholar (d. 1988)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and scholar (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian author, poet, and scholar (d. 1988)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1914", "text": "<PERSON><PERSON><PERSON><PERSON>, Estonian shot putter and discus thrower (d. 1977)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/Aleksander_Kreek\" title=\"Aleksander Kreek\">Aleksander Kreek</a>, Estonian shot putter and discus thrower (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksander_Kreek\" title=\"Aleksander Kreek\"><PERSON>ek<PERSON><PERSON> Kreek</a>, Estonian shot putter and discus thrower (d. 1977)", "links": [{"title": "Aleksander Kreek", "link": "https://wikipedia.org/wiki/Aleksander_Kreek"}]}, {"year": "1917", "text": "<PERSON>, Canadian lawyer and jurist (d. 2005)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and jurist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Dutch painter, sculptor, and illustrator (d. 2005)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Con<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter, sculptor, and illustrator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch painter, sculptor, and illustrator (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Con<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Russian-American violinist and conductor (d. 2001)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American violinist and conductor (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American violinist and conductor (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Algerian-French journalist and author (d. 2020)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French journalist and author (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Algerian-French journalist and author (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, American sociologist and author (d. 2000)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist and author (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, English actor (d. 2014)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (d. 2014)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> (d. 2020)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Vusamazulu_Credo_Mutwa\" title=\"Vusamazulu Credo Mutwa\">Vusamazulu Credo Mutwa</a>, <PERSON><PERSON> sangoma (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vusamazulu_Credo_Mutwa\" title=\"Vusamazulu Credo Mutwa\">Vusamazulu Credo Mutwa</a>, <PERSON><PERSON> sangoma (d. 2020)", "links": [{"title": "Vusama<PERSON><PERSON> Credo <PERSON>", "link": "https://wikipedia.org/wiki/Vusamazulu_Credo_Mutwa"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, German Romani author (d. 2022)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Romani author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German Romani author (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, American singer (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, English actress (d. 2009)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress (d. 2009)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>den"}]}, {"year": "1923", "text": "<PERSON>, Canadian-American chemist and academic, Nobel Prize laureate", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American chemist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1923", "text": "<PERSON><PERSON>, English actress and singer (d. 1980)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and singer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English actress and singer (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actor, comedian, and screenwriter (d. 2006)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, comedian, and screenwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Canadian hockey player (d. 2021)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian hockey player (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American actor (d. 2009)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (d. 2009)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1926", "text": "<PERSON>, Canadian actor, director, and producer (d. 2024)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Jewison\"><PERSON></a>, Canadian actor, director, and producer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Norman Jewison\"><PERSON></a>, Canadian actor, director, and producer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON><PERSON>, Pakistani general and politician, 7th Governor of Balochistan (d. 2022)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Balochistan\" class=\"mw-redirect\" title=\"Governor of Balochistan\">Governor of Balochistan</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Pakistani general and politician, 7th <a href=\"https://wikipedia.org/wiki/Governor_of_Balochistan\" class=\"mw-redirect\" title=\"Governor of Balochistan\">Governor of Balochistan</a> (d. 2022)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Balochistan", "link": "https://wikipedia.org/wiki/Governor_of_Balochistan"}]}, {"year": "1926", "text": "<PERSON>, English actor (d. 2013)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Czech-English director and producer (d. 2002)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-English director and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech-English director and producer (d. 2002)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, Canadian wrestler (d. 1998)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Sky_Low_Low\" title=\"Sky Low Low\"><PERSON> Low Low</a>, Canadian wrestler (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sky_Low_Low\" title=\"Sky Low Low\"><PERSON></a>, Canadian wrestler (d. 1998)", "links": [{"title": "Sky Low Low", "link": "https://wikipedia.org/wiki/Sky_Low_Low"}]}, {"year": "1929", "text": "<PERSON>, American wrestler (d. 2006)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Indian poet and songwriter (d. 2002)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anand Bakshi\"><PERSON></a>, Indian poet and songwriter (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Anand Bakshi\"><PERSON></a>, Indian poet and songwriter (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>hi"}]}, {"year": "1930", "text": "<PERSON>, American singer", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American pianist and composer (d. 1963)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American saxophonist", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Chilean-Israeli painter and composer (d. 2022)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Israeli painter and composer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean-Israeli painter and composer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American singer and actress (d. 2011)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American novelist, essayist, and critic (d. 1982)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_writer)\" title=\"<PERSON> (American writer)\"><PERSON></a>, American novelist, essayist, and critic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_writer)\" title=\"<PERSON> (American writer)\"><PERSON></a>, American novelist, essayist, and critic (d. 1982)", "links": [{"title": "<PERSON> (American writer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(American_writer)"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Indian cricketer and manager", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Borde\"><PERSON><PERSON></a>, Indian cricketer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Borde\"><PERSON><PERSON></a>, Indian cricketer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor, director, and author (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, director, and author (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, German businessman and politician (d. 2020)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Bl%C3%BCm\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l%C3%BCm\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German businessman and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Norbert_Bl%C3%BCm"}]}, {"year": "1935", "text": "<PERSON>, Polish-American baseball player and coach (d. 2006)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American baseball player and coach (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American baseball player and coach (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Soviet footballer (d. 1990)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet footballer (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet footballer (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American captain and politician, 18th United States Secretary of Defense (d. 1995)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain and politician, 18th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a> (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1938", "text": "<PERSON>, Austrian-Canadian pianist, composer, and conductor", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Canadian pianist, composer, and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Canadian pianist, composer, and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and politician, 79th United States Attorney General (d. 2016)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 79th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 79th <a href=\"https://wikipedia.org/wiki/United_States_Attorney_General\" title=\"United States Attorney General\">United States Attorney General</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Attorney General", "link": "https://wikipedia.org/wiki/United_States_Attorney_General"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American saxophonist and educator", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American saxophonist and educator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter, producer, and manager (d. 2015)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and manager (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, and manager (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, English-American diplomat, 23rd United States Ambassador to the United Nations", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American diplomat, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American diplomat, 23rd <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations\" class=\"mw-redirect\" title=\"United States Ambassador to the United Nations\">United States Ambassador to the United Nations</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Ambassador to the United Nations", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_the_United_Nations"}]}, {"year": "1942", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian politician, 98th President of the Indian National Congress", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 98th <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian politician, 98th <a href=\"https://wikipedia.org/wiki/President_of_the_Indian_National_Congress\" class=\"mw-redirect\" title=\"President of the Indian National Congress\">President of the Indian National Congress</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ge"}, {"title": "President of the Indian National Congress", "link": "https://wikipedia.org/wiki/President_of_the_Indian_National_Congress"}]}, {"year": "1943", "text": "<PERSON>, Austrian race car driver (d. 2002)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian race car driver (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fritz_<PERSON>\" title=\"Fritz <PERSON>\"><PERSON></a>, Austrian race car driver (d. 2002)", "links": [{"title": "Fritz Glatz", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor (d. 2014)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Northern Irish guitarist, singer and songwriter (d. 2016)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish guitarist, singer and songwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish guitarist, singer and songwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American author and political advisor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and political advisor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and political advisor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>um"}]}, {"year": "1944", "text": "<PERSON>, Ghanaian lawyer and politician, 3rd President of Ghana (d. 2012)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian lawyer and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Ghana\" title=\"President of Ghana\">President of Ghana</a> (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Ghana", "link": "https://wikipedia.org/wiki/President_of_Ghana"}]}, {"year": "1944", "text": "<PERSON><PERSON>, Nigerian author and academic (d. 2017)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/Buchi_Emecheta\" title=\"Buchi Emecheta\"><PERSON><PERSON> Emech<PERSON></a>, Nigerian author and academic (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Buchi_Emecheta\" title=\"Buchi Emecheta\">B<PERSON> Emecheta</a>, Nigerian author and academic (d. 2017)", "links": [{"title": "Buchi Emecheta", "link": "https://wikipedia.org/wiki/Buchi_Emecheta"}]}, {"year": "1944", "text": "<PERSON>, American academic and politician (d. 2002)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American academic and politician (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English poet, critic, and educator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet, critic, and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Australian cricketer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, South African cricketer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American lawyer and judge, 39th Solicitor General of the United States (d. 2022)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 39th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_the_United_States\" title=\"Solicitor General of the United States\">Solicitor General of the United States</a> (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and judge, 39th <a href=\"https://wikipedia.org/wiki/Solicitor_General_of_the_United_States\" title=\"Solicitor General of the United States\">Solicitor General of the United States</a> (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Solicitor General of the United States", "link": "https://wikipedia.org/wiki/Solicitor_General_of_the_United_States"}]}, {"year": "1946", "text": "<PERSON>, American author, screenwriter and producer", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, screenwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(writer)\" title=\"<PERSON> (writer)\"><PERSON></a>, American author, screenwriter and producer", "links": [{"title": "<PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON>_(writer)"}]}, {"year": "1947", "text": "<PERSON><PERSON><PERSON>, Indian cricketer and politician (d. 2020)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and politician (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer and politician (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian actor and director", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Art_Hindle\" title=\"<PERSON> Hindle\"><PERSON></a>, Canadian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Hindle\" title=\"<PERSON> Hi<PERSON>le\"><PERSON></a>, Canadian actor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hindle"}]}, {"year": "1948", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American cartoonist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American playwright and actress", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON>, New Zealand singer-songwriter and poet (d. 2003)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rini Melbourne\"><PERSON><PERSON></a>, New Zealand singer-songwriter and poet (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>rini Melbourne\"><PERSON><PERSON></a>, New Zealand singer-songwriter and poet (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, Argentinian footballer and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Argentinian footballer and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>llol"}]}, {"year": "1950", "text": "<PERSON>, <PERSON>, English politician, Minister of State for Transport", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Transport\" title=\"Minister of State for Transport\">Minister of State for Transport</a>", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Minister of State for Transport", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Transport"}]}, {"year": "1951", "text": "<PERSON>, English politician and diplomat, 30th Lieutenant Governor of the Isle of Man, 139th Governor of Bermuda", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, 30th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_Isle_of_Man\" title=\"Lieutenant Governor of the Isle of Man\">Lieutenant Governor of the Isle of Man</a>, 139th <a href=\"https://wikipedia.org/wiki/Governor_of_Bermuda\" title=\"Governor of Bermuda\">Governor of Bermuda</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician and diplomat, 30th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_Isle_of_Man\" title=\"Lieutenant Governor of the Isle of Man\">Lieutenant Governor of the Isle of Man</a>, 139th <a href=\"https://wikipedia.org/wiki/Governor_of_Bermuda\" title=\"Governor of Bermuda\">Governor of Bermuda</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of the Isle of Man", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_the_Isle_of_Man"}, {"title": "Governor of Bermuda", "link": "https://wikipedia.org/wiki/Governor_of_Bermuda"}]}, {"year": "1951", "text": "<PERSON>, American actor and comedian (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and comedian (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American physician and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Malaysian economist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian economist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American singer-songwriter, multi-instrumentalist, arranger, and producer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, arranger, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, multi-instrumentalist, arranger, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Australian keyboard player and actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian keyboard player and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian keyboard player and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, New Zealand rugby player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1953", "text": "<PERSON>, English footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American bass player, songwriter, and producer (d. 2003)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American bass player, songwriter, and producer (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON>, American lawyer and politician, 88th Governor of Connecticut", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Mall<PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 88th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American lawyer and politician, 88th <a href=\"https://wikipedia.org/wiki/Governor_of_Connecticut\" class=\"mw-redirect\" title=\"Governor of Connecticut\">Governor of Connecticut</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Governor of Connecticut", "link": "https://wikipedia.org/wiki/Governor_of_Connecticut"}]}, {"year": "1955", "text": "<PERSON><PERSON>, Indonesian-born Dutch singer and entertainer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Indonesian-born Dutch singer and entertainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Indonesian-born Dutch singer and entertainer", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Hungarian director, producer, and screenwriter", "html": "1955 - <a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>rr\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian director, producer, and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9la_Tarr"}]}, {"year": "1956", "text": "<PERSON>, American author", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Swedish trade union leader and politician, 33rd Prime Minister of Sweden", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6fven\" title=\"<PERSON>\"><PERSON></a>, Swedish trade union leader and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6fven\" title=\"<PERSON>\"><PERSON></a>, Swedish trade union leader and politician, 33rd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Sweden\" title=\"Prime Minister of Sweden\">Prime Minister of Sweden</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stefan_L%C3%B6fven"}, {"title": "Prime Minister of Sweden", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Sweden"}]}, {"year": "1957", "text": "<PERSON>, American comedian, actor, and producer", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American comedian, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American baseball player and sportscaster (d. 2015)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby league player and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON>, Turkish journalist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Australian rugby league player, coach, and sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Indian singer-songwriter (d. 1988)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer-songwriter (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian singer-songwriter (d. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>lin_Mati%C4%87"}]}, {"year": "1960", "text": "<PERSON>, German footballer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_b._1960)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, b. 1960)\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_b._1960)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, b. 1960)\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON> (footballer, b. 1960)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_b._1960)"}]}, {"year": "1961", "text": "<PERSON>, Australian politician, 40th Premier of New South Wales", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 40th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1961", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)"}]}, {"year": "1962", "text": "<PERSON>, Baron <PERSON>, English businessman", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English businessman", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Brazilian basketball player, mixed martial artist, and wrestler", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Silva\" title=\"<PERSON> Silva\"><PERSON></a>, Brazilian basketball player, mixed martial artist, and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Silva\" title=\"Giant Silva\"><PERSON></a>, Brazilian basketball player, mixed martial artist, and wrestler", "links": [{"title": "<PERSON> Silva", "link": "https://wikipedia.org/wiki/<PERSON>_Silva"}]}, {"year": "1964", "text": "<PERSON>, Irish boxer and actor", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish boxer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, English actor and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, German ski jumper and journalist", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9Fflog\" title=\"<PERSON><PERSON>flog\"><PERSON><PERSON></a>, German ski jumper and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9Fflog\" title=\"<PERSON><PERSON>flog\"><PERSON><PERSON></a>, German ski jumper and journalist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%9Fflog"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON><PERSON>, Icelandic footballer and lawyer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Gu%C3%B0<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gu%C3%B0<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Icelandic footballer and lawyer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gu%C3%B0ni_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American baseball player, coach, and sportscaster", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Welsh author", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, American soccer player and sportscaster", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American soccer player and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chastain"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indian actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Canadian ice hockey player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American comedian and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(comedian)\" title=\"<PERSON> (comedian)\"><PERSON></a>, American comedian and actor", "links": [{"title": "<PERSON> (comedian)", "link": "https://wikipedia.org/wiki/<PERSON>_(comedian)"}]}, {"year": "1969", "text": "<PERSON>, German race car driver", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" class=\"mw-redirect\" title=\"<PERSON> (racing driver)\"><PERSON></a>, German race car driver", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1969", "text": "<PERSON>, American singer-songwriter, guitarist, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Hart\"><PERSON></a>, American singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, German equestrian", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German equestrian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German equestrian", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_(musician)"}]}, {"year": "1971", "text": "<PERSON>, French long jumper", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French long jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French long jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1971", "text": "<PERSON>, English-French actress and singer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charlotte_<PERSON>bourg"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Israeli footballer and manager (d. 2014)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer and manager (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli footballer and manager (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American baseball player (d. 2021)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2021)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American singer and guitarist", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Kenyan marathon runner", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan marathon runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kenyan marathon runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American baseball player and coach", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Estonian actor, director, and screenwriter", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Reinum%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Reinum%C3%A4gi\" title=\"<PERSON>\"><PERSON></a>, Estonian actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Reinum%C3%A4gi"}]}, {"year": "1975", "text": "<PERSON>, American author and educator", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Irish singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Sri Lankan cricketer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, American football player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American politician", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish film director and screenwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish film director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish film director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, English actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English golfer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Brazilian footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American actor", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English academic and politician", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Jamaican singer-songwriter and producer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Scottish footballer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, American football player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Catchings\" title=\"Tamika Catchings\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Catchings\" title=\"Tamika Catchings\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Catchings"}]}, {"year": "1979", "text": "<PERSON>, Mexican footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Ukrainian footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American football player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, French skier", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/CC_Sabathia\" title=\"CC Sabathia\"><PERSON> Sabathia</a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/CC_Sabathia\" title=\"CC Sabathia\">CC Sabathia</a>, American baseball player", "links": [{"title": "CC Sabathia", "link": "https://wikipedia.org/wiki/CC_Sabathia"}]}, {"year": "1980", "text": "<PERSON>, Australian journalist and sportscaster", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Australian rules footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Heath_Scotland\" title=\"Heath Scotland\">Heath Scotland</a>, Australian rules footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Heath_Scotland\" title=\"Heath Scotland\">Heath Scotland</a>, Australian rules footballer", "links": [{"title": "Heath Scotland", "link": "https://wikipedia.org/wiki/Heath_Scotland"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Faith\" title=\"Paloma Faith\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"Paloma Faith\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ma_Faith"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Canadian figure skater", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Joaqu%C3%ADn_(footballer,_b._1981)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, b. 1981)\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo<PERSON><PERSON>%C3%ADn_(footballer,_b._1981)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> (footballer, b. 1981)\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (footballer, b. 1981)", "link": "https://wikipedia.org/wiki/Joaqu%C3%ADn_(footballer,_b._1981)"}]}, {"year": "1981", "text": "<PERSON>, American musician, American Idol contestant", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, <a href=\"https://wikipedia.org/wiki/American_Idol\" title=\"American Idol\">American Idol</a> contestant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician, <a href=\"https://wikipedia.org/wiki/American_Idol\" title=\"American Idol\">American Idol</a> contestant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "American Idol", "link": "https://wikipedia.org/wiki/American_Idol"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, German cyclist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Australian swimmer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Japanese newscaster and actress (d. 2017)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Japanese newscaster and actress (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, Japanese newscaster and actress (d. 2017)", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Ke<PERSON>_<PERSON>_II\" title=\"Kellen Winslow II\"><PERSON><PERSON> II</a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_II\" title=\"Kellen Winslow II\"><PERSON><PERSON> II</a>, American football player", "links": [{"title": "<PERSON><PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_II"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Dutch footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, English footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON>, Estonian footballer", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mati_<PERSON>mber"}]}, {"year": "1985", "text": "<PERSON>, American basketball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Ghanaian footballer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ghanaian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American-English singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American-English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American-English singer-songwriter", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1987", "text": "<PERSON>, American journalist[citation needed]", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON><PERSON>, Mexican footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/Jes%C3%<PERSON><PERSON>_<PERSON><PERSON><PERSON>_(footballer)"}]}, {"year": "1988", "text": "<PERSON><PERSON>, American rapper", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)\" title=\"<PERSON><PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON><PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(rapper)"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, American basketball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Scottish footballer (d. 2016)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Scottish_footballer)\" title=\"<PERSON> (Scottish footballer)\"><PERSON></a>, Scottish footballer (d. 2016)", "links": [{"title": "<PERSON> (Scottish footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_(Scottish_footballer)"}]}, {"year": "1989", "text": "<PERSON>, American actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Mexican footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Mexican footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Marco_Fabi%C3%A1n"}]}, {"year": "1989", "text": "<PERSON>, English actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Juno_Temple\" title=\"Juno Temple\">Juno Temple</a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Juno_Temple\" title=\"Juno Temple\">Juno Temple</a>, English actress", "links": [{"title": "Juno Temple", "link": "https://wikipedia.org/wiki/Juno_Temple"}]}, {"year": "1989", "text": "<PERSON>, British actor", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON>, Congolese athlete", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>an<PERSON>em<PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON>an<PERSON>em<PERSON>\"><PERSON><PERSON><PERSON></a>, Congolese athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>an<PERSON>_<PERSON>emba"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_b._1990)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, b. 1990)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_b._1990)\" class=\"mw-redirect\" title=\"<PERSON> (footballer, b. 1990)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer, b. 1990)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer,_b._1990)"}]}, {"year": "1990", "text": "<PERSON>, South African-English cricketer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-English cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON><PERSON><PERSON>, Cuban amateur heavyweight boxer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Erislandy_Sav%C3%B3n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban amateur heavyweight boxer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erislandy_Sav%C3%B3n\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cuban amateur heavyweight boxer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erislandy_Sav%C3%B3n"}]}, {"year": "1991", "text": "<PERSON>, Portuguese model", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sara Sampaio\"><PERSON></a>, Portuguese model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Sara Sampaio\"><PERSON></a>, Portuguese model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sara_<PERSON>io"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Italian-American football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English actress", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Estonian épée fencer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian épée fencer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian épée fencer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Turkish actor and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Burak_%C3%87elik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bura<PERSON>_%C3%87elik\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish actor and model", "links": [{"title": "Burak <PERSON>k", "link": "https://wikipedia.org/wiki/Burak_%C3%87elik"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American rapper", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON><PERSON></a>, American rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1992", "text": "<PERSON>, Italian slalom canoeist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Italian slalom canoeist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(canoeist)\" title=\"<PERSON> (canoeist)\"><PERSON></a>, Italian slalom canoeist", "links": [{"title": "<PERSON> (canoeist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(canoeist)"}]}, {"year": "1992", "text": "<PERSON>, Belgian DJ and record producer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian DJ and record producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian DJ and record producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>itte"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Polish volleyball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish volleyball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish volleyball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American figure skater", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American figure skater", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, Australian cricketer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American Canadian football player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Canadian_football\" title=\"Canadian football\">Canadian football</a> player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Canadian_football\" title=\"Canadian football\">Canadian football</a> player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Canadian football", "link": "https://wikipedia.org/wiki/Canadian_football"}]}, {"year": "1992", "text": "<PERSON>, American soccer player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American baseball pitcher", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball pitcher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)\" title=\"<PERSON> (left-handed pitcher)\"><PERSON></a>, American baseball pitcher", "links": [{"title": "<PERSON> (left-handed pitcher)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(left-handed_pitcher)"}]}, {"year": "1992", "text": "<PERSON>, Moldovan DJ and producer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan DJ and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moldovan DJ and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Japanese javelin thrower", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(javelin_thrower)\" title=\"<PERSON><PERSON> (javelin thrower)\"><PERSON><PERSON></a>, Japanese javelin thrower", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(javelin_thrower)\" title=\"<PERSON><PERSON> (javelin thrower)\"><PERSON><PERSON></a>, Japanese javelin thrower", "links": [{"title": "<PERSON><PERSON> (javelin thrower)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_(javelin_thrower)"}]}, {"year": "1992", "text": "<PERSON>, American-born Nigerian hurdler", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-born Nigerian hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Miles_U<PERSON>oma\" title=\"<PERSON>\"><PERSON></a>, American-born Nigerian hurdler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Miles_Ukaoma"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gebrigtsen"}]}, {"year": "1998", "text": "<PERSON>, American singer-songwriter.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Czech tennis player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Czech tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Norwegian footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Erling_<PERSON>\" title=\"<PERSON>rlin<PERSON> Ha<PERSON>d\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Erling_<PERSON>\" title=\"<PERSON>rlin<PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Erling_<PERSON>d"}]}, {"year": "2000", "text": "<PERSON>, South Korean singer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)\" title=\"<PERSON> (South Korean singer)\"><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON> (South Korean singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Korean_singer)"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Brazilian footballer", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_2006)\" title=\"<PERSON><PERSON> (footballer, born 2006)\"><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_2006)\" title=\"<PERSON><PERSON> (footballer, born 2006)\"><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON> (footballer, born 2006)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(footballer,_born_2006)"}]}], "Deaths": [{"year": "658", "text": "<PERSON><PERSON><PERSON> <PERSON>, Mayan ruler (b. 588)", "html": "658 - <a href=\"https://wikipedia.org/wiki/K%27an_II\" class=\"mw-redirect\" title=\"K'an II\">K'an II</a>, Mayan ruler (b. 588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%27an_II\" class=\"mw-redirect\" title=\"K'an II\">K'<PERSON> II</a>, Mayan ruler (b. 588)", "links": [{"title": "K'an II", "link": "https://wikipedia.org/wiki/K%27an_II"}]}, {"year": "710", "text": "<PERSON>, princess of the Tang dynasty", "html": "710 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>\"><PERSON></a>, princess of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>\" title=\"Princess <PERSON><PERSON>\"><PERSON></a>, princess of the <a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang dynasty</a>", "links": [{"title": "Princess <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}]}, {"year": "710", "text": "<PERSON>, empress of the Tang dynasty", "html": "710 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Tang_Dynasty)\" class=\"mw-redirect\" title=\"Empress <PERSON> (Tang Dynasty)\"><PERSON></a>, empress of the Tang dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Tang_Dynasty)\" class=\"mw-redirect\" title=\"Empress <PERSON> (Tang Dynasty)\"><PERSON></a>, empress of the Tang dynasty", "links": [{"title": "<PERSON> (Tang Dynasty)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(Tang_Dynasty)"}]}, {"year": "710", "text": "<PERSON><PERSON><PERSON>, Chinese poet (b. 664)", "html": "710 - <a href=\"https://wikipedia.org/wiki/Shan<PERSON><PERSON>_<PERSON>%27er\" title=\"<PERSON><PERSON><PERSON>'er\"><PERSON><PERSON><PERSON></a>, Chinese poet (b. 664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shan<PERSON><PERSON>_<PERSON>%27er\" title=\"<PERSON><PERSON><PERSON>'er\"><PERSON><PERSON><PERSON></a>, Chinese poet (b. 664)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shangg<PERSON>_<PERSON>%27er"}]}, {"year": "987", "text": "<PERSON>, Count of Anjou", "html": "987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou\" title=\"<PERSON>, Count of Anjou\"><PERSON>, Count of Anjou</a>", "links": [{"title": "<PERSON>, Count of Anjou", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Anjou"}]}, {"year": "1259", "text": "<PERSON><PERSON> of Goryeo", "html": "1259 - <a href=\"https://wikipedia.org/wiki/Gojong_of_Goryeo\" title=\"Gojong of Goryeo\">Gojong of Goryeo</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gojong_of_Goryeo\" title=\"Gojong of Goryeo\">Gojong of Goryeo</a>", "links": [{"title": "<PERSON><PERSON> of Goryeo", "link": "https://wikipedia.org/wiki/Gojong_of_Goryeo"}]}, {"year": "1403", "text": "<PERSON>, English soldier (b. 1364)", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hotspur)\" title=\"<PERSON> (Hotspur)\"><PERSON></a>, English soldier (b. 1364)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hotspur)\" title=\"<PERSON> (Hotspur)\"><PERSON></a>, English soldier (b. 1364)", "links": [{"title": "<PERSON> (Hotspur)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Hotspur)"}]}, {"year": "1403", "text": "Sir <PERSON>, English soldier, standard-bearer of <PERSON>", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\">Sir <PERSON></a>, English soldier, standard-bearer of <PERSON>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)\" title=\"<PERSON> (soldier)\">Sir <PERSON></a>, English soldier, standard-bearer of <PERSON>", "links": [{"title": "<PERSON> (soldier)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(soldier)"}]}, {"year": "1403", "text": "<PERSON>, 5th Earl of Stafford, English soldier", "html": "1403 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Stafford\" title=\"<PERSON>, 5th Earl of Stafford\"><PERSON>, 5th Earl of Stafford</a>, English soldier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Stafford\" title=\"<PERSON>, 5th Earl of Stafford\"><PERSON>, 5th Earl of Stafford</a>, English soldier", "links": [{"title": "<PERSON>, 5th Earl of Stafford", "link": "https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Stafford"}]}, {"year": "1425", "text": "<PERSON>, Byzantine emperor (b. 1350)", "html": "1425 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1350)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine emperor (b. 1350)", "links": [{"title": "<PERSON> II <PERSON>laiologos", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1552", "text": "<PERSON>, Spanish politician, 1st Viceroy of New Spain (b. 1495)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, 1st <a href=\"https://wikipedia.org/wiki/Viceroy_of_New_Spain\" class=\"mw-redirect\" title=\"Viceroy of New Spain\">Viceroy of New Spain</a> (b. 1495)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish politician, 1st <a href=\"https://wikipedia.org/wiki/Viceroy_of_New_Spain\" class=\"mw-redirect\" title=\"Viceroy of New Spain\">Viceroy of New Spain</a> (b. 1495)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Viceroy of New Spain", "link": "https://wikipedia.org/wiki/Viceroy_of_New_Spain"}]}, {"year": "1688", "text": "<PERSON>, 1st Duke of Ormonde, English soldier and politician, Lord Lieutenant of Ireland (b. 1610)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ormonde\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Ormonde\"><PERSON>, 1st Duke of Ormonde</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ormonde\" class=\"mw-redirect\" title=\"<PERSON>, 1st Duke of Ormonde\"><PERSON>, 1st Duke of Ormonde</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1610)", "links": [{"title": "<PERSON>, 1st Duke of Ormonde", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Ormonde"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1793", "text": "<PERSON>, French admiral, explorer, and politician (b. 1739)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Entrecasteaux\" title=\"<PERSON>Entrecasteau<PERSON>\"><PERSON></a>, French admiral, explorer, and politician (b. 1739)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%27Entrecasteaux\" title=\"<PERSON>Entrecasteau<PERSON>\"><PERSON></a>, French admiral, explorer, and politician (b. 1739)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>%27Entrecasteaux"}]}, {"year": "1796", "text": "<PERSON>, Scottish poet and songwriter (b. 1759)", "html": "1796 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and songwriter (b. 1759)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish poet and songwriter (b. 1759)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Count of Clerfayt, Austrian field marshal (b. 1733)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Clerfayt\" title=\"<PERSON>, Count of Clerfayt\"><PERSON>, Count of Clerfayt</a>, Austrian field marshal (b. 1733)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>,_Count_of_Clerfayt\" title=\"<PERSON>, Count of Clerfayt\"><PERSON>, Count of Clerfayt</a>, Austrian field marshal (b. 1733)", "links": [{"title": "<PERSON>, Count of Clerfayt", "link": "https://wikipedia.org/wiki/Fran%C3%A7ois_S%C3%A<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>_<PERSON>,_Count_<PERSON>_<PERSON><PERSON>"}]}, {"year": "1798", "text": "<PERSON>, Irish rebel leader (b. ca. 1760)", "html": "1798 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel leader (b. ca. 1760)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish rebel leader (b. ca. 1760)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON>, Australian surgeon and politician (b. 1789)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and politician (b. 1789)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian surgeon and politician (b. 1789)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American outlaw (b. 1851)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a>, American outlaw (b. 1851)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)\" title=\"<PERSON> (outlaw)\"><PERSON></a>, American outlaw (b. 1851)", "links": [{"title": "<PERSON> (outlaw)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outlaw)"}]}, {"year": "1880", "text": "<PERSON><PERSON>, American general and politician (b. 1800)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and politician (b. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American general and politician (b. 1800)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, American lawyer and politician, 1st Governor of Wisconsin (b. 1813)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Governor_of_Wisconsin\" title=\"Governor of Wisconsin\">Governor of Wisconsin</a> (b. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Wisconsin", "link": "https://wikipedia.org/wiki/Governor_of_Wisconsin"}]}, {"year": "1899", "text": "<PERSON>, American soldier, lawyer, and politician (b. 1833)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1833)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier, lawyer, and politician (b. 1833)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON><PERSON><PERSON><PERSON>, English astronomer and educator (b. 1864)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English astronomer and educator (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, English astronomer and educator (b. 1864)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, English actress (b. 1847)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1847)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress (b. 1847)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American baseball player (b. 1858)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American lawyer and author (b. 1860)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and author (b. 1860)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON><PERSON>, Ukrainian poet and scholar (b. 1872)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian poet and scholar (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian poet and scholar (b. 1872)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American runner and actor (b. 1900)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and actor (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner and actor (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, French art critic (b. 1870)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art critic (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French art critic (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_<PERSON>celles"}]}, {"year": "1944", "text": "<PERSON>, German soldier who attempted to assassinate <PERSON> (b. 1907)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier who <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">attempted to assassinate <PERSON></a> (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German soldier who <a href=\"https://wikipedia.org/wiki/20_July_plot\" title=\"20 July plot\">attempted to assassinate <PERSON></a> (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "20 July plot", "link": "https://wikipedia.org/wiki/20_July_plot"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Bolivian soldier and politician, 45th President of Bolivia (b. 1908)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian soldier and politician, 45th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bolivian soldier and politician, 45th <a href=\"https://wikipedia.org/wiki/President_of_Bolivia\" title=\"President of Bolivia\">President of Bolivia</a> (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Bolivia", "link": "https://wikipedia.org/wiki/President_of_Bolivia"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON><PERSON>, Armenian-American painter and illustrator (b. 1904)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Arshile_Gorky\" title=\"Arshile Gorky\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian-American painter and illustrator (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ars<PERSON>e_Gorky\" title=\"Arshile Gorky\"><PERSON><PERSON><PERSON><PERSON></a>, Armenian-American painter and illustrator (b. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Mexican politician, president for 45 minutes on February 13, 1913. (b. 1856)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, president for 45 minutes on February 13, 1913. (b. 1856)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1in\" title=\"<PERSON>\"><PERSON></a>, Mexican politician, president for 45 minutes on February 13, 1913. (b. 1856)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Lascur%C3%A1in"}]}, {"year": "1966", "text": "<PERSON>, Austrian-American physicist, mathematician, and philosopher, Vienna Circle member (b. 1884)", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist, mathematician, and philosopher, Vienna Circle member (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American physicist, mathematician, and philosopher, Vienna Circle member (b. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, American baseball player, coach, and manager (b. 1907)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, South African academic and politician, Nobel Prize laureate (b. 1898)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, South African academic and politician, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1967", "text": "<PERSON>, South African-American actor and singer (b. 1892)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor and singer (b. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-American actor and singer (b. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American dancer and choreographer (b. 1878)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Denis\" title=\"Ruth <PERSON>. Denis\"><PERSON></a>, American dancer and choreographer (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>._Denis\" title=\"Ruth St. Denis\"><PERSON></a>, American dancer and choreographer (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ruth_St._Denis"}]}, {"year": "1970", "text": "<PERSON>, Russian anthropologist and sculptor (b. 1907)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian anthropologist and sculptor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Russian anthropologist and sculptor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American football player and lieutenant (b. 1945)", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and lieutenant (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and lieutenant (b. 1945)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American sprinter and sailor (b. 1889)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and sailor (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sprinter and sailor (b. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Bhutanese king (b. 1928)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Jigme_Do<PERSON><PERSON>_<PERSON>\" title=\"Jigme <PERSON>\">Jig<PERSON></a>, Bhutanese king (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jigme_Do<PERSON><PERSON>_<PERSON>\" title=\"Jigme Do<PERSON>\">Jig<PERSON></a>, Bhutanese king (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American model and photographer (b. 1907)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and photographer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American model and photographer (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American journalist and actor (b. 1913)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, English race car driver (b. 1969)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(racing_driver)\" title=\"<PERSON> (racing driver)\"><PERSON></a>, English race car driver (b. 1969)", "links": [{"title": "<PERSON> (racing driver)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racing_driver)"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON>, French author and illustrator (b. 1908)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Marija<PERSON>\" title=\"Mari<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marija<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French author and illustrator (b. 1908)", "links": [{"title": "Marijac", "link": "https://wikipedia.org/wiki/Marijac"}]}, {"year": "1997", "text": "<PERSON>, Estonian-Canadian conductor and composer (b. 1926)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Canadian conductor and composer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian-Canadian conductor and composer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American admiral, pilot, and astronaut (b. 1923)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, pilot, and astronaut (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral, pilot, and astronaut (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor and singer (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and singer (b. 1907)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2000", "text": "<PERSON>, American environmentalist and author (b. 1948)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American environmentalist and author (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2002", "text": "<PERSON><PERSON><PERSON><PERSON>, Russian-American author and illustrator (b. 1908)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Esphyr_Slobodkina\" title=\"Esp<PERSON>r Slobodkina\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-American author and illustrator (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/E<PERSON><PERSON>r_Slobodkina\" title=\"<PERSON>sp<PERSON>r Slobodkina\"><PERSON><PERSON><PERSON><PERSON></a>, Russian-American author and illustrator (b. 1908)", "links": [{"title": "<PERSON><PERSON><PERSON>r <PERSON>", "link": "https://wikipedia.org/wiki/Esphyr_Slobodkina"}]}, {"year": "2003", "text": "<PERSON>, English-New Zealand runner and coach (b. 1938)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(middle-distance_runner)\" class=\"mw-redirect\" title=\"<PERSON> (middle-distance runner)\"><PERSON></a>, English-New Zealand runner and coach (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(middle-distance_runner)\" class=\"mw-redirect\" title=\"<PERSON> (middle-distance runner)\"><PERSON></a>, English-New Zealand runner and coach (b. 1938)", "links": [{"title": "<PERSON> (middle-distance runner)", "link": "https://wikipedia.org/wiki/<PERSON>_(middle-distance_runner)"}]}, {"year": "2004", "text": "<PERSON>, American composer and conductor (b. 1929)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American geneticist and biologist, Nobel Prize laureate (b. 1918)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American geneticist and biologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2005", "text": "<PERSON>, English-Canadian singer and actor (b. 1941)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Long John Baldry\"><PERSON></a>, English-Canadian singer and actor (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Long John Baldry\"><PERSON></a>, English-Canadian singer and actor (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "Lord <PERSON>, English-American wrestler and manager (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English-American wrestler and manager (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>\" title=\"Lord <PERSON>\">Lord <PERSON></a>, English-American wrestler and manager (b. 1928)", "links": [{"title": "Lord <PERSON>", "link": "https://wikipedia.org/wiki/Lord_<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Japanese-American actor and singer (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actor and singer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actor and singer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Cambodian soldier and monk (b. 1926)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Mo<PERSON>\" title=\"<PERSON> Mok\"><PERSON></a>, Cambodian soldier and monk (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Mo<PERSON>\" title=\"<PERSON> Mo<PERSON>\"><PERSON></a>, Cambodian soldier and monk (b. 1926)", "links": [{"title": "Ta Mok", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Croatian linguist and academic (b. 1949)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Dubravko_%C5%A0kiljan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian linguist and academic (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Dubravko_%C5%A0kiljan\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian linguist and academic (b. 1949)", "links": [{"title": "Dubrav<PERSON>", "link": "https://wikipedia.org/wiki/Dubravko_%C5%A0kiljan"}]}, {"year": "2008", "text": "<PERSON>, English businessman (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON></a>, English businessman (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON>\"><PERSON></a>, English businessman (b. 1914)", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Chilean educator and politician (b. 1916)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Chilean educator and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Chilean educator and politician (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Corval%C3%A1n"}]}, {"year": "2010", "text": "<PERSON>, American baseball player, coach, and manager (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, Canadian businessman (b. 1932)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Scottish-American journalist and author (b. 1941)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American journalist and author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American journalist and author (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American baseball player (b. 1924)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Albanian poet and author (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian poet and author (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Albanian poet and author (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, American admiral and pilot (b. 1916)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ramage\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Ramage\" title=\"<PERSON>\"><PERSON></a>, American admiral and pilot (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>ge"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, English-b. Welsh actress (b. 1944)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-b. Welsh actress (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English-b. Welsh actress (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English cricketer and coach (b. 1937)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, English cricketer and coach (b. 1937)", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "2013", "text": "<PERSON>, Italian motorcycle racer (b. 1988)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (b. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian motorcycle racer (b. 1988)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian martial artist (b. 1981)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_B<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Brojeshori Devi\"><PERSON><PERSON><PERSON><PERSON></a>, Indian martial artist (b. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> Brojeshori Devi\"><PERSON><PERSON><PERSON><PERSON></a>, Indian martial artist (b. 1981)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Dutch field hockey player (b. 1958)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de Be<PERSON>\"><PERSON></a>, Dutch field hockey player (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> de Beus\"><PERSON></a>, Dutch field hockey player (b. 1958)", "links": [{"title": "<PERSON> Be<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Colombian-French composer and educator (b. 1971)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian-French composer and educator (b. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Colombian-French composer and educator (b. 1971)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>-<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American football player and coach (b. 1920)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)\" title=\"<PERSON> (American football coach)\"><PERSON></a>, American football player and coach (b. 1920)", "links": [{"title": "<PERSON> (American football coach)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football_coach)"}]}, {"year": "2014", "text": "<PERSON>, Isleta Pueblo (Native American) writer, poet, and educator (b. 1926)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Isleta_Pueblo\" class=\"mw-redirect\" title=\"Isleta Pueblo\">Isleta Pueblo</a> (Native American) writer, poet, and educator (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Isleta_Pueblo\" class=\"mw-redirect\" title=\"Isleta Pueblo\">Isleta Pueblo</a> (Native American) writer, poet, and educator (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Isleta Pueblo", "link": "https://wikipedia.org/wiki/Isleta_Pueblo"}]}, {"year": "2014", "text": "<PERSON>, American businessman, invented the magicJack (b. 1961)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/MagicJack\" title=\"MagicJack\">magicJack</a> (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, invented the <a href=\"https://wikipedia.org/wiki/MagicJack\" title=\"MagicJack\">magicJack</a> (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "MagicJack", "link": "https://wikipedia.org/wiki/MagicJack"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, English engineer and pilot (b. 1915)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English engineer and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English engineer and pilot (b. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Let<PERSON>e_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, German lawyer and judge (b. 1943)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and judge (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German lawyer and judge (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON>, Nigerian engineer and politician (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian engineer and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Nigerian engineer and politician (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, New Zealand rugby player and boxer (b. 1927)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player and boxer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, New Zealand rugby player and boxer (b. 1927)", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "2015", "text": "<PERSON>, Swedish singer-songwriter (b. 1940)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish singer-songwriter (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON> <PERSON><PERSON>, American novelist, short story writer, and playwright (b. 1931)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist, short story writer, and playwright (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist, short story writer, and playwright (b. 1931)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American physician (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, American physician (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)\" title=\"<PERSON> (physician)\"><PERSON></a>, American physician (b. 1947)", "links": [{"title": "<PERSON> (physician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(physician)"}]}, {"year": "2015", "text": "<PERSON><PERSON><PERSON>, Polish-English sailor and academic (b. 1918)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Marchaj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English sailor and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Czes%C5%82aw_Marchaj\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish-English sailor and academic (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Czes%C5%82aw_Marchaj"}]}, {"year": "2015", "text": "<PERSON>, Dutch footballer (b. 1949)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch footballer (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American football player and coach (b. 1949)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach (b. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON>, American film and television actor (b. 1946)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film and television actor (b. 1946)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American film and television actor (b. 1946)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2018", "text": "<PERSON><PERSON>, U.S. Navy first female admiral (b. 1920)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, U.S. Navy first female admiral (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, U.S. Navy first female admiral (b. 1920)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Scottish-American singer and actress (b. 1930)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer and actress (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-American singer and actress (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, South African political activist (b. 1925)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African political activist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African political activist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American singer (b. 1926)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}