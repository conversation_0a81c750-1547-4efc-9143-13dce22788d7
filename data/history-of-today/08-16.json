{"date": "August 16", "url": "https://wikipedia.org/wiki/August_16", "data": {"Events": [{"year": "1 BC", "text": "<PERSON> consolidates his power in China and is declared marshal of state. Emperor <PERSON> of Han, who died the previous day, had no heirs.", "html": "1 BC - 1 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> consolidates his power in China and is declared marshal of state. <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>\" title=\"Emperor <PERSON>\">Emperor <PERSON> of Han</a>, who died the previous day, had no heirs.", "no_year_html": "1 BC - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> consolidates his power in China and is declared marshal of state. <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>\" title=\"Emperor <PERSON>\">Emperor <PERSON> of Han</a>, who died the previous day, had no heirs.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Emperor <PERSON> of Han", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "942", "text": "Start of the four-day Battle of al-Mada'in, between the Hamdanids of Mosul and the Baridis of Basra over control of the Abbasid capital, Baghdad.", "html": "942 - Start of the four-day <a href=\"https://wikipedia.org/wiki/Battle_of_al-Mada%27in\" title=\"Battle of al-Mada'in\">Battle of al-Mada'in</a>, between the <a href=\"https://wikipedia.org/wiki/Hamdanids\" class=\"mw-redirect\" title=\"Hamdanids\">Hamdanids</a> of <a href=\"https://wikipedia.org/wiki/Mosul\" title=\"Mosul\">Mosul</a> and the Baridis of <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a> over control of the <a href=\"https://wikipedia.org/wiki/Abbasid\" class=\"mw-redirect\" title=\"Abbasid\">Abbasid</a> capital, <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>.", "no_year_html": "Start of the four-day <a href=\"https://wikipedia.org/wiki/Battle_of_al-Mada%27in\" title=\"Battle of al-Mada'in\">Battle of al-Mada'in</a>, between the <a href=\"https://wikipedia.org/wiki/Hamdanids\" class=\"mw-redirect\" title=\"Hamdanids\">Hamdanids</a> of <a href=\"https://wikipedia.org/wiki/Mosul\" title=\"Mosul\">Mo<PERSON></a> and the Baridis of <a href=\"https://wikipedia.org/wiki/Basra\" title=\"Basra\">Basra</a> over control of the <a href=\"https://wikipedia.org/wiki/Abbasid\" class=\"mw-redirect\" title=\"Abbasid\">Abbasid</a> capital, <a href=\"https://wikipedia.org/wiki/Baghdad\" title=\"Baghdad\">Baghdad</a>.", "links": [{"title": "Battle of al-Mada'in", "link": "https://wikipedia.org/wiki/Battle_of_al-Mada%27in"}, {"title": "Hamdanids", "link": "https://wikipedia.org/wiki/Hamdanids"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mosul"}, {"title": "Basra", "link": "https://wikipedia.org/wiki/Basra"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>id"}, {"title": "Baghdad", "link": "https://wikipedia.org/wiki/Baghdad"}]}, {"year": "963", "text": "<PERSON><PERSON><PERSON><PERSON> is crowned emperor of the Byzantine Empire.", "html": "963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> Phokas</a> is crowned emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_II_Phokas\" title=\"<PERSON><PERSON><PERSON><PERSON> II Phokas\"><PERSON><PERSON><PERSON><PERSON> II Phokas</a> is crowned emperor of the <a href=\"https://wikipedia.org/wiki/Byzantine_Empire\" title=\"Byzantine Empire\">Byzantine Empire</a>.", "links": [{"title": "Nikephoros II Phokas", "link": "https://wikipedia.org/wiki/<PERSON>ph<PERSON>s_II_<PERSON>okas"}, {"title": "Byzantine Empire", "link": "https://wikipedia.org/wiki/Byzantine_Empire"}]}, {"year": "1328", "text": "The House of Gonzaga seizes power in the Duchy of Mantua, and will rule until 1708.", "html": "1328 - The <a href=\"https://wikipedia.org/wiki/House_of_Gonzaga\" title=\"House of Gonzaga\">House of Gonzaga</a> seizes power in the <a href=\"https://wikipedia.org/wiki/Duchy_of_Mantua\" title=\"Duchy of Mantua\">Duchy of Mantua</a>, and will rule until 1708.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/House_of_Gonzaga\" title=\"House of Gonzaga\">House of Gonzaga</a> seizes power in the <a href=\"https://wikipedia.org/wiki/Duchy_of_Mantua\" title=\"Duchy of Mantua\">Duchy of Mantua</a>, and will rule until 1708.", "links": [{"title": "House of Gonzaga", "link": "https://wikipedia.org/wiki/House_of_Gonzaga"}, {"title": "Duchy of Mantua", "link": "https://wikipedia.org/wiki/Duchy_of_Mantua"}]}, {"year": "1513", "text": "Battle of the Spurs (Battle of Guinegate): King <PERSON> of England and his Imperial allies defeat French Forces who are then forced to retreat.", "html": "1513 - <a href=\"https://wikipedia.org/wiki/Battle_of_the_Spurs\" title=\"Battle of the Spurs\">Battle of the Spurs</a> (Battle of Guinegate): King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"Henry VIII of England\"><PERSON> of England</a> and his <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial allies</a> defeat French Forces who are then forced to retreat.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_the_Spurs\" title=\"Battle of the Spurs\">Battle of the Spurs</a> (Battle of Guinegate): King <a href=\"https://wikipedia.org/wiki/<PERSON>_VIII_of_England\" class=\"mw-redirect\" title=\"<PERSON> VIII of England\"><PERSON> of England</a> and his <a href=\"https://wikipedia.org/wiki/Holy_Roman_Empire\" title=\"Holy Roman Empire\">Imperial allies</a> defeat French Forces who are then forced to retreat.", "links": [{"title": "Battle of the Spurs", "link": "https://wikipedia.org/wiki/Battle_of_the_Spurs"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_VIII_of_England"}, {"title": "Holy Roman Empire", "link": "https://wikipedia.org/wiki/Holy_Roman_Empire"}]}, {"year": "1570", "text": "The Principality of Transylvania is established after <PERSON> renounces his claim as King of Hungary in the Treaty of Speyer.", "html": "1570 - The <a href=\"https://wikipedia.org/wiki/Principality_of_Transylvania_(1570-1711)\" class=\"mw-redirect\" title=\"Principality of Transylvania (1570-1711)\">Principality of Transylvania</a> is established after <a href=\"https://wikipedia.org/wiki/John_II_Z%C3%A1polya\" class=\"mw-redirect\" title=\"John <PERSON>\">John <PERSON></a> renounces his claim as <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Speyer_(1570)\" title=\"Treaty of Speyer (1570)\">Treaty of Speyer</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Principality_of_Transylvania_(1570-1711)\" class=\"mw-redirect\" title=\"Principality of Transylvania (1570-1711)\">Principality of Transylvania</a> is established after <a href=\"https://wikipedia.org/wiki/John_II_Z%C3%A1polya\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> renounces his claim as <a href=\"https://wikipedia.org/wiki/King_of_Hungary\" title=\"King of Hungary\">King of Hungary</a> in the <a href=\"https://wikipedia.org/wiki/Treaty_of_Speyer_(1570)\" title=\"Treaty of Speyer (1570)\">Treaty of Speyer</a>.", "links": [{"title": "Principality of Transylvania (1570-1711)", "link": "https://wikipedia.org/wiki/Principality_of_Transylvania_(1570-1711)"}, {"title": "John II Zápolya", "link": "https://wikipedia.org/wiki/John_II_Z%C3%A1polya"}, {"title": "King of Hungary", "link": "https://wikipedia.org/wiki/King_of_Hungary"}, {"title": "Treaty of Speyer (1570)", "link": "https://wikipedia.org/wiki/Treaty_of_Speyer_(1570)"}]}, {"year": "1652", "text": "Battle of Plymouth: Inconclusive naval action between the fleets of <PERSON><PERSON><PERSON> and <PERSON> in the First Anglo-Dutch War.", "html": "1652 - <a href=\"https://wikipedia.org/wiki/Battle_of_Plymouth\" title=\"Battle of Plymouth\">Battle of Plymouth</a>: Inconclusive naval action between the fleets of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/First_Anglo-Dutch_War\" title=\"First Anglo-Dutch War\">First Anglo-Dutch War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Plymouth\" title=\"Battle of Plymouth\">Battle of Plymouth</a>: Inconclusive naval action between the fleets of <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> in the <a href=\"https://wikipedia.org/wiki/First_Anglo-Dutch_War\" title=\"First Anglo-Dutch War\">First Anglo-Dutch War</a>.", "links": [{"title": "Battle of Plymouth", "link": "https://wikipedia.org/wiki/Battle_of_Plymouth"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "First Anglo-Dutch War", "link": "https://wikipedia.org/wiki/First_Anglo-Dutch_War"}]}, {"year": "1777", "text": "American Revolutionary War: The Americans led by General <PERSON> rout British and Brunswick troops under <PERSON> at the Battle of Bennington in Walloomsac, New York.", "html": "1777 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The Americans led by General <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> rout <PERSON> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Brunswick\" title=\"Duchy of Brunswick\">Brunswick</a> troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bennington\" title=\"Battle of Bennington\">Battle of Bennington</a> in <a href=\"https://wikipedia.org/wiki/Walloomsac,_New_York\" title=\"Walloomsac, New York\">Walloomsac, New York</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The Americans led by General <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> rout <PERSON> and <a href=\"https://wikipedia.org/wiki/Duchy_of_Brunswick\" title=\"Duchy of Brunswick\">Brunswick</a> troops under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Bennington\" title=\"Battle of Bennington\">Battle of Bennington</a> in <a href=\"https://wikipedia.org/wiki/Walloomsac,_New_York\" title=\"Walloomsac, New York\">Walloomsac, New York</a>.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Duchy of Brunswick", "link": "https://wikipedia.org/wiki/Duchy_of_Brunswick"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Battle of Bennington", "link": "https://wikipedia.org/wiki/Battle_of_Bennington"}, {"title": "Walloomsac, New York", "link": "https://wikipedia.org/wiki/Walloomsac,_New_York"}]}, {"year": "1780", "text": "American Revolutionary War: Battle of Camden: The British defeat the Americans near Camden, South Carolina.", "html": "1780 - American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Battle_of_Camden\" title=\"Battle of Camden\">Battle of Camden</a>: The British defeat the Americans near <a href=\"https://wikipedia.org/wiki/Camden,_South_Carolina\" title=\"Camden, South Carolina\">Camden, South Carolina</a>.", "no_year_html": "American Revolutionary War: <a href=\"https://wikipedia.org/wiki/Battle_of_Camden\" title=\"Battle of Camden\">Battle of Camden</a>: The British defeat the Americans near <a href=\"https://wikipedia.org/wiki/Camden,_South_Carolina\" title=\"Camden, South Carolina\">Camden, South Carolina</a>.", "links": [{"title": "Battle of Camden", "link": "https://wikipedia.org/wiki/Battle_of_Camden"}, {"title": "Camden, South Carolina", "link": "https://wikipedia.org/wiki/Camden,_South_Carolina"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON> presents the petition of the Commune of Paris to the Legislative Assembly, which demanded the formation of a revolutionary tribunal.", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Maximilien_de_Robespierre\" class=\"mw-redirect\" title=\"Maximilien de Robespierre\">Maximilien de Robespierre</a> presents the petition of the <a href=\"https://wikipedia.org/wiki/Paris_Commune_(French_Revolution)\" class=\"mw-redirect\" title=\"Paris Commune (French Revolution)\">Commune of Paris</a> to the <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_(France)\" title=\"Legislative Assembly (France)\">Legislative Assembly</a>, which demanded the formation of a <a href=\"https://wikipedia.org/wiki/Revolutionary_Tribunal\" title=\"Revolutionary Tribunal\">revolutionary tribunal</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maximilien_de_Robespierre\" class=\"mw-redirect\" title=\"Maximilien de Robespierre\"><PERSON><PERSON><PERSON> de Robespierre</a> presents the petition of the <a href=\"https://wikipedia.org/wiki/Paris_Commune_(French_Revolution)\" class=\"mw-redirect\" title=\"Paris Commune (French Revolution)\">Commune of Paris</a> to the <a href=\"https://wikipedia.org/wiki/Legislative_Assembly_(France)\" title=\"Legislative Assembly (France)\">Legislative Assembly</a>, which demanded the formation of a <a href=\"https://wikipedia.org/wiki/Revolutionary_Tribunal\" title=\"Revolutionary Tribunal\">revolutionary tribunal</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maximilien_de_Robespierre"}, {"title": "Paris Commune (French Revolution)", "link": "https://wikipedia.org/wiki/Paris_Commune_(French_Revolution)"}, {"title": "Legislative Assembly (France)", "link": "https://wikipedia.org/wiki/Legislative_Assembly_(France)"}, {"title": "Revolutionary Tribunal", "link": "https://wikipedia.org/wiki/Revolutionary_Tribunal"}]}, {"year": "1793", "text": "French Revolution: A levée en masse is decreed by the National Convention.", "html": "1793 - <a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: A <a href=\"https://wikipedia.org/wiki/Lev%C3%A9e_en_masse\" title=\"Levée en masse\">levée en masse</a> is decreed by the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/French_Revolution\" title=\"French Revolution\">French Revolution</a>: A <a href=\"https://wikipedia.org/wiki/Lev%C3%A9e_en_masse\" title=\"Levée en masse\">levée en masse</a> is decreed by the <a href=\"https://wikipedia.org/wiki/National_Convention\" title=\"National Convention\">National Convention</a>.", "links": [{"title": "French Revolution", "link": "https://wikipedia.org/wiki/French_Revolution"}, {"title": "<PERSON><PERSON> en masse", "link": "https://wikipedia.org/wiki/Lev%C3%A9e_en_masse"}, {"title": "National Convention", "link": "https://wikipedia.org/wiki/National_Convention"}]}, {"year": "1812", "text": "War of 1812: American General <PERSON> surrenders Fort Detroit without a fight to the British Army.", "html": "1812 - <a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American General <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Hull\"><PERSON></a> surrenders <a href=\"https://wikipedia.org/wiki/Fort_Shelby_(Michigan)\" title=\"Fort Shelby (Michigan)\">Fort Detroit</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Detroit\" title=\"Siege of Detroit\">without a fight</a> to the British Army.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/War_of_1812\" title=\"War of 1812\">War of 1812</a>: American General <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Hull\"><PERSON></a> surrenders <a href=\"https://wikipedia.org/wiki/Fort_Shelby_(Michigan)\" title=\"Fort Shelby (Michigan)\">Fort Detroit</a> <a href=\"https://wikipedia.org/wiki/Siege_of_Detroit\" title=\"Siege of Detroit\">without a fight</a> to the British Army.", "links": [{"title": "War of 1812", "link": "https://wikipedia.org/wiki/War_of_1812"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> (Michigan)", "link": "https://wikipedia.org/wiki/Fort_Shelby_(Michigan)"}, {"title": "Siege of Detroit", "link": "https://wikipedia.org/wiki/Siege_of_Detroit"}]}, {"year": "1819", "text": "Peterloo Massacre: Seventeen people die and over 600 are injured in cavalry charges at a public meeting at St. Peter's Field, Manchester, England.", "html": "1819 - <a href=\"https://wikipedia.org/wiki/Peterloo_Massacre\" title=\"Peterloo Massacre\">Peterloo Massacre</a>: Seventeen people die and over 600 are injured in <a href=\"https://wikipedia.org/wiki/Cavalry\" title=\"Cavalry\">cavalry</a> charges at a public meeting at St. Peter's Field, <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peterloo_Massacre\" title=\"Peterloo Massacre\">Peterloo Massacre</a>: Seventeen people die and over 600 are injured in <a href=\"https://wikipedia.org/wiki/Cavalry\" title=\"Cavalry\">cavalry</a> charges at a public meeting at St. Peter's Field, <a href=\"https://wikipedia.org/wiki/Manchester\" title=\"Manchester\">Manchester</a>, England.", "links": [{"title": "Peterloo Massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Massacre"}, {"title": "Cavalry", "link": "https://wikipedia.org/wiki/Cavalry"}, {"title": "Manchester", "link": "https://wikipedia.org/wiki/Manchester"}]}, {"year": "1841", "text": "U.S. President <PERSON> vetoes a bill which called for the re-establishment of the Second Bank of the United States. Enraged Whig Party members riot outside the White House in the most violent demonstration on White House grounds in U.S. history.", "html": "1841 - <a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> vetoes a bill which called for the re-establishment of the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>. Enraged <a href=\"https://wikipedia.org/wiki/Whig_Party_(United_States)\" title=\"Whig Party (United States)\">Whig Party</a> members riot outside the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in the most violent demonstration on White House grounds in U.S. history.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/President_of_the_United_States\" title=\"President of the United States\">U.S. President</a> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a> vetoes a bill which called for the re-establishment of the <a href=\"https://wikipedia.org/wiki/Second_Bank_of_the_United_States\" title=\"Second Bank of the United States\">Second Bank of the United States</a>. Enraged <a href=\"https://wikipedia.org/wiki/Whig_Party_(United_States)\" title=\"Whig Party (United States)\">Whig Party</a> members riot outside the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in the most violent demonstration on White House grounds in U.S. history.", "links": [{"title": "President of the United States", "link": "https://wikipedia.org/wiki/President_of_the_United_States"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Second Bank of the United States", "link": "https://wikipedia.org/wiki/Second_Bank_of_the_United_States"}, {"title": "Whig Party (United States)", "link": "https://wikipedia.org/wiki/Whig_Party_(United_States)"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}]}, {"year": "1844", "text": "Governor-general of the Philippines <PERSON><PERSON><PERSON><PERSON>, signs a decree to reform the country's calendar by skipping Tuesday, December 31, as a solution to anomalies that had existed since <PERSON>'s arrival in 1521.", "html": "1844 - Governor-general of the Philippines <a href=\"https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa\" title=\"Narciso Clavería y Zaldúa\">Narciso Claveria</a>, signs a decree to reform the country's calendar by skipping Tuesday, <a href=\"https://wikipedia.org/wiki/December_31\" title=\"December 31\">December 31</a>, as a solution to anomalies that had existed since <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s arrival in <a href=\"https://wikipedia.org/wiki/1521\" title=\"1521\">1521</a>.", "no_year_html": "Governor-general of the Philippines <a href=\"https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa\" title=\"Narciso Clavería y Zaldúa\">Narciso Claveria</a>, signs a decree to reform the country's calendar by skipping Tuesday, <a href=\"https://wikipedia.org/wiki/December_31\" title=\"December 31\">December 31</a>, as a solution to anomalies that had existed since <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s arrival in <a href=\"https://wikipedia.org/wiki/1521\" title=\"1521\">1521</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> y Zaldúa", "link": "https://wikipedia.org/wiki/Narciso_Claver%C3%ADa_y_Zald%C3%BAa"}, {"title": "December 31", "link": "https://wikipedia.org/wiki/December_31"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "1521", "link": "https://wikipedia.org/wiki/1521"}]}, {"year": "1858", "text": "U.S. President <PERSON> inaugurates the new transatlantic telegraph cable by exchanging greetings with Queen <PERSON> of the United Kingdom. However, a weak signal forces a shutdown of the service in a few weeks.", "html": "1858 - U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> inaugurates the new <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> by exchanging greetings with <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom. However, a weak signal forces a shutdown of the service in a few weeks.", "no_year_html": "U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> inaugurates the new <a href=\"https://wikipedia.org/wiki/Transatlantic_telegraph_cable\" title=\"Transatlantic telegraph cable\">transatlantic telegraph cable</a> by exchanging greetings with <a href=\"https://wikipedia.org/wiki/Queen_Victoria\" title=\"Queen <PERSON>\">Queen <PERSON></a> of the United Kingdom. However, a weak signal forces a shutdown of the service in a few weeks.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Transatlantic telegraph cable", "link": "https://wikipedia.org/wiki/Transatlantic_telegraph_cable"}, {"title": "Queen <PERSON>", "link": "https://wikipedia.org/wiki/Queen_<PERSON>"}]}, {"year": "1859", "text": "The Grand Duchy of Tuscany formally deposes the exiled House of Lorraine.", "html": "1859 - The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Tuscany\" title=\"Grand Duchy of Tuscany\">Grand Duchy of Tuscany</a> formally deposes the exiled <a href=\"https://wikipedia.org/wiki/House_of_Lorraine\" title=\"House of Lorraine\">House of Lorraine</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Grand_Duchy_of_Tuscany\" title=\"Grand Duchy of Tuscany\">Grand Duchy of Tuscany</a> formally deposes the exiled <a href=\"https://wikipedia.org/wiki/House_of_Lorraine\" title=\"House of Lorraine\">House of Lorraine</a>.", "links": [{"title": "Grand Duchy of Tuscany", "link": "https://wikipedia.org/wiki/Grand_Duchy_of_Tuscany"}, {"title": "House of Lorraine", "link": "https://wikipedia.org/wiki/House_of_Lorraine"}]}, {"year": "1863", "text": "The Dominican Restoration War begins when <PERSON><PERSON> raises the Dominican flag in Santo Domingo after Spain had recolonized the country.", "html": "1863 - The <a href=\"https://wikipedia.org/wiki/Dominican_Restoration_War\" title=\"Dominican Restoration War\">Dominican Restoration War</a> begins when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> raises the <a href=\"https://wikipedia.org/wiki/Flag_of_the_Dominican_Republic\" title=\"Flag of the Dominican Republic\">Dominican flag</a> in <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\"><PERSON></a> after Spain had recolonized the country.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dominican_Restoration_War\" title=\"Dominican Restoration War\">Dominican Restoration War</a> begins when <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> raises the <a href=\"https://wikipedia.org/wiki/Flag_of_the_Dominican_Republic\" title=\"Flag of the Dominican Republic\">Dominican flag</a> in <a href=\"https://wikipedia.org/wiki/Santo_Domingo\" title=\"Santo Domingo\">Santo <PERSON></a> after Spain had recolonized the country.", "links": [{"title": "Dominican Restoration War", "link": "https://wikipedia.org/wiki/Dominican_Restoration_War"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gregorio_Luper%C3%B3n"}, {"title": "Flag of the Dominican Republic", "link": "https://wikipedia.org/wiki/Flag_of_the_Dominican_Republic"}, {"title": "Santo Domingo", "link": "https://wikipedia.org/wiki/Santo_Domingo"}]}, {"year": "1869", "text": "Battle of Acosta Ñu: A Paraguayan battalion largely made up of children is massacred by the Brazilian Army during the Paraguayan War.", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Battle_of_Acosta_%C3%91u\" title=\"Battle of Acosta Ñu\">Battle of Acosta Ñu</a>: A <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguayan</a> battalion largely made up of children is massacred by the <a href=\"https://wikipedia.org/wiki/Brazilian_Army\" title=\"Brazilian Army\">Brazilian Army</a> during the <a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Acosta_%C3%91u\" title=\"Battle of Acosta Ñu\">Battle of Acosta Ñu</a>: A <a href=\"https://wikipedia.org/wiki/Paraguay\" title=\"Paraguay\">Paraguayan</a> battalion largely made up of children is massacred by the <a href=\"https://wikipedia.org/wiki/Brazilian_Army\" title=\"Brazilian Army\">Brazilian Army</a> during the <a href=\"https://wikipedia.org/wiki/Paraguayan_War\" title=\"Paraguayan War\">Paraguayan War</a>.", "links": [{"title": "Battle of Acosta Ñu", "link": "https://wikipedia.org/wiki/Battle_of_Acosta_%C3%91u"}, {"title": "Paraguay", "link": "https://wikipedia.org/wiki/Paraguay"}, {"title": "Brazilian Army", "link": "https://wikipedia.org/wiki/Brazilian_Army"}, {"title": "Paraguayan War", "link": "https://wikipedia.org/wiki/Paraguayan_War"}]}, {"year": "1870", "text": "Franco-Prussian War: The Battle of Mars-la-Tour is fought, resulting in a Prussian victory.", "html": "1870 - <a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mars-la-Tour\" title=\"Battle of Mars-la-Tour\">Battle of Mars-la-Tour</a> is fought, resulting in a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franco-Prussian_War\" title=\"Franco-Prussian War\">Franco-Prussian War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Mars-la-Tour\" title=\"Battle of Mars-la-Tour\">Battle of Mars-la-Tour</a> is fought, resulting in a <a href=\"https://wikipedia.org/wiki/Kingdom_of_Prussia\" title=\"Kingdom of Prussia\">Prussian</a> victory.", "links": [{"title": "Franco-Prussian War", "link": "https://wikipedia.org/wiki/Franco-Prussian_War"}, {"title": "Battle of Mars-la-Tour", "link": "https://wikipedia.org/wiki/Battle_of_Mars-la-Tour"}, {"title": "Kingdom of Prussia", "link": "https://wikipedia.org/wiki/Kingdom_of_Prussia"}]}, {"year": "1876", "text": "<PERSON>'s <PERSON><PERSON><PERSON>, the penultimate opera in his Ring cycle, is premiered at the Bayreuth Festspielhaus.", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera)\" title=\"<PERSON><PERSON><PERSON> (opera)\"><PERSON><PERSON><PERSON></a></i>, the penultimate opera in his <i><a href=\"https://wikipedia.org/wiki/Der_Ring_des_Nibelungen\" title=\"Der Ring des Nibelungen\">Ring</a></i> cycle, is premiered at the <a href=\"https://wikipedia.org/wiki/Bayreuth_Festspielhaus\" title=\"Bayreuth Festspielhaus\">Bayreuth Festspielhaus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera)\" title=\"<PERSON><PERSON><PERSON> (opera)\"><PERSON><PERSON><PERSON></a></i>, the penultimate opera in his <i><a href=\"https://wikipedia.org/wiki/Der_Ring_des_Nibelungen\" title=\"Der Ring des Nibelungen\">Ring</a></i> cycle, is premiered at the <a href=\"https://wikipedia.org/wiki/Bayreuth_Festspielhaus\" title=\"Bayreuth Festspielhaus\">Bayreuth Festspielhaus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (opera)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(opera)"}, {"title": "Der Ring des Nibelungen", "link": "https://wikipedia.org/wiki/Der_Ring_des_Nibelungen"}, {"title": "Bayreuth Festspielhaus", "link": "https://wikipedia.org/wiki/Bayreuth_Festspielhaus"}]}, {"year": "1891", "text": "The Basilica of San Sebastian, Manila, the first all-steel church in Asia, is officially inaugurated and blessed.", "html": "1891 - The <a href=\"https://wikipedia.org/wiki/Basilica_of_San_Sebastian,_Manila\" class=\"mw-redirect\" title=\"Basilica of San Sebastian, Manila\">Basilica of San Sebastian, Manila</a>, the first all-steel church in Asia, is officially inaugurated and blessed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Basilica_of_San_Sebastian,_Manila\" class=\"mw-redirect\" title=\"Basilica of San Sebastian, Manila\">Basilica of San Sebastian, Manila</a>, the first all-steel church in Asia, is officially inaugurated and blessed.", "links": [{"title": "Basilica of San Sebastian, Manila", "link": "https://wikipedia.org/wiki/Basilica_of_San_Sebastian,_Manila"}]}, {"year": "1896", "text": "Skooku<PERSON>, <PERSON> and <PERSON> discover gold in a tributary of the Klondike River in Canada, setting off the Klondike Gold Rush.", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Keish\" title=\"Keish\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dawson <PERSON>\"><PERSON></a> discover gold in a tributary of the <a href=\"https://wikipedia.org/wiki/Klondike_River\" title=\"Klondike River\">Klondike River</a> in Canada, setting off the <a href=\"https://wikipedia.org/wiki/Klondike_Gold_Rush\" title=\"Klondike Gold Rush\">Klondike Gold Rush</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Keish\" title=\"Ke<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Dawson <PERSON>\"><PERSON></a> discover gold in a tributary of the <a href=\"https://wikipedia.org/wiki/Klondike_River\" title=\"Klondike River\">Klondike River</a> in Canada, setting off the <a href=\"https://wikipedia.org/wiki/Klondike_Gold_Rush\" title=\"Klondike Gold Rush\">Klondike Gold Rush</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Keish"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Klondike River", "link": "https://wikipedia.org/wiki/Klondike_River"}, {"title": "Klondike Gold Rush", "link": "https://wikipedia.org/wiki/Klondike_Gold_Rush"}]}, {"year": "1900", "text": "The Battle of Elands River during the Second Boer War ends after a 13-day siege is lifted by the British. The battle had begun when a force of between 2,000 and 3,000 Boers had surrounded a force of 500 Australians, Rhodesians, Canadians and British soldiers at a supply dump at Brakfontein Drift.", "html": "1900 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Elands_River_(1900)\" title=\"Battle of Elands River (1900)\">Battle of Elands River</a> during the Second Boer War ends after a 13-day siege is lifted by the British. The battle had begun when a force of between 2,000 and 3,000 Boers had surrounded a force of 500 Australians, Rhodesians, Canadians and British soldiers at a supply dump at Brakfontein Drift.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Elands_River_(1900)\" title=\"Battle of Elands River (1900)\">Battle of Elands River</a> during the Second Boer War ends after a 13-day siege is lifted by the British. The battle had begun when a force of between 2,000 and 3,000 Boers had surrounded a force of 500 Australians, Rhodesians, Canadians and British soldiers at a supply dump at Brakfontein Drift.", "links": [{"title": "Battle of Elands River (1900)", "link": "https://wikipedia.org/wiki/Battle_of_Elands_River_(1900)"}]}, {"year": "1906", "text": "The 8.2 .mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}Mw Valparaíso earthquake hits central Chile, killing 3,882 people.", "html": "1906 - The 8.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1906_Valpara%C3%ADso_earthquake\" title=\"1906 Valparaíso earthquake\">Valparaíso earthquake</a> hits central Chile, killing 3,882 people.", "no_year_html": "The 8.2 <a href=\"https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw\" title=\"Seismic magnitude scales\"><style data-mw-deduplicate=\"TemplateStyles:r1038841319\">.mw-parser-output .tooltip-dotted{border-bottom:1px dotted;cursor:help}</style>\n<span class=\"rt-commentedText tooltip\" title=\"Moment mag. scale\">M<sub>w</sub></span></a> <a href=\"https://wikipedia.org/wiki/1906_Valpara%C3%ADso_earthquake\" title=\"1906 Valparaíso earthquake\">Valparaíso earthquake</a> hits central Chile, killing 3,882 people.", "links": [{"title": "Seismic magnitude scales", "link": "https://wikipedia.org/wiki/Seismic_magnitude_scales#Mw"}, {"title": "1906 Valparaíso earthquake", "link": "https://wikipedia.org/wiki/1906_Valpara%C3%ADso_earthquake"}]}, {"year": "1913", "text": "Tōhoku Imperial University of Japan (modern day Tohoku University) becomes the first university in Japan to admit female students.", "html": "1913 - Tōhoku Imperial University of Japan (modern day <a href=\"https://wikipedia.org/wiki/Tohoku_University\" title=\"Tohoku University\">Tohoku University</a>) becomes the first university in Japan to admit female students.", "no_year_html": "Tōhoku Imperial University of Japan (modern day <a href=\"https://wikipedia.org/wiki/Tohoku_University\" title=\"Tohoku University\">Tohoku University</a>) becomes the first university in Japan to admit female students.", "links": [{"title": "Tohoku University", "link": "https://wikipedia.org/wiki/Tohoku_University"}]}, {"year": "1913", "text": "Completion of the Royal Navy battlecruiser HMS Queen Mary.", "html": "1913 - Completion of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battlecruiser\" title=\"Battlecruiser\">battlecruiser</a> <a href=\"https://wikipedia.org/wiki/HMS_Queen_Mary\" title=\"HMS Queen Mary\">HMS <i>Queen <PERSON></i></a>.", "no_year_html": "Completion of the <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> <a href=\"https://wikipedia.org/wiki/Battlecruiser\" title=\"Battlecruiser\">battlecruiser</a> <a href=\"https://wikipedia.org/wiki/HMS_Queen_Mary\" title=\"HMS Queen Mary\">HMS <i>Queen <PERSON></i></a>.", "links": [{"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battlecruiser", "link": "https://wikipedia.org/wiki/Battlecruiser"}, {"title": "HMS Queen Mary", "link": "https://wikipedia.org/wiki/HMS_Queen_<PERSON>"}]}, {"year": "1916", "text": "The Migratory Bird Treaty between Canada and the United States is signed.", "html": "1916 - The <a href=\"https://wikipedia.org/wiki/Migratory_Bird_Treaty\" title=\"Migratory Bird Treaty\">Migratory Bird Treaty</a> between Canada and the United States is signed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Migratory_Bird_Treaty\" title=\"Migratory Bird Treaty\">Migratory Bird Treaty</a> between Canada and the United States is signed.", "links": [{"title": "Migratory Bird Treaty", "link": "https://wikipedia.org/wiki/Migratory_Bird_Treaty"}]}, {"year": "1918", "text": "The Battle of Lake Baikal was fought between the Czechoslovak Legion and the Red Army.", "html": "1918 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Baikal\" title=\"Battle of Lake Baikal\">Battle of Lake Baikal</a> was fought between the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Legion\" title=\"Czechoslovak Legion\">Czechoslovak Legion</a> and the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Lake_Baikal\" title=\"Battle of Lake Baikal\">Battle of Lake Baikal</a> was fought between the <a href=\"https://wikipedia.org/wiki/Czechoslovak_Legion\" title=\"Czechoslovak Legion\">Czechoslovak Legion</a> and the <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a>.", "links": [{"title": "Battle of Lake Baikal", "link": "https://wikipedia.org/wiki/Battle_of_Lake_Baikal"}, {"title": "Czechoslovak Legion", "link": "https://wikipedia.org/wiki/Czechoslovak_Legion"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}]}, {"year": "1920", "text": "<PERSON> of the Cleveland Indians is hit on the head by a fastball thrown by <PERSON> of the New York Yankees. Next day, <PERSON> will become the second player to die from injuries sustained in a Major League Baseball game.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cleveland_Indians\" class=\"mw-redirect\" title=\"Cleveland Indians\">Cleveland Indians</a> is hit on the head by a <a href=\"https://wikipedia.org/wiki/Fastball\" title=\"Fastball\">fastball</a> thrown by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/New_York_Yankees\" title=\"New York Yankees\">New York Yankees</a>. Next day, <PERSON> will become the second player to die from injuries sustained in a <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> game.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/Cleveland_Indians\" class=\"mw-redirect\" title=\"Cleveland Indians\">Cleveland Indians</a> is hit on the head by a <a href=\"https://wikipedia.org/wiki/Fastball\" title=\"Fastball\">fastball</a> thrown by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> of the <a href=\"https://wikipedia.org/wiki/New_York_Yankees\" title=\"New York Yankees\">New York Yankees</a>. Next day, <PERSON> will become the second player to die from injuries sustained in a <a href=\"https://wikipedia.org/wiki/Major_League_Baseball\" title=\"Major League Baseball\">Major League Baseball</a> game.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Cleveland Indians", "link": "https://wikipedia.org/wiki/Cleveland_Indians"}, {"title": "Fastball", "link": "https://wikipedia.org/wiki/Fastball"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York Yankees", "link": "https://wikipedia.org/wiki/New_York_Yankees"}, {"title": "Major League Baseball", "link": "https://wikipedia.org/wiki/Major_League_Baseball"}]}, {"year": "1920", "text": "The congress of the Communist Party of Bukhara opens. The congress would call for armed revolution.", "html": "1920 - The congress of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Bukhara\" title=\"Communist Party of Bukhara\">Communist Party of Bukhara</a> opens. The congress would call for armed revolution.", "no_year_html": "The congress of the <a href=\"https://wikipedia.org/wiki/Communist_Party_of_Bukhara\" title=\"Communist Party of Bukhara\">Communist Party of Bukhara</a> opens. The congress would call for armed revolution.", "links": [{"title": "Communist Party of Bukhara", "link": "https://wikipedia.org/wiki/Communist_Party_of_Bukhara"}]}, {"year": "1920", "text": "Polish-Soviet War: The Battle of Radzymin concludes; the Soviet Red Army is forced to turn away from Warsaw.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Radzymin_(1920)\" title=\"Battle of Radzymin (1920)\">Battle of Radzymin</a> concludes; the Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> is forced to turn away from <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War\" title=\"Polish-Soviet War\">Polish-Soviet War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Radzymin_(1920)\" title=\"Battle of Radzymin (1920)\">Battle of Radzymin</a> concludes; the Soviet <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> is forced to turn away from <a href=\"https://wikipedia.org/wiki/Warsaw\" title=\"Warsaw\">Warsaw</a>.", "links": [{"title": "Polish-Soviet War", "link": "https://wikipedia.org/wiki/Polish%E2%80%93Soviet_War"}, {"title": "Battle of Radzymin (1920)", "link": "https://wikipedia.org/wiki/Battle_of_Ra<PERSON><PERSON><PERSON>_(1920)"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Warsaw", "link": "https://wikipedia.org/wiki/Warsaw"}]}, {"year": "1923", "text": "The United Kingdom gives the name \"Ross Dependency\" to part of its claimed Antarctic territory and makes the Governor-General of the Dominion of New Zealand its administrator.", "html": "1923 - The United Kingdom gives the name \"<a href=\"https://wikipedia.org/wiki/Ross_Dependency\" title=\"Ross Dependency\">Ross De<PERSON>dency</a>\" to part of its claimed Antarctic territory and makes the <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General</a> of the <a href=\"https://wikipedia.org/wiki/Dominion_of_New_Zealand\" title=\"Dominion of New Zealand\">Dominion of New Zealand</a> its administrator.", "no_year_html": "The United Kingdom gives the name \"<a href=\"https://wikipedia.org/wiki/Ross_Dependency\" title=\"Ross Dependency\">Ross Dependency</a>\" to part of its claimed Antarctic territory and makes the <a href=\"https://wikipedia.org/wiki/Governor-General_of_New_Zealand\" title=\"Governor-General of New Zealand\">Governor-General</a> of the <a href=\"https://wikipedia.org/wiki/Dominion_of_New_Zealand\" title=\"Dominion of New Zealand\">Dominion of New Zealand</a> its administrator.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ross_<PERSON>pendency"}, {"title": "Governor-General of New Zealand", "link": "https://wikipedia.org/wiki/Governor-General_of_New_Zealand"}, {"title": "Dominion of New Zealand", "link": "https://wikipedia.org/wiki/Dominion_of_New_Zealand"}]}, {"year": "1927", "text": "The Dole Air Race begins from Oakland, California, to Honolulu, Hawaii, during which six out of the eight participating planes crash or disappear.", "html": "1927 - The <a href=\"https://wikipedia.org/wiki/Dole_Air_Race\" title=\"Dole Air Race\">Dole Air Race</a> begins from <a href=\"https://wikipedia.org/wiki/Oakland,_California\" title=\"Oakland, California\">Oakland, California</a>, to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a>, during which six out of the eight participating planes crash or disappear.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Dole_Air_Race\" title=\"Dole Air Race\">Dole Air Race</a> begins from <a href=\"https://wikipedia.org/wiki/Oakland,_California\" title=\"Oakland, California\">Oakland, California</a>, to <a href=\"https://wikipedia.org/wiki/Honolulu\" title=\"Honolulu\">Honolulu, Hawaii</a>, during which six out of the eight participating planes crash or disappear.", "links": [{"title": "Dole Air Race", "link": "https://wikipedia.org/wiki/Dole_Air_Race"}, {"title": "Oakland, California", "link": "https://wikipedia.org/wiki/Oakland,_California"}, {"title": "Honolulu", "link": "https://wikipedia.org/wiki/Honolulu"}]}, {"year": "1929", "text": "The 1929 Palestine riots break out in Mandatory Palestine between Palestinian Arabs and Jews and continue until the end of the month. In total, 133 Jews and 116 Arabs are killed.", "html": "1929 - The <a href=\"https://wikipedia.org/wiki/1929_Palestine_riots\" title=\"1929 Palestine riots\">1929 Palestine riots</a> break out in <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">Mandatory Palestine</a> between <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian Arabs</a> and Jews and continue until the end of the month. In total, 133 Jews and 116 Arabs are killed.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1929_Palestine_riots\" title=\"1929 Palestine riots\">1929 Palestine riots</a> break out in <a href=\"https://wikipedia.org/wiki/Mandatory_Palestine\" title=\"Mandatory Palestine\">Mandatory Palestine</a> between <a href=\"https://wikipedia.org/wiki/Palestinians\" title=\"Palestinians\">Palestinian Arabs</a> and Jews and continue until the end of the month. In total, 133 Jews and 116 Arabs are killed.", "links": [{"title": "1929 Palestine riots", "link": "https://wikipedia.org/wiki/1929_Palestine_riots"}, {"title": "Mandatory Palestine", "link": "https://wikipedia.org/wiki/Mandatory_Palestine"}, {"title": "Palestinians", "link": "https://wikipedia.org/wiki/Palestinians"}]}, {"year": "1930", "text": "The first color sound cartoon, <PERSON><PERSON><PERSON><PERSON>, is released by Ub Iwerks.", "html": "1930 - The first color sound <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a>, <i><a href=\"https://wikipedia.org/wiki/Fiddlesticks_(1930_film)\" title=\"Fiddlesticks (1930 film)\">Fiddlesticks</a></i>, is released by <a href=\"https://wikipedia.org/wiki/Ub_Iwerks\" title=\"Ub Iwerks\">Ub Iwerks</a>.", "no_year_html": "The first color sound <a href=\"https://wikipedia.org/wiki/Cartoon\" title=\"Cartoon\">cartoon</a>, <i><a href=\"https://wikipedia.org/wiki/Fiddlesticks_(1930_film)\" title=\"Fiddlesticks (1930 film)\">Fiddlesticks</a></i>, is released by <a href=\"https://wikipedia.org/wiki/Ub_Iwerks\" title=\"Ub Iwerks\">Ub Iwerks</a>.", "links": [{"title": "Cartoon", "link": "https://wikipedia.org/wiki/Cartoon"}, {"title": "Fiddlesticks (1930 film)", "link": "https://wikipedia.org/wiki/Fiddlesticks_(1930_film)"}, {"title": "Ub Iwerks", "link": "https://wikipedia.org/wiki/Ub_Iwerks"}]}, {"year": "1930", "text": "The first British Empire Games are opened in Hamilton, Ontario, by the Governor General of Canada, the Viscount <PERSON>.", "html": "1930 - The first <a href=\"https://wikipedia.org/wiki/Commonwealth_Games\" title=\"Commonwealth Games\">British Empire Games</a> are opened in <a href=\"https://wikipedia.org/wiki/Hamilton,_Ontario\" title=\"Hamilton, Ontario\">Hamilton, Ontario</a>, by the <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Willingdon\" title=\"<PERSON>, 1st Marquess of Willingdon\">Viscount <PERSON></a>.", "no_year_html": "The first <a href=\"https://wikipedia.org/wiki/Commonwealth_Games\" title=\"Commonwealth Games\">British Empire Games</a> are opened in <a href=\"https://wikipedia.org/wiki/Hamilton,_Ontario\" title=\"Hamilton, Ontario\">Hamilton, Ontario</a>, by the <a href=\"https://wikipedia.org/wiki/Governor_General_of_Canada\" title=\"Governor General of Canada\">Governor General of Canada</a>, the <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Marquess_of_Willingdon\" title=\"<PERSON>, 1st Marquess of Willingdon\">Viscount <PERSON></a>.", "links": [{"title": "Commonwealth Games", "link": "https://wikipedia.org/wiki/Commonwealth_Games"}, {"title": "Hamilton, Ontario", "link": "https://wikipedia.org/wiki/Hamilton,_Ontario"}, {"title": "Governor General of Canada", "link": "https://wikipedia.org/wiki/Governor_General_of_Canada"}, {"title": "<PERSON><PERSON>, 1st Marquess of Willingdon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>,_1st_Marquess_of_Willingdon"}]}, {"year": "1933", "text": "Christie Pits riot takes place in Toronto, Ontario.", "html": "1933 - <a href=\"https://wikipedia.org/wiki/Christie_Pits_riot\" title=\"Christie Pits riot\">Christie Pits riot</a> takes place in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Christie_Pits_riot\" title=\"Christie Pits riot\">Christie Pits riot</a> takes place in <a href=\"https://wikipedia.org/wiki/Toronto\" title=\"Toronto\">Toronto</a>, <a href=\"https://wikipedia.org/wiki/Ontario\" title=\"Ontario\">Ontario</a>.", "links": [{"title": "Christie Pits riot", "link": "https://wikipedia.org/wiki/Christie_Pits_riot"}, {"title": "Toronto", "link": "https://wikipedia.org/wiki/Toronto"}, {"title": "Ontario", "link": "https://wikipedia.org/wiki/Ontario"}]}, {"year": "1942", "text": "World War II: US Navy L-class blimp L-8 drifts in from the Pacific and eventually crashes in Daly City, California. The two-man crew cannot be found.", "html": "1942 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">US Navy</a> <a href=\"https://wikipedia.org/wiki/L-class_blimp\" title=\"L-class blimp\">L-class blimp</a> <a href=\"https://wikipedia.org/wiki/L-8\" title=\"L-8\">L-8</a> drifts in from the Pacific and eventually crashes in <a href=\"https://wikipedia.org/wiki/Daly_City,_California\" title=\"Daly City, California\">Daly City, California</a>. The two-man crew cannot be found.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/United_States_Navy\" title=\"United States Navy\">US Navy</a> <a href=\"https://wikipedia.org/wiki/L-class_blimp\" title=\"L-class blimp\">L-class blimp</a> <a href=\"https://wikipedia.org/wiki/L-8\" title=\"L-8\">L-8</a> drifts in from the Pacific and eventually crashes in <a href=\"https://wikipedia.org/wiki/Daly_City,_California\" title=\"Daly City, California\">Daly City, California</a>. The two-man crew cannot be found.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "United States Navy", "link": "https://wikipedia.org/wiki/United_States_Navy"}, {"title": "L-class blimp", "link": "https://wikipedia.org/wiki/L-class_blimp"}, {"title": "L-8", "link": "https://wikipedia.org/wiki/L-8"}, {"title": "Daly City, California", "link": "https://wikipedia.org/wiki/Daly_City,_California"}]}, {"year": "1944", "text": "First flight of a jet with forward-swept wings, the Junkers Ju 287.", "html": "1944 - First flight of a jet with <a href=\"https://wikipedia.org/wiki/Forward-swept_wing\" title=\"Forward-swept wing\">forward-swept wings</a>, the <a href=\"https://wikipedia.org/wiki/Junkers_Ju_287\" title=\"Junkers Ju 287\">Junkers Ju 287</a>.", "no_year_html": "First flight of a jet with <a href=\"https://wikipedia.org/wiki/Forward-swept_wing\" title=\"Forward-swept wing\">forward-swept wings</a>, the <a href=\"https://wikipedia.org/wiki/Junkers_Ju_287\" title=\"Junkers Ju 287\">Junkers Ju 287</a>.", "links": [{"title": "Forward-swept wing", "link": "https://wikipedia.org/wiki/Forward-swept_wing"}, {"title": "Junkers Ju 287", "link": "https://wikipedia.org/wiki/Junkers_Ju_287"}]}, {"year": "1945", "text": "The National Representatives' Congress, the precursor of the current National Assembly of Vietnam, convenes in Sơn Dương.", "html": "1945 - The National Representatives' Congress, the precursor of the current <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Vietnam\" title=\"National Assembly of Vietnam\">National Assembly of Vietnam</a>, convenes in <a href=\"https://wikipedia.org/wiki/S%C6%A1n_D%C6%B0%C6%A1ng\" class=\"mw-redirect\" title=\"Sơn Dương\">Sơn Dương</a>.", "no_year_html": "The National Representatives' Congress, the precursor of the current <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Vietnam\" title=\"National Assembly of Vietnam\">National Assembly of Vietnam</a>, convenes in <a href=\"https://wikipedia.org/wiki/S%C6%A1n_D%C6%B0%C6%A1ng\" class=\"mw-redirect\" title=\"Sơn Dương\">Sơn Dươ<PERSON></a>.", "links": [{"title": "National Assembly of Vietnam", "link": "https://wikipedia.org/wiki/National_Assembly_of_Vietnam"}, {"title": "Sơn <PERSON>", "link": "https://wikipedia.org/wiki/S%C6%A1n_D%C6%B0%C6%A1ng"}]}, {"year": "1946", "text": "Mass riots in Kolkata begin; more than 4,000 people would be killed in 72 hours.", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Direct_Action_Day\" title=\"Direct Action Day\">Mass riots</a> in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Kolkata</a> begin; more than 4,000 people would be killed in 72 hours.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Direct_Action_Day\" title=\"Direct Action Day\">Mass riots</a> in <a href=\"https://wikipedia.org/wiki/Kolkata\" title=\"Kolkata\">Kolkata</a> begin; more than 4,000 people would be killed in 72 hours.", "links": [{"title": "Direct Action Day", "link": "https://wikipedia.org/wiki/Direct_Action_Day"}, {"title": "Kolkata", "link": "https://wikipedia.org/wiki/Kolkata"}]}, {"year": "1946", "text": "The All Hyderabad Trade Union Congress is founded in Secunderabad.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/All_Hyderabad_Trade_Union_Congress\" title=\"All Hyderabad Trade Union Congress\">All Hyderabad Trade Union Congress</a> is founded in <a href=\"https://wikipedia.org/wiki/Secunderabad\" title=\"Secunderabad\">Secunderabad</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/All_Hyderabad_Trade_Union_Congress\" title=\"All Hyderabad Trade Union Congress\">All Hyderabad Trade Union Congress</a> is founded in <a href=\"https://wikipedia.org/wiki/Secunderabad\" title=\"Secunderabad\">Secunderabad</a>.", "links": [{"title": "All Hyderabad Trade Union Congress", "link": "https://wikipedia.org/wiki/All_Hyderabad_Trade_Union_Congress"}, {"title": "Secunderabad", "link": "https://wikipedia.org/wiki/Secunderabad"}]}, {"year": "1954", "text": "The first issue of Sports Illustrated is published.", "html": "1954 - The first issue of <i><a href=\"https://wikipedia.org/wiki/Sports_Illustrated\" title=\"Sports Illustrated\">Sports Illustrated</a></i> is published.", "no_year_html": "The first issue of <i><a href=\"https://wikipedia.org/wiki/Sports_Illustrated\" title=\"Sports Illustrated\">Sports Illustrated</a></i> is published.", "links": [{"title": "Sports Illustrated", "link": "https://wikipedia.org/wiki/Sports_Illustrated"}]}, {"year": "1960", "text": "Cyprus gains its independence from the United Kingdom.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> gains its independence from the United Kingdom.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyprus\" title=\"Cyprus\">Cyprus</a> gains its independence from the United Kingdom.", "links": [{"title": "Cyprus", "link": "https://wikipedia.org/wiki/Cyprus"}]}, {"year": "1960", "text": "<PERSON> parachutes from a balloon over New Mexico, United States, at 102,800 feet (31,300 m), setting three records that held until 2012: High-altitude jump, free fall, and highest speed by a human without an aircraft.", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> parachutes from a balloon over <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>, United States, at 102,800 feet (31,300 m), setting three records that held until 2012: High-altitude jump, <a href=\"https://wikipedia.org/wiki/Free_fall\" title=\"Free fall\">free fall</a>, and highest speed by a human without an aircraft.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> parachutes from a balloon over <a href=\"https://wikipedia.org/wiki/New_Mexico\" title=\"New Mexico\">New Mexico</a>, United States, at 102,800 feet (31,300 m), setting three records that held until 2012: High-altitude jump, <a href=\"https://wikipedia.org/wiki/Free_fall\" title=\"Free fall\">free fall</a>, and highest speed by a human without an aircraft.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "New Mexico", "link": "https://wikipedia.org/wiki/New_Mexico"}, {"title": "Free fall", "link": "https://wikipedia.org/wiki/Free_fall"}]}, {"year": "1964", "text": "Vietnam War: A coup d'état replaces <PERSON><PERSON><PERSON><PERSON> with General <PERSON><PERSON><PERSON><PERSON> as President of South Vietnam. A new constitution is established with aid from the U.S. Embassy.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: A <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> replaces <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Vă<PERSON>\"><PERSON>ư<PERSON><PERSON></a> with General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as President of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>. A new <a href=\"https://wikipedia.org/wiki/Constitution\" title=\"Constitution\">constitution</a> is established with aid from the <a href=\"https://wikipedia.org/wiki/List_of_diplomatic_missions_of_the_United_States\" title=\"List of diplomatic missions of the United States\">U.S. Embassy</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: A <i><a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a></i> replaces <a href=\"https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh\" title=\"Dương Văn <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> with General <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh\" title=\"<PERSON>uy<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> as President of <a href=\"https://wikipedia.org/wiki/South_Vietnam\" title=\"South Vietnam\">South Vietnam</a>. A new <a href=\"https://wikipedia.org/wiki/Constitution\" title=\"Constitution\">constitution</a> is established with aid from the <a href=\"https://wikipedia.org/wiki/List_of_diplomatic_missions_of_the_United_States\" title=\"List of diplomatic missions of the United States\">U.S. Embassy</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C6%B0%C6%A1ng_V%C4%83n_Minh"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Kh%C3%A1nh"}, {"title": "South Vietnam", "link": "https://wikipedia.org/wiki/South_Vietnam"}, {"title": "Constitution", "link": "https://wikipedia.org/wiki/Constitution"}, {"title": "List of diplomatic missions of the United States", "link": "https://wikipedia.org/wiki/List_of_diplomatic_missions_of_the_United_States"}]}, {"year": "1966", "text": "Vietnam War: The House Un-American Activities Committee begins investigations of Americans who have aided the Viet Cong. The committee intends to introduce legislation making these activities illegal. Anti-war demonstrators disrupt the meeting and 50 people are arrested.", "html": "1966 - Vietnam War: The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins investigations of Americans who have aided the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a>. The committee intends to introduce legislation making these activities illegal. Anti-war demonstrators disrupt the meeting and 50 people are arrested.", "no_year_html": "Vietnam War: The <a href=\"https://wikipedia.org/wiki/House_Un-American_Activities_Committee\" title=\"House Un-American Activities Committee\">House Un-American Activities Committee</a> begins investigations of Americans who have aided the <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a>. The committee intends to introduce legislation making these activities illegal. Anti-war demonstrators disrupt the meeting and 50 people are arrested.", "links": [{"title": "House Un-American Activities Committee", "link": "https://wikipedia.org/wiki/House_Un-American_Activities_Committee"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}]}, {"year": "1972", "text": "In an unsuccessful coup d'état attempt, the Royal Moroccan Air Force fires upon <PERSON> of Morocco's plane while he is traveling back to Rabat.", "html": "1972 - In an unsuccessful <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> <a href=\"https://wikipedia.org/wiki/1972_Moroccan_coup_attempt\" title=\"1972 Moroccan coup attempt\">attempt</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Moroccan_Air_Force\" title=\"Royal Moroccan Air Force\">Royal Moroccan Air Force</a> fires upon <a href=\"https://wikipedia.org/wiki/Hassan_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a>'s plane while he is traveling back to <a href=\"https://wikipedia.org/wiki/Rabat\" title=\"Rabat\">Ra<PERSON></a>.", "no_year_html": "In an unsuccessful <a href=\"https://wikipedia.org/wiki/Coup_d%27%C3%A9tat\" title=\"Coup d'état\">coup d'état</a> <a href=\"https://wikipedia.org/wiki/1972_Moroccan_coup_attempt\" title=\"1972 Moroccan coup attempt\">attempt</a>, the <a href=\"https://wikipedia.org/wiki/Royal_Moroccan_Air_Force\" title=\"Royal Moroccan Air Force\">Royal Moroccan Air Force</a> fires upon <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Morocco\" title=\"<PERSON> II of Morocco\"><PERSON> of Morocco</a>'s plane while he is traveling back to <a href=\"https://wikipedia.org/wiki/Rabat\" title=\"Rabat\"><PERSON><PERSON></a>.", "links": [{"title": "Coup d'état", "link": "https://wikipedia.org/wiki/Coup_d%27%C3%A9tat"}, {"title": "1972 Moroccan coup attempt", "link": "https://wikipedia.org/wiki/1972_Moroccan_coup_attempt"}, {"title": "Royal Moroccan Air Force", "link": "https://wikipedia.org/wiki/Royal_Moroccan_Air_Force"}, {"title": "<PERSON> II of Morocco", "link": "https://wikipedia.org/wiki/Hassan_II_of_Morocco"}, {"title": "Rabat", "link": "https://wikipedia.org/wiki/Rabat"}]}, {"year": "1975", "text": "Australian Prime Minister <PERSON><PERSON> symbolically hands over land to the Gurindji people after the eight-year Wave Hill walk-off, a landmark event in the history of Indigenous land rights in Australia, commemorated in a 1991 song by <PERSON> and an annual celebration.", "html": "1975 - Australian Prime Minister <a href=\"https://wikipedia.org/wiki/Gough_Whitlam\" title=\"Gough Whitlam\"><PERSON><PERSON> Whitlam</a> symbolically hands over land to the <a href=\"https://wikipedia.org/wiki/Gurindji_people\" title=\"Gurindji people\">Gurindji people</a> after the eight-year <a href=\"https://wikipedia.org/wiki/Wave_Hill_walk-off\" title=\"Wave Hill walk-off\">Wave Hill walk-off</a>, a landmark event in the history of <a href=\"https://wikipedia.org/wiki/Indigenous_land_rights_in_Australia\" title=\"Indigenous land rights in Australia\">Indigenous land rights in Australia</a>, commemorated in a 1991 song by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a> and an annual celebration.", "no_year_html": "Australian Prime Minister <a href=\"https://wikipedia.org/wiki/Gough_Whitlam\" title=\"Gough Whitlam\"><PERSON><PERSON> Whitlam</a> symbolically hands over land to the <a href=\"https://wikipedia.org/wiki/Gurindji_people\" title=\"Gurindji people\">Gurindji people</a> after the eight-year <a href=\"https://wikipedia.org/wiki/Wave_Hill_walk-off\" title=\"Wave Hill walk-off\">Wave Hill walk-off</a>, a landmark event in the history of <a href=\"https://wikipedia.org/wiki/Indigenous_land_rights_in_Australia\" title=\"Indigenous land rights in Australia\">Indigenous land rights in Australia</a>, commemorated in a 1991 song by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_musician)\" title=\"<PERSON> (Australian musician)\"><PERSON></a> and an annual celebration.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Go<PERSON>_Whitlam"}, {"title": "Gurindji people", "link": "https://wikipedia.org/wiki/Gurindji_people"}, {"title": "Wave Hill walk-off", "link": "https://wikipedia.org/wiki/Wave_Hill_walk-off"}, {"title": "Indigenous land rights in Australia", "link": "https://wikipedia.org/wiki/Indigenous_land_rights_in_Australia"}, {"title": "<PERSON> (Australian musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(Australian_musician)"}]}, {"year": "1987", "text": "Northwest Airlines Flight 255, a McDonnell Douglas MD-82, crashes after takeoff in Detroit, Michigan, killing 154 of the 155 on board, plus two people on the ground.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Northwest_Airlines_Flight_255\" title=\"Northwest Airlines Flight 255\">Northwest Airlines Flight 255</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80#MD-82\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-82</a>, crashes after takeoff in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>, killing 154 of the 155 on board, plus two people on the ground.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Northwest_Airlines_Flight_255\" title=\"Northwest Airlines Flight 255\">Northwest Airlines Flight 255</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80#MD-82\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-82</a>, crashes after takeoff in <a href=\"https://wikipedia.org/wiki/Detroit\" title=\"Detroit\">Detroit</a>, <a href=\"https://wikipedia.org/wiki/Michigan\" title=\"Michigan\">Michigan</a>, killing 154 of the 155 on board, plus two people on the ground.", "links": [{"title": "Northwest Airlines Flight 255", "link": "https://wikipedia.org/wiki/Northwest_Airlines_Flight_255"}, {"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_MD-80#MD-82"}, {"title": "Detroit", "link": "https://wikipedia.org/wiki/Detroit"}, {"title": "Michigan", "link": "https://wikipedia.org/wiki/Michigan"}]}, {"year": "1989", "text": "A solar particle event affects computers at the Toronto Stock Exchange, forcing a halt to trading.", "html": "1989 - A <a href=\"https://wikipedia.org/wiki/Solar_particle_event\" title=\"Solar particle event\">solar particle event</a> affects computers at the <a href=\"https://wikipedia.org/wiki/Toronto_Stock_Exchange\" title=\"Toronto Stock Exchange\">Toronto Stock Exchange</a>, forcing a halt to trading.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Solar_particle_event\" title=\"Solar particle event\">solar particle event</a> affects computers at the <a href=\"https://wikipedia.org/wiki/Toronto_Stock_Exchange\" title=\"Toronto Stock Exchange\">Toronto Stock Exchange</a>, forcing a halt to trading.", "links": [{"title": "Solar particle event", "link": "https://wikipedia.org/wiki/Solar_particle_event"}, {"title": "Toronto Stock Exchange", "link": "https://wikipedia.org/wiki/Toronto_Stock_Exchange"}]}, {"year": "1991", "text": "Indian Airlines Flight 257, a Boeing 737-200, crashes during approach to Imphal Airport, killing all 69 people on board.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_257\" title=\"Indian Airlines Flight 257\">Indian Airlines Flight 257</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-200</a>, crashes during approach to <a href=\"https://wikipedia.org/wiki/Imphal_Airport\" class=\"mw-redirect\" title=\"Imphal Airport\">Imphal Airport</a>, killing all 69 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Indian_Airlines_Flight_257\" title=\"Indian Airlines Flight 257\">Indian Airlines Flight 257</a>, a <a href=\"https://wikipedia.org/wiki/Boeing_737\" title=\"Boeing 737\">Boeing 737-200</a>, crashes during approach to <a href=\"https://wikipedia.org/wiki/Imphal_Airport\" class=\"mw-redirect\" title=\"Imphal Airport\">Imphal Airport</a>, killing all 69 people on board.", "links": [{"title": "Indian Airlines Flight 257", "link": "https://wikipedia.org/wiki/Indian_Airlines_Flight_257"}, {"title": "Boeing 737", "link": "https://wikipedia.org/wiki/Boeing_737"}, {"title": "Imphal Airport", "link": "https://wikipedia.org/wiki/Imphal_Airport"}]}, {"year": "2005", "text": "West Caribbean Airways Flight 708, a McDonnell Douglas MD-82, crashes in Machiques, Venezuela, killing all 160 people on board.", "html": "2005 - <a href=\"https://wikipedia.org/wiki/West_Caribbean_Airways_Flight_708\" title=\"West Caribbean Airways Flight 708\">West Caribbean Airways Flight 708</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80#MD-82\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-82</a>, crashes in <a href=\"https://wikipedia.org/wiki/Machiques\" title=\"Machiques\">Machiques</a>, <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, killing all 160 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/West_Caribbean_Airways_Flight_708\" title=\"West Caribbean Airways Flight 708\">West Caribbean Airways Flight 708</a>, a <a href=\"https://wikipedia.org/wiki/McDonnell_Douglas_MD-80#MD-82\" title=\"McDonnell Douglas MD-80\">McDonnell Douglas MD-82</a>, crashes in <a href=\"https://wikipedia.org/wiki/Machiques\" title=\"Machiques\">Machiques</a>, <a href=\"https://wikipedia.org/wiki/Venezuela\" title=\"Venezuela\">Venezuela</a>, killing all 160 people on board.", "links": [{"title": "West Caribbean Airways Flight 708", "link": "https://wikipedia.org/wiki/West_Caribbean_Airways_Flight_708"}, {"title": "McDonnell Douglas MD-80", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_MD-80#MD-82"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>hiques"}, {"title": "Venezuela", "link": "https://wikipedia.org/wiki/Venezuela"}]}, {"year": "2008", "text": "The Trump International Hotel and Tower in Chicago is topped off at 1,389 feet (423 m), at the time becoming the world's highest residence above ground-level.", "html": "2008 - The <a href=\"https://wikipedia.org/wiki/Trump_International_Hotel_and_Tower_(Chicago)\" title=\"Trump International Hotel and Tower (Chicago)\">Trump International Hotel and Tower</a> in Chicago is topped off at 1,389 feet (423 m), at the time becoming the world's highest <a href=\"https://wikipedia.org/wiki/Dwelling\" title=\"Dwelling\">residence</a> above ground-level.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Trump_International_Hotel_and_Tower_(Chicago)\" title=\"Trump International Hotel and Tower (Chicago)\">Trump International Hotel and Tower</a> in Chicago is topped off at 1,389 feet (423 m), at the time becoming the world's highest <a href=\"https://wikipedia.org/wiki/Dwelling\" title=\"Dwelling\">residence</a> above ground-level.", "links": [{"title": "Trump International Hotel and Tower (Chicago)", "link": "https://wikipedia.org/wiki/Trump_International_Hotel_and_Tower_(Chicago)"}, {"title": "Dwelling", "link": "https://wikipedia.org/wiki/Dwelling"}]}, {"year": "2010", "text": "AIRES Flight 8250 crashes at Gustavo Rojas Pinilla International Airport in San Andrés, San Andrés y Providencia, Colombia, killing two people.", "html": "2010 - <a href=\"https://wikipedia.org/wiki/AIRES_Flight_8250\" title=\"AIRES Flight 8250\">AIRES Flight 8250</a> crashes at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_International_Airport\" title=\"Gustavo Rojas Pinilla International Airport\">Gustavo <PERSON> International Airport</a> in <a href=\"https://wikipedia.org/wiki/San_Andr%C3%A9s,_San_Andr%C3%A9s_y_Providencia\" title=\"San Andrés, San Andrés y Providencia\">San Andrés, San Andrés y Providencia</a>, Colombia, killing two people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/AIRES_Flight_8250\" title=\"AIRES Flight 8250\">AIRES Flight 8250</a> crashes at <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_International_Airport\" title=\"Gustavo R<PERSON>s <PERSON>lla International Airport\">Gustavo <PERSON> International Airport</a> in <a href=\"https://wikipedia.org/wiki/San_Andr%C3%A9s,_San_Andr%C3%A9s_y_Providencia\" title=\"San Andrés, San Andrés y Providencia\">San Andrés, San Andrés y Providencia</a>, Colombia, killing two people.", "links": [{"title": "AIRES Flight 8250", "link": "https://wikipedia.org/wiki/AIRES_Flight_8250"}, {"title": "<PERSON> International Airport", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_International_Airport"}, {"title": "San Andrés, San Andrés y Providencia", "link": "https://wikipedia.org/wiki/San_Andr%C3%A9s,_San_Andr%C3%A9s_y_Providencia"}]}, {"year": "2012", "text": "South African police fatally shoot 34 miners and wound 78 more during an industrial dispute at Marikana near Rustenburg.", "html": "2012 - <a href=\"https://wikipedia.org/wiki/South_African_Police_Service\" title=\"South African Police Service\">South African police</a> fatally shoot 34 miners and wound 78 more during an <a href=\"https://wikipedia.org/wiki/Marikana_miners%27_strike\" class=\"mw-redirect\" title=\"Marikana miners' strike\">industrial dispute</a> at Marikana near <a href=\"https://wikipedia.org/wiki/Rustenburg\" title=\"Rustenburg\">Rustenburg</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/South_African_Police_Service\" title=\"South African Police Service\">South African police</a> fatally shoot 34 miners and wound 78 more during an <a href=\"https://wikipedia.org/wiki/Marikana_miners%27_strike\" class=\"mw-redirect\" title=\"Marikana miners' strike\">industrial dispute</a> at Marikana near <a href=\"https://wikipedia.org/wiki/Rustenburg\" title=\"Rustenburg\">Rustenburg</a>.", "links": [{"title": "South African Police Service", "link": "https://wikipedia.org/wiki/South_African_Police_Service"}, {"title": "Marikana miners' strike", "link": "https://wikipedia.org/wiki/Marikana_miners%27_strike"}, {"title": "Rustenburg", "link": "https://wikipedia.org/wiki/Rustenburg"}]}, {"year": "2013", "text": "The ferry St. Thomas <PERSON> collides with a cargo ship and sinks at Cebu, Philippines, killing 61 people with 59 others missing.", "html": "2013 - The ferry <a href=\"https://wikipedia.org/wiki/MV_St._<PERSON>_<PERSON>\" title=\"MV St. Thomas Aquinas\"><i><PERSON><PERSON></i></a> collides with a cargo ship and sinks at <a href=\"https://wikipedia.org/wiki/Cebu\" title=\"Cebu\">Cebu</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, killing 61 people with 59 others missing.", "no_year_html": "The ferry <a href=\"https://wikipedia.org/wiki/MV_St._<PERSON>_<PERSON>\" title=\"MV St. Thomas Aquinas\"><i><PERSON><PERSON></i></a> collides with a cargo ship and sinks at <a href=\"https://wikipedia.org/wiki/Cebu\" title=\"Cebu\">Cebu</a>, <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>, killing 61 people with 59 others missing.", "links": [{"title": "MV St. Thomas <PERSON>", "link": "https://wikipedia.org/wiki/MV_St<PERSON>_<PERSON>_<PERSON>"}, {"title": "Cebu", "link": "https://wikipedia.org/wiki/Cebu"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}]}, {"year": "2015", "text": "More than 96 people are killed and hundreds injured following a series of air-raids by the Syrian Arab Air Force on the rebel-held market town of Douma.", "html": "2015 - More than 96 people are killed and hundreds injured following a <a href=\"https://wikipedia.org/wiki/2015_Douma_market_massacre\" class=\"mw-redirect\" title=\"2015 Douma market massacre\">series of air-raids</a> by the <a href=\"https://wikipedia.org/wiki/Syrian_Arab_Air_Force\" class=\"mw-redirect\" title=\"Syrian Arab Air Force\">Syrian Arab Air Force</a> on the <a href=\"https://wikipedia.org/wiki/Syrian_opposition_to_Ba<PERSON>r_al-Assad\" title=\"Syrian opposition to Bashar al-Assad\">rebel</a>-held market town of <a href=\"https://wikipedia.org/wiki/Douma,_Syria\" title=\"Douma, Syria\">Douma</a>.", "no_year_html": "More than 96 people are killed and hundreds injured following a <a href=\"https://wikipedia.org/wiki/2015_Douma_market_massacre\" class=\"mw-redirect\" title=\"2015 Douma market massacre\">series of air-raids</a> by the <a href=\"https://wikipedia.org/wiki/Syrian_Arab_Air_Force\" class=\"mw-redirect\" title=\"Syrian Arab Air Force\">Syrian Arab Air Force</a> on the <a href=\"https://wikipedia.org/wiki/Syrian_opposition_to_Bashar_al-Assad\" title=\"Syrian opposition to Bashar al-Assad\">rebel</a>-held market town of <a href=\"https://wikipedia.org/wiki/Douma,_Syria\" title=\"Douma, Syria\">Douma</a>.", "links": [{"title": "2015 Douma market massacre", "link": "https://wikipedia.org/wiki/2015_Douma_market_massacre"}, {"title": "Syrian Arab Air Force", "link": "https://wikipedia.org/wiki/Syrian_Arab_Air_Force"}, {"title": "Syrian opposition to <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Syrian_opposition_to_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Douma, Syria", "link": "https://wikipedia.org/wiki/Douma,_Syria"}]}, {"year": "2015", "text": "Trigana Air Flight 267, an ATR 42, crashes in Oksibl, Bintang Mountains Regency, killing all 54 people on board.", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Trigana_Air_Flight_267\" title=\"Trigana Air Flight 267\">Trigana Air Flight 267</a>, an <a href=\"https://wikipedia.org/wiki/ATR_42\" title=\"ATR 42\">ATR 42</a>, crashes in <a href=\"https://wikipedia.org/wiki/Oksibil\" title=\"<PERSON><PERSON><PERSON>\">Oksibl</a>, <a href=\"https://wikipedia.org/wiki/Bintang_Mountains_Regency\" title=\"Bintang Mountains Regency\">Bintang Mountains Regency</a>, killing all 54 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Trigana_Air_Flight_267\" title=\"Trigana Air Flight 267\">Trigana Air Flight 267</a>, an <a href=\"https://wikipedia.org/wiki/ATR_42\" title=\"ATR 42\">ATR 42</a>, crashes in <a href=\"https://wikipedia.org/wiki/Oksibil\" title=\"<PERSON><PERSON><PERSON>\">Oksibl</a>, <a href=\"https://wikipedia.org/wiki/Bintang_Mountains_Regency\" title=\"Bintang Mountains Regency\">Bintang Mountains Regency</a>, killing all 54 people on board.", "links": [{"title": "Trigana Air Flight 267", "link": "https://wikipedia.org/wiki/Trigana_Air_Flight_267"}, {"title": "ATR 42", "link": "https://wikipedia.org/wiki/ATR_42"}, {"title": "Oksibil", "link": "https://wikipedia.org/wiki/Oksibil"}, {"title": "Bintang Mountains Regency", "link": "https://wikipedia.org/wiki/Bintang_Mountains_Regency"}]}, {"year": "2020", "text": "The August Complex fire in California burns more than one million acres of land.", "html": "2020 - The <a href=\"https://wikipedia.org/wiki/August_Complex_fire\" title=\"August Complex fire\">August Complex fire</a> in California burns more than one million acres of land.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/August_Complex_fire\" title=\"August Complex fire\">August Complex fire</a> in California burns more than one million acres of land.", "links": [{"title": "August Complex fire", "link": "https://wikipedia.org/wiki/August_Complex_fire"}]}], "Births": [{"year": "1355", "text": "<PERSON><PERSON>, 5th Countess of Ulster (d. 1382)", "html": "1355 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_5th_Countess_of_Ulster\" title=\"<PERSON><PERSON>, 5th Countess of Ulster\"><PERSON><PERSON>, 5th Countess of Ulster</a> (d. 1382)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>,_5th_Countess_of_Ulster\" title=\"<PERSON><PERSON>, 5th Countess of Ulster\"><PERSON><PERSON>, 5th Countess of Ulster</a> (d. 1382)", "links": [{"title": "<PERSON><PERSON>, 5th Countess of Ulster", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_5th_Countess_of_Ulster"}]}, {"year": "1378", "text": "Hongxi Emperor of China (d. 1425)", "html": "1378 - <a href=\"https://wikipedia.org/wiki/Hongxi_Emperor\" title=\"Hongxi Emperor\">Hongxi Emperor</a> of China (d. 1425)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hongxi_Emperor\" title=\"Hongxi Emperor\">Hongxi Emperor</a> of China (d. 1425)", "links": [{"title": "Hongxi Emperor", "link": "https://wikipedia.org/wiki/Hongxi_Emperor"}]}, {"year": "1401", "text": "<PERSON>, Countess of Hainaut (d. 1436)", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut</a> (d. 1436)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Countess_of_Hainaut\" title=\"<PERSON>, Countess of Hainaut\"><PERSON>, Countess of Hainaut</a> (d. 1436)", "links": [{"title": "<PERSON>, Countess of Hainaut", "link": "https://wikipedia.org/wiki/<PERSON>,_Countess_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1557", "text": "<PERSON><PERSON><PERSON>, Italian painter and etcher (d. 1602)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and etcher (d. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian painter and etcher (d. 1602)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ci"}]}, {"year": "1565", "text": "<PERSON>, Grand Duchess of Tuscany (d. 1637)", "html": "1565 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON>, Grand Duchess of Tuscany\"><PERSON>, Grand Duchess of Tuscany</a> (d. 1637)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Tuscany\" class=\"mw-redirect\" title=\"<PERSON>, Grand Duchess of Tuscany\"><PERSON>, Grand Duchess of Tuscany</a> (d. 1637)", "links": [{"title": "<PERSON>, Grand Duchess of Tuscany", "link": "https://wikipedia.org/wiki/<PERSON>,_Grand_Duchess_of_Tuscany"}]}, {"year": "1573", "text": "<PERSON> of Austria, Queen of Poland (d. 1598)", "html": "1573 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Poland\" title=\"<PERSON> of Austria, Queen of Poland\"><PERSON> of Austria, Queen of Poland</a> (d. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Austria,_Queen_of_Poland\" title=\"<PERSON> of Austria, Queen of Poland\"><PERSON> of Austria, Queen of Poland</a> (d. 1598)", "links": [{"title": "<PERSON> of Austria, Queen of Poland", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Austria,_Queen_of_Poland"}]}, {"year": "1637", "text": "Countess <PERSON><PERSON> of Barby-Mühlingen (d. 1706)", "html": "1637 - <a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Barby-M%C3%BChlingen\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Barby-Mühlingen\">Countess <PERSON><PERSON> of Barby-Mühlingen</a> (d. 1706)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON>_of_Barby-M%C3%BChlingen\" class=\"mw-redirect\" title=\"Countess <PERSON><PERSON> of Barby-Mühlingen\">Countess <PERSON><PERSON> of Barby-Mühlingen</a> (d. 1706)", "links": [{"title": "Countess <PERSON><PERSON> of Barby-Mühlingen", "link": "https://wikipedia.org/wiki/Countess_<PERSON><PERSON>_<PERSON><PERSON>_of_Barby-M%C3%BChlingen"}]}, {"year": "1645", "text": "<PERSON>, French philosopher and author (d. 1696)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1696)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON>\"><PERSON></a>, French philosopher and author (d. 1696)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1650", "text": "<PERSON>, Italian monk, cosmographer, and cartographer (d. 1718)", "html": "1650 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, cosmographer, and cartographer (d. 1718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian monk, cosmographer, and cartographer (d. 1718)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1682", "text": "<PERSON>, Duke of Burgundy (d. 1712)", "html": "1682 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(1682-1712)\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France (1682-1712)\"><PERSON>, Duke of Burgundy</a> (d. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(1682-1712)\" class=\"mw-redirect\" title=\"<PERSON>, <PERSON><PERSON><PERSON> of France (1682-1712)\"><PERSON>, Duke of Burgundy</a> (d. 1712)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON> of France (1682-1712)", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_of_France_(1682-1712)"}]}, {"year": "1744", "text": "<PERSON>, French astronomer and surveyor (d. 1804)", "html": "1744 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9chain\" title=\"<PERSON>\"><PERSON></a>, French astronomer and surveyor (d. 1804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9chain\" title=\"<PERSON>\"><PERSON></a>, French astronomer and surveyor (d. 1804)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pierre_M%C3%A9chain"}]}, {"year": "1761", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Russian pianist and composer (d. 1800)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/Yevstigney_Fomin\" title=\"Yevstigney Fomin\">Yev<PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Yevstigney_Fomin\" title=\"Yevstigney Fomin\">Yev<PERSON><PERSON><PERSON></a>, Russian pianist and composer (d. 1800)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yevstigney_Fomin"}]}, {"year": "1815", "text": "<PERSON>, Italian priest and educator (d. 1888)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and educator (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian priest and educator (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON><PERSON>, daughter of <PERSON> (d. 1820)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1820)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> (d. 1820)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, British salon organiser (d. 1959)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British salon organiser (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British salon organiser (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>p"}]}, {"year": "1820", "text": "<PERSON>, Canadian lawyer and politician, 1st Premier of New Brunswick (d. 1892)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (d. 1892)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Premier_of_New_Brunswick\" title=\"Premier of New Brunswick\">Premier of New Brunswick</a> (d. 1892)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Wetmore"}, {"title": "Premier of New Brunswick", "link": "https://wikipedia.org/wiki/Premier_of_New_Brunswick"}]}, {"year": "1821", "text": "<PERSON>, English mathematician and academic (d. 1895)", "html": "1821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English mathematician and academic (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON>, American cattle baron (d. 1884)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cattle baron (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cattle baron (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1831", "text": "<PERSON>, Canadian lawyer and politician, 7th Premier of Quebec (d. 1901)", "html": "1831 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 7th <a href=\"https://wikipedia.org/wiki/Premier_of_Quebec\" title=\"Premier of Quebec\">Premier of Quebec</a> (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of Quebec", "link": "https://wikipedia.org/wiki/Premier_of_Quebec"}]}, {"year": "1832", "text": "<PERSON>, German physician, psychologist, and physiologist (d. 1920)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, psychologist, and physiologist (d. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German physician, psychologist, and physiologist (d. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1842", "text": "<PERSON>, Ukrainian-German mathematician, chess player, and academic (d. 1922)", "html": "1842 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German mathematician, chess player, and academic (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian-German mathematician, chess player, and academic (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Luxembourger-French physicist and academic, Nobel Prize laureate (d. 1921)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-French physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "1848", "text": "<PERSON>, Russian general (d. 1926)", "html": "1848 - <a href=\"https://wikipedia.org/wiki/Vladimir_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general (d. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vladimir_<PERSON>"}]}, {"year": "1855", "text": "<PERSON>, Australian politician, 18th Premier of New South Wales (d. 1922)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_New_South_Wales\" title=\"Premier of New South Wales\">Premier of New South Wales</a> (d. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of New South Wales", "link": "https://wikipedia.org/wiki/Premier_of_New_South_Wales"}]}, {"year": "1856", "text": "<PERSON><PERSON><PERSON><PERSON>, Uruguayan general and politician (d. 1904)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/Aparicio_Saravia\" title=\"<PERSON><PERSON><PERSON><PERSON> Sara<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan general and politician (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON>o_<PERSON>\" title=\"<PERSON><PERSON><PERSON>o <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Uruguayan general and politician (d. 1904)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aparicio_<PERSON>"}]}, {"year": "1858", "text": "<PERSON>, German author (d. 1927)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German author (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, 7th <PERSON>, English-Scottish cricketer (d. 1938)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th <PERSON>\"><PERSON>, 7th <PERSON></a>, English-Scottish cricketer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th <PERSON>\"><PERSON>, 7th Baron <PERSON></a>, English-Scottish cricketer (d. 1938)", "links": [{"title": "<PERSON>, 7th <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Uruguayan-French poet and author (d. 1887)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan-French poet and author (d. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan-French poet and author (d. 1887)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1862", "text": "<PERSON>, American baseball player and coach (d. 1965)", "html": "1862 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ag<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (d. 1965)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, Scottish surgeon and suffragette (d. 1917)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and suffragette (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and suffragette (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, Australian socialist, poet and journalist (d. 1962)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian socialist, poet and journalist (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian socialist, poet and journalist (d. 1962)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1868", "text": "<PERSON><PERSON><PERSON>, American bodybuilder and publisher, founded Macfadden Publications (d. 1955)", "html": "1868 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American bodybuilder and publisher, founded <a href=\"https://wikipedia.org/wiki/Macfadden_Publications\" class=\"mw-redirect\" title=\"Macfadden Publications\">Macfadden Publications</a> (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American bodybuilder and publisher, founded <a href=\"https://wikipedia.org/wiki/Macfadden_Publications\" class=\"mw-redirect\" title=\"Macfadden Publications\">Macfadden Publications</a> (d. 1955)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Macfadden Publications", "link": "https://wikipedia.org/wiki/<PERSON>fadden_Publications"}]}, {"year": "1876", "text": "<PERSON>, Russian illustrator and stage designer (d. 1942)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian illustrator and stage designer (d. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian illustrator and stage designer (d. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON><PERSON><PERSON>, Spanish priest and engineer (d. 1935)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/Roque_Rua%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish priest and engineer (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ro<PERSON>_<PERSON>ua%C3%B1o\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish priest and engineer (d. 1935)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roque_Rua%C3%B1o"}]}, {"year": "1882", "text": "<PERSON><PERSON><PERSON><PERSON>, French swimmer and water polo player (d. 1968)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French swimmer and water polo player (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French swimmer and water polo player (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_M%C3%A9rchez"}]}, {"year": "1884", "text": "<PERSON>, Luxembourger-American author and publisher (d. 1967)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-American author and publisher (d. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Luxembourger-American author and publisher (d. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, British colonel, diplomat, writer and archaeologist (d. 1935)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British colonel, diplomat, writer and archaeologist (d. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, British colonel, diplomat, writer and archaeologist (d. 1935)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American violinist, composer, and bandleader (d. 1943)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and bandleader (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American violinist, composer, and bandleader (d. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, Canadian-American author and illustrator (d. 1982)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and illustrator (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and illustrator (d. 1982)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, American cartoonist and animator, co-created <PERSON> the Cat (d. 1983)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and animator, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cat\" title=\"<PERSON> the Cat\"><PERSON> the Cat</a></i> (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist and animator, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON>_the_Cat\" title=\"<PERSON> the Cat\"><PERSON> the Cat</a></i> (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> the Cat", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Cat"}]}, {"year": "1894", "text": "<PERSON>, American plumber and labor leader (d. 1980)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American plumber and labor leader (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American plumber and labor leader (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Greek-Swiss author and playwright (d. 1981)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Greek-Swiss author and playwright (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(novelist)\" title=\"<PERSON> (novelist)\"><PERSON></a>, Greek-Swiss author and playwright (d. 1981)", "links": [{"title": "<PERSON> (novelist)", "link": "https://wikipedia.org/wiki/<PERSON>(novelist)"}]}, {"year": "1895", "text": "<PERSON><PERSON>, Austrian-Swiss actress and singer (d. 2000)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss actress and singer (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Austrian-Swiss actress and singer (d. 2000)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, First Eagle Scout in the Boy Scouts of America (d. 1951)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Eagle Scout in the Boy Scouts of America (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, First Eagle Scout in the Boy Scouts of America (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Australian geologist and palaeontologist (d. 1976)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian geologist and palaeontologist (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian geologist and palaeontologist (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON><PERSON>, English author (d. 1974)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English author (d. 1974)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American author and playwright (d. 1934)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and playwright (d. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Japanese general, pilot, and politician (d. 1989)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a>, Japanese general, pilot, and politician (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>da\"><PERSON><PERSON></a>, Japanese general, pilot, and politician (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>u_Genda"}]}, {"year": "1904", "text": "<PERSON>, American biochemist and virologist, Nobel Prize laureate (d. 1971)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American biochemist and virologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1908", "text": "<PERSON>, American cellist and educator (d. 2010)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Cole\" title=\"Orlando Cole\"><PERSON></a>, American cellist and educator (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Orlando_Cole\" title=\"Orlando Cole\"><PERSON></a>, American cellist and educator (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Orlando_Cole"}]}, {"year": "1908", "text": "<PERSON>, Jr., American editor, novelist, short story writer, and essayist (d. 2000)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American editor, novelist, short story writer, and essayist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American editor, novelist, short story writer, and essayist (d. 2000)", "links": [{"title": "<PERSON>s <PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>,_Jr."}]}, {"year": "1909", "text": "<PERSON>, American organist and conductor (d. 1995)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and conductor (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American organist and conductor (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1986)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>dell"}]}, {"year": "1910", "text": "<PERSON>, American actress (d. 1992)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON> <PERSON><PERSON>, German economist and statistician (d. 1977)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German economist and statistician (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, German economist and statistician (d. 1977)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English footballer and manager (d. 1995)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and manager (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON><PERSON><PERSON>, Belarusian-Israeli politician, Prime Minister of Israel, Nobel Prize laureate (d. 1992)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Be<PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belarusian-Israeli politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Israel\" title=\"Prime Minister of Israel\">Prime Minister of Israel</a>, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>gin"}, {"title": "Prime Minister of Israel", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Israel"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1915", "text": "<PERSON>, American baritone singer (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baritone singer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baritone singer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hibbler"}]}, {"year": "1916", "text": "<PERSON><PERSON>, American race car driver (d. 2003)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Iggy Katona\"><PERSON><PERSON></a>, American race car driver (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"Iggy Katona\"><PERSON><PERSON></a>, American race car driver (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_<PERSON><PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American author (d. 1997)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Panamanian composer and educator (d. 2008)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Panamanian composer and educator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Panamanian composer and educator (d. 2008)", "links": [{"title": "<PERSON><PERSON>que <PERSON>ro", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ro"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, German captain (d. 2014)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German captain (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1920", "text": "<PERSON>, German-American poet, novelist, and short story writer (d. 1994)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American poet, novelist, and short story writer (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American poet, novelist, and short story writer (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON>, English comedian, radio scriptwriter and producer (d. 2011)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(variety_artist)\" title=\"<PERSON> (variety artist)\"><PERSON></a>, English comedian, radio scriptwriter and producer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(variety_artist)\" title=\"<PERSON> (variety artist)\"><PERSON></a>, English comedian, radio scriptwriter and producer (d. 2011)", "links": [{"title": "<PERSON> (variety artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(variety_artist)"}]}, {"year": "1922", "text": "<PERSON>, American pianist and bandleader (d. 2001)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and bandleader (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON><PERSON><PERSON>, Brazilian journalist and playwright (d. 2012)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/Mill%C3%B4r_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian journalist and playwright (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mill%C3%B4r_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian journalist and playwright (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mill%C3%B4r_<PERSON><PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American actor (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON></a>, American actor (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON>, American baseball player (d. 2022)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/Inez_<PERSON>\" title=\"Inez V<PERSON>\"><PERSON><PERSON></a>, American baseball player (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inez_<PERSON>\" title=\"Inez Voyce\"><PERSON><PERSON></a>, American baseball player (d. 2022)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Inez_Voyce"}]}, {"year": "1925", "text": "<PERSON>, American baseball player (d. 1983)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, American baseball player (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(third_baseman)\" title=\"<PERSON> (third baseman)\"><PERSON></a>, American baseball player (d. 1983)", "links": [{"title": "<PERSON> (third baseman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(third_baseman)"}]}, {"year": "1925", "text": "<PERSON>, American pianist and composer (d. 2002)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Mal_W<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mal_Waldron\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mal_Waldron"}]}, {"year": "1927", "text": "<PERSON>, American actress (d. 2008)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American actress and singer", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, American singer (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A9\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/E<PERSON><PERSON>_<PERSON>rm%C3%A9"}]}, {"year": "1928", "text": "<PERSON>, Turkish photographer and journalist (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/Ara_G%C3%BCler\" title=\"<PERSON>\"><PERSON></a>, Turkish photographer and journalist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ara_G%C3%<PERSON>ler\" title=\"<PERSON>\"><PERSON></a>, Turkish photographer and journalist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ara_G%C3%BCler"}]}, {"year": "1928", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American pastor, theologian, and activist (d. 2018)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, theologian, and activist (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor, theologian, and activist (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American pianist and composer (d. 1980)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, German footballer (d. 2003)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, American wrestler and trainer (d. 1997)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler and trainer (d. 1997)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor, director, and screenwriter (d. 2010)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American football player, sportscaster, and actor (d. 2015)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, sportscaster, and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Haitian educator and politician, 43rd President of Haiti (d. 2014)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian educator and politician, 43rd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian educator and politician, 43rd <a href=\"https://wikipedia.org/wiki/President_of_Haiti\" title=\"President of Haiti\">President of Haiti</a> (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Haiti", "link": "https://wikipedia.org/wiki/President_of_Haiti"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Mexican singer and actress (d. 2020)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>lor_<PERSON>\" title=\"<PERSON>lor Si<PERSON>tre\"><PERSON><PERSON></a>, Mexican singer and actress (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>lor Si<PERSON>\"><PERSON><PERSON></a>, Mexican singer and actress (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON>, German poet and translator", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>nz<PERSON>\"><PERSON><PERSON></a>, German poet and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Kunze\"><PERSON><PERSON></a>, German poet and translator", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>e"}]}, {"year": "1933", "text": "<PERSON>, English author and publisher (d. 2020)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and publisher (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American actress", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American colonel, pilot, and astronaut (d. 1994)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American colonel, pilot, and astronaut (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, British tennis player (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British tennis player (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English author (d. 2011)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Canadian-American photographer (d. 2022)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>land\" title=\"Douglas Kirkland\"><PERSON></a>, Canadian-American photographer (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>land\" title=\"Douglas Kirkland\"><PERSON></a>, Canadian-American photographer (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Douglas_<PERSON>land"}]}, {"year": "1934", "text": "<PERSON><PERSON>, American singer and actress", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, French actor, director, and screenwriter", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, English actor", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1934", "text": "<PERSON>, Australian cricketer (d. 2019)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>mble"}]}, {"year": "1935", "text": "<PERSON>, Canadian businessman", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Greek footballer and coach", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek footballer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American actress and singer", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English footballer and coach (d. 2015)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer and coach (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian journalist, lawyer, and politician", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Columbia_politician)\" title=\"<PERSON> (British Columbia politician)\"><PERSON></a>, Canadian journalist, lawyer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Columbia_politician)\" title=\"<PERSON> (British Columbia politician)\"><PERSON></a>, Canadian journalist, lawyer, and politician", "links": [{"title": "<PERSON> (British Columbia politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(British_Columbia_politician)"}]}, {"year": "1937", "text": "<PERSON>, American composer and producer", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Canadian politician (d. 2016)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Estonian chess player (d. 1987)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5tov\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B5tov\" title=\"<PERSON>\"><PERSON></a>, Estonian chess player (d. 1987)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Boris_R%C3%B5tov"}]}, {"year": "1939", "text": "<PERSON><PERSON>, Irish cardinal", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Se%C3%<PERSON><PERSON>_<PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, Irish cardinal", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Se%C3%<PERSON><PERSON>_<PERSON>_(bishop)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> (bishop)\"><PERSON><PERSON></a>, Irish cardinal", "links": [{"title": "<PERSON><PERSON> (bishop)", "link": "https://wikipedia.org/wiki/Se%C3%<PERSON><PERSON>_<PERSON>_(bishop)"}]}, {"year": "1939", "text": "<PERSON>, Trinidadian-English journalist and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-English journalist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian-English journalist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver\" title=\"<PERSON>haver\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver\" title=\"<PERSON>haver\"><PERSON></a>, American singer-songwriter and guitarist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>haver"}]}, {"year": "1939", "text": "<PERSON>, American singer, banjo player, and multi-instrumentalist (d. 2020)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, banjo player, and multi-instrumentalist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, banjo player, and multi-instrumentalist (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Australian director and producer", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Australian tennis player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American R&B singer-songwriter (d. 2006)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American R&amp;B singer-songwriter (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American soul singer (d. 2010)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soul singer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American soul singer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American football player (d. 2010)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Peoples\"><PERSON></a>, American football player (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, English singer-songwriter and guitarist (d. 2013)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor, director, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English race car driver (d. 2019)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English race car driver (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American ballerina and educator", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ballerina and educator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American guitarist, singer, recording engineer, and record producer (d. 2016)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer, recording engineer, and record producer (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist, singer, recording engineer, and record producer (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, British stage and film actor (d. 2015)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British stage and film actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British stage and film actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Iranian-Kurdish politician, President of Iraqi Kurdistan", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Kurdish politician, <a href=\"https://wikipedia.org/wiki/President_of_Iraqi_Kurdistan\" class=\"mw-redirect\" title=\"President of Iraqi Kurdistan\">President of Iraqi Kurdistan</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian-Kurdish politician, <a href=\"https://wikipedia.org/wiki/President_of_Iraqi_Kurdistan\" class=\"mw-redirect\" title=\"President of Iraqi Kurdistan\">President of Iraqi Kurdistan</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}, {"title": "President of Iraqi Kurdistan", "link": "https://wikipedia.org/wiki/President_of_Iraqi_Kurdistan"}]}, {"year": "1946", "text": "<PERSON>, American actress", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American lawyer and politician, United States Ambassador to New Zealand", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_New_Zealand\" class=\"mw-redirect\" title=\"United States Ambassador to New Zealand\">United States Ambassador to New Zealand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, <a href=\"https://wikipedia.org/wiki/United_States_Ambassador_to_New_Zealand\" class=\"mw-redirect\" title=\"United States Ambassador to New Zealand\">United States Ambassador to New Zealand</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "United States Ambassador to New Zealand", "link": "https://wikipedia.org/wiki/United_States_Ambassador_to_New_Zealand"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English fashion designer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English fashion designer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American politician, U.S. Representative from Oregon", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, U.S. Representative from Oregon", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician, U.S. Representative from Oregon", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Indian-born Dutch rock musician", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-born Dutch rock musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-born Dutch rock musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and manager", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Canadian educator and politician (d. 2021)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter and bass player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American drummer (d. 2014)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American football player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American guitarist and songwriter", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON>, Trinidadian runner", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Trinidadian runner", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Iranian-American actor", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Iranian-American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian cricketer", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Nigerian businessman and politician, 13th President of Nigeria (d. 2010)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua\" title=\"Umaru Musa Yar'Adua\"><PERSON><PERSON></a>, Nigerian businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua\" title=\"Umaru Musa Yar'Adua\"><PERSON><PERSON></a>, Nigerian businessman and politician, 13th <a href=\"https://wikipedia.org/wiki/President_of_Nigeria\" title=\"President of Nigeria\">President of Nigeria</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umaru_Musa_Yar%27Adua"}, {"title": "President of Nigeria", "link": "https://wikipedia.org/wiki/President_of_Nigeria"}]}, {"year": "1952", "text": "<PERSON>, American actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, American talk show host, singer, and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON></a>, American talk show host, singer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">Kat<PERSON><PERSON></a>, American talk show host, singer, and actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON> \"<PERSON><PERSON><PERSON><PERSON><PERSON>, American R&B singer-songwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22J.T.%22_<PERSON>\" title='<PERSON> \"J.<PERSON>.\" <PERSON>'><PERSON> \"J.T.\" <PERSON></a>, American R&amp;B singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22J.T.%22_<PERSON>\" title='<PERSON> \"J.<PERSON>.\" <PERSON>'><PERSON> \"J.T.\" <PERSON></a>, American R&amp;B singer-songwriter", "links": [{"title": "<PERSON> \"J.T<PERSON>\" <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_%22J.T.%22_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Canadian director, producer, and screenwriter", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Scottish-English politician and broadcaster", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English politician and broadcaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-English politician and broadcaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American actor", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)\" title=\"<PERSON> (American actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (American actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_actor)"}]}, {"year": "1955", "text": "<PERSON>, Irish surgeon and politician, Minister for Children and Youth Affairs", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, Irish surgeon and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Children,_Equality,_Disability,_Integration_and_Youth\" title=\"Minister for Children, Equality, Disability, Integration and Youth\">Minister for Children and Youth Affairs</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(Irish_politician)\" title=\"<PERSON> (Irish politician)\"><PERSON></a>, Irish surgeon and politician, <a href=\"https://wikipedia.org/wiki/Minister_for_Children,_Equality,_Disability,_Integration_and_Youth\" title=\"Minister for Children, Equality, Disability, Integration and Youth\">Minister for Children and Youth Affairs</a>", "links": [{"title": "<PERSON> (Irish politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(Irish_politician)"}, {"title": "Minister for Children, Equality, Disability, Integration and Youth", "link": "https://wikipedia.org/wiki/Minister_for_Children,_Equality,_Disability,_Integration_and_Youth"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Armenian soldier and politician (d. 2014)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian soldier and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Armenian soldier and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, American actress and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian lawyer and politician, Deputy Chief Minister of Maharashtra (d. 2015)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Deputy Chief Minister of Maharashtra\">Deputy Chief Minister of Maharashtra</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra\" class=\"mw-redirect\" title=\"Deputy Chief Minister of Maharashtra\">Deputy Chief Minister of Maharashtra</a> (d. 2015)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Deputy Chief Minister of Maharashtra", "link": "https://wikipedia.org/wiki/Deputy_Chief_Minister_of_Maharashtra"}]}, {"year": "1958", "text": "<PERSON>, American singer-songwriter, producer, actress, and director", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actress, and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, producer, actress, and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, French physicist", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Huillier\" title=\"<PERSON>\"><PERSON></a>, French physicist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Huillier\" title=\"<PERSON>\"><PERSON></a>, French physicist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Anne_L%27Huillier"}]}, {"year": "1958", "text": "<PERSON>, Argentinian tennis player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Belgian cyclist and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian cyclist and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Marc <PERSON>\"><PERSON></a>, Belgian cyclist and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON>, Belizean choreographer, dancer, and dance instructor (d. 2015)", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean choreographer, dancer, and dance instructor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Belizean choreographer, dancer, and dance instructor (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American actor, producer and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON><PERSON><PERSON><PERSON>, Austrian-American conductor and director", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-M%C3%B6st\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%B6st\" title=\"<PERSON>\"><PERSON></a>, Austrian-American conductor and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>er-M%C3%B6st"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian footballer and manager", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Alo%C3%ADsio_Pires_Alves\" class=\"mw-redirect\" title=\"Aloísio Pires Alves\"><PERSON><PERSON><PERSON><PERSON> Pi<PERSON></a>, Brazilian footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alo%C3%ADsio_Pires_Alves\" class=\"mw-redirect\" title=\"Aloísio Pires Alves\"><PERSON><PERSON><PERSON><PERSON> Pires Al<PERSON></a>, Brazilian footballer and manager", "links": [{"title": "Aloísio Pi<PERSON>", "link": "https://wikipedia.org/wiki/Alo%C3%ADsio_Pires_Alves"}]}, {"year": "1963", "text": "<PERSON>, American voice artist (d. 2014)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice artist (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American voice artist (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American tennis player and sportscaster", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American ice hockey player, coach, and commentator", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and commentator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player, coach, and commentator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, Australian rugby league player", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Swedish journalist, actress, and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist, actress, and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist, actress, and author", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Indian civil servant and politician, 7th Chief Minister of Delhi", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a>, Indian civil servant and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Delhi\" class=\"mw-redirect\" title=\"Chief Minister of Delhi\">Chief Minister of Delhi</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\">A<PERSON><PERSON></a>, Indian civil servant and politician, 7th <a href=\"https://wikipedia.org/wiki/Chief_Minister_of_Delhi\" class=\"mw-redirect\" title=\"Chief Minister of Delhi\">Chief Minister of Delhi</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/A<PERSON><PERSON>_<PERSON>"}, {"title": "Chief Minister of Delhi", "link": "https://wikipedia.org/wiki/Chief_Minister_of_Delhi"}]}, {"year": "1968", "text": "<PERSON>, American actor", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON><PERSON>, Slovenian skier", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vet\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>vet"}]}, {"year": "1968", "text": "<PERSON>, German photographer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German photographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American journalist and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON><PERSON>, Nepalese actress in Indian films", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nepalese actress in Indian films", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Nepalese actress in Indian films", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American actor", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, German footballer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Australian footballer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American singer and musician", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Guyanese cricketer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Shiv<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>v<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guyanese cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>v<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Guyanese cricketer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>ul"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Swiss skier", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON>, Hungarian swimmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Krisz<PERSON>_Egerszegi"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Ecuadorian footballer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Hurtado\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iv%C3%A1n_Hurtado\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ecuadorian footballer and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Iv%C3%A1n_Hurtado"}]}, {"year": "1974", "text": "<PERSON>, American football player", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, French footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> A<PERSON>he\"><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Didier A<PERSON>he\"><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>gathe"}]}, {"year": "1975", "text": "<PERSON><PERSON><PERSON>, Finnish footballer, coach, and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Finnish footballer, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)\" title=\"<PERSON><PERSON><PERSON> (footballer)\"><PERSON><PERSON><PERSON></a>, Finnish footballer, coach, and manager", "links": [{"title": "<PERSON><PERSON><PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(footballer)"}]}, {"year": "1975", "text": "<PERSON>, American actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, New Zealand director, screenwriter and actor", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Tai<PERSON>_Wait<PERSON>\" title=\"Tai<PERSON> Wait<PERSON>\"><PERSON><PERSON></a>, New Zealand director, screenwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wait<PERSON>\" title=\"<PERSON><PERSON> Wait<PERSON>\"><PERSON><PERSON></a>, New Zealand director, screenwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taika_Waititi"}]}, {"year": "1979", "text": "<PERSON>, Scottish footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian cricketer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)"}]}, {"year": "1980", "text": "<PERSON>, American singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English bass player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(bassist)\" title=\"<PERSON> (bassist)\"><PERSON></a>, English bass player", "links": [{"title": "<PERSON> (bassist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(bassist)"}]}, {"year": "1980", "text": "<PERSON>, Brazilian footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Piet_Rooija<PERSON>s\" title=\"Piet Rooijakkers\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Piet_Rooijakkers\" title=\"Piet Rooijakkers\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>s"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Paraguayan footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/R<PERSON>que_Santa_Cruz\" title=\"Roque <PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R<PERSON>que_Santa_Cruz\" title=\"Roque <PERSON>\"><PERSON><PERSON><PERSON></a>, Paraguayan footballer", "links": [{"title": "Roque <PERSON>", "link": "https://wikipedia.org/wiki/Roque_Santa_Cruz"}]}, {"year": "1982", "text": "<PERSON>, American actor", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Cam_Gigandet"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, English footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Greek basketball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Italian speed skater", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian speed skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian speed skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, American basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Candice Dupree\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Can<PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Estonian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Japanese baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yu Dar<PERSON>\"><PERSON></a>, Japanese baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yu Dar<PERSON>\"><PERSON></a>, Japanese baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Puerto Rican baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Maldonado\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mart%C3%ADn_Maldonado\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mart%C3%ADn_Maldonado"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Price\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Japanese voice actress and singer.", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Moroccan footballer", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Isma%C3%AFl_Aissati\" title=\"<PERSON><PERSON><PERSON><PERSON> Ai<PERSON>ti\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Isma%C3%AFl_Aissati\" title=\"<PERSON><PERSON><PERSON><PERSON> Ai<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Isma%C3%AFl_Ai<PERSON>ti"}]}, {"year": "1988", "text": "<PERSON>, American football player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Rume<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, American wrestler", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Chinese race walker", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Chinese race walker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)\" class=\"mw-redirect\" title=\"<PERSON> (racewalker)\"><PERSON></a>, Chinese race walker", "links": [{"title": "<PERSON> (racewalker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(racewalker)"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, French footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Nigerian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Godfrey_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Brazilian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAjo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%BAjo\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_<PERSON>_<PERSON>%C3%BAjo"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Irish actress", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Irish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American rapper, singer and songwriter", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Young_Thug\" title=\"Young Thug\"><PERSON> Thug</a>, American rapper, singer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Young_Thug\" title=\"Young Thug\"><PERSON> Thug</a>, American rapper, singer and songwriter", "links": [{"title": "<PERSON> Thug", "link": "https://wikipedia.org/wiki/<PERSON>_Thug"}]}, {"year": "1992", "text": "<PERSON>, Argentinian tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American actor and model", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American swimmer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dressel\" title=\"Cael<PERSON> Dressel\"><PERSON><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Dressel\" title=\"Cael<PERSON> Dressel\"><PERSON><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, American musician", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>son_Chance\" title=\"Greyson Chance\"><PERSON><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Greyson_Chance\" title=\"Greyson Chance\"><PERSON><PERSON></a>, American musician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Chance"}]}, {"year": "1999", "text": "<PERSON>, American figure skater", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Italian tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}], "Deaths": [{"year": "79", "text": "<PERSON> <PERSON>, Chinese Han dynasty consort (b. 40)", "html": "79 - AD 79 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Han_dynasty)\" title=\"Empress <PERSON> (Han dynasty)\">Empress <PERSON></a>, Chinese Han dynasty consort (b. 40)", "no_year_html": "AD 79 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>_(Han_dynasty)\" title=\"Empress <PERSON> (Han dynasty)\">Empress <PERSON></a>, Chinese Han dynasty consort (b. 40)", "links": [{"title": "Empress <PERSON> (Han dynasty)", "link": "https://wikipedia.org/wiki/Empress_<PERSON>_(Han_dynasty)"}]}, {"year": "856", "text": "<PERSON><PERSON><PERSON><PERSON>, bishop of Langres", "html": "856 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_(bishop_of_Langres)\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> (bishop of Langres)\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Langres\" title=\"Roman Catholic Diocese of Langres\">Langres</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_(bishop_of_Langres)\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON> (bishop of Langres)\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, bishop of <a href=\"https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Langres\" title=\"Roman Catholic Diocese of Langres\">Langres</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> (bishop of Langres)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_I_(bishop_of_Langres)"}, {"title": "Roman Catholic Diocese of Langres", "link": "https://wikipedia.org/wiki/Roman_Catholic_Diocese_of_Langres"}]}, {"year": "963", "text": "<PERSON><PERSON>, Byzantine general (b. 944)", "html": "963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Argyros\"><PERSON><PERSON></a>, Byzantine general (b. 944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Argy<PERSON>\"><PERSON><PERSON></a>, Byzantine general (b. 944)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1027", "text": "<PERSON> of Georgia (b. 998)", "html": "1027 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Georgia\" title=\"<PERSON> of Georgia\"><PERSON> of Georgia</a> (b. 998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Georgia\" title=\"<PERSON> of Georgia\"><PERSON> of Georgia</a> (b. 998)", "links": [{"title": "<PERSON> of Georgia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Georgia"}]}, {"year": "1153", "text": "<PERSON>, fourth Grand Master of the Knights Templar", "html": "1153 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, fourth <a href=\"https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar\" class=\"mw-redirect\" title=\"Grand Masters of the Knights Templar\">Grand Master of the Knights Templar</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Grand Masters of the Knights Templar", "link": "https://wikipedia.org/wiki/Grand_Masters_of_the_Knights_Templar"}]}, {"year": "1225", "text": "<PERSON><PERSON><PERSON>, Japanese regent and on<PERSON><PERSON><PERSON><PERSON><PERSON> (b. 1156)", "html": "1225 - <a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese regent and <a href=\"https://wikipedia.org/wiki/Onna-bugeisha\" class=\"mw-redirect\" title=\"Onna-bugeisha\">onna-bugeisha</a> (b. 1156)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese regent and <a href=\"https://wikipedia.org/wiki/Onna-bugeisha\" class=\"mw-redirect\" title=\"Onna-bugeisha\">onna-bugeisha</a> (b. 1156)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C5%8Dj%C5%8D_<PERSON><PERSON><PERSON>"}, {"title": "Onna-bugeisha", "link": "https://wikipedia.org/wiki/Onna-bugeisha"}]}, {"year": "1258", "text": "<PERSON>, Byzantine-Greek emperor (b. 1222)", "html": "1258 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine-Greek emperor (b. 1222)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Byzantine-Greek emperor (b. 1222)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1285", "text": "<PERSON>, Count of Savoy (b. 1207)", "html": "1285 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON>, Count of Savoy</a> (b. 1207)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy\" title=\"<PERSON>, Count of Savoy\"><PERSON>, Count of Savoy</a> (b. 1207)", "links": [{"title": "<PERSON>, Count of Savoy", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Count_of_Savoy"}]}, {"year": "1297", "text": "<PERSON> <PERSON> of Trebizond (b. 1262)", "html": "1297 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Trebizond\" title=\"<PERSON> II of Trebizond\"><PERSON> II of Trebizond</a> (b. 1262)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_Trebizond\" title=\"<PERSON> II of Trebizond\"><PERSON> II of Trebizond</a> (b. 1262)", "links": [{"title": "<PERSON> of Trebizond", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Trebizond"}]}, {"year": "1327", "text": "<PERSON><PERSON>, French saint (b. 1295)", "html": "1327 - <a href=\"https://wikipedia.org/wiki/Saint_R<PERSON>\" title=\"Saint Roch\"><PERSON><PERSON></a>, French saint (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Saint_Roch\" title=\"Saint Roch\"><PERSON><PERSON></a>, French saint (b. 1295)", "links": [{"title": "Saint Roch", "link": "https://wikipedia.org/wiki/Saint_Roch"}]}, {"year": "1339", "text": "<PERSON><PERSON><PERSON>, founder of the state of Milan (b. 1302)", "html": "1339 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> V<PERSON>nti\"><PERSON><PERSON><PERSON></a>, founder of the state of Milan (b. 1302)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>nti\"><PERSON><PERSON><PERSON></a>, founder of the state of Milan (b. 1302)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1358", "text": "<PERSON>, Duke of Austria (b. 1298)", "html": "1358 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1298)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Austria\" title=\"<PERSON>, Duke of Austria\"><PERSON>, Duke of Austria</a> (b. 1298)", "links": [{"title": "<PERSON>, Duke of Austria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Austria"}]}, {"year": "1419", "text": "<PERSON><PERSON><PERSON> of Bohemia (b. 1361)", "html": "1419 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> IV of Bohemia\"><PERSON><PERSON><PERSON> IV of Bohemia</a> (b. 1361)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_IV_of_Bohemia\" title=\"<PERSON><PERSON><PERSON> IV of Bohemia\"><PERSON><PERSON><PERSON> IV of Bohemia</a> (b. 1361)", "links": [{"title": "<PERSON><PERSON><PERSON> IV of Bohemia", "link": "https://wikipedia.org/wiki/Wen<PERSON>laus_IV_of_Bohemia"}]}, {"year": "1443", "text": "<PERSON><PERSON><PERSON>, Japanese shōgun (b. 1434)", "html": "1443 - <a href=\"https://wikipedia.org/wiki/Ash<PERSON><PERSON>_Yoshi<PERSON>su\" title=\"Ashikaga Yoshikatsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1434)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>su\" title=\"Ashika<PERSON> Yoshikatsu\"><PERSON><PERSON><PERSON></a>, Japanese shōgun (b. 1434)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yo<PERSON>su"}]}, {"year": "1492", "text": "<PERSON> Silva, Dominican nun", "html": "1492 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Silva\"><PERSON></a>, Dominican nun", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Silva\"><PERSON> Silva</a>, Dominican nun", "links": [{"title": "<PERSON> of Silva", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1518", "text": "<PERSON><PERSON><PERSON>, French composer (b. 1445)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/Loyset_Comp%C3%A8re\" title=\"Loyset Compère\"><PERSON><PERSON><PERSON></a>, French composer (b. 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Loyset_Comp%C3%A8re\" title=\"Loyset Compère\"><PERSON><PERSON><PERSON>mpère</a>, French composer (b. 1445)", "links": [{"title": "<PERSON><PERSON>et Compère", "link": "https://wikipedia.org/wiki/Loyset_Comp%C3%A8re"}]}, {"year": "1532", "text": "<PERSON>, Elector of Saxony (b. 1468)", "html": "1532 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1468)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony\" title=\"<PERSON>, Elector of Saxony\"><PERSON>, Elector of Saxony</a> (b. 1468)", "links": [{"title": "<PERSON>, Elector of Saxony", "link": "https://wikipedia.org/wiki/<PERSON>,_Elector_of_Saxony"}]}, {"year": "1661", "text": "<PERSON>, English historian and author (b. 1608)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian and author (b. 1608)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, English poet and author (b. 1621)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1621)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1621)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1705", "text": "<PERSON>, Swiss mathematician and theorist (b. 1654)", "html": "1705 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and theorist (b. 1654)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss mathematician and theorist (b. 1654)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1733", "text": "<PERSON>, English philosopher and author (b. 1657)", "html": "1733 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1657)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and author (b. 1657)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1791", "text": "<PERSON><PERSON><PERSON>, marquis <PERSON>, French soldier and diplomat (b. 1719)", "html": "1791 - <a href=\"https://wikipedia.org/wiki/<PERSON>-<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_<PERSON>_<PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, marquis de <PERSON>\"><PERSON><PERSON><PERSON>, marquis de <PERSON></a>, French soldier and diplomat (b. 1719)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_de_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, marquis de <PERSON>\"><PERSON><PERSON><PERSON>, marquis de <PERSON></a>, French soldier and diplomat (b. 1719)", "links": [{"title": "<PERSON><PERSON><PERSON>, marquis de <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON>%C3%A<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>,_marquis_<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1836", "text": "<PERSON><PERSON><PERSON>, French mathematician and theorist (b. 1755)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French mathematician and theorist (b. 1755)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1855", "text": "<PERSON>, English publisher (b. 1785)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1785)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English publisher (b. 1785)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1861", "text": "<PERSON><PERSON><PERSON> <PERSON>, Queen consort of Kingdom of Madagascar and then sovereign (b. 1778)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/Ranavalona_I\" title=\"Ranavalona I\">Ranavalona I</a>, Queen consort of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Madagascar\" class=\"mw-redirect\" title=\"Kingdom of Madagascar\">Kingdom of Madagascar</a> and then sovereign (b. 1778)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ranavalona_I\" title=\"Ranavalona I\">Ranavalona I</a>, Queen consort of <a href=\"https://wikipedia.org/wiki/Kingdom_of_Madagascar\" class=\"mw-redirect\" title=\"Kingdom of Madagascar\">Kingdom of Madagascar</a> and then sovereign (b. 1778)", "links": [{"title": "Ranavalona I", "link": "https://wikipedia.org/wiki/Ranavalona_I"}, {"title": "Kingdom of Madagascar", "link": "https://wikipedia.org/wiki/Kingdom_of_Madagascar"}]}, {"year": "1878", "text": "<PERSON>, English-American architect (b. 1802)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect (b. 1802)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American architect (b. 1802)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1886", "text": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, Indian mystic and philosopher (b. 1836)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\">Sri <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian mystic and philosopher (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\">Sri <PERSON><PERSON><PERSON><PERSON><PERSON></a>, Indian mystic and philosopher (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English civil engineer (b. 1837)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil engineer (b. 1837)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English civil engineer (b. 1837)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON>, American pharmacist and chemist, invented Coca-Cola (b. 1831)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pharmacist and chemist, invented <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a> (b. 1831)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American pharmacist and chemist, invented <a href=\"https://wikipedia.org/wiki/Coca-Cola\" title=\"Coca-Cola\">Coca-Cola</a> (b. 1831)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Coca-Cola", "link": "https://wikipedia.org/wiki/Coca-Cola"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON>, French neurologist and academic (b. 1825)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French neurologist and academic (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French neurologist and academic (b. 1825)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1899", "text": "<PERSON>, German chemist and academic (b. 1811)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1811)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and academic (b. 1811)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Portuguese journalist and author (b. 1845)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese journalist and author (b. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Portuguese journalist and author (b. 1845)", "links": [{"title": "<PERSON> Que<PERSON>ós", "link": "https://wikipedia.org/wiki/Jos%C3%A9_<PERSON>_de_E%C3%A7a_de_Queir%C3%B3s"}]}, {"year": "1904", "text": "<PERSON><PERSON><PERSON>, American soldier and author (b. 1843)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/Prentiss_Ingraham\" title=\"Prentiss Ingraham\"><PERSON><PERSON><PERSON></a>, American soldier and author (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prentiss_Ingraham\" title=\"Prentiss Ingraham\"><PERSON><PERSON><PERSON></a>, American soldier and author (b. 1843)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prentiss_<PERSON>graham"}]}, {"year": "1911", "text": "<PERSON>, Irish-Australian cardinal (b. 1830)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian cardinal (b. 1830)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian cardinal (b. 1830)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, German-Norwegian gardener (b. 1835)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Norwegian gardener (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Norwegian gardener (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, English footballer (b. 1885)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1885)\" title=\"<PERSON> (footballer, born 1885)\"><PERSON></a>, English footballer (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1885)\" title=\"<PERSON> (footballer, born 1885)\"><PERSON></a>, English footballer (b. 1885)", "links": [{"title": "<PERSON> (footballer, born 1885)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer,_born_1885)"}]}, {"year": "1920", "text": "<PERSON>, Australian politician, Premier of Western Australia (b. 1866)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, <a href=\"https://wikipedia.org/wiki/Premier_of_Western_Australia\" title=\"Premier of Western Australia\">Premier of Western Australia</a> (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Western Australia", "link": "https://wikipedia.org/wiki/Premier_of_Western_Australia"}]}, {"year": "1921", "text": "<PERSON> of Serbia (b. 1844)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (b. 1844)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Serbia\" title=\"<PERSON> of Serbia\"><PERSON> of Serbia</a> (b. 1844)", "links": [{"title": "<PERSON> of Serbia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Serbia"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Slovak priest, journalist, and politician (b. 1864)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest, journalist, and politician (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Slovak priest, journalist, and politician (b. 1864)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American singer-songwriter and guitarist (b. 1911)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(guitarist)\" class=\"mw-redirect\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(guitarist)\" class=\"mw-redirect\" title=\"<PERSON> (guitarist)\"><PERSON></a>, American singer-songwriter and guitarist (b. 1911)", "links": [{"title": "<PERSON> (guitarist)", "link": "https://wikipedia.org/wiki/<PERSON>_(guitarist)"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese admiral (b. 1891)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese admiral (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Takijir%C5%8D_%C5%8Cnishi"}]}, {"year": "1948", "text": "<PERSON>, American baseball player and coach (b. 1895)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"Babe Ruth\"><PERSON></a>, American baseball player and coach (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Babe_Ruth\" title=\"Babe Ruth\"><PERSON></a>, American baseball player and coach (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American journalist and author (b. 1900)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American painter and academic (b. 1866)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>met\"><PERSON></a>, American painter and academic (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Lydia_Field_Emmet"}]}, {"year": "1956", "text": "<PERSON><PERSON>, Hungarian-American actor (b. 1882)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lugos<PERSON>\"><PERSON><PERSON></a>, Hungarian-American actor (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Lugosi\"><PERSON><PERSON></a>, Hungarian-American actor (b. 1882)", "links": [{"title": "Bela Lugosi", "link": "https://wikipedia.org/wiki/Bela_Lugosi"}]}, {"year": "1957", "text": "<PERSON>, American chemist and physicist, Nobel Prize laureate (b. 1881)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American chemist and physicist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1958", "text": "<PERSON>, Soviet Consul General in New York City,  journalist and economist (b. 1904)", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Consul General in New York City, journalist and economist (b. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Soviet Consul General in New York City, journalist and economist (b. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Jr., American admiral (b. 1882)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American admiral (b. 1882)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>,_Jr."}]}, {"year": "1959", "text": "<PERSON>, Polish-French harpsichord player (b. 1879)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-French <a href=\"https://wikipedia.org/wiki/Harpsichord\" title=\"Harpsichord\">harpsichord</a> player (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Harpsichord", "link": "https://wikipedia.org/wiki/Harpsichord"}]}, {"year": "1961", "text": "<PERSON>, Pakistani linguist and scholar (b. 1870)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Urdu_scholar)\" title=\"<PERSON> (Urdu scholar)\"><PERSON></a>, Pakistani linguist and scholar (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Urdu_scholar)\" title=\"<PERSON> (Urdu scholar)\"><PERSON></a>, Pakistani linguist and scholar (b. 1870)", "links": [{"title": "<PERSON> (Urdu scholar)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_(Urdu_scholar)"}]}, {"year": "1963", "text": "<PERSON>, British artist (b. 1921)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British artist (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Greek-American businessman (b. 1893)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American businessman (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek-American businessman (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>s"}]}, {"year": "1972", "text": "<PERSON>, French actor and screenwriter (b. 1905)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor and screenwriter (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Ukrainian-American biochemist and microbiologist, Nobel Prize laureate (b. 1888)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American biochemist and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ukrainian-American biochemist and microbiologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1888)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1977", "text": "<PERSON>, American singer and actor  (b. 1935)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\">Elvis <PERSON></a>, American singer and actor (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elvis_Presley\" title=\"Elvis Presley\"><PERSON></a>, American singer and actor (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Dutch soldier and politician, Governor-General of the Dutch East Indies (b. 1888)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Starkenborgh_Stachouwer\" title=\"<PERSON><PERSON> <PERSON><PERSON> van Starkenborgh Stachouwer\"><PERSON><PERSON>h Stachouwer</a>, Dutch soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_Starkenborgh_Stachouwer\" title=\"<PERSON><PERSON> <PERSON><PERSON> van Starkenborgh Stachouwer\"><PERSON><PERSON> Stark<PERSON>borgh Stachouwer</a>, Dutch soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies\" title=\"Governor-General of the Dutch East Indies\">Governor-General of the Dutch East Indies</a> (b. 1888)", "links": [{"title": "<PERSON><PERSON>h Stachouwer", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Stachouwer"}, {"title": "Governor-General of the Dutch East Indies", "link": "https://wikipedia.org/wiki/Governor-General_of_the_Dutch_East_Indies"}]}, {"year": "1979", "text": "<PERSON>, Canadian lawyer and politician, 13th Prime Minister of Canada (b. 1895)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 13th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a> (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1983", "text": "<PERSON>, American baseball player (b. 1902)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Serbian children's writer, poet, journalist, aphorist and TV editor (b. 1922)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Du%C5%A1<PERSON>_Radovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian children's writer, poet, journalist, aphorist and TV editor (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Du%C5%<PERSON><PERSON>_Radovi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian children's writer, poet, journalist, aphorist and TV editor (b. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Du%C5%A1<PERSON>_Radovi%C4%87"}]}, {"year": "1986", "text": "<PERSON>, English cricketer and administrator (b. 1902)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and administrator (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and administrator (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Bolivian author and poet (b. 1921)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1enz\" title=\"<PERSON>\"><PERSON></a>, Bolivian author and poet (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1enz\" title=\"<PERSON>\"><PERSON></a>, Bolivian author and poet (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jaime_S%C3%A1enz"}]}, {"year": "1989", "text": "<PERSON>, American actress (b. 1929)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, New Zealand wrestler and trainer (b. 1925)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, New Zealand wrestler and trainer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, New Zealand wrestler and trainer (b. 1925)", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27<PERSON><PERSON><PERSON>_(wrestler)"}]}, {"year": "1991", "text": "<PERSON>, Italian director and screenwriter (b. 1905)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian director and screenwriter (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter, guitarist, and producer (b. 1951)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and producer (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, English-American actor (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON>, Pakistani musician and Qawwali singer (b. 1948)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani musician and <a href=\"https://wikipedia.org/wiki/Qawwali\" title=\"Qawwal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> singer (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Pakistani musician and <a href=\"https://wikipedia.org/wiki/Qawwali\" title=\"Qawwal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> singer (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Qawwali"}]}, {"year": "1997", "text": "<PERSON>, Bangladeshi Islamic scholar and teacher (b. 1914)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>_<PERSON>\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Bangladeshi Islamic scholar and teacher (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sultan_<PERSON>_<PERSON>\" title=\"Sultan <PERSON>\">Sultan <PERSON></a>, Bangladeshi Islamic scholar and teacher (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American actor (b. 1916)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Phil_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Phil_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Leeds"}]}, {"year": "1998", "text": "<PERSON>, American journalist and author (b. 1907)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> West\"><PERSON></a>, American journalist and author (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dorothy_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, Palestinian terrorist leader (b. 1937)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Nidal\"><PERSON></a>, Palestinian terrorist leader (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Abu Nidal\"><PERSON></a>, Palestinian terrorist leader (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1914)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American baseball player and coach (b. 1933)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON>, Ugandan field marshal and politician, 3rd President of Uganda (b. 1928)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan field marshal and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Uganda\" title=\"President of Uganda\">President of Uganda</a> (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Ugandan field marshal and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Uganda\" title=\"President of Uganda\">President of Uganda</a> (b. 1928)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Idi_<PERSON>in"}, {"title": "President of Uganda", "link": "https://wikipedia.org/wiki/President_of_Uganda"}]}, {"year": "2004", "text": "<PERSON>, Czech ice hockey player and coach (b. 1950)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech ice hockey player and coach (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Sri Lankan journalist and poet (b. 1957)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON> I<PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan journalist and poet (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, Sri Lankan journalist and poet (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American photographer and journalist (b. 1907)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and journalist (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, American boxer (b. 1969)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (b. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, American fiddler (b. 1928)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Clements\"><PERSON><PERSON><PERSON></a>, American fiddler (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American fiddler (b. 1928)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Italian cinematographer (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cinematographer (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian cinematographer (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, English novelist and playwright (b. 1938)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and playwright (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Swiss monk and mystic (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/Fr%C3%A8re_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss monk and mystic (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fr%C3%A8re_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swiss monk and mystic (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fr%C3%A8re_Roger"}]}, {"year": "2006", "text": "<PERSON>, Paraguayan general and dictator; 46th President of Paraguay (b. 1912)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan general and dictator; 46th <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (b. 1912)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Paraguayan general and dictator; 46th <a href=\"https://wikipedia.org/wiki/President_of_Paraguay\" title=\"President of Paraguay\">President of Paraguay</a> (b. 1912)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Paraguay", "link": "https://wikipedia.org/wiki/President_of_Paraguay"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON><PERSON>, Iranian engineer and politician (b. 1945)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Bahaed<PERSON>_<PERSON>\" title=\"Bahaedin Ada<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian engineer and politician (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bahaed<PERSON>_<PERSON>\" title=\"Bahaedin Adab\"><PERSON><PERSON><PERSON><PERSON></a>, Iranian engineer and politician (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Adab"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Brazilian singer-songwriter and actor (b. 1914)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian singer-songwriter and actor (b. 1914)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2008", "text": "<PERSON>, Irish musician, folk singer and actor (b. 1934)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish musician, folk singer and actor (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish musician, folk singer and actor (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese farmer and author (b. 1913)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese farmer and author (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese farmer and author (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON><PERSON>, Greek general (b. 1923)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek general (b. 1923)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Turkish activist and politician (b. 1916)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish activist and politician (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish activist and politician (b. 1916)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "2012", "text": "Princess <PERSON><PERSON> of Morocco (b. 1954)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Morocco\" title=\"Princess <PERSON><PERSON> of Morocco\">Princess <PERSON><PERSON> of Morocco</a> (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON>_of_Morocco\" title=\"Princess <PERSON><PERSON> of Morocco\">Princess <PERSON><PERSON> of Morocco</a> (b. 1954)", "links": [{"title": "Princess <PERSON><PERSON> of Morocco", "link": "https://wikipedia.org/wiki/Princess_<PERSON><PERSON>_<PERSON><PERSON>_of_Morocco"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Belgian photographer and director (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian photographer and director (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian photographer and director (b. 1938)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Ethiopian patriarch (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Abune_Paulos\" title=\"Abune <PERSON>\"><PERSON><PERSON></a>, Ethiopian patriarch (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Abune_Paulos\" title=\"Abu<PERSON>\"><PERSON><PERSON></a>, Ethiopian patriarch (b. 1935)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Abune_Paulos"}]}, {"year": "2012", "text": "<PERSON>, American actor (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1923)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "2013", "text": "<PERSON>, Welsh mathematician and academic (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Welsh mathematician and academic (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(mathematician)\" title=\"<PERSON> (mathematician)\"><PERSON></a>, Welsh mathematician and academic (b. 1918)", "links": [{"title": "<PERSON> (mathematician)", "link": "https://wikipedia.org/wiki/<PERSON>(mathematician)"}]}, {"year": "2014", "text": "<PERSON>, Nigerian general and politician, Governor of Kebbi State (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kebbi_State\" class=\"mw-redirect\" title=\"List of Governors of Kebbi State\">Governor of Kebbi State</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nigerian general and politician, <a href=\"https://wikipedia.org/wiki/List_of_Governors_of_Kebbi_State\" class=\"mw-redirect\" title=\"List of Governors of Kebbi State\">Governor of Kebbi State</a> (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Governors of Kebbi State", "link": "https://wikipedia.org/wiki/List_of_Governors_of_Kebbi_State"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Ukrainian author (b. 1930)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Vsevolod_Nestayko\" title=\"Vsevolod Nestayko\">V<PERSON><PERSON><PERSON><PERSON></a>, Ukrainian author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vsevolod_Nestayko\" title=\"Vsevolod Nestayko\">V<PERSON><PERSON><PERSON><PERSON></a>, Ukrainian author (b. 1930)", "links": [{"title": "Vsevolod Nestayko", "link": "https://wikipedia.org/wiki/Vsevolod_Nestayko"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian-South African lawyer and politician (b. 1960)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-South African lawyer and politician (b. 1960)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-South African lawyer and politician (b. 1960)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, German journalist, author, and academic (b. 1924)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, author, and academic (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist, author, and academic (b. 1924)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Mexican-American physicist, astronomer, and academic (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American physicist, astronomer, and academic (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican-American physicist, astronomer, and academic (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, British actress (b. 1934)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actress (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON><PERSON>, Pakistani colonel and politician (b. 1943)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani colonel and politician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani colonel and politician (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Serb general (b. 1947)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Mile_Mrk%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serb general (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mile_Mrk%C5%A1i%C4%87\" title=\"<PERSON>\"><PERSON></a>, Serb general (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mile_Mrk%C5%A1i%C4%87"}]}, {"year": "2016", "text": "<PERSON>, Brazilian water polo player, lawyer, and businessman (b. 1916)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Havelange\" title=\"<PERSON>\"><PERSON></a>, Brazilian water polo player, lawyer, and businessman (b. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jo%C3%A3o_Havelange\" title=\"<PERSON>\"><PERSON></a>, Brazilian water polo player, lawyer, and businessman (b. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jo%C3%A3o_Havelange"}]}, {"year": "2016", "text": "<PERSON>, American television personality (b. 1927)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, American television personality (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(host)\" title=\"<PERSON> (host)\"><PERSON></a>, American television personality (b. 1927)", "links": [{"title": "<PERSON> (host)", "link": "https://wikipedia.org/wiki/<PERSON>_(host)"}]}, {"year": "2018", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1942)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American singer-songwriter (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON><PERSON> <PERSON>, Indian prime minister (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/Atal_Bihari_Vajpayee\" title=\"Atal Bihari Vajpayee\">Atal Bihar<PERSON></a>, Indian prime minister (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Atal_Bihari_Vajpayee\" title=\"Atal Bihari Vajpayee\">Atal Bihar<PERSON></a>, Indian prime minister (b. 1924)", "links": [{"title": "Atal Bihari Vajpayee", "link": "https://wikipedia.org/wiki/Atal_Bihari_Vajpayee"}]}, {"year": "2018", "text": "<PERSON><PERSON><PERSON>, American-Japanese writer (b. 1924)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese writer (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American-Japanese writer (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, American actor, director, and screenwriter. (b. 1940)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter. (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and screenwriter. (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON>, Canadian-British animator (b. 1933)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, Canadian-British animator (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)\" title=\"<PERSON> (animator)\"><PERSON></a>, Canadian-British animator (b. 1933)", "links": [{"title": "<PERSON> (animator)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(animator)"}]}, {"year": "2021", "text": "<PERSON>, English comedian and actor (b. 1963)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian and actor (b. 1963)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2023", "text": "<PERSON>, American sociologist (b. 1928)", "html": "2023 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sociologist (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}]}}