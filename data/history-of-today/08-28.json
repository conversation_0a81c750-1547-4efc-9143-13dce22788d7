{"date": "August 28", "url": "https://wikipedia.org/wiki/August_28", "data": {"Events": [{"year": "475", "text": "The Roman general <PERSON><PERSON><PERSON> forces western Roman Emperor <PERSON> to flee his capital city, Ravenna.", "html": "475 - The <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_R<PERSON><PERSON>_Augustulus)\" title=\"<PERSON><PERSON><PERSON> (father of <PERSON><PERSON><PERSON> Augustulus)\"><PERSON><PERSON><PERSON></a> forces western <a href=\"https://wikipedia.org/wiki/Roman_Emperors\" class=\"mw-redirect\" title=\"Roman Emperors\">Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to flee his <a href=\"https://wikipedia.org/wiki/Capital_city\" title=\"Capital city\">capital city</a>, <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Roman_Empire\" title=\"Roman Empire\">Roman</a> general <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_Romulus_Augustulus)\" title=\"<PERSON><PERSON><PERSON> (father of Romulus Augustulus)\"><PERSON><PERSON><PERSON></a> forces western <a href=\"https://wikipedia.org/wiki/Roman_Emperors\" class=\"mw-redirect\" title=\"Roman Emperors\">Roman Emperor</a> <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> to flee his <a href=\"https://wikipedia.org/wiki/Capital_city\" title=\"Capital city\">capital city</a>, <a href=\"https://wikipedia.org/wiki/Ravenna\" title=\"Ravenna\">Ravenna</a>.", "links": [{"title": "Roman Empire", "link": "https://wikipedia.org/wiki/Roman_Empire"}, {"title": "<PERSON><PERSON><PERSON> (father of <PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_R<PERSON><PERSON>_August<PERSON>)"}, {"title": "Roman Emperors", "link": "https://wikipedia.org/wiki/Roman_Emperors"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Julius_Nepos"}, {"title": "Capital city", "link": "https://wikipedia.org/wiki/Capital_city"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ravenna"}]}, {"year": "489", "text": "<PERSON><PERSON>, king of the Ostrogoths, defeats <PERSON><PERSON><PERSON><PERSON> at the Battle of Isonzo, forcing his way into Italy.", "html": "489 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, defeats <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\"><PERSON><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Isonzo_(489)\" title=\"Battle of Isonzo (489)\">Battle of Isonzo</a>, forcing his way into Italy.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great\" title=\"<PERSON><PERSON> the Great\"><PERSON><PERSON></a>, king of the <a href=\"https://wikipedia.org/wiki/Ostrogoths\" title=\"Ostrogoths\">Ostrogoths</a>, defeats <a href=\"https://wikipedia.org/wiki/Odoacer\" title=\"Odoacer\"><PERSON><PERSON><PERSON><PERSON></a> at the <a href=\"https://wikipedia.org/wiki/Battle_of_Isonzo_(489)\" title=\"Battle of Isonzo (489)\">Battle of Isonzo</a>, forcing his way into Italy.", "links": [{"title": "<PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_the_Great"}, {"title": "Ostrogoths", "link": "https://wikipedia.org/wiki/Ostrogoths"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Odoacer"}, {"title": "Battle of Isonzo (489)", "link": "https://wikipedia.org/wiki/Battle_of_Isonzo_(489)"}]}, {"year": "632", "text": "<PERSON><PERSON><PERSON>, daughter of the Islamic prophet <PERSON>, dies, with her cause of death being a controversial topic among the Sunni Muslims and Shia Muslims.", "html": "632 - <a href=\"https://wikipedia.org/wiki/Fatimah\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the Islamic prophet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Muhammad\"><PERSON></a>, dies, with her cause of death being a controversial topic among the <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> Muslims and <a href=\"https://wikipedia.org/wiki/Shia\" class=\"mw-redirect\" title=\"Shia\">Shia</a> Muslims.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>h\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of the Islamic prophet <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"Muhammad\"><PERSON></a>, dies, with her cause of death being a controversial topic among the <a href=\"https://wikipedia.org/wiki/Sunni\" class=\"mw-redirect\" title=\"Sunni\">Sunni</a> Muslims and <a href=\"https://wikipedia.org/wiki/Shia\" class=\"mw-redirect\" title=\"Shia\">Shia</a> Muslims.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatimah"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Sunni", "link": "https://wikipedia.org/wiki/Sunni"}, {"title": "Shia", "link": "https://wikipedia.org/wiki/Shia"}]}, {"year": "663", "text": "Silla-Tang armies crush the Baekje restoration attempt and force Yamato Japan to withdraw from Korea in the Battle of Baekgang.", "html": "663 - <a href=\"https://wikipedia.org/wiki/Silla\" title=\"Silla\"><PERSON>lla</a>-<a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang</a> armies crush the <a href=\"https://wikipedia.org/wiki/Baekje\" title=\"Baekje\">Baekje</a> restoration attempt and force <a href=\"https://wikipedia.org/wiki/Yamato_period\" title=\"Yamato period\">Yamato Japan</a> to withdraw from Korea in the <a href=\"https://wikipedia.org/wiki/Battle_of_Baekgang\" title=\"Battle of Baekgang\">Battle of Baekgang</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Silla\" title=\"Silla\"><PERSON>lla</a>-<a href=\"https://wikipedia.org/wiki/Tang_dynasty\" title=\"Tang dynasty\">Tang</a> armies crush the <a href=\"https://wikipedia.org/wiki/Baekje\" title=\"Baekje\">Baekje</a> restoration attempt and force <a href=\"https://wikipedia.org/wiki/Yamato_period\" title=\"Yamato period\">Yamato Japan</a> to withdraw from Korea in the <a href=\"https://wikipedia.org/wiki/Battle_of_Baekgang\" title=\"Battle of Baekgang\">Battle of Baekgang</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silla"}, {"title": "Tang dynasty", "link": "https://wikipedia.org/wiki/Tang_dynasty"}, {"title": "<PERSON>ek<PERSON>", "link": "https://wikipedia.org/wiki/Baekje"}, {"title": "Yamato period", "link": "https://wikipedia.org/wiki/Yamato_period"}, {"title": "Battle of Baekgang", "link": "https://wikipedia.org/wiki/Battle_of_Baekgang"}]}, {"year": "1189", "text": "Third Crusade: The Crusaders begin the Siege of Acre under <PERSON> of Lusignan.", "html": "1189 - <a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: The Crusaders begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Acre_(1189%E2%80%9391)\" class=\"mw-redirect\" title=\"Siege of Acre (1189-91)\">Siege of Acre</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lusignan\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Third_Crusade\" title=\"Third Crusade\">Third Crusade</a>: The Crusaders begin the <a href=\"https://wikipedia.org/wiki/Siege_of_Acre_(1189%E2%80%9391)\" class=\"mw-redirect\" title=\"Siege of Acre (1189-91)\">Siege of Acre</a> under <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Lusignan\" title=\"<PERSON> of Lusignan\"><PERSON> of Lusignan</a>.", "links": [{"title": "Third Crusade", "link": "https://wikipedia.org/wiki/Third_Crusade"}, {"title": "Siege of Acre (1189-91)", "link": "https://wikipedia.org/wiki/Siege_of_Acre_(1189%E2%80%9391)"}, {"title": "<PERSON> of Lusignan", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1521", "text": "Ottoman wars in Europe: The Ottoman Turks occupy Belgrade.", "html": "1521 - <a href=\"https://wikipedia.org/wiki/Ottoman_wars_in_Europe\" title=\"Ottoman wars in Europe\">Ottoman wars in Europe</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> occupy <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman_wars_in_Europe\" title=\"Ottoman wars in Europe\">Ottoman wars in Europe</a>: The <a href=\"https://wikipedia.org/wiki/Ottoman_Turks\" title=\"Ottoman Turks\">Ottoman Turks</a> occupy <a href=\"https://wikipedia.org/wiki/Belgrade\" title=\"Belgrade\">Belgrade</a>.", "links": [{"title": "Ottoman wars in Europe", "link": "https://wikipedia.org/wiki/Ottoman_wars_in_Europe"}, {"title": "Ottoman Turks", "link": "https://wikipedia.org/wiki/Ottoman_Turks"}, {"title": "Belgrade", "link": "https://wikipedia.org/wiki/Belgrade"}]}, {"year": "1524", "text": "The Kaqchikel Maya rebel against their former Spanish allies during the Spanish conquest of Guatemala.", "html": "1524 - The <a href=\"https://wikipedia.org/wiki/Kaqchikel_people\" title=\"Kaqchikel people\">Kaqchikel</a> <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> rebel against their former Spanish allies during the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala\" title=\"Spanish conquest of Guatemala\">Spanish conquest of Guatemala</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kaqchikel_people\" title=\"Kaqchikel people\">Kaqchikel</a> <a href=\"https://wikipedia.org/wiki/Maya_civilization\" title=\"Maya civilization\">Maya</a> rebel against their former Spanish allies during the <a href=\"https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala\" title=\"Spanish conquest of Guatemala\">Spanish conquest of Guatemala</a>.", "links": [{"title": "Kaqchikel people", "link": "https://wikipedia.org/wiki/Kaqchikel_people"}, {"title": "Maya civilization", "link": "https://wikipedia.org/wiki/Maya_civilization"}, {"title": "Spanish conquest of Guatemala", "link": "https://wikipedia.org/wiki/Spanish_conquest_of_Guatemala"}]}, {"year": "1542", "text": "Turkish-Portuguese War: Battle of Wofla: The Portuguese are scattered, their leader <PERSON><PERSON><PERSON> is captured and later executed.", "html": "1542 - <a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Portuguese_conflicts_(1538%E2%80%9359)\" class=\"mw-redirect\" title=\"Ottoman-Portuguese conflicts (1538-59)\">Turkish-Portuguese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wofla\" title=\"Battle of Wofla\">Battle of Wofla</a>: The Portuguese are scattered, their leader <a href=\"https://wikipedia.org/wiki/Christov%C3%A3o_da_Gama\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> da Gama\"><PERSON><PERSON><PERSON> Gama</a> is captured and later executed.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ottoman%E2%80%93Portuguese_conflicts_(1538%E2%80%9359)\" class=\"mw-redirect\" title=\"Ottoman-Portuguese conflicts (1538-59)\">Turkish-Portuguese War</a>: <a href=\"https://wikipedia.org/wiki/Battle_of_Wofla\" title=\"Battle of Wofla\">Battle of Wofla</a>: The Portuguese are scattered, their leader <a href=\"https://wikipedia.org/wiki/Christov%C3%A3o_da_Gama\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> da Gama\"><PERSON><PERSON><PERSON> Gama</a> is captured and later executed.", "links": [{"title": "Ottoman-Portuguese conflicts (1538-59)", "link": "https://wikipedia.org/wiki/Ottoman%E2%80%93Portuguese_conflicts_(1538%E2%80%9359)"}, {"title": "Battle of Wofla", "link": "https://wikipedia.org/wiki/Battle_of_Wofla"}, {"title": "<PERSON><PERSON><PERSON> da Gama", "link": "https://wikipedia.org/wiki/Christov%C3%A3o_da_<PERSON>ama"}]}, {"year": "1565", "text": "<PERSON> Avilés sights land near St. Augustine, Florida and founds the oldest continuously occupied European-established city in the continental United States.", "html": "1565 - <a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a> sights land near <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> and founds the oldest continuously occupied European-established city in the <a href=\"https://wikipedia.org/wiki/Contiguous_United_States\" title=\"Contiguous United States\">continental United States</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s\" title=\"<PERSON>\"><PERSON></a> sights land near <a href=\"https://wikipedia.org/wiki/St._Augustine,_Florida\" title=\"St. Augustine, Florida\">St. Augustine, Florida</a> and founds the oldest continuously occupied European-established city in the <a href=\"https://wikipedia.org/wiki/Contiguous_United_States\" title=\"Contiguous United States\">continental United States</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_Men%C3%A9ndez_de_Avil%C3%A9s"}, {"title": "St. Augustine, Florida", "link": "https://wikipedia.org/wiki/St._Augustine,_Florida"}, {"title": "Contiguous United States", "link": "https://wikipedia.org/wiki/Contiguous_United_States"}]}, {"year": "1609", "text": "<PERSON> discovers Delaware Bay.", "html": "1609 - <a href=\"https://wikipedia.org/wiki/Henry_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Delaware_Bay\" title=\"Delaware Bay\">Delaware Bay</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_<PERSON>\" title=\"Henry Hudson\"><PERSON></a> discovers <a href=\"https://wikipedia.org/wiki/Delaware_Bay\" title=\"Delaware Bay\">Delaware Bay</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Delaware Bay", "link": "https://wikipedia.org/wiki/Delaware_Bay"}]}, {"year": "1619", "text": "Election of <PERSON>, Holy Roman Emperor.", "html": "1619 - Election of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>.", "no_year_html": "Election of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor\" title=\"<PERSON> II, Holy Roman Emperor\"><PERSON>, Holy Roman Emperor</a>.", "links": [{"title": "<PERSON>, Holy Roman Emperor", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Holy_Roman_Emperor"}]}, {"year": "1640", "text": "Second Bishop's War: King <PERSON>'s English army loses to a Scottish Covenanter force at the Battle of Newburn.", "html": "1640 - <a href=\"https://wikipedia.org/wiki/Bishops%27_Wars#Second_Bishops'_War_(1640)\" title=\"Bishops' Wars\">Second Bishop's War</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> I</a>'s English army loses to a <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Scottish Covenanter</a> force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Newburn\" title=\"Battle of Newburn\">Battle of Newburn</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bishops%27_Wars#Second_Bishops'_War_(1640)\" title=\"Bishops' Wars\">Second Bishop's War</a>: King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"Charles I of England\"><PERSON> I</a>'s English army loses to a <a href=\"https://wikipedia.org/wiki/Covenanter\" class=\"mw-redirect\" title=\"Covenanter\">Scottish Covenanter</a> force at the <a href=\"https://wikipedia.org/wiki/Battle_of_Newburn\" title=\"Battle of Newburn\">Battle of Newburn</a>.", "links": [{"title": "Bishops' Wars", "link": "https://wikipedia.org/wiki/Bishops%27_Wars#Second_Bishops'_War_(1640)"}, {"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Covenanter", "link": "https://wikipedia.org/wiki/Covenanter"}, {"title": "Battle of Newburn", "link": "https://wikipedia.org/wiki/Battle_of_Newburn"}]}, {"year": "1648", "text": "Second English Civil War: The Siege of Colchester ends when Royalists Forces surrender to the Parliamentary Forces after eleven weeks.", "html": "1648 - <a href=\"https://wikipedia.org/wiki/Second_English_Civil_War\" title=\"Second English Civil War\">Second English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Colchester\" title=\"Siege of Colchester\">Siege of Colchester</a> ends when Royalists Forces surrender to the Parliamentary Forces after eleven weeks.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_English_Civil_War\" title=\"Second English Civil War\">Second English Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Colchester\" title=\"Siege of Colchester\">Siege of Colchester</a> ends when Royalists Forces surrender to the Parliamentary Forces after eleven weeks.", "links": [{"title": "Second English Civil War", "link": "https://wikipedia.org/wiki/Second_English_Civil_War"}, {"title": "Siege of Colchester", "link": "https://wikipedia.org/wiki/Siege_of_Colchester"}]}, {"year": "1709", "text": "<PERSON><PERSON><PERSON> is crowned King of Manipur.", "html": "1709 - <a href=\"https://wikipedia.org/wiki/Pamheiba\" class=\"mw-redirect\" title=\"Pamhe<PERSON>\"><PERSON><PERSON><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Manipur\" title=\"Manipur\">Manipur</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pamheiba\" class=\"mw-redirect\" title=\"Pam<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is crowned King of <a href=\"https://wikipedia.org/wiki/Manipur\" title=\"Manipur\">Manipur</a>.", "links": [{"title": "Pamheiba", "link": "https://wikipedia.org/wiki/Pamheiba"}, {"title": "Manipur", "link": "https://wikipedia.org/wiki/Manipur"}]}, {"year": "1789", "text": "<PERSON> discovers a new moon of Saturn: Enceladus.", "html": "1789 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers a new <a href=\"https://wikipedia.org/wiki/Moons_of_Saturn\" title=\"Moons of Saturn\">moon of Saturn</a>: <a href=\"https://wikipedia.org/wiki/Enceladus\" title=\"Enceladus\">Enceladus</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> discovers a new <a href=\"https://wikipedia.org/wiki/Moons_of_Saturn\" title=\"Moons of Saturn\">moon of Saturn</a>: <a href=\"https://wikipedia.org/wiki/Enceladus\" title=\"Enceladus\">Enceladus</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Moons of Saturn", "link": "https://wikipedia.org/wiki/Moons_of_Saturn"}, {"title": "Encelad<PERSON>", "link": "https://wikipedia.org/wiki/Enceladus"}]}, {"year": "1810", "text": "Napoleonic Wars: The French Navy accepts the surrender of a British Royal Navy fleet at the Battle of Grand Port.", "html": "1810 - <a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> accepts the surrender of a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Port\" title=\"Battle of Grand Port\">Battle of Grand Port</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Napoleonic_Wars\" title=\"Napoleonic Wars\">Napoleonic Wars</a>: The <a href=\"https://wikipedia.org/wiki/French_Navy\" title=\"French Navy\">French Navy</a> accepts the surrender of a British <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Grand_Port\" title=\"Battle of Grand Port\">Battle of Grand Port</a>.", "links": [{"title": "Napoleonic Wars", "link": "https://wikipedia.org/wiki/Napoleonic_Wars"}, {"title": "French Navy", "link": "https://wikipedia.org/wiki/French_Navy"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "Battle of Grand Port", "link": "https://wikipedia.org/wiki/Battle_of_Grand_Port"}]}, {"year": "1830", "text": "The Baltimore and Ohio Railroad's new Tom Thumb steam locomotive races a horse-drawn car, presaging steam's role in U.S. railroads.", "html": "1830 - The <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a>'s new <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Thumb_(locomotive)\" title=\"<PERSON> (locomotive)\"><PERSON></a></i> <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> races a horse-drawn car, presaging steam's role in U.S. railroads.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad\" title=\"Baltimore and Ohio Railroad\">Baltimore and Ohio Railroad</a>'s new <i><a href=\"https://wikipedia.org/wiki/<PERSON>_Thumb_(locomotive)\" title=\"<PERSON> (locomotive)\"><PERSON></a></i> <a href=\"https://wikipedia.org/wiki/Steam_locomotive\" title=\"Steam locomotive\">steam locomotive</a> races a horse-drawn car, presaging steam's role in U.S. railroads.", "links": [{"title": "Baltimore and Ohio Railroad", "link": "https://wikipedia.org/wiki/Baltimore_and_Ohio_Railroad"}, {"title": "<PERSON> (locomotive)", "link": "https://wikipedia.org/wiki/<PERSON>_Thumb_(locomotive)"}, {"title": "Steam locomotive", "link": "https://wikipedia.org/wiki/Steam_locomotive"}]}, {"year": "1833", "text": "The Slavery Abolition Act 1833 receives royal assent, making the purchase or ownership of slaves illegal in the British Empire with exceptions.", "html": "1833 - The <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a> receives <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">royal assent</a>, making the purchase or ownership of slaves illegal in the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> with <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833#Exceptions_and_continuations\" title=\"Slavery Abolition Act 1833\">exceptions</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833\" title=\"Slavery Abolition Act 1833\">Slavery Abolition Act 1833</a> receives <a href=\"https://wikipedia.org/wiki/Royal_assent\" title=\"Royal assent\">royal assent</a>, making the purchase or ownership of slaves illegal in the <a href=\"https://wikipedia.org/wiki/British_Empire\" title=\"British Empire\">British Empire</a> with <a href=\"https://wikipedia.org/wiki/Slavery_Abolition_Act_1833#Exceptions_and_continuations\" title=\"Slavery Abolition Act 1833\">exceptions</a>.", "links": [{"title": "Slavery Abolition Act 1833", "link": "https://wikipedia.org/wiki/Slavery_Abolition_Act_1833"}, {"title": "Royal assent", "link": "https://wikipedia.org/wiki/Royal_assent"}, {"title": "British Empire", "link": "https://wikipedia.org/wiki/British_Empire"}, {"title": "Slavery Abolition Act 1833", "link": "https://wikipedia.org/wiki/Slavery_Abolition_Act_1833#Exceptions_and_continuations"}]}, {"year": "1845", "text": "The first issue of Scientific American magazine is published.", "html": "1845 - The first issue of <i><a href=\"https://wikipedia.org/wiki/Scientific_American\" title=\"Scientific American\">Scientific American</a></i> magazine is published.", "no_year_html": "The first issue of <i><a href=\"https://wikipedia.org/wiki/Scientific_American\" title=\"Scientific American\">Scientific American</a></i> magazine is published.", "links": [{"title": "Scientific American", "link": "https://wikipedia.org/wiki/Scientific_American"}]}, {"year": "1849", "text": "Revolutions of 1848 in the Austrian Empire: After a month-long siege, Venice, which had declared itself independent as the Republic of San Marco, surrenders to Austria.", "html": "1849 - <a href=\"https://wikipedia.org/wiki/Revolutions_of_1848_in_the_Austrian_Empire\" title=\"Revolutions of 1848 in the Austrian Empire\">Revolutions of 1848 in the Austrian Empire</a>: After a month-long siege, <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>, which had declared itself independent as the <a href=\"https://wikipedia.org/wiki/Republic_of_San_Marco\" title=\"Republic of San Marco\">Republic of San Marco</a>, surrenders to Austria.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Revolutions_of_1848_in_the_Austrian_Empire\" title=\"Revolutions of 1848 in the Austrian Empire\">Revolutions of 1848 in the Austrian Empire</a>: After a month-long siege, <a href=\"https://wikipedia.org/wiki/Venice\" title=\"Venice\">Venice</a>, which had declared itself independent as the <a href=\"https://wikipedia.org/wiki/Republic_of_San_Marco\" title=\"Republic of San Marco\">Republic of San Marco</a>, surrenders to Austria.", "links": [{"title": "Revolutions of 1848 in the Austrian Empire", "link": "https://wikipedia.org/wiki/Revolutions_of_1848_in_the_Austrian_Empire"}, {"title": "Venice", "link": "https://wikipedia.org/wiki/Venice"}, {"title": "Republic of San Marco", "link": "https://wikipedia.org/wiki/Republic_of_San_Marco"}]}, {"year": "1850", "text": "<PERSON>’s Lohengrin premieres at the Staatskapelle Weimar.", "html": "1850 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s <i><a href=\"https://wikipedia.org/wiki/Loheng<PERSON>_(opera)\" title=\"<PERSON>heng<PERSON> (opera)\"><PERSON><PERSON><PERSON><PERSON></a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Deutsches_Nationaltheater_und_Staatskapelle_Weimar\" title=\"Deutsches Nationaltheater und Staatskapelle Weimar\">Staatskapelle Weimar</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>’s <i><a href=\"https://wikipedia.org/wiki/<PERSON>heng<PERSON>_(opera)\" title=\"<PERSON>heng<PERSON> (opera)\"><PERSON><PERSON><PERSON><PERSON></a></i> premieres at the <a href=\"https://wikipedia.org/wiki/Deutsches_Nationaltheater_und_Staatskapelle_Weimar\" title=\"Deutsches Nationaltheater und Staatskapelle Weimar\">Staatskapelle Weimar</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON><PERSON> (opera)", "link": "https://wikipedia.org/wiki/Lohengrin_(opera)"}, {"title": "Deutsches Nationaltheater und Staatskapelle Weimar", "link": "https://wikipedia.org/wiki/Deutsches_Nationaltheater_und_Staatskapelle_Weimar"}]}, {"year": "1859", "text": "The Carrington event is the strongest geomagnetic storm on record to strike the Earth. Electrical telegraph service is widely disrupted.", "html": "1859 - The <a href=\"https://wikipedia.org/wiki/Solar_storm_of_1859\" class=\"mw-redirect\" title=\"Solar storm of 1859\">Carrington event</a> is the strongest <a href=\"https://wikipedia.org/wiki/Geomagnetic_storm\" title=\"Geomagnetic storm\">geomagnetic storm</a> on record to strike the Earth. <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">Electrical telegraph</a> service is widely disrupted.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Solar_storm_of_1859\" class=\"mw-redirect\" title=\"Solar storm of 1859\">Carrington event</a> is the strongest <a href=\"https://wikipedia.org/wiki/Geomagnetic_storm\" title=\"Geomagnetic storm\">geomagnetic storm</a> on record to strike the Earth. <a href=\"https://wikipedia.org/wiki/Electrical_telegraph\" title=\"Electrical telegraph\">Electrical telegraph</a> service is widely disrupted.", "links": [{"title": "Solar storm of 1859", "link": "https://wikipedia.org/wiki/Solar_storm_of_1859"}, {"title": "Geomagnetic storm", "link": "https://wikipedia.org/wiki/Geomagnetic_storm"}, {"title": "Electrical telegraph", "link": "https://wikipedia.org/wiki/Electrical_telegraph"}]}, {"year": "1861", "text": "American Civil War: Union forces attack Cape Hatteras, North Carolina in the Battle of Hatteras Inlet Batteries which lasts for two days.", "html": "1861 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces attack <a href=\"https://wikipedia.org/wiki/Cape_Hatteras\" title=\"Cape Hatteras\">Cape Hatteras</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries\" title=\"Battle of Hatteras Inlet Batteries\">Battle of Hatteras Inlet Batteries</a> which lasts for two days.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Union_(American_Civil_War)\" title=\"Union (American Civil War)\">Union</a> forces attack <a href=\"https://wikipedia.org/wiki/Cape_Hatteras\" title=\"Cape Hatteras\">Cape Hatteras</a>, <a href=\"https://wikipedia.org/wiki/North_Carolina\" title=\"North Carolina\">North Carolina</a> in the <a href=\"https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries\" title=\"Battle of Hatteras Inlet Batteries\">Battle of Hatteras Inlet Batteries</a> which lasts for two days.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "Union (American Civil War)", "link": "https://wikipedia.org/wiki/Union_(American_Civil_War)"}, {"title": "Cape Hatteras", "link": "https://wikipedia.org/wiki/Cape_Hatteras"}, {"title": "North Carolina", "link": "https://wikipedia.org/wiki/North_Carolina"}, {"title": "Battle of Hatteras Inlet Batteries", "link": "https://wikipedia.org/wiki/Battle_of_Hatteras_Inlet_Batteries"}]}, {"year": "1862", "text": "American Civil War: The Second Battle of Bull Run, also known as the Battle of Second Manassas, begins in Virginia.  The battle ends on August 30 with another Union defeat.", "html": "1862 - American Civil War: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Bull_Run\" title=\"Second Battle of Bull Run\">Second Battle of Bull Run</a>, also known as the Battle of Second Manassas, begins in Virginia. The battle ends on August 30 with another Union defeat.", "no_year_html": "American Civil War: The <a href=\"https://wikipedia.org/wiki/Second_Battle_of_Bull_Run\" title=\"Second Battle of Bull Run\">Second Battle of Bull Run</a>, also known as the Battle of Second Manassas, begins in Virginia. The battle ends on August 30 with another Union defeat.", "links": [{"title": "Second Battle of Bull Run", "link": "https://wikipedia.org/wiki/Second_Battle_of_Bull_Run"}]}, {"year": "1867", "text": "The United States takes possession of the (at this point unoccupied) Midway Atoll.", "html": "1867 - The United States takes possession of the (at this point unoccupied) <a href=\"https://wikipedia.org/wiki/Midway_Atoll\" title=\"Midway Atoll\">Midway Atoll</a>.", "no_year_html": "The United States takes possession of the (at this point unoccupied) <a href=\"https://wikipedia.org/wiki/Midway_Atoll\" title=\"Midway Atoll\">Midway Atoll</a>.", "links": [{"title": "Midway Atoll", "link": "https://wikipedia.org/wiki/Midway_Atoll"}]}, {"year": "1879", "text": "Anglo-Zulu War: <PERSON><PERSON><PERSON><PERSON>, last king of the Zulus, is captured by the British.", "html": "1879 - <a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: <a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\"><PERSON><PERSON><PERSON><PERSON></a>, last king of the <a href=\"https://wikipedia.org/wiki/Zulu_people\" title=\"Zulu people\">Zulus</a>, is captured by the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anglo-Zulu_War\" title=\"Anglo-Zulu War\">Anglo-Zulu War</a>: <a href=\"https://wikipedia.org/wiki/Cetshwayo\" title=\"Cetshwayo\"><PERSON><PERSON><PERSON><PERSON></a>, last king of the <a href=\"https://wikipedia.org/wiki/Zulu_people\" title=\"Zulu people\">Zulus</a>, is captured by the <a href=\"https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland\" title=\"United Kingdom of Great Britain and Ireland\">British</a>.", "links": [{"title": "Anglo-Zulu War", "link": "https://wikipedia.org/wiki/Anglo-Zulu_War"}, {"title": "Cetshwayo", "link": "https://wikipedia.org/wiki/Cetshwayo"}, {"title": "Zulu people", "link": "https://wikipedia.org/wiki/Zulu_people"}, {"title": "United Kingdom of Great Britain and Ireland", "link": "https://wikipedia.org/wiki/United_Kingdom_of_Great_Britain_and_Ireland"}]}, {"year": "1898", "text": "<PERSON>'s beverage \"Brad's Drink\" is renamed \"Pepsi-Cola\".", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s beverage \"Brad's Drink\" is renamed \"<a href=\"https://wikipedia.org/wiki/Pepsi\" title=\"Pepsi\">Pepsi-Cola</a>\".", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s beverage \"Brad's Drink\" is renamed \"<a href=\"https://wikipedia.org/wiki/Pepsi\" title=\"Pepsi\">Pepsi-Cola</a>\".", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pepsi", "link": "https://wikipedia.org/wiki/Pepsi"}]}, {"year": "1901", "text": "Silliman University is founded in the Philippines. It is the first American private school in the country.", "html": "1901 - <a href=\"https://wikipedia.org/wiki/Silliman_University\" title=\"Silliman University\">Silliman University</a> is founded in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>. It is the first American <a href=\"https://wikipedia.org/wiki/Private_school\" title=\"Private school\">private school</a> in the country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Silliman_University\" title=\"Silliman University\">Silliman University</a> is founded in the <a href=\"https://wikipedia.org/wiki/Philippines\" title=\"Philippines\">Philippines</a>. It is the first American <a href=\"https://wikipedia.org/wiki/Private_school\" title=\"Private school\">private school</a> in the country.", "links": [{"title": "Silliman University", "link": "https://wikipedia.org/wiki/Silliman_University"}, {"title": "Philippines", "link": "https://wikipedia.org/wiki/Philippines"}, {"title": "Private school", "link": "https://wikipedia.org/wiki/Private_school"}]}, {"year": "1909", "text": "A group of mid-level Greek Army officers launches the <PERSON>udi coup, seeking wide-ranging reforms.", "html": "1909 - A group of mid-level <a href=\"https://wikipedia.org/wiki/Greek_Army\" class=\"mw-redirect\" title=\"Greek Army\">Greek Army</a> officers launches the <a href=\"https://wikipedia.org/wiki/Goudi_coup\" title=\"Goudi coup\"><PERSON>udi coup</a>, seeking wide-ranging reforms.", "no_year_html": "A group of mid-level <a href=\"https://wikipedia.org/wiki/Greek_Army\" class=\"mw-redirect\" title=\"Greek Army\">Greek Army</a> officers launches the <a href=\"https://wikipedia.org/wiki/Goudi_coup\" title=\"Goudi coup\"><PERSON>udi coup</a>, seeking wide-ranging reforms.", "links": [{"title": "Greek Army", "link": "https://wikipedia.org/wiki/Greek_Army"}, {"title": "<PERSON><PERSON> coup", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_coup"}]}, {"year": "1913", "text": "Queen <PERSON><PERSON> opens the Peace Palace in The Hague.", "html": "1913 - <a href=\"https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands\" title=\"Wilhelmina of the Netherlands\">Queen <PERSON><PERSON></a> opens the <a href=\"https://wikipedia.org/wiki/Peace_Palace\" title=\"Peace Palace\">Peace Palace</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands\" title=\"Wilhelmina of the Netherlands\">Queen <PERSON><PERSON></a> opens the <a href=\"https://wikipedia.org/wiki/Peace_Palace\" title=\"Peace Palace\">Peace Palace</a> in <a href=\"https://wikipedia.org/wiki/The_Hague\" title=\"The Hague\">The Hague</a>.", "links": [{"title": "Wilhelmina of the Netherlands", "link": "https://wikipedia.org/wiki/Wilhelmina_of_the_Netherlands"}, {"title": "Peace Palace", "link": "https://wikipedia.org/wiki/Peace_Palace"}, {"title": "The Hague", "link": "https://wikipedia.org/wiki/The_Hague"}]}, {"year": "1914", "text": "World War I: The Royal Navy defeats the German fleet in the Battle of Heligoland Bight.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heligoland_Bight_(1914)\" title=\"Battle of Heligoland Bight (1914)\">Battle of Heligoland Bight</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Royal_Navy\" title=\"Royal Navy\">Royal Navy</a> defeats the <a href=\"https://wikipedia.org/wiki/German_Empire\" title=\"German Empire\">German</a> fleet in the <a href=\"https://wikipedia.org/wiki/Battle_of_Heligoland_Bight_(1914)\" title=\"Battle of Heligoland Bight (1914)\">Battle of Heligoland Bight</a>.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Royal Navy", "link": "https://wikipedia.org/wiki/Royal_Navy"}, {"title": "German Empire", "link": "https://wikipedia.org/wiki/German_Empire"}, {"title": "Battle of Heligoland Bight (1914)", "link": "https://wikipedia.org/wiki/Battle_of_Heligoland_Bight_(1914)"}]}, {"year": "1916", "text": "World War I: Germany declares war on Romania.", "html": "1916 - World War I: Germany declares war on <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "no_year_html": "World War I: Germany declares war on <a href=\"https://wikipedia.org/wiki/Kingdom_of_Romania\" title=\"Kingdom of Romania\">Romania</a>.", "links": [{"title": "Kingdom of Romania", "link": "https://wikipedia.org/wiki/Kingdom_of_Romania"}]}, {"year": "1916", "text": "World War I: Italy declares war on Germany.", "html": "1916 - World War I: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> declares war on Germany.", "no_year_html": "World War I: <a href=\"https://wikipedia.org/wiki/Kingdom_of_Italy\" title=\"Kingdom of Italy\">Italy</a> declares war on Germany.", "links": [{"title": "Kingdom of Italy", "link": "https://wikipedia.org/wiki/Kingdom_of_Italy"}]}, {"year": "1917", "text": "Ten suffragists, members of the Silent Sentinels, are arrested while picketing the White House in favor of women's suffrage in the United States.", "html": "1917 - Ten <a href=\"https://wikipedia.org/wiki/Suffragists\" class=\"mw-redirect\" title=\"Suffragists\">suffragists</a>, members of the <a href=\"https://wikipedia.org/wiki/Silent_Sentinels\" title=\"Silent Sentinels\">Silent Sentinels</a>, are arrested while picketing the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in favor of <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">women's suffrage in the United States</a>.", "no_year_html": "Ten <a href=\"https://wikipedia.org/wiki/Suffragists\" class=\"mw-redirect\" title=\"Suffragists\">suffragists</a>, members of the <a href=\"https://wikipedia.org/wiki/Silent_Sentinels\" title=\"Silent Sentinels\">Silent Sentinels</a>, are arrested while picketing the <a href=\"https://wikipedia.org/wiki/White_House\" title=\"White House\">White House</a> in favor of <a href=\"https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States\" title=\"Women's suffrage in the United States\">women's suffrage in the United States</a>.", "links": [{"title": "Suffragists", "link": "https://wikipedia.org/wiki/Suffragists"}, {"title": "Silent Sentinels", "link": "https://wikipedia.org/wiki/Silent_Sentinels"}, {"title": "White House", "link": "https://wikipedia.org/wiki/White_House"}, {"title": "Women's suffrage in the United States", "link": "https://wikipedia.org/wiki/Women%27s_suffrage_in_the_United_States"}]}, {"year": "1921", "text": "Russian Civil War: The Red Army dissolves the Makhnovshchina after driving the Revolutionary Insurgent Army out of Ukraine.", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> dissolves the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> after driving the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army</a> out of <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukraine</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Russian_Civil_War\" title=\"Russian Civil War\">Russian Civil War</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> dissolves the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a> after driving the <a href=\"https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine\" title=\"Revolutionary Insurgent Army of Ukraine\">Revolutionary Insurgent Army</a> out of <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukraine</a>.", "links": [{"title": "Russian Civil War", "link": "https://wikipedia.org/wiki/Russian_Civil_War"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}, {"title": "Revolutionary Insurgent Army of Ukraine", "link": "https://wikipedia.org/wiki/Revolutionary_Insurgent_Army_of_Ukraine"}, {"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}]}, {"year": "1924", "text": "The Georgian opposition stages the August Uprising against the Soviet Union.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic\" title=\"Georgian Soviet Socialist Republic\">Georgian</a> opposition stages the <a href=\"https://wikipedia.org/wiki/August_Uprising\" title=\"August Uprising\">August Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic\" title=\"Georgian Soviet Socialist Republic\">Georgian</a> opposition stages the <a href=\"https://wikipedia.org/wiki/August_Uprising\" title=\"August Uprising\">August Uprising</a> against the <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a>.", "links": [{"title": "Georgian Soviet Socialist Republic", "link": "https://wikipedia.org/wiki/Georgian_Soviet_Socialist_Republic"}, {"title": "August Uprising", "link": "https://wikipedia.org/wiki/August_Uprising"}, {"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}]}, {"year": "1936", "text": "Nazi Germany begins its mass arrests of <PERSON><PERSON><PERSON>'s Witnesses, who are interned in concentration camps.", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins its <a href=\"https://wikipedia.org/wiki/Persecution_of_Je<PERSON>ah%27s_Witnesses_in_Nazi_Germany\" title=\"Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany\">mass arrests</a> of <a href=\"https://wikipedia.org/wiki/Jehovah%27s_Witnesses\" title=\"<PERSON><PERSON><PERSON>'s Witnesses\"><PERSON><PERSON><PERSON>'s Witnesses</a>, who are interned in <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camps</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Nazi Germany</a> begins its <a href=\"https://wikipedia.org/wiki/Persecution_of_Jehovah%27s_Witnesses_in_Nazi_Germany\" title=\"Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany\">mass arrests</a> of <a href=\"https://wikipedia.org/wiki/Jehovah%27s_Witnesses\" title=\"<PERSON><PERSON><PERSON>'s Witnesses\"><PERSON><PERSON><PERSON>'s Witnesses</a>, who are interned in <a href=\"https://wikipedia.org/wiki/Nazi_concentration_camps\" title=\"Nazi concentration camps\">concentration camps</a>.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "Persecution of <PERSON><PERSON><PERSON>'s Witnesses in Nazi Germany", "link": "https://wikipedia.org/wiki/Persecution_of_Jehovah%27s_Witnesses_in_Nazi_Germany"}, {"title": "<PERSON><PERSON><PERSON>'s Witnesses", "link": "https://wikipedia.org/wiki/Jehovah%27s_Witnesses"}, {"title": "Nazi concentration camps", "link": "https://wikipedia.org/wiki/Nazi_concentration_camps"}]}, {"year": "1937", "text": "Toyota Motors becomes an independent company.", "html": "1937 - <a href=\"https://wikipedia.org/wiki/Toyota\" title=\"Toyota\">Toyota Motors</a> becomes an independent company.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Toyota\" title=\"Toyota\">Toyota Motors</a> becomes an independent company.", "links": [{"title": "Toyota", "link": "https://wikipedia.org/wiki/Toyota"}]}, {"year": "1943", "text": "Denmark in World War II: German authorities demand that Danish authorities crack down on acts of resistance. The next day, martial law is imposed on Denmark.", "html": "1943 - <a href=\"https://wikipedia.org/wiki/Denmark_in_World_War_II\" title=\"Denmark in World War II\">Denmark in World War II</a>: German authorities demand that Danish authorities crack down on acts of resistance. The next day, <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> is imposed on Denmark.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Denmark_in_World_War_II\" title=\"Denmark in World War II\">Denmark in World War II</a>: German authorities demand that Danish authorities crack down on acts of resistance. The next day, <a href=\"https://wikipedia.org/wiki/Martial_law\" title=\"Martial law\">martial law</a> is imposed on Denmark.", "links": [{"title": "Denmark in World War II", "link": "https://wikipedia.org/wiki/Denmark_in_World_War_II"}, {"title": "Martial law", "link": "https://wikipedia.org/wiki/Martial_law"}]}, {"year": "1944", "text": "World War II: Marseille and Toulon are liberated.", "html": "1944 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Marseille\" title=\"Marseille\">Marseille</a> and <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a> are liberated.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: <a href=\"https://wikipedia.org/wiki/Marseille\" title=\"Marseille\">Marseille</a> and <a href=\"https://wikipedia.org/wiki/Toulon\" title=\"Toulon\">Toulon</a> are liberated.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Marseille", "link": "https://wikipedia.org/wiki/Marseille"}, {"title": "Toulon", "link": "https://wikipedia.org/wiki/Toulon"}]}, {"year": "1946", "text": "The Workers' Party of North Korea, predecessor of the ruling Workers' Party of Korea, is founded at a congress held in Pyongyang, North Korea.", "html": "1946 - The <a href=\"https://wikipedia.org/wiki/Workers%27_Party_of_North_Korea\" title=\"Workers' Party of North Korea\">Workers' Party of North Korea</a>, predecessor of the ruling <a href=\"https://wikipedia.org/wiki/Workers%27_Party_of_Korea\" title=\"Workers' Party of Korea\">Workers' Party of Korea</a>, is founded at a <a href=\"https://wikipedia.org/wiki/1st_Congress_of_the_Workers%27_Party_of_North_Korea\" title=\"1st Congress of the Workers' Party of North Korea\">congress</a> held in <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>, <a href=\"https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea\" title=\"Provisional People's Committee of North Korea\">North Korea</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Workers%27_Party_of_North_Korea\" title=\"Workers' Party of North Korea\">Workers' Party of North Korea</a>, predecessor of the ruling <a href=\"https://wikipedia.org/wiki/Workers%27_Party_of_Korea\" title=\"Workers' Party of Korea\">Workers' Party of Korea</a>, is founded at a <a href=\"https://wikipedia.org/wiki/1st_Congress_of_the_Workers%27_Party_of_North_Korea\" title=\"1st Congress of the Workers' Party of North Korea\">congress</a> held in <a href=\"https://wikipedia.org/wiki/Pyongyang\" title=\"Pyongyang\">Pyongyang</a>, <a href=\"https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea\" title=\"Provisional People's Committee of North Korea\">North Korea</a>.", "links": [{"title": "Workers' Party of North Korea", "link": "https://wikipedia.org/wiki/Workers%27_Party_of_North_Korea"}, {"title": "Workers' Party of Korea", "link": "https://wikipedia.org/wiki/Workers%27_Party_of_Korea"}, {"title": "1st Congress of the Workers' Party of North Korea", "link": "https://wikipedia.org/wiki/1st_Congress_of_the_Workers%27_Party_of_North_Korea"}, {"title": "Pyongyang", "link": "https://wikipedia.org/wiki/Pyongyang"}, {"title": "Provisional People's Committee of North Korea", "link": "https://wikipedia.org/wiki/Provisional_People%27s_Committee_of_North_Korea"}]}, {"year": "1955", "text": "Black teenager <PERSON><PERSON> is lynched in Mississippi for whistling at a white woman, galvanizing the nascent civil rights movement.", "html": "1955 - <a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">Black</a> teenager <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is lynched in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> for whistling at a white woman, galvanizing the nascent <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/African_Americans\" title=\"African Americans\">Black</a> teenager <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> is lynched in <a href=\"https://wikipedia.org/wiki/Mississippi\" title=\"Mississippi\">Mississippi</a> for whistling at a white woman, galvanizing the nascent <a href=\"https://wikipedia.org/wiki/Civil_rights_movement\" title=\"Civil rights movement\">civil rights movement</a>.", "links": [{"title": "African Americans", "link": "https://wikipedia.org/wiki/African_Americans"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}, {"title": "Mississippi", "link": "https://wikipedia.org/wiki/Mississippi"}, {"title": "Civil rights movement", "link": "https://wikipedia.org/wiki/Civil_rights_movement"}]}, {"year": "1957", "text": "U.S. Senator <PERSON><PERSON> begins a filibuster to prevent the United States Senate from voting on the Civil Rights Act of 1957; he stopped speaking 24 hours and 18 minutes later, the longest filibuster ever conducted by a single Senator.", "html": "1957 - <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>rom_Thurmond\" title=\"Strom Thurmond\"><PERSON><PERSON></a> begins <a href=\"https://wikipedia.org/wiki/Strom_Thurmond_filibuster_of_the_Civil_Rights_Act_of_1957\" title=\"Strom Thurmond filibuster of the Civil Rights Act of 1957\">a filibuster</a> to prevent the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> from voting on the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1957\" title=\"Civil Rights Act of 1957\">Civil Rights Act of 1957</a>; he stopped speaking 24 hours and 18 minutes later, the longest <a href=\"https://wikipedia.org/wiki/Filibuster_in_the_United_States_Senate\" title=\"Filibuster in the United States Senate\">filibuster</a> ever conducted by a single Senator.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">U.S. Senator</a> <a href=\"https://wikipedia.org/wiki/<PERSON>rom_Thurmond\" title=\"<PERSON>rom Thurmond\"><PERSON><PERSON></a> begins <a href=\"https://wikipedia.org/wiki/Strom_Thurmond_filibuster_of_the_Civil_Rights_Act_of_1957\" title=\"Strom Thurmond filibuster of the Civil Rights Act of 1957\">a filibuster</a> to prevent the <a href=\"https://wikipedia.org/wiki/United_States_Senate\" title=\"United States Senate\">United States Senate</a> from voting on the <a href=\"https://wikipedia.org/wiki/Civil_Rights_Act_of_1957\" title=\"Civil Rights Act of 1957\">Civil Rights Act of 1957</a>; he stopped speaking 24 hours and 18 minutes later, the longest <a href=\"https://wikipedia.org/wiki/Filibuster_in_the_United_States_Senate\" title=\"Filibuster in the United States Senate\">filibuster</a> ever conducted by a single Senator.", "links": [{"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Thurmond"}, {"title": "<PERSON><PERSON>mond filibuster of the Civil Rights Act of 1957", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_T<PERSON><PERSON>_filibuster_of_the_Civil_Rights_Act_of_1957"}, {"title": "United States Senate", "link": "https://wikipedia.org/wiki/United_States_Senate"}, {"title": "Civil Rights Act of 1957", "link": "https://wikipedia.org/wiki/Civil_Rights_Act_of_1957"}, {"title": "Filibuster in the United States Senate", "link": "https://wikipedia.org/wiki/Filibuster_in_the_United_States_Senate"}]}, {"year": "1963", "text": "March on Washington for Jobs and Freedom: Rev. Dr. <PERSON> gives his I Have a Dream speech.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/March_on_Washington_for_Jobs_and_Freedom\" class=\"mw-redirect\" title=\"March on Washington for Jobs and Freedom\">March on Washington for Jobs and Freedom</a>: Rev. Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a> gives his <i><a href=\"https://wikipedia.org/wiki/I_Have_a_Dream\" title=\"I Have a Dream\">I Have a Dream</a></i> speech.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/March_on_Washington_for_Jobs_and_Freedom\" class=\"mw-redirect\" title=\"March on Washington for Jobs and Freedom\">March on Washington for Jobs and Freedom</a>: Rev. Dr. <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a> gives his <i><a href=\"https://wikipedia.org/wiki/I_Have_a_Dream\" title=\"I Have a Dream\">I Have a Dream</a></i> speech.", "links": [{"title": "March on Washington for Jobs and Freedom", "link": "https://wikipedia.org/wiki/March_on_Washington_for_Jobs_and_Freedom"}, {"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}, {"title": "I Have a Dream", "link": "https://wikipedia.org/wiki/I_Have_a_Dream"}]}, {"year": "1964", "text": "The Philadelphia race riot begins.", "html": "1964 - The <a href=\"https://wikipedia.org/wiki/1964_Philadelphia_race_riot\" title=\"1964 Philadelphia race riot\">Philadelphia race riot</a> begins.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/1964_Philadelphia_race_riot\" title=\"1964 Philadelphia race riot\">Philadelphia race riot</a> begins.", "links": [{"title": "1964 Philadelphia race riot", "link": "https://wikipedia.org/wiki/1964_Philadelphia_race_riot"}]}, {"year": "1968", "text": "Police and protesters clash during 1968 Democratic National Convention protests as protesters chant \"The whole world is watching\".", "html": "1968 - Police and protesters clash during <a href=\"https://wikipedia.org/wiki/1968_Democratic_National_Convention_protests\" title=\"1968 Democratic National Convention protests\">1968 Democratic National Convention protests</a> as protesters chant \"<a href=\"https://wikipedia.org/wiki/The_whole_world_is_watching\" title=\"The whole world is watching\">The whole world is watching</a>\".", "no_year_html": "Police and protesters clash during <a href=\"https://wikipedia.org/wiki/1968_Democratic_National_Convention_protests\" title=\"1968 Democratic National Convention protests\">1968 Democratic National Convention protests</a> as protesters chant \"<a href=\"https://wikipedia.org/wiki/The_whole_world_is_watching\" title=\"The whole world is watching\">The whole world is watching</a>\".", "links": [{"title": "1968 Democratic National Convention protests", "link": "https://wikipedia.org/wiki/1968_Democratic_National_Convention_protests"}, {"title": "The whole world is watching", "link": "https://wikipedia.org/wiki/The_whole_world_is_watching"}]}, {"year": "1973", "text": "Norrmalmstorg robbery: Stockholm police secure the surrenders of hostage-takers <PERSON><PERSON><PERSON> and <PERSON>, defusing the Norrmalmstorg hostage crisis. The behaviours of the hostages later give rise to the term Stockholm syndrome.", "html": "1973 - <a href=\"https://wikipedia.org/wiki/Norrmalmstorg_robbery\" title=\"Norrmalmstorg robbery\">Norrmalmstorg robbery</a>: <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a> police secure the surrenders of hostage-takers <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defusing the <a href=\"https://wikipedia.org/wiki/Norrmalmstorg_robbery\" title=\"Norrmalmstorg robbery\">Norrmalmstorg hostage crisis</a>. The behaviours of the hostages later give rise to the term <i><a href=\"https://wikipedia.org/wiki/Stockholm_syndrome\" title=\"Stockholm syndrome\">Stockholm syndrome</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Norrmalmstorg_robbery\" title=\"Norrmalmstorg robbery\">Norrmalmstorg robbery</a>: <a href=\"https://wikipedia.org/wiki/Stockholm\" title=\"Stockholm\">Stockholm</a> police secure the surrenders of hostage-takers <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, defusing the <a href=\"https://wikipedia.org/wiki/Norrmalmstorg_robbery\" title=\"Norrmalmstorg robbery\">Norrmalmstorg hostage crisis</a>. The behaviours of the hostages later give rise to the term <i><a href=\"https://wikipedia.org/wiki/Stockholm_syndrome\" title=\"Stockholm syndrome\">Stockholm syndrome</a></i>.", "links": [{"title": "Norrmalmstorg robbery", "link": "https://wikipedia.org/wiki/Norrmalmstorg_robbery"}, {"title": "Stockholm", "link": "https://wikipedia.org/wiki/Stockholm"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Norrmalmstorg robbery", "link": "https://wikipedia.org/wiki/Norrmalmstorg_robbery"}, {"title": "Stockholm syndrome", "link": "https://wikipedia.org/wiki/Stockholm_syndrome"}]}, {"year": "1988", "text": "Ramstein air show disaster: Three aircraft of the Frecce Tricolori demonstration team collide and the wreckage falls into the crowd. Seventy-five are killed and 346 seriously injured.", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Ramstein_air_show_disaster\" title=\"Ramstein air show disaster\">Ramstein air show disaster</a>: Three aircraft of the <a href=\"https://wikipedia.org/wiki/<PERSON>ecce_Tricolori\" title=\"<PERSON><PERSON><PERSON> Tricolori\"><PERSON><PERSON>ce <PERSON>colori</a> demonstration team collide and the wreckage falls into the crowd. Seventy-five are killed and 346 seriously injured.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ramstein_air_show_disaster\" title=\"Ramstein air show disaster\">Ramstein air show disaster</a>: Three aircraft of the <a href=\"https://wikipedia.org/wiki/Frecce_Tricolori\" title=\"<PERSON><PERSON><PERSON> Tricolori\"><PERSON><PERSON>ce <PERSON>colori</a> demonstration team collide and the wreckage falls into the crowd. Seventy-five are killed and 346 seriously injured.", "links": [{"title": "Ramstein air show disaster", "link": "https://wikipedia.org/wiki/<PERSON>tein_air_show_disaster"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>ce_<PERSON>colori"}]}, {"year": "1990", "text": "Gulf War: Iraq declares Kuwait to be its newest province.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> declares <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> to be its <a href=\"https://wikipedia.org/wiki/Kuwait_Governorate\" title=\"Kuwait Governorate\">newest</a> <a href=\"https://wikipedia.org/wiki/Governorates_of_Iraq\" title=\"Governorates of Iraq\">province</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gulf_War\" title=\"Gulf War\">Gulf War</a>: <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> declares <a href=\"https://wikipedia.org/wiki/Kuwait\" title=\"Kuwait\">Kuwait</a> to be its <a href=\"https://wikipedia.org/wiki/Kuwait_Governorate\" title=\"Kuwait Governorate\">newest</a> <a href=\"https://wikipedia.org/wiki/Governorates_of_Iraq\" title=\"Governorates of Iraq\">province</a>.", "links": [{"title": "Gulf War", "link": "https://wikipedia.org/wiki/Gulf_War"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "Kuwait", "link": "https://wikipedia.org/wiki/Kuwait"}, {"title": "Kuwait Governorate", "link": "https://wikipedia.org/wiki/Kuwait_Governorate"}, {"title": "Governorates of Iraq", "link": "https://wikipedia.org/wiki/Governorates_of_Iraq"}]}, {"year": "1990", "text": "An F5 tornado strikes the Illinois cities of Plainfield and Joliet, killing 29 people.", "html": "1990 - <a href=\"https://wikipedia.org/wiki/1990_Plainfield_tornado\" title=\"1990 Plainfield tornado\">An F5 tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> cities of <a href=\"https://wikipedia.org/wiki/Plainfield,_Illinois\" title=\"Plainfield, Illinois\">Plainfield</a> and <a href=\"https://wikipedia.org/wiki/Joliet,_Illinois\" title=\"Joliet, Illinois\">Joliet</a>, killing 29 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1990_Plainfield_tornado\" title=\"1990 Plainfield tornado\">An F5 tornado</a> strikes the <a href=\"https://wikipedia.org/wiki/Illinois\" title=\"Illinois\">Illinois</a> cities of <a href=\"https://wikipedia.org/wiki/Plainfield,_Illinois\" title=\"Plainfield, Illinois\">Plainfield</a> and <a href=\"https://wikipedia.org/wiki/Joliet,_Illinois\" title=\"Joliet, Illinois\">Joliet</a>, killing 29 people.", "links": [{"title": "1990 Plainfield tornado", "link": "https://wikipedia.org/wiki/1990_Plainfield_tornado"}, {"title": "Illinois", "link": "https://wikipedia.org/wiki/Illinois"}, {"title": "Plainfield, Illinois", "link": "https://wikipedia.org/wiki/Plainfield,_Illinois"}, {"title": "Joliet, Illinois", "link": "https://wikipedia.org/wiki/Joliet,_Illinois"}]}, {"year": "1993", "text": "NASA's Galileo probe performs a flyby of the asteroid 243 Ida. Astronomers later discover a moon, the first known asteroid moon, in pictures from the flyby and name it Dact<PERSON>.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i><PERSON></i></a> probe performs a <a href=\"https://wikipedia.org/wiki/243_Ida#Galileo_flyby\" title=\"243 Ida\">flyby of the asteroid 243 Ida</a>. Astronomers later discover a moon, the first known <a href=\"https://wikipedia.org/wiki/Minor-planet_moon\" title=\"Minor-planet moon\">asteroid moon</a>, in pictures from the flyby and name it Dactyl.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NASA\" title=\"NASA\">NASA</a>'s <a href=\"https://wikipedia.org/wiki/<PERSON>_(spacecraft)\" title=\"<PERSON> (spacecraft)\"><i><PERSON></i></a> probe performs a <a href=\"https://wikipedia.org/wiki/243_Ida#Galileo_flyby\" title=\"243 Ida\">flyby of the asteroid 243 Ida</a>. Astronomers later discover a moon, the first known <a href=\"https://wikipedia.org/wiki/Minor-planet_moon\" title=\"Minor-planet moon\">asteroid moon</a>, in pictures from the flyby and name it Dactyl.", "links": [{"title": "NASA", "link": "https://wikipedia.org/wiki/NASA"}, {"title": "<PERSON> (spacecraft)", "link": "https://wikipedia.org/wiki/<PERSON>_(spacecraft)"}, {"title": "243 Ida", "link": "https://wikipedia.org/wiki/243_Ida#Galileo_flyby"}, {"title": "Minor-planet moon", "link": "https://wikipedia.org/wiki/Minor-planet_moon"}]}, {"year": "1993", "text": "Singaporean presidential election: Former Deputy Prime Minister <PERSON><PERSON> is elected President of Singapore. Although it is the first presidential election to be determined by popular vote, the allowed candidates consist only of <PERSON><PERSON> and a reluctant whom the government had asked to run to confer upon the election the semblance of an opposition.", "html": "1993 - <a href=\"https://wikipedia.org/wiki/1993_Singaporean_presidential_election\" title=\"1993 Singaporean presidential election\">Singaporean presidential election</a>: Former Deputy Prime Minister <a href=\"https://wikipedia.org/wiki/Ong_Teng_<PERSON>eong\" title=\"Ong <PERSON>g <PERSON>eong\"><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a>. Although it is the first presidential election to be determined by popular vote, the allowed candidates consist only of <PERSON><PERSON> and a reluctant whom the government had asked to run to confer upon the election the semblance of an opposition.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1993_Singaporean_presidential_election\" title=\"1993 Singaporean presidential election\">Singaporean presidential election</a>: Former Deputy Prime Minister <a href=\"https://wikipedia.org/wiki/Ong_Teng_<PERSON>eong\" title=\"Ong <PERSON>g <PERSON>eong\"><PERSON><PERSON></a> is elected <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a>. Although it is the first presidential election to be determined by popular vote, the allowed candidates consist only of <PERSON><PERSON> and a reluctant whom the government had asked to run to confer upon the election the semblance of an opposition.", "links": [{"title": "1993 Singaporean presidential election", "link": "https://wikipedia.org/wiki/1993_Singaporean_presidential_election"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ong_Teng_<PERSON>eong"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "1993", "text": "The autonomous Croatian Community of Herzeg-Bosnia in Bosnia and Herzegovina is transformed into the Croatian Republic of Herzeg-Bosnia.", "html": "1993 - The autonomous Croatian Community of Herzeg-Bosnia in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> is transformed into the <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Republic of Herzeg-Bosnia</a>.", "no_year_html": "The autonomous Croatian Community of Herzeg-Bosnia in <a href=\"https://wikipedia.org/wiki/Bosnia_and_Herzegovina\" title=\"Bosnia and Herzegovina\">Bosnia and Herzegovina</a> is transformed into the <a href=\"https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia\" title=\"Croatian Republic of Herzeg-Bosnia\">Croatian Republic of Herzeg-Bosnia</a>.", "links": [{"title": "Bosnia and Herzegovina", "link": "https://wikipedia.org/wiki/Bosnia_and_Herzegovina"}, {"title": "Croatian Republic of Herzeg-Bosnia", "link": "https://wikipedia.org/wiki/Croatian_Republic_of_Herzeg-Bosnia"}]}, {"year": "1993", "text": "A Tajikistan Airlines Yakovlev Yak-40 crashes during takeoff from Khorog Airport in Tajikistan, killing 82.", "html": "1993 - A <a href=\"https://wikipedia.org/wiki/Tajik_Air\" title=\"Tajik Air\">Tajikistan Airlines</a> <a href=\"https://wikipedia.org/wiki/Yakovlev_Yak-40\" title=\"Yakovlev Yak-40\"><PERSON><PERSON><PERSON> Yak-40</a> <a href=\"https://wikipedia.org/wiki/1993_Tajikistan_Airlines_Yakovlev_Yak-40_crash\" title=\"1993 Tajikistan Airlines Yakovlev Yak-40 crash\">crashes</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Khorog_Airport\" title=\"Khorog Airport\">Khorog Airport</a> in Tajikistan, killing 82.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Tajik_Air\" title=\"Tajik Air\">Tajikistan Airlines</a> <a href=\"https://wikipedia.org/wiki/Yakovlev_Yak-40\" title=\"Yakovlev Yak-40\"><PERSON><PERSON><PERSON> Yak-40</a> <a href=\"https://wikipedia.org/wiki/1993_Tajikistan_Airlines_Yakovlev_Yak-40_crash\" title=\"1993 Tajikistan Airlines Yakovlev Yak-40 crash\">crashes</a> during takeoff from <a href=\"https://wikipedia.org/wiki/Khorog_Airport\" title=\"Khorog Airport\">Khorog Airport</a> in Tajikistan, killing 82.", "links": [{"title": "Tajik Air", "link": "https://wikipedia.org/wiki/Tajik_Air"}, {"title": "<PERSON><PERSON><PERSON>-40", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Yak-40"}, {"title": "1993 Tajikistan Airlines Yakovlev Yak-40 crash", "link": "https://wikipedia.org/wiki/1993_Tajikistan_Airlines_Yakovlev_Yak-40_crash"}, {"title": "Khorog Airport", "link": "https://wikipedia.org/wiki/Khorog_Airport"}]}, {"year": "1996", "text": "Chicago Seven defendant <PERSON>, antiwar activist <PERSON>, Civil Rights Movement historian <PERSON>, and eight others are arrested by the Federal Protective Service while protesting in a demonstration at the Kluczynski Federal Building in downtown Chicago during that year's Democratic National Convention.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Chicago_Seven\" title=\"Chicago Seven\">Chicago Seven</a> defendant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, antiwar activist <a href=\"https://wikipedia.org/wiki/Bradford_Lyttle\" title=\"Bradford Lyttle\">Bradford Lyttle</a>, <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a> historian <PERSON>, and eight others are arrested by the <a href=\"https://wikipedia.org/wiki/Federal_Protective_Service_(United_States)\" title=\"Federal Protective Service (United States)\">Federal Protective Service</a> while protesting in a demonstration at the <a href=\"https://wikipedia.org/wiki/Kluczynski_Federal_Building\" title=\"Kluczynski Federal Building\">Kluczynski Federal Building</a> in downtown <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> during <a href=\"https://wikipedia.org/wiki/1996_Democratic_National_Convention\" title=\"1996 Democratic National Convention\">that year's Democratic National Convention</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chicago_Seven\" title=\"Chicago Seven\">Chicago Seven</a> defendant <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, antiwar activist <a href=\"https://wikipedia.org/wiki/Bradford_Lyttle\" title=\"Bradford Lyttle\">Bradford Lyttle</a>, <a href=\"https://wikipedia.org/wiki/Civil_Rights_Movement\" class=\"mw-redirect\" title=\"Civil Rights Movement\">Civil Rights Movement</a> historian <PERSON>, and eight others are arrested by the <a href=\"https://wikipedia.org/wiki/Federal_Protective_Service_(United_States)\" title=\"Federal Protective Service (United States)\">Federal Protective Service</a> while protesting in a demonstration at the <a href=\"https://wikipedia.org/wiki/Kluczynski_Federal_Building\" title=\"Kluczynski Federal Building\">Kluczynski Federal Building</a> in downtown <a href=\"https://wikipedia.org/wiki/Chicago\" title=\"Chicago\">Chicago</a> during <a href=\"https://wikipedia.org/wiki/1996_Democratic_National_Convention\" title=\"1996 Democratic National Convention\">that year's Democratic National Convention</a>.", "links": [{"title": "Chicago Seven", "link": "https://wikipedia.org/wiki/Chicago_Seven"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Bradford Lyttle", "link": "https://wikipedia.org/wiki/Bradford_Lyttle"}, {"title": "Civil Rights Movement", "link": "https://wikipedia.org/wiki/Civil_Rights_Movement"}, {"title": "Federal Protective Service (United States)", "link": "https://wikipedia.org/wiki/Federal_Protective_Service_(United_States)"}, {"title": "Kluczynski Federal Building", "link": "https://wikipedia.org/wiki/K<PERSON><PERSON>ynski_Federal_Building"}, {"title": "Chicago", "link": "https://wikipedia.org/wiki/Chicago"}, {"title": "1996 Democratic National Convention", "link": "https://wikipedia.org/wiki/1996_Democratic_National_Convention"}]}, {"year": "1998", "text": "Pakistan's National Assembly passes a constitutional amendment to make the \"Qur'an and Sunnah\" the \"supreme law\" but the bill is defeated in the Senate.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>'s <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Pakistan\" title=\"National Assembly of Pakistan\">National Assembly</a> passes a <a href=\"https://wikipedia.org/wiki/Constitution_of_Pakistan\" title=\"Constitution of Pakistan\">constitutional</a> amendment to make the \"<a href=\"https://wikipedia.org/wiki/Quran\" title=\"Quran\">Qur'an</a> and <a href=\"https://wikipedia.org/wiki/Sunnah\" title=\"Sunnah\">Sunnah</a>\" the \"supreme law\" but the bill is defeated in the <a href=\"https://wikipedia.org/wiki/Senate_of_Pakistan\" title=\"Senate of Pakistan\">Senate</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a>'s <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Pakistan\" title=\"National Assembly of Pakistan\">National Assembly</a> passes a <a href=\"https://wikipedia.org/wiki/Constitution_of_Pakistan\" title=\"Constitution of Pakistan\">constitutional</a> amendment to make the \"<a href=\"https://wikipedia.org/wiki/Quran\" title=\"Quran\">Qur'an</a> and <a href=\"https://wikipedia.org/wiki/Sunnah\" title=\"Sunnah\">Sunnah</a>\" the \"supreme law\" but the bill is defeated in the <a href=\"https://wikipedia.org/wiki/Senate_of_Pakistan\" title=\"Senate of Pakistan\">Senate</a>.", "links": [{"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "National Assembly of Pakistan", "link": "https://wikipedia.org/wiki/National_Assembly_of_Pakistan"}, {"title": "Constitution of Pakistan", "link": "https://wikipedia.org/wiki/Constitution_of_Pakistan"}, {"title": "Quran", "link": "https://wikipedia.org/wiki/Quran"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sunnah"}, {"title": "Senate of Pakistan", "link": "https://wikipedia.org/wiki/Senate_of_Pakistan"}]}, {"year": "1998", "text": "Second Congo War: Loyalist troops backed by Angolan and Zimbabwean forces repulse the RCD and Rwandan offensive on Kinshasa.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_the_Democratic_Republic_of_the_Congo\" title=\"Armed Forces of the Democratic Republic of the Congo\">Loyalist troops</a> backed by <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angolan</a> and <a href=\"https://wikipedia.org/wiki/Zimbabwean\" class=\"mw-redirect\" title=\"Zimbabwean\">Zimbabwean</a> forces repulse the <a href=\"https://wikipedia.org/wiki/Rally_for_Congolese_Democracy\" title=\"Rally for Congolese Democracy\">RCD</a> and <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> offensive on <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Second_Congo_War\" title=\"Second Congo War\">Second Congo War</a>: <a href=\"https://wikipedia.org/wiki/Armed_Forces_of_the_Democratic_Republic_of_the_Congo\" title=\"Armed Forces of the Democratic Republic of the Congo\">Loyalist troops</a> backed by <a href=\"https://wikipedia.org/wiki/Angola\" title=\"Angola\">Angolan</a> and <a href=\"https://wikipedia.org/wiki/Zimbabwean\" class=\"mw-redirect\" title=\"Zimbabwean\">Zimbabwean</a> forces repulse the <a href=\"https://wikipedia.org/wiki/Rally_for_Congolese_Democracy\" title=\"Rally for Congolese Democracy\">RCD</a> and <a href=\"https://wikipedia.org/wiki/Rwanda\" title=\"Rwanda\">Rwandan</a> offensive on <a href=\"https://wikipedia.org/wiki/Kinshasa\" title=\"Kinshasa\">Kinshasa</a>.", "links": [{"title": "Second Congo War", "link": "https://wikipedia.org/wiki/Second_Congo_War"}, {"title": "Armed Forces of the Democratic Republic of the Congo", "link": "https://wikipedia.org/wiki/Armed_Forces_of_the_Democratic_Republic_of_the_Congo"}, {"title": "Angola", "link": "https://wikipedia.org/wiki/Angola"}, {"title": "Zimbabwean", "link": "https://wikipedia.org/wiki/Zimbabwean"}, {"title": "Rally for Congolese Democracy", "link": "https://wikipedia.org/wiki/Rally_for_Congolese_Democracy"}, {"title": "Rwanda", "link": "https://wikipedia.org/wiki/Rwanda"}, {"title": "Kinshasa", "link": "https://wikipedia.org/wiki/Kinshasa"}]}, {"year": "1999", "text": "The Russian space mission Soyuz TM-29 reaches completion, ending nearly 10 years of continuous occupation on the space station Mir as it approaches the end of its life.", "html": "1999 - The Russian space mission <a href=\"https://wikipedia.org/wiki/Soyuz_TM-29\" title=\"Soyuz TM-29\">Soyuz TM-29</a> reaches completion, ending nearly 10 years of continuous occupation on the <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> as it approaches the end of its life.", "no_year_html": "The Russian space mission <a href=\"https://wikipedia.org/wiki/Soyuz_TM-29\" title=\"Soyuz TM-29\">Soyuz TM-29</a> reaches completion, ending nearly 10 years of continuous occupation on the <a href=\"https://wikipedia.org/wiki/Space_station\" title=\"Space station\">space station</a> <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i> as it approaches the end of its life.", "links": [{"title": "Soyuz TM-29", "link": "https://wikipedia.org/wiki/Soyuz_TM-29"}, {"title": "Space station", "link": "https://wikipedia.org/wiki/Space_station"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "2003", "text": "In \"one of the most complicated and bizarre crimes in the annals of the FBI\", <PERSON> dies after becoming involved in a complex plot involving a bank robbery, a scavenger hunt, and a homemade explosive device.", "html": "2003 - In \"one of the most complicated and bizarre crimes in the annals of the <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a>\", <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\"><PERSON></a> dies after becoming involved in a complex plot involving a <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robbery</a>, a <a href=\"https://wikipedia.org/wiki/Scavenger_hunt\" title=\"Scavenger hunt\">scavenger hunt</a>, and a <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">homemade explosive device</a>.", "no_year_html": "In \"one of the most complicated and bizarre crimes in the annals of the <a href=\"https://wikipedia.org/wiki/FBI\" class=\"mw-redirect\" title=\"FBI\">FBI</a>\", <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>\" title=\"Death of <PERSON>\"><PERSON></a> dies after becoming involved in a complex plot involving a <a href=\"https://wikipedia.org/wiki/Bank_robbery\" title=\"Bank robbery\">bank robbery</a>, a <a href=\"https://wikipedia.org/wiki/Scavenger_hunt\" title=\"Scavenger hunt\">scavenger hunt</a>, and a <a href=\"https://wikipedia.org/wiki/Improvised_explosive_device\" title=\"Improvised explosive device\">homemade explosive device</a>.", "links": [{"title": "FBI", "link": "https://wikipedia.org/wiki/FBI"}, {"title": "Death of <PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON>_<PERSON>"}, {"title": "Bank robbery", "link": "https://wikipedia.org/wiki/Bank_robbery"}, {"title": "Scavenger hunt", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_hunt"}, {"title": "Improvised explosive device", "link": "https://wikipedia.org/wiki/Improvised_explosive_device"}]}, {"year": "2009", "text": "NASA's Space Shuttle Discovery launches on STS-128.", "html": "2009 - NASA's <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-128\" title=\"STS-128\">STS-128</a>.", "no_year_html": "NASA's <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Discovery\" title=\"Space Shuttle Discovery\">Space Shuttle <i>Discovery</i></a> launches on <a href=\"https://wikipedia.org/wiki/STS-128\" title=\"STS-128\">STS-128</a>.", "links": [{"title": "Space Shuttle Discovery", "link": "https://wikipedia.org/wiki/Space_Shuttle_Discovery"}, {"title": "STS-128", "link": "https://wikipedia.org/wiki/STS-128"}]}, {"year": "2016", "text": "The first experimental mission of ISRO's Scramjet Engine towards the realisation of an Air Breathing Propulsion System is successfully conducted from Satish Dhawan Space Centre SHAR, Sriharikota.", "html": "2016 - The first experimental mission of <a href=\"https://wikipedia.org/wiki/Indian_Space_Research_Organisation\" class=\"mw-redirect\" title=\"Indian Space Research Organisation\">ISRO</a>'s Scramjet Engine towards the realisation of an Air Breathing Propulsion System is successfully conducted from <a href=\"https://wikipedia.org/wiki/Satish_Dhawan_Space_Centre\" title=\"Satish Dhawan Space Centre\">Satish Dhawan Space Centre</a> SHAR, <a href=\"https://wikipedia.org/wiki/Sriharikota\" title=\"Sriharikota\">Sriharikota</a>.", "no_year_html": "The first experimental mission of <a href=\"https://wikipedia.org/wiki/Indian_Space_Research_Organisation\" class=\"mw-redirect\" title=\"Indian Space Research Organisation\">ISRO</a>'s Scramjet Engine towards the realisation of an Air Breathing Propulsion System is successfully conducted from <a href=\"https://wikipedia.org/wiki/Satish_Dhawan_Space_Centre\" title=\"Satish Dhawan Space Centre\">Satish Dhawan Space Centre</a> SHAR, <a href=\"https://wikipedia.org/wiki/Sriharikota\" title=\"Sriharikota\">Sriharikota</a>.", "links": [{"title": "Indian Space Research Organisation", "link": "https://wikipedia.org/wiki/Indian_Space_Research_Organisation"}, {"title": "Satish Dhawan Space Centre", "link": "https://wikipedia.org/wiki/Satish_<PERSON><PERSON>an_Space_Centre"}, {"title": "Sriharikota", "link": "https://wikipedia.org/wiki/Sriharikota"}]}, {"year": "2017", "text": "China-India border standoff: China and India both pull their troops out of Doklam, putting an end to a two-month-long stalemate over China's construction of a road in disputed territory.", "html": "2017 - <a href=\"https://wikipedia.org/wiki/2017_China%E2%80%93India_border_standoff\" title=\"2017 China-India border standoff\">China-India border standoff</a>: <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> and <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> both pull their troops out of <a href=\"https://wikipedia.org/wiki/Doklam\" title=\"Doklam\">Doklam</a>, putting an end to a two-month-long stalemate over China's construction of a road in disputed territory.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2017_China%E2%80%93India_border_standoff\" title=\"2017 China-India border standoff\">China-India border standoff</a>: <a href=\"https://wikipedia.org/wiki/China\" title=\"China\">China</a> and <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a> both pull their troops out of <a href=\"https://wikipedia.org/wiki/Doklam\" title=\"Doklam\">Doklam</a>, putting an end to a two-month-long stalemate over China's construction of a road in disputed territory.", "links": [{"title": "2017 China-India border standoff", "link": "https://wikipedia.org/wiki/2017_China%E2%80%93India_border_standoff"}, {"title": "China", "link": "https://wikipedia.org/wiki/China"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}, {"title": "Doklam", "link": "https://wikipedia.org/wiki/<PERSON>klam"}]}, {"year": "2022", "text": "2022 Phoenix shooting: A man opens fire on pedestrians outside of a hotel in Phoenix, Arizona, resulting in the deaths of 3 people, including the perpetrator.", "html": "2022 - <a href=\"https://wikipedia.org/wiki/2022_Phoenix_shooting\" title=\"2022 Phoenix shooting\">2022 Phoenix shooting</a>: A man opens fire on pedestrians outside of a hotel in <a href=\"https://wikipedia.org/wiki/Phoenix,_Arizona\" title=\"Phoenix, Arizona\">Phoenix, Arizona</a>, resulting in the deaths of 3 people, including the perpetrator.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2022_Phoenix_shooting\" title=\"2022 Phoenix shooting\">2022 Phoenix shooting</a>: A man opens fire on pedestrians outside of a hotel in <a href=\"https://wikipedia.org/wiki/Phoenix,_Arizona\" title=\"Phoenix, Arizona\">Phoenix, Arizona</a>, resulting in the deaths of 3 people, including the perpetrator.", "links": [{"title": "2022 Phoenix shooting", "link": "https://wikipedia.org/wiki/2022_Phoenix_shooting"}, {"title": "Phoenix, Arizona", "link": "https://wikipedia.org/wiki/Phoenix,_Arizona"}]}], "Births": [{"year": "1023", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, emperor of Japan (d. 1068)", "html": "1023 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Reizei\" title=\"Emperor <PERSON>-Reizei\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1068)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Re<PERSON>i\" title=\"Emperor Go-Reizei\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, emperor of Japan (d. 1068)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-Re<PERSON>i"}]}, {"year": "1366", "text": "<PERSON>, marshal of France (d. 1421)", "html": "1366 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, marshal of France (d. 1421)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, marshal of France (d. 1421)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1476", "text": "<PERSON><PERSON><PERSON>, Japanese painter (d. 1559)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Motonobu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (d. 1559)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kan%C5%8D_Motonobu\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese painter (d. 1559)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Kan%C5%8D_<PERSON><PERSON><PERSON>u"}]}, {"year": "1481", "text": "<PERSON>, Portuguese poet (d. 1558)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_de_Miranda\" title=\"Francisco de Sá de Miranda\"><PERSON></a>, Portuguese poet (d. 1558)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_de_S%C3%A1_de_Miranda\" title=\"Francisco de Sá de Miranda\"><PERSON></a>, Portuguese poet (d. 1558)", "links": [{"title": "Francisco de Sá de Miranda", "link": "https://wikipedia.org/wiki/Francisco_de_S%C3%A1_de_Miranda"}]}, {"year": "1582", "text": "<PERSON><PERSON>, emperor of China (d. 1620)", "html": "1582 - <a href=\"https://wikipedia.org/wiki/Taichang_Emperor\" title=\"Taichang Emperor\"><PERSON><PERSON></a>, emperor of China (d. 1620)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Taichang_Emperor\" title=\"Taichang Emperor\"><PERSON><PERSON></a>, emperor of China (d. 1620)", "links": [{"title": "Taichang Emperor", "link": "https://wikipedia.org/wiki/Tai<PERSON>_Emperor"}]}, {"year": "1591", "text": "<PERSON> of Brieg, duke of Brzeg (d. 1639)", "html": "1591 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brieg\" title=\"<PERSON> of Brieg\"><PERSON> of Brieg</a>, duke of Brzeg (d. 1639)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Brieg\" title=\"<PERSON> of Brieg\"><PERSON> of Brieg</a>, duke of Brzeg (d. 1639)", "links": [{"title": "<PERSON> of Brieg", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>g"}]}, {"year": "1592", "text": "<PERSON>, 1st Duke of Buckingham, English courtier and politician (d. 1628)", "html": "1592 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Duke_of_Buckingham\" title=\"<PERSON>, 1st Duke of Buckingham\"><PERSON>, 1st Duke of Buckingham</a>, English courtier and politician (d. 1628)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Buckingham\" title=\"<PERSON>, 1st Duke of Buckingham\"><PERSON>, 1st Duke of Buckingham</a>, English courtier and politician (d. 1628)", "links": [{"title": "<PERSON>, 1st Duke of Buckingham", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Duke_of_Buckingham"}]}, {"year": "1612", "text": "<PERSON>, Dutch linguist and scholar (d. 1653)", "html": "1612 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist and scholar (d. 1653)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch linguist and scholar (d. 1653)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON> of Mecklenburg-Güstrow, queen of Denmark and Norway (d. 1721)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-G%C3%BCstrow\" title=\"<PERSON> of Mecklenburg-Güstrow\"><PERSON> of Mecklenburg<PERSON>Güstrow</a>, queen of Denmark and Norway (d. 1721)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-G%C3%BCstrow\" title=\"<PERSON> of Mecklenburg-Güstrow\"><PERSON> of Mecklenburg<PERSON>Güstrow</a>, queen of Denmark and Norway (d. 1721)", "links": [{"title": "Louise of Mecklenburg-Güstrow", "link": "https://wikipedia.org/wiki/<PERSON>_of_Mecklenburg-G%C3%BCstrow"}]}, {"year": "1691", "text": "<PERSON> of Brunswick-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Holy Roman Empress (d. 1750)", "html": "1691 - <a href=\"https://wikipedia.org/wiki/Elisabeth_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON> of Brunswick-Wolfenbüttel\"><PERSON> of Brunswick-Wolfenbüttel</a>, Holy Roman Empress (d. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Elisabeth_<PERSON>_of_Brunswick-Wolfenb%C3%BCttel\" title=\"<PERSON> of Brunswick-Wolfenbüttel\"><PERSON> of Brunswick-Wolfenbü<PERSON>l</a>, Holy Roman Empress (d. 1750)", "links": [{"title": "<PERSON> Brunswick-Wolfenbüttel", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_Brunswick-Wolfenb%C3%BCttel"}]}, {"year": "1714", "text": "<PERSON>, duke of Brunswick-Lüneburg (d. 1774)", "html": "1714 - <a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_of_Brunswick\" title=\"Duke <PERSON> of Brunswick\"><PERSON></a>, duke of Brunswick-Lüneburg (d. 1774)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Duke_<PERSON>_<PERSON>_of_Brunswick\" title=\"Duke <PERSON> of Brunswick\"><PERSON></a>, duke of Brunswick-Lüneburg (d. 1774)", "links": [{"title": "Duke <PERSON> of Brunswick", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>_Brunswick"}]}, {"year": "1728", "text": "<PERSON>, American general (d. 1822)", "html": "1728 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American general (d. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1739", "text": "<PERSON><PERSON><PERSON>, Italian composer (d. 1818)", "html": "1739 - <a href=\"https://wikipedia.org/wiki/Agos<PERSON>_Accorimboni\" title=\"Agos<PERSON> Accorimboni\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1818)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agos<PERSON>_Accorimboni\" title=\"Agostino Accorimboni\"><PERSON><PERSON><PERSON></a>, Italian composer (d. 1818)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agostino_Accorimboni"}]}, {"year": "1749", "text": "<PERSON>, German novelist, poet, playwright, and diplomat (d. 1832)", "html": "1749 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German novelist, poet, playwright, and diplomat (d. 1832)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German novelist, poet, playwright, and diplomat (d. 1832)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1774", "text": "<PERSON>, American nun and saint, co-founded the Sisters of Charity Federation in the Vincentian-Setonian Tradition (d. 1821)", "html": "1774 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nun and saint, co-founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Charity_Federation_in_the_Vincentian-Setonian_Tradition\" title=\"Sisters of Charity Federation in the Vincentian-Setonian Tradition\">Sisters of Charity Federation in the Vincentian-Setonian Tradition</a> (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nun and saint, co-founded the <a href=\"https://wikipedia.org/wiki/Sisters_of_Charity_Federation_in_the_Vincentian-Setonian_Tradition\" title=\"Sisters of Charity Federation in the Vincentian-Setonian Tradition\">Sisters of Charity Federation in the Vincentian-Setonian Tradition</a> (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Sisters of Charity Federation in the Vincentian-Setonian Tradition", "link": "https://wikipedia.org/wiki/Sisters_of_Charity_Federation_in_the_Vincentian-Setonian_Tradition"}]}, {"year": "1801", "text": "<PERSON>, French mathematician and philosopher (d. 1877)", "html": "1801 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and philosopher (d. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1814", "text": "<PERSON>, Irish author (d. 1873)", "html": "1814 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish author (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1816", "text": "<PERSON>, English-Australian politician, 6th Premier of Victoria (d. 1884)", "html": "1816 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 6th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1822", "text": "<PERSON>, English-Australian politician, 11th Premier of Victoria (d. 1904)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Australian politician, 11th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1827", "text": "<PERSON>, Russian grand duchess (d. 1894)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\"><PERSON></a>, Russian grand duchess (d. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia\" title=\"Grand Duchess <PERSON> of Russia\"><PERSON></a>, Russian grand duchess (d. 1894)", "links": [{"title": "Grand Duchess <PERSON> of Russia", "link": "https://wikipedia.org/wiki/Grand_Duchess_<PERSON>_<PERSON>_of_Russia"}]}, {"year": "1833", "text": "<PERSON>, English artist of the Pre-Raphaelite movement (d. 1898)", "html": "1833 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist of the <a href=\"https://wikipedia.org/wiki/Pre-Raphaelite_movement\" class=\"mw-redirect\" title=\"Pre-Raphaelite movement\">Pre-Raphaelite movement</a> (d. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English artist of the <a href=\"https://wikipedia.org/wiki/Pre-Raphaelite_movement\" class=\"mw-redirect\" title=\"Pre-Raphaelite movement\">Pre-Raphaelite movement</a> (d. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pre-Raphaelite movement", "link": "https://wikipedia.org/wiki/Pre-Raphaelite_movement"}]}, {"year": "1837", "text": "<PERSON>, duke of Teck (d. 1900)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Teck\" title=\"<PERSON>, Duke of Teck\"><PERSON></a>, duke of Teck (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Duke_of_Teck\" title=\"<PERSON>, Duke of Teck\"><PERSON></a>, duke of Teck (d. 1900)", "links": [{"title": "<PERSON>, Duke of Teck", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_Teck"}]}, {"year": "1840", "text": "<PERSON>, Scottish-Japanese pharmacist and businessman, founded Kobe Regatta & Athletic Club (d. 1900)", "html": "1840 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Japanese pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Kobe_Regatta_%26_Athletic_Club\" title=\"Kobe Regatta &amp; Athletic Club\">Kobe Regatta &amp; Athletic Club</a> (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Japanese pharmacist and businessman, founded <a href=\"https://wikipedia.org/wiki/Kobe_Regatta_%26_Athletic_Club\" title=\"Kobe Regatta &amp; Athletic Club\">Kobe Regatta &amp; Athletic Club</a> (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Kobe Regatta & Athletic Club", "link": "https://wikipedia.org/wiki/Kobe_Regatta_%26_Athletic_Club"}]}, {"year": "1853", "text": "<PERSON>, Russian architect and engineer, designed the Adziogol Lighthouse (d. 1939)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Adziogol_Lighthouse\" title=\"Adziogol Lighthouse\">Adziogol Lighthouse</a> (d. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian architect and engineer, designed the <a href=\"https://wikipedia.org/wiki/Adziogol_Lighthouse\" title=\"Adziogol Lighthouse\">Adziogol Lighthouse</a> (d. 1939)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Adziogol Lighthouse", "link": "https://wikipedia.org/wiki/Adziogol_Lighthouse"}]}, {"year": "1859", "text": "<PERSON>, American archer (d. 1938)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American archer (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American archer (d. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON><PERSON><PERSON>, Italian mountaineer and photographer (d. 1943)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer and photographer (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian mountaineer and photographer (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1867", "text": "<PERSON><PERSON>, Italian composer and academic (d. 1948)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/Umberto_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and academic (d. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Umberto_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian composer and academic (d. 1948)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Umberto_<PERSON>"}]}, {"year": "1878", "text": "<PERSON>, American physician and pathologist, Nobel Prize laureate (d. 1976)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and pathologist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1884", "text": "<PERSON>, Scottish-New Zealand journalist and politician, 24th Prime Minister of New Zealand (d. 1950)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand journalist and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-New Zealand journalist and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand\" title=\"Prime Minister of New Zealand\">Prime Minister of New Zealand</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of New Zealand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_New_Zealand"}]}, {"year": "1885", "text": "<PERSON>, Australian author, playwright, and critic (d. 1959)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author, playwright, and critic (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian author, playwright, and critic (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, Estonian-Australian wrestler and poet (d. 1973)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian-Australian wrestler and poet (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/August_<PERSON>\" title=\"August <PERSON>\">August <PERSON></a>, Estonian-Australian wrestler and poet (d. 1973)", "links": [{"title": "August <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/August_<PERSON><PERSON>sto"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Slovenian priest and politician (d. 1922)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_K%C3%BCh%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian priest and politician (d. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_K%C3%BCh%C3%A1r\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian priest and politician (d. 1922)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_K%C3%BCh%C3%A1r"}]}, {"year": "1888", "text": "<PERSON><PERSON><PERSON>, Australian actress, astrologer, and author (d. 1985)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress, astrologer, and author (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian actress, astrologer, and author (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Price"}]}, {"year": "1891", "text": "<PERSON><PERSON>, Estonian-Scottish sculptor and engineer (d. 1984)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Scottish sculptor and engineer (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian-Scottish sculptor and engineer (d. 1984)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>z"}]}, {"year": "1894", "text": "<PERSON>, Austrian conductor and director (d. 1981)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6hm\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor and director (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_B%C3%B6hm"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Indian author, poet, and critic (d. 1982)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Firaq_Gorakhpuri\" title=\"Firaq Gorakhpuri\"><PERSON><PERSON><PERSON> Gorak<PERSON></a>, Indian author, poet, and critic (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Firaq_Gorakhpuri\" title=\"Firaq Gorakhpuri\"><PERSON><PERSON><PERSON> Gorak<PERSON></a>, Indian author, poet, and critic (d. 1982)", "links": [{"title": "Firaq <PERSON>", "link": "https://wikipedia.org/wiki/Firaq_Gorakhpuri"}]}, {"year": "1898", "text": "<PERSON>, American baseball player, manager, and sportscaster (d. 1983)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, manager, and sportscaster (d. 1983)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, French-American actor, singer, and producer (d. 1978)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor, singer, and producer (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-American actor, singer, and producer (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Russian author and poet (d. 1951)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian author and poet (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Chinese American cinematographer (d. 1976)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese American cinematographer (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese American cinematographer (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, Austrian-American psychologist and author (d. 1990)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and author (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American psychologist and author (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Italian-American engineer (d. 1980)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American engineer (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ini\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian-American engineer (d. 1980)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ini"}]}, {"year": "1904", "text": "<PERSON><PERSON>, Estonian chess player (d. 1998)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian chess player (d. 1998)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1905", "text": "<PERSON>, Welsh-English cricketer (d. 1992)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh-English cricketer (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1906", "text": "<PERSON>, English poet and academic (d. 1984)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 1984)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and academic (d. 1984)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, American ornithologist and author (d. 1996)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ornithologist and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American painter and academic (d. 2001)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and academic (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Graves\"><PERSON></a>, American painter and academic (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON><PERSON><PERSON>, Dutch-American mathematician and economist Nobel Prize laureate (d. 1985)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/Tja<PERSON>_<PERSON>\" title=\"Tja<PERSON>\">T<PERSON><PERSON></a>, Dutch-American mathematician and economist <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tja<PERSON>_<PERSON>\" title=\"Tja<PERSON>\">T<PERSON><PERSON></a>, Dutch-American mathematician and economist <a href=\"https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences\" title=\"Nobel Memorial Prize in Economic Sciences\">Nobel Prize</a> laureate (d. 1985)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tja<PERSON>_<PERSON>"}, {"title": "Nobel Memorial Prize in Economic Sciences", "link": "https://wikipedia.org/wiki/Nobel_Memorial_Prize_in_Economic_Sciences"}]}, {"year": "1911", "text": "<PERSON>, Dutch politician and diplomat, 5th Secretary General of NATO (d. 2002)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch politician and diplomat, 5th <a href=\"https://wikipedia.org/wiki/Secretary_General_of_NATO\" title=\"Secretary General of NATO\">Secretary General of NATO</a> (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary General of NATO", "link": "https://wikipedia.org/wiki/Secretary_General_of_NATO"}]}, {"year": "1913", "text": "<PERSON>, Canadian journalist, author, and playwright (d. 1995)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, author, and playwright (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian journalist, author, and playwright (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American businessman, founded the Dreyfus Corporation (d. 2009)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Dreyfus_Corporation\" title=\"Dreyfus Corporation\">Dreyfus Corporation</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman, founded the <a href=\"https://wikipedia.org/wiki/Dreyfus_Corporation\" title=\"Dreyfus Corporation\">Dreyfus Corporation</a> (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dreyfus Corporation", "link": "https://wikipedia.org/wiki/Dreyfus_Corporation"}]}, {"year": "1913", "text": "<PERSON>, Australian cricketer and sportscaster (d. 1993)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer and sportscaster (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, English conductor and director (d. 1991)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(conductor)\" title=\"<PERSON> (conductor)\"><PERSON></a>, English conductor and director (d. 1991)", "links": [{"title": "<PERSON> (conductor)", "link": "https://wikipedia.org/wiki/<PERSON>(conductor)"}]}, {"year": "1913", "text": "<PERSON>, English bridge player and author (d. 1996)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bridge player and author (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bridge player and author (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1913", "text": "<PERSON>, American tenor and actor (d. 1975)", "html": "1913 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" class=\"mw-redirect\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" class=\"mw-redirect\" title=\"<PERSON> (tenor)\"><PERSON></a>, American tenor and actor (d. 1975)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1915", "text": "<PERSON>, Bengal-born English sportscaster and author (d. 2009)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengal-born English sportscaster and author (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bengal-born English sportscaster and author (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, American author and illustrator (d. 2008)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author and illustrator (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian singer and actress (d. 1997)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and actress (d. 1997)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/H%C3%A9l%C3%A8<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian singer and actress (d. 1997)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/H%C3%A9l%C3%A8ne_<PERSON>geon"}]}, {"year": "1916", "text": "<PERSON><PERSON> American sociologist and author (d. 1962)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> American sociologist and author (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> American sociologist and author (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, American author (d. 2013)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON>, American author and illustrator (d. 1994)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON> <PERSON><PERSON>, American illustrator and publisher (d. 1995)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. B<PERSON> Cole\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and publisher (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"L. B<PERSON> Cole\"><PERSON><PERSON> <PERSON><PERSON></a>, American illustrator and publisher (d. 1995)", "links": [{"title": "L. B<PERSON> Cole", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, English biophysicist and engineer Nobel Prize laureate (d. 2004)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biophysicist and engineer <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine\" class=\"mw-redirect\" title=\"Nobel Prize for Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biophysicist and engineer <a href=\"https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine\" class=\"mw-redirect\" title=\"Nobel Prize for Physiology or Medicine\">Nobel Prize</a> laureate (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize for Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_for_Physiology_or_Medicine"}]}, {"year": "1921", "text": "<PERSON>, Canadian physicist and engineer (d. 1979)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and engineer (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physicist and engineer (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON>, Spanish actor, director, and playwright (d. 2007)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and playwright (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n_G%C3%B3mez\" title=\"<PERSON>\"><PERSON></a>, Spanish actor, director, and playwright (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fernando_Fern%C3%A1n_G%C3%B3mez"}]}, {"year": "1921", "text": "<PERSON>, American actress and soldier (d. 1991)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and soldier (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and soldier (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON>, the first female President of Bolivia (d. 2011)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>jada\" class=\"mw-redirect\" title=\"<PERSON><PERSON>ue<PERSON> Tejada\"><PERSON><PERSON></a>, the first female President of Bolivia (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>da\" class=\"mw-redirect\" title=\"Lidia Gueiler Tejada\"><PERSON><PERSON></a>, the first female President of Bolivia (d. 2011)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>da"}]}, {"year": "1924", "text": "<PERSON>, New Zealand author and poet (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and poet (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand author and poet (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, New Zealand cricketer and engineer (d. 2010)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and engineer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand cricketer and engineer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American actress and dancer (d. 2004)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and dancer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, Ukrainian-American rabbi and author (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American rabbi and author (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-American rabbi and author (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American actor, singer, and dancer (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor\" title=\"<PERSON>\"><PERSON></a>, American actor, singer, and dancer (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Connor"}]}, {"year": "1925", "text": "<PERSON>, English author and critic (d. 2022)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON>, American businessman (d. 2003)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"F<PERSON>\"><PERSON><PERSON></a>, American businessman (d. 2003)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian sitar player and composer (d. 2004)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer (d. 2004)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Khan"}, {"title": "<PERSON>ar", "link": "https://wikipedia.org/wiki/Sitar"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Hungarian conductor (d. 1973)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9<PERSON>_(conductor)\" title=\"<PERSON><PERSON><PERSON> (conductor)\"><PERSON><PERSON><PERSON></a>, Hungarian conductor (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9<PERSON>_(conductor)\" title=\"<PERSON><PERSON><PERSON> (conductor)\"><PERSON><PERSON><PERSON></a>, Hungarian conductor (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON> (conductor)", "link": "https://wikipedia.org/wiki/Istv%C3%A1n_Kert%C3%A9sz_(conductor)"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, American actress (d. 1995)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Roxie_Roker\" title=\"Roxie Roker\"><PERSON><PERSON><PERSON></a>, American actress (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roxie_Roker\" title=\"Roxie Roker\"><PERSON><PERSON><PERSON></a>, American actress (d. 1995)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Roxie_Roker"}]}, {"year": "1930", "text": "<PERSON>, British actor (d. 2019)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British actor (d. 2019)", "links": [{"title": "Windsor Davies", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, American actor (d. 2012)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Argentinian director and producer (d. 2018)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian director and producer (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Dutch soprano and actress (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch soprano and actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch soprano and actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON>, American colonel, Medal of Honor recipient (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, American colonel, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 2014)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1931", "text": "<PERSON>-<PERSON><PERSON>, English actor, singer, and educator (d. 2014)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor, singer, and educator (d. 2014)", "links": [{"title": "<PERSON>rk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rk"}]}, {"year": "1931", "text": "<PERSON>, English hepatologist and academic (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hepatologist)\" title=\"<PERSON> (hepatologist)\"><PERSON></a>, English hepatologist and academic (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hepatologist)\" title=\"<PERSON> (hepatologist)\"><PERSON></a>, English hepatologist and academic (d. 2020)", "links": [{"title": "<PERSON> (hepatologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hepatologist)"}]}, {"year": "1932", "text": "<PERSON><PERSON><PERSON>, Israeli academic and educator", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli academic and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli academic and educator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, Canadian ice hockey player, coach, and manager (d. 2016)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player, coach, and manager (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English journalist, critic, and producer (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Philip <PERSON>\"><PERSON></a>, English journalist, critic, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Philip_<PERSON>\" title=\"Philip <PERSON>\"><PERSON></a>, English journalist, critic, and producer (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Malawian bishop and theologian (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian bishop and theologian (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malawian bishop and theologian (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian sculptor and architect (d. 2012)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and architect (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian sculptor and architect (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Canadian businessman and politician (d. 1998)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businessman and politician (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actor", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American baseball player and umpire (d. 2023)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, American atmospheric scientist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/Warren_M<PERSON>_Washington\" title=\"Warren M. Washington\"><PERSON></a>, American atmospheric scientist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Warren_M<PERSON>_Washington\" title=\"Warren M. Washington\"><PERSON></a>, American atmospheric scientist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Warren_<PERSON>._<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, American actress (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON>, Italian journalist and academic (d. 2023)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and academic (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian journalist and academic (d. 2023)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Swedish journalist (d. 2017)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Ben<PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>r%C3%B6m\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish journalist (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bengt_Fahlstr%C3%B6m"}]}, {"year": "1938", "text": "<PERSON><PERSON>, Italian automotive designer (d. 2024)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian automotive designer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian automotive designer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian lawyer and politician, 21st Prime Minister of Canada", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 21st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Canada\" title=\"Prime Minister of Canada\">Prime Minister of Canada</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Canada", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Canada"}]}, {"year": "1939", "text": "<PERSON>, English mathematician and academic", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"John Kingman\"><PERSON></a>, English mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/John_Kingman\" title=\"John Kingman\"><PERSON></a>, English mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/John_Kingman"}]}, {"year": "1940", "text": "<PERSON>, American lawyer and politician, 20th United States Secretary of Defense", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 20th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Defense\" title=\"United States Secretary of Defense\">United States Secretary of Defense</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "United States Secretary of Defense", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Defense"}]}, {"year": "1940", "text": "<PERSON>, American actor", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, French cyclist (d. 2017)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cyclist (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Irish painter and illustrator", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish painter and illustrator", "links": [{"title": "<PERSON>-<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, Estonian tennis player and coach", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian tennis player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, English drummer (d. 2023)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English drummer (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American opera singer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American opera singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Welsh historian and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh historian and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Venezuelan cardinal (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan cardinal (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Venezuelan cardinal (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Lebanese actor and voice actor", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Al<PERSON>\"><PERSON><PERSON></a>, Lebanese actor and voice actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Al-<PERSON>\"><PERSON><PERSON></a>, Lebanese actor and voice actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON><PERSON>, Thai general and politician, 24th Prime Minister of Thailand", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai general and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Thai general and politician, 24th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Thailand\" title=\"Prime Minister of Thailand\">Prime Minister of Thailand</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of Thailand", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Thailand"}]}, {"year": "1943", "text": "<PERSON>, American director and producer", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, Pakistani colonel and politician (d. 2015)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani colonel and politician (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani colonel and politician (d. 2015)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American baseball player and manager", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American actor and singer (d. 2024)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Soul\"><PERSON></a>, American actor and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"David Soul\"><PERSON></a>, American actor and singer (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, Dutch swimmer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American-Canadian singer-songwriter (d. 2023)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer-songwriter (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer-songwriter (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, English footballer (d. 2004)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English footballer (d. 2004)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON><PERSON>, American actress", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, Hong Kong actress and singer", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hong Kong actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, American author (d. 2019)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Vonda N<PERSON> McIntyre\"><PERSON><PERSON> <PERSON><PERSON></a>, American author (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"Vonda N. McIntyre\"><PERSON><PERSON><PERSON></a>, American author (d. 2019)", "links": [{"title": "Vonda N. McIntyre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, New Zealand cricketer and educator", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and educator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, New Zealand cricketer and educator", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1948", "text": "<PERSON>, Canadian publisher and businesswoman", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher and businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian publisher and businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American drummer and producer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English academic and jurist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and jurist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English academic and jurist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elizabeth_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Serbian basketball player and coach", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>e%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1i%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Serbian basketball player and coach", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%A1i%C4%87"}]}, {"year": "1950", "text": "<PERSON>, American baseball player and coach", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, English cartoonist (d. 2023)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cartoonist (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Husband\"><PERSON></a>, English cartoonist (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish footballer (d. 2013)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, Scottish footballer (d. 2013)", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and actor (d. 2025)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and actor (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> (composer)\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(composer)"}]}, {"year": "1952", "text": "<PERSON>, Canadian educator and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American poet and essayist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Dove\"><PERSON></a>, American poet and essayist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON>, German businessman", "html": "1952 - <a href=\"https://wikipedia.org/wiki/Wen<PERSON>in_Wiedeking\" title=\"Wendel<PERSON> Wiedeking\"><PERSON><PERSON><PERSON></a>, German businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wen<PERSON><PERSON>_Wiedeking\" title=\"Wendelin Wiedeking\"><PERSON><PERSON><PERSON></a>, German businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Di<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, Estonian conductor and journalist", "html": "1953 - <a href=\"https://wikipedia.org/wiki/T%C3%B5nu_<PERSON>lju<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian conductor and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T%C3%B5nu_<PERSON>l<PERSON>\" title=\"<PERSON>õ<PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian conductor and journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T%C3%B5nu_Kal<PERSON>ste"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American feminist economist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American feminist economist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American feminist economist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American geneticist, chemist, and engineer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist, chemist, and engineer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American geneticist, chemist, and engineer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Australian rugby player and coach", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, Indian-English economist and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English economist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English economist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bur"}]}, {"year": "1956", "text": "<PERSON>, Puerto Rican-American actor and producer", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1n\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican-American actor and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Guzm%C3%A1n"}]}, {"year": "1956", "text": "<PERSON>, American basketball player", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_player)\" title=\"<PERSON> (basketball player)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_player)\" title=\"<PERSON> (basketball player)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball_player)"}]}, {"year": "1956", "text": "<PERSON>, American singer-songwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English businessman and politician, Secretary of State for Communities and Local Government", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government\" class=\"mw-redirect\" title=\"Secretary of State for Communities and Local Government\">Secretary of State for Communities and Local Government</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government\" class=\"mw-redirect\" title=\"Secretary of State for Communities and Local Government\">Secretary of State for Communities and Local Government</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Communities and Local Government", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Communities_and_Local_Government"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Croatian lawyer, jurist, and politician, 3rd President of Croatia", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer, jurist, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Croatia\" title=\"President of Croatia\">President of Croatia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Croatian lawyer, jurist, and politician, 3rd <a href=\"https://wikipedia.org/wiki/President_of_Croatia\" title=\"President of Croatia\">President of Croatia</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/I<PERSON>_Josipovi%C4%87"}, {"title": "President of Croatia", "link": "https://wikipedia.org/wiki/President_of_Croatia"}]}, {"year": "1957", "text": "<PERSON>, American actor and director", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and director", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1957", "text": "<PERSON>, Chinese sculptor and activist", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese sculptor and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese sculptor and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ai_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American figure skater", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)"}]}, {"year": "1959", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1960", "text": "<PERSON>, English actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English singer-songwriter and actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American football player", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Benson\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Benson\" title=\"<PERSON> Benson\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, American actress", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON>, English cricketer and coach", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Indian actor and director", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i<PERSON>ri\" title=\"<PERSON><PERSON> Tijori\"><PERSON><PERSON></a>, Indian actor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i<PERSON>\" title=\"<PERSON><PERSON> Tijori\"><PERSON><PERSON></a>, Indian actor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Ti<PERSON>ri"}]}, {"year": "1962", "text": "<PERSON>, English footballer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>(footballer)"}]}, {"year": "1962", "text": "<PERSON>, American actor and screenwriter", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American director and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, Romanian folk singer-songwriter", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian folk singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian folk singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American runner", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American golfer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Faroese footballer and politician, 12th Prime Minister of the Faroe Islands", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese footballer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Faroese footballer and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands\" class=\"mw-redirect\" title=\"Prime Minister of the Faroe Islands\">Prime Minister of the Faroe Islands</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of the Faroe Islands", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_Faroe_Islands"}]}, {"year": "1965", "text": "<PERSON>, Australian rugby player", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, Australian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)\" class=\"mw-redirect\" title=\"<PERSON> (rugby player)\"><PERSON></a>, Australian rugby player", "links": [{"title": "<PERSON> (rugby player)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_player)"}]}, {"year": "1965", "text": "<PERSON>, Australian television host and actress", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian television host and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Japanese video game developer; created Pokémon", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese video game developer; created <i><a href=\"https://wikipedia.org/wiki/Pok%C3%A9mon\" title=\"Pokémon\">Pokémon</a></i>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese video game developer; created <i><a href=\"https://wikipedia.org/wiki/Pok%C3%A9mon\" title=\"Pokémon\">Pokémon</a></i>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Pokémon", "link": "https://wikipedia.org/wiki/Pok%C3%A9mon"}]}, {"year": "1965", "text": "<PERSON>, British-Canadian actress and director", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Canadian actress and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British-Canadian actress and director", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON>, Canadian singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wain\" title=\"<PERSON><PERSON> Twain\"><PERSON><PERSON></a>, Canadian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> Twain\"><PERSON><PERSON></a>, Canadian singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>wain"}]}, {"year": "1966", "text": "<PERSON><PERSON>, Indian social worker and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian social worker and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian social worker and politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English jockey and trainer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(jockey)\" title=\"<PERSON> (jockey)\"><PERSON></a>, English jockey and trainer", "links": [{"title": "<PERSON> (jockey)", "link": "https://wikipedia.org/wiki/<PERSON>_(jockey)"}]}, {"year": "1968", "text": "<PERSON>, Scottish actor and singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" class=\"mw-redirect\" title=\"<PERSON> (actor)\"><PERSON></a>, Scottish actor and singer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1969", "text": "<PERSON>, American actor and comedian", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Black\"><PERSON></a>, American actor and comedian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jack_Black\" title=\"Jack Black\"><PERSON></a>, American actor and comedian", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, English photographer and activist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, English photographer and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian actor, director, and producer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, American business executive", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American business executive", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American business executive", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Canadian-American ice hockey player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1970", "text": "<PERSON><PERSON>, German-Greek singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Greek singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German-Greek singer-songwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American baseball player", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American figure skater and coach", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American swimmer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American swimmer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Australian-American actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian-American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, Australian-American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1971", "text": "<PERSON><PERSON>, Mexican-American boxer and sportscaster", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_M%C3%A1rquez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American boxer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_M%C3%A1rquez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican-American boxer and sportscaster", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_M%C3%A1rquez"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Kenyan cricketer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Kenyan cricketer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American baseball player and coach", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ick"}]}, {"year": "1973", "text": "<PERSON><PERSON> <PERSON>, American actor", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_August_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON>_August_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\"><PERSON><PERSON> <PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/J._August_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Swedish game designer and programmer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_programmer)\" class=\"mw-redirect\" title=\"<PERSON> (game programmer)\"><PERSON></a>, Swedish game designer and programmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(game_programmer)\" class=\"mw-redirect\" title=\"<PERSON> (game programmer)\"><PERSON></a>, Swedish game designer and programmer", "links": [{"title": "<PERSON> (game programmer)", "link": "https://wikipedia.org/wiki/<PERSON>_(game_programmer)"}]}, {"year": "1974", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese pianist and composer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese pianist and composer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON><PERSON>, German footballer and manager", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Irish footballer and manager", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Australian television personality", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television personality", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television personality", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, New Zealand rugby player", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Uruguayan footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Uruguayan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Canadian weightlifter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian weightlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Spanish singer-songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Shaila_D%C3%BArcal\" title=\"Shai<PERSON> Dúrcal\"><PERSON><PERSON><PERSON></a>, Spanish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/S<PERSON>la_D%C3%BArcal\" title=\"<PERSON><PERSON><PERSON> Dú<PERSON>\"><PERSON><PERSON><PERSON></a>, Spanish singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Shaila_D%C3%BArcal"}]}, {"year": "1979", "text": "<PERSON>, German footballer and referee", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German footballer and referee", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, German footballer and referee", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, Australian netball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian netball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Australian netball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, German footballer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Pr%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%B6ll\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Markus_Pr%C3%B6ll"}]}, {"year": "1979", "text": "<PERSON>, American basketball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Finnish singer-songwriter", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4m%C3%A4l%C3%A4inen\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_H%C3%A4m%C3%A4l%C3%A4inen\" title=\"<PERSON>\"><PERSON></a>, Finnish singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Antony_H%C3%A4m%C3%A4l%C3%A4inen"}]}, {"year": "1980", "text": "<PERSON><PERSON>, American sex offender and former teacher", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sex offender and former teacher", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American sex offender and former teacher", "links": [{"title": "Debra Lafave", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Lafave"}]}, {"year": "1980", "text": "<PERSON>, American baseball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON><PERSON>, Finnish decathlete", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish decathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Finnish decathlete", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian actress and producer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, English lawyer and politician", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Scottish politician", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Dugdale\"><PERSON><PERSON></a>, Scottish politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Ke<PERSON> Dugdale\"><PERSON><PERSON></a>, Scottish politician", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>dale"}]}, {"year": "1981", "text": "<PERSON>, Swiss footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Gygax"}]}, {"year": "1981", "text": "<PERSON>, Brazilian race car driver", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Moroccan footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Moroccan footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON>, Polish weightlifter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Agata_Wr%C3%B3bel\" title=\"Agata Wróbel\">A<PERSON>r<PERSON></a>, Polish weightlifter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Agata_Wr%C3%B3bel\" title=\"Agata Wróbel\">A<PERSON>r<PERSON></a>, Polish weightlifter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Agata_Wr%C3%B3bel"}]}, {"year": "1982", "text": "<PERSON>, Brazilian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A7a\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A7a\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%A7a"}]}, {"year": "1982", "text": "<PERSON>, Scottish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, Brazilian-Italian footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Thia<PERSON>_<PERSON>\" title=\"T<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Brazilian-Italian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thia<PERSON>_<PERSON>tta"}]}, {"year": "1982", "text": "<PERSON>, American baseball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter and actress", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>nn_<PERSON><PERSON>\" title=\"LeAnn Rimes\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"LeAnn Rimes\"><PERSON><PERSON><PERSON></a>, American singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>nn <PERSON>", "link": "https://wikipedia.org/wiki/LeAnn_Rimes"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Sri Lankan cricketer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Sri Lankan cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lasith_<PERSON>nga"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>er"}]}, {"year": "1983", "text": "<PERSON><PERSON>, German heptathlete", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German heptathlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German heptathlete", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American baseball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Norwegian skier", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Norwegian skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American basketball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)\" title=\"<PERSON> (basketball)\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON> (basketball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(basketball)"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player (d. 2015)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, New Zealand rugby league player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Israeli soldier and hostage", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli soldier and hostage", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli soldier and hostage", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, English singer-songwriter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Florence_Welch\" title=\"<PERSON> Welch\"><PERSON></a>, English singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Florence_Welch\" title=\"Florence Welch\"><PERSON></a>, English singer-songwriter", "links": [{"title": "Florence Welch", "link": "https://wikipedia.org/wiki/Florence_Welch"}]}, {"year": "1987", "text": "<PERSON>, American snowmobile racer (d. 2013)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowmobile racer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American snowmobile racer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON>, American actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian trampoline gymnast", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trampoline gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian trampoline gymnast", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Spanish footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Azpi<PERSON>ta\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9sar_<PERSON>pi<PERSON>ta\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9sar_Azpilicueta"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Finnish race car driver", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bottas\"><PERSON><PERSON><PERSON></a>, Finnish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Bottas\"><PERSON><PERSON><PERSON></a>, Finnish race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>s"}]}, {"year": "1989", "text": "<PERSON>, South Korean singer and dancer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, South Korean singer and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Canadian actor", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON><PERSON>, Spanish footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_K<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_K<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bojan_Krki%C4%87"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, German footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_Forbes"}]}, {"year": "1991", "text": "<PERSON>, American actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American actor", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Bosnian model ", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>eji%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian model ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i%C4%87\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Bosnian model ", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Andreja_Peji%C4%87"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Congolese basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Bismack_Biyombo\" title=\"Bismack Biyombo\"><PERSON><PERSON><PERSON></a>, Congolese basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bismack_B<PERSON>yo<PERSON>\" title=\"Bismack Biyombo\"><PERSON><PERSON><PERSON></a>, Congolese basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American-Filipino actress and model", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American-Filipino actress and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)\" title=\"<PERSON> (actress)\"><PERSON></a>, American-Filipino actress and model", "links": [{"title": "<PERSON> (actress)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actress)"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Romanian gymnast", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dr%C4%83goi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Dr%C4%83goi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian gymnast", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gabriela_Dr%C4%83goi"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Czech footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Sokol%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J<PERSON><PERSON>_Sokol%C3%ADk\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jakub_Sokol%C3%ADk"}]}, {"year": "1994", "text": "<PERSON><PERSON>, French tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Arcangioli"}]}, {"year": "1994", "text": "<PERSON><PERSON>, Tunisian tennis player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"On<PERSON> Jabeur\"><PERSON><PERSON></a>, Tunisian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"On<PERSON> Jabeur\"><PERSON><PERSON></a>, Tunisian tennis player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American soccer player", "html": "1998 - <a href=\"https://wikipedia.org/wiki/Weston_M<PERSON>\" title=\"<PERSON> M<PERSON>Kennie\"><PERSON></a>, American soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Weston_Mc<PERSON>nie\" title=\"<PERSON> M<PERSON>K<PERSON>\"><PERSON></a>, American soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Weston_<PERSON>nie"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON>, Russian tennis player", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American actress", "html": "2003 - <a href=\"https://wikipedia.org/wiki/Quvenzhan%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Quvenzhan%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Quvenzhan%C3%A9_<PERSON>"}]}], "Deaths": [{"year": "388", "text": "<PERSON>, Roman emperor (b. 335)", "html": "388 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (b. 335)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman emperor (b. 335)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "430", "text": "<PERSON> of Hippo, Algerian bishop, theologian, and saint (b. 354)", "html": "430 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hippo\" title=\"<PERSON> of Hippo\"><PERSON> of Hippo</a>, Algerian bishop, theologian, and saint (b. 354)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hippo\" title=\"<PERSON> of Hippo\"><PERSON> of Hippo</a>, Algerian bishop, theologian, and saint (b. 354)", "links": [{"title": "<PERSON> of Hippo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Hip<PERSON>"}]}, {"year": "476", "text": "<PERSON><PERSON><PERSON>, Roman general and politician", "html": "476 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_Romulus_Augustulus)\" title=\"<PERSON><PERSON><PERSON> (father of Romulus Augustulus)\"><PERSON><PERSON><PERSON></a>, Roman general and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_Romulus_Augustulus)\" title=\"<PERSON><PERSON><PERSON> (father of Romulus Augustulus)\"><PERSON><PERSON><PERSON></a>, Roman general and politician", "links": [{"title": "<PERSON><PERSON><PERSON> (father of <PERSON><PERSON><PERSON>)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(father_of_R<PERSON><PERSON>_August<PERSON>)"}]}, {"year": "632", "text": "<PERSON><PERSON><PERSON>, daughter of <PERSON> (b. 605)", "html": "632 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, daughter of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 605)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatimah"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "683", "text": "<PERSON><PERSON><PERSON><PERSON>, ajaw of the city-state of Palenque (b. 615)", "html": "683 - <a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Janaab_Pakal_I\" class=\"mw-redirect\" title=\"Kʼinich Janaab Pakal I\"><PERSON><PERSON><PERSON><PERSON> Janaab Pakal I</a>, ajaw of the city-state of Palenque (b. 615)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K%CA%BCinich_Janaab_Pakal_I\" class=\"mw-redirect\" title=\"Kʼinich Janaab Pakal I\"><PERSON><PERSON><PERSON><PERSON> Janaab Pakal I</a>, ajaw of the city-state of Palenque (b. 615)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/K%CA%<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>al_I"}]}, {"year": "770", "text": "<PERSON><PERSON><PERSON>, emperor of Japan (b. 718)", "html": "770 - <a href=\"https://wikipedia.org/wiki/Empress_<PERSON>%C5%8Dken\" title=\"Empress <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 718)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Empress_K%C5%8Dken\" title=\"Empress <PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, emperor of Japan (b. 718)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Empress_K%C5%8Dken"}]}, {"year": "876", "text": "<PERSON> the German, Frankish king (b. 804)", "html": "876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_German\" title=\"<PERSON> the German\"><PERSON> the German</a>, Frankish king (b. 804)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_German\" title=\"<PERSON> the German\"><PERSON> the German</a>, Frankish king (b. 804)", "links": [{"title": "<PERSON> the German", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_German"}]}, {"year": "919", "text": "<PERSON>, Chinese general (b. 858)", "html": "919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON></a>, Chinese general (b. 858)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1055", "text": "<PERSON><PERSON>, Chinese emperor (b. 1016)", "html": "1055 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (b. 1016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao\" title=\"Emperor <PERSON><PERSON><PERSON> of Liao\"><PERSON><PERSON></a>, Chinese emperor (b. 1016)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON> of Liao", "link": "https://wikipedia.org/wiki/Emperor_<PERSON><PERSON><PERSON>_of_Liao"}]}, {"year": "1149", "text": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Turkish ruler and regent", "html": "1149 - <a href=\"https://wikipedia.org/wiki/Mu%27in_ad-<PERSON>_<PERSON>\" title=\"Mu'in ad-<PERSON>\">Mu'in ad-<PERSON></a>, Turkish ruler and regent", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mu%27in_ad-<PERSON>_<PERSON>\" title=\"Mu'in ad-<PERSON>\">Mu'in ad-<PERSON></a>, Turkish ruler and regent", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mu%27in_<PERSON>-<PERSON>_<PERSON>"}]}, {"year": "1231", "text": "<PERSON> of Portugal, Queen of Denmark", "html": "1231 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Denmark\" title=\"<PERSON> of Portugal, Queen of Denmark\"><PERSON> of Portugal, Queen of Denmark</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal,_Queen_of_Denmark\" title=\"<PERSON> of Portugal, Queen of Denmark\"><PERSON> of Portugal, Queen of Denmark</a>", "links": [{"title": "<PERSON> of Portugal, Queen of Denmark", "link": "https://wikipedia.org/wiki/Eleanor_of_Portugal,_Queen_of_Denmark"}]}, {"year": "1341", "text": "<PERSON><PERSON>, king of Armenia (b. 1309)", "html": "1341 - <a href=\"https://wikipedia.org/wiki/Levon_IV_of_Armenia\" class=\"mw-redirect\" title=\"Levon IV of Armenia\"><PERSON><PERSON> IV</a>, king of Armenia (b. 1309)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Levon_IV_of_Armenia\" class=\"mw-redirect\" title=\"Levon IV of Armenia\"><PERSON><PERSON> IV</a>, king of Armenia (b. 1309)", "links": [{"title": "Levon IV of Armenia", "link": "https://wikipedia.org/wiki/Levon_IV_of_Armenia"}]}, {"year": "1406", "text": "<PERSON>, Baron <PERSON> of Dudley (b. 1380)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> V\"><PERSON> V</a>, <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON> of Dudley</a> (b. 1380)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON> V\"><PERSON> V</a>, <a href=\"https://wikipedia.org/wiki/Baron_<PERSON>\" title=\"Baron <PERSON>\">Baron <PERSON> of Dudley</a> (b. 1380)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1481", "text": "<PERSON><PERSON><PERSON>, king of Portugal (b. 1432)", "html": "1481 - <a href=\"https://wikipedia.org/wiki/Afonso_V_of_Portugal\" title=\"<PERSON><PERSON><PERSON> V of Portugal\"><PERSON><PERSON><PERSON> V</a>, king of Portugal (b. 1432)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Afonso_V_of_Portugal\" title=\"<PERSON><PERSON><PERSON> V of Portugal\"><PERSON><PERSON><PERSON> V</a>, king of Portugal (b. 1432)", "links": [{"title": "<PERSON><PERSON><PERSON> V of Portugal", "link": "https://wikipedia.org/wiki/Afonso_V_of_Portugal"}]}, {"year": "1540", "text": "<PERSON>, duke of Mantua (b. 1500)", "html": "1540 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Mantua\" title=\"<PERSON>, Duke of Mantua\"><PERSON></a>, duke of Mantua (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_of_Mantua\" title=\"<PERSON>, Duke of Mantua\"><PERSON></a>, duke of Mantua (b. 1500)", "links": [{"title": "<PERSON>, Duke of Mantua", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Duke_<PERSON>_Mantua"}]}, {"year": "1609", "text": "<PERSON>, English governor and general", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English governor and general", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English governor and general", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1645", "text": "<PERSON>, Dutch playwright, philosopher, and jurist (b. 1583)", "html": "1645 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch playwright, philosopher, and jurist (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch playwright, philosopher, and jurist (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1646", "text": "<PERSON>, English-Hungarian alchemist, chemist and metallurgist. (b. 1576)", "html": "1646 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Hungarian alchemist, chemist and metallurgist. (b. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Hungarian alchemist, chemist and metallurgist. (b. 1576)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, English general (b. 1610)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\"><PERSON></a>, English general (b. 1610)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Sir <PERSON>\"><PERSON></a>, English general (b. 1610)", "links": [{"title": "Sir <PERSON>", "link": "https://wikipedia.org/wiki/Sir_<PERSON>_<PERSON>"}]}, {"year": "1648", "text": "<PERSON>, English general (b. 1613)", "html": "1648 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English general (b. 1613)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1654", "text": "<PERSON>, Swedish lawyer and politician, Lord High Chancellor of Sweden (b. 1583)", "html": "1654 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden\" title=\"Lord High Chancellor of Sweden\">Lord High Chancellor of Sweden</a> (b. 1583)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish lawyer and politician, <a href=\"https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden\" title=\"Lord High Chancellor of Sweden\">Lord High Chancellor of Sweden</a> (b. 1583)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ier<PERSON>"}, {"title": "Lord High Chancellor of Sweden", "link": "https://wikipedia.org/wiki/Lord_High_Chancellor_of_Sweden"}]}, {"year": "1665", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian painter (b. 1638)", "html": "1665 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Italian painter (b. 1638)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, 1st Baron <PERSON> of Stratton, English soldier and politician, Lord Lieutenant of Ireland (b. 1602)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Stratton\" title=\"<PERSON>, 1st Baron Berkeley of Stratton\"><PERSON>, 1st Baron <PERSON> of Stratton</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1602)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Stratton\" title=\"<PERSON>, 1st Baron <PERSON> of Stratton\"><PERSON>, 1st Baron <PERSON> of Stratton</a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1602)", "links": [{"title": "<PERSON>, 1st Baron <PERSON> of Stratton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Baron_<PERSON>_of_Stratton"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1735", "text": "<PERSON>, English landowner and cricketer (b. 1701)", "html": "1735 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English landowner and cricketer (b. 1701)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English landowner and cricketer (b. 1701)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1757", "text": "<PERSON>, English psychologist and philosopher (b. 1705)", "html": "1757 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English psychologist and philosopher (b. 1705)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(philosopher)\" title=\"<PERSON> (philosopher)\"><PERSON></a>, English psychologist and philosopher (b. 1705)", "links": [{"title": "<PERSON> (philosopher)", "link": "https://wikipedia.org/wiki/<PERSON>(philosopher)"}]}, {"year": "1761", "text": "<PERSON><PERSON>, Spanish colonial governor of Cartagena de Indias (Colombia, 1739-1742); of Spanish Florida (1749-1752); and of Yucatán (Mexico, 1754-1758) (b. 1693)", "html": "1761 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish colonial governor of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> (Colombia, 1739-1742); of Spanish Florida (1749-1752); and of Yucatán (Mexico, 1754-1758) (b. 1693)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish colonial governor of <a href=\"https://wikipedia.org/wiki/Cartagena_de_Indias\" class=\"mw-redirect\" title=\"Cartagena de Indias\">Cartagena de Indias</a> (Colombia, 1739-1742); of Spanish Florida (1749-1752); and of Yucatán (Mexico, 1754-1758) (b. 1693)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Cartagena de Indias", "link": "https://wikipedia.org/wiki/Cartagena_de_Indias"}]}, {"year": "1784", "text": "<PERSON><PERSON><PERSON><PERSON>, Spanish priest and missionary (b. 1713)", "html": "1784 - <a href=\"https://wikipedia.org/wiki/Jun%C3%ADpero_Serra\" title=\"Junípero Serra\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish priest and missionary (b. 1713)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jun%C3%ADpero_Serra\" title=\"Junípero Serra\"><PERSON><PERSON><PERSON><PERSON></a>, Spanish priest and missionary (b. 1713)", "links": [{"title": "Juní<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jun%C3%ADpero_Serra"}]}, {"year": "1793", "text": "<PERSON>, <PERSON><PERSON><PERSON>, French general (b. 1740)", "html": "1793 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON><PERSON><PERSON> Custine\"><PERSON>, <PERSON><PERSON><PERSON></a>, French general (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>, <PERSON><PERSON><PERSON> Custine\"><PERSON>, <PERSON><PERSON><PERSON></a>, French general (b. 1740)", "links": [{"title": "<PERSON>, <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1805", "text": "<PERSON>, Scottish church leader and author (b. 1722)", "html": "1805 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish church leader and author (b. 1722)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish church leader and author (b. 1722)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1818", "text": "<PERSON>, American fur trader, founded Chicago (b. 1750)", "html": "1818 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Point_du_Sable\" title=\"Jean <PERSON> Point du Sable\"><PERSON> Sable</a>, American fur trader, founded Chicago (b. 1750)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Point_du_Sable\" title=\"Jean <PERSON> Point du Sable\"><PERSON> du Sable</a>, American fur trader, founded Chicago (b. 1750)", "links": [{"title": "<PERSON> Sable", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_du_<PERSON>"}]}, {"year": "1820", "text": "<PERSON>, American surveyor and urban planner (b. 1754)", "html": "1820 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and urban planner (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surveyor and urban planner (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, English thief", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English thief", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English thief", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1839", "text": "<PERSON>, English geologist and engineer (b. 1769)", "html": "1839 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist and engineer (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(geologist)\" title=\"<PERSON> (geologist)\"><PERSON></a>, English geologist and engineer (b. 1769)", "links": [{"title": "<PERSON> (geologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(geologist)"}]}, {"year": "1888", "text": "<PERSON>, Finnish poet and journalist (b. 1835)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and journalist (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish poet and journalist (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, English missionary and linguist (b. 1814)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and linguist (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English missionary and linguist (b. 1814)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, English economist and philosopher (b. 1838)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English economist and philosopher (b. 1838)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American journalist and architect, co-designed Central Park (b. 1822)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and architect, co-designed <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a> (b. 1822)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and architect, co-designed <a href=\"https://wikipedia.org/wiki/Central_Park\" title=\"Central Park\">Central Park</a> (b. 1822)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Central Park", "link": "https://wikipedia.org/wiki/Central_Park"}]}, {"year": "1919", "text": "<PERSON>, Austrian fencer and cyclist (b. 1872)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer and cyclist (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian fencer and cyclist (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1934", "text": "<PERSON><PERSON>, Welsh-Australian geologist and explorer (b. 1858)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Edgeworth_David\" title=\"Edgeworth David\"><PERSON><PERSON></a>, Welsh-Australian geologist and explorer (b. 1858)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Edgeworth_David\" title=\"Edgeworth David\"><PERSON><PERSON></a>, Welsh-Australian geologist and explorer (b. 1858)", "links": [{"title": "<PERSON><PERSON> David", "link": "https://wikipedia.org/wiki/Edge<PERSON>_David"}]}, {"year": "1937", "text": "<PERSON>, Australian politician, 28th Premier of Victoria (b. 1854)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian politician, 28th <a href=\"https://wikipedia.org/wiki/Premier_of_Victoria\" title=\"Premier of Victoria\">Premier of Victoria</a> (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Premier of Victoria", "link": "https://wikipedia.org/wiki/Premier_of_Victoria"}]}, {"year": "1943", "text": "<PERSON>, Estonian architect (b. 1870)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Estonian architect (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON> of Bulgaria (b. 1894)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a> (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a> (b. 1894)", "links": [{"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria"}]}, {"year": "1955", "text": "<PERSON><PERSON>, African-American kidnapping and lynching victim (b. 1941)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American kidnapping and lynching victim (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, African-American kidnapping and lynching victim (b. 1941)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Em<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON><PERSON><PERSON>, Czech-American composer and educator (b. 1890)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American composer and educator (b. 1890)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C5%AF\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Czech-American composer and educator (b. 1890)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Martin%C5%AF"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, Italian-Israeli physicist and mathematician (b. 1909)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Israeli physicist and mathematician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian-Israeli physicist and mathematician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>h"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Greek architect and academic (b. 1887)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek architect and academic (b. 1887)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek architect and academic (b. 1887)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Israeli author and scholar (b. 1889)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Reuvein_Margolies\" class=\"mw-redirect\" title=\"Reuvein Margolies\"><PERSON><PERSON><PERSON></a>, Israeli author and scholar (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Reuvein_Margolies\" class=\"mw-redirect\" title=\"Reuvein Margolies\"><PERSON><PERSON><PERSON></a>, Israeli author and scholar (b. 1889)", "links": [{"title": "Reuvein <PERSON>", "link": "https://wikipedia.org/wiki/Reuvein_Margolies"}]}, {"year": "1972", "text": "<PERSON> Gloucester (b. 1941)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Gloucester\" title=\"Prince <PERSON> of Gloucester\">Prince <PERSON> of Gloucester</a> (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Prince_<PERSON>_of_Gloucester\" title=\"Prince <PERSON> of Gloucester\">Prince <PERSON> of Gloucester</a> (b. 1941)", "links": [{"title": "Prince <PERSON> of Gloucester", "link": "https://wikipedia.org/wiki/Prince_<PERSON>_<PERSON>_Gloucester"}]}, {"year": "1975", "text": "<PERSON>, Austrian sculptor (b. 1907)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian sculptor (b. 1907)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian sculptor (b. 1907)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American actress (b. 1958)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress (b. 1958)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, American historian and journalist (b. 1899)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and journalist (b. 1899)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, English actor (b. 1927)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English actor (b. 1927)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Hungarian footballer, coach, and manager (b. 1899)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/B%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer, coach, and manager (b. 1899)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian footballer, coach, and manager (b. 1899)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/B%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, South African cricketer (b. 1911)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African cricketer (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, Egyptian general and politician, 1st President of Egypt (b. 1901)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Egyptian general and politician, 1st <a href=\"https://wikipedia.org/wiki/President_of_Egypt\" title=\"President of Egypt\">President of Egypt</a> (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of Egypt", "link": "https://wikipedia.org/wiki/President_of_Egypt"}]}, {"year": "1985", "text": "<PERSON>, American actress and screenwriter (b. 1896)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and screenwriter (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American photographer and journalist (b. 1903)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(photographer)\" title=\"<PERSON> (photographer)\"><PERSON></a>, American photographer and journalist (b. 1903)", "links": [{"title": "<PERSON> (photographer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(photographer)"}]}, {"year": "1987", "text": "<PERSON>, Irish actor, director, and screenwriter (b. 1906)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, director, and screenwriter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish actor, director, and screenwriter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, Canadian union leader and politician, 43rd Secretary of State for Canada (b. 1918)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian union leader and politician, 43rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian union leader and politician, 43rd <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Canada\" title=\"Secretary of State for Canada\">Secretary of State for Canada</a> (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for Canada", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Canada"}]}, {"year": "1988", "text": "<PERSON>, American author and screenwriter (b. 1919)", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American author and illustrator (b. 1950)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, Belgian author and illustrator (b. 1913)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian author and illustrator (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Greek director and screenwriter (b. 1913)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Alekos_Sakellarios\" title=\"<PERSON>ek<PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and screenwriter (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Alek<PERSON>_Sakellarios\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek director and screenwriter (b. 1913)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Alekos_Sakellarios"}]}, {"year": "1993", "text": "<PERSON>, American poet and academic (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American poet and academic (b. 1914)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_(poet)"}]}, {"year": "1995", "text": "<PERSON>, American rodeo performer and painter (b. 1906)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rodeo performer and painter (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American rodeo performer and painter (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, German scientist and author (b. 1929)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist and author (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German scientist and author (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, French actor (b. 1914)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Hungarian-Australian mathematician and academic (b. 1910)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian mathematician and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian mathematician and academic (b. 1910)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Hungarian-Australian mathematician and academic (b. 1911)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian mathematician and academic (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Australian mathematician and academic (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Estonian shot putter and discus thrower (b. 1922)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Lipp\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian shot putter and discus thrower (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Li<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian shot putter and discus thrower (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/He<PERSON>_Lipp"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian educator and politician (b. 1963)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>u<PERSON>au\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian educator and politician (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Beno%C3%AEt_<PERSON>u<PERSON>au\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian educator and politician (b. 1963)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beno%C3%AEt_Sauvageau"}]}, {"year": "2006", "text": "<PERSON>, American physicist and academic, Nobel Prize laureate (b. 1932)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physics\" title=\"Nobel Prize in Physics\">Nobel Prize</a> laureate (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physics", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physics"}]}, {"year": "2007", "text": "<PERSON>, American businessman, founded Nautilus, Inc. and MedX Corporation (b. 1926)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Nautilus,_Inc.\" class=\"mw-redirect\" title=\"Nautilus, Inc.\">Nautilus, Inc.</a> and <a href=\"https://wikipedia.org/wiki/MedX_Corporation\" title=\"MedX Corporation\">MedX Corporation</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(inventor)\" title=\"<PERSON> (inventor)\"><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Nautilus,_Inc.\" class=\"mw-redirect\" title=\"Nautilus, Inc.\">Nautilus, Inc.</a> and <a href=\"https://wikipedia.org/wiki/MedX_Corporation\" title=\"MedX Corporation\">MedX Corporation</a> (b. 1926)", "links": [{"title": "<PERSON> (inventor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(inventor)"}, {"title": "Nautilus, Inc.", "link": "https://wikipedia.org/wiki/Nautilus,_Inc."}, {"title": "MedX Corporation", "link": "https://wikipedia.org/wiki/MedX_Corporation"}]}, {"year": "2007", "text": "<PERSON><PERSON>, American businessman, founded CBGB (b. 1932)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/CBGB\" title=\"CBGB\">CBGB</a> (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/CBGB\" title=\"CBGB\">CBGB</a> (b. 1932)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "CBGB", "link": "https://wikipedia.org/wiki/CBGB"}]}, {"year": "2007", "text": "<PERSON>, American engineer and businessman, founded AeroVironment (b. 1925)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/AeroVironment\" title=\"AeroVironment\">AeroVironment</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/AeroVironment\" title=\"AeroVironment\">AeroVironment</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "AeroVironment", "link": "https://wikipedia.org/wiki/AeroVironment"}]}, {"year": "2007", "text": "<PERSON>, Spanish journalist and author (b. 1935)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/Francisco_Umbral\" title=\"Francisco Umbral\"><PERSON></a>, Spanish journalist and author (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Francisco_Umbral\" title=\"Francisco Umbral\"><PERSON></a>, Spanish journalist and author (b. 1935)", "links": [{"title": "Francisco <PERSON>", "link": "https://wikipedia.org/wiki/Francisco_Umbral"}]}, {"year": "2007", "text": "<PERSON><PERSON>, Japanese-American actress (b. 1929)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese-American actress (b. 1929)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American race car driver (b. 1927)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Phil <PERSON>\"><PERSON></a>, American race car driver (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Phil Hill\"><PERSON></a>, American race car driver (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Phil_Hill"}]}, {"year": "2009", "text": "<PERSON>, American drummer, DJ, and producer (b. 1973)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/DJ_AM\" title=\"DJ AM\"><PERSON></a>, American drummer, DJ, and producer (b. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_AM\" title=\"DJ AM\"><PERSON></a>, American drummer, DJ, and producer (b. 1973)", "links": [{"title": "DJ AM", "link": "https://wikipedia.org/wiki/DJ_AM"}]}, {"year": "2009", "text": "<PERSON>, US Ambassador, Owner of Dell EMC, Engineer (b. 1936)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, US Ambassador, Owner of <a href=\"https://wikipedia.org/wiki/Dell_EMC\" title=\"Dell EMC\">Dell EMC</a>, Engineer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(businessman)\" class=\"mw-redirect\" title=\"<PERSON> (businessman)\"><PERSON></a>, US Ambassador, Owner of <a href=\"https://wikipedia.org/wiki/Dell_EMC\" title=\"Dell EMC\">Dell EMC</a>, Engineer (b. 1936)", "links": [{"title": "<PERSON> (businessman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(businessman)"}, {"title": "Dell EMC", "link": "https://wikipedia.org/wiki/Dell_EMC"}]}, {"year": "2010", "text": "<PERSON>, American bandleader and educator (b. 1919)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and educator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bandleader and educator (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON>, English footballer (b. 1967)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1967)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1967)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, English educator and politician (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Boyson\"><PERSON></a>, English educator and politician (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Boyson\" title=\"<PERSON> Boyson\"><PERSON></a>, English educator and politician (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rhodes_Boyson"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Canadian-American activist and author (b. 1945)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Shulamith_Firestone\" title=\"Shulamith Firestone\"><PERSON><PERSON><PERSON> Firestone</a>, Canadian-American activist and author (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shulamith_Firestone\" title=\"Shulamith Firestone\"><PERSON><PERSON><PERSON> Firestone</a>, Canadian-American activist and author (b. 1945)", "links": [{"title": "Shulamith Firestone", "link": "https://wikipedia.org/wiki/Shulamith_Firestone"}]}, {"year": "2012", "text": "<PERSON>, American author, poet, and playwright (b. 1928)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American author, poet, and playwright (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)\" title=\"<PERSON> (poet)\"><PERSON></a>, American author, poet, and playwright (b. 1928)", "links": [{"title": "<PERSON> (poet)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(poet)"}]}, {"year": "2012", "text": "<PERSON>, Polish-Israeli ophthalmologist and academic (b. 1933)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli ophthalmologist and academic (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Israeli ophthalmologist and academic (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Spanish golfer (b. 1938)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_So<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_So<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish golfer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Sota"}]}, {"year": "2013", "text": "<PERSON>, Scottish painter and academic (b. 1942)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish painter and academic (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Italian political scientist and philosopher (b. 1961)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian political scientist and philosopher (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian political scientist and philosopher (b. 1961)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "2013", "text": "<PERSON>, American businessman (b. 1926)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American baseball player and umpire (b. 1935)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and umpire (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, English footballer (b. 1938)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, Ecuadorian journalist, author, and poet (b. 1925)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz_Ycaza\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian journalist, author, and poet (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%ADaz_Ycaza\" title=\"<PERSON>\"><PERSON></a>, Ecuadorian journalist, author, and poet (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Rafael_D%C3%ADaz_Y<PERSON>za"}]}, {"year": "2014", "text": "<PERSON>, English bass guitarist (b. 1947)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass guitarist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American cryptographer and programmer (b. 1956)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American cryptographer and programmer (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)\" title=\"<PERSON> (computer scientist)\"><PERSON></a>, American cryptographer and programmer (b. 1956)", "links": [{"title": "<PERSON> (computer scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(computer_scientist)"}]}, {"year": "2014", "text": "<PERSON>, American soldier and spy (b. 1937)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and spy (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and spy (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Canadian-American ice hockey player and coach (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Al_Arbour\" title=\"Al Arbour\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Al_Arbour\" title=\"Al Arbour\"><PERSON></a>, Canadian-American ice hockey player and coach (b. 1932)", "links": [{"title": "Al Arbour", "link": "https://wikipedia.org/wiki/Al_Arbour"}]}, {"year": "2015", "text": "<PERSON>, Kosovan ethnographer, poet, and translator (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kosovan ethnographer, poet, and translator (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kosovan ethnographer, poet, and translator (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American painter and educator (b. 1937)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and educator (b. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Mexican singer and songwriter (b. 1950)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer and songwriter (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Mexican singer and songwriter (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "Mr. <PERSON>, American professional wrestler and manager (b. 1934)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Mr._<PERSON>\" title=\"Mr. <PERSON>\">Mr. <PERSON></a>, American professional wrestler and manager (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mr._<PERSON>\" title=\"Mr. <PERSON>\">Mr. <PERSON></a>, American professional wrestler and manager (b. 1934)", "links": [{"title": "Mr. <PERSON>", "link": "https://wikipedia.org/wiki/Mr._<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, French actress and model (b. 1938)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and model (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French actress and model (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>c"}]}, {"year": "2020", "text": "<PERSON><PERSON>, American actor and playwright (b. 1976)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/Chadwick_<PERSON>\" title=\"Chadwick Boseman\"><PERSON><PERSON></a>, American actor and playwright (b. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chadwick_<PERSON>\" title=\"Chadwick Boseman\"><PERSON><PERSON></a>, American actor and playwright (b. 1976)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON>, American actor (b. 1972)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/Obi_Ndefo\" title=\"<PERSON>bi Ndefo\"><PERSON><PERSON></a>, American actor (b. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>bi_Ndefo\" title=\"<PERSON>bi Ndefo\"><PERSON><PERSON></a>, American actor (b. 1972)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Obi_Ndefo"}]}, {"year": "2024", "text": "<PERSON>, American writer and journalist (b. 1957)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and journalist (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American writer and journalist (b. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}