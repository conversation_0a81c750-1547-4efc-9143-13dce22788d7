{"date": "November 26", "url": "https://wikipedia.org/wiki/November_26", "data": {"Events": [{"year": "783", "text": "The Asturian queen <PERSON><PERSON><PERSON> is held at a monastery to prevent her nephew from retaking the throne from <PERSON><PERSON><PERSON><PERSON>.", "html": "783 - The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Asturias\" title=\"Kingdom of Asturias\">Asturian</a> queen <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is held at a monastery to prevent her nephew from retaking the throne from <a href=\"https://wikipedia.org/wiki/Mauregatus_of_Asturias\" class=\"mw-redirect\" title=\"<PERSON><PERSON>gat<PERSON> of Asturias\"><PERSON><PERSON>gatus</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Kingdom_of_Asturias\" title=\"Kingdom of Asturias\">Asturian</a> queen <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> is held at a monastery to prevent her nephew from retaking the throne from <a href=\"https://wikipedia.org/wiki/Mauregatus_of_Asturias\" class=\"mw-redirect\" title=\"Mauregat<PERSON> of Asturias\"><PERSON><PERSON>gat<PERSON></a>.", "links": [{"title": "Kingdom of Asturias", "link": "https://wikipedia.org/wiki/Kingdom_of_Asturias"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adosinda"}, {"title": "<PERSON><PERSON><PERSON><PERSON> of Asturias", "link": "https://wikipedia.org/wiki/Mauregat<PERSON>_of_Asturias"}]}, {"year": "1161", "text": "Battle of Caishi: A Song dynasty fleet fights a naval engagement with Jin dynasty ships on the Yangtze river during the Jin-Song Wars.", "html": "1161 - <a href=\"https://wikipedia.org/wiki/Battle_of_Caishi\" title=\"Battle of Caishi\">Battle of Caishi</a>: A <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> fleet fights a naval engagement with <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a> ships on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river during the <a href=\"https://wikipedia.org/wiki/Jin%E2%80%93Song_Wars\" class=\"mw-redirect\" title=\"Jin-Song Wars\">Jin-Song Wars</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Caishi\" title=\"Battle of Caishi\">Battle of Caishi</a>: A <a href=\"https://wikipedia.org/wiki/Song_dynasty\" title=\"Song dynasty\">Song dynasty</a> fleet fights a naval engagement with <a href=\"https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)\" title=\"Jin dynasty (1115-1234)\">Jin dynasty</a> ships on the <a href=\"https://wikipedia.org/wiki/Yangtze\" title=\"Yangtze\">Yangtze</a> river during the <a href=\"https://wikipedia.org/wiki/Jin%E2%80%93Song_Wars\" class=\"mw-redirect\" title=\"Jin-Song Wars\">Jin-Song Wars</a>.", "links": [{"title": "Battle of Caishi", "link": "https://wikipedia.org/wiki/Battle_of_Caishi"}, {"title": "Song dynasty", "link": "https://wikipedia.org/wiki/Song_dynasty"}, {"title": "Jin dynasty (1115-1234)", "link": "https://wikipedia.org/wiki/Jin_dynasty_(1115%E2%80%931234)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Yang<PERSON>e"}, {"title": "Jin-Song Wars", "link": "https://wikipedia.org/wiki/Jin%E2%80%93Song_Wars"}]}, {"year": "1476", "text": "<PERSON> the Impaler defeats <PERSON><PERSON><PERSON> with the help of <PERSON> the <PERSON> and <PERSON> and becomes the ruler of Wallachia for the third time.", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Impaler\" title=\"<PERSON> the Impaler\"><PERSON> the Impaler</a> defeats <a href=\"https://wikipedia.org/wiki/Basarab_Laiot%C4%83_cel_B%C4%83tr%C3%A2n\" class=\"mw-redirect\" title=\"Basarab Laiotă cel Bătrân\"><PERSON><PERSON><PERSON></a> with the help of <a href=\"https://wikipedia.org/wiki/<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_V_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a> and becomes the ruler of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a> for the third time.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_the_Impaler\" title=\"<PERSON> the Impaler\"><PERSON> the Impaler</a> defeats <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Laiot%C4%83_cel_B%C4%83tr%C3%A2n\" class=\"mw-redirect\" title=\"Basarab Laiotă cel Bătrân\"><PERSON><PERSON><PERSON></a> with the help of <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>_V_B%C3%A1thory\" title=\"<PERSON>\"><PERSON></a> and becomes the ruler of <a href=\"https://wikipedia.org/wiki/Wallachia\" title=\"Wallachia\">Wallachia</a> for the third time.", "links": [{"title": "<PERSON> the Impaler", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON><PERSON>r"}, {"title": "Basarab <PERSON> cel Bătrân", "link": "https://wikipedia.org/wiki/Basarab_Laiot%C4%83_cel_B%C4%83tr%C3%A2n"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Stephen_V_B%C3%A1thory"}, {"title": "Wallachia", "link": "https://wikipedia.org/wiki/Wallachia"}]}, {"year": "1778", "text": "In the Hawaiian Islands, Captain <PERSON> becomes the first European to visit Maui.", "html": "1778 - In the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>, Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to visit <a href=\"https://wikipedia.org/wiki/Maui\" title=\"Maui\">Maui</a>.", "no_year_html": "In the <a href=\"https://wikipedia.org/wiki/Hawaiian_Islands\" title=\"Hawaiian Islands\">Hawaiian Islands</a>, Captain <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first European to visit <a href=\"https://wikipedia.org/wiki/Maui\" title=\"Maui\">Maui</a>.", "links": [{"title": "Hawaiian Islands", "link": "https://wikipedia.org/wiki/Hawaiian_Islands"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Maui"}]}, {"year": "1789", "text": "A national Thanksgiving Day is observed in the United States as proclaimed by President <PERSON> at the request of Congress.", "html": "1789 - A national <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving Day</a> is observed in the United States as proclaimed by President <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"<PERSON>\"><PERSON></a> at the request of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>.", "no_year_html": "A national <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving Day</a> is observed in the United States as proclaimed by President <a href=\"https://wikipedia.org/wiki/George_Washington\" title=\"<PERSON>\"><PERSON></a> at the request of <a href=\"https://wikipedia.org/wiki/United_States_Congress\" title=\"United States Congress\">Congress</a>.", "links": [{"title": "Thanksgiving (United States)", "link": "https://wikipedia.org/wiki/Thanksgiving_(United_States)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/George_Washington"}, {"title": "United States Congress", "link": "https://wikipedia.org/wiki/United_States_Congress"}]}, {"year": "1805", "text": "Official opening of <PERSON>'s Pontcysyllte Aqueduct.", "html": "1805 - Official opening of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Pontcysyllte_Aqueduct\" title=\"Pontcysyllte Aqueduct\">Pontcysyllte Aqueduct</a>.", "no_year_html": "Official opening of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s <a href=\"https://wikipedia.org/wiki/Pontcysyllte_Aqueduct\" title=\"Pontcysyllte Aqueduct\">Pontcysyllte Aqueduct</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Pontcysyllte Aqueduct", "link": "https://wikipedia.org/wiki/Pontcysyllte_Aqueduct"}]}, {"year": "1812", "text": "The Battle of Berezina begins during <PERSON>'s retreat from Russia.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/Battle_of_Berezina\" title=\"Battle of Berezina\">Battle of Berezina</a> begins during <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s retreat from <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Battle_of_Berezina\" title=\"Battle of Berezina\">Battle of Berezina</a> begins during <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s retreat from <a href=\"https://wikipedia.org/wiki/Russia\" title=\"Russia\">Russia</a>.", "links": [{"title": "Battle of Berezina", "link": "https://wikipedia.org/wiki/Battle_of_Berezina"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Napoleon"}, {"title": "Russia", "link": "https://wikipedia.org/wiki/Russia"}]}, {"year": "1852", "text": "An earthquake as high as magnitude 8.8 rocks the Banda Sea, triggering a tsunami and killing at least 60 in the Dutch East Indies.", "html": "1852 - An earthquake as high as <a href=\"https://wikipedia.org/wiki/1852_Banda_Sea_earthquake\" title=\"1852 Banda Sea earthquake\">magnitude 8.8 rocks the Banda Sea</a>, triggering a tsunami and killing at least 60 in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "no_year_html": "An earthquake as high as <a href=\"https://wikipedia.org/wiki/1852_Banda_Sea_earthquake\" title=\"1852 Banda Sea earthquake\">magnitude 8.8 rocks the Banda Sea</a>, triggering a tsunami and killing at least 60 in the <a href=\"https://wikipedia.org/wiki/Dutch_East_Indies\" title=\"Dutch East Indies\">Dutch East Indies</a>.", "links": [{"title": "1852 Banda Sea earthquake", "link": "https://wikipedia.org/wiki/1852_Banda_Sea_earthquake"}, {"title": "Dutch East Indies", "link": "https://wikipedia.org/wiki/Dutch_East_Indies"}]}, {"year": "1863", "text": "United States President <PERSON> proclaims November 26 as a national Thanksgiving Day, to be celebrated annually on the final Thursday of November.  Following the Franksgiving controversy from 1939 to 1941, it has been observed on the fourth Thursday in 1942 and subsequent years.", "html": "1863 - United States President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims November 26 as a national <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving Day</a>, to be celebrated annually on the final Thursday of November. Following the <a href=\"https://wikipedia.org/wiki/Franksgiving\" title=\"Franksgiving\">Franksgiving</a> controversy from 1939 to 1941, it has been observed on the fourth Thursday in 1942 and subsequent years.", "no_year_html": "United States President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> proclaims November 26 as a national <a href=\"https://wikipedia.org/wiki/Thanksgiving_(United_States)\" title=\"Thanksgiving (United States)\">Thanksgiving Day</a>, to be celebrated annually on the final Thursday of November. Following the <a href=\"https://wikipedia.org/wiki/Franksgiving\" title=\"Franksgiving\">Franksgiving</a> controversy from 1939 to 1941, it has been observed on the fourth Thursday in 1942 and subsequent years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Thanksgiving (United States)", "link": "https://wikipedia.org/wiki/Thanksgiving_(United_States)"}, {"title": "Franksgiving", "link": "https://wikipedia.org/wiki/Franksgiving"}]}, {"year": "1865", "text": "Battle of Papudo: A Spanish navy schooner is defeated by a Chilean corvette north of Valparaíso, Chile.", "html": "1865 - <a href=\"https://wikipedia.org/wiki/Battle_of_Papudo\" title=\"Battle of Papudo\">Battle of Papudo</a>: A Spanish navy schooner is defeated by a Chilean corvette north of <a href=\"https://wikipedia.org/wiki/Valpara%C3%ADso\" title=\"Valparaíso\">Valparaíso</a>, Chile.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Battle_of_Papudo\" title=\"Battle of Papudo\">Battle of Papudo</a>: A Spanish navy schooner is defeated by a Chilean corvette north of <a href=\"https://wikipedia.org/wiki/Valpara%C3%ADso\" title=\"Valparaíso\">Valparaíso</a>, Chile.", "links": [{"title": "Battle of Papudo", "link": "https://wikipedia.org/wiki/Battle_of_Papudo"}, {"title": "Valparaíso", "link": "https://wikipedia.org/wiki/Valpara%C3%ADso"}]}, {"year": "1914", "text": "HMS Bulwark is destroyed by a large internal explosion with the loss of 741 men while at anchor near Sheerness.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/HMS_Bulwark_(1899)\" title=\"HMS Bulwark (1899)\">HMS <i>Bulwark</i></a> is destroyed by a large internal explosion with the loss of 741 men while at anchor near <a href=\"https://wikipedia.org/wiki/Sheerness\" title=\"Sheerness\">Sheerness</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/HMS_Bulwark_(1899)\" title=\"HMS Bulwark (1899)\">HMS <i>Bulwark</i></a> is destroyed by a large internal explosion with the loss of 741 men while at anchor near <a href=\"https://wikipedia.org/wiki/Sheerness\" title=\"Sheerness\">Sheerness</a>.", "links": [{"title": "HMS Bulwark (1899)", "link": "https://wikipedia.org/wiki/HMS_Bulwark_(1899)"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sheerness"}]}, {"year": "1917", "text": "The Manchester Guardian publishes the 1916 secret Sykes-Picot Agreement between the United Kingdom and France.", "html": "1917 - <i><a href=\"https://wikipedia.org/wiki/The_Guardian\" title=\"The Guardian\">The Manchester Guardian</a></i> publishes the 1916 secret <a href=\"https://wikipedia.org/wiki/Sykes-Picot_Agreement\" class=\"mw-redirect\" title=\"Sykes-Picot Agreement\">Sykes-Picot Agreement</a> between the United Kingdom and France.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Guardian\" title=\"The Guardian\">The Manchester Guardian</a></i> publishes the 1916 secret <a href=\"https://wikipedia.org/wiki/Sykes-Picot_Agreement\" class=\"mw-redirect\" title=\"Sykes-Picot Agreement\">Sykes-Picot Agreement</a> between the United Kingdom and France.", "links": [{"title": "The Guardian", "link": "https://wikipedia.org/wiki/The_Guardian"}, {"title": "Sykes-Picot Agreement", "link": "https://wikipedia.org/wiki/Sykes-Picot_Agreement"}]}, {"year": "1917", "text": "The National Hockey League is formed, with the Montreal Canadiens, Montreal Wanderers, Ottawa Senators, Quebec Bulldogs, and Toronto Arenas as its first teams.", "html": "1917 - The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> is formed, with the <a href=\"https://wikipedia.org/wiki/Montreal_Canadiens\" title=\"Montreal Canadiens\">Montreal Canadiens</a>, <a href=\"https://wikipedia.org/wiki/Montreal_Wanderers\" title=\"Montreal Wanderers\">Montreal Wanderers</a>, <a href=\"https://wikipedia.org/wiki/Ottawa_Senators_(original)\" title=\"Ottawa Senators (original)\">Ottawa Senators</a>, <a href=\"https://wikipedia.org/wiki/Quebec_Bulldogs\" title=\"Quebec Bulldogs\">Quebec Bulldogs</a>, and <a href=\"https://wikipedia.org/wiki/Toronto_Arenas\" title=\"Toronto Arenas\">Toronto Arenas</a> as its first teams.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/National_Hockey_League\" title=\"National Hockey League\">National Hockey League</a> is formed, with the <a href=\"https://wikipedia.org/wiki/Montreal_Canadiens\" title=\"Montreal Canadiens\">Montreal Canadiens</a>, <a href=\"https://wikipedia.org/wiki/Montreal_Wanderers\" title=\"Montreal Wanderers\">Montreal Wanderers</a>, <a href=\"https://wikipedia.org/wiki/Ottawa_Senators_(original)\" title=\"Ottawa Senators (original)\">Ottawa Senators</a>, <a href=\"https://wikipedia.org/wiki/Quebec_Bulldogs\" title=\"Quebec Bulldogs\">Quebec Bulldogs</a>, and <a href=\"https://wikipedia.org/wiki/Toronto_Arenas\" title=\"Toronto Arenas\">Toronto Arenas</a> as its first teams.", "links": [{"title": "National Hockey League", "link": "https://wikipedia.org/wiki/National_Hockey_League"}, {"title": "Montreal Canadiens", "link": "https://wikipedia.org/wiki/Montreal_Canadiens"}, {"title": "Montreal Wanderers", "link": "https://wikipedia.org/wiki/Montreal_Wanderers"}, {"title": "Ottawa Senators (original)", "link": "https://wikipedia.org/wiki/Ottawa_Senators_(original)"}, {"title": "Quebec Bulldogs", "link": "https://wikipedia.org/wiki/Quebec_Bulldogs"}, {"title": "Toronto Arenas", "link": "https://wikipedia.org/wiki/Toronto_Arenas"}]}, {"year": "1918", "text": "The Montenegrin Podgorica Assembly votes for a \"union of the people\", declaring assimilation into the Kingdom of Serbia.", "html": "1918 - The Montenegrin <a href=\"https://wikipedia.org/wiki/Podgorica_Assembly\" title=\"Podgorica Assembly\">Podgorica Assembly</a> votes for a \"union of the people\", declaring assimilation into the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a>.", "no_year_html": "The Montenegrin <a href=\"https://wikipedia.org/wiki/Podgorica_Assembly\" title=\"Podgorica Assembly\">Podgorica Assembly</a> votes for a \"union of the people\", declaring assimilation into the <a href=\"https://wikipedia.org/wiki/Kingdom_of_Serbia\" title=\"Kingdom of Serbia\">Kingdom of Serbia</a>.", "links": [{"title": "Podgorica Assembly", "link": "https://wikipedia.org/wiki/Podgorica_Assembly"}, {"title": "Kingdom of Serbia", "link": "https://wikipedia.org/wiki/Kingdom_of_Serbia"}]}, {"year": "1920", "text": "Ukrainian War of Independence: The Red Army launches a surprise attack against the Makhnovshchina.", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> launches a <a href=\"https://wikipedia.org/wiki/Bolshevik%E2%80%93Makhnovist_conflict#Surprise_attack\" title=\"Bolshevik-Makhnovist conflict\">surprise attack</a> against the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ukrainian_War_of_Independence\" title=\"Ukrainian War of Independence\">Ukrainian War of Independence</a>: The <a href=\"https://wikipedia.org/wiki/Red_Army\" title=\"Red Army\">Red Army</a> launches a <a href=\"https://wikipedia.org/wiki/Bolshevik%E2%80%93Makhnovist_conflict#Surprise_attack\" title=\"Bolshevik-Makhnovist conflict\">surprise attack</a> against the <a href=\"https://wikipedia.org/wiki/Makhnovshchina\" title=\"Makhnovshchina\">Makhnovshchina</a>.", "links": [{"title": "Ukrainian War of Independence", "link": "https://wikipedia.org/wiki/Ukrainian_War_of_Independence"}, {"title": "Red Army", "link": "https://wikipedia.org/wiki/Red_Army"}, {"title": "Bolshevik-Makhnovist conflict", "link": "https://wikipedia.org/wiki/Bolshevik%E2%80%93Makhnovist_conflict#Surprise_attack"}, {"title": "Makhnovshchina", "link": "https://wikipedia.org/wiki/Makhnovshchina"}]}, {"year": "1922", "text": "<PERSON> and <PERSON> become the first people to enter the tomb of <PERSON><PERSON><PERSON> in over 3,000 years.", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Carnarvon\" title=\"<PERSON>, 5th Earl of Carnarvon\">Lord <PERSON></a> become the first people to enter the <a href=\"https://wikipedia.org/wiki/Tomb_of_Tutankhamun\" title=\"Tomb of Tutankhamun\">tomb of <PERSON><PERSON><PERSON>n</a> in over 3,000 years.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> and <a href=\"https://wikipedia.org/wiki/<PERSON>,_5th_Earl_of_Carnarvon\" title=\"<PERSON>, 5th Earl of Carnarvon\">Lord <PERSON></a> become the first people to enter the <a href=\"https://wikipedia.org/wiki/Tomb_of_Tutankhamun\" title=\"Tomb of Tutankhamun\">tomb of <PERSON><PERSON><PERSON></a> in over 3,000 years.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON>, 5th Earl of Carnarvon", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_5th_Earl_of_Carnarvon"}, {"title": "Tomb of Tutankhamun", "link": "https://wikipedia.org/wiki/Tomb_of_Tutankhamun"}]}, {"year": "1922", "text": "The Toll of the Sea debuts as the first general release film to use two-tone Technicolor. (The Gulf Between was the first film to do so, but it was not widely distributed.)", "html": "1922 - <i><a href=\"https://wikipedia.org/wiki/The_Toll_of_the_Sea\" title=\"The Toll of the Sea\">The Toll of the Sea</a></i> debuts as the first general release film to use two-tone <a href=\"https://wikipedia.org/wiki/Technicolor\" title=\"Technicolor\">Technicolor</a>. (<i><a href=\"https://wikipedia.org/wiki/The_Gulf_Between_(1917_film)\" class=\"mw-redirect\" title=\"The Gulf Between (1917 film)\">The Gulf Between</a></i> was the first film to do so, but it was not widely distributed.)", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Toll_of_the_Sea\" title=\"The Toll of the Sea\">The Toll of the Sea</a></i> debuts as the first general release film to use two-tone <a href=\"https://wikipedia.org/wiki/Technicolor\" title=\"Technicolor\">Technicolor</a>. (<i><a href=\"https://wikipedia.org/wiki/The_Gulf_Between_(1917_film)\" class=\"mw-redirect\" title=\"The Gulf Between (1917 film)\">The Gulf Between</a></i> was the first film to do so, but it was not widely distributed.)", "links": [{"title": "The Toll of the Sea", "link": "https://wikipedia.org/wiki/The_Toll_of_the_Sea"}, {"title": "Technicolor", "link": "https://wikipedia.org/wiki/Technicolor"}, {"title": "The Gulf Between (1917 film)", "link": "https://wikipedia.org/wiki/The_Gulf_Between_(1917_film)"}]}, {"year": "1924", "text": "The Mongolian People's Republic is officially established after a new constitution, passed by the first State Great <PERSON>al, abolishes the monarchy.", "html": "1924 - The <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolian People's Republic</a> is officially established after a new <a href=\"https://wikipedia.org/wiki/Constitutions_of_the_Mongolian_People%27s_Republic\" title=\"Constitutions of the Mongolian People's Republic\">constitution</a>, passed by the first <a href=\"https://wikipedia.org/wiki/State_Great_Khural\" title=\"State Great Khural\">State Great Khural</a>, abolishes the <a href=\"https://wikipedia.org/wiki/Bogd_Khanate_of_Mongolia\" title=\"Bogd Khanate of Mongolia\">monarchy</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mongolian_People%27s_Republic\" title=\"Mongolian People's Republic\">Mongolian People's Republic</a> is officially established after a new <a href=\"https://wikipedia.org/wiki/Constitutions_of_the_Mongolian_People%27s_Republic\" title=\"Constitutions of the Mongolian People's Republic\">constitution</a>, passed by the first <a href=\"https://wikipedia.org/wiki/State_Great_Khural\" title=\"State Great Khural\">State Great Khural</a>, abolishes the <a href=\"https://wikipedia.org/wiki/Bogd_Khanate_of_Mongolia\" title=\"Bogd Khanate of Mongolia\">monarchy</a>.", "links": [{"title": "Mongolian People's Republic", "link": "https://wikipedia.org/wiki/Mongolian_People%27s_Republic"}, {"title": "Constitutions of the Mongolian People's Republic", "link": "https://wikipedia.org/wiki/Constitutions_of_the_Mongolian_People%27s_Republic"}, {"title": "State Great Khural", "link": "https://wikipedia.org/wiki/State_Great_<PERSON><PERSON><PERSON>"}, {"title": "Bogd Khanate of Mongolia", "link": "https://wikipedia.org/wiki/Bogd_Khanate_of_Mongolia"}]}, {"year": "1939", "text": "Shelling of Mainila: The Soviet Army orchestrates an incident which is used to justify the start of the Winter War with Finland four days later.", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Shelling_of_Mainila\" title=\"Shelling of Mainila\">Shelling of Mainila</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet Army</a> orchestrates an incident which is used to justify the start of the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> with Finland four days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shelling_of_Mainila\" title=\"Shelling of Mainila\">Shelling of Mainila</a>: The <a href=\"https://wikipedia.org/wiki/Soviet_Army\" title=\"Soviet Army\">Soviet Army</a> orchestrates an incident which is used to justify the start of the <a href=\"https://wikipedia.org/wiki/Winter_War\" title=\"Winter War\">Winter War</a> with Finland four days later.", "links": [{"title": "Shelling of Mainila", "link": "https://wikipedia.org/wiki/Shelling_of_Mainila"}, {"title": "Soviet Army", "link": "https://wikipedia.org/wiki/Soviet_Army"}, {"title": "Winter War", "link": "https://wikipedia.org/wiki/Winter_War"}]}, {"year": "1941", "text": "World War II: The Hull note is given to the Japanese ambassador, demanding that Japan withdraw from China and French Indochina, in return for which the United States would lift economic sanctions. On the same day, Japan's 1st Air Fleet departs Hitokappu Bay for Hawaii.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Hull_note\" title=\"Hull note\">Hull note</a> is given to the Japanese ambassador, demanding that Japan withdraw from China and <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, in return for which the United States would lift economic sanctions. On the same day, Japan's 1st Air Fleet departs <a href=\"https://wikipedia.org/wiki/Kasatka_Bay\" title=\"Kasatka Bay\">Hitokappu Bay</a> for Hawaii.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: The <a href=\"https://wikipedia.org/wiki/Hull_note\" title=\"Hull note\">Hull note</a> is given to the Japanese ambassador, demanding that Japan withdraw from China and <a href=\"https://wikipedia.org/wiki/French_Indochina\" title=\"French Indochina\">French Indochina</a>, in return for which the United States would lift economic sanctions. On the same day, Japan's 1st Air Fleet departs <a href=\"https://wikipedia.org/wiki/Kasatka_Bay\" title=\"Kasatka Bay\">Hitokappu Bay</a> for Hawaii.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Hull note", "link": "https://wikipedia.org/wiki/Hull_note"}, {"title": "French Indochina", "link": "https://wikipedia.org/wiki/French_Indochina"}, {"title": "Kasatka Bay", "link": "https://wikipedia.org/wiki/Kasatka_Bay"}]}, {"year": "1942", "text": "World War II: Yugoslav Partisans convene the first meeting of the Anti-Fascist Council for the National Liberation of Yugoslavia at Bihać in northwestern Bosnia.", "html": "1942 - World War II: <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> convene the first meeting of the <a href=\"https://wikipedia.org/wiki/Anti-Fascist_Council_for_the_National_Liberation_of_Yugoslavia\" title=\"Anti-Fascist Council for the National Liberation of Yugoslavia\">Anti-Fascist Council for the National Liberation of Yugoslavia</a> at <a href=\"https://wikipedia.org/wiki/Biha%C4%87\" title=\"Bihać\">Bihać</a> in northwestern <a href=\"https://wikipedia.org/wiki/Bosnia_(region)\" title=\"Bosnia (region)\">Bosnia</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Yugoslav_Partisans\" title=\"Yugoslav Partisans\">Yugoslav Partisans</a> convene the first meeting of the <a href=\"https://wikipedia.org/wiki/Anti-Fascist_Council_for_the_National_Liberation_of_Yugoslavia\" title=\"Anti-Fascist Council for the National Liberation of Yugoslavia\">Anti-Fascist Council for the National Liberation of Yugoslavia</a> at <a href=\"https://wikipedia.org/wiki/Biha%C4%87\" title=\"Bihać\">Bihać</a> in northwestern <a href=\"https://wikipedia.org/wiki/Bosnia_(region)\" title=\"Bosnia (region)\">Bosnia</a>.", "links": [{"title": "Yugoslav Partisans", "link": "https://wikipedia.org/wiki/Yugoslav_Partisans"}, {"title": "Anti-Fascist Council for the National Liberation of Yugoslavia", "link": "https://wikipedia.org/wiki/Anti-Fascist_Council_for_the_National_Liberation_of_Yugoslavia"}, {"title": "Bihać", "link": "https://wikipedia.org/wiki/Biha%C4%87"}, {"title": "Bosnia (region)", "link": "https://wikipedia.org/wiki/Bosnia_(region)"}]}, {"year": "1942", "text": "Casablanca, the movie starring <PERSON> and <PERSON>, premieres in New York City.", "html": "1942 - <i><a href=\"https://wikipedia.org/wiki/Casablanca_(film)\" title=\"Casablanca (film)\">Casablanca</a></i>, the movie starring <PERSON> and <PERSON>, premieres in New York City.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/Casablanca_(film)\" title=\"Casablanca (film)\">Casablanca</a></i>, the movie starring <PERSON> and <PERSON>, premieres in New York City.", "links": [{"title": "Casablanca (film)", "link": "https://wikipedia.org/wiki/Casablanca_(film)"}]}, {"year": "1943", "text": "World War II: HMT Rohna is sunk by the Luftwaffe in an air attack in the Mediterranean north of Béjaïa, Algeria.", "html": "1943 - World War II: <a href=\"https://wikipedia.org/wiki/HMT_Rohna\" title=\"HMT Rohna\">HMT <i><PERSON><PERSON><PERSON></i></a> is sunk by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> in an air attack in the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean</a> north of <a href=\"https://wikipedia.org/wiki/B%C3%A9ja%C3%AFa\" title=\"Béjaïa\">Béjaïa</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/HMT_Rohna\" title=\"HMT Rohna\">HMT <i><PERSON><PERSON><PERSON></i></a> is sunk by the <a href=\"https://wikipedia.org/wiki/Luftwaffe\" title=\"Luftwaffe\">Luftwaffe</a> in an air attack in the <a href=\"https://wikipedia.org/wiki/Mediterranean_Sea\" title=\"Mediterranean Sea\">Mediterranean</a> north of <a href=\"https://wikipedia.org/wiki/B%C3%A9ja%C3%AFa\" title=\"Béjaïa\">Béjaïa</a>, <a href=\"https://wikipedia.org/wiki/Algeria\" title=\"Algeria\">Algeria</a>.", "links": [{"title": "HMT Rohna", "link": "https://wikipedia.org/wiki/HMT_<PERSON><PERSON>na"}, {"title": "Luftwaffe", "link": "https://wikipedia.org/wiki/Luftwaffe"}, {"title": "Mediterranean Sea", "link": "https://wikipedia.org/wiki/Mediterranean_Sea"}, {"title": "Béjaïa", "link": "https://wikipedia.org/wiki/B%C3%A9ja%C3%AFa"}, {"title": "Algeria", "link": "https://wikipedia.org/wiki/Algeria"}]}, {"year": "1944", "text": "World War II: A German V-2 rocket hits a Woolworth's shop in New Cross, London, killing 168 people.", "html": "1944 - World War II: A German <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> hits a <PERSON><PERSON>worth's shop in <a href=\"https://wikipedia.org/wiki/New_Cross\" title=\"New Cross\">New Cross</a>, London, killing 168 people.", "no_year_html": "World War II: A German <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2 rocket</a> hits a Woolworth's shop in <a href=\"https://wikipedia.org/wiki/New_Cross\" title=\"New Cross\">New Cross</a>, London, killing 168 people.", "links": [{"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}, {"title": "New Cross", "link": "https://wikipedia.org/wiki/New_Cross"}]}, {"year": "1944", "text": "World War II: Germany begins V-1 and V-2 attacks on Antwerp, Belgium.", "html": "1944 - World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> begins <a href=\"https://wikipedia.org/wiki/V-1_flying_bomb\" title=\"V-1 flying bomb\">V-1</a> and <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2</a> attacks on <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a>, Belgium.", "no_year_html": "World War II: <a href=\"https://wikipedia.org/wiki/Nazi_Germany\" title=\"Nazi Germany\">Germany</a> begins <a href=\"https://wikipedia.org/wiki/V-1_flying_bomb\" title=\"V-1 flying bomb\">V-1</a> and <a href=\"https://wikipedia.org/wiki/V-2_rocket\" title=\"V-2 rocket\">V-2</a> attacks on <a href=\"https://wikipedia.org/wiki/Antwerp\" title=\"Antwerp\">Antwerp</a>, Belgium.", "links": [{"title": "Nazi Germany", "link": "https://wikipedia.org/wiki/Nazi_Germany"}, {"title": "V-1 flying bomb", "link": "https://wikipedia.org/wiki/V-1_flying_bomb"}, {"title": "V-2 rocket", "link": "https://wikipedia.org/wiki/V-2_rocket"}, {"title": "Antwerp", "link": "https://wikipedia.org/wiki/Antwerp"}]}, {"year": "1949", "text": "The Constituent Assembly of India adopts the constitution presented by Dr. <PERSON><PERSON> <PERSON><PERSON>.", "html": "1949 - The <a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_India\" title=\"Constituent Assembly of India\">Constituent Assembly of India</a> adopts the <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">constitution</a> presented by Dr. <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Constituent_Assembly_of_India\" title=\"Constituent Assembly of India\">Constituent Assembly of India</a> adopts the <a href=\"https://wikipedia.org/wiki/Constitution_of_India\" title=\"Constitution of India\">constitution</a> presented by Dr. <a href=\"https://wikipedia.org/wiki/B<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>.", "links": [{"title": "Constituent Assembly of India", "link": "https://wikipedia.org/wiki/Constituent_Assembly_of_India"}, {"title": "Constitution of India", "link": "https://wikipedia.org/wiki/Constitution_of_India"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1950", "text": "Korean War: Communist Chinese troops launch a massive counterattack (Battle of the Ch'ongch'on River and Battle of Chosin Reservoir) against United Nations and South Korean forces.", "html": "1950 - <a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Communist Chinese troops launch a massive counterattack (<a href=\"https://wikipedia.org/wiki/Battle_of_the_Ch%27ongch%27on_River\" title=\"Battle of the Ch'ongch'on River\">Battle of the Ch'ongch'on River</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir\" title=\"Battle of Chosin Reservoir\">Battle of Chosin Reservoir</a>) against United Nations and South Korean forces.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Korean_War\" title=\"Korean War\">Korean War</a>: Communist Chinese troops launch a massive counterattack (<a href=\"https://wikipedia.org/wiki/Battle_of_the_Ch%27ongch%27on_River\" title=\"Battle of the Ch'ongch'on River\">Battle of the Ch'ongch'on River</a> and <a href=\"https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir\" title=\"Battle of Chosin Reservoir\">Battle of Chosin Reservoir</a>) against United Nations and South Korean forces.", "links": [{"title": "Korean War", "link": "https://wikipedia.org/wiki/Korean_War"}, {"title": "Battle of the Ch'ongch'on River", "link": "https://wikipedia.org/wiki/Battle_of_the_Ch%27ongch%27on_River"}, {"title": "Battle of Chosin Reservoir", "link": "https://wikipedia.org/wiki/Battle_of_Chosin_Reservoir"}]}, {"year": "1965", "text": "France launches Astérix, becoming the third nation to put an object in orbit using its own booster.", "html": "1965 - France launches <i><a href=\"https://wikipedia.org/wiki/Ast%C3%A9rix_(satellite)\" title=\"Astérix (satellite)\">Astérix</a></i>, becoming the third nation to put an object in orbit using its own booster.", "no_year_html": "France launches <i><a href=\"https://wikipedia.org/wiki/Ast%C3%A9rix_(satellite)\" title=\"Astérix (satellite)\">Astérix</a></i>, becoming the third nation to put an object in orbit using its own booster.", "links": [{"title": "Astérix (satellite)", "link": "https://wikipedia.org/wiki/Ast%C3%A9rix_(satellite)"}]}, {"year": "1968", "text": "Vietnam War: United States Air Force helicopter pilot <PERSON> rescues an Army Special Forces unit pinned down by Viet Cong fire. He is later awarded the Medal of Honor.", "html": "1968 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> helicopter pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> rescues an Army Special Forces unit pinned down by <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> fire. He is later awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/United_States_Air_Force\" title=\"United States Air Force\">United States Air Force</a> helicopter pilot <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> rescues an Army Special Forces unit pinned down by <a href=\"https://wikipedia.org/wiki/Viet_Cong\" title=\"Viet Cong\">Viet Cong</a> fire. He is later awarded the <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a>.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "United States Air Force", "link": "https://wikipedia.org/wiki/United_States_Air_Force"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Viet Cong", "link": "https://wikipedia.org/wiki/Viet_Cong"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1970", "text": "In Basse-Terre, Guadeloupe, 38 millimetres (1.5 in) of rain fall in a minute, the heaviest rainfall ever recorded.", "html": "1970 - In <a href=\"https://wikipedia.org/wiki/Basse-Terre\" title=\"Basse-Terre\">Basse-Terre</a>, <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, 38 millimetres (1.5 in) of rain fall in a minute, the heaviest rainfall ever recorded.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Basse-Terre\" title=\"Basse-Terre\">Basse-Terre</a>, <a href=\"https://wikipedia.org/wiki/Guadeloupe\" title=\"Guadeloupe\">Guadeloupe</a>, 38 millimetres (1.5 in) of rain fall in a minute, the heaviest rainfall ever recorded.", "links": [{"title": "Basse-Terre", "link": "https://wikipedia.org/wiki/Basse-Terre"}, {"title": "Guadeloupe", "link": "https://wikipedia.org/wiki/Guadeloupe"}]}, {"year": "1977", "text": "An unidentified hijacker named <PERSON><PERSON><PERSON>, claiming to be the representative of the \"Ashtar Galactic Command\", takes over Britain's Southern Television for six minutes, starting at 5:12 pm.", "html": "1977 - An unidentified hijacker named <a href=\"https://wikipedia.org/wiki/Vrillon\" class=\"mw-redirect\" title=\"Vrillon\">Vrillon</a>, claiming to be the representative of the \"Ashtar Galactic Command\", takes over Britain's <a href=\"https://wikipedia.org/wiki/Southern_Television\" title=\"Southern Television\">Southern Television</a> for six minutes, starting at 5:12 pm.", "no_year_html": "An unidentified hijacker named <a href=\"https://wikipedia.org/wiki/Vrillon\" class=\"mw-redirect\" title=\"Vrillon\"><PERSON>rillon</a>, claiming to be the representative of the \"Ashtar Galactic Command\", takes over Britain's <a href=\"https://wikipedia.org/wiki/Southern_Television\" title=\"Southern Television\">Southern Television</a> for six minutes, starting at 5:12 pm.", "links": [{"title": "Vrillon", "link": "https://wikipedia.org/wiki/<PERSON>rillon"}, {"title": "Southern Television", "link": "https://wikipedia.org/wiki/Southern_Television"}]}, {"year": "1979", "text": "Pakistan International Airlines Flight 740 crashes near Taif in Mecca Province, Saudi Arabia, killing all 156 people on board.", "html": "1979 - <a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_740\" title=\"Pakistan International Airlines Flight 740\">Pakistan International Airlines Flight 740</a> crashes near <a href=\"https://wikipedia.org/wiki/Taif\" title=\"Taif\">Taif</a> in <a href=\"https://wikipedia.org/wiki/Mecca_Province\" title=\"Mecca Province\">Mecca Province</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing all 156 people on board.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_740\" title=\"Pakistan International Airlines Flight 740\">Pakistan International Airlines Flight 740</a> crashes near <a href=\"https://wikipedia.org/wiki/Taif\" title=\"Taif\">Taif</a> in <a href=\"https://wikipedia.org/wiki/Mecca_Province\" title=\"Mecca Province\">Mecca Province</a>, <a href=\"https://wikipedia.org/wiki/Saudi_Arabia\" title=\"Saudi Arabia\">Saudi Arabia</a>, killing all 156 people on board.", "links": [{"title": "Pakistan International Airlines Flight 740", "link": "https://wikipedia.org/wiki/Pakistan_International_Airlines_Flight_740"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Taif"}, {"title": "Mecca Province", "link": "https://wikipedia.org/wiki/Mecca_Province"}, {"title": "Saudi Arabia", "link": "https://wikipedia.org/wiki/Saudi_Arabia"}]}, {"year": "1983", "text": "Brink's-Mat robbery: In London, 6,800 gold bars worth nearly £26 million are stolen from the Brink's-Mat vault at Heathrow Airport.", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Brink%27s-Mat_robbery\" title=\"Brink's-Mat robbery\">Brink's-Mat robbery</a>: In London, 6,800 gold bars worth nearly £26 million are stolen from the <PERSON><PERSON>k's-<PERSON> vault at <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Brink%27s-Mat_robbery\" title=\"Brink's-Mat robbery\">Brink's-Mat robbery</a>: In London, 6,800 gold bars worth nearly £26 million are stolen from the <PERSON><PERSON><PERSON>'s-<PERSON> vault at <a href=\"https://wikipedia.org/wiki/Heathrow_Airport\" title=\"Heathrow Airport\">Heathrow Airport</a>.", "links": [{"title": "<PERSON>rin<PERSON>'s-Mat robbery", "link": "https://wikipedia.org/wiki/Brink%27s-Mat_robbery"}, {"title": "Heathrow Airport", "link": "https://wikipedia.org/wiki/Heathrow_Airport"}]}, {"year": "1986", "text": "Iran-Contra affair: U.S. President <PERSON> announces the members of what will become known as the Tower Commission.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the members of what will become known as the <a href=\"https://wikipedia.org/wiki/Tower_Commission\" title=\"Tower Commission\">Tower Commission</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair\" title=\"Iran-Contra affair\">Iran-Contra affair</a>: U.S. President <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces the members of what will become known as the <a href=\"https://wikipedia.org/wiki/Tower_Commission\" title=\"Tower Commission\">Tower Commission</a>.", "links": [{"title": "Iran-Contra affair", "link": "https://wikipedia.org/wiki/Iran%E2%80%93Contra_affair"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Tower Commission", "link": "https://wikipedia.org/wiki/Tower_Commission"}]}, {"year": "1986", "text": "The trial of <PERSON>, accused of committing war crimes as a guard at the Nazi Treblinka extermination camp, starts in Jerusalem.", "html": "1986 - The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, accused of committing war crimes as a guard at the Nazi <a href=\"https://wikipedia.org/wiki/Treblinka_extermination_camp\" title=\"Treblinka extermination camp\">Treblinka extermination camp</a>, starts in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "no_year_html": "The trial of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, accused of committing war crimes as a guard at the Nazi <a href=\"https://wikipedia.org/wiki/Treblinka_extermination_camp\" title=\"Treblinka extermination camp\">Treblinka extermination camp</a>, starts in <a href=\"https://wikipedia.org/wiki/Jerusalem\" title=\"Jerusalem\">Jerusalem</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Treblinka extermination camp", "link": "https://wikipedia.org/wiki/Treblinka_extermination_camp"}, {"title": "Jerusalem", "link": "https://wikipedia.org/wiki/Jerusalem"}]}, {"year": "1991", "text": "National Assembly of Azerbaijan abolishes the autonomous status of Nagorno-Karabakh Autonomous Oblast of Azerbaijan and renames several cities with Azeri names.", "html": "1991 - <a href=\"https://wikipedia.org/wiki/National_Assembly_of_Azerbaijan\" class=\"mw-redirect\" title=\"National Assembly of Azerbaijan\">National Assembly of Azerbaijan</a> <a href=\"https://wikipedia.org/wiki/Law_on_Abolishment_of_Nagorno-Karabakh_Autonomous_Oblast\" title=\"Law on Abolishment of Nagorno-Karabakh Autonomous Oblast\">abolishes the autonomous status of Nagorno-Karabakh Autonomous Oblast</a> of <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> and renames several cities with Azeri names.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/National_Assembly_of_Azerbaijan\" class=\"mw-redirect\" title=\"National Assembly of Azerbaijan\">National Assembly of Azerbaijan</a> <a href=\"https://wikipedia.org/wiki/Law_on_Abolishment_of_Nagorno-Karabakh_Autonomous_Oblast\" title=\"Law on Abolishment of Nagorno-Karabakh Autonomous Oblast\">abolishes the autonomous status of Nagorno-Karabakh Autonomous Oblast</a> of <a href=\"https://wikipedia.org/wiki/Azerbaijan\" title=\"Azerbaijan\">Azerbaijan</a> and renames several cities with Azeri names.", "links": [{"title": "National Assembly of Azerbaijan", "link": "https://wikipedia.org/wiki/National_Assembly_of_Azerbaijan"}, {"title": "Law on Abolishment of Nagorno-Karabakh Autonomous Oblast", "link": "https://wikipedia.org/wiki/Law_on_Abolishment_of_Nagorno-Karabakh_Autonomous_Oblast"}, {"title": "Azerbaijan", "link": "https://wikipedia.org/wiki/Azerbaijan"}]}, {"year": "1998", "text": "<PERSON> becomes the first Prime Minister of the United Kingdom to address the Oireachtas, the parliament of the Republic of Ireland.", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> to address the <a href=\"https://wikipedia.org/wiki/Oireachtas\" title=\"Oireachtas\">Oireachtas</a>, the parliament of the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> becomes the first <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> to address the <a href=\"https://wikipedia.org/wiki/Oireachtas\" title=\"Oireachtas\">Oireachtas</a>, the parliament of the <a href=\"https://wikipedia.org/wiki/Republic_of_Ireland\" title=\"Republic of Ireland\">Republic of Ireland</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}, {"title": "Oireachtas", "link": "https://wikipedia.org/wiki/Oireachtas"}, {"title": "Republic of Ireland", "link": "https://wikipedia.org/wiki/Republic_of_Ireland"}]}, {"year": "1998", "text": "The Khanna rail disaster takes 212 lives in Khanna, Ludhiana, India.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/Khanna_rail_disaster\" class=\"mw-redirect\" title=\"Khanna rail disaster\">Khanna rail disaster</a> takes 212 lives in <a href=\"https://wikipedia.org/wiki/Khan<PERSON>,_Ludhiana\" title=\"Khanna, Ludhiana\"><PERSON><PERSON>, Ludhiana</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Khanna_rail_disaster\" class=\"mw-redirect\" title=\"Khanna rail disaster\">Khanna rail disaster</a> takes 212 lives in <a href=\"https://wikipedia.org/wiki/Khanna,_Ludhiana\" title=\"Khanna, Ludhiana\"><PERSON><PERSON>, Ludhiana</a>, <a href=\"https://wikipedia.org/wiki/India\" title=\"India\">India</a>.", "links": [{"title": "Khanna rail disaster", "link": "https://wikipedia.org/wiki/Khanna_rail_disaster"}, {"title": "Khanna, Ludhiana", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>,_<PERSON><PERSON><PERSON>"}, {"title": "India", "link": "https://wikipedia.org/wiki/India"}]}, {"year": "1999", "text": "The 7.5 Mw  Ambrym earthquake shakes Vanuatu and a destructive tsunami follows. Ten people were killed and forty were injured.", "html": "1999 - The 7.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_Ambrym_earthquake\" title=\"1999 Ambrym earthquake\">Ambrym earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Vanuatu\" title=\"Vanuatu\">Vanuatu</a> and a destructive tsunami follows. Ten people were killed and forty were injured.", "no_year_html": "The 7.5 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1999_Ambrym_earthquake\" title=\"1999 Ambrym earthquake\">Ambrym earthquake</a> shakes <a href=\"https://wikipedia.org/wiki/Vanuatu\" title=\"Vanuatu\">Vanuatu</a> and a destructive tsunami follows. Ten people were killed and forty were injured.", "links": [{"title": "1999 Ambrym earthquake", "link": "https://wikipedia.org/wiki/1999_Ambrym_earthquake"}, {"title": "Vanuatu", "link": "https://wikipedia.org/wiki/Vanuatu"}]}, {"year": "2000", "text": "<PERSON> is certified the winner of Florida's electoral votes by <PERSON>, going on to win the United States presidential election, despite losing in the national popular vote.", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is certified the winner of Florida's electoral votes by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, going on to win the <a href=\"https://wikipedia.org/wiki/2000_United_States_presidential_election\" title=\"2000 United States presidential election\">United States presidential election</a>, despite losing in the national popular vote.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is certified the winner of Florida's electoral votes by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, going on to win the <a href=\"https://wikipedia.org/wiki/2000_United_States_presidential_election\" title=\"2000 United States presidential election\">United States presidential election</a>, despite losing in the national popular vote.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "2000 United States presidential election", "link": "https://wikipedia.org/wiki/2000_United_States_presidential_election"}]}, {"year": "2003", "text": "The Concorde makes its final flight, over Bristol, England.", "html": "2003 - The <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> makes its final flight, over <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a>, England.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Concorde\" title=\"Concorde\">Concorde</a> makes its final flight, over <a href=\"https://wikipedia.org/wiki/Bristol\" title=\"Bristol\">Bristol</a>, England.", "links": [{"title": "Concorde", "link": "https://wikipedia.org/wiki/Concorde"}, {"title": "Bristol", "link": "https://wikipedia.org/wiki/Bristol"}]}, {"year": "2004", "text": "Ruzhou School massacre: A man stabs and kills eight people and seriously wounds another four in a school dormitory in Ruzhou, China.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Ruzhou_School_massacre\" class=\"mw-redirect\" title=\"Ruzhou School massacre\">Ruzhou School massacre</a>: A man stabs and kills eight people and seriously wounds another four in a school dormitory in <a href=\"https://wikipedia.org/wiki/Ruzhou\" title=\"Ruzhou\">Ruzhou</a>, China.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ruzhou_School_massacre\" class=\"mw-redirect\" title=\"Ruzhou School massacre\">Ruzhou School massacre</a>: A man stabs and kills eight people and seriously wounds another four in a school dormitory in <a href=\"https://wikipedia.org/wiki/Ruzhou\" title=\"Ruzhou\">Ruzhou</a>, China.", "links": [{"title": "Ruzhou School massacre", "link": "https://wikipedia.org/wiki/Ruzhou_School_massacre"}, {"title": "Ruzhou", "link": "https://wikipedia.org/wiki/Ruzhou"}]}, {"year": "2004", "text": "The last <PERSON><PERSON><PERSON><PERSON> (Black-faced honeycreeper) dies of avian malaria in the Maui Bird Conservation Center in Olinda, Hawaii, before it could breed, making the species in all probability extinct.", "html": "2004 - The last <a href=\"https://wikipedia.org/wiki/Po%CA%BBouli\" title=\"Poʻouli\"><PERSON><PERSON><PERSON><PERSON></a> (Black-faced honeycreeper) dies of <a href=\"https://wikipedia.org/wiki/Avian_malaria\" title=\"Avian malaria\">avian malaria</a> in the Maui Bird Conservation Center in Olinda, Hawaii, before it could breed, making the species in all probability extinct.", "no_year_html": "The last <a href=\"https://wikipedia.org/wiki/Po%CA%BBouli\" title=\"Poʻouli\"><PERSON><PERSON><PERSON><PERSON></a> (Black-faced honeycreeper) dies of <a href=\"https://wikipedia.org/wiki/Avian_malaria\" title=\"Avian malaria\">avian malaria</a> in the Maui Bird Conservation Center in Olinda, Hawaii, before it could breed, making the species in all probability extinct.", "links": [{"title": "Po<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Po%CA%BBouli"}, {"title": "Avian malaria", "link": "https://wikipedia.org/wiki/Avian_malaria"}]}, {"year": "2008", "text": "Mumbai attacks, a series of terrorist attacks killing approximately 175 citizens by 10 members of Lashkar-e-Taiba, a Pakistan based extremist Islamist terrorist organisation.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/2008_Mumbai_attacks\" title=\"2008 Mumbai attacks\">Mumbai attacks</a>, a series of <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist attacks</a> killing approximately 175 citizens by 10 members of <a href=\"https://wikipedia.org/wiki/Lashkar-e-Taiba\" title=\"Lashkar-e-Taiba\"><PERSON>h<PERSON>-e-Taiba</a>, a <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> based extremist <a href=\"https://wikipedia.org/wiki/Islamist\" class=\"mw-redirect\" title=\"Islamist\">Islamist</a> terrorist organisation.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2008_Mumbai_attacks\" title=\"2008 Mumbai attacks\">Mumbai attacks</a>, a series of <a href=\"https://wikipedia.org/wiki/Terrorism\" title=\"Terrorism\">terrorist attacks</a> killing approximately 175 citizens by 10 members of <a href=\"https://wikipedia.org/wiki/Lashkar-e-Taiba\" title=\"Lashkar-e-Taiba\">Lashkar-e-Taiba</a>, a <a href=\"https://wikipedia.org/wiki/Pakistan\" title=\"Pakistan\">Pakistan</a> based extremist <a href=\"https://wikipedia.org/wiki/Islamist\" class=\"mw-redirect\" title=\"Islamist\">Islamist</a> terrorist organisation.", "links": [{"title": "2008 Mumbai attacks", "link": "https://wikipedia.org/wiki/2008_Mumbai_attacks"}, {"title": "Terrorism", "link": "https://wikipedia.org/wiki/Terrorism"}, {"title": "Lashkar-e-Taiba", "link": "https://wikipedia.org/wiki/Las<PERSON><PERSON>-e-Taiba"}, {"title": "Pakistan", "link": "https://wikipedia.org/wiki/Pakistan"}, {"title": "Islamist", "link": "https://wikipedia.org/wiki/Islamist"}]}, {"year": "2008", "text": "The ocean liner Queen Elizabeth 2, now out of service, docks in Dubai.", "html": "2008 - The ocean liner <i><a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_2\" title=\"Queen Elizabeth 2\">Queen <PERSON> 2</a></i>, now out of service, docks in Dubai.", "no_year_html": "The ocean liner <i><a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_2\" title=\"Queen Elizabeth 2\">Queen <PERSON> 2</a></i>, now out of service, docks in Dubai.", "links": [{"title": "Queen Elizabeth 2", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_2"}]}, {"year": "2011", "text": "NATO attack in Pakistan: NATO forces in Afghanistan attack a Pakistani check post in a friendly fire incident, killing 24 soldiers and wounding 13 others.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/NATO_attack_in_Pakistan\" class=\"mw-redirect\" title=\"NATO attack in Pakistan\">NATO attack in Pakistan</a>: NATO forces in Afghanistan attack a Pakistani check post in a friendly fire incident, killing 24 soldiers and wounding 13 others.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/NATO_attack_in_Pakistan\" class=\"mw-redirect\" title=\"NATO attack in Pakistan\">NATO attack in Pakistan</a>: NATO forces in Afghanistan attack a Pakistani check post in a friendly fire incident, killing 24 soldiers and wounding 13 others.", "links": [{"title": "NATO attack in Pakistan", "link": "https://wikipedia.org/wiki/NATO_attack_in_Pakistan"}]}, {"year": "2011", "text": "The Mars Science Laboratory launches to Mars with the Curiosity Rover.", "html": "2011 - The <a href=\"https://wikipedia.org/wiki/Mars_Science_Laboratory\" title=\"Mars Science Laboratory\">Mars Science Laboratory</a> launches to Mars with the <a href=\"https://wikipedia.org/wiki/Curiosity_(rover)\" title=\"Curiosity (rover)\">Curiosity</a> Rover.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Mars_Science_Laboratory\" title=\"Mars Science Laboratory\">Mars Science Laboratory</a> launches to Mars with the <a href=\"https://wikipedia.org/wiki/Curiosity_(rover)\" title=\"Curiosity (rover)\">Curiosity</a> Rover.", "links": [{"title": "Mars Science Laboratory", "link": "https://wikipedia.org/wiki/Mars_Science_Laboratory"}, {"title": "Curiosity (rover)", "link": "https://wikipedia.org/wiki/Curiosity_(rover)"}]}, {"year": "2018", "text": "The robotic probe Insight lands on Elysium Planitia, Mars.", "html": "2018 - The <a href=\"https://wikipedia.org/wiki/Robotic_spacecraft\" class=\"mw-redirect\" title=\"Robotic spacecraft\">robotic probe</a> <a href=\"https://wikipedia.org/wiki/InSight\" title=\"InSight\">Insight</a> lands on <a href=\"https://wikipedia.org/wiki/Elysium_Planitia\" title=\"Elysium Planitia\">Elysium Planitia</a>, <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Robotic_spacecraft\" class=\"mw-redirect\" title=\"Robotic spacecraft\">robotic probe</a> <a href=\"https://wikipedia.org/wiki/InSight\" title=\"InSight\">Insight</a> lands on <a href=\"https://wikipedia.org/wiki/Elysium_Planitia\" title=\"Elysium Planitia\">Elysium Planitia</a>, <a href=\"https://wikipedia.org/wiki/Mars\" title=\"Mars\">Mars</a>.", "links": [{"title": "Robotic spacecraft", "link": "https://wikipedia.org/wiki/Robotic_spacecraft"}, {"title": "InSight", "link": "https://wikipedia.org/wiki/InSight"}, {"title": "Elysium Planitia", "link": "https://wikipedia.org/wiki/Elysium_Planitia"}, {"title": "Mars", "link": "https://wikipedia.org/wiki/Mars"}]}, {"year": "2019", "text": "A magnitude 6.4 earthquake strikes western Albania leaving at least 52 people dead and over 1,000 injured. This was the world's deadliest earthquake of 2019, and the deadliest to strike the country in 99 years.", "html": "2019 - A <a href=\"https://wikipedia.org/wiki/2019_Albania_earthquake\" title=\"2019 Albania earthquake\">magnitude 6.4 earthquake</a> strikes western Albania leaving at least 52 people dead and over 1,000 injured. This was the world's deadliest earthquake of 2019, and the deadliest to strike the country in 99 years.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/2019_Albania_earthquake\" title=\"2019 Albania earthquake\">magnitude 6.4 earthquake</a> strikes western Albania leaving at least 52 people dead and over 1,000 injured. This was the world's deadliest earthquake of 2019, and the deadliest to strike the country in 99 years.", "links": [{"title": "2019 Albania earthquake", "link": "https://wikipedia.org/wiki/2019_Albania_earthquake"}]}, {"year": "2021", "text": "COVID-19 pandemic: The World Health Organization identifies the SARS-CoV-2 Omicron variant.", "html": "2021 - <a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> identifies the <a href=\"https://wikipedia.org/wiki/SARS-CoV-2_Omicron_variant\" title=\"SARS-CoV-2 Omicron variant\">SARS-CoV-2 Omicron variant</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/COVID-19_pandemic\" title=\"COVID-19 pandemic\">COVID-19 pandemic</a>: The <a href=\"https://wikipedia.org/wiki/World_Health_Organization\" title=\"World Health Organization\">World Health Organization</a> identifies the <a href=\"https://wikipedia.org/wiki/SARS-CoV-2_Omicron_variant\" title=\"SARS-CoV-2 Omicron variant\">SARS-CoV-2 Omicron variant</a>.", "links": [{"title": "COVID-19 pandemic", "link": "https://wikipedia.org/wiki/COVID-19_pandemic"}, {"title": "World Health Organization", "link": "https://wikipedia.org/wiki/World_Health_Organization"}, {"title": "SARS-CoV-2 Omicron variant", "link": "https://wikipedia.org/wiki/SARS-CoV-2_Omicron_variant"}]}], "Births": [{"year": "907", "text": "<PERSON><PERSON><PERSON>, Galician bishop (d. 977)", "html": "907 - <a href=\"https://wikipedia.org/wiki/Rudes<PERSON>\" title=\"<PERSON>udes<PERSON>\"><PERSON><PERSON><PERSON></a>, Galician bishop (d. 977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Rudes<PERSON>\" title=\"<PERSON>udes<PERSON>\"><PERSON><PERSON><PERSON></a>, Galician bishop (d. 977)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R<PERSON>ind"}]}, {"year": "1288", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese emperor (d. 1339)", "html": "1288 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor Go-Daigo\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1339)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>-Daigo\" title=\"Emperor Go-Daigo\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese emperor (d. 1339)", "links": [{"title": "Emperor <PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>-<PERSON>"}]}, {"year": "1401", "text": "<PERSON>, 2nd Earl of Somerset (d. 1418)", "html": "1401 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Somerset\" title=\"<PERSON>, 2nd Earl of Somerset\"><PERSON>, 2nd Earl of Somerset</a> (d. 1418)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Somerset\" title=\"<PERSON>, 2nd Earl of Somerset\"><PERSON>, 2nd Earl of Somerset</a> (d. 1418)", "links": [{"title": "<PERSON>, 2nd Earl of Somerset", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Somerset"}]}, {"year": "1436", "text": "<PERSON> of Portugal (d. 1463)", "html": "1436 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal_(nun)\" title=\"<PERSON> of Portugal (nun)\"><PERSON> of Portugal</a> (d. 1463)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Portugal_(nun)\" title=\"<PERSON> of Portugal (nun)\"><PERSON> of Portugal</a> (d. 1463)", "links": [{"title": "<PERSON> of Portugal (nun)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Portugal_(nun)"}]}, {"year": "1466", "text": "<PERSON>, 2nd Baron <PERSON>, English noble (d. 1506)", "html": "1466 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English noble (d. 1506)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English noble (d. 1506)", "links": [{"title": "<PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>"}]}, {"year": "1518", "text": "<PERSON>, Catholic cardinal (d. 1564)", "html": "1518 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Sforza_di_Santa_Fiora\" title=\"<PERSON> di Santa Fiora\"><PERSON> Fiora</a>, Catholic cardinal (d. 1564)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rza_di_Santa_Fiora\" title=\"<PERSON> Sforz<PERSON> di Santa Fiora\"><PERSON> Santa Fiora</a>, Catholic cardinal (d. 1564)", "links": [{"title": "<PERSON> di Santa Fiora", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON><PERSON>a_di_Santa_<PERSON>"}]}, {"year": "1534", "text": "<PERSON>, 7th Baron <PERSON> (d. 1613)", "html": "1534 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th Baron <PERSON>\"><PERSON>, 7th Baron <PERSON></a> (d. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_7th_Baron_<PERSON>\" title=\"<PERSON>, 7th Baron <PERSON>\"><PERSON>, 7th Baron <PERSON></a> (d. 1613)", "links": [{"title": "<PERSON>, 7th Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_7th_Baron_<PERSON>"}]}, {"year": "1552", "text": "<PERSON><PERSON><PERSON> of Joseon, King of Joseon (d. 1608)", "html": "1552 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON> of Joseon</a>, King of Joseon (d. 1608)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon\" title=\"<PERSON><PERSON><PERSON> of Joseon\"><PERSON><PERSON><PERSON> of Joseon</a>, King of Joseon (d. 1608)", "links": [{"title": "<PERSON><PERSON><PERSON> of Joseon", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Joseon"}]}, {"year": "1594", "text": "<PERSON>, Irish genealogist (d. 1666)", "html": "1594 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Irish genealogist (d. 1666)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)\" title=\"<PERSON> (historian)\"><PERSON></a>, Irish genealogist (d. 1666)", "links": [{"title": "<PERSON> (historian)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(historian)"}]}, {"year": "1604", "text": "<PERSON>, German organist and composer (d. 1673)", "html": "1604 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German organist and composer (d. 1673)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, German organist and composer (d. 1673)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, English minister and philanthropist (d. 1638)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, English minister and philanthropist (d. 1638)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)\" title=\"<PERSON> (clergyman)\"><PERSON></a>, English minister and philanthropist (d. 1638)", "links": [{"title": "<PERSON> (clergyman)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(clergyman)"}]}, {"year": "1609", "text": "<PERSON>, English-American clergyman and academic (d. 1659)", "html": "1609 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American clergyman and academic (d. 1659)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American clergyman and academic (d. 1659)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1657", "text": "<PERSON>, English minister and philosopher (d. 1735)", "html": "1657 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and philosopher (d. 1735)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English minister and philosopher (d. 1735)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1678", "text": "<PERSON><PERSON><PERSON><PERSON>, French geophysicist and astronomer (d. 1771)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Ort<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geophysicist and astronomer (d. 1771)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Ort<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French geophysicist and astronomer (d. 1771)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%27Ort<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1679", "text": "<PERSON><PERSON><PERSON>,  Franciscan missionary from Spanish Texas (d. 1755)", "html": "1679 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Espinosa\" title=\"<PERSON><PERSON><PERSON> Espinosa\"><PERSON><PERSON><PERSON> Espinosa</a>, Franciscan missionary from Spanish Texas (d. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Espinosa\" title=\"<PERSON><PERSON><PERSON> Espinosa\"><PERSON><PERSON><PERSON> Espinosa</a>, Franciscan missionary from Spanish Texas (d. 1755)", "links": [{"title": "<PERSON><PERSON><PERSON>ino<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_de_Espinosa"}]}, {"year": "1703", "text": "<PERSON><PERSON><PERSON>, English actor and playwright (d. 1758)", "html": "1703 - <a href=\"https://wikipedia.org/wiki/Theophilus_Cibber\" title=\"Theophilus Cibber\"><PERSON><PERSON><PERSON></a>, English actor and playwright (d. 1758)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Theophilus_<PERSON>ib<PERSON>\" title=\"Theophilus Cibber\"><PERSON><PERSON><PERSON></a>, English actor and playwright (d. 1758)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Theophilus_Cibber"}]}, {"year": "1727", "text": "<PERSON><PERSON>, American general and politician (d. 1800)", "html": "1727 - <a href=\"https://wikipedia.org/wiki/Artemas_Ward\" title=\"Artemas Ward\"><PERSON><PERSON></a>, American general and politician (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Artemas_Ward\" title=\"Artemas Ward\"><PERSON><PERSON></a>, American general and politician (d. 1800)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Artemas_Ward"}]}, {"year": "1731", "text": "<PERSON>, English poet and hymnwriter (d. 1800)", "html": "1731 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymnwriter (d. 1800)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and hymnwriter (d. 1800)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1792", "text": "<PERSON>, American author and activist (d. 1873)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, American author and activist (d. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>rim<PERSON>%C3%A9"}]}, {"year": "1811", "text": "<PERSON><PERSON>, Chinese general and politician, Viceroy of Liangjiang (d. 1872)", "html": "1811 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Viceroy_of_Liangjiang\" title=\"Viceroy of Liangjiang\">Viceroy of Liangjiang</a> (d. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/Viceroy_of_Liangjiang\" title=\"Viceroy of Liangjiang\">Viceroy of Liangjiang</a> (d. 1872)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Viceroy of Liangjiang", "link": "https://wikipedia.org/wiki/Viceroy_of_Liangjiang"}]}, {"year": "1817", "text": "<PERSON>, Alsatian-French chemist (d. 1884)", "html": "1817 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian-French chemist (d. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian-French chemist (d. 1884)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1827", "text": "<PERSON>, American religious leader and author, co-founded the Seventh-day Adventist Church (d. 1915)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, co-founded the <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American religious leader and author, co-founded the <a href=\"https://wikipedia.org/wiki/Seventh-day_Adventist_Church\" title=\"Seventh-day Adventist Church\">Seventh-day Adventist Church</a> (d. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Seventh-day Adventist Church", "link": "https://wikipedia.org/wiki/Seventh-day_Adventist_Church"}]}, {"year": "1828", "text": "<PERSON>, American surgeon and academic (d. 1895)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and academic (d. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, French journalist and politician, 52nd Prime Minister of France (d. 1905)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goblet\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_Goblet\" title=\"<PERSON>\"><PERSON></a>, French journalist and politician, 52nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (d. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ren%C3%A9_Goblet"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1832", "text": "<PERSON>, German-French physicist and academic (d. 1901)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and academic (d. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-French physicist and academic (d. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1832", "text": "<PERSON>, American surgeon and activist, Medal of Honor recipient (d. 1919)", "html": "1832 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and activist, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American surgeon and activist, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1837", "text": "<PERSON>, English-Australian politician, 17th Premier of South Australia (d. 1915)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> II\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>ford II\"><PERSON></a>, English-Australian politician, 17th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (d. 1915)", "links": [{"title": "<PERSON> II", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1853", "text": "<PERSON>, American police officer and journalist (d. 1921)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masterson\"><PERSON></a>, American police officer and journalist (d. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Masterson\"><PERSON></a>, American police officer and journalist (d. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Masterson"}]}, {"year": "1857", "text": "<PERSON>, Swiss linguist and author (d. 1913)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss linguist and author (d. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss linguist and author (d. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, American nun and saint (d. 1955)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nun and saint (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American nun and saint (d. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1864", "text": "<PERSON>, English 3rd General of the Salvation Army (d. 1947)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English 3rd <a href=\"https://wikipedia.org/wiki/General_of_the_Salvation_Army\" class=\"mw-redirect\" title=\"General of the Salvation Army\">General of the Salvation Army</a> (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English 3rd <a href=\"https://wikipedia.org/wiki/General_of_the_Salvation_Army\" class=\"mw-redirect\" title=\"General of the Salvation Army\">General of the Salvation Army</a> (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "General of the Salvation Army", "link": "https://wikipedia.org/wiki/General_of_the_Salvation_Army"}]}, {"year": "1869", "text": "<PERSON> of Wales (d. 1938)", "html": "1869 - <a href=\"https://wikipedia.org/wiki/Maud_of_Wales\" title=\"Maud of Wales\"><PERSON> of Wales</a> (d. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Maud_of_Wales\" title=\"Maud of Wales\"><PERSON> of Wales</a> (d. 1938)", "links": [{"title": "Maud of Wales", "link": "https://wikipedia.org/wiki/Maud_of_Wales"}]}, {"year": "1870", "text": "Sir <PERSON>, founder and Vice-Chancellor of the University of Sagar (d. 1949)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, founder and Vice-Chancellor of <a href=\"https://wikipedia.org/wiki/Dr._<PERSON>_<PERSON>\" title=\"Dr. <PERSON>\">the University of Sagar</a> (d. 1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\">Sir <PERSON></a>, founder and Vice-Chancellor of <a href=\"https://wikipedia.org/wiki/Dr._<PERSON>_<PERSON>\" title=\"Dr. <PERSON>\">the University of Sagar</a> (d. 1949)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Dr. <PERSON> University", "link": "https://wikipedia.org/wiki/Dr._<PERSON>_<PERSON>_<PERSON>_University"}]}, {"year": "1873", "text": "<PERSON>, Scottish golfer (d. 1954)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer (d. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish golfer (d. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American engineer, invented air conditioning (d. 1950)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Air_conditioning\" title=\"Air conditioning\">air conditioning</a> (d. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Willis Carrier\"><PERSON></a>, American engineer, invented <a href=\"https://wikipedia.org/wiki/Air_conditioning\" title=\"Air conditioning\">air conditioning</a> (d. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Air conditioning", "link": "https://wikipedia.org/wiki/Air_conditioning"}]}, {"year": "1878", "text": "<PERSON>, American cyclist (d. 1932)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/Major_<PERSON>\" title=\"Major <PERSON>\">Major <PERSON></a>, American cyclist (d. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Major <PERSON>\">Major <PERSON></a>, American cyclist (d. 1932)", "links": [{"title": "Major <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1885", "text": "<PERSON>, German lieutenant, economist, and politician, Chancellor of Germany (d. 1970)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCning\" title=\"<PERSON>\"><PERSON></a>, German lieutenant, economist, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%BCning\" title=\"<PERSON>\"><PERSON></a>, German lieutenant, economist, and politician, <a href=\"https://wikipedia.org/wiki/List_of_Chancellors_of_Germany\" class=\"mw-redirect\" title=\"List of Chancellors of Germany\">Chancellor of Germany</a> (d. 1970)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Heinrich_Br%C3%BCning"}, {"title": "List of Chancellors of Germany", "link": "https://wikipedia.org/wiki/List_of_Chancellors_of_Germany"}]}, {"year": "1888", "text": "<PERSON>, American director and screenwriter (d. 1978)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Beebe\" title=\"Ford Beebe\"><PERSON></a>, American director and screenwriter (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Beebe\" title=\"Ford Beebe\"><PERSON></a>, American director and screenwriter (d. 1978)", "links": [{"title": "Ford Beebe", "link": "https://wikipedia.org/wiki/Ford_Beebe"}]}, {"year": "1889", "text": "<PERSON>, French actor, director, and screenwriter (d. 1976)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1976)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (d. 1976)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9"}]}, {"year": "1891", "text": "<PERSON>, American pianist, composer, and conductor (d. 1977)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist, composer, and conductor (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)\" title=\"<PERSON> (composer)\"><PERSON></a>, American pianist, composer, and conductor (d. 1977)", "links": [{"title": "<PERSON> (composer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(composer)"}]}, {"year": "1894", "text": "<PERSON>, Canadian cardinal (d. 1974)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 1974)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian cardinal (d. 1974)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON><PERSON>, American-Swedish mathematician and philosopher (d. 1964)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Swedish mathematician and philosopher (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-Swedish mathematician and philosopher (d. 1964)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, American activist, co-founded Alcoholics Anonymous (d. 1971)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON></a>, American activist, co-founded <a href=\"https://wikipedia.org/wiki/Alcoholics_Anonymous\" title=\"Alcoholics Anonymous\">Alcoholics Anonymous</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>."}, {"title": "Alcoholics Anonymous", "link": "https://wikipedia.org/wiki/Alcoholics_Anonymous"}]}, {"year": "1898", "text": "<PERSON>, German chemist and engineer, Nobel Prize laureate (d. 1973)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1899", "text": "<PERSON>, German-American murderer (d. 1936)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American murderer (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-American murderer (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Swiss biologist, known for her study of bees (d. 1993)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist, known for her study of bees (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss biologist, known for her study of bees (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, American admiral (d. 1953)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (d. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1902", "text": "<PERSON>, American businessman, co-founded McDonald's (d. 1971)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American businessman, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>%27s\" title=\"McDonald's\"><PERSON>'s</a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "McDonald's", "link": "https://wikipedia.org/wiki/<PERSON>%27s"}]}, {"year": "1903", "text": "<PERSON><PERSON><PERSON>, Czech-English pianist and educator (d. 2014)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English pianist and educator (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Czech-English pianist and educator (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON>, Canadian physician and microbiologist (d. 1991)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and microbiologist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and microbiologist (d. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1904", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian poet, scholar, writer, philosopher, and cultural critic (d. 2011)", "html": "1904 - <a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. D. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet, scholar, writer, philosopher, and cultural critic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"K. D. Seth<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian poet, scholar, writer, philosopher, and cultural critic (d. 2011)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American baseball player (d. 1982)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (d. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)\" title=\"<PERSON> (outfielder)\"><PERSON></a>, American baseball player (d. 1982)", "links": [{"title": "<PERSON> (outfielder)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(outfielder)"}]}, {"year": "1907", "text": "<PERSON>, American botanist (d. 2013)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American botanist (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1908", "text": "<PERSON>, Baron <PERSON>, Italian-Scottish businessman, founded Forte Group (d. 2007)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Italian-Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Forte_Group\" title=\"Forte Group\">Forte Group</a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, Italian-Scottish businessman, founded <a href=\"https://wikipedia.org/wiki/Forte_Group\" title=\"Forte Group\">Forte Group</a> (d. 2007)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Forte Group", "link": "https://wikipedia.org/wiki/Forte_Group"}]}, {"year": "1908", "text": "<PERSON><PERSON>, American baseball player and manager (d. 1989)", "html": "1908 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and manager (d. 1989)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, German footballer and manager (d. 1998)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer and manager (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, American actress and singer (d. 2004)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1909", "text": "<PERSON>, Romanian-French playwright and critic (d. 1994)", "html": "1909 - <a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French playwright and critic (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Eug%C3%A8ne_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian-French playwright and critic (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Eug%C3%A8ne_Ionesco"}]}, {"year": "1910", "text": "<PERSON>, South African-born Irish actor (d. 1993)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Irish actor (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African-born Irish actor (d. 1993)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, Polish-American chess player and author (d. 1992)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American chess player and author (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-American chess player and author (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, American journalist (d. 1992)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist (d. 1992)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1915", "text": "<PERSON><PERSON>, German-born Australian sculptor (d. 2016)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/Inge_King\" title=\"Inge King\"><PERSON><PERSON></a>, German-born Australian sculptor (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Inge_King\" title=\"Inge King\"><PERSON><PERSON></a>, German-born Australian sculptor (d. 2016)", "links": [{"title": "<PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/Inge_King"}]}, {"year": "1915", "text": "<PERSON>, American pianist and composer (d. 2010)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist and composer (d. 2010)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON><PERSON>, Turkish-American record producer (d. 1989)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/N<PERSON>uh<PERSON>_<PERSON>\" title=\"Nes<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish-American record producer (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/N<PERSON><PERSON><PERSON>_<PERSON>\" title=\"N<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish-American record producer (d. 1989)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Chilean lawyer and politician, 31st President of Chile (d. 2016)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean lawyer and politician, 31st <a href=\"https://wikipedia.org/wiki/President_of_Chile\" title=\"President of Chile\">President of Chile</a> (d. 2016)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patric<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "President of Chile", "link": "https://wikipedia.org/wiki/President_of_Chile"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, Polish soldier and politician, 6th President of the Republic of Poland (d. 2010)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland\" title=\"List of heads of state of Poland\">President of the Republic of Poland</a> (d. 2010)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Polish soldier and politician, 6th <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland\" title=\"List of heads of state of Poland\">President of the Republic of Poland</a> (d. 2010)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of heads of state of Poland", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_Poland"}]}, {"year": "1919", "text": "<PERSON><PERSON><PERSON>, American journalist and author (d. 2013)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American journalist and author (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Indian historian and academic (d. 2011)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian historian and academic (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian historian and academic (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, Canadian-American director and producer (d. 2004)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director and producer (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American director and producer (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian engineer and businessman, founded <PERSON><PERSON> (d. 2012)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/Verghese_Kurien\" title=\"Verghese Kurien\"><PERSON><PERSON><PERSON><PERSON></a>, Indian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Amul\" title=\"Amul\"><PERSON>ul</a> (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Verghese_Ku<PERSON>\" title=\"Verghese Kurien\"><PERSON><PERSON><PERSON><PERSON></a>, Indian engineer and businessman, founded <a href=\"https://wikipedia.org/wiki/Amul\" title=\"Amul\"><PERSON><PERSON></a> (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amul"}]}, {"year": "1922", "text": "<PERSON>, American cartoonist, created Peanuts (d. 2000)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/Peanuts\" title=\"Peanuts\">Peanuts</a></i> (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American cartoonist, created <i><a href=\"https://wikipedia.org/wiki/Peanuts\" title=\"Peanuts\">Peanuts</a></i> (d. 2000)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Peanuts", "link": "https://wikipedia.org/wiki/Peanuts"}]}, {"year": "1923", "text": "<PERSON>, Australian politician and barrister (d. 2024)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician and barrister (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian politician and barrister (d. 2024)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}]}, {"year": "1923", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian cinematographer (d. 2014)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cinematographer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian cinematographer (d. 2014)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON><PERSON><PERSON>, Indian cricketer (d. 1992)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian cricketer (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, American painter and sculptor (d. 2000)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and sculptor (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)\" title=\"<PERSON> (artist)\"><PERSON></a>, American painter and sculptor (d. 2000)", "links": [{"title": "<PERSON> (artist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(artist)"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Uruguayan dictator (d. 2016)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_%C3%81lvarez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan dictator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_%C3%81lvarez\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Uruguayan dictator (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Conrad<PERSON>_%C3%81l<PERSON><PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American pianist (d. 2003)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, Filipino visual artist (d. 2021)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino visual artist (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Filipino visual artist (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Arturo_<PERSON>"}]}, {"year": "1926", "text": "<PERSON><PERSON>, Indian activist and politician, 10th Speaker of the Lok Sabha (d. 2017)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician, 10th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian activist and politician, 10th <a href=\"https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha\" title=\"Speaker of the Lok Sabha\">Speaker of the Lok Sabha</a> (d. 2017)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Speaker of the Lok Sabha", "link": "https://wikipedia.org/wiki/Speaker_of_the_Lok_Sabha"}]}, {"year": "1927", "text": "<PERSON>, American-Canadian television host (d. 2001)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian television host (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian television host (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, Japanese linguist and academic (d. 2012)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese linguist and academic (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese linguist and academic (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>o"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Slovenian singer-songwriter and accordion player (d. 2015)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Avsenik\" title=\"Slavko Avsenik\"><PERSON><PERSON><PERSON></a>, Slovenian singer-songwriter and accordion player (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_A<PERSON>ik\" title=\"Slavko Avsenik\"><PERSON><PERSON><PERSON></a>, Slovenian singer-songwriter and accordion player (d. 2015)", "links": [{"title": "Slavko Avsenik", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_A<PERSON>ik"}]}, {"year": "1929", "text": "<PERSON><PERSON>, American actress, singer and dancer (d. 2023)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Bet<PERSON>_St._John\" title=\"Betta St. John\"><PERSON><PERSON> St. John</a>, American actress, singer and dancer (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bet<PERSON>_St._John\" title=\"Betta St. John\"><PERSON><PERSON> St. John</a>, American actress, singer and dancer (d. 2023)", "links": [{"title": "Betta St. John", "link": "https://wikipedia.org/wiki/Betta_St._John"}]}, {"year": "1930", "text": "<PERSON><PERSON>, German engineer and philanthropist, founded Berthold Leibinger Stiftung (d. 2018)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Leibinger\" title=\"<PERSON>hold Leibinger\"><PERSON><PERSON></a>, German engineer and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Bert<PERSON>_Leibinger_Stiftung\" title=\"Berthold Leibinger Stiftung\">Berthold Leibinger Stiftung</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Leibinger\" title=\"<PERSON><PERSON> Leibinger\"><PERSON><PERSON></a>, German engineer and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Bert<PERSON>_Leibinger_Stiftung\" title=\"Berthold Leibinger Stiftung\">Berthold Leibinger Stiftung</a> (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> Stiftung", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Leibinger_Stiftung"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Argentinian painter, sculptor, and activist, Nobel Prize laureate", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9rez_Esquivel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian painter, sculptor, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A9rez_Esquivel\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Argentinian painter, sculptor, and activist, <a href=\"https://wikipedia.org/wiki/Nobel_Peace_Prize\" title=\"Nobel Peace Prize\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Adolfo_P%C3%A9rez_Esquivel"}, {"title": "Nobel Peace Prize", "link": "https://wikipedia.org/wiki/Nobel_Peace_Prize"}]}, {"year": "1931", "text": "<PERSON><PERSON>, Dutch cardinal (d. 2020)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cardinal (d. 2020)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, American-Canadian singer and actor (d. 2007)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer and actor (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian singer and actor (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, Scottish bishop and radio host", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bishop and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish bishop and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, English director, producer, and screenwriter (d. 2012)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director, producer, and screenwriter (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON><PERSON><PERSON>, Iranian actor (d. 2019)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian actor (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Iranian actor (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>i"}]}, {"year": "1933", "text": "<PERSON>, American director and producer, invented instant replay (d. 2015)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, invented <a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">instant replay</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer, invented <a href=\"https://wikipedia.org/wiki/Instant_replay\" title=\"Instant replay\">instant replay</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>a"}, {"title": "Instant replay", "link": "https://wikipedia.org/wiki/Instant_replay"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Turkish architect, engineer, and journalist (d. 2020)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/Cengiz_<PERSON>a%C5%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish architect, engineer, and journalist (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cengiz_<PERSON>kta%C5%9F\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Turkish architect, engineer, and journalist (d. 2020)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cengiz_Bekta%C5%9F"}]}, {"year": "1934", "text": "<PERSON>, American director and producer", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American actress and singer (d. 2011)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON>, English computer scientist and psychologist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and psychologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English computer scientist and psychologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American bass player (d. 2012)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, Baron <PERSON> of Lower Marsh, English businessman and politician, Secretary of State for Health (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Lower_Marsh\" title=\"<PERSON>, Baron <PERSON> of Lower Marsh\"><PERSON>, Baron <PERSON> of Lower Marsh</a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>_of_Lower_Marsh\" title=\"<PERSON>, Baron <PERSON> of Lower Marsh\"><PERSON>, Baron <PERSON> of Lower Marsh</a>, English businessman and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_Health\" class=\"mw-redirect\" title=\"Secretary of State for Health\">Secretary of State for Health</a> (d. 2019)", "links": [{"title": "<PERSON>, Baron <PERSON> of Lower Marsh", "link": "https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>_of_Lower_Marsh"}, {"title": "Secretary of State for Health", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_Health"}]}, {"year": "1937", "text": "<PERSON>, Russian physician and astronaut (d. 1994)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physician and astronaut (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American economist (d. 2022)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American economist (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, American soldier and politician, 19th Director of the CIA", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soldier and politician, 19th <a href=\"https://wikipedia.org/wiki/Director_of_the_CIA\" class=\"mw-redirect\" title=\"Director of the CIA\">Director of the CIA</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>ss\"><PERSON></a>, American soldier and politician, 19th <a href=\"https://wikipedia.org/wiki/Director_of_the_CIA\" class=\"mw-redirect\" title=\"Director of the CIA\">Director of the CIA</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ss"}, {"title": "Director of the CIA", "link": "https://wikipedia.org/wiki/Director_of_the_CIA"}]}, {"year": "1938", "text": "<PERSON>, Australian physicist and academic (d. 2021)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and academic (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian physicist and academic (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1938", "text": "<PERSON>, Canadian-American comedian, actor, and singer", "html": "1938 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American comedian, actor, and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American comedian, actor, and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, Malaysian civil servant and politician, 5th Prime Minister of Malaysia", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian civil servant and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Malaysian civil servant and politician, 5th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Malaysia\" title=\"Prime Minister of Malaysia\">Prime Minister of Malaysia</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Prime Minister of Malaysia", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Malaysia"}]}, {"year": "1939", "text": "<PERSON><PERSON>, American actor and puppeteer (d. 1988)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Wayland_Flowers\" title=\"Wayland Flowers\"><PERSON><PERSON> Flowers</a>, American actor and puppeteer (d. 1988)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Wayland_Flowers\" title=\"Wayland Flowers\"><PERSON><PERSON> Flowers</a>, American actor and puppeteer (d. 1988)", "links": [{"title": "Wayland Flowers", "link": "https://wikipedia.org/wiki/Wayland_Flowers"}]}, {"year": "1939", "text": "<PERSON>, English politician, Secretary of State for the Environment", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment\" title=\"Secretary of State for the Environment\">Secretary of State for the Environment</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment\" title=\"Secretary of State for the Environment\">Secretary of State for the Environment</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for the Environment", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Environment"}]}, {"year": "1939", "text": "<PERSON>, American actor (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, 2nd Earl of Gowrie, Irish-Scottish politician, Chancellor of the Duchy of Lancaster (d. 2021)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_of_Gowrie\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Gowrie\"><PERSON>, 2nd Earl of Gowrie</a>, Irish-Scottish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Gow<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, 2nd Earl of Gowrie\"><PERSON>, 2nd Earl of Gowrie</a>, Irish-Scottish politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster\" title=\"Chancellor of the Duchy of Lancaster\">Chancellor of the Duchy of Lancaster</a> (d. 2021)", "links": [{"title": "<PERSON>, 2nd Earl of Gowrie", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Earl_<PERSON>_Go<PERSON>"}, {"title": "Chancellor of the Duchy of Lancaster", "link": "https://wikipedia.org/wiki/Chancellor_of_the_Duchy_of_Lancaster"}]}, {"year": "1939", "text": "<PERSON>, English saxophonist and surgeon", "html": "1939 - <a href=\"https://wikipedia.org/wiki/Art_Themen\" title=\"Art Themen\"><PERSON></a>, English saxophonist and surgeon", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Themen\" title=\"Art Themen\"><PERSON></a>, English saxophonist and surgeon", "links": [{"title": "Art Themen", "link": "https://wikipedia.org/wiki/Art_Themen"}]}, {"year": "1939", "text": "<PERSON>, American-Swiss singer-songwriter, dancer, and actress (d. 2023)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss singer-songwriter, dancer, and actress (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Swiss singer-songwriter, dancer, and actress (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Italian mathematician and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian mathematician and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, English guitarist and songwriter (d. 2008)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English guitarist and songwriter (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 53rd <PERSON><PERSON><PERSON><PERSON> (d. 2007)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/Kotozakura_Masakatsu_I\" title=\"Kotozakura Masakatsu I\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 53rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kotozakura_Masakatsu_I\" title=\"Kotozakura Masakatsu I\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 53rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yo<PERSON>zuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>ura_Masakatsu_I"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1940", "text": "<PERSON>, English historian, author, and academic", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English historian, author, and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON><PERSON>, American mezzo-soprano", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mezzo-soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American mezzo-soprano", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American baseball player and manager (d. 2025)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2025)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 2025)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON><PERSON>, Japanese actor", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Carrousel\" title=\"<PERSON><PERSON> Carrousel\"><PERSON><PERSON></a>, Japanese actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ousel\" title=\"<PERSON><PERSON> Carrousel\"><PERSON><PERSON></a>, Japanese actor", "links": [{"title": "<PERSON><PERSON>l", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Carrousel"}]}, {"year": "1942", "text": "<PERSON>, American actress (d. 2018)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cole\"><PERSON></a>, American actress (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Norwegian-American football player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian-American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "Đặng <PERSON><PERSON><PERSON><PERSON>, Vietnamese physician and author (d. 1970)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m\" title=\"Đặng Thùy Trâm\">Đặng Thù<PERSON> Trâm</a>, Vietnamese physician and author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m\" title=\"Đặng Thùy Trâm\">Đặng Thù<PERSON> Trâm</a>, Vietnamese physician and author (d. 1970)", "links": [{"title": "Đặng <PERSON><PERSON><PERSON><PERSON>âm", "link": "https://wikipedia.org/wiki/%C4%90%E1%BA%B7ng_Th%C3%B9y_Tr%C3%A2m"}]}, {"year": "1943", "text": "<PERSON>, English radio host", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American director and producer (d. 2002)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and producer (d. 2002)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON><PERSON>, American novelist and essayist", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and essayist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American novelist and essayist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, American radio host (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American radio host (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, <PERSON>, English academic and politician, Minister of State for Europe", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baroness_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baroness <PERSON>\"><PERSON>, Baroness <PERSON></a>, English academic and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1944", "text": "<PERSON>, American singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American actor", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_(actor)"}]}, {"year": "1945", "text": "<PERSON>, English-American bass player", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, Scottish guitarist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American singer-songwriter, keyboard player, and producer", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, keyboard player, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON><PERSON><PERSON><PERSON>, Swedish academic and politician, 27th Swedish Minister for Defence", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> von <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Swedish academic and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister for Defence</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Swedish academic and politician, 27th <a href=\"https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)\" title=\"Minister for Defence (Sweden)\">Swedish Minister for Defence</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Bj%C3%B6<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister for Defence (Sweden)", "link": "https://wikipedia.org/wiki/Minister_for_Defence_(Sweden)"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter, saxophonist, and producer (d. 2014)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, saxophonist, and producer (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, saxophonist, and producer (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American football player and coach", "html": "1946 - <a href=\"https://wikipedia.org/wiki/Art_Shell\" title=\"Art Shell\"><PERSON></a>, American football player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Shell\" title=\"Art Shell\"><PERSON></a>, American football player and coach", "links": [{"title": "Art Shell", "link": "https://wikipedia.org/wiki/Art_Shell"}]}, {"year": "1946", "text": "<PERSON><PERSON><PERSON>, Romanian-Israeli historian and author (d. 2012)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Romanian-Israeli historian and author (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Singer\"><PERSON><PERSON><PERSON></a>, Romanian-Israeli historian and author (d. 2012)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, American football player", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, Australian-American biologist and academic, Nobel Prize laureate", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian-American biologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Swedish journalist", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swedish journalist", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Russian swimmer and journalist (d. 2015)", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian swimmer and journalist (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian-Russian swimmer and journalist (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English rugby player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_union)"}]}, {"year": "1949", "text": "<PERSON>, East Timorese geographer and politician, 1st Prime Minister of East Timor", "html": "1949 - <a href=\"https://wikipedia.org/wiki/Mari_Alkatiri\" title=\"<PERSON>\"><PERSON></a>, East Timorese geographer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_East_Timor\" class=\"mw-redirect\" title=\"Prime Minister of East Timor\">Prime Minister of East Timor</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mari_Al<PERSON>\" title=\"Mari Alkat<PERSON>\"><PERSON></a>, East Timorese geographer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_East_Timor\" class=\"mw-redirect\" title=\"Prime Minister of East Timor\">Prime Minister of East Timor</a>", "links": [{"title": "Mari Alkatiri", "link": "https://wikipedia.org/wiki/Mari_Alkatiri"}, {"title": "Prime Minister of East Timor", "link": "https://wikipedia.org/wiki/Prime_Minister_of_East_Timor"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, Israeli singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>hl<PERSON>_<PERSON>\" title=\"Shlomo <PERSON>zi\"><PERSON><PERSON><PERSON></a>, Israeli singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Shl<PERSON>_<PERSON>\" title=\"Shl<PERSON>zi\"><PERSON><PERSON><PERSON></a>, Israeli singer-songwriter and guitarist", "links": [{"title": "Shlomo <PERSON>", "link": "https://wikipedia.org/wiki/Shlomo_<PERSON>zi"}]}, {"year": "1949", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1949", "text": "<PERSON>, American political scientist and academic", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, Romanian canoe world and Olympic champion (d. 2021)", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian canoe world and Olympic champion (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Romanian canoe world and Olympic champion (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON><PERSON>, Hungarian-Italian porn actress, singer, and politician", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Ilona_Staller\" title=\"Ilona Staller\"><PERSON><PERSON> Staller</a>, Hungarian-Italian porn actress, singer, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ilona_Staller\" title=\"Ilona Staller\"><PERSON><PERSON> Staller</a>, Hungarian-Italian porn actress, singer, and politician", "links": [{"title": "Ilona Staller", "link": "https://wikipedia.org/wiki/Ilona_Staller"}]}, {"year": "1951", "text": "<PERSON><PERSON><PERSON>, Bosnian lawyer, judge, and politician (d. 2014)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/Sulejman_Tihi%C4%87\" title=\"<PERSON><PERSON><PERSON> Tihić\"><PERSON><PERSON><PERSON></a>, Bosnian lawyer, judge, and politician (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sulejman_Tihi%C4%87\" title=\"Sulej<PERSON> Tihić\"><PERSON><PERSON><PERSON></a>, Bosnian lawyer, judge, and politician (d. 2014)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sulejman_Tihi%C4%87"}]}, {"year": "1952", "text": "<PERSON>,  Mexican-American science teacher and entomologist", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American science teacher and entomologist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Mexican-American science teacher and entomologist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Australian tennis player", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English politician, Secretary of State for International Development", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_International_Development\" class=\"mw-redirect\" title=\"Secretary of State for International Development\">Secretary of State for International Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Secretary of State for International Development", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_International_Development"}]}, {"year": "1953", "text": "<PERSON>, American politician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American football player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Australian television host and actress", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television host and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian television host and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English director, producer, and screenwriter", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Julien_Temple\" title=\"Julien Temple\">Julien Temple</a>, English director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Julien_Temple\" title=\"Julien Temple\"><PERSON> Temple</a>, English director, producer, and screenwriter", "links": [{"title": "Julien Temple", "link": "https://wikipedia.org/wiki/Julien_Temple"}]}, {"year": "1953", "text": "<PERSON><PERSON><PERSON>, South African race car driver", "html": "1953 - <a href=\"https://wikipedia.org/wiki/Desir%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Desir%C3%A<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African race car driver", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Desir%C3%A9_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, American cartoonist", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>st\" title=\"<PERSON><PERSON> Chast\"><PERSON><PERSON></a>, American cartoonist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>st\" title=\"<PERSON><PERSON> Chast\"><PERSON><PERSON></a>, American cartoonist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Sri Lankan rebel leader, founded the Liberation Tigers of Tamil Eelam (d. 2009)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/Velupillai_Prabhakaran\" title=\"Velupillai Prabhakaran\"><PERSON><PERSON><PERSON><PERSON><PERSON>rab<PERSON></a>, Sri Lankan rebel leader, founded the <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a> (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Velupillai_Prabhakaran\" title=\"Velupillai Prabhakaran\"><PERSON><PERSON><PERSON><PERSON><PERSON>rab<PERSON>aran</a>, Sri Lankan rebel leader, founded the <a href=\"https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam\" title=\"Liberation Tigers of Tamil Eelam\">Liberation Tigers of Tamil Eelam</a> (d. 2009)", "links": [{"title": "<PERSON>el<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Velupillai_Prabhakaran"}, {"title": "Liberation Tigers of Tamil Eelam", "link": "https://wikipedia.org/wiki/Liberation_Tigers_of_Tamil_Eelam"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Slovenian politician and a former Member of the European Parliament", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian politician and a former Member of the European Parliament", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian politician and a former Member of the European Parliament", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, German-English academic and politician", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-English academic and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-English academic and politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American race car driver and sportscaster", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, Canadian actor, producer, and screenwriter", "html": "1956 - <a href=\"https://wikipedia.org/wiki/Don_Lake\" title=\"Don Lake\"><PERSON></a>, Canadian actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Don_Lake\" title=\"Don Lake\"><PERSON></a>, Canadian actor, producer, and screenwriter", "links": [{"title": "Don Lake", "link": "https://wikipedia.org/wiki/Don_Lake"}]}, {"year": "1956", "text": "<PERSON>, Indian-English lawyer and politician, Minister of State for Europe", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_of_State_for_Europe\" class=\"mw-redirect\" title=\"Minister of State for Europe\">Minister of State for Europe</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Minister of State for Europe", "link": "https://wikipedia.org/wiki/Minister_of_State_for_Europe"}]}, {"year": "1957", "text": "<PERSON>, Cuban-American sculptor (d. 1996)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Gonz%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American sculptor (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/F%C3%A9lix_Gonz%C3%<PERSON><PERSON><PERSON><PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cuban-American sculptor (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/F%C3%A9lix_Gonz%C3%A1lez-Torres"}]}, {"year": "1958", "text": "<PERSON>, English rugby player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, English rugby player", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_(rugby_union)"}]}, {"year": "1959", "text": "<PERSON> Welsh politician and independent Member of Parliament (MP)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a> Welsh politician and independent <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a> Welsh politician and independent <a href=\"https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)\" title=\"Member of Parliament (United Kingdom)\">Member of Parliament</a> (MP)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "Member of Parliament (United Kingdom)", "link": "https://wikipedia.org/wiki/Member_of_Parliament_(United_Kingdom)"}]}, {"year": "1959", "text": "<PERSON><PERSON>, American author and academic", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Gabriella_Guti%C3%A9rrez_y_Muhs\" title=\"<PERSON><PERSON> Gutiérre<PERSON> y <PERSON>\"><PERSON><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Gabriella_Guti%C3%A9rrez_y_<PERSON>hs\" title=\"<PERSON><PERSON> Gutiérre<PERSON> y <PERSON>\"><PERSON><PERSON></a>, American author and academic", "links": [{"title": "<PERSON><PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/Gabriella_Guti%C3%A9rrez_y_Muhs"}]}, {"year": "1959", "text": "<PERSON>, American actress, singer, and dancer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, singer, and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American sportscaster", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Russian serial killer, rapist, torturer, and necrophile (d. 1996)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian serial killer, rapist, torturer, and necrophile (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian serial killer, rapist, torturer, and necrophile (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American journalist", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American baseball player and sportscaster", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON>, <PERSON>, Indian-English businessman, co-founded Cobra Beer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Indian-English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Cobra_Beer\" title=\"Cobra Beer\">Cobra Beer</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>,_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>, <PERSON>\"><PERSON><PERSON>, <PERSON></a>, Indian-English businessman, co-founded <a href=\"https://wikipedia.org/wiki/Cobra_Beer\" title=\"Cobra Beer\">Cobra Beer</a>", "links": [{"title": "<PERSON><PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON><PERSON>,_<PERSON>_<PERSON>"}, {"title": "Cobra Beer", "link": "https://wikipedia.org/wiki/Cobra_Beer"}]}, {"year": "1961", "text": "<PERSON>, Australian surfer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, Australian surfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(surfer)\" title=\"<PERSON> (surfer)\"><PERSON></a>, Australian surfer", "links": [{"title": "<PERSON> (surfer)", "link": "https://wikipedia.org/wiki/<PERSON>_(surfer)"}]}, {"year": "1961", "text": "<PERSON>, American wrestler and trainer", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and trainer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, American wrestler and trainer", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_(wrestler)"}]}, {"year": "1962", "text": "<PERSON>, Portuguese footballer and manager", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Portuguese footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, American baseball player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, American basketball player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, German-English journalist and author", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English journalist and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, English rugby player and coach", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)\" title=\"<PERSON> (rugby)\"><PERSON></a>, English rugby player and coach", "links": [{"title": "<PERSON> (rugby)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby)"}]}, {"year": "1964", "text": "<PERSON><PERSON><PERSON>, Swiss skier", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Swiss skier", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actor, director, producer, and screenwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, English footballer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American actress", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Straten\"><PERSON><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Straten\"><PERSON><PERSON></a>, American actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON><PERSON>, Haitian-American actress and singer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian-American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Haitian-American actress and singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gar<PERSON><PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON><PERSON>, Tunisian footballer", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>ahed_<PERSON>\" title=\"Fahed Dermech\"><PERSON><PERSON><PERSON></a>, Tunisian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>ah<PERSON>_<PERSON>\" title=\"Fahed Dermech\"><PERSON><PERSON><PERSON></a>, Tunisian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fahed_<PERSON>ch"}]}, {"year": "1966", "text": "<PERSON>, American basketball player and coach", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Wicks\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>\" title=\"<PERSON> Wicks\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Wicks"}]}, {"year": "1967", "text": "<PERSON>, Antiguan cricketer", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Antiguan cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American basketball player", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Turkish singer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Levent\"><PERSON><PERSON></a>, Turkish singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish singer", "links": [{"title": "Haluk Levent", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Levent"}]}, {"year": "1969", "text": "<PERSON>, American basketball player", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, American painter and illustrator", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American-English basketball player and sportscaster", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English basketball player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English basketball player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, Australian comedian and radio host", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and radio host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian comedian and radio host", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American author", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American boxer and actor", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American boxer and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American author", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1972", "text": "<PERSON><PERSON><PERSON>, Indian actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, American actor, director, and producer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, director, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Norwegian tuba player, composer, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Line_Horntveth\" title=\"Line Horntveth\"><PERSON></a>, Norwegian tuba player, composer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Line_Horntveth\" title=\"Line Horntveth\"><PERSON></a>, Norwegian tuba player, composer, and producer", "links": [{"title": "Line Horntveth", "link": "https://wikipedia.org/wiki/Line_Horntveth"}]}, {"year": "1974", "text": "<PERSON>, American actress", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, Czech decathlete and high jumper", "html": "1974 - <a href=\"https://wikipedia.org/wiki/Roman_%C5%A0ebrle\" title=\"Roman Šebrle\"><PERSON></a>, Czech decathlete and high jumper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_%C5%A0ebrle\" title=\"Roman Šebrle\"><PERSON></a>, Czech decathlete and high jumper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_%C5%A0ebrle"}]}, {"year": "1975", "text": "<PERSON> <PERSON><PERSON><PERSON>, American rapper and producer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON><PERSON>\" title=\"DJ Khale<PERSON>\">DJ <PERSON></a>, American rapper and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/DJ_<PERSON><PERSON><PERSON>\" title=\"DJ Khale<PERSON>\">DJ <PERSON><PERSON></a>, American rapper and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Canadian figure skater", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian figure skater", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, Swedish footballer", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON><PERSON>, American wrestler", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1976", "text": "<PERSON>, American baseball player and manager", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Italian cyclist", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cyclist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American football player", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Paris_Lenon\" title=\"Paris Lenon\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Paris_Lenon\" title=\"Paris Lenon\"><PERSON></a>, American football player", "links": [{"title": "Paris Lenon", "link": "https://wikipedia.org/wiki/Paris_Lenon"}]}, {"year": "1977", "text": "<PERSON>, Scottish canoe racer", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish canoe racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish canoe racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, Japanese voice actor and singer", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese voice actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese voice actor and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Japanese singer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American tennis player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jackie Trail\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Trail\" title=\"Jackie Trail\"><PERSON></a>, American tennis player", "links": [{"title": "Jackie <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Danish footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English singer-songwriter and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Australian singer and pianist", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer and pianist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian ice hockey player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Canadian football player", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American ice hockey player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, American basketball player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Matt_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, American publisher and businessman, co-founded Facebook", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American publisher and businessman, co-founded <a href=\"https://wikipedia.org/wiki/Facebook\" title=\"Facebook\">Facebook</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Facebook", "link": "https://wikipedia.org/wiki/Facebook"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Japanese voice actress and singer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Kat%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%8D\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese voice actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Emiri_Kat%C5%8D"}]}, {"year": "1984", "text": "<PERSON>, Spanish footballer (d. 2007)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer (d. 2007)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1986", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek pole vaulter", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Konst<PERSON><PERSON>_<PERSON>lippidis\" class=\"mw-redirect\" title=\"Konst<PERSON><PERSON>lip<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek pole vaulter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Konst<PERSON><PERSON>_<PERSON>pidis\" class=\"mw-redirect\" title=\"Konst<PERSON><PERSON>lippid<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek pole vaulter", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Konstadi<PERSON>_Filippidis"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Dutch cyclist", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Italian rugby player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American singer, songwriter and dancer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kat <PERSON>\"><PERSON></a>, American singer, songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Kat <PERSON>\"><PERSON></a>, American singer, songwriter and dancer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Greek footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Greek footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ge"}]}, {"year": "1988", "text": "<PERSON><PERSON>, Japanese model and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Japanese model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Filipina singer and actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipina singer and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, English footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Junior Stan<PERSON>las\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Junior <PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English rapper", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, English rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(rapper)\" title=\"<PERSON> (rapper)\"><PERSON></a>, English rapper", "links": [{"title": "<PERSON> (rapper)", "link": "https://wikipedia.org/wiki/<PERSON>_(rapper)"}]}, {"year": "1990", "text": "<PERSON>, Brazilian footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, <PERSON><PERSON><PERSON>-English singer-songwriter and actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON>-English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <PERSON><PERSON><PERSON>-English singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English footballer", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, Italian footballer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, Puerto Rican rapper and singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Anuel_AA\" title=\"Anuel AA\"><PERSON><PERSON></a>, Puerto Rican rapper and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Anuel_<PERSON>\" title=\"Anuel AA\"><PERSON><PERSON></a>, Puerto Rican rapper and singer", "links": [{"title": "Anuel <PERSON>", "link": "https://wikipedia.org/wiki/Anuel_AA"}]}, {"year": "1995", "text": " <PERSON>, English swimmer", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(swimmer)\" title=\"<PERSON> (swimmer)\"> <PERSON></a>, English swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)\" title=\"<PERSON> (swimmer)\"> <PERSON></a>, English swimmer", "links": [{"title": "<PERSON> (swimmer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(swimmer)"}]}, {"year": "1996", "text": "<PERSON>, American basketball player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American ice hockey player", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON>, French singer and actress", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, French singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, French singer and actress", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1996", "text": "<PERSON>, Spanish footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American actor", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON>, Swedish curler", "html": "1997 - <a href=\"https://wikipedia.org/wiki/Jen<PERSON>_W%C3%A5hlin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish curler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_W%C3%A5hlin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Swedish curler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jennie_W%C3%A5hlin"}]}, {"year": "1997", "text": "<PERSON>, English footballer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON><PERSON>, Indian cricketer", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American singer-songwriter", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_O%27Brien\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Olivia_O%27Brien"}]}, {"year": "1999", "text": "<PERSON>, Canadian soccer player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian soccer player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON><PERSON>, Ethiopian athlete", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ma\"><PERSON><PERSON><PERSON></a>, Ethiopian athlete", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> G<PERSON>ma\"><PERSON><PERSON><PERSON></a>, Ethiopian athlete", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>ech<PERSON>_G<PERSON>ma"}]}, {"year": "2001", "text": "<PERSON><PERSON>, Spanish footballer", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Pau_V%C3%ADctor\" title=\"<PERSON><PERSON> Víctor\"><PERSON><PERSON></a>, Spanish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pau_V%C3%ADctor\" title=\"<PERSON><PERSON> Víctor\"><PERSON><PERSON></a>, Spanish footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Pau_V%C3%ADctor"}]}], "Deaths": [{"year": "399", "text": "<PERSON><PERSON><PERSON>, pope of the Catholic Church (b. 334)", "html": "399 - <a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON>us\" title=\"Pope Sir<PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church (b. 334)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pope_<PERSON><PERSON>us\" title=\"Pope <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, pope of the Catholic Church (b. 334)", "links": [{"title": "<PERSON> <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>us"}]}, {"year": "946", "text": "<PERSON>, Chinese general (b. 898)", "html": "946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese general (b. 898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>"}]}, {"year": "975", "text": "<PERSON> of Constance, German bishop and saint (b.c. 900)", "html": "975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Constance\" title=\"<PERSON> of Constance\"><PERSON> of Constance</a>, German bishop and saint (b.c. 900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Constance\" title=\"<PERSON> of Constance\"><PERSON> of Constance</a>, German bishop and saint (b.c. 900)", "links": [{"title": "<PERSON> of Constance", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_Constance"}]}, {"year": "1014", "text": "<PERSON><PERSON><PERSON> of Saxony, margravine of Meissen", "html": "1014 - <a href=\"https://wikipedia.org/wiki/Swanehilde_of_Saxony\" title=\"Swaneh<PERSON> of Saxony\"><PERSON><PERSON><PERSON> of Saxony</a>, margravine of <a href=\"https://wikipedia.org/wiki/Margravate_of_Meissen\" title=\"Margravate of Meissen\">Meissen</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Swanehilde_of_Saxony\" title=\"Swanehilde of Saxony\"><PERSON><PERSON><PERSON> of Saxony</a>, margravine of <a href=\"https://wikipedia.org/wiki/Margravate_of_Meissen\" title=\"Margravate of Meissen\">Meissen</a>", "links": [{"title": "<PERSON>ehilde of Saxony", "link": "https://wikipedia.org/wiki/Swanehilde_of_Saxony"}, {"title": "Margravate of Meissen", "link": "https://wikipedia.org/wiki/Margravate_of_Meissen"}]}, {"year": "1236", "text": "<PERSON><PERSON><PERSON> ibn <PERSON>, Ayyubid emir of Aleppo (b. 1216)", "html": "1236 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Ayyubid emir of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a> (b. 1216)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON> ibn <PERSON></a>, Ayyubid emir of <a href=\"https://wikipedia.org/wiki/Aleppo\" title=\"Aleppo\">Aleppo</a> (b. 1216)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Aleppo", "link": "https://wikipedia.org/wiki/Aleppo"}]}, {"year": "1267", "text": "<PERSON>, Italian founder of the Sylvestrines (b. 1177)", "html": "1267 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian founder of the <a href=\"https://wikipedia.org/wiki/Sylvestrines\" title=\"Sylvestrines\">Syl<PERSON>trines</a> (b. 1177)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian founder of the <a href=\"https://wikipedia.org/wiki/Sylvestrines\" title=\"Sylvestrines\">Syl<PERSON>trines</a> (b. 1177)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Sylvestrines", "link": "https://wikipedia.org/wiki/Sylvestrines"}]}, {"year": "1473", "text": "<PERSON>, 1st Viscount of Huelma", "html": "1473 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_1st_Viscount_of_Huelma\" title=\"<PERSON>, 1st Viscount of Huelma\"><PERSON>, 1st Viscount of Huelma</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_1st_Viscount_of_Huelma\" title=\"<PERSON>, 1st Viscount of Huelma\"><PERSON>, 1st Viscount of Huelma</a>", "links": [{"title": "<PERSON>, 1st Viscount of Huelma", "link": "https://wikipedia.org/wiki/Diego_Fern%C3%<PERSON><PERSON><PERSON>_<PERSON>_la_Cueva,_1st_Viscount_of_<PERSON>elma"}]}, {"year": "1504", "text": "<PERSON>, queen of Castile and León (b. 1451)", "html": "1504 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, queen of <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (b. 1451)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile\" title=\"<PERSON> I of Castile\"><PERSON> I</a>, queen of <a href=\"https://wikipedia.org/wiki/Crown_of_Castile\" title=\"Crown of Castile\">Castile</a> and <a href=\"https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n\" title=\"Kingdom of León\">León</a> (b. 1451)", "links": [{"title": "<PERSON> of Castile", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Castile"}, {"title": "Crown of Castile", "link": "https://wikipedia.org/wiki/Crown_of_Castile"}, {"title": "Kingdom of León", "link": "https://wikipedia.org/wiki/Kingdom_of_Le%C3%B3n"}]}, {"year": "1621", "text": "<PERSON>, English surveyor and cartographer (b. 1540)", "html": "1621 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and cartographer (b. 1540)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English surveyor and cartographer (b. 1540)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1639", "text": "<PERSON>, Scottish archbishop and historian (b. 1565)", "html": "1639 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archbishop and historian (b. 1565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish archbishop and historian (b. 1565)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1651", "text": "<PERSON>, English-Irish general and politician, Lord Lieutenant of Ireland (b. 1611)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1611)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-Irish general and politician, <a href=\"https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland\" title=\"Lord Lieutenant of Ireland\">Lord Lieutenant of Ireland</a> (b. 1611)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lord Lieutenant of Ireland", "link": "https://wikipedia.org/wiki/Lord_Lieutenant_of_Ireland"}]}, {"year": "1661", "text": "<PERSON>, Spanish general and politician (b. 1598)", "html": "1661 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9ndez_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician (b. 1598)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_<PERSON>%C3%A9ndez_de_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Spanish general and politician (b. 1598)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_M%C3%A9ndez_<PERSON>_<PERSON>ro"}]}, {"year": "1688", "text": "<PERSON>, French playwright and composer (b. 1635)", "html": "1688 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French playwright and composer (b. 1635)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1689", "text": "<PERSON><PERSON><PERSON>, German archaeologist and scholar (b. 1635)", "html": "1689 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archaeologist and scholar (b. 1635)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German archaeologist and scholar (b. 1635)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1717", "text": "<PERSON>, English organist and composer (b. 1664)", "html": "1717 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1664)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English organist and composer (b. 1664)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1719", "text": "<PERSON>, English librarian and scholar (b. 1662)", "html": "1719 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, English librarian and scholar (b. 1662)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)\" title=\"<PERSON> (classicist)\"><PERSON></a>, English librarian and scholar (b. 1662)", "links": [{"title": "<PERSON> (classicist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(classicist)"}]}, {"year": "1780", "text": "<PERSON>, Scottish economist (b. 1712)", "html": "1780 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish economist (b. 1712)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)\" title=\"<PERSON> (economist)\"><PERSON></a>, Scottish economist (b. 1712)", "links": [{"title": "<PERSON> (economist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(economist)"}]}, {"year": "1829", "text": "<PERSON>, American lawyer and politician (b. 1787)", "html": "1829 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1787)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician (b. 1787)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1836", "text": "<PERSON>, Scottish engineer (b. 1756)", "html": "1836 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer (b. 1756)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish engineer (b. 1756)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1851", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, French general and politician, 12th Prime Minister of France (b. 1769)", "html": "1851 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1769)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></a>, French general and politician, 12th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1769)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>-<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1855", "text": "<PERSON>, Polish poet and playwright (b. 1798)", "html": "1855 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and playwright (b. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish poet and playwright (b. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, German poet and author (b. 1788)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German poet and author (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, English brewer, founded <PERSON> (b. 1780)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a> (b. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)\" title=\"<PERSON> (brewer)\"><PERSON></a>, English brewer, founded <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> King\"><PERSON></a> (b. 1780)", "links": [{"title": "<PERSON> (brewer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(brewer)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": "<PERSON>, Russian general and politician (b. 1788)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician (b. 1788)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian general and politician (b. 1788)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Prussian lawyer and politician, Minister President of Prussia (b. 1805)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Prussian lawyer and politician, <a href=\"https://wikipedia.org/wiki/Minister_President_of_Prussia\" title=\"Minister President of Prussia\">Minister President of Prussia</a> (b. 1805)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Minister President of Prussia", "link": "https://wikipedia.org/wiki/Minister_President_of_Prussia"}]}, {"year": "1883", "text": "Sojourner <PERSON>, American activist (b. 1797)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/Sojourner_Truth\" title=\"Sojourner Truth\">Sojourner Truth</a>, American activist (b. 1797)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sojourner_Truth\" title=\"Sojourner Truth\">Sojourner Truth</a>, American activist (b. 1797)", "links": [{"title": "Sojourner Truth", "link": "https://wikipedia.org/wiki/Sojourner_Truth"}]}, {"year": "1885", "text": "<PERSON>, Irish chemist and physicist (b. 1813)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Irish chemist and physicist (b. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(scientist)\" title=\"<PERSON> (scientist)\"><PERSON></a>, Irish chemist and physicist (b. 1813)", "links": [{"title": "<PERSON> (scientist)", "link": "https://wikipedia.org/wiki/<PERSON>_(scientist)"}]}, {"year": "1892", "text": "<PERSON>, French cardinal and academic (b. 1825)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and academic (b. 1825)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cardinal and academic (b. 1825)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON>, Irish zoologist, photographer, and surgeon (b. 1848)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish zoologist, photographer, and surgeon (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish zoologist, photographer, and surgeon (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON>, English poet and critic (b. 1823)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Coventry_Patmore\" title=\"Coventry Patmore\"><PERSON> Patmore</a>, English poet and critic (b. 1823)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Coventry_Patmore\" title=\"Coventry Patmore\">Coventry Patmore</a>, English poet and critic (b. 1823)", "links": [{"title": "Coventry Patmore", "link": "https://wikipedia.org/wiki/Coventry_Patmore"}]}, {"year": "1912", "text": "<PERSON> of Constantinople (b. 1834)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON> of Constantinople</a> (b. 1834)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople\" title=\"<PERSON> of Constantinople\"><PERSON> of Constantinople</a> (b. 1834)", "links": [{"title": "<PERSON> of Constantinople", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Constantinople"}]}, {"year": "1917", "text": "<PERSON>, Scottish surgeon and suffragette (b. 1864)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and suffragette (b. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish surgeon and suffragette (b. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, Mexican general (b. 1868)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngeles\" title=\"<PERSON>\"><PERSON></a>, Mexican general (b. 1868)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%C3%81ngeles\" title=\"<PERSON>\"><PERSON></a>, Mexican general (b. 1868)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Felipe_%C3%81ngeles"}]}, {"year": "1920", "text": "<PERSON><PERSON>, Ukrainian anarchist military commander (b. 1893)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Se<PERSON>_Ka<PERSON>k\" title=\"Se<PERSON> Karet<PERSON>k\"><PERSON><PERSON></a>, Ukrainian anarchist military commander (b. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"Se<PERSON> Ka<PERSON>k\"><PERSON><PERSON></a>, Ukrainian anarchist military commander (b. 1893)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Semen_<PERSON>k"}]}, {"year": "1926", "text": "<PERSON>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (b. 1854)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (b. 1854)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English barrister, journalist, philosopher, men's rights advocate, socialist and historian (b. 1854)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American weapons designer, founded the Browning Arms Company (b. 1855)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American weapons designer, founded the <a href=\"https://wikipedia.org/wiki/Browning_Arms_Company\" title=\"Browning Arms Company\">Browning Arms Company</a> (b. 1855)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON> Browning\"><PERSON></a>, American weapons designer, founded the <a href=\"https://wikipedia.org/wiki/Browning_Arms_Company\" title=\"Browning Arms Company\">Browning Arms Company</a> (b. 1855)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Browning Arms Company", "link": "https://wikipedia.org/wiki/Browning_Arms_Company"}]}, {"year": "1928", "text": "<PERSON><PERSON><PERSON>, German admiral (b. 1863)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German admiral (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German admiral (b. 1863)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON>, Scottish-Australian politician, 18th Premier of South Australia (b. 1850)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Scottish-Australian politician, 18th <a href=\"https://wikipedia.org/wiki/Premier_of_South_Australia\" title=\"Premier of South Australia\">Premier of South Australia</a> (b. 1850)", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}, {"title": "Premier of South Australia", "link": "https://wikipedia.org/wiki/Premier_of_South_Australia"}]}, {"year": "1934", "text": "<PERSON><PERSON><PERSON>, Ukrainian historian and politician (b. 1866)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian historian and politician (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Ukrainian historian and politician (b. 1866)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Turkish general (b. 1876)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Naili_G%C3%B6kberk\" title=\"Şükrü Naili Gökberk\">Şükrü Naili Gökberk</a>, Turkish general (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Naili_G%C3%B6kberk\" title=\"Şükrü Naili Gökberk\"><PERSON>ükr<PERSON> Naili Gökberk</a>, Turkish general (b. 1876)", "links": [{"title": "Şükrü Naili Gökberk", "link": "https://wikipedia.org/wiki/%C5%9E%C3%BCkr%C3%BC_Naili_G%C3%B6kberk"}]}, {"year": "1937", "text": "<PERSON><PERSON><PERSON>, Lithuanian general (b. 1860)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%BDuka<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian general (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_%C5%BD<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian general (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Silvestras_%C5%BDuka<PERSON><PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Canadian lawyer and politician, 18th Canadian Minister of Justice (b. 1876)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and politician, 18th <a href=\"https://wikipedia.org/wiki/Minister_of_Justice_(Canada)\" class=\"mw-redirect\" title=\"Minister of Justice (Canada)\">Canadian Minister of Justice</a> (b. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Minister of Justice (Canada)", "link": "https://wikipedia.org/wiki/Minister_of_Justice_(Canada)"}]}, {"year": "1943", "text": "<PERSON>, American lieutenant and pilot (b. 1914)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Hare\" title=\"<PERSON>\"><PERSON></a>, American lieutenant and pilot (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Edward_O%27Hare"}]}, {"year": "1943", "text": "<PERSON>, American litterateur and poet (b. 1865)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American litterateur and poet (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American litterateur and poet (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON><PERSON><PERSON>, German writer (b. 1867)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, German writer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>-<PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, German writer (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>-<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, Swedish geographer and explorer (b. 1865)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish geographer and explorer (b. 1865)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish geographer and explorer (b. 1865)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American baseball player and coach (b. 1891)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach (b. 1891)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American trombonist, trumpet player, and composer (b. 1905)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, trumpet player, and composer (b. 1905)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist, trumpet player, and composer (b. 1905)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, English pianist, composer, and conductor (b. 1875)", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8lbey\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8l<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English pianist, composer, and conductor (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A8lbey"}]}, {"year": "1962", "text": "<PERSON>, French lawyer and politician, 106th Prime Minister of France (b. 1872)", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French lawyer and politician, 106th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a> (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1963", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Italian soprano (b. 1882)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soprano (b. 1882)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian soprano (b. 1882)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Amelita_Gall<PERSON>-<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, Italian priest and publisher (b. 1884)", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Giacomo Alberione\"><PERSON></a>, Italian priest and publisher (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"Giacomo Al<PERSON>\"><PERSON></a>, Italian priest and publisher (b. 1884)", "links": [{"title": "Giacomo <PERSON>", "link": "https://wikipedia.org/wiki/Giacomo_Alberione"}]}, {"year": "1973", "text": "<PERSON>, English bass player and songwriter (b. 1942)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and songwriter (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, English author and critic (b. 1903)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and critic (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 43rd <PERSON><PERSON><PERSON><PERSON> (b. 1920)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_Jun<PERSON>uke\" title=\"<PERSON><PERSON><PERSON><PERSON> Junnosuke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 43rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON><PERSON>#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>uke\" title=\"<PERSON><PERSON><PERSON><PERSON> Jun<PERSON>uke\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 43rd <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1978", "text": "<PERSON>, American director and screenwriter (b. 1888)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Beebe\" title=\"Ford Beebe\"><PERSON></a>, American director and screenwriter (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ford_Beebe\" title=\"Ford Beebe\"><PERSON></a>, American director and screenwriter (b. 1888)", "links": [{"title": "Ford Beebe", "link": "https://wikipedia.org/wiki/Ford_Beebe"}]}, {"year": "1978", "text": "<PERSON>, American trombonist (b. 1926)", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trombonist (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Frank_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American race car driver (b. 1898)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1898)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Dutch chess player, mathematician, and author  (b. 1901)", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player, mathematician, and author (b. 1901)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch chess player, mathematician, and author (b. 1901)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>we"}]}, {"year": "1982", "text": "<PERSON><PERSON>, Estonian composer and conductor (b. 1884)", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Estonian composer and conductor (b. 1884)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, American surgeon and academic (b. 1910)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American surgeon and academic (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American surgeon and academic (b. 1910)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Viv<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Aruban activist and politician (b. 1938)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Betico_Croes\" title=\"Betico Croes\"><PERSON><PERSON></a>, Aruban activist and politician (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Betico_Croes\" title=\"Betico Croes\"><PERSON><PERSON></a>, Aruban activist and politician (b. 1938)", "links": [{"title": "Betico Croes", "link": "https://wikipedia.org/wiki/Betico_Croes"}]}, {"year": "1987", "text": "<PERSON>, Jr., American colonel and pilot (b. 1915)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American colonel and pilot (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr.\" class=\"mw-redirect\" title=\"<PERSON>, Jr.\"><PERSON>, Jr.</a>, American colonel and pilot (b. 1915)", "links": [{"title": "<PERSON>, Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Jr."}]}, {"year": "1987", "text": "<PERSON><PERSON> <PERSON><PERSON>, American psychologist and academic (b. 1897)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J<PERSON> <PERSON>. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American psychologist and academic (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"J. P. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American psychologist and academic (b. 1897)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American photographer (b. 1934)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1934)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer (b. 1934)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, Comorian politician, President of Comoros (b. 1919)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Comorian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Comoros\" title=\"List of heads of state of the Comoros\">President of Comoros</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Comorian politician, <a href=\"https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Comoros\" title=\"List of heads of state of the Comoros\">President of Comoros</a> (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "List of heads of state of the Comoros", "link": "https://wikipedia.org/wiki/List_of_heads_of_state_of_the_Comoros"}]}, {"year": "1991", "text": "<PERSON>, American engineer (b. 1908)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, American ice hockey player and coach (b. 1931)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1931)\" title=\"<PERSON> (ice hockey, born 1931)\"><PERSON></a>, American ice hockey player and coach (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1931)\" title=\"<PERSON> (ice hockey, born 1931)\"><PERSON></a>, American ice hockey player and coach (b. 1931)", "links": [{"title": "<PERSON> (ice hockey, born 1931)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey,_born_1931)"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, Brazilian violinist, composer, and conductor (b. 1914)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/C%C3%A9sar_Guerra-Peixe\" title=\"<PERSON>\"><PERSON></a>, Brazilian violinist, composer, and conductor (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>-<PERSON>eixe\" title=\"<PERSON>\"><PERSON></a>, Brazilian violinist, composer, and conductor (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/C%C3%A9<PERSON>_<PERSON>uerra-Peixe"}]}, {"year": "1994", "text": "<PERSON>, English car designer (b. 1925)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English car designer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON>, Salvadoran archbishop (b. 1923)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Salvadoran archbishop (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> y <PERSON>\"><PERSON></a>, Salvadoran archbishop (b. 1923)", "links": [{"title": "<PERSON> y <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, English actor and screenwriter (b. 1922)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and screenwriter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1996", "text": "<PERSON>, American art director and graphic designer (b. 1914)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art director and graphic designer (b. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American art director and graphic designer (b. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American author (b. 1902)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (b. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, American journalist and author (b. 1941)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1941)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1941)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Finnish author, poet, and painter (b. 1943)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>ap%C3%A4%C3%A4\" title=\"<PERSON><PERSON>-<PERSON><PERSON>ap<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Finnish author, poet, and painter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON><PERSON>_<PERSON>ap%C3%A4%C3%A4\" title=\"<PERSON><PERSON>-<PERSON><PERSON> Val<PERSON>apä<PERSON>\"><PERSON><PERSON>-<PERSON><PERSON></a>, Finnish author, poet, and painter (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ni<PERSON>-<PERSON><PERSON>_Valkeap%C3%A4%C3%A4"}]}, {"year": "2002", "text": "<PERSON>, Cuban singer-songwriter (b. 1955)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/Polo_Monta%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Cuban singer-songwriter (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Polo_Monta%C3%B1ez\" title=\"<PERSON>\"><PERSON></a>, Cuban singer-songwriter (b. 1955)", "links": [{"title": "Polo <PERSON>ñez", "link": "https://wikipedia.org/wiki/Polo_Monta%C3%B1ez"}]}, {"year": "2002", "text": "<PERSON><PERSON>, American businessman, founded Winchell's Donuts (b. 1915)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l\" title=\"<PERSON><PERSON> Winchell\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Winchell%27s_Donuts\" title=\"Win<PERSON>l's Donuts\">Winchell's Donuts</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>l\" title=\"<PERSON><PERSON> Winchell\"><PERSON><PERSON></a>, American businessman, founded <a href=\"https://wikipedia.org/wiki/Winchell%27s_Donuts\" title=\"Win<PERSON>l's Donuts\">Winchell's Donuts</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON>_<PERSON>l"}, {"title": "Winchell's Donuts", "link": "https://wikipedia.org/wiki/Winchell%27s_Donuts"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American rapper (b. 1977)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slim\" title=\"Soul<PERSON> Slim\"><PERSON><PERSON></a>, American rapper (b. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Slim\" title=\"Soul<PERSON> Slim\"><PERSON><PERSON></a>, American rapper (b. 1977)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, French surgeon and author (b. 1922)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and author (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French surgeon and author (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON>, French actor, director, and screenwriter (b. 1933)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French actor, director, and screenwriter (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2004", "text": "<PERSON><PERSON>, English author and illustrator (b. 1909)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English author and illustrator (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON> <PERSON></a>, English author and illustrator (b. 1909)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON><PERSON>, Japanese composer and conductor (b. 1951)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese composer and conductor (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American author and illustrator, co-created the Berenstain Bears (b. 1923)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American author and illustrator, co-created the <i><a href=\"https://wikipedia.org/wiki/Berenstain_Bears\" title=\"Berenstain Bears\">Berenstain Bears</a></i> (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"<PERSON> and <PERSON>\"><PERSON></a>, American author and illustrator, co-created the <i><a href=\"https://wikipedia.org/wiki/Berenstain_Bears\" title=\"Berenstain Bears\">Berenstain Bears</a></i> (b. 1923)", "links": [{"title": "<PERSON> and <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}, {"title": "Berenstain Bears", "link": "https://wikipedia.org/wiki/Berenstain_Bears"}]}, {"year": "2005", "text": "<PERSON>, American drummer (b. 1952)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1952)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American drummer (b. 1952)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON>, Portuguese painter and poet (b. 1923)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Cesariny_de_Vasconcelos\" title=\"<PERSON><PERSON><PERSON> Vasconcelos\"><PERSON><PERSON><PERSON></a>, Portuguese painter and poet (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M%C3%A1rio_Cesariny_de_Vasconcelos\" title=\"<PERSON><PERSON><PERSON> Vasconcelos\"><PERSON><PERSON><PERSON></a>, Portuguese painter and poet (b. 1923)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M%C3%A1rio_Cesariny_de_Vasconcelos"}]}, {"year": "2006", "text": "<PERSON>, American author and illustrator (b. 1943)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON>, Spanish cyclist (b. 1975)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lvez\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lvez\" title=\"<PERSON>\"><PERSON></a>, Spanish cyclist (b. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Isaac_G%C3%A1lvez"}]}, {"year": "2006", "text": "<PERSON><PERSON>, Mexican television host and producer (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_Velasco\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican television host and producer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ra%C3%BAl_V<PERSON>sco\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Mexican television host and producer (b. 1933)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ra%C3%BAl_Velasco"}]}, {"year": "2007", "text": "<PERSON><PERSON><PERSON>, Mexican-American sergeant, Medal of Honor recipient (b. 1917)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Mexican-American sergeant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (b. 1917)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "2007", "text": "<PERSON>, Russian-Canadian screenwriter and producer (b. 1913)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian screenwriter and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Canadian screenwriter and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, Jamaican sprinter (b. 1922)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Jamaican sprinter (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American songwriter (b. 1936)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON>, American painter and academic (b. 1942)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Cleary\" title=\"Manon Cleary\"><PERSON><PERSON></a>, American painter and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Man<PERSON>_Cleary\" title=\"Manon Cleary\"><PERSON><PERSON></a>, American painter and academic (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Manon_Cleary"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Filipino actor, director, and screenwriter (b. 1943)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Ce<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, director, and screenwriter (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ce<PERSON><PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Filipino actor, director, and screenwriter (b. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Australian table tennis player (b. 1948)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian table tennis player (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(athlete)\" title=\"<PERSON> (athlete)\"><PERSON></a>, Australian table tennis player (b. 1948)", "links": [{"title": "<PERSON> (athlete)", "link": "https://wikipedia.org/wiki/<PERSON>(athlete)"}]}, {"year": "2012", "text": "<PERSON>, American surgeon and soldier, Nobel Prize laureate (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and soldier, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American surgeon and soldier, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1919)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "2012", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian author and translator (b. 1919)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/M._C._Nambudiripad\" title=\"M. C. Nambudiripad\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and translator (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M._C._Nambudiripad\" title=\"M. C. Nambudiripad\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian author and translator (b. 1919)", "links": [{"title": "M. C. <PERSON>", "link": "https://wikipedia.org/wiki/M._C._Nambudiripad"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Israeli singer-songwriter (b. 1939)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter (b. 1939)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli singer-songwriter (b. 1939)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actress and singer (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American photographer and painter (b. 1923)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and painter (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American photographer and painter (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American actor and screenwriter (b. 1936)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor and screenwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, American dancer and choreographer (b. 1925)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American dancer and choreographer (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON>, Canadian ice hockey player and sportscaster (b. 1938)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(ice_hockey)\" title=\"<PERSON> (ice hockey)\"><PERSON></a>, Canadian ice hockey player and sportscaster (b. 1938)", "links": [{"title": "<PERSON> (ice hockey)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(ice_hockey)"}]}, {"year": "2014", "text": "<PERSON>, English parapsychologist and author (b. 1932)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(parapsychologist)\" title=\"<PERSON> (parapsychologist)\"><PERSON></a>, English parapsychologist and author (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(parapsychologist)\" title=\"<PERSON> (parapsychologist)\"><PERSON></a>, English parapsychologist and author (b. 1932)", "links": [{"title": "<PERSON> (parapsychologist)", "link": "https://wikipedia.org/wiki/<PERSON>_(parapsychologist)"}]}, {"year": "2015", "text": "<PERSON>, Israeli-American mathematician, historian, and academic (b. 1950)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American mathematician, historian, and academic (b. 1950)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Israeli-American mathematician, historian, and academic (b. 1950)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American basketball player and coach (b. 1922)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American actor (b. 1926)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American animator, voice actor, and marine science educator (b. 1961)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, voice actor, and marine science educator (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, voice actor, and marine science educator (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American composer and lyricist (b. 1930)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and lyricist (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and lyricist (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2022", "text": "<PERSON><PERSON><PERSON>, Indian actor and director (b. 1945)", "html": "2022 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian actor and director (b. 1945)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, American film director and writer (b. 1944)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and writer (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American film director and writer (b. 1944)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}]}}