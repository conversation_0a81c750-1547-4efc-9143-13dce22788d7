{"date": "May 2", "url": "https://wikipedia.org/wiki/May_2", "data": {"Events": [{"year": "1194", "text": "King <PERSON> of England gives Portsmouth its first royal charter.", "html": "1194 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> I of England\"><PERSON> of England</a> gives <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a> its first <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a>.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> of England\"><PERSON> of England</a> gives <a href=\"https://wikipedia.org/wiki/Portsmouth\" title=\"Portsmouth\">Portsmouth</a> its first <a href=\"https://wikipedia.org/wiki/Royal_charter\" title=\"Royal charter\">royal charter</a>.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Portsmouth", "link": "https://wikipedia.org/wiki/Portsmouth"}, {"title": "Royal charter", "link": "https://wikipedia.org/wiki/Royal_charter"}]}, {"year": "1230", "text": "<PERSON> is hanged by Prince <PERSON><PERSON><PERSON><PERSON> the <PERSON>.", "html": "1230 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1230)\" title=\"<PERSON> (died 1230)\"><PERSON></a> is hanged by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1230)\" title=\"<PERSON> (died 1230)\"><PERSON></a> is hanged by <PERSON> <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON> the Great\"><PERSON><PERSON><PERSON><PERSON> the Great</a>.", "links": [{"title": "<PERSON> (died 1230)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1230)"}, {"title": "<PERSON><PERSON><PERSON><PERSON> the Great", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_the_Great"}]}, {"year": "1536", "text": "<PERSON>, Queen of England, is arrested and imprisoned on charges of adultery, incest, treason and witchcraft.", "html": "1536 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">Queen of England</a>, is arrested and imprisoned on charges of <a href=\"https://wikipedia.org/wiki/Adultery\" title=\"Adultery\">adultery</a>, <a href=\"https://wikipedia.org/wiki/Incest\" title=\"Incest\">incest</a>, <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> and <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Queen_consort\" title=\"Queen consort\">Queen of England</a>, is arrested and imprisoned on charges of <a href=\"https://wikipedia.org/wiki/Adultery\" title=\"Adultery\">adultery</a>, <a href=\"https://wikipedia.org/wiki/Incest\" title=\"Incest\">incest</a>, <a href=\"https://wikipedia.org/wiki/Treason\" title=\"Treason\">treason</a> and <a href=\"https://wikipedia.org/wiki/Witchcraft\" title=\"Witchcraft\">witchcraft</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Queen consort", "link": "https://wikipedia.org/wiki/Queen_consort"}, {"title": "Adultery", "link": "https://wikipedia.org/wiki/Adultery"}, {"title": "Incest", "link": "https://wikipedia.org/wiki/Incest"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Treason"}, {"title": "Witchcraft", "link": "https://wikipedia.org/wiki/Witchcraft"}]}, {"year": "1559", "text": "<PERSON> returns from exile to Scotland to become the leader of the nascent Scottish Reformation.", "html": "1559 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns from exile to Scotland to become the leader of the nascent <a href=\"https://wikipedia.org/wiki/Scottish_Reformation\" title=\"Scottish Reformation\">Scottish Reformation</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> returns from exile to Scotland to become the leader of the nascent <a href=\"https://wikipedia.org/wiki/Scottish_Reformation\" title=\"Scottish Reformation\">Scottish Reformation</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Scottish Reformation", "link": "https://wikipedia.org/wiki/Scottish_Reformation"}]}, {"year": "1568", "text": "<PERSON>, Queen of Scots, escapes from Lochleven Castle.", "html": "1568 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, escapes from <a href=\"https://wikipedia.org/wiki/Lochleven_Castle\" title=\"Lochleven Castle\">Lochleven Castle</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots\" title=\"<PERSON>, Queen of Scots\"><PERSON>, Queen of Scots</a>, escapes from <a href=\"https://wikipedia.org/wiki/Lochleven_Castle\" title=\"Lochleven Castle\">Lochleven Castle</a>.", "links": [{"title": "<PERSON>, Queen of Scots", "link": "https://wikipedia.org/wiki/<PERSON>,_Queen_of_Scots"}, {"title": "Lochleven Castle", "link": "https://wikipedia.org/wiki/Lochleven_Castle"}]}, {"year": "1611", "text": "The King James Version of the Bible is published for the first time in London, England, by printer <PERSON>.", "html": "1611 - The <a href=\"https://wikipedia.org/wiki/King_James_Version\" title=\"King James Version\">King <PERSON> Version</a> of the <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a> is published for the first time in London, England, by printer <a href=\"https://wikipedia.org/wiki/<PERSON>(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/King_James_Version\" title=\"King James Version\">King <PERSON> Version</a> of the <a href=\"https://wikipedia.org/wiki/Bible\" title=\"Bible\">Bible</a> is published for the first time in London, England, by printer <a href=\"https://wikipedia.org/wiki/<PERSON>(printer)\" title=\"<PERSON> (printer)\"><PERSON></a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Bible", "link": "https://wikipedia.org/wiki/Bible"}, {"title": "<PERSON> (printer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(printer)"}]}, {"year": "1625", "text": "<PERSON><PERSON><PERSON>, appointed by <PERSON> <PERSON> as Latin Patriarch of Ethiopia, arrives at Beilul from Goa.", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, appointed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Gregory XV\"><PERSON> <PERSON> XV</a> as <a href=\"https://wikipedia.org/wiki/Latin_Patriarchate_of_Ethiopia\" title=\"Latin Patriarchate of Ethiopia\">Latin Patriarch of Ethiopia</a>, arrives at <a href=\"https://wikipedia.org/wiki/Beilul\" title=\"Beil<PERSON>\">Be<PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, appointed by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> Gregory XV\"><PERSON> XV</a> as <a href=\"https://wikipedia.org/wiki/Latin_Patriarchate_of_Ethiopia\" title=\"Latin Patriarchate of Ethiopia\">Latin Patriarch of Ethiopia</a>, arrives at <a href=\"https://wikipedia.org/wiki/Beilul\" title=\"Beil<PERSON>\">Be<PERSON><PERSON></a> from <a href=\"https://wikipedia.org/wiki/Goa\" title=\"Goa\">Goa</a>.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Latin Patriarchate of Ethiopia", "link": "https://wikipedia.org/wiki/Latin_Patriarchate_of_Ethiopia"}, {"title": "Be<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Beilul"}, {"title": "Goa", "link": "https://wikipedia.org/wiki/Goa"}]}, {"year": "1670", "text": "King <PERSON> of England grants a permanent charter to the Hudson's Bay Company to open up the fur trade in North America.", "html": "1670 - King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> grants a permanent charter to the <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a> to open up the <a href=\"https://wikipedia.org/wiki/Fur_trade\" title=\"Fur trade\">fur trade</a> in North America.", "no_year_html": "King <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"<PERSON> II of England\"><PERSON> of England</a> grants a permanent charter to the <a href=\"https://wikipedia.org/wiki/Hudson%27s_Bay_Company\" title=\"Hudson's Bay Company\">Hudson's Bay Company</a> to open up the <a href=\"https://wikipedia.org/wiki/Fur_trade\" title=\"Fur trade\">fur trade</a> in North America.", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}, {"title": "Hudson's Bay Company", "link": "https://wikipedia.org/wiki/Hudson%27s_Bay_Company"}, {"title": "Fur trade", "link": "https://wikipedia.org/wiki/Fur_trade"}]}, {"year": "1808", "text": "Outbreak of the Peninsular War: The people of Madrid rise up in rebellion against French occupation. <PERSON> later memorializes this event in his painting The Second of May 1808.", "html": "1808 - Outbreak of the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The people of <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> <a href=\"https://wikipedia.org/wiki/Dos_de_Mayo_Uprising\" title=\"Dos de Mayo Uprising\">rise up in rebellion</a> against French occupation. <a href=\"https://wikipedia.org/wiki/Francisco_de_Goya\" class=\"mw-redirect\" title=\"Francisco de Goya\"><PERSON> Goya</a> later memorializes this event in his painting <i><a href=\"https://wikipedia.org/wiki/The_Second_of_May_1808\" title=\"The Second of May 1808\">The Second of May 1808</a></i>.", "no_year_html": "Outbreak of the <a href=\"https://wikipedia.org/wiki/Peninsular_War\" title=\"Peninsular War\">Peninsular War</a>: The people of <a href=\"https://wikipedia.org/wiki/Madrid\" title=\"Madrid\">Madrid</a> <a href=\"https://wikipedia.org/wiki/Dos_de_Mayo_Uprising\" title=\"Dos de Mayo Uprising\">rise up in rebellion</a> against French occupation. <a href=\"https://wikipedia.org/wiki/Francisco_de_Goya\" class=\"mw-redirect\" title=\"Francisco de Goya\"><PERSON> Goya</a> later memorializes this event in his painting <i><a href=\"https://wikipedia.org/wiki/The_Second_of_May_1808\" title=\"The Second of May 1808\">The Second of May 1808</a></i>.", "links": [{"title": "Peninsular War", "link": "https://wikipedia.org/wiki/Peninsular_War"}, {"title": "Madrid", "link": "https://wikipedia.org/wiki/Madrid"}, {"title": "Dos de Mayo Uprising", "link": "https://wikipedia.org/wiki/Dos_de_Mayo_Uprising"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Francisco_<PERSON>_<PERSON>"}, {"title": "The Second of May 1808", "link": "https://wikipedia.org/wiki/The_Second_of_May_1808"}]}, {"year": "1812", "text": "The Siege of Cuautla during the Mexican War of Independence ends with both sides claiming victory.", "html": "1812 - The <a href=\"https://wikipedia.org/wiki/Siege_of_Cuautla\" title=\"Siege of Cuautla\">Siege of Cuautla</a> during the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a> ends with both sides claiming victory.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Siege_of_Cuautla\" title=\"Siege of Cuautla\">Siege of Cuautla</a> during the <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexican War of Independence</a> ends with both sides claiming victory.", "links": [{"title": "Siege of Cuautla", "link": "https://wikipedia.org/wiki/Siege_of_Cuautla"}, {"title": "Mexican War of Independence", "link": "https://wikipedia.org/wiki/Mexican_War_of_Independence"}]}, {"year": "1829", "text": "After anchoring nearby, Captain <PERSON> of HMS Challenger, declares the Swan River Colony in Australia.", "html": "1829 - After anchoring nearby, Captain <a href=\"https://wikipedia.org/wiki/Charles_Fremantle\" title=\"Charles Fremantle\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/HMS_Challenger_(1826)\" title=\"HMS Challenger (1826)\">HMS <i>Challenger</i></a>, declares the <a href=\"https://wikipedia.org/wiki/Swan_River_Colony\" title=\"Swan River Colony\">Swan River Colony</a> in Australia.", "no_year_html": "After anchoring nearby, Captain <a href=\"https://wikipedia.org/wiki/Charles_Fremantle\" title=\"Charles Fremantle\"><PERSON></a> of <a href=\"https://wikipedia.org/wiki/HMS_Challenger_(1826)\" title=\"HMS Challenger (1826)\">HMS <i>Challenger</i></a>, declares the <a href=\"https://wikipedia.org/wiki/Swan_River_Colony\" title=\"Swan River Colony\">Swan River Colony</a> in Australia.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Charles_<PERSON>"}, {"title": "HMS Challenger (1826)", "link": "https://wikipedia.org/wiki/HMS_Challenger_(1826)"}, {"title": "Swan River Colony", "link": "https://wikipedia.org/wiki/Swan_River_Colony"}]}, {"year": "1863", "text": "American Civil War: <PERSON><PERSON> is wounded by friendly fire while returning to camp after reconnoitering during the Battle of Chancellorsville. He succumbs to pneumonia eight days later.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Stone<PERSON>_Jackson\" title=\"Stone<PERSON> Jackson\"><PERSON><PERSON></a> is wounded by friendly fire while returning to camp after reconnoitering during the <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a>. He succumbs to <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a> eight days later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Civil_War\" title=\"American Civil War\">American Civil War</a>: <a href=\"https://wikipedia.org/wiki/Stone<PERSON>_Jackson\" title=\"Stone<PERSON> Jackson\"><PERSON><PERSON></a> is wounded by friendly fire while returning to camp after reconnoitering during the <a href=\"https://wikipedia.org/wiki/Battle_of_Chancellorsville\" title=\"Battle of Chancellorsville\">Battle of Chancellorsville</a>. He succumbs to <a href=\"https://wikipedia.org/wiki/Pneumonia\" title=\"Pneumonia\">pneumonia</a> eight days later.", "links": [{"title": "American Civil War", "link": "https://wikipedia.org/wiki/American_Civil_War"}, {"title": "<PERSON><PERSON> Jackson", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Jackson"}, {"title": "Battle of Chancellorsville", "link": "https://wikipedia.org/wiki/Battle_of_Chancellorsville"}, {"title": "Pneumonia", "link": "https://wikipedia.org/wiki/Pneumonia"}]}, {"year": "1866", "text": "Peruvian defenders fight off the Spanish fleet at the Battle of Callao.", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Peruvian\" class=\"mw-redirect\" title=\"Peruvian\">Peruvian</a> defenders fight off the Spanish fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Callao\" title=\"Battle of Callao\">Battle of Callao</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Peruvian\" class=\"mw-redirect\" title=\"Peruvian\">Peruvian</a> defenders fight off the Spanish fleet at the <a href=\"https://wikipedia.org/wiki/Battle_of_Callao\" title=\"Battle of Callao\">Battle of Callao</a>.", "links": [{"title": "Peruvian", "link": "https://wikipedia.org/wiki/Peruvian"}, {"title": "Battle of Callao", "link": "https://wikipedia.org/wiki/Battle_of_Callao"}]}, {"year": "1876", "text": "The April Uprising breaks out in Ottoman Bulgaria.", "html": "1876 - The <a href=\"https://wikipedia.org/wiki/April_Uprising\" class=\"mw-redirect\" title=\"April Uprising\">April Uprising</a> breaks out in <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman Bulgaria</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/April_Uprising\" class=\"mw-redirect\" title=\"April Uprising\">April Uprising</a> breaks out in <a href=\"https://wikipedia.org/wiki/Ottoman_Bulgaria\" title=\"Ottoman Bulgaria\">Ottoman Bulgaria</a>.", "links": [{"title": "April Uprising", "link": "https://wikipedia.org/wiki/April_Uprising"}, {"title": "Ottoman Bulgaria", "link": "https://wikipedia.org/wiki/Ottoman_Bulgaria"}]}, {"year": "1885", "text": "<PERSON><PERSON> and Assiniboine warriors win the Battle of Cut Knife, their largest victory over Canadian forces during the North-West Rebellion.", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Cree\" title=\"Cree\">Cree</a> and <a href=\"https://wikipedia.org/wiki/Assiniboine\" title=\"Assiniboine\">Assiniboine</a> warriors win the <a href=\"https://wikipedia.org/wiki/Battle_of_Cut_Knife\" title=\"Battle of Cut Knife\">Battle of Cut Knife</a>, their largest victory over Canadian forces during the <a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cree\" title=\"Cree\">Cree</a> and <a href=\"https://wikipedia.org/wiki/Assiniboine\" title=\"Assiniboine\">Assiniboine</a> warriors win the <a href=\"https://wikipedia.org/wiki/Battle_of_Cut_Knife\" title=\"Battle of Cut Knife\">Battle of Cut Knife</a>, their largest victory over Canadian forces during the <a href=\"https://wikipedia.org/wiki/North-West_Rebellion\" title=\"North-West Rebellion\">North-West Rebellion</a>.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cree"}, {"title": "Assiniboine", "link": "https://wikipedia.org/wiki/<PERSON>sinib<PERSON>ne"}, {"title": "Battle of Cut Knife", "link": "https://wikipedia.org/wiki/Battle_of_Cut_Knife"}, {"title": "North-West Rebellion", "link": "https://wikipedia.org/wiki/North-West_Rebellion"}]}, {"year": "1889", "text": "<PERSON><PERSON><PERSON> <PERSON>, Emperor of Ethiopia, signs the Treaty of Wuchale, giving Italy control over Eritrea.", "html": "1889 - <a href=\"https://wikipedia.org/wiki/Menelik_II\" title=\"Menelik II\"><PERSON><PERSON><PERSON> II</a>, Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Wuchale\" title=\"Treaty of Wuchale\">Treaty of Wuchale</a>, giving Italy control over <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Men<PERSON><PERSON>_II\" title=\"Menelik II\"><PERSON><PERSON><PERSON> II</a>, Emperor of <a href=\"https://wikipedia.org/wiki/Ethiopia\" title=\"Ethiopia\">Ethiopia</a>, signs the <a href=\"https://wikipedia.org/wiki/Treaty_of_Wuchale\" title=\"Treaty of Wuchale\">Treaty of Wuchale</a>, giving Italy control over <a href=\"https://wikipedia.org/wiki/Eritrea\" title=\"Eritrea\">Eritrea</a>.", "links": [{"title": "Menelik II", "link": "https://wikipedia.org/wiki/<PERSON>elik_II"}, {"title": "Ethiopia", "link": "https://wikipedia.org/wiki/Ethiopia"}, {"title": "Treaty of Wuchale", "link": "https://wikipedia.org/wiki/Treaty_of_Wuchale"}, {"title": "Eritrea", "link": "https://wikipedia.org/wiki/Eritrea"}]}, {"year": "1906", "text": "Closing ceremony of the Intercalated Games in Athens, Greece.", "html": "1906 - Closing ceremony of the <a href=\"https://wikipedia.org/wiki/1906_Intercalated_Games\" title=\"1906 Intercalated Games\">Intercalated Games</a> in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece.", "no_year_html": "Closing ceremony of the <a href=\"https://wikipedia.org/wiki/1906_Intercalated_Games\" title=\"1906 Intercalated Games\">Intercalated Games</a> in <a href=\"https://wikipedia.org/wiki/Athens\" title=\"Athens\">Athens</a>, Greece.", "links": [{"title": "1906 Intercalated Games", "link": "https://wikipedia.org/wiki/1906_Intercalated_Games"}, {"title": "Athens", "link": "https://wikipedia.org/wiki/Athens"}]}, {"year": "1920", "text": "The first game of the Negro National League baseball is played in Indianapolis.", "html": "1920 - The first game of the <a href=\"https://wikipedia.org/wiki/Negro_National_League_(1920%E2%80%9331)\" class=\"mw-redirect\" title=\"Negro National League (1920-31)\">Negro National League baseball</a> is played in <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>.", "no_year_html": "The first game of the <a href=\"https://wikipedia.org/wiki/Negro_National_League_(1920%E2%80%9331)\" class=\"mw-redirect\" title=\"Negro National League (1920-31)\">Negro National League baseball</a> is played in <a href=\"https://wikipedia.org/wiki/Indianapolis\" title=\"Indianapolis\">Indianapolis</a>.", "links": [{"title": "Negro National League (1920-31)", "link": "https://wikipedia.org/wiki/Negro_National_League_(1920%E2%80%9331)"}, {"title": "Indianapolis", "link": "https://wikipedia.org/wiki/Indianapolis"}]}, {"year": "1933", "text": "Germany's independent labor unions are replaced by the German Labour Front.", "html": "1933 - Germany's independent labor unions are replaced by the <a href=\"https://wikipedia.org/wiki/German_Labour_Front\" title=\"German Labour Front\">German Labour Front</a>.", "no_year_html": "Germany's independent labor unions are replaced by the <a href=\"https://wikipedia.org/wiki/German_Labour_Front\" title=\"German Labour Front\">German Labour Front</a>.", "links": [{"title": "German Labour Front", "link": "https://wikipedia.org/wiki/German_Labour_Front"}]}, {"year": "1941", "text": "World War II: Following the coup d'état against Iraq Crown <PERSON> <PERSON><PERSON> earlier that year, the United Kingdom launches the Anglo-Iraqi War to restore him to power.", "html": "1941 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Following the <i><a href=\"https://wikipedia.org/wiki/1941_Iraqi_coup_d%27%C3%A9tat\" title=\"1941 Iraqi coup d'état\">coup d'état</a></i> against <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> Crown Prince <a href=\"https://wikipedia.org/wiki/%27Abd_al-<PERSON>ah\" class=\"mw-redirect\" title=\"'<PERSON>\">'<PERSON></a> earlier that year, the United Kingdom launches the <a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a> to restore him to power.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Following the <i><a href=\"https://wikipedia.org/wiki/1941_Iraqi_coup_d%27%C3%A9tat\" title=\"1941 Iraqi coup d'état\">coup d'état</a></i> against <a href=\"https://wikipedia.org/wiki/Iraq\" title=\"Iraq\">Iraq</a> Crown Prince <a href=\"https://wikipedia.org/wiki/%27Abd_al-Ilah\" class=\"mw-redirect\" title=\"'<PERSON>\">'<PERSON></a> earlier that year, the United Kingdom launches the <a href=\"https://wikipedia.org/wiki/Anglo-Iraqi_War\" title=\"Anglo-Iraqi War\">Anglo-Iraqi War</a> to restore him to power.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "1941 Iraqi coup d'état", "link": "https://wikipedia.org/wiki/1941_Iraqi_coup_d%27%C3%A9tat"}, {"title": "Iraq", "link": "https://wikipedia.org/wiki/Iraq"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%27Abd_al-<PERSON>"}, {"title": "Anglo-Iraqi War", "link": "https://wikipedia.org/wiki/Anglo-Iraqi_War"}]}, {"year": "1945", "text": "World War II: The Soviet Union announces the fall of Berlin.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> announces the <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin\" title=\"Battle of Berlin\">fall of Berlin</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Soviet_Union\" title=\"Soviet Union\">Soviet Union</a> announces the <a href=\"https://wikipedia.org/wiki/Battle_of_Berlin\" title=\"Battle of Berlin\">fall of Berlin</a>.", "links": [{"title": "Soviet Union", "link": "https://wikipedia.org/wiki/Soviet_Union"}, {"title": "Battle of Berlin", "link": "https://wikipedia.org/wiki/Battle_of_Berlin"}]}, {"year": "1945", "text": "World War II: The surrender of Caserta comes into effect, by which German troops in Italy cease fighting.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Surrender_of_Caserta\" class=\"mw-redirect\" title=\"Surrender of Caserta\">surrender of Caserta</a> comes into effect, by which German troops in Italy cease fighting.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Surrender_of_Caserta\" class=\"mw-redirect\" title=\"Surrender of Caserta\">surrender of Caserta</a> comes into effect, by which German troops in Italy cease fighting.", "links": [{"title": "Surrender of Caserta", "link": "https://wikipedia.org/wiki/Surrender_of_Caserta"}]}, {"year": "1945", "text": "World War II: The US 82nd Airborne Division liberates Wöbbelin concentration camp finding 1,000 dead prisoners, most of whom starved to death.", "html": "1945 - World War II: The US 82nd Airborne Division liberates <a href=\"https://wikipedia.org/wiki/W%C3%B6bbelin_concentration_camp\" title=\"Wöbbelin concentration camp\">Wöbbelin concentration camp</a> finding 1,000 dead prisoners, most of whom starved to death.", "no_year_html": "World War II: The US 82nd Airborne Division liberates <a href=\"https://wikipedia.org/wiki/W%C3%B6bbelin_concentration_camp\" title=\"Wöbbelin concentration camp\">Wöbbelin concentration camp</a> finding 1,000 dead prisoners, most of whom starved to death.", "links": [{"title": "Wöbbelin concentration camp", "link": "https://wikipedia.org/wiki/W%C3%B6bbelin_concentration_camp"}]}, {"year": "1945", "text": "World War II: A death march from Dachau to the Austrian border is halted by the segregated, all-Nisei 522nd Field Artillery Battalion of the U.S. Army in southern Bavaria, saving several hundred prisoners.", "html": "1945 - World War II: A <a href=\"https://wikipedia.org/wiki/Death_marches_(Holocaust)#Dachau_to_the_Austrian_border\" class=\"mw-redirect\" title=\"Death marches (Holocaust)\">death march</a> from <a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau</a> to the Austrian border is halted by the segregated, all-<a href=\"https://wikipedia.org/wiki/Nisei\" title=\"Nisei\">Nisei</a> <a href=\"https://wikipedia.org/wiki/442nd_Infantry_Regiment_(United_States)#522nd_Field_Artillery_Battalion\" title=\"442nd Infantry Regiment (United States)\">522nd Field Artillery Battalion</a> of the U.S. Army in southern Bavaria, saving several hundred prisoners.", "no_year_html": "World War II: A <a href=\"https://wikipedia.org/wiki/Death_marches_(Holocaust)#Dachau_to_the_Austrian_border\" class=\"mw-redirect\" title=\"Death marches (Holocaust)\">death march</a> from <a href=\"https://wikipedia.org/wiki/Dachau_concentration_camp\" title=\"Dachau concentration camp\">Dachau</a> to the Austrian border is halted by the segregated, all-<a href=\"https://wikipedia.org/wiki/Nisei\" title=\"Nisei\">Ni<PERSON></a> <a href=\"https://wikipedia.org/wiki/442nd_Infantry_Regiment_(United_States)#522nd_Field_Artillery_Battalion\" title=\"442nd Infantry Regiment (United States)\">522nd Field Artillery Battalion</a> of the U.S. Army in southern Bavaria, saving several hundred prisoners.", "links": [{"title": "Death marches (Holocaust)", "link": "https://wikipedia.org/wiki/Death_marches_(Holocaust)#Dachau_to_the_Austrian_border"}, {"title": "Dachau concentration camp", "link": "https://wikipedia.org/wiki/Dachau_concentration_camp"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}, {"title": "442nd Infantry Regiment (United States)", "link": "https://wikipedia.org/wiki/442nd_Infantry_Regiment_(United_States)#522nd_Field_Artillery_Battalion"}]}, {"year": "1952", "text": "A De Havilland Comet makes the first jetliner flight with fare-paying passengers, from London to Johannesburg.", "html": "1952 - A <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">De Havilland Comet</a> makes the first jetliner flight with fare-paying passengers, from London to <a href=\"https://wikipedia.org/wiki/Johannesburg\" title=\"Johannesburg\">Johannesburg</a>.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/De_Havilland_Comet\" title=\"De Havilland Comet\">De Havilland Comet</a> makes the first jetliner flight with fare-paying passengers, from London to <a href=\"https://wikipedia.org/wiki/Johannesburg\" title=\"Johannesburg\">Johannesburg</a>.", "links": [{"title": "De Havilland Comet", "link": "https://wikipedia.org/wiki/De_Havilland_Comet"}, {"title": "Johannesburg", "link": "https://wikipedia.org/wiki/Johannesburg"}]}, {"year": "1963", "text": "<PERSON><PERSON> Seliger launches a rocket with three stages and a maximum flight altitude of more than 100 kilometres (62 mi) near Cuxhaven. It is the only sounding rocket developed in Germany.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seliger\" class=\"mw-redirect\" title=\"Berthold Seliger\"><PERSON><PERSON> Seliger</a> launches a rocket with three stages and a maximum flight altitude of more than 100 kilometres (62 mi) near <a href=\"https://wikipedia.org/wiki/Cuxhaven\" title=\"Cuxhaven\">Cuxhaven</a>. It is the only <a href=\"https://wikipedia.org/wiki/Sounding_rocket\" title=\"Sounding rocket\">sounding rocket</a> developed in Germany.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Seliger\" class=\"mw-redirect\" title=\"Berthold Seliger\"><PERSON>hold Seliger</a> launches a rocket with three stages and a maximum flight altitude of more than 100 kilometres (62 mi) near <a href=\"https://wikipedia.org/wiki/Cuxhaven\" title=\"Cuxhaven\">Cuxhaven</a>. It is the only <a href=\"https://wikipedia.org/wiki/Sounding_rocket\" title=\"Sounding rocket\">sounding rocket</a> developed in Germany.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Seliger"}, {"title": "Cuxhaven", "link": "https://wikipedia.org/wiki/Cuxhaven"}, {"title": "Sounding rocket", "link": "https://wikipedia.org/wiki/Sounding_rocket"}]}, {"year": "1964", "text": "Vietnam War: An explosion sinks the American aircraft carrier USNS Card while it is docked at Saigon. Two Viet Cong combat swimmers had placed explosives on the ship's hull. She is raised and returned to service less than seven months later.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_USNS_Card\" title=\"Attack on USNS Card\">An explosion</a> sinks the American aircraft carrier USNS <i>Card</i> while it is docked at Saigon. Two Viet Cong <a href=\"https://wikipedia.org/wiki/Frogman\" title=\"Frogman\">combat swimmers</a> had placed explosives on the ship's hull. She is raised and returned to service less than seven months later.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vietnam_War\" title=\"Vietnam War\">Vietnam War</a>: <a href=\"https://wikipedia.org/wiki/Attack_on_USNS_Card\" title=\"Attack on USNS Card\">An explosion</a> sinks the American aircraft carrier USNS <i>Card</i> while it is docked at Saigon. Two Viet Cong <a href=\"https://wikipedia.org/wiki/Frogman\" title=\"Frogman\">combat swimmers</a> had placed explosives on the ship's hull. She is raised and returned to service less than seven months later.", "links": [{"title": "Vietnam War", "link": "https://wikipedia.org/wiki/Vietnam_War"}, {"title": "Attack on USNS Card", "link": "https://wikipedia.org/wiki/Attack_on_USNS_Card"}, {"title": "Frogman", "link": "https://wikipedia.org/wiki/<PERSON>man"}]}, {"year": "1964", "text": "First ascent of Shishapangma, the fourteenth highest mountain in the world and the lowest of the Eight-thousanders.", "html": "1964 - <a href=\"https://wikipedia.org/wiki/First_ascent\" title=\"First ascent\">First ascent</a> of <a href=\"https://wikipedia.org/wiki/Shishapangma\" title=\"Shishapangma\">Shishapangma</a>, the fourteenth highest <a href=\"https://wikipedia.org/wiki/Mountain\" title=\"Mountain\">mountain</a> in the world and the lowest of the <a href=\"https://wikipedia.org/wiki/Eight-thousander\" title=\"Eight-thousander\">Eight-thousanders</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/First_ascent\" title=\"First ascent\">First ascent</a> of <a href=\"https://wikipedia.org/wiki/Shishapangma\" title=\"Shishapangma\">Shishapangma</a>, the fourteenth highest <a href=\"https://wikipedia.org/wiki/Mountain\" title=\"Mountain\">mountain</a> in the world and the lowest of the <a href=\"https://wikipedia.org/wiki/Eight-thousander\" title=\"Eight-thousander\">Eight-thousanders</a>.", "links": [{"title": "First ascent", "link": "https://wikipedia.org/wiki/First_ascent"}, {"title": "Shishapangma", "link": "https://wikipedia.org/wiki/Shishapangma"}, {"title": "Mountain", "link": "https://wikipedia.org/wiki/Mountain"}, {"title": "Eight-thousander", "link": "https://wikipedia.org/wiki/Eight-thousander"}]}, {"year": "1969", "text": "The British ocean liner Queen Elizabeth 2 departs on her maiden voyage to New York City.", "html": "1969 - The British ocean liner <i><a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_2\" title=\"Queen <PERSON> 2\">Queen <PERSON> 2</a></i> departs on her maiden voyage to New York City.", "no_year_html": "The British ocean liner <i><a href=\"https://wikipedia.org/wiki/Queen_Elizabeth_2\" title=\"Queen Elizabeth 2\">Queen <PERSON> 2</a></i> departs on her maiden voyage to New York City.", "links": [{"title": "Queen Elizabeth 2", "link": "https://wikipedia.org/wiki/Queen_<PERSON>_2"}]}, {"year": "1970", "text": "ALM Flight 980 ditches in the Caribbean Sea near Saint Croix, killing 23.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/ALM_Flight_980\" title=\"ALM Flight 980\">ALM Flight 980</a> <a href=\"https://wikipedia.org/wiki/Water_landing\" title=\"Water landing\">ditches</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a> near <a href=\"https://wikipedia.org/wiki/Saint_Croix\" title=\"Saint Croix\">Saint Croix</a>, killing 23.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/ALM_Flight_980\" title=\"ALM Flight 980\">ALM Flight 980</a> <a href=\"https://wikipedia.org/wiki/Water_landing\" title=\"Water landing\">ditches</a> in the <a href=\"https://wikipedia.org/wiki/Caribbean_Sea\" title=\"Caribbean Sea\">Caribbean Sea</a> near <a href=\"https://wikipedia.org/wiki/Saint_Croix\" title=\"Saint Croix\">Saint Croix</a>, killing 23.", "links": [{"title": "ALM Flight 980", "link": "https://wikipedia.org/wiki/ALM_Flight_980"}, {"title": "Water landing", "link": "https://wikipedia.org/wiki/Water_landing"}, {"title": "Caribbean Sea", "link": "https://wikipedia.org/wiki/Caribbean_Sea"}, {"title": "Saint Croix", "link": "https://wikipedia.org/wiki/Saint_Croix"}]}, {"year": "1972", "text": "In the early morning hours a fire breaks out at the Sunshine Mine located between Kellogg and Wallace, Idaho, killing 91 workers.", "html": "1972 - In the early morning hours a fire breaks out at the <a href=\"https://wikipedia.org/wiki/Sunshine_Mine_(Idaho)\" class=\"mw-redirect\" title=\"Sunshine Mine (Idaho)\">Sunshine Mine</a> located between Kellogg and Wallace, Idaho, killing 91 workers.", "no_year_html": "In the early morning hours a fire breaks out at the <a href=\"https://wikipedia.org/wiki/Sunshine_Mine_(Idaho)\" class=\"mw-redirect\" title=\"Sunshine Mine (Idaho)\">Sunshine Mine</a> located between Kellogg and Wallace, Idaho, killing 91 workers.", "links": [{"title": "Sunshine Mine (Idaho)", "link": "https://wikipedia.org/wiki/Sunshine_Mine_(Idaho)"}]}, {"year": "1982", "text": "Falklands War: The British nuclear submarine HMS Conqueror sinks the Argentine cruiser ARA General <PERSON>.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: The British <a href=\"https://wikipedia.org/wiki/Nuclear_submarine\" title=\"Nuclear submarine\">nuclear submarine</a> <a href=\"https://wikipedia.org/wiki/HMS_Conqueror_(S48)\" title=\"HMS Conqueror (S48)\">HMS <i>Conqueror</i></a> sinks the <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> cruiser <a href=\"https://wikipedia.org/wiki/ARA_General_<PERSON>\" title=\"ARA General Belgrano\">ARA <i>General <PERSON></i></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Falklands_War\" title=\"Falklands War\">Falklands War</a>: The British <a href=\"https://wikipedia.org/wiki/Nuclear_submarine\" title=\"Nuclear submarine\">nuclear submarine</a> <a href=\"https://wikipedia.org/wiki/HMS_Conqueror_(S48)\" title=\"HMS Conqueror (S48)\">HMS <i>Conqueror</i></a> sinks the <a href=\"https://wikipedia.org/wiki/Argentina\" title=\"Argentina\">Argentine</a> cruiser <a href=\"https://wikipedia.org/wiki/ARA_General_<PERSON>\" title=\"ARA General Belgrano\">ARA <i>General <PERSON></i></a>.", "links": [{"title": "Falklands War", "link": "https://wikipedia.org/wiki/Falklands_War"}, {"title": "Nuclear submarine", "link": "https://wikipedia.org/wiki/Nuclear_submarine"}, {"title": "HMS Conqueror (S48)", "link": "https://wikipedia.org/wiki/HMS_Conqueror_(S48)"}, {"title": "Argentina", "link": "https://wikipedia.org/wiki/Argentina"}, {"title": "ARA General Belgrano", "link": "https://wikipedia.org/wiki/ARA_General_<PERSON>"}]}, {"year": "1986", "text": "Chernobyl disaster: The City of Chernobyl is evacuated six days after the disaster.", "html": "1986 - <a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a>: The City of <a href=\"https://wikipedia.org/wiki/Chernobyl\" title=\"Chernobyl\">Chernobyl</a> is evacuated six days after the disaster.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chernobyl_disaster\" title=\"Chernobyl disaster\">Chernobyl disaster</a>: The City of <a href=\"https://wikipedia.org/wiki/Chernobyl\" title=\"Chernobyl\">Chernobyl</a> is evacuated six days after the disaster.", "links": [{"title": "Chernobyl disaster", "link": "https://wikipedia.org/wiki/Chernobyl_disaster"}, {"title": "Chernobyl", "link": "https://wikipedia.org/wiki/Chernobyl"}]}, {"year": "1989", "text": "Cold War: Hungary begins dismantling its border fence with Austria, which allows a number of East Germans to defect.", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Hungary begins <a href=\"https://wikipedia.org/wiki/Removal_of_Hungary%27s_border_fence_with_Austria\" title=\"Removal of Hungary's border fence with Austria\">dismantling its border fence with Austria</a>, which allows a number of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germans</a> to defect.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cold_War\" title=\"Cold War\">Cold War</a>: Hungary begins <a href=\"https://wikipedia.org/wiki/Removal_of_Hungary%27s_border_fence_with_Austria\" title=\"Removal of Hungary's border fence with Austria\">dismantling its border fence with Austria</a>, which allows a number of <a href=\"https://wikipedia.org/wiki/East_Germany\" title=\"East Germany\">East Germans</a> to defect.", "links": [{"title": "Cold War", "link": "https://wikipedia.org/wiki/Cold_War"}, {"title": "Removal of Hungary's border fence with Austria", "link": "https://wikipedia.org/wiki/Removal_of_Hungary%27s_border_fence_with_Austria"}, {"title": "East Germany", "link": "https://wikipedia.org/wiki/East_Germany"}]}, {"year": "1995", "text": "During the Croatian War of Independence, the Army of the Republic of Serb Krajina fires cluster bombs at Zagreb, killing seven and wounding over 175 civilians.", "html": "1995 - During the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a>, the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Serb_Krajina\" class=\"mw-redirect\" title=\"Army of the Republic of Serb Krajina\">Army of the Republic of Serb Krajina</a> <a href=\"https://wikipedia.org/wiki/Zagreb_rocket_attacks\" title=\"Zagreb rocket attacks\">fires cluster bombs</a> at <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a>, killing seven and wounding over 175 civilians.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Croatian_War_of_Independence\" title=\"Croatian War of Independence\">Croatian War of Independence</a>, the <a href=\"https://wikipedia.org/wiki/Army_of_the_Republic_of_Serb_Krajina\" class=\"mw-redirect\" title=\"Army of the Republic of Serb Krajina\">Army of the Republic of Serb Krajina</a> <a href=\"https://wikipedia.org/wiki/Zagreb_rocket_attacks\" title=\"Zagreb rocket attacks\">fires cluster bombs</a> at <a href=\"https://wikipedia.org/wiki/Zagreb\" title=\"Zagreb\">Zagreb</a>, killing seven and wounding over 175 civilians.", "links": [{"title": "Croatian War of Independence", "link": "https://wikipedia.org/wiki/Croatian_War_of_Independence"}, {"title": "Army of the Republic of Serb Krajina", "link": "https://wikipedia.org/wiki/Army_of_the_Republic_of_Serb_Krajina"}, {"title": "Zagreb rocket attacks", "link": "https://wikipedia.org/wiki/Zagreb_rocket_attacks"}, {"title": "Zagreb", "link": "https://wikipedia.org/wiki/Zagreb"}]}, {"year": "1998", "text": "The European Central Bank is founded in Brussels in order to define and execute the European Union's monetary policy.", "html": "1998 - The <a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a> is founded in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a> in order to define and execute the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>'s monetary policy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/European_Central_Bank\" title=\"European Central Bank\">European Central Bank</a> is founded in <a href=\"https://wikipedia.org/wiki/Brussels\" title=\"Brussels\">Brussels</a> in order to define and execute the <a href=\"https://wikipedia.org/wiki/European_Union\" title=\"European Union\">European Union</a>'s monetary policy.", "links": [{"title": "European Central Bank", "link": "https://wikipedia.org/wiki/European_Central_Bank"}, {"title": "Brussels", "link": "https://wikipedia.org/wiki/Brussels"}, {"title": "European Union", "link": "https://wikipedia.org/wiki/European_Union"}]}, {"year": "1999", "text": "Panamanian general election: <PERSON><PERSON><PERSON> becomes the first woman to be elected President of Panama.", "html": "1999 - <a href=\"https://wikipedia.org/wiki/1999_Panamanian_general_election\" title=\"1999 Panamanian general election\">Panamanian general election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1999_Panamanian_general_election\" title=\"1999 Panamanian general election\">Panamanian general election</a>: <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a> becomes the first woman to be elected <a href=\"https://wikipedia.org/wiki/President_of_Panama\" class=\"mw-redirect\" title=\"President of Panama\">President of Panama</a>.", "links": [{"title": "1999 Panamanian general election", "link": "https://wikipedia.org/wiki/1999_Panamanian_general_election"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "President of Panama", "link": "https://wikipedia.org/wiki/President_of_Panama"}]}, {"year": "2000", "text": "President <PERSON> announces that accurate GPS access would no longer be restricted to the United States military.", "html": "2000 - President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that accurate <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> access would no longer be restricted to the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States military</a>.", "no_year_html": "President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> announces that accurate <a href=\"https://wikipedia.org/wiki/Global_Positioning_System\" title=\"Global Positioning System\">GPS</a> access would no longer be restricted to the <a href=\"https://wikipedia.org/wiki/United_States_Armed_Forces\" title=\"United States Armed Forces\">United States military</a>.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Global Positioning System", "link": "https://wikipedia.org/wiki/Global_Positioning_System"}, {"title": "United States Armed Forces", "link": "https://wikipedia.org/wiki/United_States_Armed_Forces"}]}, {"year": "2004", "text": "The Yelwa massacre concludes. It began on 4 February 2004 when armed Muslims killed 78 Christians at Yelwa, Nigeria. In response, about 630 Muslims were killed by Christians on May 2.", "html": "2004 - The <a href=\"https://wikipedia.org/wiki/Yelwa_massacre\" title=\"Yelwa massacre\">Yelwa massacre</a> concludes. It began on 4 February 2004 when armed Muslims killed 78 Christians at <a href=\"https://wikipedia.org/wiki/Yelwa\" title=\"Yelwa\">Yelwa</a>, Nigeria. In response, about 630 Muslims were killed by Christians on May 2.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Yelwa_massacre\" title=\"Yelwa massacre\">Yelwa massacre</a> concludes. It began on 4 February 2004 when armed Muslims killed 78 Christians at <a href=\"https://wikipedia.org/wiki/Yelwa\" title=\"Yelwa\">Yelwa</a>, Nigeria. In response, about 630 Muslims were killed by Christians on May 2.", "links": [{"title": "Yelwa massacre", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_massacre"}, {"title": "Yel<PERSON>", "link": "https://wikipedia.org/wiki/Ye<PERSON>wa"}]}, {"year": "2008", "text": "Cyclone <PERSON><PERSON><PERSON> makes landfall in Burma killing over 138,000 people and leaving millions of people homeless.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Cyclone_Nargis\" title=\"Cyclone Nargis\">Cyclone <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a> killing over 138,000 people and leaving millions of people homeless.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cyclone_Nargis\" title=\"Cyclone Nargis\">Cyclone <PERSON></a> makes landfall in <a href=\"https://wikipedia.org/wiki/Burma\" class=\"mw-redirect\" title=\"Burma\">Burma</a> killing over 138,000 people and leaving millions of people homeless.", "links": [{"title": "Cyclone <PERSON><PERSON>is", "link": "https://wikipedia.org/wiki/<PERSON>_Nargis"}, {"title": "Burma", "link": "https://wikipedia.org/wiki/Burma"}]}, {"year": "2008", "text": "Chaitén Volcano begins erupting in Chile, forcing the evacuation of more than 4,500 people.", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Chait%C3%A9n_(volcano)\" title=\"Chaitén (volcano)\">Chaitén Volcano</a> begins erupting in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, forcing the evacuation of more than 4,500 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Chait%C3%A9n_(volcano)\" title=\"Chaitén (volcano)\">Chaitén Volcano</a> begins erupting in <a href=\"https://wikipedia.org/wiki/Chile\" title=\"Chile\">Chile</a>, forcing the evacuation of more than 4,500 people.", "links": [{"title": "Chaitén (volcano)", "link": "https://wikipedia.org/wiki/Chait%C3%A9n_(volcano)"}, {"title": "Chile", "link": "https://wikipedia.org/wiki/Chile"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, the suspected mastermind behind the September 11 attacks and the FBI's most wanted man, is killed by the United States Navy SEALs in Abbottabad, Pakistan.", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>den\" title=\"<PERSON>sa<PERSON> bin Laden\"><PERSON><PERSON><PERSON> bin <PERSON></a>, the suspected mastermind behind the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> and the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a>'s most wanted man, is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON>_bin_<PERSON>den\" title=\"Killing of <PERSON><PERSON><PERSON> bin <PERSON>\">killed</a> by the <a href=\"https://wikipedia.org/wiki/United_States_Navy_SEALs\" title=\"United States Navy SEALs\">United States Navy SEALs</a> in <a href=\"https://wikipedia.org/wiki/Abbottabad\" title=\"Abbottabad\">Abbottabad</a>, Pakistan.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON> bin <PERSON></a>, the suspected mastermind behind the <a href=\"https://wikipedia.org/wiki/September_11_attacks\" title=\"September 11 attacks\">September 11 attacks</a> and the <a href=\"https://wikipedia.org/wiki/Federal_Bureau_of_Investigation\" title=\"Federal Bureau of Investigation\">FBI</a>'s most wanted man, is <a href=\"https://wikipedia.org/wiki/Killing_of_<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"Killing of <PERSON><PERSON><PERSON> bin <PERSON>\">killed</a> by the <a href=\"https://wikipedia.org/wiki/United_States_Navy_SEALs\" title=\"United States Navy SEALs\">United States Navy SEALs</a> in <a href=\"https://wikipedia.org/wiki/Abbottabad\" title=\"Abbottabad\">Abbottabad</a>, Pakistan.", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>den"}, {"title": "September 11 attacks", "link": "https://wikipedia.org/wiki/September_11_attacks"}, {"title": "Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Federal_Bureau_of_Investigation"}, {"title": "Killing of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Killing_<PERSON>_<PERSON><PERSON><PERSON>_bin_<PERSON>"}, {"title": "United States Navy SEALs", "link": "https://wikipedia.org/wiki/United_States_Navy_SEALs"}, {"title": "Abbottabad", "link": "https://wikipedia.org/wiki/Abbottabad"}]}, {"year": "2011", "text": "An E. coli outbreak strikes Europe, mostly in Germany, leaving more than 30 people dead and many others are taken ill.", "html": "2011 - An <a href=\"https://wikipedia.org/wiki/2011_E._coli_O104:H4_outbreak\" class=\"mw-redirect\" title=\"2011 E. coli O104:H4 outbreak\">E. coli outbreak</a> strikes Europe, mostly in Germany, leaving more than 30 people dead and many others are taken ill.", "no_year_html": "An <a href=\"https://wikipedia.org/wiki/2011_E._coli_O104:H4_outbreak\" class=\"mw-redirect\" title=\"2011 E. coli O104:H4 outbreak\">E. coli outbreak</a> strikes Europe, mostly in Germany, leaving more than 30 people dead and many others are taken ill.", "links": [{"title": "2011 E. coli O104:H4 outbreak", "link": "https://wikipedia.org/wiki/2011_E._coli_O104:H4_outbreak"}]}, {"year": "2012", "text": "A pastel version of The Scream, by Norwegian painter <PERSON><PERSON>, sells for $120 million in a New York City auction, setting a new world record for a work of art at auction.", "html": "2012 - A <a href=\"https://wikipedia.org/wiki/Pastel\" title=\"Pastel\">pastel</a> version of <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i>, by Norwegian painter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nch\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, sells for $120 million in a New York City auction, setting a new world record for a work of art at auction.", "no_year_html": "A <a href=\"https://wikipedia.org/wiki/Pastel\" title=\"Pastel\">pastel</a> version of <i><a href=\"https://wikipedia.org/wiki/The_Scream\" title=\"The Scream\">The Scream</a></i>, by Norwegian painter <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>nch\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, sells for $120 million in a New York City auction, setting a new world record for a work of art at auction.", "links": [{"title": "Pastel", "link": "https://wikipedia.org/wiki/Pastel"}, {"title": "The Scream", "link": "https://wikipedia.org/wiki/The_Scream"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2014", "text": "Two mudslides in Badakhshan, Afghanistan, leave up to 2,500 people missing.", "html": "2014 - <a href=\"https://wikipedia.org/wiki/2014_Badakhshan_mudslides\" title=\"2014 Badakhshan mudslides\">Two mudslides</a> in <a href=\"https://wikipedia.org/wiki/Badakhshan\" title=\"Badakhshan\">Badakhshan</a>, Afghanistan, leave up to 2,500 people missing.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/2014_Badakhshan_mudslides\" title=\"2014 Badakhshan mudslides\">Two mudslides</a> in <a href=\"https://wikipedia.org/wiki/Badakhshan\" title=\"Badakhshan\">Badakhshan</a>, Afghanistan, leave up to 2,500 people missing.", "links": [{"title": "2014 Badakhshan mudslides", "link": "https://wikipedia.org/wiki/2014_Badakhshan_mudslides"}, {"title": "Badakhshan", "link": "https://wikipedia.org/wiki/Badakhshan"}]}], "Births": [{"year": "1360", "text": "<PERSON>le Emperor of China (d. 1424)", "html": "1360 - <a href=\"https://wikipedia.org/wiki/<PERSON>le_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a> of China (d. 1424)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>le_Emperor\" title=\"Yongle Emperor\">Yongle Emperor</a> of China (d. 1424)", "links": [{"title": "Yongle Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Emperor"}]}, {"year": "1402", "text": "<PERSON> Aragon, Queen of Portugal (d. 1445)", "html": "1402 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON>, Queen of Portugal\"><PERSON>, Queen of Portugal</a> (d. 1445)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal\" title=\"<PERSON>, Queen of Portugal\"><PERSON>, Queen of Portugal</a> (d. 1445)", "links": [{"title": "<PERSON> Aragon, Queen of Portugal", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_Queen_of_Portugal"}]}, {"year": "1451", "text": "<PERSON>, Duke of Lorraine (d. 1508)", "html": "1451 - <a href=\"https://wikipedia.org/wiki/Ren%C3%A9_II,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1508)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ren%C3%A9_II,_Duke_of_Lorraine\" title=\"<PERSON>, Duke of Lorraine\"><PERSON>, Duke of Lorraine</a> (d. 1508)", "links": [{"title": "<PERSON>, Duke of Lorraine", "link": "https://wikipedia.org/wiki/Ren%C3%A9_II,_<PERSON>_of_Lorraine"}]}, {"year": "1458", "text": "<PERSON> Viseu (d. 1525)", "html": "1458 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Viseu\" title=\"<PERSON> of Viseu\"><PERSON> of Viseu</a> (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> of Viseu\"><PERSON> of Viseu</a> (d. 1525)", "links": [{"title": "<PERSON> of Viseu", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1476", "text": "<PERSON>, Duke of Münsterberg-Oels, Count of Kladsko, Governor of Bohemia and Silesia (d. 1536)", "html": "1476 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_M%C3%BCnsterberg-Oels\" title=\"<PERSON>, Duke of Münsterberg-Oels\"><PERSON>, Duke of Münsterberg-Oels</a>, Count of Kladsko, Governor of Bohemia and Silesia (d. 1536)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_M%C3%BCnsterberg-Oels\" title=\"<PERSON>, Duke of Münsterberg-Oels\"><PERSON>, Duke of Münsterberg-Oels</a>, Count of Kladsko, Governor of Bohemia and Silesia (d. 1536)", "links": [{"title": "<PERSON>, Duke of Münsterberg-Oels", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_<PERSON>_M%C3%BCnsterberg-Oels"}]}, {"year": "1533", "text": "<PERSON>, Duke of Brunswick-Gruben<PERSON>n (d. 1596)", "html": "1533 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (d. 1596)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-Grubenhagen\" title=\"<PERSON>, Duke of Brunswick-Grubenhagen\"><PERSON>, Duke of Brunswick-Grubenhagen</a> (d. 1596)", "links": [{"title": "<PERSON>, Duke of Brunswick-Grubenhagen", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brunswick-<PERSON>"}]}, {"year": "1551", "text": "<PERSON>, English historian and topographer (d. 1623)", "html": "1551 - <a href=\"https://wikipedia.org/wiki/William_<PERSON>\" title=\"William Camden\"><PERSON></a>, English historian and topographer (d. 1623)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/William_Camden\" title=\"William Camden\"><PERSON></a>, English historian and topographer (d. 1623)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/William_Camden"}]}, {"year": "1567", "text": "<PERSON><PERSON><PERSON>, Dutch captain, vice-admiral of the Dutch East India Company (d. 1603)", "html": "1567 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch captain, vice-admiral of the Dutch East India Company (d. 1603)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Dutch captain, vice-admiral of the Dutch East India Company (d. 1603)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1579", "text": "<PERSON>, Japanese shōgun (d. 1632)", "html": "1579 - <a href=\"https://wikipedia.org/wiki/Tokugawa_Hidetada\" title=\"Tokugawa Hidetada\">Tokugawa <PERSON></a>, Japanese shōgun (d. 1632)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tokugawa_<PERSON>ada\" title=\"Tokugawa Hidetada\"><PERSON></a>, Japanese shōgun (d. 1632)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Hidetada"}]}, {"year": "1601", "text": "<PERSON><PERSON><PERSON>, German priest and scholar (d. 1680)", "html": "1601 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German priest and scholar (d. 1680)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German priest and scholar (d. 1680)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1660", "text": "<PERSON>, Italian composer (d. 1725)", "html": "1660 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1725)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian composer (d. 1725)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1695", "text": "<PERSON>, Italian-French painter and architect (d. 1766)", "html": "1695 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B2_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-French painter and architect (d. 1766)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B2_<PERSON>\" title=\"<PERSON> Ni<PERSON>\"><PERSON></a>, Italian-French painter and architect (d. 1766)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Giovanni_Niccol%C3%B2_<PERSON><PERSON>i"}]}, {"year": "1702", "text": "<PERSON>, German theologian and theosopher (d. 1782)", "html": "1702 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and theosopher (d. 1782)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German theologian and theosopher (d. 1782)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1707", "text": "<PERSON><PERSON><PERSON>, French cellist and composer (d. 1747)", "html": "1707 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist and composer (d. 1747)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A8re\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French cellist and composer (d. 1747)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%A8re"}]}, {"year": "1729", "text": "<PERSON> of Russia (d. 1796)", "html": "1729 - <a href=\"https://wikipedia.org/wiki/<PERSON>_the_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a> of Russia (d. 1796)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_Great\" title=\"<PERSON> the Great\"><PERSON> the <PERSON></a> of Russia (d. 1796)", "links": [{"title": "<PERSON> the <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1737", "text": "<PERSON>, 2nd Earl of Shelburne, Irish-English politician, Prime Minister of Great Britain (d. 1805)", "html": "1737 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne\" title=\"<PERSON>, 2nd Earl of Shelburne\"><PERSON>, 2nd Earl of Shelburne</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne\" title=\"<PERSON>, 2nd Earl of Shelburne\"><PERSON>, 2nd Earl of Shelburne</a>, Irish-English politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain\" class=\"mw-redirect\" title=\"Prime Minister of Great Britain\">Prime Minister of Great Britain</a> (d. 1805)", "links": [{"title": "<PERSON>, 2nd Earl of Shelburne", "link": "https://wikipedia.org/wiki/<PERSON>,_2nd_Earl_of_Shelburne"}, {"title": "Prime Minister of Great Britain", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Great_Britain"}]}, {"year": "1740", "text": "<PERSON>, American lawyer and politician, 10th President of the Continental Congress (d. 1821)", "html": "1740 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1821)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_the_Continental_Congress\" title=\"President of the Continental Congress\">President of the Continental Congress</a> (d. 1821)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "President of the Continental Congress", "link": "https://wikipedia.org/wiki/President_of_the_Continental_Congress"}]}, {"year": "1750", "text": "<PERSON>, English soldier and spy (d. 1780)", "html": "1750 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>r%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English soldier and spy (d. 1780)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, English soldier and spy (d. 1780)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Andr%C3%A9"}]}, {"year": "1752", "text": "<PERSON>, German oboe player and composer (d. 1790)", "html": "1752 - <a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON> August <PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer (d. 1790)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_August_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Oboe\" title=\"Oboe\">oboe</a> player and composer (d. 1790)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_August_<PERSON>"}, {"title": "Oboe", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1754", "text": "<PERSON>, Spanish composer (d. 1806)", "html": "1754 - <a href=\"https://wikipedia.org/wiki/Vicente_<PERSON>%C3%ADn_y_Soler\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1806)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vicente_Mart%C3%ADn_y_Soler\" title=\"<PERSON>\"><PERSON></a>, Spanish composer (d. 1806)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Vicente_Mart%C3%ADn_y_Soler"}]}, {"year": "1772", "text": "<PERSON><PERSON>, German author and poet (d. 1801)", "html": "1772 - <a href=\"https://wikipedia.org/wiki/Novalis\" title=\"Novalis\"><PERSON><PERSON></a>, German author and poet (d. 1801)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Novalis\" title=\"Novalis\"><PERSON><PERSON></a>, German author and poet (d. 1801)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1773", "text": "<PERSON>, Norwegian philosopher and poet (d. 1845)", "html": "1773 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and poet (d. 1845)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Norwegian philosopher and poet (d. 1845)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1797", "text": "<PERSON>, Canadian physician and geologist (d. 1864)", "html": "1797 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and geologist (d. 1864)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian physician and geologist (d. 1864)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1802", "text": "<PERSON>, German chemist and physicist (d. 1870)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (d. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist and physicist (d. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1806", "text": "<PERSON>, French nun and saint (d. 1876)", "html": "1806 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French nun and saint (d. 1876)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A9\" title=\"<PERSON>\"><PERSON></a>, French nun and saint (d. 1876)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Catherine_Labour%C3%A9"}]}, {"year": "1810", "text": "<PERSON>, Danish composer and conductor (d. 1874)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish composer and conductor (d. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish composer and conductor (d. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1813", "text": "<PERSON>, English novelist and poet (d. 1883)", "html": "1813 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1883)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English novelist and poet (d. 1883)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1815", "text": "<PERSON>, Canadian lawyer and judge, 1st Chief Justice of Canada (d. 1889)", "html": "1815 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge, 1st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian lawyer and judge, 1st <a href=\"https://wikipedia.org/wiki/Chief_Justice_of_Canada\" title=\"Chief Justice of Canada\">Chief Justice of Canada</a> (d. 1889)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chief Justice of Canada", "link": "https://wikipedia.org/wiki/Chief_Justice_of_Canada"}]}, {"year": "1822", "text": "<PERSON>, Scottish-Swedish governess and educator (d. 1902)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Swedish governess and educator (d. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish-Swedish governess and educator (d. 1902)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON><PERSON><PERSON><PERSON>, French archaeologist and photographer (d. 1915)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French archaeologist and photographer (d. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, French archaeologist and photographer (d. 1915)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/D%C3%A9sir%C3%A9_Charnay"}]}, {"year": "1830", "text": "<PERSON>, German entomologist and author (d. 1900)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and author (d. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German entomologist and author (d. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, Canadian-American engineer (d. 1929)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer (d. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American engineer (d. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1859", "text": "<PERSON>, English author and playwright (d. 1927)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Scottish physiologist, physician, and academic (d. 1936)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physiologist, physician, and academic (d. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish physiologist, physician, and academic (d. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1860", "text": "<PERSON>, Austro-Hungarian Zionist philosopher, journalist and author (d. 1904)", "html": "1860 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-Hungarian Zionist philosopher, journalist and author (d. 1904)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austro-Hungarian Zionist philosopher, journalist and author (d. 1904)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1865", "text": "<PERSON>, American playwright (d. 1909)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American playwright (d. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>tch\"><PERSON></a>, American playwright (d. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>tch"}]}, {"year": "1867", "text": "<PERSON>, Italian-American mobster (d. 1930)", "html": "1867 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mobster (d. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian-American mobster (d. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1872", "text": " <PERSON><PERSON><PERSON>, Japanese writer (d. 1896)", "html": "1872 - <a href=\"https://wikipedia.org/wiki/Ichiy%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese writer (d. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ichiy%C5%8D_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese writer (d. 1896)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ichiy%C5%8D_<PERSON><PERSON>"}]}, {"year": "1873", "text": "<PERSON><PERSON><PERSON>, Lithuanian poet, critic, and translator (d. 1944)", "html": "1873 - <a href=\"https://wikipedia.org/wiki/Jurg<PERSON>_<PERSON>ltru%C5%A1aitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian poet, critic, and translator (d. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>ru%C5%A1aitis\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian poet, critic, and translator (d. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jurgis_Baltru%C5%A1aitis"}]}, {"year": "1879", "text": "<PERSON>, American stenographer and politician, 49th United States Secretary of State (d. 1972)", "html": "1879 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stenographer and politician, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American stenographer and politician, 49th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_State\" title=\"United States Secretary of State\">United States Secretary of State</a> (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "United States Secretary of State", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_State"}]}, {"year": "1880", "text": "<PERSON>, American football player, discus thrower, and coach (d. 1955)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, discus thrower, and coach (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player, discus thrower, and coach (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, American lawyer, politician, and businessperson (d. 1955)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessperson (d. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and businessperson (d. 1955)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1882", "text": "<PERSON>, Puerto Rican activist who helped pave the way for Puerto Ricans' American citizenship (d. 1971)", "html": "1882 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican activist who helped pave the way for Puerto Ricans' American citizenship (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A1lez\" title=\"<PERSON>\"><PERSON></a>, Puerto Rican activist who helped pave the way for Puerto Ricans' American citizenship (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>nz%C3%A1lez"}]}, {"year": "1884", "text": "<PERSON>, American politician (d. 1958)", "html": "1884 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)\" title=\"<PERSON> (South Dakota politician)\"><PERSON></a>, American politician (d. 1958)", "links": [{"title": "<PERSON> (South Dakota politician)", "link": "https://wikipedia.org/wiki/<PERSON>_(South_Dakota_politician)"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, American actress and gossip columnist (d. 1966)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Hedd<PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and gossip columnist (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>dd<PERSON>_<PERSON>\" title=\"<PERSON>dd<PERSON> <PERSON>\"><PERSON><PERSON><PERSON></a>, American actress and gossip columnist (d. 1966)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Hedda_<PERSON>"}]}, {"year": "1886", "text": "<PERSON><PERSON><PERSON>, German author and poet (d. 1956)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German author and poet (d. 1956)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, English-American dancer (d. 1918)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"Vernon and Irene Castle\"><PERSON></a>, English-American dancer (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>\" title=\"Vernon and Irene Castle\"><PERSON></a>, English-American dancer (d. 1918)", "links": [{"title": "Vernon and Irene Castle", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, American baseball player and manager (d. 1951)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (d. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1889", "text": "<PERSON>, Indonesian philosopher, academic, and politician (d. 1959)", "html": "1889 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian philosopher, academic, and politician (d. 1959)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indonesian philosopher, academic, and politician (d. 1959)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1890", "text": "<PERSON><PERSON> <PERSON><PERSON>, American engineer and author (d. 1965)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American engineer and author (d. 1965)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American engineer and author (d. 1965)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1892", "text": "<PERSON>, German captain and pilot (d. 1918)", "html": "1892 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German captain and pilot (d. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, American actress of the silent era (d. 1957)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress of the silent era (d. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress of the silent era (d. 1957)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1894", "text": "<PERSON>, English biologist, philosopher, and academic (d. 1981)", "html": "1894 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, philosopher, and academic (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English biologist, philosopher, and academic (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, American playwright and lyricist (d. 1943)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American playwright and lyricist (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American playwright and lyricist (d. 1943)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1897", "text": "<PERSON>, American songwriter (d. 1985)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American songwriter (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON>, English bandleader, composer, and actor (d. 1989)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English bandleader, composer, and actor (d. 1989)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(bandleader)\" title=\"<PERSON> (bandleader)\"><PERSON></a>, English bandleader, composer, and actor (d. 1989)", "links": [{"title": "<PERSON> (bandleader)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(bandleader)"}]}, {"year": "1902", "text": "<PERSON>, English actor (d. 1986)", "html": "1902 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (d. 1986)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1903", "text": "<PERSON>, American pediatrician, activist, and author (d. 1998)", "html": "1903 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician, activist, and author (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pediatrician, activist, and author (d. 1998)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1905", "text": "<PERSON>, American author (d. 1969)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1969)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author (d. 1969)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1907", "text": "<PERSON><PERSON>, American comedian and television host (d. 1993)", "html": "1907 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and television host (d. 1993)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American comedian and television host (d. 1993)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, American lieutenant, Medal of Honor recipient (d. 1943)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American lieutenant, <a href=\"https://wikipedia.org/wiki/Medal_of_Honor\" title=\"Medal of Honor\">Medal of Honor</a> recipient (d. 1943)", "links": [{"title": "<PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}, {"title": "Medal of Honor", "link": "https://wikipedia.org/wiki/Medal_of_Honor"}]}, {"year": "1910", "text": "<PERSON>, American urban planner, architect, educator, and author (d. 2005)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American urban planner, architect, educator, and author (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(architect)\" title=\"<PERSON> (architect)\"><PERSON></a>, American urban planner, architect, educator, and author (d. 2005)", "links": [{"title": "<PERSON> (architect)", "link": "https://wikipedia.org/wiki/<PERSON>_(architect)"}]}, {"year": "1912", "text": "<PERSON>, German journalist and publisher, founded Axel Springer AG (d. 1985)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Axel Springer\">Axel Springer</a>, German journalist and publisher, founded <a href=\"https://wikipedia.org/wiki/Axel_Springer_AG\" class=\"mw-redirect\" title=\"Axel Springer AG\">Axel Springer AG</a> (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Springer\" title=\"Axel Springer\">Axel Springer</a>, German journalist and publisher, founded <a href=\"https://wikipedia.org/wiki/Axel_Springer_AG\" class=\"mw-redirect\" title=\"Axel Springer AG\">Axel Springer AG</a> (d. 1985)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Axel Springer AG", "link": "https://wikipedia.org/wiki/Axel_Springer_AG"}]}, {"year": "1912", "text": "<PERSON><PERSON>, Dutch comic strip creator (d. 2005)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch comic strip creator (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch comic strip creator (d. 2005)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1912", "text": "<PERSON>, English actor and director (d. 1981)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 1981)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor and director (d. 1981)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American singer-songwriter (d. 2003)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(songwriter)\" title=\"<PERSON> (songwriter)\"><PERSON></a>, American singer-songwriter (d. 2003)", "links": [{"title": "<PERSON> (songwriter)", "link": "https://wikipedia.org/wiki/<PERSON>_(songwriter)"}]}, {"year": "1915", "text": "<PERSON>, English actress (d. 2001)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peggy <PERSON>\"><PERSON></a>, English actress (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Peggy <PERSON>\"><PERSON></a>, English actress (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1917", "text": "<PERSON><PERSON><PERSON>, Vietnamese general and politician, 6th Minister of Defence for Vietnam (d. 2002)", "html": "1917 - <a href=\"https://wikipedia.org/wiki/V%C4%83n_Ti%E1%BA%BFn_D%C5%A9ng\" title=\"Văn Tiến D<PERSON>ng\"><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)\" title=\"Minister of Defence (Vietnam)\">Minister of Defence for Vietnam</a> (d. 2002)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C4%83n_Ti%E1%BA%BFn_D%C5%A9ng\" title=\"Văn Tiến D<PERSON>ng\"><PERSON><PERSON><PERSON></a>, Vietnamese general and politician, 6th <a href=\"https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)\" title=\"Minister of Defence (Vietnam)\">Minister of Defence for Vietnam</a> (d. 2002)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C4%83n_Ti%E1%BA%BFn_D%C5%A9ng"}, {"title": "Minister of Defence (Vietnam)", "link": "https://wikipedia.org/wiki/Minister_of_Defence_(Vietnam)"}]}, {"year": "1921", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian archaeologist (d. 2022)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>(archaeologist)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> (archaeologist)\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian archaeologist (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_(archaeologist)\" class=\"mw-redirect\" title=\"<PERSON><PERSON> <PERSON><PERSON> (archaeologist)\"><PERSON><PERSON> <PERSON><PERSON></a>, Indian archaeologist (d. 2022)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON> (archaeologist)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>(archaeologist)"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian director, producer, and screenwriter (d. 1992)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON> <PERSON></a>, Indian director, producer, and screenwriter (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON> <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Indian director, producer, and screenwriter (d. 1992)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Satyajit_Ray"}]}, {"year": "1922", "text": "<PERSON><PERSON><PERSON>, American actor and director (d. 2007)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director (d. 2007)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actor and director (d. 2007)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON> <PERSON><PERSON>, Canadian-born American journalist and author (d. 2006)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-born American journalist and author (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Canadian-born American journalist and author (d. 2006)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Irish physician and politician, 6th President of Ireland (d. 2008)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish physician and politician, 6th <a href=\"https://wikipedia.org/wiki/President_of_Ireland\" title=\"President of Ireland\">President of Ireland</a> (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Patrick_<PERSON>"}, {"title": "President of Ireland", "link": "https://wikipedia.org/wiki/President_of_Ireland"}]}, {"year": "1924", "text": "<PERSON>, Austrian-American singer-songwriter, guitarist, and actor (d. 2015)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer-songwriter, guitarist, and actor (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-American singer-songwriter, guitarist, and actor (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1924", "text": "<PERSON>, English soldier, historian, and diplomat, British Ambassador to Japan (d. 2018)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, historian, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Japan\">British Ambassador to Japan</a> (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier, historian, and diplomat, <a href=\"https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan\" class=\"mw-redirect\" title=\"List of Ambassadors of the United Kingdom to Japan\">British Ambassador to Japan</a> (d. 2018)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Ambassadors of the United Kingdom to Japan", "link": "https://wikipedia.org/wiki/List_of_Ambassadors_of_the_United_Kingdom_to_Japan"}]}, {"year": "1925", "text": "<PERSON>, English-Canadian actor (d. 2011)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Canadian actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, English-Canadian actor (d. 2011)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1927", "text": "<PERSON>, Australian actor and singer (d. 2009)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer (d. 2009)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actor and singer (d. 2009)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, British wine critic and writer (d. 2020)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British wine critic and writer (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, British wine critic and writer (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, Turkish-French economist and politician, 162nd Prime Minister of France", "html": "1929 - <a href=\"https://wikipedia.org/wiki/%C3%89douard_Balladur\" title=\"É<PERSON>uard Balladur\"><PERSON><PERSON><PERSON></a>, Turkish-French economist and politician, 162nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C3%89douard_Balladur\" title=\"É<PERSON>uard Balladur\"><PERSON><PERSON><PERSON></a>, Turkish-French economist and politician, 162nd <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_France\" title=\"Prime Minister of France\">Prime Minister of France</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C3%89do<PERSON>_<PERSON>"}, {"title": "Prime Minister of France", "link": "https://wikipedia.org/wiki/Prime_Minister_of_France"}]}, {"year": "1929", "text": "<PERSON>, American singer-songwriter and guitarist (d. 2005)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Wray\" title=\"<PERSON> Wray\"><PERSON></a>, American singer-songwriter and guitarist (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Wray\" title=\"<PERSON> Wray\"><PERSON></a>, American singer-songwriter and guitarist (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Link_Wray"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> of Bhutan (d. 1972)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Jigme_Do<PERSON>_<PERSON>\" title=\"Jigme Do<PERSON>\">Jigme <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Druk_Gyalpo\" class=\"mw-redirect\" title=\"Druk Gyalpo\">Druk Gyalpo</a> of Bhutan (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jigme_Do<PERSON>_<PERSON>\" title=\"Jigme Do<PERSON>\">Jigme <PERSON></a>, <a href=\"https://wikipedia.org/wiki/Druk_Gyalpo\" class=\"mw-redirect\" title=\"Druk Gyalpo\"><PERSON><PERSON> Gyalpo</a> of Bhutan (d. 1972)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Jig<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1930", "text": "<PERSON><PERSON>, Israeli painter and critic (d. 2013)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli painter and critic (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Israeli painter and critic (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1930", "text": "<PERSON>, Italian journalist and politician (d. 2016)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian journalist and politician (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, American actor and stuntman (d. 2012)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Brun<PERSON>\" title=\"<PERSON> Brun<PERSON>\"><PERSON></a>, American actor and stuntman (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>run<PERSON>\" title=\"<PERSON> Bruns\"><PERSON></a>, American actor and stuntman (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>s"}]}, {"year": "1933", "text": "<PERSON>, Baron <PERSON>, English lawyer and judge, Lord Chief Justice of England and Wales", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English lawyer and judge, <a href=\"https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales\" title=\"Lord Chief Justice of England and Wales\">Lord Chief Justice of England and Wales</a>", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>oolf"}, {"title": "Lord Chief Justice of England and Wales", "link": "https://wikipedia.org/wiki/Lord_Chief_Justice_of_England_and_Wales"}]}, {"year": "1935", "text": "<PERSON>, Spanish footballer and manager (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Luis_Su%C3%A1rez_Miramontes\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Luis_Su%C3%A1rez_Miramontes\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish footballer and manager (d. 2023)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Luis_Su%C3%A1rez_Miramontes"}]}, {"year": "1935", "text": "<PERSON><PERSON><PERSON> <PERSON> of Iraq, the last King of Iraq (d. 1958)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/Faisal_II_of_Iraq\" class=\"mw-redirect\" title=\"Faisal II of Iraq\">Fais<PERSON> II of Iraq</a>, the last King of Iraq (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Faisal_II_of_Iraq\" class=\"mw-redirect\" title=\"Faisal II of Iraq\">Faisal II of Iraq</a>, the last King of Iraq (d. 1958)", "links": [{"title": "Faisal II of Iraq", "link": "https://wikipedia.org/wiki/Faisal_II_of_Iraq"}]}, {"year": "1936", "text": "<PERSON>, Argentinian actress, director, and screenwriter", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress, director, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian actress, director, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, English singer and pianist", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, English singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)\" title=\"<PERSON><PERSON><PERSON> (singer)\"><PERSON><PERSON><PERSON></a>, English singer and pianist", "links": [{"title": "<PERSON><PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_(singer)"}]}, {"year": "1937", "text": "<PERSON>, American actor, producer, and screenwriter (d. 2001)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lorenzo Music\"><PERSON></a>, American actor, producer, and screenwriter (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lorenzo Music\"><PERSON></a>, American actor, producer, and screenwriter (d. 2001)", "links": [{"title": "Lorenzo Music", "link": "https://wikipedia.org/wiki/Lorenzo_Music"}]}, {"year": "1938", "text": "<PERSON><PERSON><PERSON><PERSON> of Lesotho (d. 1996)", "html": "1938 - <a href=\"https://wikipedia.org/wiki/Moshoeshoe_II_of_Lesotho\" title=\"Moshoeshoe II of Lesotho\">Moshoeshoe II of Lesotho</a> (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Moshoeshoe_II_of_Lesotho\" title=\"Moshoeshoe II of Lesotho\">Moshoeshoe II of Lesotho</a> (d. 1996)", "links": [{"title": "Moshoeshoe II of Lesotho", "link": "https://wikipedia.org/wiki/Moshoeshoe_II_of_Lesotho"}]}, {"year": "1941", "text": "<PERSON>, American baseball player", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, Belgian businessman (d. 2021)", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian businessman (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Bosnian actor and film director (d. 2020)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian actor and film director (d. 2020)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C4%87\" title=\"<PERSON>\"><PERSON></a>, Bosnian actor and film director (d. 2020)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mustafa_Nadarevi%C4%87"}]}, {"year": "1944", "text": "<PERSON>, English chemist, historian, and curator", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist, historian, and curator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English chemist, historian, and curator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, English singer-songwriter (d. 1998)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/Judge_Dread\" title=\"Judge Dread\">Judge <PERSON><PERSON></a>, English singer-songwriter (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Judge_Dread\" title=\"Judge Dread\">Judge <PERSON>ead</a>, English singer-songwriter (d. 1998)", "links": [{"title": "Judge <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Judge_Dread"}]}, {"year": "1945", "text": "<PERSON>, Nicaraguan-American model, actress, and activist", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan-American model, actress, and activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Nicaraguan-American model, actress, and activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American singer-songwriter (d. 2015)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, English actor", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English businessman, founded the Dyson Company", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>yson_(company)\" title=\"Dyson (company)\">Dyson Company</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businessman, founded the <a href=\"https://wikipedia.org/wiki/<PERSON>ys<PERSON>_(company)\" title=\"<PERSON>yson (company)\">Dyson Company</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Dyson (company)", "link": "https://wikipedia.org/wiki/Dyson_(company)"}]}, {"year": "1948", "text": "<PERSON>, American singer-songwriter, guitarist, and actor", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, guitarist, and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, English gardener and author", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English gardener and author", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON><PERSON><PERSON>, German celebrity chef, author and businessman", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Celebrity_chef\" title=\"Celebrity chef\">celebrity chef</a>, author and businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/Celebrity_chef\" title=\"Celebrity chef\">celebrity chef</a>, author and businessman", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Celebrity chef", "link": "https://wikipedia.org/wiki/Celebrity_chef"}]}, {"year": "1950", "text": "<PERSON>, Australian rugby league player and coach (d. 2022)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, Australian businessman and politician", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian businessman and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, English singer and bass player (d. 1979)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and bass player (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American actress and singer", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON><PERSON>, Russian conductor and director", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gergiev\" title=\"<PERSON><PERSON> Gergiev\"><PERSON><PERSON></a>, Russian conductor and director", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Gergiev\" title=\"<PERSON><PERSON> Gergiev\"><PERSON><PERSON></a>, Russian conductor and director", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_G<PERSON>giev"}]}, {"year": "1953", "text": "<PERSON><PERSON>, American basketball player", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American basketball player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American composer and conductor", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American composer and conductor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English politician", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Dawn_Primarolo"}]}, {"year": "1955", "text": "<PERSON>, Scottish footballer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON><PERSON><PERSON>, Italian fashion designer", "html": "1955 - <a href=\"https://wikipedia.org/wiki/Donate<PERSON>_Versace\" title=\"Donatella Versace\"><PERSON><PERSON><PERSON></a>, Italian fashion designer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Versace\" title=\"Donatella Versace\"><PERSON><PERSON><PERSON></a>, Italian fashion designer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Donatella_Versace"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON>, Canadian businessman and politician, 41st Mayor of Quebec City", "html": "1956 - <a href=\"https://wikipedia.org/wiki/R%C3%A9gis_Labeaume\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City\" title=\"List of mayors of Quebec City\">Mayor of Quebec City</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/R%C3%A9gis_Labeaume\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Canadian businessman and politician, 41st <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City\" title=\"List of mayors of Quebec City\">Mayor of Quebec City</a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/R%C3%A9gis_Labeaume"}, {"title": "List of mayors of Quebec City", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Quebec_City"}]}, {"year": "1958", "text": "<PERSON><PERSON><PERSON>, Japanese songwriter and producer", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese songwriter and producer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, English-Irish footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary\" title=\"<PERSON>\"><PERSON></a>, English-Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%27Leary"}]}, {"year": "1959", "text": "<PERSON>, English singer-songwriter and guitarist", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, English director and producer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, Australian rugby league player and coach", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English director and producer", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English director and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1962", "text": "<PERSON>, English snooker player", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English snooker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, Canadian businesswoman, philanthropist, and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman, philanthropist, and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian businesswoman, philanthropist, and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON><PERSON>, American journalist and author", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American journalist and author", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1967", "text": "<PERSON>, English footballer (d. 2001)", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American footballer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON><PERSON>, Malaysian singer-songwriter and actress", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Malaysian singer-songwriter and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON>, Trinidadian cricketer", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Trinidadian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON><PERSON>, Samoan-American sumo wrestler, the 67th Yo<PERSON><PERSON>na", "html": "1971 - <a href=\"https://wikipedia.org/wiki/Musashimaru_K%C5%8Dy%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan-American sumo wrestler, the 67th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Musashimaru_K%C5%8Dy%C5%8D\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Samoan-American sumo wrestler, the 67th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Musashimaru_K%C5%8Dy%C5%8D"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1972", "text": "<PERSON><PERSON>, American actor and wrestler", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actor and wrestler", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, German director and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/F<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German director and screenwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English footballer, coach, and model", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and model", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer, coach, and model", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, English comedian, actor and writer", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English comedian, actor and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American singer-songwriter", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Pakistani-American actor, stand-up comedian, and screenwriter", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-American actor, stand-up comedian, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani-American actor, stand-up comedian, and screenwriter", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, German footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American actress, comedian and writer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and writer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and writer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON><PERSON>, English footballer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>at_Knight\" title=\"Zat Knight\"><PERSON><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>at_Knight\" title=\"Zat Knight\"><PERSON><PERSON></a>, English footballer", "links": [{"title": "Zat Knight", "link": "https://wikipedia.org/wiki/Zat_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, American basketball player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Canadian ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, American actor", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, English footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>, Portuguese footballer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tia<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Portuguese footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tia<PERSON>_Mendes"}]}, {"year": "1982", "text": "<PERSON>, South African cricketer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(cricketer)\" title=\"<PERSON> (cricketer)\"><PERSON></a>, South African cricketer", "links": [{"title": "<PERSON> (cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(cricketer)"}]}, {"year": "1983", "text": "<PERSON>, Italian footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Honduran footballer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Honduran footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"May<PERSON>\"><PERSON><PERSON></a>, Honduran footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Slovenian skier", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian skier", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Slovenian skier", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Spanish race car driver", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Spanish race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON>, Lithuanian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%ABnas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C5%ABnas\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Lithuanian footballer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Saulius_Mikoli%C5%ABnas"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Swiss basketball player", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Thabo_Sefolosha\" title=\"Thabo Sefolosha\"><PERSON><PERSON><PERSON></a>, Swiss basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Thabo_Sefolosha\" title=\"Thabo Sefolosha\"><PERSON><PERSON><PERSON></a>, Swiss basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Thab<PERSON>_Se<PERSON>losha"}]}, {"year": "1985", "text": "<PERSON>, English singer-songwriter and actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American race car driver", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American tennis player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ashley_<PERSON>d"}]}, {"year": "1985", "text": "<PERSON>, American figure skater", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American figure skater", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, Pakistani cricketer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani cricketer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Finnish singer and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Finnish singer and actress", "links": [{"title": "Saara <PERSON>", "link": "https://wikipedia.org/wiki/Saara_A<PERSON>to"}]}, {"year": "1987", "text": "<PERSON>, Japanese singer-songwriter and actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese singer-songwriter and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American sports analyst and football player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports analyst and football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sports analyst and football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Canadian ice hockey player", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON><PERSON><PERSON><PERSON>, Dominican baseball player", "html": "1988 - <a href=\"https://wikipedia.org/wiki/Neftal%C3%AD_Feliz\" title=\"<PERSON>eftal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Neftal%C3%AD_Feliz\" title=\"<PERSON>eftal<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Neftal%C3%AD_Feliz"}]}, {"year": "1990", "text": "<PERSON>, American actress", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, American basketball player", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON>, South Korean actor and singer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean actor and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean actor and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>on"}]}, {"year": "1991", "text": "<PERSON>, Dominican baseball player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dominican baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON>, South Korean singer", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>mi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>mi\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South Korean singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>mi"}]}, {"year": "1992", "text": "<PERSON>, Spanish tennis player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>r%C3%B3_Flor\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_<PERSON>r%C3%B3_Flor\" title=\"<PERSON>\"><PERSON></a>, Spanish tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Mar%C3%<PERSON><PERSON>_<PERSON>_Torr%C3%B3_Flor"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Welsh track cyclist", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Doull\"><PERSON><PERSON></a>, Welsh track cyclist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Doull\"><PERSON><PERSON></a>, Welsh track cyclist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Indonesian singer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indonesian singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>i"}]}, {"year": "1993", "text": "<PERSON>, Chinese singer and rapper", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer and rapper", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese singer and rapper", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American singer-songwriter", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Lucy Dacus\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai singer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Cherprang_<PERSON>\" title=\"Cherprang Areekul\">Ch<PERSON><PERSON><PERSON></a>, Thai singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cherprang_<PERSON>\" title=\"Cherprang Areekul\">Ch<PERSON><PERSON><PERSON></a>, Thai singer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Cherprang_<PERSON><PERSON>"}]}, {"year": "1996", "text": "<PERSON>, German footballer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON>, American swimmer", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American swimmer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON><PERSON><PERSON><PERSON>, Thai singer", "html": "1997 - <a href=\"https://wikipedia.org/wiki/BamBam\" title=\"BamBam\"><PERSON><PERSON><PERSON><PERSON></a>, Thai singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/BamBam\" title=\"BamBam\"><PERSON><PERSON><PERSON><PERSON></a>, Thai singer", "links": [{"title": "BamBam", "link": "https://wikipedia.org/wiki/BamBam"}]}, {"year": "2015", "text": "Princess <PERSON> of Wales, British royal, and third in line to the British throne", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Wales_(born_2015)\" title=\"Princess <PERSON> of Wales (born 2015)\">Princess <PERSON> of Wales</a>, British royal, and third <a href=\"https://wikipedia.org/wiki/Succession_to_the_British_throne\" title=\"Succession to the British throne\">in line</a> to the <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">British throne</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Princess_<PERSON>_of_Wales_(born_2015)\" title=\"Princess <PERSON> of Wales (born 2015)\">Princess <PERSON> of Wales</a>, British royal, and third <a href=\"https://wikipedia.org/wiki/Succession_to_the_British_throne\" title=\"Succession to the British throne\">in line</a> to the <a href=\"https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom\" title=\"Monarchy of the United Kingdom\">British throne</a>", "links": [{"title": "Princess <PERSON> of Wales (born 2015)", "link": "https://wikipedia.org/wiki/Princess_Charlotte_of_Wales_(born_2015)"}, {"title": "Succession to the British throne", "link": "https://wikipedia.org/wiki/Succession_to_the_British_throne"}, {"title": "Monarchy of the United Kingdom", "link": "https://wikipedia.org/wiki/Monarchy_of_the_United_Kingdom"}]}], "Deaths": [{"year": "1203 BCE", "text": "<PERSON><PERSON><PERSON><PERSON>, pharaoh of Egypt", "html": "1203 BCE - 1203 BCE - <a href=\"https://wikipedia.org/wiki/Merneptah\" title=\"Merneptah\">Me<PERSON>ptah</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">pharaoh</a> of <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>", "no_year_html": "1203 BCE - <a href=\"https://wikipedia.org/wiki/Merneptah\" title=\"Merneptah\">Me<PERSON>ptah</a>, <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\">pharaoh</a> of <a href=\"https://wikipedia.org/wiki/Egypt\" title=\"Egypt\">Egypt</a>", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Merne<PERSON>h"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>"}, {"title": "Egypt", "link": "https://wikipedia.org/wiki/Egypt"}]}, {"year": "373 CE", "text": "<PERSON><PERSON><PERSON> of Alexandria, Egyptian bishop and saint (b. 298)", "html": "373 CE - 373 CE - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON> of Alexandria</a>, Egyptian bishop and saint (b. 298)", "no_year_html": "373 CE - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria\" title=\"<PERSON><PERSON><PERSON> of Alexandria\"><PERSON><PERSON><PERSON> of Alexandria</a>, Egyptian bishop and saint (b. 298)", "links": [{"title": "<PERSON><PERSON><PERSON> of Alexandria", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Alexandria"}]}, {"year": "649", "text": "<PERSON><PERSON><PERSON> of Tikrit, Persian theologian  of the Syriac Orthodox Church (b. 565)", "html": "649 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tikrit\" title=\"<PERSON><PERSON><PERSON> of Tikrit\"><PERSON><PERSON><PERSON> of Tikrit</a>, Persian <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theologian</a> of the <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox Church</a> (b. 565)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_of_Tikrit\" title=\"<PERSON><PERSON><PERSON> of Tikrit\"><PERSON><PERSON><PERSON> of Tikrit</a>, Persian <a href=\"https://wikipedia.org/wiki/Theology\" title=\"Theology\">theologian</a> of the <a href=\"https://wikipedia.org/wiki/Syriac_Orthodox_Church\" title=\"Syriac Orthodox Church\">Syriac Orthodox Church</a> (b. 565)", "links": [{"title": "<PERSON><PERSON><PERSON> of Tikrit", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON><PERSON><PERSON>"}, {"title": "Theology", "link": "https://wikipedia.org/wiki/Theology"}, {"title": "Syriac Orthodox Church", "link": "https://wikipedia.org/wiki/Syriac_Orthodox_Church"}]}, {"year": "821", "text": "<PERSON>, general of the Tang Dynasty", "html": "821 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, general of the Tang Dynasty", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "907", "text": "<PERSON> of Bulgaria", "html": "907 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria\" title=\"<PERSON> of Bulgaria\"><PERSON> of Bulgaria</a>", "links": [{"title": "<PERSON> of Bulgaria", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Bulgaria"}]}, {"year": "1219", "text": "<PERSON>, King of Armenia (b. 1150)", "html": "1219 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia\" title=\"<PERSON>, King of Armenia\"><PERSON>, King of Armenia</a> (b. 1150)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia\" title=\"<PERSON>, King of Armenia\"><PERSON>, King of Armenia</a> (b. 1150)", "links": [{"title": "<PERSON>, King of Armenia", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_King_of_Armenia"}]}, {"year": "1230", "text": "<PERSON>, English son of <PERSON> (b. 1197)", "html": "1230 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1230)\" title=\"<PERSON> (died 1230)\"><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1197)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(died_1230)\" title=\"<PERSON> (died 1230)\"><PERSON></a>, English son of <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> (b. 1197)", "links": [{"title": "<PERSON> (died 1230)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_(died_1230)"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1293", "text": "<PERSON><PERSON> of Rothenburg, German rabbi (b. c.1215)", "html": "1293 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Rothenburg\" title=\"<PERSON><PERSON> of Rothenburg\"><PERSON><PERSON> of Rothenburg</a>, German rabbi (b. c.1215)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Rothenburg\" title=\"<PERSON><PERSON> of Rothenburg\"><PERSON><PERSON> of Rothenburg</a>, German rabbi (b. c.1215)", "links": [{"title": "<PERSON><PERSON> of Rothenburg", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Roth<PERSON>"}]}, {"year": "1300", "text": "<PERSON> Artois (b. 1248)", "html": "1300 - <a href=\"https://wikipedia.org/wiki/<PERSON>_of_Artois\" title=\"<PERSON> of Artois\"><PERSON> of Artois</a> (b. 1248)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_of_Artois\" title=\"<PERSON> of Artois\"><PERSON> of Artois</a> (b. 1248)", "links": [{"title": "Blanche of Artois", "link": "https://wikipedia.org/wiki/<PERSON>_of_Artois"}]}, {"year": "1450", "text": "<PERSON>, 1st Duke of Suffolk, English admiral (b. 1396)", "html": "1450 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English admiral (b. 1396)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Suffolk\" title=\"<PERSON>, 1st Duke of Suffolk\"><PERSON>, 1st Duke of Suffolk</a>, English admiral (b. 1396)", "links": [{"title": "<PERSON>, 1st Duke of Suffolk", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>,_1st_Duke_of_Suffolk"}]}, {"year": "1519", "text": "<PERSON>, Italian painter, sculptor, and architect (b. 1452)", "html": "1519 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> da Vinci\"><PERSON></a>, Italian painter, sculptor, and architect (b. 1452)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON> da Vinci\"><PERSON></a>, Italian painter, sculptor, and architect (b. 1452)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1564", "text": "<PERSON><PERSON><PERSON>, Italian cardinal (b. 1500)", "html": "1564 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1500)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian cardinal (b. 1500)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1627", "text": "<PERSON><PERSON><PERSON><PERSON>, Italian composer and educator (b. 1560)", "html": "1627 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Viadana\" title=\"<PERSON><PERSON><PERSON><PERSON> da Viadana\"><PERSON><PERSON><PERSON><PERSON> Viadana</a>, Italian composer and educator (b. 1560)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Viadana\" title=\"<PERSON><PERSON><PERSON><PERSON> da Viadana\"><PERSON><PERSON><PERSON><PERSON> Viadana</a>, Italian composer and educator (b. 1560)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>dan<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1667", "text": "<PERSON>, English poet and author (b. 1588)", "html": "1667 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1588)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet and author (b. 1588)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1683", "text": "<PERSON><PERSON><PERSON>, Croatian philosopher and mathematician (b. 1613)", "html": "1683 - <a href=\"https://wikipedia.org/wiki/Stjepan_Gradi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian philosopher and mathematician (b. 1613)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Stjepan_Gradi%C4%87\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Croatian philosopher and mathematician (b. 1613)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Stjepan_Gradi%C4%87"}]}, {"year": "1711", "text": "<PERSON>, 1st Earl of Rochester, English politician, First Lord of the Treasury (b. 1641)", "html": "1711 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Rochester\" title=\"<PERSON>, 1st Earl of Rochester\"><PERSON>, 1st Earl of Rochester</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (b. 1641)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Rochester\" title=\"<PERSON>, 1st Earl of Rochester\"><PERSON>, 1st Earl of Rochester</a>, English politician, <a href=\"https://wikipedia.org/wiki/First_Lord_of_the_Treasury\" title=\"First Lord of the Treasury\">First Lord of the Treasury</a> (b. 1641)", "links": [{"title": "<PERSON>, 1st Earl of Rochester", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Rochester"}, {"title": "First Lord of the Treasury", "link": "https://wikipedia.org/wiki/First_Lord_of_the_Treasury"}]}, {"year": "1799", "text": "<PERSON>, 2nd Count of Revillagigedo (b. 1740)", "html": "1799 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_2nd_Count_of_Revillagigedo\" title=\"<PERSON>, 2nd Count of Revillagigedo\"><PERSON>, 2nd Count of Revillagigedo</a> (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>%C3%<PERSON><PERSON><PERSON>,_2nd_Count_of_Revillagigedo\" title=\"<PERSON>, 2nd Count of Revillagigedo\"><PERSON>, 2nd Count of Revillagigedo</a> (b. 1740)", "links": [{"title": "<PERSON>, 2nd Count of Revillagigedo", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_de_G%C3%<PERSON><PERSON><PERSON>,_2nd_Count_of_Revillagigedo"}]}, {"year": "1802", "text": "<PERSON>, Dutch general and politician, Governor-General of the Dutch Gold Coast (b. 1762)", "html": "1802 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch general and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_the_Dutch_Gold_Coast\" title=\"List of colonial governors of the Dutch Gold Coast\">Governor-General of the Dutch Gold Coast</a> (b. 1762)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch general and politician, <a href=\"https://wikipedia.org/wiki/List_of_colonial_governors_of_the_Dutch_Gold_Coast\" title=\"List of colonial governors of the Dutch Gold Coast\">Governor-General of the Dutch Gold Coast</a> (b. 1762)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of colonial governors of the Dutch Gold Coast", "link": "https://wikipedia.org/wiki/List_of_colonial_governors_of_the_Dutch_Gold_Coast"}]}, {"year": "1810", "text": "<PERSON>, English priest (b. 1740)", "html": "1810 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest (b. 1740)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English priest (b. 1740)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, English painter and academic (b. 1744)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English painter and academic (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1856", "text": "<PERSON>, American poet, surgeon and geologist (b. 1795)", "html": "1856 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>val\" title=\"<PERSON> Percival\"><PERSON></a>, American poet, surgeon and geologist (b. 1795)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>val\" title=\"<PERSON> Percival\"><PERSON></a>, American poet, surgeon and geologist (b. 1795)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1857", "text": "<PERSON>, French dramatist, poet, and novelist  (b. 1810)", "html": "1857 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dramatist, poet, and novelist (b. 1810)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French dramatist, poet, and novelist (b. 1810)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1864", "text": "<PERSON>, German composer and educator (b. 1791)", "html": "1864 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1791)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German composer and educator (b. 1791)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, Peruvian politician (b. 1819)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/Jos%C3%A9_G%C3%A1lvez_Eg%C3%BAsquiza\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician (b. 1819)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jos%C3%A9_G%C3%A1lvez_Eg%C3%BAsquiza\" title=\"<PERSON>\"><PERSON></a>, Peruvian politician (b. 1819)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Jos%C3%A9_G%C3%A1lvez_Eg%C3%BAsquiza"}]}, {"year": "1880", "text": "<PERSON><PERSON><PERSON>, German-American businessman, co-founded Anheuser-Busch (b. 1805)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>-<PERSON>\" title=\"Anheuser-Busch\">Anheuser-Busch</a> (b. 1805)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German-American businessman, co-founded <a href=\"https://wikipedia.org/wiki/An<PERSON><PERSON>-<PERSON>\" title=\"Anheus<PERSON>-Busch\">Anheuser-Busch</a> (b. 1805)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Anheuser-Busch", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>-Busch"}]}, {"year": "1880", "text": "<PERSON>, Australian cricketer, co-created Australian rules football (b. 1835)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, co-created <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> (b. 1835)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer, co-created <a href=\"https://wikipedia.org/wiki/Australian_rules_football\" title=\"Australian rules football\">Australian rules football</a> (b. 1835)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Australian rules football", "link": "https://wikipedia.org/wiki/Australian_rules_football"}]}, {"year": "1885", "text": "<PERSON><PERSON><PERSON>, Hungarian-Slovene author (b. 1817)", "html": "1885 - <a href=\"https://wikipedia.org/wiki/Ter%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>akou<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene author (b. 1817)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ter%C3%A<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Zakou<PERSON>\"><PERSON><PERSON><PERSON></a>, Hungarian-Slovene author (b. 1817)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ter%C3%A9<PERSON>_<PERSON>"}]}, {"year": "1900", "text": "<PERSON>, Norwegian priest, social reformer, politician, and newspaper editor (b. 1838)", "html": "1900 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)\" title=\"<PERSON> (born 1838)\"><PERSON></a>, Norwegian priest, social reformer, politician, and newspaper editor (b. 1838)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)\" title=\"<PERSON> (born 1838)\"><PERSON></a>, Norwegian priest, social reformer, politician, and newspaper editor (b. 1838)", "links": [{"title": "<PERSON> (born 1838)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(born_1838)"}]}, {"year": "1912", "text": "<PERSON>, American political cartoonist (b. 1867)", "html": "1912 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political cartoonist (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political cartoonist (b. 1867)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, German chemist (b. 1870)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German chemist (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON>, Estonian lawyer and politician (b. 1889)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/J%C3%BCri_Vilms\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (b. 1889)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/J%C3%BCri_Vilms\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Estonian lawyer and politician (b. 1889)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/J%C3%BCri_Vilms"}]}, {"year": "1925", "text": "<PERSON><PERSON>, Croatian and Bosnian-Herzegovinian poet (b. 1898)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Antun_Branko_%C5%A0imi%C4%87\" title=\"An<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian and Bosnian-Herzegovinian poet (b. 1898)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antun_Branko_%C5%A0imi%C4%87\" title=\"Antun <PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Croatian and Bosnian-Herzegovinian poet (b. 1898)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Antun_Branko_%C5%A0imi%C4%87"}]}, {"year": "1925", "text": "<PERSON>, Austrian astronomer (b. 1848)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian astronomer (b. 1848)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian astronomer (b. 1848)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, English physiologist and academic (b. 1866)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (b. 1866)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English physiologist and academic (b. 1866)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON><PERSON>, Greek general and politician, Greek Minister for Military Affairs (b. 1879)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/Charalambos_Tser<PERSON>lis\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister for Military Affairs</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charalambos_Tser<PERSON>lis\" title=\"<PERSON>ral<PERSON><PERSON>ser<PERSON>lis\"><PERSON><PERSON><PERSON><PERSON></a>, Greek general and politician, <a href=\"https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece\" title=\"List of defence ministers of Greece\">Greek Minister for Military Affairs</a> (b. 1879)", "links": [{"title": "Charalam<PERSON>", "link": "https://wikipedia.org/wiki/Charalambos_<PERSON>lis"}, {"title": "List of defence ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_defence_ministers_of_Greece"}]}, {"year": "1940", "text": "<PERSON>, English explorer (b. 1875)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English explorer (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, Greek author (b. 1874)", "html": "1941 - <a href=\"https://wikipedia.org/wiki/Penelope_Delta\" title=\"Penelope Delta\"><PERSON></a>, Greek author (b. 1874)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Penelope_Delta\" title=\"Penelope Delta\"><PERSON></a>, Greek author (b. 1874)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Penelope_<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, German politician (b. 1900)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1900)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German politician (b. 1900)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1945", "text": "<PERSON>, American baseball player and journalist (b. 1875)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (b. 1875)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and journalist (b. 1875)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian journalist, lawyer, politician, and decorated soldier (b. 1872)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, lawyer, politician, and decorated soldier (b. 1872)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist, lawyer, politician, and decorated soldier (b. 1872)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, German SS officer (b. 1920)", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German <a href=\"https://wikipedia.org/wiki/SS\" class=\"mw-redirect\" title=\"SS\">SS</a> officer (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "SS", "link": "https://wikipedia.org/wiki/SS"}]}, {"year": "1953", "text": "<PERSON>, American archer (b. 1863)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, American archer (b. 1863)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archer)\" title=\"<PERSON> (archer)\"><PERSON></a>, American archer (b. 1863)", "links": [{"title": "<PERSON> (archer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(archer)"}]}, {"year": "1957", "text": "<PERSON>, American captain, lawyer, judge, and politician (b. 1908)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, judge, and politician (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American captain, lawyer, judge, and politician (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1963", "text": "<PERSON>, 3rd Baron <PERSON>, English cricketer, peer, politician, poet, author and newspaper editor (b. 1884)", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English cricketer, peer, politician, poet, author and newspaper editor (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>\" title=\"<PERSON>, 3rd Baron <PERSON>\"><PERSON>, 3rd Baron <PERSON></a>, English cricketer, peer, politician, poet, author and newspaper editor (b. 1884)", "links": [{"title": "<PERSON>, 3rd <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>,_3rd_Baron_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Viscountess <PERSON>, American-English politician (b. 1879)", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, American-English politician (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Viscountess_<PERSON><PERSON>\" title=\"<PERSON>, Viscountess <PERSON>\"><PERSON>, Viscountess <PERSON></a>, American-English politician (b. 1879)", "links": [{"title": "<PERSON>, Viscountess <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1969", "text": "<PERSON>, German general and politician, Chancellor of Germany (b. 1879)", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1879)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German general and politician, <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1879)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1972", "text": "<PERSON><PERSON> <PERSON>, American 1st director of the Federal Bureau of Investigation (b. 1895)", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American 1st <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">director of the Federal Bureau of Investigation</a> (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American 1st <a href=\"https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation\" title=\"Director of the Federal Bureau of Investigation\">director of the Federal Bureau of Investigation</a> (b. 1895)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "Director of the Federal Bureau of Investigation", "link": "https://wikipedia.org/wiki/Director_of_the_Federal_Bureau_of_Investigation"}]}, {"year": "1974", "text": "<PERSON>, American admiral (b. 1878)", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1878)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1878)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American principal dancer and charter member of the New York City Ballet (b. 1922)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American principal dancer and charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American principal dancer and charter member of the <a href=\"https://wikipedia.org/wiki/New_York_City_Ballet\" title=\"New York City Ballet\">New York City Ballet</a> (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "New York City Ballet", "link": "https://wikipedia.org/wiki/New_York_City_Ballet"}]}, {"year": "1979", "text": "<PERSON><PERSON><PERSON>, Italian chemist and engineer, Nobel Prize laureate (b. 1903)", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian chemist and engineer, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry\" title=\"Nobel Prize in Chemistry\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Chemistry", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Chemistry"}]}, {"year": "1980", "text": "<PERSON><PERSON><PERSON>, New Zealand-Australian cricketer (b. 1891)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer (b. 1891)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, New Zealand-Australian cricketer (b. 1891)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Hungarian-American animator and producer (b. 1908)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American animator and producer (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American animator and producer (b. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, American football player and coach (b. 1926)", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American football player and coach (b. 1926)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, American game show host and producer, co-founded Barry & Enright Productions (b. 1918)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_show_host)\" title=\"<PERSON> (game show host)\"><PERSON></a>, American game show host and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_%26_Enright_Productions\" title=\"Barry &amp; Enright Productions\">Barry &amp; Enright Productions</a> (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_show_host)\" title=\"<PERSON> (game show host)\"><PERSON></a>, American game show host and producer, co-founded <a href=\"https://wikipedia.org/wiki/<PERSON>_%26_Enright_Productions\" title=\"Barry &amp; Enright Productions\">Barry &amp; Enright Productions</a> (b. 1918)", "links": [{"title": "<PERSON> (game show host)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(game_show_host)"}, {"title": "Barry & Enright Productions", "link": "https://wikipedia.org/wiki/<PERSON>_%26_Enright_Productions"}]}, {"year": "1984", "text": "<PERSON>, American animator, director, and producer (b. 1913)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator, director, and producer (b. 1913)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON><PERSON><PERSON>, Italian race car driver (b. 1951)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Italian race car driver (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Attil<PERSON>_Bettega"}]}, {"year": "1985", "text": "<PERSON>, American trumpet player and bandleader (b. 1909)", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and bandleader (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American race car driver (b. 1956)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Sergio_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, Finnish race car driver (b. 1956)", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver (b. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Finnish race car driver (b. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Russian author (b. 1902)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author (b. 1902)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Russian author (b. 1902)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V<PERSON><PERSON>_<PERSON>in"}]}, {"year": "1989", "text": "<PERSON>, Italian cardinal (b. 1906)", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian cardinal (b. 1906)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1990", "text": "<PERSON>, English-American actor (b. 1951)", "html": "1990 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actor (b. 1951)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Indian Politician (b. 1924)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Politician (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Indian Politician (b. 1924)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON>, Australian journalist and author (b. 1909)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian journalist and author (b. 1909)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American lawyer and politician (b. 1909)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (b. 1909)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American lawyer and politician (b. 1909)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, French race car driver, pilot, and politician (b. 1921)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver, pilot, and politician (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Andr%C3%A9_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French race car driver, pilot, and politician (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Andr%C3%A9_<PERSON><PERSON>t"}]}, {"year": "1994", "text": "<PERSON>, American poet and author (b. 1903)", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American poet and author (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, Australian public servant and diplomat,  (b. 1918)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" class=\"mw-redirect\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)\" class=\"mw-redirect\" title=\"<PERSON> (diplomat)\"><PERSON></a>, Australian public servant and diplomat, (b. 1918)", "links": [{"title": "<PERSON> (diplomat)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(diplomat)"}]}, {"year": "1995", "text": "<PERSON>, English actor (b. 1911)", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, Australian neurophysiologist and academic, Nobel Prize laureate (b. 1903)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(neurophysiologist)\" title=\"<PERSON> (neurophysiologist)\"><PERSON></a>, Australian neurophysiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(neurophysiologist)\" title=\"<PERSON> (neurophysiologist)\"><PERSON></a>, Australian neurophysiologist and academic, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1903)", "links": [{"title": "<PERSON> (neurophysiologist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(neurophysiologist)"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1997", "text": "<PERSON>, Brazilian philosopher and academic (b. 1921)", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian philosopher and academic (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Brazilian philosopher and academic (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1998", "text": "<PERSON>, Japanese singer-songwriter, guitarist, and producer (b. 1964)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\">hide</a>, Japanese singer-songwriter, guitarist, and producer (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\">hide</a>, Japanese singer-songwriter, guitarist, and producer (b. 1964)", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1998", "text": "<PERSON>, English footballer (b. 1961)", "html": "1998 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer (b. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, Canadian politician (b. 1903)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (b. 1903)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian politician (b. 1903)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ness"}]}, {"year": "1999", "text": "<PERSON>, English actor (b. 1938)", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor (b. 1938)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON><PERSON>, Indo-Trinidadian musician (b. 1943)", "html": "2000 - <a href=\"https://wikipedia.org/wiki/Sundar_Popo\" title=\"Sundar Popo\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Indo-Trinidadian\" class=\"mw-redirect\" title=\"Indo-Trinidadian\">Indo-Trinidadian</a> musician (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sundar_Popo\" title=\"Sundar Popo\"><PERSON><PERSON></a>, <a href=\"https://wikipedia.org/wiki/Indo-Trinidadian\" class=\"mw-redirect\" title=\"Indo-Trinidadian\">Indo-Trinidadian</a> musician (b. 1943)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sundar_Popo"}, {"title": "Indo-Trinidadian", "link": "https://wikipedia.org/wiki/Indo-Trinidadian"}]}, {"year": "2002", "text": "<PERSON><PERSON> <PERSON><PERSON>, English-Canadian mathematician and academic (b. 1917)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Canadian mathematician and academic (b. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"W<PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English-Canadian mathematician and academic (b. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2005", "text": "<PERSON><PERSON>, Singaporean journalist and politician, 4th President of Singapore (b. 1915)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean journalist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Singaporean journalist and politician, 4th <a href=\"https://wikipedia.org/wiki/President_of_Singapore\" title=\"President of Singapore\">President of Singapore</a> (b. 1915)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Singapore", "link": "https://wikipedia.org/wiki/President_of_Singapore"}]}, {"year": "2006", "text": "<PERSON>, American journalist and author (b. 1933)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and author (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "<PERSON>, New Zealand director and screenwriter (b. 1964)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and screenwriter (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand director and screenwriter (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, American actress (b. 1940)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American actress (b. 1940)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2008", "text": "<PERSON><PERSON><PERSON>, Ukrainian-Estonian astronomer and academic (b. 1938)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/Izold_Pust%C3%B5lnik\" title=\"Izold Pustõlnik\"><PERSON><PERSON><PERSON></a>, Ukrainian-Estonian astronomer and academic (b. 1938)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Izold_Pust%C3%B5lnik\" title=\"Izold Pustõlnik\"><PERSON><PERSON><PERSON></a>, Ukrainian-Estonian astronomer and academic (b. 1938)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Izold_Pust%C3%B5lnik"}]}, {"year": "2009", "text": "<PERSON>, American author and academic (b. 1929)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/Marilyn_French\" title=\"Marilyn French\"><PERSON></a>, American author and academic (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Marilyn_French\" title=\"Marilyn French\"><PERSON></a>, American author and academic (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON><PERSON>, Japanese singer-songwriter, producer, and actor (b. 1951)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, producer, and actor (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese singer-songwriter, producer, and actor (b. 1951)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, American football player and politician, 9th United States Secretary of Housing and Urban Development (b. 1935)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a> (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player and politician, 9th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a> (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Housing and Urban Development", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development"}]}, {"year": "2010", "text": "<PERSON>, English-American actress and singer (b. 1943)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and singer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-American actress and singer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Saudi Arabian terrorist, founder of Al-Qaeda (b. 1957)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON></a>, Saudi Arabian terrorist, founder of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a> (b. 1957)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>\" title=\"<PERSON><PERSON><PERSON> bin <PERSON>\"><PERSON><PERSON><PERSON></a>, Saudi Arabian terrorist, founder of <a href=\"https://wikipedia.org/wiki/Al-Qaeda\" title=\"Al-Qaeda\">Al-Qaeda</a> (b. 1957)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>den"}, {"title": "Al-Qaeda", "link": "https://wikipedia.org/wiki/Al-Qaeda"}]}, {"year": "2012", "text": "<PERSON>, Portuguese director and screenwriter (b. 1935)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Portuguese director and screenwriter (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)\" title=\"<PERSON> (filmmaker)\"><PERSON></a>, Portuguese director and screenwriter (b. 1935)", "links": [{"title": "<PERSON> (filmmaker)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(filmmaker)"}]}, {"year": "2012", "text": "<PERSON><PERSON><PERSON>, Cuban-born American-naturalized pianist (b. 1932)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Zenaida_Manfug%C3%A1s\" title=\"Zenaid<PERSON> Manfugás\"><PERSON><PERSON><PERSON></a>, Cuban-born American-naturalized pianist (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zen<PERSON><PERSON>_Manfug%C3%A1s\" title=\"Zenaid<PERSON> Manfugás\"><PERSON><PERSON><PERSON></a>, Cuban-born American-naturalized pianist (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Zenaida_Manfug%C3%A1s"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Russian playwright and politician (b. 1936)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1nullin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian playwright and politician (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>%C3%B1nullin\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Russian playwright and politician (b. 1936)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tufan_Mi%C3%B1nullin"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Indonesian physician and politician, Indonesian Minister of Health (b. 1955)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>dyaning<PERSON>h\" title=\"Endang Rahayu Sedyaning<PERSON>\"><PERSON><PERSON></a>, Indonesian physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Indonesia)\" title=\"Ministry of Health (Indonesia)\">Indonesian Minister of Health</a> (b. 1955)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>ani<PERSON>\" title=\"Endang <PERSON>ani<PERSON>\"><PERSON><PERSON></a>, Indonesian physician and politician, <a href=\"https://wikipedia.org/wiki/Ministry_of_Health_(Indonesia)\" title=\"Ministry of Health (Indonesia)\">Indonesian Minister of Health</a> (b. 1955)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>ani<PERSON>h"}, {"title": "Ministry of Health (Indonesia)", "link": "https://wikipedia.org/wiki/Ministry_of_Health_(Indonesia)"}]}, {"year": "2012", "text": "<PERSON>, Japanese physicist, author, and academic (b. 1942)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese physicist, author, and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Japanese physicist, author, and academic (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON><PERSON>, Venezuelan actress (b. 1963)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress (b. 1963)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Venezuelan actress (b. 1963)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Lourdes_Valera"}]}, {"year": "2013", "text": "<PERSON>, English boxer (b. 1943)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernie Field\"><PERSON></a>, English boxer (b. 1943)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Ernie Field\"><PERSON></a>, English boxer (b. 1943)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernie_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American guitarist and songwriter (b. 1964)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter (b. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American bishop (b. 1947)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bishop (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON><PERSON>, Israeli author and educator (b. 1932)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>vor<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli author and educator (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>vor<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Israeli author and educator (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Dvora_Omer"}]}, {"year": "2013", "text": "<PERSON>, Croatian footballer (b. 1980)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer (b. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Croatian footballer (b. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2013", "text": "<PERSON>, American painter and illustrator (b. 1918)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American painter and illustrator (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2014", "text": "<PERSON><PERSON>, Brazilian bishop (b. 1922)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Balduino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian bishop (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_Balduino\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian bishop (b. 1922)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%A1s_Balduino"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, Slovenian director, playwright, and screenwriter (b. 1929)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/%C5%BDark<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian director, playwright, and screenwriter (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C5%BDark<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Slovenian director, playwright, and screenwriter (b. 1929)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C5%BDarko_Petan"}]}, {"year": "2014", "text": "<PERSON><PERSON><PERSON>, American actor (b. 1918)", "html": "2014 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zimbalist_Jr.\" title=\"<PERSON><PERSON><PERSON> Zimbalist Jr.\"><PERSON><PERSON><PERSON> Jr.</a>, American actor (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Zimbalist_Jr.\" title=\"<PERSON><PERSON><PERSON> Zimbalist Jr.\"><PERSON><PERSON><PERSON> Jr.</a>, American actor (b. 1918)", "links": [{"title": "<PERSON><PERSON><PERSON>.", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Z<PERSON>bal<PERSON>_Jr."}]}, {"year": "2015", "text": "<PERSON>, English colonel and architect (b. 1915)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and architect (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English colonel and architect (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, American author and screenwriter (b. 1945)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(author)\" title=\"<PERSON> (author)\"><PERSON></a>, American author and screenwriter (b. 1945)", "links": [{"title": "<PERSON> (author)", "link": "https://wikipedia.org/wiki/<PERSON>_(author)"}]}, {"year": "2015", "text": "<PERSON>, American singer and musicologist (b. 1927)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musicologist (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and musicologist (b. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Russian-Lithuanian ballerina, choreographer, actress, and director (b. 1925)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Lithuanian ballerina, choreographer, actress, and director (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-Lithuanian ballerina, choreographer, actress, and director (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maya_Plisetskaya"}]}, {"year": "2015", "text": "<PERSON>, English author (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON>, American music businesswoman, activist, and Black Panther (b. 1947)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>feni_Shakur\" title=\"Afeni Shakur\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Music_industry\" title=\"Music industry\">music businesswoman</a>, <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a>, and <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther</a> (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Shakur\" title=\"Afeni Shakur\"><PERSON><PERSON><PERSON></a>, American <a href=\"https://wikipedia.org/wiki/Music_industry\" title=\"Music industry\">music businesswoman</a>, <a href=\"https://wikipedia.org/wiki/Activist\" class=\"mw-redirect\" title=\"Activist\">activist</a>, and <a href=\"https://wikipedia.org/wiki/Black_Panther_Party\" title=\"Black Panther Party\">Black Panther</a> (b. 1947)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>ur"}, {"title": "Music industry", "link": "https://wikipedia.org/wiki/Music_industry"}, {"title": "Activist", "link": "https://wikipedia.org/wiki/Activist"}, {"title": "Black Panther Party", "link": "https://wikipedia.org/wiki/Black_Panther_Party"}]}, {"year": "2020", "text": "<PERSON><PERSON>, Pakistani politician, leader of the Pashtun Tahafuz Movement (b. 1982)", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Wazir\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, leader of the <a href=\"https://wikipedia.org/wiki/Pashtun_Tahafuz_Movement\" title=\"Pashtun Tahafuz Movement\">Pashtun Tahafuz Movement</a> (b. 1982)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>azi<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Pakistani politician, leader of the <a href=\"https://wikipedia.org/wiki/Pashtun_Tahafuz_Movement\" title=\"Pashtun Tahafuz Movement\">Pashtun Tahafuz Movement</a> (b. 1982)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_Wazir"}, {"title": "Pashtun Tahafuz Movement", "link": "https://wikipedia.org/wiki/Pashtun_Tahafuz_Movement"}]}, {"year": "2021", "text": "<PERSON>, Belgian record producer and lyricist (b. 1925)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian record producer and lyricist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian record producer and lyricist (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2024", "text": "<PERSON><PERSON><PERSON><PERSON>, Dutch figure skater (b. 1942)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch figure skater (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"S<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Dutch figure skater (b. 1942)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sjou<PERSON><PERSON>_<PERSON>stra"}]}, {"year": "2024", "text": "<PERSON>, American basketball player (b. 1991)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (b. 1991)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, English golfer and broadcaster (b. 1948)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and broadcaster (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English golfer and broadcaster (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}]}}