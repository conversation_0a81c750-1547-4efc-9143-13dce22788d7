{"date": "September 16", "url": "https://wikipedia.org/wiki/September_16", "data": {"Events": [{"year": "681", "text": "<PERSON> <PERSON><PERSON> is posthumously excommunicated by the Sixth Ecumenical Council.", "html": "681 - <a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_I\" title=\"Pope Honorius I\">Pope <PERSON><PERSON> I</a> is posthumously <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicated</a> by the <a href=\"https://wikipedia.org/wiki/Third_Council_of_Constantinople\" title=\"Third Council of Constantinople\">Sixth Ecumenical Council</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_Honorius_I\" title=\"Pope Honorius I\">Pope <PERSON><PERSON> I</a> is posthumously <a href=\"https://wikipedia.org/wiki/Excommunication\" title=\"Excommunication\">excommunicated</a> by the <a href=\"https://wikipedia.org/wiki/Third_Council_of_Constantinople\" title=\"Third Council of Constantinople\">Sixth Ecumenical Council</a>.", "links": [{"title": "<PERSON> <PERSON> I", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Excommunication", "link": "https://wikipedia.org/wiki/Excommunication"}, {"title": "Third Council of Constantinople", "link": "https://wikipedia.org/wiki/Third_Council_of_Constantinople"}]}, {"year": "1400", "text": "<PERSON><PERSON> is declared Prince of Wales by his followers.", "html": "1400 - <a href=\"https://wikipedia.org/wiki/O<PERSON>_Glynd%C5%B5r\" title=\"<PERSON><PERSON> Glyndŵr\"><PERSON><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a> by his followers.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Glynd%C5%B5r\" title=\"<PERSON><PERSON> Glyndŵr\"><PERSON><PERSON></a> is declared <a href=\"https://wikipedia.org/wiki/Prince_of_Wales\" title=\"Prince of Wales\">Prince of Wales</a> by his followers.", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Owain_Glynd%C5%B5r"}, {"title": "Prince of Wales", "link": "https://wikipedia.org/wiki/Prince_of_Wales"}]}, {"year": "1620", "text": "Pilgrims set sail for Virginia from Plymouth, England in the Mayflower.", "html": "1620 - <a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> set sail for Virginia from Plymouth, England in the <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pilgrim_Fathers\" class=\"mw-redirect\" title=\"Pilgrim Fathers\">Pilgrims</a> set sail for Virginia from Plymouth, England in the <i><a href=\"https://wikipedia.org/wiki/Mayflower\" title=\"Mayflower\">Mayflower</a></i>.", "links": [{"title": "Pilgrim Fathers", "link": "https://wikipedia.org/wiki/Pilgrim_Fathers"}, {"title": "May<PERSON>", "link": "https://wikipedia.org/wiki/Mayflower"}]}, {"year": "1701", "text": "<PERSON>, sometimes called the \"Old Pretender\", becomes the Jacobite claimant to the thrones of England and Scotland.", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, sometimes called the \"Old Pretender\", becomes the <a href=\"https://wikipedia.org/wiki/Jacobitism\" title=\"Jacobitism\">Jacobite claimant</a> to the thrones of England and Scotland.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">James <PERSON></a>, sometimes called the \"Old Pretender\", becomes the <a href=\"https://wikipedia.org/wiki/Jacobitism\" title=\"Jacobitism\">Jacobite claimant</a> to the thrones of England and Scotland.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>"}, {"title": "Jacobitism", "link": "https://wikipedia.org/wiki/Jacobitism"}]}, {"year": "1732", "text": "In Campo Maior, Portugal, a storm hits the Armory and a violent explosion ensues, killing two-thirds of its inhabitants.", "html": "1732 - In <a href=\"https://wikipedia.org/wiki/Campo_Maior,_Portugal\" title=\"Campo Maior, Portugal\">Campo Maior, Portugal</a>, a storm hits the Armory and a violent explosion ensues, killing two-thirds of its inhabitants.", "no_year_html": "In <a href=\"https://wikipedia.org/wiki/Campo_Maior,_Portugal\" title=\"Campo Maior, Portugal\">Campo Maior, Portugal</a>, a storm hits the Armory and a violent explosion ensues, killing two-thirds of its inhabitants.", "links": [{"title": "Campo Maior, Portugal", "link": "https://wikipedia.org/wiki/Campo_Maior,_Portugal"}]}, {"year": "1776", "text": "American Revolutionary War: The Battle of Harlem Heights is fought.", "html": "1776 - <a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Harlem_Heights\" title=\"Battle of Harlem Heights\">Battle of Harlem Heights</a> is fought.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Revolutionary_War\" title=\"American Revolutionary War\">American Revolutionary War</a>: The <a href=\"https://wikipedia.org/wiki/Battle_of_Harlem_Heights\" title=\"Battle of Harlem Heights\">Battle of Harlem Heights</a> is fought.", "links": [{"title": "American Revolutionary War", "link": "https://wikipedia.org/wiki/American_Revolutionary_War"}, {"title": "Battle of Harlem Heights", "link": "https://wikipedia.org/wiki/Battle_of_Harlem_Heights"}]}, {"year": "1779", "text": "American Revolutionary War: The Franco-American Siege of Savannah begins.", "html": "1779 - American Revolutionary War: The Franco-American <a href=\"https://wikipedia.org/wiki/Siege_of_Savannah\" title=\"Siege of Savannah\">Siege of Savannah</a> begins.", "no_year_html": "American Revolutionary War: The Franco-American <a href=\"https://wikipedia.org/wiki/Siege_of_Savannah\" title=\"Siege of Savannah\">Siege of Savannah</a> begins.", "links": [{"title": "Siege of Savannah", "link": "https://wikipedia.org/wiki/Siege_of_Savannah"}]}, {"year": "1810", "text": "With the Grito de Dolores, Father <PERSON> begins Mexico's fight for independence from Spain.", "html": "1810 - With the <i><a href=\"https://wikipedia.org/wiki/Grito_de_Dolores\" class=\"mw-redirect\" title=\"Grito de Dolores\">Grito de Dolores</a>,</i> Father <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> begins <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexico's fight for independence from Spain</a>.", "no_year_html": "With the <i><a href=\"https://wikipedia.org/wiki/Grito_de_Dolores\" class=\"mw-redirect\" title=\"Grito de Dolores\">Grito de Dolores</a>,</i> Father <a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a> begins <a href=\"https://wikipedia.org/wiki/Mexican_War_of_Independence\" title=\"Mexican War of Independence\">Mexico's fight for independence from Spain</a>.", "links": [{"title": "Grito de Dolores", "link": "https://wikipedia.org/wiki/Grito_de_Dolores"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Mexican War of Independence", "link": "https://wikipedia.org/wiki/Mexican_War_of_Independence"}]}, {"year": "1822", "text": "French physicist <PERSON><PERSON><PERSON><PERSON>, in a \"note\" read to the Academy of Sciences, reports a direct refraction experiment verifying <PERSON>'s hypothesis that photoelasticity (as it is now known) is stress-induced birefringence.", "html": "1822 - French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, in a \"note\" read to the <a href=\"https://wikipedia.org/wiki/French_Academy_of_Sciences\" title=\"French Academy of Sciences\">Academy of Sciences</a>, reports a direct refraction experiment verifying <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s hypothesis that <a href=\"https://wikipedia.org/wiki/Photoelasticity\" title=\"Photoelasticity\">photoelasticity</a> (as it is now known) is stress-induced <a href=\"https://wikipedia.org/wiki/Birefringence\" title=\"Birefringence\">birefringence</a>.", "no_year_html": "French physicist <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\">August<PERSON>-<PERSON></a>, in a \"note\" read to the <a href=\"https://wikipedia.org/wiki/French_Academy_of_Sciences\" title=\"French Academy of Sciences\">Academy of Sciences</a>, reports a direct refraction experiment verifying <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>'s hypothesis that <a href=\"https://wikipedia.org/wiki/Photoelasticity\" title=\"Photoelasticity\">photoelasticity</a> (as it is now known) is stress-induced <a href=\"https://wikipedia.org/wiki/Birefringence\" title=\"Birefringence\">birefringence</a>.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>-<PERSON>_<PERSON>"}, {"title": "French Academy of Sciences", "link": "https://wikipedia.org/wiki/French_Academy_of_Sciences"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Photoelasticity", "link": "https://wikipedia.org/wiki/Photoelasticity"}, {"title": "Birefringence", "link": "https://wikipedia.org/wiki/Birefringence"}]}, {"year": "1863", "text": "Robert College, in Istanbul, the first American educational institution outside the United States, is founded by <PERSON>, an American philanthropist.", "html": "1863 - <a href=\"https://wikipedia.org/wiki/Robert_<PERSON>\" title=\"Robert College\">Robert College</a>, in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, the first American educational institution outside the United States, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an American philanthropist.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Robert_College\" title=\"Robert College\">Robert College</a>, in <a href=\"https://wikipedia.org/wiki/Istanbul\" title=\"Istanbul\">Istanbul</a>, the first American educational institution outside the United States, is founded by <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, an American philanthropist.", "links": [{"title": "Robert College", "link": "https://wikipedia.org/wiki/Robert_College"}, {"title": "Istanbul", "link": "https://wikipedia.org/wiki/Istanbul"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "The Cornell Daily Sun prints its first issue in Ithaca, New York.", "html": "1880 - <i><a href=\"https://wikipedia.org/wiki/The_Cornell_Daily_Sun\" title=\"The Cornell Daily Sun\">The Cornell Daily Sun</a></i> prints its first issue in <a href=\"https://wikipedia.org/wiki/Ithaca,_New_York\" title=\"Ithaca, New York\">Ithaca, New York</a>.", "no_year_html": "<i><a href=\"https://wikipedia.org/wiki/The_Cornell_Daily_Sun\" title=\"The Cornell Daily Sun\">The Cornell Daily Sun</a></i> prints its first issue in <a href=\"https://wikipedia.org/wiki/Itha<PERSON>,_New_York\" title=\"Ithaca, New York\">Ithaca, New York</a>.", "links": [{"title": "The Cornell Daily Sun", "link": "https://wikipedia.org/wiki/The_Cornell_Daily_Sun"}, {"title": "Ithaca, New York", "link": "https://wikipedia.org/wiki/Ithaca,_New_York"}]}, {"year": "1893", "text": "Settlers make a land run for prime land in the Cherokee Strip in Oklahoma.", "html": "1893 - Settlers make a <a href=\"https://wikipedia.org/wiki/Land_Run_of_1893\" title=\"Land Run of 1893\">land run</a> for prime land in the <a href=\"https://wikipedia.org/wiki/Cherokee_Outlet\" title=\"Cherokee Outlet\">Cherokee Strip</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>.", "no_year_html": "Settlers make a <a href=\"https://wikipedia.org/wiki/Land_Run_of_1893\" title=\"Land Run of 1893\">land run</a> for prime land in the <a href=\"https://wikipedia.org/wiki/Cherokee_Outlet\" title=\"Cherokee Outlet\">Cherokee Strip</a> in <a href=\"https://wikipedia.org/wiki/Oklahoma\" title=\"Oklahoma\">Oklahoma</a>.", "links": [{"title": "Land Run of 1893", "link": "https://wikipedia.org/wiki/Land_Run_of_1893"}, {"title": "Cherokee Outlet", "link": "https://wikipedia.org/wiki/Cherokee_Outlet"}, {"title": "Oklahoma", "link": "https://wikipedia.org/wiki/Oklahoma"}]}, {"year": "1908", "text": "The General Motors Corporation is founded.", "html": "1908 - The <a href=\"https://wikipedia.org/wiki/General_Motors_Corporation\" class=\"mw-redirect\" title=\"General Motors Corporation\">General Motors Corporation</a> is founded.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/General_Motors_Corporation\" class=\"mw-redirect\" title=\"General Motors Corporation\">General Motors Corporation</a> is founded.", "links": [{"title": "General Motors Corporation", "link": "https://wikipedia.org/wiki/General_Motors_Corporation"}]}, {"year": "1914", "text": "World War I: The Siege of Przemyśl (present-day Poland) begins.", "html": "1914 - <a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Przemy%C5%9Bl\" title=\"Siege of Przemyśl\">Siege of Przemyśl</a> (present-day Poland) begins.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_I\" title=\"World War I\">World War I</a>: The <a href=\"https://wikipedia.org/wiki/Siege_of_Przemy%C5%9Bl\" title=\"Siege of Przemyśl\">Siege of Przemyśl</a> (present-day Poland) begins.", "links": [{"title": "World War I", "link": "https://wikipedia.org/wiki/World_War_I"}, {"title": "Siege of Przemyśl", "link": "https://wikipedia.org/wiki/Siege_of_Przemy%C5%9Bl"}]}, {"year": "1920", "text": "The Wall Street bombing: A bomb in a horse wagon explodes in front of the J. P. Morgan building in New York City killing 38 and injuring 400.", "html": "1920 - The <a href=\"https://wikipedia.org/wiki/Wall_Street_bombing\" title=\"Wall Street bombing\">Wall Street bombing</a>: A bomb in a horse wagon explodes in front of the J. P. Morgan building in New York City killing 38 and injuring 400.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Wall_Street_bombing\" title=\"Wall Street bombing\">Wall Street bombing</a>: A bomb in a horse wagon explodes in front of the J. P. Morgan building in New York City killing 38 and injuring 400.", "links": [{"title": "Wall Street bombing", "link": "https://wikipedia.org/wiki/Wall_Street_bombing"}]}, {"year": "1940", "text": "World War II: Italian troops conquer Sidi Barrani.", "html": "1940 - <a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Italian troops <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Egypt\" title=\"Italian invasion of Egypt\">conquer <PERSON><PERSON></a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/World_War_II\" title=\"World War II\">World War II</a>: Italian troops <a href=\"https://wikipedia.org/wiki/Italian_invasion_of_Egypt\" title=\"Italian invasion of Egypt\">conquer <PERSON><PERSON></a>.", "links": [{"title": "World War II", "link": "https://wikipedia.org/wiki/World_War_II"}, {"title": "Italian invasion of Egypt", "link": "https://wikipedia.org/wiki/Italian_invasion_of_Egypt"}]}, {"year": "1943", "text": "World War II: The German Tenth Army reports that it can no longer contain the Allied bridgehead around Salerno.", "html": "1943 - World War II: The <a href=\"https://wikipedia.org/wiki/10th_Army_(Wehrmacht)\" title=\"10th Army (Wehrmacht)\">German Tenth Army</a> reports that it can no longer contain the <a href=\"https://wikipedia.org/wiki/Operation_Avalanche\" title=\"Operation Avalanche\">Allied bridgehead around Salerno</a>.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/10th_Army_(Wehrmacht)\" title=\"10th Army (Wehrmacht)\">German Tenth Army</a> reports that it can no longer contain the <a href=\"https://wikipedia.org/wiki/Operation_Avalanche\" title=\"Operation Avalanche\">Allied bridgehead around Salerno</a>.", "links": [{"title": "10th Army (Wehrmacht)", "link": "https://wikipedia.org/wiki/10th_Army_(Wehrmacht)"}, {"title": "Operation Avalanche", "link": "https://wikipedia.org/wiki/Operation_Avalanche"}]}, {"year": "1945", "text": "World War II: The Japanese occupation of Hong Kong comes to an end.", "html": "1945 - World War II: The <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong\" title=\"Japanese occupation of Hong Kong\">Japanese occupation of Hong Kong</a> comes to an end.", "no_year_html": "World War II: The <a href=\"https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong\" title=\"Japanese occupation of Hong Kong\">Japanese occupation of Hong Kong</a> comes to an end.", "links": [{"title": "Japanese occupation of Hong Kong", "link": "https://wikipedia.org/wiki/Japanese_occupation_of_Hong_Kong"}]}, {"year": "1953", "text": "American Airlines Flight 723 crashes in Colonie, New York, killing 28 people.", "html": "1953 - <a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_723\" title=\"American Airlines Flight 723\">American Airlines Flight 723</a> crashes in <a href=\"https://wikipedia.org/wiki/Colonie,_New_York\" title=\"Colonie, New York\">Colonie, New York</a>, killing 28 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/American_Airlines_Flight_723\" title=\"American Airlines Flight 723\">American Airlines Flight 723</a> crashes in <a href=\"https://wikipedia.org/wiki/Colonie,_New_York\" title=\"Colonie, New York\">Colonie, New York</a>, killing 28 people.", "links": [{"title": "American Airlines Flight 723", "link": "https://wikipedia.org/wiki/American_Airlines_Flight_723"}, {"title": "Colonie, New York", "link": "https://wikipedia.org/wiki/Col<PERSON>e,_New_York"}]}, {"year": "1955", "text": "The military coup to unseat President <PERSON> of Argentina is launched at midnight.", "html": "1955 - The military coup to unseat President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> of Argentina is launched at midnight.", "no_year_html": "The military coup to unseat President <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B3n\" title=\"<PERSON>\"><PERSON></a> of Argentina is launched at midnight.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Juan_Per%C3%B3n"}]}, {"year": "1955", "text": "A Soviet Zulu-class submarine becomes the first to launch a ballistic missile.", "html": "1955 - A Soviet <a href=\"https://wikipedia.org/wiki/Zulu-class_submarine\" title=\"Zulu-class submarine\">Zulu-class submarine</a> becomes the first to launch a <a href=\"https://wikipedia.org/wiki/R-11_Zemlya\" title=\"R-11 Zemlya\">ballistic missile</a>.", "no_year_html": "A Soviet <a href=\"https://wikipedia.org/wiki/Zulu-class_submarine\" title=\"Zulu-class submarine\">Zulu-class submarine</a> becomes the first to launch a <a href=\"https://wikipedia.org/wiki/R-11_Zemlya\" title=\"R-11 Zemlya\">ballistic missile</a>.", "links": [{"title": "Zulu-class submarine", "link": "https://wikipedia.org/wiki/Zulu-class_submarine"}, {"title": "R-11 Zemlya", "link": "https://wikipedia.org/wiki/R-11_Zemlya"}]}, {"year": "1956", "text": "TCN-9 Sydney is the first Australian television station to commence regular broadcasts.", "html": "1956 - <a href=\"https://wikipedia.org/wiki/TCN\" title=\"TCN\">TCN-9 Sydney</a> is the first Australian television station to commence regular broadcasts.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/TCN\" title=\"TCN\">TCN-9 Sydney</a> is the first Australian television station to commence regular broadcasts.", "links": [{"title": "TCN", "link": "https://wikipedia.org/wiki/TCN"}]}, {"year": "1959", "text": "The first successful photocopier, the Xerox 914, is introduced in a demonstration on live television from New York City.", "html": "1959 - The first successful photocopier, the <a href=\"https://wikipedia.org/wiki/Xerox_914\" title=\"Xerox 914\">Xerox 914</a>, is introduced in a demonstration on live television from New York City.", "no_year_html": "The first successful photocopier, the <a href=\"https://wikipedia.org/wiki/Xerox_914\" title=\"Xerox 914\">Xerox 914</a>, is introduced in a demonstration on live television from New York City.", "links": [{"title": "Xerox 914", "link": "https://wikipedia.org/wiki/Xerox_914"}]}, {"year": "1961", "text": "The United States National Hurricane Research Project drops eight cylinders of silver iodide into the eyewall of Hurricane Esther. Wind speed reduces by 10%, giving rise to Project Stormfury.", "html": "1961 - The United States National Hurricane Research Project drops eight cylinders of silver iodide into the eyewall of <a href=\"https://wikipedia.org/wiki/Hurricane_Esther\" title=\"Hurricane Esther\">Hurricane <PERSON></a>. Wind speed reduces by 10%, giving rise to <a href=\"https://wikipedia.org/wiki/Project_Stormfury\" title=\"Project Stormfury\">Project Stormfury</a>.", "no_year_html": "The United States National Hurricane Research Project drops eight cylinders of silver iodide into the eyewall of <a href=\"https://wikipedia.org/wiki/Hurricane_Esther\" title=\"Hurricane Esther\">Hurricane <PERSON></a>. Wind speed reduces by 10%, giving rise to <a href=\"https://wikipedia.org/wiki/Project_Stormfury\" title=\"Project Stormfury\">Project Stormfury</a>.", "links": [{"title": "Hurricane <PERSON>", "link": "https://wikipedia.org/wiki/Hurricane_Esther"}, {"title": "Project Stormfury", "link": "https://wikipedia.org/wiki/Project_Stormfury"}]}, {"year": "1961", "text": "Typhoon <PERSON>, with possibly the strongest winds ever measured in a tropical cyclone, makes landfall in Osaka, Japan, killing 173 people.", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Typhoon_Nancy_(1961)\" title=\"Typhoon Nancy (1961)\">Typhoon <PERSON></a>, with possibly the strongest winds ever measured in a tropical cyclone, makes landfall in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan, killing 173 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Typhoon_Nancy_(1961)\" title=\"Typhoon Nancy (1961)\">Typhoon <PERSON></a>, with possibly the strongest winds ever measured in a tropical cyclone, makes landfall in <a href=\"https://wikipedia.org/wiki/Osaka\" title=\"Osaka\">Osaka</a>, Japan, killing 173 people.", "links": [{"title": "Typhoon Nancy (1961)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1961)"}, {"title": "Osaka", "link": "https://wikipedia.org/wiki/Osaka"}]}, {"year": "1961", "text": "Pakistan establishes its Space and Upper Atmosphere Research Commission with <PERSON><PERSON> as its head.", "html": "1961 - Pakistan establishes its <a href=\"https://wikipedia.org/wiki/Space_and_Upper_Atmosphere_Research_Commission\" class=\"mw-redirect\" title=\"Space and Upper Atmosphere Research Commission\">Space and Upper Atmosphere Research Commission</a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as its head.", "no_year_html": "Pakistan establishes its <a href=\"https://wikipedia.org/wiki/Space_and_Upper_Atmosphere_Research_Commission\" class=\"mw-redirect\" title=\"Space and Upper Atmosphere Research Commission\">Space and Upper Atmosphere Research Commission</a> with <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a> as its head.", "links": [{"title": "Space and Upper Atmosphere Research Commission", "link": "https://wikipedia.org/wiki/Space_and_Upper_Atmosphere_Research_Commission"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1963", "text": "Malaysia is formed from the Federation of Malaya, Singapore, North Borneo (Sabah) and Sarawak. However, Singapore is soon expelled from this new country.", "html": "1963 - <a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> is formed from the <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Federation of Malaya</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Singapore\" class=\"mw-redirect\" title=\"Crown Colony of Singapore\">Singapore</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_North_Borneo\" title=\"Crown Colony of North Borneo\">North Borneo</a> (<a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>) and <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Sarawak\" title=\"Crown Colony of Sarawak\">Sarawak</a>. However, Singapore is soon expelled from this new country.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Malaysia\" title=\"Malaysia\">Malaysia</a> is formed from the <a href=\"https://wikipedia.org/wiki/Federation_of_Malaya\" title=\"Federation of Malaya\">Federation of Malaya</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Singapore\" class=\"mw-redirect\" title=\"Crown Colony of Singapore\">Singapore</a>, <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_North_Borneo\" title=\"Crown Colony of North Borneo\">North Borneo</a> (<a href=\"https://wikipedia.org/wiki/Sabah\" title=\"Sabah\">Sabah</a>) and <a href=\"https://wikipedia.org/wiki/Crown_Colony_of_Sarawak\" title=\"Crown Colony of Sarawak\">Sarawak</a>. However, Singapore is soon expelled from this new country.", "links": [{"title": "Malaysia", "link": "https://wikipedia.org/wiki/Malaysia"}, {"title": "Federation of Malaya", "link": "https://wikipedia.org/wiki/Federation_of_Malaya"}, {"title": "Crown Colony of Singapore", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Singapore"}, {"title": "Crown Colony of North Borneo", "link": "https://wikipedia.org/wiki/Crown_Colony_of_North_Borneo"}, {"title": "Sabah", "link": "https://wikipedia.org/wiki/Sabah"}, {"title": "Crown Colony of Sarawak", "link": "https://wikipedia.org/wiki/Crown_Colony_of_Sarawak"}]}, {"year": "1966", "text": "The Metropolitan Opera House opens at Lincoln Center in New York City with the world premiere of <PERSON>'s opera <PERSON> and Cleopatra.", "html": "1966 - The <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera_House_(Lincoln_Center)\" title=\"Metropolitan Opera House (Lincoln Center)\">Metropolitan Opera House</a> opens at Lincoln Center in New York City with the world premiere of <PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_(1966_opera)\" class=\"mw-redirect\" title=\"<PERSON> and Cleopatra (1966 opera)\"><PERSON> and <PERSON></a></i>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Metropolitan_Opera_House_(Lincoln_Center)\" title=\"Metropolitan Opera House (Lincoln Center)\">Metropolitan Opera House</a> opens at Lincoln Center in New York City with the world premiere of <PERSON>'s opera <i><a href=\"https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_(1966_opera)\" class=\"mw-redirect\" title=\"<PERSON> and Cleopatra (1966 opera)\"><PERSON> and <PERSON></a></i>.", "links": [{"title": "Metropolitan Opera House (Lincoln Center)", "link": "https://wikipedia.org/wiki/Metropolitan_Opera_House_(Lincoln_Center)"}, {"title": "<PERSON> and <PERSON> (1966 opera)", "link": "https://wikipedia.org/wiki/<PERSON>_and_<PERSON>_(1966_opera)"}]}, {"year": "1970", "text": "King <PERSON> of Jordan declares war against the Palestine Liberation Organization, the conflict came to be known as Black September.", "html": "1970 - <a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> of Jordan\">King <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> declares war against the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>, the conflict came to be known as <a href=\"https://wikipedia.org/wiki/Black_September\" title=\"Black September\">Black September</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hussein_of_Jordan\" title=\"<PERSON> Jordan\">King <PERSON></a> of <a href=\"https://wikipedia.org/wiki/Jordan\" title=\"Jordan\">Jordan</a> declares war against the <a href=\"https://wikipedia.org/wiki/Palestine_Liberation_Organization\" title=\"Palestine Liberation Organization\">Palestine Liberation Organization</a>, the conflict came to be known as <a href=\"https://wikipedia.org/wiki/Black_September\" title=\"Black September\">Black September</a>.", "links": [{"title": "<PERSON> of Jordan", "link": "https://wikipedia.org/wiki/Hussein_of_Jordan"}, {"title": "Jordan", "link": "https://wikipedia.org/wiki/Jordan"}, {"title": "Palestine Liberation Organization", "link": "https://wikipedia.org/wiki/Palestine_Liberation_Organization"}, {"title": "Black September", "link": "https://wikipedia.org/wiki/Black_September"}]}, {"year": "1975", "text": "Papua New Guinea gains independence from Australia.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a> gains independence from Australia.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Papua_New_Guinea\" title=\"Papua New Guinea\">Papua New Guinea</a> gains independence from Australia.", "links": [{"title": "Papua New Guinea", "link": "https://wikipedia.org/wiki/Papua_New_Guinea"}]}, {"year": "1975", "text": "Cape Verde, Mozambique, and São Tomé and Príncipe join the United Nations.", "html": "1975 - <a href=\"https://wikipedia.org/wiki/Cape_Verde\" title=\"Cape Verde\">Cape Verde</a>, <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, and <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" title=\"São Tomé and Príncipe\">São Tomé and Prín<PERSON><PERSON></a> join the United Nations.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Cape_Verde\" title=\"Cape Verde\">Cape Verde</a>, <a href=\"https://wikipedia.org/wiki/Mozambique\" title=\"Mozambique\">Mozambique</a>, and <a href=\"https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe\" title=\"São Tomé and Príncipe\"><PERSON> and <PERSON>r<PERSON><PERSON><PERSON></a> join the United Nations.", "links": [{"title": "Cape Verde", "link": "https://wikipedia.org/wiki/Cape_Verde"}, {"title": "Mozambique", "link": "https://wikipedia.org/wiki/Mozambique"}, {"title": "São Tomé and Príncipe", "link": "https://wikipedia.org/wiki/S%C3%A3o_Tom%C3%A9_and_Pr%C3%ADncipe"}]}, {"year": "1975", "text": "The first prototype of the Mikoyan MiG-31 interceptor makes its maiden flight.", "html": "1975 - The first prototype of the <a href=\"https://wikipedia.org/wiki/Mikoyan_MiG-31\" title=\"Mikoyan MiG-31\">Mikoyan MiG-31</a> interceptor makes its maiden flight.", "no_year_html": "The first prototype of the <a href=\"https://wikipedia.org/wiki/Mikoyan_MiG-31\" title=\"Mikoyan MiG-31\">Mikoyan MiG-31</a> interceptor makes its maiden flight.", "links": [{"title": "Mikoyan MiG-31", "link": "https://wikipedia.org/wiki/Mikoyan_MiG-31"}]}, {"year": "1976", "text": "Armenian champion swimmer <PERSON><PERSON><PERSON><PERSON> saves 20 people from a trolleybus that had fallen into a Yerevan reservoir.", "html": "1976 - Armenian champion swimmer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> saves 20 people from a trolleybus that had fallen into a <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a> reservoir.", "no_year_html": "Armenian champion swimmer <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> saves 20 people from a trolleybus that had fallen into a <a href=\"https://wikipedia.org/wiki/Yerevan\" title=\"Yerevan\">Yerevan</a> reservoir.", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "Yerevan", "link": "https://wikipedia.org/wiki/Yerevan"}]}, {"year": "1978", "text": "The 7.4 Mw  Tabas earthquake affects the city of Tabas, Iran with a maximum Mercalli intensity of IX (Violent). At least 15,000 people are killed.", "html": "1978 - The 7.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1978_Tabas_earthquake\" title=\"1978 Tabas earthquake\">Tabas earthquake</a> affects the city of <a href=\"https://wikipedia.org/wiki/Tabas\" title=\"Tabas\">Tabas</a>, Iran with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). At least 15,000 people are killed.", "no_year_html": "The 7.4 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/1978_Tabas_earthquake\" title=\"1978 Tabas earthquake\">Tabas earthquake</a> affects the city of <a href=\"https://wikipedia.org/wiki/Tabas\" title=\"Tabas\">Tabas</a>, Iran with a maximum <a href=\"https://wikipedia.org/wiki/Mercalli_intensity_scale\" class=\"mw-redirect\" title=\"Mercalli intensity scale\">Mercalli intensity</a> of IX (<i>Violent</i>). At least 15,000 people are killed.", "links": [{"title": "1978 Tabas earthquake", "link": "https://wikipedia.org/wiki/1978_Tabas_earthquake"}, {"title": "Tabas", "link": "https://wikipedia.org/wiki/Tabas"}, {"title": "Mercalli intensity scale", "link": "https://wikipedia.org/wiki/Mercalli_intensity_scale"}]}, {"year": "1979", "text": "Eight people escape from East Germany to the west in a homemade hot air balloon.", "html": "1979 - Eight people <a href=\"https://wikipedia.org/wiki/East_German_balloon_escape\" class=\"mw-redirect\" title=\"East German balloon escape\">escape from East Germany</a> to the west in a homemade hot air balloon.", "no_year_html": "Eight people <a href=\"https://wikipedia.org/wiki/East_German_balloon_escape\" class=\"mw-redirect\" title=\"East German balloon escape\">escape from East Germany</a> to the west in a homemade hot air balloon.", "links": [{"title": "East German balloon escape", "link": "https://wikipedia.org/wiki/East_German_balloon_escape"}]}, {"year": "1982", "text": "Lebanon War: The Sabra and Shatila massacre in Lebanon takes place.", "html": "1982 - <a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a>: The <a href=\"https://wikipedia.org/wiki/Sabra_and_Shatila_massacre\" title=\"Sabra and Shatila massacre\">Sabra and Shatila massacre</a> in Lebanon takes place.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1982_Lebanon_War\" title=\"1982 Lebanon War\">Lebanon War</a>: The <a href=\"https://wikipedia.org/wiki/Sabra_and_Shatila_massacre\" title=\"Sabra and Shatila massacre\">Sabra and Shatila massacre</a> in Lebanon takes place.", "links": [{"title": "1982 Lebanon War", "link": "https://wikipedia.org/wiki/1982_Lebanon_War"}, {"title": "Sabra and Shatila massacre", "link": "https://wikipedia.org/wiki/Sabra_and_Shatila_massacre"}]}, {"year": "1987", "text": "The Montreal Protocol is signed to protect the ozone layer from depletion.", "html": "1987 - The <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> is signed to protect the ozone layer from <a href=\"https://wikipedia.org/wiki/Ozone_depletion\" title=\"Ozone depletion\">depletion</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Montreal_Protocol\" title=\"Montreal Protocol\">Montreal Protocol</a> is signed to protect the ozone layer from <a href=\"https://wikipedia.org/wiki/Ozone_depletion\" title=\"Ozone depletion\">depletion</a>.", "links": [{"title": "Montreal Protocol", "link": "https://wikipedia.org/wiki/Montreal_Protocol"}, {"title": "Ozone depletion", "link": "https://wikipedia.org/wiki/Ozone_depletion"}]}, {"year": "1990", "text": "The railroad between the People's Republic of China and Kazakhstan is completed at Dostyk, adding a sizable link to the concept of the Eurasian Land Bridge.", "html": "1990 - The railroad between the People's Republic of China and Kazakhstan is completed at <a href=\"https://wikipedia.org/wiki/Dostyk\" title=\"Dostyk\">Dostyk</a>, adding a sizable link to the concept of the <a href=\"https://wikipedia.org/wiki/Eurasian_Land_Bridge\" title=\"Eurasian Land Bridge\">Eurasian Land Bridge</a>.", "no_year_html": "The railroad between the People's Republic of China and Kazakhstan is completed at <a href=\"https://wikipedia.org/wiki/Dostyk\" title=\"Dostyk\">Dostyk</a>, adding a sizable link to the concept of the <a href=\"https://wikipedia.org/wiki/Eurasian_Land_Bridge\" title=\"Eurasian Land Bridge\">Eurasian Land Bridge</a>.", "links": [{"title": "Dostyk", "link": "https://wikipedia.org/wiki/Dostyk"}, {"title": "Eurasian Land Bridge", "link": "https://wikipedia.org/wiki/Eurasian_Land_Bridge"}]}, {"year": "1992", "text": "The trial of the deposed Panamanian dictator <PERSON> ends in the United States with a 40-year sentence for drug trafficking and money laundering.", "html": "1992 - The trial of the deposed Panamanian dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> ends in the United States with a 40-year sentence for drug trafficking and money laundering.", "no_year_html": "The trial of the deposed Panamanian dictator <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a> ends in the United States with a 40-year sentence for drug trafficking and money laundering.", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "Black Wednesday: The British pound is forced out of the European Exchange Rate Mechanism by currency speculators and is forced to devalue against the German mark.", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Black_Wednesday\" title=\"Black Wednesday\">Black Wednesday</a>: The British pound is forced out of the <a href=\"https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism\" title=\"European Exchange Rate Mechanism\">European Exchange Rate Mechanism</a> by currency speculators and is forced to devalue against the German mark.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Black_Wednesday\" title=\"Black Wednesday\">Black Wednesday</a>: The British pound is forced out of the <a href=\"https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism\" title=\"European Exchange Rate Mechanism\">European Exchange Rate Mechanism</a> by currency speculators and is forced to devalue against the German mark.", "links": [{"title": "Black Wednesday", "link": "https://wikipedia.org/wiki/Black_Wednesday"}, {"title": "European Exchange Rate Mechanism", "link": "https://wikipedia.org/wiki/European_Exchange_Rate_Mechanism"}]}, {"year": "1994", "text": "The British government lifts the broadcasting ban imposed against members of Sinn Féin and Irish paramilitary groups in 1988.", "html": "1994 - The British government lifts the <a href=\"https://wikipedia.org/wiki/1988%E2%80%931994_British_broadcasting_voice_restrictions\" title=\"1988-1994 British broadcasting voice restrictions\">broadcasting ban</a> imposed against members of Sinn Féin and Irish paramilitary groups in 1988.", "no_year_html": "The British government lifts the <a href=\"https://wikipedia.org/wiki/1988%E2%80%931994_British_broadcasting_voice_restrictions\" title=\"1988-1994 British broadcasting voice restrictions\">broadcasting ban</a> imposed against members of Sinn Féin and Irish paramilitary groups in 1988.", "links": [{"title": "1988-1994 British broadcasting voice restrictions", "link": "https://wikipedia.org/wiki/1988%E2%80%931994_British_broadcasting_voice_restrictions"}]}, {"year": "1996", "text": "Space Shuttle Atlantis is launched on STS-79 to dock to the Russian space station Mir.", "html": "1996 - <a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-79\" title=\"STS-79\">STS-79</a> to dock to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Space_Shuttle_Atlantis\" title=\"Space Shuttle Atlantis\">Space Shuttle <i>Atlantis</i></a> is launched on <a href=\"https://wikipedia.org/wiki/STS-79\" title=\"STS-79\">STS-79</a> to dock to the Russian space station <i><a href=\"https://wikipedia.org/wiki/Mir\" title=\"Mir\">Mir</a></i>.", "links": [{"title": "Space Shuttle Atlantis", "link": "https://wikipedia.org/wiki/Space_Shuttle_Atlantis"}, {"title": "STS-79", "link": "https://wikipedia.org/wiki/STS-79"}, {"title": "Mir", "link": "https://wikipedia.org/wiki/Mir"}]}, {"year": "2004", "text": "Hurricane <PERSON> makes landfall in Gulf Shores, Alabama as a Category 3 hurricane.", "html": "2004 - <a href=\"https://wikipedia.org/wiki/Hurricane_Ivan\" title=\"Hurricane Ivan\">Hurricane <PERSON></a> makes landfall in Gulf Shores, Alabama as a <a href=\"https://wikipedia.org/wiki/Category_3_hurricane\" class=\"mw-redirect\" title=\"Category 3 hurricane\">Category 3 hurricane</a>.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Hurricane_Ivan\" title=\"Hurricane Ivan\">Hurricane <PERSON></a> makes landfall in Gulf Shores, Alabama as a <a href=\"https://wikipedia.org/wiki/Category_3_hurricane\" class=\"mw-redirect\" title=\"Category 3 hurricane\">Category 3 hurricane</a>.", "links": [{"title": "Hurricane Ivan", "link": "https://wikipedia.org/wiki/Hurricane_Ivan"}, {"title": "Category 3 hurricane", "link": "https://wikipedia.org/wiki/Category_3_hurricane"}]}, {"year": "2005", "text": "The Camorra organized crime boss <PERSON> is arrested in Naples, Italy.", "html": "2005 - The <a href=\"https://wikipedia.org/wiki/Camorra\" title=\"Camorra\">Camorra</a> organized crime boss <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in Naples, Italy.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Camorra\" title=\"Camorra\">Camorra</a> organized crime boss <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a> is arrested in Naples, Italy.", "links": [{"title": "Camorra", "link": "https://wikipedia.org/wiki/Camorra"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2007", "text": "One-Two-Go Airlines Flight 269 carrying 130 crew and passengers crashes in Thailand, killing 90 people.", "html": "2007 - <a href=\"https://wikipedia.org/wiki/One-Two-Go_Airlines_Flight_269\" title=\"One-Two-Go Airlines Flight 269\">One-Two-Go Airlines Flight 269</a> carrying 130 crew and passengers crashes in Thailand, killing 90 people.", "no_year_html": "<a href=\"https://wikipedia.org/wiki/One-Two-Go_Airlines_Flight_269\" title=\"One-Two-Go Airlines Flight 269\">One-Two-Go Airlines Flight 269</a> carrying 130 crew and passengers crashes in Thailand, killing 90 people.", "links": [{"title": "One-Two-Go Airlines Flight 269", "link": "https://wikipedia.org/wiki/One-Two-Go_Airlines_Flight_269"}]}, {"year": "2007", "text": "Security guards working for Blackwater Worldwide shoot and kill 17 Iraqis in Nisour Square, Baghdad.", "html": "2007 - Security guards working for Blackwater Worldwide <a href=\"https://wikipedia.org/wiki/Nisour_Square_massacre\" title=\"Nisour Square massacre\">shoot and kill</a> 17 Iraqis in Nisour Square, Baghdad.", "no_year_html": "Security guards working for Blackwater Worldwide <a href=\"https://wikipedia.org/wiki/Nisour_Square_massacre\" title=\"Nisour Square massacre\">shoot and kill</a> 17 Iraqis in Nisour Square, Baghdad.", "links": [{"title": "Nisour Square massacre", "link": "https://wikipedia.org/wiki/Nisour_Square_massacre"}]}, {"year": "2013", "text": "A gunman kills twelve people at the Washington Navy Yard in Washington, D.C.", "html": "2013 - A gunman <a href=\"https://wikipedia.org/wiki/Washington_Navy_Yard_shooting\" title=\"Washington Navy Yard shooting\">kills twelve people</a> at the Washington Navy Yard in Washington, D.C.", "no_year_html": "A gunman <a href=\"https://wikipedia.org/wiki/Washington_Navy_Yard_shooting\" title=\"Washington Navy Yard shooting\">kills twelve people</a> at the Washington Navy Yard in Washington, D.C.", "links": [{"title": "Washington Navy Yard shooting", "link": "https://wikipedia.org/wiki/Washington_Navy_Yard_shooting"}]}, {"year": "2014", "text": "The Islamic State of Iraq and the Levant launches its Kobani offensive against Syrian-Kurdish forces.", "html": "2014 - The Islamic State of Iraq and the Levant launches its <a href=\"https://wikipedia.org/wiki/Siege_of_Koban%C3%AE\" title=\"Siege of Kobanî\">Kobani offensive</a> against Syrian-Kurdish forces.", "no_year_html": "The Islamic State of Iraq and the Levant launches its <a href=\"https://wikipedia.org/wiki/Siege_of_Koban%C3%AE\" title=\"Siege of Kobanî\">Kobani offensive</a> against Syrian-Kurdish forces.", "links": [{"title": "Siege of Kobanî", "link": "https://wikipedia.org/wiki/Siege_of_Koban%C3%AE"}]}, {"year": "2015", "text": "A 8.3 Mw  earthquake strikes the Chilean city of Illapel, killing 15 people, injuring at least 34, leaving at least six missing, and causing extensive damage. One person also dies in Argentina.", "html": "2015 - A 8.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2015_Illapel_earthquake\" title=\"2015 Illapel earthquake\">earthquake</a> strikes the Chilean city of <a href=\"https://wikipedia.org/wiki/Illapel\" title=\"Illap<PERSON>\"><PERSON><PERSON><PERSON></a>, killing 15 people, injuring at least 34, leaving at least six missing, and causing extensive damage. One person also dies in Argentina.", "no_year_html": "A 8.3 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2015_Illapel_earthquake\" title=\"2015 Illapel earthquake\">earthquake</a> strikes the Chilean city of <a href=\"https://wikipedia.org/wiki/Illapel\" title=\"Illap<PERSON>\"><PERSON><PERSON><PERSON></a>, killing 15 people, injuring at least 34, leaving at least six missing, and causing extensive damage. One person also dies in Argentina.", "links": [{"title": "2015 Illapel earthquake", "link": "https://wikipedia.org/wiki/2015_Illapel_earthquake"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Illapel"}]}, {"year": "2019", "text": "Five months before the COVID-19 stock market crash, an overnight spike in lending rates in the United States prompts the Federal Reserve to conduct operations in the repo market.", "html": "2019 - Five months before the <a href=\"https://wikipedia.org/wiki/2020_stock_market_crash\" title=\"2020 stock market crash\">COVID-19 stock market crash</a>, an overnight spike in lending rates in the United States prompts the <a href=\"https://wikipedia.org/wiki/Federal_Reserve\" title=\"Federal Reserve\">Federal Reserve</a> to conduct operations in the <a href=\"https://wikipedia.org/wiki/Repo_market\" class=\"mw-redirect\" title=\"Repo market\">repo market</a>.", "no_year_html": "Five months before the <a href=\"https://wikipedia.org/wiki/2020_stock_market_crash\" title=\"2020 stock market crash\">COVID-19 stock market crash</a>, an overnight spike in lending rates in the United States prompts the <a href=\"https://wikipedia.org/wiki/Federal_Reserve\" title=\"Federal Reserve\">Federal Reserve</a> to conduct operations in the <a href=\"https://wikipedia.org/wiki/Repo_market\" class=\"mw-redirect\" title=\"Repo market\">repo market</a>.", "links": [{"title": "2020 stock market crash", "link": "https://wikipedia.org/wiki/2020_stock_market_crash"}, {"title": "Federal Reserve", "link": "https://wikipedia.org/wiki/Federal_Reserve"}, {"title": "Repo market", "link": "https://wikipedia.org/wiki/Repo_market"}]}, {"year": "2021", "text": "A 6.0 Mw  earthquake strikes Lu County, Sichuan, China, killing three and injuring more than 88.", "html": "2021 - A 6.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2021_Luxian_earthquake\" title=\"2021 Luxian earthquake\">earthquake strikes Lu County</a>, Sichuan, China, killing three and injuring more than 88.", "no_year_html": "A 6.0 M<sub>w</sub>  <a href=\"https://wikipedia.org/wiki/2021_Luxian_earthquake\" title=\"2021 Luxian earthquake\">earthquake strikes Lu County</a>, Sichuan, China, killing three and injuring more than 88.", "links": [{"title": "2021 Luxian earthquake", "link": "https://wikipedia.org/wiki/2021_Luxian_earthquake"}]}, {"year": "2022", "text": "During the Let Yet Kone massacre, the Burmese military kills 13 villagers, including eight children, after attacking a school in Sagaing Region, Myanmar.", "html": "2022 - During the <a href=\"https://wikipedia.org/wiki/Let_Yet_Kone_massacre\" title=\"Let Yet Kone massacre\">Let Yet Kone massacre</a>, the <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> kills 13 villagers, including eight children, after attacking a school in <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing Region</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "no_year_html": "During the <a href=\"https://wikipedia.org/wiki/Let_Yet_Kone_massacre\" title=\"Let Yet Kone massacre\">Let Yet Kone massacre</a>, the <a href=\"https://wikipedia.org/wiki/Tatmadaw\" title=\"Tatmadaw\">Burmese military</a> kills 13 villagers, including eight children, after attacking a school in <a href=\"https://wikipedia.org/wiki/Sagaing_Region\" title=\"Sagaing Region\">Sagaing Region</a>, <a href=\"https://wikipedia.org/wiki/Myanmar\" title=\"Myanmar\">Myanmar</a>.", "links": [{"title": "Let Yet Kone massacre", "link": "https://wikipedia.org/wiki/Let_Yet_<PERSON><PERSON>_massacre"}, {"title": "Tatmadaw", "link": "https://wikipedia.org/wiki/Tatmadaw"}, {"title": "Sagaing Region", "link": "https://wikipedia.org/wiki/Sagaing_Region"}, {"title": "Myanmar", "link": "https://wikipedia.org/wiki/Myanmar"}]}, {"year": "2022", "text": "The death of <PERSON><PERSON><PERSON> occurred, which sparked worldwide protests.", "html": "2022 - The <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Death of <PERSON><PERSON><PERSON>ini\">death of <PERSON><PERSON><PERSON></a> occurred, which sparked worldwide <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_protests\" title=\"Mahsa Amini protests\">protests</a>.", "no_year_html": "The <a href=\"https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>\" title=\"Death of <PERSON><PERSON><PERSON><PERSON>\">death of <PERSON><PERSON><PERSON></a> occurred, which sparked worldwide <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_protests\" title=\"Mahsa Amini protests\">protests</a>.", "links": [{"title": "Death of <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Death_of_<PERSON><PERSON><PERSON>_<PERSON><PERSON>"}, {"title": "Mahsa Amini protests", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_protests"}]}], "Births": [{"year": "16", "text": "<PERSON>, Roman daughter of <PERSON><PERSON> (d. 38)", "html": "16 - AD 16 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a> (d. 38)", "no_year_html": "AD 16 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Roman daughter of <a href=\"https://wikipedia.org/wiki/Germanicus\" title=\"Germanicus\">Germanicus</a> (d. 38)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Germanicus"}]}, {"year": "508", "text": "<PERSON>, emperor of the Liang dynasty (d. 555)", "html": "508 - <a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Liang\" title=\"Emperor <PERSON> of Liang\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (d. 555)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Emperor_<PERSON>_of_Liang\" title=\"Emperor <PERSON> of Liang\"><PERSON></a>, emperor of the <a href=\"https://wikipedia.org/wiki/Liang_dynasty\" title=\"Liang dynasty\">Liang dynasty</a> (d. 555)", "links": [{"title": "Emperor <PERSON> of Liang", "link": "https://wikipedia.org/wiki/Emperor_<PERSON>_<PERSON>_Liang"}, {"title": "Liang dynasty", "link": "https://wikipedia.org/wiki/Liang_dynasty"}]}, {"year": "1295", "text": "<PERSON>, English noblewoman (d. 1360)", "html": "1295 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (d. 1360)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English noblewoman (d. 1360)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1386", "text": "<PERSON> of England (d. 1422)", "html": "1386 - <a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a> (d. 1422)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Henry_V_of_England\" title=\"Henry V of England\"><PERSON> of England</a> (d. 1422)", "links": [{"title": "<PERSON> of England", "link": "https://wikipedia.org/wiki/Henry_V_of_England"}]}, {"year": "1462", "text": "<PERSON>, Italian philosopher (d. 1525)", "html": "1462 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher (d. 1525)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian philosopher (d. 1525)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1507", "text": "<PERSON><PERSON><PERSON> Emperor of China (d. 1567)", "html": "1507 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiajing Emperor\"><PERSON>ajing Emperor</a> of China (d. 1567)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Jiajing Emperor\">Jiajing Emperor</a> of China (d. 1567)", "links": [{"title": "<PERSON><PERSON><PERSON> Emperor", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1541", "text": "<PERSON>, 1st Earl of Essex, English nobleman (d. 1576)", "html": "1541 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl of Essex</a>, English nobleman (d. 1576)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex\" title=\"<PERSON>, 1st Earl of Essex\"><PERSON>, 1st Earl of Essex</a>, English nobleman (d. 1576)", "links": [{"title": "<PERSON>, 1st Earl of Essex", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Essex"}]}, {"year": "1557", "text": "<PERSON>, French composer (d. 1627)", "html": "1557 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1627)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer (d. 1627)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1615", "text": "<PERSON>, German organist and composer (d. 1692)", "html": "1615 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, German organist and composer (d. 1692)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bach\"><PERSON></a>, German organist and composer (d. 1692)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1625", "text": "<PERSON><PERSON>, Roman Catholic saint (d. 1697)", "html": "1625 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>go\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Roman Catholic saint (d. 1697)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Barbarigo\"><PERSON><PERSON></a>, Roman Catholic saint (d. 1697)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Gregorio_Barbarigo"}]}, {"year": "1651", "text": "<PERSON><PERSON><PERSON>, German physician and botanist (d. 1716)", "html": "1651 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and botanist (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German physician and botanist (d. 1716)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1666", "text": "<PERSON>, French mathematician and theorist (d. 1716)", "html": "1666 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (d. 1716)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French mathematician and theorist (d. 1716)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1678", "text": "<PERSON>, 1st Viscount <PERSON>, English philosopher and politician, Secretary of State for the Southern Department (d. 1751)", "html": "1678 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Bolingbroke\" title=\"<PERSON>, 1st Viscount Boling<PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1751)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Viscount_Bolingbro<PERSON>\" title=\"<PERSON>, 1st Viscount Boling<PERSON>\"><PERSON>, 1st Viscount <PERSON></a>, English philosopher and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department\" title=\"Secretary of State for the Southern Department\">Secretary of State for the Southern Department</a> (d. 1751)", "links": [{"title": "<PERSON>, 1st Viscount <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_1st_Viscount_<PERSON>ling<PERSON>"}, {"title": "Secretary of State for the Southern Department", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Southern_Department"}]}, {"year": "1716", "text": "<PERSON>, Italian tenor and actor (d. 1798)", "html": "1716 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 1798)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian tenor and actor (d. 1798)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1722", "text": "<PERSON>, Scottish-Canadian general (d. 1799)", "html": "1722 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish-Canadian general (d. 1799)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)\" title=\"<PERSON> (British Army officer)\"><PERSON></a>, Scottish-Canadian general (d. 1799)", "links": [{"title": "<PERSON> (British Army officer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(British_Army_officer)"}]}, {"year": "1725", "text": "<PERSON>, French geologist, zoologist, and author (d. 1815)", "html": "1725 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist, zoologist, and author (d. 1815)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist, zoologist, and author (d. 1815)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1745", "text": "<PERSON>, Russian field marshal (d. 1813)", "html": "1745 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1813)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian field marshal (d. 1813)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1777", "text": "<PERSON>, German-English banker and financier (d. 1836)", "html": "1777 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English banker and financier (d. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English banker and financier (d. 1836)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1782", "text": "<PERSON><PERSON><PERSON> of China (d. 1850)", "html": "1782 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"Daoguang Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1850)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor\" title=\"<PERSON>oguang Emperor\"><PERSON><PERSON><PERSON> Emperor</a> of China (d. 1850)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Emperor"}]}, {"year": "1812", "text": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>, Dutch novelist (d. 1886)", "html": "1812 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>boom-To<PERSON>t\" title=\"<PERSON>-Touss<PERSON>\"><PERSON>-<PERSON></a>, Dutch novelist (d. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>boom-To<PERSON>t\" title=\"<PERSON>-Touss<PERSON>\"><PERSON>-<PERSON></a>, Dutch novelist (d. 1886)", "links": [{"title": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>m-<PERSON><PERSON><PERSON>t"}]}, {"year": "1822", "text": "<PERSON>, American businessman (d. 1888)", "html": "1822 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON>, American historian and author (d. 1893)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1893)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American historian and author (d. 1893)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1823", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish anatomist (d. 1895)", "html": "1823 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish anatomist (d. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish anatomist (d. 1895)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>d<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1827", "text": "<PERSON>, French geologist and paleontologist (d. 1908)", "html": "1827 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist and paleontologist (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French geologist and paleontologist (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1828", "text": "<PERSON>, Swedish murderer (d. 1914)", "html": "1828 - <a href=\"https://wikipedia.org/wiki/Per_P%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish murderer (d. 1914)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Per_P%C3%A<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swedish murderer (d. 1914)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Per_P%C3%A5lsson"}]}, {"year": "1830", "text": "<PERSON>, Irish-Australian cardinal (d. 1911)", "html": "1830 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian cardinal (d. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish-Australian cardinal (d. 1911)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1837", "text": "<PERSON> of Portugal (d. 1861)", "html": "1837 - <a href=\"https://wikipedia.org/wiki/Pedro_V_of_Portugal\" title=\"Pedro V of Portugal\">Pedro V of Portugal</a> (d. 1861)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Pedro_V_of_Portugal\" title=\"Pedro V of Portugal\">Pedro V of Portugal</a> (d. 1861)", "links": [{"title": "Pedro V of Portugal", "link": "https://wikipedia.org/wiki/Pedro_V_of_Portugal"}]}, {"year": "1838", "text": "<PERSON>, Canadian-American railroad executive (d. 1916)", "html": "1838 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Canadian-American railroad executive (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"James <PERSON>\"><PERSON></a>, Canadian-American railroad executive (d. 1916)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1844", "text": "<PERSON>, French flute player and conductor (d. 1908)", "html": "1844 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and conductor (d. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French flute player and conductor (d. 1908)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1846", "text": "<PERSON>, English author, poet, and activist (d. 1888)", "html": "1846 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and activist (d. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and activist (d. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1853", "text": "<PERSON><PERSON>, German physician and biochemist, Nobel Prize laureate (d. 1927)", "html": "1853 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, German physician and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1858", "text": "<PERSON>, English lawyer and politician (d. 1927)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edward <PERSON> Hall\"><PERSON></a>, English lawyer and politician (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Edward <PERSON> Hall\"><PERSON></a>, English lawyer and politician (d. 1927)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1858", "text": "<PERSON><PERSON>, Canadian-Scottish banker and politician, Prime Minister of the United Kingdom (d. 1923)", "html": "1858 - <a href=\"https://wikipedia.org/wiki/Bonar_Law\" title=\"Bonar Law\"><PERSON><PERSON></a>, Canadian-Scottish banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Bonar_Law\" title=\"Bonar Law\"><PERSON><PERSON></a>, Canadian-Scottish banker and politician, <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom\" title=\"Prime Minister of the United Kingdom\">Prime Minister of the United Kingdom</a> (d. 1923)", "links": [{"title": "Bonar Law", "link": "https://wikipedia.org/wiki/Bonar_Law"}, {"title": "Prime Minister of the United Kingdom", "link": "https://wikipedia.org/wiki/Prime_Minister_of_the_United_Kingdom"}]}, {"year": "1859", "text": "<PERSON>, Chinese general and politician, President of the Republic of China (d. 1916)", "html": "1859 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Shi<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1916)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Yuan Shi<PERSON>\"><PERSON></a>, Chinese general and politician, <a href=\"https://wikipedia.org/wiki/President_of_the_Republic_of_China\" title=\"President of the Republic of China\">President of the Republic of China</a> (d. 1916)", "links": [{"title": "Yuan Shikai", "link": "https://wikipedia.org/wiki/Yuan_Shikai"}, {"title": "President of the Republic of China", "link": "https://wikipedia.org/wiki/President_of_the_Republic_of_China"}]}, {"year": "1861", "text": "<PERSON>, African-American educator and inventor (d. 1947)", "html": "1861 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American educator and inventor (d. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, African-American educator and inventor (d. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1866", "text": "<PERSON>, German lawyer and politician, Mayor of Marburg (d. 1927)", "html": "1866 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (d. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/List_of_mayors_of_Marburg\" title=\"List of mayors of Marburg\">Mayor of Marburg</a> (d. 1927)", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)"}, {"title": "List of mayors of Marburg", "link": "https://wikipedia.org/wiki/List_of_mayors_of_Marburg"}]}, {"year": "1870", "text": "<PERSON>, Irish tennis player and politician (d. 1958)", "html": "1870 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish tennis player and politician (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Irish tennis player and politician (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1875", "text": "<PERSON>, American businessman and philanthropist, founded <PERSON><PERSON> <PERSON><PERSON> (d. 1971)", "html": "1875 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"J. C. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"J. <PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a> (d. 1971)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1876", "text": "<PERSON>, American boxer (d. 1931)", "html": "1876 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer (d. 1931)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1877", "text": "<PERSON>, American-Canadian inventor and businessman, founded Schick Razors (d. 1937)", "html": "1877 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(razors)\" title=\"<PERSON><PERSON><PERSON> (razors)\">Schick Razors</a> (d. 1937)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-Canadian inventor and businessman, founded <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(razors)\" title=\"<PERSON><PERSON><PERSON> (razors)\">Schick Razors</a> (d. 1937)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON> (razors)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_(razors)"}]}, {"year": "1878", "text": "<PERSON>, German sculptor, lithographer, and educator (d. 1961)", "html": "1878 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor, lithographer, and educator (d. 1961)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German sculptor, lithographer, and educator (d. 1961)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, American nurse (d. 1917)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American nurse (d. 1917)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1880", "text": "<PERSON>, English author, poet, and playwright (d. 1958)", "html": "1880 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 1958)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author, poet, and playwright (d. 1958)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1881", "text": "<PERSON>, English philosopher and critic (d. 1964)", "html": "1881 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English philosopher and critic (d. 1964)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1883", "text": "<PERSON><PERSON> <PERSON><PERSON>, English poet and critic (d. 1917)", "html": "1883 - <a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and critic (d. 1917)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, English poet and critic (d. 1917)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>"}]}, {"year": "1886", "text": "<PERSON>, Alsatian sculptor and painter (d. 1966)", "html": "1886 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian sculptor and painter (d. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Alsatian sculptor and painter (d. 1966)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON>, French composer and educator (d. 1979)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1979)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French composer and educator (d. 1979)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, English race car driver and engineer, founded Bentley Motors Limited (d. 1971)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. O<PERSON> Bentley\"><PERSON><PERSON> <PERSON><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/Bentley\" title=\"Bentley\">Bentley Motors Limited</a> (d. 1971)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"W. O<PERSON> Bentley\"><PERSON><PERSON> <PERSON><PERSON></a>, English race car driver and engineer, founded <a href=\"https://wikipedia.org/wiki/Bentley\" title=\"Bentley\">Bentley Motors Limited</a> (d. 1971)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Bentley", "link": "https://wikipedia.org/wiki/Bentley"}]}, {"year": "1888", "text": "<PERSON><PERSON> <PERSON><PERSON>, Finnish author, Nobel Prize laureate (d. 1964)", "html": "1888 - <a href=\"https://wikipedia.org/wiki/Frans_E<PERSON>il_Sillanp%C3%A4%C3%A4\" title=\"Frans Eemil Sillanpää\">Frans E<PERSON><PERSON></a>, Finnish author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1964)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Frans_E<PERSON>il_Sillanp%C3%A4%C3%A4\" title=\"Frans Eemil Sillanpää\">Frans E<PERSON>il <PERSON></a>, Finnish author, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Literature\" title=\"Nobel Prize in Literature\">Nobel Prize</a> laureate (d. 1964)", "links": [{"title": "Frans E<PERSON>il <PERSON>", "link": "https://wikipedia.org/wiki/Frans_Eemil_Sillanp%C3%A4%C3%A4"}, {"title": "Nobel Prize in Literature", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Literature"}]}, {"year": "1890", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli author (d. 1970)", "html": "1890 - <a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author (d. 1970)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/A<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli author (d. 1970)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Avig<PERSON>_<PERSON>"}]}, {"year": "1891", "text": "<PERSON>, German admiral and politician, President of Germany (d. 1980)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral and politician, <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%B6<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German admiral and politician, <a href=\"https://wikipedia.org/wiki/List_of_German_presidents\" class=\"mw-redirect\" title=\"List of German presidents\">President of Germany</a> (d. 1980)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Karl_D%C3%B6nitz"}, {"title": "List of German presidents", "link": "https://wikipedia.org/wiki/List_of_German_presidents"}]}, {"year": "1891", "text": "<PERSON>, Austrian-German spy (d. 1972)", "html": "1891 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German spy (d. 1972)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-German spy (d. 1972)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON>, Hungarian-English director, producer, and screenwriter (d. 1956)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English director, producer, and screenwriter (d. 1956)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-English director, producer, and screenwriter (d. 1956)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1893", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian-American physiologist and biochemist, Nobel Prize laureate (d. 1986)", "html": "1893 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-Gy%C3%B6rgyi\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physiologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>-<PERSON>%C3%B6rgyi\" title=\"<PERSON>\"><PERSON></a>, Hungarian-American physiologist and biochemist, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (d. 1986)", "links": [{"title": "<PERSON>Gyö<PERSON>", "link": "https://wikipedia.org/wiki/Albert_Szent-Gy%C3%B6rgyi"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1895", "text": "<PERSON><PERSON><PERSON>, Malaysian author and scholar (d. 1973)", "html": "1895 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Malaysian author and scholar (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(writer)\" title=\"<PERSON><PERSON><PERSON> (writer)\"><PERSON><PERSON><PERSON></a>, Malaysian author and scholar (d. 1973)", "links": [{"title": "<PERSON><PERSON><PERSON> (writer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_(writer)"}]}, {"year": "1897", "text": "<PERSON><PERSON>, American composer (d. 1962)", "html": "1897 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1962)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American composer (d. 1962)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Mi<PERSON>_<PERSON>"}]}, {"year": "1898", "text": "<PERSON><PERSON> <PERSON><PERSON>, American author and illustrator, co-created <PERSON><PERSON><PERSON> (d. 1977)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. A<PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>ous <PERSON>\"><PERSON><PERSON><PERSON></a></i> (d. 1977)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"H. A<PERSON> Rey\"><PERSON><PERSON> <PERSON><PERSON></a>, American author and illustrator, co-created <i><a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>ous_<PERSON>\" title=\"<PERSON><PERSON>ous <PERSON>\"><PERSON><PERSON><PERSON> <PERSON></a></i> (d. 1977)", "links": [{"title": "H. A<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}, {"title": "Curious <PERSON>", "link": "https://wikipedia.org/wiki/Curious_<PERSON>"}]}, {"year": "1899", "text": "<PERSON>, Hungarian-Austrian conductor and educator (d. 1975)", "html": "1899 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian conductor and educator (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Hungarian-Austrian conductor and educator (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1901", "text": "<PERSON>, Austrian rabbi and philosopher from the Vienna Circle (d. 1994)", "html": "1901 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4chter\" title=\"<PERSON>\"><PERSON></a>, Austrian rabbi and philosopher from the Vienna Circle (d. 1994)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4chter\" title=\"<PERSON>\"><PERSON></a>, Austrian rabbi and philosopher from the Vienna Circle (d. 1994)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4chter"}]}, {"year": "1905", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech poet and author (d. 1980)", "html": "1905 - <a href=\"https://wikipedia.org/wiki/<PERSON>im%C3%<PERSON>r_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and author (d. 1980)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>im%C3%<PERSON>r_<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Czech poet and author (d. 1980)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vladim%C3%ADr_<PERSON>lan"}]}, {"year": "1906", "text": "<PERSON>, Sri Lankan-British colonel (d. 1996)", "html": "1906 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-British colonel (d. 1996)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Sri Lankan-British colonel (d. 1996)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German colonel and chauffeur (d. 1975)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and chauffeur (d. 1975)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German colonel and chauffeur (d. 1975)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1910", "text": "<PERSON>, German race car driver and manager (d. 2003)", "html": "1910 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and manager (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German race car driver and manager (d. 2003)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON><PERSON><PERSON>, Australian journalist and author (d. 1983)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist and author (d. 1983)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Australian journalist and author (d. 1983)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W<PERSON><PERSON>_<PERSON>"}]}, {"year": "1911", "text": "<PERSON>, American screenwriter and producer (d. 2005)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2005)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American screenwriter and producer (d. 2005)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON>, American director, producer, and screenwriter (d. 1999)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1915", "text": "<PERSON>, American pianist (d. 1968)", "html": "1915 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pianist (d. 1968)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Caribbean politician, 1st Prime Minister of Saint Kitts and <PERSON><PERSON><PERSON> (d. 1978)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Saint_Kitts_and_Nevis\" class=\"mw-redirect\" title=\"Prime Minister of Saint Kitts and Nevis\">Prime Minister of Saint Kitts and Nevis</a> (d. 1978)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Caribbean politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Saint_Kitts_and_Nevis\" class=\"mw-redirect\" title=\"Prime Minister of Saint Kitts and Nevis\">Prime Minister of Saint Kitts and Nevis</a> (d. 1978)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "Prime Minister of Saint Kitts and <PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Saint_Kitts_and_<PERSON><PERSON><PERSON>"}]}, {"year": "1916", "text": "<PERSON>, Australian rugby league player and policeman (d. 1985)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and policeman (d. 1985)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)\" title=\"<PERSON> (rugby league)\"><PERSON></a>, Australian rugby league player and policeman (d. 1985)", "links": [{"title": "<PERSON> (rugby league)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_league)"}]}, {"year": "1916", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indian Carnatic vocalist (d. 2004)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/M._S._Subbulakshmi\" title=\"M. S. Subbulakshmi\">M. S. Subbulakshmi</a>, Indian <a href=\"https://wikipedia.org/wiki/Carnatic_region\" title=\"Carnatic region\">Carnatic</a> vocalist (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/M._S._Subbulakshmi\" title=\"M. S. Subbulakshmi\">M. S. Subbulakshmi</a>, Indian <a href=\"https://wikipedia.org/wiki/Carnatic_region\" title=\"Carnatic region\">Carnatic</a> vocalist (d. 2004)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/M._S._Subbulakshmi"}, {"title": "Carnatic region", "link": "https://wikipedia.org/wiki/Carnatic_region"}]}, {"year": "1916", "text": "<PERSON>, Barbadian cricketer, umpire, and politician (d. 1999)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer, umpire, and politician (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Barbadian cricketer, umpire, and politician (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON>, Haitian writer (d. 1973)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer (d. 1973)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Haitian writer (d. 1973)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1916", "text": "<PERSON><PERSON><PERSON><PERSON>, Indian industrialist (d. 2000)", "html": "1916 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON> Gogte\"><PERSON><PERSON><PERSON><PERSON></a>, Indian industrialist (d. 2000)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e\" title=\"<PERSON><PERSON><PERSON><PERSON> Gogte\"><PERSON><PERSON><PERSON><PERSON></a>, Indian industrialist (d. 2000)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>e"}]}, {"year": "1918", "text": "<PERSON><PERSON><PERSON><PERSON>, Polish pianist (d. 1968)", "html": "1918 - <a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist (d. 1968)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Polish pianist (d. 1968)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W%C5%82adys%C5%82aw_K%C4%99dra"}]}, {"year": "1919", "text": "<PERSON>, American football player and sportscaster (d. 2015)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player and sportscaster (d. 2015)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "1919", "text": "<PERSON>, Canadian-American hierarchiologist and educator (d. 1990)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American hierarchiologist and educator (d. 1990)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American hierarchiologist and educator (d. 1990)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1919", "text": "<PERSON>, American singer and actor (d. 1992)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer and actor (d. 1992)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)"}]}, {"year": "1920", "text": "<PERSON><PERSON>, American air force general (d. 2015)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Staryl_C._Austin\" title=\"Staryl C. Austin\"><PERSON><PERSON></a>, American air force general (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Staryl_C._Austin\" title=\"Staryl C. Austin\"><PERSON><PERSON></a>, American air force general (d. 2015)", "links": [{"title": "Staryl C. <PERSON>", "link": "https://wikipedia.org/wiki/Staryl_C._Austin"}]}, {"year": "1920", "text": "<PERSON>, English nurse and educator (d. 2016)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and educator (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English nurse and educator (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1920", "text": "<PERSON>, American cartoonist (d. 1991)", "html": "1920 - <a href=\"https://wikipedia.org/wiki/Art_Sansom\" title=\"Art Sansom\"><PERSON></a>, American cartoonist (d. 1991)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Art_Sansom\" title=\"Art Sansom\"><PERSON></a>, American cartoonist (d. 1991)", "links": [{"title": "Art Sansom", "link": "https://wikipedia.org/wiki/Art_Sansom"}]}, {"year": "1921", "text": "<PERSON>, German-Canadian metallurgist (d. 2016)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian <a href=\"https://wikipedia.org/wiki/Metallurgy\" title=\"Metallurgy\">metallurgist</a> (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-Canadian <a href=\"https://wikipedia.org/wiki/Metallurgy\" title=\"Metallurgy\">metallurgist</a> (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Metallurgy", "link": "https://wikipedia.org/wiki/Metallurgy"}]}, {"year": "1921", "text": "<PERSON>, American singer-songwriter (d. 2017)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1921", "text": "<PERSON><PERSON><PERSON>, American pianist and composer (d. 1998)", "html": "1921 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Pandit\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1998)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Pan<PERSON>\" title=\"<PERSON><PERSON><PERSON> Pan<PERSON>\"><PERSON><PERSON><PERSON></a>, American pianist and composer (d. 1998)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Korla_Pandit"}]}, {"year": "1922", "text": "<PERSON>, French-English director and screenwriter (d. 2016)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English director and screenwriter (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French-English director and screenwriter (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1922", "text": "<PERSON><PERSON>, American actress and singer (d. 2024)", "html": "1922 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer (d. 2024)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1923", "text": "<PERSON>, Singaporean lawyer and politician, 1st Prime Minister of Singapore (d. 2015)", "html": "1923 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a> (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Singaporean lawyer and politician, 1st <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Singapore\" title=\"Prime Minister of Singapore\">Prime Minister of Singapore</a> (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>w"}, {"title": "Prime Minister of Singapore", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Singapore"}]}, {"year": "1924", "text": "<PERSON>, American actress (d. 2014)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>l"}]}, {"year": "1924", "text": "<PERSON>, French cinematographer and director (d. 2016)", "html": "1924 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer and director (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French cinematographer and director (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, American singer and guitarist (d. 1999)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1999)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer and guitarist (d. 1999)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Irish accountant, lawyer, and politician, 7th Taoiseach of Ireland (d. 2006)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish accountant, lawyer, and politician, 7th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 2006)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish accountant, lawyer, and politician, 7th <a href=\"https://wikipedia.org/wiki/Taoiseach\" title=\"Taoiseach\">Taoiseach of Ireland</a> (d. 2006)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>iseach"}]}, {"year": "1925", "text": "<PERSON><PERSON><PERSON><PERSON>, American singer-songwriter, guitarist, and producer (d. 2015)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/B<PERSON>B._King\" class=\"mw-redirect\" title=\"B.B. King\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/B.B._King\" class=\"mw-redirect\" title=\"B.B. King\"><PERSON><PERSON><PERSON><PERSON></a>, American singer-songwriter, guitarist, and producer (d. 2015)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON> King", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_King"}]}, {"year": "1926", "text": "<PERSON>, Austrian-Australian pianist and composer (d. 2011)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian pianist and composer (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian-Australian pianist and composer (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American novelist (d. 2001)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2001)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist (d. 2001)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American baseball player (d. 2014)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1926", "text": "<PERSON>, American pastor and author (d. 2015)", "html": "1926 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (d. 2015)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American pastor and author (d. 2015)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor (d. 2011)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1927", "text": "<PERSON>, American actor and politician (d. 1992)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and politician (d. 1992)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and politician (d. 1992)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1927", "text": "<PERSON><PERSON><PERSON>, Japanese academic and diplomat, United Nations High Commissioner for Refugees (d. 2019)", "html": "1927 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees\" title=\"United Nations High Commissioner for Refugees\">United Nations High Commissioner for Refugees</a> (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese academic and diplomat, <a href=\"https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees\" title=\"United Nations High Commissioner for Refugees\">United Nations High Commissioner for Refugees</a> (d. 2019)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sad<PERSON>o_Ogata"}, {"title": "United Nations High Commissioner for Refugees", "link": "https://wikipedia.org/wiki/United_Nations_High_Commissioner_for_Refugees"}]}, {"year": "1928", "text": "<PERSON>, American television host, actor, and singer (d. 2013)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host, actor, and singer (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American television host, actor, and singer (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Trailer"}]}, {"year": "1928", "text": "<PERSON>, English author and educator (d. 1986)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Lady <PERSON>\"><PERSON></a>, English author and educator (d. 1986)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and educator (d. 1986)", "links": [{"title": "<PERSON> <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1928", "text": "<PERSON>, American judge (d. 2019)", "html": "1928 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American judge (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1929", "text": "<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar, last sultan of Zanzibar", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a>, last sultan of Zanzibar", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar\" title=\"<PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar\"><PERSON><PERSON><PERSON> bin <PERSON> of Zanzibar</a>, last sultan of Zanzibar", "links": [{"title": "<PERSON><PERSON><PERSON> <PERSON> of Zanzibar", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_bin_<PERSON>_of_Zanzibar"}]}, {"year": "1929", "text": "<PERSON>, Canadian-American politician, 20th Governor of Montana (d. 2021)", "html": "1929 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American politician, 20th <a href=\"https://wikipedia.org/wiki/Governor_of_Montana\" class=\"mw-redirect\" title=\"Governor of Montana\">Governor of Montana</a> (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Montana", "link": "https://wikipedia.org/wiki/Governor_of_Montana"}]}, {"year": "1930", "text": "<PERSON>, American actress (d. 2011)", "html": "1930 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2011)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (d. 2011)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON><PERSON> <PERSON><PERSON>, Sri Lankan zoologist and academic (d. 2003)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/K._<PERSON>._A<PERSON><PERSON>pragasam\" title=\"K. D. Arulpragasam\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan zoologist and academic (d. 2003)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/K._<PERSON><PERSON>_A<PERSON><PERSON>gasam\" title=\"K. D. Arulpragasam\"><PERSON><PERSON> <PERSON><PERSON></a>, Sri Lankan zoologist and academic (d. 2003)", "links": [{"title": "K. D<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>am"}]}, {"year": "1931", "text": "<PERSON> <PERSON>, American-Dutch singer-songwriter and pianist (d. 2013)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Little <PERSON></a>, American-Dutch singer-songwriter and pianist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\">Little <PERSON></a>, American-Dutch singer-songwriter and pianist (d. 2013)", "links": [{"title": "Little <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON>, American actor", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1932", "text": "<PERSON><PERSON>, English cricketer and coach", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English cricketer and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1933", "text": "<PERSON>, German-English businesswoman and philanthropist, founded Xansa", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English businesswoman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Xansa\" title=\"Xansa\">Xansa</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German-English businesswoman and philanthropist, founded <a href=\"https://wikipedia.org/wiki/Xansa\" title=\"Xansa\">Xansa</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON><PERSON>a", "link": "https://wikipedia.org/wiki/Xansa"}]}, {"year": "1934", "text": "<PERSON>, American basketball player and coach (d. 2021)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Elgin_Baylor"}]}, {"year": "1934", "text": "<PERSON>, Irish singer-songwriter and guitarist  (d. 2008)", "html": "1934 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish singer-songwriter and guitarist (d. 2008)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American sculptor (d. 2024)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American sculptor (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billy <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Billy <PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American director, producer, composer, and author (d. 2022)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American director, producer, composer, and author (d. 2022)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bass\"><PERSON></a>, American director, producer, composer, and author (d. 2022)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON><PERSON>, Filipino actress (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino actress (d. 2016)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}]}, {"year": "1935", "text": "<PERSON>, American-English businessman (d. 2016)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English businessman (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English businessman (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, Argentinian-German author and playwright", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-German author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Argentinian-German author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1935", "text": "<PERSON>, American fashion model (d. 2023)", "html": "1935 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American fashion model (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)\" title=\"<PERSON> (model)\"><PERSON></a>, American fashion model (d. 2023)", "links": [{"title": "<PERSON> (model)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(model)"}]}, {"year": "1937", "text": "<PERSON>, Russian wrestler (d. 2024)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian wrestler (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian wrestler (d. 2024)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1937", "text": "<PERSON>, American businessman and philanthropist (d. 2019)", "html": "1937 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman and philanthropist (d. 2019)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON><PERSON><PERSON>, South African-French poet and painter (d. 2024)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Brey<PERSON>bach\"><PERSON><PERSON><PERSON></a>, South African-French poet and painter (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, South African-French poet and painter (d. 2024)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1939", "text": "<PERSON>, American basketball player (d. 2014)", "html": "1939 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player (d. 2014)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON><PERSON>, American jazz saxophonist and composer (d. 2018)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>iet_Blu<PERSON>t\" title=\"Hamiet Blu<PERSON>t\"><PERSON><PERSON></a>, American jazz saxophonist and composer (d. 2018)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Blu<PERSON>t\" title=\"Hamiet Bluiett\"><PERSON><PERSON></a>, American jazz saxophonist and composer (d. 2018)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>t"}]}, {"year": "1940", "text": "<PERSON>, American tennis player", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1940", "text": "<PERSON>, Baron <PERSON>, British life peer (d. 2024)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British life peer (d. 2024)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, British life peer (d. 2024)", "links": [{"title": "<PERSON>, Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}]}, {"year": "1941", "text": "<PERSON>, American singer, autoharp player, and drummer", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, <a href=\"https://wikipedia.org/wiki/Autoharp\" title=\"Autoharp\">autoharp</a> player, and drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer, <a href=\"https://wikipedia.org/wiki/Autoharp\" title=\"Autoharp\">autoharp</a> player, and drummer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Autoharp", "link": "https://wikipedia.org/wiki/Autoharp"}]}, {"year": "1941", "text": "<PERSON>, American political scientist and politician", "html": "1941 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American political scientist and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, English bass player and keyboard player", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and keyboard player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English bass player and keyboard player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1942", "text": "<PERSON>, American computer scientist and academic", "html": "1942 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American computer scientist and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>, Chinese footballer and manager (d. 2012)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chinese footballer and manager (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1943", "text": "<PERSON>,  American short story writer and essayist (d. 2016)", "html": "1943 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and essayist (d. 2016)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American short story writer and essayist (d. 2016)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American actress", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1944", "text": "<PERSON>, American soul/R&B singer", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul/R&amp;B singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American soul/R&amp;B singer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, American country music singer-songwriter and bass player", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American country music singer-songwriter and bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1946", "text": "<PERSON>, Australian lawyer and politician", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)\" title=\"<PERSON> (Australian politician)\"><PERSON></a>, Australian lawyer and politician", "links": [{"title": "<PERSON> (Australian politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Australian_politician)"}]}, {"year": "1946", "text": "<PERSON><PERSON>, Spanish singer-songwriter and producer (d. 2019)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and producer (d. 2019)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish singer-songwriter and producer (d. 2019)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1947", "text": "<PERSON>, English director and playwright", "html": "1947 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English director and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)\" title=\"<PERSON> (playwright)\"><PERSON></a>, English director and playwright", "links": [{"title": "<PERSON> (playwright)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(playwright)"}]}, {"year": "1948", "text": "<PERSON>, American bass player", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American tennis player and sportscaster", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American tennis player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, English author and playwright", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English author and playwright", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON><PERSON>, English drummer", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English drummer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1948", "text": "<PERSON>, American actress", "html": "1948 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1949", "text": "<PERSON>, American actor and environmental activist", "html": "1949 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and environmental activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON> Jr.\"><PERSON>.</a>, American actor and environmental activist", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1950", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/The_Bellamy_Brothers\" title=\"The Bellamy Brothers\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/The_Bellamy_Brothers\" title=\"The Bellamy Brothers\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "The Bellamy Brothers", "link": "https://wikipedia.org/wiki/The_Bellamy_Brothers"}]}, {"year": "1950", "text": "<PERSON>, American historian, scholar, and journalist", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>\"><PERSON>.</a>, American historian, scholar, and journalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>.\" title=\"<PERSON>.\"><PERSON>.</a>, American historian, scholar, and journalist", "links": [{"title": "<PERSON> Jr.", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>_<PERSON>."}]}, {"year": "1950", "text": "<PERSON><PERSON>, American-English singer, guitarist, and television host", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English singer, guitarist, and television host", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American-English singer, guitarist, and television host", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Bell\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1951", "text": "<PERSON>, Scottish rugby player and coach", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)\" title=\"<PERSON> (rugby union)\"><PERSON></a>, Scottish rugby player and coach", "links": [{"title": "<PERSON> (rugby union)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(rugby_union)"}]}, {"year": "1951", "text": "<PERSON>, Bruneian cardinal (d. 2021)", "html": "1951 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bruneian cardinal (d. 2021)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Bruneian cardinal (d. 2021)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, English educator and politician", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON><PERSON><PERSON><PERSON>, Lithuanian historian", "html": "1952 - <a href=\"https://wikipedia.org/wiki/%C4%8Ceslovas_Laurinavi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian historian", "no_year_html": "<a href=\"https://wikipedia.org/wiki/%C4%8Ceslovas_Laurinavi%C4%8Dius\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Lithuanian historian", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/%C4%8Ceslovas_Laurinavi%C4%8Dius"}]}, {"year": "1952", "text": "<PERSON>, South African swimmer and physician (d. 2013)", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer and physician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, South African swimmer and physician (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1952", "text": "<PERSON>, American boxer and actor", "html": "1952 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American boxer and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American character actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American character actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American character actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, English singer and guitarist (d. 1995)", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist (d. 1995)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer and guitarist (d. 1995)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Canadian-American author and translator", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and translator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian-American author and translator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American musician", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American musician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, <PERSON>, English journalist and politician, 2nd Deputy Secretary-General of the United Nations", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Baron_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, <PERSON></a>, English journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>, Baron <PERSON>\"><PERSON>, Baron <PERSON></a>, English journalist and politician, 2nd <a href=\"https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations\" title=\"Deputy Secretary-General of the United Nations\">Deputy Secretary-General of the United Nations</a>", "links": [{"title": "<PERSON>, <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>,_<PERSON>_<PERSON>"}, {"title": "Deputy Secretary-General of the United Nations", "link": "https://wikipedia.org/wiki/Deputy_Secretary-General_of_the_United_Nations"}]}, {"year": "1953", "text": "<PERSON>, American golfer and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American golfer and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, Chilean footballer and manager", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Chilean footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1953", "text": "<PERSON>, American actor", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>(actor)"}]}, {"year": "1953", "text": "<PERSON>, Canadian ice hockey player and sportscaster", "html": "1953 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and sportscaster", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON><PERSON>, Indian sitar player and composer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Indian <a href=\"https://wikipedia.org/wiki/Sitar\" title=\"Sitar\">sitar</a> player and composer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>y"}, {"title": "<PERSON>ar", "link": "https://wikipedia.org/wiki/Sitar"}]}, {"year": "1954", "text": "<PERSON>, American author and academic", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and academic", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, English singer-songwriter, guitarist and producer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, guitarist and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1954", "text": "<PERSON>, American singer-songwriter (d. 2014)", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2014)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, American singer-songwriter (d. 2014)", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>(singer)"}]}, {"year": "1954", "text": "<PERSON>, Australian cricketer", "html": "1954 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian cricketer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American basketball player", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, American baseball player and coach", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, English educator and civil servant", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and civil servant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English educator and civil servant", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American magician and actor", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illusionist)\" title=\"<PERSON> (illusionist)\"><PERSON></a>, American magician and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(illusionist)\" title=\"<PERSON> (illusionist)\"><PERSON></a>, American magician and actor", "links": [{"title": "<PERSON> (illusionist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(illusionist)"}]}, {"year": "1956", "text": "<PERSON>, American journalist and antivirus pioneer (d. 2017)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and antivirus pioneer (d. 2017)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American journalist and antivirus pioneer (d. 2017)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON>, American bass player (d. 2004)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2004)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American bass player (d. 2004)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1956", "text": "<PERSON><PERSON><PERSON><PERSON>, Japanese wrestler (d. 1987)", "html": "1956 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler (d. 1987)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Japanese wrestler (d. 1987)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON> <PERSON><PERSON>, American wrestler", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/D._<PERSON><PERSON>_<PERSON>\" title=\"D. <PERSON><PERSON> Drake\"><PERSON><PERSON> <PERSON><PERSON></a>, American wrestler", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, English businesswoman", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English businesswoman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>rse"}]}, {"year": "1957", "text": "<PERSON>, English lawyer and politician", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Lamb\"><PERSON></a>, English lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON>, Northern Irish footballer and manager", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Northern Irish footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1957", "text": "<PERSON><PERSON>, Romanian singer and pianist (d. 2008)", "html": "1957 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian singer and pianist (d. 2008)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Romanian singer and pianist (d. 2008)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1958", "text": "<PERSON><PERSON>, American baseball player and coach", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American baseball player and coach", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, Welsh footballer and manager", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Welsh footballer and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1958", "text": "<PERSON>, American actress and poker player", "html": "1958 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and poker player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and poker player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, Canadian actor and screenwriter", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian actor and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, American baseball player, coach, and manager", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player, coach, and manager", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1959", "text": "<PERSON>, South African cricketer, manager, and lawyer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer, manager, and lawyer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)\" title=\"<PERSON> (South African cricketer)\"><PERSON></a>, South African cricketer, manager, and lawyer", "links": [{"title": "<PERSON> (South African cricketer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(South_African_cricketer)"}]}, {"year": "1959", "text": "<PERSON>-<PERSON>, American bass player, director, and producer", "html": "1959 - <a href=\"https://wikipedia.org/wiki/Victory_Tischler-<PERSON>\" title=\"Victory Tischler-Blue\"><PERSON> Tischler-<PERSON></a>, American bass player, director, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Victory_Tischler-<PERSON>\" title=\"Victory Tischler-Blue\"><PERSON>-<PERSON></a>, American bass player, director, and producer", "links": [{"title": "Victory Tischler-Blue", "link": "https://wikipedia.org/wiki/<PERSON>_T<PERSON><PERSON>-Blue"}]}, {"year": "1960", "text": "<PERSON>, American actress", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American trumpet player and composer", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American trumpet player and composer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1960", "text": "<PERSON>, American author and illustrator", "html": "1960 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and illustrator", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON>, English singer-songwriter and guitarist", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Butcher\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Butcher\"><PERSON><PERSON><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "B<PERSON><PERSON> Butcher", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Butcher"}]}, {"year": "1961", "text": "<PERSON>, Canadian wrestler", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1961", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian journalist, economist, and politician (d. 2013)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Annam%C3%A1ria_Szalai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian journalist, economist, and politician (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Annam%C3%A1ria_Szalai\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian journalist, economist, and politician (d. 2013)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Annam%C3%A1ria_Szalai"}]}, {"year": "1962", "text": "<PERSON>, Canadian author and illustrator", "html": "1962 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Canadian author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(cartoonist)\" title=\"<PERSON> (cartoonist)\"><PERSON></a>, Canadian author and illustrator", "links": [{"title": "<PERSON> (cartoonist)", "link": "https://wikipedia.org/wiki/<PERSON>_(cartoonist)"}]}, {"year": "1963", "text": "<PERSON>, American singer-songwriter and producer", "html": "1963 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, Australian actress and screenwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian actress and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON><PERSON>, Spanish-French model and actress", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-French model and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Spanish-French model and actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_de_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American guitarist and songwriter", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American guitarist and songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1964", "text": "<PERSON>, American actress, comedian and producer", "html": "1964 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American actress and producer", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1965", "text": "<PERSON><PERSON><PERSON>, German footballer and manager", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, German footballer and manager", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1965", "text": "<PERSON>, American singer-songwriter", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American attorney and politician", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American attorney and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1966", "text": "<PERSON><PERSON>, American author and playwright", "html": "1966 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\">W<PERSON></a>, American author and playwright", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON>\">W<PERSON></a>, American author and playwright", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Wil_<PERSON>"}]}, {"year": "1966", "text": "<PERSON>, American hurdler", "html": "1966 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON> (hurdler)\"><PERSON></a>, American hurdler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)\" title=\"<PERSON> (hurdler)\"><PERSON></a>, American hurdler", "links": [{"title": "<PERSON> (hurdler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(hurdler)"}]}, {"year": "1967", "text": "<PERSON><PERSON><PERSON>, Japanese author and illustrator", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Japanese author and illustrator", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>u"}]}, {"year": "1967", "text": "<PERSON>, Kentucky State Senate Majority Leader", "html": "1967 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kentucky State Senate Majority Leader", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Kentucky State Senate Majority Leader", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American singer-songwriter, actor, and producer", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, actor, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, American director, producer, and screenwriter", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1968", "text": "<PERSON>, Irish footballer (d. 2012)", "html": "1968 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer (d. 2012)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer (d. 2012)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, English singer-songwriter and guitarist", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, English singer-songwriter and guitarist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1969", "text": "<PERSON><PERSON>, Filipino singer-songwriter and actor", "html": "1969 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Filipino singer-songwriter and actor", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1970", "text": "<PERSON>, American singer-songwriter", "html": "1970 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(musician)\" title=\"<PERSON> (musician)\"><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(musician)"}]}, {"year": "1971", "text": "<PERSON>, American actor, producer, and screenwriter", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, producer, and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American businessman", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American businessman", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American actress, comedian, and producer", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress, comedian, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1971", "text": "<PERSON>, American wrestler", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American wrestler", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1971", "text": "<PERSON><PERSON>, American beauty pageant contestant", "html": "1971 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American beauty pageant contestant", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American beauty pageant contestant", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American football player", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1972", "text": "<PERSON>, American actor and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor and producer", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)"}]}, {"year": "1972", "text": "<PERSON>, Italian singer-songwriter and producer", "html": "1972 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian singer-songwriter and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, English footballer", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(footballer)\" title=\"<PERSON> (footballer)\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON> (footballer)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer)"}]}, {"year": "1973", "text": "<PERSON><PERSON>, Dutch businessman and member of the International Olympic Committee", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch businessman and member of the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Dutch businessman and member of the <a href=\"https://wikipedia.org/wiki/International_Olympic_Committee\" title=\"International Olympic Committee\">International Olympic Committee</a>", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}, {"title": "International Olympic Committee", "link": "https://wikipedia.org/wiki/International_Olympic_Committee"}]}, {"year": "1973", "text": "<PERSON>, American author and screenwriter", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American author and screenwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON>, Kazakh cyclist and manager", "html": "1973 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(sportsman)\" class=\"mw-redirect\" title=\"<PERSON> (sportsman)\"><PERSON></a>, Kazakh cyclist and manager", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(sportsman)\" class=\"mw-redirect\" title=\"<PERSON> (sportsman)\"><PERSON></a>, Kazakh cyclist and manager", "links": [{"title": "<PERSON> (sportsman)", "link": "https://wikipedia.org/wiki/<PERSON>_(sportsman)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Dutch singer-songwriter and dancer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Dutch singer-songwriter and dancer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)\" title=\"<PERSON><PERSON> (singer)\"><PERSON><PERSON></a>, Dutch singer-songwriter and dancer", "links": [{"title": "<PERSON><PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(singer)"}]}, {"year": "1974", "text": "<PERSON><PERSON>, Australian singer-songwriter, guitarist, and producer", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Australian singer-songwriter, guitarist, and producer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American lawyer and politician", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1974", "text": "<PERSON>, American lawyer and politician, 16th United States Secretary of Housing and Urban Development", "html": "1974 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer and politician, 16th <a href=\"https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development\" title=\"United States Secretary of Housing and Urban Development\">United States Secretary of Housing and Urban Development</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "United States Secretary of Housing and Urban Development", "link": "https://wikipedia.org/wiki/United_States_Secretary_of_Housing_and_Urban_Development"}]}, {"year": "1975", "text": "<PERSON>, American race car driver (d. 2013)", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American race car driver (d. 2013)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1975", "text": "<PERSON>, Australian singer-songwriter", "html": "1975 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian singer-songwriter", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ll"}]}, {"year": "1975", "text": "<PERSON><PERSON>, Nigerian actress", "html": "1975 - <a href=\"https://wikipedia.org/wiki/To<PERSON>_Olagundoye\" title=\"<PERSON><PERSON> Olagundoye\"><PERSON><PERSON></a>, Nigerian actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/To<PERSON>_<PERSON>lagundoye\" title=\"<PERSON><PERSON> Olagundoye\"><PERSON><PERSON></a>, Nigerian actress", "links": [{"title": "Toks <PERSON>e", "link": "https://wikipedia.org/wiki/Toks_Olagundoye"}]}, {"year": "1976", "text": "<PERSON><PERSON><PERSON>, Latvian soprano", "html": "1976 - <a href=\"https://wikipedia.org/wiki/El%C4%ABna_Garan%C4%8Da\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian soprano", "no_year_html": "<a href=\"https://wikipedia.org/wiki/El%C4%ABna_Garan%C4%8Da\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian soprano", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/El%C4%ABna_Garan%C4%8Da"}]}, {"year": "1976", "text": "<PERSON>, English singer-songwriter, dancer, and actress", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1976", "text": "<PERSON>, American basketball player and coach", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, American captain and politician", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American captain and politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(politician)\" title=\"<PERSON> (politician)\"><PERSON></a>, American captain and politician", "links": [{"title": "<PERSON> (politician)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(politician)"}]}, {"year": "1977", "text": "<PERSON><PERSON><PERSON>, American singer-songwriter", "html": "1977 - <a href=\"https://wikipedia.org/wiki/Musi<PERSON>_Soulchild\" title=\"Musiq Soulchild\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>si<PERSON>_Soulchild\" title=\"Musiq Soulchild\"><PERSON><PERSON><PERSON></a>, American singer-songwriter", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Musi<PERSON>_Soulchild"}]}, {"year": "1978", "text": "<PERSON>, American basketball player and coach", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player and coach", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON>, German runner", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German runner", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1978", "text": "<PERSON><PERSON>, Mexican wrestler", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)\" title=\"<PERSON><PERSON> (wrestler)\"><PERSON><PERSON></a>, Mexican wrestler", "links": [{"title": "<PERSON><PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(wrestler)"}]}, {"year": "1978", "text": "<PERSON>, American lawyer, politician, and LGBT activist", "html": "1978 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and LGBT activist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American lawyer, politician, and LGBT activist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON>, French singer", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_(singer)\" title=\"<PERSON> (singer)\"><PERSON></a>, French singer", "links": [{"title": "<PERSON> (singer)", "link": "https://wikipedia.org/wiki/<PERSON>_(singer)"}]}, {"year": "1979", "text": "<PERSON>, American baseball player", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1979", "text": "<PERSON><PERSON>, American rapper, singer, and songwriter", "html": "1979 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R<PERSON>\" title=\"Flo Rida\"><PERSON><PERSON></a>, American rapper, singer, and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_R<PERSON>\" title=\"Flo Rida\"><PERSON><PERSON></a>, American rapper, singer, and songwriter", "links": [{"title": "Flo Rida", "link": "https://wikipedia.org/wiki/Flo_Rida"}]}, {"year": "1980", "text": "<PERSON><PERSON>, Czech ice hockey player", "html": "1980 - <a href=\"https://wikipedia.org/wiki/Patrik_%C5%A0tefan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Patrik_%C5%A0tefan\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Czech ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Patrik_%C5%A0tefan"}]}, {"year": "1980", "text": "<PERSON>, Dutch wheelchair racer", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch wheelchair racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Dutch wheelchair racer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON>, Chinese actress, singer, and producer", "html": "1981 - <a href=\"https://wikipedia.org/wiki/Fan_Bingbing\" title=\"Fan Bingbing\"><PERSON></a>, Chinese actress, singer, and producer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fan_Bingbing\" title=\"Fan Bingbing\"><PERSON></a>, Chinese actress, singer, and producer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Fan_Bingbing"}]}, {"year": "1981", "text": "<PERSON>, American actress", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1981", "text": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, Virgin Islander sprinter", "html": "1981 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON>-<PERSON></a>, Virgin Islander sprinter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Virgin Islander sprinter", "links": [{"title": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>-<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, English footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON>, Italian rugby player", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1982", "text": "<PERSON><PERSON>, German footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/Fiete_Sykora\" title=\"Fiete Sykora\"><PERSON><PERSON></a>, German footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fiete_Sykora\" title=\"Fiete Sykora\"><PERSON><PERSON></a>, German footballer", "links": [{"title": "Fiete Sykora", "link": "https://wikipedia.org/wiki/Fiete_Sykora"}]}, {"year": "1982", "text": "<PERSON>, Scottish footballer", "html": "1982 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Scottish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)\" title=\"<PERSON> (footballer, born 1982)\"><PERSON></a>, Scottish footballer", "links": [{"title": "<PERSON> (footballer, born 1982)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(footballer,_born_1982)"}]}, {"year": "1983", "text": "<PERSON>, New Zealand rugby player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, New Zealand rugby player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON>, Belgian singer and pianist", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer and pianist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Belgian singer and pianist", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON>, Canadian wrestler", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>(wrestler)\" title=\"<PERSON> (wrestler)\"><PERSON></a>, Canadian wrestler", "links": [{"title": "<PERSON> (wrestler)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(wrestler)"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, Zimbabwean swimmer", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Kirsty_Coventry\" title=\"Kirsty Coventry\"><PERSON><PERSON><PERSON></a>, Zimbabwean swimmer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Kirsty_Coventry\" title=\"Kirsty Coventry\"><PERSON><PERSON><PERSON></a>, Zimbabwean swimmer", "links": [{"title": "Kirsty Coventry", "link": "https://wikipedia.org/wiki/Kirsty_Coventry"}]}, {"year": "1983", "text": "<PERSON>, American baseball player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1983", "text": "<PERSON><PERSON><PERSON>, American football player", "html": "1983 - <a href=\"https://wikipedia.org/wiki/Legedu_Naanee\" title=\"Legedu Naanee\"><PERSON><PERSON><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Legedu_Naanee\" title=\"Legedu Naanee\"><PERSON><PERSON><PERSON></a>, American football player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Leged<PERSON>_<PERSON>e"}]}, {"year": "1984", "text": "<PERSON>, American singer-songwriter, dancer, and actress", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter, dancer, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON><PERSON><PERSON>, Brazilian footballer", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Sergin<PERSON>_Catarinense\" title=\"Serginho Catarinense\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Sergin<PERSON>_Catarinense\" title=\"Sergin<PERSON> Catarinense\"><PERSON><PERSON><PERSON></a>, Brazilian footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Sergin<PERSON>_Catarinense"}]}, {"year": "1984", "text": "<PERSON>, Georgian-English singer-songwriter and guitarist", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-English singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Georgian-English singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American baseball player", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)\" title=\"<PERSON> (baseball)\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON> (baseball)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(baseball)"}]}, {"year": "1985", "text": "<PERSON>, English actor", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1985", "text": "<PERSON>, American actress", "html": "1985 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American baseball player", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON>, American actor", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1986", "text": "<PERSON><PERSON>, American actress and singer", "html": "1986 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American actress and singer", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, Turkish actress", "html": "1987 - <a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>%C4%9Fur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Me<PERSON>_<PERSON>%C4%9Fur\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish actress", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Merve_Bolu%C4%9Fur"}]}, {"year": "1987", "text": "<PERSON>, Irish footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Irish footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, Cameroonian footballer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Cameroonian footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, American internet personality and filmmaker", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality and filmmaker", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American internet personality and filmmaker", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON><PERSON>, South African cyclist (d. 2013)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cyclist (d. 2013)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, South African cyclist (d. 2013)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>er"}]}, {"year": "1987", "text": "<PERSON>, American dancer and choreographer", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wall\"><PERSON></a>, American dancer and choreographer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Wall\"><PERSON></a>, American dancer and choreographer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1988", "text": "<PERSON>, American singer-songwriter, guitarist, and actress", "html": "1988 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Geiger\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Geiger\"><PERSON></a>, American singer-songwriter, guitarist, and actress", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American actress", "html": "1989 - <a href=\"https://wikipedia.org/wiki/1989\" title=\"1989\">1989</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "no_year_html": "<a href=\"https://wikipedia.org/wiki/1989\" title=\"1989\">1989</a> - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress", "links": [{"title": "1989", "link": "https://wikipedia.org/wiki/1989"}, {"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON>, American baseball player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1989", "text": "<PERSON><PERSON><PERSON>, Venezuelan footballer", "html": "1989 - <a href=\"https://wikipedia.org/wiki/Salom%C3%B3n_Rond%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Salom%C3%B3n_Rond%C3%B3n\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Venezuelan footballer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Salom%C3%B3n_Rond%C3%B3n"}]}, {"year": "1989", "text": "<PERSON>, Canadian ice hockey player", "html": "1989 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1991", "text": "<PERSON><PERSON><PERSON>, Latvian tennis player", "html": "1991 - <a href=\"https://wikipedia.org/wiki/Di%C4%81na_Buk%C4%81jeva\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian tennis player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Di%C4%81na_Buk%C4%81jeva\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Latvian tennis player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Di%C4%81na_Buk%C4%81jeva"}]}, {"year": "1991", "text": "<PERSON>, Canadian figure skater (d. 2023)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater (d. 2023)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>(figure_skater)\" title=\"<PERSON> (figure skater)\"><PERSON></a>, Canadian figure skater (d. 2023)", "links": [{"title": "<PERSON> (figure skater)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(figure_skater)"}]}, {"year": "1991", "text": "<PERSON>, English motorcycle racer", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, English motorcycle racer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)\" title=\"<PERSON> (motorcyclist)\"><PERSON></a>, English motorcycle racer", "links": [{"title": "<PERSON> (motorcyclist)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(motorcyclist)"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, Lithuanian basketball player", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Vytenis_%C4%8Ci%C5%BEauskas\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Vytenis_%C4%8Ci%C5%B<PERSON>aus<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Lithuanian basketball player", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Vytenis_%C4%8Ci%C5%BEauskas"}]}, {"year": "1992", "text": "<PERSON>, American singer-songwriter and guitarist", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American singer-songwriter and guitarist", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, English singer-songwriter and actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON>, American actor", "html": "1992 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1993", "text": "<PERSON>, American record producer and songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Metro_Boomin\" title=\"Metro Boomin\">Metro Boomin</a>, American record producer and songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Metro_Boomin\" title=\"Metro Boomin\">Metro Boomin</a>, American record producer and songwriter", "links": [{"title": "Metro Boomin", "link": "https://wikipedia.org/wiki/Metro_Boomin"}]}, {"year": "1993", "text": "<PERSON>, English footballer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>ram"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON>, American golfer", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, American golfer", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>hambeau"}]}, {"year": "1993", "text": "<PERSON><PERSON>, Japanese-Australian singer-songwriter", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese-Australian singer-songwriter", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)\" title=\"<PERSON><PERSON> (musician)\"><PERSON><PERSON></a>, Japanese-Australian singer-songwriter", "links": [{"title": "<PERSON><PERSON> (musician)", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_(musician)"}]}, {"year": "1994", "text": "<PERSON>, Canadian ice hockey player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1994", "text": "<PERSON><PERSON><PERSON><PERSON>, Serbian footballer", "html": "1994 - <a href=\"https://wikipedia.org/wiki/Aleksandar_Mitrovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Aleksandar_Mitrovi%C4%87\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Serbian footballer", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Aleksandar_Mitrovi%C4%87"}]}, {"year": "1994", "text": "<PERSON>, Australian rugby league player", "html": "1994 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1995", "text": "<PERSON>, American basketball player", "html": "1995 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1997", "text": "<PERSON>, American basketball player", "html": "1997 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American basketball player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1999", "text": "<PERSON>, American ice hockey player", "html": "1999 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American ice hockey player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, American football player", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American football player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2000", "text": "<PERSON>, English footballer", "html": "2000 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English footballer", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON><PERSON><PERSON><PERSON>, Israeli Olympic taekwondo bronze medalist", "html": "2001 - <a href=\"https://wikipedia.org/wiki/Avishag_<PERSON>\" title=\"Avishag <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli Olympic <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">taekwondo</a> bronze medalist", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Avisha<PERSON>_<PERSON>\" title=\"Avishag <PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Israeli Olympic <a href=\"https://wikipedia.org/wiki/Taekwondo\" title=\"Taekwondo\">taekwondo</a> bronze medalist", "links": [{"title": "Avishag Semberg", "link": "https://wikipedia.org/wiki/Avishag_Semberg"}, {"title": "Taekwondo", "link": "https://wikipedia.org/wiki/Taekwondo"}]}, {"year": "2003", "text": "<PERSON>, Australian rugby league player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2003", "text": "<PERSON>, Australian rugby league player", "html": "2003 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Australian rugby league player", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}], "Deaths": [{"year": "307", "text": "<PERSON><PERSON><PERSON>, Roman emperor", "html": "307 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rius_<PERSON>us\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Valerius <PERSON>us\"><PERSON><PERSON><PERSON></a>, Roman emperor", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>rius_<PERSON>us\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON> Valerius <PERSON>us\"><PERSON><PERSON><PERSON></a>, Roman emperor", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>verus"}]}, {"year": "655", "text": "<PERSON>", "html": "655 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope <PERSON>\"><PERSON> I</a>", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Martin I\"><PERSON> I</a>", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1087", "text": "<PERSON> (b. 1026)", "html": "1087 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON> <PERSON></a> (b. 1026)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"Pope Victor <PERSON>\"><PERSON></a> (b. 1026)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1100", "text": "<PERSON><PERSON> of Constance, German priest and historian (b. 1054)", "html": "1100 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Constance\" title=\"<PERSON><PERSON> of Constance\"><PERSON><PERSON> of Constance</a>, German priest and historian (b. 1054)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Constance\" title=\"<PERSON><PERSON> of Constance\"><PERSON><PERSON> of Constance</a>, German priest and historian (b. 1054)", "links": [{"title": "<PERSON><PERSON> of Constance", "link": "https://wikipedia.org/wiki/<PERSON>old_of_Constance"}]}, {"year": "1122", "text": "<PERSON><PERSON> of Savigny, Catholic French saint and itinerant preacher (b. 1060)", "html": "1122 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_of_Savigny\" title=\"<PERSON><PERSON> of Savigny\"><PERSON><PERSON> of Savigny</a>, Catholic French saint and itinerant preacher (b. 1060)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_Savigny\" title=\"<PERSON><PERSON> of Savigny\"><PERSON><PERSON> of Savigny</a>, Catholic French saint and itinerant preacher (b. 1060)", "links": [{"title": "<PERSON><PERSON> of Savigny", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1226", "text": "<PERSON><PERSON><PERSON>, Roman ecclesiastical politician", "html": "1226 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman ecclesiastical politician", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Roman ecclesiastical politician", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1343", "text": "<PERSON> of Navarre (b. 1306)", "html": "1343 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1306)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre\" title=\"<PERSON> of Navarre\"><PERSON> of Navarre</a> (b. 1306)", "links": [{"title": "<PERSON> of Navarre", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Navarre"}]}, {"year": "1345", "text": "<PERSON>, Duke of Brittany (b. 1295)", "html": "1345 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1295)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany\" title=\"<PERSON>, Duke of Brittany\"><PERSON>, Duke of Brittany</a> (b. 1295)", "links": [{"title": "<PERSON>, Duke of Brittany", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_Duke_of_Brittany"}]}, {"year": "1360", "text": "<PERSON>, 1st Earl of Northampton (b. 1319)", "html": "1360 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northampton\" title=\"<PERSON>, 1st Earl of Northampton\"><PERSON>, 1st Earl of Northampton</a> (b. 1319)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>,_1st_Earl_of_Northampton\" title=\"<PERSON>, 1st Earl of Northampton\"><PERSON>, 1st Earl of Northampton</a> (b. 1319)", "links": [{"title": "<PERSON>, 1st Earl of Northampton", "link": "https://wikipedia.org/wiki/<PERSON>,_1st_Earl_of_Northampton"}]}, {"year": "1380", "text": "<PERSON> of France (b. 1338)", "html": "1380 - <a href=\"https://wikipedia.org/wiki/Charles_V_of_France\" title=\"Charles V of France\"><PERSON> of France</a> (b. 1338)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Charles_V_of_France\" title=\"Charles V of France\"><PERSON> of France</a> (b. 1338)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Charles_V_of_France"}]}, {"year": "1394", "text": "<PERSON><PERSON><PERSON> (b. 1342)", "html": "1394 - <a href=\"https://wikipedia.org/wiki/Antipope_Clement_VII\" title=\"Antipope Clement VII\">Antipope Clement VII</a> (b. 1342)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Antipope_Clement_VII\" title=\"Antipope Clement VII\">Antipope Clement VII</a> (b. 1342)", "links": [{"title": "Antipop<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1406", "text": "<PERSON><PERSON><PERSON>, Metropolitan of Moscow (b. 1336)", "html": "1406 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Metropolitan_of_Moscow\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Metropolitan of Moscow\"><PERSON><PERSON><PERSON>, Metropolitan of Moscow</a> (b. 1336)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Metropolitan_of_Moscow\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON>, Metropolitan of Moscow\"><PERSON><PERSON><PERSON>, Metropolitan of Moscow</a> (b. 1336)", "links": [{"title": "<PERSON><PERSON><PERSON>, Metropolitan of Moscow", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>,_Metropolitan_of_Moscow"}]}, {"year": "1498", "text": "<PERSON><PERSON>, Spanish friar (b. 1420)", "html": "1498 - <a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Torquemada\" title=\"<PERSON><PERSON> Torquemada\"><PERSON><PERSON></a>, Spanish friar (b. 1420)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tom%C3%A1s_de_Torquemada\" title=\"<PERSON><PERSON> Torquemada\"><PERSON><PERSON></a>, Spanish friar (b. 1420)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Tom%C3%<PERSON><PERSON>_de_<PERSON>mada"}]}, {"year": "1581", "text": "<PERSON>, notorious German bandit (date of birth unknown)", "html": "1581 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, notorious German bandit (date of birth unknown)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, notorious German bandit (date of birth unknown)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1583", "text": "<PERSON>, queen of <PERSON> of Sweden (b. 1526)", "html": "1583 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1526)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, queen of <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden\" title=\"<PERSON> of Sweden\"><PERSON> of Sweden</a> (b. 1526)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "<PERSON> of Sweden", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_Sweden"}]}, {"year": "1589", "text": "<PERSON>, Belgian theologian and academic (b. 1513)", "html": "1589 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian theologian and academic (b. 1513)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Belgian theologian and academic (b. 1513)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1607", "text": "<PERSON>, English-Scottish princess (b. 1605)", "html": "1607 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)\" title=\"<PERSON> (1605-1607)\"><PERSON></a>, English-Scottish princess (b. 1605)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)\" title=\"<PERSON> (1605-1607)\"><PERSON></a>, English-Scottish princess (b. 1605)", "links": [{"title": "<PERSON> (1605-1607)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(1605%E2%80%931607)"}]}, {"year": "1672", "text": "<PERSON>, English poet (b. 1612)", "html": "1672 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1612)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English poet (b. 1612)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1701", "text": "<PERSON> of England (b. 1633)", "html": "1701 - <a href=\"https://wikipedia.org/wiki/<PERSON>_II_of_England\" title=\"James II of England\"><PERSON> II of England</a> (b. 1633)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England\" title=\"James II of England\"><PERSON> of England</a> (b. 1633)", "links": [{"title": "<PERSON> of <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_of_England"}]}, {"year": "1736", "text": "<PERSON>, Polish-Dutch physicist and engineer, invented the thermometer (b. 1686)", "html": "1736 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>heit\" title=\"<PERSON>\"><PERSON></a>, Polish-Dutch physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Mercury-in-glass_thermometer\" title=\"Mercury-in-glass thermometer\">thermometer</a> (b. 1686)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Polish-Dutch physicist and engineer, invented the <a href=\"https://wikipedia.org/wiki/Mercury-in-glass_thermometer\" title=\"Mercury-in-glass thermometer\">thermometer</a> (b. 1686)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>heit"}, {"title": "Mercury-in-glass thermometer", "link": "https://wikipedia.org/wiki/Mercury-in-glass_thermometer"}]}, {"year": "1792", "text": "<PERSON><PERSON><PERSON><PERSON>, Vietnamese emperor (b. 1753)", "html": "1792 - <a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1753)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87\" class=\"mw-redirect\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Vietnamese emperor (b. 1753)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Nguy%E1%BB%85n_Hu%E1%BB%87"}]}, {"year": "1803", "text": "<PERSON>, French explorer, hydrographer, and cartographer (b. 1754)", "html": "1803 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer, hydrographer, and cartographer (b. 1754)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, French explorer, hydrographer, and cartographer (b. 1754)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1819", "text": "<PERSON>, American physician and surgeon (b. 1744)", "html": "1819 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (b. 1744)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physician and surgeon (b. 1744)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1824", "text": "<PERSON> (b. 1755)", "html": "1824 - <a href=\"https://wikipedia.org/wiki/Louis_XVIII_of_France\" class=\"mw-redirect\" title=\"Louis XVIII of France\"><PERSON> XVIII of France</a> (b. 1755)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_XVIII_of_France\" class=\"mw-redirect\" title=\"Louis XVIII of France\"><PERSON> XVIII of France</a> (b. 1755)", "links": [{"title": "<PERSON> of France", "link": "https://wikipedia.org/wiki/Louis_XVIII_of_France"}]}, {"year": "1843", "text": "<PERSON><PERSON><PERSON><PERSON>, Canadian businessman and politician (b. 1770)", "html": "1843 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (b. 1770)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Canadian businessman and politician (b. 1770)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "1845", "text": "<PERSON>, Irish poet and publisher (b. 1814)", "html": "1845 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Young_Irelander)\" title=\"<PERSON> (Young Irelander)\"><PERSON></a>, Irish poet and publisher (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Young_Irelander)\" title=\"<PERSON> (Young Irelander)\"><PERSON></a>, Irish poet and publisher (b. 1814)", "links": [{"title": "<PERSON> (Young Irelander)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(Young_Irelander)"}]}, {"year": "1865", "text": "<PERSON>, Danish general (b. 1792)", "html": "1865 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish general (b. 1792)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Danish general (b. 1792)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1887", "text": "<PERSON><PERSON><PERSON>, Japanese sumo wrestler, the 14th <PERSON><PERSON><PERSON><PERSON> (b. 1841)", "html": "1887 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_Namiemon\" title=\"<PERSON><PERSON><PERSON> Namiemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 14th <a href=\"https://wikipedia.org/wiki/Makuuchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1841)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON> Namiemon\"><PERSON><PERSON><PERSON></a>, Japanese sumo wrestler, the 14th <a href=\"https://wikipedia.org/wiki/Ma<PERSON>uchi#Yokozuna\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a> (b. 1841)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>mon"}, {"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Makuuchi#Yokozuna"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON><PERSON>, Brazilian composer (b. 1836)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian composer (b. 1836)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Brazilian composer (b. 1836)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B4<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1896", "text": "<PERSON><PERSON><PERSON>, Greek jurist and politician, Foreign Minister of Greece (b. 1814)", "html": "1896 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Foreign Minister of Greece</a> (b. 1814)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Greek jurist and politician, <a href=\"https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece\" title=\"List of foreign ministers of Greece\">Foreign Minister of Greece</a> (b. 1814)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "List of foreign ministers of Greece", "link": "https://wikipedia.org/wiki/List_of_foreign_ministers_of_Greece"}]}, {"year": "1898", "text": "<PERSON>, Puerto Rican surgeon and politician (b. 1827)", "html": "1898 - <a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances\" title=\"Ramón Emeterio Betances\"><PERSON></a>, Puerto Rican surgeon and politician (b. 1827)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances\" title=\"Ramón Emeterio Betances\"><PERSON></a>, Puerto Rican surgeon and politician (b. 1827)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ram%C3%B3n_Emeterio_Betances"}]}, {"year": "1911", "text": "<PERSON>, English-French mountaineer, explorer, and author (b. 1840)", "html": "1911 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French mountaineer, explorer, and author (b. 1840)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English-French mountaineer, explorer, and author (b. 1840)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1914", "text": "<PERSON><PERSON> <PERSON><PERSON>, American businessman (b. 1843)", "html": "1914 - <a href=\"https://wikipedia.org/wiki/C._X._<PERSON>\" title=\"C. X. La<PERSON>bee\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman (b. 1843)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/C._X._<PERSON>\" title=\"C. X. Larrabee\"><PERSON><PERSON> <PERSON><PERSON></a>, American businessman (b. 1843)", "links": [{"title": "C. <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/C._<PERSON>._<PERSON>bee"}]}, {"year": "1919", "text": "<PERSON>, Ukrainian anarchist partisan leader (b. 1885)", "html": "1919 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian anarchist partisan leader (b. 1885)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Ukrainian anarchist partisan leader (b. 1885)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1925", "text": "<PERSON>, Czech-Austrian composer (b. 1873)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/Leo_Fall\" title=\"Leo Fall\"><PERSON></a>, Czech-Austrian composer (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Leo_Fall\" title=\"Leo Fall\"><PERSON></a>, Czech-Austrian composer (b. 1873)", "links": [{"title": "Leo Fall", "link": "https://wikipedia.org/wiki/Leo_Fall"}]}, {"year": "1925", "text": "<PERSON>, Russian physicist and mathematician (b. 1888)", "html": "1925 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (b. 1888)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian physicist and mathematician (b. 1888)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1931", "text": "<PERSON>, Libyan theorist and educator (b. 1862)", "html": "1931 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Libyan theorist and educator (b. 1862)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, Libyan theorist and educator (b. 1862)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "1932", "text": "Millicent <PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>, British stage and screen actress (b. 1908)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Entwistle\" title=\"<PERSON><PERSON> Entwistle\"><PERSON>icent <PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON><PERSON></a>, British stage and screen actress (b. 1908)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_Entwistle\" title=\"<PERSON><PERSON> Entwistle\"><PERSON><PERSON>nt <PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON><PERSON><PERSON></a>, British stage and screen actress (b. 1908)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Peg_Entwistle"}]}, {"year": "1932", "text": "<PERSON>, Indian-English physician and mathematician, Nobel Prize laureate (b. 1857)", "html": "1932 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English physician and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English physician and mathematician, <a href=\"https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine\" title=\"Nobel Prize in Physiology or Medicine\">Nobel Prize</a> laureate (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Nobel Prize in Physiology or Medicine", "link": "https://wikipedia.org/wiki/Nobel_Prize_in_Physiology_or_Medicine"}]}, {"year": "1933", "text": "<PERSON>, American baseball player and manager (b. 1857)", "html": "1933 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1857)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American baseball player and manager (b. 1857)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1936", "text": "<PERSON><PERSON><PERSON>, French physician and explorer (b. 1867)", "html": "1936 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (b. 1867)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, French physician and explorer (b. 1867)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>"}]}, {"year": "1940", "text": "<PERSON>, 2nd Baron <PERSON>, English-Scottish politician, 8th Governor of Queensland (b. 1860)", "html": "1940 - <a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English-Scottish politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (b. 1860)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>,_2nd_Baron_<PERSON>\" title=\"<PERSON>, 2nd Baron <PERSON>\"><PERSON>, 2nd Baron <PERSON></a>, English-Scottish politician, 8th <a href=\"https://wikipedia.org/wiki/Governor_of_Queensland\" title=\"Governor of Queensland\">Governor of Queensland</a> (b. 1860)", "links": [{"title": "<PERSON><PERSON><PERSON>, 2nd Baron <PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>,_2nd_Baron_<PERSON>"}, {"title": "Governor of Queensland", "link": "https://wikipedia.org/wiki/Governor_of_Queensland"}]}, {"year": "1944", "text": "<PERSON>, German journalist and politician, 11th Chancellor of Germany (b. 1870)", "html": "1944 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1870)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German journalist and politician, 11th <a href=\"https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)\" class=\"mw-redirect\" title=\"Chancellor of Germany (German Reich)\">Chancellor of Germany</a> (b. 1870)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Chancellor of Germany (German Reich)", "link": "https://wikipedia.org/wiki/Chancellor_of_Germany_(German_Reich)"}]}, {"year": "1945", "text": "<PERSON>, Irish tenor and actor (b. 1884)", "html": "1945 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Irish tenor and actor (b. 1884)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)\" title=\"<PERSON> (tenor)\"><PERSON></a>, Irish tenor and actor (b. 1884)", "links": [{"title": "<PERSON> (tenor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(tenor)"}]}, {"year": "1946", "text": "<PERSON>, English physicist, astronomer, and mathematician (b. 1877)", "html": "1946 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist, astronomer, and mathematician (b. 1877)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" class=\"mw-redirect\" title=\"<PERSON>\"><PERSON></a>, English physicist, astronomer, and mathematician (b. 1877)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "1950", "text": "<PERSON>, American actor (b. 1881)", "html": "1950 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cordoba\"><PERSON></a>, American actor (b. 1881)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Cordoba\"><PERSON></a>, American actor (b. 1881)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Pedro_<PERSON>_<PERSON>"}]}, {"year": "1955", "text": "<PERSON>, Indian-English journalist and politician, Secretary of State for the Colonies (b. 1873)", "html": "1955 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1873)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Indian-English journalist and politician, <a href=\"https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies\" title=\"Secretary of State for the Colonies\">Secretary of State for the Colonies</a> (b. 1873)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_Amery"}, {"title": "Secretary of State for the Colonies", "link": "https://wikipedia.org/wiki/Secretary_of_State_for_the_Colonies"}]}, {"year": "1961", "text": "<PERSON>, Turkish politician, 15th Turkish Minister of Finance (b. 1915)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Finance Ministers of Turkey\">Turkish Minister of Finance</a> (b. 1915)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Turkish politician, 15th <a href=\"https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Finance Ministers of Turkey\">Turkish Minister of Finance</a> (b. 1915)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "List of Finance Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Finance_Ministers_of_Turkey"}]}, {"year": "1961", "text": "<PERSON><PERSON>, Turkish diplomat and politician, 21st Deputy Prime Minister of Turkey (b. 1910)", "html": "1961 - <a href=\"https://wikipedia.org/wiki/Fatin_R%C3%BC%C5%9Ft%C3%BC_Zorlu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish diplomat and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Fatin_R%C3%BC%C5%9Ft%C3%BC_Zorlu\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Turkish diplomat and politician, 21st <a href=\"https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey\" class=\"mw-redirect\" title=\"List of Deputy Prime Ministers of Turkey\">Deputy Prime Minister of Turkey</a> (b. 1910)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Fatin_R%C3%BC%C5%9Ft%C3%BC_Zorlu"}, {"title": "List of Deputy Prime Ministers of Turkey", "link": "https://wikipedia.org/wiki/List_of_Deputy_Prime_Ministers_of_Turkey"}]}, {"year": "1965", "text": "<PERSON><PERSON>, North Korean composer and conductor (b. 1906)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/Ahn_<PERSON><PERSON>-tai\" title=\"Ahn <PERSON>-tai\"><PERSON><PERSON>-<PERSON></a>, North Korean composer and conductor (b. 1906)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ahn_<PERSON><PERSON>-tai\" title=\"Ahn <PERSON>-tai\"><PERSON><PERSON>-ta<PERSON></a>, North Korean composer and conductor (b. 1906)", "links": [{"title": "<PERSON><PERSON>-tai", "link": "https://wikipedia.org/wiki/Ahn_Eak-tai"}]}, {"year": "1965", "text": "<PERSON>, American animator and producer (b. 1886)", "html": "1965 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer (b. 1886)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American animator and producer (b. 1886)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1973", "text": "<PERSON><PERSON><PERSON>, Chilean singer-songwriter, teacher and theatre director (b. 1932)", "html": "1973 - <a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean singer-songwriter, teacher and theatre director (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/V%C3%<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Chilean singer-songwriter, teacher and theatre director (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/V%C3%ADctor_Jara"}]}, {"year": "1976", "text": "<PERSON><PERSON>, Brazilian feminist and scientist (b. 1894)", "html": "1976 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian feminist and scientist (b. 1894)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian feminist and scientist (b. 1894)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, English singer-songwriter and guitarist  (b. 1947)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English singer-songwriter and guitarist (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1977", "text": "<PERSON>, Greek operatic soprano (b. 1923)", "html": "1977 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek operatic soprano (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Greek operatic soprano (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Maria_<PERSON>"}]}, {"year": "1980", "text": "<PERSON>, Swiss psychologist and philosopher (b. 1896)", "html": "1980 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and philosopher (b. 1896)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Swiss psychologist and philosopher (b. 1896)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1984", "text": "<PERSON>, French engineer and fashion designer, created the bikini (b. 1897)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/Louis_<PERSON>%C3%A9ard\" title=\"<PERSON>\"><PERSON></a>, French engineer and fashion designer, created the <a href=\"https://wikipedia.org/wiki/Bikini\" title=\"Bikini\">bikini</a> (b. 1897)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Louis_<PERSON>%C3%A9ard\" title=\"<PERSON>\"><PERSON></a>, French engineer and fashion designer, created the <a href=\"https://wikipedia.org/wiki/Bikini\" title=\"Bikini\">bikini</a> (b. 1897)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Louis_R%C3%A9ard"}, {"title": "Bikini", "link": "https://wikipedia.org/wiki/Bikini"}]}, {"year": "1984", "text": "<PERSON>, American novelist, poet, and short story writer (b. 1935)", "html": "1984 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American novelist, poet, and short story writer (b. 1935)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1987", "text": "<PERSON>, English soldier and politician, Governor of Southern Rhodesia (b. 1920)", "html": "1987 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English soldier and politician, <a href=\"https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia\" title=\"Governor of Southern Rhodesia\">Governor of Southern Rhodesia</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Governor of Southern Rhodesia", "link": "https://wikipedia.org/wiki/Governor_of_Southern_Rhodesia"}]}, {"year": "1991", "text": "<PERSON>, Russian-American ballerina (b. 1895)", "html": "1991 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (b. 1895)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian-American ballerina (b. 1895)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "1992", "text": "<PERSON><PERSON><PERSON>, American journalist and politician (b. 1910)", "html": "1992 - <a href=\"https://wikipedia.org/wiki/Millicent_Fenwick\" title=\"Millicent Fenwick\"><PERSON><PERSON><PERSON></a>, American journalist and politician (b. 1910)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Millicent_Fenwick\" title=\"Millicent Fenwick\"><PERSON><PERSON><PERSON></a>, American journalist and politician (b. 1910)", "links": [{"title": "<PERSON><PERSON>nt <PERSON>", "link": "https://wikipedia.org/wiki/Millicent_Fenwick"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Czech conductor (b. 1913)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_J%C3%ADlek\" title=\"František Jílek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor (b. 1913)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Franti%C5%A1ek_J%C3%ADlek\" title=\"František Jílek\"><PERSON><PERSON><PERSON><PERSON></a>, Czech conductor (b. 1913)", "links": [{"title": "František Jílek", "link": "https://wikipedia.org/wiki/Franti%C5%A1ek_J%C3%ADlek"}]}, {"year": "1993", "text": "<PERSON><PERSON><PERSON><PERSON>, Australian poet and activist (b. 1920)", "html": "1993 - <a href=\"https://wikipedia.org/wiki/<PERSON>odger<PERSON>_Noonuccal\" title=\"Oodgeroo Noonuccal\"><PERSON><PERSON><PERSON><PERSON></a>, Australian poet and activist (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>od<PERSON><PERSON>_Noonuccal\" title=\"Oodgeroo Noonuccal\"><PERSON><PERSON><PERSON><PERSON></a>, Australian poet and activist (b. 1920)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Oodger<PERSON>_<PERSON>uccal"}]}, {"year": "1996", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>, American intelligence officer and diplomat, 6th United States National Security Advisor (b. 1919)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American intelligence officer and diplomat, 6th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a> (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON><PERSON></a>, American intelligence officer and diplomat, 6th <a href=\"https://wikipedia.org/wiki/National_Security_Advisor_(United_States)\" title=\"National Security Advisor (United States)\">United States National Security Advisor</a> (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"title": "National Security Advisor (United States)", "link": "https://wikipedia.org/wiki/National_Security_Advisor_(United_States)"}]}, {"year": "1996", "text": "<PERSON>, American actor, dancer, and director (b. 1920)", "html": "1996 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and director (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actor, dancer, and director (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2001", "text": "<PERSON>, American producer (b. 1918)", "html": "2001 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer (b. 1918)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American producer (b. 1918)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2002", "text": "<PERSON>, American actor (b. 1911)", "html": "2002 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1911)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(actor)\" title=\"<PERSON> (actor)\"><PERSON></a>, American actor (b. 1911)", "links": [{"title": "<PERSON> (actor)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>(actor)"}]}, {"year": "2003", "text": "<PERSON><PERSON>, American singer-songwriter (b. 1921)", "html": "2003 - <a href=\"https://wikipedia.org/wiki/She<PERSON>_<PERSON>ooley\" title=\"She<PERSON> Wooley\"><PERSON><PERSON></a>, American singer-songwriter (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>ooley\" title=\"<PERSON><PERSON> Wooley\"><PERSON><PERSON></a>, American singer-songwriter (b. 1921)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/She<PERSON>_Wooley"}]}, {"year": "2004", "text": "<PERSON>, American-English poet and author (b. 1954)", "html": "2004 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet and author (b. 1954)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American-English poet and author (b. 1954)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, Canadian horn player, composer, and educator (b. 1922)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian horn player, composer, and educator (b. 1922)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian horn player, composer, and educator (b. 1922)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2005", "text": "<PERSON>, American physicist and academic, invented the laser (b. 1920)", "html": "2005 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American physicist and academic, invented the <a href=\"https://wikipedia.org/wiki/Laser\" title=\"Laser\">laser</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Laser", "link": "https://wikipedia.org/wiki/Laser"}]}, {"year": "2006", "text": "<PERSON>, Canadian ice hockey player and coach (b. 1925)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Canadian ice hockey player and coach (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2006", "text": "<PERSON><PERSON><PERSON><PERSON>, Hungarian tennis player and coach (b. 1924)", "html": "2006 - <a href=\"https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy\" title=\"Zsuz<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player and coach (b. 1924)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy\" title=\"Zsuzsa <PERSON>öczy\"><PERSON><PERSON><PERSON><PERSON></a>, Hungarian tennis player and coach (b. 1924)", "links": [{"title": "Zsuzsa <PERSON>y", "link": "https://wikipedia.org/wiki/Zsuzsa_K%C3%B6rm%C3%B6czy"}]}, {"year": "2007", "text": "<PERSON>, American engineer and author (b. 1948)", "html": "2007 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American engineer and author (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Robert <PERSON>\"><PERSON></a>, American engineer and author (b. 1948)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Robert_<PERSON>"}]}, {"year": "2008", "text": "<PERSON>, American songwriter and producer (b. 1940)", "html": "2008 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American songwriter and producer (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON><PERSON>, American philosopher and academic (b. 1942)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brand\"><PERSON><PERSON></a>, American philosopher and academic (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> Brand\"><PERSON><PERSON></a>, American philosopher and academic (b. 1942)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2009", "text": "<PERSON>, Austrian conductor (b. 1921)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rzendorfer\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor (b. 1921)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>%C3%A4rzendorfer\" title=\"<PERSON>\"><PERSON></a>, Austrian conductor (b. 1921)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Ernst_M%C3%A4<PERSON><PERSON>orfer"}]}, {"year": "2009", "text": "<PERSON>, American singer-songwriter (b. 1936)", "html": "2009 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, American singer-songwriter (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Travers\"><PERSON></a>, American singer-songwriter (b. 1936)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, American educator and bandleader (b. 1953)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON>\"><PERSON></a>, American educator and bandleader (b. 1953)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"George <PERSON> Parks\"><PERSON></a>, American educator and bandleader (b. 1953)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}]}, {"year": "2010", "text": "<PERSON>, English footballer (b. 1933)", "html": "2010 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Jim Towers\"><PERSON></a>, English footballer (b. 1933)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Jim_<PERSON>\" title=\"Jim Towers\"><PERSON></a>, English footballer (b. 1933)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2011", "text": "<PERSON> \"<PERSON>\" <PERSON>, American singer-songwriter, harmonica player, and drummer (b. 1936)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Eyes\" Smith'><PERSON> \"<PERSON> Eyes\" <PERSON></a>, American singer-songwriter, harmonica player, and drummer (b. 1936)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>\" title='<PERSON> \"<PERSON> Eyes\" Smith'><PERSON> \"<PERSON> Eyes\" <PERSON></a>, American singer-songwriter, harmonica player, and drummer (b. 1936)", "links": [{"title": "<PERSON> \"<PERSON> Eyes\" Smith", "link": "https://wikipedia.org/wiki/<PERSON>_%22B<PERSON>_<PERSON>%22_<PERSON>"}]}, {"year": "2011", "text": "<PERSON><PERSON><PERSON>, Bangladeshi politician (b. 1948)", "html": "2011 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1948)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Bangladeshi politician (b. 1948)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2012", "text": "<PERSON>, Canadian director and producer, co-founded IMAX (b. 1926)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/Roman_Kroitor\" title=\"Roman Kroitor\"><PERSON></a>, Canadian director and producer, co-founded <a href=\"https://wikipedia.org/wiki/IMAX\" title=\"IMAX\">IMAX</a> (b. 1926)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Roman_Kroitor\" title=\"Roman Kroitor\"><PERSON></a>, Canadian director and producer, co-founded <a href=\"https://wikipedia.org/wiki/IMAX\" title=\"IMAX\">IMAX</a> (b. 1926)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Roman_Kroitor"}, {"title": "IMAX", "link": "https://wikipedia.org/wiki/IMAX"}]}, {"year": "2012", "text": "<PERSON>, American admiral (b. 1923)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Bourgeois\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1923)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>Bourgeois\" title=\"<PERSON>\"><PERSON></a>, American admiral (b. 1923)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_LeBourgeois"}]}, {"year": "2012", "text": "<PERSON>, German lawyer and politician, German Federal Minister of the Interior (b. 1925)", "html": "2012 - <a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">German Federal Minister of the Interior</a> (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, German lawyer and politician, <a href=\"https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)\" title=\"Federal Ministry of the Interior (Germany)\">German Federal Minister of the Interior</a> (b. 1925)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>"}, {"title": "Federal Ministry of the Interior (Germany)", "link": "https://wikipedia.org/wiki/Federal_Ministry_of_the_Interior_(Germany)"}]}, {"year": "2013", "text": "<PERSON>, American football player (b. 1966)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1966)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)\" title=\"<PERSON> (American football)\"><PERSON></a>, American football player (b. 1966)", "links": [{"title": "<PERSON> (American football)", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_(American_football)"}]}, {"year": "2013", "text": "<PERSON><PERSON>, Egyptian soprano and director (b. 1931)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian soprano and director (b. 1931)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>-<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Egyptian soprano and director (b. 1931)", "links": [{"title": "Ratiba <PERSON>", "link": "https://wikipedia.org/wiki/Ratiba_El-He<PERSON>"}]}, {"year": "2013", "text": "<PERSON><PERSON>, American dancer and choreographer (b. 1927)", "html": "2013 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer (b. 1927)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, American dancer and choreographer (b. 1927)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>"}]}, {"year": "2015", "text": "<PERSON>, Egyptian-French singer-songwriter (b. 1930)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/Guy_B%C3%A9art\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter (b. 1930)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_B%C3%A9art\" title=\"<PERSON>\"><PERSON></a>, Egyptian-French singer-songwriter (b. 1930)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/Guy_B%C3%A9art"}]}, {"year": "2015", "text": "<PERSON>, Virgin Islander lawyer, judge, and politician, 5th Lieutenant Governor of the United States Virgin Islands (b. 1942)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander lawyer, judge, and politician, 5th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands\" title=\"Lieutenant Governor of the United States Virgin Islands\">Lieutenant Governor of the United States Virgin Islands</a> (b. 1942)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Virgin Islander lawyer, judge, and politician, 5th <a href=\"https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands\" title=\"Lieutenant Governor of the United States Virgin Islands\">Lieutenant Governor of the United States Virgin Islands</a> (b. 1942)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}, {"title": "Lieutenant Governor of the United States Virgin Islands", "link": "https://wikipedia.org/wiki/Lieutenant_Governor_of_the_United_States_Virgin_Islands"}]}, {"year": "2015", "text": "<PERSON>, Austrian figure skater and coach (b. 1932)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian figure skater and coach (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Austrian figure skater and coach (b. 1932)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2015", "text": "<PERSON>, English captain and pilot (b. 1920)", "html": "2015 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and pilot (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English captain and pilot (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, American director and playwright (b. 1928)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and playwright (b. 1928)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American director and playwright (b. 1928)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON>, Italian priest and exorcist (b. 1925)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian priest and exorcist (b. 1925)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Italian priest and exorcist (b. 1925)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "<PERSON>, Italian economist and politician, 10th President of Italy and 49th Prime Minister of Italy (b. 1920)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> and 49th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Italian economist and politician, 10th <a href=\"https://wikipedia.org/wiki/President_of_Italy\" title=\"President of Italy\">President of Italy</a> and 49th <a href=\"https://wikipedia.org/wiki/Prime_Minister_of_Italy\" title=\"Prime Minister of Italy\">Prime Minister of Italy</a> (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>"}, {"title": "President of Italy", "link": "https://wikipedia.org/wiki/President_of_Italy"}, {"title": "Prime Minister of Italy", "link": "https://wikipedia.org/wiki/Prime_Minister_of_Italy"}]}, {"year": "2016", "text": "<PERSON><PERSON> <PERSON><PERSON>, American novelist (b. 1935)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/W<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist (b. 1935)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, American novelist (b. 1935)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/W._<PERSON><PERSON>_<PERSON><PERSON><PERSON>"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, Cabo Verdean politician, 2nd President of Cape Verde (b. 1944)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Mascarenhas_Monteiro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cabo Verdean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a> (b. 1944)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Ant%C3%B3nio_Mascarenhas_Monteiro\" title=\"<PERSON><PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Cabo Verdean politician, 2nd <a href=\"https://wikipedia.org/wiki/President_of_Cape_Verde\" class=\"mw-redirect\" title=\"President of Cape Verde\">President of Cape Verde</a> (b. 1944)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/Ant%C3%B3<PERSON>_Mascarenhas_Monteiro"}, {"title": "President of Cape Verde", "link": "https://wikipedia.org/wiki/President_of_Cape_Verde"}]}, {"year": "2016", "text": "<PERSON><PERSON><PERSON><PERSON>, French-born American businessman (b. 1932)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-born American businessman (b. 1932)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, French-born American businessman (b. 1932)", "links": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/G%C3%A9<PERSON><PERSON>_<PERSON>"}]}, {"year": "2016", "text": "  <PERSON><PERSON><PERSON><PERSON>, Turkish actor, director and activist (b.1949)", "html": "2016 - <a href=\"https://wikipedia.org/wiki/Tar%C4%B1k_Akan\" title=\"Tar<PERSON><PERSON> A<PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish actor, director and activist (b.1949)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/Tar%C4%B1k_Akan\" title=\"<PERSON>r<PERSON><PERSON>\"><PERSON><PERSON><PERSON><PERSON></a>, Turkish actor, director and activist (b.1949)", "links": [{"title": "Tarık Akan", "link": "https://wikipedia.org/wiki/Tar%C4%B1k_Akan"}]}, {"year": "2017", "text": "<PERSON><PERSON>, Brazilian journalist (b. 1951)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian journalist (b. 1951)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON>\"><PERSON><PERSON></a>, Brazilian journalist (b. 1951)", "links": [{"title": "<PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON>"}]}, {"year": "2017", "text": "<PERSON><PERSON><PERSON>, Marshal of the Indian Air Force (b. 1919)", "html": "2017 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Marshal of the Indian Air Force (b. 1919)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON><PERSON>\"><PERSON><PERSON><PERSON></a>, Marshal of the Indian Air Force (b. 1919)", "links": [{"title": "<PERSON><PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON><PERSON>_<PERSON>"}]}, {"year": "2018", "text": "<PERSON>, American brigadier general (b. 1920)", "html": "2018 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American brigadier general (b. 1920)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American brigadier general (b. 1920)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2019", "text": "<PERSON><PERSON> <PERSON><PERSON>, Indonesian politician and human rights defender (b. 1945)", "html": "2019 - <a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian politician and human rights defender (b. 1945)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>\" title=\"<PERSON><PERSON> <PERSON><PERSON>\"><PERSON><PERSON> <PERSON><PERSON></a>, Indonesian politician and human rights defender (b. 1945)", "links": [{"title": "<PERSON><PERSON> <PERSON><PERSON>", "link": "https://wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>"}]}, {"year": "2020", "text": "<PERSON>, Russian social activist and media personality (b. 1984) ", "html": "2020 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian social activist and media personality (b. 1984) ", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, Russian social activist and media personality (b. 1984) ", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, American actress (b. 1929)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, American actress (b. 1929)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2021", "text": "<PERSON>, English entrepreneur and inventor (b. 1940)", "html": "2021 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entrepreneur and inventor (b. 1940)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON>\"><PERSON></a>, English entrepreneur and inventor (b. 1940)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>"}]}, {"year": "2024", "text": "<PERSON>, Chinese revolutionary (b. 1947)", "html": "2024 - <a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"<PERSON> Binbin\"><PERSON></a>, Chinese revolutionary (b. 1947)", "no_year_html": "<a href=\"https://wikipedia.org/wiki/<PERSON>_<PERSON>\" title=\"Song Binbin\"><PERSON></a>, Chinese revolutionary (b. 1947)", "links": [{"title": "<PERSON>", "link": "https://wikipedia.org/wiki/<PERSON>_<PERSON>bin"}]}]}}